<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.bilibili.adp</groupId>
        <artifactId>advertiser-platform</artifactId>
        <version>2.1.13-SNAPSHOT</version>
    </parent>
    <artifactId>advertiser-portal</artifactId>
    <packaging>war</packaging>

    <dependencies>
        <dependency>
            <groupId>org.hibernate.validator</groupId>
            <artifactId>hibernate-validator</artifactId>
        </dependency>

        <dependency>
            <groupId>javax.validation</groupId>
            <artifactId>validation-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bilibili.business.cmpt</groupId>
            <artifactId>grpc-plugin</artifactId>
            <version>1.0.1-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-tomcat</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.bilibili</groupId>
            <artifactId>logback-otel-appender</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bilibili.report</groupId>
            <artifactId>report-common</artifactId>
            <version>${report.version}</version>
        </dependency>

        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>

        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
            <optional>true</optional>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.bilibili.adp</groupId>
            <artifactId>adp-soa-api</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bilibili.adp</groupId>
            <artifactId>adp-web-framework</artifactId>
            <version>2.1.13-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>netty</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>config</artifactId>
                    <groupId>com.bilibili.config</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.bilibili.adp</groupId>
            <artifactId>launch-biz</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>jackson-dataformat-cbor</artifactId>
                    <groupId>com.fasterxml.jackson.dataformat</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j-api</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j-core</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>universal-process-spring-boot-starter</artifactId>
                    <groupId>com.bilibili.process</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.bilibili</groupId>
                    <artifactId>warp-grpc</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-all</artifactId>
            <version>${netty-all.version}</version>
        </dependency>

        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-staticdocs</artifactId>
            <version>2.5.0</version>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-annotations</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.restdocs</groupId>
            <artifactId>spring-restdocs-mockmvc</artifactId>
            <version>1.1.2.RELEASE</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>${junit.version}</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
            <version>3.1.0</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.bilibili.bjcom</groupId>
            <artifactId>bjcom-cat-spring</artifactId>
            <version>1.0.1-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>io.netty</groupId>
                    <artifactId>netty-all</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.bilibili.rbac</groupId>
            <artifactId>rbac-filter</artifactId>
            <version>${rbac.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.bilibili.adp</groupId>
                    <artifactId>common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.bilibili.adp</groupId>
            <artifactId>mail-biz</artifactId>
            <version>${adp.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.bilibili.adp</groupId>
                    <artifactId>common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.bilibili.bjcom</groupId>
            <artifactId>bjcom-log-agent-logback</artifactId>
            <version>1.0.1-SNAPSHOT</version>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>com.bilibili.gateway</groupId>-->
<!--            <artifactId>gateway-filter</artifactId>-->
<!--            <version>${gateway.version}</version>-->
<!--            <exclusions>-->
<!--                <exclusion>-->
<!--                    <groupId>com.bilibili.adp</groupId>-->
<!--                    <artifactId>common</artifactId>-->
<!--                </exclusion>-->
<!--                <exclusion>-->
<!--                    <artifactId>c3p0</artifactId>-->
<!--                    <groupId>c3p0</groupId>-->
<!--                </exclusion>-->
<!--            </exclusions>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
            <version>${jackson.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>${jackson-databind.version}</version>
        </dependency>

        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
            <version>bili-2.5.6-RELEASE</version>
        </dependency>

        <dependency>
            <groupId>com.bilibili.bjcom</groupId>
            <artifactId>bjcom-test</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.bilibili.bv</groupId>
            <artifactId>bvutils</artifactId>
            <version>1.0.0-RELEASE</version>
        </dependency>

        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-spring-ui</artifactId>
            <version>2.0.3</version>
        </dependency>

        <dependency>
            <groupId>com.bilibili.bjcom</groupId>
            <artifactId>bjcom-util</artifactId>
            <version>1.2.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>pleiades.component.rpc</groupId>
            <artifactId>rpc-client</artifactId>
            <version>${pleiades.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>guava</artifactId>
                    <groupId>com.google.guava</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>pleiades.venus.naming</groupId>
            <artifactId>naming-discovery</artifactId>
            <version>${pleiades.version}</version>
        </dependency>

        <!--临时处理-->
        <dependency>
            <groupId>org.codehaus.mojo</groupId>
            <artifactId>animal-sniffer-annotations</artifactId>
            <version>1.18</version>
        </dependency>
        <dependency>
            <groupId>com.bilibili.adp</groupId>
            <artifactId>adp-cpc</artifactId>
            <version>${project.parent.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>config</artifactId>
                    <groupId>com.bilibili.config</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-core</artifactId>
            <version>2.17.0</version>
        </dependency>
        <dependency>
            <groupId>com.bilibili.sycpb</groupId>
            <artifactId>binlog-consumer</artifactId>
            <version>${bible.consumer.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-slf4j-impl</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.bilibili.sycpb</groupId>
                    <artifactId>bible-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.bilibili.crm</groupId>
            <artifactId>crm-common</artifactId>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <artifactId>simpleclient_servlet</artifactId>
                    <groupId>io.prometheus</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>simpleclient_httpserver</artifactId>
                    <groupId>io.prometheus</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>simpleclient</artifactId>
                    <groupId>io.prometheus</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>simpleclient_hotspot</artifactId>
                    <groupId>io.prometheus</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.bilibili.config</groupId>
            <artifactId>config-refresh</artifactId>
            <version>${config-refresh.version}</version>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-autoconfigure</artifactId>
        </dependency>
    </dependencies>
    <build>
        <finalName>cpm-adp</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <version>2.6</version>
                <configuration>
                    <!--如果想在没有web.xml文件的情况下构建WAR，请设置为false。-->
                    <failOnMissingWebXml>false</failOnMissingWebXml>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.5.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                    <compilerArgs>
                        <arg>-parameters</arg>
                    </compilerArgs>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>no.entur.mapstruct.spi</groupId>
                            <artifactId>protobuf-spi-impl</artifactId>
                            <version>1.43</version>
                        </path>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${org.mapstruct.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok-mapstruct-binding</artifactId>
                            <version>0.2.0</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>2.7</version>
                <configuration>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>versions-maven-plugin</artifactId>
                <groupId>org.codehaus.mojo</groupId>
                <version>2.2</version>
            </plugin>
        </plugins>
    </build>
</project>
