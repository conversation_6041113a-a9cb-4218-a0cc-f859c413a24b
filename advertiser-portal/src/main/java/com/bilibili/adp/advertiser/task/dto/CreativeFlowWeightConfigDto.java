package com.bilibili.adp.advertiser.task.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Arrays;
import java.util.List;

/**
 * 创意降权配置。
 *
 * <AUTHOR>
 * @since 2019年11月21日
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CreativeFlowWeightConfigDto {

    /**
     * 待降权创意曝光最低值
     */
    private int waitCreativeShowCountThreshold = 10000;

    /**
     * 统计Benchmark创意曝光最低值
     */
    private int statBenchmarkShowCountThreshold = 10000;

    /**
     * 统计内广BenchmarkCTR系数
     */
    private double statInnerCategoryBenchmarkRatio = 0.5;

    /**
     * 统计外广BenchmarkCTR系数
     */
    private double statOuterCategoryBenchmarkRatio = 0.5;

    /**
     * 平台内广BenchmarkCTR
     */
    private double innerPlatformBenchmarkCtr = 0.00375;

    /**
     * 平台外广BenchmarkCTR
     */
    private double outerPlatformBenchmarkCtr = 0.00375;

    /**
     * 信息流资源位
     */
    private List<Integer> infoFeedsSourceIds = Arrays.asList(1891, 1898, 2003);

    /**
     * 播放页资源位
     */
    private List<Integer> playPageSourceIds = Arrays.asList(2030, 2031);

}
