package com.bilibili.adp.advertiser.portal.webapi.flyPro.v2.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class FlyProxyVo {
    @ApiModelProperty("mid")
    private Long mid;
    @ApiModelProperty("昵称")
    private String nickname;
}
