package com.bilibili.adp.advertiser.portal.webapi.flyPro;

import com.bilibili.adp.account.dto.AccountBaseDto;
import com.bilibili.adp.account.service.IQueryAccountService;
import com.bilibili.adp.advertiser.portal.common.BasicController;
import com.bilibili.adp.advertiser.portal.common.Context;
import com.bilibili.adp.advertiser.portal.common.LaunchPortalUtils;
import com.bilibili.adp.advertiser.portal.webapi.flyPro.vo.ProFlyVideoInfoVo;
import com.bilibili.adp.advertiser.portal.webapi.flyPro.vo.ProFlyVideoListVo;
import com.bilibili.adp.advertiser.portal.webapi.launch.cpc.common.CpcLaunchWebUtil;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.StringDateParser;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.biz.services.pickup.PickupOrderQuerier;
import com.bilibili.adp.cpc.biz.services.pickup.dto.PickupOrderDto;
import com.bilibili.adp.launch.api.flyPro.IFlyProLaunchService;
import com.bilibili.adp.launch.api.flyPro.dto.FlyProVideoInfoDto;
import com.bilibili.adp.launch.api.flyPro.dto.FlyProViewQueryDto;
import com.bilibili.adp.passport.api.dto.ArchiveDetail;
import com.bilibili.adp.passport.biz.common.ArchiveState;
import com.bilibili.adp.passport.biz.manager.ArchiveManager;
import com.bilibili.adp.passport.biz.manager.bean.ArchiveStat;
import com.bilibili.adp.web.framework.core.Pagination;
import com.bilibili.adp.web.framework.core.Response;
import com.bilibili.bjcom.querydsl.paging.Page;
import com.bilibili.bvid.BVIDUtils;
import com.bilibili.mas.common.utils.Values;
import com.google.common.base.Strings;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/web_api/v1/fly_pro/video")
@Api(value = "/fly_pro/video", description = "专业起飞视频相关")
public class FlyProVideoController extends BasicController {
    private static final Logger LOGGER = LoggerFactory.getLogger(FlyProVideoController.class);
    @Autowired
    private IFlyProLaunchService flyProLaunchService;
    @Autowired
    private IQueryAccountService queryAccountService;
    @Autowired
    private PickupOrderQuerier pickupOrderQuerier;
    @Autowired
    private ArchiveManager archiveManager;

    @ApiOperation(value = "待推广的视频信息")
    @RequestMapping(value = "/{video_id}", method = RequestMethod.GET)
    public Response<ProFlyVideoInfoVo> getVideoInfo(
            @ApiIgnore Context context,
            @ApiParam("视频ID") @PathVariable(value = "video_id") String videoId) throws ServiceException {

        Long aid = StringUtils.isNumeric(videoId) ? Long.parseLong(videoId) : BVIDUtils.bvToAv(videoId);

        ArchiveDetail archiveDetail = archiveManager.queryArchivesByAid(aid);
        ArchiveStat archiveStatInfo = archiveManager.getArchiveStatInfo(aid);

        return Response.SUCCESS(archiveDetail == null ? null : ProFlyVideoInfoVo.builder()
                .avid(archiveDetail.getArchive().getAid())
                .cover(Utils.fillImgDomain(archiveDetail.getArchive().getCover()))
                .title(archiveDetail.getArchive().getTitle())
                .duration(archiveDetail.getArchive().getDuration())
                .pub_time(StringDateParser.getDateString(new Date(Values.zeroIfNull(archiveDetail.getArchive().getPtime()) * 1000L)))
                .view(archiveStatInfo == null ? 0L : archiveStatInfo.getView())
                .build());
    }

    @ApiOperation(value = "待推广的视频列表")
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public Response<Pagination<List<ProFlyVideoListVo>>> getVideos(@ApiIgnore Context context,
                                                                   @ApiParam("视频标题") @RequestParam(value = "title", required = false) String title,
                                                                   @ApiParam("avid/bvid") @RequestParam(value = "video_id", required = false) String videoId,
                                                                   @ApiParam("结束时间") @RequestParam(value = "end_time", required = false) Long endTime,
                                                                   @ApiParam("开始时间") @RequestParam(value = "begin_time", required = false) Long beginTime,
                                                                   @ApiParam("页码") @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
                                                                   @ApiParam("页大小") @RequestParam(value = "size", required = false, defaultValue = "15") Integer size) {

        AccountBaseDto accountBaseDto = queryAccountService.getAccountBaseDtoByIdFromCache(context.getAccountId());
        Assert.notNull(accountBaseDto, "当前用户不存在");
        Assert.isTrue(accountBaseDto.getIsSupportContent() == 1, "您没有投放非商业内容的权限");
        LOGGER.info("accountBaseDto ---> {}", accountBaseDto);

        List<Integer> archiveStates = Arrays.asList(ArchiveState.OPEN_BROWSE.getCode(), ArchiveState.ORANGE_THROUGH.getCode(),
                ArchiveState.REPAIR_PENDING.getCode(), ArchiveState.VIP_ACCESS.getCode(), ArchiveState.DELAY_RELEASE.getCode(), ArchiveState.UP_TIMED_RELEASE.getCode());

        Long aid = null;
        if (StringUtils.isNotBlank(videoId)) {
            aid = StringUtils.isNumeric(videoId) ? Long.parseLong(videoId) : BVIDUtils.bvToAv(videoId);
        }
        Page<FlyProVideoInfoDto> flyProViewPage = flyProLaunchService.queryFlyProVideoInfoList(FlyProViewQueryDto.builder()
                .accountId(context.getAccountId())
                .mid(accountBaseDto.getMid())
                .archiveStates(archiveStates)
                .title(title)
                .avid(aid)
                .endTime(endTime)
                .startTime(beginTime)
                .page(page)
                .size(size)
                .build());
        if (flyProViewPage == null || CollectionUtils.isEmpty(flyProViewPage.getRows())) {
            return Response.SUCCESS(new Pagination<>(page, 0, Collections.emptyList()));
        }
        LOGGER.info("flyProLaunchService.queryFlyProVideoInfoList mid is {}, res is {}", accountBaseDto.getMid(), flyProViewPage);

        List<PickupOrderDto> pickupOrderDtos = pickupOrderQuerier.searchPickupOrder(flyProViewPage.getRows().stream()
                        .map(FlyProVideoInfoDto::getAvid).distinct().collect(Collectors.toList()));
        List<Long> flowerFireAids = CollectionUtils.isEmpty(pickupOrderDtos) ? Collections.emptyList() :
                pickupOrderDtos.stream().map(PickupOrderDto::getAvId).collect(Collectors.toList());

        List<ProFlyVideoListVo> vos = new ArrayList<>();
        flyProViewPage.getRows().forEach(dto -> {
            ProFlyVideoListVo vo = new ProFlyVideoListVo();
            BeanUtils.copyProperties(dto, vo);

            String cover = dto.getCover();
            if (!Strings.isNullOrEmpty(cover) && !cover.trim().startsWith(LaunchPortalUtils.ARCHIVE_HAS_COVER_PREFIX)) {
                vo.setCover(LaunchPortalUtils.ARCHIVE_COVER_DOMAIN + cover);
            }

            vo.setCampaign_id(dto.getCampaignId());
            vo.setIs_popularized(dto.getIsPopularized());
            vo.setVideo_id(BVIDUtils.avToBv(dto.getAvid()));
            vo.setPub_time(dto.getPubTime());
            vo.setClick_rate(CpcLaunchWebUtil.getIntegerPercent(dto.getClick(), dto.getShow()));
            vo.setFans_increase(dto.getFansIncrease());
            vo.setCvr(CpcLaunchWebUtil.getIntegerPercent(dto.getFansIncrease(), dto.getClick()));
            vo.setTotal_cost(dto.getTotalCost());
            vo.setFans_attention_cost(CpcLaunchWebUtil.getCostPerInt(dto.getTotalCost(), dto.getFansIncrease()));
            vo.setPopularizable(1);
            Integer attribute = Values.zeroIfNull(dto.getAttribute());
            if (Values.zeroIfNull(dto.getOrder_id()) > 0
                    || Utils.getIntegerBit(attribute, LaunchPortalUtils.PRIVATE_ORDER_INDEX) == 1
                    || flowerFireAids.indexOf(dto.getAvid()) >= 0) {

                LOGGER.info("popularizable is 0,mid is {},avid is {},order_id is {},attribute is {},isFlowerFireAids is {}",
                        accountBaseDto.getMid(), dto.getAvid(), dto.getOrder_id(), attribute, flowerFireAids.indexOf(dto.getAvid()) >= 0);
                vo.setPopularizable(0);
            }
            vos.add(vo);
        });
        return Response.SUCCESS(new Pagination<>(page, (int) flyProViewPage.getTotal(), vos));
    }

}
