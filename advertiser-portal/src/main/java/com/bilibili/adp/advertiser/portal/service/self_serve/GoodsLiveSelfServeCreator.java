package com.bilibili.adp.advertiser.portal.service.self_serve;

import cn.hutool.core.lang.Assert;
import com.bapis.ad.goods.SelfServeNewOrderRequest;
import com.bilibili.adp.advertiser.portal.common.Context;
import com.bilibili.adp.advertiser.portal.webapi.flyPro.v2.vo.FlyProBatchCreativeBannerVo;
import com.bilibili.adp.advertiser.portal.webapi.flyPro.v2.vo.FlyProCreativeBannerVo;
import com.bilibili.adp.advertiser.portal.webapi.flyPro.v2.vo.FlyProSelectScenesVo;
import com.bilibili.adp.advertiser.portal.webapi.launch.cpc.vo.CpcImageVo;
import com.bilibili.adp.advertiser.portal.webapi.launch.cpc.vo.CpcUnitArchiveContentTargetVo;
import com.bilibili.adp.advertiser.portal.webapi.launch.cpc.vo.CpcUnitTargetVo;
import com.bilibili.adp.advertiser.portal.webapi.middleground.vo.cpc.MiddleNewCpcUnitVo;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.enums.AdpVersion;
import com.bilibili.adp.common.enums.OperatorType;
import com.bilibili.adp.common.enums.SalesType;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.exception.ServiceRuntimeException;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.biz.constants.Constants;
import com.bilibili.adp.cpc.biz.services.live.AdpCpcLiveRoomService;
import com.bilibili.adp.cpc.dao.querydsl.pos.SelfServeGoodsOrderPo;
import com.bilibili.adp.cpc.enums.SpecificScenesEnum;
import com.bilibili.adp.cpc.enums.ad.OcpcTargetEnum;
import com.bilibili.adp.cpc.enums.ad.PromotionPurposeType;
import com.bilibili.adp.cpc.enums.effectad.unit.UnitLiveLaunchType;
import com.bilibili.adp.cpc.utils.TimeUtils;
import com.bilibili.adp.launch.api.campaign.dto.NewCpcCampaignDto;
import com.bilibili.adp.launch.api.common.*;
import com.bilibili.adp.launch.api.flyPro.dto.enums.ScenesEnum;
import com.bilibili.adp.passport.api.dto.UserInfoDto;
import com.bilibili.adp.passport.api.service.IPassportService;
import com.bilibili.adp.passport.biz.manager.bean.LiveBroadcastRoomInfo;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 直播间自主投流
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class GoodsLiveSelfServeCreator extends BaseSelfServeCreator {

    @Autowired
    private AdpCpcLiveRoomService adpCpcLiveRoomService;
    @Autowired
    private IPassportService passportService;

    @Override
    public SelfServeGoodsOrderPo createSelfServeGoodsOrder(SelfServeNewOrderRequest request) {
        return super.createSelfServeGoodsOrder(request);
    }

    @Override
    @SneakyThrows
    public Integer createCampaign(SelfServeNewOrderRequest request, Operator operator) {
        final NewCpcCampaignDto dto = NewCpcCampaignDto.builder()
                .accountId(request.getAccountId())
                .campaignName(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + "_带货投流_" + request.getMaterialId())
                .promotionPurposeType(PromotionPurposeType.BRAND_SPREAD.getCode())
                .budgetType(BudgetType.TOTAL.getCode())
                .budgetLimitType(BudgetLimitType.TOTAL.getCode())
                .budget((long) request.getBudgetFen())
                .speedMode(SpeedMode.SPEED_UP.getCode())
                .isMiddleAd(Constants.INT_TRUE)
                .adpVersion(AdpVersion.MIDDLE.getKey())
                .build();
        return cpcCampaignService.createCpcCampaign(dto, operator);
    }

    @SneakyThrows
    @Override
    public Integer createUnit(SelfServeNewOrderRequest request, SelfServeGoodsOrderPo orderPo) {
        Assert.isTrue(Utils.isPositive(request.getMaterialId()), "material id 不能为空");
        Assert.isTrue(Utils.isPositive(request.getOcpxBid()), "ocpx bid 不能为空");
        Integer roomId = Math.toIntExact(request.getMaterialId());

        final Context ctx = new Context();
        ctx.setAccountId(request.getAccountId());
        ctx.setUsername("SYSTEM");
        ctx.setType(OperatorType.PERSON_FLY.getCode());
        ctx.setProxyId(0);

        final LocalDateTime startLdt = orderPo.getStartTs().toLocalDateTime();
        final LocalDateTime endLdt = orderPo.getEndTs().toLocalDateTime();

        final CpcUnitTargetVo targetVo = new CpcUnitTargetVo();
        if (!CollectionUtils.isEmpty(request.getCrowdAgeSetList())) {
            targetVo.setAge(request.getCrowdAgeSetList());
        }
        if (!CollectionUtils.isEmpty(request.getCrowdGenderSetList())) {
            targetVo.setGender(request.getCrowdGenderSetList());
        }
        if (!CollectionUtils.isEmpty(request.getCrowdAreaSetList())) {
            targetVo.setArea(request.getCrowdAreaSetList());
        }
        // 需要包含粉丝定向的话
        if (Utils.isPositive(request.getIsMidFansTarget())) {
            CpcUnitArchiveContentTargetVo archiveContentTargetVo = new CpcUnitArchiveContentTargetVo();
//            archiveContentTargetVo.setFans_relation(UnitTargetFansRelation.FANS.getCode());
            archiveContentTargetVo.setFans_relation(0);
            archiveContentTargetVo.setInclude_theirs_fans(Arrays.asList(request.getLiveRoomMid()));
            targetVo.setArchive_content(archiveContentTargetVo);
        }

        final MiddleNewCpcUnitVo vo = MiddleNewCpcUnitVo.builder()
                .campaign_id(orderPo.getCampaignId())
                .unit_name(startLdt.toLocalDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + "_带货投流_" + roomId)
                // 带货直播
                .unit_promotion_purpose_type(PromotionPurposeType.GOODS_LIVE.getCode())
                .material_id(String.valueOf(roomId))
                .goodsLiveLaunchType(UnitLiveLaunchType.LIVE_DIRECT_LAUNCH_VALUE)
                .begin_date(startLdt.toLocalDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")))
                .end_date(endLdt.toLocalDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")))
                // 投放时间小时处理
                .launch_time(TimeUtils.buildTimeMatrix(startLdt, endLdt))
                .frequency_unit(FrequencyUnit.DAY.getCode())
                .daily_budget_type(DailyBudgetType.MANUAL.getCode())
                // 转成元
                .budget(BigDecimal.valueOf(request.getBudgetFen()).divide(BigDecimal.valueOf(100), RoundingMode.HALF_EVEN))
                .sales_type(SalesType.CPM.getCode())
                // 直播间进人
                .ocpc_target(OcpcTargetEnum.LIVE_ENTRY.getCode())
                .is_no_bid(Constants.IS_NO_BIND)
                .two_stage_bid(Utils.fromFenToYuan(request.getOcpxBid()))
                .speed_mode(SpeedMode.SPEED_UP.getCode())
                // 定向
                .targets(targetVo)
                .enable_ocpc(true)
//                .launch_ad_type(LaunchVideoType.BUSINESS.getCode())
                .build();
        return middleUnitController.createUnit(ctx, vo).getResult();
    }

    @Override
    public Integer createCreative(SelfServeNewOrderRequest request, Integer unitId, Operator operator) {
        // 获取直播间信息
        Assert.isTrue(Utils.isPositive(request.getMaterialId()), "material id 不能为空");
        Integer roomId = Math.toIntExact(request.getMaterialId());
        LiveBroadcastRoomInfo roomInfo = adpCpcLiveRoomService.queryLiveBroadcastRoomInfoById(roomId);
        Assert.notNull(roomInfo, "直播间信息不存在, roomId=" + roomId);

        // mid 信息
        UserInfoDto userInfoDto = getUserInfoByMid(roomInfo.getUid());
        Assert.notNull(userInfoDto, "mid信息不存在, mid=" + roomInfo.getUid());

        // 标题描述
        String defaultTitle = String.format("%s的直播间", userInfoDto.getName());
        final String title = StringUtils.hasText(request.getTitle()) ? request.getTitle() : defaultTitle;
        final String description = StringUtils.hasText(request.getDescription()) ? request.getDescription() : roomInfo.getTitle();

        // 创意图片没有的话用直播间默认图片
        String imageUrl = CollectionUtils.isEmpty(request.getImageUrlsList()) ? roomInfo.getCover() :
                request.getImageUrlsList().get(0);
        log.info("createCreative, imageUrl:{}", imageUrl);

        List<CpcImageVo> imageVos = new ArrayList<>();
        if (!StringUtils.isEmpty(imageUrl)) {
            imageVos = Collections.singletonList(CpcImageVo.builder()
                    .image_url(imageUrl)
                    .image_hash("--")
                    .build());
        }

        final FlyProBatchCreativeBannerVo vo = FlyProBatchCreativeBannerVo.builder()
                .unit_id(unitId)
                .select_scenes(FlyProSelectScenesVo.builder()
                        // 指定场景
                        .launch_scene(ScenesEnum.MOBILE_SCENES.getCode())
                        .multi_specific_scene(Arrays.asList(SpecificScenesEnum.MSG_FLOW.getCode(), SpecificScenesEnum.STORY.getCode()))
                        .build())
                .creativeAll(Collections.singletonList(FlyProCreativeBannerVo.builder()
                        .creative_name(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + "_带货投流_" + request.getLiveRoomMid())
                        .description(description)
                        .title(title)
                        .images(imageVos)
                        .is_yellow_car(request.getIsYellowCar())
                        .yellow_car_title(request.getYellowCarTitle())
                        .build()))
                .build();
        final List<Integer> creativeIds = middleCreativeController.saveFlyUnitCreatives(vo, operator);
        return creativeIds.get(0);
    }

    private UserInfoDto getUserInfoByMid(Long mid) {
        Assert.notNull(mid);

        UserInfoDto userInfoDto = null;
        try {
            userInfoDto = passportService.getUserByMid(mid);
        } catch (ServiceException e) {
            log.error("passportService.getUserByMid fail, mid:{}, e:{}", mid, e);
            throw new ServiceRuntimeException("mid不存在");
        }
        return userInfoDto;
    }
}
