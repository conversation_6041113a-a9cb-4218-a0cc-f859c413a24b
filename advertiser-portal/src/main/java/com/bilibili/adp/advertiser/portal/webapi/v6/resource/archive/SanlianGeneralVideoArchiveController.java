package com.bilibili.adp.advertiser.portal.webapi.v6.resource.archive;

import com.bilibili.adp.advertiser.portal.common.Context;
import com.bilibili.adp.advertiser.portal.service.launch.BvidSwitchService;
import com.bilibili.adp.advertiser.portal.webapi.v6.resource.archive.converters.GeneralArchiveControllerConverter;
import com.bilibili.adp.advertiser.portal.webapi.v6.resource.archive.vos.*;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.cpc.utils.Response;
import com.bilibili.adp.v6.resource.archive.GeneralArchiveService;
import com.bilibili.adp.v6.resource.archive.bos.GeneralArchiveAuthInfoBo;
import com.bilibili.adp.v6.resource.archive.bos.GeneralVideoArcAuthInfoQueryBo;
import com.bilibili.adp.web.framework.controller.BaseController;
import com.bilibili.adp.web.framework.core.Pagination;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;
import java.util.stream.Collectors;

@Api(tags = {"普通内容-非商单稿件授权"})
@RestController
@RequestMapping("/web_api/v6/resource/sanlian/general_video/authorize")
public class SanlianGeneralVideoArchiveController extends BaseController {

    @Autowired
    private GeneralArchiveService generalArchiveService;

    @ApiOperation("批量获取视频授权记录")
    @GetMapping("/list")
    public Response<Pagination<List<GeneralArchiveAuthInfoVo>>> listAuthorizedArchive(
            @ApiIgnore Context context,
            @ApiParam("授权范围") @RequestParam(value = "auth_mode", required = false) Integer authMode,
            @ApiParam("授权来源") @RequestParam(value = "auth_source", required = false) Integer authSource,
            @ApiParam("授权状态") @RequestParam(value = "status", required = false) Integer status,
            @ApiParam("所属Up主") @RequestParam(value = "up_mid", required = false) Long upMid,
            @ApiParam("授权稿件类别") @RequestParam(value = "arc_type", required = false) Integer arcType,
            @ApiParam("稿件标题搜索") @RequestParam(value = "keyword", required = false, defaultValue = "") String keyWord,
            @ApiParam("页码") @RequestParam(value = "page", defaultValue = "1") Integer page,
            @ApiParam("每页大小") @RequestParam(value = "size", defaultValue = "10") Integer pageSize,
            @ApiParam("BVID") @RequestParam(value = "bvid", required = false) String bvid,
            @ApiParam("共享范围 1-本账号  3-同客户同品牌 4-同客户同品牌同代理") @RequestParam(value = "share_scope", required = false) Integer shareScope) {
     Long avid = StringUtils.isNotEmpty(bvid) ? BvidSwitchService.convert2Avid(bvid) : null;
        GeneralVideoArcAuthInfoQueryBo queryBo = GeneralVideoArcAuthInfoQueryBo.builder()
                .accountId(context.getAccountId())
                .authMode(authMode)
                .authSource(authSource)
                .status(status)
                .upMid(upMid)
                .page(page)
                .pageSize(pageSize)
                .avid(avid)
                .arcType(arcType)
                .keyWord(keyWord)
                .fromMaster(true)
                .shareScope(shareScope)
                .build();
        PageResult<GeneralArchiveAuthInfoBo> result = generalArchiveService.list(queryBo);

        return Response.ok(new Pagination<>(page,
                result.getTotal(),
                result.getRecords().stream().map(GeneralArchiveControllerConverter.MAPPER::toVo)
                        .collect(Collectors.toList())));
    }

    @ApiOperation("获取视频授权状态")
    @GetMapping("/state")
    public Response<GeneralArchiveStatusVo> getArchiveState(
            @ApiIgnore Context context,
            @ApiParam("avid/bvid") @RequestParam(value = "archive_id") String archiveId) {
        long avid = StringUtils.isNumeric(archiveId) ? Long.parseLong(archiveId) : BvidSwitchService.convert2Avid(
                archiveId);
        GeneralArchiveStatusVo generalArchiveStatusVo = GeneralArchiveControllerConverter.MAPPER.toVo(
                generalArchiveService.state(context.getAccountId(), avid));
        return Response.ok(generalArchiveStatusVo);
    }

    @ApiOperation("批量申请授权")
    @PostMapping("/bind")
    public Response<Void> bindArchives(@ApiIgnore Context context,
                                       @RequestBody GeneralArchiveBindVo generalArchiveBindVo
    ) {
        generalArchiveService.batchBind(context.getAccountId(),
                GeneralArchiveControllerConverter.MAPPER.toBo(generalArchiveBindVo));
        return Response.ok();
    }

    @ApiOperation("撤回申请")
    @PostMapping("/cancel/{authId}")
    public Response<Void> cancelAuthorize(@ApiIgnore Context context,
                                          @ApiParam("授权码") @PathVariable String authId) {
        generalArchiveService.cancel(context.getAccountId(), authId);
        return Response.ok();
    }

    @ApiOperation("授权续期")
    @PostMapping("/renewal")
    public Response<Void> renewalAuthorize(@ApiIgnore Context context,
                                           @RequestBody GeneralArchiveRenewalVo generalArchiveRenewalVo) {
        generalArchiveService.renewal(context.getAccountId(),
                GeneralArchiveControllerConverter.MAPPER.toBo(generalArchiveRenewalVo));
        return Response.ok();
    }

    @ApiOperation("更新授权范围")
    @PostMapping("/update")
    public Response<Void> updateAuthorize(@ApiIgnore Context context,
                                          @RequestBody GeneralArchiveUpdateVo generalArchiveUpdateVo) {
        generalArchiveService.update(context.getAccountId(),
                GeneralArchiveControllerConverter.MAPPER.toBo(generalArchiveUpdateVo));
        return Response.ok();
    }
}
