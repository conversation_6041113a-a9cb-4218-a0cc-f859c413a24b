package com.bilibili.adp.advertiser.task.job;

import com.bilibili.adp.cpc.biz.services.creative.AdpCpcCreativePubInfoService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * @ClassName RefreshPubTotalGameCreativeJob
 * <AUTHOR>
 * @Date 2023/12/15 8:58 下午
 * @Version 1.0
 **/
@Component
@Slf4j
@JobHandler("RefreshPubTotalGameCreativeJob")
public class RefreshPubTotalGameCreativeJob extends IJobHandler {

    @Autowired
    private AdpCpcCreativePubInfoService adpCpcCreativePubInfoService;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        log.info("********  start job: RefreshPubTotalGameCreativeJob....param={}  ******", param);
        Integer startCreativeId = 0;
        Integer lauGameProductRelationStartId = 0;
        if (!StringUtils.isEmpty(param)) {
            String[] split = param.split(",");
            startCreativeId = Integer.parseInt(split[0]);
            if (split.length > 1) {
                lauGameProductRelationStartId = Integer.parseInt(split[1]);
            }

        }
        adpCpcCreativePubInfoService.pubTotalAndroidGameCreativeInfo(startCreativeId);
        adpCpcCreativePubInfoService.pubTotalIOSGameCreativeInfo(startCreativeId, lauGameProductRelationStartId);
        log.info("********  end job: RefreshPubTotalGameCreativeJob....  ******");
        return ReturnT.SUCCESS;
    }
}
