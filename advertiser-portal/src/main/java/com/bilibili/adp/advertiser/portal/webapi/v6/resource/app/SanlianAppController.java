package com.bilibili.adp.advertiser.portal.webapi.v6.resource.app;


import com.bilibili.adp.advertiser.portal.common.BasicController;
import com.bilibili.adp.advertiser.portal.common.Context;
import com.bilibili.adp.advertiser.portal.webapi.effect_ad.converter.AppConverter;
import com.bilibili.adp.advertiser.portal.webapi.effect_ad.vo.AppPackageVo;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.cpc.biz.bos.app.AppPackageBo;
import com.bilibili.adp.cpc.biz.services.app.EffectAdAppService;
import com.bilibili.adp.cpc.utils.Response;
import com.bilibili.adp.web.framework.core.Pagination;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping(value = "/web_api/v6/resource/sanlian/app")
@RequiredArgsConstructor
public class SanlianAppController extends BasicController {
    private final EffectAdAppService effectAdAppService;

    /**
     * 复制来源
     * /web_api/v1/effect_ad/app/list
     */
    @GetMapping(value = "/list")
    public Response<Pagination<List<AppPackageVo>>> listByPage(@ApiIgnore Context context,
                                                               @ApiParam("app 平台类型 1-ios 2-android 3-iPhone 4-iPad")
                                                               @RequestParam(value = "platform_types", required = false) List<Integer> platformTypes,
                                                               @ApiParam(value = "app_package_id") @RequestParam(value = "app_package_id", required = false) Long appPackageId,
                                                               @ApiParam(value = "page") @RequestParam(value = "page", defaultValue = "1") Integer page,
                                                               @ApiParam(value = "page_size") @RequestParam(value = "page_size", defaultValue = "20") Integer pageSize) {

        PageResult<AppPackageBo> appPackageBoPageResult = effectAdAppService.listByPage(Collections.singletonList(context.getAccountId()), platformTypes, appPackageId, page, pageSize);
        return Response.ok(
                new Pagination<>(
                        page,
                        appPackageBoPageResult.getTotal(),
                        appPackageBoPageResult.getRecords().stream()
                                .map(AppConverter.MAPPER::bo2Vo)
                                .collect(Collectors.toList())
                )
        );
    }
}
