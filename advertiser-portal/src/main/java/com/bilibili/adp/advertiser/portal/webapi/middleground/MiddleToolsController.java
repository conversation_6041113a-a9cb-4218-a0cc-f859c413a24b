package com.bilibili.adp.advertiser.portal.webapi.middleground;

import com.bilibili.adp.advertiser.portal.common.BasicController;
import com.bilibili.adp.advertiser.portal.common.Context;
import com.bilibili.adp.advertiser.portal.webapi.middleground.vo.WebImageVo;
import com.bilibili.adp.advertiser.portal.webapi.v2.misc.AdpCpcToolsController;
import com.bilibili.adp.cpc.biz.services.common.SimpleBFSService;
import com.bilibili.adp.cpc.biz.services.common.bos.BFSInfoBo;
import com.bilibili.adp.cpc.biz.services.recommend.AdpCpcRecommendService;
import com.bilibili.adp.cpc.biz.services.recommend.bos.TitleSearchBo;
import com.bilibili.adp.cpc.utils.Response;
import com.bilibili.adp.launch.api.service.validation.IValidationService;
import com.bilibili.adp.launch.api.service.validation.bos.MonitorUrlValidationBo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.List;
import java.util.Objects;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/web_api/v1/middle/tools")
@Api(value = "/middle/tools", description = "广告中台【工具】")
public class MiddleToolsController extends BasicController {

    private final AdpCpcToolsController adpCpcToolsController;
    private final SimpleBFSService simpleBFSService;
    private final IValidationService validationService;
    private final AdpCpcRecommendService adpCpcRecommendService;

    @Value("${bfs.category.sanlian:sanlian}")
    private String dir;

    @ApiOperation("校验点击监控链接")
    @GetMapping("/validate/monitor")
    public Response<MonitorUrlValidationBo> validateMonitorUrl(@ApiIgnore Context context,
                                                               @RequestParam String url,
                                                               @RequestParam(value = "is_ios", required = false, defaultValue = "false") Boolean isIos,
                                                               @RequestParam(value = "is_android", required = false, defaultValue = "false") Boolean isAndroid) {
        Assert.notNull(context, "检查登录状态失败");
        return Response.ok(validationService.validateMonitorUrl(url, isIos, isAndroid));
    }


    @SneakyThrows
    @ApiOperation("上传图片至BFS")
    @PostMapping("/upload")
    public Response<WebImageVo> upload(@RequestParam(value = "parse", defaultValue = "false") Boolean parse,
                                       @RequestParam("file") MultipartFile multipartFile) {
        try (final InputStream is = multipartFile.getInputStream()) {
            final String ext = simpleBFSService.getExt(multipartFile.getOriginalFilename());
            final byte[] bytes = IOUtils.toByteArray(is);
            final BFSInfoBo bfsInfoBo = simpleBFSService.upload(dir, multipartFile.getContentType(), bytes, ext);
            final WebImageVo.WebImageVoBuilder builder = WebImageVo.builder()
                    .url(bfsInfoBo.getUrl())
                    .md5(bfsInfoBo.getMd5())
                    .size(bytes.length);
            if (parse) {
                try (final InputStream is1 = new ByteArrayInputStream(bytes)) {
                    final BufferedImage bufferedImage = ImageIO.read(is1);
                    if (!Objects.isNull(bufferedImage)) {
                        builder.width(bufferedImage.getWidth())
                                .height(bufferedImage.getHeight());
                    }
                } catch (Throwable t) {
                    log.error("解析图片错误", t);
                }
            }
            return Response.ok(builder.build());
        } catch (Throwable t) {
            return Response.fail(500, t.getMessage());
        }
    }

    @ApiOperation("获取scv应用域名")
    @GetMapping("/scv_domain")
    public Response<String> getScvDomain() {
        return adpCpcToolsController.getScvDomain();
    }

    @ApiOperation("请求资源并返回其md5")
    @GetMapping("/md5")
    public Response<String> downloadAndCalculateMd5(@ApiIgnore Context context,
                                                    @RequestParam("url") String url) {
        return adpCpcToolsController.downloadAndCalculateMd5(context, url);
    }

    @Deprecated
    @ApiOperation("标题搜索")
    @GetMapping("/search_title")
    public Response<List<TitleSearchBo>> searchTitle(@ApiIgnore Context context,
                                                     @RequestParam(value = "keyword") String keyword,
                                                     @RequestParam(value = "game_base_id", required = false) Integer gameBaseId,
                                                     @RequestParam(value = "keyword_min_length", required = false, defaultValue = "1") Integer minLength,
                                                     @RequestParam(value = "keyword_max_length", required = false, defaultValue = "500") Integer maxLength,
                                                     @RequestParam(value = "size", required = false, defaultValue = "10") Integer size) {
        return Response.ok();
    }
}
