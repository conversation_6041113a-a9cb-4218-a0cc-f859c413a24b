package com.bilibili.adp.advertiser.portal.webapi.effect_ad;

import com.bapis.ad.jupiter.search.words.SearchWordAnalysisReportReply;
import com.bapis.ad.jupiter.search.words.SearchWordsAnalysisReportServiceGrpc;
import com.bapis.ad.jupiter.search.words.StatReq;
import com.bilibili.adp.advertiser.portal.common.BasicController;
import com.bilibili.adp.advertiser.portal.common.Context;
import com.bilibili.adp.advertiser.portal.webapi.effect_ad.converter.ProgramedCreativeAnalysisConverter;
import com.bilibili.adp.advertiser.portal.webapi.effect_ad.vo.SearchWordAnalysisChartVo;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.StringDateParser;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.biz.services.campaign.api.ICpcCampaignService;
import com.bilibili.adp.cpc.biz.services.creative.api.ICpcCreativeService;
import com.bilibili.adp.cpc.biz.services.creative.dto.CpcCreativeDto;
import com.bilibili.adp.cpc.biz.services.search.overt.bo.SearchKeyReportBo;
import com.bilibili.adp.cpc.biz.services.search.overt.bo.SearchKeyReportVo;
import com.bilibili.adp.cpc.biz.services.unit.api.ICpcUnitService;
import com.bilibili.adp.cpc.biz.services.unit.dto.QueryCpcUnitDto;
import com.bilibili.adp.cpc.dto.CpcLightUnitDto;
import com.bilibili.adp.cpc.enums.ad.CampaignAdType;
import com.bilibili.adp.cpc.enums.ad.OcpcTargetEnum;
import com.bilibili.adp.cpc.proxy.CpmJupiterProxy;
import com.bilibili.adp.cpc.utils.LocalDateUtils;
import com.bilibili.adp.cpc.utils.Response;
import com.bilibili.adp.cpc.utils.TimeUtils;
import com.bilibili.adp.cpc.biz.services.campaign.dto.CpcCampaignDto;
import com.bilibili.adp.cpc.biz.services.campaign.dto.QueryCpcCampaignDto;
import com.bilibili.adp.launch.api.creative.dto.QueryCpcCreativeDto;
import com.bilibili.adp.v6.report.SearchWordAnalysisReportService;
import com.bilibili.adp.v6.report.bo.ExportAnalysisExcelBo;
import com.bilibili.adp.v6.report.bo.ExportSearchWordAnalysisBo;
import com.bilibili.adp.web.framework.core.Pagination;
import com.bilibili.adp.web.framework.excel.ExcelUtils;
import com.bilibili.adp.web.framework.exception.WebApiExceptionCode;
import com.bilibili.crm.platform.common.IsValid;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import pleiades.venus.starter.rpc.client.RPCClient;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @Description
 * @date 4/1/24
 **/
@Slf4j
@RestController
@RequestMapping(value = "/web_api/v1/effect_ad/search_word_analysis_report")
@Api(value = "/effect_ad/search_word_analysis_report")
public class SearchWordAnalysisReportController extends BasicController {

    public static final String HOUR_SUFFIX = ":00:00";
    @Resource
    private ICpcUnitService iCpcUnitService;
    @Resource
    private ICpcCampaignService iCpcCampaignService;
    @Resource
    private ICpcCreativeService iCpcCreativeService;
    @Resource
    private CpmJupiterProxy cpmJupiterProxy;
    @Autowired
    private SearchWordAnalysisReportService searchWordAnalysisReportService;

    @ApiOperation(value = "搜索词报表列表数据")
    @GetMapping(value = "/detail")
    public Response<Pagination<List<SearchKeyReportVo>>> queryDetail(
            @ApiIgnore Context context,
            @RequestParam(value = "campaign_id", required = false) List<Integer> campaignIds,
            @RequestParam(value = "unit_id", required = false) List<Integer> unitIds,
            @RequestParam(value = "creative_id", required = false) List<Integer> creativeIds,
            @RequestParam(value = "time_type", required = false, defaultValue = "0") Integer timeType,
            @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
            @RequestParam(value = "size", required = false, defaultValue = "10") Integer size,
            @RequestParam(value = "from_time") Long fromTime,
            @RequestParam(value = "to_time") Long toTime,
            @ApiParam(value = " campaign 1 unit 2 creative 3") @RequestParam(value = "group_by", required = false, defaultValue = "0") Integer groupBy,
            @ApiParam(value = " pv cost click convCnt") @RequestParam(value = "sort_field", required = false, defaultValue = "") String sortField,
            @ApiParam(value = "key_word_group_by  拆分0、不拆分1 ") @RequestParam(value = "key_word_group_by", required = false, defaultValue = "0") Integer keyWordGroupBy,
            @RequestParam(value = "location_type", required = false, defaultValue = "0") Integer locationType,
            @RequestParam(value = "sort_type", required = false, defaultValue = "DESC") @ApiParam("ASC-正序, DESC-倒序") String sortType) {

        StatReq.Builder reqBuilder = StatReq.newBuilder()
                .setAccountId(context.getAccountId())
                .setBeginTime(fromTime)
                .setEndTime(toTime)
                .setGroupBy(GroupByEnum.getById(groupBy).getName())
                .setPage(page)
                .setPageSize(size)
                .setKeyWordGroupBy(keyWordGroupBy)
                .setSortField(sortField)
                .setSortType(sortType)
                .setLocationType(locationType)
                .setTimeType(StatReq.TimeType.forNumber(timeType));

        if (!CollectionUtils.isEmpty(campaignIds)) {
            reqBuilder.addAllCampaignIds(campaignIds);
        }

        if (!CollectionUtils.isEmpty(unitIds)) {
            reqBuilder.addAllUnitIds(unitIds);
        }

        if (!CollectionUtils.isEmpty(creativeIds)) {
            reqBuilder.addAllCreativeIds(creativeIds);
        }

        SearchWordAnalysisReportReply reportReply = cpmJupiterProxy.list(reqBuilder.build());
        List<SearchKeyReportBo> reportBos = ProgramedCreativeAnalysisConverter.MAPPER.grpcsToBos(reportReply.getDataList());

        List<SearchKeyReportBo> bos = buildHourDate(reportBos, fromTime, toTime, timeType);
        return Response.ok(new Pagination<>(page, reportReply.getTotal(),
                ProgramedCreativeAnalysisConverter.MAPPER.searchKeyBoToVo(enhanceField(bos, fromTime, toTime, timeType))));
    }

    @ApiOperation(value = "搜索词报表折线图")
    @GetMapping("/chart")
    public Response<SearchWordAnalysisChartVo> chart(
            @ApiIgnore Context context,
            @RequestParam(value = "campaign_id", required = false) List<Integer> campaignIds,
            @RequestParam(value = "unit_id", required = false) List<Integer> unitIds,
            @RequestParam(value = "creative_id", required = false) List<Integer> creativeIds,
            @RequestParam(value = "time_type", required = false, defaultValue = "2") Integer timeType,
            @RequestParam(value = "from_time") Long fromTime,
            @RequestParam(value = "to_time") Long toTime
    ) {
        //折线图时间范围超过一天 按天聚合
        if (toTime - fromTime > TimeUtils.DAY_TIME_MILLS) {
            timeType = StatReq.TimeType.DAY_VALUE;
        } else {
            timeType = StatReq.TimeType.HOUR_VALUE;
        }

        StatReq.Builder reqBuilder = StatReq.newBuilder()
                .setAccountId(context.getAccountId())
                .setBeginTime(fromTime)
                .setEndTime(toTime)
                .setTimeType(StatReq.TimeType.forNumber(timeType));

        if (!CollectionUtils.isEmpty(campaignIds)) {
            reqBuilder.addAllCampaignIds(campaignIds);
        }

        if (!CollectionUtils.isEmpty(unitIds)) {
            reqBuilder.addAllUnitIds(unitIds);
        }

        if (!CollectionUtils.isEmpty(creativeIds)) {
            reqBuilder.addAllCreativeIds(creativeIds);
        }

        SearchWordAnalysisReportReply reportReply = cpmJupiterProxy.chart(reqBuilder.build());
        List<SearchKeyReportBo> reportBoList = ProgramedCreativeAnalysisConverter.MAPPER.grpcsToBos(reportReply.getDataList());


        List<String> xaxis = buildXaxis(fromTime, toTime);
        reportBoList = chartVosFill(fromTime, toTime, reportBoList, xaxis);
        List<SearchKeyReportBo> bos = buildHourDate(reportBoList, fromTime, toTime, timeType);
        List<SearchKeyReportVo> details = ProgramedCreativeAnalysisConverter.MAPPER.searchKeyBoToVo(enhanceField(bos, fromTime, toTime, timeType));
        return Response.ok(SearchWordAnalysisChartVo.builder()
                .details(details)
                .total(ProgramedCreativeAnalysisConverter.MAPPER.searchKeyBoToVo(calTotal(reportBoList)))
                .xaxis(xaxis)
                .cost(details.stream().map(SearchKeyReportVo::getCost).filter(Objects::nonNull).collect(Collectors.toList()))
                .pv(details.stream().map(SearchKeyReportVo::getPv).filter(Objects::nonNull).collect(Collectors.toList()))
                .click(details.stream().map(SearchKeyReportVo::getClick).filter(Objects::nonNull).collect(Collectors.toList()))
                .convCnt(details.stream().map(SearchKeyReportVo::getConvCnt).filter(Objects::nonNull).collect(Collectors.toList()))
                .convDeepCnt(details.stream().map(SearchKeyReportVo::getConvDeepCnt).filter(Objects::nonNull).collect(Collectors.toList()))
                .averageCostPerThousand(details.stream().map(SearchKeyReportVo::getAverageCostPerThousand).filter(Objects::nonNull).collect(Collectors.toList()))
                .ctr(details.stream().map(SearchKeyReportVo::getCtr).filter(Objects::nonNull).collect(Collectors.toList()))
                .costPerClick(details.stream().map(SearchKeyReportVo::getCostPerClick).filter(Objects::nonNull).collect(Collectors.toList()))
                .costPerConv(details.stream().map(SearchKeyReportVo::getCostPerConv).filter(Objects::nonNull).collect(Collectors.toList()))
                .build());
    }

    private SearchKeyReportBo calTotal(List<SearchKeyReportBo> reportBoList) {
        SearchKeyReportBo total = SearchKeyReportBo.builder()
                .cost(reportBoList.stream().map(SearchKeyReportBo::getCost).filter(Objects::nonNull).reduce(Float::sum).orElse(0F))
                .pv(reportBoList.stream().map(SearchKeyReportBo::getPv).filter(Objects::nonNull).reduce(Long::sum).orElse(0L))
                .click(reportBoList.stream().map(SearchKeyReportBo::getClick).filter(Objects::nonNull).reduce(Long::sum).orElse(0L))
                .convCnt(reportBoList.stream().map(SearchKeyReportBo::getConvCnt).filter(Objects::nonNull).reduce(Integer::sum).orElse(0))
                .build();
        total.setCostPerConv((double) (total.getCost() / total.getConvCnt()));
        total.setCostPerClick((double) total.getCost() / total.getClick());
        total.setAverageCostPerThousand((double) total.getCost() * 1000 / total.getPv());
        total.setCtr((double) total.getClick() / total.getPv());
        return total;
    }

    @ApiOperation(value = "搜索词报表导出")
    @GetMapping("/export")
    public void export(
            @ApiIgnore Context context,
            @RequestParam(value = "campaign_id", required = false) List<Integer> campaignIds,
            @RequestParam(value = "unit_id", required = false) List<Integer> unitIds,
            @RequestParam(value = "creative_id", required = false) List<Integer> creativeIds,
            @RequestParam(value = "time_type", required = false, defaultValue = "0") Integer timeType,
            @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
            @RequestParam(value = "size", required = false, defaultValue = "10") Integer size,
            @RequestParam(value = "from_time") Long fromTime,
            @RequestParam(value = "to_time") Long toTime,
            @ApiParam(value = "campaign 1 unit 2 creative 3") @RequestParam(value = "group_by", required = false, defaultValue = "0") Integer groupBy,
            @ApiParam(value = " pv cost click convCnt") @RequestParam(value = "sort_field", required = false, defaultValue = "pv") String sortField,
            @ApiParam(value = "key_word_group_by  拆分0、不拆分1 ") @RequestParam(value = "key_word_group_by", required = false, defaultValue = "0") Integer keyWordGroupBy,
            @RequestParam(value = "location_type", required = false, defaultValue = "0") Integer locationType,
            @RequestParam(value = "sort_type", required = false, defaultValue = "1") @ApiParam("ASC-正序, DESC-倒序") String sortType,
            HttpServletResponse response
    ) throws ServiceException {


        try (ServletOutputStream outputStream = response.getOutputStream()) {

            ExportSearchWordAnalysisBo export = ExportSearchWordAnalysisBo.builder()
                    .accountId(context.getAccountId())
                    .campaignIds(campaignIds)
                    .unitIds(unitIds)
                    .creativeIds(creativeIds)
                    .timeType(timeType)
                    .page(1)
                    .size(100000)
                    .fromTime(fromTime)
                    .toTime(toTime)
                    .groupBy(groupBy)
                    .sortField(sortField)
                    .keyWordGroupBy(keyWordGroupBy)
                    .sortType(sortType)
                    .locationType(locationType)
                    .build();

            searchWordAnalysisReportService.export(export, outputStream);

            String dateRange = StringDateParser.getDateString(new Date(fromTime))
                    + "~" + StringDateParser.getDateString(new Date(toTime));
            final String fileName = "搜索词分析报表" + dateRange + ".xlsx";
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName);
        } catch (Exception e) {
            log.error("exportSearchWordAnalysisReport error", e);
            throw new ServiceException(WebApiExceptionCode.SYSTEM_ERROR);
        }


        //StatReq.Builder reqBuilder = StatReq.newBuilder()
        //        .setAccountId(context.getAccountId())
        //        .setBeginTime(fromTime)
        //        .setEndTime(toTime)
        //        .setGroupBy(GroupByEnum.getById(groupBy).getName())
        //        .setPage(page)
        //        .setPageSize(100000)
        //        .setKeyWordGroupBy(keyWordGroupBy)
        //        .setSortField(sortField)
        //        .setSortType(sortType)
        //        .setTimeType(StatReq.TimeType.forNumber(timeType));
        //
        //if (!CollectionUtils.isEmpty(campaignIds)) {
        //    reqBuilder.addAllCampaignIds(campaignIds);
        //}
        //
        //if (!CollectionUtils.isEmpty(unitIds)) {
        //    reqBuilder.addAllUnitIds(unitIds);
        //}
        //
        //if (!CollectionUtils.isEmpty(creativeIds)) {
        //    reqBuilder.addAllCreativeIds(creativeIds);
        //}
        //
        //SearchWordAnalysisReportReply reportReply = serviceWordBlockingStub.list(reqBuilder.build());
        //List<SearchKeyReportBo> reportBos = ProgramedCreativeAnalysisConverter.MAPPER.grpcsToBos(reportReply.getDataList());
        //
        //String dateRange = StringDateParser.getDateString(new Date(fromTime))
        //        + "~" + StringDateParser.getDateString(new Date(toTime));
        //
        //final String fileName = "搜索词分析报表" + dateRange + ".xlsx";
        //List<SearchKeyReportBo> bos = buildHourDate(reportBos, fromTime, toTime, timeType);
        //ExcelUtils.exportExcelOpt(SearchKeyReportVo.class,
        //        ProgramedCreativeAnalysisConverter.MAPPER.searchKeyBoToVo(changeGroupByVal(enhanceField(bos, fromTime, toTime, timeType), groupBy, keyWordGroupBy, timeType)),
        //        fileName, response);
    }

    private List<SearchKeyReportBo> chartVosFill(Long fromTime, Long toTime, List<SearchKeyReportBo> vos, List<String> xaxis) {

        if (toTime - fromTime <= TimeUtils.DAY_TIME_MILLS) {
            List<String> hours = vos.stream().map(SearchKeyReportBo::getLogHour).collect(Collectors.toList());

            for (int i = 0; i < 24; i++) {
                if (!hours.contains(String.format("%02d", i))) {
                    SearchKeyReportBo searchKeyReportBo = SearchKeyReportBo.emptyInstance(LocalDateUtils.formatTime(fromTime, "yyyy-MM-dd"), "yyyy-MM-dd");
                    searchKeyReportBo.setLogHour(String.format("%02d", i));
                    vos.add(searchKeyReportBo);
                }
            }
            vos = vos.stream().sorted(Comparator.comparing(r -> Integer.parseInt(r.getLogHour()))).collect(Collectors.toList());
            return vos;
        }

        List<String> dateStrs = vos.stream().map(r -> LocalDateUtils.formatTime(r.getDate().getTime(), "yyyy-MM-dd")).collect(Collectors.toList());
        for (int i = 0; i < xaxis.size(); i++) {
            if (!dateStrs.contains(xaxis.get(i))) {
                vos.add(i, SearchKeyReportBo.emptyInstance(xaxis.get(i), "yyyy-MM-dd"));
            }
        }
        vos = vos.stream().sorted(Comparator.comparing(SearchKeyReportBo::getDate)).collect(Collectors.toList());
        return vos;
    }

    private List<SearchKeyReportBo> buildHourDate(List<SearchKeyReportBo> bos, Long from, Long to, Integer timeType) {
        if (to - from > TimeUtils.DAY_TIME_MILLS) {
            return bos;
        }
        for (SearchKeyReportBo searchKeyReportBo : bos) {
            long hourDate = searchKeyReportBo.getDate().getTime() + Integer.parseInt(searchKeyReportBo.getLogHour()) * 3600 * 1000L;
            if (null != timeType && com.bapis.ad.report.StatReq.TimeType.HOUR.getNumber() == timeType) {
                searchKeyReportBo.setDate(new Timestamp(hourDate));
            }
        }
        return bos;
    }

    private List<String> buildXaxis(Long fromTime, Long toTime) {
        if (toTime - fromTime < TimeUtils.DAY_TIME_MILLS) {
            List<String> hourAxis = new ArrayList<>();
            for (int i = 0; i < 24; i++) {
                hourAxis.add(LocalDateUtils.formatTime(fromTime + i * 3600 * 1000L, "yyyy-MM-dd HH:mm:ss"));
            }
            return hourAxis;
        }
        return TimeUtils.genDateDayList(fromTime, toTime, "yyyy-MM-dd");
    }


    private List<SearchKeyReportBo> enhanceField(List<SearchKeyReportBo> bos, Long fromTime, Long toTime, Integer timeType) {
        String allTimeStr = null;
        if (null != timeType && com.bapis.ad.report.StatReq.TimeType.ALL.getNumber() == timeType) {
            String fromTimeStr = LocalDateUtils.formatTime(fromTime, LocalDateUtils.DATE_PATTERN);
            String toTimeStr = LocalDateUtils.formatTime(toTime, LocalDateUtils.DATE_PATTERN);
            allTimeStr = fromTimeStr + "~" + toTimeStr;
        }

        List<Integer> campaignIds = bos.stream().map(SearchKeyReportBo::getCampaignId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<Integer> unitIds = bos.stream().map(SearchKeyReportBo::getUnitId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<Integer> creativeIds = bos.stream().map(SearchKeyReportBo::getCreativeId).filter(Objects::nonNull).distinct().collect(Collectors.toList());

        Map<Integer, CpcCampaignDto> campaignDtoMap = new HashMap<>();
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(campaignIds)) {
            List<CpcCampaignDto> cpcCampaignDtos = iCpcCampaignService.queryCpcCampaign(QueryCpcCampaignDto.builder().campaignIds(campaignIds).build());
            campaignDtoMap = cpcCampaignDtos.stream().collect(Collectors.toMap(CpcCampaignDto::getCampaignId, Function.identity()));
        }

        Map<Integer, CpcLightUnitDto> unitDtoMap = new HashMap<>();
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(unitIds)) {
            List<CpcLightUnitDto> cpcLightUnitDtos = iCpcUnitService.queryLightUnits(QueryCpcUnitDto.builder().unitIds(unitIds).build());
            unitDtoMap = cpcLightUnitDtos.stream().collect(Collectors.toMap(CpcLightUnitDto::getUnitId, Function.identity()));
        }

        Map<Integer, CpcCreativeDto> creativeDtoMap = new HashMap<>();
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(creativeIds)) {
            List<CpcCreativeDto> cpcCreativeDtos = iCpcCreativeService.querySimpleCreativeS(QueryCpcCreativeDto.builder().creativeIds(creativeIds).build());
            creativeDtoMap = cpcCreativeDtos.stream().collect(Collectors.toMap(CpcCreativeDto::getCreativeId, Function.identity()));
        }

        for (SearchKeyReportBo reportBo : bos) {

            reportBo.setCampaignName(campaignDtoMap.getOrDefault(reportBo.getCampaignId(), CpcCampaignDto.builder().build()).getCampaignName());
            reportBo.setUnitName(unitDtoMap.getOrDefault(reportBo.getUnitId(), CpcLightUnitDto.builder().build()).getUnitName());
            reportBo.setCreativeName(creativeDtoMap.getOrDefault(reportBo.getCreativeId(), CpcCreativeDto.builder().build()).getCreativeName());
//                reportBo.setDateStr(Utils.getTimestamp2String(new Timestamp(reportBo.getDate().getTime()), "yyyy-MM-dd HH:mm:ss"));
            reportBo.setDateStr(Utils.getTimestamp2String(new Timestamp(reportBo.getDate().getTime()), "yyyy-MM-dd"));
            reportBo.setLogHour(reportBo.getLogHour() + HOUR_SUFFIX);

            if (StringUtils.isNotBlank(allTimeStr)) {
                reportBo.setDateStr(allTimeStr);
            }
            reportBo.setOcpcTargetName(!Utils.isPositive(reportBo.getOcpcTarget()) ? "" : OcpcTargetEnum.getByCode(reportBo.getOcpcTarget()).getDesc());
            reportBo.setOcpxTargetTwoName(!Utils.isPositive(reportBo.getOcpxTargetTwo()) ? "" : OcpcTargetEnum.getByCode(reportBo.getOcpxTargetTwo()).getDesc());
            reportBo.setIsSearchKuaitouName(null == reportBo.getIsSearchKuaitou() ? "未知" : IsValid.getByCode(reportBo.getIsSearchKuaitou()).getDesc());
            reportBo.setAdSearchTypeName(CampaignAdType.getByCodeWithDefaultDesc(reportBo.getAdSearchType()));
        }

        return bos;
    }

    private List<SearchKeyReportBo> changeGroupByVal(List<SearchKeyReportBo> bos, Integer groupBy, Integer keyWordGroupBy, Integer timeType) {
        GroupByEnum groupByEnum = GroupByEnum.getById(groupBy);
        for (SearchKeyReportBo bo : bos) {
            switch (groupByEnum) {
                case CAMPAIGN:
                    bo.setUnitId(null);
                    bo.setUnitName(null);
                    bo.setCreativeName(null);
                    bo.setCreativeId(null);
                    break;
                case UNIT:
                    bo.setCreativeName(null);
                    bo.setCreativeId(null);
                    break;
                default:
                    break;
            }
            if (IsValid.TRUE.getCode().equals(keyWordGroupBy)) {
                bo.setSearchKey(null);
            }

            if (timeType != StatReq.TimeType.HOUR.getNumber()) {
                bo.setLogHour(null);
            }
        }
        return bos;
    }


    private enum GroupByEnum {
        /**
         *
         */
        CAMPAIGN(1, "campaign"),

        /**
         *
         */
        UNIT(2, "unit"),

        /**
         *
         */
        CREATIVE(3, "creative"),

        /**
         * 全部
         */
        ALL(0, "all"),

        ;

        private Integer id;

        /**
         * 名称
         */
        private String name;

        public String getName() {
            return name;
        }

        public Integer getId() {
            return id;
        }

        GroupByEnum(Integer id, String name) {
            this.id = id;
            this.name = name;
        }

        static GroupByEnum getByName(String name) {
            for (GroupByEnum value : GroupByEnum.values()) {
                if (value.getName().equals(name)) {
                    return value;
                }
            }
            throw new RuntimeException("unknown name" + name);
        }

        static GroupByEnum getById(Integer id) {
            for (GroupByEnum value : GroupByEnum.values()) {
                if (value.getId().equals(id)) {
                    return value;
                }
            }
            throw new RuntimeException("unknown id" + id);
        }
    }

}
