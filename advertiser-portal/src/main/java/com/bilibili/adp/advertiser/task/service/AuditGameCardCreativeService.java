package com.bilibili.adp.advertiser.task.service;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.common.DbTable;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.exception.ServiceRuntimeException;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.biz.converter.game.IGameConverter;
import com.bilibili.adp.cpc.biz.services.creative.CpcCreativeServiceDelegate;
import com.bilibili.adp.cpc.biz.services.creative.dto.CpcCreativeDto;
import com.bilibili.adp.cpc.core.LaunchCreativeService;
import com.bilibili.adp.cpc.dao.querydsl.pos.LauBilibiliGamePo;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauCreativeImagePo;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauUnitCreativePo;
import com.bilibili.adp.cpc.databus.CreativeAuditDatabusPub;
import com.bilibili.adp.cpc.enums.AuditStatus;
import com.bilibili.adp.cpc.enums.ad.PromotionPurposeType;
import com.bilibili.adp.launch.api.common.CreativeStatus;
import com.bilibili.adp.launch.api.creative.dto.CpcCreativeAuditDto;
import com.bilibili.adp.launch.biz.bean.audit.AuditCreativeMessage;
import com.bilibili.adp.log.service.LogOperateService;
import com.bilibili.adp.passport.api.dto.GameDto;
import com.bilibili.adp.passport.api.service.IGameCenterService;
import com.bilibili.bjcom.querydsl.BaseQueryFactory;
import com.bilibili.crm.platform.common.IsValid;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.bilibili.adp.cpc.dao.querydsl.QLauBilibiliGame.lauBilibiliGame;
import static com.bilibili.adp.cpc.dao.ad_core.querydsl.QLauCreativeImage.lauCreativeImage;
import static com.bilibili.adp.cpc.dao.config.PlatformMySqlConfig.*;
import static com.bilibili.adp.cpc.dao.ad_core.querydsl.QLauUnit.lauUnit;
import static com.bilibili.adp.cpc.dao.ad_core.querydsl.QLauUnitCreative.lauUnitCreative;
import static com.bilibili.adp.cpc.dao.ad_core.querydsl.QLauUnitGame.lauUnitGame;

@Slf4j
@Service
public class AuditGameCardCreativeService {
    private static final Integer MAX_UNIT_GAME_SIZE = 2000;
    private static final String REJECT_REASON = "游戏未上架";

    @Resource(name = AD_BQF)
    private BaseQueryFactory adBqf;

    private final IGameCenterService gameCenterService;
    private final LaunchCreativeService launchCreativeService;

    @Resource(name = "okHttpClient")
    private  OkHttpClient okHttpClient;
    private final CreativeAuditDatabusPub creativeAuditDatabusPub;
    private final LogOperateService logOperateService;
    private final CpcCreativeServiceDelegate creativeServiceDelegate;

    @Resource(name = AD_CORE_BQF)
    private BaseQueryFactory adCoreBqf;

    public AuditGameCardCreativeService(
        IGameCenterService gameCenterService,
        LaunchCreativeService launchCreativeService,
         CreativeAuditDatabusPub creativeAuditDatabusPub,
        LogOperateService logOperateService,
        CpcCreativeServiceDelegate creativeServiceDelegate) {
        this.gameCenterService = gameCenterService;
        this.launchCreativeService = launchCreativeService;
        this.creativeAuditDatabusPub = creativeAuditDatabusPub;
        this.logOperateService = logOperateService;
        this.creativeServiceDelegate = creativeServiceDelegate;
    }

    @Transactional(value = AD_CORE_TM, rollbackFor = Exception.class)
    public void refresh() {
        int maxId = 0;
        int currentSize = MAX_UNIT_GAME_SIZE;
        while (currentSize >= MAX_UNIT_GAME_SIZE) {
            List<LauBilibiliGamePo> launchGames = adBqf.selectFrom(lauBilibiliGame)
                .where(lauBilibiliGame.id.goe(maxId))
                .limit(MAX_UNIT_GAME_SIZE).fetch();
            currentSize = launchGames.size();
            maxId = launchGames.get(currentSize - 1).getId();

            launchGames.forEach(launchGame -> {
                // 游戏中心获取游戏信息
                log.info("AuditGameCardCreativeJob - game id:{}, platformType:{}", launchGame.getGameBaseId(), launchGame.getPlatformType());
                GameDto game = gameCenterService.getGameDtoByIdPlatform(launchGame.getGameBaseId(),
                        launchGame.getPlatformType());
                log.info("AuditGameCardCreativeJob - game info:{}", JSON.toJSONString(game));
                boolean needToSkip = Objects.isNull(game) || Objects.isNull(game.getGameIcon());
                if (needToSkip) {
                    return;
                }
                Integer isOnlineFromLocal = launchGame.getIsOnline();
                Integer isOnlineFromGameCenter = Boolean.TRUE.equals(game.getIsOnline()) ? 1 : 0;
                // 当状态未发生变更时，直接返回
                if (Objects.equals(isOnlineFromLocal, isOnlineFromGameCenter)) {
                    return;
                }
                log.info("AuditGameCardCreativeJob - game status changed, gameBaseId:{}, platformType:{}, isOnlineFromLocal:{}, isOnlineFromGameCenter:{}",
                    launchGame.getGameBaseId(), launchGame.getPlatformType(), isOnlineFromLocal, isOnlineFromGameCenter);

                // 更新游戏信息
                LauBilibiliGamePo existGamePo = adBqf.selectFrom(lauBilibiliGame)
                    .where(lauBilibiliGame.gameBaseId.eq(launchGame.getGameBaseId()))
                    .where(lauBilibiliGame.platformType.eq(launchGame.getPlatformType()))
                    .fetchFirst();
                LauBilibiliGamePo newGamePo = IGameConverter.MAPPER.gameDto2GamePo(
                    launchGame.getPlatformType(), existGamePo.getId(), game);
                adBqf.update(lauBilibiliGame).updateBean(newGamePo);

                log.info("AuditGameCardCreativeJob - lauBilibiliGame update, gameBaseId:{}, platformType:{}", launchGame.getGameBaseId(), launchGame.getPlatformType());

                //查询使用到游戏的单元
                List<Integer> gameUnitIds = adCoreBqf.select(lauUnitGame.unitId)
                    .from(lauUnitGame)
                    .where(lauUnitGame.gameBaseId.eq(launchGame.getGameBaseId()))
                    .where(lauUnitGame.platformType.eq(launchGame.getPlatformType()))
                    .fetch();
                // 由于使用到游戏的单元 包含「安卓游戏」+ 「游戏卡」
                // 本次只处理游戏卡单元
                List<Integer> gameCardUnitIds = adCoreBqf.select(lauUnit.unitId)
                    .from(lauUnit)
                    .where(
                        lauUnit.promotionPurposeType.eq(PromotionPurposeType.GAME_CARD.getCode()))
                    .where(lauUnit.unitId.in(gameUnitIds)).fetch();
                List<LauUnitCreativePo> creatives = adCoreBqf.selectFrom(lauUnitCreative)
                    .where(lauUnitCreative.unitId.in(gameCardUnitIds))
                    .fetch();

                if (Boolean.TRUE.equals(game.getIsOnline())) {
                    log.info("AuditGameCardCreativeJob - game status from offline to online, gameBaseId:{}, platformType:{}", launchGame.getGameBaseId(), launchGame.getPlatformType());
                    String iconUrl = game.getGameIcon();
                    String iconMd5 = StringUtils.EMPTY;
                    final Request request = new Request.Builder()
                        .header(HttpHeaders.CONNECTION, "close")
                        // 实属无奈
                        .url(iconUrl.replace("https", "http"))
                        .get()
                        .build();
                    try (final okhttp3.Response response = okHttpClient.newCall(request).execute()) {
                        if (Objects.equals(HttpStatus.OK.value(), response.code())
                            && Objects.nonNull(response.body())) {
                            final byte[] bytes = response.body().bytes();
                            iconMd5 = DigestUtils.md5Hex(bytes);

                        }
                    } catch (IOException e) {
                        log.error("AuditGameCardCreativeJob - 请求url失败", e);
                        throw new ServiceRuntimeException("请求url失败");
                    }

                    String finalIconMd5 = iconMd5;
                    // 更新创意信息
                    creatives.forEach(creative -> {
                            //创意状态审核中
                            creative.setCreativeStatus(CreativeStatus.AUDITING.getCode());
                            // 审核状态 待审核
                            creative.setAuditStatus(AuditStatus.WAITING.getCode());
                            // 更新原因
                            creative.setReason(StringUtils.EMPTY);
                            // 更新创意描述
                            creative.setDescription(game.getGameTags());
                            // 更新推广内容，拼接归因参数
                            creative.setPromotionPurposeContent(game.getGameLink() + "&sourceFrom=1000100183");
                            // 创意图片
                            creative.setImageUrl(iconUrl);
                            // 创意图片 md5
                            creative.setImageMd5(finalIconMd5);
                            creative.setMtime(Utils.getNow());
                        }
                    );
                    adCoreBqf.update(lauUnitCreative).updateBeans(creatives);
                    // 记录日志
                    addCreativeReAuditLog(creatives);

                    // 更新创意图片
                    List<Integer> needToPushAuditCreativeIds = creatives.stream()
                        .map(LauUnitCreativePo::getCreativeId)
                        .collect(Collectors.toList());
                    List<LauCreativeImagePo> creativeImages = adCoreBqf.selectFrom(lauCreativeImage)
                        .where(lauCreativeImage.creativeId.in(needToPushAuditCreativeIds))
                        .fetch(LauCreativeImagePo.class);
                    creativeImages.forEach(image -> {
                            image.setImageUrl(iconUrl);
                            image.setImageMd5(finalIconMd5);
                            image.setMtime(Utils.getNow());
                        }
                    );
                    adCoreBqf.update(lauCreativeImage).updateBeans(creativeImages);
                    //推审
                    try {
                        log.info("AuditGameCardCreativeJob - 必选系统创意-{}，推审", needToPushAuditCreativeIds);
                        Cat.logEvent("auditCreativePub",
                            "creativeIds:" + needToPushAuditCreativeIds);
                        AuditCreativeMessage auditCreativeMessage = AuditCreativeMessage.builder().creativeId(needToPushAuditCreativeIds)
                                .build();

                        creativeAuditDatabusPub.pubMsg(auditCreativeMessage);
                    } catch (Exception e) {
                        log.error("AuditGameCardCreativeJob - 必选系统创意-{}，推审失败,Exp:{}", needToPushAuditCreativeIds, e);
                    }
                } else {
                    List<Integer> rejectCreativeIds = creatives.stream().map(LauUnitCreativePo::getCreativeId).collect(Collectors.toList());
                    log.info("AuditGameCardCreativeJob - game status from online to offline, gameBaseId:{}, platformType:{}, rejectCreativeIds:{}", launchGame.getGameBaseId(), launchGame.getPlatformType(), rejectCreativeIds);
                    // 拒审
                    creatives.forEach(
                        creative -> {
                            if (CreativeStatus.VALID_CREATIVE_STATUS_LIST.contains(
                                creative.getCreativeStatus())) {
                                launchCreativeService.auditReject(CpcCreativeAuditDto.builder()
                                    .creativeId(creative.getCreativeId())
                                    .reason(REJECT_REASON)
                                    .version(creative.getVersion())
                                    .operator(Operator.SYSTEM)
                                    .reCheckFlag(IsValid.FALSE.getCode())
                                    .build());
                            }
                        }
                    );
                }
            });
        }
    }

    private void addCreativeReAuditLog(List<LauUnitCreativePo> creatives) {
        creatives.forEach(creative -> {
            CpcCreativeDto logDto = new CpcCreativeDto();
            logDto.setAuditStatus(AuditStatus.WAITING.getCode());
            logDto.setReason("游戏卡创意-游戏上架，关联创意送审");
            logOperateService.addBatchUpdateStatusLog(
                    DbTable.LAU_UNIT_CREATIVE,
                    Operator.SYSTEM,
                    Lists.newArrayList(creativeServiceDelegate.creativeDtoToOperationDto(logDto,null)),
                    Lists.newArrayList(creative.getCreativeId()), Lists.newArrayList(creative.getAccountId())
            );
        });

    }
}
