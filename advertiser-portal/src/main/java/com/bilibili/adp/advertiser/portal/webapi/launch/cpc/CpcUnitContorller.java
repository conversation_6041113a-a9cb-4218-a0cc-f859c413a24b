package com.bilibili.adp.advertiser.portal.webapi.launch.cpc;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bapis.archive.service.Arc;
import com.bapis.community.interfaces.tag.Tag;
import com.bilibili.adp.account.dto.AccountAllInfoDto;
import com.bilibili.adp.account.dto.AccountBaseDto;
import com.bilibili.adp.account.service.IQueryAccountService;
import com.bilibili.adp.advertiser.portal.common.*;
import com.bilibili.adp.advertiser.portal.converter.UnitKeywordConverter;
import com.bilibili.adp.advertiser.portal.service.LaunchCommonService;
import com.bilibili.adp.advertiser.portal.service.launch.BvidSwitchService;
import com.bilibili.adp.advertiser.portal.service.launch.LimitBsiType;
import com.bilibili.adp.advertiser.portal.service.launch.WebAppPackageService;
import com.bilibili.adp.advertiser.portal.service.openapi.OpenAPILimitType;
import com.bilibili.adp.advertiser.portal.service.openapi.OpenAPIValidator;
import com.bilibili.adp.advertiser.portal.service.openapi.OpenApiFlyValidator;
import com.bilibili.adp.advertiser.portal.service.unit.TargetLowestBidProc;
import com.bilibili.adp.advertiser.portal.service.unit.UnitSlotGroupComponent;
import com.bilibili.adp.advertiser.portal.webapi.launch.cpc.common.UpdateStatusRequestBean;
import com.bilibili.adp.advertiser.portal.webapi.launch.cpc.converter.CpcProductConverter;
import com.bilibili.adp.advertiser.portal.webapi.launch.cpc.converter.game.GameConverter;
import com.bilibili.adp.advertiser.portal.webapi.launch.cpc.converter.unit.GameCardUnitTargetConverter;
import com.bilibili.adp.advertiser.portal.webapi.launch.cpc.converter.unit.TargetPackageUpgradeConvertor;
import com.bilibili.adp.advertiser.portal.webapi.launch.cpc.vo.*;
import com.bilibili.adp.advertiser.portal.webapi.launch.cpc.vo.converter.UnitConverter;
import com.bilibili.adp.advertiser.portal.webapi.resource.vo.BusinessInterestVo;
import com.bilibili.adp.advertiser.portal.webapi.resource.vo.CrowdPackVo;
import com.bilibili.adp.common.Constants;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.TargetRule;
import com.bilibili.adp.common.enums.*;
import com.bilibili.adp.common.enums.professional_fly.RecommendTypeEnum;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.exception.ServiceRuntimeException;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.biz.bos.archive.PgcArchiveBo;
import com.bilibili.adp.cpc.biz.bos.goods.GoodsArchiveHintProc;
import com.bilibili.adp.cpc.biz.bos.unit.LauUnitBusinessCategoryBo;
import com.bilibili.adp.cpc.biz.bos.unit.QueryUnitBo;
import com.bilibili.adp.cpc.biz.bos.unit.targets.LauStartUpCrowdsBo;
import com.bilibili.adp.cpc.biz.services.AdpCpcLauStartUpCrowdsService;
import com.bilibili.adp.cpc.biz.services.account.budget.DoBudgetCheckUtils;
import com.bilibili.adp.cpc.biz.services.app.AppPackageService;
import com.bilibili.adp.cpc.biz.services.archive.ArchiveService;
import com.bilibili.adp.cpc.biz.services.campaign.api.ICpcCampaignService;
import com.bilibili.adp.cpc.biz.services.campaign.api.ILauCampaignService;
import com.bilibili.adp.cpc.biz.services.campaign.dto.CpcCampaignDto;
import com.bilibili.adp.cpc.biz.services.creative.AdpCpcCreativeService;
import com.bilibili.adp.cpc.biz.services.creative.api.ICpcCreativeService;
import com.bilibili.adp.cpc.biz.services.creative.bos.LauCreativeBusinessCategoryBo;
import com.bilibili.adp.cpc.biz.services.creative.config.AccLabelConfig;
import com.bilibili.adp.cpc.biz.services.game.AdpCpcGameService;
import com.bilibili.adp.cpc.biz.services.goods.PromotionPurposeDescProc;
import com.bilibili.adp.cpc.biz.services.live.AdpCpcLiveRoomService;
import com.bilibili.adp.cpc.biz.services.live.bos.LiveReservationInfoBo;
import com.bilibili.adp.cpc.biz.services.misc.AdpCpcOcpxService;
import com.bilibili.adp.cpc.biz.services.misc.bos.LauOcpxConfigBo;
import com.bilibili.adp.cpc.biz.services.misc.bos.OcpxTargetBo;
import com.bilibili.adp.cpc.biz.services.recommend.bos.AdStatSearchWordBo;
import com.bilibili.adp.cpc.biz.services.resource.AdpCpcResourceService;
import com.bilibili.adp.cpc.biz.services.search_ad_unit.EffectAdSearchAdUnitService;
import com.bilibili.adp.cpc.biz.services.search_ad_unit.dto.UnitKeywordsBo;
import com.bilibili.adp.cpc.biz.services.tag.TagService;
import com.bilibili.adp.cpc.biz.services.target_package.api.ICrowdPackService;
import com.bilibili.adp.cpc.biz.services.target_package.api.IResTargetItemService;
import com.bilibili.adp.cpc.biz.services.target_package.api.upgrade.IResTargetPackageUpgradeService;
import com.bilibili.adp.cpc.biz.services.target_package.api.upgrade.dto.TargetPackageUpgradeDto;
import com.bilibili.adp.cpc.biz.services.unit.*;
import com.bilibili.adp.cpc.biz.services.unit.api.ICpcUnitService;
import com.bilibili.adp.cpc.biz.services.unit.api.ILauUnitService;
import com.bilibili.adp.cpc.biz.services.unit.bos.DynamicBo;
import com.bilibili.adp.cpc.biz.services.unit.bos.GoodsBo;
import com.bilibili.adp.cpc.biz.services.unit.dto.*;
import com.bilibili.adp.cpc.biz.validator.CpcUnitValidator;
import com.bilibili.adp.cpc.core.LaunchUnitAssistSearchService;
import com.bilibili.adp.cpc.core.LaunchUnitV1Service;
import com.bilibili.adp.cpc.core.bos.ContentDetailBo;
import com.bilibili.adp.cpc.core.constants.IsInterestAuto;
import com.bilibili.adp.cpc.core.constants.OcpxTarget;
import com.bilibili.adp.cpc.core.promotion.PromotionContentDetailFacade;
import com.bilibili.adp.cpc.dto.CpcLightUnitDto;
import com.bilibili.adp.cpc.dto.DpaShopGoodsDto;
import com.bilibili.adp.cpc.dto.NewBatchUpdateCpcUnitBudgetDto;
import com.bilibili.adp.cpc.dto.UnitForecastDto;
import com.bilibili.adp.cpc.enums.AdpVersion;
import com.bilibili.adp.cpc.enums.YesOrNoEnum;
import com.bilibili.adp.cpc.enums.ad.CampaignAdType;
import com.bilibili.adp.cpc.enums.ad.OcpcTargetEnum;
import com.bilibili.adp.cpc.enums.ad.PromotionPurposeType;
import com.bilibili.adp.cpc.enums.ad.UnitDetailPromotionContentEnum;
import com.bilibili.adp.cpc.enums.target_package.AreaTypeEnum;
import com.bilibili.adp.cpc.repo.CampaignRepo;
import com.bilibili.adp.cpc.utils.TreeUtils;
import com.bilibili.adp.launch.api.campaign.dto.CampaignQueryDto;
import com.bilibili.adp.launch.api.common.*;
import com.bilibili.adp.launch.api.creative.dto.LauSubjectDto;
import com.bilibili.adp.launch.api.creative.dto.QueryCpcCreativeDto;
import com.bilibili.adp.launch.api.launch.dto.GetBidCostParam;
import com.bilibili.adp.launch.api.launch.dto.LauProductGroupDto;
import com.bilibili.adp.launch.api.minigame.dto.MiniGameMapDto;
import com.bilibili.adp.launch.api.minigame.dto.MiniGameTargetDto;
import com.bilibili.adp.launch.api.service.ILauProductGroupService;
import com.bilibili.adp.launch.api.unit.dto.UnitUpdateBidPriceQueryDto;
import com.bilibili.adp.launch.biz.common.LaunchUtil;
import com.bilibili.adp.launch.biz.pojo.LauCampaignPo;
import com.bilibili.adp.launch.biz.service.unit.LaunchUnitLowestBidService;
import com.bilibili.adp.legacy.LauCampaignDto;
import com.bilibili.adp.passport.api.dto.ArchiveBase;
import com.bilibili.adp.passport.api.dto.ArchiveDetail;
import com.bilibili.adp.passport.api.dto.GameDto;
import com.bilibili.adp.passport.api.dto.UserInfoDto;
import com.bilibili.adp.passport.api.service.IGameCenterService;
import com.bilibili.adp.passport.api.service.IPassportService;
import com.bilibili.adp.passport.biz.manager.ArchiveManager;
import com.bilibili.adp.passport.biz.manager.LiveBroadcastHttpService;
import com.bilibili.adp.passport.biz.manager.bean.LiveBroadcastRoomInfo;
import com.bilibili.adp.resource.api.account.label.IAccountLabelService;
import com.bilibili.adp.resource.api.app_package.dto.AppPackageDto;
import com.bilibili.adp.resource.api.app_package.dto.QueryAppPackageDto;
import com.bilibili.adp.resource.api.slot_group.IResSlotGroupService;
import com.bilibili.adp.resource.api.slot_group.QueryTemplateLaunchTypeMappingDto;
import com.bilibili.adp.resource.api.slot_group.ResSlotGroupBaseDto;
import com.bilibili.adp.resource.api.slot_group.ResSlotGroupTemplateMappingDto;
import com.bilibili.adp.resource.api.target_lau.dto.TargetTreeDto;
import com.bilibili.adp.resource.api.targetmeta.TargetType;
import com.bilibili.adp.web.framework.core.Response;
import com.bilibili.adp.web.framework.exception.WebApiExceptionCode;
import com.bilibili.bvid.BVIDUtils;
import com.bilibili.crm.platform.common.IsValid;
import com.bilibili.crm.platform.soa.ISoaAccountLabelService;
import com.bilibili.location.api.cardtype.dto.CreativeStyle;
import com.bilibili.location.api.common.TemplateUtils;
import com.bilibili.location.api.template.dto.TemplateDto;
import com.bilibili.location.api.template.dto.TemplateGroupBo;
import com.bilibili.report.platform.api.soa.ISoaStatUnitService;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Event;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.vavr.control.Try;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.bilibili.adp.cpc.constants.AccountLabelConstants.oldSanlianAllowEdit;
import static com.bilibili.adp.resource.api.targetmeta.TargetType.PROFESSION_INTEREST;
import static com.bilibili.adp.web.framework.exception.ExceptionUtils.errorMsgBuild;
import static com.bilibili.adp.web.framework.exception.enums.ErrorCodeEnum.*;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/web_api/v1/launch/cpc/unit")
@Api(value = "/cpc/unit", description = "cpc推广单元相关")
public class CpcUnitContorller extends BasicExportController {

    private static final Logger LOGGER = LoggerFactory.getLogger(CpcUnitContorller.class);

    // 旧版可以开智能起量的广告位组
    private static final Map<Integer, Set<Integer>> smartIncreaseMap = new HashMap<Integer, Set<Integer>>() {{
        put(PromotionPurposeType.LANDING_PAGE.getCode(), new HashSet<>(Arrays.asList(69, 70, 75, 80, 85, 103, 105, 135, 148, 166, 171, 186, 232, 235, 240, 243)));
        put(PromotionPurposeType.APP_DOWNLOAD.getCode(), new HashSet<>(Arrays.asList(85, 103, 108, 116, 120, 138, 148, 151, 152, 171, 173, 186)));
        put(PromotionPurposeType.SHOP_GOODS.getCode(), new HashSet<>(Collections.singletonList(105)));
        put(PromotionPurposeType.ON_SHELF_GAME.getCode(), new HashSet<>(Collections.singletonList(105)));
        put(PromotionPurposeType.LIVE_ROOM.getCode(), new HashSet<>(Arrays.asList(105, 80)));
        put(PromotionPurposeType.SALE_GOODS.getCode(), new HashSet<>(Arrays.asList(69, 70, 71, 75, 80, 85, 103, 105, 148, 171, 186, 188, 232, 243)));
        put(PromotionPurposeType.BRAND_VIDEO_PROMOTION.getCode(), new HashSet<>(80));
    }};

    // 粉丝关系: 包含
    private static final String INCLUDED_FANS = "included_fans";
    // 粉丝关系: 排除
    private static final String EXCLUDED_FANS = "excluded_fans";
    // 人群包: 包含
    private static final String INCLUDED_CROWD_PACKS = "included_crowd_packs";
    // 人群包: 排除
    private static final String EXCLUDED_CROWD_PACKS = "excluded_crowd_packs";
    // 视频分区兴趣
    private static final String VIDEO_SECOND_PARTITIONS = "video_second_partitions";
    // 系统推荐up主类型
    private static final String RECOMMEND_UPPER_TYPE = "recommend_upper_type";
    // 智能放量 扩展种子人群
    private static final String EXTRA_CROWD_PACKS = "extra_crowd_packs";
    // 操作系统版本
    private static final String OS_VERSION = "os_version";

    private static final String PROFESSION_AUTO_DESC = "行业优选";

    private static final String PROFESSION_AUTO = "profession_auto";
    // bili 客户端版本
    private static final String BILI_CLIENT_VERSION = "bili_client_version";

    @Value("#{'${platform.dpa.account.ids}'.split(',')}")
    private List<Integer> dpaAccountIds;

    @Value("#{'${cpc.bid.no.limit.account.ids:2325}'.split(',')}")
    private List<Integer> bidNoLimitAccountIds;
    @Value("${cpc.bid.no.limit.low.price:30}")
    private Integer noLimitLowBid;

    @Value("#{'${platform.unit.support.store.direct.launch.ocpc.targets}'.split(',')}")
    private List<Integer> supportStoreDirectLaunchOcpcTargets;

    @Value("#{'${platform.unit.support.store.direct.launch.ocpc.deep.targets}'.split(',')}")
    private List<Integer> supportStoreDirectLaunchOcpcDeepTargets;

    @Value("#{'${platform.unit.support.store.direct.total.launch.ocpc.targets}'.split(',')}")
    private List<Integer> supportStoreTotalDirectLaunchOcpcTargets;

    @Value("#{'${platform.unit.support.store.direct.total.launch.ocpc.deep.targets}'.split(',')}")
    private List<Integer> supportStoreTotalDirectLaunchOcpcDeepTargets;

    @Value("#{'${mapi.live.room.ocpc.support.targets:5,19,22}'.split(',')}")
    private List<Integer> mapiLiveRoomSupportTargets;

    @Value("${clue.orderPlace.label.id:604}")
    private Integer clueOrderPlaceLabel;

    @Value("#{PropertySplitter.getInt2IntMap('${dpa.accountLabel.productGroupMap:{}}')}")
    private Map<Integer, Integer> dpaAccountLabelProductGroupMap;

    @Autowired
    private ICpcUnitService cpcUnitService;
    @Autowired
    private CpcUnitServiceDelegate cpcUnitServiceDelegate;
    @Autowired
    private ILauUnitService lauUnitService;
    @Autowired
    private ISoaStatUnitService statUnitService;
    @Autowired
    private IResTargetItemService resTargetItemService;
    @Autowired
    private ICpcCampaignService cpcCampaignService;
    @Autowired
    private IResSlotGroupService resSlotGroupService;
    @Autowired
    private IQueryAccountService queryAccountService;
    @Autowired
    private ICpcCreativeService cpcCreativeService;
    @Autowired
    private ILauCampaignService lauCampaignService;
    @Autowired
    private IResTargetPackageUpgradeService resTargetPackageUpgradeService;

    @Autowired
    private UnitConverter converter;
    @Autowired
    private LiveBroadcastHttpService liveBroadcastHttpService;
    @Autowired
    private IPassportService passportService;
    @Autowired
    private ArchiveManager archiveManager;
    @Autowired
    private LaunchCommonService launchCommonService;
    @Autowired
    private AdpCpcLauStartUpCrowdsService adpCpcLauStartUpCrowdsService;
    @Autowired
    private AdpCpcCreativeService adpCpcCreativeService;
    @Autowired
    private AdpCpcLauUnitBusinessCategoryService adpCpcLauUnitBusinessCategoryService;
    @Autowired
    private AdpCpcUnitService adpCpcUnitService;
    @Autowired
    private AdpCpcResourceService slotGroupService;
    @Autowired
    private ISoaAccountLabelService soaAccountLabelService;

    @Value("#{PropertySplitter.mapInt('${launch.unit.high.bid}')}")
    private Map<Integer, Integer> highBid;
    @Autowired
    private BvidSwitchService bvidSwitchService;
    @Autowired
    private ILauProductGroupService lauProductGroupService;
    @Autowired
    private CpcProductConverter cpcProductConverter;
    @Autowired
    private ICrowdPackService crowdPackService;
    @Resource(name = "cpcAppPackageService")
    private AppPackageService appPackageService;
    @Resource
    private WebAppPackageService webAppPackageService;
    @Autowired
    private LaunchUnitLowestBidService lowestBidService;
    @Autowired
    private AdpCpcOcpxService ocpxService;
    @Autowired
    private OpenApiFlyValidator openApiFlyValidator;
    @Autowired
    private CampaignRepo campaignRepo;
    @Autowired
    private PromotionContentDetailFacade promotionContentLinkFacade;

    @Value("${apple.brand.id:628}")
    private Integer appleBrandId;

    //起飞在必选系统需要排除的资源位组
    @Value("#{'${effect.exclude.slot.group.ids:281,282,285,286,287,288,366,367,349,350,351}'.split(',')}")
    private List<Integer> effectExcludeSlotGroupIds;
    //内容&商业起飞的资源位组
    @Value("#{'${fly.slot.group.ids:143,144,281,282,285,286,287,288}'.split(',')}")
    private List<Integer> flySlotGroupIds;

    @Value("#{'${allow.pc.channel.ocpc.target.ids:4,9,10}'.split(',')}")
    private List<Integer> allowPcChannelOcpcTargetIds;

    @Resource
    private OpenAPIValidator openAPIValidator;

    @Autowired
    private UnitFillService unitFillService;

    @Autowired
    private AdpCpcGameService adpCpcGameService;

    @Autowired
    private UnitService unitService;
    @Autowired
    private TargetLowestBidProc targetLowestBidProc;

    @Autowired
    private TargetPackageUpgradeConvertor targetPackageUpgradeConvertor;
    @Autowired
    private AdpCpcLiveRoomService adpCpcLiveRoomService;
    @Autowired
    private IGameCenterService gameCenterService;

    @Autowired
    private TagService tagService;

    @Autowired
    private ArchiveService archiveService;
    @Value("${game.card.unit.lowest.cost.price:1}")
    private Integer lowestCostPrice;

    @Autowired
    private IAccountLabelService accountLabelService;
    @Autowired
    private AccLabelConfig accLabelConfig;
    @Autowired
    private UnitSlotGroupComponent unitSlotGroupComponent;
    @Autowired
    private EffectAdSearchAdUnitService effectAdSearchAdUnitService;
    @Autowired
    private ISoaStatUnitService soaStatUnitService;
    @Autowired
    private GoodsArchiveHintProc goodsArchiveHintProc;
    @Autowired
    private LaunchUnitAssistSearchService launchUnitAssistSearchService;
    private final LaunchUnitV1Service launchUnitV1Service;
    private final PromotionPurposeDescProc promotionPurposeDescProc;

    @Value("${shut.down.label.id}")
    private int shutDownLabeId;

    @Value("${shut.down.date}")
    private String shutdownDateString;

    @Autowired
    private CpcUnitValidator cpcUnitValidator;

    @Value("${platform.account.label.general.professionInterest:801}")
    private Integer supportProfessionInterest;

    @Value("${platform.account.label.general.professionInterestAuto:800}")
    private Integer supportProfessionInterestAuto;

    @ApiOperation(value = "根据计划ID获取单元数")
    @RequestMapping(value = "/count", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<Long> getUnitCountInCampaignIds(
            @ApiIgnore Context context,
            @RequestParam("campaign_ids") List<Integer> campaignIds) throws ServiceException {
        return Response.SUCCESS(lauUnitService.getValidUnitCountByCampaignIds(campaignIds));
    }

    @ApiOperation(value = "根据批量计划ID获取单元数")
    @RequestMapping(value = "/batch", method = RequestMethod.GET)
    public Response<List<DpaCampaignVo>> getCampaignInfoInCampaignIds(@ApiIgnore Context context,
                                                                      @RequestParam("campaign_ids") List<Integer> campaignIds) throws ServiceException {
        if (CollectionUtils.isEmpty(campaignIds)) {
            return Response.SUCCESS(Collections.emptyList());
        }
        List<LauCampaignDto> campaignDtos = lauCampaignService.queryCampaign(CampaignQueryDto.builder()
                .accountIds(Collections.singletonList(context.getAccountId()))
                .campaignIds(campaignIds)
                .withUnitCount(true)
                .build());
        return Response.SUCCESS(converter.lauCampaignDtos2Vos(campaignDtos));
    }

    @ApiOperation(value = "批量修改单元")
    @RequestMapping(value = "/batch", method = RequestMethod.PUT)
    public
    @ResponseBody
    Response<Integer> batchUpdateUnit(
            @ApiIgnore Context context,
            @RequestBody BatchUpdateUnitVo vo) throws ServiceException {
        AccountBaseDto accountBaseDto = queryAccountService.getAccountBaseDtoByIdFromCache(context.getAccountId());
        Assert.isTrue(IsValid.TRUE.getCode().equals(accountBaseDto.getIsSupportDpa()), "您无权限进行批量修改单元");

        List<Integer> accountIds = lauCampaignService.getAccountIdsInCampaignIds(vo.getCampaign_ids());
        Assert.isTrue(accountIds.contains(context.getAccountId()), "不能操作不属于你的计划");
        cpcUnitService.batchUpdateDpaUnit(converter.batchUpdateUnitVo2Dto(vo), super.getOperator(context));
        return Response.SUCCESS(null);
    }

    @ApiOperation(value = "创建单元")
    @PostMapping(value = "")
    @ResponseBody
    public Response<Integer> createUnit(
            @ApiIgnore Context context,
            @RequestBody @Validated NewCpcUnitVo vo) throws ServiceException {
        final Integer aid = context.getAccountId();
        Assert.isTrue(Utils.isPositive(aid), "获取账号ID失败");
        AccountBaseDto accountBaseDto = queryAccountService.getAccountBaseDtoByIdFromCache(aid);
        // 非三连
        if (!IsValid.TRUE.getCode().equals(vo.getIs_middle_ad())) {
            vo.setMid(accountBaseDto.getMid());
        }
        CpcCampaignDto campaignDto = cpcCampaignService.loadCpcCampaignDto(vo.getCampaign_id());
        boolean inShutdownLabel = accountLabelService.isAccountIdsInLabels(Collections.singletonList(context.getAccountId()), Collections.singletonList(shutDownLabeId));
        LocalDateTime shutDownDate = LocalDateTime.parse(shutdownDateString, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        Assert.isTrue(campaignDto.getIsMiddleAd() == 1 || !inShutdownLabel || LocalDateTime.now().isBefore(shutDownDate), "必选系统已不再维护，请至新三连推广平台进行投放");
        Assert.isTrue(aid.equals(campaignDto.getAccountId()), "您不能在不属于您的计划下创建单元");

        //不允许创建新起飞的单元
        if (campaignDto.getIsNewFly() != null && campaignDto.getIsNewFly() == 1) {
            throw new IllegalArgumentException("不允许创建新起飞的单元");
        }

        // 从计划上获取 adp version(三联的计划上adpVersion=middle)
        final Integer adpVersion = campaignDto.getAdpVersion();
        vo.setAdp_version(adpVersion);
        boolean bindTargetUpgradePackage = Objects.nonNull(vo.getTargets())
                && Utils.isPositive(vo.getTarget_package_id())
                && AdpVersion.isMiddle(vo.getAdp_version());

        Long targetPackageMtime = null;
        // 满足条件时视为绑新版定向包id
        if (bindTargetUpgradePackage) {
            TargetPackageUpgradeDto targetPackageUpgradeDto = resTargetPackageUpgradeService.load(vo.getTarget_package_id(), false);
            Assert.notNull(targetPackageUpgradeDto, "新版定向包id错误");
            Assert.isTrue(AdpVersion.isMiddle(targetPackageUpgradeDto.getAdpVersion()), "老三连单元只能使用老三连定向包");
            Assert.isTrue(aid.equals(targetPackageUpgradeDto.getAccountId()), "您不能绑定不属于自己的定向包");
            Assert.isTrue(targetPackageUpgradeDto.getTotalUnitCount() <= 1000, "单个新版定向包同时最多绑定1000个单元");
            Assert.isTrue(campaignDto.getPromotionPurposeType() == targetPackageUpgradeDto.getPromotionPurposeType(), "您不能绑定推广目的不匹配的定向包");
            Assert.isTrue(!Utils.isPositive(campaignDto.getAdType()), "计划为搜索或闪屏广告的单元不支持新版定向包");
            targetPackageMtime = targetPackageUpgradeDto.getMtime().getTime();
            // 根据定向包id，拿到数据赋给vo的targets字段
            vo.setTargets(targetPackageUpgradeConvertor.convertDto2CpcUnitTargetVo(targetPackageUpgradeDto));
            targetPackageUpgradeConvertor.buildCrowdPackVo(Lists.newArrayList(vo.getTargets()), context.getAccountId());
            targetPackageUpgradeConvertor.extractStartUpCrowds(Lists.newArrayList(vo.getTargets()));
        } else {
            vo.setTarget_package_id(0);
        }
        // 过滤非法人群包
        filterNonValidCrowds(vo.getTargets(), aid);
        // 打包冷启人群包
        //todo 下线行业优选
        //packStartUpCrowds(vo.getTargets());

        validateDeviceBrand(vo.getTargets());

        if (!AdpVersion.isLegacy(adpVersion) && !Utils.isPositive(vo.getSpeed_mode())) {
            // 非旧版数据投放速度在单元上, 如果没传, 使用默认值
            vo.setSpeed_mode(SpeedMode.SMOOTH.getCode());
        }
        vo.setIs_middle_ad(campaignDto.getIsMiddleAd());
        vo.setIs_new_fly(campaignDto.getIsNewFly());

        // 2023.4.17 产品：空痕 需求TAPD https://www.tapd.bilibili.co/********/prong/stories/view/11********002969444
        // 阿里UD黑盒需要支持同一个公司组id下不同账号使用不同的xml入库的商品进行隔离，且客户不同意自己传自定义标签进行商品组拆分
        // 所以需要手工在商品上增加自定义标签，用于区分不同账号的商品，同时在单元上根据账号标签写死商品组id进行商品组隔离
        Integer resetProductGroupId = resetProductGroupIdByAccountLabel(context.getAccountId(), campaignDto.getPromotionPurposeType());
        if (resetProductGroupId != null) {
            vo.setProduct_group_id(resetProductGroupId);
        }


        validateCreate(context, campaignDto, vo);

        //目前硬编码 游戏预约目标下 二阶段底价 后续优化部分
        if (Objects.equals(vo.getOcpc_target(), 3) && Objects.nonNull(vo.getTwo_stage_bid())) {
            boolean isSupportGameLowBid = accountLabelService.isAccountIdsInLabels(Collections.singletonList(context.getAccountId()),
                    Collections.singletonList(accLabelConfig.getLabelGameAppointLowestPrice()));
            BigDecimal gameSubscribeLowestTwoStageBid = targetLowestBidProc.fetchLowestBid(isSupportGameLowBid);
            Assert.isTrue(vo.getTwo_stage_bid().compareTo(gameSubscribeLowestTwoStageBid) >= 0,
                    "游戏预约转化目标下，目标转化出价必须大于等于 " + gameSubscribeLowestTwoStageBid + " 元");
        }
        if (OcpcTargetEnum.PAID_IN_24H_ROI.getCode().equals(vo.getOcpc_target())) {
            BigDecimal bid = new BigDecimal(cpcUnitServiceDelegate.getBrandSpreadPaidIn24HROILostBid()).divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_UP);
            Assert.isTrue(vo.getTwo_stage_bid().compareTo(bid) >= 0,
                    "24小时ROI转化目标下，目标转化出价必须大于等于 " + bid);
        }

        // unit vo -> unit dto
        NewCpcUnitDto newCpcUnitDto = converter.newUnitVoToDto(vo);
        // 计划的推广目的
        newCpcUnitDto.setPromotionPurposeType(campaignDto.getPromotionPurposeType());
        newCpcUnitDto.setAccountId(aid);
        newCpcUnitDto.setTargetPackageMtime(targetPackageMtime);
        // 创建单元
        final int unitId = unitService.createUnit(newCpcUnitDto, getOperator(context));

        // 单元的商业分类
        saveUnitBusinessCategory(unitId, vo.getTargets(), vo.getTarget_package_id());
        return Response.SUCCESS(unitId);
    }

    /**
     * 校验定向设备品牌
     *
     * @param target
     */
    private void validateDeviceBrand(CpcUnitTargetVo target) {
        Assert.notNull(target, "单元定向不能为空");

        final List<Integer> osIds = target.getOs();
        // 如果os不限, 不需要校验品牌
        if (CollectionUtils.isEmpty(osIds)) {
            return;
        }

        final List<Integer> deviceBrandIds = target.getDevice_brand();
        // 如果品牌不限, 不需要校验品牌
        if (CollectionUtils.isEmpty(deviceBrandIds)) {
            return;
        }

        final Set<Integer> deviceBrandIdSet = new HashSet<>(deviceBrandIds);
        final boolean hasAndroid = osIds.contains(Device.ANDROID.getCode());
        final boolean hasIOS =
                osIds.contains(Device.IPHONE.getCode()) || osIds.contains(Device.IPAD.getCode());
        // 如果既包含安卓又包含IOS设备, 不需要校验品牌
        if (hasAndroid && hasIOS) {
            return;
        }

        if (hasAndroid) {
            Assert.isTrue(!deviceBrandIdSet.contains(appleBrandId), "定向冲突: 平台不包含IOS, 品牌选择了苹果");
            return;
        }

        if (hasIOS) {
            Assert.isTrue(CollectionUtils.isEmpty(deviceBrandIdSet), "定向冲突: 平台不包含安卓, 品牌不可选择");
        }
    }

    /**
     * 过滤非法人群包
     *
     * @param target
     * @param aid
     * @throws ServiceException
     */
    private void filterNonValidCrowds(CpcUnitTargetVo target, Integer aid) throws ServiceException {
        final CpcUnitTargetCrowdPackVo crowdPack = target.getCrowd_pack();
        if (Objects.isNull(crowdPack)) {
            return;
        }

        if (CollectionUtils.isEmpty(crowdPack.getCrowd_pack_ids())) {
            crowdPack.setCrowd_pack_ids(new ArrayList<>());
        }

        if (CollectionUtils.isEmpty(crowdPack.getExclude_crowd_pack_ids())) {
            crowdPack.setExclude_crowd_pack_ids(new ArrayList<>());
        }

        final List<Integer> includedIds = crowdPack.getCrowd_pack_ids();
        final List<Integer> excludedIds = crowdPack.getExclude_crowd_pack_ids();
        if (CollectionUtils.isEmpty(includedIds) && CollectionUtils.isEmpty(excludedIds)) {
            return;
        }

        // 所有人群包
        Set<Integer> totalCrowdPackIds = Stream.of(includedIds, excludedIds)
                .filter(list -> !CollectionUtils.isEmpty(list))
                .flatMap(Collection::stream)
                .collect(Collectors.toSet());
        Set<Integer> validCrowdPackIdSet =
                crowdPackService.getValidCrowdPackSetFromDmp(new ArrayList<>(totalCrowdPackIds), aid);

        // 如果有绑定无效人群包且单元直接绑定定向包 直接报错
        List<Integer> invalidIds = new ArrayList<>();
        List<Integer> invalidIncludedIds = includedIds.stream()
                .filter(includedId -> !validCrowdPackIdSet.contains(includedId))
                .collect(Collectors.toList());
        List<Integer> invalidExcludedIds = excludedIds.stream()
                .filter(excludedId -> !validCrowdPackIdSet.contains(excludedId))
                .collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(invalidIncludedIds)) {
            invalidIds.addAll(invalidIncludedIds);
        }
        if (!CollectionUtils.isEmpty(invalidExcludedIds)) {
            invalidIds.addAll(invalidExcludedIds);
        }

        if (!CollectionUtils.isEmpty(invalidIds)) {
            StringBuilder sb = new StringBuilder();
            sb.append("当前共").append(invalidIds.size()).append("个人群包无效,请至人群包管理界面重新保存,人群包id:");
            sb.append(invalidIds.size() <= 10 ? invalidIds.toString() : invalidIds.subList(0, 10).toString());
            sb.append("等");
            throw new IllegalArgumentException(sb.toString());
        }

        if (!CollectionUtils.isEmpty(includedIds)) {
            final List<Integer> filteredIncludedIds = includedIds.stream()
                    .filter(validCrowdPackIdSet::contains)
                    .collect(Collectors.toList());
            crowdPack.setCrowd_pack_ids(filteredIncludedIds);
        }

        if (!CollectionUtils.isEmpty(excludedIds)) {
            final List<Integer> filteredExcludedIds = excludedIds.stream()
                    .filter(validCrowdPackIdSet::contains)
                    .collect(Collectors.toList());
            crowdPack.setExclude_crowd_pack_ids(filteredExcludedIds);
        }
    }

    /**
     * 新建单元校验
     * 后置位还有单元层级校验:
     *
     * @param context
     * @param campaignDto
     * @param vo
     * @see CpcUnitValidator#createCpc(ResSlotGroupBaseDto, TemplateDto, NewCpcUnitDto, CpcCampaignDto, Operator)
     */
    private void validateCreate(Context context, CpcCampaignDto campaignDto, NewCpcUnitVo vo) {
        Integer ocpcTarget = vo.getOcpc_target();
        Integer campaignId = campaignDto.getCampaignId();

        // mapi
        if (context.isOpenApi()) {
            vo.setDaily_budget_type(DailyBudgetType.MANUAL.getCode());
            Assert.isTrue(!Utils.isPositive(campaignDto.getIsManaged()), "不允许新建托管计划下的单元");
            // 默认每日
            vo.setFrequency_unit(1);
            //speedMode校验
            if (campaignDto.getPromotionPurposeType() == PromotionPurposeType.GOODS_CATALOG.getCode()) {
                vo.setSpeed_mode(1);
            }
            speedModeCheck(vo.getSpeed_mode());
            // 开放平台 设置 enable_ocpc  0 或者 null 是cpc,  ocpc ocpx只支持2(应用激活)
            if (null == ocpcTarget || 0 == ocpcTarget) {
                vo.setOcpc_target(0);
                vo.setEnable_ocpc(false);
            } else {
                vo.setEnable_ocpc(true);
                ocpcTargetVerify(campaignDto, null, ocpcTarget, vo.getOcpxTargetTwo(), vo.getOcpxTargetTwoBid(),
                        vo.getIsStoreDirectLaunch(), vo.getTargets().getOs(), vo.getSales_type(), vo.getVideo_id());
            }

            if (campaignDto.getPromotionPurposeType() == PromotionPurposeType.GAME_CARD.getCode()) {
                vo.setSales_type(SalesType.CPC.getCode());
            }

            //推广目的 参数校验
            if (null != vo.getApp_package_id()) {
                Assert.isTrue((campaignDto.getPromotionPurposeType() == PromotionPurposeType.APP_DOWNLOAD.getCode()
                                || campaignDto.getPromotionPurposeType() == PromotionPurposeType.GOODS_CATALOG.getCode())
                        , errorMsgBuild(PARAM_ERROR, "此推广目的不支持app_package_id"));
            }

            Assert.isTrue(null != vo.getBudget(), errorMsgBuild(PARAM_ERROR, "budget必填"));
            boolean isMiddleFly = Utils.isPositive(vo.getIs_middle_ad()) &&
                    openApiFlyValidator.judgeIsFly(campaignDto.getPromotionPurposeType(), vo.getOcpc_target(), vo.getSales_type());
            if (!isMiddleFly) {
                Assert.isTrue(null != vo.getCost_price(), errorMsgBuild(PARAM_ERROR, "cost_price必填"));
                Assert.isTrue(StringUtils.isEmpty(vo.getVideo_id()), "当前单元类型不允许在单元级别绑定稿件");
            } else if (PromotionPurposeType.BRAND_SPREAD.getCode() == campaignDto.getPromotionPurposeType()) {
                Assert.isTrue(!StringUtils.isEmpty(vo.getVideo_id()), "起飞类型单元稿件id不可为空");
                Assert.isTrue(LaunchVideoType.BUSINESS.getCode().equals(vo.getLaunch_ad_type())
                        || LaunchVideoType.OTHER.getCode().equals(vo.getLaunch_ad_type())
                        || LaunchVideoType.GENERAL_VIDEO.getCode().equals(vo.getLaunch_ad_type()), "未知的稿件类型");
            } else {
                Assert.isTrue(StringUtils.isEmpty(vo.getVideo_id()), "当前起飞单元类型不允许在单元级别绑定稿件");
            }

            Integer dayLimitValue = openAPIValidator.getLimitValue(OpenAPILimitType.UNIT, LimitBsiType.DAY_LIMIT, context.getAccountId());
            Assert.isTrue(openAPIValidator.dayCountUnitOpenAPI(context.getAccountId()) < dayLimitValue, errorMsgBuild(DAY_LIMIT, "可用单元个数超过" + dayLimitValue + "日上限"));
            Integer allLimitValue = openAPIValidator.getLimitValue(OpenAPILimitType.UNIT, LimitBsiType.ALL_LIMIT, context.getAccountId());
            Assert.isTrue(openAPIValidator.countUnitOpenAPI(context.getAccountId()) < allLimitValue, errorMsgBuild(ALL_LIMIT, "可用单元个数超过" + allLimitValue + "上限"));

            if (vo.getEnable_ocpc()) {
                Assert.notNull(vo.getTwo_stage_bid(), errorMsgBuild(PARAM_ERROR, "ocpc时two_stage_bid字段必填"));
                Assert.notNull(ocpcTarget, errorMsgBuild(PARAM_ERROR, "ocpc时ocpc_target字段必填"));
            }

            BigDecimal budget = vo.getBudget();
            if (budget.doubleValue() > 9999999.99 || budget.doubleValue() < 100 || budget.scale() > 2 || budget.doubleValue() < 0) {
                Assert.isTrue(false, errorMsgBuild(PARAM_ERROR, "指定预算不符合规则"));
            }

            BigDecimal costPrice = vo.getCost_price();
            if (Objects.nonNull(costPrice) && (
                    costPrice.doubleValue() < 0
                            || (vo.getSales_type().equals(SalesType.CPC.getCode()) && costPrice.doubleValue() > 20.01)
                            || (vo.getSales_type().equals(SalesType.CPM.getCode()) && costPrice.doubleValue() > 100.01))) {
                Assert.isTrue(false, errorMsgBuild(PARAM_ERROR, "指定预算不符合规则 大于0 cpc小于20 cpm小于100"));
            }
            validateCommon(context, campaignDto, vo.getUnit_name(), null, vo, null);
        } else {
            // roi的优化目标下，必传，并且 > 0
            if (OcpxTarget.isRoiNew(vo.getOcpxTargetTwo())) {
                Assert.isTrue(Utils.isPositive(vo.getOcpxTargetTwoBid()), "ROI出价必须大于0");
            }
        }

        // 校验新版定向包
        validateTargetPackageUpgradeTargets(campaignDto.getPromotionPurposeType(), vo, null);
    }

    /**
     * 校验新版定向包
     *
     * @param cppt
     * @param createVo
     * @param updateCpcUnitVo
     */
    private void validateTargetPackageUpgradeTargets(int cppt, NewCpcUnitVo createVo, UpdateCpcUnitVo updateCpcUnitVo) {
        Integer salesType, ocpcTarget;
        CpcUnitTargetVo targets;
        Integer appPackageId;

        if (null != createVo) {
            targets = createVo.getTargets();
            appPackageId = createVo.getApp_package_id();
            salesType = createVo.getSales_type();
            ocpcTarget = createVo.getOcpc_target();
        } else {
            targets = updateCpcUnitVo.getTargets();
            appPackageId = updateCpcUnitVo.getApp_package_id();
            salesType = updateCpcUnitVo.getSales_type();
            ocpcTarget = updateCpcUnitVo.getOcpc_target();
        }

        List<Integer> oss = targets.getOs();
        CpcUnitTargetIntelligentMassVo intelligentMass = targets.getIntelligentMass();
        // 已转化用户过滤定向，0:不限，1:公司组，2:账户，3:计划，4:单元
        List<Integer> convertedUserFilterList = targets.getConverted_user_filter();

        if (Objects.nonNull(intelligentMass) && !CollectionUtils.isEmpty(intelligentMass.getTargets())) {
            Assert.isTrue(Utils.isPositive(ocpcTarget) && SalesType.CPC.getCode() == salesType, "智能放量仅支持OCPC单元");
        }

        if (!CollectionUtils.isEmpty(convertedUserFilterList)) {
            Assert.isTrue(PromotionPurposeType.CONVERTED_USER_FILTER_SUPPORT_CAMPAIGN_PPT_SET.contains(cppt), "当前推广目的不支持已转化用户过滤");
            Assert.isTrue(Utils.isPositive(ocpcTarget), "非OCPX单元不支持已转化用户过滤");
            if (PromotionPurposeType.SHOP_GOODS.getCode() == cppt) {
                Assert.isTrue(SalesType.CPM.getCode() != salesType, "当前推广目的下,OCPM单元不支持已转化用户过滤");
            }
        }

        if (Utils.isPositive(appPackageId) && !CollectionUtils.isEmpty(oss) && !Lists.newArrayList(-1).containsAll(oss)) {
            List<AppPackageDto> appPackageDtos = appPackageService.query(QueryAppPackageDto.builder()
                    .ids(Lists.newArrayList(appPackageId))
                    .build());
            Assert.isTrue(!CollectionUtils.isEmpty(appPackageDtos), "应用包无效");
            List<Integer> platforms = webAppPackageService.getPlatforms(appPackageDtos.get(0).getPlatform());
            Assert.isTrue(platforms.containsAll(oss), errorMsgBuild(PARAM_ERROR, "应用包" + appPackageId + "和定向os不匹配"));
        }
    }

    private void ocpcTargetVerify(CpcCampaignDto campaignDto, Integer unitId, Integer ocpcTarget,
                                  Integer ocpcTargetTwo, BigDecimal ocpxTargetTwoBid,
                                  Integer isDirectStoreLaunch, List<Integer> osList, Integer salesType, String videoId) {
        // mapi: roi的优化目标下，可以不传(落的是0)，如果主动传，只能 > 0
        if (OcpxTarget.isRoiNew(ocpcTargetTwo)) {
            if (ocpxTargetTwoBid != null) {
                Assert.isTrue(ocpxTargetTwoBid.compareTo(BigDecimal.ZERO) > 0, "ROI出价必须大于0");
            }
        }

        PromotionPurposeType promotionPurposeType = PromotionPurposeType.getByCode(campaignDto.getPromotionPurposeType());
        if (PromotionPurposeType.UNKNOWN == promotionPurposeType) {
            throw new IllegalArgumentException("推广目的未知");
        }

        if (!PromotionPurposeType.BRAND_SPREAD.equals(promotionPurposeType)
                && !PromotionPurposeType.APP_DOWNLOAD.equals(promotionPurposeType)) {
            Assert.isTrue(SalesType.CPM.getCode() != salesType, "当前接口不允许品牌传播/应用推广以外的推广目的计划下保存OCPM单元");
        }

        if (CollectionUtils.isEmpty(osList)) {
            osList = new ArrayList<>();
        }

        Long avid = null;
        if (!StringUtils.isEmpty(videoId)) {
            avid = Long.parseLong(videoId);
        }

        switch (promotionPurposeType) {
            case APP_DOWNLOAD:
                if (isDirectStoreLaunch != null && isDirectStoreLaunch == 1) {
                    Assert.isTrue(supportStoreDirectLaunchOcpcTargets.contains(ocpcTarget), errorMsgBuild(PARAM_ERROR,
                            "推广目的应用下载的计划不支持当前优化目标，参照/open_api/launch/cpc/meta_data/resource/sales_type"));
                    Assert.isTrue(ocpcTargetTwo == null || ocpcTargetTwo == 0 || supportStoreDirectLaunchOcpcDeepTargets.contains(ocpcTargetTwo), errorMsgBuild(PARAM_ERROR,
                            "推广目的应用下载的计划不支持当前深度优化目标，参照/open_api/launch/cpc/meta_data/resource/sales_type"));
                } else {
                    openAPIValidator.validateUnitSalesTypeAndOcpxTarget(campaignDto.getAccountId(), osList, campaignDto.getCampaignId(), unitId,
                            null, campaignDto.getPromotionPurposeType(), null, campaignDto.getPromotionPurposeType(), salesType, ocpcTarget, ocpcTargetTwo);

//                    List<OcpxTargetBo> targets = ocpxService.listAvailableOcpxTargetsByCampaign(campaignDto.getAccountId(),
//                            campaignDto.getCampaignId(), new HashSet<>(osList), null, salesType, OcpxTargetSupportBizTypeEnum.NORMAL.getCode());
//
//                    Assert.isTrue(targets.stream().anyMatch(target -> target.getId().equals(ocpcTarget)),
//                            errorMsgBuild(PARAM_ERROR, "不支持的优化目标，请参照/open_api/launch/cpc/meta_data/resource/sales_type"));
//
//                    Assert.isTrue(ocpcTargetTwo == null || ocpcTargetTwo == 0 ||
//                                    targets.stream().anyMatch(target -> target.getDescendants().stream().anyMatch(deep -> deep.getId().equals(ocpcTargetTwo))),
//                            errorMsgBuild(PARAM_ERROR, "不支持的深度优化目标，请参照/open_api/launch/cpc/meta_data/resource" +
//                                    "/all_ocpc_targets"));
                }
                break;
            case LANDING_PAGE:
            case SALE_GOODS:
                openAPIValidator.validateUnitSalesTypeAndOcpxTarget(campaignDto.getAccountId(), osList, campaignDto.getCampaignId(), unitId,
                        null, campaignDto.getPromotionPurposeType(), null, campaignDto.getPromotionPurposeType(), salesType, ocpcTarget, ocpcTargetTwo);
//                List<OcpxTargetBo> targets = ocpxService.listAvailableOcpxTargetsByCampaign(campaignDto.getAccountId(),
//                        campaignDto.getCampaignId(), new HashSet<>(osList), null, salesType, OcpxTargetSupportBizTypeEnum.NORMAL.getCode());
//                Assert.isTrue(targets.stream().anyMatch(target -> target.getId().equals(ocpcTarget)),
//                        errorMsgBuild(PARAM_ERROR, "不支持的优化目标，请参照/open_api/launch/cpc/meta_data/resource/sales_type"));
//                Assert.isTrue(ocpcTargetTwo == null || ocpcTargetTwo == 0 ||
//                                targets.stream().anyMatch(target -> target.getDescendants().stream().anyMatch(deep -> deep.getId().equals(ocpcTargetTwo))),
//                        errorMsgBuild(PARAM_ERROR, "不支持的深度优化目标，请参照/open_api/launch/cpc/meta_data/resource" +
//                                "/all_ocpc_targets"));
                break;
            case ON_SHELF_GAME:
                openAPIValidator.validateUnitSalesTypeAndOcpxTarget(campaignDto.getAccountId(), osList, campaignDto.getCampaignId(), unitId,
                        GameStatusEnum.DOWNLOAD.getCode(), campaignDto.getPromotionPurposeType(), null, campaignDto.getPromotionPurposeType(), salesType, ocpcTarget, ocpcTargetTwo);
                break;
            case BRAND_SPREAD:
                openAPIValidator.validateUnitSalesTypeAndOcpxTarget(campaignDto.getAccountId(), osList, campaignDto.getCampaignId(), unitId,
                        null, campaignDto.getPromotionPurposeType(), avid, campaignDto.getPromotionPurposeType(), salesType, ocpcTarget, ocpcTargetTwo);
                break;
        }
    }

    private void speedModeCheck(int code) {
        try {
            SpeedMode.getByCode(code);
        } catch (Exception e) {
            throw new IllegalArgumentException(errorMsgBuild(PARAM_ERROR, e.getMessage()));
        }
    }

    private void validateCommon(Context context, CpcCampaignDto campaignDto, String unitName, Integer unitId, NewCpcUnitVo createVo, UpdateCpcUnitVo updateCpcUnitVo) {
        boolean create = false;
        Integer campaignId = campaignDto.getCampaignId();
        int promotionPurposeType = campaignDto.getPromotionPurposeType();
        CpcUnitTargetVo targets;
        Integer appPackageId;
        String liveRoomId;
        Integer gameBaseId;
        Long liveRoomReserveId;
        Integer salesType;
        Integer ocpcTarget;
        String videoId;
        Integer adpVersion;
        Integer ocpxTargetTwo;

        Integer isStoreDirectLaunch = createVo != null ? createVo.getIsStoreDirectLaunch() : updateCpcUnitVo.getIsStoreDirectLaunch();
        if (isStoreDirectLaunch == null) {
            if (createVo != null) {
                createVo.setIsStoreDirectLaunch(0);
            } else {
                updateCpcUnitVo.setIsStoreDirectLaunch(0);
            }
        }

        if (null != createVo) {
            create = true;
            targets = createVo.getTargets();
            appPackageId = createVo.getApp_package_id();
            liveRoomId = createVo.getMaterial_id();
            gameBaseId = createVo.getGame_base_id();
            liveRoomReserveId = createVo.getSid();
            salesType = createVo.getSales_type();
            ocpcTarget = createVo.getOcpc_target();
            videoId = createVo.getVideo_id();
            adpVersion = createVo.getAdp_version();
            ocpxTargetTwo = createVo.getOcpxTargetTwo();
        } else {
            targets = updateCpcUnitVo.getTargets();
            appPackageId = updateCpcUnitVo.getApp_package_id();
            liveRoomId = updateCpcUnitVo.getMaterial_id();
            gameBaseId = updateCpcUnitVo.getGame_base_id();
            liveRoomReserveId = updateCpcUnitVo.getSid();
            salesType = updateCpcUnitVo.getSales_type();
            ocpcTarget = updateCpcUnitVo.getOcpc_target();
            videoId = updateCpcUnitVo.getVideo_id();
            ocpxTargetTwo = updateCpcUnitVo.getOcpxTargetTwo();
            adpVersion = null;
        }

        if (SalesType.CPM.getCode() == salesType && Utils.isPositive(ocpcTarget)) {
            Assert.isTrue(!Utils.isPositive(ocpxTargetTwo), "目前ocpm不支持第二优化目标");
        }

        if (promotionPurposeType == PromotionPurposeType.LIVE_ROOM.getCode()) {
            Assert.isTrue(StringUtils.isNotEmpty(liveRoomId) || Utils.isPositive(liveRoomReserveId),
                    errorMsgBuild(PARAM_ERROR, "直播间推广目的时，直播间id和直播间预约id不能同时为空"));

            if (Utils.isPositive(liveRoomReserveId)) {
                Assert.isTrue(salesType == null || salesType == SalesType.CPC.getCode(), "创建或编辑直播间预约单元时，sales_type" +
                        "必须为12-CPC");
                Assert.isTrue(ocpcTarget == null || ocpcTarget == 0, "直播间预约单元时不支持优化目标");
            } else {
                if (salesType != null && salesType == SalesType.CPC.getCode()) {
                    String msg = mapiLiveRoomSupportTargets.stream()
                            .map(target -> OcpcTargetEnum.getByCode(target).getDesc())
                            .collect(Collectors.joining(","));

                    Assert.isTrue(mapiLiveRoomSupportTargets.contains(ocpcTarget), "创建或编辑直播间单元时，ocpc_target必须为" + msg);

                }
            }
        }

        if (PromotionPurposeType.BRAND_SPREAD.getCode() == promotionPurposeType) {
            Assert.isTrue(!StringUtils.isEmpty(videoId), "品牌传播/我要带货计划下单元稿件字段不能为空");
            Assert.isTrue(Utils.isPositive(Long.valueOf(videoId)), "品牌传播/我要带货计划下单元必须绑定稿件");
            Assert.isTrue(Objects.isNull(gameBaseId), "品牌传播/我要带货计划下单元不支持绑定游戏id");
            Assert.isTrue(Objects.isNull(liveRoomId), "品牌传播/我要带货计划下单元不支持绑定直播间id");
            Assert.isTrue(Objects.isNull(liveRoomReserveId), "品牌传播/我要带货计划下单元不支持绑定直播间预约id");
            Assert.isTrue(!Utils.isPositive(isStoreDirectLaunch), "品牌传播/我要带货计划下单元不支持应用商店直投");
            if (create) {
                Assert.isTrue(Utils.isPositive(adpVersion)
                        && AdpVersion.isMiddle(adpVersion), "目前仅三连版本单元支持品牌传播下单元创建");
                // 目前 品牌传播下仅支持稿件
                Assert.isTrue(
                        createVo.getUnit_promotion_purpose_type().equals(PromotionPurposeType.ARCHIVE_CONTENT.getCode()),
                        "品牌传播下推广目的仅支持稿件");
            }
        } else {
            Assert.isTrue(StringUtils.isEmpty(videoId), "目前仅品牌传播/我要带货计划下单元支持稿件字段");
        }

        List<Integer> ages = targets.getAge();
        List<Integer> genders = targets.getGender();
        List<Integer> areas = targets.getArea();
        List<Integer> deviceBrands = targets.getDevice_brand();
        List<Integer> networks = targets.getNetwork();
        List<Integer> oss = targets.getOs();
        //定向信息校验   年龄、性别、地域、设备、网络、粉丝关系、人群包
        Map<TargetType, List<TargetTreeDto>> target2ItemMap = resTargetItemService.getAllValidTarget2ItemTreeMap();
        targetValidate(target2ItemMap, ages, "age", TargetType.AGE);
        targetValidate(target2ItemMap, genders, "gender", TargetType.GENDER);
        targetValidate(target2ItemMap, areas, "area", TargetType.AREA);
        targetValidate(target2ItemMap, networks, "netWork", TargetType.NETWORK);
        targetValidate(target2ItemMap, deviceBrands, "device_brand", TargetType.DEVICE_BRAND);
        targetValidate(target2ItemMap, oss, "os", TargetType.OS);

        if (promotionPurposeType == PromotionPurposeType.APP_DOWNLOAD.getCode()) {
            List<Integer> os = targets.getOs();
            Assert.isTrue(!CollectionUtils.isEmpty(os),
                    errorMsgBuild(PARAM_ERROR, "推广目的应用下载时，定向os不能为空"));
            AppPackageDto appPackageDto = appPackageService.load(appPackageId);
            Assert.isTrue(null != appPackageDto, errorMsgBuild(PARAM_ERROR, "app_package_id不正确"));
            List<Integer> platforms = webAppPackageService.getPlatforms(appPackageDto.getPlatform());
            Assert.isTrue(platforms.containsAll(os), errorMsgBuild(PARAM_ERROR, "包和os不匹配"));

        } else if (PromotionPurposeType.ON_SHELF_GAME.getCode() == campaignDto.getPromotionPurposeType()) {
            CpcUnitTargetVo target = createVo != null ? createVo.getTargets() : updateCpcUnitVo.getTargets();
            Assert.isTrue(target.getOs() == null || target.getOs() != null
                            && target.getOs().size() == 1
                            && target.getOs().get(0).equals(webAppPackageService.getPlatforms(AppPlatformType.ANDROID.getCode()).get(0))
                    , errorMsgBuild(PARAM_ERROR, "安卓游戏的设备定向需为安卓"));
            if (create) {
                Assert.isTrue(gameBaseId != null, errorMsgBuild(PARAM_ERROR, "游戏id不能为空"));
            } else {
                Assert.isTrue(gameBaseId == null, errorMsgBuild(PARAM_ERROR, "游戏不能变更"));
            }
            if (target.getOs() == null) {
                //默认设备为安卓
                target.setOs(webAppPackageService.getPlatforms(AppPlatformType.ANDROID.getCode()));
            }

            //游戏卡
        } else if (promotionPurposeType == PromotionPurposeType.GAME_CARD.getCode()) {
            //默认加速
            if (createVo != null) {
                createVo.setSpeed_mode(SpeedMode.SPEED_UP.getCode());
            } else if (updateCpcUnitVo != null) {
                updateCpcUnitVo.setSpeedMode(SpeedMode.SPEED_UP.getCode());
            }

            //检查定向信息
            if (CollectionUtils.isEmpty(targets.getCategory())) {
                targets.setCategory(new LinkedList<>());
            }
            targets.setApp_category(new LinkedList<>());
            Assert.isTrue(CollectionUtils.isEmpty(ages), "游戏卡不支持填写年龄定向");
            targets.setAge(new LinkedList<>());
            Assert.isTrue(CollectionUtils.isEmpty(areas), "游戏卡不支持填写区域定向");
            targets.setArea(new LinkedList<>());
            Assert.isTrue(CollectionUtils.isEmpty(networks), "游戏卡不支持填写网络定向");
            targets.setNetwork(new LinkedList<>());
            Assert.isTrue(CollectionUtils.isEmpty(genders), "游戏卡不支持填写性别定向");
            targets.setGender(new LinkedList<>());
            Assert.isTrue(CollectionUtils.isEmpty(deviceBrands), "游戏卡不支持填写设备品牌定向");
            targets.setDevice_brand(new LinkedList<>());
            Assert.isTrue(targets.getIntelligentMass() == null
                    || CollectionUtils.isEmpty(targets.getIntelligentMass().getTargets())
                    || CollectionUtils.isEmpty(targets.getIntelligentMass().getExtraCrowdPackIds()), "游戏卡不支持填写智能放量信息");
            Assert.isTrue(CollectionUtils.isEmpty(targets.getConverted_user_filter()), "游戏卡不支持填写已转化用户过滤信息");
            targets.setConverted_user_filter(new LinkedList<>());
            //检查人群包
            CpcUnitTargetCrowdPackVo crowdPackVo = targets.getCrowd_pack();
            if (crowdPackVo != null) {
                Assert.isTrue(CollectionUtils.isEmpty(crowdPackVo.getCrowd_pack_ids())
                        || CollectionUtils.isEmpty(crowdPackVo.getExclude_crowd_pack_ids()), "游戏卡不支持填写指定人群包和排除人群包信息");
            } else {
                crowdPackVo = new CpcUnitTargetCrowdPackVo();
            }
            crowdPackVo.setCrowd_pack_ids(new LinkedList<>());
            crowdPackVo.setExclude_crowd_pack_ids(new LinkedList<>());
            targets.setCrowd_pack(crowdPackVo);
        }

        if (context.isOpenApi()) {
//            QueryCpcUnitDto query = QueryCpcUnitDto.builder()
//                    .campaignId(campaignId)
//                    .accountId(context.getAccountId())
//                    .unitName(unitName)
//                    .unitStatusList(UnitStatus.NON_DELETED_UNIT_STATUS_LIST)
//                    .build();
//            List<CpcUnitDto> dtoList = cpcUnitService.queryCpcUnit(query);
            QueryUnitBo query = QueryUnitBo.builder()
                    .campaignIds(Collections.singletonList(campaignId))
                    .accountIds(Collections.singletonList(context.getAccountId()))
                    .equalUnitName(unitName)
                    .unitStatusList(UnitStatus.NON_DELETED_UNIT_STATUS_LIST)
                    .build();
            List<CpcUnitDto> dtoList = launchUnitV1Service.listUnits(query);
            if (null != unitId) {
                dtoList.removeIf(item -> item.getUnitId().equals(unitId));
            }
            Assert.isTrue(CollectionUtils.isEmpty(dtoList), errorMsgBuild(REPEAT_REQUEST, "存在同名单元，重复！"));
        }
    }

    private void targetValidate(Map<TargetType, List<TargetTreeDto>> target2ItemMap, List<Integer> targets, String field, TargetType targetType) {
        if (!CollectionUtils.isEmpty(targets)) {
            List<TargetTreeDto> targetTreeDtos = target2ItemMap.get(targetType);
            List<Integer> list = targetTreeDtos.stream().map(TargetTreeDto::getId).collect(Collectors.toList());
            if (targetType == TargetType.AREA) {
                list = buildAreaIds(targetTreeDtos, new ArrayList<>());
            }
            Assert.isTrue(list.containsAll(targets), errorMsgBuild(PARAM_ERROR, field + "不正确"));
        }
    }

    private List<Integer> buildAreaIds(List<TargetTreeDto> targetTreeDtos, List<Integer> result) {
        if (CollectionUtils.isEmpty(targetTreeDtos)) {
            return result;
        }
        result.addAll(targetTreeDtos.stream().map(TargetTreeDto::getId).collect(Collectors.toList()));
        for (TargetTreeDto dto : targetTreeDtos) {
            List<TargetTreeDto> children = dto.getChildren();
            buildAreaIds(children, result);
        }
        return result;
    }

    @ApiOperation(value = "编辑单元")
    @PutMapping(value = "")
    @ResponseBody
    public Response<Object> updateUnit(
            @ApiIgnore Context context,
            @Validated @RequestBody UpdateCpcUnitVo vo) throws ServiceException {
        final Integer aid = context.getAccountId();
        Assert.isTrue(Utils.isPositive(aid), "获取账号ID失败");
        AccountBaseDto accountBaseDto = queryAccountService.getAccountBaseDtoByIdFromCache(aid);
        vo.setMid(accountBaseDto.getMid());
        CpcUnitDto unitDto = cpcUnitService.loadCpcUnit(vo.getUnit_id());
        Assert.isTrue(aid.equals(unitDto.getAccountId()), "您不能编辑不属于您的单元");
        Assert.isTrue(accountLabelService.isAccountIdInLabel(aid, oldSanlianAllowEdit), "不支持编辑旧版广告单元");
        CpcCampaignDto campaignDto = cpcCampaignService.loadCpcCampaignDto(unitDto.getCampaignId());

        Assert.isTrue(!AdpVersion.isCpcFlyMerge(campaignDto.getAdpVersion()), "不允许使用当前接口编辑新三连单元");

        //是否`所有广告`搜索词单元
        buildUpdateAllAdSearchUnit(vo, campaignDto);
        Assert.isTrue(vo.getIs_no_bid() == null || Objects.equals(unitDto.getIsNoBid(), vo.getIs_no_bid()), "nobid单元编辑不支持修改投放方式");

        // PAID_IN_24H_ROI 出价校验
        if (OcpcTargetEnum.PAID_IN_24H_ROI.getCode().equals(vo.getOcpc_target())) {
            BigDecimal bid = new BigDecimal(cpcUnitServiceDelegate.getBrandSpreadPaidIn24HROILostBid()).divide(BigDecimal.valueOf(100), 4, RoundingMode.HALF_UP);
            Assert.isTrue(vo.getTwo_stage_bid().compareTo(bid) >= 0,
                    "24小时ROI转化目标下，目标转化出价必须大于等于 " + bid);
        }
        // 设置默认地域定向
        this.setDefaultAreaTypeIfNecessary(vo);
        boolean needTryInitAssistData = false;
        if (AdpVersion.isMiddle(unitDto.getAdpVersion())) {
            // 稿件校验
            needTryInitAssistData = this.archiveCheck(campaignDto, context, vo, unitDto);
            // 安卓游戏校验
            this.androidGameCheck(campaignDto, context, vo);
        }

        boolean inShutdownLabel = accountLabelService.isAccountIdsInLabels(Collections.singletonList(context.getAccountId()), Collections.singletonList(shutDownLabeId));
        LocalDateTime shutDownDate = LocalDateTime.parse(shutdownDateString, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        Assert.isTrue(campaignDto.getIsMiddleAd() == 1 || !inShutdownLabel || LocalDateTime.now().isBefore(shutDownDate), "必选系统已不再维护，请至新三连推广平台进行投放");
        boolean isArcCpc = unitDto.getPromotionPurposeType() == PromotionPurposeType.ARCHIVE_CONTENT.getCode() && Objects.equals(vo.getSales_type(), SalesType.CPC.getCode());
        Assert.isTrue(!isArcCpc, "该版本广告单元已不再支持新建或复制");
        if (Utils.isPositive(vo.getOcpc_target()) && slotGroupService.hasPcSource(vo.getUnit_id())) {
            Assert.isTrue(allowPcChannelOcpcTargetIds.contains(vo.getOcpc_target()), "单元包含的创意已绑定了PC资源位, 请先解除绑定再修改OCPC优化目标");
        }

        //不允许编辑新起飞的单元
        if (unitDto.getIsNewFly() != null && unitDto.getIsNewFly() == 1) {
            throw new IllegalArgumentException("不允许编辑新起飞的单元");
        }

        this.checkUnitBudgetIfChange(Utils.fromFenToYuan(unitDto.getBudget()),
                vo.getDaily_budget_type(), vo.getBudget(), vo.getUnit_id());

        vo.setAdpVersion(unitDto.getAdpVersion());
        Long targetPackageMtime = null;
        boolean bindTargetUpgradePackage = Objects.nonNull(vo.getTargets())
                && Utils.isPositive(vo.getTarget_package_id())
                && AdpVersion.isMiddle(vo.getAdpVersion());
        // 满足条件时视为绑新版定向包id
        if (bindTargetUpgradePackage) {
            TargetPackageUpgradeDto targetPackageUpgradeDto = resTargetPackageUpgradeService.load(vo.getTarget_package_id(), false);
            Assert.notNull(targetPackageUpgradeDto, "新版定向包id错误");
            Assert.isTrue(AdpVersion.isMiddle(targetPackageUpgradeDto.getAdpVersion()), "老三连单元只能使用老三连定向包");
            Assert.isTrue(aid.equals(targetPackageUpgradeDto.getAccountId()), "您不能绑定不属于自己的定向包");
            Assert.isTrue(targetPackageUpgradeDto.getTotalUnitCount() <= 1000, "单个新版定向包同时最多绑定1000个单元");
            Assert.isTrue(campaignDto.getPromotionPurposeType() == targetPackageUpgradeDto.getPromotionPurposeType(), "您不能绑定推广目的不匹配的定向包");
            Assert.isTrue(!Utils.isPositive(campaignDto.getAdType()), "计划为搜索或闪屏广告的单元不支持新版定向包");
            targetPackageMtime = targetPackageUpgradeDto.getMtime().getTime();
            // 根据定向包id，拿到数据赋给vo的targets字段
            vo.setTargets(targetPackageUpgradeConvertor.convertDto2CpcUnitTargetVo(targetPackageUpgradeDto));
            targetPackageUpgradeConvertor.buildCrowdPackVo(Lists.newArrayList(vo.getTargets()), context.getAccountId());
            targetPackageUpgradeConvertor.extractStartUpCrowds(Lists.newArrayList(vo.getTargets()));
        } else {
            vo.setTarget_package_id(0);
        }
        validateDeviceBrand(vo.getTargets());
        filterNonValidCrowds(vo.getTargets(), aid);
        packStartUpCrowds(vo.getTargets());
        //直播间ID默认值
        if (null != unitDto.getLauSubject()) {
            vo.setMaterial_id(unitDto.getLauSubject().getMaterialId());
        }
        if (!AdpVersion.isLegacy(unitDto.getAdpVersion()) && !Utils.isPositive(vo.getSpeedMode())) {
            // 非旧版数据投放速度在单元上, 如果没传, 使用默认值
            vo.setSpeedMode(SpeedMode.SMOOTH.getCode());
        }

        if (AdpVersion.isMiddle(unitDto.getAdpVersion())) {
            boolean newEnableOcpc = vo.getEnable_ocpc() == null ? false : vo.getEnable_ocpc();
            unitService.verifyOcpx(newEnableOcpc, vo.getSales_type(), unitDto, campaignDto);
        }

        // 2023.4.17 产品：空痕 需求TAPD https://www.tapd.bilibili.co/********/prong/stories/view/11********002969444
        // 阿里UD黑盒需要支持同一个公司组id下不同账号使用不同的xml入库的商品进行隔离，且客户不同意自己传自定义标签进行商品组拆分
        // 所以需要手工在商品上增加自定义标签，用于区分不同账号的商品，同时在单元上根据账号标签写死商品组id进行商品组隔离
        Integer resetProductGroupId = resetProductGroupIdByAccountLabel(context.getAccountId(), campaignDto.getPromotionPurposeType());
        if (resetProductGroupId != null) {
            vo.setProduct_group_id(resetProductGroupId);
        }

        validateUpdate(context, campaignDto, vo, unitDto);

        //目前硬编码 游戏预约目标下 二阶段底价 后续优化部分
        if (Objects.equals(vo.getOcpc_target(), 3) && Objects.nonNull(vo.getTwo_stage_bid())) {
            boolean isSupportGameLowBid = accountLabelService.isAccountIdsInLabels(Collections.singletonList(context.getAccountId()),
                    Collections.singletonList(accLabelConfig.getLabelGameAppointLowestPrice()));
            BigDecimal gameSubscribeLowestTwoStageBid = targetLowestBidProc.fetchLowestBid(isSupportGameLowBid);
            Assert.isTrue(vo.getTwo_stage_bid().compareTo(gameSubscribeLowestTwoStageBid) >= 0,
                    "游戏预约转化目标下，目标转化出价必须大于等于 " + gameSubscribeLowestTwoStageBid + " 元");
        }
        UpdateCpcUnitDto updateCpcUnitDto = converter.updateUnitVoToDto(vo);
        updateCpcUnitDto.setAccountId(unitDto.getAccountId());
        updateCpcUnitDto.setPromotionPurposeType(unitDto.getPromotionPurposeType());
        updateCpcUnitDto.setTargetPackageMtime(targetPackageMtime);
        unitService.updateUnit(updateCpcUnitDto, getOperator(context));

        saveUnitBusinessCategory(vo.getUnit_id(), vo.getTargets(), vo.getTarget_package_id());
        // 编辑单元的时候如果非绑定新版定向包且修改了冷启包的行业, 需要同步修改单元下所有创意
        if (vo.getTargets().getStartUpCrowd() != null) {
            final LauStartUpCrowdsBo startUpCrowdsBo = vo.getTargets().getStartUpCrowd();
            final CpcUnitTargetVo prevVo = CpcUnitTargetVo.builder()
                    .crowd_pack(CpcUnitTargetCrowdPackVo.builder()
                            .crowd_pack_ids(unitDto.getCrowdPackIds())
                            .build())
                    .build();
            extractStartUpCrowds(vo.getUnit_id(), prevVo);
            if (!Utils.isPositive(vo.getTarget_package_id())
                    && (prevVo.getStartUpCrowd() == null || !prevVo.getStartUpCrowd().getCrowdId()
                    .equals(startUpCrowdsBo.getCrowdId()))) {
                adpCpcCreativeService.updateCreativeBusinessCategory(unitDto.getUnitId(),
                        LauCreativeBusinessCategoryBo.builder()
                                .firstCategoryId(startUpCrowdsBo.getLevelOneId())
                                .secondCategoryId(startUpCrowdsBo.getLevelTwoId())
                                .thirdCategoryId(startUpCrowdsBo.getLevelThreeId())
                                .build());
            }
        }
        slotGroupService.updateUnitOcpxInfoOnCreativeChange(unitDto.getUnitId());
        // 搜索广告，保存关键词
        if (CampaignAdType.SEARCH.getCode().equals(campaignDto.getAdType()) || CampaignAdType.ALL.getCode().equals(campaignDto.getAdType())) {
            Integer unitId = vo.getUnit_id();
            if (!CollectionUtils.isEmpty(vo.getSearch_ad_keywords_tag())) {
                // 前端传 search_ad_keywords_tag，为了适配以前的逻辑，需要对 search_ad_keywords进行赋值
                vo.setSearch_ad_keywords(vo.getSearch_ad_keywords_tag().stream()
                        .map(AdStatSearchWordBo::getKeyword).collect(Collectors.toList()));

                vo.getSearch_ad_keywords_tag().forEach(x -> {
                    x.setUnitId(unitId);
                    x.setAccountId(context.getAccountId());
                });
            } else if (!CollectionUtils.isEmpty(vo.getSearch_ad_keywords())) {
                // search_ad_keywords_tag 为空时，使用 search_ad_keywords 为 search_ad_keywords_tag 赋值
                vo.setSearch_ad_keywords_tag(vo.getSearch_ad_keywords().stream()
                        .map(x -> AdStatSearchWordBo.builder().keyword(x)
                                .unitId(unitId)
                                .accountId(context.getAccountId()).build())
                        .collect(Collectors.toList()));
            }
            UnitKeywordsBo unitKeywordsBo = UnitKeywordConverter.MAPPER.fromVo(vo);
            unitKeywordsBo.setAccountId(context.getAccountId());
            unitKeywordsBo.setOperateType(2);
            unitKeywordsBo.setAdType(campaignDto.getAdType());
            unitKeywordsBo.setOldTargetExpand(unitDto.getTargetExpand());
            unitKeywordsBo.setNewTargetExpand(vo.getTarget_expand());
            effectAdSearchAdUnitService.save(unitId, unitKeywordsBo);
        }
        if (needTryInitAssistData) {
            launchUnitAssistSearchService.insertInitDataIfNotExist(vo.getUnit_id());
        }
        return Response.SUCCESS(null);
    }

    private boolean archiveCheck(CpcCampaignDto campaignDto, Context context, UpdateCpcUnitVo vo, CpcUnitDto unitDto) {
        boolean ans = false;
        if (PromotionPurposeType.BRAND_SPREAD.getCode() == campaignDto.getPromotionPurposeType() &&
                Objects.equals(PromotionPurposeType.ARCHIVE_CONTENT.getCode(), unitDto.getPromotionPurposeType())) {
            // 如果是订单提交优化目标，需要校验2个assist属性
            if (accLabelConfig.supportFlyDingdanyouhuaAssist(context.getAccountId())
                    && ObjectUtils.equals(OcpcTargetEnum.GOODS_TRANSACTION.getCode(), vo.getOcpc_target())) {
                goodsArchiveHintProc.checkAssistTargetAndAssistPrice(vo.getAssist_target(), vo.getAssist_price());
                ans = true;
            } else {
                // 如果不是，给这2个属性默认值
                vo.setAssist_target(0);
                vo.setAssist_price(BigDecimal.ZERO);
            }
        }
        return ans;
    }

    private void androidGameCheck(CpcCampaignDto campaignDto, Context context, UpdateCpcUnitVo vo) {
        // 计划是安卓游戏 or 品牌传播且选了游戏包
        if (PromotionPurposeType.ON_SHELF_GAME.getCode() == campaignDto.getPromotionPurposeType() ||
                (PromotionPurposeType.BRAND_SPREAD.getCode() == campaignDto.getPromotionPurposeType() && Utils.isPositive(vo.getGame_base_id()))) {
            // 设备定向必须为"安卓"
            Assert.notNull(vo.getTargets(), "目标不能为空");
            cpcUnitValidator.validateOsMustOnlyAndroid(vo.getTargets().getOs());
            // 智能放量不能突破设备635
            List<Integer> IntelligentMass = Optional.ofNullable(vo).map(o -> o.getTargets())
                    .map(o -> o.getIntelligentMass())
                    .map(o -> o.getTargets()).orElse(new ArrayList<>());
            cpcUnitValidator.validateIntelligentMassNotDevice(IntelligentMass);
        }
    }

    private void setDefaultAreaTypeIfNecessary(UpdateCpcUnitVo vo) {
        if (vo.getTargets() != null && CollectionUtils.isEmpty(vo.getTargets().getArea_type())) {
            vo.getTargets().setArea_type(Arrays.asList(AreaTypeEnum.ALL.getCode()));
        }
    }

    private void validateUpdate(Context context, CpcCampaignDto campaignDto, UpdateCpcUnitVo vo, CpcUnitDto unitDto) {
        Integer ocpcTarget = vo.getOcpc_target();
        if (context.isOpenApi()) {
            // 开放平台 设置 enable_ocpc  0 或者 null 是cpc,  ocpc ocpx只支持2
            if (null == ocpcTarget || 0 == ocpcTarget) {
                vo.setOcpc_target(0);
                vo.setEnable_ocpc(false);
            } else {
                vo.setEnable_ocpc(true);
                String videoIdStr = Objects.isNull(unitDto.getVideoId()) ? null : unitDto.getVideoId().toString();
                ocpcTargetVerify(campaignDto, vo.getUnit_id(), ocpcTarget, vo.getOcpxTargetTwo(), vo.getOcpxTargetTwoBid(),
                        vo.getIsStoreDirectLaunch(), vo.getTargets().getOs(), unitDto.getSalesType(), videoIdStr);
            }

            Assert.isTrue(!Utils.isPositive(campaignDto.getIsManaged()), "不允许编辑托管计划下的单元");
            Assert.isTrue(null == vo.getSales_type() || unitDto.getSalesType().equals(vo.getSales_type()),
                    errorMsgBuild(PARAM_ERROR, "不支持修改sales_type"));
            vo.setSales_type(unitDto.getSalesType());
            //speedMode校验
            speedModeCheck(vo.getSpeedMode());

            if (campaignDto.getPromotionPurposeType() == PromotionPurposeType.GAME_CARD.getCode()) {
                vo.setSales_type(SalesType.CPC.getCode());
            }

            boolean isMiddleFly = AdpVersion.isMiddle(unitDto.getAdpVersion()) &&
                    openApiFlyValidator.judgeIsFly(campaignDto.getPromotionPurposeType(), vo.getOcpc_target(), vo.getSales_type());
            if (!isMiddleFly) {
                Assert.isTrue(null != vo.getCost_price(), errorMsgBuild(PARAM_ERROR, "cost_price必填"));
                Assert.isTrue(StringUtils.isEmpty(vo.getVideo_id()), "当前单元类型不允许在单元级别绑定稿件");
            } else if (PromotionPurposeType.BRAND_SPREAD.getCode() == campaignDto.getPromotionPurposeType()) {
                Assert.isTrue(!StringUtils.isEmpty(vo.getVideo_id()), "起飞类型单元稿件id不可为空");
                Assert.isTrue(unitDto.getVideoId().equals(Long.parseLong(vo.getVideo_id())), "起飞类型的单元稿件id不允许变更");
            } else {
                Assert.isTrue(StringUtils.isEmpty(vo.getVideo_id()), "当前起飞单元类型不允许在单元级别绑定稿件");
            }


            //日预算默认手动
            vo.setDaily_budget_type(DailyBudgetType.MANUAL.getCode());
            // 默认每日
            vo.setFrequency_unit(1);
            //推广目的 参数校验
            if (null != vo.getApp_package_id()) {
                Assert.isTrue((campaignDto.getPromotionPurposeType() == PromotionPurposeType.APP_DOWNLOAD.getCode()
                                || campaignDto.getPromotionPurposeType() == PromotionPurposeType.GOODS_CATALOG.getCode())
                        , errorMsgBuild(PARAM_ERROR, "此推广目的不支持app_package_id"));
            }
            //不支持修改app包
            Integer dayLimitValue = openAPIValidator.getLimitValue(OpenAPILimitType.UNIT, LimitBsiType.DAY_LIMIT, context.getAccountId());
            Assert.isTrue(openAPIValidator.dayCountUnitOpenAPI(context.getAccountId()) < dayLimitValue, errorMsgBuild(DAY_LIMIT, "可用单元个数超过" + dayLimitValue + "日上限"));
            Integer allLimitValue = openAPIValidator.getLimitValue(OpenAPILimitType.UNIT, LimitBsiType.ALL_LIMIT, context.getAccountId());
            Assert.isTrue(openAPIValidator.countUnitOpenAPI(context.getAccountId()) < allLimitValue, errorMsgBuild(ALL_LIMIT, "可用单元个数超过" + allLimitValue + "上限"));


            if (vo.getEnable_ocpc()) {
                Assert.notNull(vo.getTwo_stage_bid(), errorMsgBuild(PARAM_ERROR, "ocpc时two_stage_bid字段必填"));
                Assert.notNull(ocpcTarget, errorMsgBuild(PARAM_ERROR, "ocpc时ocpc_target字段必填"));
            }

            BigDecimal budget = vo.getBudget();
            if (budget.doubleValue() > 9999999.99 || budget.doubleValue() < 100 || budget.scale() > 2 || budget.doubleValue() < 0) {
                Assert.isTrue(false, errorMsgBuild(PARAM_ERROR, "指定预算不符合规则"));
            }

            BigDecimal costPrice = vo.getCost_price();
            if (Objects.nonNull(costPrice) &&
                    (costPrice.doubleValue() < 0
                            || (vo.getSales_type().equals(SalesType.CPC.getCode()) && costPrice.doubleValue() > 20.01)
                            || (vo.getSales_type().equals(SalesType.CPM.getCode()) && costPrice.doubleValue() > 100.01))) {
                Assert.isTrue(false, errorMsgBuild(PARAM_ERROR, "指定预算不符合规则 大于0 cpc小于20 cpm小于100"));
            }
            validateCommon(context, campaignDto, vo.getUnit_name(), vo.getUnit_id(), null, vo);
        } else {
            // roi的优化目标下，必传，并且 > 0
            if (OcpxTarget.isRoiNew(vo.getOcpxTargetTwo())) {
                Assert.isTrue(Utils.isPositive(vo.getOcpxTargetTwoBid()), "ROI出价必须大于0");
            }
        }
        validateTargetPackageUpgradeTargets(campaignDto.getPromotionPurposeType(), null, vo);
    }

    @ApiOperation(value = "启用单元")
    @RequestMapping(params = {"status=1"}, value = "/status", method = RequestMethod.PUT)
    public
    @ResponseBody
    Response<Object> enableUnit(
            @ApiIgnore Context context,
            @RequestBody UpdateStatusRequestBean usrb,
            @RequestParam("status") int status) throws ServiceException {

        if (!accountLabelService.isAccountIdInLabel(context.getAccountId(), oldSanlianAllowEdit)) {
            Assert.isTrue(adpCpcUnitService.getAdp5UnitBos(usrb.getIds()).isEmpty(), "不支持编辑旧版广告单元");
        }
        Assert.isTrue(usrb != null && !CollectionUtils.isEmpty(usrb.getIds()), "ids不能为空");
        unitService.enableUnit(getOperator(context), usrb.getIds());
        return Response.SUCCESS(null);
    }

    @ApiOperation(value = "暂停单元")
    @RequestMapping(params = {"status=2"}, value = "/status", method = RequestMethod.PUT)
    public
    @ResponseBody
    Response<Object> pauseUnit(@ApiIgnore Context context, @RequestBody UpdateStatusRequestBean usrb,
                               @RequestParam("status") int status) throws ServiceException {

        Assert.isTrue(usrb != null && !CollectionUtils.isEmpty(usrb.getIds()), "ids不能为空");
        unitService.pauseUnit(getOperator(context), usrb.getIds());
        return Response.SUCCESS(null);
    }

    @ApiOperation(value = "结束单元")
    @RequestMapping(params = {"status=3"}, value = "/status", method = RequestMethod.PUT)
    public
    @ResponseBody
    Response<Object> finishUnit(
            @ApiIgnore Context context,
            @RequestBody UpdateStatusRequestBean usrb,
            @RequestParam("status") int status) throws ServiceException {

        Assert.isTrue(usrb != null && !CollectionUtils.isEmpty(usrb.getIds()), "ids不能为空");

        cpcUnitService.batchEnd(getOperator(context), usrb.getIds());
        return Response.SUCCESS(null);
    }

    @ApiOperation(value = "删除单元")
    @RequestMapping(params = {"status=4"}, value = "/{uids}", method = RequestMethod.PUT)
    public
    @ResponseBody
    Response<Object> deleteUnit(
            @ApiIgnore Context context,
            @RequestBody UpdateStatusRequestBean usrb,
            @RequestParam("status") int status) throws ServiceException {

        Assert.isTrue(usrb != null && !CollectionUtils.isEmpty(usrb.getIds()), "ids不能为空");
        unitService.deleteUnit(getOperator(context), usrb.getIds());
        // 删除对应的ocpx信息
        slotGroupService.deleteUnitOcpxInfo(usrb.getIds());
        return Response.SUCCESS(null);
    }

    @ApiOperation(value = "修改预算")
    @RequestMapping(value = "/budget", method = RequestMethod.PUT)
    public
    @ResponseBody
    Response<Object> updateBudget(@ApiIgnore Context context,
                                  @RequestParam("unit_id") List<Integer> unitIds,
                                  @RequestParam("budget") BigDecimal budget,
                                  @RequestParam Integer daily_budget_type,
                                  @RequestParam("effect_type") Integer effectType,
                                  @RequestParam(value = "is_repeat", required = false, defaultValue = "0") Integer isRepeat) throws ServiceException {
        boolean inShutdownLabel = accountLabelService.isAccountIdsInLabels(Collections.singletonList(context.getAccountId()), Collections.singletonList(shutDownLabeId));
        LocalDateTime shutDownDate = LocalDateTime.parse(shutdownDateString, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        Assert.isTrue(!inShutdownLabel || LocalDateTime.now().isBefore(shutDownDate), "必选系统已不再维护，请至新三连推广平台进行投放");
        Assert.notNull(unitIds, "单元id不能为空");

        cpcUnitService.batchUpdateCpcUnitBudget(NewBatchUpdateCpcUnitBudgetDto.builder()
                .budget(budget)
                .unitIds(unitIds)
                .dailyBudgetType(daily_budget_type)
                .effectType(effectType)
                .isRepeat(isRepeat)
                .build(), getOperator(context));
        //cpcUnitService.batchUpdateBudget(unitIds, budget, daily_budget_type, getOperator(context));
        return Response.SUCCESS(null);
    }

    @ApiOperation(value = "删除单元次日预算")
    @RequestMapping(value = "/nextday_budget/{unitId}", method = RequestMethod.DELETE)
    @ResponseBody
    public Response<Object> deleteNextdayBudget(@ApiIgnore Context context, @PathVariable("unitId") Integer unitId) throws ServiceException {
        Assert.notNull(unitId, "单元id不能为空");
        CpcUnitDto cpcUnitDto = cpcUnitService.loadCpcUnit(unitId);
        Assert.isTrue(context.getAccountId().equals(cpcUnitDto.getAccountId()), "不能操作不属于你的单元");
        cpcUnitService.deleteCpcUnitNextdayBudget(unitId, getOperator(context));
        return Response.SUCCESS(null);
    }

    @Deprecated
    @ApiOperation(value = "修改出价")
    @RequestMapping(value = "/cost_price", method = RequestMethod.PUT)
    public
    @ResponseBody
    Response<Object> updateCostPrice(@ApiIgnore Context context,
                                     @RequestParam("unit_id") List<Integer> unitIds,
                                     @RequestParam("cost_price") BigDecimal costPrice) throws ServiceException {
        Assert.notNull(unitIds, "单元id不能为空");
        Assert.notNull(costPrice, "出价不能为空");

        cpcUnitService.batchUpdateCostPrice(unitIds, costPrice, getOperator(context));
        return Response.SUCCESS(null);
    }

    @ApiOperation(value = "批量修改出价")
    @RequestMapping(value = "/batch/bid_price", method = RequestMethod.PUT)
    public
    @ResponseBody
    Response<Object> batchUpdateBidPrice(@ApiIgnore Context context,
                                         @RequestParam("unit_ids") List<Integer> unitIds,
                                         @RequestParam("bid_price") BigDecimal bidPrice,
                                         @RequestParam(value = "two_stage_bid", defaultValue = "") BigDecimal twoStageBid) throws ServiceException {
        cpcUnitService.batchUpdateBidPrice(super.getOperator(context), unitIds, bidPrice, twoStageBid);
        return Response.SUCCESS(null);
    }


    @ApiOperation(value = "批量修改出价计算更新结果")
    @RequestMapping(value = "/batch/bid_price", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<UnitPriceListVo> getBatchUpdateBidPrice(@ApiIgnore Context context,
                                                     @ApiParam("unit_ids") @RequestParam @Valid List<Integer> unit_ids,
                                                     @ApiParam("出价方式 11-CPM 12-CPC") @RequestParam @Valid Integer sales_type,
                                                     @ApiParam("修改类型 0-CPX出价 1-OCPX一阶段出价 2-OCPX优化目标出价 3-OCPC深度优化目标出价") @Valid @RequestParam Integer update_type,
                                                     @ApiParam("修改值类型 0-修改至指定价格 1-修改差值 2-修改差值百分比") @Valid @RequestParam Integer update_value_type,
                                                     @ApiParam("更新值 正负") @Valid @RequestParam(value = "update_value") BigDecimal update_value,
                                                     @ApiParam("限制值 上限/下限") @RequestParam(value = "limit", required = false) BigDecimal limit) {
        // 百分比处理
        if (UnitUpdateBidPriceValueTypeEnum.DIFF_VALUE_PERCENT.getCode().equals(update_value_type)) {
            update_value = update_value.divide(BigDecimal.valueOf(100), 2, BigDecimal.ROUND_HALF_DOWN);
        }
        UnitUpdateBidPriceQueryDto queryDto = UnitUpdateBidPriceQueryDto.builder()
                .unitIds(unit_ids)
                .salesType(sales_type)
                .updateType(update_type)
                .updateValueType(update_value_type)
                .updateValue(update_value)
                .limit(limit)
                .build();
        UnitPriceListDto result = cpcUnitService.getBatchUpdateBidPrice(super.getOperator(context), queryDto);
        return Response.SUCCESS(UnitPriceListVo.convertFromDto(result));
    }

    @ApiOperation(value = "批量修改出价新版")
    @RequestMapping(value = "/batch/bid_price/new", method = RequestMethod.PUT)
    public
    @ResponseBody
    Response<Object> batchUpdateBidPriceNew(@ApiIgnore Context context,
                                            @RequestBody UnitUpdateBidPriceVo vo) throws ServiceException {
        vo.validVo();
        cpcUnitService.batchUpdateBidPrice(super.getOperator(context), UnitUpdateBidPriceVo.convertToDto(vo));
        return Response.SUCCESS();
    }

    @ApiOperation(value = "校验预算修改是否合法")
    @RequestMapping(value = "/validate/budget", method = RequestMethod.POST)
    public
    @ResponseBody
    Response<Object> validateBudget(@ApiIgnore Context context,
                                    @RequestParam("unit_id") Integer unitId,
                                    @RequestParam("budget") BigDecimal budget) throws ServiceException {
        CpcUnitDto unit = cpcUnitService.loadCpcUnit(unitId);
        Assert.isTrue(context.getAccountId().equals(unit.getAccountId()), "该单元不存在");

        lauUnitService.validateBudget(unitId, Utils.fromYuanToFen(budget));
        return Response.SUCCESS(null);
    }

    @ApiOperation(value = "校验预算修改是否合法")
    @RequestMapping(value = "/mock/update_app_id/{id}", method = RequestMethod.GET)
    public
    @ResponseBody
    String updateAppId(@ApiIgnore Context context,
                       @PathVariable("id") Integer appId) throws ServiceException {
        cpcUnitService.updateAppIdForAccount(getOperator(context), appId);
        return "ok";
    }

    @ApiOperation(value = "查询单元详情")
    @GetMapping(value = "/detail/{id}")
    public Response<CpcUnitDetailVo> getUnitDetail(
            @ApiIgnore Context context,
            @PathVariable("id") @ApiParam(required = true, value = "unitId", defaultValue = "1") Integer unitId
    ) throws ServiceException {

        if (!Utils.isPositive(unitId)) {
            return Response.SUCCESS();
        }
        val errTitle = "get cpc unit detail";
        AccountAllInfoDto accountAllInfoDto = queryAccountService.getAccountAllInfoFromCache(context.getAccountId());

//        val unit = cpcUnitService.loadCpcUnit(unitId);
        CpcUnitDto unit = cpcUnitServiceDelegate.loadCpcUnit(unitId, true, true, true, true, true);

        ageCusTrans(unit);
        Cat.logEvent("other", "loadCpcUnit", Event.SUCCESS, "loadCpcUnit ok");

        // 根据推广目的 填充单元信息
        unitFillService.fillUnit(unit);
        Assert.isTrue(context.getAccountId().equals(unit.getAccountId()), "您不能操作不属于您的单元");
        LauCampaignPo lauCampaignPo = campaignRepo.fetchCampaign(unit.getCampaignId());

        val cpcUnitVo = converter.dtoToVo(unit);
        val cpcUnitDetailVo = new CpcUnitDetailVo();
        cpcUnitDetailVo.setVideoAvid(cpcUnitVo.getVideo_id());
        cpcUnitDetailVo.setCtime(cpcUnitVo.getCtime());
        cpcUnitDetailVo.setCampaign_promotion_purpose_type(lauCampaignPo.getPromotionPurposeType());
        String promotionPurposeDesc = promotionPurposeDescProc.getPromotionPurposeDesc(lauCampaignPo.getPromotionPurposeType(), accountAllInfoDto.getAccountDto().getDepartmentId());
        cpcUnitDetailVo.setCampaign_promotion_purpose_type_desc(promotionPurposeDesc);
        dealAvIdToBvIdWhenSwitchOpen(cpcUnitVo);
        CpcCampaignDto cpcCampaignDto = cpcCampaignService.loadCpcCampaignDto(unit.getCampaignId());
        cpcUnitDetailVo.setAd_type(cpcCampaignDto.getAdType());
        cpcUnitDetailVo.setAdpVersion(unit.getAdpVersion());
        if (AdpVersion.isMergedInGeneral(unit.getAdpVersion())) {
            cpcUnitDetailVo.setSpeedMode(unit.getSpeedMode());
            cpcUnitDetailVo.setSpeedModeDesc(SpeedMode.getByCode(unit.getSpeedMode()).getName());
        }

        BeanUtils.copyProperties(cpcUnitVo, cpcUnitDetailVo);

        // 单元层级推广目的
        val tryPromotionPurposeType = Try.of(() -> PromotionPurposeType.getByCode(unit.getPromotionPurposeType()));
        if (tryPromotionPurposeType.isFailure()) {
            log.error("{}: promotion purpose type {} unrecognized", errTitle, unit.getPromotionPurposeType());
            return Response.FAIL(WebApiExceptionCode.SYSTEM_ERROR);
        }

        Cat.logEvent("other", "getUnitDetail", Event.SUCCESS, "start");
        Try.run(() -> {
            final KVElemVo.KVElemVoBuilder builder = KVElemVo.builder();
            UnitDetailPromotionContentEnum unitPromotionContentEnum = UnitDetailPromotionContentEnum.NONE;
            ContentDetailVo contentDetailVo = ContentDetailVo.builder().build();
            ContentDetailBo contentDetailBo = ContentDetailBo.empty();
            switch (tryPromotionPurposeType.get()) {
                case ARCHIVE_CONTENT: // 投稿内容(品牌传播，账号推广)
                    unitPromotionContentEnum = UnitDetailPromotionContentEnum.ARCHIVE;
                    ArchiveDetail archiveDetail = unit.getArchiveDetail();
                    if (archiveDetail != null) {
                        final ArchiveBase archive = archiveDetail.getArchive();
                        if (archive != null) {
                            builder.id(archive.getAid())
                                    .name(archive.getDesc());

                            contentDetailBo = promotionContentLinkFacade.queryUnitContentDetail(unitPromotionContentEnum, String.valueOf(archive.getAid()));
                            contentDetailVo.setId(archive.getAid());
                            contentDetailVo.setTitle(archive.getTitle());
                            contentDetailVo.setLink(contentDetailBo.getLink());
                            Cat.logEvent("other", "getUnitDetail", Event.SUCCESS, "archive");
                        }
                    } else {
                        unitPromotionContentEnum = UnitDetailPromotionContentEnum.APP_PACKAGE;
                        final AppPackageDto app = unit.getAppInfo();
                        if (app != null) {
                            builder.id(app.getId())
                                    .name(app.getAppName());
                            contentDetailVo.setId(app.getId());
                            contentDetailVo.setTitle(app.getAppName());
                            Cat.logEvent("other", "getUnitDetail", Event.SUCCESS, "app");
                        }
                    }
                    break;
                case ON_SHELF_GAME: // 安卓游戏
                    unitPromotionContentEnum = UnitDetailPromotionContentEnum.ON_SHELF_GAME;
                    GameDto game = unit.getGameDto();
                    builder.id(game.getGameBaseId())
                            .name(game.getGameName());
                    contentDetailVo.setId(game.getGameBaseId());
                    contentDetailVo.setTitle(game.getGameName());
                    break;
                case GAME_CARD: // 游戏卡
                    unitPromotionContentEnum = UnitDetailPromotionContentEnum.GAME_CARD;
                    game = unit.getGameDto();
                    builder.id(game.getGameBaseId())
                            .name(game.getGameName());
                    contentDetailVo.setId(game.getGameBaseId());
                    contentDetailVo.setTitle(game.getGameName());
                    break;
                case APP_DOWNLOAD: // 应用推广
                case LANDING_PAGE: // 销售线索收集
                case SALE_GOODS: // 店铺推广
                    unitPromotionContentEnum = UnitDetailPromotionContentEnum.APP_PACKAGE;
                    final AppPackageDto app = unit.getAppInfo();
                    if (app != null) {
                        builder.id(app.getId())
                                .name(app.getAppName());
                        contentDetailVo.setId(app.getId());
                        contentDetailVo.setTitle(app.getAppName());
                    }
                    break;
                case SHOP_GOODS: // 会员购
                    unitPromotionContentEnum = UnitDetailPromotionContentEnum.MEMBER_SHOP_GOODS;
                    final DpaShopGoodsDto goods = unit.getShopGoodsDto();
                    builder.id(goods.getGoodsId())
                            .name(goods.getName());
                    contentDetailVo.setId(goods.getGoodsId());
                    contentDetailVo.setTitle(goods.getName());
                    break;
                case DYNAMIC: // 动态
                    unitPromotionContentEnum = UnitDetailPromotionContentEnum.DYNAMIC;
                    DynamicBo dynamicBo = unit.getDynamic();
                    if (dynamicBo != null) {
                        builder.id(dynamicBo.getDynamicId())
                                .name(dynamicBo.getOuterJumlUrl());
                        contentDetailVo.setId(dynamicBo.getDynamicId());
                        contentDetailVo.setTitle(dynamicBo.getDynamicContent());
                        contentDetailVo.setLink(dynamicBo.getOuterJumlUrl());
                    }
                    break;
                case LIVE_ROOM:
                case GOODS_LIVE: // 品牌传播-带货直播22，直播推广下直播间
                    unitPromotionContentEnum = UnitDetailPromotionContentEnum.LIVE_ROOM;
                    LauSubjectDto lauSubject = unit.getLauSubject();
                    if (lauSubject != null) {
                        builder.id(lauSubject.getMaterialId());
                        contentDetailBo = promotionContentLinkFacade.queryUnitContentDetail(unitPromotionContentEnum, String.valueOf(lauSubject.getMaterialId()));
                        contentDetailVo.setId(lauSubject.getMaterialId());
                        contentDetailVo.setTitle(contentDetailBo.getTitle());
                        contentDetailVo.setLink(contentDetailBo.getLink());
                        Cat.logEvent("other", "getUnitDetail", Event.SUCCESS, "live room");
                    }

                    // 直播预约
                    LiveReservationInfoBo liveReserve = unit.getLiveReserve();
                    if (liveReserve != null) {
                        unitPromotionContentEnum = UnitDetailPromotionContentEnum.LIVE_RESERVE;
                        builder.id(liveReserve.getRoomId());
                        contentDetailBo = promotionContentLinkFacade.queryUnitContentDetail(unitPromotionContentEnum, String.valueOf(liveReserve.getRoomId()));
                        contentDetailVo.setId(liveReserve.getRoomId());
                        contentDetailVo.setTitle(contentDetailBo.getTitle());
                        contentDetailVo.setLink(contentDetailBo.getLink());
                        Cat.logEvent("other", "getUnitDetail", Event.SUCCESS, "live reserve1");
                    }
                    break;
                case LIVE_RESERVE:
                    // 直播预约
                    liveReserve = unit.getLiveReserve();
                    if (liveReserve != null) {
                        unitPromotionContentEnum = UnitDetailPromotionContentEnum.LIVE_RESERVE;
                        builder.id(liveReserve.getRoomId());
                        contentDetailBo = promotionContentLinkFacade.queryUnitContentDetail(unitPromotionContentEnum, String.valueOf(liveReserve.getRoomId()));
                        contentDetailVo.setId(liveReserve.getRoomId());
                        contentDetailVo.setTitle(contentDetailBo.getTitle());
                        contentDetailVo.setLink(contentDetailBo.getLink());
                        Cat.logEvent("other", "getUnitDetail", Event.SUCCESS, "live reserve2");
                    }
                    break;
                case GOODS: // 商品
                    unitPromotionContentEnum = UnitDetailPromotionContentEnum.LIVE_GOODS;
                    GoodsBo unitGoods = unit.getGoods();
                    if (unitGoods != null) {
                        builder.id(unitGoods.getItemId())
                                .name(unitGoods.getSchemaUrl());

                        contentDetailVo.setId(unitGoods.getItemId());
                        contentDetailVo.setTitle(unitGoods.getItemName());
                        contentDetailVo.setLink(unitGoods.getJumpLink());
                    }
                    break;
                case OGV:
                    unitPromotionContentEnum = UnitDetailPromotionContentEnum.OGV;
                    PgcArchiveBo pgcArchiveBo = unit.getPgcArchiveBo();
                    if (pgcArchiveBo != null) {
                        builder.id(pgcArchiveBo.getAid());
                        contentDetailBo = promotionContentLinkFacade.queryUnitContentDetail(unitPromotionContentEnum, String.valueOf(pgcArchiveBo.getAid()));
                        contentDetailVo.setId(pgcArchiveBo.getAid());
                        contentDetailVo.setTitle(pgcArchiveBo.getTitle());
                        contentDetailVo.setLink(contentDetailBo.getLink());
                        Cat.logEvent("other", "getUnitDetail", Event.SUCCESS, "ogv");
                    }
                    break;
                case ENTERPRISE_PROMOTION:
                    unitPromotionContentEnum = UnitDetailPromotionContentEnum.LANDING_PAGE;
                    contentDetailBo = promotionContentLinkFacade.queryUnitContentDetail(unitPromotionContentEnum, null);
                    contentDetailVo.setTitle(contentDetailBo.getTitle());
                    contentDetailVo.setLink(contentDetailBo.getLink());
                    break;
                default:
                    // 落地页, 游戏活动卡，商品目录等其他类别: 什么都不做
            }
            cpcUnitDetailVo.setPromotionContent(builder.build());
            cpcUnitDetailVo.setUnit_promotion_content(unitPromotionContentEnum.getDesc());
            cpcUnitDetailVo.setContent_detail_vo(contentDetailVo);
        }).onFailure(t -> log.error("{}: failed to find {} info attached to unit {}", errTitle, tryPromotionPurposeType.get().getDesc(), unit.getUnitId()));

        Cat.logEvent("other", "loadCpcUnit", Event.SUCCESS, "set content ok");

        if (AdpVersion.isLegacy(unit.getAdpVersion())) {
            // 旧版包含slotGroupId
            val slotGroup = resSlotGroupService.getGroupById(unit.getSlotGroup());
            if (slotGroup != null) {
                cpcUnitDetailVo.setSlotGroup(
                        KVElemVo.builder()
                                .id(unit.getSlotGroup())
                                .name(slotGroup.getSlotGroupName())
                                .build());
            }
        }

        if (Utils.isPositive(cpcUnitVo.getTarget_package_id())) {
            cpcUnitDetailVo.setTargetPackageId(cpcUnitVo.getTarget_package_id());
            cpcUnitDetailVo.setTargetPackageName(cpcUnitVo.getTarget_package_name());
        }
        // 定向处理
        val targets = cpcUnitVo.getTargets();
        if (targets != null) {
            // 获取定向树
            val rawMap = queryTargetsByAccountId(context.getAccountId());
            val map = new HashMap<String, List<TreeKVElemVo>>();
            cpcUnitDetailVo.setTargetMap(map);

            rawMap.forEach((k, v) -> {
                List<Integer> list;
                boolean ignore = false;
                if (k.equals(TargetType.AGE.getByName())) {
                    list = targets.getAge();
                } else if (k.equals(TargetType.GENDER.getByName())) {
                    list = targets.getGender();
                } else if (k.equals(TargetType.AREA.getByName())) {
                    list = targets.getArea();
                } else if (k.equals(TargetType.APP_CATEGORY.getByName())) {
                    list = targets.getApp_category();
                } else if (k.equals(UnitConverter.UNIT_TARGET_BUSINESS_INTEREST_KEY)) {
                    list = targets.getBusiness_interest();
                } else if (k.equals(TargetType.OS.getByName())) {
                    list = targets.getOs();
                } else if (k.equals(TargetType.DEVICE_BRAND.getByName())) {
                    list = targets.getDevice_brand();
                } else if (k.equals(TargetType.NETWORK.getByName())) {
                    list = targets.getNetwork();
                } else if (k.equals(TargetType.CATEGORY.getByName())) {
                    list = targets.getCategory();
                } else if (k.equals(TargetType.CONVERTED_USER_FILTER.getByName())) {
                    list = targets.getConverted_user_filter();
                } else if (k.equals(TargetType.INTELLIGENT_MASS.getByName())) {
                    list = targets.getIntelligentMass().getTargets();
                } else if (k.equals(TargetType.VIDEO_PARTITION.getByName())) {
                    list = targets.getVideoPartition();
                } else if (k.equals(TargetType.AREA_TYPE.getByName())) {
                    list = targets.getArea_type();
                } else if (k.equals(TargetType.AREA_LEVEL.getByName())) {
                    list = targets.getArea_level();
                } else if (k.equals(TargetType.PHONE_PRICE.getByName())) {
                    list = targets.getPhone_price();
                } else if (k.equals(PROFESSION_INTEREST.getByName())) {
                    list = targets.getProfession_interest();
                } else {
                    list = Collections.emptyList();
                    ignore = true;
                }

                // 根据 list 生成定向 map
                if (!ignore) {
                    if (list != null) {
                        final Set<Integer> set = new HashSet<>(list);
                        map.put(k, v.stream()
                                .filter(x -> set.contains(x.getId()))
                                .collect(Collectors.toList()));
                    } else {
                        map.put(k, Collections.emptyList());
                    }
                }
            });


            //行业优选定向
            Integer profession_auto = targets.getProfession_auto();
            List<TreeKVElemVo> autoKVElemVos = new ArrayList<>();
            if (IsInterestAuto.IS_AUTO == profession_auto) {
                autoKVElemVos.add(TreeKVElemVo.builder().id(profession_auto).name(PROFESSION_AUTO_DESC).build());
            }
            map.put(PROFESSION_AUTO, autoKVElemVos);

            // 操作系统版本
            List<TreeKVElemVo> treeKVElemVos = rawMap.get(OS_VERSION);
            if (!CollectionUtils.isEmpty(treeKVElemVos)) {
                map.put(OS_VERSION, treeKVElemVos);
            }

            val intelligentMass = targets.getIntelligentMass();
            if (Objects.nonNull(intelligentMass) && !CollectionUtils.isEmpty(intelligentMass.getExtraCrowdPackIds())) {
                val crowdKVList = rawMap.get(UnitConverter.UNIT_TARGET_CROWD_PACK_KEY);
                if (!CollectionUtils.isEmpty(crowdKVList)) {
                    final Map<Integer, TreeKVElemVo> crowdMap = crowdKVList
                            .stream()
                            .collect(Collectors.toMap(x -> (Integer) x.getId(), Function.identity()));
                    val extraCrowdPackIds = intelligentMass.getExtraCrowdPackIds();
                    final Function<List<Integer>, List<TreeKVElemVo>> convertFunc = x -> x
                            .stream()
                            .map(crowdMap::get)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
                    map.put(EXTRA_CROWD_PACKS, convertFunc.apply(extraCrowdPackIds));
                }
            }

            val archiveContent = targets.getArchive_content();
            if (archiveContent != null) {
                val includedFans = archiveContent.getInclude_theirs_fans();
                if (includedFans != null) {
                    map.put(INCLUDED_FANS, getUpInfoList(includedFans).stream()
                            .map(TreeKVElemVo::fromKVElemVo)
                            .collect(Collectors.toList()));
                }
                val excludedFans = archiveContent.getExclude_theirs_fans();
                if (excludedFans != null) {
                    map.put(EXCLUDED_FANS, getUpInfoList(excludedFans).stream()
                            .map(TreeKVElemVo::fromKVElemVo)
                            .collect(Collectors.toList()));
                }
                val videoSecondPartitions = archiveContent.getVideo_second_partition();
                if (videoSecondPartitions != null) {
                    val allPartitions = archiveManager.getAllVideoPartitions();
                    if (allPartitions != null && !allPartitions.isEmpty() && !videoSecondPartitions.isEmpty()) {
                        val partitionSet = new HashSet<>(videoSecondPartitions);
                        map.put(VIDEO_SECOND_PARTITIONS, allPartitions
                                .stream()
                                .filter(x -> partitionSet.contains(x.getId()))
                                .map(x -> TreeKVElemVo.builder()
                                        .id(x.getId())
                                        .name(x.getName())
                                        .build())
                                .collect(Collectors.toList()));
                    }
                }
                val recommendType = archiveContent.getRecommend_type();
                if (recommendType != null && recommendType > 0) {
                    map.put(RECOMMEND_UPPER_TYPE, Arrays.asList(TreeKVElemVo.builder()
                            .id(recommendType)
                            .name(RecommendTypeEnum.getByCode(recommendType).getDesc())
                            .build()));
                } else {
                    map.put(RECOMMEND_UPPER_TYPE, new ArrayList<>());
                }
            }
            map.putIfAbsent(INCLUDED_FANS, Collections.emptyList());
            map.putIfAbsent(EXCLUDED_FANS, Collections.emptyList());
            map.putIfAbsent(VIDEO_SECOND_PARTITIONS, Collections.emptyList());
            val crowdPack = targets.getCrowd_pack();
            val crowdKVList = rawMap.get(UnitConverter.UNIT_TARGET_CROWD_PACK_KEY);
            if (crowdPack != null && crowdKVList != null) {
                final Map<Integer, TreeKVElemVo> crowdMap = crowdKVList
                        .stream()
                        .collect(Collectors.toMap(x -> (Integer) x.getId(), Function.identity(), (m1, m2) -> m2));
                val includedCrowdPack = crowdPack.getCrowd_pack_ids();
                final Function<List<Integer>, List<TreeKVElemVo>> convertFunc = x -> x
                        .stream()
                        .map(crowdMap::get)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
                if (includedCrowdPack != null) {
                    map.put(INCLUDED_CROWD_PACKS, convertFunc.apply(includedCrowdPack));
                }

                val excludedCrowdPack = crowdPack.getExclude_crowd_pack_ids();
                if (excludedCrowdPack != null) {
                    map.put(EXCLUDED_CROWD_PACKS, convertFunc.apply(excludedCrowdPack));
                }
            }
            map.putIfAbsent(INCLUDED_CROWD_PACKS, Collections.emptyList());
            map.putIfAbsent(EXCLUDED_CROWD_PACKS, Collections.emptyList());

//            val ocpcMap = cpcUnitService.getOcpcTargetByAccountId(context.getAccountId(),
//                    cpcUnitVo.getPromotion_purpose_type(),
//                    targets.getOs(),
//                    cpcUnitVo.getSales_type(),
//                    AdpVersion.isLegacy(unit.getAdpVersion()) ? cpcUnitVo.getSlot_group_id() : null)
//                    .stream()
//                    .collect(Collectors.toMap(OcpcTargetDto::getId, OcpcTargetDto::getName));
//            if (cpcUnitVo.getOcpc_target() != null && ocpcMap.containsKey(cpcUnitVo.getOcpc_target())) {
//                cpcUnitDetailVo.setOcpcTargetDesc(ocpcMap.get(cpcUnitVo.getOcpc_target()));
//            }
            if (Utils.isPositive(cpcUnitVo.getOcpc_target())) {
                cpcUnitDetailVo.setOcpcTargetDesc(OcpcTargetEnum.getByCode(cpcUnitVo.getOcpc_target()).getDesc());
            }
            if (Utils.isPositive(cpcUnitVo.getOcpxTargetTwo())) {
                cpcUnitDetailVo.setOcpxTargetTwoDesc(OcpcTargetEnum.getByCode(cpcUnitVo.getOcpxTargetTwo()).getDesc());
            }
        } else {
            cpcUnitDetailVo.setTargetMap(Collections.emptyMap());
        }
        if (cpcUnitDetailVo.getOcpcTargetDesc() == null) {
            cpcUnitDetailVo.setOcpcTargetDesc("");
        }
        extractStartUpCrowds(unitId, cpcUnitDetailVo.getTargets());

        // 设置小游戏定向配置信息
        MiniGameTargetDto miniGameDto = cpcUnitService.getUnitMiniGameTargetInfo(unitId);
        if (miniGameDto != null) {
            CpcUnitMiniGameTargetVo miniGameTargetVo = new CpcUnitMiniGameTargetVo();
            int type = (CollectionUtils.isEmpty(miniGameDto.getIncludeGameIds()) && CollectionUtils.isEmpty(miniGameDto.getIncludeGameIds())) ? 0 : 1;
            miniGameTargetVo.setType(type);
            miniGameTargetVo.setInclude_mini_game_ids(miniGameDto.getIncludeGameIds());
            miniGameTargetVo.setExclude_mini_game_ids(miniGameDto.getExcludeGameIds());

            if (cpcUnitDetailVo.getTargets() != null) {
                cpcUnitDetailVo.getTargets().setMini_game(miniGameTargetVo);
            }

            Map<String, String> miniGameItemMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(miniGameDto.getIncludeGameIds())) {
                List<MiniGameMapDto> miniGames = cpcUnitService.queryMiniGameMapByAppIds(miniGameDto.getIncludeGameIds());
                for (MiniGameMapDto item : miniGames) {
                    miniGameItemMap.putIfAbsent(item.getAppId(), item.getName());
                }
            }

            if (!CollectionUtils.isEmpty(miniGameDto.getExcludeGameIds())) {
                List<MiniGameMapDto> miniGames = cpcUnitService.queryMiniGameMapByAppIds(miniGameDto.getExcludeGameIds());
                for (MiniGameMapDto item : miniGames) {
                    miniGameItemMap.putIfAbsent(item.getAppId(), item.getName());
                }
            }

            List<TreeKVElemVo> includeIds = (miniGameDto.getIncludeGameIds() == null) ? new ArrayList<>() : miniGameDto.getIncludeGameIds().stream()
                    .map(item -> {
                        TreeKVElemVo treeNode = new TreeKVElemVo();
                        treeNode.setId(item);
                        treeNode.setName(miniGameItemMap.get(item));
                        treeNode.setDepth(0);
                        treeNode.setPid(null);

                        return treeNode;
                    }).collect(Collectors.toList());


            List<TreeKVElemVo> excludeIds = (miniGameDto.getExcludeGameIds() == null) ? new ArrayList<>() : miniGameDto.getExcludeGameIds().stream()
                    .map(item -> {
                        TreeKVElemVo treeNode = new TreeKVElemVo();
                        treeNode.setId(item);
                        treeNode.setName(miniGameItemMap.get(item));
                        treeNode.setDepth(0);
                        treeNode.setPid(null);

                        return treeNode;
                    }).collect(Collectors.toList());

            cpcUnitDetailVo.getTargetMap().put("include_mini_game_ids", includeIds);
            cpcUnitDetailVo.getTargetMap().put("exclude_mini_game_ids", excludeIds);
        } else {
            CpcUnitMiniGameTargetVo miniGameTargetVo = new CpcUnitMiniGameTargetVo();
            miniGameTargetVo.setType(0);
            miniGameTargetVo.setInclude_mini_game_ids(Collections.emptyList());
            miniGameTargetVo.setExclude_mini_game_ids(Collections.emptyList());

            if (cpcUnitDetailVo.getTargets() != null) {
                cpcUnitDetailVo.getTargets().setMini_game(miniGameTargetVo);

                cpcUnitDetailVo.getTargetMap().put("include_mini_game_ids", Collections.emptyList());
                cpcUnitDetailVo.getTargetMap().put("exclude_mini_game_ids", Collections.emptyList());
            }
        }
        // 设置商品组信息
        cpcUnitDetailVo.setProduct_group(getProductGroupVo(tryPromotionPurposeType.get(), unit.getProductGroupId(),
                getOperator(context).getOperatorId()));
        if (AdpVersion.isMiddle(unit.getAdpVersion())) {
            // 辅助价格正常传值
            cpcUnitDetailVo.setAssistPrice(Utils.fromFenToYuan(unit.getAssistPrice()));
            // 这里强制处理老数据，必须展示辅助目标为应用唤起
            if (accLabelConfig.supportFlyDingdanyouhuaAssist(context.getAccountId()) &&
                    OcpcTargetEnum.GOODS_TRANSACTION.getCode().equals(unit.getOcpcTarget())) {
                cpcUnitDetailVo.setAssistTarget(OcpcTargetEnum.LP_CALL_UP_SUCCESS.getCode());
                cpcUnitDetailVo.setAssistTargetDesc(OcpcTargetEnum.LP_CALL_UP_SUCCESS.getDesc());
            } else {
                cpcUnitDetailVo.setAssistTarget(0);
                cpcUnitDetailVo.setAssistTargetDesc("-");
            }
        }

        cpcUnitDetailVo.setSearchPriceCoefficient(Utils.fromFenToYuan(new BigDecimal(unit.getSearchPriceCoefficient())));
        if (unit.getSearchFirstPriceCoefficient() != null) {
            cpcUnitDetailVo.setSearchFirstPriceCoefficient(Utils.fromFenToYuan(new BigDecimal(unit.getSearchFirstPriceCoefficient())));
        }
        //搜索明投词
        buildSearchOvertWords(unitId, cpcUnitDetailVo::setSearchOvertWords, cpcUnitDetailVo::setNegateSearchOvertWords);

        // 新三连兜底
        if (AdpVersion.isCpcFlyMerge(cpcUnitDetailVo.getAdpVersion())
                && SalesType.CPC.getCode() == cpcUnitDetailVo.getSales_type()
                && Utils.isPositive(cpcUnitDetailVo.getOcpc_target())) {
            cpcUnitDetailVo.setSales_type(SalesType.CPM.getCode());
            cpcUnitDetailVo.setSales_type_desc(SalesType.CPM.getDesc());
        }
        return Response.SUCCESS(cpcUnitDetailVo);
    }

    private void buildSearchOvertWords(Integer unitId, Consumer<List<String>> searchWordConsumer, Consumer<List<String>> negSearchWordConsumer) {
        UnitKeywordsBo unitKeywordsBo = effectAdSearchAdUnitService.fetch(unitId);
        searchWordConsumer.accept(unitKeywordsBo.getKeywords());
        ArrayList<String> allNegWords = new ArrayList<>(unitKeywordsBo.getNegPreciseKeywords());
        allNegWords.addAll(unitKeywordsBo.getNegTermKeywords());
        negSearchWordConsumer.accept(allNegWords);
    }

    private CpcProductGroupVo getProductGroupVo(PromotionPurposeType purposeType, Integer productGroupId, Integer accountId) {
        if (purposeType.equals(PromotionPurposeType.GOODS_CATALOG)) {
            LauProductGroupDto productGroupDto = lauProductGroupService.getGroupById(productGroupId, accountId);
            if (productGroupDto != null) {
                return cpcProductConverter.getCpcProductGroupVo(productGroupDto, null);
            }
        }
        return null;
    }

    /**
     * 保存单元的商业分类
     *
     * @param unitId
     * @param targetVo
     */
    private void saveUnitBusinessCategory(int unitId, CpcUnitTargetVo targetVo, Integer targetPackageId) {
        if (Utils.isPositive(targetPackageId)) {
            return;
        }
        if (targetVo == null) {
            adpCpcLauUnitBusinessCategoryService.deleteByUnitId(unitId);
            return;
        }
        final LauStartUpCrowdsBo bo = targetVo.getStartUpCrowd();
        if (bo == null) {
            adpCpcLauUnitBusinessCategoryService.deleteByUnitId(unitId);
            return;
        }
        adpCpcLauUnitBusinessCategoryService.insertOrUpdate(LauUnitBusinessCategoryBo.builder()
                .unitId(unitId)
                .firstCategoryId(bo.getLevelOneId())
                .secondCategoryId(bo.getLevelTwoId())
                .thirdCategoryId(bo.getLevelThreeId())
                .build());
    }

    /**
     * 打包冷启人群包
     *
     * @param targetVo
     * @throws ServiceException
     */
    private void packStartUpCrowds(CpcUnitTargetVo targetVo) throws ServiceException {
        if (targetVo == null || targetVo.getCrowd_pack() == null || targetVo.getCrowd_pack().getCrowd_pack_ids() == null) {
            return;
        }
        final LauStartUpCrowdsBo startUpCrowdsBo = targetVo.getStartUpCrowd();
        if (startUpCrowdsBo != null) {
            if (startUpCrowdsBo.getLevelOneId() == null
                    || startUpCrowdsBo.getLevelTwoId() == null
                    || startUpCrowdsBo.getLevelThreeId() == null) {
                throw new ServiceException("冷启包必须指定全部三层的层级结构");
            }
            final LauStartUpCrowdsBo bo = adpCpcLauStartUpCrowdsService.getByHierarchy(
                    startUpCrowdsBo.getLevelOneId(),
                    startUpCrowdsBo.getLevelTwoId(),
                    startUpCrowdsBo.getLevelThreeId());
            if (bo == null) {
                throw new ServiceException("找不到对应的冷启包");
            }
            // 冷启包按照普通的包含人群包处理
            targetVo.getCrowd_pack().getCrowd_pack_ids().add(bo.getCrowdId());
        }
    }

    // 解包冷启人群包
    public void extractStartUpCrowds(int unitId, CpcUnitTargetVo targetVo) {
        if (targetVo == null || targetVo.getCrowd_pack() == null || targetVo.getCrowd_pack().getCrowd_pack_ids() == null) {
            return;
        }
        final List<Integer> crowdPackIds = targetVo.getCrowd_pack().getCrowd_pack_ids();
        final LauStartUpCrowdsBo bo = launchCommonService.getStartUpCrowds(crowdPackIds, unitId);
        if (bo == null) {
            return;
        }
        targetVo.getCrowd_pack().setCrowd_pack_ids(Collections.emptyList());
        targetVo.setStartUpCrowd(bo);
    }

    /**
     * 获取账户的定向树
     *
     * @param accountId
     * @return
     * @throws ServiceException
     */
    public Map<String, List<TreeKVElemVo>> queryTargetsByAccountId(Integer accountId) throws ServiceException {

        // 获取定向树
        Map<TargetType, List<TargetTreeDto>> allValidTarget2ItemTreeMap = resTargetItemService.getAllValidTarget2ItemTreeMap();
        final Map<String, List<TreeKVElemVo>> map = allValidTarget2ItemTreeMap
                .entrySet()
                .stream()
                .map(kv -> new AbstractMap.SimpleEntry<>(kv.getKey().getByName(), kv.getValue()
                        .stream()
                        .flatMap(x -> CommonFunctions.bfs(x, TargetTreeDto::getChildren, TargetTreeDto::getId))
                        .filter(Objects::nonNull)
                        .map(x -> TreeKVElemVo.builder()
                                .id(x._1.getId())
                                .name(x._1.getName())
                                .depth(x._2)
                                .pid(x._3)
                                .build())
                        .collect(Collectors.toList())
                ))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

        // 操作系统版本
        List<TargetTreeDto> osTargetTreeDtos = allValidTarget2ItemTreeMap.get(TargetType.OS);
        List<TreeKVElemVo> osTreeKVElemVos = convertTargetTreeDtos2TreeKVElemVos(osTargetTreeDtos);
        map.put(UnitConverter.OS_VERSION, osTreeKVElemVos);

        map.put(UnitConverter.UNIT_TARGET_CROWD_PACK_KEY, converter.getCrowdPackVosByAccountId(accountId)
                .stream()
                .flatMap(x -> CommonFunctions.bfs(x, CrowdPackVo::getChilds, CrowdPackVo::getId))
                .filter(Objects::nonNull)
                .map(x -> TreeKVElemVo.builder()
                        .id(x._1.getId())
                        .name(x._1.getName())
                        .depth(x._2)
                        .pid(x._3)
                        .build())
                .collect(Collectors.toList()));
        map.put(UnitConverter.UNIT_TARGET_BUSINESS_INTEREST_KEY, converter.getBusinessInterestVos(accountId)
                .stream()
                .flatMap(x -> CommonFunctions.bfs(x, BusinessInterestVo::getChildren, BusinessInterestVo::getId))
                .filter(Objects::nonNull)
                .map(x -> TreeKVElemVo.builder()
                        .id(x._1.getId())
                        .name(x._1.getName())
                        .depth(x._2)
                        .pid(x._3)
                        .build())
                .collect(Collectors.toList()));

        map.put(UnitConverter.UNIT_TARGET_PROFESSION_INTEREST_KEY, converter.getProfessionInterest()
                .stream()
                .flatMap(x -> CommonFunctions.bfs(x, TreeUtils::getChildren, TreeUtils::getId))
                .filter(Objects::nonNull)
                .map(x -> TreeKVElemVo.builder()
                        .id(x._1.getId())
                        .name(x._1.getName())
                        .depth(x._2)
                        .pid(x._3)
                        .build())
                .collect(Collectors.toList()));
        return map;
    }

    private static List<TreeKVElemVo> convertTargetTreeDtos2TreeKVElemVos(List<TargetTreeDto> osTargetTreeDtos) {
        if (CollectionUtils.isEmpty(osTargetTreeDtos)) {
            return Collections.EMPTY_LIST;
        }

        return osTargetTreeDtos.stream().map(t -> {
            TreeKVElemVo treeKVElemVo = TreeKVElemVo.builder()
                    .id(t.getId())
                    .name(t.getName())
                    .build();

            List<TargetTreeDto> children = t.getChildren();
            if (!CollectionUtils.isEmpty(children)) {
                List<TreeKVElemVo> treeKVElemVos = convertTargetTreeDtos2TreeKVElemVos(children);
                treeKVElemVo.setChildren(treeKVElemVos);
            }
            return treeKVElemVo;
        }).collect(Collectors.toList());
    }

    private List<KVElemVo> getUpInfoList(List<Long> midList) throws ServiceException {
        if (CollectionUtils.isEmpty(midList)) {
            return Collections.emptyList();
        }
        Map<Long, UserInfoDto> userMapInMids = passportService.getUserMapInMids(midList
                .stream()
                .distinct()
                .collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(userMapInMids)) {
            return Collections.emptyList();
        }

        return userMapInMids.values().stream()
                .map(user -> KVElemVo.builder()
                        .id(user.getMid())
                        .name(user.getName())
                        .build())
                .collect(Collectors.toList());
    }

    @ApiOperation(value = "查询单元")
    @GetMapping(value = "/{id}")
    @ResponseBody
    public Response<CpcUnitVo> queryUnit(
            @ApiIgnore Context context,
            @PathVariable("id")
            @ApiParam(required = true, value = "unitId", defaultValue = "1")
            Integer unitId) throws ServiceException {

        CpcUnitDto unit = cpcUnitService.loadCpcUnit(unitId);
        ageCusTrans(unit);
        List<Integer> labelIdsByAccountId = accountLabelService.getLabelIdsByAccountId(context.getAccountId());
//        if (!labelIdsByAccountId.contains(supportProfessionInterest)){
//            //不在行业兴趣人群白名单不展示professionInterest
//            unit.setProfessionInterestIds(Collections.emptyList());
//        }
        unitFillService.fillUnit(unit);
        Assert.isTrue(context.getAccountId().equals(unit.getAccountId()), "您不能操作不属于您的单元");
        // 根据推广目的 填充单元信息
        //unitFillService.fillUnit(unit);
        CpcUnitVo cpcUnitVo = converter.dtoToVo(unit);
        // 设置小游戏定向配置信息
        MiniGameTargetDto miniGameDto = cpcUnitService.getUnitMiniGameTargetInfo(unitId);
        LOGGER.info("unit mini game info: {}", JSON.toJSONString(miniGameDto));
        if (miniGameDto != null) {
            CpcUnitMiniGameTargetVo miniGameTargetVo = new CpcUnitMiniGameTargetVo();
            int type = (CollectionUtils.isEmpty(miniGameDto.getIncludeGameIds()) && CollectionUtils.isEmpty(miniGameDto.getIncludeGameIds())) ? 0 : 1;
            miniGameTargetVo.setType(type);
            miniGameTargetVo.setInclude_mini_game_ids(miniGameDto.getIncludeGameIds());
            miniGameTargetVo.setExclude_mini_game_ids(miniGameDto.getExcludeGameIds());

            if (cpcUnitVo.getTargets() != null) {
                cpcUnitVo.getTargets().setMini_game(miniGameTargetVo);
            }
        } else {
            CpcUnitMiniGameTargetVo miniGameTargetVo = new CpcUnitMiniGameTargetVo();
            miniGameTargetVo.setType(0);
            miniGameTargetVo.setInclude_mini_game_ids(Collections.emptyList());
            miniGameTargetVo.setExclude_mini_game_ids(Collections.emptyList());

            if (cpcUnitVo.getTargets() != null) {
                cpcUnitVo.getTargets().setMini_game(miniGameTargetVo);
            }
        }

        dealAvIdToBvIdWhenSwitchOpen(cpcUnitVo);
        final int adpVersion = unit.getAdpVersion();
        if (!Objects.equals(AdpVersion.LEGACY.getKey(), adpVersion)) {
            final int bidPrice = unit.getCostPrice();
            cpcUnitVo.setUnderbid(launchCommonService.isUnitUnderBid(unitId, bidPrice));
        }
        extractStartUpCrowds(unitId, cpcUnitVo.getTargets());
        cpcUnitVo.setIsAbleToCreateProgrammatic(adpCpcUnitService.isAbleToCreateProgrammatic(unitId));
        cpcUnitVo.setIsProgrammatic(adpCpcUnitService.isProgrammatic(unitId));

        // 选择oCPX优化目标为落地页调起店铺的单元，创意上的卡片调起链接隐藏
        // 游戏卡单元，创意上的卡片调起链接隐藏
        boolean isGameCardUnit = Objects.equals(unit.getPromotionPurposeType(), PromotionPurposeType.GAME_CARD.getCode())
                || Objects.equals(unit.getPromotionPurposeType(), PromotionPurposeType.GAME_ACTIVITY_CARD.getCode());
        //https://www.tapd.bilibili.co/********/prong/stories/view/11********002853700
//        if (Objects.equals(OcpcTargetEnum.LP_CALLUP.getCode(),cpcUnitVo.getOcpc_target()) ||
//                Objects.equals(OcpcTargetEnum.LP_CALL_UP_SUCCESS.getCode(),cpcUnitVo.getOcpc_target()) || isGameCardUnit) {
//            cpcUnitVo.set_support_scheme(false);
//        }else {
//            cpcUnitVo.set_support_scheme(true);
//        }
        cpcUnitVo.set_support_scheme(!isGameCardUnit);
        // 设置商品组信息
        cpcUnitVo.setProduct_group(getProductGroupVo(PromotionPurposeType.getByCode(unit.getPromotionPurposeType()),
                unit.getProductGroupId(), getOperator(context).getOperatorId()));
        // 游戏分包修改显示文案
        if (Objects.nonNull(cpcUnitVo.getGame_detail())) {
            final Set<Integer> gameBaseIds = adpCpcGameService.fetchSubPkgAvailableGameBaseIds(Collections.singletonList(cpcUnitVo.getGameBaseId()));
            if (!CollectionUtils.isEmpty(gameBaseIds)) {
                if (Objects.equals(AdpCpcGameService.AD_SUB_PKG, cpcUnitVo.getSubPkg())) {
                    cpcUnitVo.getGame_detail().setGameName(cpcUnitVo.getGame_detail().getGameName() + "(广告包)");
                } else if (Objects.equals(AdpCpcGameService.REGULAR_PKG, cpcUnitVo.getSubPkg())) {
                    cpcUnitVo.getGame_detail().setGameName(cpcUnitVo.getGame_detail().getGameName() + "(联运包)");
                }
            }
        }
        //设置明投系数搜索词
        cpcUnitVo.setSearchPriceCoefficient(Utils.fromFenToYuan(new BigDecimal(unit.getSearchPriceCoefficient())));
        if (unit.getSearchFirstPriceCoefficient() != null) {
            cpcUnitVo.setSearchPriceCoefficient(Utils.fromFenToYuan(new BigDecimal(unit.getSearchFirstPriceCoefficient())));
        }
        //搜索明投词
        buildSearchOvertWords(unitId, cpcUnitVo::setSearchOvertWords, cpcUnitVo::setNegateSearchOvertWords);

        // 新三连兜底
        if (AdpVersion.isCpcFlyMerge(cpcUnitVo.getAdp_version())
                && SalesType.CPC.getCode() == cpcUnitVo.getSales_type()
                && Utils.isPositive(cpcUnitVo.getOcpc_target())) {
            cpcUnitVo.setSales_type(SalesType.CPM.getCode());
            cpcUnitVo.setSales_type_desc(SalesType.CPM.getDesc());
        }
        return Response.SUCCESS(cpcUnitVo);
    }

    private void dealAvIdToBvIdWhenSwitchOpen(CpcUnitVo cpcUnitVo) {
        if (cpcUnitVo == null) {
            return;
        }
        long videoId = StringUtils.isNumeric(cpcUnitVo.getVideo_id()) ? Long.valueOf(cpcUnitVo.getVideo_id()) : 0L;

        CpcVideoVo videoVo = cpcUnitVo.getVideo_info();
        long videoInfoId = (videoVo == null || !StringUtils.isNumeric(videoVo.getId())) ? 0L : Long.valueOf(videoVo.getId());
        if (videoId <= 0 && videoInfoId <= 0) {
            return;
        }

        if (bvidSwitchService.checkBvidSwitchOpenFromCache()) {
            if (videoId > 0) {
                cpcUnitVo.setVideo_id(BVIDUtils.avToBv(videoId));
            }

            if (videoInfoId > 0) {
                videoVo.setId(BVIDUtils.avToBv(videoInfoId));
            }
        }
    }

    @ApiOperation(value = "查询推广单元花费")
    @RequestMapping(value = "/{uid}/cost", method = RequestMethod.GET)
    public @ResponseBody
    Response<Object> queryCampaignCost(@ApiIgnore Context context,
                                       @PathVariable("uid") Integer unitId,
                                       @RequestParam("from_time") Long fromTime,
                                       @RequestParam("to_time") Long toTime) throws ServiceException {

        CpcUnitDto unit = cpcUnitService.loadCpcUnit(unitId);
        Assert.isTrue(context.getAccountId().equals(unit.getAccountId()), "不能操作不属于你的单元");

        BigDecimal cost = statUnitService.getSumCostByUnitId(unitId, new Timestamp(fromTime), new Timestamp(toTime));
        JSONObject costJson = new JSONObject(1);
        costJson.put("cost", LaunchUtil.roundUp(cost, 2));
        return Response.SUCCESS(costJson);
    }

    @ApiOperation(value = "新建单元时查询广告位组模板")
    @RequestMapping(value = "/slot_group", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<List<UnitSlotGroupInfo>> querySlotGroupByCampaignId(@ApiIgnore Context context,
                                                                 @RequestParam("campaign_id") Integer campaignId) {
        AccountBaseDto account = queryAccountService.getAccountBaseDtoByIdFromCache(context.getAccountId());
        CpcCampaignDto campaign = cpcCampaignService.loadCpcCampaignDto(campaignId);

        List<UnitSlotGroupInfo> unitSlotGroupInfoList = unitSlotGroupComponent.getUnitSlotGroupInfoList(account, campaign);

        return Response.SUCCESS(unitSlotGroupInfoList);
    }

    @ApiOperation(value = "编辑单元时查询广告位组模板")
    @RequestMapping(value = "/{unit_id}/slot_group", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<List<UnitSlotGroupInfo>> querySlotGroupByUnitId(@ApiIgnore Context context,
                                                             @PathVariable("unit_id") Integer unitId) throws ServiceException {

        CpcUnitDto unit = cpcUnitService.loadCpcUnit(unitId);
        Assert.isTrue(context.getAccountId().equals(unit.getAccountId()), "不能操作不属于你的单元");

        if (IsValid.TRUE.getCode().equals(unit.getIsHistory())) {
            ResSlotGroupBaseDto slotGroup = resSlotGroupService.getGroupById(unit.getSlotGroup());
            return Response.SUCCESS(Lists.newArrayList(UnitSlotGroupInfo.builder()
                    .slot_group_id(slotGroup.getId())
                    .slot_group_name(slotGroup.getSlotGroupName())
                    .build()));
        } else {
            AccountBaseDto account = queryAccountService.getAccountBaseDtoByIdFromCache(context.getAccountId());

            List<Integer> slotGroupIds = resSlotGroupService.getValidGroupBySysType(unit.getSalesType(), context.getAccountId());

            List<ResSlotGroupTemplateMappingDto> validSlotGroups = resSlotGroupService.querySlotGroupTemplateMappingInSlotGroupIds(
                    QueryTemplateLaunchTypeMappingDto
                            .builder()
                            .promotionPurposeType(unit.getPromotionPurposeType())
                            .slotGroupIds(slotGroupIds)
                            .build());

            if (!CollectionUtils.isEmpty(validSlotGroups)) {
                validSlotGroups.forEach(sg -> {
                    if (sg.getSlotGroup() != null) {
                        sg.getSlotGroup().setSalesTypes(Collections.singletonList(unit.getSalesType()));
                    }
                });
            }

            List<UnitSlotGroupInfo> unitSlotGroupInfoList = converter.buildUnitSlotGroupInfo(validSlotGroups, account, true);
            AccountBaseDto accountBaseDto = queryAccountService.getAccountBaseDtoByIdFromCache(context.getAccountId());
            //起飞账号
            if (Integer.valueOf(1).equals(accountBaseDto.getIsSupportContent()) || Integer.valueOf(1).equals(accountBaseDto.getIsSupportFly())) {
                //过滤掉指定id广告位组（默认过滤281,282）
                unitSlotGroupInfoList = unitSlotGroupInfoList.stream().filter(o -> !effectExcludeSlotGroupIds.contains(o.getSlot_group_id())).collect(Collectors.toList());
            }
            //直播间
            if (unit.getPromotionPurposeType() != null && unit.getPromotionPurposeType() == PromotionPurposeType.LIVE_ROOM.getCode()) {
                //是起飞账号，不显示任何广告位组
                if (Integer.valueOf(1).equals(accountBaseDto.getIsSupportFly()) ||
                        Integer.valueOf(1).equals(accountBaseDto.getIsSupportContent())) {
                    unitSlotGroupInfoList = Collections.emptyList();
                }
            }
            updateSlotGroupInfoList(unitSlotGroupInfoList, unit.getPromotionPurposeType());
            return Response.SUCCESS(unitSlotGroupInfoList);
        }
    }

    private void updateSlotGroupInfoList(List<UnitSlotGroupInfo> unitSlotGroupInfoList, int promotionPurposeType) {
        final Set<Integer> set = smartIncreaseMap.get(promotionPurposeType);
        if (set != null) {
            unitSlotGroupInfoList.forEach(x -> {
                x.set_info_feeds(set.contains(x.getSlot_group_id()));
            });
        } else {
            unitSlotGroupInfoList.forEach(x -> x.set_info_feeds(false));
        }
    }

    @ApiOperation(value = "查询指定单元广告位组的模板信息")
    @RequestMapping(value = "/slot_group/{unit_id}", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<CpcTemplateGroupVo> querySlotGroupForCreativeByUnitId(@ApiIgnore Context context,
                                                                   @PathVariable("unit_id") Integer unitId) throws ServiceException {
        CpcUnitDto unit = cpcUnitService.loadCpcUnit(unitId);
        Assert.isTrue(context.getAccountId().equals(unit.getAccountId()), "不能操作不属于你的计划");

        List<ResSlotGroupTemplateMappingDto> validSlotGroups = resSlotGroupService.querySlotGroupTemplateMappingInSlotGroupIds(
                QueryTemplateLaunchTypeMappingDto
                        .builder()
                        .promotionPurposeType(unit.getPromotionPurposeType())
                        .slotGroupId(unit.getSlotGroup())
                        .build());

        if (CollectionUtils.isEmpty(validSlotGroups) || CollectionUtils.isEmpty(validSlotGroups.get(0).getTemplates())) {
            return Response.SUCCESS(null);
        }

        TemplateDto templateDto = validSlotGroups.get(0).getTemplates().get(0);
        List<CreativeStyle> supportedStyles = templateDto.getCreativeStyles() == null ? Collections.emptyList() : templateDto.getCreativeStyles();

        //对内容&商业起飞广告位组，不让老平台用户看到gif的创意形态
        if (!CollectionUtils.isEmpty(flySlotGroupIds) && flySlotGroupIds.contains(validSlotGroups.get(0).getSlotGroupId())) {
            supportedStyles = supportedStyles.stream().filter(o -> !o.getCode().equals(CreativeStyle.GIF.getCode()))
                    .collect(Collectors.toList());
        }

        TemplateGroupBo templateGroupBo = TemplateUtils.template2TemplateGroup(templateDto);
        return Response.SUCCESS(converter.buildTemplateDetailVo(templateGroupBo, unit.getPromotionPurposeType(), context.getAccountId(), supportedStyles));
    }

    //todo 下个版本移除接口
    @ApiOperation(value = "新建单元时查询广告位组模板")
    @RequestMapping(value = "/slot_group/drop_box", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<List<DropBoxItemVo>> getSlotGroupDropBox(@ApiIgnore Context context,
                                                      @RequestParam(value = "campaign_id", required = false) Integer campaignId) throws ServiceException {
       /* List<Integer> slotGroupIds = resSlotGroupService.getValidGroupInSysTypes(SalesType.PLATFORM_SALES_TYPES, context.getAccountId());

        QueryTemplateLaunchTypeMappingDto query = QueryTemplateLaunchTypeMappingDto.builder()
                .slotGroupIds(slotGroupIds)
                .build();

        if (campaignId != null && campaignId.compareTo(0) > 0) {
            CpcCampaignDto campaign = cpcCampaignService.loadCpcCampaignDto(campaignId);
            Assert.isTrue(context.getAccountId().equals(campaign.getAccountId()), "不能操作不属于你的计划");

            query.setPromotionPurposeType(campaign.getPromotionPurposeType());
        }

        List<ResSlotGroupTemplateMappingDto> validSlotGroups = resSlotGroupService.querySlotGroupTemplateMappingInSlotGroupIds(query);

        List<DropBoxItemVo> dropBoxs = validSlotGroups.stream()
                .map(sg -> DropBoxItemVo.builder()
                        .id(sg.getSlotGroupId())
                        .name(sg.getSlotGroup() == null ? "--" : sg.getSlotGroup().getSlotGroupName())
                        .build())

                .distinct()
                .collect(Collectors.toList());*/

        return Response.SUCCESS(Collections.emptyList());
    }

    @ApiOperation(value = "竞价广告查询定向条件")
    @RequestMapping(value = "/target", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<Object> queryTargetListForPrice(
            @ApiIgnore Context context,
            @RequestParam(value = "campaign_id", required = false) Integer campaignId) throws InterruptedException {
        Long timeCost1 = System.currentTimeMillis();

        // 获取所有的可用的定向
        Map<TargetType, List<TargetTreeDto>> target2ItemMap = resTargetItemService.getAllValidTarget2ItemTreeMap();
        Long timeCost2 = System.currentTimeMillis();
        LOGGER.info("target time [getAllValidTarget2ItemTreeMap] " + campaignId + " " + (timeCost2 - timeCost1));

        Map<String, Object> ans = converter.buildTargetMap(context.getAccountId(), target2ItemMap, campaignId, null, getOperator(context));
        Long timeCost3 = System.currentTimeMillis();
        LOGGER.info("target time [buildTargetMap] " + campaignId + " " + (timeCost3 - timeCost2));
        return Response.SUCCESS(ans);
    }

    @ApiOperation(value = "最低出价")
    @RequestMapping(value = "/lowest_bid", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<BigDecimal> getLowestBidCost(@ApiIgnore Context context,
                                          @RequestParam(value = "slot_group_id", required = false) Integer slotGroupId,
                                          @RequestParam(value = "promotion_purpose_type", required = false) Integer promotionPurposeType,
                                          @RequestParam(value = "sales_type") Integer salesType,
                                          @RequestParam(value = "adp_version", required = false, defaultValue = "0") Integer adpVersion) throws ServiceException {
        Integer lowestBidFen;
        if (AdpVersion.isLegacy(adpVersion)) {
            Assert.notNull(slotGroupId, "旧版广告组id不能为空");
            Assert.notNull(promotionPurposeType, "旧版推广类型不能为空");
            if (!CollectionUtils.isEmpty(bidNoLimitAccountIds) && bidNoLimitAccountIds.contains(context.getAccountId())) {
                return Response.SUCCESS(Utils.fromFenToYuan(noLimitLowBid));
            }
            lowestBidFen = cpcUnitService.getReservedPrice(GetBidCostParam.builder()
                    .accountId(context.getAccountId())
                    .slotGroupId(slotGroupId)
                    .launchType(promotionPurposeType)
                    .salesType(salesType)
                    .build(), adpVersion);
        } else {
            lowestBidFen = lowestBidService.getLowestBidFen(context.getAccountId(), salesType);
        }
        if (Objects.equals(promotionPurposeType, PromotionPurposeType.GAME_CARD.getCode())
                || Objects.equals(promotionPurposeType, PromotionPurposeType.GAME_ACTIVITY_CARD.getCode())) {
            lowestBidFen = BigDecimal.valueOf(lowestCostPrice).multiply(BigDecimal.valueOf(100)).intValue();
        }
        return Response.SUCCESS(Utils.fromFenToYuan(lowestBidFen));
    }

    @ApiOperation(value = "列表最低出价展示")
    @RequestMapping(value = "/list_lowest_bid_show", method = RequestMethod.GET)
    public Response<BigDecimal> ListLowestBidShow(@ApiIgnore Context context,
                                                  @RequestParam(value = "unit_id", required = false) Integer unitId) throws ServiceException {
        CpcUnitDto cpcUnitDto = cpcUnitService.loadCpcUnit(unitId);
        if (ObjectUtils.equals(cpcUnitDto.getPromotionPurposeType(), PromotionPurposeType.ARCHIVE_CONTENT.getCode())) {
            if (ObjectUtils.equals(cpcUnitDto.getSalesType(), SalesType.CPM.getCode())) {
                if (ObjectUtils.equals(cpcUnitDto.getOcpcTarget(), 0)) {
                    return Response.SUCCESS(new BigDecimal(8.00));
                }
            }
        }
        return this.getLowestBidCost(context, cpcUnitDto.getSlotGroup(), cpcUnitDto.getPromotionPurposeType(),
                cpcUnitDto.getSalesType(), cpcUnitDto.getAdpVersion());
    }

    @ApiOperation(value = "最高出价")
    @RequestMapping(value = "/highest_bid", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<Map<Integer, Integer>> getHighestBidCost(@ApiIgnore Context context) throws ServiceException {
        // 写死，放在配置中心的
        return Response.SUCCESS(highBid);
    }

    @ApiOperation(value = "人群预估")
    @RequestMapping(value = "/forecast", method = RequestMethod.POST)
    public
    @ResponseBody
    Response<CpcUnitCrowdForecastResponseVo> crowdForecast(@ApiIgnore Context context,
                                                           @RequestBody CpcUnitCrowdForecastRequestVo vo) throws ServiceException {
        log.info("crowdForecast ... vo= {} ", vo);
        packStartUpCrowds(vo.getTarget());
        MiddleCpcUnitForecastDto result = cpcUnitService.forecast(UnitForecastDto.builder()
                // 定向
                .target(converter.unitTargetVoToDto(vo.getTarget(), vo.getApp_id()))
                .gameCardUnitTargets(GameCardUnitTargetConverter.MAPPER.newVo2Dto(vo.getGameCardUnitTargets()))
                .unitId(vo.getUnit_id())
                .launch_time(vo.getLaunch_time())
                .frequencyLimit(vo.getFrequency_limit())
                .campaignId(vo.getCampaign_id())
                .salesType(vo.getSales_type())
                .operator(getOperator(context))
                // 是否需要建议出价
                .suggestCostPrice(true)
                .build());

        // 预估结果 vo
        CpcUnitCrowdForecastResponseVo responseVo = CpcUnitCrowdForecastResponseVo.builder()
                .max_crowd_count(result.getMax_crowd_count())
                .max_impression_count(result.getMax_impression_count())
                .suggest_cost_min(result.getSuggest_cost_min())
                .suggest_cost_max(result.getSuggest_cost_max())
                .estimate_status(result.getType())
                .build();
        return Response.SUCCESS(responseVo);
    }

    @ApiOperation(value = "创意复制检查")
    @RequestMapping(value = "/check_copy", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<CpcUnitCreativeCheckCopyResponseVo> checkCreativeCopy(@ApiIgnore Context context,
                                                                   @RequestParam Integer unit_id,
                                                                   @RequestParam(required = false) Integer new_unit_id,
                                                                   @RequestParam(required = false) Integer slot_group_id) throws ServiceException {

        CpcUnitCreativeCheckCopyResponseVo response = new CpcUnitCreativeCheckCopyResponseVo();

        List<Integer> unitIds = Stream.of(unit_id, new_unit_id)
                .filter(Utils::isPositive).collect(Collectors.toList());

        Map<Integer, CpcLightUnitDto> lightUnitMap = cpcUnitService.queryLightUnitMap(QueryCpcUnitDto.builder()
                .unitIds(unitIds)
                .build());

        CpcLightUnitDto unit = lightUnitMap.get(unit_id);
        CpcLightUnitDto newUnit = Utils.isPositive(slot_group_id) ? CpcLightUnitDto.builder()
                .slotGroup(slot_group_id).build() : lightUnitMap.get(new_unit_id);

        if (unit == null || newUnit == null) {
            response.setCan_copy(false);
            response.setMassage("复制创意时单元无效");
            return Response.SUCCESS(response);
        }

        if (AdpVersion.isMergedInGeneral(unit.getAdpVersion())) {
            // 新版必须要以下参数都相同才能复制创意
            if (Objects.equals(unit.getPromotionPurposeType(), newUnit.getPromotionPurposeType()) &&
                    Objects.equals(unit.getSalesType(), newUnit.getSalesType()) &&
                    Objects.equals(unit.getOcpcTarget(), newUnit.getOcpcTarget())) {

                List<Integer> creativeIds = cpcCreativeService.queryCpcCreativeIds(QueryCpcCreativeDto
                        .builder()
                        .unitId(unit_id)
                        .statusList(CreativeStatus.NON_DELETE_CREATIVE_STATUS_LIST)
                        .build());

                response.setCan_copy(true);
                response.setCreative_ids(creativeIds);
                return Response.SUCCESS(response);
            } else {
                response.setCan_copy(false);
                response.setMassage("投放内容和出价方式(含优化目标)都相同时才能复制创意");
                return Response.SUCCESS(response);
            }
        } else {
            Integer templateId = getSameTemplateId(unit, newUnit);

            if (templateId == null) {
                response.setCan_copy(false);
                response.setMassage("相同的投放内容和广告位组才能复制创意");
                return Response.SUCCESS(response);
            }

            List<Integer> creativeIds = cpcCreativeService.queryCpcCreativeIds(QueryCpcCreativeDto
                    .builder()
                    .unitId(unit_id)
                    .templateId(templateId)
                    .statusList(CreativeStatus.NON_DELETE_CREATIVE_STATUS_LIST)
                    .build());

            response.setCan_copy(true);
            response.setCreative_ids(creativeIds);
            return Response.SUCCESS(response);
        }
    }

    @ApiOperation(value = "获取单元下拉列表")
    @RequestMapping(value = "/drop_box", method = RequestMethod.GET)
    public
    @ResponseBody
    Response<List<SimpleUnitVo>> getUnitDropBox(
            @ApiIgnore Context context,
            @RequestParam(required = true, value = "campaign_id") Integer campaignId,
            @RequestParam(required = false, value = "template_id") Integer templateId,
            @RequestParam(required = false, value = "no_programmatic", defaultValue = "false") boolean noProgrammatic) throws ServiceException {

//        QueryCpcUnitDto query = QueryCpcUnitDto
//                .builder()
//                .accountId(context.getAccountId())
//                .campaignId(campaignId)
//                .unitStatusList(UnitStatus.NON_DELETED_UNIT_STATUS_LIST)
//                .orderBy(Constants.MTIME_DESC)
//                .isNewFly(0)
//                .isMiddleAd(0)
//                .build();
//
//        List<CpcUnitDto> dtos = cpcUnitService.queryCpcUnit(query);
        QueryUnitBo query = QueryUnitBo
                .builder()
                .accountIds(Collections.singletonList(context.getAccountId()))
                .campaignIds(Collections.singletonList(campaignId))
                .unitStatusList(UnitStatus.NON_DELETED_UNIT_STATUS_LIST)
                .orderBy(Constants.MTIME_DESC)
                .isNewFly(0)
                .isMiddleAd(0)
                .build();
        List<CpcUnitDto> dtos = launchUnitV1Service.listUnits(query);
        if (noProgrammatic) {
            dtos = dtos.stream()
                    .filter(x -> !adpCpcUnitService.isProgrammatic(x.getUnitId()))
                    .collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(dtos)) {
            Response.SUCCESS(Collections.emptyList());
        }

        if (!Utils.isPositive(templateId)) {
            final List<SimpleUnitVo> dropboxItems = dtos.stream()
                    .map(x -> {
                        return SimpleUnitVo.builder()
                                .id(x.getUnitId())
                                .name(x.getUnitName())
                                .adpVersion(x.getAdpVersion())
                                .build();
                    }).collect(Collectors.toList());
            return Response.SUCCESS(dropboxItems);
        }

        Map<Integer, List<SimpleUnitVo>> unitPerSlotGroupId = dtos.stream()
                .collect(Collectors.groupingBy(CpcUnitDto::getSlotGroup,
                        Collectors.mapping(dto -> SimpleUnitVo.builder()
                                .id(dto.getUnitId())
                                .name(dto.getUnitName())
                                .adpVersion(dto.getAdpVersion())
                                .build(), Collectors.toList())));

        List<Integer> slotGroupIds = dtos
                .stream()
                .map(CpcUnitDto::getSlotGroup)
                .distinct()
                .collect(Collectors.toList());

        QueryTemplateLaunchTypeMappingDto qtlm = QueryTemplateLaunchTypeMappingDto
                .builder()
                .slotGroupIds(slotGroupIds)
                .build();

        if (campaignId != null && campaignId.compareTo(0) > 0) {
            CpcCampaignDto campaign = cpcCampaignService.loadCpcCampaignDto(campaignId);
            Assert.isTrue(context.getAccountId().equals(campaign.getAccountId()), "不能操作不属于你的计划");

            qtlm.setPromotionPurposeType(campaign.getPromotionPurposeType());
        }

        List<ResSlotGroupTemplateMappingDto> validSlotGroups = resSlotGroupService.querySlotGroupTemplateMappingInSlotGroupIds(qtlm);

        if (CollectionUtils.isEmpty(validSlotGroups)) {
            return Response.SUCCESS(Collections.emptyList());
        }

        List<Integer> validSlotGroupIds = validSlotGroups
                .stream()
                .filter(sg -> !CollectionUtils.isEmpty(sg.getTemplates()) && sg.getTemplates().stream().filter(t -> templateId.equals(t.getTemplateId())).count() > 0)
                .map(ResSlotGroupTemplateMappingDto::getSlotGroupId)
                .collect(Collectors.toList());

        return Response.SUCCESS(
                unitPerSlotGroupId
                        .entrySet()
                        .stream()
                        .filter(e -> validSlotGroupIds.contains(e.getKey()))
                        .flatMap(e -> e.getValue().stream())
                        .collect(Collectors.toList()));
    }

    private Integer getSameTemplateId(CpcLightUnitDto unit, CpcLightUnitDto newUnit) {
        if (unit == null || newUnit == null) {
            return null;
        }

        List<ResSlotGroupTemplateMappingDto> validSlotGroups = resSlotGroupService.querySlotGroupTemplateMappingInSlotGroupIds(
                QueryTemplateLaunchTypeMappingDto
                        .builder()
                        .promotionPurposeType(unit.getPromotionPurposeType())
                        .slotGroupIds(Arrays.asList(newUnit.getSlotGroup(), unit.getSlotGroup()))
                        .build());

        if (CollectionUtils.isEmpty(validSlotGroups)) {
            return null;
        } else if (newUnit.getSlotGroup().equals(unit.getSlotGroup())) {
            List<TemplateDto> t = validSlotGroups.get(0).getTemplates();

            return CollectionUtils.isEmpty(t) ? null : t.get(0).getTemplateId();
        } else {
            List<TemplateDto> t1 = validSlotGroups.get(0).getTemplates();
            List<TemplateDto> t2 = validSlotGroups.get(1).getTemplates();

            if (CollectionUtils.isEmpty(t1) || CollectionUtils.isEmpty(t2)) {
                return null;
            }

            List<Integer> template1Ids = t1.stream().map(TemplateDto::getTemplateId).collect(Collectors.toList());
            TemplateDto template = t2.stream().filter(t -> template1Ids.contains(t.getTemplateId())).findFirst().orElse(null);

            if (template != null) {
                return template.getTemplateId();
            } else {
                return null;
            }
        }

    }

    @ApiOperation("获取直播间信息")
    @RequestMapping(value = "/live_room/{live_id}", method = RequestMethod.GET)
    public Response<LiveMateriaInfolVo> getLiveBroadcastRoomInfoById(
            @ApiParam("直播间id") @PathVariable(value = "live_id") String liveId) {

        Assert.isTrue(StringUtils.isNotBlank(liveId) && StringUtils.isNumeric(liveId), "直播间id不能为空");

        Integer liveId2 = Integer.valueOf(liveId);
        Map<Integer, LiveBroadcastRoomInfo> roomIdMapRoomInfo = liveBroadcastHttpService.queryLiveBroadcastRoomInfoByIds(Arrays.asList(liveId2));

        LiveBroadcastRoomInfo roomInfo = null;
        Assert.isTrue(!CollectionUtils.isEmpty(roomIdMapRoomInfo)
                && (roomInfo = roomIdMapRoomInfo.get(liveId2)) != null, "直播间不存在");

        // 获取直播间的 up 信息
        String nickname = "";
        String face = "";
        if (roomInfo.getUid() != null) {
            try {
                UserInfoDto userInfoDto = passportService.getUserByMid(roomInfo.getUid());
                nickname = userInfoDto == null ? "" : userInfoDto.getName();
                face = userInfoDto == null ? "" : userInfoDto.getFace();
            } catch (Exception e) {
                LOGGER.error("getLiveBroadcastRoomInfoById request user's nickname occur error! {}", e);
            }
        }
        return Response.SUCCESS(LiveMateriaInfolVo.builder()
                .description(nickname)
                .live_id(roomInfo.getRoomId())
                .uid(roomInfo.getUid())
                .name(nickname)
                .face(face)
                .title(roomInfo.getTitle())
                .cover(roomInfo.getCover())
                .status(roomInfo.getIsShow())
                .status_desc((roomInfo.getIsShow() == null || roomInfo.getIsShow() == 0) ? "未开播" : "直播中")
                .link_url(roomInfo.getLink())
                .area_v2_id(roomInfo.getAreaV2Id())
                .area_v2_name(roomInfo.getAreaV2Name())
                .area_v2_parent_id(roomInfo.getAreaV2ParentId())
                .area_v2_parent_name(roomInfo.getAreaV2ParentName())
                .build());
    }

    @ApiOperation("应用商店直投优化目标")
    @GetMapping(value = "/support_store_direct_launch")
    public Response<List<UnitSupportStoreDirectLaunchVo>> supportStoreDirectLaunch(@ApiIgnore Context context) {
        final Integer aid = context.getAccountId();
        Assert.isTrue(Utils.isPositive(aid), "账号ID获取失败");

        final Map<Integer, LauOcpxConfigBo> configMap = ocpxService.getOcpxTargetMap(null, supportStoreTotalDirectLaunchOcpcTargets);
        final List<OcpxTargetBo> ocpxTargets = configMap.values()
                .stream()
                .map(x -> {
                    final OcpxTargetBo ocpxTargetBo = OcpxTargetBo.fromConfig(configMap, x.getTargetId());
                    if (Objects.isNull(ocpxTargetBo)) return null;

                    final List<OcpxTargetBo> descendants = ocpxTargetBo.getDescendants()
                            .stream()
                            .filter(y -> supportStoreTotalDirectLaunchOcpcDeepTargets.contains(y.getId()))
                            .collect(Collectors.toList());
                    ocpxTargetBo.setDescendants(descendants);
                    return ocpxTargetBo;
                }).filter(Objects::nonNull)
                .collect(Collectors.toList());
        return Response.SUCCESS(ocpxTargets.stream()
                .map(UnitSupportStoreDirectLaunchVo::fromBo)
                .collect(Collectors.toList()));
    }


    @GetMapping(value = "/verify_game_unit_game_info")
    public Response<List<GameDetailVo>> verifyGameUnitGameInfo(
            @RequestParam("game_base_id") Integer gameBaseId) {
        GameDto androidGameDto = gameCenterService.getGameDtoByIdPlatform(gameBaseId,
                GamePlatformTypeEnum.ANDROID.getCode());
        GameDto iosGameDto = gameCenterService.getGameDtoByIdPlatform(gameBaseId,
                GamePlatformTypeEnum.IOS.getCode());
        List<GameDetailVo> gameDetails = new ArrayList<>();
        Optional.ofNullable(androidGameDto).ifPresent(androidGame -> gameDetails.add(GameConverter.MAPPER.dto2Vo(GamePlatformTypeEnum.ANDROID.getCode(), androidGame)));
        Optional.ofNullable(iosGameDto).ifPresent(iosGame -> gameDetails.add(GameConverter.MAPPER.dto2Vo(GamePlatformTypeEnum.IOS.getCode(), iosGame)));
        return Response.SUCCESS(gameDetails);
    }

    @PostMapping(value = "/verify_game_unit_tags_targets")
    public Response<List<TagReplyVo>> verifyGameUnitTagsTargets(@RequestBody TagsReqVo tagsReqVo) {
        Assert.isTrue(!CollectionUtils.isEmpty(tagsReqVo.getTagNames())
                || !CollectionUtils.isEmpty(tagsReqVo.getTagIds()), "标签名列表 标签id列表不能全为空");
        Map<Long, Tag> tagsMap = tagService.verifyGameUnitTagsTargets(tagsReqVo.getPlatformType(),
                tagsReqVo.getTagNames(), tagsReqVo.getTagIds());
        return Response.SUCCESS(
                tagsMap.values().stream()
                        .map(tag -> TagReplyVo.builder().id(tag.getId()).name(tag.getName()).build())
                        .collect(Collectors.toList())
        );
    }

    @PostMapping(value = "/verify_game_unit_archives_targets")
    public Response<List<ArchiveReplyVo>> verifyGameUnitArchivesTargets(
            @RequestBody ArchivesReqVo archivesReqVo) {
        Map<Long, Arc> arcsMap = archiveService.verifyGameUnitArchivesTargets(
                archivesReqVo.getPlatformType(), archivesReqVo.getBvids());
        return Response.SUCCESS(
                arcsMap.values().stream()
                        .map(arc -> ArchiveReplyVo.builder().avid(arc.getAid())
                                .bvid(BVIDUtils.avToBv(arc.getAid())).title(arc.getTitle()).build())
                        .collect(Collectors.toList()));
    }

    public void checkUnitBudgetIfChange(BigDecimal oldBudget,
                                        Integer newDailyBudgetType, BigDecimal newBudget,
                                        Integer unitId) {
        // 改成不限不校验
        if (Objects.equals(newDailyBudgetType, DailyBudgetType.AUTO.getCode()) || Objects.isNull(newBudget)) {
            return;
        }
        // 预算不变或调大
        if (newBudget.compareTo(oldBudget) >= 0) {
            return;
        }
        Map<Integer, BigDecimal> unitTodayCostMap = soaStatUnitService.getTodayActualCost(Arrays.asList(unitId));
        BigDecimal unitMinBudget = DoBudgetCheckUtils.getUnitMinBudget(unitId, unitTodayCostMap);
        if (newBudget.compareTo(unitMinBudget) < 0) {
            throw new ServiceRuntimeException("单元id:" + unitId + ",预算不能低于" + unitMinBudget.toString());
        }
    }

    private Integer resetProductGroupIdByAccountLabel(Integer accountId, Integer promotionPurposeType) {
        if (!promotionPurposeType.equals(PromotionPurposeType.GOODS_CATALOG.getCode())
                || CollectionUtils.isEmpty(dpaAccountLabelProductGroupMap)) {
            return null;
        }

        for (Map.Entry<Integer, Integer> entry : dpaAccountLabelProductGroupMap.entrySet()) {
            if (accountLabelService.isAccountIdInLabel(accountId, entry.getKey())) {
                return entry.getValue();
            }
        }

        return null;
    }

    //todo 临时处理，自定义年龄回显时转为年龄四分段，上线后统一刷数
    private void ageCusTrans(CpcUnitDto unit) {
        try {
            List<TargetRule> targetRules = unit.getTargetRules();
            TargetRule ageCustargetRule = targetRules.stream().filter(rule -> TargetType.AGE_CUSTOMIZE.getCode() == rule.getRuleType()).findFirst().orElse(null);
            TargetRule agetargetRule = targetRules.stream().filter(rule -> TargetType.AGE.getCode() == rule.getRuleType()).findFirst().orElse(null);
            List<Integer> ageCus = ageCustargetRule.getValueIds();
            List<Integer> age = agetargetRule.getValueIds();
            if (CollectionUtils.isEmpty(age) || !age.toString().contains("-1") || CollectionUtils.isEmpty(ageCus) || ageCus.toString().contains("-1")) {
                return;
            }
            targetRules.remove(ageCustargetRule);
            targetRules.remove(agetargetRule);


            Integer left = ageCus.get(0);
            Integer right = ageCus.get(1);
            List<Integer> ageList = new ArrayList<>();
            //(18-24)= 396 (25-30)= 397 (30+) = 420
            if (right >= 31) {
                ageList.add(420);
                if (left <= 30) {
                    ageList.add(397);
                }
                if (left <= 24) {
                    ageList.add(396);
                }
            }

            if (25 <= right && right <= 30) {
                ageList.add(397);
                if (18 <= left && left <= 24) {
                    ageList.add(396);
                }
            }

            if (right <= 24) {
                ageList.add(396);
            }
            agetargetRule.setValueIds(ageList);
            ageCustargetRule.setValueIds(Collections.emptyList());
            targetRules.add(ageCustargetRule);
            targetRules.add(agetargetRule);
        } catch (Exception e) {
            log.warn("unit ageCusTrans err unit={}", unit, e);
        }

    }

    private Integer buildUpdateAllAdSearchUnit(UpdateCpcUnitVo vo, CpcCampaignDto cpcCampaignDto) {
        Integer isAllAdSearch = YesOrNoEnum.NO.getCode();
        if (CampaignAdType.ALL.getCode().equals(cpcCampaignDto.getAdType())) {
            isAllAdSearch = !CollectionUtils.isEmpty(vo.getSearch_ad_keywords()) ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode();
        }

        //不支持搜索明投设置默认值
        if (YesOrNoEnum.NO.getCode().equals(isAllAdSearch)) {
            vo.setTarget_expand(IsValid.FALSE.getCode());
            vo.setSearch_price_coefficient(BigDecimal.ZERO);
        }
        vo.setIs_all_ad_search_unit(isAllAdSearch);
        return isAllAdSearch;
    }
}
