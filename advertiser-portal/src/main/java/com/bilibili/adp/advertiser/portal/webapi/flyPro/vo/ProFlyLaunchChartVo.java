package com.bilibili.adp.advertiser.portal.webapi.flyPro.vo;

import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProFlyLaunchChartVo implements Serializable {
    private static final long serialVersionUID = -4974141111605059843L;

    @ApiModelProperty(notes = "x轴字段")
    private List<String> xAxis;

    @ApiModelProperty(notes = "展示数量")
    private List<Integer> show_count;

    @ApiModelProperty(notes = "点击次数")
    private List<Integer> click_count;

    @ApiModelProperty(notes = "点击率")
    private List<BigDecimal> click_rate;

    @ApiModelProperty(notes = "消费(元)")
    private List<BigDecimal> cost;

    @ApiModelProperty(notes = "粉丝增长数")
    private List<Integer> fan_follow_count;

    @ApiModelProperty(notes = "粉丝增长率")
    private List<BigDecimal> fan_follow_rate;

    @ApiModelProperty(notes = "总计")
    private FlyProStatVo total;

    public static ProFlyLaunchChartVo getEmptyChartVo() {
        return ProFlyLaunchChartVo
                .builder()
                .xAxis(Lists.newArrayList())
                .show_count(Lists.newArrayList())
                .click_count(Lists.newArrayList())
                .click_rate(Lists.newArrayList())
                .fan_follow_count(Lists.newArrayList())
                .fan_follow_rate(Lists.newArrayList())
                .cost(Lists.newArrayList())
                .build();
    }
}
