package com.bilibili.adp.advertiser.portal.service.openapi;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.account.dto.AccountBaseDto;
import com.bilibili.adp.account.dto.AccountDto;
import com.bilibili.adp.account.service.IQueryAccountService;
import com.bilibili.adp.advertiser.portal.common.Context;
import com.bilibili.adp.advertiser.portal.common.enums.RequestTypeEnum;
import com.bilibili.adp.advertiser.portal.service.launch.LimitBsiType;
import com.bilibili.adp.advertiser.portal.service.launch.bo.CreativeNameCheckBo;
import com.bilibili.adp.advertiser.portal.service.unit.UnitOcpxTargetComponent;
import com.bilibili.adp.advertiser.portal.webapi.dpa.vo.DpaCreativeVo;
import com.bilibili.adp.advertiser.portal.webapi.dpa.vo.DpaUnitVo;
import com.bilibili.adp.advertiser.portal.webapi.launch.cpc.vo.*;
import com.bilibili.adp.advertiser.portal.webapi.launch.creative.vo.ImageInfoVo;
import com.bilibili.adp.advertiser.portal.webapi.v2.cpc.vos.SimpleTemplateGroupVo;
import com.bilibili.adp.advertiser.portal.webapi.v2.cpc.vos.StyleVo;
import com.bilibili.adp.advertiser.portal.webapi.v2.ocpx.vos.OcpxTargetVo;
import com.bilibili.adp.advertiser.portal.webapi.v2.vo.common.BiliVideoMediaVo;
import com.bilibili.adp.advertiser.portal.webapi.v2.vo.common.MediaGroupVo;
import com.bilibili.adp.advertiser.portal.webapi.v2.vo.common.TitleVo;
import com.bilibili.adp.advertiser.portal.webapi.v2.vo.requests.SaveProgrammaticCreativeVo;
import com.bilibili.adp.advertiser.portal.webapi.v2.vo.responses.ProgrammaticCreativeVo;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.enums.*;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.Page;
import com.bilibili.adp.common.util.TimeUtils;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.biz.bos.archive.ArchiveBo;
import com.bilibili.adp.cpc.biz.constants.Constants;
import com.bilibili.adp.cpc.biz.services.account.GeneralVideoAuthService;
import com.bilibili.adp.cpc.biz.services.archive.ArchiveService;
import com.bilibili.adp.cpc.biz.services.campaign.api.ICpcCampaignService;
import com.bilibili.adp.cpc.biz.services.campaign.dto.CpcCampaignDto;
import com.bilibili.adp.cpc.biz.services.creative.CpcCreativeServiceDelegate;
import com.bilibili.adp.cpc.biz.services.creative.CpcSaveCreativeService;
import com.bilibili.adp.cpc.biz.services.creative.MiddleCreativeQualificationPackageService;
import com.bilibili.adp.cpc.biz.services.creative.MiddleCreativeQualificationService;
import com.bilibili.adp.cpc.biz.services.creative.api.ICpcCreativeService;
import com.bilibili.adp.cpc.biz.services.creative.bos.*;
import com.bilibili.adp.cpc.biz.services.creative.dto.CpcCreativeDto;
import com.bilibili.adp.cpc.biz.services.live.AdpCpcLiveReserveService;
import com.bilibili.adp.cpc.biz.services.live.bos.LiveReservationInfoBo;
import com.bilibili.adp.cpc.biz.services.unit.CpcUnitService;
import com.bilibili.adp.cpc.biz.services.unit.LauSubjectService;
import com.bilibili.adp.cpc.biz.services.unit.dto.CpcUnitDto;
import com.bilibili.adp.cpc.core.constants.BusinessDomain;
import com.bilibili.adp.cpc.core.constants.CreativeStatus;
import com.bilibili.adp.cpc.enums.AdpVersion;
import com.bilibili.adp.cpc.enums.JumpType;
import com.bilibili.adp.cpc.enums.LaunchStatus;
import com.bilibili.adp.cpc.enums.ad.PromotionPurposeType;
import com.bilibili.adp.cpc.enums.ad.*;
import com.bilibili.adp.launch.api.common.CampaignStatus;
import com.bilibili.adp.launch.api.common.CreativeRefreshType;
import com.bilibili.adp.launch.api.common.LauSubjectType;
import com.bilibili.adp.launch.api.common.ShareState;
import com.bilibili.adp.launch.api.common.enums.FlagEnums;
import com.bilibili.adp.launch.api.creative.dto.QueryCpcCreativeDto;
import com.bilibili.adp.launch.api.minigame.dto.LauMiniGameDto;
import com.bilibili.adp.launch.api.minigame.dto.QueryLauMiniGameDto;
import com.bilibili.adp.launch.api.service.ILauMiniGameService;
import com.bilibili.adp.launch.biz.ad_core.mybatis.LauCampaignDao;
import com.bilibili.adp.launch.biz.ad_core.mybatis.LauUnitCreativeDao;
import com.bilibili.adp.launch.biz.ad_core.mybatis.LauUnitDao;
import com.bilibili.adp.launch.biz.pojo.*;
import com.bilibili.adp.launch.biz.service.account.LaunchBrandInfoService;
import com.bilibili.adp.launch.biz.service.account.bos.LauAccountInfoBo;
import com.bilibili.adp.passport.biz.manager.LiveBroadcastHttpService;
import com.bilibili.adp.passport.biz.manager.bean.LiveBroadcastRoomInfo;
import com.bilibili.bjcom.querydsl.BaseQueryFactory;
import com.bilibili.collage.api.dto.CollageMediaDto;
import com.bilibili.collage.api.dto.QueryCollageMediaDto;
import com.bilibili.collage.api.soa.ISoaCollageMediaService;
import com.bilibili.commercialorder.soa.adauth.pojo.SoaAdAuthInfoDto;
import com.bilibili.commercialorder.soa.adauth.pojo.SoaGetPageAdAuthReqDto;
import com.bilibili.commercialorder.soa.adauth.service.ISoaAdAuth4AdpService;
import com.bilibili.crm.platform.soa.ISoaAccountLabelService;
import com.bilibili.location.common.CmMarkEnum;
import com.bilibili.sycpb.acc.api.dict.common.YesNoEnum;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.bilibili.adp.cpc.dao.ad_core.querydsl.QLauUnitCreative.lauUnitCreative;
import static com.bilibili.adp.cpc.dao.config.PlatformMySqlConfig.AD_CORE_BQF;
import static com.bilibili.adp.web.framework.exception.ExceptionUtils.errorMsgBuild;
import static com.bilibili.adp.web.framework.exception.enums.ErrorCodeEnum.*;

/**
 * <AUTHOR>
 * @Description
 * @date 2020/12/30
 **/
@Component
@Slf4j
public class OpenAPIValidator {

    @Autowired
    private IQueryAccountService queryAccountService;

    @Resource
    private LauCampaignDao lauCampaignDao;

    @Resource
    private LauUnitDao lauUnitDao;

    @Resource
    private LauUnitCreativeDao lauUnitCreativeDao;

    @Autowired
    private ICpcCreativeService cpcCreativeService;

    @Autowired
    private CpcSaveCreativeService cpcSaveCreativeService;

    @Resource
    private ISoaCollageMediaService iSoaCollageMediaService;

    @Resource
    private LaunchBrandInfoService launchBrandInfoService;

    @Autowired
    private ISoaAccountLabelService soaAccountLabelService;

    @Autowired
    private CpcCreativeServiceDelegate creativeServiceDelegate;
    @Autowired
    private CpcUnitService cpcUnitService;
    @Autowired
    private ICpcCampaignService cpcCampaignService;
    @Resource(name = AD_CORE_BQF)
    private BaseQueryFactory adCoreBqf;

    @Autowired
    private ArchiveService archiveService;

    @Autowired
    private MiddleCreativeQualificationPackageService middleCreativeQualificationPackageService;

    @Autowired
    private MiddleCreativeQualificationService middleCreativeQualificationService;

    @Autowired
    private AdpCpcLiveReserveService liveReserveService;

    @Autowired
    private LauSubjectService subjectService;

    @Autowired
    private LiveBroadcastHttpService liveBroadcastHttpService;

    @Autowired
    private ISoaAdAuth4AdpService soaAdAuth4AdpService;

    @Autowired
    private GeneralVideoAuthService generalVideoAuthService;

    @Autowired
    private UnitOcpxTargetComponent unitOcpxTargetComponent;

    @Autowired
    private ILauMiniGameService lauMiniGameService;

    @Resource(name = "okHttpClient")
    private OkHttpClient client;


    @Value("${open.api.limit.config:{\"10010\":{\"campaign\":{\"day\":300,\"all\":5000},\"unit\":{\"day\":300,\"all\":5000},\"creative\":{\"day\":300,\"all\":5000}}}}")
    private String limitConfigString;

    @Value("${clue.orderPlace.label.id:604}")
    private Integer clueOrderPlaceLabel;

    private static final Gson gson = new GsonBuilder().create();

    private final List<PromotionPurposeType> programmedSupportPromotions = Arrays.asList(PromotionPurposeType.LANDING_PAGE,
            PromotionPurposeType.APP_DOWNLOAD, PromotionPurposeType.ON_SHELF_GAME);


    public long countCampaignOpenAPI(Integer accountId) {
        return cpcCampaignService.getCampaignNumByAccountIdWithCache(accountId, CampaignCountEnum.OPEN_API.getCode());
    }

    public long dayCountCampaignOpenAPI(Integer accountId) {
        return cpcCampaignService.getTodayCampaignNumByAccountIdWithCache(accountId, TimeUtils.getTodayStr(), CampaignCountEnum.OPEN_API.getCode());
    }

    public long campaignNameCreateCheck(NewCpcCampaignVo vo, Context context) {
        LauCampaignPoExample example = new LauCampaignPoExample();
        example.or()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andCampaignStatusNotIn(Collections.singletonList(CampaignStatus.DELETED.getCode()))
                .andAccountIdEqualTo(context.getAccountId())
                .andCampaignNameEqualTo(vo.getCampaign_name());
        return lauCampaignDao.countByExample(example);
    }

    public long campaignNameCreateCheck(String campaignName, Context context) {
        LauCampaignPoExample example = new LauCampaignPoExample();
        example.or()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andCampaignStatusNotIn(Collections.singletonList(CampaignStatus.DELETED.getCode()))
                .andAccountIdEqualTo(context.getAccountId())
                .andCampaignNameEqualTo(campaignName);
        return lauCampaignDao.countByExample(example);
    }

    public long campaignNameUpdateCheck(UpdateCpcCampaignVo vo, Context context) {
        LauCampaignPoExample example = new LauCampaignPoExample();
        example.or()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andCampaignStatusNotIn(Collections.singletonList(CampaignStatus.DELETED.getCode()))
                .andAccountIdEqualTo(context.getAccountId())
                .andCampaignIdNotEqualTo(vo.getCampaign_id())
                .andCampaignNameEqualTo(vo.getCampaign_name());
        return lauCampaignDao.countByExample(example);
    }

    public long campaignNameUpdateCheck(Integer campaignId, String campaignName, Context context) {
        LauCampaignPoExample example = new LauCampaignPoExample();
        example.or()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andCampaignStatusNotIn(Collections.singletonList(CampaignStatus.DELETED.getCode()))
                .andAccountIdEqualTo(context.getAccountId())
                .andCampaignIdNotEqualTo(campaignId)
                .andCampaignNameEqualTo(campaignName);
        return lauCampaignDao.countByExample(example);
    }

    public int countUnitOpenAPI(Integer accountId) {
        return cpcUnitService.getUnitNumByAccountIdWithCache(accountId, UnitCountEnum.OPEN_API.getCode());
    }

    public int dayCountUnitOpenAPI(Integer accountId) {
        return cpcUnitService.getTodayUnitNumByAccountIdWithCache(accountId, TimeUtils.getTodayStr(), UnitCountEnum.OPEN_API.getCode());
    }

    public int countCreativeOpenAPI(Integer accountId) {
        return cpcCreativeService.getCreativeNumByAccountIdWithCache(accountId, CreativeCountEnum.OPEN_API.getCode());
    }

    public int dayCountCreativeOpenAPI(Integer accountId) {
        return cpcCreativeService.getTodayCreativeNumByAccountIdWithCache(accountId, TimeUtils.getTodayStr(), CreativeCountEnum.OPEN_API.getCode());
    }

    public long countUnitCreativeOpenAPI(Integer accountId, Integer unitId) {
        LauUnitCreativePoExample example = new LauUnitCreativePoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andStatusNotIn(Collections.singletonList(LaunchStatus.DELETE.getCode())).andUnitIdEqualTo(unitId).andAccountIdEqualTo(accountId);
        return lauUnitCreativeDao.countByExample(example);
    }

    public LauCampaignPo getCampaignByUnitId(Integer accountId, Integer unitId) {

        LauUnitPo lauUnitPo = getUnitByUnitId(accountId, unitId);
        LauCampaignPoExample campaignPoExample = new LauCampaignPoExample();
        campaignPoExample.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andAccountIdEqualTo(accountId).andCampaignIdEqualTo(lauUnitPo.getCampaignId());
        List<LauCampaignPo> lauCampaignPos = lauCampaignDao.selectByExample(campaignPoExample);
        Assert.isTrue(!CollectionUtils.isEmpty(lauCampaignPos), errorMsgBuild(PARAM_ERROR, "没有有效的计划"));
        return lauCampaignPos.get(0);
    }

    public LauUnitPo getUnitByUnitId(Integer accountId, Integer unitId) {

        LauUnitPoExample example = new LauUnitPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andAccountIdEqualTo(accountId).andUnitIdEqualTo(unitId);
        List<LauUnitPo> lauUnitPos = lauUnitDao.selectByExample(example);
        Assert.isTrue(!CollectionUtils.isEmpty(lauUnitPos), errorMsgBuild(PARAM_ERROR, "没有有效的单元"));
        return lauUnitPos.get(0);
    }

    public List<LauUnitCreativePo> creativeNameCheck(CreativeDetailVos vos, Context context) {
        List<LauUnitCreativePo> ret = new ArrayList<>();
        Integer accountId = context.getAccountId();
        int unitId = vos.getUnit_id();
        LauUnitPo lauUnitPo = getUnitByUnitId(accountId, unitId);
        Set<String> baseCheckNameStrSet = new HashSet<>();

        fillImagesFromMgk(vos, accountId);

        for (CreativeDetailVo creativeVo : vos.getCreativeVos()) {
            if (creativeVo.getBilibili_video() == null) {
                Assert.notNull(creativeVo.getImages(), "创意图片列表不可为空");
                for (CpcImageVo image : creativeVo.getImages()) {

                    Assert.isTrue(image != null &&
                                    (Utils.isPositive(image.getId())
                                            || image.getImageInfo() != null
                                            && org.apache.commons.lang3.StringUtils.isNotBlank(image.getImageInfo().getMediaUrl())),
                            "图片id或者图片url不能同时为空");
                    if (StringUtils.isEmpty(image.getImage_hash())) {
                        image.setImage_hash("--");
                    }
                }
            }
            Assert.isTrue(!StringUtils.isEmpty(creativeVo.getPromotion_purpose_content()), errorMsgBuild(PARAM_ERROR, "promotion_purpose_content必填"));
//            Assert.isTrue(!StringUtils.isEmpty(creativeVo.getDescription()), errorMsgBuild(PARAM_ERROR, "description必传"));

            //init
            creativeVo.setTemplate_id(creativeVo.getTemplate_group_id());
            //creativeVo.setCreative_name("");
            if (Utils.isPositive(creativeVo.getJump_type())) {
                Assert.isTrue(JumpType.LINK.getCode().equals(creativeVo.getJump_type())
                                || JumpType.MGK_PAGE_ID.getCode().equals(creativeVo.getJump_type()),
                        "mapi仅支持跳转类型为链接或者落地页");
            } else {
                creativeVo.setJump_type(JumpType.LINK.getCode());
            }

            creativeVo.setPrefer_scene(PreferScene.PREFER_SCENE.getCode());
            //attach_type 校验
            if (null == creativeVo.getButton_copy_id()) {
                creativeVo.setAttach_type(CreativeAttachTypeEnum.NO_TEXT.getCode());
            } else {
                creativeVo.setAttach_type(CreativeAttachTypeEnum.BUTTON_COPY.getCode());
            }

            //直播间 开放创意配置方式字段 自动配置 支持自动过审  其他推广目的默认手动更新
            if (PromotionPurposeType.LIVE_ROOM.getCode() == lauUnitPo.getPromotionPurposeType()) {
                CreativeRefreshType byCode = CreativeRefreshType.getByCode(creativeVo.getRefresh_type());
            } else {
                creativeVo.setRefresh_type(0);
            }

            creativeVo.setForward_state(1);
            creativeVo.setReply_monitor(1);
            creativeVo.setReply_state(1);

            if (null == creativeVo.getShare_state()) {
                creativeVo.setShare_state(ShareState.CLOSE.getCode());
            }

//            if (null != creativeVo.getButton_copy_id()) {
//                Assert.isTrue(6 == creativeVo.getButton_copy_id(), errorMsgBuild(PARAM_ERROR, "button_copy_id支持6或者null"));
//            }

            //bfs校验
            List<CpcImageVo> images = creativeVo.getImages();
            if (!CollectionUtils.isEmpty(images)) {
                for (CpcImageVo image : images) {
                    Assert.isTrue(org.apache.commons.lang3.StringUtils.isBlank(image.getImage_url())
                            || image.getImage_url().contains("hdslb.com"), errorMsgBuild(PARAM_ERROR, "图片素材必须来源于本站"));
                }
            }

            LauUnitCreativePoExample example = new LauUnitCreativePoExample();
            String mediaMd5 = null;
            if (!CollectionUtils.isEmpty(creativeVo.getImages()) && null != creativeVo.getImages().get(0).getImageInfo()) {
                mediaMd5 = creativeVo.getImages().get(0).getImageInfo().getMediaMd5();
            }
            String mediaMd5Final = mediaMd5;
            String title = cpcSaveCreativeService.genWildcard(creativeVo.getTitle(), creativeVo.getDesc_wildcard());

            Long videoId = getVideoId(creativeVo);
            String baseCheckJson = generateNameCheckStr(creativeVo, mediaMd5Final, title, videoId);
            Assert.isTrue(!baseCheckNameStrSet.contains(baseCheckJson), "请求内容本身存在相同指纹创意");
            baseCheckNameStrSet.add(baseCheckJson);

            LauUnitCreativePoExample.Criteria criteria = example.or();
            criteria.andAccountIdEqualTo(accountId)
                    .andUnitIdEqualTo(unitId)
                    .andTemplateIdEqualTo(creativeVo.getTemplate_group_id())
                    .andDescriptionEqualTo(creativeVo.getDescription())
                    .andCreativeStatusIn(CreativeStatus.NON_DELETE_CREATIVE_STATUS_LIST)
                    .andFlagIn(Collections.singletonList(FlagEnums.OPEN_API.getId()));
            exampleParamInit(mediaMd5, criteria, () -> criteria.andImageMd5EqualTo(mediaMd5Final));
            exampleParamInit(creativeVo.getExt_description(), criteria, () -> criteria.andExtDescriptionEqualTo(creativeVo.getExt_description()));
            exampleParamInit(creativeVo.getVideo_url(), criteria, () -> criteria.andVideoUrlEqualTo(creativeVo.getVideo_url()));
            exampleParamInit(title, criteria, () -> criteria.andTitleEqualTo(title));
            exampleParamInit(videoId, criteria, () -> criteria.andVideoIdEqualTo(videoId));
//            exampleParamInit(creativeVo.getPromotion_purpose_content(), criteria, ()-> criteria.andPromotionPurposeContentEqualTo(creativeVo.getPromotion_purpose_content()));
            List<LauUnitCreativePo> duplicatedResult = lauUnitCreativeDao.selectByExample(example);
            ret.addAll(duplicatedResult);
        }
        return ret;
    }

    private void fillImagesFromMgk(CreativeDetailVos vos, Integer accountId) {
        List<Integer> queryImageIds = vos.getCreativeVos().stream()
                .filter(Objects::nonNull)
                .map(CreativeDetailVo::getImages)
                .filter(images -> !CollectionUtils.isEmpty(images))
                .flatMap(Collection::stream)
                .filter(Objects::nonNull)
                .map(CpcImageVo::getId)
                .filter(Utils::isPositive)
                .distinct()
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(queryImageIds)) {
            return;
        }

        QueryCollageMediaDto dto = QueryCollageMediaDto.builder()
                .accountIds(Collections.singletonList(accountId))
                .ids(queryImageIds)
                .pageInfo(Page.valueOf(1, queryImageIds.size()))
                .filterNoMd5(true)
                .build();
        PageResult<CollageMediaDto> mediaList = iSoaCollageMediaService.getMediaList(dto);
        List<CollageMediaDto> records = mediaList.getRecords();
        Assert.isTrue(records.size() == queryImageIds.size(), errorMsgBuild(PARAM_ERROR,
                "部分image id不存在或不属于当前账户"));

        Map<Integer, CollageMediaDto> recordMap = records.stream()
                .collect(Collectors.toMap(CollageMediaDto::getId, Function.identity()));

        vos.getCreativeVos().forEach(vo -> {
            if (Objects.isNull(vo)) {
                return;
            }
            List<CpcImageVo> imageVos = vo.getImages();
            if (CollectionUtils.isEmpty(imageVos)) {
                return;
            }
            imageVos.forEach(imageVo -> {
                if (Objects.isNull(imageVo) || !Utils.isPositive(imageVo.getId())) {
                    return;
                }
                CollageMediaDto collageMediaDto = recordMap.get(imageVo.getId());
                ImageInfoVo imageInfoVo = ImageInfoVo.builder()
                        .mediaId(String.valueOf(collageMediaDto.getMediaId()))
                        .mediaMd5(collageMediaDto.getMediaMd5())
                        .mediaUrl(collageMediaDto.getMediaUrl())
                        .mediaName(collageMediaDto.getMediaName())
                        .mediaOrigin(collageMediaDto.getMediaOrigin())
                        .mediaRatio(collageMediaDto.getMediaRatio())
                        .mediaSize(collageMediaDto.getMediaSize())
                        .mediaType(collageMediaDto.getMediaType())
                        .width(collageMediaDto.getWidth())
                        .height(collageMediaDto.getHeight())
                        .worksId(collageMediaDto.getWorksId())
                        .patternId(collageMediaDto.getPatternId())
                        .build();
                imageVo.setImageInfo(imageInfoVo);
                imageVo.setImage_url(collageMediaDto.getMediaUrl());
            });
        });
    }

    private String generateNameCheckStr(CreativeDetailVo creativeDetailVo, String mediaMd5, String title, Long videoId) {
        Assert.notNull(creativeDetailVo, "创意校验对象不能为空");

        CreativeNameCheckBo checkBo = CreativeNameCheckBo.builder()
                .title(title)
                .mediaMd5(mediaMd5)
                .videoUrl(creativeDetailVo.getVideo_url())
                .templateId(creativeDetailVo.getTemplate_group_id())
                .description(creativeDetailVo.getDescription())
                .extDescription(creativeDetailVo.getExt_description())
                .videoId(videoId)
                .build();
        return JSON.toJSONString(checkBo);
    }

    private Long getVideoId(CreativeDetailVo creativeDetailVo) {
        Long videoId = null;
        if (Objects.nonNull(creativeDetailVo.getBilibili_video())
                && Utils.isPositive(creativeDetailVo.getBilibili_video().getAvId())) {
            videoId = creativeDetailVo.getBilibili_video().getAvId();
        }
        return videoId;
    }

    public <T> T exampleParamInit(Object param, T defaultValue, Supplier<T> supplier) {
        if (null == param) {
            return defaultValue;
        }

        if (param instanceof String) {
            if (StringUtils.isEmpty(param)) {
                return defaultValue;
            }
        } else if (param instanceof Collection) {
            if (CollectionUtils.isEmpty((Collection) param)) {
                return defaultValue;
            }
        }
        return supplier.get();
    }

    public void creativeValidator(Context context, CpcUnitDto unit, CreativeDetailVos vos) throws ServiceException {
        final boolean create = CollectionUtils.isEmpty(vos.getCreativeVos()) || vos.getCreativeVos().stream()
                .noneMatch(creative -> Utils.isPositive(creative.getCreative_id()));
        final boolean checkLimit = CollectionUtils.isEmpty(vos.getCreativeVos()) || vos.getCreativeVos().stream()
                .anyMatch(creative -> !Utils.isPositive(creative.getCreative_id()));

        if (context.isOpenApi()) {

            if (unit.getPromotionPurposeType() == PromotionPurposeType.GAME_CARD.getCode()) {
                checkAndSetGameCardDefaultInfo(vos);
                return;
            }

            if (Objects.isNull(vos.getAdvertisingMode())) {
                vos.setAdvertisingMode(AdvertisingMode.NORMAL_CONTENT.getKey());
            }
            Integer advertisingMode = vos.getAdvertisingMode();
            AdvertisingMode advertisingModeEnum = AdvertisingMode.getByKey(advertisingMode);
            Assert.isTrue(!AdvertisingMode.UNKNOWN.equals(advertisingModeEnum), "未知的投放模式");
            validateNativeContentCreative(context.getAccountId(), vos);

            if (!CollectionUtils.isEmpty(vos.getCreativeVos())) {
                CpcCampaignDto campaign = cpcCampaignService.loadCpcCampaignDto(unit.getCampaignId());
                validateNotFly(unit, campaign);
                int campaignAdpVersion = campaign.getAdpVersion();
                int unitAdpVersion = unit.getAdpVersion();
                List<Integer> creativeIsMiddleVal = vos.getCreativeVos().stream().map(CreativeDetailVo::getIs_middle_ad)
                        .distinct().collect(Collectors.toList());
                Assert.isTrue(creativeIsMiddleVal.size() == 1, "不可在同一单元下保存不同系统版本必选创意");

                boolean campaignIsMiddle = AdpVersion.isMiddle(campaignAdpVersion),
                        unitIsMiddle = AdpVersion.isMiddle(unitAdpVersion),
                        creativeIsMiddle = Utils.isPositive(creativeIsMiddleVal.get(0));
                Assert.isTrue(campaignIsMiddle == creativeIsMiddle
                        && unitIsMiddle == creativeIsMiddle, "不允许在计划/单元下新增不同系统版本创意");
                Assert.notEmpty(vos.getTags(), "创意标签不可为空");
            }

            fillDefaultPromotionContentIfNecessary(unit, vos);
            fillDefaultValue(vos.getCreativeVos());
            Assert.isTrue(vos.getCreativeVos().size() <= 10, "mapi单个单元下最大保存十个自定义创意");

            checkBrandInfo(vos.getEnableSpace(), vos.getBrandInfoId(), vos.getSpaceMid(), unit);
            List<Integer> qualificationPackageIds = vos.getCreativeVos().stream()
                    .map(CreativeDetailVo::getQualification_package_id)
                    .filter(Utils::isPositive)
                    .distinct()
                    .collect(Collectors.toList());
            List<Integer> qualificationIds = vos.getCreativeVos().stream()
                    .map(CreativeDetailVo::getQualification_ids)
                    .filter(subList -> !CollectionUtils.isEmpty(subList))
                    .flatMap(Collection::stream)
                    .distinct()
                    .collect(Collectors.toList());
            checkQualification(context.getAccountId(),
                    qualificationPackageIds,
                    qualificationIds,
                    unit.getPromotionPurposeType());

            List<Integer> miniGameIdList = vos.getCreativeVos().stream()
                    .map(CreativeDetailVo::getMini_game_id)
                    .collect(Collectors.toList());

            List<String> miniGameUrlList = vos.getCreativeVos().stream()
                    .map(CreativeDetailVo::getMini_game_url)
                    .collect(Collectors.toList());
            validateMiniGame(miniGameIdList, miniGameUrlList, context.getAccountId(), unit.getPromotionPurposeType());

            //旧广告标设置默认值 广告
            vos.setCm_mark(1);
            // 新广告标如果没有的话 需要有默认值 否则更新会无条件重复推审
            vos.getCreativeVos().forEach(vo -> {
                if (Objects.isNull(vo.getBus_mark_id())) {
                    vo.setBus_mark_id(Utils.isPositive(vos.getAdvertisingMode()) ? 54 : 1);
                }
            });

            if (Objects.equals(unit.getAdpVersion(), AdpVersion.VIDEO_MERGED.getKey())
                    || AdpVersion.isMiddle(unit.getAdpVersion())) {
                vos.setChannelId(ChannelEnum.MOB.getCode());
            }

            vos.setFlag(RequestTypeEnum.OPEN_API.getId());
            List<LauUnitCreativePo> lauUnitCreativePos = creativeNameCheck(vos, context);
            if (checkLimit) {
                checkLimit(context.getAccountId(), vos.getUnit_id(), OpenAPILimitType.CREATIVE);
            }
            if (!create) {
                for (CreativeDetailVo creativeVo : vos.getCreativeVos()) {
                    lauUnitCreativePos.removeIf(item -> item.getCreativeId().equals(creativeVo.getCreative_id()));
                }
            }
            Assert.isTrue(CollectionUtils.isEmpty(lauUnitCreativePos), errorMsgBuild(REPEAT_REQUEST, "存在相同指纹创意"));

            List<CpcCreativeDto> creatives = creativeServiceDelegate.queryCpcCreativeDto(
                    QueryCpcCreativeDto.builder()
                            .accountId(context.getAccountId())
                            .statusList(com.bilibili.adp.launch.api.common.LaunchStatus.CAN_LAUNCH_STATUS)
                            .unitId(unit.getUnitId())
                            .build()
            );
            if (!CollectionUtils.isEmpty(creatives)) {
                if (AdvertisingMode.NORMAL_CONTENT.getKey().equals(advertisingMode)) {
                    boolean allNormalContent = creatives.stream()
                            .allMatch(creative ->
                                    AdvertisingMode.NORMAL_CONTENT.getKey().equals(creative.getAdvertisingMode()));
                    Assert.isTrue(allNormalContent, "一个单元下不能同时创建常规投放创意和内容投放创意");
                } else {
                    boolean allNativeContent = creatives.stream()
                            .allMatch(creative ->
                                    AdvertisingMode.NATIVE_CONTET.getKey().equals(creative.getAdvertisingMode()));
                    Assert.isTrue(allNativeContent, "一个单元下不能同时创建常规投放创意和内容投放创意");
                }

                boolean hasProgramCreative =
                        creatives.stream().anyMatch(creative -> creative.getIsProgrammatic() == 1);
                Assert.isTrue(!hasProgramCreative, "创建失败，已存在程序化创意，不能操作自定义创意");
                Set<Integer> creativeIds = creatives.stream().map(CpcCreativeDto::getCreativeId).collect(Collectors.toSet());

                Set<Integer> updateCreativeIds;
                if (CollectionUtils.isEmpty(vos.getCreativeVos())) {
                    updateCreativeIds = new LinkedHashSet<>();
                } else {
                    updateCreativeIds = vos.getCreativeVos().stream()
                            .map(CreativeDetailVo::getCreative_id)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
                }
                Assert.isTrue(CollectionUtils.isEmpty(updateCreativeIds) || creativeIds.containsAll(updateCreativeIds), "更新的创意不属于这个单元");
            }
        }
    }

    private void fillDefaultValue(List<CreativeDetailVo> creativeVos) {
        if (CollectionUtils.isEmpty(creativeVos)) {
            return;
        }

        creativeVos.forEach(vo -> {
            if (Objects.isNull(vo.getDescription())) {
                vo.setDescription("");
            }
        });
    }

    public void validateNotFlyUnit(Integer unitId, Context context) {
        if (!context.isOpenApi()) {
            return;
        }
        Assert.notNull(unitId, "单元id不可为空");
        LauUnitPo unitPo = getUnitByUnitId(context.getAccountId(), unitId);
        CpcCampaignDto campaign = cpcCampaignService.loadCpcCampaignDto(unitPo.getCampaignId());
        boolean isMiddleFly = OpenApiCommonUtil.judgeIsFly(campaign.getPromotionPurposeType(), unitPo.getOcpcTarget(), unitPo.getSalesType());
        Assert.isTrue(!isMiddleFly, "不允许使用必选程序化创意接口查询起飞程序化创意, 目前不支持起飞程序化创意相关功能");
    }

    private void validateNotFly(CpcUnitDto unit, CpcCampaignDto campaign) {
        Assert.isTrue(Objects.nonNull(unit)
                && Utils.isPositive(unit.getUnitId()), "单元不能为空");
        boolean isMiddleFly = OpenApiCommonUtil.judgeIsFly(campaign.getPromotionPurposeType(), unit.getOcpcTarget(), unit.getSalesType());
        Assert.isTrue(!isMiddleFly,
                "当前单元计划参数不支持非起飞保存创意接口,推广目的为" + campaign.getPromotionPurposeType() +
                        ",优化目标为:" + unit.getOcpcTarget() + "出价类型为:" + unit.getSalesType());
    }

    private void checkAndSetGameCardDefaultInfo(CreativeDetailVos vo) {
        vo.setAdvertisingMode(AdvertisingMode.NORMAL_CONTENT.getKey());
        vo.setChannelId(ChannelEnum.MOB.getCode());
        vo.setCm_mark(CmMarkEnum.GAME.getCode());

        List<CreativeDetailVo> details = vo.getCreativeVos();
        Assert.isTrue(!CollectionUtils.isEmpty(details) && details.size() == 1, "游戏卡只能有一个创意信息");

        CreativeDetailVo detail = details.get(0);
        Assert.isTrue(detail.getTemplate_group_id() != 0, "广告位组不能为0");
        detail.setCreative_name("创意1");
        boolean hasQualification = Utils.isPositive(detail.getQualification_package_id())
                || !CollectionUtils.isEmpty(detail.getQualification_ids());
        Assert.isTrue(!hasQualification, "游戏卡不支持资质配置");
    }

    private void checkBrandInfoBase(Integer brandInfoId, Integer accountId) {
        if (!Utils.isPositive(brandInfoId)) {
            return;
        }
        //brand info 校验
        List<LauAccountInfoBo> bos = launchBrandInfoService.list(accountId);
        List<Integer> brandInfoIds = bos.stream().map(LauAccountInfoBo::getId).collect(Collectors.toList());
        Assert.isTrue(!CollectionUtils.isEmpty(brandInfoIds), errorMsgBuild(PARAM_ERROR, "账户品牌不存在, 请先创建品牌"));
        Assert.isTrue(brandInfoIds.contains(brandInfoId), errorMsgBuild(PARAM_ERROR, "brand_info_id不准确"));
    }

    private void checkOpenSpaceBase(Long openSpaceMid, Integer accountId) {
        if (!Utils.isPositive(openSpaceMid)) {
            return;
        }
        boolean hasCorp = archiveService.checkAccountMidCorp(accountId, null, openSpaceMid);
        Assert.isTrue(hasCorp, "账户授权mid不存在，请先授权");
    }

    private void checkBrandInfo(Integer enableSpace, Integer brandInfoId, Long spaceMid, CpcUnitDto unitDto) {
        if (Utils.isPositive(enableSpace)) {
            brandInfoId = 0;
        } else {
            spaceMid = 0L;
        }

        boolean hasBrandInfo = Utils.isPositive(brandInfoId)
                || Utils.isPositive(spaceMid);
        Assert.isTrue(hasBrandInfo || unitDto.getBusinessDomain().equals(BusinessDomain.BUSINESS_FLY),
                "必选品牌空间信息必传");
        if (hasBrandInfo) {
            Assert.isTrue(!unitDto.getBusinessDomain().equals(BusinessDomain.BUSINESS_FLY), "起飞不支持品牌空间");
        }

        if (Utils.isPositive(brandInfoId)) {
            checkBrandInfoBase(brandInfoId, unitDto.getAccountId());
        }

        if (Utils.isPositive(spaceMid)) {
            checkOpenSpaceBase(spaceMid, unitDto.getAccountId());
        }
    }

    private void checkProgrammaticBrandInfo(Integer brandInfoId, Long spaceMid, CpcUnitDto unitDto) {
        boolean hasBrandInfo = Utils.isPositive(brandInfoId)
                || Utils.isPositive(spaceMid);
        Assert.isTrue(hasBrandInfo || unitDto.getBusinessDomain().equals(BusinessDomain.BUSINESS_FLY),
                "必选品牌空间信息必传");
        if (hasBrandInfo) {
            Assert.isTrue(!unitDto.getBusinessDomain().equals(BusinessDomain.BUSINESS_FLY), "起飞不支持品牌空间");
        }
        Assert.isTrue(!Utils.isPositive(brandInfoId) || !Utils.isPositive(spaceMid), "品牌标和开放空间不能同时配置");

        if (Utils.isPositive(brandInfoId)) {
            checkBrandInfoBase(brandInfoId, unitDto.getAccountId());
        }

        if (Utils.isPositive(spaceMid)) {
            checkOpenSpaceBase(spaceMid, unitDto.getAccountId());
        }
    }

    private void checkQualification(Integer accountId,
                                    List<Integer> qualificationPackageIds,
                                    List<Integer> qualificationIds,
                                    Integer uppt) {
        if (CollectionUtils.isEmpty(qualificationPackageIds) && CollectionUtils.isEmpty(qualificationIds)) {
            return;
        }

        AccountBaseDto accountBaseDto = queryAccountService.getAccountBaseDtoByIdFromCache(accountId);
        Assert.isTrue(Constants.INT_FALSE == accountBaseDto.getIsSupportDpa(), "dpa账户不支持资质");

        Assert.isTrue(PromotionPurposeType.GAME_CARD.getCode() != uppt
                && PromotionPurposeType.GAME_ACTIVITY_CARD.getCode() != uppt, "游戏卡和游戏活动卡不支持资质");


        Assert.isTrue(CollectionUtils.isEmpty(qualificationIds)
                || CollectionUtils.isEmpty(qualificationPackageIds), "资质和资质包不能同时选择");

        if (!CollectionUtils.isEmpty(qualificationIds)) {
            QualificationQueryDto queryDto = QualificationQueryDto.builder()
                    .curAccountId(accountId)
                    .qualificationIds(qualificationIds)
                    .build();
            List<LauQualificationBo> lauQualificationBos =
                    middleCreativeQualificationService.qualificationDropList(queryDto);
            Assert.isTrue(lauQualificationBos.size() == qualificationIds.size(), "部分资质无效或不属于当前账户");
        }

        if (!CollectionUtils.isEmpty(qualificationPackageIds)) {
            QualificationPackageQueryDto queryDto = QualificationPackageQueryDto.builder()
                    .packageIds(qualificationPackageIds)
                    .curAccountId(accountId)
                    .build();
            PageResult<LauQualificationPackageBo> allResult =
                    middleCreativeQualificationPackageService.listByAccountId(queryDto);
            Assert.isTrue(qualificationPackageIds.size() == allResult.getRecords().size(), "部分资质包无效或不属于当前账户");
        }
    }

    private void validateMiniGame(List<Integer> miniGameIdList, List<String> miniGameUrlList, int accountId, int uppt) {
        List<Integer> miniGameIdCheckList = miniGameIdList.stream()
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(miniGameIdCheckList)) {
            return;
        }
        Assert.isTrue(PromotionPurposeType.LANDING_PAGE.getCode() == uppt, "目前仅销售线索收集且非起飞单元下支持微信小游戏组件");
        QueryLauMiniGameDto queryDto = QueryLauMiniGameDto.builder()
                .accountId(accountId)
                .state(YesNoEnum.YES.getCode())
                .ids(miniGameIdCheckList)
                .build();
        List<LauMiniGameDto> lauMiniGameDtos = lauMiniGameService.queryLauMiniGames(queryDto);
        Assert.isTrue(lauMiniGameDtos.size() == miniGameIdCheckList.size(),
                "您不能绑定不属于当前账户或未发布的微信小游戏");
        Map<Integer, String> miniGameDataMap = lauMiniGameDtos.stream()
                .collect(Collectors.toMap(LauMiniGameDto::getId, LauMiniGameDto::getGameUrl));
        IntStream.range(0, miniGameIdList.size()).forEach(index -> {
            Integer miniGameId = miniGameIdList.get(index);
            if (!Utils.isPositive(miniGameId)) {
                return;
            }
            String miniGameUrl = miniGameUrlList.get(index);
            String miniGameExistUrl = miniGameDataMap.get(miniGameId);
            Assert.isTrue(!StringUtils.isEmpty(miniGameUrl), "微信小游戏url不可为空");
            Assert.isTrue(miniGameUrl.contains("__TRACKID__"), "小游戏路径必须包含通配符__TRACKID__");
            if (!StringUtils.isEmpty(miniGameExistUrl)) {
                Assert.isTrue(miniGameExistUrl.equals(miniGameUrl),
                        "微信小游戏路径不匹配，微信小游戏路径:" + miniGameId + "微信小游戏url:" + miniGameUrl);
            }
        });
    }

    private void validateNativeContentCreative(int accountId, CreativeDetailVos vos) {
        AdvertisingMode advertisingMode = AdvertisingMode.getByKey(vos.getAdvertisingMode());
        Assert.isTrue(!AdvertisingMode.UNKNOWN.equals(advertisingMode), "未知的创意投放形式");
        if (!AdvertisingMode.NATIVE_CONTET.equals(advertisingMode)) {
            return;
        }
        Assert.isTrue(Objects.nonNull(vos.getBilibiliVideo()), "内容投放必须绑定稿件");
        Long avid = vos.getBilibiliVideo().getAvId();
        Assert.isTrue(Utils.isPositive(avid), "内容投放绑定稿件id不可为空");
        vos.getCreativeVos().forEach(creativeVo -> {
            Assert.notNull(creativeVo.getBilibili_video(), "内容投放创意必须绑定稿件");
            Assert.isTrue(Utils.isPositive(creativeVo.getBilibili_video().getAvId()), "内容投放创意绑定稿件id不可为空");
            Assert.isTrue(avid.equals(creativeVo.getBilibili_video().getAvId()), "内容投放单元下创意绑定素材稿件id必须与整体绑定稿件id一致");
        });
//        validateNativeContentArc(accountId, avid);
    }

    private void validateNativeContentArc(int accountId, long avid) {
        AccountDto accountDto = queryAccountService.getAccount(accountId);
        // bilibili账号视频
        ArchiveBo arcBo = archiveService.getArchiveByAid(avid);
        Assert.notNull(arcBo, "稿件不存在");
        Long mid = arcBo.getAuthor().getMid();
        boolean isBilibiliArc = archiveService.checkAccountMidCorpWithoutBlueV(accountDto, accountId, null, mid);
        if (isBilibiliArc) {
            return;
        }

        // 必选商单内容投放稿件逻辑
        //内容投放只支持 mode = 1
        //授权模式 1-原视频+UP主空间头像 2-原视频
        SoaGetPageAdAuthReqDto reqDto = new SoaGetPageAdAuthReqDto();
        reqDto.setAdvertisersAccountId(accountId);
        reqDto.setState(1);
        reqDto.setStateExt(4);
        reqDto.setMode(1);
        reqDto.setAvid(avid);
        PageResult<SoaAdAuthInfoDto> pageAdAuth = soaAdAuth4AdpService.getPageAdAuth(reqDto);
        if (!CollectionUtils.isEmpty(pageAdAuth.getRecords())) {
            return;
        }

        // 必选普通视频内容投放稿件逻辑
        boolean isCommonNativeContentArc = generalVideoAuthService.checkAvidHasAuth(accountId, avid, 1);
        if (isCommonNativeContentArc) {
            return;
        }

        throw new IllegalArgumentException("仅原视频+UP主空间头像授权模式的 商单视频/普通内容视频 或者bilibili账号视频支持内容投放");

    }

    public void programmedCreativeValidatorIfNecessary(Context context, SaveProgrammaticCreativeVo vo) throws ServiceException {
        if (!context.isOpenApi()) {
            return;
        }
        //设置默认值
        fillProgrammedCreativeDefaultValue(vo);
        //基础参数校验
        checkProgrammedCreativeBaseParams(context.getAccountId(), vo);
        // 内容投放校验
        validateNativeContentProgrammaticCreative(context.getAccountId(), vo);
        //新增时校验数量
        if (vo.getCreativeId() == null || vo.getCreativeId() == 0) {
            checkLimit(context.getAccountId(), vo.getUnitId(), OpenAPILimitType.PROGRAMMED_CREATIVE);
        }
        //根据单元信息进行校验
        checkProgrammedCreativeWithUnit(vo, context.getAccountId());
    }

    public void dpaCreativeValidatorIfNecessary(Context context, Integer unitId, DpaUnitVo dpaUnitVo) {
        if (!context.isOpenApi() || dpaUnitVo == null) {
            return;
        }
        //设置默认值
        fillDpaCreativeDefaultValue(unitId, dpaUnitVo);
    }

    private void fillDpaCreativeDefaultValue(Integer unitId, DpaUnitVo dpaUnitVo) {

        dpaUnitVo.setEnableSpace(0);
        List<DpaCreativeVo> creatives = dpaUnitVo.getCreatives();
        if (!CollectionUtils.isEmpty(creatives)) {
            creatives.forEach(creative -> {
                creative.setUnitId(unitId);
                creative.setCmMark(1);
                creative.setRefreshType(0);
                creative.setBusMarkId(1);
            });
        }
    }

    public void filterProgrammedCreativeInfoIfNecessay(Context context, ProgrammaticCreativeVo creative) {
        if (!context.isOpenApi()) {
            return;
        }
        Assert.isTrue(isProgrammedCreative(creative.getUnitId()),
                "该单元下创意为自定义创意，请使用自定义创意查询接口");
        if (!CollectionUtils.isEmpty(creative.getCreativeMonitoring())) {
            //返回时过滤安卓游戏自动填入的监控链接
            creative.setCreativeMonitoring(
                    creative.getCreativeMonitoring()
                            .stream()
                            .filter(monitor -> monitor.getType() != 6)
                            .collect(Collectors.toList())
            );
        }
    }

    public void checkCreativeTypeIfNecessary(Context context, int unitId) {
        if (!context.isOpenApi()) {
            return;
        }
        Assert.isTrue(!isProgrammedCreative(unitId), "该单元下创意为程序化创意，请使用程序化创意查询接口");
    }

    private boolean isProgrammedCreative(int unitId) {
        LauUnitCreativeBo lauUnitCreativeBo = adCoreBqf.from(lauUnitCreative)
                .select(lauUnitCreative.isProgrammatic)
                .where(lauUnitCreative.unitId.eq(unitId)
                        .and(lauUnitCreative.status.ne(LaunchStatus.DELETE.getCode())))
                .fetchFirst(LauUnitCreativeBo.class);
        return lauUnitCreativeBo != null && lauUnitCreativeBo.getIsProgrammatic() == 1;
    }

    /**
     * 过滤模板
     *
     * @param context
     * @param styleVos
     * @return
     */
    public List<StyleVo> filterTemplateIfNecessary(Context context, List<StyleVo> styleVos) {
        // 非 openApi 不需要过滤
        if (!context.isOpenApi()) {
            return styleVos;
        }
        if (CollectionUtils.isEmpty(styleVos)) {
            return styleVos;
        }

        // 过滤出 tg status 为1的样式
        return styleVos.stream()
                .filter(style -> {
                    SimpleTemplateGroupVo simpleTemplateGroup = style.getSimpleTemplateGroup();
                    if (simpleTemplateGroup != null) {
                        Integer status = simpleTemplateGroup.getTgStatus();
                        return status != null && status == 1;
                    }
                    return false;
                }).collect(Collectors.toList());
    }

    private void checkProgrammedCreativeWithUnit(SaveProgrammaticCreativeVo vo, int accountId) throws ServiceException {

        CpcUnitDto unit = cpcUnitService.loadCpcUnit(vo.getUnitId());
        Assert.isTrue(unit != null, "单元不存在或已删除");

        vo.setAdpVersion(unit.getAdpVersion());
        if (Objects.equals(unit.getAdpVersion(), AdpVersion.VIDEO_MERGED.getKey())) {
            vo.setChannelId(ChannelEnum.MOB.getCode());
        }
        Assert.isTrue(programmedSupportPromotions.stream().anyMatch(promotionEnum -> promotionEnum.getCode() == unit.getPromotionPurposeType()),
                "程序化创意推广目的仅支持" + programmedSupportPromotions.stream().map(PromotionPurposeType::getDesc).collect(Collectors.joining(",")));
        Assert.isTrue(unit.getSalesType() != SalesType.CPM.getCode(), "程序化创意不支持CPM计费");
        CpcCampaignDto campaign = cpcCampaignService.loadCpcCampaignDto(unit.getCampaignId());
        validateNotFly(unit, campaign);
        fillDefaultPromotionContentIfNecessary(unit, vo);

//        checkProgrammedOcpcTarget(accountId, unit.getPromotionPurposeType(), unit.getOcpcTarget());

        checkProgrammaticBrandInfo(vo.getBrandInfoId(), vo.getSpaceMid(), unit);
        List<Integer> qualificationPackageIdList = Utils.isPositive(vo.getQualificationPackageId()) ?
                Lists.newArrayList(vo.getQualificationPackageId()) : Collections.emptyList();
        checkQualification(accountId,
                qualificationPackageIdList,
                vo.getQualificationIds(),
                unit.getPromotionPurposeType());

        List<CpcCreativeDto> creatives = creativeServiceDelegate.queryCpcCreativeDto(
                QueryCpcCreativeDto.builder()
                        .accountId(accountId)
                        .statusList(com.bilibili.adp.launch.api.common.LaunchStatus.CAN_LAUNCH_STATUS)
                        .unitId(vo.getUnitId())
                        .build()
        );

        if (vo.getCreativeId() == null || vo.getCreativeId() == 0) {
            Assert.isTrue(CollectionUtils.isEmpty(creatives) || creatives.stream().noneMatch(creative -> creative.getIsProgrammatic().equals(0)),
                    "创建失败，已存在自定义程序化创意，不能新建程序化创意");
            Assert.isTrue(CollectionUtils.isEmpty(creatives), "创建失败，一个单元下只能有一个程序化创意");
        } else {
            CpcCreativeDto updateTarget = creatives.stream()
                    .filter(creative -> vo.getCreativeId().equals(creative.getCreativeId()))
                    .findFirst().orElse(null);
            Assert.notNull(updateTarget, "更新的程序化创意不属于这个单元");
            Assert.isTrue(updateTarget.getAdvertisingMode().equals(vo.getAdvertisingMode()), "不能改变程序化创意的投放类型:常规投放/内容投放");
        }

        validateMiniGame(Lists.newArrayList(vo.getMiniGameId()),
                Lists.newArrayList(vo.getMiniGameUrl()),
                accountId,
                unit.getPromotionPurposeType());
    }

    private void checkProgrammedCreativeBaseParams(int accountId, SaveProgrammaticCreativeVo vo) {
        Assert.isTrue(Arrays.stream(JumpTypeEnum.values()).anyMatch(jumpType -> Objects.equals(jumpType.getCode(),
                vo.getJumpType())), "跳转类型不正确");
        Assert.isTrue(!CollectionUtils.isEmpty(vo.getTags()), "标签不能为空");
        Assert.isTrue(vo.getBuSecondCategoryId() != null, "创意分类二级类目ID不能为空");
        Assert.isTrue(!StringUtils.isEmpty(vo.getTitles()) && vo.getTitles().size() >= 3 && vo.getTitles().size() <= 6,
                "程序化创意标题数量最少3个最多6个");
        Assert.isTrue(vo.getTitles().stream().map(TitleVo::getTitle).collect(Collectors.toSet()).size() == vo.getTitles().size(),
                "程序化创意标题重复");
        Function<List<?>, Integer> getListSize = list -> {
            if (CollectionUtils.isEmpty(list)) {
                return 0;
            } else {
                return list.size();
            }
        };
        List<MediaGroupVo> mediaGroups = vo.getMediaGroups();
        int totalElementSize = 0;

        for (MediaGroupVo mediaGroup : mediaGroups) {
            int eachGroupElementSize = 0;
            //目前mapi不支持视频
            Assert.isTrue(CollectionUtils.isEmpty(mediaGroup.getMgkVideoMediaList()), "不支持上传本地视频信息");
            Assert.isTrue(CollectionUtils.isEmpty(mediaGroup.getImageSetMediaList()), "mapi不支持三图");
            //eachGroupElementSize += getListSize.apply(mediaGroup.getMgkVideoMediaList());
            eachGroupElementSize += getListSize.apply(mediaGroup.getMonoMediaList());
            eachGroupElementSize += getListSize.apply(mediaGroup.getImageSetMediaList());
            eachGroupElementSize += getListSize.apply(mediaGroup.getBiliVideoMediaList());
            List<BiliVideoMediaVo> biliVideoMediaList = mediaGroup.getBiliVideoMediaList();
            if (!CollectionUtils.isEmpty(biliVideoMediaList)) {
                biliVideoMediaList.forEach(biliVideoMediaVo -> {
                    if (Objects.isNull(biliVideoMediaVo)) {
                        return;
                    }
                    // 后续会进行封面信息的填充 这里不进行校验
                    Assert.isTrue(Utils.isPositive(biliVideoMediaVo.getAvId()), "程序化创意稿件id不可为空");
                });
            }
            Assert.isTrue(eachGroupElementSize <= 6, "单个模板下元素最多只能有6个");
            totalElementSize += eachGroupElementSize;
        }
        Assert.isTrue(totalElementSize >= 2, "所有模板对应素材总数必须大于等于2个");
    }

    private void validateNativeContentProgrammaticCreative(int accountId, SaveProgrammaticCreativeVo saveVo) {
        AdvertisingMode advertisingMode = AdvertisingMode.getByKey(saveVo.getAdvertisingMode());
        Assert.isTrue(!AdvertisingMode.UNKNOWN.equals(advertisingMode), "未知的创意投放形式");
        if (!AdvertisingMode.NATIVE_CONTET.equals(advertisingMode)) {
            return;
        }
        Assert.isTrue(Objects.nonNull(saveVo.getBilibiliVideo()), "内容投放必须绑定稿件");
        Long avid = saveVo.getBilibiliVideo().getAvId();
        Assert.isTrue(Utils.isPositive(avid), "内容投放绑定稿件id不可为空");
        validateNativeContentArc(accountId, avid);
        Assert.isTrue(saveVo.getMediaGroups()
                        .stream()
                        .allMatch(mediaGroup -> CollectionUtils.isEmpty(mediaGroup.getBiliVideoMediaList())),
                "内容投放程序化创意不允许自行选择素材稿件");
    }

    private void fillProgrammedCreativeDefaultValue(SaveProgrammaticCreativeVo vo) {
        vo.setFlag(RequestTypeEnum.OPEN_API.getId());
        vo.setCmMark(1);
        if (Objects.isNull(vo.getAdvertisingMode())) {
            vo.setAdvertisingMode(AdvertisingMode.NORMAL_CONTENT.ordinal());
        }
        vo.setCreativeName("创意");
        vo.setPreferScene(PreferScene.PREFER_SCENE.getCode());
        if (vo.getScenes() == null) {
            vo.setScenes(new LinkedList<>());
        }
        if (vo.getAttachType() == null) {
            vo.setAttachType(0);
        }

        vo.setIsAutoFill(0);
        vo.setShareState(0);
        vo.setForwardState(1);
        vo.setRefreshType(1);
        vo.setReplyMonitor(1);
        vo.setReplyState(1);
        if (!Utils.isPositive(vo.getBusMarkId())) {
            // 内容投放默认54 常规默认1
            vo.setBusMarkId(Utils.isPositive(vo.getAdvertisingMode()) ? 54 : 1);
        }
        //mediaGroup中的三图和稿件都不支持，@球童
    }

    private void checkLimit(int accountId, int unitId, OpenAPILimitType createType) {
        Integer dayLimitValue = getLimitValue(createType, LimitBsiType.DAY_LIMIT, accountId);
        Assert.isTrue(dayCountCreativeOpenAPI(accountId) < dayLimitValue, errorMsgBuild(DAY_LIMIT, "可用创意个数超过" + dayLimitValue + "日上限"));
        Integer allLimitValue = getLimitValue(createType, LimitBsiType.ALL_LIMIT, accountId);
        Assert.isTrue(countCreativeOpenAPI(accountId) < allLimitValue, errorMsgBuild(ALL_LIMIT, "可用创意个数超过" + allLimitValue + "上限"));
        Assert.isTrue(countUnitCreativeOpenAPI(accountId, unitId) <= 10, errorMsgBuild(PARAM_ERROR, "单个单元创意个数超过10上限"));
    }

    private void checkProgrammedOcpcTarget(int accountId, int promotionPurposrType, int ocpcTarget) {
        if (promotionPurposrType == PromotionPurposeType.APP_DOWNLOAD.getCode()) {

            Assert.isTrue(ocpcTarget == 0 ||
                            ocpcTarget == OcpcTargetEnum.APP_ACTIVE.getCode() ||
                            ocpcTarget == OcpcTargetEnum.GAME_RESERVE.getCode(),
                    "应用推广目的下，如果指定ocpc，只支持应用激活或游戏预约");
        } else if (promotionPurposrType == PromotionPurposeType.ON_SHELF_GAME.getCode()) {
            List<OcpcTargetEnum> gameOcpcTargets = Arrays.asList(
                    OcpcTargetEnum.APP_ACTIVE,
                    OcpcTargetEnum.GAME_RESERVE,
                    OcpcTargetEnum.USER_REGISTER,
                    OcpcTargetEnum.USER_COST,
                    OcpcTargetEnum.ANDROID_GAME_CENTER_ACTIVATION
            );
            Assert.isTrue(ocpcTarget == 0 || gameOcpcTargets.stream().anyMatch(targetEnum -> targetEnum.getCode() == ocpcTarget),
                    "安卓游戏目的下，如果指定ocpc，只支持" + gameOcpcTargets.stream().map(OcpcTargetEnum::getDesc).collect(Collectors.joining(",")));

        } else if (promotionPurposrType == PromotionPurposeType.LANDING_PAGE.getCode()) {
            boolean supportClueOrder = soaAccountLabelService.checkAccountWithLabel(accountId, clueOrderPlaceLabel);
            Assert.isTrue(ocpcTarget == 0 || ocpcTarget == OcpcTargetEnum.FORM_SUBMIT.getCode()
                            || supportClueOrder && ocpcTarget == OcpcTargetEnum.GOODS_TRANSACTION.getCode(),
                    "销售线索收集目的下，如果指定ocpc，只支持特定的优化目标类型");
        }
    }

    private void fillDefaultPromotionContentIfNecessary(CpcUnitDto unit, SaveProgrammaticCreativeVo vo) {
        if (com.bilibili.adp.cpc.enums.ad.PromotionPurposeType.ON_SHELF_GAME.getCode() == unit.getPromotionPurposeType()) {
            if (org.apache.commons.lang3.StringUtils.isBlank(vo.getPromotionPurposeContent())) {
                if (unit.getGameDto() == null || org.apache.commons.lang3.StringUtils.isBlank(unit.getGameDto().getGameLink())) {
                    throw new IllegalArgumentException(errorMsgBuild(UNKNOWN_ERROR, "游戏信息不存在"));
                }
                vo.setPromotionPurposeContent(unit.getGameDto().getGameLink());
            }
        }
    }

    private void fillDefaultPromotionContentIfNecessary(CpcUnitDto unit, CreativeDetailVos vos) {
        if (PromotionPurposeType.ON_SHELF_GAME.getCode() == unit.getPromotionPurposeType()) {
            if (vos.getCreativeVos() != null && vos.getCreativeVos().size() > 0) {
                List<CreativeDetailVo> creativeDetails = vos.getCreativeVos();
                creativeDetails.forEach(detail -> {
                    if (org.apache.commons.lang3.StringUtils.isBlank(detail.getPromotion_purpose_content())) {
                        if (unit.getGameDto() == null || org.apache.commons.lang3.StringUtils.isBlank(unit.getGameDto().getGameLink())) {
                            throw new IllegalArgumentException(errorMsgBuild(UNKNOWN_ERROR, "游戏信息不存在"));
                        }
                        detail.setPromotion_purpose_content(unit.getGameDto().getGameLink());
                    }
                });
            }
        } else if (PromotionPurposeType.LIVE_ROOM.getCode() == unit.getPromotionPurposeType() ||
                PromotionPurposeType.LIVE_RESERVE.getCode() == unit.getPromotionPurposeType()) {

            setLiveRoomCoverIfNecessary(unit, vos);
        }
    }

    private void setLiveRoomCoverIfNecessary(CpcUnitDto unit, CreativeDetailVos vos) {
        List<CreativeDetailVo> creatives = vos.getCreativeVos();
        if (!CollectionUtils.isEmpty(creatives)) {

            creatives.forEach(creative -> {
                if (creative.getImages() == null) {
                    creative.setImages(Collections.singletonList(new CpcImageVo()));
                }
            });

            List<CpcImageVo> emptyImages = creatives.stream()
                    .map(CreativeDetailVo::getImages)
                    .flatMap(Collection::stream)
                    .filter(image -> (image.getId() == null || image.getId() == 0)
                            && (image.getImageInfo() == null || org.apache.commons.lang3.StringUtils.isBlank(image.getImageInfo().getMediaUrl())))
                    .collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(emptyImages)) {
                long liveRoomId;
                if (PromotionPurposeType.LIVE_ROOM.getCode() == unit.getPromotionPurposeType()) {
                    Integer subjectId = unit.getSubjectId();
                    Map<Integer, String> roomInfoMap = subjectService.querySubjectMaterialRel(Collections.singletonList(subjectId),
                            LauSubjectType.LIVE_ROOM);
                    liveRoomId = Long.parseLong(roomInfoMap.get(subjectId));
                } else {
                    LiveReservationInfoBo liveReservationInfoBo = liveReserveService.fetchUnitLiveReserveInfo(unit.getUnitId());
                    liveRoomId = liveReservationInfoBo.getRoomId();
                }

                Map<Integer, LiveBroadcastRoomInfo> roomInfoMap =
                        liveBroadcastHttpService.queryLiveBroadcastRoomInfoByIds(Collections.singletonList((int) liveRoomId));
                Assert.isTrue(!CollectionUtils.isEmpty(roomInfoMap)
                        && (roomInfoMap.get((int) liveRoomId)) != null, "直播间不存在");

                LiveBroadcastRoomInfo roomInfo = roomInfoMap.get((int) liveRoomId);
                String coverUrl = roomInfo.getCover();
                String md5 = downloadPicAndReturnMd5(coverUrl);
                emptyImages.forEach(image -> {
                    ImageInfoVo imageInfoVo = new ImageInfoVo();
                    imageInfoVo.setMediaUrl(coverUrl);
                    imageInfoVo.setMediaMd5(md5);
                    image.setImageInfo(imageInfoVo);
                });

            }
        }
    }

    private String downloadPicAndReturnMd5(String url) {
        Request request = new Request.Builder()
                .header(HttpHeaders.CONNECTION, "close")
                .url(url)
                .get()
                .build();
        try {
            try (Response response = client.newCall(request).execute()) {
                byte[] bytes = response.body().bytes();
                return DigestUtils.md5Hex(bytes);
            }

        } catch (Exception e) {
            log.error("下载图片{}发生异常:{}", url, ExceptionUtils.getStackTrace(e));
            throw new RuntimeException(e);
        }
    }

    public void openApiUploadValidator(@ApiIgnore Context context, MultipartFile multipartFile) {
        if (context.isOpenApi()) {

            String contentType = multipartFile.getContentType();
            Assert.isTrue(null != contentType && contentType.startsWith("image/"), errorMsgBuild(UPLOAD_FILE_ERROR, "请输入完整文件名后缀"));
            String contentTypeExtend = getFileExtend(contentType, "/");

            String fileExtend = getFileExtend(multipartFile.getOriginalFilename(), ".");
            Assert.isTrue(null != fileExtend, errorMsgBuild(UPLOAD_FILE_ERROR, "请输入完整文件名后缀"));
            Assert.isTrue(fileExtend.equalsIgnoreCase(contentTypeExtend), errorMsgBuild(UPLOAD_FILE_ERROR, "请保持文件名后缀和ContextType一致"));

            QueryCollageMediaDto dto = QueryCollageMediaDto.builder()
                    .accountIds(Collections.singletonList(context.getAccountId()))
                    .pageInfo(Page.valueOf(1, 1))
                    .filterNoMd5(true)
                    .build();
            PageResult<CollageMediaDto> mediaList = iSoaCollageMediaService.getMediaList(dto);
            Assert.isTrue(mediaList.getTotal() < 20000, errorMsgBuild(PARAM_ERROR, "该账户素材总数超过20000"));
        }
    }

    private String getFileExtend(String fileName, String string) {
        if (fileName == null) {
            return null;
        } else {
            int pos = fileName.lastIndexOf(string);
            return pos < 0 ? null : fileName.substring(pos + 1);
        }
    }

    /**
     * @param type
     * @param accountId
     * @return
     */
    public Integer getLimitValue(OpenAPILimitType type, LimitBsiType limitBsiType, Integer accountId) {
        //config get
        Map<Integer, Map<String, Map<String, Integer>>> config = gson.fromJson(limitConfigString, new TypeToken<Map<Integer, Map<String, Map<String, Integer>>>>() {
        }.getType());

        Map<String, Map<String, Integer>> configValue = config.get(accountId);
        if (null != configValue) {
            Map<String, Integer> bsiTypeConfigMap = configValue.get(type.getName());
            if (bsiTypeConfigMap != null) {
                return limitBsiType == LimitBsiType.DAY_LIMIT ? bsiTypeConfigMap.getOrDefault(LimitBsiType.DAY_LIMIT.getName(), 3000) : bsiTypeConfigMap.getOrDefault(LimitBsiType.ALL_LIMIT.getName(), 5000);
            }
        }
        return limitBsiType == LimitBsiType.DAY_LIMIT ? type.getDayLimitDefault() : type.getAllLimitDefault();
    }

    public void validateUnitSalesTypeAndOcpxTarget(int accountId,
                                                   List<Integer> osList,
                                                   Integer campaignId,
                                                   Integer unitId,
                                                   Integer gameStatus,
                                                   Integer unitPromotionPurposeType,
                                                   Long avid,
                                                   Integer promotionPurposeType,
                                                   Integer expectSalesType,
                                                   Integer expectOcpxTarget,
                                                   Integer expectDeepOcpxTarget) {
        if (!Utils.isPositive(expectOcpxTarget)) {
            Assert.isTrue(!Utils.isPositive(expectDeepOcpxTarget), "无浅层优化目标时，不支持深度优化目标");
            return;
        }
        OcpcTargetEnum expectEnum = OcpcTargetEnum.getByCode(expectOcpxTarget);
        Assert.isTrue(!OcpcTargetEnum.UNDEFINED.equals(expectEnum), "未知的优化目标:" + expectOcpxTarget);
        if (Utils.isPositive(expectDeepOcpxTarget)) {
            OcpcTargetEnum expectDeepEnum = OcpcTargetEnum.getByCode(expectDeepOcpxTarget);
            Assert.isTrue(!OcpcTargetEnum.UNDEFINED.equals(expectDeepEnum), "未知的深层优化目标:" + expectDeepOcpxTarget);
        }

        List<SalesTypeOptionVo> unitSalesTypeListWithOcpxTargets = unitOcpxTargetComponent.getUnitSalesTypeListWithOcpxTargets(accountId, true, osList,
                null, campaignId, unitId, gameStatus,
                unitPromotionPurposeType, avid, promotionPurposeType, null);
        unitSalesTypeListWithOcpxTargets = unitSalesTypeListWithOcpxTargets.stream()
                .filter(vo -> expectSalesType.equals(vo.getSalesType()))
                .collect(Collectors.toList());
        Assert.notEmpty(unitSalesTypeListWithOcpxTargets, "当前单元情况不支持出价类型:" +
                SalesType.getByCode(expectSalesType).name());
        List<Integer> availableOcpxTargets = unitSalesTypeListWithOcpxTargets.stream()
                .map(SalesTypeOptionVo::getTargets)
                .filter(org.apache.commons.collections.CollectionUtils::isNotEmpty)
                .flatMap(Collection::stream)
                .map(OcpxTargetVo::getId)
                .collect(Collectors.toList());
        Assert.isTrue(availableOcpxTargets.contains(expectOcpxTarget), "当前单元情况不支持优化目标:" +
                OcpcTargetEnum.getByCode(expectOcpxTarget).getDesc() +
                "请参照/open_api/launch/cpc/meta_data/resource/sales_type");
        if (!Utils.isPositive(expectDeepOcpxTarget)) {
            return;
        }
        List<Integer> availableDeepOcpxTargets = unitSalesTypeListWithOcpxTargets.stream()
                .map(SalesTypeOptionVo::getTargets)
                .filter(org.apache.commons.collections.CollectionUtils::isNotEmpty)
                .flatMap(Collection::stream)
                .filter(target -> target.getId().equals(expectOcpxTarget))
                .map(OcpxTargetVo::getDescendants)
                .filter(org.apache.commons.collections.CollectionUtils::isNotEmpty)
                .flatMap(Collection::stream)
                .map(OcpxTargetVo::getId)
                .collect(Collectors.toList());
        Assert.isTrue(availableDeepOcpxTargets.contains(expectDeepOcpxTarget), "当前单元情况不支持深度优化目标:" +
                OcpcTargetEnum.getByCode(expectDeepOcpxTarget).getDesc() +
                "请参照/open_api/launch/cpc/meta_data/resource/sales_type");
    }

}
