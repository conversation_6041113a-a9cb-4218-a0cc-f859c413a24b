package com.bilibili.adp.advertiser.task.job.bonus_fly;

import com.bilibili.adp.advertiser.helper.CatMonitorPointcut;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.launch.biz.flyPro.v2.FlySigningBonusService;
import com.bilibili.adp.launch.biz.flyPro.v2.IFlySigningBonusService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.sql.Timestamp;

/**
 * 签约金账号财务记录聚合任务
 */
@Component
@Slf4j
@JobHandler("AccountLogAggregateJob")
public class AccountLogAggregateJob extends IJobHandler {
    @Autowired
    private IFlySigningBonusService flySigningBonusService;

    @CatMonitorPointcut(transactionName = "AccountLogAggregateJob")
    @Override
    public ReturnT<String> execute(String s) throws Exception {
        Timestamp day = Utils.getYesteday();
        if (StringUtils.hasText(s)){
            day = Utils.getTimestamp(s, "yyyy-MM-dd");
        }
        flySigningBonusService.accountLogAggregateWithTime(Utils.getBeginOfDay(day), Utils.getEndOfDay(day));
        return ReturnT.SUCCESS;
    }
}
