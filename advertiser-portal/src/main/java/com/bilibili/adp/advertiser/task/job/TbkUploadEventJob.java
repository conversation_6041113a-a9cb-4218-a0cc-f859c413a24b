package com.bilibili.adp.advertiser.task.job;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.biz.services.taobao.TbkUploadService;
import com.bilibili.adp.cpc.biz.services.taobao.bos.TbkCreativeDealBo;
import com.bilibili.adp.cpc.biz.services.taobao.bos.TbkCreativeMissionInfoBo;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * @ClassName TbkUploadEventJob
 * <AUTHOR>
 * @Date 2023/9/18 10:52 下午
 * @Version 1.0
 **/
@Component
@JobHandler("TbkUploadEventJob")
@Slf4j
public class TbkUploadEventJob extends IJobHandler {

    private static final String TBK_BIND_EVENT_REFRESH_CREATIVE_REDIS_LOCK_KEY = "tbk:bind:account:event:refresh:creative";

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private TbkUploadService tbkUploadService;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        final RLock lock = redissonClient.getLock(TBK_BIND_EVENT_REFRESH_CREATIVE_REDIS_LOCK_KEY);
        boolean ok = false;
        try {
            ok = lock.tryLock(1, 15, TimeUnit.SECONDS);
            Assert.isTrue(ok, "目前已有其他定时任务TbkUploadEventJob执行中, 请耐心等候");
            Long id = 0L;
            List<TbkCreativeMissionInfoBo> currentRefreshMissionList = tbkUploadService.getCurrentRefreshMission(id);
            // 没有需要刷新上报的任务
            if (CollectionUtils.isEmpty(currentRefreshMissionList)) {
                return ReturnT.SUCCESS;
            }
            while (!CollectionUtils.isEmpty(currentRefreshMissionList)) {
                currentRefreshMissionList.forEach(this::refreshSingleMission);
                id = currentRefreshMissionList.get(currentRefreshMissionList.size() - 1).getId();
                currentRefreshMissionList = tbkUploadService.getCurrentRefreshMission(id);
            }
            return ReturnT.SUCCESS;
        }  catch (InterruptedException e) {
            log.error("TbkUploadEventJob 获取资源失败", e);
            throw new IllegalArgumentException("获取资源错误");
        } finally {
            if (ok && lock.isLocked()) {
                lock.unlock();
            }
        }
    }

    private void refreshSingleMission(TbkCreativeMissionInfoBo missionInfoBo) {
        if (Objects.isNull(missionInfoBo)) {
            return;
        }
        TbkCreativeDealBo dealBo = TbkCreativeDealBo.builder()
                .accountId(missionInfoBo.getAccountId())
                .eventId(missionInfoBo.getEventId())
                .startCreativeId(missionInfoBo.getStartCreativeId())
                .endCreativeId(missionInfoBo.getEndCreativeId())
                .build();
        Integer count = tbkUploadService.uploadAllCreativeByAccount(dealBo);
        log.info("TbkUploadEventJob refreshSingleMission current Count:{}", count);
    }
}
