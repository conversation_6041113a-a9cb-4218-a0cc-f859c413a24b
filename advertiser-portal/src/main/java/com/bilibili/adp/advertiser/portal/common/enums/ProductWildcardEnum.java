package com.bilibili.adp.advertiser.portal.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum ProductWildcardEnum {
    PID(0, "${pid}", "B站商品ID", ProductWildcardType.NO_URL),
    OUTER_ID(1, "${outerId}", "商家内部ID", ProductWildcardType.NO_URL),
    NAME(2, "${name}", "商品名称", ProductWildcardType.NO_URL),
    CATEGORY(3, "${category}", "一级分类", ProductWildcardType.NO_URL),
    SUB_CATEGORY(4, "${subCategory}", "二级分类", ProductWildcardType.NO_URL),
    THIRD_CATEGORY(5, "${thirdCategory}", "三级分类", ProductWildcardType.NO_URL),
    BRAND(6, "${brand}", "品牌", ProductWildcardType.NO_URL),
    SELLER_NAME(7, "${sellerName}", "店铺名称", ProductWildcardType.NO_URL),
    PRICE(8, "${price}", "现价", ProductWildcardType.NO_URL),
    VALUE(9, "${value}", "原价", ProductWildcardType.NO_URL),
    SAVING(10, "${saving}", "节省的钱", ProductWildcardType.NO_URL),
    LOC(11, "${loc}", "产品页面URL", ProductWildcardType.URL),
    SELLER_SITE_URL(12, "${sellerSiteUrl}", "商家站点URL", ProductWildcardType.URL),
    TITLE(13, "${title}", "描述", ProductWildcardType.NO_URL),
    IMAGE(14, "${image}", "主图", ProductWildcardType.IMG),
    CITY(15, "${city}", "city", ProductWildcardType.NO_URL),
    H_5_SKU_URL(16, "${h5skuUrl}", "h5skuUrl", ProductWildcardType.URL),
    RE_H_5_SKU_URL(17, "${reH5skuUrl}", "reH5skuUrl", ProductWildcardType.URL),
    RE_H_5_SKU_SEARCH_URL(18, "${reH5skuSearchUrl}", "reH5skuSearchUrl", ProductWildcardType.URL),
    H_5_SKU_SEARCH_URL(19, "${h5skuSearchUrl}", "h5skuSearchUrl", ProductWildcardType.URL),
    DEEPLINK_SKU_URL(20, "${deeplinkskuUrl}", "deeplinkskuUrl", ProductWildcardType.URL),
    RE_DEEPLINK_SKU_URL(21, "${reDeeplinkskuUrl}", "reDeeplinkskuUrl", ProductWildcardType.URL),
    RE_DEEPLINK_SKU_SEARCH_URL(22, "${reDeeplinkskuSearchUrl}", "reDeeplinkskuSearchUrl", ProductWildcardType.URL),
    DEEPLINK_SKU_SEARCH_URL(23, "${deeplinkskuSearchUrl}", "deeplinkskuSearchUrl", ProductWildcardType.URL),
    MODEL(24, "${model}", "型号", ProductWildcardType.NO_URL),
    SERVICES(25, "${services}", "服务保障与特色", ProductWildcardType.NO_URL),
    TAGS(26, "${tags}", "标签", ProductWildcardType.NO_URL),
    PROMOTION(27, "${promotion}", "促销活动", ProductWildcardType.NO_URL),
    TARGET_REGION(28, "${targetRegion}", "推广地域", ProductWildcardType.NO_URL),
    DEEP_LINK_URL(29, "${deepLinkUrl}", "唤起地址", ProductWildcardType.URL),
    TARGET_URL(30, "${targetUrl}", "创意跳转的目标网址", ProductWildcardType.URL),
    IP(31, "${ip}", "", ProductWildcardType.NO_URL),
    MONITOR(32, "${monitor}", "", ProductWildcardType.URL),
    BLIND_BOX(33, "${blindBox}", "", ProductWildcardType.NO_URL),
    BRIEF(34, "${brief}", "", ProductWildcardType.NO_URL),
    WANT(35, "${want}", "", ProductWildcardType.NO_URL),
    EN_BRAND(36, "${enbrand}", "", ProductWildcardType.NO_URL),
    COMMENT_CONTENT(37, "${commentContent}", "", ProductWildcardType.NO_URL),
    ORIGIN_NAME(38, "${originName}", "", ProductWildcardType.NO_URL),
    ALIAS(39, "${alias}", "", ProductWildcardType.NO_URL),
    ACTOR_NAME(40, "${actorName}", "", ProductWildcardType.NO_URL),
    AUTHOR_NAME(41, "${authorName}", "", ProductWildcardType.NO_URL),
    LOC_COMIC(42, "${locComic}", "", ProductWildcardType.URL),
    AREA_NAME(43, "${areaName}", "", ProductWildcardType.NO_URL),
    SCORE_VALUE(44, "${scoreValue}", "", ProductWildcardType.NO_URL),
    INTERACTIVE(45, "${interactive}", "", ProductWildcardType.NO_URL),
    READING_INDEX(46, "${readingIndex}", "", ProductWildcardType.NO_URL),
    FORM(47, "${form}", "", ProductWildcardType.NO_URL),
    IMAGE_1(48, "${image1}", "子图1", ProductWildcardType.IMG),
    IMAGE_2(49, "${image2}", "子图2", ProductWildcardType.IMG),
    IMAGE_3(50, "${image3}", "子图3", ProductWildcardType.IMG),
    IMAGE_4(51, "${image4}", "子图4", ProductWildcardType.IMG),
    UNIVERSAL_URL(52, "${universalUrl}", "universalUrl", ProductWildcardType.URL),
    RE_UNIVERSAL_URL(53, "${reUniversalUrl}", "reUniversalUrl", ProductWildcardType.URL),
    IMG_INDEX_1(54, "${img_index_1}", "img_index_1", ProductWildcardType.IMG),
    IMG_INDEX_2(55, "${img_index_2}", "img_index_2", ProductWildcardType.IMG),
    IMG_INDEX_3(56, "${img_index_3}", "img_index_3", ProductWildcardType.IMG),
    IMG_INDEX_4(57, "${img_index_4}", "img_index_4", ProductWildcardType.IMG),
    IMG_INDEX_5(58, "${img_index_5}", "img_index_5", ProductWildcardType.IMG),
    IMG_INDEX_6(59, "${img_index_6}", "img_index_6", ProductWildcardType.IMG),
    IMG_INDEX_7(60, "${img_index_7}", "img_index_7", ProductWildcardType.IMG);

    private final Integer code;
    private final String key;
    private final String desc;
    private final ProductWildcardType type;

    public static final Set<String> URL_KEYS = Arrays.stream(ProductWildcardEnum.values()).filter(e -> e.getType().equals(ProductWildcardType.URL)).map(ProductWildcardEnum::getKey).collect(Collectors.toSet());
    public static final Set<String> NO_URL_KEYS = Arrays.stream(ProductWildcardEnum.values()).filter(e -> e.getType().equals(ProductWildcardType.NO_URL)).map(ProductWildcardEnum::getKey).collect(Collectors.toSet());
    public static final Set<String> IMAGE_KEYS = Arrays.stream(ProductWildcardEnum.values()).filter(e -> e.getType().equals(ProductWildcardType.IMG)).map(ProductWildcardEnum::getKey).collect(Collectors.toSet());

    public static ProductWildcardEnum getByCode(Integer code) {
        for (ProductWildcardEnum status : values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("unknown code ProductWildcardEnum " + code);
    }

    public static ProductWildcardEnum getByCodeWithoutValidation(Integer code) {
        for (ProductWildcardEnum status : values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        return null;
    }
}