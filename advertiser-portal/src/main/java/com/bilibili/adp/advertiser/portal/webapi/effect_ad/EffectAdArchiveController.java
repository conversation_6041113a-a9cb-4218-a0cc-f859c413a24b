package com.bilibili.adp.advertiser.portal.webapi.effect_ad;

import com.alibaba.fastjson.JSON;
import com.bapis.ad.account.crm.acc.AccountBase;
import com.bapis.ad.archive.ListCmArchiveReq;
import com.bapis.ad.archive.ListCmArchiveResp;
import com.bapis.ad.audit.NativeBizType;
import com.bapis.ad.cmc.cidgoods.CidGoodsQueryReq;
import com.bapis.ad.cmc.cidgoods.CidGoodsQueryResp;
import com.bapis.ad.cmc.up.ReserveInfo;
import com.bapis.ad.component.*;
import com.bapis.ad.pandora.core.batch.BatchOperationType;
import com.bapis.ad.scv.component_group.ComponentGroup;
import com.bapis.ad.scv.component_group.QueryComponentGroupPageListReq;
import com.bapis.ad.scv.component_group.QueryComponentGroupRep;
import com.bapis.ad.scv.component_group.SaveComponentGroupReq;
import com.bilibili.ad.manager.common.enums.ValidEnum;
import com.bilibili.adp.account.dto.AccountBaseDto;
import com.bilibili.adp.account.service.QueryAccountService;
import com.bilibili.adp.advertiser.portal.common.AdvancedContext;
import com.bilibili.adp.advertiser.portal.common.AdvancedContext.ContextSessionType;
import com.bilibili.adp.advertiser.portal.common.BasicController;
import com.bilibili.adp.advertiser.portal.common.Context;
import com.bilibili.adp.advertiser.portal.common.SelectOptionVo;
import com.bilibili.adp.advertiser.portal.service.material.aggregation.ProductInfoQueryService;
import com.bilibili.adp.advertiser.portal.service.session.agent.AllowAgentLogin;
import com.bilibili.adp.advertiser.portal.webapi.effect_ad.account.converter.InfoConverter;
import com.bilibili.adp.advertiser.portal.webapi.effect_ad.account.vo.InfoVo;
import com.bilibili.adp.advertiser.portal.webapi.effect_ad.cm_archive.CmArchiveBo;
import com.bilibili.adp.advertiser.portal.webapi.effect_ad.cm_archive.CmArchiveConverter;
import com.bilibili.adp.advertiser.portal.webapi.effect_ad.cm_archive.CmArchiveV2Bo;
import com.bilibili.adp.advertiser.portal.webapi.effect_ad.converter.ArchiveConversionComponentConverter;
import com.bilibili.adp.advertiser.portal.webapi.effect_ad.converter.ArchiveConverter;
import com.bilibili.adp.advertiser.portal.webapi.effect_ad.converter.ArchiveInteractConverter;
import com.bilibili.adp.advertiser.portal.webapi.effect_ad.converter.GoodsConvertor;
import com.bilibili.adp.advertiser.portal.webapi.effect_ad.vo.*;
import com.bilibili.adp.advertiser.portal.webapi.effect_ad.vo.anchor.LauComponentGroupLabelVo;
import com.bilibili.adp.advertiser.portal.webapi.effect_ad.vo.goods.GoodsVo;
import com.bilibili.adp.advertiser.portal.webapi.flyPro.v2.FlyProCampaignController;
import com.bilibili.adp.advertiser.portal.webapi.launch.cpc.CpcArchiveController;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.enums.AuditStatus;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.Page;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.biz.bos.account.InfoBo;
import com.bilibili.adp.cpc.biz.bos.anchor.AnchorPreviewBo;
import com.bilibili.adp.cpc.biz.bos.app.AppPackageBo;
import com.bilibili.adp.cpc.biz.bos.archive.*;
import com.bilibili.adp.cpc.biz.services.account.EffectAdMidService;
import com.bilibili.adp.cpc.biz.services.account.bos.EffectAdMidQueryBo;
import com.bilibili.adp.cpc.biz.services.anchor.ArchiveAnchorQuerier;
import com.bilibili.adp.cpc.biz.services.archive.*;
import com.bilibili.adp.cpc.biz.services.archive.bos.CommentComponentBo;
import com.bilibili.adp.cpc.biz.services.archive.bos.CommentComponentExtraJsonBo;
import com.bilibili.adp.cpc.biz.services.creative.AdpCpcCreativeArcInteractStateService;
import com.bilibili.adp.cpc.biz.services.dynamic.BiliDynamicService;
import com.bilibili.adp.cpc.biz.services.dynamic.bos.DynamicBo;
import com.bilibili.adp.cpc.biz.services.game.LaunchBiliMiniGameService;
import com.bilibili.adp.cpc.biz.services.game.bos.BiliMiniGameBo;
import com.bilibili.adp.cpc.biz.services.goods.GoodsManagerService;
import com.bilibili.adp.cpc.biz.services.goods.SanlianCreativeGoodsContentService;
import com.bilibili.adp.cpc.biz.services.live.AdpCpcLiveReserveService;
import com.bilibili.adp.cpc.core.constants.IsDeleted;
import com.bilibili.adp.cpc.dao.querydsl.pos.LauNativeArchivePo;
import com.bilibili.adp.cpc.enums.ClueTypeEnum;
import com.bilibili.adp.cpc.enums.account.ContentType;
import com.bilibili.adp.cpc.enums.archive.ArchiveSizeTypeEnum;
import com.bilibili.adp.cpc.enums.natives.NativeArchiveStatusEnum;
import com.bilibili.adp.cpc.proxy.CpmScvProxy;
import com.bilibili.adp.cpc.proxy.CpmTavernPlatformProxy;
import com.bilibili.adp.cpc.repo.LauNativeArchiveRepo;
import com.bilibili.adp.cpc.utils.CursorPage;
import com.bilibili.adp.cpc.utils.ListIdUtils;
import com.bilibili.adp.cpc.utils.Response;
import com.bilibili.adp.launch.api.minigame.dto.LauMiniGameDto;
import com.bilibili.adp.launch.api.minigame.dto.QueryLauMiniGameDto;
import com.bilibili.adp.launch.api.service.ILauMiniGameService;
import com.bilibili.adp.v6.account.AccountV6Service;
import com.bilibili.adp.web.framework.core.Pagination;
import com.bilibili.bvid.BVIDUtils;
import com.bilibili.crm.platform.common.IsValid;
import com.bilibili.mgk.platform.api.form.dto.MgkFormDto;
import com.bilibili.mgk.platform.api.form.dto.QueryFormParamDto;
import com.bilibili.mgk.platform.api.form.soa.ISoaMgkFormService;
import com.bilibili.mgk.platform.api.landing_page.dto.MgkLandingPageDto;
import com.bilibili.mgk.platform.api.wechat.dto.WechatPackageDto;
import com.bilibili.mgk.platform.api.wechat.dto.WechatPackageListDto;
import com.bilibili.mgk.platform.api.wechat.dto.WechatPackageQueryDto;
import com.bilibili.mgk.platform.api.wechat.soa.ISoaMgkWechatPackageService;
import com.bilibili.mgk.platform.common.LandingPageStatusEnum;
import com.bilibili.sycpb.acc.api.dict.common.YesNoEnum;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.bapis.ad.crm.account.ListAccountIdsByTypeReq.Type.SAME_GROUP_SAME_PRODUCT_VALUE;

/**
 * 【三连推广】稿件相关逻辑
 * 1.稿件互动状态相关(历史逻辑迁移) 从CpcArchiveController复制而来
 * 2.单元选稿件相关(历史逻辑迁移) 注意：launchVideoType、isBusinessVideo 均为历史逻辑，参考com.bilibili.adp.advertiser.portal.webapi.flyPro.v2.FlyProCampaignController#getUpVideos
 * 3.稿件评论组件相关(三连推广新增)
 * 4.mapi已接入bilibili账号视频视频 商单稿件 手动绑定稿件接口 相关vo改动需慎重
 *
 * <AUTHOR>
 * @see CpcArchiveController
 * @see FlyProCampaignController
 */
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping(value = "/web_api/v1/effect_ad/archive")
@Api(value = "/effect_ad/archive")
@AllowAgentLogin
public class EffectAdArchiveController extends BasicController {
    private static final int DATA_SOURCE_TYPE_SELF = 1;
    private static final int DATA_SOURCE_TYPE_SHARE = 2;
    private static final Integer ARCHIVE_OR_DYNAMIC_ABNORMAL = 1;
    private final EffectAdMidService effectAdInfoService;
    private final AdpCpcCreativeArcInteractStateService adpCpcCreativeArcInteractStateService;
    private final ArchiveService archiveService;
    private final ArchiveConversionComponentService archiveConversionComponentService;
    private final ArchiveEscapeComponent archiveEscapeComponent;
    private final ISoaMgkFormService soaMgkFormService;
    private final ISoaMgkWechatPackageService soaMgkWechatPackageService;
    private final ILauMiniGameService lauMiniGameService;
    private final BiliDynamicService biliDynamicService;
    private final QueryAccountService queryAccountService;
    private final AdpCpcLiveReserveService adpCpcLiveReserveService;
    private final LauNativeArchiveRepo lauNativeArchiveRepo;
    private final CpmScvProxy cpmScvProxy;
    private final LaunchBiliMiniGameService launchBiliMiniGameService;
    @Resource(name = "cpcOttService")
    private final OttService ottService;
    private final GoodsManagerService goodsManagerService;
    private final ArchiveAnchorQuerier archiveAnchorQuerier;
    private final AccountV6Service accountV6Service;

    @Resource
    private CpmTavernPlatformProxy cpmTavernPlatformProxy;

    @Value("#{'${mapi.pdd.appkey:318B37503278467CA091AC54B658010C}'.split(',')}")
    private List<String> mapiPddAppKeyList;
    @Resource
    private NewGoodsArchiveQuerier newGoodsArchiveQuerier;


    @Autowired
    private SanlianCreativeGoodsContentService sanlianCreativeGoodsContentService;

    @Resource
    private ProductInfoQueryService productInfoQueryService;

    private static void generateAppPlatformTypes(CommentComponentBo component, ArchiveCommentConversionComponentDetailVo vo) {
        List<Integer> app_platform_types = new ArrayList<>(); // 前端老师要求
        if (Objects.equals(ComponentType.APP_VALUE, component.getComponentType()) || Objects.equals(ComponentType.GAME_VALUE, component.getComponentType())) {
            // android 和 ios 都有
            if (Utils.isPositive(component.getAndroidAppPackageId()) && Utils.isPositive(component.getIosAppPackageId())) {
                app_platform_types.add(1);
                app_platform_types.add(2);
            }
            // 仅仅 android
            else if (Utils.isPositive(component.getAndroidAppPackageId()) && !Utils.isPositive(component.getIosAppPackageId())) {
                app_platform_types.add(1);
            }
            // 仅仅 ios
            else if (!Utils.isPositive(component.getAndroidAppPackageId()) && Utils.isPositive(component.getIosAppPackageId())) {
                app_platform_types.add(2);
            }
            vo.setApp_platform_types(app_platform_types);
        }
    }

    @ApiOperation("获取组件分组列表")
    @GetMapping("/group/list")
    public Response<Pagination<List<LauComponentGroupLabelVo>>> groupList(@ApiIgnore Context context,
                                                                          @RequestParam(value = "component_type", required = false, defaultValue = "1") Integer componentType) {
        Integer accountId = context.getAccountId();

        QueryComponentGroupRep queryComponentGroupRep = cpmScvProxy.queryComponentGroupTotalList(QueryComponentGroupPageListReq.newBuilder().setAccountId(accountId)
                .setComponentType(componentType).build());
        List<ComponentGroup> dataList = queryComponentGroupRep.getDataList();
        if (CollectionUtils.isEmpty(dataList)) {
            return Response.ok(new Pagination<>(0, 0, null));
        }
        int total = queryComponentGroupRep.getTotal();

        return Response.ok(new Pagination<>(1, total, ArchiveConversionComponentConverter.MAPPER.fromRpcBos(dataList)));
    }

    public void validComponentGroup(LauComponentGroupLabelVo componentGroupLabelVo) {
        Assert.isTrue(componentGroupLabelVo.getComponentType() > 0, "组件分组类型不可为空");
        Assert.isTrue(StringUtils.isNotEmpty(componentGroupLabelVo.getGroupName()), "分组名称不可为空");
    }

    @ApiOperation("创建分组")
    @PostMapping("/group/create")
    public Response<String> groupCreate(@ApiIgnore Context context, @RequestBody LauComponentGroupLabelVo componentGroupLabelVo) {
        validComponentGroup(componentGroupLabelVo);

        Integer accountId = context.getAccountId();
        SaveComponentGroupReq saveComponentGroupReq = SaveComponentGroupReq.newBuilder()
                .setAccountId(accountId)
                .setComponentType(componentGroupLabelVo.getComponentType())
                .setGroupName(componentGroupLabelVo.getGroupName())
                .build();
        cpmScvProxy.createComponentGroup(saveComponentGroupReq);

        return Response.ok();
    }

    @ApiOperation("编辑分组")
    @PostMapping("/group/update")
    public Response<String> groupUpdate(@ApiIgnore Context context, @RequestBody LauComponentGroupLabelVo componentGroupLabelVo) {
        validComponentGroup(componentGroupLabelVo);

        Integer accountId = context.getAccountId();
        SaveComponentGroupReq saveComponentGroupReq = SaveComponentGroupReq.newBuilder()
                .setId(componentGroupLabelVo.getId())
                .setAccountId(accountId)
                .setComponentType(componentGroupLabelVo.getComponentType())
                .setGroupName(componentGroupLabelVo.getGroupName())
                .build();
        cpmScvProxy.updateComponentGroup(saveComponentGroupReq);

        return Response.ok();
    }

    @ApiOperation("删除分组")
    @PostMapping("/group/delete")
    public Response<String> groupDelete(@ApiIgnore Context context, @RequestBody LauComponentGroupLabelVo componentGroupLabelVo) {
        validComponentGroup(componentGroupLabelVo);

        Integer accountId = context.getAccountId();
        SaveComponentGroupReq saveComponentGroupReq = SaveComponentGroupReq.newBuilder()
                .setAccountId(accountId)
                .setId(componentGroupLabelVo.getId())
                .setComponentType(componentGroupLabelVo.getComponentType())
                .build();
        cpmScvProxy.deleteComponentGroup(saveComponentGroupReq);

        return Response.ok();
    }


    @AllowAgentLogin
    @ApiOperation("获取商业稿件列表")
    @GetMapping("/list_cm_archive_v2")
    public Response<Pagination<List<CmArchiveV2Bo>>> listCmArchivesV2(
            @ApiIgnore AdvancedContext context,
            @RequestParam(value = "page_no", required = false, defaultValue = "1") Integer pageNo,
            @RequestParam(value = "page_size", required = false, defaultValue = "10") Integer pageSize,
            @RequestParam(value = "duration_begin", required = false) Integer minDuration,
            @RequestParam(value = "duration_end", required = false) Integer maxDuration,
            @RequestParam(value = "ratio_width", required = false) Integer ratioWidth,
            @RequestParam(value = "ratio_height", required = false) Integer ratioHeight,
            @RequestParam(value = "audit_pass", required = false, defaultValue = "false") boolean auditPass,
            @RequestParam(value = "name", required = false) String name,
            @RequestParam(value = "archive_mode", required = false, defaultValue = "0") Integer archiveMode,
            @RequestParam(value = "size_type", required = false) Integer sizeType,
            @RequestParam(value = "mid", required = false) Long mid,
            @RequestParam(value = "data_source_type", required = false, defaultValue = "0") Integer dataSourceType,
            @RequestParam(value = "is_deleted", required = false, defaultValue = "0") Integer isDeleted,
            @RequestParam(value = "ctime_from", required = false) Long ctimeFrom,
            @RequestParam(value = "ctime_to", required = false) Long ctimeTo,
            @RequestParam(value = "order_by", required = false) String orderBy,
            @RequestParam(value = "md5s", required = false) List<String> md5s,
            @RequestParam(value = "aid", required = false) Long avid,
            @RequestParam(value = "bvid", required = false) String bvid,
            @RequestParam(value = "query_is_goods", required = false, defaultValue = "false") Boolean queryIsGoods,
            @RequestParam(value = "search_word", required = false) String searchWord
    ) {
        if (StringUtils.isNotEmpty(bvid)) {
            avid = BVIDUtils.bvToAv(bvid);
        }

        final ListCmArchiveReq.Builder reqBuilder = ListCmArchiveReq.newBuilder();

        if (context.getSessionType() == ContextSessionType.account) {
            final Integer accountId = context.getAccountId(true);
            Assert.isTrue(Utils.isPositive(accountId), "账号id不能为空");
            reqBuilder.addAccountIdSet(accountId);
        } else if (context.getSessionType() == ContextSessionType.agent) {

            List<Integer> accountIds = context.getAccountIds().get();
            reqBuilder.addAllAccountIdsForAggregationView(accountIds);
        } else {
            throw new IllegalArgumentException("非法登录状态");
        }

        reqBuilder.setArchiveModeValue(archiveMode)
                .addIsAuditPassedSet(auditPass)
                .setRatioWidth(Optional.ofNullable(ratioWidth).orElse(0))
                .setRatioHeight(Optional.ofNullable(ratioHeight).orElse(0))
                .setMinDuration(Optional.ofNullable(minDuration).orElse(0))
                .setMaxDuration(Optional.ofNullable(maxDuration).orElse(0))
                .setMinCtime(Optional.ofNullable(ctimeFrom).orElse(0L))
                .setMaxCtime(Optional.ofNullable(ctimeTo).orElse(0L))
                .setPageNo(pageNo)
                .setPageSize(pageSize)
                .setOrderBy(Optional.ofNullable(orderBy).orElse(""))
                .setName(Optional.ofNullable(name).orElse(""));
        if (Objects.nonNull(mid)) {
            reqBuilder.addMidSet(mid);
        }
        if (Objects.nonNull(avid)) {
            reqBuilder.addAvIdSet(avid);
        }
        if (!CollectionUtils.isEmpty(md5s)) {
            reqBuilder.addAllVideoMd5Set(md5s);
        }

        // 1=本地上传 2=账户共享 0或空=全部
        if (Objects.equals(dataSourceType, DATA_SOURCE_TYPE_SELF)) {
            reqBuilder.addIsSharedSet(false);
        } else if (Objects.equals(dataSourceType, DATA_SOURCE_TYPE_SHARE)) {
            reqBuilder.addIsSharedSet(true);
        }
        if (Objects.equals(isDeleted, IsDeleted.VALID)) {
            reqBuilder.addIsDeletedSet(false);
        } else {
            reqBuilder.addIsDeletedSet(true);
        }

        if (Utils.isPositive(sizeType)) {
            ArchiveSizeTypeEnum archiveSizeTypeEnum = ArchiveSizeTypeEnum.getByCode(sizeType);
            reqBuilder.setRatioWidth(archiveSizeTypeEnum.getRatioWidth());
            reqBuilder.setRatioHeight(archiveSizeTypeEnum.getRatioHeight());
        }
        if (StringUtils.isNotEmpty(searchWord)) {
            reqBuilder.setMaterialSearchWord(searchWord);
        }

        final ListCmArchiveResp resp = cpmScvProxy.listCmArchive(reqBuilder.build());

        final Map<Long, Integer> arcGoodsMap;
        final List<Long> avids = resp.getCmArchiveSetList()
                .stream()
                .map(x -> x.getUgc().getAvid())
                .collect(Collectors.toList());
        if (queryIsGoods) {
            arcGoodsMap = newGoodsArchiveQuerier.queryArchiveTaskGoods2(avids);
        } else {
            arcGoodsMap = Collections.emptyMap();
        }

        List<ReserveInfo> archiveLiveReserveInfo = adpCpcLiveReserveService.getArchiveLiveReserveInfo(avids);
        Map<Long, ReserveInfo> aid2ReserveInfoMap = archiveLiveReserveInfo.stream().collect(Collectors.toMap(ReserveInfo::getAvid, Function.identity(), (v1, v2) -> v1));

        final List<CmArchiveV2Bo> bos = resp.getCmArchiveSetList()
                .stream()
                .map(x -> {
                    final Integer isGoodsArchive = arcGoodsMap.get(x.getUgc().getAvid());
                    ReserveInfo reserveInfo = aid2ReserveInfoMap.get(x.getUgc().getAvid());

                    return CmArchiveConverter.MAPPER.toCmArchiveV2Bo(x, Objects.isNull(isGoodsArchive) ? null : isGoodsArchive > 0, reserveInfo);
                }).collect(Collectors.toList());
        if ("ctime desc".equals(orderBy) || "pubTime desc".equals(orderBy)) {
            Comparator<CmArchiveV2Bo> compare = (bo1, bo2) -> (int) (bo1.getAudit().getPubTime() - bo2.getAudit().getPubTime());
            bos.sort(compare.reversed());
        } else if ("ctime asc".equals(orderBy) || "pubTime asc".equals(orderBy)) {
            bos.sort(Comparator.comparing(bo -> bo.getAudit().getPubTime()));
        }

        //根据原生状态排序
        if (StringUtils.isBlank(orderBy)) {
            List<LauNativeArchivePo> lauNativeArchivePos = lauNativeArchiveRepo.queryNativeArchive(avids, NativeBizType.SANLIAN_VALUE, 1);
            Map<Long, Integer> archiveAuditMap = lauNativeArchivePos.stream().collect(Collectors.toMap(LauNativeArchivePo::getAvid, LauNativeArchivePo::getAuditStatus, (t1, t2) -> t1));
            bos.forEach(cmArchiveV2Bo -> {
                Long archiveAvid = Long.parseLong(cmArchiveV2Bo.getUgc().getAvid());
                Integer status = archiveAuditMap.get(archiveAvid);
                if (Objects.isNull(status)) {
                    cmArchiveV2Bo.setNativeStatus(NativeArchiveStatusEnum.NOT_NATIVE.getCode());
                    cmArchiveV2Bo.setNativeStatusDesc(NativeArchiveStatusEnum.NOT_NATIVE.getShowText());
                } else if (Objects.equals(status, com.bilibili.adp.common.enums.AuditStatus.ACCEPT.getCode())) {
                    cmArchiveV2Bo.setNativeStatus(NativeArchiveStatusEnum.NATIVE_PASS.getCode());
                    cmArchiveV2Bo.setNativeStatusDesc(NativeArchiveStatusEnum.NATIVE_PASS.getShowText());
                } else if (Objects.equals(status, AuditStatus.REJECT.getCode())) {
                    cmArchiveV2Bo.setNativeStatus(NativeArchiveStatusEnum.NATIVE_REJECT.getCode());
                    cmArchiveV2Bo.setNativeStatusDesc(NativeArchiveStatusEnum.NATIVE_REJECT.getShowText());
                }
            });
        }
        bos.sort(Comparator.comparing(cmArchiveV2Bo -> Objects.isNull(cmArchiveV2Bo.getNativeStatus()) ? Integer.MAX_VALUE : cmArchiveV2Bo.getNativeStatus()));


        processCmArchiveHasCommentComponent(bos, avids);

        if (context.getSessionType() == ContextSessionType.agent) {
            productInfoQueryService.fillingProductInfo4AggregationView(bos);
        }

        return Response.ok(new Pagination<>(pageNo, resp.getTotal(), bos));
    }

    @ApiOperation("获取商业稿件列表")
    @GetMapping("/list_cm_archive")
    public Response<Pagination<List<CmArchiveBo>>> listCmArchives(
            Context context,
            @RequestParam(value = "page_no", required = false, defaultValue = "1") Integer pageNo,
            @RequestParam(value = "page_size", required = false, defaultValue = "10") Integer pageSize,
            @RequestParam(value = "duration_begin", required = false) Integer minDuration,
            @RequestParam(value = "duration_end", required = false) Integer maxDuration,
            @RequestParam(value = "ratio_width", required = false) Integer ratioWidth,
            @RequestParam(value = "ratio_height", required = false) Integer ratioHeight,
            @RequestParam(value = "audit_pass", required = false, defaultValue = "false") boolean auditPass,
            @RequestParam(value = "name", required = false) String name,
            @RequestParam(value = "archive_mode", required = false, defaultValue = "0") Integer archiveMode,
            @RequestParam(value = "mid", required = false) Long mid,
            @RequestParam(value = "data_source_type", required = false, defaultValue = "0") Integer dataSourceType,
            @RequestParam(value = "is_deleted", required = false, defaultValue = "0") Integer isDeleted,
            @RequestParam(value = "ctime_from", required = false) Long ctimeFrom,
            @RequestParam(value = "ctime_to", required = false) Long ctimeTo,
            @RequestParam(value = "md5s", required = false) List<String> md5s,
            @RequestParam(value = "aid", required = false) Long avid,
            @RequestParam(value = "bvid", required = false) String bvid
    ) {
        if (StringUtils.isNotEmpty(bvid)) {
            avid = BVIDUtils.bvToAv(bvid);
        }

        final Integer accountId = context.getAccountId();
        Assert.isTrue(Utils.isPositive(accountId), "账号id不能为空");
        final ListCmArchiveReq.Builder reqBuilder = ListCmArchiveReq.newBuilder()
                .setArchiveModeValue(archiveMode)
                .addAccountIdSet(accountId)
                .addIsAuditPassedSet(auditPass)
                .setRatioWidth(Optional.ofNullable(ratioWidth).orElse(0))
                .setRatioHeight(Optional.ofNullable(ratioHeight).orElse(0))
                .setMinDuration(Optional.ofNullable(minDuration).orElse(0))
                .setMaxDuration(Optional.ofNullable(maxDuration).orElse(0))
                .setMinCtime(Optional.ofNullable(ctimeFrom).orElse(0L))
                .setMaxCtime(Optional.ofNullable(ctimeTo).orElse(0L))
                .setPageNo(pageNo)
                .setPageSize(pageSize)
                .setName(Optional.ofNullable(name).orElse(""));
        if (Objects.nonNull(avid)) {
            reqBuilder.addAvIdSet(avid);
        }
        if (Objects.nonNull(mid)) {
            reqBuilder.addMidSet(mid);
        }
        if (!CollectionUtils.isEmpty(md5s)) {
            reqBuilder.addAllVideoMd5Set(md5s);
        }
        if (Objects.equals(dataSourceType, DATA_SOURCE_TYPE_SELF)) {
            reqBuilder.addIsSharedSet(false);
        } else if (Objects.equals(dataSourceType, DATA_SOURCE_TYPE_SHARE)) {
            reqBuilder.addIsSharedSet(true);
        }
        if (Objects.equals(isDeleted, IsDeleted.VALID)) {
            reqBuilder.addIsDeletedSet(false);
        } else {
            reqBuilder.addIsDeletedSet(true);
        }
        final ListCmArchiveResp resp = cpmScvProxy.listCmArchive(reqBuilder.build());
        final List<CmArchiveBo> bos = resp.getCmArchiveSetList()
                .stream()
                .map(CmArchiveConverter.MAPPER::toCmArchiveBo)
                .collect(Collectors.toList());
        return Response.ok(new Pagination<>(pageNo, resp.getTotal(), bos));
    }

    @ApiOperation(value = "获取当前账户下可选的mid列表")
    @GetMapping(value = "/infos")
    public Response<List<InfoVo>> infos(@ApiIgnore Context context,
                                        @ApiParam("upload") @RequestParam(value = "is_support_upload", required = false) Integer isSupportUpload) {
        List<InfoBo> infos;
        if (Utils.isPositive(isSupportUpload)) {
            infos = archiveService.uploadInfo(context.getAccountId());
        } else {
            EffectAdMidQueryBo queryBo = EffectAdMidQueryBo.builder()
                    .accountId(context.getAccountId())
                    .contentType(ContentType.Archive)
                    .page(1)
                    .build();
            infos = effectAdInfoService.infos(queryBo);
        }
        return Response.ok(infos
                .stream()
                .map(InfoConverter.MAPPER::infoBo2InfoVo)
                .collect(Collectors.toList()));
    }

    @ApiOperation(value = "根据mid获取bilibili账号稿件")
    @GetMapping(value = "/bilibili_account")
    public Response<Pagination<List<ArchiveVo>>> bilibiliAccountArcs(@ApiIgnore Context context,
                                                                     @ApiParam("mid") @RequestParam(value = "mid") Long mid,
                                                                     @ApiParam("aid") @RequestParam(value = "aid", required = false) Long aid,
                                                                     @ApiParam("bvid") @RequestParam(value = "bvid", required = false) String bvid,
                                                                     @ApiParam("关键字") @RequestParam(value = "keyword", required = false) String keyword,
                                                                     @ApiParam("版本") @RequestParam(value = "adp_version", required = false, defaultValue = "5") int adpVersion,
                                                                     @ApiParam("page") @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
                                                                     @ApiParam("size") @RequestParam(value = "size", required = false, defaultValue = "10") Integer size) {
        boolean hasCorp = archiveService.checkAccountMidCorp(context.getAccountId(), null, mid);
        Assert.isTrue(hasCorp, "当前mid未授权,不可查询稿件信息");
        Assert.isTrue(size <= 100, "单次分页大小最大为100");
        PageResult<ArchiveBo> pageResult = archiveService.getBilibiliAccountArcs(mid, aid, bvid, keyword, page, size);
        if (CollectionUtils.isEmpty(pageResult.getRecords())) {
            return Response.ok(new Pagination<>(page, pageResult.getTotal(), Collections.emptyList()));
        }
        archiveService.getBilibiliAccountArcsPostHandler(pageResult, mid, context.getAccountId(), adpVersion);

        return Response.ok(new Pagination<>(page, pageResult.getTotal(), pageResult.getRecords()
                .stream()
                .map(ArchiveConverter.MAPPER::bo2Vo)
                .collect(Collectors.toList())));
    }

    @ApiOperation(value = "短视频引流情况下根据mid获取bilibili账号稿件")
    @GetMapping(value = "/arc_drainage")
    public Response<Pagination<List<ArchiveV2Vo>>> liveDrainage(@ApiParam("mid") @RequestParam(value = "mid") Long mid,
                                                                @ApiParam("aid") @RequestParam(value = "aid", required = false) Long aid,
                                                                @ApiParam("bvid") @RequestParam(value = "bvid", required = false) String bvid,
                                                                @ApiParam("关键字") @RequestParam(value = "keyword", required = false) String keyword,
                                                                @ApiParam("page") @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
                                                                @ApiParam("size") @RequestParam(value = "size", required = false, defaultValue = "10") Integer size) {
        PageResult<ArchiveBo> pageResult = archiveService.getBilibiliAccountArcs(mid, aid, bvid, keyword, page, size);
        //这段逻辑有点特殊,直播间引流的情况下,可以自动播放=支持投放 不可以自动播放=不支持投放
        pageResult.getRecords().forEach(archiveBo -> archiveBo.setIsSupportAdvertising(archiveBo.getRights().getAutoplay()));
        return Response.ok(new Pagination<>(page, pageResult.getTotal(), pageResult.getRecords()
                .stream()
                .map(ArchiveConverter.MAPPER::bo2Vo)
                .map(ArchiveConverter.MAPPER::toV2)
                .collect(Collectors.toList())));
    }

    @ApiOperation(value = "查询账户下的商单稿件")
    @GetMapping(value = "/commercial_order")
    public Response<Pagination<List<ArchiveV2Vo>>> commercialOrderArcs(@ApiIgnore Context context,
                                                                       @ApiParam("aid") @RequestParam(value = "aid", required = false) Long aid,
                                                                       @ApiParam("aids") @RequestParam(value = "aids", required = false) List<Long> aids,
                                                                       @ApiParam("bvid") @RequestParam(value = "bvid", required = false) String bvid,
                                                                       @ApiParam("关键字") @RequestParam(value = "keyword", required = false) String keyword,
                                                                       @ApiParam("是否仅仅带货稿件 -1所有 1带货") @RequestParam(value = "only_goods", required = false, defaultValue = "-1") int onlyGoods,
                                                                       @ApiParam("mapi appkey") @RequestParam(value = "appkey", required = false, defaultValue = "") String appKey,
                                                                       @ApiParam("版本") @RequestParam(value = "adp_version", required = false, defaultValue = "5") int adp_version,
                                                                       @ApiParam("page") @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
                                                                       @ApiParam("size") @RequestParam(value = "size", required = false, defaultValue = "10") Integer size) {
        Assert.isTrue(size <= 100, "单次分页大小最大为100");

        // 临时逻辑 后面整个去掉重新设计吧 for pdd
        if (mapiPddAppKeyList.contains(appKey)) {
            PageResult<ArchiveBo> pddPageResult =
                    archiveService.getCommercialOderArcsForPdd(context.getAccountId(), aid, aids, bvid, keyword, page, size);
            List<ArchiveBo> arcs = pddPageResult.getRecords();

            if (CollectionUtils.isEmpty(arcs)) {
                return Response.ok(new Pagination<>(page, pddPageResult.getTotal(), Collections.emptyList()));
            }

            archiveService.fillIsOtt(arcs);
            archiveEscapeComponent.fillEscapeInfo(arcs, null, context.getAccountId(), adp_version);
            List<Long> avids = arcs.stream().map(ArchiveBo::getAid).collect(Collectors.toList());

            // 是否带货稿件
            if (!Utils.isPositive(onlyGoods)) {
                Map<Long, Integer> arcGoodsMap = newGoodsArchiveQuerier.queryArchiveTaskGoods2(avids);
                arcs.forEach(archiveBo -> {
                    archiveBo.setIsGoodsArchive(arcGoodsMap.getOrDefault(archiveBo.getAid(), YesNoEnum.NO.getCode()));
                });
            }

            return Response.ok(new Pagination<>(page, pddPageResult.getTotal(), arcs
                    .stream()
                    .map(ArchiveConverter.MAPPER::bo2Vo)
                    .map(ArchiveConverter.MAPPER::toV2)
                    .collect(Collectors.toList())));
        }

        PageResult<ArchiveBo> result = archiveService.getCommercialOrderArcs(context.getAccountId(), aid, aids, bvid, keyword, page, size);

        int total = result.getTotal();
        List<ArchiveBo> arcs = result.getRecords();
        archiveService.archiveGoodsPostHandler(arcs, true);

        if (CollectionUtils.isEmpty(arcs)) {
            return Response.ok(new Pagination<>(page, total, Collections.emptyList()));
        }
        archiveService.fillIsOtt(arcs);
        archiveEscapeComponent.fillEscapeInfo(arcs, null, context.getAccountId(), adp_version);
        List<Long> avids = arcs.stream().map(ArchiveBo::getAid).collect(Collectors.toList());

        // 是否带货稿件
        if (!Utils.isPositive(onlyGoods)) {
            Map<Long, Integer> arcGoodsMap = newGoodsArchiveQuerier.queryArchiveTaskGoods2(avids);
            arcs.stream().forEach(archiveBo -> {
                archiveBo.setIsGoodsArchive(arcGoodsMap.getOrDefault(archiveBo.getAid(), YesNoEnum.NO.getCode()));
            });
        }
        // 是否有锚点
        Map<Long, AnchorPreviewBo> anchorsMap = archiveAnchorQuerier.querySanlianAnchorsMap(avids);
        arcs.stream().forEach(archiveBo -> {
            AnchorPreviewBo anchorPreviewBo = anchorsMap.get(archiveBo.getAid());
            archiveBo.setHasAnchor(0);
            if (anchorPreviewBo != null) {
                archiveBo.setHasAnchor(1);
                archiveBo.setAnchorExistMsg(ArchiveAnchorQuerier.getAnchorExistMsg(context.getAccountId(), anchorPreviewBo));
            }
        });

        List<ArchiveV2Vo> resultArchiveVoList = arcs.stream()
                .map(ArchiveConverter.MAPPER::bo2Vo)
                .map(ArchiveConverter.MAPPER::toV2)
                .collect(Collectors.toList());

        List<LauNativeArchivePo> lauNativeArchivePos = lauNativeArchiveRepo.queryNativeArchive(avids, NativeBizType.SANLIAN_VALUE, 1);
        Map<Long, Integer> archiveAuditMap = lauNativeArchivePos.stream().collect(Collectors.toMap(LauNativeArchivePo::getAvid, LauNativeArchivePo::getAuditStatus, (t1, t2) -> t1));
        resultArchiveVoList.forEach(archiveV2Vo -> {
            Long archiveAvid = Long.parseLong(archiveV2Vo.getAid());
            Integer status = archiveAuditMap.get(archiveAvid);
            if (Objects.isNull(status)) {
                archiveV2Vo.setNativeStatus(NativeArchiveStatusEnum.NOT_NATIVE.getCode());
                archiveV2Vo.setNativeStatusDesc(NativeArchiveStatusEnum.NOT_NATIVE.getShowText());
            } else if (Objects.equals(status, com.bilibili.adp.common.enums.AuditStatus.ACCEPT.getCode())) {
                archiveV2Vo.setNativeStatus(NativeArchiveStatusEnum.NATIVE_PASS.getCode());
                archiveV2Vo.setNativeStatusDesc(NativeArchiveStatusEnum.NATIVE_PASS.getShowText());
            } else if (Objects.equals(status, AuditStatus.REJECT.getCode())) {
                archiveV2Vo.setNativeStatus(NativeArchiveStatusEnum.NATIVE_REJECT.getCode());
                archiveV2Vo.setNativeStatusDesc(NativeArchiveStatusEnum.NATIVE_REJECT.getShowText());
            }
        });
        resultArchiveVoList.sort(Comparator.comparing(archiveV2Vo -> Objects.isNull(archiveV2Vo.getNativeStatus()) ? Integer.MAX_VALUE : archiveV2Vo.getNativeStatus()));

        processArchiveHasCommentComponent(resultArchiveVoList, avids);

        return Response.ok(new Pagination<>(page, total, resultArchiveVoList));
    }

    @SneakyThrows
    @ApiOperation(value = "查询内容起飞稿件")
    @GetMapping(value = "/content_fly")
    public Response<List<ArchiveV2Vo>> contentFlyArcs(
            @ApiIgnore Context context,
            @ApiParam("keyword") @RequestParam(value = "kw", required = false, defaultValue = "") String kw) {
        // https://www.tapd.cn/********/prong/stories/view/11********004159242
        final AccountBaseDto accountBaseDto = queryAccountService.getAccountBaseDtoByIdFromCache(context.getAccountId());
        Assert.isTrue(Objects.nonNull(accountBaseDto) && accountBaseDto.getIsSupportContent() > 0, "目前仅支持内容起飞用户查询");

        final List<ArchiveBo> bos = archiveService.getContentFlyArcs(accountBaseDto.getAccountId(), accountBaseDto.getMid(), kw);
        return Response.ok(bos.stream()
                .map(ArchiveConverter.MAPPER::bo2Vo)
                .map(ArchiveConverter.MAPPER::toV2)
                .collect(Collectors.toList()));
    }

    @ApiOperation(value = "查询账户下的手动绑定稿件")
    @GetMapping(value = "/manual_bind")
    public Response<Pagination<List<ArchiveV2Vo>>> manualBindArcs(@ApiIgnore Context context,
                                                                  @ApiParam("aid") @RequestParam(value = "aid", required = false) Long aid,
                                                                  @ApiParam("aids") @RequestParam(value = "aids", required = false) List<Long> aids,
                                                                  @ApiParam("bvid") @RequestParam(value = "bvid", required = false) String bvid,
                                                                  @ApiParam("关键字") @RequestParam(value = "keyword", required = false) String keyword,
                                                                  @ApiParam("是否仅仅带货稿件 -1所有 1带货") @RequestParam(value = "only_goods", required = false, defaultValue = "-1") int onlyGoods,
                                                                  @ApiParam("版本") @RequestParam(value = "adp_version", required = false, defaultValue = "5") int adp_version,
                                                                  @ApiParam("page") @RequestParam(value = "page", defaultValue = "1") Integer page,
                                                                  @ApiParam("size") @RequestParam(value = "size", defaultValue = "10") Integer size) {
        Assert.isTrue(size <= 100, "单次分页大小最大为100");
        List<ArchiveBo> arcsFromManualBind = archiveService.getManualBindArcs(context.getAccountId(), aid, aids, bvid, keyword);

        // 内存分页
        List<ArchiveBo> arcs = arcsFromManualBind
                .stream()
                .skip((long) size * (page - 1))
                .limit(size)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(arcs)) {
            return Response.ok(new Pagination<>(page, arcsFromManualBind.size(), Collections.emptyList()));
        }
        archiveService.fillIsOtt(arcs);

        archiveEscapeComponent.fillEscapeInfo(arcs, null, context.getAccountId(), adp_version);
        List<Long> avids = arcs.stream().map(t -> t.getAid()).collect(Collectors.toList());

        // 是否带货稿件
        if (!Utils.isPositive(onlyGoods)) {
            Map<Long, Integer> arcGoodsMap = newGoodsArchiveQuerier.queryArchiveTaskGoods2(avids);
            arcs.stream().forEach(archiveBo -> {
                archiveBo.setIsGoodsArchive(arcGoodsMap.getOrDefault(archiveBo.getAid(), YesNoEnum.NO.getCode()));
            });
        }
        // 锚点
        Map<Long, AnchorPreviewBo> anchorsMap = archiveAnchorQuerier.querySanlianAnchorsMap(avids);
        arcs.stream().forEach(archiveBo -> {
            AnchorPreviewBo anchorPreviewBo = anchorsMap.get(archiveBo.getAid());
            archiveBo.setHasAnchor(0);
            if (anchorPreviewBo != null) {
                archiveBo.setHasAnchor(1);
                archiveBo.setAnchorExistMsg(ArchiveAnchorQuerier.getAnchorExistMsg(context.getAccountId(), anchorPreviewBo));
            }
        });

        List<ArchiveV2Vo> resultArchiveVoList = arcs.stream()
                .map(ArchiveConverter.MAPPER::bo2Vo)
                .map(ArchiveConverter.MAPPER::toV2)
                .collect(Collectors.toList());

        List<LauNativeArchivePo> lauNativeArchivePos = lauNativeArchiveRepo.queryNativeArchive(avids, NativeBizType.SANLIAN_VALUE, 1);
        Map<Long, Integer> archiveAuditMap = lauNativeArchivePos.stream().collect(Collectors.toMap(LauNativeArchivePo::getAvid, LauNativeArchivePo::getAuditStatus, (t1, t2) -> t1));
        resultArchiveVoList.forEach(archiveV2Vo -> {
            Long archiveAvid = Long.parseLong(archiveV2Vo.getAid());
            Integer status = archiveAuditMap.get(archiveAvid);
            if (Objects.isNull(status)) {
                archiveV2Vo.setNativeStatus(NativeArchiveStatusEnum.NOT_NATIVE.getCode());
                archiveV2Vo.setNativeStatusDesc(NativeArchiveStatusEnum.NOT_NATIVE.getShowText());
            } else if (Objects.equals(status, com.bilibili.adp.common.enums.AuditStatus.ACCEPT.getCode())) {
                archiveV2Vo.setNativeStatus(NativeArchiveStatusEnum.NATIVE_PASS.getCode());
                archiveV2Vo.setNativeStatusDesc(NativeArchiveStatusEnum.NATIVE_PASS.getShowText());
            } else if (Objects.equals(status, AuditStatus.REJECT.getCode())) {
                archiveV2Vo.setNativeStatus(NativeArchiveStatusEnum.NATIVE_REJECT.getCode());
                archiveV2Vo.setNativeStatusDesc(NativeArchiveStatusEnum.NATIVE_REJECT.getShowText());
            }
        });
        resultArchiveVoList.sort(Comparator.comparing(archiveV2Vo -> Objects.isNull(archiveV2Vo.getNativeStatus()) ? Integer.MAX_VALUE : archiveV2Vo.getNativeStatus()));

        processArchiveHasCommentComponent(resultArchiveVoList, avids);

        return Response.ok(new Pagination<>(page, arcsFromManualBind.size(), resultArchiveVoList));
    }

    @ApiOperation(value = "获取视频当前评论弹幕状态")
    @GetMapping(value = "/interact")
    public Response<List<ArchiveInteractStatusVo>> getArchiveInteractStatus(@ApiParam("avid") @RequestParam(value = "av_ids") List<Long> avids) {
        List<LauArchiveInteractStateBo> bos = adpCpcCreativeArcInteractStateService.getArchiveInteractStateList(avids);
        return Response.ok(bos
                .stream()
                .map(ArchiveInteractConverter.MAPPER::archiveInteractStatusBo2Vo)
                .collect(Collectors.toList()));
    }

    @ApiOperation(value = "修改视频当前评论状态")
    @PutMapping(value = "/reply")
    public Response<Object> replyUpdate(@RequestBody ArchiveInteractStatusVo vo) {
        ArchiveInteractStatusVo.validateVo(vo);
        adpCpcCreativeArcInteractStateService.validateSingleAvidIsControllable(vo.getAvId());
        adpCpcCreativeArcInteractStateService.updateReplyStateRequest(vo.getAvId(), vo.getReplyState());
        return Response.ok();
    }

    @ApiOperation(value = "分页获取当前账户下稿件以及关联创意")
    @GetMapping(value = "/creative")
    public Response<Pagination<List<ArchiveCreativesVo>>> getArchives(@ApiIgnore Context context,
                                                                      @ApiParam("创意类型 0-自定义 1-程序化") @RequestParam(value = "creative_type") Integer creativeType,
                                                                      @ApiParam("avid") @RequestParam(value = "av_id", required = false) String avIdStr,
                                                                      @ApiParam("创意更新起始时间") @RequestParam(value = "begin_date") Long beginDate,
                                                                      @ApiParam("创意更新结束时间") @RequestParam(value = "end_date") Long endDate,
                                                                      @ApiParam("分页") @RequestParam(value = "page", defaultValue = "1") Integer page,
                                                                      @ApiParam("分页大小") @RequestParam(value = "page_size", defaultValue = "20") Integer pageSize) {
        if (context == null || context.getAccountId() == null) {
            return Response.ok(Pagination.emptyPagination());
        }
        QueryAccountArchiveBo queryBo = QueryAccountArchiveBo.builder()
                .accountId(context.getAccountId())
                .creativeType(creativeType)
                .avidStr(avIdStr)
                .fromTime(Utils.getBeginOfDay(new Timestamp(beginDate)))
                .toTime(Utils.getEndOfDay(new Timestamp(endDate)))
                .page(Page.valueOf(page, pageSize))
                .build();
        queryBo.validInput();
        PageResult<LauArchiveListBo> pageResult = adpCpcCreativeArcInteractStateService.getArchiveListByAccount(queryBo);
        return Response.ok(new Pagination<>(page, pageResult.getTotal(), pageResult
                .getRecords()
                .stream()
                .map(ArchiveInteractConverter.MAPPER::archiveListBo2Vo)
                .collect(Collectors.toList())));
    }

    @ApiOperation(value = "获取稿件评论列表")
    @GetMapping(value = "/reply/selection")
    public Response<ArchiveUpSelectionReplyCursorPageVo> getReplySelection(@ApiParam("avid") @RequestParam(value = "av_id") Long avid,
                                                                           @ApiParam("分页大小") @RequestParam(value = "page_size", defaultValue = "20") Integer pageSize,
                                                                           @ApiParam("翻页方向 0-下一页 1-上一页") @RequestParam(value = "direction", defaultValue = "0") Integer direction,
                                                                           @ApiParam("翻页所需游标参数 第一页为空") @RequestParam(value = "cursor", defaultValue = "") String cursor) {
        try {
            if (!StringUtils.isEmpty(cursor)) {
                cursor = URLDecoder.decode(cursor, "UTF-8");
            }
        } catch (UnsupportedEncodingException e) {
            throw new IllegalArgumentException("游标数据错误");
        }
        CursorPage cursorPage = CursorPage.valueOf(pageSize, direction, cursor);
        adpCpcCreativeArcInteractStateService.validateSingleAvidIsControllable(avid);
        ArcUpSelectionReplyCursorPageBo bo = adpCpcCreativeArcInteractStateService.getRepliesForUpSelection(avid, cursorPage);
        return Response.ok(ArchiveInteractConverter.MAPPER.arcUpSelectionReplyCursorPageBo2Vo(bo));
    }

    @ApiOperation(value = "up主精选评论操作")
    @PutMapping(value = "/reply/selection")
    public Response<Object> updateReplySelection(@ApiParam("精选评论操作") @RequestBody ArchiveUpSelectionActionVo queryVo) {
        adpCpcCreativeArcInteractStateService.validateSingleAvidIsControllable(queryVo.getAvId());
        ArchiveUpSelectionActionVo.validateVo(queryVo);
        adpCpcCreativeArcInteractStateService.arcUpDoSelectionAction(ArchiveInteractConverter.MAPPER.archiveUpSelectionActionVo2Bo(queryVo));
        return Response.ok();
    }

    @ApiOperation(value = "PGC稿件列表")
    @GetMapping(value = "/pgc")
    public Response<Pagination<List<PgcArchiveVo>>> pgcArchives(
            @ApiIgnore Context context,
            @ApiParam("页号") @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
            @ApiParam("页大小") @RequestParam(value = "size", required = false, defaultValue = "20") Integer size) {
        Map<Long, PgcArchiveBo> pgcArchiveBoMap = archiveService.getPgcArcsByAccountId(context.getAccountId());
        List<PgcArchiveBo> result = pgcArchiveBoMap
                .values()
                .stream()
                //过滤付费
                .filter(PgcArchiveBo::getIsFree)
                //过滤互动视频
                .filter(p -> !p.getIsInteractiveVideo())
                //过滤地区，只展示 全球 || 中国大陆
                .filter(PgcArchiveBo::getIsValidLimitGroup)
                .collect(Collectors.toList());
        // 逻辑分页
        if (CollectionUtils.isEmpty(result) || size * (page - 1) >= result.size()) {
            return Response.ok(new Pagination<>(page, 0, Lists.newArrayList()));
        }
        List<PgcArchiveBo> subBoList = result.subList(size * (page - 1), Math.min(size * page, result.size()));
        // 填充是否可在OTT端展示
        fillOttInfo(subBoList);
        return Response.ok(new Pagination<>(page, result.size(), subBoList
                .stream()
                .map(ArchiveConverter.MAPPER::bo2Vo)
                .collect(Collectors.toList())));
    }

    //region 稿件评论转化组件相关

    @ApiOperation(value = "PGC稿件列表V1")
    @GetMapping(value = "/pgc_v1")
    public Response<Pagination<List<PgcArchiveVo>>> pgcArchivesV1(
            @ApiParam("mid") @RequestParam(value = "mid") Long mid,
            @ApiParam("aid") @RequestParam(value = "aid", required = false) Long aid,
            @ApiParam("bvid") @RequestParam(value = "bvid", required = false) String bvid,
            @ApiParam("关键字") @RequestParam(value = "keyword", required = false) String keyword,
            @ApiParam("页号") @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
            @ApiParam("页大小") @RequestParam(value = "size", required = false, defaultValue = "20") Integer size) {
        PageResult<PgcArchiveBo> pageResult = archiveService.getPgcArcsV1(mid, aid, bvid, keyword, page, size);
        List<PgcArchiveVo> pgcArchiveVos = pageResult
                .getRecords()
                .stream()
                .map(ArchiveConverter.MAPPER::bo2Vo)
                .collect(Collectors.toList());
        return Response.ok(new Pagination<>(page, pageResult.getTotal(), pgcArchiveVos));
    }

    @ApiOperation(value = "获取评论转化组件")
    @GetMapping(value = "/comment_conversion_component/{container_id}")
    public Response<ArchiveCommentConversionComponentDetailVo> getCommentConversionComponent(
            @ApiIgnore Context context,
            @PathVariable(value = "container_id") Long containerId,
            @RequestParam(value = "container_type", required = false, defaultValue = "1") Integer containerType
    ) {
        final CommentComponent oriComponent = archiveConversionComponentService.getCommentConversionComponent(containerId, containerType).getComponent();
        // 过滤删除组件
        if (Objects.equals(oriComponent.getStatusValue(), CommentComponentStatus.DELETED_VALUE)) {
            return Response.ok();
        }
        CommentComponentBo component = handleShadowComponent(oriComponent);

        // 同集团同品牌的账号id
        //List<Integer> allAccountIds = adpCpcAccountService.querySameGroupAndSameBrandAccountIds(context.getAccountId());
        List<Integer> allAccountIds = accountV6Service.listAccountIdsByType(context.getAccountId(), SAME_GROUP_SAME_PRODUCT_VALUE);
        //List<Integer> accountIds = Arrays.asList(component.getAccountId());
        // 同代理的账号id
        //List<Integer> sameAgentAccountIds = adpCpcAccountService.querySameAgentAccountIds(context.getAccountId(), accountIds);
//        List<Integer> sameAgentAccountIds = accountV6Service.listAccountIdsByType(context.getAccountId(), SAME_AGENT_VALUE);
//        Assert.isTrue(allAccountIds.contains(component.getAccountId()) && sameAgentAccountIds.contains(component.getAccountId()), "无法对已有置顶蓝链的稿件进行配置。");
        Assert.isTrue(allAccountIds.contains(component.getAccountId()), "无法对已有置顶蓝链的稿件进行配置。");
        ArchiveBo archive = null;
        if (Objects.equals(containerType, CommentContainerTypeEnum.ARCHIVE.getNumber())) {
            archive = archiveService.getArchiveByAid(containerId);
        }
        // 获取建站落地页名称
        Map<Long, String> pageMap = archiveConversionComponentService.landingPage4ComponentList(Arrays.asList(component));
        // 获取iOS，Android应用包信息
        Map<Integer, AppPackageBo> appPackageBoMap = archiveConversionComponentService.appPackage4ComponentList(Arrays.asList(component));
        // 获取资质信息
        Map<Integer, String> qualificationMap = archiveConversionComponentService.qualifications4ComponentList(Arrays.asList(component));

        final Map<Long, DynamicBo> dynamicMap = biliDynamicService.fetchDynamics(Objects.equals(containerType, CommentContainerTypeEnum.DYNAMIC_VALUE) ? Collections.singletonList(containerId) : Collections.emptyList());
        ArchiveCommentConversionComponentDetailVo vo = ArchiveConversionComponentConverter.MAPPER.component2DetailVoV2(dynamicMap.get(containerId), archive, pageMap, appPackageBoMap.get(component.getIosAppPackageId()), appPackageBoMap.get(component.getAndroidAppPackageId()), generateQualificationNames(JSON.parseArray(component.getQualificationIds(), Integer.class), qualificationMap), component);
        final List<Long> productIds = fetchProductIds(component.getProductId(), component.getProductIdSet());
        if (!CollectionUtils.isEmpty(productIds)) {
            vo.setProducts(fetchGoods(context.getAccountId(), productIds));
        }
        vo.setCanOperate(IsValid.TRUE.getCode());
        // 表单浮层
        if (ClueTypeEnum.FORM_FLOAT.getCode().equals(component.getClueType())) {
            MgkFormDto formDto = soaMgkFormService.getFormDtoByFormId(Long.parseLong(component.getClueData()));
            String name = formDto == null ? "--" : formDto.getName();
            vo.setClueDataName(name);
            log.info("getFormDtoByFormId formDto [{}], name[{}]", formDto, name);
        }
        // 微信浮层
        else if (ClueTypeEnum.WECHAT_FLOAT.getCode().equals(component.getClueType())) {
            WechatPackageDto packageDto = soaMgkWechatPackageService
                    .getWechatPackageByWechatPackageId(Integer.parseInt(component.getClueData()));
            String name = packageDto == null ? "--" : packageDto.getName();
            vo.setClueDataName(name);
            log.info("getWechatPackageByWechatPackageId WechatPackage [{}], name [{}]", packageDto, name);
        }
        // 微信小游戏
        else if (ClueTypeEnum.MINI_GAME.getCode().equals(component.getClueType())) {
            LauMiniGameDto miniGameDto = lauMiniGameService.getLauMiniGameById(Integer.parseInt(component.getClueData()));
            String name = miniGameDto == null ? "--" : miniGameDto.getName();
            vo.setClueDataName(name);
        }
        if (Objects.equals(ComponentType.BILI_MINI_GAME_VALUE, component.getComponentType())) {
            BiliMiniGameBo biliMiniGameBo = launchBiliMiniGameService.get(component.getBiliMiniGameMid(), component.getGameBaseId());
            if (biliMiniGameBo != null) {
                vo.setIsAndroidOnline(biliMiniGameBo.getIsAndroidOnline());
                vo.setIsIosOnline(biliMiniGameBo.getIsIosOnline());
            }
        }
        return Response.ok(vo);
    }

    @ApiOperation(value = "删除评论转化组件")
    @DeleteMapping(value = "/comment_conversion_component/{container_id}")
    public Response<Void> deleteCommentConversionComponent(
            @ApiIgnore Context context,
            @PathVariable(value = "container_id") Long containerId,
            @RequestParam(value = "container_type", required = false, defaultValue = "1") Integer containerType) {
        archiveConversionComponentService.deleteCommentConversionComponent(containerId, containerType, super.getOperator(context));
        return Response.ok();
    }

    @ApiOperation(value = "评论转化组件列表")
    @GetMapping(value = "/comment_conversion_components")
    public Response<Pagination<List<ArchiveCommentConversionComponentDetailVo>>> listCommentConversionComponent(
            @ApiIgnore Context context,
            @RequestParam(value = "aid", required = false) List<String> videoIds,
            @RequestParam(value = "dynamic_ids", required = false) List<Long> dynamicIds,
            @RequestParam(value = "mid", required = false) List<Long> mids,
            @RequestParam(value = "audit_status", required = false) List<Integer> auditStatus,
            @RequestParam(value = "customer_id", required = false) List<Integer> customerIds,
            @RequestParam(value = "agent_id", required = false) List<Integer> agentIds,
            @RequestParam(value = "search_account_ids", required = false) List<Integer> searchAccountIds,
            @RequestParam(value = "from_time", required = false) Long fromTime,
            @RequestParam(value = "to_time", required = false) Long toTime,
            @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
            @RequestParam(value = "size", required = false, defaultValue = "10") Integer size,
            @RequestParam(value = "archive_title", required = false, defaultValue = "") String archiveTitle,
            @RequestParam(value = "dynamic_title", required = false, defaultValue = "") String dynamicTitle,
            @RequestParam(value = "group_id", required = false, defaultValue = "0") Integer groupId,
            @RequestParam(value = "content_type", required = false, defaultValue = "0") Integer contentType,
            @RequestParam(value = "comment_status", required = false, defaultValue = "0") Integer commentStatus,
            @RequestParam(value = "component_type", required = false, defaultValue = "0") Integer componentType) {
        Assert.isTrue(size <= 100, "单次分页大小最大为100");
        final List<Long> avids = new ArrayList<>();
        if (!CollectionUtils.isEmpty(videoIds)) {
            for (String videoId : videoIds) {
                try {
                    final long avid = Long.parseLong(videoId);
                    avids.add(avid);
                } catch (Throwable t) {
                    final long avid = BVIDUtils.bvToAv(videoId);
                    avids.add(avid);
                }
            }
        }
        ComponentsReply componentsReply = archiveConversionComponentService.listCommentConversionComponent(
                context.getAccountId(),
                avids,
                dynamicIds,
                mids,
                auditStatus,
                customerIds,
                agentIds,
                Optional.ofNullable(fromTime).orElse(0L),
                Optional.ofNullable(toTime).orElse(LocalDateTime.of(LocalDate.now(), LocalTime.MIDNIGHT).plusDays(1L).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()),
                page,
                size,
                contentType,
                commentStatus,
                componentType,
                searchAccountIds,
                archiveTitle,
                dynamicTitle,
                groupId
        );
        List<CommentComponent> oriComponents = componentsReply.getComponentsList();

        if (CollectionUtils.isEmpty(oriComponents)) {
            return Response.ok(new Pagination<>(page, (int) componentsReply.getTotal(), Collections.emptyList()));
        }

        List<CommentComponentBo> components = oriComponents.stream().map(this::handleShadowComponent).collect(Collectors.toList());

        final List<Long> dynamicIdsFromComponents = components.stream()
                .map(CommentComponentBo::getDynamicId)
                .filter(Utils::isPositive)
                .collect(Collectors.toList());
        final Map<Long, DynamicBo> dynamicMap = biliDynamicService.fetchDynamics(dynamicIdsFromComponents);
        List<Long> aidsFromComponents = components.stream()
                .map(CommentComponentBo::getAid)
                .filter(Utils::isPositive)
                .collect(Collectors.toList());
        Map<Long, ArchiveBo> archives = archiveService.getArcsByAids(aidsFromComponents);
        // 查询这批账号id哪些和登陆账号id是同代理的
        //List<Integer> accountIds = components.stream().map(CommentComponentBo::getAccountId).distinct().collect(Collectors.toList());
        //List<Integer> sameAgentAccountIds = adpCpcAccountService.querySameAgentAccountIds(context.getAccountId(), accountIds);
//        List<Integer> sameAgentAccountIds = accountV6Service.listAccountIdsByType(context.getAccountId(), SAME_AGENT_VALUE);

        // 获取组件账户的代理
        List<Integer> accountIdsOfComponents = components.stream().map(CommentComponentBo::getAccountId).distinct().collect(Collectors.toList());
        accountIdsOfComponents.add(context.getAccountId());
        List<AccountBase> accountBases = accountV6Service.listAccounts(accountIdsOfComponents);
        Map<Integer, AccountBase> accountBaseMap = accountBases.stream().collect(Collectors.toMap(t -> t.getAccountId(), t -> t, (t1, t2) -> t1));

        // 获取建站落地页名称
        Map<Long, String> pageMap = archiveConversionComponentService.landingPage4ComponentList(components);
        // 获取iOS，Android应用包信息
        Map<Integer, AppPackageBo> appPackageBoMap = archiveConversionComponentService.appPackage4ComponentList(components);
        // 获取资质信息
        Map<Integer, String> qualificationMap = archiveConversionComponentService.qualifications4ComponentList(components);

        // 商品ids
        List<Long> goodsItemIds = components.stream()
                .filter(t -> Objects.equals(ComponentType.GOODS_VALUE, t.getComponentType()))
                .flatMap(t -> ListIdUtils.unionSingleAndList(t.getProductId(), t.getProductIdSet()).stream())
                .distinct()
                .collect(Collectors.toList());
        Map<Long, Integer> goodsRealStatusMap = goodsManagerService.queryRealStatusMapByItemIds(goodsItemIds, context.getAccountId());

        List<ArchiveCommentConversionComponentDetailVo> vos = components
                .stream()
                .map(component -> {

                    ArchiveCommentConversionComponentDetailVo vo = ArchiveConversionComponentConverter.MAPPER.component2DetailVoV2(dynamicMap.get(component.getDynamicId()), archives.get(component.getAid()), pageMap, appPackageBoMap.get(component.getAndroidAppPackageId()), appPackageBoMap.get(component.getIosAppPackageId()), generateQualificationNames(JSON.parseArray(component.getQualificationIds(), Integer.class), qualificationMap), component);
                    handleOperate(component, vo, accountBaseMap, context.getAccountId());
                    final Tuple2<Integer, Integer> tuple2 = areProductsAndComponentValid(component, goodsRealStatusMap);
                    vo.setGoodsIsValid(tuple2._1);
                    vo.setComponentIsValid(tuple2._2);

                    // 兜底链接清空
                    clearFallbackUrl(vo);
                    // 统一 schecma url
                    vo.setSchema_url_type(2); // 前端老师要求
                    if (Objects.equals(vo.getAndroidSchemaUrl(), vo.getIosSchemaUrl())) {
                        vo.setSchemaUrl(vo.getAndroidSchemaUrl());
                        vo.setSchema_url_type(1);
                        vo.setAndroidSchemaUrl("");
                        vo.setIosSchemaUrl("");
                    }


                    // 前端老师要求
                    generateAppPlatformTypes(component, vo);
                    return vo;
                }).collect(Collectors.toList());
        processClueDataName(vos);

        return Response.ok(new Pagination<>(page, (int) componentsReply.getTotal(), vos));
    }

    private void clearFallbackUrl(ArchiveCommentConversionComponentDetailVo vo) {
        if (vo.getConversionUrl().contains(archiveConversionComponentService.getConvH5BottomLinkKeyword())) {
            vo.setConversionUrl("");
        }
        if (vo.getAndroidUrl().contains(archiveConversionComponentService.getConvH5BottomLinkKeyword())) {
            vo.setAndroidUrl("");
        }
        if (vo.getIosUrl().contains(archiveConversionComponentService.getConvH5BottomLinkKeyword())) {
            vo.setIosUrl("");
        }
    }

    private List<String> generateQualificationNames(List<Integer> qualificationIds, Map<Integer, String> qualificationMap) {
        List<String> res = new ArrayList<>();
        qualificationIds.forEach(o -> {
            res.add(qualificationMap.getOrDefault(o, ""));
        });
        return res;
    }

    // 京东专用 仅支持稿件
    @ApiOperation(value = "评论转化组件列表")
    @GetMapping(value = "/comment_conversion_components/jd_extra_info")
    public Response<Pagination<List<ArchiveJDExtraInfoVo>>> getArchiveCommentJDExtraInfo(
            @ApiIgnore Context context,
            @RequestParam(value = "aid", required = false) List<String> videoIds,
            @RequestParam(value = "mid", required = false) List<Long> mids,
            @RequestParam(value = "from_time", required = false) Long fromTime,
            @RequestParam(value = "to_time", required = false) Long toTime,
            @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
            @RequestParam(value = "size", required = false, defaultValue = "10") Integer size,
            @RequestParam(value = "search_account_ids", required = false) List<Integer> searchAccountIds,
            @RequestParam(value = "comment_status", required = false, defaultValue = "0") Integer commentStatus) {
        Assert.isTrue(size <= 100, "单次分页大小最大为100");
        final List<Long> avids = new ArrayList<>();
        if (!CollectionUtils.isEmpty(videoIds)) {
            for (String videoId : videoIds) {
                try {
                    final long avid = Long.parseLong(videoId);
                    avids.add(avid);
                } catch (Throwable t) {
                    final long avid = BVIDUtils.bvToAv(videoId);
                    avids.add(avid);
                }
            }
        }
        ComponentsReply componentsReply = archiveConversionComponentService.listCommentConversionComponent(
                context.getAccountId(),
                avids,
                Collections.emptyList(),
                mids,
                Collections.emptyList(),
                Collections.emptyList(),
                Collections.emptyList(),
                Optional.ofNullable(fromTime).orElse(0L),
                Optional.ofNullable(toTime).orElse(LocalDateTime.of(LocalDate.now(), LocalTime.MIDNIGHT).plusDays(1L).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()),
                page,
                size,
                1,
                commentStatus,
                0,
                searchAccountIds
        );
        List<CommentComponent> oriComponents = componentsReply.getComponentsList();
        Map<Long, Integer> avid2AccoutIdMap = oriComponents.stream().collect(Collectors.toMap(CommentComponent::getAid, CommentComponent::getAccountId));
        List<SanlianArchiveExtraBo> extraBoList = sanlianCreativeGoodsContentService.getJDExtraInfoListByArcCommentList(oriComponents);
        List<ArchiveJDExtraInfoVo> voList = extraBoList.stream()
                .map(extraBo -> {
                    ArchiveJDExtraInfoVo archiveJDExtraInfoVo = ArchiveConversionComponentConverter.MAPPER.convertJDExtraBotoVo(extraBo);
                    archiveJDExtraInfoVo.setAccountId(avid2AccoutIdMap.get(extraBo.getAvid()));
                    return archiveJDExtraInfoVo;
                })
                .collect(Collectors.toList());
        return Response.ok(new Pagination<>(page, (int) componentsReply.getTotal(), voList));
    }

    @SneakyThrows
    @ApiOperation("同步重新触发评论转化组件")
    @PostMapping("/resend/comment_conversion_component")
    public Response<Void> resendCommentComponent(@ApiIgnore Context context, @RequestBody ResendCommentComponentVo vo) {
        final long containerId;
        final int containerType;
        if (org.springframework.util.StringUtils.hasText(vo.getAvid())) {
            containerId = Long.parseLong(vo.getAvid());
            containerType = CommentContainerTypeEnum.ARCHIVE_VALUE;
        } else if (org.springframework.util.StringUtils.hasText(vo.getDynamicId())) {
            containerId = Long.parseLong(vo.getDynamicId());
            containerType = CommentContainerTypeEnum.DYNAMIC_VALUE;
        } else {
            throw new IllegalArgumentException("必须指定评论组件的容器");
        }
        final ArchiveCommentConversionComponentDetailVo detailVo = getCommentConversionComponentWithDelete(context, containerId, containerType);
        if (Objects.isNull(detailVo)) {
            throw new IllegalArgumentException("容器对应的组件不存在");
        }

        ArchiveCommentConversionComponentVo archiveCommentConversionComponentVo = ArchiveConversionComponentConverter.MAPPER.fromDetailVo(detailVo);
        if (Objects.equals(CommentContainerTypeEnum.DYNAMIC_VALUE, containerType)) {
            archiveCommentConversionComponentVo.setDynamicIds(Collections.singletonList(containerId));
        } else if (Objects.equals(CommentContainerTypeEnum.ARCHIVE_VALUE, containerType)) {
            archiveCommentConversionComponentVo.setAvids(Collections.singletonList(containerId));
        }
        if (archiveCommentConversionComponentVo.getComponentType().equals(ComponentType.GOODS_VALUE)) {
            ArchiveComponentConversionComponentBatchVo batchVo = new ArchiveComponentConversionComponentBatchVo();
            batchVo.setTemplate(archiveCommentConversionComponentVo);
            batchVo.setAvids(archiveCommentConversionComponentVo.getAvids());
            batchVo.setDynamicIds(archiveCommentConversionComponentVo.getDynamicIds());
            batchSaveCommentConversionComponentAsync(context, batchVo);
        } else {
            saveCommentConversionComponent(context, archiveCommentConversionComponentVo);
        }
        return Response.ok();
    }

    @ApiOperation("异步批量新增评论转化组件(带货)")
    @PostMapping("/batch/comment_conversion_components")
    public Response<String> batchSaveCommentConversionComponentAsync(@ApiIgnore Context context, @RequestBody ArchiveComponentConversionComponentBatchVo batchVo) {
        log.info("batchSaveCommentConversionComponentAsync,batchVo{}", JSON.toJSONString(batchVo));
        batchVo.getTemplate().setIsMapiRequest(context.isOpenApi());
        ArchiveCommentConversionComponentBo componentBo = ArchiveConversionComponentConverter.MAPPER.vo2Bo(batchVo.getTemplate());
        Long operationId = archiveConversionComponentService.batchCreateCommentConversionComponentsAsync(getOperator(context), componentBo, batchVo.getAvids(), batchVo.getDynamicIds());
        return Response.ok(operationId.toString());
    }

    @ApiOperation("异步批量新增评论转化组件(带货)")
    @PutMapping("/batch/comment_conversion_components")
    public Response<String> batchUpdateCommentConversionComponentAsync(@ApiIgnore Context context, @RequestBody ArchiveComponentConversionComponentBatchVo batchVo) {
        log.info("batchSaveCommentConversionComponentAsync,batchVo{}", JSON.toJSONString(batchVo));
        ArchiveCommentConversionComponentBo componentBo = ArchiveConversionComponentConverter.MAPPER.vo2Bo(batchVo.getTemplate());
        Long operationId = archiveConversionComponentService.batchUpdateCommentConversionComponentsAsync(getOperator(context), componentBo, batchVo.getAvids(), batchVo.getDynamicIds());
        return Response.ok(operationId.toString());
    }

    @ApiOperation(value = "新增评论转化组件")
    @PostMapping(value = "/comment_conversion_component")
    public Response<String> saveCommentConversionComponent(@ApiIgnore Context context, @RequestBody ArchiveCommentConversionComponentVo archiveCommentConversionComponentVo) throws ServiceException {
        log.info("saveCommentConversionComponent, vo={}", JSON.toJSONString(archiveCommentConversionComponentVo));
        archiveCommentConversionComponentVo.setIsMapiRequest(context.isOpenApi());
        log.info("saveCommentConversionComponent,batchVo{}", JSON.toJSONString(archiveCommentConversionComponentVo));
        ArchiveCommentConversionComponentBo componentBo = ArchiveConversionComponentConverter.MAPPER.vo2Bo(archiveCommentConversionComponentVo);
        Long operationId = archiveConversionComponentService.saveCommentConversionComponent(getOperator(context), componentBo);
        return Response.ok(operationId.toString());
    }

    @ApiOperation(value = "修改评论转化组件")
    @PutMapping(value = "/comment_conversion_component")
    public Response<String> updateCommentConversionComponent(@ApiIgnore Context context,
                                                             @RequestBody ArchiveCommentConversionComponentVo archiveCommentConversionComponentVo
    ) throws ServiceException, UnsupportedEncodingException {
        archiveCommentConversionComponentVo.setIsMapiRequest(context.isOpenApi());
        log.info("updateCommentConversionComponent,batchVo{}", JSON.toJSONString(archiveCommentConversionComponentVo));

        ArchiveCommentConversionComponentBo componentBo = ArchiveConversionComponentConverter.MAPPER.vo2Bo(archiveCommentConversionComponentVo);
        Long operationId = archiveConversionComponentService.updateCommentConversionComponent(getOperator(context), componentBo);
        return Response.ok(operationId.toString());
    }

    @ApiOperation(value = "修改评论转化组件的分组")
    @PostMapping(value = "/comment_conversion_component/group")
    public Response<Void> updateCommentConversionComponentGroup(@ApiIgnore Context context,
                                                                @RequestBody ArchiveCommentConversionComponentVo archiveCommentConversionComponentVo
    ) throws ServiceException, UnsupportedEncodingException {
        log.info("updateCommentConversionComponent,batchVo{}", JSON.toJSONString(archiveCommentConversionComponentVo));

        ArchiveCommentConversionComponentBo componentBo = ArchiveConversionComponentConverter.MAPPER.vo2Bo(archiveCommentConversionComponentVo);
        archiveConversionComponentService.commentGroup(getOperator(context), componentBo);
        return Response.ok();
    }

    @ApiOperation(value = "删除评论转化组件")
    @PostMapping(value = "/comment_conversion_component/delete")
    public Response<String> deleteCommentConversionComponent(@ApiIgnore Context context, @RequestBody ArchiveCommentConversionComponentVo archiveCommentConversionComponentVo) throws ServiceException {
        log.info("saveCommentConversionComponent, vo={}", JSON.toJSONString(archiveCommentConversionComponentVo));
        boolean isMapiRequest = context.isOpenApi();
        archiveCommentConversionComponentVo.setIsMapiRequest(isMapiRequest);
        log.info("saveCommentConversionComponent,batchVo{}", JSON.toJSONString(archiveCommentConversionComponentVo));
        ArchiveCommentConversionComponentBo componentBo = ArchiveConversionComponentConverter.MAPPER.vo2Bo(archiveCommentConversionComponentVo);
        Long operationId = archiveConversionComponentService.batchOperateCommentComponent(getOperator(context), componentBo, BatchOperationType.BATCH_COMPONENT_DELETE, 1);
        return Response.ok(operationId.toString());
    }

    @ApiOperation(value = "获取落地页")
    @GetMapping(value = "/landing_pages_options")
    public Response<List<SelectOptionVo>> landingPagesOption(@ApiIgnore Context context,
                                                             @ApiParam(value = "contain_consult") @RequestParam(value = "contain_consult", required = false, defaultValue = "false") boolean containConsult) {
        List<MgkLandingPageDto> mgkLandingPageDtos = archiveConversionComponentService.landingPages(context.getAccountId(), containConsult);
        final List<SelectOptionVo> options = mgkLandingPageDtos.stream()
                .map(x -> SelectOptionVo.builder()
                        .value(x.getPageId().toString())
                        .name(x.getName())
                        .isPc(x.getIsPc())
                        .avid(Optional.ofNullable(x.getAvid()).orElse(0L).toString())
                        .isVideoPage(x.getIsVideoPage())
                        .status(x.getStatus())
                        .statusDesc(LandingPageStatusEnum.getByCode(x.getStatus()).getDesc())
                        .isConsultPage(x.getType() == 6)
                        .build())
                .collect(Collectors.toList());
        return Response.ok(options);
    }
    //endregion

    @ApiOperation(value = "获取评论浮层表单")
    @GetMapping(value = "/comment/form")
    public Response<List<SelectOptionVo>> getCommentForm(@ApiIgnore Context context) {
        List<MgkFormDto> mgkFormDtoS = archiveConversionComponentService.getMgkFormList(context.getAccountId());
        final List<SelectOptionVo> options = mgkFormDtoS.stream()
                .map(x -> SelectOptionVo.builder()
                        .value(x.getFormId().toString())
                        .name(x.getName())
                        .build())
                .collect(Collectors.toList());
        return Response.ok(options);
    }

    @ApiOperation(value = "获取微信包")
    @GetMapping(value = "/wechat/package")
    public Response<List<SelectOptionVo>> getWechatPackage(@ApiIgnore Context context) {
        List<WechatPackageListDto> packageDtoS = archiveConversionComponentService.getWeChatPackageList(context.getAccountId());
        final List<SelectOptionVo> options = packageDtoS.stream()
                .map(x -> SelectOptionVo.builder()
                        .value(x.getId().toString())
                        .name(x.getName())
                        .build())
                .collect(Collectors.toList());
        return Response.ok(options);
    }


    private void fillOttInfo(List<PgcArchiveBo> pgcArchiveBos) {
        if (CollectionUtils.isEmpty(pgcArchiveBos)) {
            return;
        }
        Map<Integer, Boolean> ottMap = ottService.isOttFromPgc(pgcArchiveBos.stream().map(PgcArchiveBo::getEpisodeId).collect(Collectors.toList()));
        pgcArchiveBos.stream().map(a -> {
            a.setIsOtt(ottMap.get(a.getEpisodeId()));
            return a;
        }).collect(Collectors.toList());
    }

    private List<GoodsVo> fetchGoods(Integer accountId, List<Long> productIds) {
        // 调用带货接口获取商品列表(已经根据outerId和source做了去重)
        final CidGoodsQueryReq req = CidGoodsQueryReq.newBuilder()
                .addAllItemId(productIds)
                .addAuthedAccountId(accountId)
                .setImporter(String.valueOf(accountId))
                .setIsDeleted(false)
                .setPage(1)
                .setSize(50)
                .build();
        final CidGoodsQueryResp resp = cpmTavernPlatformProxy.queryCidGoods(req);
        return GoodsConvertor.MAPPER.convertGoodsDetails2GoodsVos(resp.getGoodsDetailsList());
    }

    private List<Long> fetchProductIds(Long productId, List<Long> productIds) {
        final List<Long> ids = new ArrayList<>();
        ids.add(productId);
        ids.addAll(productIds);
        return ids.stream()
                .filter(Utils::isPositive)
                .collect(Collectors.toList());
    }

    private Tuple2<Integer, Integer> areProductsAndComponentValid(CommentComponentBo component, Map<Long, Integer> goodsRealStatusMap) {
        final List<Long> productIds = ListIdUtils.unionSingleAndList(component.getProductId(), component.getProductIdSet());
        final boolean productsValid = productIds.stream().map(x -> goodsRealStatusMap.getOrDefault(x, 0)).allMatch(Utils::isPositive);
        return Tuple.of(productsValid ? 1 : 0, productsValid && Utils.isPositive(component.getCommentId()) ? 1 : 0);
    }

    private void handleOperate(CommentComponentBo commentComponent, ArchiveCommentConversionComponentDetailVo componentDetailVo, Map<Integer, AccountBase> accountBaseMap, Integer accountId) {

        if (Objects.equals(componentDetailVo.getComponentType(), ComponentType.GOODS_VALUE)) {
            if (Objects.equals(commentComponent.getStatus(), CommentComponentStatus.NOT_TRIGGERED.getNumber())) {
                componentDetailVo.setCanOperate(ValidEnum.VALID.getCode());
                componentDetailVo.setCanResend(ValidEnum.INVALID.getCode());
            }
            if (Objects.equals(commentComponent.getStatus(), CommentComponentStatus.DELETED.getNumber())) {
                componentDetailVo.setCanOperate(ValidEnum.INVALID.getCode());
                componentDetailVo.setCanCopy(ValidEnum.INVALID.getCode());
                componentDetailVo.setCanDelete(ValidEnum.INVALID.getCode());
                componentDetailVo.setCanResend(ValidEnum.INVALID.getCode());
            }
            if (Objects.equals(commentComponent.getStatus(), CommentComponentStatus.UN_TOP.getNumber())) {
                componentDetailVo.setCanOperate(ValidEnum.VALID.getCode());
                componentDetailVo.setCanResend(ValidEnum.INVALID.getCode());
            }

            //稿件和动态的状态 0是正常 1是异常
            if (Objects.equals(commentComponent.getArchiveStatus(), ARCHIVE_OR_DYNAMIC_ABNORMAL) || Objects.equals(commentComponent.getDynamicStatus(), ARCHIVE_OR_DYNAMIC_ABNORMAL)) {
                componentDetailVo.setCanOperate(ValidEnum.VALID.getCode());
                componentDetailVo.setCanResend(ValidEnum.INVALID.getCode());
            }

            if (Objects.equals(commentComponent.getStatus(), CommentComponentStatus.TOP.getNumber())) {
                componentDetailVo.setCanOperate(ValidEnum.VALID.getCode());
                componentDetailVo.setCanResend(ValidEnum.INVALID.getCode());
            }
        } else {
            if (Objects.equals(commentComponent.getStatus(), CommentComponentStatus.NOT_TRIGGERED.getNumber())) {
                componentDetailVo.setCanResend(ValidEnum.INVALID.getCode());
            }
            if (Objects.equals(commentComponent.getStatus(), CommentComponentStatus.DELETED.getNumber())) {
                componentDetailVo.setCanOperate(ValidEnum.INVALID.getCode());
                componentDetailVo.setCanCopy(ValidEnum.INVALID.getCode());
                componentDetailVo.setCanDelete(ValidEnum.INVALID.getCode());
                componentDetailVo.setCanResend(ValidEnum.INVALID.getCode());
            }
            if (Objects.equals(commentComponent.getStatus(), CommentComponentStatus.UN_TOP.getNumber())) {

                componentDetailVo.setCanResend(ValidEnum.INVALID.getCode());

            }

            //稿件和动态的状态 0是正常 1是异常
            if (Objects.equals(commentComponent.getArchiveStatus(), ARCHIVE_OR_DYNAMIC_ABNORMAL) || Objects.equals(commentComponent.getDynamicStatus(), ARCHIVE_OR_DYNAMIC_ABNORMAL)) {

                componentDetailVo.setCanResend(ValidEnum.INVALID.getCode());
            }

            if (Objects.equals(commentComponent.getStatus(), CommentComponentStatus.TOP.getNumber())) {
                componentDetailVo.setCanResend(ValidEnum.INVALID.getCode());
            }
        }

        // 是否同代理
        AccountBase accountBaseOfComponent = accountBaseMap.getOrDefault(commentComponent.getAccountId(), AccountBase.getDefaultInstance());
        AccountBase accountBaseOfContext = accountBaseMap.getOrDefault(accountId, AccountBase.getDefaultInstance());
        if (!Objects.equals(accountBaseOfComponent.getDependencyAgentId(), accountBaseOfContext.getDependencyAgentId())) {
            componentDetailVo.setCanOperate(ValidEnum.INVALID.getCode());
            componentDetailVo.setCanCopy(ValidEnum.INVALID.getCode());
            componentDetailVo.setCanDelete(ValidEnum.INVALID.getCode());
            componentDetailVo.setCanResend(ValidEnum.INVALID.getCode());
        }
        if (!Objects.equals(commentComponent.getAccountId(), accountId)) {
            componentDetailVo.setCanOperate(ValidEnum.INVALID.getCode());
            componentDetailVo.setCanCopy(ValidEnum.INVALID.getCode());
        }

        if (Objects.isNull(componentDetailVo.getCanCopy())) {
            componentDetailVo.setCanCopy(ValidEnum.VALID.getCode());
        }
        if (Objects.isNull(componentDetailVo.getCanOperate())) {
            componentDetailVo.setCanOperate(ValidEnum.VALID.getCode());
        }
        if (Objects.isNull(componentDetailVo.getCanResend())) {
            componentDetailVo.setCanResend(ValidEnum.VALID.getCode());
        }
        if (Objects.isNull(componentDetailVo.getCanDelete())) {
            componentDetailVo.setCanDelete(ValidEnum.VALID.getCode());
        }
    }


    private CommentComponentBo handleShadowComponent(CommentComponent componentRecord) {
        String shadowComponentStr = componentRecord.getShadowComponent();
        CommentComponentBo commentComponentBo = new CommentComponentBo();

        commentComponentBo.setId(componentRecord.getId());
        commentComponentBo.setCtime(new Timestamp(componentRecord.getCtime()));
        commentComponentBo.setMtime(new Timestamp(componentRecord.getMtime()));
        commentComponentBo.setAid(componentRecord.getAid());
        commentComponentBo.setMid(componentRecord.getMid());
        commentComponentBo.setAccountId(componentRecord.getAccountId());
        commentComponentBo.setCustomerId(componentRecord.getCustomerId());
        commentComponentBo.setAgentId(componentRecord.getAgentId());
        commentComponentBo.setCampaignId(componentRecord.getCampaignId());
        commentComponentBo.setUnitId(componentRecord.getUnitId());
        commentComponentBo.setCreativeId(componentRecord.getCreativeId());
        commentComponentBo.setCommentId(componentRecord.getCommentId());
        commentComponentBo.setDynamicId(componentRecord.getDynamicId());

        commentComponentBo.setComponentType(Objects.equals(componentRecord.getComponentType(), ComponentType.UNRECOGNIZED) ? -1 : componentRecord.getComponentType().getNumber());
        commentComponentBo.setConversionUrlType(componentRecord.getConversionUrlType().getNumber());
        commentComponentBo.setConversionUrlPageId(componentRecord.getConversionUrlPageId());
        commentComponentBo.setConversionUrl(componentRecord.getConversionUrl());
        commentComponentBo.setConversionUrlText(componentRecord.getConversionUrlText());

        commentComponentBo.setIosUrlType(componentRecord.getIosUrlType().getNumber());
        commentComponentBo.setIosUrlPageId(componentRecord.getIosUrlPageId());
        commentComponentBo.setIosUrl(componentRecord.getIosUrl());
        commentComponentBo.setAndroidUrlType(componentRecord.getAndroidUrlType().getNumber());
        commentComponentBo.setAndroidUrlPageId(componentRecord.getAndroidUrlPageId());
        commentComponentBo.setAndroidUrl(componentRecord.getAndroidUrl());
        commentComponentBo.setIosSchemaUrl(componentRecord.getIosSchemaUrl());
        commentComponentBo.setAndroidSchemaUrl(componentRecord.getAndroidSchemaUrl());
        commentComponentBo.setTextLocation(componentRecord.getTextLocation().getNumber());
        commentComponentBo.setGamePlatformType(componentRecord.getGamePlatformType().getNumber());
        commentComponentBo.setGameBaseId(componentRecord.getGameBaseId());
        commentComponentBo.setBiliMiniGameMid(componentRecord.getBiliMiniGameMid());
        commentComponentBo.setIosAppPackageId(componentRecord.getIosAppPackageId());
        commentComponentBo.setAndroidAppPackageId(componentRecord.getAndroidAppPackageId());
        commentComponentBo.setSubPkg(componentRecord.getSubPkg());
        commentComponentBo.setClueType(componentRecord.getClueType());
        commentComponentBo.setClueData(componentRecord.getClueData());

        commentComponentBo.setCustomizedImpUrl(componentRecord.getCustomizedImpUrl());
        commentComponentBo.setCustomizedClickUrl(componentRecord.getCustomizedClickUrl());
        commentComponentBo.setGeneralCommentTextExt(componentRecord.getGeneralCommentTextExt());
        commentComponentBo.setContactType(componentRecord.getContactType());
        commentComponentBo.setAppSubType(componentRecord.getAppSubType().getNumber());
        commentComponentBo.setShadowComponent("");
        commentComponentBo.setProductIdSet(componentRecord.getProductIdSetList());
        commentComponentBo.setQualificationIds(JSON.toJSONString(componentRecord.getQualificationIdList()));
        commentComponentBo.setGeneralCommentText(componentRecord.getGeneralCommentText());

        commentComponentBo.setBizCode(componentRecord.getBizCode());
        commentComponentBo.setContactType(componentRecord.getContactType());

        commentComponentBo.setAuditStatus(componentRecord.getShadowAuditStatus());
        commentComponentBo.setReason(componentRecord.getReason());
        commentComponentBo.setStatus(componentRecord.getStatus().getNumber());
        commentComponentBo.setProductId(componentRecord.getProductId());
        commentComponentBo.setProductIdSet(componentRecord.getProductIdSetList());
        commentComponentBo.setArchiveStatus(componentRecord.getArchiveStatus());
        commentComponentBo.setDynamicStatus(componentRecord.getDynamicStatus());
        commentComponentBo.setIsAndroidAppDirect(componentRecord.getIsAndroidAppDirect());
        commentComponentBo.setGroupName(componentRecord.getGroupName());
        commentComponentBo.setGroupId(componentRecord.getGroupId());
        commentComponentBo.setBiliAppletUrl(componentRecord.getBiliAppletUrl());


        if (StringUtils.isNotBlank(shadowComponentStr)) {
            CommentComponentBo shadowCommentComponent = JSON.parseObject(shadowComponentStr, CommentComponentBo.class);
            commentComponentBo.setGeneralCommentText(shadowCommentComponent.getGeneralCommentText());
            commentComponentBo.setConversionUrlText(shadowCommentComponent.getConversionUrlText());
            commentComponentBo.setComponentType(shadowCommentComponent.getComponentType());
            commentComponentBo.setConversionUrlText(shadowCommentComponent.getConversionUrlText());
            commentComponentBo.setConversionUrlType(shadowCommentComponent.getConversionUrlType());
            commentComponentBo.setConversionUrlPageId(shadowCommentComponent.getConversionUrlPageId());
            commentComponentBo.setConversionUrl(shadowCommentComponent.getConversionUrl());
            commentComponentBo.setIosUrlType(shadowCommentComponent.getIosUrlType());
            commentComponentBo.setIosUrlPageId(shadowCommentComponent.getIosUrlPageId());
            commentComponentBo.setIosUrl(shadowCommentComponent.getIosUrl());
            commentComponentBo.setAndroidUrlType(shadowCommentComponent.getAndroidUrlType());
            commentComponentBo.setAndroidUrlPageId(shadowCommentComponent.getAndroidUrlPageId());
            commentComponentBo.setAndroidUrl(shadowCommentComponent.getAndroidUrl());
            commentComponentBo.setIosSchemaUrl(shadowCommentComponent.getIosSchemaUrl());
            commentComponentBo.setAndroidSchemaUrl(shadowCommentComponent.getAndroidSchemaUrl());
            commentComponentBo.setTextLocation(shadowCommentComponent.getTextLocation());
            commentComponentBo.setGamePlatformType(shadowCommentComponent.getGamePlatformType());
            commentComponentBo.setGameBaseId(shadowCommentComponent.getGameBaseId());
            commentComponentBo.setIosAppPackageId(shadowCommentComponent.getIosAppPackageId());
            commentComponentBo.setAndroidAppPackageId(shadowCommentComponent.getAndroidAppPackageId());
            commentComponentBo.setQualificationIds(shadowCommentComponent.getQualificationIds());
            commentComponentBo.setSubPkg(shadowCommentComponent.getSubPkg());
            commentComponentBo.setClueType(shadowCommentComponent.getClueType());
            commentComponentBo.setClueData(shadowCommentComponent.getClueData());
            commentComponentBo.setAutoFillText(shadowCommentComponent.getAutoFillText());
            commentComponentBo.setAutoFillLink(shadowCommentComponent.getAutoFillLink());
            commentComponentBo.setCustomizedImpUrl(shadowCommentComponent.getCustomizedImpUrl());
            commentComponentBo.setCustomizedClickUrl(shadowCommentComponent.getCustomizedClickUrl());
            commentComponentBo.setGeneralCommentTextExt(shadowCommentComponent.getGeneralCommentTextExt());
            commentComponentBo.setAppSubType(shadowCommentComponent.getAppSubType());
            commentComponentBo.setTextLocation(shadowCommentComponent.getTextLocation());
            commentComponentBo.setShadowComponent("");
            commentComponentBo.setAuditStatus(componentRecord.getShadowAuditStatus());
            commentComponentBo.setGroupId(shadowCommentComponent.getGroupId());
            CommentComponentExtraJsonBo commentComponentExtraJsonBo = JSON.parseObject(shadowCommentComponent.getExtra(), CommentComponentExtraJsonBo.class);
            commentComponentBo.setBiliAppletUrl(null == commentComponentExtraJsonBo ? null : commentComponentExtraJsonBo.getBiliAppletUrl());
        }
        return commentComponentBo;

    }

    public void processCmArchiveHasCommentComponent(List<CmArchiveV2Bo> cmArchiveV2BoList, List<Long> avids) {

        if (CollectionUtils.isEmpty(avids)) {
            return;
        }

        ComponentsReq componentsReq = ComponentsReq.newBuilder()
                .addAllAid(avids)
                .setPn(1)
                .setPs(20)
                .build();

        ComponentsReply components = cpmScvProxy.getComponents(componentsReq);
        Set<Long> hasCommentComponentArchive = components.getComponentsList().stream()
                .filter(commentComponent -> !Objects.equals(commentComponent.getStatus(), CommentComponentStatus.DELETED))
                .map(CommentComponent::getAid)
                .collect(Collectors.toSet());
        for (CmArchiveV2Bo cmArchiveV2Bo : cmArchiveV2BoList) {
            String avidStr = cmArchiveV2Bo.getUgc().getAvid();
            long avid = Long.parseLong(avidStr);
            if (hasCommentComponentArchive.contains(avid)) {
                cmArchiveV2Bo.setHasBindComment(IsValid.TRUE.getCode());
            } else {
                cmArchiveV2Bo.setHasBindComment(IsValid.FALSE.getCode());
            }
        }
    }

    public void processArchiveHasCommentComponent(List<ArchiveV2Vo> archiveV2Vos, List<Long> avids) {

        if (CollectionUtils.isEmpty(avids)) {
            return;
        }

        ComponentsReq componentsReq = ComponentsReq.newBuilder()
                .addAllAid(avids)
                .setPn(1)
                .setPs(20)
                .build();

        ComponentsReply components = cpmScvProxy.getComponents(componentsReq);
        Set<Long> hasCommentComponentArchive = components.getComponentsList().stream()
                .filter(commentComponent -> !Objects.equals(commentComponent.getStatus(), CommentComponentStatus.DELETED))
                .map(CommentComponent::getAid)
                .collect(Collectors.toSet());
        for (ArchiveV2Vo archiveV2Vo : archiveV2Vos) {
            String avidStr = archiveV2Vo.getAid();
            long avid = Long.parseLong(avidStr);
            if (hasCommentComponentArchive.contains(avid)) {
                archiveV2Vo.setHasBindComment(IsValid.TRUE.getCode());
            } else {
                archiveV2Vo.setHasBindComment(IsValid.FALSE.getCode());
            }
        }
    }

    private void processClueDataName(List<ArchiveCommentConversionComponentDetailVo> componentDetailVos) {
        Map<Long, ArchiveCommentConversionComponentDetailVo> formIdMap = componentDetailVos.stream()
                .filter(componentDetailVo -> Objects.equals(componentDetailVo.getClueType(), ClueTypeEnum.FORM_FLOAT.getCode()))
                .collect(Collectors.toMap(detailVo -> Long.parseLong(detailVo.getClueData()), Function.identity(), (i1, i2) -> i1));

        Map<Integer, ArchiveCommentConversionComponentDetailVo> wechatPackageIdMap = componentDetailVos.stream()
                .filter(componentDetailVo -> Objects.equals(componentDetailVo.getClueType(), ClueTypeEnum.WECHAT_FLOAT.getCode()))
                .collect(Collectors.toMap(detailVo -> Integer.parseInt(detailVo.getClueData()), Function.identity(), (i1, i2) -> i1));

        Map<Integer, ArchiveCommentConversionComponentDetailVo> miniGameIdMap = componentDetailVos.stream()
                .filter(componentDetailVo -> Objects.equals(componentDetailVo.getClueType(), ClueTypeEnum.MINI_GAME.getCode()))
                .collect(Collectors.toMap(detailVo -> Integer.parseInt(detailVo.getClueData()), Function.identity(), (i1, i2) -> i1));

        if (!formIdMap.isEmpty()) {
            List<MgkFormDto> formDtoList = soaMgkFormService.getFormDtoS(QueryFormParamDto.builder().formIds(new ArrayList<>(formIdMap.keySet())).build());
            Map<Long, MgkFormDto> mgkFormDtoMap = formDtoList.stream().collect(Collectors.toMap(MgkFormDto::getFormId, Function.identity()));
            for (ArchiveCommentConversionComponentDetailVo detailVo : formIdMap.values()) {
                long formId = Long.parseLong(detailVo.getClueData());
                MgkFormDto mgkFormDto = mgkFormDtoMap.get(formId);
                if (Objects.nonNull(mgkFormDto)) {
                    detailVo.setClueDataName(mgkFormDto.getName());
                } else {
                    detailVo.setClueDataName("--");
                }
            }
        }

        if (!wechatPackageIdMap.isEmpty()) {
            List<WechatPackageListDto> wechatPackageListDtos = soaMgkWechatPackageService.queryList(WechatPackageQueryDto.builder().ids(new ArrayList<>(wechatPackageIdMap.keySet())).build());
            Map<Integer, WechatPackageListDto> wechatPackageListDtoMap = wechatPackageListDtos.stream().collect(Collectors.toMap(WechatPackageListDto::getId, Function.identity()));
            for (ArchiveCommentConversionComponentDetailVo detailVo : wechatPackageIdMap.values()) {
                int packageId = Integer.parseInt(detailVo.getClueData());
                WechatPackageListDto wechatPackageListDto = wechatPackageListDtoMap.get(packageId);
                if (Objects.nonNull(wechatPackageListDto)) {
                    detailVo.setClueDataName(wechatPackageListDto.getName());
                } else {
                    detailVo.setClueDataName("--");
                }
            }
        }
        if (!miniGameIdMap.isEmpty()) {
            List<LauMiniGameDto> lauMiniGameDtos = lauMiniGameService.queryLauMiniGames(QueryLauMiniGameDto.builder().ids(new ArrayList<>(miniGameIdMap.keySet())).build());
            Map<Integer, LauMiniGameDto> lauMiniGameDtoMap = lauMiniGameDtos.stream().collect(Collectors.toMap(LauMiniGameDto::getId, Function.identity()));
            for (ArchiveCommentConversionComponentDetailVo detailVo : miniGameIdMap.values()) {
                int miniGameId = Integer.parseInt(detailVo.getClueData());
                LauMiniGameDto lauMiniGameDto = lauMiniGameDtoMap.get(miniGameId);
                if (Objects.nonNull(lauMiniGameDto)) {
                    detailVo.setClueDataName(lauMiniGameDto.getName());
                } else {
                    detailVo.setClueDataName("--");
                }
            }
        }


    }


    public ArchiveCommentConversionComponentDetailVo getCommentConversionComponentWithDelete(Context context, Long containerId, Integer containerType) {
        final CommentComponent oriComponent = archiveConversionComponentService.getCommentConversionComponent(containerId, containerType).getComponent();
        // 过滤删除组件

        CommentComponentBo component = handleShadowComponent(oriComponent);

        // 同集团同品牌的账号id
        //List<Integer> allAccountIds = adpCpcAccountService.querySameGroupAndSameBrandAccountIds(context.getAccountId());
        List<Integer> allAccountIds = accountV6Service.listAccountIdsByType(context.getAccountId(), SAME_GROUP_SAME_PRODUCT_VALUE);
        //List<Integer> accountIds = Arrays.asList(component.getAccountId());
        // 同代理的账号id
        //List<Integer> sameAgentAccountIds = adpCpcAccountService.querySameAgentAccountIds(context.getAccountId(), accountIds);
//        List<Integer> sameAgentAccountIds = accountV6Service.listAccountIdsByType(context.getAccountId(), SAME_AGENT_VALUE);
        // 获取组件账户的代理
        // 获取组件账户的代理
        List<AccountBase> accountBases = accountV6Service.listAccounts(Stream.of(component.getAccountId(), context.getAccountId()).collect(Collectors.toList()));
        Map<Integer, AccountBase> accountBaseMap = accountBases.stream().collect(Collectors.toMap(t -> t.getAccountId(), t -> t, (t1, t2) -> t1));
        AccountBase accountBaseOfContext = accountBaseMap.getOrDefault(context.getAccountId(), AccountBase.getDefaultInstance());
        AccountBase accountBaseOfComponent = accountBaseMap.getOrDefault(component.getAccountId(), AccountBase.getDefaultInstance());

        Assert.isTrue(allAccountIds.contains(component.getAccountId()) && Objects.equals(accountBaseOfContext.getDependencyAgentId(), accountBaseOfComponent.getDependencyAgentId()), "无法对已有置顶蓝链的稿件进行配置。");

        ArchiveBo archive = null;
        if (Objects.equals(containerType, CommentContainerTypeEnum.ARCHIVE.getNumber())) {
            archive = archiveService.getArchiveByAid(containerId);
        }
        // 获取建站落地页名称
        Map<Long, String> pageMap = archiveConversionComponentService.landingPage4ComponentList(Arrays.asList(component));
        // 获取iOS，Android应用包信息
        Map<Integer, AppPackageBo> appPackageBoMap = archiveConversionComponentService.appPackage4ComponentList(Arrays.asList(component));
        // 获取资质信息
        Map<Integer, String> qualificationMap = archiveConversionComponentService.qualifications4ComponentList(Arrays.asList(component));

        final Map<Long, DynamicBo> dynamicMap = biliDynamicService.fetchDynamics(Objects.equals(containerType, CommentContainerTypeEnum.DYNAMIC_VALUE) ? Collections.singletonList(containerId) : Collections.emptyList());
        ArchiveCommentConversionComponentDetailVo vo = ArchiveConversionComponentConverter.MAPPER.component2DetailVoV2(dynamicMap.get(containerId), archive, pageMap, appPackageBoMap.get(component.getIosAppPackageId()), appPackageBoMap.get(component.getAndroidAppPackageId()), generateQualificationNames(JSON.parseArray(component.getQualificationIds(), Integer.class), qualificationMap), component);
        final List<Long> productIds = fetchProductIds(component.getProductId(), component.getProductIdSet());
        if (!CollectionUtils.isEmpty(productIds)) {
            vo.setProducts(fetchGoods(context.getAccountId(), productIds));
        }
        vo.setCanOperate(IsValid.TRUE.getCode());
        // 表单浮层
        if (ClueTypeEnum.FORM_FLOAT.getCode().equals(component.getClueType())) {
            MgkFormDto formDto = soaMgkFormService.getFormDtoByFormId(Long.parseLong(component.getClueData()));
            String name = formDto == null ? "--" : formDto.getName();
            vo.setClueDataName(name);
            log.info("getFormDtoByFormId formDto [{}], name[{}]", formDto, name);
        }
        // 微信浮层
        else if (ClueTypeEnum.WECHAT_FLOAT.getCode().equals(component.getClueType())) {
            WechatPackageDto packageDto = soaMgkWechatPackageService
                    .getWechatPackageByWechatPackageId(Integer.parseInt(component.getClueData()));
            String name = packageDto == null ? "--" : packageDto.getName();
            vo.setClueDataName(name);
            log.info("getWechatPackageByWechatPackageId WechatPackage [{}], name [{}]", packageDto, name);
        }
        // 微信小游戏
        else if (ClueTypeEnum.MINI_GAME.getCode().equals(component.getClueType())) {
            LauMiniGameDto miniGameDto = lauMiniGameService.getLauMiniGameById(Integer.parseInt(component.getClueData()));
            String name = miniGameDto == null ? "--" : miniGameDto.getName();
            vo.setClueDataName(name);
        }
        return vo;
    }


}
