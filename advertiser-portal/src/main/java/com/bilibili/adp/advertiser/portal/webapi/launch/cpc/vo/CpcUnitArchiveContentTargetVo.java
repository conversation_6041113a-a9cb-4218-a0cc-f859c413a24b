/**
 * <AUTHOR>
 * @date 2018年7月11日
 */

package com.bilibili.adp.advertiser.portal.webapi.launch.cpc.vo;

import com.bilibili.adp.cpc.annotation.SerializeFilter;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonSerialize(include=JsonSerialize.Inclusion.NON_NULL)
public class CpcUnitArchiveContentTargetVo {
    @SerializeFilter
    @ApiModelProperty("粉丝关系: 0-不限, 1-该 UP主的粉丝, 2-未关注该 UP主的人 ")
    private Integer fans_relation;

    @ApiModelProperty("粉丝定向：包含他们的粉丝")
    private List<Long> include_theirs_fans;

    @SerializeFilter
    @ApiModelProperty("粉丝定向：排除他们的粉丝")
    private List<Long> exclude_theirs_fans;

    @SerializeFilter
    @ApiModelProperty("互动行为: 0-不限, 1-3天内, 1-7天内, 1-1个月内, 1-3个月内")
    private Integer interaction;

    @SerializeFilter
    @ApiModelProperty("浏览行为: 0-不限, 1-3天内, 1-7天内, 1-1个月内, 1-3个月内")
    private Integer browse;

    @ApiModelProperty("感兴趣的二级分区(视频分区兴趣)")
    private List<Integer> video_second_partition;

    @SerializeFilter
    @ApiModelProperty("系统推荐up主类型（0-无 1-核心up主 2-优选up主 3-高潜up主）")
    private Integer recommend_type;

    public static CpcUnitArchiveContentTargetVo getEmpty() {
        return CpcUnitArchiveContentTargetVo
                .builder()
                .browse(0)
                .fans_relation(0)
                .include_theirs_fans(Collections.emptyList())
                .exclude_theirs_fans(Collections.emptyList())
                .interaction(0)
                .video_second_partition(Collections.emptyList())
                .build();
    }
}
