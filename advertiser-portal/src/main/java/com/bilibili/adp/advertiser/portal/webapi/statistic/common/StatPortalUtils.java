package com.bilibili.adp.advertiser.portal.webapi.statistic.common;

import com.bilibili.adp.account.dto.AccountBaseDto;
import com.bilibili.adp.advertiser.portal.common.GroupType;
import com.bilibili.adp.advertiser.portal.webapi.flyPro.vo.FlyProStatisticVo;
import com.bilibili.adp.advertiser.portal.webapi.launch.cpc.common.CpcLaunchWebUtil;
import com.bilibili.adp.advertiser.portal.webapi.launch.cpc.common.FlyReportUtil;
import com.bilibili.adp.advertiser.portal.webapi.launch.cpc.vo.CpcReportCommonColumnVo;
import com.bilibili.adp.advertiser.portal.webapi.statistic.vo.LaunchDataDetailExcludeSomeGameLineVo;
import com.bilibili.adp.advertiser.portal.webapi.statistic.vo.LaunchDataDetailVo;
import com.bilibili.adp.common.enums.AdvertisingMode;
import com.bilibili.adp.common.enums.SalesType;
import com.bilibili.adp.common.enums.StatQueryTimeType;
import com.bilibili.adp.common.enums.StatQueryType;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.biz.bos.unit.QueryUnitBo;
import com.bilibili.adp.cpc.biz.services.campaign.api.ICpcCampaignService;
import com.bilibili.adp.cpc.biz.services.creative.api.ICpcCreativeService;
import com.bilibili.adp.cpc.biz.services.unit.api.ICpcUnitService;
import com.bilibili.adp.cpc.biz.services.unit.dto.CpcUnitDto;
import com.bilibili.adp.cpc.core.LaunchUnitV1Service;
import com.bilibili.adp.cpc.enums.ad.CampaignAdType;
import com.bilibili.adp.cpc.enums.ad.OcpcTargetEnum;
import com.bilibili.adp.cpc.enums.ad.PromotionPurposeType;
import com.bilibili.adp.cpc.biz.services.campaign.dto.CpcCampaignDto;
import com.bilibili.adp.launch.api.creative.dto.LauCreativeSceneBo;
import com.bilibili.adp.launch.api.creative.dto.LauUnitCreativeBo;
import com.bilibili.adp.launch.api.creative.dto.QueryLauCreativeSceneBo;
import com.bilibili.adp.launch.api.creative.dto.QueryLauUnitCreativeBo;
import com.bilibili.adp.resource.api.account.label.IAccountLabelService;
import com.bilibili.adp.resource.api.slot_group.IResSlotGroupService;
import com.bilibili.adp.resource.api.slot_group.SlotGroupSimpleDto;
import com.bilibili.report.platform.api.dto.StatLaunchDetailDto;
import com.bilibili.report.platform.api.dto.StatQueryDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@Slf4j
@RequiredArgsConstructor
public class StatPortalUtils {
    @Autowired
    private ICpcCampaignService cpcCampaignService;
    @Autowired
    private ICpcUnitService cpcUnitService;
    @Autowired
    private ICpcCreativeService cpcCreativeService;

    @Autowired
    private IResSlotGroupService slotGroupService;
    @Autowired
    private FlyReportUtil flyReportUtil;
    @Autowired
    private IAccountLabelService accountLabelService;
    @Value("${platform.launch.video_ecommerce.label:396}")
    private Integer videoEcommerceLabelId;

    private final LaunchUnitV1Service launchUnitV1Service;
    public List<LaunchDataDetailExcludeSomeGameLineVo> detailDtosToVosExcludeGameSomeLine(List<StatLaunchDetailDto> dtos, StatQueryDto query) {
        List<LaunchDataDetailVo> vos = detailDtosToVos(dtos,query);
        if(CollectionUtils.isEmpty(vos)){
            return Collections.emptyList();
        }
        return vos.stream().map(o->{
            LaunchDataDetailExcludeSomeGameLineVo vo = new LaunchDataDetailExcludeSomeGameLineVo();
            BeanUtils.copyProperties(o,vo);
            return vo;
        }).collect(Collectors.toList());

    }

    public List<LaunchDataDetailVo> detailDtosToVos(List<StatLaunchDetailDto> dtos, StatQueryDto query) {
        if (CollectionUtils.isEmpty(dtos)) {
            return Collections.emptyList();
        }

        List<Integer> campaignIds = dtos
                .stream()
                .map(StatLaunchDetailDto::getCampaignId)
                .filter(id -> id != null && id > 0)
                .distinct()
                .collect(Collectors.toList());

        List<Integer> unitIds = dtos
                .stream()
                .map(StatLaunchDetailDto::getUnitId)
                .filter(id -> id != null && id > 0)
                .distinct()
                .collect(Collectors.toList());

        Map<Integer, CpcCampaignDto> cMap = cpcCampaignService.getCampaignMapInCampaignIds(campaignIds);

        Map<Integer, String> campaignMap = cMap.entrySet()
                .stream()
                .collect(Collectors.toMap(Map.Entry::getKey, e -> e.getValue().getCampaignName()));

        Map<Integer, Integer> campaignAdTypeMap = cMap.entrySet()
                .stream()
                .collect(Collectors.toMap(Map.Entry::getKey, e -> e.getValue().getAdType()));

        // 获取单元报表对象信息
        //List<CpcUnitDto> unitReportDtos = CollectionUtils.isEmpty(unitIds) ? Collections.emptyList() : cpcUnitService.queryCpcUnit(QueryCpcUnitDto.builder().unitIds(unitIds).build());
        List<CpcUnitDto> unitReportDtos = CollectionUtils.isEmpty(unitIds) ? Collections.emptyList() : launchUnitV1Service.listUnits(QueryUnitBo.builder().unitIds(unitIds).build());

        // 获取单元对象
        Map<Integer, CpcUnitDto> unitMap = CollectionUtils.isEmpty(unitReportDtos) ? Collections.emptyMap() :
                unitReportDtos.stream()
                        .collect(Collectors.toMap(CpcUnitDto::getUnitId, u -> u));

        // 获取广告位组ID
        List<Integer> sourceIds = CollectionUtils.isEmpty(unitReportDtos) ? Collections.emptyList() :
                unitReportDtos.stream()
                        .map(CpcUnitDto::getSlotGroup).distinct().collect(Collectors.toList());

        // 获取广告位组名称
        Map<Integer, String> sourceNameMap = CollectionUtils.isEmpty(sourceIds) ? Collections.emptyMap() :
                slotGroupService.getSlotGroupByIds(sourceIds)
                        .stream()
                        .collect(Collectors.toMap(SlotGroupSimpleDto::getId, SlotGroupSimpleDto::getSlotGroupName));

        final List<Integer> unitIdList = dtos.stream()
                .map(StatLaunchDetailDto::getUnitId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        final List<Long> creativeIdList = dtos.stream()
                .map(StatLaunchDetailDto::getCreativeId)
                .filter(Objects::nonNull)
                .distinct()
                .map(x -> (long) x)
                .collect(Collectors.toList());

        final Map<String, List<LauCreativeSceneBo>> creativeSceneMap = cpcCreativeService.getLauCreativeScenes(QueryLauCreativeSceneBo.builder()
                .unitIdList(unitIdList)
                .creativeIdList(creativeIdList)
                .build())
                .stream()
                .collect(Collectors.groupingBy(x -> genUnitCreativeKey(x.getUnitId(), x.getCreativeId())));

        log.info("查询创意场景, 单元列表={}, 创意列表={}, 查询结果={}", unitIdList, creativeIdList, creativeSceneMap.values());

        final Map<String, LauUnitCreativeBo> unitCreativeMap = cpcCreativeService.getLauUnitCreatives(QueryLauUnitCreativeBo.builder()
                .unitIdList(unitIdList)
                .creativeIdList(creativeIdList)
                .build())
                .stream()
                .collect(Collectors.toMap(x -> genUnitCreativeKey(x.getUnitId(), (long) x.getCreativeId()), Function.identity()));

        log.info("查询单元创意, 单元列表={}, 创意列表={}, 查询结果={}", unitIdList, creativeIdList, unitCreativeMap.values());

        return dtos.stream()
                .map(dto -> {
                    Integer activityPagePullUpCount = 0;
                    BigDecimal activityPagePullUpCost = BigDecimal.ZERO;
                    BigDecimal activityPagePullUpRate = BigDecimal.ZERO;

                    Integer dynamicDetailPageBrowseCount = 0;
                    BigDecimal dynamicDetailPageBrowseCost = BigDecimal.ZERO;
                    BigDecimal dynamicDetailPageBrowseRate = BigDecimal.ZERO;

                    CpcCampaignDto cpcCampaignDto = cMap.get(dto.getCampaignId());
                    //不是账号维度查询
                    if (!StatQueryType.ACCOUNT.equals(query.getType())&& cpcCampaignDto != null) {
                        Integer ppt = cpcCampaignDto.getPromotionPurposeType();
                        activityPagePullUpCount = flyReportUtil.getActivityPagePullUpCount(ppt,dto.getClickCount());
                        activityPagePullUpCost =  flyReportUtil.getActivityPagePullUpCost(ppt,dto.getClickCount(),dto.getCost());
                        activityPagePullUpRate = flyReportUtil.getActivityPagePullUpRate(ppt,dto.getClickCount(),dto.getShowAccount());
                        dynamicDetailPageBrowseCount = flyReportUtil.getDynamicDetailPageBrowseCount(ppt,dto.getClickCount());
                        dynamicDetailPageBrowseCost =  flyReportUtil.getDynamicDetailPageBrowseCost(ppt,dto.getClickCount(),dto.getCost());
                        dynamicDetailPageBrowseRate =  flyReportUtil.getDynamicDetailPageBrowseRate(ppt,dto.getClickCount(),dto.getShowAccount());
                    }

                    CpcUnitDto unit = unitMap.get(dto.getUnitId());

                    OcpcTargetEnum ocpxStateDescEnum = unit != null ? OcpcTargetEnum.getByCode(unit.getOcpxTargetTwo()) : null;
                    final LaunchDataDetailVo vo = LaunchDataDetailVo.builder()
                            //若时间维度是ALL，则date填充为开始时间~结束时间
                            .date(StatQueryTimeType.ALL.equals(query.getTimeType()) ? getTimeRangeStr(query.getStartDate(), query.getEndDate()) : getDate(dto.getDate()))
                            .time(query.getTimeType() == StatQueryTimeType.HOUR ? getTime(dto.getDate()) : "")
                            .campaignName(campaignMap.getOrDefault(dto.getCampaignId(), ""))
                            .campaignId(dto.getCampaignId() != null && dto.getCampaignId() > 0 ? dto.getCampaignId() : null)
                            .campaignAdType(campaignAdTypeMap.get(dto.getCampaignId())==null ? "" : CampaignAdType.getByCode(campaignAdTypeMap.get(dto.getCampaignId())).getDesc())
                            .unitName(unitMap.getOrDefault(dto.getUnitId(), CpcUnitDto.builder().unitName("").build()).getUnitName())
                            .unitId(dto.getUnitId() != null && dto.getUnitId() > 0 ? dto.getUnitId() : null)
                            .creativeName(dto.getCreativeId() != null && dto.getCreativeId() > 0 ? String.valueOf(dto.getCreativeId()) : "")
                            .creativeId(dto.getCreativeId())
                            .slotGroupName(sourceNameMap.getOrDefault(unitMap.getOrDefault(dto.getUnitId(), new CpcUnitDto()).getSlotGroup(), ""))
                            .promotionType(Optional.ofNullable(cMap.get(dto.getCampaignId()))
                                    .map(c -> c.getPromotionPurposeType() > 0 ? PromotionPurposeType.getByCode(c.getPromotionPurposeType()).getDesc() : "").orElse(""))
                            .showCount(CpcReportCommonColumnVo.genLong(Long.valueOf(dto.getShowAccount())))
                            .clickCount(CpcReportCommonColumnVo.genInteger(dto.getClickCount()))
                            .clickRate(dto.getClickRate() == null ? "0%" : dto.getClickRate().setScale(2, RoundingMode.HALF_UP).toString() + "%")
                            .cost(CpcReportCommonColumnVo.genBigDecimal(dto.getCost()))
                            .costPerClick(CpcReportCommonColumnVo.genBigDecimal(dto.getCostPerClick()))
                            .averageCostPerThousand(CpcReportCommonColumnVo.genBigDecimal(dto.getAverageCostPerThousand()))
                            .orderAddCount(CpcReportCommonColumnVo.genInteger((dto.getOrderAddCount())))
                            .orderAddPrice(CpcReportCommonColumnVo.genBigDecimal((dto.getOrderAddPrice())))
                            .orderSubmitRate(CpcLaunchWebUtil.getPercentRate(dto.getOrderAddCount(), dto.getClickCount()))
                            .orderSubmitCost(CpcReportCommonColumnVo.genAvgCost(dto.getCost(), dto.getOrderAddCount()))
                            .firstOrderPlaceCount(CpcReportCommonColumnVo.genInteger((dto.getFirstOrderPlaceCount())))
                            .firstOrderPlaceAmount(CpcReportCommonColumnVo.genBigDecimal((dto.getFirstOrderPlaceAmount())))
                            .firstOrderPlaceRate(CpcLaunchWebUtil.getPercentRate(dto.getFirstOrderPlaceCount(), dto.getClickCount()))
                            .firstOrderPlaceCost(CpcReportCommonColumnVo.genAvgCost(dto.getCost(), dto.getFirstOrderPlaceCount()))
                            .firstDayPayCount(CpcReportCommonColumnVo.genInteger((dto.getFirstDayPayCount())))
                            .firstDayPayAmount(CpcReportCommonColumnVo.genBigDecimal((dto.getFirstDayPayAmount())))
                            .firstDayPayRate(dto.getFirstDayPayRate() == null ? "0%" : dto.getFirstDayPayRate().toString() + "%")
                            .firstDayPayCost(CpcLaunchWebUtil.getCostPerInteger(dto.getCost(),dto.getFirstDayPayCount()))
                            .firstDayPayROI(CpcLaunchWebUtil.divideCost(dto.getFirstDayPayAmount(),dto.getCost(),4))
                            .newFirstDayPayCount(CpcReportCommonColumnVo.genInteger((dto.getNewFirstDayPayCount())))
                            .newFirstDayPayAmount(CpcReportCommonColumnVo.genBigDecimal((dto.getNewFirstDayPayAmount())))
                            .newFirstDayPayRate(dto.getNewFirstDayPayRate() == null ? "0%" : dto.getNewFirstDayPayRate().toString() + "%")
                            .newFirstDayPayCost(CpcLaunchWebUtil.getCostPerInteger(dto.getCost(),dto.getNewFirstDayPayCount()))
                            .newFirstDayPayRoi(CpcLaunchWebUtil.divideCost(dto.getNewFirstDayPayAmount(),dto.getCost(),4))
                            //定义修改
                            .orderCount(Optional.ofNullable(dto.getOrderCount()).orElse(0))
                            .orderPayment(Optional.ofNullable(dto.getOrderPayment()).orElse(BigDecimal.ZERO))
                            // 新增应用内首次付费
                            .orderFirstCount(CpcReportCommonColumnVo.genInteger(dto.getOrderFirstCount()))
                            .orderFirstPayment(CpcReportCommonColumnVo.genBigDecimal(dto.getOrderFirstPayment()))
                            .goodsConversionRate(dto.getGoodsConversionRate() == null ? "0%" : dto.getGoodsConversionRate().toString() + "%")
                            .goodsRoi(CpcReportCommonColumnVo.genBigDecimal(dto.getGoodsROI()))
                            .activateCount(CpcReportCommonColumnVo.genInteger(dto.getActivateCount()))
                            .reserveCount(CpcReportCommonColumnVo.genInteger(dto.getReserveCount()))
                            .fanFollowCount(CpcReportCommonColumnVo.genInteger(dto.getFanFollowCount()))
                            .fanWhisperCount(CpcReportCommonColumnVo.genInteger(dto.getFanWhisperCount()))
                            .iosActivateCount(CpcReportCommonColumnVo.genInteger(dto.getIosActivateCount()))
                            .androidActivateCount(CpcReportCommonColumnVo.genInteger(dto.getAndroidActivateCount()))
                            .registerCount(CpcReportCommonColumnVo.genInteger(dto.getRegisterCount()))
                            .costPerIncreaseFans(CpcLaunchWebUtil.getCostPerInteger(dto.getCost(), dto.getFanFollowCount()))
                            .costPerAppActivate(CpcLaunchWebUtil.getCostPerInteger(dto.getCost(), CpcLaunchWebUtil.addInt(dto.getAndroidActivateCount(), dto.getIosActivateCount())))
                            .costPerGameActivate(CpcLaunchWebUtil.getCostPerInteger(dto.getCost(), dto.getActivateCount()))
                            .costPerGameReserve(CpcLaunchWebUtil.getCostPerInteger(dto.getCost(), dto.getReserveCount()))
                            .costPerRegister(CpcLaunchWebUtil.getCostPerInteger(dto.getCost(), dto.getRegisterCount()))
                            .appActivateRate(CpcLaunchWebUtil.getPercentRate(CpcLaunchWebUtil.addInt(dto.getAndroidActivateCount(), dto.getIosActivateCount()), dto.getClickCount()))
                            .fansFollowRate(CpcLaunchWebUtil.getPercentRate(dto.getFanFollowCount(), dto.getClickCount()))
                            .gameActivateRate(CpcLaunchWebUtil.getPercentRate(dto.getActivateCount(), dto.getClickCount()))
                            .gameReserveRate(CpcLaunchWebUtil.getPercentRate(dto.getReserveCount(), dto.getClickCount()))
                            .registerRate(CpcLaunchWebUtil.getPercentRate(dto.getRegisterCount(), dto.getClickCount()))
                            .formSubmitCount(CpcReportCommonColumnVo.genInteger(dto.getFormSubmitCount()))
                            .formSubmitRate(CpcLaunchWebUtil.getPercentRate(dto.getFormSubmitCount(), dto.getClickCount()))
                            .costPerFormSubmit(CpcLaunchWebUtil.getCostPerInteger(dto.getCost(), dto.getFormSubmitCount()))
                            .androidDownloadCount(CpcReportCommonColumnVo.genInteger(dto.getAndroidDownloadCount()))
                            .androidDownloadRate(CpcLaunchWebUtil.getPercentRate(dto.getAndroidDownloadCount(), dto.getClickCount()))
                            .costPerAndroidDownload(CpcLaunchWebUtil.getCostPerInteger(dto.getCost(), dto.getAndroidDownloadCount()))
                            .androidInstallCount(CpcReportCommonColumnVo.genInteger(dto.getAndroidInstallCount()))
                            .androidInstallRate(CpcLaunchWebUtil.getPercentRate(dto.getAndroidInstallCount(), dto.getClickCount()))

                            .costPerAndroidInstall(CpcLaunchWebUtil.getCostPerInteger(dto.getCost(), dto.getAndroidInstallCount()))
                            .validClueCount(CpcReportCommonColumnVo.genInteger(dto.getClueValidCount()))
                            .validClueCost(CpcReportCommonColumnVo.genAvgCost(dto.getCost(), dto.getClueValidCount()))
                            .validClueRatio(CpcLaunchWebUtil.getPercentRate(dto.getClueValidCount(), dto.getFormSubmitCount()))
                            .retentionCount(CpcReportCommonColumnVo.genInteger(dto.getRetention()))
                            .appCallupCount(CpcReportCommonColumnVo.genInteger(dto.getAppCallUpCount()))
                            .appCallupCost(CpcReportCommonColumnVo.genAvgCost(dto.getCost(), dto.getAppCallUpCount()))
                            .appCallupRate(CpcLaunchWebUtil.getPercentRate(dto.getAppCallUpCount(), dto.getClickCount()))
//                            .lpCallupCount(CpcReportCommonColumnVo.genInteger(dto.getLpCallUpCount()))
//                            .lpCallupCost(CpcReportCommonColumnVo.genAvgCost(dto.getCost(), dto.getLpCallUpCount()))
//                            .lpCallupRate(CpcReportCommonColumnVo.genRate(dto.getLpCallUpCount(), dto.getClickCount()))
                            .androidGameCenterActivationCost(CpcReportCommonColumnVo.genAvgCost(dto.getCost(), dto.getAndroidGameCenterActivationCount()))
                            .androidGameCenterActivationCount(CpcReportCommonColumnVo.genInteger(dto.getAndroidGameCenterActivationCount()))
                            .androidGameCenterActivationRatio(CpcLaunchWebUtil.getPercentRate(dto.getAndroidGameCenterActivationCount(), dto.getClickCount()))
                            .formPaidCost(CpcReportCommonColumnVo.genAvgCost(dto.getCost(), dto.getFormPaidCount()))
                            .formPaidCount(CpcReportCommonColumnVo.genInteger(dto.getFormPaidCount()))
                            .formPaidRate(CpcLaunchWebUtil.getPercentRate(dto.getFormPaidCount(), dto.getClickCount()))
                            .lpCallUpSuccessCount(CpcReportCommonColumnVo.genInteger(dto.getLpCallUpSuccessCount()))
                            .lpCallUpSuccessCost(CpcReportCommonColumnVo.genAvgCost(dto.getCost(), dto.getLpCallUpSuccessCount()))
                            .lpCallUpSuccessRate(CpcLaunchWebUtil.getPercentRate(dto.getLpCallUpSuccessCount(), dto.getClickCount()))
                            .lpCallUpSuccessStayCount(CpcReportCommonColumnVo.genInteger(dto.getLpCallUpSuccessStayCount()))
                            .lpCallUpSuccessStayCost(CpcReportCommonColumnVo.genAvgCost(dto.getCost(), dto.getLpCallUpSuccessStayCount()))
                            .lpCallUpSuccessStayRate(CpcLaunchWebUtil.getPercentRate(dto.getLpCallUpSuccessStayCount(), dto.getClickCount()))
                            .accountSubscribeCount(CpcReportCommonColumnVo.genInteger(dto.getAccountSubscribeCount()) + CpcReportCommonColumnVo.genInteger(dto.getEnterpriseAccountSubscribeCount()))
                            .accountSubscribeCost(CpcReportCommonColumnVo.genAvgCost(dto.getCost(), dto.getAccountSubscribeCount()))
                            .accountSubscribeRate(CpcLaunchWebUtil.getPercentRate(dto.getAccountSubscribeCount(), dto.getClickCount()))
                            .dynamicDetailPageBrowseCount(dynamicDetailPageBrowseCount)
                            .dynamicDetailPageBrowseCost(dynamicDetailPageBrowseCost)
                            .dynamicDetailPageBrowseRate(dynamicDetailPageBrowseRate)
                            .playCount(CpcReportCommonColumnVo.genInteger(dto.getPlayCount()))
                            .playCost(CpcReportCommonColumnVo.genBigDecimal(dto.getPlayCost()))
                            .playShowCount(CpcReportCommonColumnVo.genInteger(dto.getPlayShowCount()))
                            .costPerPlayCount(CpcReportCommonColumnVo.genAvgCost(dto.getPlayCost(), dto.getPlayCount()))
                            .playRate(CpcLaunchWebUtil.getPercentRate(dto.getPlayCount(), dto.getPlayShowCount()))
                            .play2FansRate(CpcLaunchWebUtil.getPercentRate(dto.getFanFollowCount(), dto.getPlayCount()))
                            .underBoxLinkClickCount(CpcReportCommonColumnVo.genInteger(dto.getUnderBoxLinkClickCount()))
                            .costPerUnderBoxLinkClickCount(CpcReportCommonColumnVo.genAvgCost(dto.getCost(), dto.getUnderBoxLinkClickCount()))
                            .underBoxLinkClickRate(CpcLaunchWebUtil.getPercentRate(dto.getUnderBoxLinkClickCount(), dto.getPlayCount()))
                            .firstCommentCopyCount(CpcReportCommonColumnVo.genInteger(dto.getFirstCommentCopyCount()))
                            .costPerFirstCommentCopyCount(CpcReportCommonColumnVo.genAvgCost(dto.getCost(), dto.getFirstCommentCopyCount()))
                            .firstCommentCopyRate(CpcLaunchWebUtil.getPercentRate(dto.getFirstCommentCopyCount(), dto.getPlayCount()))
                            .commentClick(CpcReportCommonColumnVo.genInteger(dto.getCommentClick()))
                            .androidGameCenterPaymentInAppCount(CpcReportCommonColumnVo.genInteger(dto.getAndroidGameCenterPaymentInAppCount()))
                            .androidGameCenterPaymentInAppAmount(CpcReportCommonColumnVo.genBigDecimal((dto.getAndroidGameCenterPaymentInAppAmount())))
                            .androidGameCenterFirstPaymentInAppCount(CpcReportCommonColumnVo.genInteger(dto.getAndroidGameCenterFirstPaymentInAppCount()))
                            .androidGameCenterFirstPaymentInAppAmount(CpcReportCommonColumnVo.genBigDecimal((dto.getAndroidGameCenterFirstPaymentInAppAmount())))
                            .androidGameCenterFirstPaymentInAppCost(CpcReportCommonColumnVo.genAvgCost(dto.getCost(), dto.getAndroidGameCenterFirstPaymentInAppCount()))
                            .androidGameCenterFirstPaymentInAppRate(CpcLaunchWebUtil.getPercentRate(dto.getAndroidGameCenterFirstPaymentInAppCount(), dto.getAndroidGameCenterActivationCount()))
                            .keyBehaviorCount(CpcReportCommonColumnVo.genInteger(dto.getKeyBehaviorCount()))
                            .keyBehaviorCost(CpcReportCommonColumnVo.genAvgCost(dto.getCost(), dto.getKeyBehaviorCount()))
                            .keyBehaviorRate(CpcLaunchWebUtil.getPercentRate(dto.getKeyBehaviorCount(), dto.getClickCount()))
                            .activityPagePullUpCount(activityPagePullUpCount)
                            .activityPagePullUpCost(activityPagePullUpCost)
                            .activityPagePullUpRate(activityPagePullUpRate)
                            .wxCopyCount(CpcReportCommonColumnVo.genInteger(dto.getWxCopyCount()))
                            .wxCopyCost(CpcLaunchWebUtil.getCostPerInteger(dto.getCost(), dto.getWxCopyCount()))
                            .wxCopyRate(CpcLaunchWebUtil.getPercentRate(dto.getWxCopyCount(), dto.getClickCount()))
                            .applyCount(CpcReportCommonColumnVo.genInteger(dto.getApplyCount()))
                            .applyCost(CpcLaunchWebUtil.getCostPerInteger(dto.getCost(), dto.getApplyCount()))
                            .applyRate(CpcLaunchWebUtil.getPercentRate(dto.getApplyCount(), dto.getClickCount()))
                            .creditCount(CpcReportCommonColumnVo.genInteger(dto.getCreditCount()))
                            .creditCost(CpcLaunchWebUtil.getCostPerInteger(dto.getCost(), dto.getCreditCount()))
                            .creditRate(CpcLaunchWebUtil.getPercentRate(dto.getCreditCount(), dto.getClickCount()))
                            .withdrawDepositsCount(CpcReportCommonColumnVo.genInteger(dto.getWithdrawDepositsCount()))
                            .withdrawDepositsCost(CpcLaunchWebUtil.getCostPerInteger(dto.getCost(), dto.getWithdrawDepositsCount()))
                            .withdrawDepositsRate(CpcLaunchWebUtil.getPercentRate(dto.getWithdrawDepositsCount(), dto.getClickCount()))
                            .videoLikeCount(CpcReportCommonColumnVo.genInteger(dto.getVideoLikeCount()))
                            .costPerVideoLike(CpcLaunchWebUtil.getCostPerInteger(dto.getCost(), dto.getVideoLikeCount()))
                            .videoLikeRate(CpcLaunchWebUtil.getPercentRate(dto.getVideoLikeCount(), dto.getShowAccount()))
                            .offerTypeDesc(unit != null ? SalesType.getByCode(unit.getSalesType()).getName() : "")
                            .ocpxStateDesc(ocpxStateDescEnum != null ? ocpxStateDescEnum.getDesc() : "-")
                            .paidIn24hCount(CpcReportCommonColumnVo.genInteger((dto.getPaidIn24hCount())))
                            .paidIn24hPrice(dto.getPaidIn24hPrice())
                            .paidIn24hCost(CpcLaunchWebUtil.getCostPerInteger(dto.getCost(),dto.getPaidIn24hCount()))
                            .paidIn24hROI(CpcLaunchWebUtil.divideCost(dto.getPaidIn24hPrice(),dto.getCost(),4))
                            .paidIn7dCount(CpcReportCommonColumnVo.genInteger((dto.getPaidIn7dCount())))
                            .paidIn7dPrice(dto.getPaidIn7dPrice())
                            .paidIn7dCost(CpcLaunchWebUtil.getCostPerInteger(dto.getCost(),dto.getPaidIn7dCount()))
                            .paidIn7dROI(CpcLaunchWebUtil.divideCost(dto.getPaidIn7dPrice(),dto.getCost(),4))
                            .wxAddFansCount(CpcReportCommonColumnVo.genInteger(dto.getWxAddFansCount()))
                            .wxAddFansRate(CpcLaunchWebUtil.getPercentRate(dto.getWxAddFansCount(), dto.getClickCount()))
                            .wxAddFansCost(CpcLaunchWebUtil.getCostPerInteger(dto.getCost(), dto.getWxAddFansCount()))
                            .commentShowCount(CpcReportCommonColumnVo.genInteger(dto.getCommentShowCount()))
                            .commentShowRate(CpcLaunchWebUtil.getPercentRate(dto.getCommentShowCount(), dto.getPlayCount()))
                            .commentShowClickRate(CpcLaunchWebUtil.getPercentRate(dto.getCommentClick(), dto.getCommentShowCount()))
                            .commentCallUpCount(CpcReportCommonColumnVo.genInteger(dto.getCommentCallUpCount()))
                            .commentCallUpRate(CpcLaunchWebUtil.getPercentRate(dto.getCommentCallUpCount(), dto.getCommentClick()))
                            .playCallUpRate(CpcLaunchWebUtil.getPercentRate(dto.getLpCallUpSuccessCount(), dto.getPlayCount()))
                            .liveEntryCount(CpcReportCommonColumnVo.genInteger(dto.getLiveEntryCount()))
                            .liveEntryCost(CpcLaunchWebUtil.getCostPerInteger(dto.getCost(), dto.getLiveEntryCount()))
                            .liveEntryRate(CpcLaunchWebUtil.getPercentRate(dto.getLiveEntryCount(), dto.getShowAccount()))
                            .liveReserveCount(CpcReportCommonColumnVo.genInteger(dto.getLiveReserveCount()))
                            .liveReserveCost(CpcLaunchWebUtil.getCostPerInteger(dto.getCost(), dto.getLiveReserveCount()))
                            .liveReserveRate(CpcLaunchWebUtil.getPercentRate(dto.getLiveReserveCount(), dto.getClickCount()))
                            .liveCallUpCount(CpcReportCommonColumnVo.genInteger(dto.getLiveCallUpCount()))
                            .liveCallUpCost(CpcLaunchWebUtil.getCostPerInteger(dto.getCost(), dto.getLiveCallUpCount()))
                            .liveCallUpRate(CpcLaunchWebUtil.getPercentRate(dto.getLiveCallUpCount(), dto.getLiveEntryCount()))
                            .androidGameActivePaidIn24hCount(CpcReportCommonColumnVo.genInteger(dto.getAndroidGameActivePaidIn24hCount()))
                            .androidGameActivePaidIn24hAmount(CpcReportCommonColumnVo.genBigDecimal((dto.getAndroidGameActivePaidIn24hAmount())))
                            .androidGameActivePaidIn24hCost(CpcLaunchWebUtil.getCostPerInteger(dto.getCost(), dto.getAndroidGameActivePaidIn24hCount()))
                            .androidGameActivePaidIn24hRoi(CpcLaunchWebUtil.divideCost(dto.getAndroidGameActivePaidIn24hAmount(),dto.getCost(),4))
                            .androidGameActivePaidIn7dCount(CpcReportCommonColumnVo.genInteger(dto.getAndroidGameActivePaidIn7dCount()))
                            .androidGameActivePaidIn7dAmount(CpcReportCommonColumnVo.genBigDecimal((dto.getAndroidGameActivePaidIn7dAmount())))
                            .androidGameActivePaidIn7dCost(CpcLaunchWebUtil.getCostPerInteger(dto.getCost(), dto.getAndroidGameActivePaidIn7dCount()))
                            .androidGameActivePaidIn7dRoi(CpcLaunchWebUtil.divideCost(dto.getAndroidGameActivePaidIn7dAmount(),dto.getCost(),4))
                            .androidGameActivePaidIn1dCount(CpcReportCommonColumnVo.genInteger(dto.getAndroidGameActivePaidIn1dCount()))
                            .androidGameActivePaidIn1dAmount(CpcReportCommonColumnVo.genBigDecimal((dto.getAndroidGameActivePaidIn1dAmount())))
                            .androidGameActivePaidIn1dCost(CpcLaunchWebUtil.getCostPerInteger(dto.getCost(), dto.getAndroidGameActivePaidIn1dCount()))
                            .androidGameActivePaidIn1dRoi(CpcLaunchWebUtil.divideCost(dto.getAndroidGameActivePaidIn1dAmount(),dto.getCost(),4))
                            .androidGameActivePaidIn1dRate(CpcLaunchWebUtil.getPercentRate(dto.getAndroidGameActivePaidIn1dCount(), dto.getAndroidGameCenterActivationCount()))
                            .newFirstDayPayCount(CpcReportCommonColumnVo.genInteger((dto.getNewFirstDayPayCount())))
                            .newFirstDayPayAmount(CpcReportCommonColumnVo.genBigDecimal((dto.getNewFirstDayPayAmount())))
                            .newFirstDayPayRate(dto.getNewFirstDayPayRate() == null ? "0%" : dto.getNewFirstDayPayRate().toString() + "%")
                            .newFirstDayPayCost(CpcLaunchWebUtil.getCostPerInteger(dto.getCost(),dto.getNewFirstDayPayCount()))
                            .newFirstDayPayRoi(CpcLaunchWebUtil.divideCost(dto.getNewFirstDayPayAmount(),dto.getCost(),4))
                            .firstWithdrawCount(CpcReportCommonColumnVo.genInteger(dto.getFirstWithdrawCount()))
                            .firstWithdrawCost(CpcLaunchWebUtil.getCostPerInteger(dto.getCost(), dto.getFirstWithdrawCount()))
                            .firstWithdrawRate(CpcLaunchWebUtil.getPercentRate(dto.getFirstWithdrawCount(), dto.getClickCount()))
                            .componentClickCount(CpcReportCommonColumnVo.genInteger(dto.getComponentClickCount()))
                            .componentClickCost(CpcLaunchWebUtil.getCostPerInteger(dto.getCost(), dto.getComponentClickCount()))
                            .componentClickRate(CpcLaunchWebUtil.getPercentRate(dto.getComponentClickCount(), dto.getPlayCount()))
                            .liveGameCardShowCount(CpcReportCommonColumnVo.genInteger(dto.getLiveGameCardShowCount()))
                            .liveGameCardClickCount(CpcReportCommonColumnVo.genInteger(dto.getLiveGameCardClickCount()))
                            .liveGameCardClickRate(CpcLaunchWebUtil.getPercentRate(dto.getLiveGameCardClickCount(), dto.getLiveGameCardShowCount()))
                            .liveGameCardClickCost(CpcLaunchWebUtil.getCostPerInteger(dto.getCost(), dto.getLiveGameCardClickCount()))
                            .uvNum(dto.getUvNum())
                            .newVisitorUvNum(dto.getNewVisitorUvNum())
                            .payBuyerUv(dto.getPayBuyerUv())
                            .cltUvNum(dto.getCltUvNum())
                            .seLeadConv(dto.getSeLeadConv())
                            .addUvNum(dto.getAddUvNum())
                            .build();

                    if (dto.getCreativeId() != null) {
                        final LauUnitCreativeBo lauUnitCreativeBo = unitCreativeMap.get(genUnitCreativeKey(dto.getUnitId(), (long) dto.getCreativeId()));
                        if (lauUnitCreativeBo != null) {
                            vo.setAdvertisingModeDesc(Objects.nonNull(lauUnitCreativeBo) ? AdvertisingMode.getByKey(lauUnitCreativeBo.getAdvertisingMode()).getValue() : AdvertisingMode.NORMAL_CONTENT.getValue());
                        }
                    }
                    // 带货的需求 替换文案 其他都不变
                    // https://www.tapd.bilibili.co/********/prong/stories/view/11********002705475
                    Optional.ofNullable(cMap.get(dto.getCampaignId())).ifPresent(campaign -> {
                        if (Objects.equals(campaign.getPromotionPurposeType(), PromotionPurposeType.BRAND_SPREAD.getCode())) {
                            boolean supportVideoCommerce = accountLabelService.isAccountIdsInLabels(Collections.singletonList(dto.getAccountId()), Collections.singletonList(videoEcommerceLabelId));
                            if (supportVideoCommerce) {
                                vo.setPromotionType("视频带货");
                            }
                        }
                    });
                    return vo;
                }).collect(Collectors.toList());
    }

    private String genUnitCreativeKey(Integer unitId, Long creativeId) {
        return String.format("%d-%d", unitId, creativeId);
    }

    private String getTimeRangeStr(Timestamp start, Timestamp end) {
        return Utils.getTimestamp2String(start) + "~" + Utils.getTimestamp2String(end);
    }

    public static String genarateFileName(String userName, Long fromTime, Long toTime, GroupType groupType) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        return userName + "-" + sdf.format(new Date(fromTime)) + "-" + sdf.format(new Date(toTime)) + "-" + groupType.getDesc() + "-" + sdf.format(new Date()) + ".csv";
    }

    public static String genarateFileName(String userName, Long fromTime, Long toTime, StatQueryTimeType groupType) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        return userName + "-" + sdf.format(new Date(fromTime)) + "-" + sdf.format(new Date(toTime)) + "-" + groupType.name() + "-" + sdf.format(new Date()) + ".csv";
    }

    public static String getTime(String dateTime) {
        String[] s = dateTime.split(" ");
        if (s != null && s.length > 1) {
            return s[1].trim();
        } else {
            return "";
        }
    }

    public static String getDate(String dateTime) {
        String[] s = dateTime.split(" ");
        if (s != null && s.length > 1) {
            return s[0].trim();
        } else {
            return dateTime;
        }
    }

    public static BigDecimal sumBigData(List<BigDecimal> datas) {
        if (CollectionUtils.isEmpty(datas)) {
            return BigDecimal.ZERO;
        }

        return datas.stream().reduce(BigDecimal.ZERO, (b1, b2) -> b1.add(b2));
    }

    public static Long sumIntData(List<Integer> datas) {
        if (CollectionUtils.isEmpty(datas)) {
            return 0L;
        }

        return datas.stream().collect(Collectors.summingLong(d -> d));
    }

    public static BigDecimal getClickRate(Long clickCount, Long showCount) {
        if (0 == showCount) {
            return BigDecimal.ZERO;
        }
        return new BigDecimal(clickCount).divide(new BigDecimal(showCount), 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal(100));
    }

    public static BigDecimal getCostPerClick(BigDecimal cost, Long clickCount) {
        if (0 == clickCount) {
            return BigDecimal.ZERO;
        }
        return cost.divide(new BigDecimal(clickCount), 2, BigDecimal.ROUND_HALF_UP);
    }

    public static BigDecimal getAverageCostPerThousand(BigDecimal cost, Long showCount) {
        if (0 == showCount) {
            return BigDecimal.ZERO;
        }
        return cost.multiply(new BigDecimal(1000)).divide(new BigDecimal(showCount), 2, BigDecimal.ROUND_HALF_UP);
    }

    public List<FlyProStatisticVo> detailDtosToFlyProStatisticVos(List<StatLaunchDetailDto> dtos, StatQueryDto query, AccountBaseDto account) {
        if (CollectionUtils.isEmpty(dtos)) {
            return Collections.emptyList();
        }
        List<Integer> campaignIds = dtos
                .stream()
                .map(dto -> dto.getCampaignId())
                .filter(id -> id != null && id > 0)
                .distinct()
                .collect(Collectors.toList());

        List<Integer> unitIds = dtos
                .stream()
                .map(dto -> dto.getUnitId())
                .filter(id -> id != null && id > 0)
                .distinct()
                .collect(Collectors.toList());

        Map<Integer, CpcCampaignDto> cMap = cpcCampaignService.getCampaignMapInCampaignIds(campaignIds);

        Map<Integer, String> campaignMap = cMap.entrySet()
                .stream()
                .collect(Collectors.toMap(e -> e.getKey(), e -> e.getValue().getCampaignName()));

        // 获取单元报表对象信息
        //List<CpcUnitDto> unitReportDtos = CollectionUtils.isEmpty(unitIds) ? Collections.emptyList() : cpcUnitService.queryCpcUnit(QueryCpcUnitDto.builder().unitIds(unitIds).build());
        List<CpcUnitDto> unitReportDtos = CollectionUtils.isEmpty(unitIds) ? Collections.emptyList() : launchUnitV1Service.listUnits(QueryUnitBo.builder().unitIds(unitIds).build());
        // 获取单元对象
        Map<Integer, CpcUnitDto> unitMap = CollectionUtils.isEmpty(unitReportDtos) ? Collections.emptyMap() :
                unitReportDtos.stream()
                        .collect(Collectors.toMap(CpcUnitDto::getUnitId, u -> u));
        return dtos
                .stream()
                .map(dto -> FlyProStatisticVo
                        .builder()
                        //若时间维度是ALL，则date填充为开始时间~结束时间
                        .date(StatQueryTimeType.ALL.equals(query.getTimeType()) ? getTimeRangeStr(query.getStartDate(), query.getEndDate()) : getDate(dto.getDate()))
                        .time(query.getTimeType() == StatQueryTimeType.HOUR ? getTime(dto.getDate()) : "")
                        .campaign_name(campaignMap.getOrDefault(dto.getCampaignId(), ""))
                        .campaign_id(dto.getCampaignId() != null && dto.getCampaignId() > 0 ? dto.getCampaignId() : null)
                        .unit_name(unitMap.getOrDefault(dto.getUnitId(), CpcUnitDto.builder().unitName("").build()).getUnitName())
                        .unit_id(dto.getUnitId() != null && dto.getUnitId() > 0 ? dto.getUnitId() : null)
                        .show_count(dto.getShowAccount())
                        .click_count(dto.getClickCount())
                        .click_rate(dto.getClickRate() == null ? "0%" : dto.getClickRate().setScale(2, RoundingMode.HALF_UP).toString() + "%")
                        .cost(dto.getCost())
                        .cpm(dto.getAverageCostPerThousand())
                        .fan_follow_count(dto.getFanFollowCount())
                        .cost_per_increase_fans(CpcLaunchWebUtil.getCostPerInteger(dto.getCost(), dto.getFanFollowCount()))
                        .fans_follow_rate(CpcLaunchWebUtil.getPercentRate(dto.getFanFollowCount(), dto.getClickCount()))
                        .play2_fans_rate(CpcLaunchWebUtil.getPercentRate(dto.getFanFollowCount(), dto.getPlayCount()))
                        .build())
                .collect(Collectors.toList());
    }
}
