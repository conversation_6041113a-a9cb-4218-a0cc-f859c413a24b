package com.bilibili.adp.advertiser.portal.service.v6.unit;

import com.alibaba.fastjson.JSON;
import com.bapis.ad.pandora.core.v6.*;
import com.bapis.ad.pandora.resource.BaseTarget;
import com.bilibili.adp.advertiser.portal.common.BasicController;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.enums.OperatorType;
import com.bilibili.adp.common.enums.SystemType;
import com.bilibili.adp.v6.creative.bos.SanlianGetCreativeRespBo;
import com.bilibili.adp.v6.creative.bos.SanlianSaveCreativeReqBo;
import com.bilibili.adp.v6.creative.bos.SanlianSaveCreativeRespBo;
import com.bilibili.adp.v6.creative.bos.save.SanlianCreativeBo;
import com.bilibili.adp.v6.creative.bos.save.SanlianCreativeTitleBo;
import com.bilibili.adp.v6.creative.bos.save.unit.SanlianCreativeUnitBo;
import com.bilibili.adp.v6.creative.mapper.CreativeMapper;
import com.bilibili.adp.v6.unit.bos.*;
import com.bilibili.adp.cpc.biz.services.common.InfoReportService;
import com.bilibili.adp.cpc.biz.services.common.bos.ReportAigcTitleWhenCopyUnitBo;
import com.bilibili.adp.cpc.biz.services.game.LaunchBiliMiniGameService;
import com.bilibili.adp.cpc.biz.services.game.bos.BiliMiniGameBo;
import com.bilibili.adp.cpc.biz.services.unit.AdpCpcUnitService;
import com.bilibili.adp.cpc.biz.services.unit.api.IQueryShopGoodsService;
import com.bilibili.adp.cpc.dao.querydsl_es.pos.TitlePO;
import com.bilibili.adp.cpc.dto.DpaShopGoodsDto;
import com.bilibili.adp.cpc.repo.TitleSuggestionRepoV2;
import com.bilibili.adp.cpc.utils.TimeUtils;
import com.bilibili.adp.cpc.utils.metrics.CustomMetrics;
import com.bilibili.adp.launch.api.common.ShopGoodsStatusEnum;
import com.bilibili.adp.resource.api.account.label.IAccountLabelService;
import com.bilibili.adp.v6.operator.AdpOperator;
import com.bilibili.adp.advertiser.portal.common.Context;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.advertiser.portal.common.BasicController;
import com.bilibili.adp.advertiser.portal.common.Context;
import com.bilibili.adp.common.enums.GamePlatformTypeEnum;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.biz.services.ad_product.dto.AdProductBaseDto;
import com.bilibili.adp.cpc.biz.services.app.api.IAppPackageService;
import com.bilibili.adp.cpc.biz.services.live.AdpCpcLiveReserveService;
import com.bilibili.adp.cpc.biz.services.live.AdpCpcLiveRoomService;
import com.bilibili.adp.cpc.biz.services.live.bos.LiveReservationInfoBo;
import com.bilibili.adp.cpc.biz.services.live.bos.LiveRoomInfoBo;
import com.bilibili.adp.cpc.biz.services.resource.AdpCpcResourceService;
import com.bilibili.adp.cpc.biz.services.unit.UnitService;
import com.bilibili.adp.cpc.biz.services.unit.bos.GoodsBo;
import com.bilibili.adp.cpc.core.LaunchUnitGoodsService;
import com.bilibili.adp.cpc.databus.platform_ad_elements.pub.PlatformAdElementsPub;
import com.bilibili.adp.cpc.enums.ad_product.AdProductBindTypeEnum;
import com.bilibili.adp.cpc.enums.sanlian.SanlianPromotionContentTypeEnum;
import com.bilibili.adp.cpc.proxy.CpmPandoraProxy;
import com.bilibili.adp.cpc.repo.AdProductRepo;
import com.bilibili.adp.passport.api.dto.GameDto;
import com.bilibili.adp.passport.api.service.IGameCenterService;
import com.bilibili.adp.resource.api.app_package.dto.AppPackageDto;
import com.bilibili.adp.v6.operator.SanlianOperatorMapper;
import com.bilibili.adp.v6.unit.bos.info.*;
import com.bilibili.adp.v6.unit.mapper.*;
import com.bilibili.dpa.product.api.enums.OpenPlatformBusinessType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import io.opentelemetry.api.common.AttributeKey;
import io.opentelemetry.api.common.Attributes;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

import static com.bilibili.adp.cpc.constants.AccountLabelConstants.oldSanlianAllowEdit;
import static com.bilibili.adp.cpc.core.constants.AccountLabels.AI_REPLACE_TITLE_WHEN_COPY_BLACK_LIST_LABEL_ID;

/**
 * @ClassName SanlianWebUnitService
 * <AUTHOR>
 * @Date 2024/3/6 6:08 下午
 * @Version 1.0
 **/
@Service
@Slf4j
public class SanlianWebUnitService {

    @Resource(name = "cpcAppPackageService")
    private IAppPackageService appPackageService;
    @Autowired
    private IGameCenterService gameCenterService;
    @Autowired
    private AdpCpcLiveRoomService adpCpcLiveRoomService;
    @Autowired
    private AdpCpcLiveReserveService adpCpcLiveReserveService;
    @Autowired
    private LaunchUnitGoodsService launchUnitGoodsService;
    @Autowired
    private AdProductRepo adProductRepo;

    @Autowired
    private CpmPandoraProxy cpmPandoraProxy;

    @Resource
    private UnitService unitService;
    @Resource
    private AdpCpcResourceService slotGroupService;
    @Resource
    private SystemType systemType;
    @Resource
    private LaunchBiliMiniGameService launchBiliMiniGameService;
    @Resource
    private PlatformAdElementsPub platformAdElementsPub;
    @Resource
    private IQueryShopGoodsService queryShopGoodsService;
    @Autowired
    private IAccountLabelService accountLabelService;
    @Autowired
    private AdpCpcUnitService adpCpcUnitService;
    @Resource
    private TitleSuggestionRepoV2 titleSuggestionRepoV2;
    @Autowired
    private CustomMetrics customMetrics;
    private static final int AIGC_TITLE_THREAD_POOL_SIZE = 60;
    private static final int AIGC_TITLE_TIMEOUT_SECONDS = 2;
    // 暂定10是因为一个单元下最多10个创意，单元内标题会去重，同一个标题在一个单元内理论上最多出现10次
    private static final int AIGC_TITLE_CANDIDATE_NUM = 10;
    public String GET_AIGC_TITLE_CANDIDATE_COUNT_METRIC_KEY = "get_aigc_title_candidate_when_copy_unit";

    private ExecutorService aigcTitleExecutor;
    @Autowired
    private InfoReportService infoReportService;

    @PostConstruct
    public void initAigcTitleExecutor() {
        aigcTitleExecutor = Executors.newFixedThreadPool(
                AIGC_TITLE_THREAD_POOL_SIZE,
                new ThreadFactoryBuilder().setNameFormat("aigc-title-pool-%d").build()
        );
    }

    @PreDestroy
    public void destroyAigcTitleExecutor() {
        if (aigcTitleExecutor != null && !aigcTitleExecutor.isShutdown()) {
            aigcTitleExecutor.shutdown();
            try {
                if (!aigcTitleExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    aigcTitleExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                aigcTitleExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }

    @Resource
    private ObjectMapper om;

    public void updatePriceCoefficient(Context context, UpdatePriceCoefficientReqBo reqBo) {
        if (!accountLabelService.isAccountIdInLabel(context.getAccountId(), oldSanlianAllowEdit)) {
            Assert.isTrue(adpCpcUnitService.getAdp5UnitBos(Collections.singletonList(Integer.parseInt(reqBo.getUnitId()))).isEmpty(), "不支持编辑旧版广告计划");
        }
        UpdateSearchPriceCoefficientReq req = UpdateSearchPriceCoefficientReq.newBuilder()
                .setUnitId(Integer.parseInt(reqBo.getUnitId()))
                .setOperator(SanlianOperatorMapper.MAPPER.toRpcBo(BasicController.getAdpOperator(context)))
                .setPriceCoefficient(null != reqBo.getPriceCoefficient() ? reqBo.getPriceCoefficient().multiply(new BigDecimal(100)).intValue() : 0)
                .setPriceCoefficientType(reqBo.getPriceCoefficientType()).build();
        cpmPandoraProxy.updateSearchPriceCoefficient(req);
    }

    public PartialSaveUnitRespBo handlePartialSaveReq(Context context, PartialSaveUnitReqBo reqBo) {
        final PartialUnitBo partialUnitBo = reqBo.getUnit();
        Assert.isTrue(Objects.nonNull(partialUnitBo), "单元参数不能为空");
        if (Utils.isPositive(partialUnitBo.getUnitId())) {
            // 如果是新建, 使用母单元作为原型; 如果是更新, 使用子单元作为原型
            partialUnitBo.setSourceUnitId(partialUnitBo.getUnitId());
        } else {
            partialUnitBo.setUnitId(null);
        }
        final PartialSaveUnitResp resp = cpmPandoraProxy.partialSaveUnit(PartialSaveUnitReq.newBuilder()
                .setUnit(PartialUnitMapper.MAPPER.toRo(partialUnitBo))
                .setOperator(SanlianOperatorMapper.MAPPER.toRpcBo(BasicController.getAdpOperator(context)))
                .build());
        return new PartialSaveUnitRespBo(resp.getUnitId());
    }

    public SanlianSaveUnitRespBo handleSaveReq(Context context, SanlianSaveUnitReqBo reqBo) {
        final SaveUnitResp rpcResp = saveUnit(context, reqBo);
        return new SanlianSaveUnitRespBo(rpcResp.getUnitId());
    }

    private SaveUnitResp saveUnit(Context context, SanlianSaveUnitReqBo reqBo) {
        if (!context.isAutoEngine()) {
            reqBo.getUnit().setParentUnitId(null);
        }
        log.info("save unit req = {}", JSON.toJSONString(reqBo));

        final SaveUnitResp rpcResp = cpmPandoraProxy.saveUnit(SaveUnitReq.newBuilder()
                .setUnit(UnitMapper.MAPPER.toUnitRpcBo(reqBo.getUnit()))
                .setOperator(SanlianOperatorMapper.MAPPER.toRpcBo(BasicController.getAdpOperator(context)))
                .build());
        platformAdElementsPub.pub(context.getAccountId(), PlatformAdElementsPub.DIMENSION_UNIT, Collections.singletonList(rpcResp.getUnitId()));
        return rpcResp;
    }

    public SanlianGetUnitRespBo handleGetReq(Context context, Integer unitId) {
        return handleGetRespBo(getUnitBo(context, unitId), context.isAutoEngine());
    }

    private UnitBo getUnitBo(Context context, Integer unitId) {
        final GetUnitResp rpcResp = cpmPandoraProxy.getUnit(unitId);
        Assert.isTrue(Utils.isPositive(rpcResp.getUnit().getUnitId()), "单元" + unitId + "不存在, 查询失败");
        Assert.isTrue(Objects.equals(context.getAccountId(), rpcResp.getUnit().getAccountId()), "禁止操作不属于自己的单元");
        return UnitMapper.MAPPER.fromUnitRpcBo(rpcResp.getUnit());
    }

    private SanlianGetUnitRespBo handleGetRespBo(UnitBo bo, boolean isAutoEngine) {
        if (Objects.isNull(bo)) {
            return null;
        }

        // 产品要求写死
        if (Utils.isPositive(bo.getSalesMode().getCpaTarget())) {
            bo.getSalesMode().setBaseTarget(BaseTarget.BT_CPM_VALUE);
        }

        if (isAutoEngine) {
            return SanlianGetUnitRespBo.builder()
                    .unitBo(bo)
                    .build();
        }

        bo.setParentUnitId(null);
        //预算类型不暴露给前端
        bo.setBudgetType(null);

        String promotionContentTypeDesc =
                SanlianPromotionContentTypeEnum.getByCode(bo.getPromotionContentType()).getName();
        SanlianAppPackageInfoBo appPackageInfoBo = handleAppPackageInfo(bo.getAppPackage());
        SanlianGameInfoBo gameInfoBo = handleGameInfo(bo.getGame());
        SanlianLiveInfoBo liveInfoBo = handleLiveInfo(bo.getLive());
        SanlianLiveReserveInfoBo liveReserveInfoBo = handleLiveReserveInfo(bo.getLiveReserve());
        // 直播带货
        SanlianGoodsInfoBo goodsInfoBo = handleGoodsInfo(bo.getAccountId(), bo.getGoods());
        SanlianAdProductInfoBo adProductInfoBo = handleAdProductInfo(bo.getUnitId());
        SanlianShopGoodsInfoBo shopGoodsInfo = handleShopGoodsBo(bo.getShopGoods());
        SanlianGameCardInfoBo gameCardInfoBo = handleGameCardInfo(bo.getGameCard());

        UnitInfoBo unitInfoBo = UnitInfoBo.builder()
                .promotionContentTypeDesc(promotionContentTypeDesc)
                .appPackageInfo(appPackageInfoBo)
                .gameInfo(gameInfoBo)
                .liveInfo(liveInfoBo)
                .liveReserveInfo(liveReserveInfoBo)
                .goodsInfo(goodsInfoBo)
                .adProductInfo(adProductInfoBo)
                .biliMiniGameInfo(fetchMiniGame(bo.getBiliMiniGame()))
                .shopGoodsInfo(shopGoodsInfo)
                .gameCardInfo(gameCardInfoBo)
                .build();

        return SanlianGetUnitRespBo.builder()
                .unitInfoBo(unitInfoBo)
                .unitBo(bo)
                .build();
    }

    @SneakyThrows
    public List<SanlianCopyUnitRespBo> copy(Context context, Integer campaignId, Integer unitId, Integer count, Boolean needAiReplaceTitle) {
        // 获取当前时间
        Long timeStart = TimeUtils.nowUnix();
        log.info("sanlian unit copy num={},campaignId={},unitId={},accountId={}, needAiReplaceTitle={}", count, campaignId, unitId, context.getAccountId(), needAiReplaceTitle);
        //查询出单元
        GetUnitResp getUnitResp = cpmPandoraProxy.getUnit(unitId);
        UnitBo unitBo = UnitMapper.MAPPER.fromUnitRpcBo(getUnitResp.getUnit());

        // 查询出创意
        GetUnitCreativesResp getUnitCreativesResp = cpmPandoraProxy.getUnitCreatives(GetUnitCreativesReq.newBuilder()
                .setUnitId(unitId)
                .build());
        SanlianGetCreativeRespBo creativeBo = CreativeMapper.MAPPER.convertUnitAndCreativeRpcInfo2Bo(getUnitCreativesResp.getUnitCreative());

        List<Integer> accountLabelIds = accountLabelService.getLabelIdsByAccountId(context.getAccountId());
        Boolean hitAiReplace = !accountLabelIds.contains(AI_REPLACE_TITLE_WHEN_COPY_BLACK_LIST_LABEL_ID);
        List<SanlianCopyUnitRespBo> res = copyUnitsAndCreatives(context, campaignId, unitBo, creativeBo, count, needAiReplaceTitle && hitAiReplace);
        Attributes attributes = Attributes.of(AttributeKey.stringKey("copy_num"), count.toString(),
                AttributeKey.stringKey("need_aigc_replace_title"), (needAiReplaceTitle && hitAiReplace) ? "true" : "false");
        customMetrics.longHistogram("unit_copy_latency_ms", TimeUtils.nowUnix() - timeStart, attributes);
        return res;
    }


    @SneakyThrows
    private List<SanlianCopyUnitRespBo> copyUnitsAndCreatives(Context context, Integer campaignId, UnitBo unitBo, SanlianGetCreativeRespBo creativeBo, Integer count, boolean needReplace2AigcTitle) {

        List<SanlianCopyUnitRespBo> responseList = Lists.newArrayList();
        List<Integer> copyGenUnitIds = Lists.newArrayList();


        SanlianCreativeUnitBo creativeUnitBo = creativeBo.getUnit();
        List<SanlianCreativeBo> creativeList = creativeBo.getCreativeList();
        //单元和创意预处理
        preUnitBo(unitBo, campaignId);
        preCreativeBo(creativeUnitBo, creativeList, campaignId);
        AdpOperator operator = BasicController.getAdpOperator(context);

        // 获取创意下的所有标题，如果是智能标题则跳过，把标题的内容都放到set
        Set<String> titleSet = Sets.newHashSet();
        Map<String, List<String>> userTitle2AigcTitleListMap = Maps.newHashMap();
        Map<String, Integer> candidateTitleIndex =  Maps.newHashMap();

        if (needReplace2AigcTitle) {
            for (SanlianCreativeBo creative : creativeList) {
                for (SanlianCreativeTitleBo titleBo : creative.getTitleList()) {
                    // 如果是智能标题则跳过
                    if (titleBo.getSmartTitleList() != null && !titleBo.getSmartTitleList().isEmpty()) {
                        continue;
                    }
                    titleSet.add(titleBo.getTitle());
                }
            }
            // 获取衍生标题
            userTitle2AigcTitleListMap = getAigcTitleCandidates(titleSet, AIGC_TITLE_CANDIDATE_NUM);
            log.info("[copyUnitsAndCreatives] userTitle2AigcTitleListMap:{}", JSON.toJSONString(userTitle2AigcTitleListMap));
            // 打点统计成功率
            if (userTitle2AigcTitleListMap == null || userTitle2AigcTitleListMap.isEmpty()){
                customMetrics.count(GET_AIGC_TITLE_CANDIDATE_COUNT_METRIC_KEY, 1, Attributes.of(
                        AttributeKey.stringKey("success"), String.valueOf(false),
                        AttributeKey.stringKey("title_size"), String.valueOf(titleSet.size())
                ));
            }else {
                customMetrics.count(GET_AIGC_TITLE_CANDIDATE_COUNT_METRIC_KEY, 1, Attributes.of(
                        AttributeKey.stringKey("success"), String.valueOf(true),
                        AttributeKey.stringKey("title_size"), String.valueOf(titleSet.size())
                ));
            }
            candidateTitleIndex = titleSet.stream().collect(Collectors.toMap(title -> title, title -> 0));
        }
        // 实际复制流程
        try {
            //先创建单元
            for (int i = 0; i < count; i++) {
                copyGenUnitIds.add(cpmPandoraProxy.saveUnit(SaveUnitReq.newBuilder()
                        .setUnit(UnitMapper.MAPPER.toUnitRpcBo(unitBo))
                        .setOperator(SanlianOperatorMapper.MAPPER.toRpcBo(operator))
                        .build()).getUnitId());
            }

            for (int i = 0; i < count; i++) {
                creativeUnitBo.setUnitId(copyGenUnitIds.get(i));
                List<SanlianCreativeBo> newCreativeList;
                List<ReportAigcTitleWhenCopyUnitBo> reportAigcTitleWhenCopyUnitBoList = Lists.newArrayList();
                // 复制creativeList，并替换内部的title
                if (needReplace2AigcTitle) {
                    // 对于creativeList，使用深拷贝复制出全新的newCreativeList方便后续直接替换titleBo
                    newCreativeList = om.readValue(
                            om.writeValueAsString(creativeList),
                            om.getTypeFactory().constructCollectionType(List.class, SanlianCreativeBo.class)
                    );

                    for (SanlianCreativeBo creative : newCreativeList) {
                        List<String> AigcTitleAcceptedListInCreative = Lists.newArrayList();
                        List<SanlianCreativeTitleBo> titleList = creative.getTitleList();
                        for (SanlianCreativeTitleBo titleBo : titleList) {
                            // 如果是智能标题则跳过
                            if (titleBo.getSmartTitleList() != null && !titleBo.getSmartTitleList().isEmpty()) {
                                continue;
                            }
                            // 替换标题
                            String title = titleBo.getTitle();
                            if (userTitle2AigcTitleListMap.containsKey(title)) {
                                List<String> candidates = userTitle2AigcTitleListMap.get(title);
                                // 去重
                                String AigcTitleAccepted = candidates.get(candidateTitleIndex.get(title)%AIGC_TITLE_CANDIDATE_NUM);
                                AigcTitleAcceptedListInCreative.add(AigcTitleAccepted);
                                titleBo.setTitle(AigcTitleAccepted);
                                candidateTitleIndex.put(title, candidateTitleIndex.get(title) + 1);
                            }
                        }
                        reportAigcTitleWhenCopyUnitBoList.add(ReportAigcTitleWhenCopyUnitBo.builder()
                                .unitId(creativeUnitBo.getUnitId())
                                .creativeId(creativeUnitBo.getUnitId())
                                .accountId(creativeUnitBo.getAccountId())
                                .titles(AigcTitleAcceptedListInCreative)
                                .build());
                    }
                } else {
                    newCreativeList = creativeList;
                }

                SanlianSaveCreativeReqBo unitCreativeInfo = SanlianSaveCreativeReqBo.builder().unit(creativeUnitBo).creativeList(newCreativeList).build();
                SaveUnitCreativesResp saveUnitCreativesResp = cpmPandoraProxy.saveUnitCreatives(
                        SaveUnitCreativesReq.newBuilder()
                                .setOperator(SanlianOperatorMapper.MAPPER.toRpcBo(operator))
                                .setUnitCreative(CreativeMapper.MAPPER.convertUnitAndCreativeBo2RpcInfo(unitCreativeInfo))
                                .build());
                SanlianSaveCreativeRespBo saveUnitCreativesBo = CreativeMapper.MAPPER.convertRpcResp2Bo(saveUnitCreativesResp);
                SanlianCopyUnitRespBo response = SanlianCopyUnitRespBo.builder()
                        .unitId(copyGenUnitIds.get(i))
                        .creativeIds(saveUnitCreativesBo.getCreativeIdList())
                        .build();

                // set reportAigcTitleWhenCopyUnitBo creativeId & report
                if (needReplace2AigcTitle) {
                    for (int j = 0; j < saveUnitCreativesBo.getCreativeIdList().size(); j++) {
                        ReportAigcTitleWhenCopyUnitBo reportBo = reportAigcTitleWhenCopyUnitBoList.get(j);
                        reportBo.setCreativeId(saveUnitCreativesBo.getCreativeIdList().get(j));
                        infoReportService.reportAigcTitleWhenCopyUnit(reportBo);
                    }
                }
                responseList.add(response);
            }
        } catch (Exception e) {
            if (!CollectionUtils.isEmpty(copyGenUnitIds)) {
                unitService.deleteUnit(BasicController.getOperator(context), copyGenUnitIds);
                // 删除对应的ocpx信息
                slotGroupService.deleteUnitOcpxInfo(copyGenUnitIds);
            }

            log.error("sanlian unit copy err account={} unitIds={} 复制单元已删除", operator.getOperatorId(), copyGenUnitIds, e);
            throw new ServiceException(e.getMessage());
        }
        return responseList;
    }


    @SneakyThrows
    private Map<String, List<String>> getAigcTitleCandidates(Set<String> titles, int candidateNum) {
        if (CollectionUtils.isEmpty(titles)) {
            return Maps.newHashMap();
        }

        Map<String, List<String>> titleMap = Maps.newConcurrentMap();
        final CountDownLatch countDownLatch = new CountDownLatch(titles.size());

        List<CompletableFuture<Void>> futures = new ArrayList<>();
        for (String title : titles) {
            CompletableFuture<Void> future = CompletableFuture.supplyAsync(() -> {
                List<TitlePO> titlePOS = titleSuggestionRepoV2.getTitlesSuggestion(title, candidateNum*2);
                if (CollectionUtils.isEmpty(titlePOS)) {
                    return Lists.newArrayList();
                }
                List<String> titleList = Lists.newArrayList();
                Collections.shuffle(titlePOS);
                for (int i=0;i<candidateNum;i++) {
                    titleList.add(titlePOS.get(i).getTitle());
                }
                return titleList;
            }, aigcTitleExecutor).thenAccept(titleList -> {
                if (!titleList.isEmpty()) {
                    log.info("[wbh] titleList:{}", JSON.toJSONString(titleList));
                    List<String> stringTitleList = titleList.stream().map(Object::toString).collect(Collectors.toList());
                    titleMap.put(title, stringTitleList);
                }
                countDownLatch.countDown();
            });
            futures.add(future);
        }

        try {
            // 等待所有任务完成或超时
            final boolean ok = countDownLatch.await(AIGC_TITLE_TIMEOUT_SECONDS, TimeUnit.SECONDS);
            if (!ok) {
                throw new TimeoutException("获取AIGC标题候选项超时");
            }

            // 确保所有Future完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).exceptionally(e -> {
                log.error("获取AIGC标题候选项时发生异常", e);
                return null;
            }).join();
        } catch (InterruptedException e) {
            log.warn("等待AIGC标题候选项时被中断", e);
            Thread.currentThread().interrupt();
        }

        return titleMap;
    }


    private void preUnitBo(UnitBo unitBo, Integer campaignId) {
        LocalDate beginDate = LocalDate.now();
        unitBo.setCampaignId(campaignId);
        unitBo.setUnitId(0);
        unitBo.setLaunchBeginDate(beginDate.format(DateTimeFormatter.ISO_DATE));
        unitBo.setLaunchEndDate(beginDate.plusYears(100).format(DateTimeFormatter.ISO_DATE));
        if (Objects.isNull(unitBo.getAppPackage())) {
            unitBo.setAppPackage(SanlianAppPackageBo.builder().deviceAppStore("").build());
        }
    }

    private void preCreativeBo(SanlianCreativeUnitBo creativeUnitBo, List<SanlianCreativeBo> creativeList, Integer campaignId) {
        creativeUnitBo.setCampaignId(campaignId);
        creativeList.forEach(creative -> creative.setCreativeId(0));
    }

    private SanlianAppPackageInfoBo handleAppPackageInfo(SanlianAppPackageBo bo) {
        if (Objects.isNull(bo)) {
            return new SanlianAppPackageInfoBo();
        }

        if (!Utils.isPositive(bo.getAppPackageId())) {
            return new SanlianAppPackageInfoBo();
        }

        AppPackageDto appPackageDto = appPackageService.load(bo.getAppPackageId());
        return AppPackageMapper.MAPPER.convertAppPackageDto2Bo(appPackageDto);
    }

    private SanlianGameInfoBo handleGameInfo(SanlianGameBo bo) {
        if (Objects.isNull(bo)) {
            return new SanlianGameInfoBo();
        }

        if (!Utils.isPositive(bo.getGameBaseId())) {
            return new SanlianGameInfoBo();
        }

        GameDto gameDto = gameCenterService.getGameDtoById(bo.getGameBaseId());
        if (Objects.equals(bo.getSubPkg(), 1)) {
            gameDto.setGameName(gameDto.getGameName()+"(广告包)");
        }
        return GameMapper.MAPPER.convertGameDto2Bo(GamePlatformTypeEnum.ANDROID.getCode(), gameDto);
    }

    private SanlianLiveInfoBo handleLiveInfo(SanlianLiveBo bo) {
        if (Objects.isNull(bo)) {
            return new SanlianLiveInfoBo();
        }

        if (StringUtils.isEmpty(bo.getRoomId())) {
            return new SanlianLiveInfoBo();
        }

        long roomId = Long.parseLong(bo.getRoomId());
        if (!Utils.isPositive(roomId)) {
            return new SanlianLiveInfoBo();
        }

        LiveRoomInfoBo roomInfoBo = adpCpcLiveRoomService.fetchInHouseLiveInfo(String.valueOf(roomId));
        return LiveMapper.MAPPER.convertLiveRoomInfo(roomInfoBo);
    }

    private SanlianLiveReserveInfoBo handleLiveReserveInfo(SanlianLiveReserveBo bo) {
        if (Objects.isNull(bo)) {
            return new SanlianLiveReserveInfoBo();
        }

        if (StringUtils.isEmpty(bo.getSid())) {
            return new SanlianLiveReserveInfoBo();
        }

        Long reserveId = Long.parseLong(bo.getSid());
        if (!Utils.isPositive(reserveId)) {
            return new SanlianLiveReserveInfoBo();
        }

        Map<Long, LiveReservationInfoBo> reserveInfoMap =
                adpCpcLiveReserveService.batchQueryBiliLiveReserveInfo(Lists.newArrayList(reserveId));
        if (!reserveInfoMap.containsKey(reserveId)) {
            return new SanlianLiveReserveInfoBo();
        }
        return LiveMapper.MAPPER.convertLiveReserveInfo(reserveInfoMap.get(reserveId));
    }

    private SanlianGoodsInfoBo handleGoodsInfo(Integer accountId, SanlianGoodsBo bo) {
        if (Objects.isNull(bo)) {
            return new SanlianGoodsInfoBo();
        }

        if (StringUtils.isEmpty(bo.getItemId())) {
            return new SanlianGoodsInfoBo();
        }

        long itemId = Long.parseLong(bo.getItemId());
        if (!Utils.isPositive(itemId)) {
            return new SanlianGoodsInfoBo();
        }
        GoodsBo goodsBo = launchUnitGoodsService.getGoods(accountId, itemId);
        return GoodsMapper.MAPPER.bo2Vo(goodsBo);
    }

    private BiliMiniGameBo fetchMiniGame(SanlianBiliMiniGameBo bo) {
        if (Objects.isNull(bo)) return null;

        return launchBiliMiniGameService.get(bo.getCpMid(), bo.getGameBaseId());
    }

    private SanlianAdProductInfoBo handleAdProductInfo(Integer unitId) {
        if (!Utils.isPositive(unitId)) {
            return new SanlianAdProductInfoBo();
        }

        List<AdProductBaseDto> productList = adProductRepo.getProductsByMappingIdsV2(unitId, AdProductBindTypeEnum.UNIT.getCode());
        if (CollectionUtils.isEmpty(productList)) {
            return new SanlianAdProductInfoBo();
        }

        return AdProductMapper.MAPPER.fromBaseDto(productList.get(0));
    }

    private SanlianGameCardInfoBo handleGameCardInfo(SanlianGameCardBo bo) {
        if (Objects.isNull(bo)) {
            return new SanlianGameCardInfoBo();
        }
        if (!Utils.isPositive(bo.getGameBaseId())) {
            return new SanlianGameCardInfoBo();
        }
        Integer platformType = bo.getPlatformType();
        GameDto gameDto = gameCenterService.getGameDtoById(bo.getGameBaseId());
        log.info("handleGameCardInfo gameDto={}", JSON.toJSONString(gameDto));
        return GameMapper.MAPPER.convertGameCardDto2Bo(platformType, gameDto);
    }

    private SanlianShopGoodsInfoBo handleShopGoodsBo(SanlianShopGoodsBo bo) {
        if (Objects.isNull(bo)) {
            return null;
        }

        if (!Utils.isPositive(bo.getGoodsId())) {
            return null;
        }
        DpaShopGoodsDto dto = queryShopGoodsService.getDpaShopGoodsByGoodsId(bo.getGoodsId());
        if (Objects.isNull(dto)) {
            return null;
        }

        return SanlianShopGoodsInfoBo.builder()
                .id(dto.getGoodsId())
                .name(dto.getName())
                .price(dto.getPrice())
                .imageUrl(dto.getImageUrl())
                .jumpUrl(dto.getJumpUrl())
                .status(dto.getStatus())
                .categoryName(dto.getCategoryName())
                .productTypeName(dto.getProductType() == null ? "未知" : OpenPlatformBusinessType.getBusinessType(dto.getProductType()).getName())
                .statusDesc(ShopGoodsStatusEnum.getByCode(dto.getStatus()) == null ? "未知" : ShopGoodsStatusEnum.getByCode(dto.getStatus()).getDesc())
                .build();
    }

    public com.bilibili.adp.common.bean.Operator getOperator(Context context) {
        return Operator.builder().operatorId(context.getAccountId())
                .operatorName(context.getUsername())
                .operatorType(OperatorType.getByCode(context.getType()))
                .systemType(systemType)
                .bilibiliUserName(context.getProxyId() > 0 ? context.getProxyName() + "(" + context.getProxyId() + ")" : context.getProxyName())
                .bid(context.getBid())
                .appKey(context.getAppKey())
                .build();
    }

    private AdpOperator getAdpOperator(Context context) {
        return AdpOperator.builder()
                .operatorId(context.getAccountId())
                .operatorName(context.getUsername())
                .operatorType(OperatorType.getByCode(context.getType()))
                .systemType(systemType)
                .bilibiliUserName(context.getProxyId() > 0 ? context.getProxyName() + "(" + context.getProxyId() + ")" : context.getProxyName())
                .flag(context.genFlagValue())
                .bid(context.getBid())
                .appKey(context.getAppKey())
                .build();
    }
}
