package com.bilibili.adp.advertiser.portal.webapi.flyPro.v2;


import com.alibaba.fastjson.JSON;
import com.bilibili.adp.account.dto.AccountAllInfoDto;
import com.bilibili.adp.account.dto.AccountBaseDto;
import com.bilibili.adp.account.service.ICrmCompanyGroupService;
import com.bilibili.adp.account.service.IQueryAccountService;
import com.bilibili.adp.advertiser.helper.cpc.CpcHelper;
import com.bilibili.adp.advertiser.portal.common.BasicController;
import com.bilibili.adp.advertiser.portal.common.Context;
import com.bilibili.adp.advertiser.portal.common.DropBoxItemVo;
import com.bilibili.adp.advertiser.portal.common.SelectDropBoxItemVo;
import com.bilibili.adp.advertiser.portal.service.LaunchCommonService;
import com.bilibili.adp.advertiser.portal.service.creative.FlyCreativeSceneComponent;
import com.bilibili.adp.advertiser.portal.service.stat.HystrixUnitService;
import com.bilibili.adp.advertiser.portal.webapi.flyPro.enums.OfferTypeEnum;
import com.bilibili.adp.advertiser.portal.webapi.flyPro.v2.conveter.FlyUnitConverter;
import com.bilibili.adp.advertiser.portal.webapi.flyPro.v2.vo.*;
import com.bilibili.adp.advertiser.portal.webapi.flyPro.vo.FlyProSlotGroupVo;
import com.bilibili.adp.advertiser.portal.webapi.launch.cpc.CpcResourceContorller;
import com.bilibili.adp.advertiser.portal.webapi.launch.cpc.common.CpcLaunchWebUtil;
import com.bilibili.adp.advertiser.portal.webapi.launch.cpc.common.UpdateStatusRequestBean;
import com.bilibili.adp.advertiser.portal.webapi.launch.cpc.vo.CpcUnitLaunchTimeVo;
import com.bilibili.adp.advertiser.portal.webapi.launch.cpc.vo.CpcUnitReportVo;
import com.bilibili.adp.advertiser.portal.webapi.launch.cpc.vo.OcpcTargetVo;
import com.bilibili.adp.advertiser.portal.webapi.launch.cpc.vo.converter.UnitConverter;
import com.bilibili.adp.advertiser.portal.webapi.resource.vo.UpUserInfoVo;
import com.bilibili.adp.common.Constants;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.bean.TargetRule;
import com.bilibili.adp.common.enums.SalesType;
import com.bilibili.adp.common.enums.professional_fly.FlyCampaignTypeEnum;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.Page;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.biz.bos.unit.QueryUnitBo;
import com.bilibili.adp.cpc.biz.services.campaign.CpcCampaignService;
import com.bilibili.adp.cpc.biz.services.campaign.FlyCampaignService;
import com.bilibili.adp.cpc.biz.services.creative.FlyCreativeService;
import com.bilibili.adp.cpc.biz.services.creative.dto.NativeCreativeJudgeResultDto;
import com.bilibili.adp.cpc.biz.services.creative.native_ad.NativeAdJudger;
import com.bilibili.adp.cpc.biz.services.offline.LaunchOfflineService;
import com.bilibili.adp.cpc.biz.services.pickup.PickupOrderQuerier;
import com.bilibili.adp.cpc.biz.services.pickup.dto.PickupOrderDto;
import com.bilibili.adp.cpc.biz.services.target_package.ResTargetItemService;
import com.bilibili.adp.cpc.biz.services.unit.FlyUnitService;
import com.bilibili.adp.cpc.biz.services.unit.UnitService;
import com.bilibili.adp.cpc.biz.services.unit.api.ICpcUnitService;
import com.bilibili.adp.cpc.biz.services.unit.api.ILauUnitService;
import com.bilibili.adp.cpc.biz.services.unit.dto.*;
import com.bilibili.adp.cpc.core.LaunchUnitV1Service;
import com.bilibili.adp.cpc.databus.NativeCreativeRelativityAuditDatabusPub;
import com.bilibili.adp.cpc.databus.bos.NativeCreativeRelativityAuditDatabusMessage;
import com.bilibili.adp.cpc.databus.bos.NativeMsgItem;
import com.bilibili.adp.cpc.dto.LauUnitBaseDto;
import com.bilibili.adp.cpc.dto.UnitForecastDto;
import com.bilibili.adp.cpc.enums.AdpVersion;
import com.bilibili.adp.cpc.enums.ad.OcpcTargetEnum;
import com.bilibili.adp.cpc.enums.ad.PromotionPurposeType;
import com.bilibili.adp.cpc.biz.services.campaign.dto.CpcCampaignDto;
import com.bilibili.adp.launch.api.common.LaunchConstant;
import com.bilibili.adp.launch.api.common.UnitStatus;
import com.bilibili.adp.launch.api.flyPro.dto.enums.BusinessLiveRoomType;
import com.bilibili.adp.launch.api.flyPro.dto.enums.RoomPromoteScenesEnum;
import com.bilibili.adp.launch.api.flyPro.dto.v2.FlyCampaignDetailDto;
import com.bilibili.adp.launch.api.flyPro.dto.v2.SplitDaysImpressDto;
import com.bilibili.adp.launch.api.launch.dto.GetBidCostParam;
import com.bilibili.adp.launch.api.service.ILauTagIdsService;
import com.bilibili.adp.launch.api.service.IUnitTargetRuleService;
import com.bilibili.adp.launch.api.unit.dto.BatchUpdateNoDpaUnitDto;
import com.bilibili.adp.launch.biz.config.FlyGdPlusConfig;
import com.bilibili.adp.passport.api.dto.ArchiveDetail;
import com.bilibili.adp.passport.api.dto.UserInfoDto;
import com.bilibili.adp.passport.biz.manager.ArchiveManager;
import com.bilibili.adp.passport.biz.manager.LiveBroadcastHttpService;
import com.bilibili.adp.passport.biz.manager.bean.LiveBroadcastRoomInfo;
import com.bilibili.adp.passport.biz.manager.bean.LiveRoomDetailResponse;
import com.bilibili.adp.passport.biz.service.PassportService;
import com.bilibili.adp.resource.api.account.label.IAccountLabelService;
import com.bilibili.adp.resource.api.slot_group.QueryTemplateLaunchTypeMappingDto;
import com.bilibili.adp.resource.api.slot_group.ResSlotGroupBaseDto;
import com.bilibili.adp.resource.api.slot_group.ResSlotGroupTemplateMappingDto;
import com.bilibili.adp.resource.api.target_lau.dto.TargetTreeDto;
import com.bilibili.adp.resource.api.targetmeta.TargetType;
import com.bilibili.adp.resource.biz.service.ResSlotGroupService;
import com.bilibili.adp.web.framework.core.Pagination;
import com.bilibili.adp.web.framework.core.Response;
import com.bilibili.brand.api.soa.service.ISoaStockService;
import com.bilibili.brand.api.stock.dto.fly.*;
import com.bilibili.crm.platform.common.IsValid;
import groovy.util.logging.Slf4j;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.tuple.MutablePair;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.util.Pair;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.bilibili.adp.advertiser.portal.common.LaunchPortalUtils.ARCHIVE_COVER_DOMAIN;
import static com.bilibili.adp.advertiser.portal.common.LaunchPortalUtils.ARCHIVE_HAS_COVER_PREFIX;
import static com.bilibili.adp.cpc.enums.ad.LaunchTargetEnum.*;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/web_api/v2/fly_pro/unit")
@Api(value = "/fly_pro/unit", description = "【专业起飞【2.0】】单元相关")
public class FlyProUnitController extends BasicController {


    @Autowired
    private FlyUnitService flyUnitService;
    @Autowired
    private ICpcUnitService cpcUnitService;
    @Autowired
    private IUnitTargetRuleService unitTargetRuleService;
    @Autowired
    private HystrixUnitService hystrixUnitService;
    @Autowired
    private IQueryAccountService queryAccountService;
    @Autowired
    private CpcCampaignService cpcCampaignService;
    @Autowired
    private FlyUnitConverter flyUnitConverter;
    @Autowired
    private FlyCampaignService flyCampaignService;
    @Autowired
    private ResTargetItemService resTargetItemService;
    @Autowired
    private CpcResourceContorller cpcResourceContorller;
    @Autowired
    private ILauTagIdsService lauTagIdsService;
    @Autowired
    private ResSlotGroupService resSlotGroupService;
    @Autowired
    private UnitConverter unitConverter;
    @Autowired
    private PassportService passportService;
    @Autowired
    private ArchiveManager archiveManager;
    @Autowired
    private IAccountLabelService accountLabelService;
    @Autowired
    private ILauUnitService lauUnitService;
    @Autowired
    private UnitService unitService;
    @Autowired
    private FlyCreativeSceneComponent flyCreativeSceneComponent;
    private final LaunchUnitV1Service launchUnitV1Service;

    @Value("${platform.launch.support.orderby:false}")
    private boolean isSupportOrderBy;
    @Value("#{PropertySplitter.listInt('${platform.unit.play.page.page_ids:151,152,153}')}")
    private List<Integer> playPagePageIds;
    @Value("#{'${cpc.bid.no.limit.account.ids:2325}'.split(',')}")
    private List<Integer> bidNoLimitAccountIds;
    @Value("${cpc.bid.no.limit.low.price:30}")
    private Integer noLimitLowBid;
    @Value("#{'${cpc.can.ocpc.target.source.ids:1891,1898,2003,2030,2031}'.split(',')}")
    public List<Integer> canOcpcTargetSourceIds = java.util.Collections.emptyList();

    @Value("#{'${fly.exclude.slot.group.ids:231,229,318,346}'.split(',')}")
    private List<Integer> flyExcludeSlotGroupIds;
    @Value("${fly.pro.live.slot.group.id:371}")
    private Integer flyProLiveSlotGroupId;

    @Value("#{'${fly.pro.dynamic.slot.group.ids:346,347}'.split(',')}")
    private List<Integer> flyProDynamicSlotGroupIds;

    @Value("${dynamic.account.opcm.label.id:199}")
    private Integer dynamicAccountOcpmLabel;
    @Value("${label.activity.ocpm.id:226}")
    private Integer activityOcpmLabel;
    @Value("${under.box.link.click.label.id:224}")
    private Integer underBoxLinkClickLabelId;
    @Value("${first.comment.copy.label.id:227}")
    private Integer firstCommentCopyLabelId;

    @Autowired
    private LiveBroadcastHttpService liveBroadcastHttpService;
    @Autowired
    private LaunchCommonService launchCommonService;
    @Autowired
    private FlyCreativeService flyCreativeService;
    @Autowired
    private CpcHelper cpcCreativeHelper;
    @Autowired
    private ICrmCompanyGroupService crmCompanyGroupService;
    @Autowired
    private PickupOrderQuerier pickupOrderQuerier;
    @Autowired
    private ISoaStockService stockService;
    @Autowired
    private FlyGdPlusConfig flyGdPlusConfig;
    @Autowired
    private LaunchOfflineService launchOfflineService;
    @Autowired
    private NativeAdJudger nativeAdJudger;
    @Autowired
    private NativeCreativeRelativityAuditDatabusPub nativeCreativeRelativityAuditDatabusPub;

    @Value("${shut.down.label.id}")
    private int shutDownLabeId;

    @Value("${shut.down.date}")
    private String shutdownDateString;

    @Value("${platform.effect_ad.cant.edit.adp24.label.id:399}")
    private Integer cantEditAdp24LabelId;
    @ApiOperation(value = "更新单元状态")
    @RequestMapping(value = "/status", method = RequestMethod.PUT)
    public Response<Object> status(
            @ApiIgnore Context context,
            @RequestBody UpdateStatusRequestBean usrb,
            @ApiParam("目标状态 1-有效 2-暂停 4-删除") @RequestParam("status") int status) throws ServiceException {
        Assert.isTrue(usrb != null && !CollectionUtils.isEmpty(usrb.getIds()), "ids不能为空");
        if (status == UnitStatus.VALID.getCode()) {
            launchOfflineService.validateOfflineStage(context.getAccountId(), false);
            cpcUnitService.batchEnable(getOperator(context), usrb.getIds());
        } else if (status == UnitStatus.PAUSED.getCode()) {
            cpcUnitService.batchPause(getOperator(context), usrb.getIds());
        } else if (status == UnitStatus.DELETED.getCode()) {
            cpcUnitService.batchDelete(getOperator(context), usrb.getIds());
        }
        return Response.SUCCESS(null);
    }

    @ApiOperation(value = "查询推广单元报告列表")
    @RequestMapping(value = "/report", method = RequestMethod.GET)
    public Response<Pagination<List<FlyProUnitReportVo>>> queryUnitList(
            @ApiIgnore Context context,
            @RequestParam(value = "campaign_id", required = false) Integer campaignId,
            @RequestParam(value = "unit_ids", required = false, defaultValue = "") List<Integer> uids,
            @ApiParam("目标状态 1-有效 2-已暂停 4-已删除 5-未开始 6-预算超限 7-不在投放时段 8-已完成")
            @RequestParam(value = "status", required = false) List<Integer> statusList,
            @RequestParam(value = "unit_name", required = false) String likeUnitName,
            @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
            @RequestParam(value = "size", required = false, defaultValue = "10") Integer size,
            @RequestParam(value = "from_time", required = false) Long fromTime,
            @RequestParam(value = "to_time", required = false) Long toTime,
            @RequestParam(value = "sort_field", required = false, defaultValue = "") String sortField,
            @RequestParam(value = "sort_type", required = false, defaultValue = "0") @ApiParam("0-正序, 1-倒序") int sortType,
            @ApiParam("类型 -1-全部 0-非托管 1-托管 2-GD+")
            @RequestParam(value = "type", required = false, defaultValue = "-1") Integer type) {
        boolean queryByIds = !CollectionUtils.isEmpty(uids);
        AccountBaseDto accountBaseDto = queryAccountService.getAccountBaseDtoByIdFromCache(context.getAccountId());
        boolean hasOrderBy = isSupportOrderBy && !StringUtils.isEmpty(sortField) &&
                IsValid.FALSE.getCode().equals(accountBaseDto.getIsSupportDpa());

        String realLikeName = "";
        if (StringUtils.isNotBlank(likeUnitName)) {
            realLikeName = "%" + likeUnitName + "%";
        }

        QueryCpcUnitDto query = QueryCpcUnitDto.builder()
                .unitIds(uids)
                .accountId(context.getAccountId())
                .campaignId(queryByIds ? null : campaignId)
                .unitStatusList(queryByIds ? null : CollectionUtils.isEmpty(statusList) ?
                        UnitStatus.NON_DELETED_UNIT_STATUS_LIST : statusList)
                .likeUnitName(queryByIds ? null : realLikeName)
                .page(queryByIds || hasOrderBy ? null : Page.valueOf(page, size))
                .orderBy("unit_id desc")
                .build();

        query.setWithBeforeBudget(true);
        query.setWithBudgetRemainingModifyTimes(true);
        query.setWithCampaign(true);
        query.setNeedParseEndDate(true);
        query.setWithSlotGroupDto(true);
        query.setWithGdPlus(true);

        FlyCampaignTypeEnum typeEnum = FlyCampaignTypeEnum.getByCode(type);
        switch (typeEnum) {
            case COMMON:
                query.setIsManaged(IsValid.FALSE.getCode());
                query.setIsGdPlus(IsValid.FALSE.getCode());
                break;
            case MANAGED:
                query.setIsManaged(IsValid.TRUE.getCode());
                query.setIsGdPlus(IsValid.FALSE.getCode());
                break;
            case GD_PLUS:
                query.setIsManaged(IsValid.FALSE.getCode());
                query.setIsGdPlus(IsValid.TRUE.getCode());
                break;
            default:
                break;
        }

        PageResult<CpcUnitDto> pageResult = flyUnitService.getFlyUnitByPage(query);
        List<CpcUnitDto> unitList = pageResult.getRecords();

        if (CollectionUtils.isEmpty(unitList)) {
            return Response.SUCCESS(new Pagination<>(page, pageResult.getTotal(), Collections.emptyList()));
        }
        List<Integer> unitIds = unitList.stream().map(CpcUnitDto::getUnitId).collect(Collectors.toList());
        Map<Integer, List<TargetRule>> targetRuleMap = unitTargetRuleService.getTargetRulesMapInUnitIds(unitIds);
        unitList.forEach(x -> x.setTargetRules(targetRuleMap.getOrDefault(x.getUnitId(), null)));

        //不传时间默认今天
        Timestamp startTime = fromTime == null ? Utils.getBeginOfDay(Utils.getToday()) : Utils.getBeginOfDay(new Timestamp(fromTime));
        Timestamp endTime = toTime == null ? Utils.getEndOfDay(Utils.getToday()) : Utils.getEndOfDay(new Timestamp(toTime));
        List<CpcUnitReportVo> resultVos = hystrixUnitService.getCpcUnitReportVosFromES(context, campaignId, page, size, sortField, sortType, startTime, endTime, hasOrderBy, unitList, unitIds);
        Map<Integer, CpcUnitDto> idUnitMap = unitList.stream().collect(Collectors.toMap(CpcUnitDto::getUnitId, Function.identity()));
        final List<FlyProUnitReportVo> voList = resultVos.stream()
                .map(FlyProUnitReportVo::from)
                .filter(Objects::nonNull)
                .peek(report -> {
                    int id = report.getUnitId();
                    CpcUnitDto unit = idUnitMap.get(id);
                    report.setManaged(unit.getIsManaged() == 1);
                    report.setIs_gd_plus(unit.getIsGdPlus() == 1);
                    report.setGd_plus_finish_flag(unit.getGdPlusFinishFlag());
                    report.setGd_plus_fail_msg(unit.getGdPlusFailMsg());
                    report.setGd_plus_launch_date(unit.getGdPlusLaunchDate());
                })
                .collect(Collectors.toList());
        return Response.SUCCESS(new Pagination<>(page, pageResult.getTotal(), voList));
    }

    @ApiOperation(value = "新建单元")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    public Response<Object> create(
            @ApiIgnore Context context,
            @RequestBody FlyProUnitNewVo vo) throws ServiceException {
        launchOfflineService.validateOfflineStage(context.getAccountId(), true);
        FlyCampaignDetailDto flyCampaignDetailDto = flyCampaignService.getFlyCampaignById(vo.getCampaign_id());
        boolean inShutdownLabel = accountLabelService.isAccountIdsInLabels(Collections.singletonList(context.getAccountId()), Collections.singletonList(shutDownLabeId));
        LocalDateTime shutDownDate = LocalDateTime.parse(shutdownDateString, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        Assert.isTrue(flyCampaignDetailDto.isManaged() || flyCampaignDetailDto.isGdPlus() || !inShutdownLabel || LocalDateTime.now().isBefore(shutDownDate), "起飞专业版系统已不再维护，请至新三连推广平台进行投放");
        boolean inCantEditAdp24LabelId = accountLabelService.isAccountIdsInLabels(Collections.singletonList(context.getAccountId()), Collections.singletonList(cantEditAdp24LabelId));
        //【【专业起飞】商业起飞CPC出价方式下线】 https://www.tapd.bilibili.co/********/prong/stories/view/11********002820346
        boolean isCpc = Objects.equals(vo.getOffer_type(), OfferTypeEnum.CPC.getCode());
        Assert.isTrue(!inCantEditAdp24LabelId && !isCpc, "该版本广告单元已不再支持新建或复制，请新建新版本广告体验更好的广告能力");
        Assert.isTrue(context.getAccountId().equals(flyCampaignDetailDto.getAccountId()), "您不能在不属于您的计划下创建单元");
        AccountAllInfoDto accountAllInfoDto = queryAccountService.getAccountAllInfoFromCache(context.getAccountId());
        //单元的adpVersion跟账号&计划走
        int adpVersion = launchCommonService.getFlyUnitAdpVersion(accountAllInfoDto, flyCampaignDetailDto.isGdPlus());
        AccountBaseDto accountBaseDto = queryAccountService.getAccountBaseDtoByIdFromCache(context.getAccountId());
        //新起飞isNewFly写死1
        Long mid = this.generateUnitArchiveMid(accountBaseDto, flyCampaignDetailDto);
        NewCpcUnitDto newCpcUnitDto = flyUnitConverter.createVo2Dto(vo, flyCampaignDetailDto, mid, adpVersion);
        int unitId = unitService.createUnit(newCpcUnitDto, getOperator(context));
        return Response.SUCCESS(unitId);
    }

    @ApiOperation(value = "编辑单元")
    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    public Response<Object> edit(
            @ApiIgnore Context context,
            @RequestBody FlyProUnitUpdateVo vo) throws ServiceException {
        launchOfflineService.validateOfflineStage(context.getAccountId(), false);
        FlyCampaignDetailDto flyCampaignDetailDto = flyCampaignService.getFlyCampaignById(vo.getCampaign_id());
        boolean inShutdownLabel = accountLabelService.isAccountIdsInLabels(Collections.singletonList(context.getAccountId()), Collections.singletonList(shutDownLabeId));
        LocalDateTime shutDownDate = LocalDateTime.parse(shutdownDateString, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        Assert.isTrue(flyCampaignDetailDto.isManaged() || flyCampaignDetailDto.isGdPlus() || !inShutdownLabel || LocalDateTime.now().isBefore(shutDownDate), "起飞专业版系统已不再维护，请至新三连推广平台进行投放");
        boolean inCantEditAdp24LabelId = accountLabelService.isAccountIdsInLabels(Collections.singletonList(context.getAccountId()), Collections.singletonList(cantEditAdp24LabelId));
        //【【专业起飞】商业起飞CPC出价方式下线】 https://www.tapd.bilibili.co/********/prong/stories/view/11********002820346
        boolean isCpc = Objects.equals(vo.getOffer_type(), OfferTypeEnum.CPC.getCode());
        Assert.isTrue(!inCantEditAdp24LabelId && !isCpc, "该版本广告单元已不再支持新建或复制，请新建新版本广告体验更好的广告能力");
        CpcUnitDto unitDto = cpcUnitService.loadCpcUnit(vo.getUnit_id());
        Assert.isTrue(context.getAccountId().equals(unitDto.getAccountId()), "您不能编辑不属于您的单元");
        Assert.isTrue(unitDto.getIsManaged() == 0, "托管单元不可修改");
        UpdateCpcUnitDto updateCpcUnitDto = flyUnitConverter.updateVo2Dto(vo, unitDto.getLaunchBeginDate());
        cpcUnitService.updateCpcUnit(updateCpcUnitDto, getOperator(context));
        return Response.SUCCESS(null);
    }

    @ApiOperation(value = "单元详情")
    @RequestMapping(value = "/detail/{unit_id}", method = RequestMethod.GET)
    public Response<FlyProUnitDetailVo> detail(
            @ApiIgnore Context context,
            @PathVariable("unit_id") Integer unitId) throws ServiceException {
        CpcUnitDto unit = cpcUnitService.loadCpcUnit(unitId);
        Assert.isTrue(context.getAccountId().equals(unit.getAccountId()), "您不能操作不属于您的单元");

        String groupName = "";
        String imgUrl = "";
        Integer templateId = 0;
        //旧版关注资源位相关信息&模板相关信息
        if (AdpVersion.isLegacy(unit.getAdpVersion())) {
            List<ResSlotGroupTemplateMappingDto> result = resSlotGroupService.querySlotGroupTemplateMappingInSlotGroupIds(
                    QueryTemplateLaunchTypeMappingDto
                            .builder()
                            .promotionPurposeType(unit.getPromotionPurposeType())
                            .slotGroupIds(Arrays.asList(unit.getSlotGroup()))
                            .build());
            //获取资源位相关信息
            if (!CollectionUtils.isEmpty(result) && result.get(0).getSlotGroup() != null && result.get(0).getSlotGroup().getSlotGroupName() != null) {
                groupName = nameOptimize(result.get(0).getSlotGroup().getSlotGroupName());
            }
            //获取模板相关信息
            if (!CollectionUtils.isEmpty(result) && !CollectionUtils.isEmpty(result.get(0).getTemplates()) &&
                    !CollectionUtils.isEmpty(result.get(0).getTemplates().get(0).getReviewExampleDtos()) &&
                    result.get(0).getTemplates().get(0).getReviewExampleDtos().get(0).getBackgroundPicUrl() != null) {
                imgUrl = result.get(0).getTemplates().get(0).getReviewExampleDtos().get(0).getBackgroundPicUrl();
                templateId = result.get(0).getTemplates().get(0).getTemplateId();
            }
        }

        Boolean underbid = false;
        //新版关注underBid信息
        if (AdpVersion.isFlyBanner(unit.getAdpVersion())) {
            int bidPrice = unit.getCostPrice();
            underbid = launchCommonService.isUnitUnderBid(unitId, bidPrice);
        }

        String cover = this.generateCover(unit, context.getAccountId());
        Pair<Integer, Integer> pair = flyUnitService.getIsSetInviteLinkAndShowJumpBasicBubble(unit.getOcpcTarget(), unit.getVideoId());
        FlyProUnitDetailVo flyProUnitDetailVo = flyUnitConverter.detailDto2Vo(unit, groupName, imgUrl, templateId,
                cover, underbid, pair.getFirst(), pair.getSecond());
        return Response.SUCCESS(flyProUnitDetailVo);
    }

    private String generateCoverByAvid(Long avid) throws ServiceException {
        String cover = "";
        ArchiveDetail archiveInfo = archiveManager.queryArchivesByAid(avid);
        if (archiveInfo != null && archiveInfo.getArchive() != null && archiveInfo.getArchive().getCover() != null) {
            if (!archiveInfo.getArchive().getCover().trim().startsWith(ARCHIVE_HAS_COVER_PREFIX)) {
                cover = ARCHIVE_COVER_DOMAIN + archiveInfo.getArchive().getCover().trim();
            } else {
                cover = archiveInfo.getArchive().getCover();
            }
        }
        return cover;
    }

    private String generateCover(CpcUnitDto unit, Integer accountId) throws ServiceException {
        //获取稿件相关信息
        String cover = "";
        //投稿内容封面
        if (unit.getPromotionPurposeType() != null && PromotionPurposeType.ARCHIVE_CONTENT.getCode() == unit.getPromotionPurposeType()) {
            cover = this.generateCoverByAvid(unit.getVideoId());
        } else {
            //直播间封面
            if (unit.getPromotionPurposeType() != null && PromotionPurposeType.LIVE_ROOM.getCode() == unit.getPromotionPurposeType()) {
                FlyCampaignDetailDto flyCampaignDetailDto = flyCampaignService.getFlyCampaignById(unit.getCampaignId());
                AccountBaseDto accountBaseDto = queryAccountService.getAccountBaseDtoByIdFromCache(accountId);
                //预约拿稿件的封面
                if (RoomPromoteScenesEnum.RESERVE.getCode().equals(flyCampaignDetailDto.getRoomPromoteScenes())) {
                    cover = this.generateCoverByAvid(unit.getVideoId());
                } else {
                    BusinessLiveRoomType type = BusinessLiveRoomType.getByCode(flyCampaignDetailDto.getLiveRoomType());
                    if (type == null || type == BusinessLiveRoomType.NOT_BUSINESS_LIVE_ROOM) {
                        Long mid = flyCampaignDetailDto.getProxyMid() != null && flyCampaignDetailDto.getProxyMid() > 0 ?
                                flyCampaignDetailDto.getProxyMid() : accountBaseDto.getMid();
                        LiveRoomDetailResponse resp = liveBroadcastHttpService.queryLiveRoomByUid(mid.longValue());
                        cover = Optional.ofNullable(resp).map(LiveRoomDetailResponse::getCover).orElse("");
                    } else {
                        Integer queryRoomId;
                        if (type == BusinessLiveRoomType.MANUAL_LIVE_ROOM) {
                            queryRoomId = Integer.valueOf(flyCampaignDetailDto.getManuallyRoomId());
                        } else if (type == BusinessLiveRoomType.CHOOSE_LIVE_ROOM) {
                            queryRoomId = Integer.valueOf(flyCampaignDetailDto.getRoomId());
                    } else {
                            queryRoomId = null;
                        }
                        if (queryRoomId != null) {
                            Map<Integer, LiveBroadcastRoomInfo> res =
                                    liveBroadcastHttpService.queryLiveBroadcastRoomInfoByIds(Collections.singletonList(queryRoomId));
                            cover = Optional.ofNullable(res).map(info -> info.get(queryRoomId).getCover()).orElse("");
                        }
                    }
                }
            }
        }
        //动态在单元上没有封面
        return cover;
    }

    @Deprecated
    @ApiOperation(value = "新建推广时查询广告位组模板")
    @RequestMapping(value = "/slot_group", method = RequestMethod.GET)
    public Response<List<FlyProSlotGroupVo>> querySlotGroupByCampaignId(
            @ApiIgnore Context context,
            @RequestParam(value = "campaign_id") Integer campaignId) throws ServiceException {
        CpcCampaignDto campaign = cpcCampaignService.loadCpcCampaignDto(campaignId);

        Assert.isTrue(context.getAccountId().equals(campaign.getAccountId()), "不能操作不属于你的计划");
        List<Integer> slotGroupIds = resSlotGroupService.getValidGroupInSysTypes(SalesType.PLATFORM_SALES_TYPES, context.getAccountId());

        List<ResSlotGroupTemplateMappingDto> validSlotGroups = CollectionUtils.isEmpty(slotGroupIds) ? Collections.emptyList() :
                resSlotGroupService.querySlotGroupTemplateMappingInSlotGroupIds(
                        QueryTemplateLaunchTypeMappingDto
                                .builder()
                                .promotionPurposeType(campaign.getPromotionPurposeType())
                                .slotGroupIds(slotGroupIds)
                                .build());

        AccountBaseDto accountBaseDto = queryAccountService.getAccountBaseDtoByIdFromCache(context.getAccountId());

        //是直播间计划，只显示指定的广告位组
        if (PromotionPurposeType.LIVE_ROOM.getCode() == campaign.getPromotionPurposeType()) {
            validSlotGroups = validSlotGroups.stream().filter(o -> o.getSlotGroupId().equals(flyProLiveSlotGroupId)).collect(Collectors.toList());
        }

        boolean isGdPlus = IsValid.TRUE.getCode().equals(campaign.getIsGdPlus());
        //商业起飞账号非GD+要过滤掉内容起飞广告位组（新建非GD单元时用）
        if (Integer.valueOf(1).equals(accountBaseDto.getIsSupportFly()) && !isGdPlus) {
            validSlotGroups = validSlotGroups.stream().filter(o -> !flyExcludeSlotGroupIds.contains(o.getSlotGroupId())).collect(Collectors.toList());
        }

        //isGdPlus是在GD+单元建创意时用
        return Response.SUCCESS(validSlotGroups.stream()
                .map(dto -> FlyProSlotGroupVo
                        .builder()
                        .slot_group_id(dto.getSlotGroupId())
                        .slot_group_name(nameOptimize(dto.getSlotGroup().getSlotGroupName()))
                        .template(unitConverter.templateDtoToFlyVo(dto.getTemplates().get(0), Collections.emptyMap(), isGdPlus))
                        .is_mobile(dto.getSlotGroup().getChannelId().equals(LaunchConstant.CHANNEL_MOBILE_ID))
                        .channel_id(dto.getSlotGroup().getChannelId())
                        .channel_name(dto.getSlotGroup().getChannelName())
                        .is_video_page(!CollectionUtils.isEmpty(playPagePageIds) && playPagePageIds.contains(dto.getSlotGroupId()))
                        .build())
                .collect(Collectors.toList()));
    }

    @ApiOperation(value = "编辑单元时查询广告位组模板")
    @RequestMapping(value = "/{unit_id}/slot_group", method = RequestMethod.GET)
    public Response<List<FlyProSlotGroupVo>> querySlotGroupByUnitId(@ApiIgnore Context context,
                                                                    @PathVariable("unit_id") Integer unitId) throws ServiceException {
        CpcUnitDto unit = cpcUnitService.loadCpcUnit(unitId);
        Assert.isTrue(context.getAccountId().equals(unit.getAccountId()), "不能操作不属于你的单元");
        // 根据 salesTypes 和 accountId 获取广告位组列表
        List<Integer> slotGroupIds = resSlotGroupService.getValidGroupInSysTypes(SalesType.PLATFORM_SALES_TYPES, context.getAccountId());

        List<ResSlotGroupTemplateMappingDto> validSlotGroups = CollectionUtils.isEmpty(slotGroupIds) ? Collections.emptyList() :
                resSlotGroupService.querySlotGroupTemplateMappingInSlotGroupIds(
                        QueryTemplateLaunchTypeMappingDto
                                .builder()
                                .promotionPurposeType(unit.getPromotionPurposeType())
                                .slotGroupIds(slotGroupIds)
                                .build());
        boolean isGdPlus = IsValid.TRUE.getCode().equals(unit.getIsGdPlus());
        return Response.SUCCESS(validSlotGroups.stream()
                .map(dto -> FlyProSlotGroupVo
                        .builder()
                        .slot_group_id(dto.getSlotGroupId())
                        .slot_group_name(nameOptimize(dto.getSlotGroup().getSlotGroupName()))
                        .template(unitConverter.templateDtoToFlyVo(dto.getTemplates().get(0), Collections.emptyMap(), isGdPlus))
                        .is_mobile(dto.getSlotGroup().getChannelId().equals(LaunchConstant.CHANNEL_MOBILE_ID))
                        .channel_id(dto.getSlotGroup().getChannelId())
                        .channel_name(dto.getSlotGroup().getChannelName())
                        .is_video_page(!CollectionUtils.isEmpty(playPagePageIds) && playPagePageIds.contains(dto.getSlotGroupId()))
                        .build())
                .collect(Collectors.toList()));
    }

    private String nameOptimize(String name) {
        if ("移动-信息流-小图-视频".equals(name)) {
            return "信息流";
        }
        if ("移动-播放页-推荐位首位-视频-CPM".equals(name)) {
            return "播放页";
        }
        return name;
    }

    @ApiOperation(value = "竞价广告查询定向条件")
    @RequestMapping(value = "/target", method = RequestMethod.GET)
    public Response<Object> queryTargetList(
            @ApiIgnore Context context,
            @RequestParam(value = "campaign_id", required = false) Integer campaignId) throws ServiceException, InterruptedException {
        Map<TargetType, List<TargetTreeDto>> target2ItemMap = resTargetItemService.getAllValidTarget2ItemTreeMap();
        Map<String, Object> ans = flyUnitConverter.buildTargetMap(context.getAccountId(), target2ItemMap, campaignId, null, getOperator(context),false);
        Response response = cpcResourceContorller.getVideoSecondPartitions();
        List<SelectDropBoxItemVo> second = (List) response.getResult();
        ans.put("video_interest", second);
        return Response.SUCCESS(ans);
    }

    @ApiOperation(value = "人群预估")
    @RequestMapping(value = "/forecast", method = RequestMethod.POST)
    public Response<FlyProForecastResponseVo> crowdForecast(@ApiIgnore Context context,
                                                            @RequestBody FlyProForecastRequestVo vo) throws ServiceException {
        Integer slotGroupId = vo.getSlot_group_id() != null && vo.getSlot_group_id() > 0 ? vo.getSlot_group_id() : null;
        boolean newFlyBanner = false;
        //新建页面
        if (vo.getUnit_id() == null || vo.getUnit_id() <= 0) {
            //版位收敛全开
            newFlyBanner = true;
        } else {
            //编辑页面
            //查看单元是否版位收敛
            CpcUnitDto unitDto = cpcUnitService.loadCpcUnit(vo.getUnit_id());
            if (unitDto.getAdpVersion() != null && unitDto.getAdpVersion() == 2) {
                newFlyBanner = true;
            }
        }
        MiddleCpcUnitForecastDto result = cpcUnitService.forecast(UnitForecastDto.builder()
                .target(flyUnitConverter.unitTargetVo2Dto(vo.getTargets(), slotGroupId))
                .unitId(vo.getUnit_id())
                .launch_time(vo.getLaunch_time())
                .campaignId(vo.getCampaign_id())
                //salestype默认11(cpm)
                .salesType(vo.getOffer_type() != null ? vo.getOffer_type() / 10 : 11)
                .operator(getOperator(context))
                .suggestCostPrice(true)
                .newFlyBanner(newFlyBanner)
                .build());
        FlyProForecastResponseVo responseVo = FlyProForecastResponseVo.builder()
                .max_crowd_count(result.getMax_crowd_count())
                .max_impression_count(result.getMax_impression_count())
                .suggest_cost_min(result.getSuggest_cost_min())
                .suggest_cost_max(result.getSuggest_cost_max())
                .build();
        return Response.SUCCESS(responseVo);
    }

    @ApiOperation(value = "检查tag是否存在")
    @RequestMapping(value = "/check_tag", method = RequestMethod.POST)
    public Response<Map<String, Boolean>> checkTag(@ApiIgnore Context context, @RequestBody List<String> tags) throws ServiceException {
        return Response.SUCCESS(lauTagIdsService.checkTagExistInRedis(tags));
    }

    @ApiOperation(value = "tag相关数量")
    @RequestMapping(value = "/tag_total_count", method = RequestMethod.POST)
    public Response<Long> calculateTagCount(@ApiIgnore Context context,
                                            @ApiParam("匹配：1-精确匹配 2-模糊匹配") @RequestParam("is_fuzzy_tags") Integer isFuzzyTags,
                                            @RequestBody List<String> tags) throws ServiceException {
        return Response.SUCCESS(lauTagIdsService.getContentTagPeopleCount(tags, isFuzzyTags));
    }

    @ApiOperation(value = "视频tag数量")
    @RequestMapping(value = "/video_tag_total_count", method = RequestMethod.POST)
    public Response<Long> calculateVideoTagCount(@ApiIgnore Context context,
                                                 @RequestParam("is_fuzzy_tags") Integer isFuzzyTags,
                                                 @RequestBody List<String> tags) throws ServiceException {
        return Response.SUCCESS(lauTagIdsService.getContentTagPeopleCountFromES(tags, isFuzzyTags));
    }

    @ApiOperation(value = "计划下单元下拉列表")
    @RequestMapping(value = "/drop_box", method = RequestMethod.GET)
    public Response<List<DropBoxItemVo>> getUnitDropBox(
            @ApiIgnore Context context,
            @RequestParam(value = "campaign_ids") List<Integer> campaignIds) throws ServiceException {

//        QueryCpcUnitDto query = QueryCpcUnitDto
//                .builder()
//                .accountId(context.getAccountId())
//                .campaignIds(campaignIds)
//                .unitStatusList(UnitStatus.NON_DELETED_UNIT_STATUS_LIST)
//                .orderBy(Constants.MTIME_DESC)
//                .build();
//
//        List<CpcUnitDto> dtos = cpcUnitService.queryCpcUnit(query);
        QueryUnitBo query = QueryUnitBo
                .builder()
                .accountIds(Collections.singletonList(context.getAccountId()))
                .campaignIds(campaignIds)
                .unitStatusList(UnitStatus.NON_DELETED_UNIT_STATUS_LIST)
                .orderBy(Constants.MTIME_DESC)
                .build();
        List<CpcUnitDto> dtos = launchUnitV1Service.listUnits(query);
        if (CollectionUtils.isEmpty(dtos)) {
            Response.SUCCESS(Collections.emptyList());
        }
        List<DropBoxItemVo> vos = dtos.stream().map(o -> DropBoxItemVo.builder()
                .id(o.getUnitId())
                .name(o.getUnitName())
                .is_gd_plus(o.getIsGdPlus())
                .adp_version(o.getAdpVersion())
                .build()).collect(Collectors.toList());
        return Response.SUCCESS(vos);
    }

    @ApiOperation(value = "批量查看投放时段")
    @RequestMapping(value = "/launch_time", method = RequestMethod.GET)
    public Response<List<CpcUnitLaunchTimeVo>> getLaunchTimes(@ApiIgnore Context context,
                                                              @RequestParam(value = "unit_ids") List<Integer> unitIds) {
        List<Integer> accountIds = lauUnitService.getBaseDtosInIds(unitIds).stream().map(LauUnitBaseDto::getAccountId).distinct().collect(Collectors.toList());
        Assert.isTrue(accountIds.contains(context.getAccountId()), "不能操作不属于你的单元");
        List<CpcUnitLaunchTimeVo> cpcUnitLaunchTimeVoList = new ArrayList<>();
        Map<Integer, String> launchTimeMap = lauUnitService.getLaunchTimes(unitIds);
        for (Map.Entry<Integer, String> entry : launchTimeMap.entrySet()) {
            cpcUnitLaunchTimeVoList.add(CpcUnitLaunchTimeVo.builder()
                    .unit_id(entry.getKey())
                    .launch_time(CpcLaunchWebUtil.buildLaunchTimeForVo(entry.getValue()))
                    .build());
        }
        return Response.SUCCESS(cpcUnitLaunchTimeVoList);
    }

    @ApiOperation(value = "批量修改单元", notes = "批量修改预算、出价_notes", tags = "批量修改预算、出价_tags")
    @RequestMapping(value = "/batch", method = RequestMethod.PUT)
    public Response<Object> batchUpdateUnit(@ApiIgnore Context context,
                                            @RequestBody @Valid FlyProBatchUpdateUnitVo vo) {
        launchOfflineService.validateOfflineStage(context.getAccountId(), false);
        AccountBaseDto accountBaseDto = queryAccountService.getAccountBaseDtoByIdFromCache(context.getAccountId());
        Assert.isTrue(IsValid.FALSE.getCode().equals(accountBaseDto.getIsSupportDpa()), "您无权限进行批量修改单元");
        List<FlyProUpdateUnitVo> unitVoList = vo.getVo_list();
        List<BatchUpdateNoDpaUnitDto> batchUpdateNoDpaUnitDtoList = new ArrayList<>(unitVoList.size());
        AccountAllInfoDto accountAllInfoDto = queryAccountService.getAccountAllInfoFromCache(context.getAccountId());
        for (FlyProUpdateUnitVo unitVo : unitVoList) {
            List<LauUnitBaseDto> units = lauUnitService.getBaseDtosInIds(unitVo.getUnit_ids());
            Integer ocpcTarget = units.get(0).getOcpcTarget();
            Assert.isTrue(units.stream().noneMatch(unit -> unit.getIsManaged() == 1), "托管单元不支持批量修改");
            List<Integer> accountIds = units
                    .stream()
                    .map(LauUnitBaseDto::getAccountId)
                    .distinct()
                    .collect(Collectors.toList());
            Assert.isTrue(accountIds.contains(context.getAccountId()), "不能操作不属于你的单元");
            BatchUpdateNoDpaUnitDto updateNoDpaUnitDto = flyUnitConverter.batchUpdateNoDpaUnitVo2Dto(unitVo);
            flyUnitConverter.batchUpdateUnitCheck(units, ocpcTarget, updateNoDpaUnitDto, accountAllInfoDto);
            batchUpdateNoDpaUnitDtoList.add(updateNoDpaUnitDto);
        }

        cpcUnitService.batchUpdateNoDpaUnit(batchUpdateNoDpaUnitDtoList, super.getOperator(context));
        return Response.SUCCESS(null);
    }

    @ApiOperation(value = "修改预算")
    @RequestMapping(value = "/budget", method = RequestMethod.PUT)
    public Response<Object> updateBudget(@ApiIgnore Context context,
                                         @RequestParam("unit_id") List<Integer> unitIds,
                                         @RequestParam("budget") BigDecimal budget,
                                         @RequestParam Integer daily_budget_type) throws ServiceException {
        launchOfflineService.validateOfflineStage(context.getAccountId(), false);
        boolean inShutdownLabel = accountLabelService.isAccountIdsInLabels(Collections.singletonList(context.getAccountId()), Collections.singletonList(shutDownLabeId));
        LocalDateTime shutDownDate = LocalDateTime.parse(shutdownDateString, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        Assert.isTrue(!inShutdownLabel || LocalDateTime.now().isBefore(shutDownDate), "起飞专业版系统已不再维护，请至新三连推广平台进行投放");
        Assert.notNull(unitIds, "单元id不能为空");
        List<LauUnitBaseDto> units = lauUnitService.getBaseDtosInIds(unitIds);
        Assert.isTrue(units.stream().noneMatch(unit -> unit.getIsManaged() == 1), "托管单元不支持修改");
        cpcUnitService.batchUpdateBudget(unitIds, budget, daily_budget_type, getOperator(context));
        return Response.SUCCESS(null);
    }

    @ApiOperation(value = "批量修改出价")
    @RequestMapping(value = "/batch/bid_price", method = RequestMethod.PUT)

    public Response<Object> batchUpdateBidPrice(@ApiIgnore Context context,
                                                @RequestParam("unit_ids") List<Integer> unitIds,
                                                @RequestParam("bid_price") BigDecimal bidPrice,
                                                @RequestParam(value = "two_stage_bid", defaultValue = "") BigDecimal twoStageBid) throws ServiceException {
        launchOfflineService.validateOfflineStage(context.getAccountId(), false);
        cpcUnitService.batchUpdateBidPrice(super.getOperator(context), unitIds, bidPrice, twoStageBid);
        return Response.SUCCESS(null);
    }

    @ApiOperation("获取投放方式及OCPC目标对象")
    @GetMapping(value = "/all_ocpc_targets")
    public Response<FlyProNewSlotGroupMetaVo> getAllOcpcTargets(
            @ApiIgnore Context context,
            @RequestParam(value = "os") List<Integer> osList,
            @RequestParam(value = "slot_group_id", required = false) Integer slotGroupId,
            @RequestParam(value = "campaign_id") Integer campaignId,
            @RequestParam(value = "unit_id", required = false) Integer unitId) throws ServiceException {

        AccountAllInfoDto accInfo = queryAccountService.getAccountAllInfoFromCache(context.getAccountId());
        CpcCampaignDto campaign = cpcCampaignService.loadCpcCampaignDto(campaignId);
        FlyCampaignDetailDto flyCampaignDetailDto = flyCampaignService.getFlyCampaignById(campaignId);
        //默认是都是false和空数组
        FlyProNewSlotGroupMetaVo ans = FlyProNewSlotGroupMetaVo.builder()
                .slot_group_id(slotGroupId)
                .can_ocpm(false)
                .can_cpm(false)
                .can_cpc(false)
                .ocpm_targets(Collections.emptyList())
                .build();

        //编辑页面，根据单元表的字段出1个投放方式
        if (unitId != null) {
            CpcUnitDto unitDto = cpcUnitService.simpleLoadCpcUnit(unitId);
            Integer saleType = Optional.ofNullable(unitDto).map(o -> o.getSalesType()).orElse(0);
            Integer ocpcTarget = Optional.ofNullable(unitDto).map(o -> o.getOcpcTarget()).orElse(0);
            if (SalesType.CPM.getCode() == saleType) {
                if (flyUnitConverter.flyOcpmList.contains(ocpcTarget)) {
                    ans.setCan_ocpm(true);
                    List<OcpcTargetVo> ocpcTargetVos = new ArrayList<>();
                    this.ocpcTargetVosAddElement(ocpcTargetVos, OcpcTargetEnum.getByCode(ocpcTarget));
                    ans.setOcpm_targets(ocpcTargetVos);
                } else {
                    ans.setCan_cpm(true);
                }
            }
        } else {
            // 新建页面
            // 是版位收敛后的，根据账号权限出
            if (launchCommonService.isFlyProBannerAdp(context.getAccountId(), IsValid.TRUE.getCode().equals(campaign.getIsGdPlus()))) {
                ans.setCan_cpm(true);
                MutablePair<Boolean, List<OcpcTargetVo>> canOcpm = this.getCanOcpm(accInfo, campaign, flyCampaignDetailDto.getAvId(), flyCampaignDetailDto.getRoomPromoteScenes());
                ans.setCan_ocpm(canOcpm.getLeft());
                ans.setOcpm_targets(canOcpm.getRight());
            } else {
                // 是版位收敛前的，根据广告位组的信息出多个投放方式（已废弃，不再维护）
                // 没传slotGroupId，什么都不返回
                if (slotGroupId == null || slotGroupId <= 0) {
                    return Response.SUCCESS(FlyProNewSlotGroupMetaVo.builder()
                            .slot_group_id(slotGroupId)
                            .can_ocpm(false)
                            .can_cpm(false)
                            .can_cpc(false)
                            .ocpm_targets(Collections.emptyList())
                            .build());
                }

                //todo 代码优化
                List<Integer> slotGroupIds = resSlotGroupService.getValidGroupInSysTypes(SalesType.PLATFORM_SALES_TYPES, context.getAccountId());
                List<ResSlotGroupTemplateMappingDto> validSlotGroups = CollectionUtils.isEmpty(slotGroupIds) ? Collections.emptyList() :
                        resSlotGroupService.querySlotGroupTemplateMappingInSlotGroupIds(
                                QueryTemplateLaunchTypeMappingDto
                                        .builder()
                                        .promotionPurposeType(campaign.getPromotionPurposeType())
                                        .slotGroupIds(slotGroupIds)
                                        .build());
                ResSlotGroupTemplateMappingDto resSlotGroupTemplateMappingDto = validSlotGroups.stream()
                        .filter(o -> slotGroupId.equals(o.getSlotGroupId())).findFirst().orElse(null);
                List<Integer> saleTypes = Optional.ofNullable(resSlotGroupTemplateMappingDto)
                        .map(ResSlotGroupTemplateMappingDto::getSlotGroup)
                        .map(ResSlotGroupBaseDto::getSalesTypes)
                        .orElse(new ArrayList<>());
                if (saleTypes.contains(SalesType.CPM.getCode())) {
                    ans.setCan_cpm(true);
                    //查询ocpmTarget
                    List<OcpcTargetVo> ocpcTargetVos = new ArrayList<>();
                    ResSlotGroupBaseDto resSlotGroup = resSlotGroupService.getGroupById(slotGroupId);
                    if (Objects.nonNull(resSlotGroup) && !CollectionUtils.isEmpty(this.canOcpcTargetSourceIds)
                            && CollectionUtils.isEmpty(Utils.getIntersection(resSlotGroup.getSlotIds(), this.canOcpcTargetSourceIds))) {
                        //广告位组的广告位不支持ocpm，没有ocpm
                    } else {
                        if (resSlotGroup.getId().equals(flyProLiveSlotGroupId)) {
                            //专业起飞直播间，没有ocpm
                        } else {
                            //动态
                            if (flyProDynamicSlotGroupIds.contains(resSlotGroup.getId())) {
                                if (accountLabelService.isAccountIdsInLabels(Collections.singletonList(accInfo.getAccountDto().getAccountId())
                                        , Collections.singletonList(dynamicAccountOcpmLabel))) {
                                    this.ocpcTargetVosAddElement(ocpcTargetVos, OcpcTargetEnum.DYNAMIC_DETAIL_BROWSE);
                                }
                            } else {
                                //不是直播间或动态的广告位组，是内容起飞
                                if (Integer.valueOf(1).equals(accInfo.getAccountDto().getIsSupportContent())) {
                                    this.ocpcTargetVosAddElement(ocpcTargetVos, OcpcTargetEnum.USER_FOLLOW);

                                }
                            }
                        }
                    }
                    boolean canOcpxTarget = !CollectionUtils.isEmpty(ocpcTargetVos);
                    ans.setCan_ocpm(canOcpxTarget);
                    ans.setOcpm_targets(canOcpxTarget ? ocpcTargetVos : Collections.emptyList());
                }
            }
        }
        return Response.SUCCESS(ans);
    }


    private MutablePair<Boolean, List<OcpcTargetVo>> getCanOcpm(AccountAllInfoDto accInfo, CpcCampaignDto campaign,
                                                                String avId, Integer roomPromoteScenes) throws ServiceException {
        List<OcpcTargetVo> ocpcTargetVos = new ArrayList<>();
        PromotionPurposeType purposeType = PromotionPurposeType.getByCode(campaign.getPromotionPurposeType());
        //是内容起飞
        if (Integer.valueOf(1).equals(accInfo.getAccountDto().getIsSupportContent())) {
            //稿件
            if (purposeType == PromotionPurposeType.ARCHIVE_CONTENT) {
                //涨粉全量
                this.ocpcTargetVosAddElement(ocpcTargetVos, OcpcTargetEnum.USER_FOLLOW);
                //播放全量
                this.ocpcTargetVosAddElement(ocpcTargetVos, OcpcTargetEnum.VIDEO_PLAY);
                //评论白名单
                if (accountLabelService.isAccountIdsInLabels(Collections.singletonList(accInfo.getAccountDto().getAccountId())
                        , Collections.singletonList(firstCommentCopyLabelId))) {
                    this.ocpcTargetVosAddElement(ocpcTargetVos, OcpcTargetEnum.FIRST_COMMENT_COPY);
                }
                //框下链接白名单
                this.generateUnderBoxLinkClick(accInfo, avId, ocpcTargetVos);
            }
            //动态
            if (ObjectUtils.equals(campaign.getPromotionPurposeType(), PromotionPurposeType.DYNAMIC.getCode())) {
                //动态详情浏览白名单
                if (accountLabelService.isAccountIdsInLabels(Collections.singletonList(accInfo.getAccountDto().getAccountId())
                        , Collections.singletonList(dynamicAccountOcpmLabel))) {
                    this.ocpcTargetVosAddElement(ocpcTargetVos, OcpcTargetEnum.DYNAMIC_DETAIL_BROWSE);
                }
            }
        } else
            //是商业起飞
            if (Integer.valueOf(1).equals(accInfo.getAccountDto().getIsSupportFly())) {
                //稿件
                if (ObjectUtils.equals(campaign.getPromotionPurposeType(), PromotionPurposeType.ARCHIVE_CONTENT.getCode())) {
                    List<Long> buleMids = flyCreativeSceneComponent.getBlueMidsByAccountId(accInfo.getAccountDto().getAccountId());
                    FlyCampaignDetailDto flyCampaignDetailDto = flyCampaignService.getFlyCampaignById(campaign.getCampaignId());
                    Long archiveMid = this.generateMidByAid(Long.valueOf(flyCampaignDetailDto.getAvId()));
                    //是蓝V稿件 涨粉全量
                    if (buleMids.contains(archiveMid)) {
                        this.ocpcTargetVosAddElement(ocpcTargetVos, OcpcTargetEnum.USER_FOLLOW);
                    }
                    //播放全量
                    this.ocpcTargetVosAddElement(ocpcTargetVos, OcpcTargetEnum.VIDEO_PLAY);
                    //评论白名单
                    if (accountLabelService.isAccountIdsInLabels(Collections.singletonList(accInfo.getAccountDto().getAccountId())
                            , Collections.singletonList(firstCommentCopyLabelId))) {
                        this.ocpcTargetVosAddElement(ocpcTargetVos, OcpcTargetEnum.FIRST_COMMENT_COPY);
                    }
                    //框下链接白名单
                    this.generateUnderBoxLinkClick(accInfo, avId, ocpcTargetVos);
                }
                //动态
                if (ObjectUtils.equals(campaign.getPromotionPurposeType(), PromotionPurposeType.DYNAMIC.getCode())) {
                    this.ocpcTargetVosAddElement(ocpcTargetVos, OcpcTargetEnum.DYNAMIC_DETAIL_BROWSE);
                }
            }
        //直播间
        if (ObjectUtils.equals(campaign.getPromotionPurposeType(), PromotionPurposeType.LIVE_ROOM.getCode())) {
            //预约
            if (RoomPromoteScenesEnum.RESERVE.getCode().equals(roomPromoteScenes)) {
                this.ocpcTargetVosAddElement(ocpcTargetVos, OcpcTargetEnum.VIDEO_PLAY);
            }
        }
        if (purposeType == PromotionPurposeType.ACTIVITY) {
            //活动投放
            if (accountLabelService.isAccountIdsInLabels(Collections.singletonList(accInfo.getAccountDto().getAccountId())
                    , Collections.singletonList(activityOcpmLabel))) {
                ocpcTargetVosAddElement(ocpcTargetVos, OcpcTargetEnum.ACTIVITY_PAGE_PULL_UP);
            }
        }
        if (CollectionUtils.isEmpty(ocpcTargetVos)) {
            return MutablePair.of(false, Collections.emptyList());
        }
        return MutablePair.of(true, ocpcTargetVos);
    }

    private void generateUnderBoxLinkClick(AccountAllInfoDto accInfo, String avId, List<OcpcTargetVo> ocpcTargetVos) {
        if (accountLabelService.isAccountIdsInLabels(Collections.singletonList(accInfo.getAccountDto().getAccountId())
                , Collections.singletonList(underBoxLinkClickLabelId))) {
            List<Long> avIdist = new ArrayList<>();
            avIdist.add(Long.valueOf(avId));
            //花火avId
            List<PickupOrderDto> pickupOrderDtos = pickupOrderQuerier.searchPickupOrder(avIdist);
            //是商单稿件
            if (!CollectionUtils.isEmpty(pickupOrderDtos)) {
                this.ocpcTargetVosAddElement(ocpcTargetVos, OcpcTargetEnum.UNDER_BOX_LINK_CLICK);
            }
        }
    }

    private void ocpcTargetVosAddElement(List<OcpcTargetVo> ocpcTargetVos, OcpcTargetEnum ocpcTargetEnum) {
        ocpcTargetVos.add(OcpcTargetVo.builder()
                .id(ocpcTargetEnum.getCode())
                .name(ocpcTargetEnum.getDesc())
                .enable(false)
                .build());
    }

    @ApiOperation(value = "查询UP主信息")
    @RequestMapping(value = "/up_info", method = RequestMethod.GET)
    public Response<List<UpUserInfoVo>> getUpInfos(@ApiIgnore Context context,
                                                   @RequestParam("mids") List<Long> mids) throws ServiceException {
        if (CollectionUtils.isEmpty(mids)) {
            return Response.SUCCESS(Collections.emptyList());
        }
        Map<Long, UserInfoDto> userMapInMids = passportService.getUserMapInMids(
                mids.stream().distinct().collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(userMapInMids)) {
            return Response.SUCCESS(Collections.emptyList());
        }
        Map<Long, Integer> userRelationStatMap = passportService.getUserRelationStatMapInMids(
                mids.stream().distinct().collect(Collectors.toList()))
                .entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().getFollower()));
        return Response.SUCCESS(userMapInMids.values().stream()
                .map(user -> UpUserInfoVo.builder()
                        .mid(user.getMid())
                        .name(user.getName() + getFansShow(userRelationStatMap.get(user.getMid())))
                        .face(user.getFace())
                        .build())
                .collect(Collectors.toList()));
    }

    private static String getFansShow(Integer fans) {
        String fansStr = " (粉丝数:";
        if (fans == null) {
            fansStr += "0";
        } else if (fans >= 10000) {
            fansStr += BigDecimal.valueOf(fans / 10000.0).setScale(1, BigDecimal.ROUND_HALF_UP) + "万";
        } else {
            fansStr += fans;
        }
        return fansStr + ")";
    }

    @ApiOperation(value = "最低出价")
    @RequestMapping(value = "/lowest_bid", method = RequestMethod.GET)
    public Response<BigDecimal> getLowestBidCost(@ApiIgnore Context context,
                                                 @RequestParam(value = "slot_group_id", required = false) Integer slotGroupId,
                                                 @RequestParam(value = "offer_type") Integer offerType,
                                                 @RequestParam(value = "campaign_id") Integer campaignId,
                                                 @RequestParam(value = "adp_version", required = false, defaultValue = "0")
                                                 @ApiParam("0-旧版, 2-起飞新版") Integer adpVersion) throws ServiceException {
        Integer saleType = offerType / 10;
        if (AdpVersion.isLegacy(adpVersion)) {
            Assert.notNull(slotGroupId, "旧版广告组id不能为空");
            if (!CollectionUtils.isEmpty(bidNoLimitAccountIds) && bidNoLimitAccountIds.contains(context.getAccountId())) {
                return Response.SUCCESS(Utils.fromFenToYuan(noLimitLowBid));
            }
        }

        CpcCampaignDto campaign = cpcCampaignService.loadCpcCampaignDto(campaignId);

        final int rp = cpcUnitService.getReservedPrice(GetBidCostParam.builder()
                .accountId(context.getAccountId())
                .slotGroupId(slotGroupId)
                .launchType(campaign.getPromotionPurposeType())
                .salesType(saleType)
                .build(), adpVersion);

        return Response.SUCCESS(Utils.fromFenToYuan(rp));
    }

    @ApiOperation(value = "列表最低出价展示")
    @RequestMapping(value = "/list_lowest_bid_show", method = RequestMethod.GET)
    public Response<BigDecimal> ListLowestBidShow(@ApiIgnore Context context,
                                                  @RequestParam(value = "unit_id", required = false) Integer unitId) throws ServiceException {
        CpcUnitDto cpcUnitDto = cpcUnitService.loadCpcUnit(unitId);
        if (ObjectUtils.equals(cpcUnitDto.getPromotionPurposeType(), PromotionPurposeType.ARCHIVE_CONTENT.getCode())) {
            if (ObjectUtils.equals(cpcUnitDto.getSalesType(), SalesType.CPM.getCode())) {
                if (ObjectUtils.equals(cpcUnitDto.getOcpcTarget(), 0)) {
                    return Response.SUCCESS(new BigDecimal(8.00));
                }
            }
        }
        return this.getLowestBidCost(context, cpcUnitDto.getSlotGroup(), cpcUnitDto.getSalesType() * 10,
                cpcUnitDto.getCampaignId(), cpcUnitDto.getAdpVersion());
    }

    @ApiOperation(value = "复制单元")
    @RequestMapping(value = "/copy", method = RequestMethod.POST)
    public Response<Object> copy(
            @ApiIgnore Context context,
            @RequestBody FlyProCopyVo vo) throws Exception {
        launchOfflineService.validateOfflineStage(context.getAccountId(), true);
        Assert.isTrue(vo.getSource_unit_id() != null && vo.getSource_unit_id() > 0, "源单元id必须大于0");
        //新建单元
        FlyCampaignDetailDto flyCampaignDetailDto = flyCampaignService.getFlyCampaignById(vo.getCampaign_id());
        Assert.isTrue(context.getAccountId().equals(flyCampaignDetailDto.getAccountId()), "您不能在不属于您的计划下创建单元");
        Assert.isTrue(!flyCampaignDetailDto.isGdPlus(), "您不能在复制gd+单元");
        boolean inCantEditAdp24LabelId = accountLabelService.isAccountIdsInLabels(Collections.singletonList(context.getAccountId()), Collections.singletonList(cantEditAdp24LabelId));
        //【【专业起飞】商业起飞CPC出价方式下线】 https://www.tapd.bilibili.co/********/prong/stories/view/11********002820346
        boolean isCpc = Objects.equals(vo.getOffer_type(), OfferTypeEnum.CPC.getCode());
        Assert.isTrue(!inCantEditAdp24LabelId && !isCpc, "该版本广告单元已不再支持新建或复制，请新建新版本广告体验更好的广告能力");
        AccountAllInfoDto accountAllInfoDto = queryAccountService.getAccountAllInfoFromCache(context.getAccountId());
        //单元的adpVersion跟账号,计划gd_plus走
        int adpVersion = launchCommonService.getFlyUnitAdpVersion(accountAllInfoDto, flyCampaignDetailDto.isGdPlus());
        AccountBaseDto accountBaseDto = queryAccountService.getAccountBaseDtoByIdFromCache(context.getAccountId());
        //新起飞isNewFly写死1
        Long mid = this.generateUnitArchiveMid(accountBaseDto, flyCampaignDetailDto);
        FlyProUnitNewVo flyProUnitNewVo = new FlyProUnitNewVo();
        BeanUtils.copyProperties(vo, flyProUnitNewVo);
        //复制创意
        NewCpcUnitDto newCpcUnitDto = flyUnitConverter.createVo2Dto(flyProUnitNewVo, flyCampaignDetailDto, mid, adpVersion);
        List<Integer> ans = flyCreativeService.copy(adpVersion, newCpcUnitDto, getOperator(context), vo.getSource_unit_id(), vo.getSource_creative_ids());
        //发送推审消息
        cpcCreativeHelper.flyProCreativeAuditPub(ans);
        //发送原生推审消息
        List<NativeCreativeJudgeResultDto> nativeCreativeJudgeResultDtos = nativeAdJudger.judgeIsNativeByCreativeIds(ans);
        List<NativeMsgItem> nativeMsgItemList = nativeCreativeJudgeResultDtos.stream()
                .filter(nativeCreativeJudgeResultDto -> BooleanUtils.isTrue(nativeCreativeJudgeResultDto.getIsNativeCreative()))
                .map(nativeCreativeJudgeResultDto -> {
                    NativeMsgItem nativeMsgItem = new NativeMsgItem();
                    nativeMsgItem.setCreativeId(nativeCreativeJudgeResultDto.getCreativeId());
                    nativeMsgItem.setAvid(nativeCreativeJudgeResultDto.getAvid());
                    return nativeMsgItem;
                }).collect(Collectors.toList());
        nativeCreativeRelativityAuditDatabusPub.pubMsg(NativeCreativeRelativityAuditDatabusMessage.builder().items(nativeMsgItemList).build());
        return Response.SUCCESS(ans);
    }

    @ApiOperation(value = "ocpm最低出价")
    @RequestMapping(value = "/lowest_bid_ocpm", method = RequestMethod.GET)
    public Response<BigDecimal> getLowestBidOcpmCost(@ApiIgnore Context context,
                                                     @ApiParam("目标状态 9-稿件播放 10-用户关注 25-动态详情页浏览")
                                                     @RequestParam(value = "ocpm_target", required = false) Integer ocpmTarget) {
        AccountAllInfoDto accountAllInfoDto = queryAccountService.getAccountAllInfoFromCache(context.getAccountId());
        BigDecimal lowestBidPrice = flyUnitConverter.getLowestBidOcpmCost(ocpmTarget, accountAllInfoDto);
        if (lowestBidPrice == null) {
            return Response.FAIL(500, "不支持此ocpm目标");
        }
        return Response.SUCCESS(lowestBidPrice);
    }

    private Long generateUnitArchiveMid(AccountBaseDto accountBaseDto, FlyCampaignDetailDto flyCampaignDetailDto) throws ServiceException {
        //计划类型是稿件
        if (ACCOUNT_GROWTH.getCode().equals(flyCampaignDetailDto.getLaunchTarget()) ||
                TRAFFIC_BOOST.getCode().equals(flyCampaignDetailDto.getLaunchTarget())) {
            //商业起飞
            if (TRAFFIC_BOOST.getCode().equals(flyCampaignDetailDto.getLaunchTarget())) {
                List<Long> blueMids = flyCreativeSceneComponent.getBlueMidsByAccountId(accountBaseDto.getAccountId());
                Long archiveMid = this.generateMidByAid(Long.valueOf(flyCampaignDetailDto.getAvId()));
                //是蓝V稿件,建单元时mid传稿件mid（让数据正确归因）
                if (blueMids.contains(archiveMid)) {
                    return archiveMid;
                } else {
                    //否则传账号mid（不让数据归因上）
                    return accountBaseDto.getMid();
                }
            } else {
                //内容起飞代投，建单元的时mid传稿件mid（让数据正确归因）
                if (flyCampaignDetailDto.getProxyMid() != null && flyCampaignDetailDto.getProxyMid() > 0) {
                    return flyCampaignDetailDto.getProxyMid();
                }
                //其它内容起飞，建单元的时mid传账号mid（内容起飞只会投自己mid的稿件，可归因上）
                return accountBaseDto.getMid();
            }
        }
        //计划类型是直播间
        if (LIVE_PROMOTION.getCode().equals(flyCampaignDetailDto.getLaunchTarget()) ||
                BUSINESS_LIVE_PROMOTION.getCode().equals(flyCampaignDetailDto.getLaunchTarget())) {
            if (RoomPromoteScenesEnum.RESERVE.getCode().equals(flyCampaignDetailDto.getRoomPromoteScenes())) {
                //商业起飞
                if (BUSINESS_LIVE_PROMOTION.getCode().equals(flyCampaignDetailDto.getLaunchTarget())) {
                    List<Long> blueMids = flyCreativeSceneComponent.getBlueMidsByAccountId(accountBaseDto.getAccountId());
                    Long archiveMid = this.generateMidByAid(Long.valueOf(flyCampaignDetailDto.getAvId()));
                    //是蓝V稿件,建单元时mid传稿件mid（让数据正确归因）
                    if (blueMids.contains(archiveMid)) {
                        return archiveMid;
                    } else {
                        //否则传账号mid（不让数据归因上）
                        return accountBaseDto.getMid();
                    }
                } else {
                    //内容起飞，建单元的时mid传账号mid（内容起飞只会投自己mid的稿件，可归因上）
                    return accountBaseDto.getMid();
                }
            }
        }
        return accountBaseDto.getMid();
    }

    public Long generateMidByAid(Long aid) throws ServiceException {
        ArchiveDetail archiveDetail = archiveManager.queryArchivesByAid(aid);
        Long archiveMid = Optional.ofNullable(archiveDetail)
                .map(o -> o.getArchive())
                .map(o -> o.getMid())
                .orElse(-1L);
        return archiveMid;
    }

    @ApiOperation(value = "次日预算调整")
    @RequestMapping(value = "/next_day_budget", method = RequestMethod.POST)
    public Response<Void> updateNextDayUnitBudget(@ApiIgnore Context context,
                                                  @ApiParam("次日预算信息") @RequestBody FlyProNextDayBudgetVo nextDayBudgetVo) throws ServiceException {

        Assert.notNull(nextDayBudgetVo.getUnit_id(), "单元id不能为空");
        LauUnitBaseDto unitInfo = lauUnitService.getBaseDtoById(nextDayBudgetVo.getUnit_id());
        Assert.isTrue(unitInfo != null && unitInfo.getIsManaged() == 0, "托管单元不支持修改次日预算");

        flyUnitService.setUnitNextDayBudget(nextDayBudgetVo.getUnit_id(), nextDayBudgetVo.getNext_day_budget(), getOperator(context));

        return Response.SUCCESS();
    }

    @ApiOperation(value = "查询次日预算")
    @RequestMapping(value = "/next_day_budget", method = RequestMethod.GET)
    public Response<BigDecimal> queryNextDayUnitBudget(@ApiParam("单元id") @RequestParam(value = "unit_id") Integer unitId) throws ServiceException {

        Assert.notNull(unitId, "单元id不能为空");

        Long nextDayBudget = flyUnitService.getNextDayBudget(unitId);

        return Response.SUCCESS(nextDayBudget == null ? new BigDecimal(-1) : Utils.fromFenToYuan(nextDayBudget));
    }

    @ApiOperation(value = "删除次日预算")
    @RequestMapping(value = "/next_day_budget", method = RequestMethod.DELETE)
    public Response<Void> deleteNextDayUnitBudget(@ApiIgnore Context context,
                                                  @ApiParam("单元id") @RequestParam(value = "unit_id") Integer unitId) throws ServiceException {

        Assert.notNull(unitId, "单元id不能为空");
        LauUnitBaseDto unitInfo = lauUnitService.getBaseDtoById(unitId);
        Assert.isTrue(unitInfo != null && unitInfo.getIsManaged() == 0, "托管单元不支持修改次日预算");

        flyUnitService.deleteNextDayBudget(unitId, getOperator(context));

        return Response.SUCCESS();
    }

    @ApiOperation("查询每日库存和每日单价")
    @RequestMapping(value = "/stock", method = RequestMethod.POST)
    public Response<StockPriceVo> queryStock(@ApiIgnore Context context,
                                             @RequestBody QueryStockVo queryStockVo) throws ServiceException {
        List<Long> excludeSchedules = new ArrayList<>();
        Long oldTotalCpm = 0L;
        List<SplitDaysImpress4QueryStockVo> oldSplitList = new ArrayList<>();
        if (queryStockVo.getUnit_id() != null) {
            CpcUnitDto cpcUnitDto = cpcUnitService.loadCpcUnit(queryStockVo.getUnit_id());
            List<SplitDaysImpressDto> splitDaysImpressDtos = cpcUnitDto.getSplitDaysImpressDtos();
            if (!CollectionUtils.isEmpty(splitDaysImpressDtos)) {
                for (SplitDaysImpressDto o : splitDaysImpressDtos) {
                    List<Long> scheduleIds = JSON.parseArray(o.getScheduleIds(), Long.class);
                    excludeSchedules.addAll(scheduleIds);
                    //过去的排期
                    if (Utils.getNow().compareTo(o.getScheduleDate()) > 0) {
                        oldTotalCpm += o.getAvailableCpm();
                        oldSplitList.add(SplitDaysImpress4QueryStockVo.builder()
                                .schedule_date(o.getScheduleDate())
                                .available_cpm(o.getAvailableCpm())
                                .prices(flyUnitConverter.platformDto2Vo(o.getPlatformType(), o.getPlatformPerPrice()))
                                .build());
                    }
                }
            }
        }
        FlyStockPriceDto dto = stockService.flyQueryStock(FlyStockTargetInfo.builder()
                .accountId(context.getAccountId())
                .scheduleIds(excludeSchedules)
                .targetingInfo(FlyTargetingInfo.builder()
                        .age(queryStockVo.getAge())
                        .gender(queryStockVo.getGender())
                        .area(queryStockVo.getArea())
                        .os(queryStockVo.getOs())
                        .inlineSalesType(this.gdPlusTemplateId2InlineSalesType(queryStockVo.getTemplate_id()))
                        .build())
                .scheduleDate(queryStockVo.getSchedule_date())
                .templateId(queryStockVo.getTemplate_id())
                .platformIds(flyUnitConverter.osTarget2PlatformIds(queryStockVo.getOs()))
                .frequencyUnit(1)
                .frequencyLimit(1)
                .dealSeq(queryStockVo.getDeal_seq())
                .areaGroupId(queryStockVo.getGd_plus_area_group_id())
                .isToday(Integer.valueOf(1).equals(queryStockVo.getIs_today()))
                .hour(queryStockVo.getHour())
                .build());
        //已经查出结果，补充查出过去时间的单价和库存
        if (dto.getIsDealFinish()) {
            List<FlySplitDaysStockDto> notZeroDtos = dto.getSplitDaysStocks()
                    .stream().filter(o -> o.getStockCpm() != null && o.getStockCpm() > 0)
                    .collect(Collectors.toList());
            List<SplitDaysImpress4QueryStockVo> noZerosplitDaysImpress4QueryStockVos = flyUnitConverter
                    .flySplitDaysStockDtos2Vos(notZeroDtos);
            Long total_cpm = dto.getStockCpm() == null ? 0L : dto.getStockCpm();
            if(!CollectionUtils.isEmpty(oldSplitList)) {
                noZerosplitDaysImpress4QueryStockVos.addAll(oldSplitList);
                total_cpm += oldTotalCpm;
            }
            return Response.SUCCESS(StockPriceVo.builder()
                    .deal_seq(dto.getDealSeq())
                    .is_deal_finish(dto.getIsDealFinish())
                    .split_days_impress_list(noZerosplitDaysImpress4QueryStockVos)
                    .total_cpm(total_cpm)
                    .build());
        }else {
            return Response.SUCCESS(StockPriceVo.builder()
                    .deal_seq(dto.getDealSeq())
                    .is_deal_finish(dto.getIsDealFinish())
                    .split_days_impress_list(new ArrayList<>())
                    .total_cpm(0L)
                    .build());
        }
    }

    private List<Integer> gdPlusTemplateId2InlineSalesType(Integer templateId) {
        //展示方式普通
        if (flyGdPlusConfig.getFlyInfoMsgTemplate().equals(templateId) ||
                flyGdPlusConfig.getFlyPlayPageTemplate().equals(templateId)) {
            return Arrays.asList(479);
        }
        //展示方式常规首刷
        if (flyGdPlusConfig.getFlyBigCardTemplate().equals(templateId) ||
                flyGdPlusConfig.getFlyBigCard5Template().equals(templateId)) {
            return Arrays.asList(480);
        }
        return new ArrayList<>();
    }

    @ApiOperation("查询每日总价")
    @RequestMapping(value = "/price", method = RequestMethod.POST)
    public Response<QueryPriceResponseVo> queryPrice(@ApiIgnore Context context,
                                                     @RequestBody QueryPriceVo queryPriceVo) throws Exception {
        //当天
        if (Integer.valueOf(1).equals(queryPriceVo.getIs_today())) {
            //编辑单元查DB
            if (queryPriceVo.getUnit_id() != null) {
                CpcUnitDto unit = cpcUnitService.loadCpcUnit(queryPriceVo.getUnit_id());
                List<SplitDaysImpressDto> splitDaysImpressDtos = unit.getSplitDaysImpressDtos();
                List<SplitDaysImpress4QueryPriceResponseVo> everyDayPrice = splitDaysImpressDtos.stream().map(o -> SplitDaysImpress4QueryPriceResponseVo.builder()
                        .schedule_date(o.getScheduleDate())
                        .price(Utils.fromFenToYuan(o.getPrice()))
                        .build()).collect(Collectors.toList());
                return Response.SUCCESS(QueryPriceResponseVo.builder()
                        .total_price(Utils.fromFenToYuan(splitDaysImpressDtos.get(0).getPrice()))
                        .every_day_price(everyDayPrice)
                        .build());
            } else {
                //新建单元根据请求参数查
                Map<Timestamp, Long> dayFlag = new HashMap<>();
                dayFlag.put(queryPriceVo.getSplit_days_impress_list().get(0).getSchedule_date(), 0L);
                Map<Timestamp, Long> dayImpression = new HashMap<>();
                dayImpression.put(queryPriceVo.getSplit_days_impress_list().get(0).getSchedule_date(),
                        queryPriceVo.getSplit_days_impress_list().get(0).getImpression_cpm());
                FlyPriceResponseDto flyPriceResponseDto = stockService.flyQueryTotalPrice(FlyPriceQueryDto.builder()
                        .dealSeq(queryPriceVo.getDeal_seq())
                        .dayImpression(dayImpression)
                        .dayFlag(dayFlag)
                        .isToday(Integer.valueOf(1).equals(queryPriceVo.getIs_today()))
                        .hour(queryPriceVo.getHour())
                        .build());
                List<SplitDaysImpress4QueryPriceResponseVo> everyDayPrice = this.getEveryDayPriceMap2List(flyPriceResponseDto.getEveryDayPrice());
                BigDecimal totalPrice = flyPriceResponseDto.getTotalPrice();
                return Response.SUCCESS(QueryPriceResponseVo.builder()
                        .total_price(totalPrice)
                        .every_day_price(everyDayPrice)
                        .build());
            }
        }
        //非当天
        //筛选出未来时间
        List<SplitDaysImpress4QueryPriceVo> splitDaysImpress4QueryPriceVos = queryPriceVo.getSplit_days_impress_list()
                .stream().filter(o -> Utils.getNow().compareTo(o.getSchedule_date()) < 0).collect(Collectors.toList());
        //并标记哪些新增修改的,flag=0表示新增/修改的,否则保存原价格
        Map<Timestamp, Long> dayFlag = new HashMap<>();
        BigDecimal oldTotalPrice = new BigDecimal("0");
        List<SplitDaysImpress4QueryPriceResponseVo> oldEveryDayPrice = new ArrayList<>();
        if (queryPriceVo.getUnit_id() == null) {
            splitDaysImpress4QueryPriceVos.forEach(o -> {
                dayFlag.put(o.getSchedule_date(), 0L);
            });
        } else {
            CpcUnitDto unit = cpcUnitService.loadCpcUnit(queryPriceVo.getUnit_id());
            List<SplitDaysImpressDto> splitDaysImpressDtos = unit.getSplitDaysImpressDtos();
            //给未来排期的dayFlag赋值
            if (!CollectionUtils.isEmpty(splitDaysImpressDtos)) {
                Map<Timestamp, Long> showCpmMap = splitDaysImpressDtos.stream().collect(Collectors.toMap(SplitDaysImpressDto::getScheduleDate, SplitDaysImpressDto::getShowCpm));
                Map<Timestamp, Long> priceMap = splitDaysImpressDtos.stream().collect(Collectors.toMap(SplitDaysImpressDto::getScheduleDate, SplitDaysImpressDto::getPrice));
                splitDaysImpress4QueryPriceVos.forEach(o -> {
                    if (o.getImpression_cpm().equals(showCpmMap.get(o.getSchedule_date()))) {
                        dayFlag.put(o.getSchedule_date(), priceMap.get(o.getSchedule_date()));
                    } else {
                        dayFlag.put(o.getSchedule_date(), 0L);
                    }
                });
                for (SplitDaysImpressDto o : splitDaysImpressDtos) {
                    //过去的排期
                    if (Utils.getNow().compareTo(o.getScheduleDate()) > 0) {
                        oldTotalPrice = oldTotalPrice.add(Utils.fromFenToYuan(o.getPrice()));
                        oldEveryDayPrice.add(SplitDaysImpress4QueryPriceResponseVo.builder()
                                .schedule_date(o.getScheduleDate())
                                .price(Utils.fromFenToYuan(o.getPrice()))
                                .build());
                    }
                }
            } else {
                //没有排期，请求的所有排期都是想要新增的
                splitDaysImpress4QueryPriceVos.forEach(o -> {
                    dayFlag.put(o.getSchedule_date(), 0L);
                });
            }
        }
        //约量
        Map<Timestamp, Long> dayImpression = new HashMap<>();
        splitDaysImpress4QueryPriceVos.forEach(o -> {
            dayImpression.put(o.getSchedule_date(), o.getImpression_cpm());
        });
        FlyPriceResponseDto flyPriceResponseDto = stockService.flyQueryTotalPrice(FlyPriceQueryDto.builder()
                .dealSeq(queryPriceVo.getDeal_seq())
                .dayImpression(dayImpression)
                .dayFlag(dayFlag)
                .isToday(Integer.valueOf(1).equals(queryPriceVo.getIs_today()))
                .hour(queryPriceVo.getHour())
                .build());
        List<SplitDaysImpress4QueryPriceResponseVo> everyDayPrice = this.getEveryDayPriceMap2List(flyPriceResponseDto.getEveryDayPrice());
        BigDecimal totalPrice = flyPriceResponseDto.getTotalPrice();
        //补充查出过去时间的总价并累加每天总价
        if (!CollectionUtils.isEmpty(oldEveryDayPrice)) {
            everyDayPrice.addAll(oldEveryDayPrice);
            totalPrice = totalPrice.add(oldTotalPrice);
        }
        return Response.SUCCESS(QueryPriceResponseVo.builder()
                .total_price(totalPrice)
                .every_day_price(everyDayPrice)
                .build());
    }

    private List<SplitDaysImpress4QueryPriceResponseVo> getEveryDayPriceMap2List(
            Map<Timestamp, BigDecimal> getEveryDayPrice) {
        List<SplitDaysImpress4QueryPriceResponseVo> ans = new ArrayList<>();
        //排序
        Map<Timestamp, BigDecimal> map = new TreeMap<>();
        map.putAll(getEveryDayPrice);
        map.forEach((k, v) -> {
            ans.add(SplitDaysImpress4QueryPriceResponseVo.builder()
                    .schedule_date(k)
                    .price(v)
                    .build());
        });
        return ans;
    }
}
