package com.bilibili.adp.advertiser.portal.webapi.v2.account;

import com.bapis.ad.account.crm.acc.AccountBase;
import com.bilibili.adp.advertiser.portal.common.BasicController;
import com.bilibili.adp.advertiser.portal.common.Context;
import com.bilibili.adp.advertiser.portal.webapi.v2.account.vo.BrandInfoVo;
import com.bilibili.adp.advertiser.portal.webapi.v2.account.vo.BrandPushRequestVo;
import com.bilibili.adp.advertiser.portal.webapi.v2.account.vo.LauAccountCorpMidMappingVo;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.enums.AuditStatus;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.biz.services.account.AdpCpcAccountMidBindingService;
import com.bilibili.adp.cpc.biz.services.account.AdpCpcAccountService;
import com.bilibili.adp.cpc.biz.services.account.AdpCpcCorpInfoService;
import com.bilibili.adp.cpc.biz.services.account.bos.CorpInfoBo;
import com.bilibili.adp.cpc.biz.services.common.SimpleBFSService;
import com.bilibili.adp.cpc.biz.services.common.bos.BFSInfoBo;
import com.bilibili.adp.cpc.utils.Response;
import com.bilibili.adp.launch.biz.common.LaunchUtil;
import com.bilibili.adp.launch.biz.service.account.LaunchAccountGroupService;
import com.bilibili.adp.cpc.biz.services.brand.LaunchBrandInfoService;
import com.bilibili.adp.cpc.biz.bos.brand.LauAccountInfoBo;
import com.bilibili.adp.resource.api.app_package.dto.AppPackageDto;
import com.bilibili.adp.web.framework.core.Pagination;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.SneakyThrows;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import java.io.InputStream;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.bilibili.adp.cpc.biz.constants.Constants.NEW_API_VERSION;

@Api(tags = NEW_API_VERSION)
@RestController
@RequestMapping("/web_api/v2/account")
public class AdpCpcAccountController extends BasicController {
    private final LaunchBrandInfoService launchBrandInfoService;
    private final AdpCpcAccountMidBindingService midBindingService;
    private final AdpCpcCorpInfoService corpInfoService;
    private final LaunchAccountGroupService accountGroupService;
    private final SimpleBFSService simpleBFSService;

    @Autowired
    private AdpCpcAccountService adpCpcAccountService;

    @Value("${bfs.category.face:face}")
    private String dir;

    public AdpCpcAccountController(LaunchBrandInfoService launchBrandInfoService,
                                   AdpCpcAccountMidBindingService midBindingService,
                                   AdpCpcCorpInfoService corpInfoService,
                                   LaunchAccountGroupService accountGroupService,
                                   SimpleBFSService simpleBFSService) {
        this.launchBrandInfoService = launchBrandInfoService;
        this.midBindingService = midBindingService;
        this.corpInfoService = corpInfoService;
        this.accountGroupService = accountGroupService;
        this.simpleBFSService = simpleBFSService;
    }

    @ApiOperation("是否支持 nobid")
    @PostMapping("/support_nobid")
    public Response<Boolean> supportNobid(@ApiIgnore Context context,
                                          @RequestParam("campaign_id") @ApiParam("计划id") Integer campaignId,
                                          @RequestParam("adp_version") @ApiParam("adpVersion") Integer adpVersion) {
        final int aid = context.getAccountId();
        Boolean result = adpCpcAccountService.supportNobid(aid, campaignId, adpVersion);
        return Response.ok(result);
    }

    @SneakyThrows
    @ApiOperation("品牌信息上传图片")
    @PostMapping("/brand_info/upload_face")
    public Response<BrandInfoVo> uploadBrandInfoFace(@RequestParam("file") MultipartFile multipartFile) {
        try (final InputStream is = multipartFile.getInputStream()) {
            final String ext = simpleBFSService.getExt(multipartFile.getOriginalFilename());
            final BFSInfoBo bo = simpleBFSService.upload(dir, multipartFile.getContentType(), IOUtils.toByteArray(is), ext);
            return Response.ok(BrandInfoVo.builder()
                    .face(bo.getUrl())
                    .faceMd5(bo.getMd5())
                    .build());
        }
    }

    @ApiOperation("删除品牌信息")
    @DeleteMapping("/brand_info/delete")
    public Response<Void> deleteBrandInfo(@ApiIgnore Context context, @RequestParam Integer id) {
        launchBrandInfoService.delete(context.getAccountId(), id);
        return Response.ok();
    }

    @ApiOperation("新建品牌信息")
    @PostMapping("/brand_info/save")
    public Response<Integer> saveBrandInfo(@ApiIgnore Context context, @RequestBody BrandInfoVo vo) {
        vo.setAccountId(context.getAccountId());
        vo.setStatus(AuditStatus.ACCEPT.getCode());
        if (Utils.isPositive(vo.getMid())) {
            vo.setSpace(LaunchUtil.BILIBILI_SPACE_PREFIX + vo.getMid());
        }
        final Integer id = launchBrandInfoService.create(BrandInfoVo.toBo(vo));
        return Response.ok(id);
    }

    //分页 推送查询
    @ApiOperation("品牌信息下拉列表")
    @GetMapping("/brand_info")
    public Response<List<BrandInfoVo>> getBrandInfo(@ApiIgnore Context context) {
        final Integer aid = context.getAccountId();
        final List<LauAccountInfoBo> bos = launchBrandInfoService.list(aid);
        final List<BrandInfoVo> vos = bos.stream()
                .map(BrandInfoVo::fromBo)
                .collect(Collectors.toList());
        return Response.ok(vos);
    }

    @ApiOperation("品牌信息下拉列表-分页")
    @GetMapping("/brand_info/page_query")
    public Response<Pagination<List<BrandInfoVo>>> getBrandInfoByPage(@ApiIgnore Context context,
                                                                      @ApiParam("推送 0-否 1-是") @RequestParam(value = "is_push", required = false, defaultValue = "0") Integer isPush,
                                                                      @RequestParam(value = "page", defaultValue = "1") @ApiParam("页码") Integer page,
                                                                      @RequestParam(value = "size", defaultValue = "10") @ApiParam("每页条数") Integer size) {
        final Integer aid = context.getAccountId();
        final PageResult<LauAccountInfoBo> pageBos = launchBrandInfoService.pageQuery(aid, page, size, isPush);
        final List<BrandInfoVo> vos = pageBos.getRecords().stream()
                .map(BrandInfoVo::fromBo)
                .collect(Collectors.toList());

        List<Integer> sourAccIds = vos.stream().map(BrandInfoVo::getCopySourceAccountId).distinct().collect(Collectors.toList());
        Map<Integer, AccountBase> sourceAppAccInfoMap = adpCpcAccountService.buildAccInfoMap(sourAccIds);
        vos.forEach(vo -> vo.setCopySourceAccountName(sourceAppAccInfoMap.getOrDefault(vo.getCopySourceAccountId(), AccountBase.getDefaultInstance()).getUserName()));

        return Response.ok(new Pagination<>(
                page,
                pageBos.getTotal(),
                vos));
    }

    @ApiOperation(value = "推送品牌")
    @RequestMapping(value = "/brand_info/push", method = RequestMethod.POST)
    public
    @ResponseBody
    Response<List<Integer>> pushBrand(@ApiIgnore Context context, @RequestBody BrandPushRequestVo vo) {
        List<Integer> bos = launchBrandInfoService.copyBrandInfo(super.getOperator(context), vo.getIds(), vo.getTo_acc_ids());
        return Response.ok(bos);
    }


    @ApiOperation("绑定的mid列表")
    @GetMapping("/corp/list")
    public Response<List<LauAccountCorpMidMappingVo>> getCorpList(@ApiIgnore Context context) {
        return Response.ok(midBindingService.listNew(context.getAccountId())
                .stream()
                .map(LauAccountCorpMidMappingVo::fromBo)
                .collect(Collectors.toList()));
    }

    @ApiOperation("获取并绑定mid(企业号绑定)")
    @PostMapping("/corp/fetch_and_bind")
    public Response<Void> bindCorpMid(@ApiIgnore Context context,
                                      @RequestParam("mid") @ApiParam("企业号mid") String mid) {
        final CorpInfoBo corpInfo = corpInfoService.getCorpInfo(Long.valueOf(mid));
        final Integer accountId = context.getAccountId();
        midBindingService.insertOrUpdateNew(accountId, corpInfo);
        return Response.ok();
    }

    @ApiOperation("绑定mid(企业号绑定)")
    @PostMapping("/corp/bind")
    public Response<Void> bindCorpMid(@ApiIgnore Context context,
                                      @RequestBody CorpInfoBo corpInfo) {
        final Integer accountId = context.getAccountId();
        midBindingService.insertOrUpdateNew(accountId, corpInfo);
        return Response.ok();
    }

    @ApiOperation("删除绑定的mid")
    @DeleteMapping("/corp/delete")
    public Response<Void> deleteCorpBinding(@ApiIgnore Context context,
                                            @RequestParam("id") @ApiParam("绑定关系id") Integer id) {
        midBindingService.delete(context.getAccountId(), id);
        return Response.ok();
    }

    @ApiOperation("获取mid信息")
    @GetMapping("/corp/info")
    public Response<CorpInfoBo> fetch(@ApiIgnore Context context,
                                   @RequestParam("mid") @ApiParam("企业号mid") String mid) {
        final CorpInfoBo corpInfo = corpInfoService.getCorpInfo(Long.valueOf(mid));
        return Response.ok(corpInfo);
    }


    @GetMapping("/corp/info/refreshDebug")
    public Response<Void> refreshTest(@ApiIgnore Context context, @RequestParam("mid") String mid) {
        midBindingService.refreshCorpInfo(context.getAccountId(), Long.valueOf(mid));
        return Response.ok();
    }

}
