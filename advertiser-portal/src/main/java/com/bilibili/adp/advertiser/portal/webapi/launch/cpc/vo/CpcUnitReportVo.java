/**
 * <AUTHOR>
 * @date 2018年1月11日
 */

package com.bilibili.adp.advertiser.portal.webapi.launch.cpc.vo;

import com.fasterxml.jackson.annotation.JsonUnwrapped;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CpcUnitReportVo {
    @ApiModelProperty("必选版本 0-老版 1-新版")
    private int adpVersion;
    private Integer speedMode;
    private Integer accountId;
    private Integer campaignId;
    private String campaignName;
    private Integer unitId;
    private String unitName;
    private Integer promotionPurposeType;
    private BigDecimal costPrice;

    private Timestamp ctime;
    private Integer ocpcTarget;

    private Long videoId;

    private Integer isNoBid;
    // nobid 上限
    private BigDecimal noBidMax;
    private BigDecimal budget;
    private Integer hasNextdayBudget;
    private BigDecimal nextdayBudget;
    /**
     * 次日预算是否开启重复
     */
    private Integer isRepeat;
    private Integer status;
    private String statusDesc;
    private String unitStatusMtime;
    private String beginDate;
    private String endDate;
    private Integer salesType;
    private String salesTypeDesc;
    private Integer salesMode;
    private String salesModeDesc;
    private Integer extraStatus;
    private String extraStatusDesc;
    private Integer slotGroupId;
    private String slotGroupName;
    private BigDecimal ocpxTargetOneBid;
    private Integer ocpxTargetTwo;
    private String ocpxTargetTwoDesc;
    private BigDecimal ocpxTargetTwoBid;

    private String createTime;

    private Integer isProgrammatic;

    // 游戏分包过期状态, 如果过期, 前端会做提示
    private Integer subPkgStatus;
    @ApiModelProperty(notes = "展示数量")
    private Integer showCount = 0;
    @ApiModelProperty(notes = "点击次数")
    private Integer clickCount = 0;
    @ApiModelProperty(notes = "点击率")
    private String clickRate;
    @ApiModelProperty(notes = "平均点击费用(元)")
    private BigDecimal costPerClick = BigDecimal.ZERO;
    @ApiModelProperty(notes = "平均千次展现费用(元)")
    private String averageCostPerThousand = "--";
    @ApiModelProperty(notes = "消费(元)")
    private BigDecimal cost = BigDecimal.ZERO;

    private long costMilli;

    @ApiModelProperty(value = "之前的预算")
    private BigDecimal beforeBudget;
    @ApiModelProperty("当日单元预算剩余修改次数")
    private Long budgetRemainingModifyTimes;
    @ApiModelProperty(value = "是否是历史创意: 0-否 1-是")
    private Integer isHistory;
    @ApiModelProperty(notes = "提交订单数")
    private Integer orderAddCount;
    @ApiModelProperty(notes = "提交订单金额")
    private BigDecimal orderAddPrice;
    @ApiModelProperty(notes = "注册数")
    private Integer registerCount;
    private String goodsConversionRate;
    private BigDecimal goodsRoi;
    @ApiModelProperty(notes = "游戏激活数")
    private Integer activateCount;
    @ApiModelProperty(notes = "游戏预约数")
    private Integer reserveCount;
    private Integer fanFollowCount;
    private Integer fanWhisperCount;
    private Integer iosActivateCount;
    private Integer androidActivateCount;
    private Integer playCount;
    private Integer under_box_link_click_count;
    private Integer first_comment_copy_count;
    @ApiModelProperty(notes = "涨粉成本(元)")
    private String costPerIncreaseFans = "--";
    @ApiModelProperty(notes = "激活成本(元)")
    private String costPerAppActivate = "--";
    @ApiModelProperty(notes = "游戏预约成本(元)")
    private String costPerGameReserve = "--";
    @ApiModelProperty(notes = "游戏激活成本(元)")
    private String costPerGameActivate = "--";
    @ApiModelProperty(notes = "注册成本(元)")
    private String costPerRegister = "--";
    @ApiModelProperty(notes = "播放成本(元)")
    private String costPerPlayCount = "--";
    @ApiModelProperty(notes = "播放率")
    private String playRate = "--";
    @ApiModelProperty(notes = "播转粉率")
    private String play2FansRate;
    @ApiModelProperty(notes = "框下链接点击成本(元)")
    private String cost_per_under_box_link_click = "--";
    @ApiModelProperty(notes = "框下链接点击率")
    private String under_box_link_click_rate;
    @ApiModelProperty(notes = "首条评论复制成本(元)")
    private String cost_per_first_comment_copy = "--";
    @ApiModelProperty(notes = "首条评论复制率")
    private String first_comment_copy_rate;

    @ApiModelProperty(notes = "涨粉率")
    private String fansFollowRate;
    @ApiModelProperty(notes = "激活率")
    private String appActivateRate;
    @ApiModelProperty(notes = "游戏预约率")
    private String gameReserveRate;
    @ApiModelProperty(notes = "游戏激活率")
    private String gameActivateRate;
    @ApiModelProperty(notes = "注册率")
    private String registerRate;
    @ApiModelProperty(notes = "表单提交数")
    private Integer formSubmitCount;
    @ApiModelProperty(notes = "表单提交成本(元)")
    private String costPerFormSubmit = "--";
    @ApiModelProperty(notes = "表单提交率")
    private String formSubmitRate;
    @ApiModelProperty(notes = "ocpc所处阶段 0-未开启 1-一阶段 2-二阶段")
    private Integer ocpxStage;
    @ApiModelProperty(notes = "ocpc所处阶段描述")
    private String ocpxStageDesc;
    @ApiModelProperty(notes = "ocpc二阶段转化目标成本")
    private BigDecimal twoStageBid;
    @ApiModelProperty(notes = "ocpc优化目标id")
    private Integer ocpxTargetId;
    @ApiModelProperty(notes = "ocpc优化目标")
    private String ocpxTargetDesc;
    @ApiModelProperty(notes = "ocpc第二目标所处阶段描述")
    private String ocpxTargetTwoStageDesc;
    @ApiModelProperty(notes = "安卓下载数")
    private Integer androidDownloadCount;
    @ApiModelProperty(notes = "安卓下载成本(元)")
    private String costPerAndroidDownload = "--";
    @ApiModelProperty(notes = "安卓下载率")
    private String androidDownloadRate;
    @ApiModelProperty(notes = "安卓安装数")
    private Integer androidInstallCount;
    @ApiModelProperty(notes = "安卓安装成本(元)")
    private String costPerAndroidInstall = "--";
    @ApiModelProperty(notes = "安卓安装率")
    private String androidInstallRate;
    @ApiModelProperty(notes = "系统")
    private String osDesc;
    private Boolean underbid;
    @ApiModelProperty(notes = "成功调起数")
    private Integer appCallupCount;
    @ApiModelProperty(notes = "调起成功成本")
    private BigDecimal appCallupCost;
    @ApiModelProperty(notes = "调起成功率")
    private String appCallupRatio;
    @ApiModelProperty(notes = "落地页调起店铺数")
    private Integer lpCallupCount;
    @ApiModelProperty(notes = "落地页调起店铺成本")
    private BigDecimal lpCallupCost;
    @ApiModelProperty(notes = "落地页调起店铺率")
    private String lpCallupRatio;
    @ApiModelProperty(notes = "提交订单成本")
    private BigDecimal orderSubmitCost;
    @ApiModelProperty(notes = "提交订单率")
    private String orderSubmitRate;
    @ApiModelProperty(notes = "OCPX赔付状态")
    private String ocpxAutoPayFinalStatus = "--";
    @ApiModelProperty(notes = "OCPX赔付浮窗状态")
    private List<UnitOcpxFloatStatusVo> ocpxAutoPayFloatStatusList;
    @ApiModelProperty(notes = "OCPX进入二阶段时间")
    private String twoStageTime;
    @ApiModelProperty(notes = "应用内付费数")
    private Integer orderCount;
    @ApiModelProperty(notes = "应用内付费金额")
    private BigDecimal orderPayment;
    @ApiModelProperty(notes = "应用内首次付费数")
    private Integer orderFirstCount;
    @ApiModelProperty(notes = "应用内首次付费金额")
    private BigDecimal orderFirstPayment;

    /**
     * 首日付费相关
     */
    @ApiModelProperty(notes = "首日付费数")
    private Integer firstDayPayCount;
    @ApiModelProperty(notes = "首日付费金额")
    private BigDecimal firstDayPayAmount;

    @ApiModelProperty(notes = "活动页浮层拉起数")
    private Integer activityPagePullUpCount;

    @ApiModelProperty(notes = "活动页浮层拉起成本(元)")
    private BigDecimal activityPagePullUpCost;

    private BigDecimal activityPagePullUpRate;

    @ApiModelProperty(notes = "是否是托管")
    private boolean isManaged;

    @ApiModelProperty("搜索词明投出价系数（针对`所有广告`类型）")
    private String searchPriceCoefficient;

    @ApiModelProperty("搜索词明投首位出价系数（针对`所有广告`类型）")
    private String searchFirstPriceCoefficient;

    @JsonUnwrapped
    private CpcReportCommonColumnVo cpcReportCommonColumnVo;


    private Integer dynamicDetailPageBrowseCount;
    private BigDecimal dynamicDetailPageBrowseCost;
    private BigDecimal dynamicDetailPageBrowseRate;

    @ApiModelProperty(notes = "单元赔付周期类型:-1:未知 0:3天*5周期 1:7天*2周期 2:6天*1周期")
    private Integer unitPeriodType;

}
