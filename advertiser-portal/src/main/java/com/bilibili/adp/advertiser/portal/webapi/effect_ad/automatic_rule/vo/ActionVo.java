package com.bilibili.adp.advertiser.portal.webapi.effect_ad.automatic_rule.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ActionVo {
    @ApiModelProperty(value = "行为id")
    private Long actionId;
    @ApiModelProperty(value = "操作对象：1-账户预算, 2-单元预算, 3-单元出价, 4-暂停单元")
    @Range(min = 1, max = 4, message = "错误的操作对象")
    private Integer subject;
    @ApiModelProperty(value = "调整方向/内容: 1-提高,2-降低,3-调整至")
    @Range(min = 1, max = 3, message = "错误的调整方向/内容")
    private Integer actionType;
    @ApiModelProperty(value = "值类型：1-数值，2-百分比")
    @Range(min = 1, max = 2, message = "错误的值类型")
    private Integer valueType;
    @ApiModelProperty(value = "值")
    private String value;
    @ApiModelProperty(value = "上限类型：0-不限，1-指定上限")
    @Range(min = 0, max = 1, message = "错误的上限类型")
    private Integer limitType;
    @ApiModelProperty(value = "上限值")
    private String limitValue;
    @ApiModelProperty(value = "调整频次：1-每天，2-每周")
    @Range(min = 1, max = 2, message = "错误的调整频次")
    private Integer frequencyScope;
    @ApiModelProperty(value = "调整次数：0-不限，1-1次，2-2次，3-3次，4-4次，5-5次")
    @Range(min = 0, max = 5, message = "错误的调整次数")
    private Integer frequencyCount;
}
