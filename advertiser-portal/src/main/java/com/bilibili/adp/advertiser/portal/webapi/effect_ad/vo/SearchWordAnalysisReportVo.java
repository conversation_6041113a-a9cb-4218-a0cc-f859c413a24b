package com.bilibili.adp.advertiser.portal.webapi.effect_ad.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @Description 
 * <AUTHOR>
 * @date 4/1/24
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class SearchWordAnalysisReportVo {
    // 汇总属性 start region
    @ApiModelProperty("日期")
    @ExcelProperty(value = "日期")
    private String date;
    // 汇总属性 end region

    @ApiModelProperty("搜索词")
    @ExcelProperty(value = "搜索词")
    private String searchKey;

    @ExcelProperty(value = "创意id")
    @ApiModelProperty(notes = "创意id")
    private Integer creativeId;

    @ExcelProperty(value = "单元id")
    @ApiModelProperty(notes = "单元id")
    private Integer unitId;

    @ExcelProperty(value = "计划id")
    @ApiModelProperty(notes = "计划id")
    private Integer campaignId;

    @ExcelProperty(value = "计划名称")
    @ApiModelProperty(notes = "计划名称")
    private String campaignName;

    @ExcelProperty(value = "单元名称")
    @ApiModelProperty(notes = "单元名称")
    private String unitName;

    @ExcelProperty(value = "创意名称")
    @ApiModelProperty(notes = "创意名称")
    private String creativeName;

    @ExcelProperty(value = "账号id")
    @ApiModelProperty("账号id")
    private Integer accountId;

    @ExcelProperty(value = "账号名称")
    @ApiModelProperty("账号名称")
    private String accountName;

//    @ExcelResources(title = "一级行业")
//    @ApiModelProperty("一级行业")
//    private String firstCategoryName;
//
//    @ExcelResources(title = "二级行业")
//    @ApiModelProperty("二级行业")
//    private String secondCategoryName;
//
//    @ExcelResources(title = "交易产品类型")
//    @ApiModelProperty("交易产品类型")
//    private String salesCategory;

    // 基础数据 start region
    @ApiModelProperty("消耗")
    @ExcelProperty(value = "消耗")
    private BigDecimal cost;
    @ApiModelProperty("曝光量")
    @ExcelProperty(value = "曝光量")
    private Long showCount;
    @ApiModelProperty("点击量")
    @ExcelProperty(value = "点击量")
    private Long clickCount;
    @ApiModelProperty("点击率")
    @ExcelProperty(value = "点击率")
    private BigDecimal clickRate;
    @ApiModelProperty("平均点击单价（元）")
    @ExcelProperty(value = "平均点击单价（元）")
    private BigDecimal costPerClick;
    @ApiModelProperty("平均千次展示费（元）")
    @ExcelProperty(value = "平均千次展示费（元）")
    private BigDecimal averageCostPerThousand;
    // 基础数据 end region

    // 销售线索数据 start region
    // 表单提交 start region
    @ApiModelProperty("表单提交数")
    @ExcelProperty(value = "表单提交数")
    private Long formSubmitCount;
    @ApiModelProperty("表单提交成本")
    @ExcelProperty(value = "表单提交成本")
    private BigDecimal formSubmitAverageCost;
    @ApiModelProperty("表单提交率")
    @ExcelProperty(value = "表单提交率")
    private BigDecimal formSubmitRate;
    // 表单提交 end region

    // 表单付费 start region
    @ApiModelProperty("表单付费数")
    @ExcelProperty(value = "表单付费数")
    private Long formPaidCount;
    @ApiModelProperty("表单付费成本")
    @ExcelProperty(value = "表单付费成本")
    private BigDecimal formPaidCost;
    @ApiModelProperty("表单付费率")
    @ExcelProperty(value = "表单付费率")
    private BigDecimal formPaidRate;
    // 表单付费 end region

    // 有效线索 start region
    @ApiModelProperty("有效线索数")
    @ExcelProperty(value = "有效线索数")
    private Long validClueCount;
    @ApiModelProperty("有效线索成本")
    @ExcelProperty(value = "有效线索成本")
    private BigDecimal validClueAverageCost;
    @ApiModelProperty("有效线索率")
    @ExcelProperty(value = "有效线索率")
    private BigDecimal validClueRate;
    // 有效线索 end region

    // 完件 start region
    @ApiModelProperty("完件数")
    @ExcelProperty(value = "完件数")
    private Long applyCount;
    @ApiModelProperty("完件成本")
    @ExcelProperty(value = "完件成本")
    private BigDecimal applyCost;
    @ApiModelProperty("完件率")
    @ExcelProperty(value = "完件率")
    private BigDecimal applyRate;
    // 完件 end region

    // 授信 start region
    @ApiModelProperty("授信数")
    @ExcelProperty(value = "授信数")
    private Long creditCount;
    @ApiModelProperty("授信成本")
    @ExcelProperty(value = "授信成本")
    private BigDecimal creditCost;
    @ApiModelProperty("授信率")
    @ExcelProperty(value = "授信率")
    private BigDecimal creditRate;
    // 授信 end region

    // 放款 start region
    @ApiModelProperty("放款数")
    @ExcelProperty(value = "放款数")
    private Long withdrawDepositsCount;
    @ApiModelProperty("放款率")
    @ExcelProperty(value = "放款率")
    private BigDecimal withdrawDepositsRate;
    @ApiModelProperty("放款成本")
    @ExcelProperty(value = "放款成本")
    private BigDecimal withdrawDepositsCost;
    @ApiModelProperty("首日放款数")
    @ExcelProperty(value = "首日放款数")
    private Long firstWithdrawCount;
    @ApiModelProperty("首日放款率")
    @ExcelProperty(value = "首日放款率")
    private BigDecimal firstWithdrawRate;
    @ApiModelProperty("首日放款成本")
    @ExcelProperty(value = "首日放款成本")
    private BigDecimal firstWithdrawCost;
    // 放款 end region
    // 销售线索数据 end region

    // 应用游戏 start region
    // 游戏预约 start region
    @ApiModelProperty("游戏预约数")
    @ExcelProperty(value = "游戏预约数")
    private Long gameReserveCount;
    @ApiModelProperty("游戏预约成本")
    @ExcelProperty(value = "游戏预约成本")
    private BigDecimal gameReserveAverageCost;
    @ApiModelProperty("游戏预约率")
    @ExcelProperty(value = "游戏预约率")
    private BigDecimal gameReserveRate;
    // 游戏预约 end region

    // 安卓下载 start region
    @ApiModelProperty("安卓下载数")
    @ExcelProperty(value = "安卓下载数")
    private Long androidDownloadCount;
    @ApiModelProperty("安卓下载成本")
    @ExcelProperty(value = "安卓下载成本")
    private BigDecimal androidDownloadAverageCost;
    @ApiModelProperty("安卓下载率")
    @ExcelProperty(value = "安卓下载率")
    private BigDecimal androidDownloadRate;
    // 安卓下载 end region

    // 安卓安装 start region
    @ApiModelProperty("安卓下载数")
    @ExcelProperty(value = "安卓下载数")
    private Long androidInstallCount;
    @ApiModelProperty("安卓下载成本")
    @ExcelProperty(value = "安卓下载成本")
    private BigDecimal androidInstallAverageCost;
    @ApiModelProperty("安卓安装率")
    @ExcelProperty(value = "安卓安装率")
    private BigDecimal androidInstallRate;
    // 安卓安装 end region

    // 应用激活 start region
    @ApiModelProperty("ios激活数")
    @ExcelProperty(value = "ios激活数")
    private Long iosActivateCount;
    @ApiModelProperty("Android激活数")
    @ExcelProperty(value = "Android激活数")
    private Long androidActivateCount;
    @ApiModelProperty("应用激活数")
    @ExcelProperty(value = "应用激活数")
    private Long activateCount;
    @ApiModelProperty("应用激活成本")
    @ExcelProperty(value = "应用激活成本")
    private BigDecimal appActivateAverageCost;
    @ApiModelProperty("应用激活率")
    @ExcelProperty(value = "应用激活率")
    private BigDecimal appActivateRate;
    // 应用激活 end region

    // 安卓游戏中心激活 start region
    @ApiModelProperty("安卓游戏中心激活数")
    @ExcelProperty(value = "安卓游戏中心激活数")
    private Long androidGameCenterActivationCount;
    @ApiModelProperty("安卓游戏中心激活成本")
    @ExcelProperty(value = "安卓游戏中心激活成本")
    private BigDecimal androidGameCenterActivationCost;
    @ApiModelProperty("安卓游戏中心激活率")
    @ExcelProperty(value = "安卓游戏中心激活率")
    private BigDecimal androidGameCenterActivationRate;
    // 安卓游戏中心激活 end region

    // 用户注册 start region
    @ApiModelProperty("用户注册数")
    @ExcelProperty(value = "用户注册数")
    private Long registerCount;
    @ApiModelProperty("用户注册成本")
    @ExcelProperty(value = "用户注册成本")
    private BigDecimal registerAverageCost;
    @ApiModelProperty("用户注册率")
    @ExcelProperty(value = "用户注册率")
    private BigDecimal registerRate;
    // 用户注册 end region

    // 应用内付费 start region
    @ApiModelProperty("应用内付费次数")
    @ExcelProperty(value = "应用内付费次数")
    private Long orderPayCount;
    @ApiModelProperty("应用内累计付费金额")
    @ExcelProperty(value = "应用内累计付费金额")
    private BigDecimal orderPayAmount;
    @ApiModelProperty("应用内首次付费数")
    @ExcelProperty(value = "应用内首次付费数")
    private Long orderFirstPayCount;
    @ApiModelProperty("应用内首次付费金额")
    @ExcelProperty(value = "应用内首次付费金额")
    private BigDecimal orderFirstPayAmount;
    @ApiModelProperty("应用内首次付费成本")
    @ExcelProperty(value = "应用内首次付费成本")
    private BigDecimal orderFirstPayCost;
    @ApiModelProperty("应用内首次付费率")
    @ExcelProperty(value = "应用内首次付费率")
    private BigDecimal orderFirstPayRate;
    // 应用内付费 end region

    // 安卓游戏中心应用内付费 start region
    @ApiModelProperty("安卓游戏中心应用内付费次数")
    @ExcelProperty(value = "安卓游戏中心应用内付费次数")
    private Long androidGameCenterPaymentInAppCount;
    @ApiModelProperty("安卓游戏中心应用内首次付费次数")
    @ExcelProperty(value = "安卓游戏中心应用内首次付费次数")
    private Long androidGameCenterFirstPaymentInAppCount;
    @ApiModelProperty("安卓游戏中心应用内首次付费成本")
    @ExcelProperty(value = "安卓游戏中心应用内首次付费成本")
    private BigDecimal androidGameCenterFirstPaymentInAppCost;
    @ApiModelProperty("安卓游戏中心应用内首次付费率")
    @ExcelProperty(value = "安卓游戏中心应用内首次付费率")
    private BigDecimal androidGameCenterFirstPaymentInAppRate;
    // 安卓游戏中心应用内付费 end region

    // 次日留存 start region
    @ApiModelProperty("次日留存数")
    @ExcelProperty(value = "次日留存数")
    private Long retentionCount;
    @ApiModelProperty("次日留存成本")
    @ExcelProperty(value = "次日留存成本")
    private BigDecimal retentionCost;
    @ApiModelProperty("次日留存率")
    @ExcelProperty(value = "次日留存率")
    private BigDecimal retentionRate;
    // 次日留存 end region

    // 应用内访问 start region
    @ApiModelProperty("应用内访问数")
    @ExcelProperty(value = "应用内访问数")
    private Long appCallupCount;
    @ApiModelProperty("应用内访问成本")
    @ExcelProperty(value = "应用内访问成本")
    private BigDecimal appCallupCost;
    @ApiModelProperty("应用内访问率")
    @ExcelProperty(value = "应用内访问率")
    private BigDecimal appCallupRate;
    // 应用内访问 end region
    // 应用游戏 end region

    // 电商店铺 start region
    // 店铺停留 start region
    @ApiModelProperty("店铺停留数")
    @ExcelProperty(value = "店铺停留数")
    private Long lpCallUpSuccessStayCount;
    @ApiModelProperty("店铺停留率")
    @ExcelProperty(value = "店铺停留率")
    private BigDecimal lpCallUpSuccessStayRate;
    @ApiModelProperty("店铺停留成本")
    @ExcelProperty(value = "店铺停留成本")
    private BigDecimal lpCallUpSuccessStayCost;
    // 店铺停留 end region

    // 订单提交 start region
    @ApiModelProperty("订单提交数")
    @ExcelProperty(value = "订单提交数")
    private Long orderSubmitCount;
    @ApiModelProperty("订单提交金额")
    @ExcelProperty(value = "订单提交金额")
    private BigDecimal orderSubmitAmount;
    @ApiModelProperty("订单提交率")
    @ExcelProperty(value = "订单提交率")
    private BigDecimal orderSubmitRate;
    @ApiModelProperty("订单提交成本")
    @ExcelProperty(value = "订单提交成本")
    private BigDecimal orderSubmitCost;
    @ApiModelProperty("订单roi")
    @ExcelProperty(value = "订单roi")
    private BigDecimal goodsRoi;
    @ApiModelProperty("首次购买数")
    @ExcelProperty(value = "首次购买数")
    private Long firstOrderPlaceCount;
    @ApiModelProperty("首次购买金额")
    @ExcelProperty(value = "首次购买金额")
    private BigDecimal firstOrderPlaceAmount;
    @ApiModelProperty("首次购买成本")
    @ExcelProperty(value = "首次购买成本")
    private BigDecimal firstOrderPlaceCost;
    @ApiModelProperty("首次购买率")
    @ExcelProperty(value = "首次购买率")
    private BigDecimal firstOrderPlaceRate;
    // 订单提交 end region

    // 关键行为 start region
    @ApiModelProperty("关键行为数")
    @ExcelProperty(value = "关键行为数")
    private Long keyBehaviorCount;
    @ApiModelProperty("关键行为率")
    @ExcelProperty(value = "关键行为率")
    private BigDecimal keyBehaviorRate;
    @ApiModelProperty("关键行为成本")
    @ExcelProperty(value = "关键行为成本")
    private BigDecimal keyBehaviorCost;

    @ApiModelProperty(notes = "ocpx转化-销售线索搜集-微信加粉数量")
    @ExcelProperty(value = "微信加粉数量")
    private Long wxAddFansCount;

    @ApiModelProperty(notes = "ocpx转化-销售线索搜集-微信加粉率")
    @ExcelProperty(value = "微信加粉率")
    private BigDecimal wxAddFansRate;

    @ApiModelProperty(notes = "ocpx转化-销售线索搜集-微信加粉成本")
    @ExcelProperty(value = "微信加粉成本")
    private BigDecimal wxAddFansCost;

    /**
     * 微信复制数
     */
    @ExcelProperty(value = "微信复制数")
    private Long wxCopyCount;
    /**
     * 【微信复制成本】=总花费/【微信复制数】
     */
    @ExcelProperty(value = "微信复制成本")
    private BigDecimal wxCopyCost;
    /**
     * 【微信复制率】=【微信复制数】/点击量
     */
    @ExcelProperty(value = "微信复制率")
    private BigDecimal wxCopyRate;
    // 关键行为 end region
    // 电商店铺 end region

    public static SearchWordAnalysisReportVo emptyInstance(String date) {
        return SearchWordAnalysisReportVo.builder()
                .date(date)
                .cost(BigDecimal.ZERO)
                .showCount(0L)
                .clickCount(0L)
                .clickRate(BigDecimal.ZERO)
                .costPerClick(BigDecimal.ZERO)
                .averageCostPerThousand(BigDecimal.ZERO)
                .formSubmitCount(0L)
                .formSubmitAverageCost(BigDecimal.ZERO)
                .formSubmitRate(BigDecimal.ZERO)
                .formPaidCount(0L)
                .formPaidCost(BigDecimal.ZERO)
                .formPaidRate(BigDecimal.ZERO)
                .validClueCount(0L)
                .validClueAverageCost(BigDecimal.ZERO)
                .validClueRate(BigDecimal.ZERO)
                .applyCount(0L)
                .applyCost(BigDecimal.ZERO)
                .applyRate(BigDecimal.ZERO)
                .creditCount(0L)
                .creditCost(BigDecimal.ZERO)
                .creditRate(BigDecimal.ZERO)
                .withdrawDepositsCount(0L)
                .withdrawDepositsCost(BigDecimal.ZERO)
                .withdrawDepositsRate(BigDecimal.ZERO)
                .firstWithdrawCount(0L)
                .firstWithdrawCost(BigDecimal.ZERO)
                .firstWithdrawRate(BigDecimal.ZERO)
                .gameReserveCount(0L)
                .gameReserveAverageCost(BigDecimal.ZERO)
                .gameReserveRate(BigDecimal.ZERO)
                .androidDownloadCount(0L)
                .androidDownloadAverageCost(BigDecimal.ZERO)
                .androidDownloadRate(BigDecimal.ZERO)
                .androidInstallCount(0L)
                .androidInstallAverageCost(BigDecimal.ZERO)
                .androidInstallRate(BigDecimal.ZERO)
                .iosActivateCount(0L)
                .androidActivateCount(0L)
                .activateCount(0L)
                .appActivateAverageCost(BigDecimal.ZERO)
                .appActivateRate(BigDecimal.ZERO)
                .androidGameCenterActivationCount(0L)
                .androidGameCenterActivationCost(BigDecimal.ZERO)
                .androidGameCenterActivationRate(BigDecimal.ZERO)
                .registerCount(0L)
                .registerAverageCost(BigDecimal.ZERO)
                .registerAverageCost(BigDecimal.ZERO)
                .orderPayCount(0L)
                .orderPayAmount(BigDecimal.ZERO)
                .orderFirstPayCount(0L)
                .orderFirstPayAmount(BigDecimal.ZERO)
                .orderFirstPayCost(BigDecimal.ZERO)
                .orderFirstPayRate(BigDecimal.ZERO)
                .androidGameCenterPaymentInAppCount(0L)
                .androidGameCenterFirstPaymentInAppCount(0L)
                .androidGameCenterFirstPaymentInAppCost(BigDecimal.ZERO)
                .androidGameCenterFirstPaymentInAppRate(BigDecimal.ZERO)
                .retentionCount(0L)
                .retentionCost(BigDecimal.ZERO)
                .retentionRate(BigDecimal.ZERO)
                .appCallupCount(0L)
                .appCallupCost(BigDecimal.ZERO)
                .appCallupRate(BigDecimal.ZERO)
                .lpCallUpSuccessStayCount(0L)
                .lpCallUpSuccessStayCost(BigDecimal.ZERO)
                .lpCallUpSuccessStayRate(BigDecimal.ZERO)
                .orderSubmitCount(0L)
                .orderSubmitAmount(BigDecimal.ZERO)
                .orderSubmitCost(BigDecimal.ZERO)
                .orderSubmitRate(BigDecimal.ZERO)
                .goodsRoi(BigDecimal.ZERO)
                .firstOrderPlaceCount(0L)
                .firstOrderPlaceAmount(BigDecimal.ZERO)
                .firstOrderPlaceCost(BigDecimal.ZERO)
                .firstOrderPlaceRate(BigDecimal.ZERO)
                .keyBehaviorCount(0L)
                .keyBehaviorCost(BigDecimal.ZERO)
                .keyBehaviorRate(BigDecimal.ZERO)
                .wxCopyCount(0L)
                .wxCopyCost(BigDecimal.ZERO)
                .wxCopyRate(BigDecimal.ZERO)
                .wxAddFansCount(0L)
                .wxAddFansCost(BigDecimal.ZERO)
                .wxAddFansRate(BigDecimal.ZERO)
                .build();
    }


}
