package com.bilibili.adp.advertiser.portal.webapi.v2.misc;

import com.bilibili.adp.advertiser.portal.common.BasicController;
import com.bilibili.adp.advertiser.portal.common.Context;
import com.bilibili.adp.advertiser.portal.webapi.v2.misc.bos.LauQualificationVo;
import com.bilibili.adp.advertiser.portal.webapi.v2.misc.bos.QualificationQueryVo;
import com.bilibili.adp.advertiser.portal.webapi.v2.vo.common.BFSInfoVo;
import com.bilibili.adp.advertiser.portal.webapi.v2.vo.common.KeyValueVo;
import com.bilibili.adp.bfs.dto.BfsUploadResult;
import com.bilibili.adp.bfs.service.IBfsService;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.cpc.biz.services.common.SimpleBFSService;
import com.bilibili.adp.cpc.biz.services.common.bos.BFSInfoBo;
import com.bilibili.adp.cpc.biz.services.creative.MiddleCreativeQualificationPackageService;
import com.bilibili.adp.cpc.biz.services.creative.AdpCpcCreativeQualificationService;
import com.bilibili.adp.cpc.biz.services.creative.bos.LauQualificationBo;
import com.bilibili.adp.cpc.biz.services.creative.bos.QualificationQueryDto;
import com.bilibili.adp.cpc.enums.QualificationType;
import com.bilibili.adp.cpc.utils.Response;
import com.bilibili.adp.web.framework.utils.ImageUtils;
import com.bilibili.sycpb.acc.api.dict.common.YesNoEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.bilibili.adp.cpc.biz.constants.Constants.NEW_API_VERSION;

/**
 * 资质相关
 */
@Slf4j
@Api(tags = NEW_API_VERSION)
@RestController
@RequestMapping(value = "/web_api/v2/qualification")
public class AdpCpcQualificationController extends BasicController {
	private static final int MAX_QUALIFICATION_BYTES = 1024*1024*5;

	/**
	 * 1024 * 1024 * 20, 20m
	 */
	@Value("${file.qualification.byte.limit:20971520}")
	private Integer FILE_MAX_QUALIFICATION_BYTES;

	private final AdpCpcCreativeQualificationService creativeQualificationService;
	private final SimpleBFSService simpleBFSService;

	@Autowired
	private IBfsService bfsService;

	@Value("${bfs.category.qualification:qualification}")
	private String dir;

	public AdpCpcQualificationController(AdpCpcCreativeQualificationService creativeQualificationService, SimpleBFSService simpleBFSService) {
		this.creativeQualificationService = creativeQualificationService;
		this.simpleBFSService = simpleBFSService;
	}

	@ApiOperation("资质列表(用来支持旧版)")
    @GetMapping("/list")
    public Response<List<LauQualificationVo>> listQualification(@ApiIgnore Context context, QualificationQueryVo queryVo) {
		final int aid = context.getAccountId();
		QualificationQueryDto queryBo = QualificationQueryVo.toQueryDto(queryVo);
		queryBo.setCurAccountId(aid);
		final List<LauQualificationBo> bos = creativeQualificationService.listByAccountId(queryBo);
		return Response.ok(bos.stream()
				.map(LauQualificationVo::fromBo)
				.collect(Collectors.toList()));
	}

	@ApiOperation("删除资质")
	@DeleteMapping("/delete/{id}")
	public Response<Void> deleteQualification(@ApiIgnore Context context,
											  @PathVariable Integer id) {
		final int aid = context.getAccountId();
		creativeQualificationService.markDeleted(aid, id, YesNoEnum.NO.getCode());
		return Response.ok();
	}

	@ApiOperation("新增资质")
	@PostMapping("/create")
	public Response<Integer> createQualification(@ApiIgnore Context ctx,
												 @RequestBody LauQualificationVo vo) {
		final int aid = ctx.getAccountId();
		final int qid = creativeQualificationService.create(LauQualificationVo.toBo(aid, vo));
		return Response.ok(qid);
	}

	@ApiOperation("资质类型列表")
	@GetMapping("/type/list")
	public Response<List<KeyValueVo<Integer, String>>> listQualificationTypes(@ApiIgnore Context ctx) {
		final List<KeyValueVo<Integer, String>> kvs = Arrays.stream(QualificationType.values())
				.map(x -> new KeyValueVo<>(x.getCode(), x.getName()))
				.collect(Collectors.toList());
		return Response.ok(kvs);
	}

	@ApiOperation("上传资质图片")
	@PostMapping(value = "/upload/image")
	public Response<BFSInfoVo> uploadImage(@ApiIgnore Context ctx, @RequestPart MultipartFile file) {
		final String ext = ImageUtils.getExt(file);

		try {
			final byte[] bytes = file.getBytes();
			Assert.isTrue(bytes.length < MAX_QUALIFICATION_BYTES, "图片大小不能超过5M");
			final BFSInfoBo bo = simpleBFSService.upload(dir, file.getContentType(), bytes, ext);
			return Response.ok(BFSInfoVo.fromBo(bo));
		} catch (IOException e) {
			throw new IllegalArgumentException("图片上传至服务器失败");
		}
	}

	@ApiOperation("上传资质文件")
	@PostMapping(value = "/upload/file")
	public Response<BFSInfoVo> uploadFile(@ApiIgnore Context ctx, @RequestPart MultipartFile file) throws ServiceException {
		final String ext = ImageUtils.getExt(file);

		try {
			final byte[] bytes = file.getBytes();
			log.info("uploadFile, fileSize:{}, FILE_MAX_QUALIFICATION_BYTES:{}", bytes.length, FILE_MAX_QUALIFICATION_BYTES);
			Assert.isTrue(bytes.length < FILE_MAX_QUALIFICATION_BYTES, "文件大小不能超过20M");

			final String md5 = DigestUtils.md5Hex(bytes);
			final String name = dir + "/" + md5 + "." + ext;
			BfsUploadResult uploadResult = bfsService.upload(dir, name, bytes);
			BFSInfoBo bo = BFSInfoBo.builder()
					.url(uploadResult.getUrl())
					.md5(uploadResult.getMd5())
					.sha1(uploadResult.getHashCode())
					.build();
			return Response.ok(BFSInfoVo.fromBo(bo));
		} catch (IOException e) {
			throw new IllegalArgumentException("图片上传至服务器失败");
		} catch (ServiceException e) {
			log.info("uploadFile, error:{}", e);
			throw e;
		}
	}

}
