package com.bilibili.adp.advertiser.portal.webapi.v6.report;

import com.bilibili.adp.advertiser.portal.common.BasicController;
import com.bilibili.adp.advertiser.portal.common.Context;
import com.bilibili.adp.advertiser.portal.common.annotation.BusinessDomain;
import com.bilibili.adp.advertiser.portal.webapi.effect_ad.converter.ReportConverter;
import com.bilibili.adp.advertiser.portal.webapi.v6.report.vo.CampaignReportV6Vo;
import com.bilibili.adp.advertiser.portal.webapi.v6.report.vo.CreativeReportV6Vo;
import com.bilibili.adp.advertiser.portal.webapi.v6.report.vo.UnitReportV6Vo;
import com.bilibili.adp.cpc.enums.ErrorCodeEnum;
import com.bilibili.adp.cpc.utils.Response;
import com.bilibili.adp.legacy.bo.CampaignReportDetailBo;
import com.bilibili.adp.legacy.bo.CreativeReportDetailBo;
import com.bilibili.adp.legacy.bo.UnitReportDetailBo;
import com.bilibili.adp.v6.report.bo.QueryCampaignReportV6Bo;
import com.bilibili.adp.v6.report.bo.QueryCreativeReportV6Bo;
import com.bilibili.adp.v6.report.bo.QueryUnitReportV6Bo;
import com.bilibili.adp.v6.report.service.LaunchReportV6Service;
import com.bilibili.adp.web.framework.core.Pagination;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/web_api/v6/sanlian/report/list")
public class SanlianReportListController extends BasicController {
    private final LaunchReportV6Service launchReportV6Service;

    @GetMapping(value = "/campaign")
    @BusinessDomain(ErrorCodeEnum.DomainType.CAMPAIGN)
    public Response<Pagination<List<CampaignReportV6Vo>>> campaign(@ApiIgnore Context context,
                                                                   @RequestParam(value = "from_time", required = false) Long fromTime,
                                                                   @RequestParam(value = "to_time", required = false) Long toTime,
                                                                   @RequestParam(value = "campaign_ids", required = false) List<Long> campaignIds,
                                                                   @RequestParam(value = "campaign_name", required = false) String campaignName,
                                                                   @RequestParam(value = "campaign_status", required = false) List<Integer> campaignStatus,
                                                                   @RequestParam(value = "campaign_extra_status", required = false) Integer campaignExtraStatus,
                                                                   @RequestParam(value = "promotion_purpose_type", required = false) List<Integer> promotionPurposeType,
                                                                   @RequestParam(value = "ad_type", required = false) List<Integer> adType,
                                                                   @RequestParam(value = "is_managed", required = false) Integer isManaged,
                                                                   @RequestParam(value = "adp_version", required = false) List<Integer> adpVersion,
                                                                   @RequestParam(value = "support_auto", required = false) Integer supportAuto,
                                                                   @RequestParam(value = "sort_field", required = false, defaultValue = "") String sortField,
                                                                   @RequestParam(value = "sort_type", required = false, defaultValue = "1") Integer sortType,
                                                                   @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
                                                                   @RequestParam(value = "size", required = false, defaultValue = "20") Integer size
    ) {
        QueryCampaignReportV6Bo query = QueryCampaignReportV6Bo.builder()
                .startTime(fromTime)
                .endTime(toTime)
                .accountId(Long.valueOf(context.getAccountId()))
                .campaignIds(campaignIds)
                .campaignName(campaignName)
                .campaignStatus(campaignStatus)
                .campaignExtraStatus(campaignExtraStatus)
                .promotionPurposeType(promotionPurposeType)
                .adType(adType)
                .isManaged(isManaged)
                .adpVersion(adpVersion)
                .supportAuto(supportAuto)
                .sortField(sortField)
                .sortOrder(sortType)
                .page(page)
                .size(size)
                .build();
        Pagination<List<CampaignReportDetailBo>> result = launchReportV6Service.listCampaignStats(query);
        return Response.ok(
                new Pagination<>(
                        page,
                        result.getTotal_count(),
                        result.getData()
                                .stream()
                                .map(ReportConverter.MAPPER::toV6Vo)
                                .collect(Collectors.toList())
                )
        );
    }

    @GetMapping(value = "/unit")
    @BusinessDomain(ErrorCodeEnum.DomainType.UNIT)
    public Response<Pagination<List<UnitReportV6Vo>>> unit(@ApiIgnore Context context,
                                                           @RequestParam(value = "from_time", required = false) Long fromTime,
                                                           @RequestParam(value = "to_time", required = false) Long toTime,
                                                           @RequestParam(value = "campaign_ids", required = false) List<Long> campaignIds,
                                                           @RequestParam(value = "unit_ids", required = false) List<Long> unitIds,
                                                           @RequestParam(value = "unit_name", required = false) String unitName,
                                                           @RequestParam(value = "unit_status", required = false) List<Integer> unitStatus,
                                                           @RequestParam(value = "unit_extra_status", required = false) Integer unitExtraStatus,
                                                           @RequestParam(value = "sales_mode", required = false) List<Integer> salesMode,
                                                           @RequestParam(value = "is_no_bid", required = false) Integer isNoBid,
                                                           @RequestParam(value = "cpa_target", required = false) List<Integer> cpaTarget,
                                                           @RequestParam(value = "deep_cpa_target", required = false) List<Integer> deepCpaTarget,
                                                           @RequestParam(value = "promotion_content_type", required = false) List<Integer> promotionContentType,
                                                           @RequestParam(value = "is_managed", required = false) Integer isManaged,
                                                           @RequestParam(value = "adp_version", required = false) List<Integer> adpVersion,
                                                           @RequestParam(value = "support_auto", required = false) Integer supportAuto,
                                                           @RequestParam(value = "sort_field", required = false, defaultValue = "") String sortField,
                                                           @RequestParam(value = "sort_type", required = false, defaultValue = "1") Integer sortType,
                                                           @RequestParam(value = "clean_up_task_id", required = false, defaultValue = "") String cleanUpTaskId,
                                                           @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
                                                           @RequestParam(value = "size", required = false, defaultValue = "20") Integer size
    ) {
        QueryUnitReportV6Bo query = QueryUnitReportV6Bo
                .builder()
                .startTime(fromTime)
                .endTime(toTime)
                .accountId(Long.valueOf(context.getAccountId()))
                .campaignIds(campaignIds)
                .unitIds(unitIds)
                .unitName(unitName)
                .unitStatus(unitStatus)
                .unitExtraStatus(unitExtraStatus)
                .salesMode(salesMode)
                .isNoBid(isNoBid)
                .cpaTarget(cpaTarget)
                .deepCpaTarget(deepCpaTarget)
                .promotionContentType(promotionContentType)
                .isManaged(isManaged)
                .adpVersion(adpVersion)
                .supportAuto(supportAuto)
                .sortField(sortField)
                .sortOrder(sortType)
                .cleanUpTaskId(cleanUpTaskId)
                .page(page)
                .size(size)
                .build();
        Pagination<List<UnitReportDetailBo>> result = launchReportV6Service.listUnitStats(query);
        return Response.ok(
                new Pagination<>(
                        page,
                        result.getTotal_count(),
                        result.getData()
                                .stream()
                                .map(ReportConverter.MAPPER::toV6Vo)
                                .collect(Collectors.toList())
                )
        );
    }

    @GetMapping(value = "/creative")
    @BusinessDomain(ErrorCodeEnum.DomainType.CREATIVE)
    public Response<Pagination<List<CreativeReportV6Vo>>> creative(@ApiIgnore Context context,
                                                                   @RequestParam(value = "appkey", required = false) String appkey,
                                                                   @RequestParam(value = "from_time", required = false) Long fromTime,
                                                                   @RequestParam(value = "to_time", required = false) Long toTime,
                                                                   @RequestParam(value = "campaign_ids", required = false) List<Long> campaignIds,
                                                                   @RequestParam(value = "unit_ids", required = false) List<Long> unitIds,
                                                                   @RequestParam(value = "creative_ids", required = false) List<Long> creativeIds,
                                                                   @RequestParam(value = "creative_status", required = false) List<Integer> creativeStatus,
                                                                   @RequestParam(value = "creative_extra_status", required = false) Integer creativeExtraStatus,
                                                                   @RequestParam(value = "advertising_mode", required = false) List<Integer> advertisingMode,
                                                                   @RequestParam(value = "av_ids", required = false) List<Long> avIds,
                                                                   @RequestParam(value = "bv_ids", required = false) List<String> bvIds,
                                                                   @RequestParam(value = "dynamic_ids", required = false) List<Long> dynamicIds,
                                                                   @RequestParam(value = "is_managed", required = false) Integer isManaged,
                                                                   @RequestParam(value = "adp_version", required = false) List<Integer> adpVersion,
                                                                   @RequestParam(value = "is_programmatic", required = false) Integer isProgrammatic,
                                                                   @RequestParam(value = "is_bili_native", required = false) Integer isBiliNative,
                                                                   @RequestParam(value = "support_auto", required = false) Integer supportAuto,
                                                                   @RequestParam(value = "material_type", required = false) List<Integer> materialType,
                                                                   @RequestParam(value = "sort_field", required = false, defaultValue = "") String sortField,
                                                                   @RequestParam(value = "sort_type", required = false, defaultValue = "1") Integer sortType,
                                                                   @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
                                                                   @RequestParam(value = "size", required = false, defaultValue = "20") Integer size
    ) {
        QueryCreativeReportV6Bo query = QueryCreativeReportV6Bo.builder()
                .isMapi(StringUtils.hasText(appkey))
                .startTime(fromTime)
                .endTime(toTime)
                .accountId(Long.valueOf(context.getAccountId()))
                .campaignIds(campaignIds)
                .unitIds(unitIds)
                .creativeIds(creativeIds)
                .creativeStatus(creativeStatus)
                .creativeExtraStatus(creativeExtraStatus)
                .advertisingMode(advertisingMode)
                .avIds(avIds)
                .bvIds(bvIds)
                .dynamicIds(dynamicIds)
                .isManaged(isManaged)
                .adpVersion(adpVersion)
                .isProgrammatic(isProgrammatic)
                .isBiliNative(isBiliNative)
                .supportAuto(supportAuto)
                .materialType(materialType)
                .sortField(sortField)
                .sortOrder(sortType)
                .page(page)
                .size(size)
                .build();
        Pagination<List<CreativeReportDetailBo>> result = launchReportV6Service.listCreativeStats(query);
        return Response.ok(
                new Pagination<>(
                        page,
                        result.getTotal_count(),
                        result.getData()
                                .stream()
                                .map(ReportConverter.MAPPER::toV6Vo)
                                .collect(Collectors.toList())
                )
        );
    }
}
