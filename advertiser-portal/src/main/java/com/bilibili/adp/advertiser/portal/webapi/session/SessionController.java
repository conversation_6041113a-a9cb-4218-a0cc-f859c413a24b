package com.bilibili.adp.advertiser.portal.webapi.session;

import com.alibaba.fastjson.JSONObject;
import com.bilibili.adp.account.common.PersonFlyPayType;
import com.bilibili.adp.account.dto.LoginInfoDto;
import com.bilibili.adp.account.dto.PermissionInfoDto;
import com.bilibili.adp.advertiser.portal.common.BasicController;
import com.bilibili.adp.advertiser.portal.common.Constants;
import com.bilibili.adp.advertiser.portal.common.Context;
import com.bilibili.adp.advertiser.portal.service.session.WebSessionService;
import com.bilibili.adp.advertiser.portal.webapi.session.vo.ContentFlyLoginVo;
import com.bilibili.adp.advertiser.portal.webapi.session.vo.LoginInfo;
import com.bilibili.adp.advertiser.portal.webapi.session.vo.PermissionInfo;
import com.bilibili.adp.common.enums.OperatorType;
import com.bilibili.adp.common.enums.SystemType;
import com.bilibili.adp.common.enums.fly.FlyOrderType;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.biz.services.account.api.ISessionService;
import com.bilibili.adp.launch.api.fly.IFlowTicketService;
import com.bilibili.adp.resource.api.account.label.IAccountLabelService;
import com.bilibili.adp.web.framework.core.Response;
import com.bilibili.adp.web.framework.exception.WebApiExceptionCode;
import com.bilibili.bjcom.sso.SSOUserInfo;
import com.bilibili.bjcom.sso.SSOUtils;
import com.bilibili.rbac.api.service.IPermissionService;
import com.dianping.cat.Cat;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;

/**
 * Created by fanwenbin on 16/9/18.
 */
@Controller
@RequestMapping("/web_api/v1/session")
@Api(value = "/session", description = "登录相关【V现金版个人起飞】")
public class SessionController extends BasicController {
    private final Logger LOGGER = LoggerFactory.getLogger(getClass());

    @Autowired
    private ISessionService sessionService;
    @Autowired
    private SystemType systemType;
    @Autowired
    private IPermissionService permissionService;
    @Value("${rbac.tenantId}")
    private Integer tenantId;
    @Autowired
    private WebSessionService webSessionService;

    @Value("${advertiser-platform.passport.refer.url}")
    private String passportReferUrl;
    @Value("${platform.cookie.max.age:86400}")
    private int cookieMaxAge;
    @Autowired
    private IAccountLabelService accountLabelService;
    @Value("${signing.bonus.tag.id:184}")
    private Integer signingBonusTagId;
    @Autowired
    private IFlowTicketService flowTicketService;

    private static final String INTRANET_DOMAIN = ".bilibili.co";

    private static final String EXTRANET_DOMAIN = ".bilibili.com";

    @ApiOperation(value = "mcn登录内容起飞广告主")
    @RequestMapping(value = "/mcn/content_fly/login", method = RequestMethod.POST)
    public
    @ResponseBody
    Response<LoginInfo> contentFlyMcnLogin(
            HttpServletRequest request,
            HttpServletResponse response,
            @RequestBody @NotNull ContentFlyLoginVo contentFlyLoginVo
    ) throws ServiceException {
        Assert.notNull(contentFlyLoginVo.getAccount_id(), "账号id不可为空");
        String cookieValue = request.getHeader("Cookie");
        LoginInfoDto loginInfoDto = sessionService.contentFlyLogin4MCN(cookieValue, contentFlyLoginVo.getAccount_id());
        addAccessTokenCookie2(loginInfoDto.getAccessToken(), response);
        return Response.SUCCESS(LoginInfo.builder()
                .access_token(loginInfoDto.getAccessToken())
                .build());
    }

    @ApiOperation(value = "代理商选择用户sso登录")
    @RequestMapping(value = "/login/sso/agent", method = RequestMethod.POST)
    public
    @ResponseBody
    Response<LoginInfo> agentLogin(HttpServletRequest request, HttpServletResponse response, @RequestParam("customer_id") Integer customerId) throws ServiceException {
        LoginInfoDto loginInfoDto = sessionService.ssoAgentLogin(request.getHeader("Cookie"), customerId);

        addAccessTokenCookie2(loginInfoDto.getAccessToken(), response);

        return Response.SUCCESS(LoginInfo.builder().access_token(loginInfoDto.getAccessToken()).build());
    }

    @ApiOperation(value = "本地广告-代理商选择用户sso登录")
    @RequestMapping(value = "/login_local/sso/agent", method = RequestMethod.POST)
    public
    @ResponseBody
    Response<LoginInfo> agentLoginLocal(HttpServletRequest request, HttpServletResponse response, @RequestParam("customer_id") Integer customerId) throws ServiceException {
        LoginInfoDto loginInfoDto = sessionService.ssoAgentLoginLocal(request.getHeader("Cookie"), customerId);

        addAccessTokenCookie(loginInfoDto.getAccessToken(), response);

        return Response.SUCCESS(LoginInfo.builder().access_token(loginInfoDto.getAccessToken()).build());
    }

    @ApiOperation(value = "bili用户sso登录")
    @RequestMapping(value = "/login/sso", method = RequestMethod.POST)
    @ResponseBody
    public Response<LoginInfo> login(HttpServletRequest request, HttpServletResponse response) throws ServiceException {
        LoginInfoDto loginInfoDto = sessionService.ssoLogin(request.getHeader("Cookie"), false);

        addAccessTokenCookie(loginInfoDto.getAccessToken(), response);

        return Response.SUCCESS(LoginInfo.builder().access_token(loginInfoDto.getAccessToken()).build());
    }

    @ApiOperation(value = "激励金个人起飞用户登录")
    @RequestMapping(value = "/person_fly/login/sso", method = RequestMethod.POST)
    public
    @ResponseBody
    Response<LoginInfo> personFlyLogin(HttpServletRequest request, HttpServletResponse response) throws ServiceException {
        String cookie = request.getHeader("Cookie");
        LoginInfoDto loginInfoDto = sessionService.personFlySsoLogin(cookie, FlyOrderType.INCENTIVE_BONUS_FLY.getCode(), PersonFlyPayType.INCENTIVE_BONUS_PAY.getCode());
        addAccessTokenCookie(loginInfoDto.getAccessToken(), response);
        return Response.SUCCESS(LoginInfo.builder()
                .access_token(loginInfoDto.getAccessToken())
                .build());
    }

    @ResponseBody
    @ApiOperation(value = "现金版本个人起飞用户登录【V现金版个人起飞】")
    @RequestMapping(value = "/cash_person_fly/login/sso", method = RequestMethod.POST)
    public Response<LoginInfo> cashPersonFlyLogin(HttpServletRequest request, HttpServletResponse response) throws ServiceException {
        String cookie = request.getHeader("Cookie");
        LoginInfoDto loginInfoDto = sessionService.personFlySsoLogin(cookie, FlyOrderType.CASH_FLY.getCode(), PersonFlyPayType.CASH_PAY.getCode());
        addAccessTokenCookie(loginInfoDto.getAccessToken(), response);
        return Response.SUCCESS(LoginInfo.builder()
                .access_token(loginInfoDto.getAccessToken())
                .build());
    }

    @ResponseBody
    @ApiOperation(value = "起飞币个人起飞用户登录")
    @RequestMapping(value = "/fly_coin/login/sso", method = RequestMethod.POST)
    public Response<LoginInfo> flyCoinLogin(HttpServletRequest request, HttpServletResponse response) throws ServiceException {
        String cookie = request.getHeader("Cookie");
        LoginInfoDto loginInfoDto = sessionService.personFlySsoLogin(cookie, FlyOrderType.FLY_COIN.getCode(), PersonFlyPayType.FLY_COIN_PAY.getCode());
        addAccessTokenCookie(loginInfoDto.getAccessToken(), response);
        return Response.SUCCESS(LoginInfo.builder()
                .access_token(loginInfoDto.getAccessToken())
                .build());
    }

    @ResponseBody
    @ApiOperation(value = "签约金版本个人起飞用户登录")
    @RequestMapping(value = "/signing_bonus/login/sso", method = RequestMethod.POST)
    public Response<LoginInfo> signingBonusLogin(HttpServletRequest request, HttpServletResponse response) throws ServiceException {
        String cookie = request.getHeader("Cookie");
        LoginInfoDto loginInfoDto = sessionService.personFlySsoLogin(cookie, FlyOrderType.SIGNING_BONUS_FLY.getCode(), PersonFlyPayType.SIGNING_BONUS_PAY.getCode());
        addAccessTokenCookie(loginInfoDto.getAccessToken(), response);
        return Response.SUCCESS(LoginInfo.builder()
                .access_token(loginInfoDto.getAccessToken())
                .build());
    }

    @ResponseBody
    @ApiOperation(value = "流量券登录")
    @RequestMapping(value = "/flow_ticket/login/sso", method = RequestMethod.POST)
    public Response<LoginInfo> flowTicketLogin(HttpServletRequest request, HttpServletResponse response) throws ServiceException {
        String cookie = request.getHeader("Cookie");
        LoginInfoDto loginInfoDto = sessionService.personFlySsoLogin(cookie, FlyOrderType.FLOW_TICKET_FLY.getCode(), PersonFlyPayType.FLOW_TICKET_PAY.getCode());
        addAccessTokenCookie(loginInfoDto.getAccessToken(), response);
        return Response.SUCCESS(LoginInfo.builder()
                .access_token(loginInfoDto.getAccessToken())
                .build());
    }

    @ApiOperation(value = "本地广告-bili用户sso登录")
    @RequestMapping(value = "/login_local/sso", method = RequestMethod.POST)
    public
    @ResponseBody
    Response<LoginInfo> loginLocal(HttpServletRequest request, HttpServletResponse response) throws ServiceException {
        Assert.isTrue(false, "请以代理商身份登录");
        return null;
    }

    @ApiOperation(value = "内网用户选择账户登录效果广告系统")
    @RequestMapping(value = "/login", method = RequestMethod.POST)
    public
    @ResponseBody
    Response<LoginInfo> loginEffectAd(HttpServletRequest request, HttpServletResponse response, @RequestParam("account_id") Integer accountId,
                                      @RequestParam(value = "system_type", required = false) Integer systemTypeCode) throws ServiceException {
        SSOUserInfo userInfo = SSOUtils.getUserInfo(request);
        if (null == userInfo) {
            return Response.FAIL(WebApiExceptionCode.UNAUTHORIZED);
        }

        SystemType tmpSystemType = systemType;
        if (systemTypeCode != null && Utils.isPositive(systemTypeCode)) {
            tmpSystemType = SystemType.getByCode(systemTypeCode);
        }

        boolean isAdmin = false;
        if (permissionService.checkPermission(tenantId, userInfo.getUserName(), "cpm_view_all_accountlist")) {
            isAdmin = true;
        }
        // 内部会检查账户权限
        LoginInfoDto loginInfoDto = sessionService.biliUserLogin(accountId, userInfo.getUserName(), isAdmin, tmpSystemType);

        addCookie(loginInfoDto.getAccessToken(), response, INTRANET_DOMAIN);

        return Response.SUCCESS(LoginInfo.builder().access_token(loginInfoDto.getAccessToken()).build());
    }

    @ApiOperation(value = "本地广告-内网用户选择账户登录效果广告系统")
    @RequestMapping(value = "/login_local", method = RequestMethod.POST)
    public
    @ResponseBody
    Response<LoginInfo> loginEffectAdLocal(HttpServletRequest request, HttpServletResponse response, @RequestParam("account_id") Integer accountId) throws ServiceException {
        SSOUserInfo userInfo = SSOUtils.getUserInfo(request);
        if (null == userInfo) {
            return Response.FAIL(WebApiExceptionCode.UNAUTHORIZED);
        }

        boolean isAdmin = false;
        if (permissionService.checkPermission(tenantId, userInfo.getUserName(), "cpm_view_all_accountlist")) {
            isAdmin = true;
        }
        LoginInfoDto loginInfoDto = sessionService.biliUserLoginLocal(accountId, userInfo.getUserName(), isAdmin, SystemType.LOCAL);

        addAccessTokenCookie(loginInfoDto.getAccessToken(), response);

        return Response.SUCCESS(LoginInfo.builder().access_token(loginInfoDto.getAccessToken()).build());
    }

    @ApiOperation(value = "登出效果广告系统")
    @RequestMapping(value = "/logout", method = RequestMethod.POST)
    public
    @ResponseBody
    Response<Object> logout(@ApiIgnore Context context,
                            HttpServletRequest request,
                            HttpServletResponse response) throws ServiceException {
        LOGGER.info("logout context: [{}], cookie:[{}]", JSONObject.toJSONString(context), JSONObject.toJSONString(request.getCookies()));
        if (context != null && OperatorType.ADVERTISERS.getCode() == context.getType()) {
            this.removeMasterStationCookie(request, response);
        }
        // 防止旧cookie无法登出
        this.removeAccessTokenCookie(response);
        this.removeCookie(response, INTRANET_DOMAIN);

        return Response.SUCCESS(null);
    }

    private void addAccessTokenCookie(String accessToken, HttpServletResponse response) {
        Cookie cookie = new Cookie(Constants.HTTP_TOKEN, accessToken);
        cookie.setPath("/");
        cookie.setMaxAge(cookieMaxAge);
        cookie.setHttpOnly(true);
        response.addCookie(cookie);
    }

    private void addAccessTokenCookie2(String accessToken, HttpServletResponse response) {
        Cookie cookie = new Cookie(Constants.HTTP_TOKEN, accessToken);
        cookie.setPath("/");
        cookie.setMaxAge(cookieMaxAge);
        cookie.setHttpOnly(true);
        cookie.setDomain(".bilibili.com");
        response.addCookie(cookie);
    }

    private void removeAccessTokenCookie(HttpServletResponse response) {
        Cookie cookie = new Cookie(Constants.HTTP_TOKEN, null);
        cookie.setPath("/");
        cookie.setMaxAge(0);
        cookie.setHttpOnly(true);
        response.addCookie(cookie);
    }

    private void addCookie(String accessToken, HttpServletResponse response, String domain) {
        Cookie cookie = new Cookie(Constants.HTTP_TOKEN, accessToken);
        cookie.setPath("/");
        cookie.setMaxAge(cookieMaxAge);
        cookie.setHttpOnly(true);
        cookie.setDomain(domain);
        response.addCookie(cookie);
    }

    private void removeCookie(HttpServletResponse response, String domain) {
        Cookie cookie = new Cookie(Constants.HTTP_TOKEN, null);
        cookie.setPath("/");
        cookie.setMaxAge(0);
        cookie.setHttpOnly(true);
        cookie.setDomain(domain);
        response.addCookie(cookie);
    }


    private void removeAccessTokenCookie2(HttpServletResponse response) {
        Cookie cookie = new Cookie(Constants.HTTP_TOKEN, null);
        cookie.setPath("/");
        cookie.setMaxAge(0);
        cookie.setHttpOnly(true);
        cookie.setDomain(".bilibili.com");
        response.addCookie(cookie);
    }

    private void removeMasterStationCookie(HttpServletRequest request, HttpServletResponse response) {
        Cookie[] cookies = request.getCookies();
        for (Cookie cookie : cookies) {
            cookie.setPath("/");
            cookie.setMaxAge(0);
            cookie.setValue(null);
            cookie.setDomain(".bilibili.com");
            response.addCookie(cookie);
        }
        response.setStatus(HttpServletResponse.SC_MOVED_TEMPORARILY);
        response.setHeader("Location", passportReferUrl);
    }

    @ResponseBody
    @ApiOperation(value = "检查用户是否登录")
    @RequestMapping(value = "/login/check", method = RequestMethod.GET)
    public Response<Integer> checkLoginStatus(HttpServletRequest request) {
        boolean validateState = false;
        try {
            validateState = webSessionService.validateSession(request);
        } catch (Exception e) {
            LOGGER.error("validateCookie.error:{}, cookie:{}", e, request.getHeader("Cookie"));
            Cat.logEvent("AD_PLATFORM_COOKIE_ERROR", "validateCookie.error");
        }
        return validateState ? Response.SUCCESS(1) : Response.SUCCESS(0);
    }

    @ApiOperation(value = "登出新起飞系统")
    @RequestMapping(value = "/logout_fly", method = RequestMethod.POST)
    public
    @ResponseBody
    Response<Object> logoutFly(@ApiIgnore Context context,
                               HttpServletRequest request,
                               HttpServletResponse response) throws ServiceException {
        LOGGER.info("logout context: [{}], cookie:[{}]", JSONObject.toJSONString(context), JSONObject.toJSONString(request.getCookies()));
        if (context != null && OperatorType.ADVERTISERS.getCode() == context.getType()) {
            this.removeMasterStationCookie4Fly(request, response);
        }
        // 防止旧cookie无法登出
        this.removeAccessTokenCookie(response);
        this.removeCookie(response, INTRANET_DOMAIN);
        this.removeCookie(response, EXTRANET_DOMAIN);
        return Response.SUCCESS(null);
    }

    private void removeMasterStationCookie4Fly(HttpServletRequest request, HttpServletResponse response) {
        Cookie[] cookies = request.getCookies();
        for (Cookie cookie : cookies) {
            cookie.setPath("/");
            cookie.setMaxAge(0);
            cookie.setValue(null);
            cookie.setDomain(".bilibili.com");
            response.addCookie(cookie);
        }
        response.setStatus(HttpServletResponse.SC_OK);
        response.setHeader("Location", passportReferUrl);
    }

    @ResponseBody
    @ApiOperation(value = "获取起飞权限")
    @RequestMapping(value = "/fly_permission", method = RequestMethod.GET)
    public Response<PermissionInfo> permission(HttpServletRequest request, HttpServletResponse response) throws ServiceException {
        //查询现金权限，没权限也带出mid
        PermissionInfoDto permissionInfoDto = sessionService.getPermission(request.getHeader("Cookie"));
        PermissionInfo permissionInfo = PermissionInfo.builder()
                .account_id(permissionInfoDto.getAccountId())
                .mid(permissionInfoDto.getMid())
                .can_cash(permissionInfoDto.getCanCash())
                .build();
        //查询流量券
        permissionInfo.setCan_traffic(flowTicketService.hasBind(permissionInfo.getMid()));
        return Response.SUCCESS(permissionInfo);
    }

    @ApiOperation(value = "内网用户用户名")
    @RequestMapping(value = "/user_name", method = RequestMethod.GET)
    @ResponseBody
    public Response<String> userNameEffectAd(HttpServletRequest request) throws ServiceException {
        SSOUserInfo userInfo = SSOUtils.getUserInfo(request);
        if (null == userInfo) {
            return Response.FAIL(WebApiExceptionCode.UNAUTHORIZED);
        }
        return Response.SUCCESS(userInfo.getUserName());
    }
}
