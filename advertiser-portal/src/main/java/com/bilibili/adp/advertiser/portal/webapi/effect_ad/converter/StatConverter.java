package com.bilibili.adp.advertiser.portal.webapi.effect_ad.converter;

import com.bilibili.adp.advertiser.portal.webapi.statistic.vo.StatVo;
import com.bilibili.adp.advertiser.portal.webapi.v2.vo.responses.ReportSummaryVo;
import com.bilibili.adp.cpc.biz.bos.report.StatBo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper
public interface StatConverter {
    StatConverter MAPPER = Mappers.getMapper(StatConverter.class);

    /**
     * convert StatBo to ReportSummaryVo
     * @param statBo
     * @return ReportSummaryVo
     */
    @Mapping(target = "wxCopyCost", source = "costPerWxCopy")
    @Mapping(target = "wxAddFansCost", source = "costPerWxAddFans")
    @Mapping(target = "withdrawDepositsCost", source = "costPerWithdrawDeposits")
    @Mapping(target = "costPerVideoLike", source = "costPerVideoLike")
    @Mapping(target = "validClueRate", source = "clueValidRate")
    @Mapping(target = "validClueCount", source = "clueValidCount")
    @Mapping(target = "validClueAverageCost", source = "costPerClueValid")
    @Mapping(target = "underBoxLinkClickRate", source = "underBoxLinkRate")
    @Mapping(target = "underBoxLinkClickCount", source = "underBoxLinkCount")
    @Mapping(target = "costPerUnderBoxLinkClickCount", source = "costPerUnderBoxLink")
    @Mapping(target = "retentionCost", source = "costPerRetention")
    @Mapping(target = "registerRate", source = "userRegisterRate")
    @Mapping(target = "registerCount", source = "userRegisterCount")
    @Mapping(target = "registerAverageCost", source = "costPerUserRegister")
    @Mapping(target = "paidIn7dROI", source = "paidIn7DayRoi")
    @Mapping(target = "paidIn7dPrice", source = "paidIn7DayAmount")
    @Mapping(target = "paidIn7dCount", source = "paidIn7DayCount")
    @Mapping(target = "paidIn7dCost", source = "costPerPaidIn7Day")
    @Mapping(target = "paidIn24hROI", source = "paidIn24HourRoi")
    @Mapping(target = "paidIn24hPrice", source = "paidIn24HourAmount")
    @Mapping(target = "paidIn24hCount", source = "paidIn24HourCount")
    @Mapping(target = "paidIn24hCost", source = "costPerPaidIn24Hour")
    @Mapping(target = "orderSubmitRate", source = "orderPlaceRate")
    @Mapping(target = "orderSubmitCount", source = "orderPlaceCount")
    @Mapping(target = "orderSubmitCost", source = "costPerOrderPlace")
    @Mapping(target = "orderSubmitAmount", source = "orderPlaceAmount")
    @Mapping(target = "orderPayCount", source = "userCostCount")
    @Mapping(target = "orderPayAmount", source = "userCostAmount")
    @Mapping(target = "orderFirstPayRate", source = "userFirstCostRate")
    @Mapping(target = "orderFirstPayCount", source = "userFirstCostCount")
    @Mapping(target = "orderFirstPayCost", source = "costPerUserFirstCost")
    @Mapping(target = "orderFirstPayAmount", source = "userFirstCostAmount")
    @Mapping(target = "lpCallupRate", source = "lpCallUpRate")
    @Mapping(target = "lpCallupCount", source = "lpCallUpCount")
    @Mapping(target = "lpCallupCost", source = "costPerLpCallUp")
    @Mapping(target = "lpCallUpSuccessCost", source = "costPerLpCallUpSuccess")
    @Mapping(target = "lpCallUpSuccessStayCost", source = "costPerLpCallUpSuccessStay")
    @Mapping(target = "liveEntryCost", source = "costPerLiveEntry")
    @Mapping(target = "liveCallUpCost", source = "costPerLiveCallUp")
    @Mapping(target = "liveReserveCost", source = "costPerLiveReserve")
    @Mapping(target = "keyBehaviorRate", source = "actionValidRate")
    @Mapping(target = "keyBehaviorCount", source = "actionValidCount")
    @Mapping(target = "keyBehaviorCost", source = "costPerActionValid")
    @Mapping(target = "iosActivateCount", source = "iosAppActiveCount")
    @Mapping(target = "androidActivateCount", source = "androidAppActiveCount")
    @Mapping(target = "activateCount", source = "appActiveCount")
    @Mapping(target = "gameReserveRate", source = "gameSubscribeApiRate")
    @Mapping(target = "gameReserveCount", source = "gameSubscribeApiCount")
    @Mapping(target = "gameReserveAverageCost", source = "costPerGameSubscribeApi")
    @Mapping(target = "gameActivateRate", source = "activateRate")
    @Mapping(target = "gameActivateCount", source = "activateCount")
    @Mapping(target = "gameActivateAverageCost", source = "costPerActivate")
    @Mapping(target = "formSubmitAverageCost", source = "costPerFormSubmit")
    @Mapping(target = "formPaidCost", source = "costPerFormPaid")
    @Mapping(target = "firstOrderPlaceCost", source = "costPerFirstOrderPlace")
    @Mapping(target = "firstDayPayROI", source = "firstDayPayRoi")
    @Mapping(target = "firstDayPayCost", source = "costPerFirstDayPay")
    @Mapping(target = "fansIncreaseRate", source = "fanIncreaseRate")
    @Mapping(target = "fansIncreaseCount", source = "fanIncreaseCount")
    @Mapping(target = "fansIncreaseAverageCost", source = "costPerFanIncrease")
    //@Mapping(target = "dynamicDetailPageBrowseCost", source = "costPerDynamicDetailPageBrowse")
    @Mapping(target = "creditCost", source = "costPerCredit")
    @Mapping(target = "costPerPlayCount", source = "costPerPlay")
    @Mapping(target = "firstCommentCopyCount", source = "firstCommentCount")
    @Mapping(target = "firstCommentCopyRate", source = "firstCommentRate")
    @Mapping(target = "costPerFirstCommentCopyCount", source = "costPerFirstComment")
    @Mapping(target = "commentClickRate", source = "commentUrlClickRate")
    @Mapping(target = "commentClickCost", source = "costPerCommentUrlClick")
    @Mapping(target = "commentClick", source = "commentUrlClickCount")
    @Mapping(target = "commentCallUpRate", source = "commentCallUpSuccessRate")
    @Mapping(target = "commentCallUpCount", source = "commentCallUpSuccessCount")
    @Mapping(target = "clickRate", source = "clickThroughRate")
    @Mapping(target = "averageCostPerThousand", source = "costPerMille")
    @Mapping(target = "applyCost", source = "costPerApply")
    @Mapping(target = "appCallupRate", source = "appCallUpRate")
    @Mapping(target = "appCallupCount", source = "appCallUpCount")
    @Mapping(target = "appCallupCost", source = "costPerAppCallUp")
    @Mapping(target = "appActivateRate", source = "appActiveRate")
    @Mapping(target = "appActivateAverageCost", source = "costPerAppActive")
    @Mapping(target = "androidInstallRate", source = "androidInstallSuccessRate")
    @Mapping(target = "androidInstallCount", source = "androidInstallSuccessCount")
    @Mapping(target = "androidInstallAverageCost", source = "costPerAndroidInstallSuccess")
    @Mapping(target = "androidGameCenterPaymentInAppCount", source = "gameUserCostCount")
    @Mapping(target = "androidGameCenterPaymentInAppAmount", source = "gameUserCostAmount")
    @Mapping(target = "androidGameCenterFirstPaymentInAppRate", source = "gameUserFirstCostRate")
    @Mapping(target = "androidGameCenterFirstPaymentInAppCount", source = "gameUserFirstCostCount")
    @Mapping(target = "androidGameCenterFirstPaymentInAppCost", source = "costPerGameUserFirstCost")
    @Mapping(target = "androidGameCenterFirstPaymentInAppAmount", source = "gameUserFirstCostAmount")
    @Mapping(target = "androidGameCenterActivationCost", source = "costPerAndroidGameCenterActivation")
    @Mapping(target = "androidDownloadRate", source = "androidDownloadSuccessRate")
    @Mapping(target = "androidDownloadCount", source = "androidDownloadSuccessCount")
    @Mapping(target = "androidDownloadAverageCost", source = "costPerAndroidDownloadSuccess")
    @Mapping(target = "activityPagePullUpCost", source = "costPerActivityPagePullUp")
    @Mapping(target = "accountSubscribeCost", source = "costPerAccountSubscribe")
    @Mapping(target = "callUpOrderSuccessRate", source = "callUpSuccessOrderRate")
    ReportSummaryVo statBo2SummaryVo(StatBo statBo);

    /**
     * convert StatBo to StatVo
     * @param statBo
     * @return StatVo
     */
    //@Mapping(target = "wxCopyCost", source = "costPerWxCopy")
    //@Mapping(target = "wxAddFansCost", source = "costPerWxAddFans")
    @Mapping(target = "withdrawDepositsCost", source = "costPerWithdrawDeposits")
    @Mapping(target = "validClueRate", source = "clueValidRate")
    @Mapping(target = "validClueCount", source = "clueValidCount")
    @Mapping(target = "validClueAverageCost", source = "costPerClueValid")
    @Mapping(target = "underBoxLinkClickRate", source = "underBoxLinkRate")
    @Mapping(target = "underBoxLinkClickCount", source = "underBoxLinkCount")
    @Mapping(target = "costPerUnderBoxLinkClickCount", source = "costPerUnderBoxLink")
    @Mapping(target = "retentionCost", source = "costPerRetention")
    @Mapping(target = "registerRate", source = "userRegisterRate")
    @Mapping(target = "registerCount", source = "userRegisterCount")
    @Mapping(target = "registerAverageCost", source = "costPerUserRegister")
    //@Mapping(target = "paidIn7dROI", source = "paidIn7DayRoi")
    //@Mapping(target = "paidIn7dPrice", source = "paidIn7DayAmount")
    //@Mapping(target = "paidIn7dCount", source = "paidIn7DayCount")
    //@Mapping(target = "paidIn7dCost", source = "costPerPaidIn7Day")
    //@Mapping(target = "paidIn24hROI", source = "paidIn24HourRoi")
    //@Mapping(target = "paidIn24hPrice", source = "paidIn24HourAmount")
    //@Mapping(target = "paidIn24hCount", source = "paidIn24HourCount")
    //@Mapping(target = "paidIn24hCost", source = "costPerPaidIn24Hour")
    @Mapping(target = "orderSubmitRate", source = "orderPlaceRate")
    @Mapping(target = "orderSubmitCount", source = "orderPlaceCount")
    @Mapping(target = "orderSubmitCost", source = "costPerOrderPlace")
    @Mapping(target = "orderSubmitAmount", source = "orderPlaceAmount")
    @Mapping(target = "orderPayCount", source = "userCostCount")
    @Mapping(target = "orderPayAmount", source = "userCostAmount")
    @Mapping(target = "orderFirstPayRate", source = "userFirstCostRate")
    @Mapping(target = "orderFirstPayCount", source = "userFirstCostCount")
    @Mapping(target = "orderFirstPayCost", source = "costPerUserFirstCost")
    @Mapping(target = "orderFirstPayAmount", source = "userFirstCostAmount")
    //@Mapping(target = "lpCallupRate", source = "lpCallUpRate")
    @Mapping(target = "lpCallupCount", source = "lpCallUpCount")
    //@Mapping(target = "lpCallupCost", source = "costPerLpCallUp")
    @Mapping(target = "lpCallUpSuccessCost", source = "costPerLpCallUpSuccess")
    @Mapping(target = "lpCallUpSuccessStayCost", source = "costPerLpCallUpSuccessStay")
    //@Mapping(target = "liveEntryCost", source = "costPerLiveEntry")
    @Mapping(target = "keyBehaviorRate", source = "actionValidRate")
    @Mapping(target = "keyBehaviorCount", source = "actionValidCount")
    @Mapping(target = "keyBehaviorCost", source = "costPerActionValid")
    @Mapping(target = "iosActivateCount", source = "iosAppActiveCount")
    @Mapping(target = "androidActivateCount", source = "androidAppActiveCount")
    @Mapping(target = "activateCount", source = "appActiveCount")
    @Mapping(target = "gameReserveRate", source = "gameSubscribeApiRate")
    @Mapping(target = "gameReserveCount", source = "gameSubscribeApiCount")
    @Mapping(target = "gameReserveAverageCost", source = "costPerGameSubscribeApi")
    //@Mapping(target = "gameActivateRate", source = "activateRate")
    //@Mapping(target = "gameActivateCount", source = "activateCount")
    //@Mapping(target = "gameActivateAverageCost", source = "costPerActivate")
    @Mapping(target = "formSubmitAverageCost", source = "costPerFormSubmit")
    @Mapping(target = "formPaidCost", source = "costPerFormPaid")
    //@Mapping(target = "firstOrderPlaceCost", source = "costPerFirstOrderPlace")
    @Mapping(target = "firstDayPayROI", source = "firstDayPayRoi")
    @Mapping(target = "firstDayPayCost", source = "costPerFirstDayPay")
    @Mapping(target = "fansIncreaseRate", source = "fanIncreaseRate")
    @Mapping(target = "fansIncreaseCount", source = "fanIncreaseCount")
    @Mapping(target = "fansIncreaseAverageCost", source = "costPerFanIncrease")
    //@Mapping(target = "dynamicDetailPageBrowseCost", source = "costPerDynamicDetailPageBrowse")
    @Mapping(target = "creditCost", source = "costPerCredit")
    @Mapping(target = "costPerPlayCount", source = "costPerPlay")
    @Mapping(target = "firstCommentCopyCount", source = "firstCommentCount")
    @Mapping(target = "firstCommentCopyRate", source = "firstCommentRate")
    @Mapping(target = "costPerFirstCommentCopyCount", source = "costPerFirstComment")
    @Mapping(target = "commentClickRate", source = "commentUrlClickRate")
    @Mapping(target = "commentClickCost", source = "costPerCommentUrlClick")
    @Mapping(target = "commentClick", source = "commentUrlClickCount")
    @Mapping(target = "commentCallUpRate", source = "commentCallUpSuccessRate")
    @Mapping(target = "commentCallUpCount", source = "commentCallUpSuccessCount")
    @Mapping(target = "clickRate", source = "clickThroughRate")
    @Mapping(target = "averageCostPerThousand", source = "costPerMille")
    @Mapping(target = "applyCost", source = "costPerApply")
    @Mapping(target = "appCallupRate", source = "appCallUpRate")
    @Mapping(target = "appCallupCount", source = "appCallUpCount")
    @Mapping(target = "appCallupCost", source = "costPerAppCallUp")
    @Mapping(target = "appActivateRate", source = "appActiveRate")
    @Mapping(target = "appActivateAverageCost", source = "costPerAppActive")
    @Mapping(target = "androidInstallRate", source = "androidInstallSuccessRate")
    @Mapping(target = "androidInstallCount", source = "androidInstallSuccessCount")
    @Mapping(target = "androidInstallAverageCost", source = "costPerAndroidInstallSuccess")
    @Mapping(target = "androidGameCenterPaymentInAppCount", source = "gameUserCostCount")
    @Mapping(target = "androidGameCenterPaymentInAppAmount", source = "gameUserCostAmount")
    @Mapping(target = "androidGameCenterFirstPaymentInAppRate", source = "gameUserFirstCostRate")
    @Mapping(target = "androidGameCenterFirstPaymentInAppCount", source = "gameUserFirstCostCount")
    @Mapping(target = "androidGameCenterFirstPaymentInAppCost", source = "costPerGameUserFirstCost")
    @Mapping(target = "androidGameCenterFirstPaymentInAppAmount", source = "gameUserFirstCostAmount")
    @Mapping(target = "androidGameCenterActivationCost", source = "costPerAndroidGameCenterActivation")
    @Mapping(target = "androidDownloadRate", source = "androidDownloadSuccessRate")
    @Mapping(target = "androidDownloadCount", source = "androidDownloadSuccessCount")
    @Mapping(target = "androidDownloadAverageCost", source = "costPerAndroidDownloadSuccess")
    //@Mapping(target = "activityPagePullUpCost", source = "costPerActivityPagePullUp")
    @Mapping(target = "accountSubscribeCost", source = "costPerAccountSubscribe")
    @Mapping(target = "callUpOrderSuccessRate", source = "callUpSuccessOrderRate")
    //@Mapping(target = "callUpOrderSuccessRate", source = "callUpSuccessOrderRate")
    @Mapping(target = "newFirstDayPayCount", source = "newFirstDayPayCount")
    StatVo bo2Vo(StatBo statBo);
}
