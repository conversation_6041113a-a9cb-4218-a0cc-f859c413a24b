package com.bilibili.adp.advertiser.portal.webapi.launch.cpc;

import com.bilibili.adp.account.dto.AccountBaseDto;
import com.bilibili.adp.account.service.IQueryAccountService;
import com.bilibili.adp.advertiser.portal.common.BasicExportController;
import com.bilibili.adp.advertiser.portal.common.Context;
import com.bilibili.adp.advertiser.portal.service.stat.HystrixUnitService;
import com.bilibili.adp.advertiser.portal.webapi.launch.StatVo;
import com.bilibili.adp.advertiser.portal.webapi.launch.cpc.vo.CpcUnitReportVo;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.enums.SalesMode;
import com.bilibili.adp.common.enums.SalesType;
import com.bilibili.adp.common.util.Page;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.biz.bos.unit.QueryUnitBo;
import com.bilibili.adp.cpc.biz.services.campaign.api.ICpcCampaignService;
import com.bilibili.adp.cpc.biz.services.unit.api.ICpcUnitService;
import com.bilibili.adp.cpc.biz.services.unit.dto.CpcUnitDto;
import com.bilibili.adp.cpc.biz.services.unit.dto.QueryCpcUnitDto;
import com.bilibili.adp.cpc.core.LaunchUnitV1Service;
import com.bilibili.adp.cpc.biz.services.campaign.dto.QueryCpcCampaignDto;
import com.bilibili.adp.launch.api.common.CampaignStatus;
import com.bilibili.adp.launch.api.common.UnitStatus;
import com.bilibili.adp.resource.api.account.label.IAccountLabelService;
import com.bilibili.adp.web.framework.core.Pagination;
import com.bilibili.adp.web.framework.core.Response;
import com.bilibili.crm.platform.common.IsValid;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.sql.Timestamp;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: wangbin01
 * @create: 2018-11-21
 **/
@Deprecated
@Slf4j
@RestController
@RequestMapping("/web_api/v1/launch/cpc/unit")
@Api(value = "/cpc/unit")
@RequiredArgsConstructor
public class CpcUnitFromESController extends BasicExportController {

    @Value("${platform.launch.support.orderby:false}")
    private boolean isSupportOrderBy;

    @Autowired
    private ICpcUnitService cpcUnitService;
    @Autowired
    private HystrixUnitService hystrixUnitService;
    @Autowired
    private IQueryAccountService queryAccountService;
    @Autowired
    private ICpcCampaignService cpcCampaignService;
    @Autowired
    private IAccountLabelService accountLabelService;
    private final LaunchUnitV1Service launchUnitV1Service;

    public static final String ORDER_BY_CAMPAIGN_ID_DESC = "campaignId desc";
    public static final String ORDER_BY_UNIT_ID_DESC = "unitId desc";

    @ApiOperation(value = "查询推广单元报告列表")
    @GetMapping(value = "/report")
    @ResponseBody
    public Response<Pagination<List<CpcUnitReportVo>>> queryUnitList(
            @ApiIgnore Context context,
            @RequestParam(value = "campaign_id", required = false) Integer campaignId,
            @RequestParam(value = "unit_ids", required = false, defaultValue = "") List<Integer> uids,
            @RequestParam(value = "unit_name", required = false) String likeUnitName,
            @RequestParam(value = "status", required = false) List<Integer> statusList,
            @RequestParam(value = "slot_group_id", required = false) List<Integer> slotGroupIds,
            @RequestParam(value = "sales_mode", required = false) List<Integer> salesModes,
            @RequestParam(value = "ocpc_target", required = false) List<Integer> ocpcTagets,
            @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
            @RequestParam(value = "page_size", required = false, defaultValue = "10") Integer pageSize,
            @RequestParam(value = "time_caliber", required = false) Integer timeCaliber,
            @RequestParam(value = "is_high_priority", required = false) @ApiParam("0-否, 1-是") Integer isHighPriority,
            @RequestParam(value = "from_time", required = false) Long fromTime,
            @RequestParam(value = "to_time", required = false) Long toTime,
            @RequestParam(value = "sort_field", required = false, defaultValue = "") String sortField,
            @RequestParam(value = "sort_type", required = false, defaultValue = "0") @ApiParam("0-正序, 1-倒序") int sortType,
            @RequestParam(value = "campaign_page", required = false, defaultValue = "1") Integer campaignPage,
            @RequestParam(value = "campaign_size", required = false, defaultValue = "100") Integer campaignSize,
            @RequestParam(value = "campaign_name_like", required = false) String campaignNameLike,
            @RequestParam(value = "is_middle_ad", required = false, defaultValue = "1") Integer isMiddleAd
    ) {

        boolean queryByIds = !CollectionUtils.isEmpty(uids);
        boolean hasOrderBy = isSupportOrderBy
                && !StringUtils.isEmpty(sortField);
        List<Integer> unitStatusList = CollectionUtils.isEmpty(statusList) ?
                UnitStatus.NON_DELETED_UNIT_STATUS_LIST : statusList;
        QueryUnitBo.QueryUnitBoBuilder builder = QueryUnitBo.builder()
                .unitIds(uids)
                //.accountId(context.getAccountId());
                .accountIds(Collections.singletonList(context.getAccountId()));

        if (!queryByIds) {
            //builder.campaignId(campaignId)
            builder.campaignIds(Collections.singletonList(campaignId))
                    .ocpcTargets(ocpcTagets)
                    .slotGroupIds(slotGroupIds)
                    .unitStatusList(unitStatusList)
                    .likeUnitName(likeUnitName)
                    .isHighPriority(isHighPriority);
        }
        if (!CollectionUtils.isEmpty(salesModes)) {
            builder.salesModes(salesModes);
            List<Integer> salesTypes = buildSalesTypesBySalesMode(salesModes);
            if (salesTypes.contains(SalesType.CPC.getCode())) {
                if (!salesModes.contains(SalesMode.OCPC.getCode())) {
                    builder.onlyCpc(true);
                }
                if (!salesModes.contains(SalesMode.CPC.getCode())) {
                    builder.onlyOcpc(true);
                }
            }
            builder.salesTypes(salesTypes);
        }
        if (!(queryByIds || hasOrderBy)) {
            builder.page(Page.valueOf(page, pageSize));
        }
        builder.orderBy(ORDER_BY_UNIT_ID_DESC)
                .isMiddleAd(isMiddleAd);
        if (Objects.equals(0, isMiddleAd)) {
            builder .isNewFly(0);
        }
        QueryUnitBo query = builder.build();
        if (campaignId == null && !queryByIds) {
            List<Integer> treeCampaignIds = cpcCampaignService.queryCpcCampaignIds(
                    QueryCpcCampaignDto.builder()
                            .accountId(context.getAccountId())
                            .likeCampaignName(campaignNameLike)
                            .campaignStatusList(CampaignStatus.NON_DELETED_CAMPAIGN_STATUS_LIST)
                            .page(Page.valueOf(campaignPage, campaignSize))
                            .orderBy(ORDER_BY_CAMPAIGN_ID_DESC)
                            .isMiddleAd(isMiddleAd)
                            .build()
            );
            if (CollectionUtils.isEmpty(treeCampaignIds)) {
                return Response.SUCCESS(Pagination.emptyPagination());
            } else {
                query.setCampaignIds(treeCampaignIds);
            }
        }
        //PageResult<CpcUnitDto> pageResult = cpcUnitService.queryCpcUnitByPage(query);
        PageResult<CpcUnitDto> pageResult = launchUnitV1Service.getUnitPage(query);
        List<CpcUnitDto> unitList = pageResult.getRecords();
        if (CollectionUtils.isEmpty(unitList)) {
            return Response.SUCCESS(new Pagination<>(page, pageResult.getTotal(), Collections.emptyList()));
        }
        Timestamp startTime = fromTime == null ? null : Utils.getBeginOfDay(Utils.getTimestamp(fromTime));
        Timestamp endTime = toTime == null ? null : Utils.getEndOfDay(Utils.getTimestamp(toTime));
        List<CpcUnitReportVo> resultVos = hystrixUnitService.getCpcUnitReportVosFromESV2(
                context.getAccountId(), campaignId, page,
                pageSize, sortField, sortType,
                timeCaliber, startTime, endTime,
                hasOrderBy, unitList, true, true);

        return Response.SUCCESS(new Pagination<>(page, pageResult.getTotal(), resultVos));
    }

    private List<Integer> buildSalesTypesBySalesMode(List<Integer> salesModes) {
        return salesModes
                .stream()
                .flatMap(mode -> SalesMode.getByCode(mode).getSalesTypes().stream())
                .distinct()
                .collect(Collectors.toList());
    }

    @ApiOperation(value = "查询推广单元统计数据")
    @GetMapping(value = "/total_statistc")
    @ResponseBody
    public Response<StatVo> queryUnitTotalStatistics(
            @ApiIgnore Context context,
            @RequestParam(value = "campaign_id", required = false) Integer campaignId,
            @RequestParam(value = "unit_ids", required = false, defaultValue = "") List<Integer> uids,
            @RequestParam(value = "status", required = false) List<Integer> statusList,
            @RequestParam(value = "unit_name", required = false) String likeUnitName,
            @RequestParam(value = "sales_type", required = false) List<Integer> salesTypes,
            @RequestParam(value = "slot_group_id", required = false) List<Integer> slotGroupIds,
            @RequestParam(value = "time_caliber", required = false) Integer timeCaliber,
            @RequestParam(value = "from_time", required = false) Long fromTime,
            @RequestParam(value = "to_time", required = false) Long toTime,
            @RequestParam(value = "campaign_page", required = false, defaultValue = "1") Integer campaignPage,
            @RequestParam(value = "campaign_size", required = false, defaultValue = "100") Integer campaignSize,
            @RequestParam(value = "campaign_name_like", required = false) String campaignNameLike,
            @RequestParam(value = "is_middle_ad", required = false, defaultValue = "0") Integer isMiddleAd
    ) {

        AccountBaseDto accountBaseDto = queryAccountService.getAccountBaseDtoByIdFromCache(context.getAccountId());
        if (IsValid.TRUE.getCode().equals(accountBaseDto.getIsSupportDpa())) {
            return Response.SUCCESS(StatVo.getEmptyVo());
        }
        boolean queryByIds = !CollectionUtils.isEmpty(uids);
        List<Integer> unitStatusList = CollectionUtils.isEmpty(statusList) ?
                UnitStatus.NON_DELETED_UNIT_STATUS_LIST : statusList;
        QueryCpcUnitDto.QueryCpcUnitDtoBuilder builder = QueryCpcUnitDto.builder();
        builder.accountId(context.getAccountId());
        if (!queryByIds) {
            builder.campaignId(campaignId)
                    .salesTypes(salesTypes)
                    .slotGroupIds(slotGroupIds)
                    .unitStatusList(unitStatusList)
                    .likeUnitName(likeUnitName);
        }
        builder.orderBy(ORDER_BY_UNIT_ID_DESC);
        if (Objects.equals(0, isMiddleAd)) {
            builder.isNewFly(0);
        }
        QueryCpcUnitDto query = builder.build();
        if (campaignId == null && !queryByIds) {
            List<Integer> treeCampaignIds = cpcCampaignService.queryCpcCampaignIds(
                    CpcCampaignController.getQueryCampaignParam(
                            context.getAccountId(), campaignPage, campaignSize,
                            campaignNameLike, isMiddleAd));
            if (CollectionUtils.isEmpty(treeCampaignIds)) {
                return Response.SUCCESS(StatVo.getEmptyVo());
            } else {
                query.setCampaignIds(treeCampaignIds);
            }
        }

        List<Integer> unitIds = cpcUnitService.queryCpcUnitIds(query);
        if (CollectionUtils.isEmpty(unitIds)) {
            return Response.SUCCESS(StatVo.getEmptyVo());
        }
        Timestamp startTime = fromTime == null ? null : Utils.getBeginOfDay(Utils.getTimestamp(fromTime));
        Timestamp endTime = toTime == null ? null : Utils.getEndOfDay(Utils.getTimestamp(toTime));
        StatVo statVo = hystrixUnitService.getTotalStatVoFromES(
                context, campaignId, timeCaliber,
                startTime, endTime, unitIds);
        return Response.SUCCESS(statVo);
    }

}
