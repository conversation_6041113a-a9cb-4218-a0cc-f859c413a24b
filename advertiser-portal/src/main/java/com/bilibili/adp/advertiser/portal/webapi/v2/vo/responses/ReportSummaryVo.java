package com.bilibili.adp.advertiser.portal.webapi.v2.vo.responses;

import com.bilibili.adp.advertiser.portal.webapi.launch.cpc.vo.CpcReportCommonColumnVo;
import com.bilibili.adp.cpc.biz.services.material.report.bos.DwsFlowProgrammaticCreativeStatL1hr1dBo;
import com.bilibili.adp.cpc.utils.CommonFuncs;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collection;
import java.util.Objects;
import java.util.Optional;

@Data
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ReportSummaryVo {
    /**
     * 次日预算是否开启重复
     * 此处骚操作，放在上层前端拿不到。。。
     */
    private Integer isRepeat;
    private Long showCount;
    private Integer clickCount;
    private BigDecimal clickRate;

    private Long chargedCostMilli;

    private BigDecimal averageCostPerThousand;
    private BigDecimal costPerClick;
    private BigDecimal cost;

    private Integer orderSubmitCount;

    /**
     * 订单提交金额
     */
    private BigDecimal orderSubmitAmount;
    private BigDecimal orderSubmitCost;
    private BigDecimal orderSubmitRate;

    private Integer firstOrderPlaceCount;
    private BigDecimal firstOrderPlaceAmount;
    private BigDecimal firstOrderPlaceRate;
    private BigDecimal firstOrderPlaceCost;

    /**
     * 首日付费相关 改成当日付费相关
     * https://www.tapd.bilibili.co/67874887/prong/stories/view/1167874887002966600
     */
    private Integer firstDayPayCount;
    private BigDecimal firstDayPayAmount;
    private BigDecimal firstDayPayRate;
    private BigDecimal firstDayPayCost;
    private BigDecimal firstDayPayROI;

    /**
     * 首日付费相关 改成当日付费相关
     * https://www.tapd.bilibili.co/67874887/prong/stories/view/1167874887002966600
     */
    private Integer newFirstDayPayCount;
    private BigDecimal newFirstDayPayAmount;
    private BigDecimal newFirstDayPayRate;
    private BigDecimal newFirstDayPayCost;
    private BigDecimal newFirstDayPayRoi;

    /**
     * 应用内付费次数
     */
    private Integer orderPayCount;
    /**
     * 应用内累计付费金额
     */
    private BigDecimal orderPayAmount;
    /**
     * 应用内首次付费次数
     */
    private Integer orderFirstPayCount;
    /**
     * 应用内首次付费金额
     */
    private BigDecimal orderFirstPayAmount;
    /**
     * 应用内首次付费消耗 = cost(总花费) / orderFirstPayCount(应用内首次付费次数)
     */
    private BigDecimal orderFirstPayCost;
    /**
     * 应用内首次付费比例 = orderFirstPayCount(应用内首次付费数) / clickCount(点击数)
     */
    private BigDecimal orderFirstPayRate;
    /**
     * 安卓游戏中心 应用内付费数
     */
    private Integer androidGameCenterPaymentInAppCount;
    /**
     * 安卓游戏中心 应用内付费金额
     */
    private BigDecimal androidGameCenterPaymentInAppAmount;
    /**
     * 安卓游戏中心 应用内首次付费数
     */
    private Integer androidGameCenterFirstPaymentInAppCount;
    /**
     * 安卓游戏中心 应用内首次付费金额
     */
    private BigDecimal androidGameCenterFirstPaymentInAppAmount;
    /**
     * 安卓游戏中心 应用内首次付费成本 【安卓游戏中心应用内首次付费成本】=总花费/【安卓游戏中心应用内首次付费数】
     */
    private BigDecimal androidGameCenterFirstPaymentInAppCost;
    /**
     * 安卓游戏中心 应用内首次付费率 【安卓游戏中心应用内首次付费率】=【安卓游戏中心应用内首次付费数】/【安卓游戏中心应用激活数】
     */
    private BigDecimal androidGameCenterFirstPaymentInAppRate;

    private Integer androidGameActivePaidIn24hCount;
    private BigDecimal androidGameActivePaidIn24hAmount;
    private BigDecimal androidGameActivePaidIn24hCost;
    private BigDecimal androidGameActivePaidIn24hRoi;

    private Integer androidGameActivePaidIn7dCount;
    private BigDecimal androidGameActivePaidIn7dAmount;
    private BigDecimal androidGameActivePaidIn7dCost;
    private BigDecimal androidGameActivePaidIn7dRoi;

    private Integer androidGameActivePaidIn1dCount;
    private BigDecimal androidGameActivePaidIn1dAmount;
    private BigDecimal androidGameActivePaidIn1dCost;
    private BigDecimal androidGameActivePaidIn1dRoi;
    private BigDecimal androidGameActivePaidIn1dRate;
    /**
     * 关键行为数
     */
    private Integer keyBehaviorCount;
    /**
     * 关键行为成本 【关键行为成本】=总花费/【关键行为数】
     */
    private BigDecimal keyBehaviorCost;
    /**
     * 关键行为率 【关键行为率】=【关键行为数】/【应用激活】
     */
    private BigDecimal keyBehaviorRate;

    private Integer registerCount;
    private BigDecimal registerAverageCost;
    private BigDecimal registerRate;

    /**
     * 订单转化率
     */
    private BigDecimal goodsConversionRate;

    /**
     * 【唤起成单率】=【订单提交数】/【应用唤起数】
     */
    private BigDecimal callUpOrderSuccessRate;

    /**
     * 订单ROI =  订单提交金额 / 总花费
     */
    private BigDecimal goodsRoi;

    private Integer gameActivateCount;
    private BigDecimal gameActivateAverageCost;
    private BigDecimal gameActivateRate;

    private Integer gameReserveCount;
    private BigDecimal gameReserveAverageCost;
    private BigDecimal gameReserveRate;

    private Integer fansIncreaseCount;
    private BigDecimal fansIncreaseAverageCost;
    private BigDecimal fansIncreaseRate;
    private BigDecimal play2FansRate;

    private Integer iosActivateCount;
    private Integer androidActivateCount;
    private Integer activateCount;
    private BigDecimal appActivateAverageCost;
    private BigDecimal appActivateRate;

    private Integer formSubmitCount;
    private BigDecimal formSubmitAverageCost;
    private BigDecimal formSubmitRate;

    private Integer androidDownloadCount;
    private BigDecimal androidDownloadAverageCost;
    private BigDecimal androidDownloadRate;

    private Integer androidInstallCount;
    private BigDecimal androidInstallAverageCost;
    private BigDecimal androidInstallRate;

    private Integer validClueCount;
    private BigDecimal validClueAverageCost;
    private BigDecimal validClueRate;

    private Integer retentionCount;
    private BigDecimal retentionCost;
    private BigDecimal retentionRate;

    private Integer appCallupCount;
    private BigDecimal appCallupCost;
    private BigDecimal appCallupRate;

    private Integer lpCallupCount;
    private BigDecimal lpCallupCost;
    private BigDecimal lpCallupRate;

    private Integer androidGameCenterActivationCount;
    private BigDecimal androidGameCenterActivationCost;
    private BigDecimal androidGameCenterActivationRate;

    private Integer formPaidCount;
    private BigDecimal formPaidCost;
    private BigDecimal formPaidRate;

    // 下载没有
    private Integer lpCallUpSuccessCount;
    private BigDecimal lpCallUpSuccessCost;
    private BigDecimal lpCallUpSuccessRate;

    private Integer lpCallUpSuccessStayCount;
    private BigDecimal lpCallUpSuccessStayCost;
    private BigDecimal lpCallUpSuccessStayRate;

    private Integer accountSubscribeCount;
    private BigDecimal accountSubscribeCost;
    private BigDecimal accountSubscribeRate;

    private Integer dynamicDetailPageBrowseCount;
    private BigDecimal dynamicDetailPageBrowseCost;

    private BigDecimal playCost;
    private Integer playCount;
    private BigDecimal costPerPlayCount;
    private Integer playShowCount;
    private BigDecimal playRate;

    private Integer underBoxLinkClickCount;
    private BigDecimal costPerUnderBoxLinkClickCount;
    private BigDecimal underBoxLinkClickRate;

    private Integer firstCommentCopyCount;
    private BigDecimal costPerFirstCommentCopyCount;
    private BigDecimal firstCommentCopyRate;

    /**
     * 评论链接点击数
     */
    private Integer commentClick;
    /**
     * 评论链接点击成本（消耗/评论链接点击数)
     */
    private BigDecimal commentClickCost;

    /**
     * 评论链接点击率（评论链接点击数/播放数）
     */
    private BigDecimal commentClickRate;

    /**
     * 微信复制数
     */
    private Integer wxCopyCount;
    /**
     * 【微信复制成本】=总花费/【微信复制数】
     */
    private BigDecimal wxCopyCost;
    /**
     * 【微信复制率】=【微信复制数】/点击量
     */
    private BigDecimal wxCopyRate;
    /**
     * 完件数
     */
    private Integer applyCount;
    /**
     * 【完件成本】= 总花费 /【完件数】
     */
    private BigDecimal applyCost;
    /**
     * 【完件率】=【完件数】/ 点击量
     */
    private BigDecimal applyRate;

    /**
     * 授信数
     */
    private Integer creditCount;
    /**
     * 【授信成本】= 总花费 /【授信数】
     */
    private BigDecimal creditCost;
    /**
     * 【授信率】=【授信数】/ 点击量
     */
    private BigDecimal creditRate;

    /**
     * 放款数
     */
    private Integer withdrawDepositsCount;
    /**
     * 【放款成本】= 总花费 /【放款数】
     */
    private BigDecimal withdrawDepositsCost;
    /**
     * 【放款率】=【放款数】/ 点击量
     */
    private BigDecimal withdrawDepositsRate;

    private Integer videoLikeCount;
    private BigDecimal videoLikeRate;
    private BigDecimal costPerVideoLike;
    private Integer videoFavCount;
    private BigDecimal videoFavRate;
    private BigDecimal costPerVideoFav;
    private Integer videoShareCount;
    private BigDecimal videoShareRate;
    private BigDecimal costPerVideoShare;
    private Integer videoBulletCount;
    private BigDecimal videoBulletRate;
    private BigDecimal costPerVideoBullet;
    private Integer videoReplyCount;
    private BigDecimal videoReplyRate;
    private BigDecimal costPerVideoReply;
    private Integer videoCoinCount;
    private BigDecimal videoCoinRate;
    private BigDecimal costPerVideoCoin;
    private Integer videoInteractCount;
    private BigDecimal videoInteractRate;
    private BigDecimal costPerVideoInteract;

    /**
     * 24h付费数
     */
    private Integer paidIn24hCount;

    /**
     * 24h付费金额
     */
    private BigDecimal paidIn24hPrice;

    /**
     * 24h付费成本
     */
    private BigDecimal paidIn24hCost;

    /**
     * 24h付ROI
     */
    private BigDecimal paidIn24hROI;

    /**
     * 7d付费数
     */
    private Integer paidIn7dCount;

    /**
     * 7d付费金额
     */
    private BigDecimal paidIn7dPrice;

    /**
     * 7d付费成本
     */
    private BigDecimal paidIn7dCost;

    /**
     * 7d付ROI
     */
    private BigDecimal paidIn7dROI;


    @ApiModelProperty(notes = "ocpx转化-销售线索搜集-微信加粉数量")
    private Integer wxAddFansCount;

    @ApiModelProperty(notes = "ocpx转化-销售线索搜集-微信加粉率")
    private BigDecimal wxAddFansRate;

    @ApiModelProperty(notes = "ocpx转化-销售线索搜集-微信加粉成本")
    private BigDecimal wxAddFansCost;

    @ApiModelProperty(notes = "评论链接曝光数")
    private Integer commentShowCount;

    @ApiModelProperty(notes = "评论链接曝光率")
    private BigDecimal commentShowRate;

    @ApiModelProperty(notes = "评论链接曝光点击率")
    private BigDecimal commentShowClickRate;

    @ApiModelProperty(notes = "评论链接唤起数量")
    private Integer commentCallUpCount;

    @ApiModelProperty(notes = "评论唤起率")
    private BigDecimal commentCallUpRate;

    @ApiModelProperty(notes = "播放唤起率")
    private BigDecimal playCallUpRate;

    /**
     * 后来回填的，排序用
     */
    private BigDecimal dynamicDetailPageBrowseRate;
    // 活动拉起次数
    private Integer activityPagePullUpCount;
    // 活动拉起成本
    private BigDecimal activityPagePullUpCost;
    private BigDecimal activityPagePullUpRate;

    /**
     * 直播间商品点击次数
     */
    private Integer liveEntryCount;
    private BigDecimal liveEntryCost;
    private BigDecimal liveEntryRate;

    /**
     * 直播间进场人数
     */
    private Integer liveCallUpCount;
    private BigDecimal liveCallUpCost;
    private BigDecimal liveCallUpRate;

    /**
     * 直播间预约数
     */
    private Integer liveReserveCount;
    private BigDecimal liveReserveCost;
    private BigDecimal liveReserveRate;

    private Integer firstWithdrawCount;
    private BigDecimal firstWithdrawCost;
    private BigDecimal firstWithdrawRate;
    /**
     * 进店UV
     */
    private Long uvNum;
    /**
     * 新访客UV
     */
    private Long newVisitorUvNum;
    /**
     * 成交UV
     */
    private Long payBuyerUv;
    /**
     * 收藏UV
     */
    private Long cltUvNum;

    /**
     * 手淘搜索进店UV
     */
    private Long seLeadConv;
    /**
     * 加购UV
     */
    private Long addUvNum;

    private Integer componentClickCount;
    private BigDecimal componentClickRate;
    private BigDecimal componentClickCost;


    private Integer liveGameCardShowCount;
    private Integer liveGameCardClickCount;
    private BigDecimal liveGameCardClickRate;
    private BigDecimal liveGameCardClickCost;

    private Integer liveBottomIconShow;
    private Integer liveBottomIconClick;
    private Integer liveNativeCardShow;
    private Integer liveNativeCardClick;


    private Integer dynamicDetailBrowseCount;
    private Integer dynamicGoodsUrlShowCount;
    private Integer dynamicGoodsUrlClickCount;

    /**
     * 智能创意花费（元）
     */
    private BigDecimal intelligentCost;
    /**
     * 智能创意花费占比
     */
    private BigDecimal intelligentCostRate;


    private Long cardOpenCount;
    private BigDecimal cardOpenRate;
    private BigDecimal costPerCardOpen;

    private BigDecimal notAutoPlayClickThroughRate;

    @JsonProperty(value = "game_charge_in_24h_count")
    private Long gameChargeIn24HCount;
    @JsonProperty(value = "game_charge_in_24h_amount")
    private BigDecimal gameChargeIn24HAmount;
    @JsonProperty(value = "cost_per_game_charge_in_24h")
    private BigDecimal costPerGameChargeIn24H;
    @JsonProperty(value = "game_charge_in_24h_roi")
    private BigDecimal gameChargeIn24HRoi;

    @JsonProperty(value = "game_charge_in_1d_count")
    private Long gameChargeIn1DCount;
    @JsonProperty(value = "game_charge_in_1d_amount")
    private BigDecimal gameChargeIn1DAmount;
    @JsonProperty(value = "cost_per_game_charge_in_1d")
    private BigDecimal costPerGameChargeIn1D;
    @JsonProperty(value = "game_charge_in_1d_roi")
    private BigDecimal gameChargeIn1DRoi;

    @JsonProperty(value = "game_charge_in_7d_count")
    private Long gameChargeIn7DCount;
    @JsonProperty(value = "game_charge_in_7d_amount")
    private BigDecimal gameChargeIn7DAmount;
    @JsonProperty(value = "cost_per_game_charge_in_7d")
    private BigDecimal costPerGameChargeIn7D;
    @JsonProperty(value = "game_charge_in_7d_roi")
    private BigDecimal gameChargeIn7DRoi;

    private Long browseUv;
    private Long addCartUv;
    private Long followUv;
    private Long orderUv;
    private BigDecimal orderGmv;
    private Long dealUv;
    private BigDecimal dealGmv;
    private Long newUserNum;
    private Long newBrowseUserNum;
    private Long searchUv;

    private Long videoLikeOldCount;
    private Long videoInteractOldCount;

    private Long messageChatCount;
    private BigDecimal messageChatRate;
    private BigDecimal costPerMessageChat;
    private Long messageChatUvCount;

    private Long shallowConvCount;

    private Long gameFirstChargeCount;

    private Long messageLeadCount;
    private BigDecimal messageLeadRate;
    private BigDecimal costPerMessageLead;

    @JsonProperty(value = "game_charge_in_14d_count")
    private Long gameChargeIn14dCount;
    @JsonProperty(value = "game_charge_in_14d_amount")
    private BigDecimal gameChargeIn14dAmount;
    @JsonProperty(value = "cost_per_game_charge_in_14d")
    private BigDecimal costPerGameChargeIn14d;
    @JsonProperty(value = "game_charge_in_14d_roi")
    private BigDecimal gameChargeIn14dRoi;

    @JsonProperty(value = "game_charge_in_30d_count")
    private Long gameChargeIn30dCount;
    @JsonProperty(value = "game_charge_in_30d_amount")
    private BigDecimal gameChargeIn30dAmount;
    @JsonProperty(value = "cost_per_game_charge_in_30d")
    private BigDecimal costPerGameChargeIn30d;
    @JsonProperty(value = "game_charge_in_30d_roi")
    private BigDecimal gameChargeIn30dRoi;

    @JsonProperty(value = "paid_in_14d_count")
    private Long paidIn14dCount;
    @JsonProperty(value = "paid_in_14d_amount")
    private BigDecimal paidIn14dAmount;
    @JsonProperty(value = "cost_per_paid_in_14d")
    private BigDecimal costPerPaidIn14d;
    @JsonProperty(value = "paid_in_14d_roi")
    private BigDecimal paidIn14dRoi;

    @JsonProperty(value = "paid_in_30d_count")
    private Long paidIn30dCount;
    @JsonProperty(value = "paid_in_30d_amount")
    private BigDecimal paidIn30dAmount;
    @JsonProperty(value = "cost_per_paid_in_30d")
    private BigDecimal costPerPaidIn30d;
    @JsonProperty(value = "paid_in_30d_roi")
    private BigDecimal paidIn30dRoi;

    @JsonProperty(value = "game_charge_in_24h_mix_amount")
    private BigDecimal gameChargeIn24hMixAmount;
    @JsonProperty(value = "game_charge_in_24h_mix_roi")
    private BigDecimal gameChargeIn24hMixRoi;
    @JsonProperty(value = "game_charge_in_1d_mix_amount")
    private BigDecimal gameChargeIn1dMixAmount;
    @JsonProperty(value = "game_charge_in_1d_mix_roi")
    private BigDecimal gameChargeIn1dMixRoi;
    @JsonProperty(value = "game_charge_in_7d_mix_amount")
    private BigDecimal gameChargeIn7dMixAmount;
    @JsonProperty(value = "game_charge_in_7d_mix_roi")
    private BigDecimal gameChargeIn7dMixRoi;

    @JsonProperty(value = "game_charge_in_14d_mix_amount")
    private BigDecimal gameChargeIn14dMixAmount;
    @JsonProperty(value = "game_charge_in_14d_mix_roi")
    private BigDecimal gameChargeIn14dMixRoi;
    @JsonProperty(value = "game_charge_in_30d_mix_amount")
    private BigDecimal gameChargeIn30dMixAmount;
    @JsonProperty(value = "game_charge_in_30d_mix_roi")
    private BigDecimal gameChargeIn30dMixRoi;

    @JsonProperty(value = "retention_3d_count")
    private Long retention3dCount;
    @JsonProperty(value = "retention_3d_rate")
    private BigDecimal retention3dRate;
    @JsonProperty(value = "cost_per_retention_3d")
    private BigDecimal costPerRetention3d;

    @JsonProperty(value = "retention_5d_count")
    private Long retention5dCount;
    @JsonProperty(value = "retention_5d_rate")
    private BigDecimal retention5dRate;
    @JsonProperty(value = "cost_per_retention_5d")
    private BigDecimal costPerRetention5d;

    @JsonProperty(value = "retention_7d_count")
    private Long retention7dCount;
    @JsonProperty(value = "retention_7d_rate")
    private BigDecimal retention7dRate;
    @JsonProperty(value = "cost_per_retention_7d")
    private BigDecimal costPerRetention7d;

    private Long retentionDaysCount;
    private BigDecimal retentionDaysRate;
    private BigDecimal costPerRetentionDays;

    private Long openActionValidCount;
    private BigDecimal costPerOpenActionValid;

    public static ReportSummaryVo clearAndroidData(ReportSummaryVo vo) {
        vo.setAndroidInstallCount(0);
        vo.setAndroidInstallRate(BigDecimal.ZERO);
        vo.setAndroidInstallAverageCost(BigDecimal.ZERO);
        vo.setAndroidDownloadCount(0);
        vo.setAndroidDownloadRate(BigDecimal.ZERO);
        vo.setAndroidDownloadAverageCost(BigDecimal.ZERO);
        return vo;
    }

    public static ReportSummaryVo fromCpcReportCommonColumnVo(CpcReportCommonColumnVo cpcVo) {
        if (Objects.isNull(cpcVo)) {
            return emptyInstance();
        }
        ReportSummaryVo reportSummaryVo = new ReportSummaryVo();

        reportSummaryVo.setShowCount(Optional.ofNullable(cpcVo.getShowCount()).orElse(0L));
        reportSummaryVo.setClickCount(Optional.ofNullable(cpcVo.getClickCount()).orElse(0));
        reportSummaryVo.setCost(Optional.ofNullable(cpcVo.getCost()).orElse(BigDecimal.ZERO));
        reportSummaryVo.setFormSubmitCount(Optional.ofNullable(cpcVo.getFormSubmitCount()).orElse(0));
        reportSummaryVo.setAndroidDownloadCount(Optional.ofNullable(cpcVo.getAndroidDownloadCount()).orElse(0));
        reportSummaryVo.setAndroidInstallCount(Optional.ofNullable(cpcVo.getAndroidInstallCount()).orElse(0));
        reportSummaryVo.setOrderSubmitCount(Optional.ofNullable(cpcVo.getOrderAddCount()).orElse(0));
        reportSummaryVo.setOrderSubmitAmount(Optional.ofNullable(cpcVo.getOrderAddPrice()).orElse(BigDecimal.ZERO));
        reportSummaryVo.setFirstOrderPlaceCount(Optional.ofNullable(cpcVo.getFirstOrderPlaceCount()).orElse(0));
        reportSummaryVo.setFirstOrderPlaceAmount(Optional.ofNullable(cpcVo.getFirstOrderPlaceAmount()).orElse(BigDecimal.ZERO));
        // 首日付费
        reportSummaryVo.setFirstDayPayCount(Optional.ofNullable(cpcVo.getFirstDayPayCount()).orElse(0));
        reportSummaryVo.setFirstDayPayAmount(Optional.ofNullable(cpcVo.getFirstDayPayAmount()).orElse(BigDecimal.ZERO));
        reportSummaryVo.setOrderPayCount(Optional.ofNullable(cpcVo.getOrderCount()).orElse(0));
        reportSummaryVo.setOrderPayAmount(Optional.ofNullable(cpcVo.getOrderPayment()).orElse(BigDecimal.ZERO));
        reportSummaryVo.setOrderFirstPayCount(Optional.ofNullable(cpcVo.getOrderFirstCount()).orElse(0));
        reportSummaryVo.setOrderFirstPayAmount(Optional.ofNullable(cpcVo.getOrderFirstPayment()).orElse(BigDecimal.ZERO));
        reportSummaryVo.setAndroidGameCenterPaymentInAppCount(Optional.ofNullable(cpcVo.getAndroidGameCenterPaymentInAppCount()).orElse(0));
        reportSummaryVo.setAndroidGameCenterPaymentInAppAmount(Optional.ofNullable(cpcVo.getAndroidGameCenterPaymentInAppAmount()).orElse(BigDecimal.ZERO));
        reportSummaryVo.setAndroidGameCenterFirstPaymentInAppCount(Optional.ofNullable(cpcVo.getAndroidGameCenterFirstPaymentInAppCount()).orElse(0));
        reportSummaryVo.setAndroidGameCenterFirstPaymentInAppAmount(Optional.ofNullable(cpcVo.getAndroidGameCenterFirstPaymentInAppAmount()).orElse(BigDecimal.ZERO));
        reportSummaryVo.setAndroidGameActivePaidIn24hCount(Optional.ofNullable(cpcVo.getAndroidGameActivePaidIn24hCount()).orElse(0));
        reportSummaryVo.setAndroidGameActivePaidIn24hAmount(Optional.ofNullable(cpcVo.getAndroidGameActivePaidIn24hAmount()).orElse(BigDecimal.ZERO));
        reportSummaryVo.setAndroidGameActivePaidIn7dCount(Optional.ofNullable(cpcVo.getAndroidGameActivePaidIn7dCount()).orElse(0));
        reportSummaryVo.setAndroidGameActivePaidIn7dAmount(Optional.ofNullable(cpcVo.getAndroidGameActivePaidIn7dAmount()).orElse(BigDecimal.ZERO));
        reportSummaryVo.setAndroidGameActivePaidIn1dCount(Optional.ofNullable(cpcVo.getAndroidGameActivePaidIn1dCount()).orElse(0));
        reportSummaryVo.setAndroidGameActivePaidIn1dAmount(Optional.ofNullable(cpcVo.getAndroidGameActivePaidIn1dAmount()).orElse(BigDecimal.ZERO));
        reportSummaryVo.setKeyBehaviorCount(Optional.ofNullable(cpcVo.getKeyBehaviorCount()).orElse(0));
        reportSummaryVo.setRegisterCount(Optional.ofNullable(cpcVo.getRegisterCount()).orElse(0));
        reportSummaryVo.setGameActivateCount(Optional.ofNullable(cpcVo.getActivateCount()).orElse(0));
        reportSummaryVo.setGameReserveCount(Optional.ofNullable(cpcVo.getReserveCount()).orElse(0));
        reportSummaryVo.setFansIncreaseCount(Optional.ofNullable(cpcVo.getFanFollowCount()).orElse(0));
        reportSummaryVo.setAndroidActivateCount(Optional.ofNullable(cpcVo.getAndroidActivateCount()).orElse(0));
        reportSummaryVo.setIosActivateCount(Optional.ofNullable(cpcVo.getIosActivateCount()).orElse(0));
        reportSummaryVo.setValidClueCount(Optional.ofNullable(cpcVo.getValidClueCount()).orElse(0));
        reportSummaryVo.setRetentionCount(Optional.ofNullable(cpcVo.getRetentionCount()).orElse(0));
        reportSummaryVo.setAndroidGameCenterActivationCount(Optional.ofNullable(cpcVo.getAndroidGameCenterActivationCount()).orElse(0));
        reportSummaryVo.setFormPaidCount(Optional.ofNullable(cpcVo.getFormPaidCount()).orElse(0));
        reportSummaryVo.setAppCallupCount(Optional.ofNullable(cpcVo.getAppCallupCount()).orElse(0));
        reportSummaryVo.setLpCallupCount(Optional.ofNullable(cpcVo.getLpCallupCount()).orElse(0));
        reportSummaryVo.setLpCallUpSuccessCount(Optional.ofNullable(cpcVo.getLpCallUpSuccessCount()).orElse(0));
        reportSummaryVo.setLpCallUpSuccessStayCount(Optional.ofNullable(cpcVo.getLpCallUpSuccessStayCount()).orElse(0));
        reportSummaryVo.setAccountSubscribeCount(Optional.ofNullable(cpcVo.getAccountSubscribeCount()).orElse(0));
        reportSummaryVo.setPlayCount(Optional.ofNullable(cpcVo.getPlayCount()).orElse(0));
        reportSummaryVo.setPlayCost(Optional.ofNullable(cpcVo.getPlayCost()).orElse(BigDecimal.ZERO));
        reportSummaryVo.setPlayShowCount(Optional.ofNullable(cpcVo.getPlayShowCount()).orElse(0));
        reportSummaryVo.setUnderBoxLinkClickCount(Optional.ofNullable(cpcVo.getUnderBoxLinkClickCount()).orElse(0));
        reportSummaryVo.setFirstCommentCopyCount(Optional.ofNullable(cpcVo.getFirstCommentCopyCount()).orElse(0));
        reportSummaryVo.setCommentClick(Optional.ofNullable(cpcVo.getCommentClick()).orElse(0));
        reportSummaryVo.setWxCopyCount(Optional.ofNullable(cpcVo.getWxCopyCount()).orElse(0));
        reportSummaryVo.setApplyCount(Optional.ofNullable(cpcVo.getApplyCount()).orElse(0));
        reportSummaryVo.setCreditCount(Optional.ofNullable(cpcVo.getCreditCount()).orElse(0));
        reportSummaryVo.setWithdrawDepositsCount(Optional.ofNullable(cpcVo.getWithdrawDepositsCount()).orElse(0));
        reportSummaryVo.setVideoLikeCount(Optional.ofNullable(cpcVo.getVideoLikeCount()).orElse(0));
        reportSummaryVo.setWxAddFansCount(Optional.ofNullable(cpcVo.getWxAddFansCount()).orElse(0));
        reportSummaryVo.setPaidIn24hCount(Optional.ofNullable(cpcVo.getPaidIn24hCount()).orElse(0));
        reportSummaryVo.setPaidIn24hPrice(Optional.ofNullable(cpcVo.getPaidIn24hPrice()).orElse(BigDecimal.ZERO));
        reportSummaryVo.setPaidIn7dCount(Optional.ofNullable(cpcVo.getPaidIn7dCount()).orElse(0));
        reportSummaryVo.setPaidIn7dPrice(Optional.ofNullable(cpcVo.getPaidIn7dPrice()).orElse(BigDecimal.ZERO));
        reportSummaryVo.setWxAddFansCount(Optional.ofNullable(cpcVo.getWxAddFansCount()).orElse(0));
        reportSummaryVo.setCommentShowCount(Optional.ofNullable(cpcVo.getCommentShowCount()).orElse(0));
        reportSummaryVo.setCommentCallUpCount(Optional.ofNullable(cpcVo.getCommentCallUpCount()).orElse(0));
        reportSummaryVo.setLiveEntryCount(Optional.ofNullable(cpcVo.getLiveEntryCount()).orElse(0));
        reportSummaryVo.setLiveCallUpCount(Optional.ofNullable(cpcVo.getLiveCallUpCount()).orElse(0));
        reportSummaryVo.setLiveReserveCount(Optional.ofNullable(cpcVo.getLiveReserveCount()).orElse(0));
        reportSummaryVo.setNewFirstDayPayCount(Optional.ofNullable(cpcVo.getNewFirstDayPayCount()).orElse(0));
        reportSummaryVo.setNewFirstDayPayAmount(Optional.ofNullable(cpcVo.getNewFirstDayPayAmount()).orElse(BigDecimal.ZERO));
        reportSummaryVo.setFirstWithdrawCount(Optional.ofNullable(cpcVo.getFirstWithdrawCount()).orElse(0));
        reportSummaryVo.setComponentClickCount(Optional.ofNullable(cpcVo.getComponentClickCount()).orElse(0));
        reportSummaryVo.setLiveGameCardShowCount(Optional.ofNullable(cpcVo.getLiveGameCardShowCount()).orElse(0));
        reportSummaryVo.setLiveGameCardClickCount(Optional.ofNullable(cpcVo.getLiveGameCardClickCount()).orElse(0));
        reportSummaryVo.setLiveBottomIconShow(Optional.ofNullable(cpcVo.getLiveBottomIconShow()).orElse(0));
        reportSummaryVo.setLiveBottomIconClick(Optional.ofNullable(cpcVo.getLiveBottomIconClick()).orElse(0));
        reportSummaryVo.setLiveNativeCardShow(Optional.ofNullable(cpcVo.getLiveNativeCardShow()).orElse(0));
        reportSummaryVo.setLiveNativeCardClick(Optional.ofNullable(cpcVo.getLiveNativeCardClick()).orElse(0));
        return reportSummaryVo;
    }

    public static BigDecimal milliFen2Yuan(Float milliFen) {
        return BigDecimal.valueOf(milliFen.longValue()).divide(BigDecimal.valueOf(100000), 2, RoundingMode.HALF_UP);
    }

    public static BigDecimal milliFen2Yuan(Long milliFen) {
        return BigDecimal.valueOf(milliFen).divide(BigDecimal.valueOf(100000), RoundingMode.HALF_EVEN);
    }

    public static ReportSummaryVo fromMaterialReportBo(DwsFlowProgrammaticCreativeStatL1hr1dBo bo) {
        if (Objects.isNull(bo)) {
            return emptyInstance();
        }

        ReportSummaryVo summaryVo = new ReportSummaryVo();
        summaryVo.setShowCount(bo.getPv());
        summaryVo.setClickCount(bo.getClick().intValue());
        summaryVo.setCost(milliFen2Yuan(bo.getCost()));
        summaryVo.setOrderSubmitCount(bo.getOrderPlaceCount().intValue());
        summaryVo.setOrderSubmitAmount(milliFen2Yuan(bo.getOrderPlace()));
        summaryVo.setFirstOrderPlaceCount(bo.getFirstOrderPlace().intValue());
        summaryVo.setFirstOrderPlaceAmount(BigDecimal.ZERO);
        summaryVo.setOrderPayCount(bo.getOrderPayCount().intValue());
        summaryVo.setOrderPayAmount(milliFen2Yuan(bo.getOrderPay()));
        summaryVo.setOrderFirstPayCount(0);
        summaryVo.setOrderFirstPayAmount(BigDecimal.ZERO);
        summaryVo.setFirstDayPayCount(0);
        summaryVo.setFirstDayPayAmount(BigDecimal.ZERO);
        summaryVo.setAndroidGameCenterPaymentInAppCount(bo.getUserCost().intValue());
        summaryVo.setAndroidGameCenterFirstPaymentInAppAmount(BigDecimal.ZERO);
        summaryVo.setAndroidGameCenterFirstPaymentInAppCount(0);
        summaryVo.setAndroidGameActivePaidIn24hAmount(BigDecimal.ZERO);
        summaryVo.setAndroidGameActivePaidIn24hCount(0);
        summaryVo.setAndroidGameActivePaidIn7dAmount(BigDecimal.ZERO);
        summaryVo.setAndroidGameActivePaidIn7dCount(0);
        summaryVo.setAndroidGameActivePaidIn1dAmount(BigDecimal.ZERO);
        summaryVo.setAndroidGameActivePaidIn1dCount(0);
        summaryVo.setKeyBehaviorCount(bo.getActionValid().intValue());
        summaryVo.setRegisterCount(bo.getUserRegister().intValue());
        summaryVo.setGameActivateCount(0);
        summaryVo.setGameReserveCount(bo.getGameSubscribe().intValue());
        summaryVo.setFansIncreaseCount(bo.getFanIncrease().intValue());
        summaryVo.setAndroidActivateCount(bo.getAndroidFirstActive().intValue());
        summaryVo.setIosActivateCount(bo.getIosFirstActive().intValue());
        summaryVo.setFormSubmitCount(bo.getFormSubmit().intValue());
        summaryVo.setAndroidDownloadCount(bo.getDownloadSuccess().intValue());
        summaryVo.setAndroidInstallCount(bo.getInstallSuccess().intValue());
        summaryVo.setValidClueCount(bo.getClueValid().intValue());
        summaryVo.setRetentionCount(bo.getRetention().intValue());
        summaryVo.setAppCallupCount(bo.getAppCallup().intValue());
        summaryVo.setLpCallupCount(bo.getLpCallup().intValue());
        summaryVo.setLpCallUpSuccessCount(bo.getLpCallupSucc().intValue());
        summaryVo.setAndroidGameCenterActivationCount(bo.getGameActiveApi().intValue());
        summaryVo.setFormPaidCount(bo.getFormUserCost().intValue());
        summaryVo.setLpCallUpSuccessStayCount(bo.getLpCallupSuccStay().intValue());
        summaryVo.setAccountSubscribeCount(bo.getAccountSubscribe().intValue() + bo.getEnterpriseAccountSubscribe().intValue());
        summaryVo.setPlayCount(bo.getVideoPlay().intValue());
        summaryVo.setPlayCost(BigDecimal.ZERO);
        summaryVo.setPlayShowCount(bo.getPv().intValue());
        summaryVo.setUnderBoxLinkClickCount(0);
        summaryVo.setFirstCommentCopyCount(bo.getFirstCommentCopy().intValue());
        summaryVo.setCommentClick(bo.getCommentClick().intValue());
        summaryVo.setWxCopyCount(bo.getWxCopy().intValue());
        summaryVo.setApplyCount(bo.getApply().intValue());
        summaryVo.setCreditCount(bo.getCredit().intValue());
        summaryVo.setWithdrawDepositsCount(bo.getWithdrawDeposits().intValue());
        summaryVo.setVideoLikeCount(0);
        summaryVo.setWxAddFansCount(bo.getWxAddFans().intValue());
        summaryVo.setPaidIn24hCount(0);
        summaryVo.setPaidIn24hPrice(BigDecimal.ZERO);
        summaryVo.setPaidIn7dCount(0);
        summaryVo.setPaidIn7dPrice(BigDecimal.ZERO);
        summaryVo.setCommentShowCount(0);
        summaryVo.setCommentCallUpCount(0);
        summaryVo.setLiveEntryCount(0);
        summaryVo.setLiveCallUpCount(0);
        summaryVo.setNewFirstDayPayCount(0);
        summaryVo.setNewFirstDayPayAmount(BigDecimal.ZERO);
        summaryVo.setLiveReserveCount(0);
        summaryVo.setFirstWithdrawCount(0);
        summaryVo.setComponentClickCount(0);
        summaryVo.setLiveGameCardShowCount(0);
        summaryVo.setLiveGameCardClickCount(0);
        summaryVo.setLiveBottomIconShow(0);
        summaryVo.setLiveBottomIconClick(0);
        summaryVo.setLiveNativeCardShow(0);
        summaryVo.setLiveNativeCardClick(0);
        summaryVo.update();
        return summaryVo;
    }

    public static ReportSummaryVo accumulate(Collection<ReportSummaryVo> vos) {
        if (CollectionUtils.isEmpty(vos)) {
            return emptyInstance();
        }
        final ReportSummaryVo accVo = emptyInstance();
        vos.forEach(x -> merge(accVo, x));
        return accVo.update();
    }

    public static ReportSummaryVo merge(ReportSummaryVo vo1, ReportSummaryVo vo2) {
        Assert.isTrue(Objects.nonNull(vo1) && Objects.nonNull(vo2), "合并的报表不能为空");
        vo1.setShowCount(vo1.getShowCount() + vo2.getShowCount());
        vo1.setClickCount(vo1.getClickCount() + vo2.getClickCount());
        vo1.setCost(CommonFuncs.bigDecimalSupplierAdd(vo1::getCost, vo2::getCost));
        vo1.setOrderSubmitCount(vo1.getOrderSubmitCount() + vo2.getOrderSubmitCount());
        vo1.setOrderSubmitAmount(CommonFuncs.bigDecimalSupplierAdd(vo1::getOrderSubmitAmount, vo2::getOrderSubmitAmount));
        vo1.setFirstOrderPlaceCount(vo1.getFirstOrderPlaceCount() + vo2.getFirstOrderPlaceCount());
        vo1.setFirstOrderPlaceAmount(CommonFuncs.bigDecimalSupplierAdd(vo1::getFirstOrderPlaceAmount, vo2::getFirstOrderPlaceAmount));
        vo1.setOrderPayCount(vo1.getOrderPayCount() + vo2.getOrderPayCount());
        vo1.setOrderPayAmount(CommonFuncs.bigDecimalSupplierAdd(vo1::getOrderPayAmount, vo2::getOrderPayAmount));
        vo1.setOrderFirstPayCount(vo1.getOrderFirstPayCount() + vo2.getOrderFirstPayCount());
        vo1.setOrderFirstPayAmount(CommonFuncs.bigDecimalSupplierAdd(vo1::getOrderFirstPayAmount, vo2::getOrderFirstPayAmount));
        vo1.setFirstDayPayCount(vo1.getFirstDayPayCount() + vo2.getFirstDayPayCount());
        vo1.setFirstDayPayAmount(CommonFuncs.bigDecimalSupplierAdd(vo1::getFirstDayPayAmount, vo2::getFirstDayPayAmount));
        vo1.setAndroidGameCenterPaymentInAppCount(vo1.getAndroidGameCenterPaymentInAppCount() + vo2.getAndroidGameCenterPaymentInAppCount());
        vo1.setAndroidGameCenterPaymentInAppAmount(CommonFuncs.bigDecimalSupplierAdd(vo1::getAndroidGameCenterPaymentInAppAmount, vo2::getAndroidGameCenterPaymentInAppAmount));
        vo1.setAndroidGameCenterFirstPaymentInAppCount(vo1.getAndroidGameCenterFirstPaymentInAppCount() + vo2.getAndroidGameCenterFirstPaymentInAppCount());
        vo1.setAndroidGameCenterFirstPaymentInAppAmount(CommonFuncs.bigDecimalSupplierAdd(vo1::getAndroidGameCenterFirstPaymentInAppAmount, vo2::getAndroidGameCenterFirstPaymentInAppAmount));

        vo1.setAndroidGameActivePaidIn24hCount(vo1.getAndroidGameActivePaidIn24hCount() + vo2.getAndroidGameActivePaidIn24hCount());
        vo1.setAndroidGameActivePaidIn24hAmount(CommonFuncs.bigDecimalSupplierAdd(vo1::getAndroidGameActivePaidIn24hAmount, vo2::getAndroidGameActivePaidIn24hAmount));
        vo1.setAndroidGameActivePaidIn7dCount(vo1.getAndroidGameActivePaidIn7dCount() + vo2.getAndroidGameActivePaidIn7dCount());
        vo1.setAndroidGameActivePaidIn7dAmount(CommonFuncs.bigDecimalSupplierAdd(vo1::getAndroidGameActivePaidIn7dAmount, vo2::getAndroidGameActivePaidIn7dAmount));
        vo1.setAndroidGameActivePaidIn1dCount(vo1.getAndroidGameActivePaidIn1dCount() + vo2.getAndroidGameActivePaidIn1dCount());
        vo1.setAndroidGameActivePaidIn1dAmount(CommonFuncs.bigDecimalSupplierAdd(vo1::getAndroidGameActivePaidIn1dAmount, vo2::getAndroidGameActivePaidIn1dAmount));

        vo1.setKeyBehaviorCount(vo1.getKeyBehaviorCount() + vo2.getKeyBehaviorCount());
        vo1.setRegisterCount(vo1.getRegisterCount() + vo2.getRegisterCount());
        vo1.setGameActivateCount(vo1.getGameActivateCount() + vo2.getGameActivateCount());
        vo1.setGameReserveCount(vo1.getGameReserveCount() + vo2.getGameReserveCount());
        vo1.setFansIncreaseCount(vo1.getFansIncreaseCount() + vo2.getFansIncreaseCount());
        vo1.setAndroidActivateCount(vo1.getAndroidActivateCount() + vo2.getAndroidActivateCount());
        vo1.setIosActivateCount(vo1.getIosActivateCount() + vo2.getIosActivateCount());
        vo1.setFormSubmitCount(vo1.getFormSubmitCount() + vo2.getFormSubmitCount());
        vo1.setAndroidDownloadCount(vo1.getAndroidDownloadCount() + vo2.getAndroidDownloadCount());
        vo1.setAndroidInstallCount(vo1.getAndroidInstallCount() + vo2.getAndroidInstallCount());
        vo1.setValidClueCount(vo1.getValidClueCount() + vo2.getValidClueCount());
        vo1.setRetentionCount(vo1.getRetentionCount() + vo2.getRetentionCount());
        vo1.setAppCallupCount(vo1.getAppCallupCount() + vo2.getAppCallupCount());
        vo1.setLpCallupCount(vo1.getLpCallupCount() + vo2.getLpCallupCount());
        vo1.setLpCallUpSuccessCount(vo1.getLpCallUpSuccessCount() + vo2.getLpCallUpSuccessCount());
        vo1.setAndroidGameCenterActivationCount(vo1.getAndroidGameCenterActivationCount() + vo2.getAndroidGameCenterActivationCount());
        vo1.setFormPaidCount(vo1.getFormPaidCount() + vo2.getFormPaidCount());
        vo1.setLpCallUpSuccessStayCount(vo1.getLpCallUpSuccessStayCount() + vo2.getLpCallUpSuccessStayCount());
        vo1.setAccountSubscribeCount(vo1.getAccountSubscribeCount() + vo2.getAccountSubscribeCount());
        vo1.setPlayCount(vo1.getPlayCount() + vo2.getPlayCount());
        vo1.setPlayCost(CommonFuncs.bigDecimalSupplierAdd(vo1::getPlayCost, vo2::getPlayCost));
        vo1.setPlayShowCount(vo1.getPlayShowCount() + vo2.getPlayShowCount());
        vo1.setUnderBoxLinkClickCount(vo1.getUnderBoxLinkClickCount() + vo2.getUnderBoxLinkClickCount());
        vo1.setFirstCommentCopyCount(vo1.getFirstCommentCopyCount() + vo2.getFirstCommentCopyCount());
        vo1.setCommentClick(vo1.getCommentClick() + vo2.getCommentClick());
        vo1.setWxCopyCount(vo1.getWxCopyCount() + vo2.getWxCopyCount());
        vo1.setApplyCount(vo1.getApplyCount() + vo2.getApplyCount());
        vo1.setCreditCount(vo1.getCreditCount() + vo2.getCreditCount());
        vo1.setWithdrawDepositsCount(vo1.getWithdrawDepositsCount() + vo2.getWithdrawDepositsCount());
        vo1.setVideoLikeCount(vo1.getVideoLikeCount() + vo2.getVideoLikeCount());
        vo1.setWxAddFansCount(vo1.getWxAddFansCount() + vo2.getWxAddFansCount());
        vo1.setPaidIn24hCount(vo1.getPaidIn24hCount() + vo2.getPaidIn24hCount());
        vo1.setPaidIn24hPrice(CommonFuncs.bigDecimalSupplierAdd(vo1::getPaidIn24hPrice, vo2::getPaidIn24hPrice));
        vo1.setPaidIn7dCount(vo1.getPaidIn7dCount() + vo2.getPaidIn7dCount());
        vo1.setPaidIn7dPrice(CommonFuncs.bigDecimalSupplierAdd(vo1::getPaidIn7dPrice, vo2::getPaidIn7dPrice));
        vo1.setCommentShowCount(vo1.getCommentShowCount() + vo2.getCommentShowCount());
        vo1.setCommentCallUpCount(vo1.getCommentCallUpCount() + vo2.getCommentCallUpCount());
        vo1.setLiveEntryCount(vo1.getLiveEntryCount() + vo2.getLiveEntryCount());
        vo1.setLiveCallUpCount(vo1.getLiveCallUpCount() + vo2.getLiveCallUpCount());
        vo1.setLiveReserveCount(vo1.getLiveReserveCount() + vo2.getLiveReserveCount());
        vo1.setFirstWithdrawCount(vo1.getFirstWithdrawCount() + vo2.getFirstWithdrawCount());
        vo1.setComponentClickCount(vo1.getComponentClickCount() + vo2.getComponentClickCount());
        vo1.setLiveGameCardShowCount(vo1.getLiveGameCardShowCount() + vo2.getLiveGameCardShowCount());
        vo1.setLiveGameCardClickCount(vo1.getLiveGameCardClickCount() + vo2.getLiveGameCardClickCount());
        vo1.setLiveBottomIconShow(vo1.getLiveBottomIconShow() + vo2.getLiveBottomIconShow());
        vo1.setLiveBottomIconClick(vo1.getLiveBottomIconClick() + vo2.getLiveBottomIconClick());
        vo1.setLiveNativeCardShow(vo1.getLiveNativeCardShow() + vo2.getLiveNativeCardShow());
        vo1.setLiveNativeCardClick(vo1.getLiveNativeCardClick() + vo2.getLiveNativeCardClick());
        vo1.update();
        return vo1;
    }

    //public static final ReportSummaryVo EMPTY_INSTANCE = emptyInstance();

    public static final ReportSummaryVo emptyInstance() {

        ReportSummaryVo summaryVo = new ReportSummaryVo();
        summaryVo.setShowCount(0L);
        summaryVo.setClickCount(0);
        summaryVo.setCost(BigDecimal.ZERO);
        summaryVo.setFormSubmitCount(0);
        summaryVo.setAndroidDownloadCount(0);
        summaryVo.setAndroidInstallCount(0);
        summaryVo.setOrderSubmitCount(0);
        summaryVo.setOrderSubmitCost(BigDecimal.ZERO);
        summaryVo.setFirstOrderPlaceCount(0);
        summaryVo.setFirstOrderPlaceCost(BigDecimal.ZERO);
        summaryVo.setOrderPayCount(0);
        summaryVo.setOrderPayAmount(BigDecimal.ZERO);
        summaryVo.setOrderFirstPayCount(0);
        summaryVo.setOrderFirstPayAmount(BigDecimal.ZERO);
        summaryVo.setFirstDayPayCount(0);
        summaryVo.setFirstDayPayAmount(BigDecimal.ZERO);
        summaryVo.setAndroidGameCenterPaymentInAppCount(0);
        summaryVo.setAndroidGameCenterPaymentInAppAmount(BigDecimal.ZERO);
        summaryVo.setAndroidGameCenterFirstPaymentInAppCount(0);
        summaryVo.setAndroidGameCenterFirstPaymentInAppAmount(BigDecimal.ZERO);
        summaryVo.setAndroidGameActivePaidIn24hCount(0);
        summaryVo.setAndroidGameActivePaidIn24hAmount(BigDecimal.ZERO);
        summaryVo.setAndroidGameActivePaidIn7dCount(0);
        summaryVo.setAndroidGameActivePaidIn7dAmount(BigDecimal.ZERO);
        summaryVo.setAndroidGameActivePaidIn1dCount(0);
        summaryVo.setAndroidGameActivePaidIn1dAmount(BigDecimal.ZERO);
        summaryVo.setKeyBehaviorCount(0);
        summaryVo.setRegisterCount(0);
        summaryVo.setGameActivateCount(0);
        summaryVo.setGameReserveCount(0);
        summaryVo.setFansIncreaseCount(0);
        summaryVo.setAndroidActivateCount(0);
        summaryVo.setIosActivateCount(0);
        summaryVo.setValidClueCount(0);
        summaryVo.setRetentionCount(0);
        summaryVo.setAndroidGameCenterActivationCount(0);
        summaryVo.setFormPaidCount(0);
        summaryVo.setAppCallupCount(0);
        summaryVo.setLpCallupCount(0);
        summaryVo.setLpCallUpSuccessCount(0);
        summaryVo.setLpCallUpSuccessStayCount(0);
        summaryVo.setAccountSubscribeCount(0);
        //summaryVo.setDynamicDetailPageBrowseCount(0);
        summaryVo.setPlayCount(0);
        summaryVo.setPlayCost(BigDecimal.ZERO);
        summaryVo.setPlayShowCount(0);
        summaryVo.setUnderBoxLinkClickCount(0);
        summaryVo.setFirstCommentCopyCount(0);
        summaryVo.setCommentClick(0);
        summaryVo.setWxCopyCount(0);
        summaryVo.setApplyCount(0);
        summaryVo.setCreditCount(0);
        summaryVo.setWithdrawDepositsCount(0);
        summaryVo.setVideoLikeCount(0);
        summaryVo.setWxAddFansCount(0);
        summaryVo.setPaidIn24hCount(0);
        summaryVo.setPaidIn24hPrice(BigDecimal.ZERO);
        summaryVo.setPaidIn7dCount(0);
        summaryVo.setPaidIn7dPrice(BigDecimal.ZERO);
        summaryVo.setWxAddFansCount(0);
        summaryVo.setCommentShowCount(0);
        summaryVo.setCommentCallUpCount(0);
        summaryVo.setFirstDayPayCount(0);
        summaryVo.setFirstDayPayAmount(BigDecimal.ZERO);
        summaryVo.setLiveEntryCount(0);
        summaryVo.setLiveCallUpCount(0);
        summaryVo.setLiveReserveCount(0);
        summaryVo.setUnderBoxLinkClickCount(0);
        summaryVo.setFirstWithdrawCount(0);
        summaryVo.setNewFirstDayPayCount(0);
        summaryVo.setNewFirstDayPayAmount(BigDecimal.ZERO);
        summaryVo.setComponentClickCount(0);
        summaryVo.setLiveGameCardShowCount(0);
        summaryVo.setLiveGameCardClickCount(0);
        summaryVo.setLiveBottomIconShow(0);
        summaryVo.setLiveBottomIconClick(0);
        summaryVo.setLiveNativeCardShow(0);
        summaryVo.setLiveNativeCardClick(0);
        summaryVo.update();
        return summaryVo;
    }

    /**
     * 此处计算了复合计算的指标，全局排序的话只能在该方法之后
     *
     * @return
     */
    public ReportSummaryVo update() {
        activateCount = iosActivateCount + androidActivateCount;
        if (showCount > 0) {
            averageCostPerThousand = CommonFuncs.divideAndReturnBigDecimal(cost.multiply(BigDecimal.valueOf(1000)), showCount);
            clickRate = CommonFuncs.divideAndReturnBigDecimalPercent(clickCount, showCount);
            videoLikeRate = CommonFuncs.divideAndReturnBigDecimalPercent(videoLikeCount, showCount);
            // 【直播间进人率】 = 【直播间进人数】/ 【点击量】
            liveEntryRate = CommonFuncs.divideAndReturnBigDecimalPercent(liveEntryCount, showCount);
        }
        if (clickCount > 0) {
            costPerClick = CommonFuncs.divideAndReturnBigDecimal(cost, clickCount);
            // todo
            goodsConversionRate = CommonFuncs.divideAndReturnBigDecimalPercent(orderSubmitCount, clickCount);
            fansIncreaseRate = CommonFuncs.divideAndReturnBigDecimalPercent(fansIncreaseCount, clickCount);
            appActivateRate = CommonFuncs.divideAndReturnBigDecimalPercent(activateCount, clickCount);
            gameActivateRate = CommonFuncs.divideAndReturnBigDecimalPercent(gameActivateCount, clickCount);
            gameReserveRate = CommonFuncs.divideAndReturnBigDecimalPercent(gameReserveCount, clickCount);
            registerRate = CommonFuncs.divideAndReturnBigDecimalPercent(registerCount, clickCount);
            formSubmitRate = CommonFuncs.divideAndReturnBigDecimalPercent(formSubmitCount, clickCount);
            androidDownloadRate = CommonFuncs.divideAndReturnBigDecimalPercent(androidDownloadCount, clickCount);
            androidInstallRate = CommonFuncs.divideAndReturnBigDecimalPercent(androidInstallCount, clickCount);

            lpCallupRate = CommonFuncs.divideAndReturnBigDecimalPercent(lpCallupCount, clickCount);
            appCallupRate = CommonFuncs.divideAndReturnBigDecimalPercent(appCallupCount, clickCount);
            // todo
            orderSubmitRate = CommonFuncs.divideAndReturnBigDecimalPercent(orderSubmitCount, clickCount);
            firstOrderPlaceRate = CommonFuncs.divideAndReturnBigDecimalPercent(firstOrderPlaceCount, clickCount);
            androidGameCenterActivationRate = CommonFuncs.divideAndReturnBigDecimalPercent(androidGameCenterActivationCount, clickCount);
            formPaidRate = CommonFuncs.divideAndReturnBigDecimalPercent(formPaidCount, clickCount);
            lpCallUpSuccessRate = CommonFuncs.divideAndReturnBigDecimalPercent(lpCallUpSuccessCount, clickCount);
            lpCallUpSuccessStayRate = CommonFuncs.divideAndReturnBigDecimalPercent(lpCallUpSuccessStayCount, clickCount);
            accountSubscribeRate = CommonFuncs.divideAndReturnBigDecimalPercent(accountSubscribeCount, clickCount);
            wxCopyRate = CommonFuncs.divideAndReturnBigDecimalPercent(wxCopyCount, clickCount);
            // 【关键行为率】=【关键行为数】/【点击】
            keyBehaviorRate = CommonFuncs.divideAndReturnBigDecimalPercent(keyBehaviorCount, clickCount);
            //【完件率】=【完件数】/ 点击量
            applyRate = CommonFuncs.divideAndReturnBigDecimalPercent(applyCount, clickCount);
            //【授信率】=【授信数】/ 点击量
            creditRate = CommonFuncs.divideAndReturnBigDecimalPercent(creditCount, clickCount);
            //【放款率】=【放款数】/ 点击量
            withdrawDepositsRate = CommonFuncs.divideAndReturnBigDecimalPercent(withdrawDepositsCount, clickCount);
            // 【微信加粉率】=【微信加粉数】/点击量
            wxAddFansRate = CommonFuncs.divideAndReturnBigDecimalPercent(wxAddFansCount, clickCount);
            liveReserveRate = CommonFuncs.divideAndReturnBigDecimalPercent(liveReserveCount, clickCount);
            // 【首日放款率】（首日放款数/点击量）
            firstWithdrawRate = CommonFuncs.divideAndReturnBigDecimalPercent(firstWithdrawCount, clickCount);
        }
        if (cost != null && cost.compareTo(BigDecimal.ZERO) > 0) {
            goodsRoi = CommonFuncs.divideAndReturnBigDecimal(orderSubmitAmount, cost);
            paidIn24hROI = CommonFuncs.divideAndReturnBigDecimalScale4(paidIn24hPrice, cost);
            paidIn7dROI = CommonFuncs.divideAndReturnBigDecimalScale4(paidIn7dPrice, cost);
            firstDayPayROI = CommonFuncs.divideAndReturnBigDecimalScale4(firstDayPayAmount, cost);
            newFirstDayPayRoi = CommonFuncs.divideAndReturnBigDecimalScale4(newFirstDayPayAmount, cost);
            androidGameActivePaidIn24hRoi = CommonFuncs.divideAndReturnBigDecimalScale4(androidGameActivePaidIn24hAmount, cost);
            androidGameActivePaidIn7dRoi = CommonFuncs.divideAndReturnBigDecimalScale4(androidGameActivePaidIn7dAmount, cost);
            androidGameActivePaidIn1dRoi = CommonFuncs.divideAndReturnBigDecimalScale4(androidGameActivePaidIn1dAmount, cost);
        }
        if (commentClick != null && commentClick > 0) {
            commentClickCost = CommonFuncs.divideAndReturnBigDecimal(cost, commentClick);
            commentCallUpRate = CommonFuncs.divideAndReturnBigDecimalPercent(commentCallUpCount, commentClick);
        }

        if (retentionCount > 0) {
            retentionCost = CommonFuncs.divideAndReturnBigDecimal(cost, retentionCount);
        }
        if (orderFirstPayCount > 0) {
            //应用内首次付费消耗计算
            orderFirstPayCost = CommonFuncs.divideAndReturnBigDecimal(cost, orderFirstPayCount);
        }
        if (firstDayPayCount > 0) {
            //应用内首次付费消耗计算
            firstDayPayCost = CommonFuncs.divideAndReturnBigDecimal(cost, firstDayPayCount);
        }
        if (newFirstDayPayCount > 0) {
            //应用内首次付费消耗计算
            newFirstDayPayCost = CommonFuncs.divideAndReturnBigDecimal(cost, newFirstDayPayCount);
        }
        Integer shallowConvEventCount = formSubmitCount + activateCount;
        if (shallowConvEventCount > 0) {
            // 【首日付费率】=【首日付费数】/ 浅层转化事件数
            newFirstDayPayRate = CommonFuncs.divideAndReturnBigDecimalScale4(new BigDecimal(newFirstDayPayCount), new BigDecimal(shallowConvEventCount));
        }

        if (fansIncreaseCount > 0) {
            fansIncreaseAverageCost = CommonFuncs.divideAndReturnBigDecimal(cost, fansIncreaseCount);
        }
        if (activateCount > 0) {
            appActivateAverageCost = CommonFuncs.divideAndReturnBigDecimal(cost, activateCount);
            //【次日留存率】=【次日留存数】/ 点击量 【应用激活数】
            retentionRate = CommonFuncs.divideAndReturnBigDecimalPercent(retentionCount, activateCount);
            //【应用内首次付费率】=【应用内付费数】/ 点击量 【应用激活数】
            orderFirstPayRate = CommonFuncs.divideAndReturnBigDecimalPercent(orderFirstPayCount, activateCount);
            // 【首日付费率】=【首日付费数】/ 点击量
            firstDayPayRate = CommonFuncs.divideAndReturnBigDecimalPercent(firstDayPayCount, activateCount);
        }

        if (keyBehaviorCount > 0) {
            keyBehaviorCost = CommonFuncs.divideAndReturnBigDecimal(cost, keyBehaviorCount);
        }
        if (gameActivateCount > 0) {
            gameActivateAverageCost = CommonFuncs.divideAndReturnBigDecimal(cost, gameActivateCount);
        }
        if (gameReserveCount > 0) {
            gameReserveAverageCost = CommonFuncs.divideAndReturnBigDecimal(cost, gameReserveCount);
        }
        if (registerCount > 0) {
            registerAverageCost = CommonFuncs.divideAndReturnBigDecimal(cost, registerCount);
        }
        if (formSubmitCount > 0) {
            formSubmitAverageCost = CommonFuncs.divideAndReturnBigDecimal(cost, formSubmitCount);
            validClueRate = CommonFuncs.divideAndReturnBigDecimalPercent(validClueCount, formSubmitCount);
        }
        if (androidDownloadCount > 0) {
            androidDownloadAverageCost = CommonFuncs.divideAndReturnBigDecimal(cost, androidDownloadCount);
        }
        if (androidInstallCount > 0) {
            androidInstallAverageCost = CommonFuncs.divideAndReturnBigDecimal(cost, androidInstallCount);
        }
        if (validClueCount > 0) {
            validClueAverageCost = CommonFuncs.divideAndReturnBigDecimal(cost, validClueCount);
        }
        if (lpCallupCount > 0) {
            lpCallupCost = CommonFuncs.divideAndReturnBigDecimal(cost, lpCallupCount);
        }
        if (appCallupCount > 0) {
            appCallupCost = CommonFuncs.divideAndReturnBigDecimal(cost, appCallupCount);
        }
        if (firstOrderPlaceCount > 0) {
            firstOrderPlaceCost = CommonFuncs.divideAndReturnBigDecimal(cost, firstOrderPlaceCount);
        }
        if (orderSubmitCount > 0) {
            orderSubmitCost = CommonFuncs.divideAndReturnBigDecimal(cost, orderSubmitCount);
        }
        if (androidGameCenterFirstPaymentInAppCount > 0) {
            //【安卓游戏中心应用内首次付费成本】=总花费/【安卓游戏中心应用内首次付费数】
            androidGameCenterFirstPaymentInAppCost = CommonFuncs.divideAndReturnBigDecimal(cost, androidGameCenterFirstPaymentInAppCount);
        }
        if (androidGameCenterActivationCount > 0) {
            androidGameCenterActivationCost = CommonFuncs.divideAndReturnBigDecimal(cost, androidGameCenterActivationCount);
            //【安卓游戏中心应用内首次付费率】=【安卓游戏中心应用内首次付费数】/【安卓游戏中心应用激活数】
            androidGameCenterFirstPaymentInAppRate = CommonFuncs.divideAndReturnBigDecimalPercent(androidGameCenterFirstPaymentInAppCount, androidGameCenterActivationCount);
            androidGameActivePaidIn1dRate = CommonFuncs.divideAndReturnBigDecimalPercent(androidGameActivePaidIn1dCount, androidGameCenterActivationCount);
        }
        if (formPaidCount > 0) {
            formPaidCost = CommonFuncs.divideAndReturnBigDecimal(cost, formPaidCount);
        }
        if (lpCallUpSuccessCount > 0) {
            lpCallUpSuccessCost = CommonFuncs.divideAndReturnBigDecimal(cost, lpCallUpSuccessCount);
            callUpOrderSuccessRate = CommonFuncs.divideAndReturnBigDecimalPercent(orderSubmitCount, lpCallUpSuccessCount);
        }
        if (lpCallUpSuccessStayCount > 0) {
            lpCallUpSuccessStayCost = CommonFuncs.divideAndReturnBigDecimal(cost, lpCallUpSuccessStayCount);
        }
        if (accountSubscribeCount > 0) {
            accountSubscribeCost = CommonFuncs.divideAndReturnBigDecimal(cost, accountSubscribeCount);
        }
        if (playCount > 0) {
            //【播放成本】=总花费/【播放量】
            costPerPlayCount = CommonFuncs.divideAndReturnBigDecimal(cost, playCount);
            play2FansRate = CommonFuncs.divideAndReturnBigDecimalPercent(fansIncreaseCount, playCount);
            underBoxLinkClickRate = CommonFuncs.divideAndReturnBigDecimalPercent(underBoxLinkClickCount, playCount);
            firstCommentCopyRate = CommonFuncs.divideAndReturnBigDecimalPercent(firstCommentCopyCount, playCount);
            commentClickRate = CommonFuncs.divideAndReturnBigDecimalPercent(commentClick, playCount);
            commentShowRate = CommonFuncs.divideAndReturnBigDecimalPercent(commentShowCount, playCount);
            playCallUpRate = CommonFuncs.divideAndReturnBigDecimalPercent(lpCallUpSuccessCount, playCount);
            componentClickRate = CommonFuncs.divideAndReturnBigDecimalPercent(componentClickCount, playCount);
        }
        if (underBoxLinkClickCount > 0) {
            costPerUnderBoxLinkClickCount = CommonFuncs.divideAndReturnBigDecimal(cost, underBoxLinkClickCount);
        }
        if (firstCommentCopyCount > 0) {
            costPerFirstCommentCopyCount = CommonFuncs.divideAndReturnBigDecimal(cost, firstCommentCopyCount);
        }
        if (playShowCount > 0) {
            //【播放率】=【播放量】/曝光量??
            playRate = CommonFuncs.divideAndReturnBigDecimalPercent(playCount, playShowCount);
        }
        if (wxCopyCount > 0) {
            wxCopyCost = CommonFuncs.divideAndReturnBigDecimal(cost, wxCopyCount);
        }
        if (applyCount > 0) {
            applyCost = CommonFuncs.divideAndReturnBigDecimal(cost, applyCount);
        }
        if (creditCount > 0) {
            creditCost = CommonFuncs.divideAndReturnBigDecimal(cost, creditCount);
        }
        if (withdrawDepositsCount > 0) {
            withdrawDepositsCost = CommonFuncs.divideAndReturnBigDecimal(cost, withdrawDepositsCount);
        }
        if (videoLikeCount > 0) {
            costPerVideoLike = CommonFuncs.divideAndReturnBigDecimal(cost, videoLikeCount);
        }
        if (wxAddFansCount > 0) {
            wxAddFansCost = CommonFuncs.divideAndReturnBigDecimal(cost, wxAddFansCount);
        }
        if (paidIn24hCount > 0) {
            paidIn24hCost = CommonFuncs.divideAndReturnBigDecimal(cost, paidIn24hCount);
        }
        if (paidIn7dCount > 0) {
            paidIn7dCost = CommonFuncs.divideAndReturnBigDecimal(cost, paidIn7dCount);
        }
        if (commentShowCount > 0) {
            commentShowClickRate = CommonFuncs.divideAndReturnBigDecimalPercent(commentClick, commentShowCount);
        }
        if (liveEntryCount > 0) {
            liveEntryCost = CommonFuncs.divideAndReturnBigDecimal(cost, liveEntryCount);
            // 直播进房商品点击率，直播间商品点击数/直播间进房数
            liveCallUpRate = CommonFuncs.divideAndReturnBigDecimalPercent(liveCallUpCount, liveEntryCount);
        }
        if (liveCallUpCount > 0) {
            liveCallUpCost = CommonFuncs.divideAndReturnBigDecimal(cost, liveCallUpCount);
        }
        if (liveReserveCount > 0) {
            liveReserveCost = CommonFuncs.divideAndReturnBigDecimal(cost, liveReserveCount);
        }
        if (androidGameActivePaidIn24hCount > 0) {
            androidGameActivePaidIn24hCost = CommonFuncs.divideAndReturnBigDecimal(cost, androidGameActivePaidIn24hCount);
        }
        if (androidGameActivePaidIn7dCount > 0) {
            androidGameActivePaidIn7dCost = CommonFuncs.divideAndReturnBigDecimal(cost, androidGameActivePaidIn7dCount);
        }
        if (androidGameActivePaidIn1dCount > 0) {
            androidGameActivePaidIn1dCost = CommonFuncs.divideAndReturnBigDecimal(cost, androidGameActivePaidIn1dCount);
        }
        if (firstWithdrawCount > 0) {
            // 【首日放款成本】（花费/首日放款数）
            firstWithdrawCost = CommonFuncs.divideAndReturnBigDecimal(cost, firstWithdrawCount);
        }
        if (componentClickCount > 0) {
            componentClickCost = CommonFuncs.divideAndReturnBigDecimal(cost, componentClickCount);
        }
        if (liveGameCardShowCount > 0) {
            liveGameCardClickRate = CommonFuncs.divideAndReturnBigDecimalPercent(liveGameCardClickCount, liveGameCardShowCount);
        }
        if (liveGameCardClickCount > 0) {
            liveGameCardClickCost = CommonFuncs.divideAndReturnBigDecimal(cost, liveGameCardClickCount);
        }
        return this;
    }
}
