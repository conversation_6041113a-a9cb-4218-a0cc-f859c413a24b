package com.bilibili.adp.advertiser.portal.util;

import com.bilibili.adp.cpc.enums.ErrorCodeEnum;
import com.bilibili.warp.ecode.Ecode;
import io.grpc.Status;
import org.springframework.http.HttpStatus;

/**
 * 错误码映射工具类
 * 将各种类型的错误码映射为统一的业务错误码格式
 */
public class ErrorCodeMapper {

    /**
     * 将HTTP状态码映射为业务错误码
     */
    public static String mapHttpStatusToErrorCode(int httpStatus, String domainType) {
        if (httpStatus == HttpStatus.BAD_REQUEST.value()) {
            return domainType + "-" + ErrorCodeEnum.ErrorType.BIZ_ERR.name() + "-" + ErrorCodeEnum.SubCode.PARAM_INVALID.getCode();
        } else if (httpStatus == HttpStatus.INTERNAL_SERVER_ERROR.value()) {
            return ErrorCodeEnum.SubCode.SYSTEM_ERROR.getCode();
        } else if (httpStatus >= HttpStatus.BAD_REQUEST.value() && httpStatus < HttpStatus.INTERNAL_SERVER_ERROR.value()) {
            return domainType + "-" + ErrorCodeEnum.ErrorType.BIZ_ERR.name() + "-" + ErrorCodeEnum.SubCode.CLIENT_ERROR.getCode();
        } else if (httpStatus >= HttpStatus.INTERNAL_SERVER_ERROR.value() && httpStatus <= HttpStatus.NETWORK_AUTHENTICATION_REQUIRED.value()) {
            return domainType + "-" + ErrorCodeEnum.ErrorType.BIZ_ERR.name() + "-" + ErrorCodeEnum.SubCode.SERVER_ERROR.getCode();
        }
        return domainType + "-" + ErrorCodeEnum.ErrorType.BIZ_ERR.name() + "-" + ErrorCodeEnum.SubCode.UNKNOWN.getCode();
    }

    /**
     * 将gRPC状态码映射为业务错误码
     */
    public static String mapGrpcStatusToErrorCode(Status.Code grpcCode, String domainType) {
        switch (grpcCode) {
            case INVALID_ARGUMENT:
                return domainType + "-" + ErrorCodeEnum.ErrorType.BIZ_ERR.name() + "-" + ErrorCodeEnum.SubCode.PARAM_INVALID.getCode();
            case DEADLINE_EXCEEDED:
                return domainType + "-" + ErrorCodeEnum.ErrorType.BIZ_ERR.name() + "-" + ErrorCodeEnum.SubCode.TIMEOUT.getCode();
            case UNKNOWN:
                return domainType + "-" + ErrorCodeEnum.ErrorType.BIZ_ERR.name() + "-" + ErrorCodeEnum.SubCode.DOWNSTREAM_CALL_ERROR.getCode();
            default:
                return domainType + "-" + ErrorCodeEnum.ErrorType.BIZ_ERR.name() + "-" + ErrorCodeEnum.SubCode.UNKNOWN.getCode();
        }
    }

    /**
     * 将错误码平台错误码映射为业务错误码
     */
    public static String mapEcodeToErrorCode(int errorCode, String domainType) {
        if (errorCode == Ecode.BAD_REQUEST.code()) {
            return domainType + "-" + ErrorCodeEnum.ErrorType.BIZ_ERR.name() + "-" + ErrorCodeEnum.SubCode.PARAM_INVALID.getCode();
        } else if (errorCode == Ecode.DEADLINE_EXCEEDED.code()) {
            return domainType + "-" + ErrorCodeEnum.ErrorType.BIZ_ERR.name() + "-" + ErrorCodeEnum.SubCode.TIMEOUT.getCode();
        } else if (errorCode == Ecode.SERVER_ERROR.code()) {
            return domainType + "-" + ErrorCodeEnum.ErrorType.BIZ_ERR.name() + "-" + ErrorCodeEnum.SubCode.SYSTEM_ERROR.getCode();
        } else if (errorCode >= Ecode.CLIENT_CLOSE_REQUEST.code() && errorCode <= Ecode.BAD_REQUEST.code()) {
            return domainType + "-" + ErrorCodeEnum.ErrorType.BIZ_ERR.name() + "-" + ErrorCodeEnum.SubCode.CLIENT_ERROR.getCode();
        } else if (errorCode >= Ecode.LIMIT_EXCEEDED.code() && errorCode <= Ecode.SERVER_ERROR.code()) {
            return domainType + "-" + ErrorCodeEnum.ErrorType.BIZ_ERR.name() + "-" + ErrorCodeEnum.SubCode.SERVER_ERROR.getCode();
        }
        return domainType + "-" + ErrorCodeEnum.ErrorType.BIZ_ERR.name() + "-" + ErrorCodeEnum.SubCode.UNKNOWN.getCode();
    }

    /**
     * 获取对应的错误描述
     */
    public static String getErrorDescription(String errorCode) {
        if (errorCode.contains(ErrorCodeEnum.SubCode.PARAM_INVALID.getCode())) {
            return ErrorCodeEnum.SubCode.PARAM_INVALID.getDesc();
        } else if (errorCode.contains(ErrorCodeEnum.SubCode.TIMEOUT.getCode())) {
            return ErrorCodeEnum.SubCode.TIMEOUT.getDesc();
        } else if (errorCode.contains(ErrorCodeEnum.SubCode.DOWNSTREAM_CALL_ERROR.getCode())) {
            return ErrorCodeEnum.SubCode.DOWNSTREAM_CALL_ERROR.getDesc();
        } else if (errorCode.contains(ErrorCodeEnum.SubCode.SYSTEM_ERROR.getCode())) {
            return ErrorCodeEnum.SubCode.SYSTEM_ERROR.getDesc();
        } else if (errorCode.contains(ErrorCodeEnum.SubCode.CLIENT_ERROR.getCode())) {
            return ErrorCodeEnum.SubCode.CLIENT_ERROR.getDesc();
        } else if (errorCode.contains(ErrorCodeEnum.SubCode.SERVER_ERROR.getCode())) {
            return ErrorCodeEnum.SubCode.SERVER_ERROR.getDesc();
        }
        return ErrorCodeEnum.SubCode.UNKNOWN.getDesc();
    }
}
