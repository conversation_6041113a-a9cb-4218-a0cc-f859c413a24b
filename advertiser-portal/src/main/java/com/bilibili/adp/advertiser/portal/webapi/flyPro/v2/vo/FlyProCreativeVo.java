package com.bilibili.adp.advertiser.portal.webapi.flyPro.v2.vo;

import com.bilibili.adp.advertiser.portal.webapi.launch.cpc.vo.CpcImageVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class FlyProCreativeVo {
    @ApiModelProperty("单元ID")
    private Integer unit_id;
    @ApiModelProperty("创意ID 有则更新，无则新增")
    private Integer creative_id;
    @ApiModelProperty("创意名称")
    private String creative_name;
    @ApiModelProperty("标题")
    private String title;
    @ApiModelProperty("封面")
    private List<CpcImageVo> images;
    @ApiModelProperty("描述")
    private String description;
    @ApiModelProperty("封面类型 1:静态图文 2:gif图文")
    private Integer cover_type;
    @ApiModelProperty("显示监控")
    private String customized_imp_url;
    @ApiModelProperty("点击监控")
    private String customized_click_url;
    @ApiModelProperty("投放场景 1:优选场景 2:指定场景")
    private Integer launch_scenes;
    @ApiModelProperty("指定场景列表 1:信息流 2:播放页 3:信息流大卡 4:动态流")
    private List<Integer> specific_scenes_list;
    @ApiModelProperty("指定场景列表 0:无指定场景 1:单选框 2:多选框")
    private Integer button_type;
    @ApiModelProperty("框下链接实体")
    private FlyProUnderBoxSaveVo fly_pro_under_box;
}
