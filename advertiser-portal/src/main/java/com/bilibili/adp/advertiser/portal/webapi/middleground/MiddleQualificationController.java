package com.bilibili.adp.advertiser.portal.webapi.middleground;

import com.bilibili.adp.advertiser.portal.common.BasicController;
import com.bilibili.adp.advertiser.portal.common.Context;
import com.bilibili.adp.advertiser.portal.webapi.v2.misc.AdpCpcQualificationController;
import com.bilibili.adp.advertiser.portal.webapi.v2.misc.bos.*;
import com.bilibili.adp.advertiser.portal.webapi.v2.vo.common.BFSInfoVo;
import com.bilibili.adp.advertiser.portal.webapi.v2.vo.common.KeyValueVo;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.Page;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.biz.services.creative.AdpCpcCreativeQualificationService;
import com.bilibili.adp.cpc.biz.services.creative.MiddleCreativeQualificationPackageService;
import com.bilibili.adp.cpc.biz.services.creative.MiddleCreativeQualificationService;
import com.bilibili.adp.cpc.biz.services.creative.bos.*;
import com.bilibili.adp.cpc.utils.Response;
import com.google.common.base.Splitter;
import groovy.util.logging.Slf4j;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/web_api/v1/middle/qualification")
@Api(value = "/middle/qualification", description = "广告中台【资质】")
public class MiddleQualificationController extends BasicController {

    @Autowired
    private AdpCpcQualificationController adpCpcQualificationController;
    @Autowired
    private MiddleCreativeQualificationService middleCreativeQualificationService;
    @Autowired
    private AdpCpcCreativeQualificationService creativeQualificationService;
    @Autowired
    private MiddleCreativeQualificationPackageService middleCreativeQualificationPackageService;

    @Deprecated
    @ApiOperation("资质列表(新版用下面这个分页的)")
    @GetMapping("/list")
    public Response<List<LauQualificationVo>> listQualification(@ApiIgnore Context context, QualificationQueryVo queryVo) {
        return adpCpcQualificationController.listQualification(context, queryVo);
    }

    @ApiOperation("资质下拉列表")
    @GetMapping("/drop_list")
    public Response<List<LauQualificationVo>> qualificationDropList(@ApiIgnore Context context, QualificationQueryVo queryVo) {
        QualificationQueryDto queryBo = QualificationQueryVo.toQueryDto(queryVo);
        queryBo.setCurAccountId(context.getAccountId());
        List<LauQualificationBo> qualificationBos = middleCreativeQualificationService.qualificationDropList(queryBo);

        return Response.ok(LauQualificationVo.fromBo(qualificationBos));
    }


    @ApiOperation("资质下拉列表")
    @GetMapping("/{qualification_ids}")
    public Response<List<LauQualificationVo>> qualificationDropList(
            @ApiIgnore Context context,
            @PathVariable("qualification_ids") String qualificationIds) {


        if(StringUtils.isEmpty(qualificationIds)){

            throw new IllegalArgumentException("qualification_ids不能为空");
        }

        List<Integer> ids = Splitter.on(",").splitToList(qualificationIds)
                .stream()
                .map(Integer::parseInt)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(ids)) {
            throw new IllegalArgumentException("qualification_ids不能为空");
        }

        QualificationQueryDto queryBo = new QualificationQueryDto();
        queryBo.setQualificationIds(ids);
        queryBo.setCurAccountId(context.getAccountId());

        // TODO 需要注意这里是否会有授权逻辑的问题

        List<LauQualificationBo> qualificationBos =
                middleCreativeQualificationService.qualificationDropList(queryBo);
        return Response.ok(LauQualificationVo.fromBo(qualificationBos));
    }

    @ApiOperation("资质列表")
    @GetMapping("/page")
    public Response<PageResult<LauQualificationVo>> listQualificationByPage(@ApiIgnore Context context, QualificationQueryVo queryVo) {
        final int aid = context.getAccountId();
        if (context.isOpenApi()) {
            Assert.isTrue(Utils.isPositive(queryVo.getPage())
                    && Utils.isPositive(queryVo.getPage_size()), "分页参数不可为空");
            Assert.isTrue(queryVo.getPage_size() <= 100, "分页过大");
        }

        QualificationQueryDto queryBo = QualificationQueryVo.toQueryDto(queryVo);
        queryBo.setCurAccountId(aid);
        // 分页，兼容老的版本不传页码，则获取全量
        if (queryVo.getPage() == null) {
            queryVo.setPage(1);
        }
        if (queryVo.getPage_size() == null) {
            queryVo.setPage(Integer.MAX_VALUE);
        }
        if (queryVo.getPage() != null && queryVo.getPage_size() != null) {
            queryBo.setPage(Page.valueOf(queryVo.getPage(), queryVo.getPage_size()));
        }
        final PageResult<LauQualificationBo> bos = creativeQualificationService.listByAccountIdByPage(queryBo);

        PageResult<LauQualificationVo> result = PageResult.<LauQualificationVo>builder()
                .total(bos.getTotal())
                .records(LauQualificationVo.fromBo(bos.getRecords()))
                .build();
        return Response.ok(result);
    }

    @ApiOperation("删除资质")
    @DeleteMapping("/delete/{id}")
    public Response<Void> deleteQualification(@ApiIgnore Context ctx,
                                              @PathVariable Integer id) {
        return adpCpcQualificationController.deleteQualification(ctx, id);
    }

    @ApiOperation("新增资质")
    @PostMapping("/create")
    public Response<Integer> createQualification(@ApiIgnore Context ctx,
                                                 @RequestBody LauQualificationVo vo) {
        return adpCpcQualificationController.createQualification(ctx, vo);
    }

    @ApiOperation("资质类型列表")
    @GetMapping("/type/list")
    public Response<List<KeyValueVo<Integer, String>>> listQualificationTypes(@ApiIgnore Context ctx) {
        return adpCpcQualificationController.listQualificationTypes(ctx);
    }

    @ApiOperation("上传资质图片")
    @PostMapping(value = "/upload/image")
    public Response<BFSInfoVo> uploadImage(@ApiIgnore Context ctx, @RequestPart MultipartFile file) {
        return adpCpcQualificationController.uploadImage(ctx,file);
    }

    @ApiOperation("上传资质文件")
    @PostMapping(value = "/upload/file")
    public Response<BFSInfoVo> uploadFile(@ApiIgnore Context ctx, @RequestPart MultipartFile file) throws ServiceException {
        return adpCpcQualificationController.uploadFile(ctx,file);
    }

    // =========== 资质包

    @ApiOperation("资质包列表")
    @GetMapping("/package/list")
    public Response<PageResult<LauQualificationPackageVo>> listQualificationPackage(@ApiIgnore Context context,
                                                                                    QualificationPackageQueryVo queryVo) {
        final int aid = context.getAccountId();
        QualificationPackageQueryDto queryDto = QualificationPackageQueryVo.toQueryDto(queryVo);
        queryDto.setCurAccountId(aid);

        final PageResult<LauQualificationPackageBo> bos = middleCreativeQualificationPackageService.listByAccountId(queryDto);

        PageResult<LauQualificationPackageVo> result = PageResult.<LauQualificationPackageVo>builder()
                .total(bos.getTotal())
                .records(bos.getRecords().stream()
                        .map(LauQualificationPackageVo::fromBo)
                        .collect(Collectors.toList()))
                .build();
        return Response.ok(result);
    }

    @ApiOperation("资质包下拉列表")
    @GetMapping("/package/drop_list")
    public Response<List<LauQualificationPackageVo>> qualificationPackageDropList(@ApiIgnore Context context,
                                                                                  @RequestParam("name") String name) {
        final int aid = context.getAccountId();
        final List<LauQualificationPackageBo> bos = middleCreativeQualificationPackageService.dropListByAccountId(aid, name);
        return Response.ok(bos.stream()
                .map(LauQualificationPackageVo::fromBo)
                .collect(Collectors.toList()));
    }

    @ApiOperation("获取资质包下的资质列表")
    @GetMapping("/list/{packageId}")
    public Response<List<QualificationSimpleVo>> listQualificationOfPackage(@ApiIgnore Context context,
                                                                            @PathVariable Integer packageId) {
        final int aid = context.getAccountId();
        final List<QualificationSimpleBo> bos = middleCreativeQualificationPackageService.listQualificationOfPackage(packageId);

        return Response.ok(QualificationSimpleVo.from(bos));
    }

    @ApiOperation("新增资质包")
    @PostMapping("/package/create")
    public Response<Integer> createQualificationPackage(@ApiIgnore Context ctx,
                                                        @RequestBody LauQualificationPackageVo vo) {
        final int aid = ctx.getAccountId();
        Assert.isTrue(Objects.isNull(vo.getId()), "创建资质包时不允许携带id参数");
        final int qid = middleCreativeQualificationPackageService.create(LauQualificationPackageVo.toBo(aid, vo));
        return Response.ok(qid);
    }

    @ApiOperation("修改资质包")
    @PostMapping("/package/update")
    public Response<Integer> updateQualificationPackage(@ApiIgnore Context ctx,
                                                        @RequestBody LauQualificationPackageVo vo) {
        final int aid = ctx.getAccountId();
        final int qid = middleCreativeQualificationPackageService.update(LauQualificationPackageVo.toBo(aid, vo));
        return Response.ok(qid);
    }

    @ApiOperation("删除资质包")
    @DeleteMapping("/package/delete/{id}")
    public Response<Void> deleteQualificationPackage(@ApiIgnore Context context,
                                                     @PathVariable Integer id) {
        final int aid = context.getAccountId();

        middleCreativeQualificationPackageService.markDeleted(aid, id);
        return Response.ok();
    }
}
