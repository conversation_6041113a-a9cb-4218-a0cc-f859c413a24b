 advertiser-platform

预发/测试环境后门地址：http://cm-mng.bilibili.co/platform/api/backdoor

###后门接口：
###     根据订单id来更新期望提升类型  1-播放量  2-粉丝量
###     执行成功则返回true
com.bilibili.adp.launch.api.up.ICashUpOrderService#updateExpectImproveTypeByOrderId(#orderId, #improveType)


###
直播间账号标签校验
com.bilibili.adp.launch.biz.validator.CpcCampaignValidator.checkLiveRoomAccountLabel(19696)
###
投直播间白名单
com.bilibili.adp.advertiser.helper.cash_fly.CashPersonFlyHelper.inWhiteList(#mid)


com.bilibili.adp.resource.api.bus_mark_rule.IBusMarkRuleService.getValidBusMarkList()