<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.bilibili.adp</groupId>
    <artifactId>advertiser-platform</artifactId>
    <version>2.1.13-SNAPSHOT</version>
    <packaging>pom</packaging>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.3.3.RELEASE</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>
    <modules>
        <module>advertiser-portal</module>
        <module>advertiser-portal-edge</module><!---->
        <module>adp-web-framework</module>
        <module>adp-cpc</module>
        <module>adp-soa-api</module>
    </modules>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <!--spring boot start-->
        <!--https://docs.spring.io/spring-boot/docs/2.3.3.RELEASE/reference/html/appendix-dependency-versions.html#dependency-versions-->
        <spring-framework.version>5.2.8.RELEASE</spring-framework.version>
        <spring-amqp.version>2.2.10.RELEASE</spring-amqp.version>
        <spring-boot.version>2.3.3.RELEASE</spring-boot.version>
        <spring-data-commons.version>2.3.3.RELEASE</spring-data-commons.version>
        <spring-data-keyvalue.version>2.3.3.RELEASE</spring-data-keyvalue.version>
        <spring-data-redis.version>2.3.3.RELEASE</spring-data-redis.version>
        <jackson.version>2.13.3</jackson.version>
        <jedis.version>3.3.0</jedis.version>
        <rabbit-amqp-client.version>5.9.0</rabbit-amqp-client.version>
        <!--spring boot end-->
        <wrap-databus.version>1.1.5</wrap-databus.version>
        <databus-java.version>1.11.4-RELEASE</databus-java.version>
        <pleiades.version>1.1.11.RELEASE</pleiades.version>
        <paladin-client.version>2.0.6</paladin-client.version>
        <adp.version>8.1.65.02-SNAPSHOT</adp.version>
        <bsi.version>0.0.1-SNAPSHOT</bsi.version>
        <rbac.version>1.0.4-SNAPSHOT</rbac.version>
        <crm.version>4.8.2-SNAPSHOT</crm.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <useDefaultDelimiters>false</useDefaultDelimiters>
        <slf4j.version>1.7.12</slf4j.version>
        <logback.version>1.1.7</logback.version>
        <fastjson.version>1.2.9</fastjson.version>
        <mybatis.version>3.5.19</mybatis.version>
        <spring-mybatis.version>2.1.2</spring-mybatis.version>
        <mysql.version>8.0.30</mysql.version>
        <org.modelmapper.version>0.7.2</org.modelmapper.version>
        <junit.version>4.12</junit.version>
        <guava.version>26.0-android</guava.version>
        <lombok.version>1.18.16</lombok.version>
        <javaee-web-api.version>7.0</javaee-web-api.version>
        <jackson.version>2.13.3</jackson.version>
        <swagger.version>2.2.2</swagger.version>
        <netty-all.version>4.1.51.Final</netty-all.version>
        <gateway.version>2.0.1-SNAPSHOT</gateway.version>
        <location.version>0.5.97-mix-SNAPSHOT</location.version>
        <mgk.version>0.2.54-SNAPSHOT</mgk.version>
        <org.mapstruct.version>1.5.3.Final</org.mapstruct.version>
        <commercial.order.version>1.35.91-SNAPSHOT</commercial.order.version>
        <dmp.api.version>3.3.6-SNAPSHOT</dmp.api.version>
        <mas.version>4.16.0-SNAPSHOT</mas.version>
        <bible.consumer.version>0.0.3-SNAPSHOT</bible.consumer.version>
        <resilience4j.version>1.7.1</resilience4j.version>
        <fastjson.version>1.2.83</fastjson.version>
        <c3p0.version>0.9.5.4</c3p0.version>
        <commons-beanutils.version>1.9.4</commons-beanutils.version>
        <commons-collections.version>3.2.2</commons-collections.version>
        <jackson-databind.version>2.13.3</jackson-databind.version>
        <cat-client.version>1.5.3-SNAPSHOT</cat-client.version>
        <report.version>2.1.30.6-SNAPSHOT</report.version>
        <io.netty.version>4.1.51.Final</io.netty.version>
        <config-refresh.version>3.0.5-RELEASE</config-refresh.version>
        <awsjavasdk.version>2.27.21</awsjavasdk.version>
        <mockito.version>5.14.2</mockito.version>
        <opentelemetry.version>1.14.0</opentelemetry.version>
        <boggart.version>2.3-SNAPSHOT</boggart.version>
        <joda-time.version>2.9.6</joda-time.version>
    </properties>

    <dependencyManagement>
        <dependencies>
<!--            <dependency>-->
<!--                <groupId>com.bilibili.redisson</groupId>-->
<!--                <artifactId>redisson</artifactId>-->
<!--                <version>3.17.6.240709</version>-->
<!--            </dependency>-->
            <dependency>
                <groupId>pleiades.component.datasource</groupId>
                <artifactId>mysql</artifactId>
                <version>${pleiades.version}</version>
            </dependency>
            <dependency>
                <groupId>pleiades.component.datasource</groupId>
                <artifactId>datasource-common</artifactId>
                <version>${pleiades.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>bom</artifactId>
                <version>2.27.21</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-aop</artifactId>
                <version>${spring-framework.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-beans</artifactId>
                <version>${spring-framework.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-context</artifactId>
                <version>${spring-framework.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-context-support</artifactId>
                <version>${spring-framework.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-core</artifactId>
                <version>${spring-framework.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-jdbc</artifactId>
                <version>${spring-framework.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-tx</artifactId>
                <version>${spring-framework.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-web</artifactId>
                <version>${spring-framework.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-webmvc</artifactId>
                <version>${spring-framework.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.amqp</groupId>
                <artifactId>spring-amqp</artifactId>
                <version>${spring-amqp.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.amqp</groupId>
                <artifactId>spring-rabbit</artifactId>
                <version>${spring-amqp.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-autoconfigure</artifactId>
                <version>${spring-boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-actuator</artifactId>
                <version>${spring-boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-tomcat</artifactId>
                <version>${spring-boot.version}</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-web</artifactId>
                <version>${spring-boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-test</artifactId>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>${mockito.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.bilibili</groupId>
                <artifactId>warp-spring-boot-starter-databus</artifactId>
                <version>${wrap-databus.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.bilibili.microservices</groupId>
                        <artifactId>databus-java</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.bilibili.microservices</groupId>
                <artifactId>databus-java</artifactId>
                <version>${databus-java.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.data</groupId>
                <artifactId>spring-data-commons</artifactId>
                <version>${spring-data-commons.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.data</groupId>
                <artifactId>spring-data-keyvalue</artifactId>
                <version>${spring-data-keyvalue.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.data</groupId>
                <artifactId>spring-data-redis</artifactId>
                <version>${spring-data-redis.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-core</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>redis.clients</groupId>
                <artifactId>jedis</artifactId>
                <version>${jedis.version}</version>
            </dependency>
            <dependency>
                <groupId>com.rabbitmq</groupId>
                <artifactId>amqp-client</artifactId>
                <version>${rabbit-amqp-client.version}</version>
            </dependency>
            <!-- pleiades start -->
            <dependency>
                <groupId>pleiades.component.http</groupId>
                <artifactId>http-client</artifactId>
                <version>${pleiades.version}</version>
            </dependency>
            <dependency>
                <groupId>pleiades.component.rpc</groupId>
                <artifactId>rpc-client</artifactId>
                <version>${pleiades.version}</version>
            </dependency>
            <dependency>
                <groupId>pleiades.component.rpc</groupId>
                <artifactId>rpc-core</artifactId>
                <version>${pleiades.version}</version>
            </dependency>
            <dependency>
                <groupId>pleiades.component.rpc</groupId>
                <artifactId>rpc-server</artifactId>
                <version>${pleiades.version}</version>
            </dependency>
            <dependency>
                <groupId>pleiades.component</groupId>
                <artifactId>boot</artifactId>
                <version>${pleiades.version}</version>
            </dependency>
            <dependency>
                <groupId>pleiades.component</groupId>
                <artifactId>component-ecode</artifactId>
                <version>${pleiades.version}</version>
            </dependency>
            <dependency>
                <groupId>pleiades.component</groupId>
                <artifactId>env</artifactId>
                <version>${pleiades.version}</version>
            </dependency>
            <dependency>
                <groupId>pleiades.component</groupId>
                <artifactId>stats</artifactId>
                <version>${pleiades.version}</version>
            </dependency>
            <dependency>
                <groupId>pleiades.component</groupId>
                <artifactId>utility</artifactId>
                <version>${pleiades.version}</version>
            </dependency>
            <dependency>
                <groupId>pleiades.venus</groupId>
                <artifactId>starter</artifactId>
                <version>${pleiades.version}</version>
            </dependency>
            <dependency>
                <groupId>pleiades.venus</groupId>
                <artifactId>config</artifactId>
                <version>${pleiades.version}</version>
            </dependency>
            <dependency>
                <groupId>pleiades.venus.naming</groupId>
                <artifactId>naming-discovery</artifactId>
                <version>${pleiades.version}</version>
            </dependency>
            <dependency>
                <groupId>pleiades.venus.logging</groupId>
                <artifactId>logging-support</artifactId>
                <version>${pleiades.version}</version>
            </dependency>
            <!-- pleiades end -->
            <dependency>
                <groupId>com.bilibili</groupId>
                <artifactId>paladin-client</artifactId>
                <version>${paladin-client.version}</version>
            </dependency>
            <!-- 千万注意，旧版bible里面 也有个com.bilibili.adp.web.framework.annotations.ExcelResources，会导致no such method error-->
            <dependency>
                <groupId>com.bilibili.sycpb</groupId>
                <artifactId>bible-common</artifactId>
                <version>1.0.7-RELEASE</version>
            </dependency>
            <!--对齐io.netty-->
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-resolver</artifactId>
                <version>4.1.48.Final</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-buffer</artifactId>
                <version>${io.netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-transport</artifactId>
                <version>${io.netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-common</artifactId>
                <version>${io.netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-transport-native-unix-common</artifactId>
                <version>${io.netty.version}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-transport-native-epoll</artifactId>
                <classifier>linux-x86_64</classifier>
                <version>${io.netty.version}</version>
            </dependency>
            <!--对齐io.netty end-->
            <dependency>
                <groupId>com.bilibili</groupId>
                <artifactId>logback-otel-appender</artifactId>
                <version>1.0.5</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis-spring</artifactId>
                <version>${spring-mybatis.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis</artifactId>
                <version>${mybatis.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bilibili.mas</groupId>
                <artifactId>mas-api</artifactId>
                <version>${mas.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bilibili.mas</groupId>
                <artifactId>mas-common</artifactId>
                <version>${mas.version}</version>
            </dependency>
            <dependency>
                <groupId>org.ow2.asm</groupId>
                <artifactId>asm</artifactId>
                <version>7.1</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>4.1.2</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>4.1.2</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml-schemas</artifactId>
                <version>4.1.2</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>3.0.5</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${org.mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${org.mapstruct.version}</version>
                <optional>true</optional>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.bilibili.adp</groupId>
                <artifactId>common</artifactId>
                <version>${adp.version}</version>
<!--                <exclusions>-->
<!--                    <exclusion>-->
<!--                        <groupId>com.bilibili.redisson</groupId>-->
<!--                        <artifactId>redisson</artifactId>-->
<!--                    </exclusion>-->
<!--                </exclusions>-->
            </dependency>
            <dependency>
                <groupId>commons-codec</groupId>
                <artifactId>commons-codec</artifactId>
                <version>1.15</version>
            </dependency>
            <dependency>
                <groupId>com.bilibili.mgk</groupId>
                <artifactId>mgk-api</artifactId>
                <version>${mgk.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bilibili.location</groupId>
                <artifactId>location-api</artifactId>
                <version>${location.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bilibili.adp</groupId>
                <artifactId>launch-biz</artifactId>
                <version>${adp.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.bilibili.adp</groupId>
                        <artifactId>passport-biz</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>c3p0</artifactId>
                        <groupId>c3p0</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>crm-common</artifactId>
                        <groupId>com.bilibili.crm</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.bilibili.adp</groupId>
                <artifactId>log-api</artifactId>
                <version>${adp.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bilibili.adp</groupId>
                <artifactId>passport-api</artifactId>
                <version>${adp.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bilibili.adp</groupId>
                <artifactId>passport-biz-temp</artifactId>
                <version>${adp.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bilibili.adp</groupId>
                <artifactId>launch-api</artifactId>
                <version>${adp.version}</version>
            </dependency>
            <dependency>
                <groupId>org.javers</groupId>
                <artifactId>javers-core</artifactId>
                <version>6.14.0</version>
            </dependency>
            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>2.9.0</version>
            </dependency>
            <dependency>
                <groupId>com.bilibili.mgk</groupId>
                <artifactId>collage-api</artifactId>
                <version>${mgk.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.bilibili.adp</groupId>
                        <artifactId>common</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.bilibili.ad</groupId>
                <artifactId>commercial-order-api</artifactId>
                <version>${commercial.order.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.bilibili.adp</groupId>
                        <artifactId>passport-biz</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.bilibili.report</groupId>
                <artifactId>report-api</artifactId>
                <version>${report.version}</version>
            </dependency>
            <dependency>
                <groupId>ru.yandex.clickhouse</groupId>
                <artifactId>clickhouse-jdbc</artifactId>
                <version>0.2.4</version>
            </dependency>
            <dependency>
                <groupId>com.zaxxer</groupId>
                <artifactId>HikariCP</artifactId>
                <version>3.4.5</version>
            </dependency>
            <dependency>
                <groupId>io.vavr</groupId>
                <artifactId>vavr</artifactId>
                <version>0.10.2</version>
            </dependency>
            <dependency>
                <groupId>com.bilibili.crm</groupId>
                <artifactId>crm-api</artifactId>
                <version>${crm.version}</version>
            </dependency>
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-classic</artifactId>
                <version>${logback.version}</version>
            </dependency>
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-core</artifactId>
                <version>${logback.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mchange</groupId>
                <artifactId>c3p0</artifactId>
                <version>${c3p0.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-beanutils</groupId>
                <artifactId>commons-beanutils</artifactId>
                <version>${commons-beanutils.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-collections</groupId>
                <artifactId>commons-collections</artifactId>
                <version>${commons-collections.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${jackson-databind.version}</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.cat</groupId>
                <artifactId>cat-client</artifactId>
                <version>${cat-client.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bilibili.mgk</groupId>
                <artifactId>mgk-common</artifactId>
                <version>${mgk.version}</version>
            </dependency>
            <dependency>
                <groupId>com.bilibili.config</groupId>
                <artifactId>config-refresh</artifactId>
                <version>${config-refresh.version}</version>
            </dependency>
            <!-- 更低版本的 crm common里面有高版本prometheus 会和pleiades冲突-->
            <dependency>
                <groupId>com.bilibili.crm</groupId>
                <artifactId>crm-common</artifactId>
                <version>4.6.92-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.bilibili.dmp</groupId>
                <artifactId>dmp-api</artifactId>
                <version>${dmp.api.version}</version>
            </dependency>
            <dependency>
                <groupId>io.opentelemetry</groupId>
                <artifactId>opentelemetry-api</artifactId>
                <version>${opentelemetry.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-annotations</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>joda-time</groupId>
                <artifactId>joda-time</artifactId>
                <version>${joda-time.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <distributionManagement>
        <repository>
            <id>bili-nexus-release-server</id>
            <url>https://nexus.bilibili.co/content/repositories/releases/</url>
        </repository>

        <snapshotRepository>
            <id>bili-nexus-snapshots-server</id>
            <url>https://nexus.bilibili.co/content/repositories/snapshots/</url>
        </snapshotRepository>
    </distributionManagement>
</project>
