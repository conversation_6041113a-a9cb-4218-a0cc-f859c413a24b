<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">


    <bean id="adBusinessCpcDataSource" class="com.mchange.v2.c3p0.ComboPooledDataSource">
        <property name="driverClass" value="${ad.business.cpc.jdbc.driver}"/>
        <property name="jdbcUrl" value="${ad.business.cpc.jdbc.url}"/>
        <property name="user" value="${ad.business.cpc.jdbc.username}"/>
        <property name="password" value="${ad.business.cpc.jdbc.password}"/>
        <property name="maxPoolSize" value="10"/>
        <property name="minPoolSize" value="1"/>
        <property name="maxIdleTime" value="7200"/>
        <property name="testConnectionOnCheckin" value="true"/>
        <property name="idleConnectionTestPeriod" value="5"/>
        <property name="preferredTestQuery" value="SELECT 1"/>
        <property name="checkoutTimeout" value="1800000"/>
    </bean>

    <bean id="adBusinessCpcTransactionManager"
          class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="adBusinessCpcDataSource"/>
    </bean>
</beans>