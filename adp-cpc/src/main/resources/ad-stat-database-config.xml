<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
  		http://www.springframework.org/schema/beans/spring-beans-3.1.xsd
  		http://www.springframework.org/schema/tx
        http://www.springframework.org/schema/tx/spring-tx-3.0.xsd">

    <bean id="adStatDataSource" class="com.mchange.v2.c3p0.ComboPooledDataSource">
        <property name="driverClass" value="${adStat.jdbc.driver}"></property>
        <property name="jdbcUrl" value="${adStat.jdbc.url}"></property>
        <property name="user" value="${adStat.jdbc.username}"></property>
        <property name="password" value="${adStat.jdbc.password}"></property>
        <property name="maxPoolSize" value="10"></property>
        <property name="maxIdleTime" value="7200"></property>
        <property name="testConnectionOnCheckin" value="true"></property>
        <property name="idleConnectionTestPeriod" value="5"></property>
        <property name="preferredTestQuery" value="SELECT 1"></property>
        <property name="checkoutTimeout" value="1800000"></property>
    </bean>

    <bean id="adStatSqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="adStatDataSource"/>
        <property name="mapperLocations">
            <array>
                <value>classpath:mapper/ad_stat/*.xml</value>
            </array>
        </property>
        <property name="plugins">
            <array>
                <ref bean="catExecutorMybatisPlugin"/>
            </array>
        </property>
    </bean>

    <bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="basePackage"
                  value="com.bilibili.adp.cpc.dao.ad_stat"/>
        <property name="sqlSessionFactoryBeanName" value="adStatSqlSessionFactory"/>
    </bean>

    <bean id="adStatTransactionManager"
          class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="adStatDataSource"/>
    </bean>

    <tx:annotation-driven transaction-manager="adStatTransactionManager"/>

    <bean id="catExecutorMybatisPlugin" class="com.bilibili.adp.cpc.plugin.CatExecutorMybatisPlugin"/>
</beans>
