<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:tx="http://www.springframework.org/schema/tx" xmlns:aop="http://www.springframework.org/schema/aop"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
    http://www.springframework.org/schema/beans/spring-beans-3.1.xsd
    http://www.springframework.org/schema/tx
        http://www.springframework.org/schema/tx/spring-tx-3.0.xsd http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd">

    <!--    crm切库，crm只读新库-->
    <bean id="crmV2DataSource" class="com.mchange.v2.c3p0.ComboPooledDataSource">
        <property name="driverClass" value="${crm.account.read.only.jdbc.driver}"/>
        <property name="jdbcUrl" value="${crm.account.read.only.jdbc.url}"/>
        <property name="user" value="${crm.account.read.only.jdbc.username}"/>
        <property name="password" value="${crm.account.read.only.jdbc.password}"/>
        <property name="maxPoolSize" value="10"/>
        <property name="minPoolSize" value="1"/>
        <property name="maxIdleTime" value="7200"/>
        <property name="testConnectionOnCheckin" value="true"/>
        <property name="idleConnectionTestPeriod" value="5"/>
        <property name="preferredTestQuery" value="SELECT 1"/>
        <property name="checkoutTimeout" value="1800000"/>
    </bean>
</beans>