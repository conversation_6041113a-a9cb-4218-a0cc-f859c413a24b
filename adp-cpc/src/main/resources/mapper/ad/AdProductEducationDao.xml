<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.adp.cpc.dao.ad.AdProductEducationDao">
  <resultMap id="BaseResultMap" type="com.bilibili.adp.cpc.po.ad.AdProductEducationPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="account_id" jdbcType="INTEGER" property="accountId" />
    <result column="library_id" jdbcType="BIGINT" property="libraryId" />
    <result column="ad_product_id" jdbcType="BIGINT" property="adProductId" />
    <result column="biz_status" jdbcType="TINYINT" property="bizStatus" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="trial_price" jdbcType="VARCHAR" property="trialPrice" />
    <result column="positive_price" jdbcType="VARCHAR" property="positivePrice" />
    <result column="area" jdbcType="VARCHAR" property="area" />
    <result column="brand" jdbcType="VARCHAR" property="brand" />
    <result column="sell_point" jdbcType="VARCHAR" property="sellPoint" />
    <result column="age" jdbcType="VARCHAR" property="age" />
    <result column="teach_channel" jdbcType="VARCHAR" property="teachChannel" />
    <result column="teach_type" jdbcType="VARCHAR" property="teachType" />
    <result column="trial_class" jdbcType="VARCHAR" property="trialClass" />
    <result column="main_img_url" jdbcType="VARCHAR" property="mainImgUrl" />
    <result column="sub_img_url" jdbcType="VARCHAR" property="subImgUrl" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="course_video_url" jdbcType="VARCHAR" property="courseVideoUrl" />
    <result column="backup_course_video_url" jdbcType="VARCHAR" property="backupCourseVideoUrl" />
    <result column="h5_landing_page_url" jdbcType="VARCHAR" property="h5LandingPageUrl" />
    <result column="pc_landing_page_url" jdbcType="VARCHAR" property="pcLandingPageUrl" />
    <result column="ios_landing_page_url" jdbcType="VARCHAR" property="iosLandingPageUrl" />
    <result column="android_landing_page_url" jdbcType="VARCHAR" property="androidLandingPageUrl" />
    <result column="ios_up_ulink_url" jdbcType="VARCHAR" property="iosUpUlinkUrl" />
    <result column="first_category_code" jdbcType="BIGINT" property="firstCategoryCode" />
    <result column="second_category_code" jdbcType="BIGINT" property="secondCategoryCode" />
    <result column="third_category_code" jdbcType="BIGINT" property="thirdCategoryCode" />
    <result column="spu_code" jdbcType="BIGINT" property="spuCode" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, account_id, library_id, ad_product_id, biz_status, name, trial_price, positive_price, 
    area, brand, sell_point, age, teach_channel, teach_type, trial_class, main_img_url, 
    sub_img_url, remark, course_video_url, backup_course_video_url, h5_landing_page_url, 
    pc_landing_page_url, ios_landing_page_url, android_landing_page_url, ios_up_ulink_url, 
    first_category_code, second_category_code, third_category_code, spu_code, is_deleted, 
    ctime, mtime
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.adp.cpc.po.ad.AdProductEducationPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from ad_product_education
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ad_product_education
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from ad_product_education
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.adp.cpc.po.ad.AdProductEducationPoExample">
    delete from ad_product_education
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.adp.cpc.po.ad.AdProductEducationPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ad_product_education (account_id, library_id, ad_product_id, 
      biz_status, name, trial_price, 
      positive_price, area, brand, 
      sell_point, age, teach_channel, 
      teach_type, trial_class, main_img_url, 
      sub_img_url, remark, course_video_url, 
      backup_course_video_url, h5_landing_page_url, 
      pc_landing_page_url, ios_landing_page_url, android_landing_page_url, 
      ios_up_ulink_url, first_category_code, second_category_code, 
      third_category_code, spu_code, is_deleted, 
      ctime, mtime)
    values (#{accountId,jdbcType=INTEGER}, #{libraryId,jdbcType=BIGINT}, #{adProductId,jdbcType=BIGINT}, 
      #{bizStatus,jdbcType=TINYINT}, #{name,jdbcType=VARCHAR}, #{trialPrice,jdbcType=VARCHAR}, 
      #{positivePrice,jdbcType=VARCHAR}, #{area,jdbcType=VARCHAR}, #{brand,jdbcType=VARCHAR}, 
      #{sellPoint,jdbcType=VARCHAR}, #{age,jdbcType=VARCHAR}, #{teachChannel,jdbcType=VARCHAR}, 
      #{teachType,jdbcType=VARCHAR}, #{trialClass,jdbcType=VARCHAR}, #{mainImgUrl,jdbcType=VARCHAR}, 
      #{subImgUrl,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{courseVideoUrl,jdbcType=VARCHAR}, 
      #{backupCourseVideoUrl,jdbcType=VARCHAR}, #{h5LandingPageUrl,jdbcType=VARCHAR}, 
      #{pcLandingPageUrl,jdbcType=VARCHAR}, #{iosLandingPageUrl,jdbcType=VARCHAR}, #{androidLandingPageUrl,jdbcType=VARCHAR}, 
      #{iosUpUlinkUrl,jdbcType=VARCHAR}, #{firstCategoryCode,jdbcType=BIGINT}, #{secondCategoryCode,jdbcType=BIGINT}, 
      #{thirdCategoryCode,jdbcType=BIGINT}, #{spuCode,jdbcType=BIGINT}, #{isDeleted,jdbcType=TINYINT}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.adp.cpc.po.ad.AdProductEducationPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ad_product_education
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        account_id,
      </if>
      <if test="libraryId != null">
        library_id,
      </if>
      <if test="adProductId != null">
        ad_product_id,
      </if>
      <if test="bizStatus != null">
        biz_status,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="trialPrice != null">
        trial_price,
      </if>
      <if test="positivePrice != null">
        positive_price,
      </if>
      <if test="area != null">
        area,
      </if>
      <if test="brand != null">
        brand,
      </if>
      <if test="sellPoint != null">
        sell_point,
      </if>
      <if test="age != null">
        age,
      </if>
      <if test="teachChannel != null">
        teach_channel,
      </if>
      <if test="teachType != null">
        teach_type,
      </if>
      <if test="trialClass != null">
        trial_class,
      </if>
      <if test="mainImgUrl != null">
        main_img_url,
      </if>
      <if test="subImgUrl != null">
        sub_img_url,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="courseVideoUrl != null">
        course_video_url,
      </if>
      <if test="backupCourseVideoUrl != null">
        backup_course_video_url,
      </if>
      <if test="h5LandingPageUrl != null">
        h5_landing_page_url,
      </if>
      <if test="pcLandingPageUrl != null">
        pc_landing_page_url,
      </if>
      <if test="iosLandingPageUrl != null">
        ios_landing_page_url,
      </if>
      <if test="androidLandingPageUrl != null">
        android_landing_page_url,
      </if>
      <if test="iosUpUlinkUrl != null">
        ios_up_ulink_url,
      </if>
      <if test="firstCategoryCode != null">
        first_category_code,
      </if>
      <if test="secondCategoryCode != null">
        second_category_code,
      </if>
      <if test="thirdCategoryCode != null">
        third_category_code,
      </if>
      <if test="spuCode != null">
        spu_code,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="libraryId != null">
        #{libraryId,jdbcType=BIGINT},
      </if>
      <if test="adProductId != null">
        #{adProductId,jdbcType=BIGINT},
      </if>
      <if test="bizStatus != null">
        #{bizStatus,jdbcType=TINYINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="trialPrice != null">
        #{trialPrice,jdbcType=VARCHAR},
      </if>
      <if test="positivePrice != null">
        #{positivePrice,jdbcType=VARCHAR},
      </if>
      <if test="area != null">
        #{area,jdbcType=VARCHAR},
      </if>
      <if test="brand != null">
        #{brand,jdbcType=VARCHAR},
      </if>
      <if test="sellPoint != null">
        #{sellPoint,jdbcType=VARCHAR},
      </if>
      <if test="age != null">
        #{age,jdbcType=VARCHAR},
      </if>
      <if test="teachChannel != null">
        #{teachChannel,jdbcType=VARCHAR},
      </if>
      <if test="teachType != null">
        #{teachType,jdbcType=VARCHAR},
      </if>
      <if test="trialClass != null">
        #{trialClass,jdbcType=VARCHAR},
      </if>
      <if test="mainImgUrl != null">
        #{mainImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="subImgUrl != null">
        #{subImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="courseVideoUrl != null">
        #{courseVideoUrl,jdbcType=VARCHAR},
      </if>
      <if test="backupCourseVideoUrl != null">
        #{backupCourseVideoUrl,jdbcType=VARCHAR},
      </if>
      <if test="h5LandingPageUrl != null">
        #{h5LandingPageUrl,jdbcType=VARCHAR},
      </if>
      <if test="pcLandingPageUrl != null">
        #{pcLandingPageUrl,jdbcType=VARCHAR},
      </if>
      <if test="iosLandingPageUrl != null">
        #{iosLandingPageUrl,jdbcType=VARCHAR},
      </if>
      <if test="androidLandingPageUrl != null">
        #{androidLandingPageUrl,jdbcType=VARCHAR},
      </if>
      <if test="iosUpUlinkUrl != null">
        #{iosUpUlinkUrl,jdbcType=VARCHAR},
      </if>
      <if test="firstCategoryCode != null">
        #{firstCategoryCode,jdbcType=BIGINT},
      </if>
      <if test="secondCategoryCode != null">
        #{secondCategoryCode,jdbcType=BIGINT},
      </if>
      <if test="thirdCategoryCode != null">
        #{thirdCategoryCode,jdbcType=BIGINT},
      </if>
      <if test="spuCode != null">
        #{spuCode,jdbcType=BIGINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.adp.cpc.po.ad.AdProductEducationPoExample" resultType="java.lang.Long">
    select count(*) from ad_product_education
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update ad_product_education
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.accountId != null">
        account_id = #{record.accountId,jdbcType=INTEGER},
      </if>
      <if test="record.libraryId != null">
        library_id = #{record.libraryId,jdbcType=BIGINT},
      </if>
      <if test="record.adProductId != null">
        ad_product_id = #{record.adProductId,jdbcType=BIGINT},
      </if>
      <if test="record.bizStatus != null">
        biz_status = #{record.bizStatus,jdbcType=TINYINT},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.trialPrice != null">
        trial_price = #{record.trialPrice,jdbcType=VARCHAR},
      </if>
      <if test="record.positivePrice != null">
        positive_price = #{record.positivePrice,jdbcType=VARCHAR},
      </if>
      <if test="record.area != null">
        area = #{record.area,jdbcType=VARCHAR},
      </if>
      <if test="record.brand != null">
        brand = #{record.brand,jdbcType=VARCHAR},
      </if>
      <if test="record.sellPoint != null">
        sell_point = #{record.sellPoint,jdbcType=VARCHAR},
      </if>
      <if test="record.age != null">
        age = #{record.age,jdbcType=VARCHAR},
      </if>
      <if test="record.teachChannel != null">
        teach_channel = #{record.teachChannel,jdbcType=VARCHAR},
      </if>
      <if test="record.teachType != null">
        teach_type = #{record.teachType,jdbcType=VARCHAR},
      </if>
      <if test="record.trialClass != null">
        trial_class = #{record.trialClass,jdbcType=VARCHAR},
      </if>
      <if test="record.mainImgUrl != null">
        main_img_url = #{record.mainImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.subImgUrl != null">
        sub_img_url = #{record.subImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.courseVideoUrl != null">
        course_video_url = #{record.courseVideoUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.backupCourseVideoUrl != null">
        backup_course_video_url = #{record.backupCourseVideoUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.h5LandingPageUrl != null">
        h5_landing_page_url = #{record.h5LandingPageUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.pcLandingPageUrl != null">
        pc_landing_page_url = #{record.pcLandingPageUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.iosLandingPageUrl != null">
        ios_landing_page_url = #{record.iosLandingPageUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.androidLandingPageUrl != null">
        android_landing_page_url = #{record.androidLandingPageUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.iosUpUlinkUrl != null">
        ios_up_ulink_url = #{record.iosUpUlinkUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.firstCategoryCode != null">
        first_category_code = #{record.firstCategoryCode,jdbcType=BIGINT},
      </if>
      <if test="record.secondCategoryCode != null">
        second_category_code = #{record.secondCategoryCode,jdbcType=BIGINT},
      </if>
      <if test="record.thirdCategoryCode != null">
        third_category_code = #{record.thirdCategoryCode,jdbcType=BIGINT},
      </if>
      <if test="record.spuCode != null">
        spu_code = #{record.spuCode,jdbcType=BIGINT},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update ad_product_education
    set id = #{record.id,jdbcType=BIGINT},
      account_id = #{record.accountId,jdbcType=INTEGER},
      library_id = #{record.libraryId,jdbcType=BIGINT},
      ad_product_id = #{record.adProductId,jdbcType=BIGINT},
      biz_status = #{record.bizStatus,jdbcType=TINYINT},
      name = #{record.name,jdbcType=VARCHAR},
      trial_price = #{record.trialPrice,jdbcType=VARCHAR},
      positive_price = #{record.positivePrice,jdbcType=VARCHAR},
      area = #{record.area,jdbcType=VARCHAR},
      brand = #{record.brand,jdbcType=VARCHAR},
      sell_point = #{record.sellPoint,jdbcType=VARCHAR},
      age = #{record.age,jdbcType=VARCHAR},
      teach_channel = #{record.teachChannel,jdbcType=VARCHAR},
      teach_type = #{record.teachType,jdbcType=VARCHAR},
      trial_class = #{record.trialClass,jdbcType=VARCHAR},
      main_img_url = #{record.mainImgUrl,jdbcType=VARCHAR},
      sub_img_url = #{record.subImgUrl,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      course_video_url = #{record.courseVideoUrl,jdbcType=VARCHAR},
      backup_course_video_url = #{record.backupCourseVideoUrl,jdbcType=VARCHAR},
      h5_landing_page_url = #{record.h5LandingPageUrl,jdbcType=VARCHAR},
      pc_landing_page_url = #{record.pcLandingPageUrl,jdbcType=VARCHAR},
      ios_landing_page_url = #{record.iosLandingPageUrl,jdbcType=VARCHAR},
      android_landing_page_url = #{record.androidLandingPageUrl,jdbcType=VARCHAR},
      ios_up_ulink_url = #{record.iosUpUlinkUrl,jdbcType=VARCHAR},
      first_category_code = #{record.firstCategoryCode,jdbcType=BIGINT},
      second_category_code = #{record.secondCategoryCode,jdbcType=BIGINT},
      third_category_code = #{record.thirdCategoryCode,jdbcType=BIGINT},
      spu_code = #{record.spuCode,jdbcType=BIGINT},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.adp.cpc.po.ad.AdProductEducationPo">
    update ad_product_education
    <set>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=INTEGER},
      </if>
      <if test="libraryId != null">
        library_id = #{libraryId,jdbcType=BIGINT},
      </if>
      <if test="adProductId != null">
        ad_product_id = #{adProductId,jdbcType=BIGINT},
      </if>
      <if test="bizStatus != null">
        biz_status = #{bizStatus,jdbcType=TINYINT},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="trialPrice != null">
        trial_price = #{trialPrice,jdbcType=VARCHAR},
      </if>
      <if test="positivePrice != null">
        positive_price = #{positivePrice,jdbcType=VARCHAR},
      </if>
      <if test="area != null">
        area = #{area,jdbcType=VARCHAR},
      </if>
      <if test="brand != null">
        brand = #{brand,jdbcType=VARCHAR},
      </if>
      <if test="sellPoint != null">
        sell_point = #{sellPoint,jdbcType=VARCHAR},
      </if>
      <if test="age != null">
        age = #{age,jdbcType=VARCHAR},
      </if>
      <if test="teachChannel != null">
        teach_channel = #{teachChannel,jdbcType=VARCHAR},
      </if>
      <if test="teachType != null">
        teach_type = #{teachType,jdbcType=VARCHAR},
      </if>
      <if test="trialClass != null">
        trial_class = #{trialClass,jdbcType=VARCHAR},
      </if>
      <if test="mainImgUrl != null">
        main_img_url = #{mainImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="subImgUrl != null">
        sub_img_url = #{subImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="courseVideoUrl != null">
        course_video_url = #{courseVideoUrl,jdbcType=VARCHAR},
      </if>
      <if test="backupCourseVideoUrl != null">
        backup_course_video_url = #{backupCourseVideoUrl,jdbcType=VARCHAR},
      </if>
      <if test="h5LandingPageUrl != null">
        h5_landing_page_url = #{h5LandingPageUrl,jdbcType=VARCHAR},
      </if>
      <if test="pcLandingPageUrl != null">
        pc_landing_page_url = #{pcLandingPageUrl,jdbcType=VARCHAR},
      </if>
      <if test="iosLandingPageUrl != null">
        ios_landing_page_url = #{iosLandingPageUrl,jdbcType=VARCHAR},
      </if>
      <if test="androidLandingPageUrl != null">
        android_landing_page_url = #{androidLandingPageUrl,jdbcType=VARCHAR},
      </if>
      <if test="iosUpUlinkUrl != null">
        ios_up_ulink_url = #{iosUpUlinkUrl,jdbcType=VARCHAR},
      </if>
      <if test="firstCategoryCode != null">
        first_category_code = #{firstCategoryCode,jdbcType=BIGINT},
      </if>
      <if test="secondCategoryCode != null">
        second_category_code = #{secondCategoryCode,jdbcType=BIGINT},
      </if>
      <if test="thirdCategoryCode != null">
        third_category_code = #{thirdCategoryCode,jdbcType=BIGINT},
      </if>
      <if test="spuCode != null">
        spu_code = #{spuCode,jdbcType=BIGINT},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.adp.cpc.po.ad.AdProductEducationPo">
    update ad_product_education
    set account_id = #{accountId,jdbcType=INTEGER},
      library_id = #{libraryId,jdbcType=BIGINT},
      ad_product_id = #{adProductId,jdbcType=BIGINT},
      biz_status = #{bizStatus,jdbcType=TINYINT},
      name = #{name,jdbcType=VARCHAR},
      trial_price = #{trialPrice,jdbcType=VARCHAR},
      positive_price = #{positivePrice,jdbcType=VARCHAR},
      area = #{area,jdbcType=VARCHAR},
      brand = #{brand,jdbcType=VARCHAR},
      sell_point = #{sellPoint,jdbcType=VARCHAR},
      age = #{age,jdbcType=VARCHAR},
      teach_channel = #{teachChannel,jdbcType=VARCHAR},
      teach_type = #{teachType,jdbcType=VARCHAR},
      trial_class = #{trialClass,jdbcType=VARCHAR},
      main_img_url = #{mainImgUrl,jdbcType=VARCHAR},
      sub_img_url = #{subImgUrl,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      course_video_url = #{courseVideoUrl,jdbcType=VARCHAR},
      backup_course_video_url = #{backupCourseVideoUrl,jdbcType=VARCHAR},
      h5_landing_page_url = #{h5LandingPageUrl,jdbcType=VARCHAR},
      pc_landing_page_url = #{pcLandingPageUrl,jdbcType=VARCHAR},
      ios_landing_page_url = #{iosLandingPageUrl,jdbcType=VARCHAR},
      android_landing_page_url = #{androidLandingPageUrl,jdbcType=VARCHAR},
      ios_up_ulink_url = #{iosUpUlinkUrl,jdbcType=VARCHAR},
      first_category_code = #{firstCategoryCode,jdbcType=BIGINT},
      second_category_code = #{secondCategoryCode,jdbcType=BIGINT},
      third_category_code = #{thirdCategoryCode,jdbcType=BIGINT},
      spu_code = #{spuCode,jdbcType=BIGINT},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.adp.cpc.po.ad.AdProductEducationPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ad_product_education (account_id, library_id, ad_product_id, 
      biz_status, name, trial_price, 
      positive_price, area, brand, 
      sell_point, age, teach_channel, 
      teach_type, trial_class, main_img_url, 
      sub_img_url, remark, course_video_url, 
      backup_course_video_url, h5_landing_page_url, 
      pc_landing_page_url, ios_landing_page_url, android_landing_page_url, 
      ios_up_ulink_url, first_category_code, second_category_code, 
      third_category_code, spu_code, is_deleted, 
      ctime, mtime)
    values (#{accountId,jdbcType=INTEGER}, #{libraryId,jdbcType=BIGINT}, #{adProductId,jdbcType=BIGINT}, 
      #{bizStatus,jdbcType=TINYINT}, #{name,jdbcType=VARCHAR}, #{trialPrice,jdbcType=VARCHAR}, 
      #{positivePrice,jdbcType=VARCHAR}, #{area,jdbcType=VARCHAR}, #{brand,jdbcType=VARCHAR}, 
      #{sellPoint,jdbcType=VARCHAR}, #{age,jdbcType=VARCHAR}, #{teachChannel,jdbcType=VARCHAR}, 
      #{teachType,jdbcType=VARCHAR}, #{trialClass,jdbcType=VARCHAR}, #{mainImgUrl,jdbcType=VARCHAR}, 
      #{subImgUrl,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{courseVideoUrl,jdbcType=VARCHAR}, 
      #{backupCourseVideoUrl,jdbcType=VARCHAR}, #{h5LandingPageUrl,jdbcType=VARCHAR}, 
      #{pcLandingPageUrl,jdbcType=VARCHAR}, #{iosLandingPageUrl,jdbcType=VARCHAR}, #{androidLandingPageUrl,jdbcType=VARCHAR}, 
      #{iosUpUlinkUrl,jdbcType=VARCHAR}, #{firstCategoryCode,jdbcType=BIGINT}, #{secondCategoryCode,jdbcType=BIGINT}, 
      #{thirdCategoryCode,jdbcType=BIGINT}, #{spuCode,jdbcType=BIGINT}, #{isDeleted,jdbcType=TINYINT}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      account_id = values(account_id),
      library_id = values(library_id),
      ad_product_id = values(ad_product_id),
      biz_status = values(biz_status),
      name = values(name),
      trial_price = values(trial_price),
      positive_price = values(positive_price),
      area = values(area),
      brand = values(brand),
      sell_point = values(sell_point),
      age = values(age),
      teach_channel = values(teach_channel),
      teach_type = values(teach_type),
      trial_class = values(trial_class),
      main_img_url = values(main_img_url),
      sub_img_url = values(sub_img_url),
      remark = values(remark),
      course_video_url = values(course_video_url),
      backup_course_video_url = values(backup_course_video_url),
      h5_landing_page_url = values(h5_landing_page_url),
      pc_landing_page_url = values(pc_landing_page_url),
      ios_landing_page_url = values(ios_landing_page_url),
      android_landing_page_url = values(android_landing_page_url),
      ios_up_ulink_url = values(ios_up_ulink_url),
      first_category_code = values(first_category_code),
      second_category_code = values(second_category_code),
      third_category_code = values(third_category_code),
      spu_code = values(spu_code),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      ad_product_education
      (account_id,library_id,ad_product_id,biz_status,name,trial_price,positive_price,area,brand,sell_point,age,teach_channel,teach_type,trial_class,main_img_url,sub_img_url,remark,course_video_url,backup_course_video_url,h5_landing_page_url,pc_landing_page_url,ios_landing_page_url,android_landing_page_url,ios_up_ulink_url,first_category_code,second_category_code,third_category_code,spu_code,is_deleted,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.accountId,jdbcType=INTEGER},
        #{item.libraryId,jdbcType=BIGINT},
        #{item.adProductId,jdbcType=BIGINT},
        #{item.bizStatus,jdbcType=TINYINT},
        #{item.name,jdbcType=VARCHAR},
        #{item.trialPrice,jdbcType=VARCHAR},
        #{item.positivePrice,jdbcType=VARCHAR},
        #{item.area,jdbcType=VARCHAR},
        #{item.brand,jdbcType=VARCHAR},
        #{item.sellPoint,jdbcType=VARCHAR},
        #{item.age,jdbcType=VARCHAR},
        #{item.teachChannel,jdbcType=VARCHAR},
        #{item.teachType,jdbcType=VARCHAR},
        #{item.trialClass,jdbcType=VARCHAR},
        #{item.mainImgUrl,jdbcType=VARCHAR},
        #{item.subImgUrl,jdbcType=VARCHAR},
        #{item.remark,jdbcType=VARCHAR},
        #{item.courseVideoUrl,jdbcType=VARCHAR},
        #{item.backupCourseVideoUrl,jdbcType=VARCHAR},
        #{item.h5LandingPageUrl,jdbcType=VARCHAR},
        #{item.pcLandingPageUrl,jdbcType=VARCHAR},
        #{item.iosLandingPageUrl,jdbcType=VARCHAR},
        #{item.androidLandingPageUrl,jdbcType=VARCHAR},
        #{item.iosUpUlinkUrl,jdbcType=VARCHAR},
        #{item.firstCategoryCode,jdbcType=BIGINT},
        #{item.secondCategoryCode,jdbcType=BIGINT},
        #{item.thirdCategoryCode,jdbcType=BIGINT},
        #{item.spuCode,jdbcType=BIGINT},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      ad_product_education
      (account_id,library_id,ad_product_id,biz_status,name,trial_price,positive_price,area,brand,sell_point,age,teach_channel,teach_type,trial_class,main_img_url,sub_img_url,remark,course_video_url,backup_course_video_url,h5_landing_page_url,pc_landing_page_url,ios_landing_page_url,android_landing_page_url,ios_up_ulink_url,first_category_code,second_category_code,third_category_code,spu_code,is_deleted,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.accountId,jdbcType=INTEGER},
        #{item.libraryId,jdbcType=BIGINT},
        #{item.adProductId,jdbcType=BIGINT},
        #{item.bizStatus,jdbcType=TINYINT},
        #{item.name,jdbcType=VARCHAR},
        #{item.trialPrice,jdbcType=VARCHAR},
        #{item.positivePrice,jdbcType=VARCHAR},
        #{item.area,jdbcType=VARCHAR},
        #{item.brand,jdbcType=VARCHAR},
        #{item.sellPoint,jdbcType=VARCHAR},
        #{item.age,jdbcType=VARCHAR},
        #{item.teachChannel,jdbcType=VARCHAR},
        #{item.teachType,jdbcType=VARCHAR},
        #{item.trialClass,jdbcType=VARCHAR},
        #{item.mainImgUrl,jdbcType=VARCHAR},
        #{item.subImgUrl,jdbcType=VARCHAR},
        #{item.remark,jdbcType=VARCHAR},
        #{item.courseVideoUrl,jdbcType=VARCHAR},
        #{item.backupCourseVideoUrl,jdbcType=VARCHAR},
        #{item.h5LandingPageUrl,jdbcType=VARCHAR},
        #{item.pcLandingPageUrl,jdbcType=VARCHAR},
        #{item.iosLandingPageUrl,jdbcType=VARCHAR},
        #{item.androidLandingPageUrl,jdbcType=VARCHAR},
        #{item.iosUpUlinkUrl,jdbcType=VARCHAR},
        #{item.firstCategoryCode,jdbcType=BIGINT},
        #{item.secondCategoryCode,jdbcType=BIGINT},
        #{item.thirdCategoryCode,jdbcType=BIGINT},
        #{item.spuCode,jdbcType=BIGINT},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      account_id = values(account_id),
      library_id = values(library_id),
      ad_product_id = values(ad_product_id),
      biz_status = values(biz_status),
      name = values(name),
      trial_price = values(trial_price),
      positive_price = values(positive_price),
      area = values(area),
      brand = values(brand),
      sell_point = values(sell_point),
      age = values(age),
      teach_channel = values(teach_channel),
      teach_type = values(teach_type),
      trial_class = values(trial_class),
      main_img_url = values(main_img_url),
      sub_img_url = values(sub_img_url),
      remark = values(remark),
      course_video_url = values(course_video_url),
      backup_course_video_url = values(backup_course_video_url),
      h5_landing_page_url = values(h5_landing_page_url),
      pc_landing_page_url = values(pc_landing_page_url),
      ios_landing_page_url = values(ios_landing_page_url),
      android_landing_page_url = values(android_landing_page_url),
      ios_up_ulink_url = values(ios_up_ulink_url),
      first_category_code = values(first_category_code),
      second_category_code = values(second_category_code),
      third_category_code = values(third_category_code),
      spu_code = values(spu_code),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.adp.cpc.po.ad.AdProductEducationPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ad_product_education
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        account_id,
      </if>
      <if test="libraryId != null">
        library_id,
      </if>
      <if test="adProductId != null">
        ad_product_id,
      </if>
      <if test="bizStatus != null">
        biz_status,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="trialPrice != null">
        trial_price,
      </if>
      <if test="positivePrice != null">
        positive_price,
      </if>
      <if test="area != null">
        area,
      </if>
      <if test="brand != null">
        brand,
      </if>
      <if test="sellPoint != null">
        sell_point,
      </if>
      <if test="age != null">
        age,
      </if>
      <if test="teachChannel != null">
        teach_channel,
      </if>
      <if test="teachType != null">
        teach_type,
      </if>
      <if test="trialClass != null">
        trial_class,
      </if>
      <if test="mainImgUrl != null">
        main_img_url,
      </if>
      <if test="subImgUrl != null">
        sub_img_url,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="courseVideoUrl != null">
        course_video_url,
      </if>
      <if test="backupCourseVideoUrl != null">
        backup_course_video_url,
      </if>
      <if test="h5LandingPageUrl != null">
        h5_landing_page_url,
      </if>
      <if test="pcLandingPageUrl != null">
        pc_landing_page_url,
      </if>
      <if test="iosLandingPageUrl != null">
        ios_landing_page_url,
      </if>
      <if test="androidLandingPageUrl != null">
        android_landing_page_url,
      </if>
      <if test="iosUpUlinkUrl != null">
        ios_up_ulink_url,
      </if>
      <if test="firstCategoryCode != null">
        first_category_code,
      </if>
      <if test="secondCategoryCode != null">
        second_category_code,
      </if>
      <if test="thirdCategoryCode != null">
        third_category_code,
      </if>
      <if test="spuCode != null">
        spu_code,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="libraryId != null">
        #{libraryId,jdbcType=BIGINT},
      </if>
      <if test="adProductId != null">
        #{adProductId,jdbcType=BIGINT},
      </if>
      <if test="bizStatus != null">
        #{bizStatus,jdbcType=TINYINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="trialPrice != null">
        #{trialPrice,jdbcType=VARCHAR},
      </if>
      <if test="positivePrice != null">
        #{positivePrice,jdbcType=VARCHAR},
      </if>
      <if test="area != null">
        #{area,jdbcType=VARCHAR},
      </if>
      <if test="brand != null">
        #{brand,jdbcType=VARCHAR},
      </if>
      <if test="sellPoint != null">
        #{sellPoint,jdbcType=VARCHAR},
      </if>
      <if test="age != null">
        #{age,jdbcType=VARCHAR},
      </if>
      <if test="teachChannel != null">
        #{teachChannel,jdbcType=VARCHAR},
      </if>
      <if test="teachType != null">
        #{teachType,jdbcType=VARCHAR},
      </if>
      <if test="trialClass != null">
        #{trialClass,jdbcType=VARCHAR},
      </if>
      <if test="mainImgUrl != null">
        #{mainImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="subImgUrl != null">
        #{subImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="courseVideoUrl != null">
        #{courseVideoUrl,jdbcType=VARCHAR},
      </if>
      <if test="backupCourseVideoUrl != null">
        #{backupCourseVideoUrl,jdbcType=VARCHAR},
      </if>
      <if test="h5LandingPageUrl != null">
        #{h5LandingPageUrl,jdbcType=VARCHAR},
      </if>
      <if test="pcLandingPageUrl != null">
        #{pcLandingPageUrl,jdbcType=VARCHAR},
      </if>
      <if test="iosLandingPageUrl != null">
        #{iosLandingPageUrl,jdbcType=VARCHAR},
      </if>
      <if test="androidLandingPageUrl != null">
        #{androidLandingPageUrl,jdbcType=VARCHAR},
      </if>
      <if test="iosUpUlinkUrl != null">
        #{iosUpUlinkUrl,jdbcType=VARCHAR},
      </if>
      <if test="firstCategoryCode != null">
        #{firstCategoryCode,jdbcType=BIGINT},
      </if>
      <if test="secondCategoryCode != null">
        #{secondCategoryCode,jdbcType=BIGINT},
      </if>
      <if test="thirdCategoryCode != null">
        #{thirdCategoryCode,jdbcType=BIGINT},
      </if>
      <if test="spuCode != null">
        #{spuCode,jdbcType=BIGINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="accountId != null">
        account_id = values(account_id),
      </if>
      <if test="libraryId != null">
        library_id = values(library_id),
      </if>
      <if test="adProductId != null">
        ad_product_id = values(ad_product_id),
      </if>
      <if test="bizStatus != null">
        biz_status = values(biz_status),
      </if>
      <if test="name != null">
        name = values(name),
      </if>
      <if test="trialPrice != null">
        trial_price = values(trial_price),
      </if>
      <if test="positivePrice != null">
        positive_price = values(positive_price),
      </if>
      <if test="area != null">
        area = values(area),
      </if>
      <if test="brand != null">
        brand = values(brand),
      </if>
      <if test="sellPoint != null">
        sell_point = values(sell_point),
      </if>
      <if test="age != null">
        age = values(age),
      </if>
      <if test="teachChannel != null">
        teach_channel = values(teach_channel),
      </if>
      <if test="teachType != null">
        teach_type = values(teach_type),
      </if>
      <if test="trialClass != null">
        trial_class = values(trial_class),
      </if>
      <if test="mainImgUrl != null">
        main_img_url = values(main_img_url),
      </if>
      <if test="subImgUrl != null">
        sub_img_url = values(sub_img_url),
      </if>
      <if test="remark != null">
        remark = values(remark),
      </if>
      <if test="courseVideoUrl != null">
        course_video_url = values(course_video_url),
      </if>
      <if test="backupCourseVideoUrl != null">
        backup_course_video_url = values(backup_course_video_url),
      </if>
      <if test="h5LandingPageUrl != null">
        h5_landing_page_url = values(h5_landing_page_url),
      </if>
      <if test="pcLandingPageUrl != null">
        pc_landing_page_url = values(pc_landing_page_url),
      </if>
      <if test="iosLandingPageUrl != null">
        ios_landing_page_url = values(ios_landing_page_url),
      </if>
      <if test="androidLandingPageUrl != null">
        android_landing_page_url = values(android_landing_page_url),
      </if>
      <if test="iosUpUlinkUrl != null">
        ios_up_ulink_url = values(ios_up_ulink_url),
      </if>
      <if test="firstCategoryCode != null">
        first_category_code = values(first_category_code),
      </if>
      <if test="secondCategoryCode != null">
        second_category_code = values(second_category_code),
      </if>
      <if test="thirdCategoryCode != null">
        third_category_code = values(third_category_code),
      </if>
      <if test="spuCode != null">
        spu_code = values(spu_code),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
    </trim>
  </insert>
</mapper>