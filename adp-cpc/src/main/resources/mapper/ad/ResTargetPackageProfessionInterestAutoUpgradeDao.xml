<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.adp.cpc.dao.ad.ResTargetPackageProfessionInterestAutoUpgradeDao">
  <resultMap id="BaseResultMap" type="com.bilibili.adp.cpc.po.ad.ResTargetPackageProfessionInterestAutoUpgradePo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="account_id" jdbcType="INTEGER" property="accountId" />
    <result column="target_package_id" jdbcType="INTEGER" property="targetPackageId" />
    <result column="interest_auto" jdbcType="TINYINT" property="interestAuto" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
  </resultMap>
  <resultMap id="AliasBaseResultMap" type="com.bilibili.adp.cpc.po.ad.ResTargetPackageProfessionInterestAutoUpgradePo">
    <id column="res_target_package_profession_interest_auto_upgrade_id" jdbcType="INTEGER" property="id" />
    <result column="res_target_package_profession_interest_auto_upgrade_account_id" jdbcType="INTEGER" property="accountId" />
    <result column="res_target_package_profession_interest_auto_upgrade_target_package_id" jdbcType="INTEGER" property="targetPackageId" />
    <result column="res_target_package_profession_interest_auto_upgrade_interest_auto" jdbcType="TINYINT" property="interestAuto" />
    <result column="res_target_package_profession_interest_auto_upgrade_ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="res_target_package_profession_interest_auto_upgrade_mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="res_target_package_profession_interest_auto_upgrade_is_deleted" jdbcType="TINYINT" property="isDeleted" />
  </resultMap>
  <sql id="Alias_Base_Column_List">
    ${alias}.id as res_target_package_profession_interest_auto_upgrade_id, ${alias}.account_id as res_target_package_profession_interest_auto_upgrade_account_id, 
    ${alias}.target_package_id as res_target_package_profession_interest_auto_upgrade_target_package_id, 
    ${alias}.interest_auto as res_target_package_profession_interest_auto_upgrade_interest_auto, 
    ${alias}.ctime as res_target_package_profession_interest_auto_upgrade_ctime, ${alias}.mtime as res_target_package_profession_interest_auto_upgrade_mtime, 
    ${alias}.is_deleted as res_target_package_profession_interest_auto_upgrade_is_deleted
  </sql>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, account_id, target_package_id, interest_auto, ctime, mtime, is_deleted
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.adp.cpc.po.ad.ResTargetPackageProfessionInterestAutoUpgradePoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from res_target_package_profession_interest_auto_upgrade
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from res_target_package_profession_interest_auto_upgrade
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from res_target_package_profession_interest_auto_upgrade
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.adp.cpc.po.ad.ResTargetPackageProfessionInterestAutoUpgradePoExample">
    delete from res_target_package_profession_interest_auto_upgrade
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.adp.cpc.po.ad.ResTargetPackageProfessionInterestAutoUpgradePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into res_target_package_profession_interest_auto_upgrade (account_id, target_package_id, interest_auto, 
      ctime, mtime, is_deleted
      )
    values (#{accountId,jdbcType=INTEGER}, #{targetPackageId,jdbcType=INTEGER}, #{interestAuto,jdbcType=TINYINT}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=TINYINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.adp.cpc.po.ad.ResTargetPackageProfessionInterestAutoUpgradePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into res_target_package_profession_interest_auto_upgrade
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        account_id,
      </if>
      <if test="targetPackageId != null">
        target_package_id,
      </if>
      <if test="interestAuto != null">
        interest_auto,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="targetPackageId != null">
        #{targetPackageId,jdbcType=INTEGER},
      </if>
      <if test="interestAuto != null">
        #{interestAuto,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.adp.cpc.po.ad.ResTargetPackageProfessionInterestAutoUpgradePoExample" resultType="java.lang.Long">
    select count(*) from res_target_package_profession_interest_auto_upgrade
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update res_target_package_profession_interest_auto_upgrade
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.accountId != null">
        account_id = #{record.accountId,jdbcType=INTEGER},
      </if>
      <if test="record.targetPackageId != null">
        target_package_id = #{record.targetPackageId,jdbcType=INTEGER},
      </if>
      <if test="record.interestAuto != null">
        interest_auto = #{record.interestAuto,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update res_target_package_profession_interest_auto_upgrade
    set id = #{record.id,jdbcType=INTEGER},
      account_id = #{record.accountId,jdbcType=INTEGER},
      target_package_id = #{record.targetPackageId,jdbcType=INTEGER},
      interest_auto = #{record.interestAuto,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.adp.cpc.po.ad.ResTargetPackageProfessionInterestAutoUpgradePo">
    update res_target_package_profession_interest_auto_upgrade
    <set>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=INTEGER},
      </if>
      <if test="targetPackageId != null">
        target_package_id = #{targetPackageId,jdbcType=INTEGER},
      </if>
      <if test="interestAuto != null">
        interest_auto = #{interestAuto,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.adp.cpc.po.ad.ResTargetPackageProfessionInterestAutoUpgradePo">
    update res_target_package_profession_interest_auto_upgrade
    set account_id = #{accountId,jdbcType=INTEGER},
      target_package_id = #{targetPackageId,jdbcType=INTEGER},
      interest_auto = #{interestAuto,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=TINYINT}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.adp.cpc.po.ad.ResTargetPackageProfessionInterestAutoUpgradePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into res_target_package_profession_interest_auto_upgrade (account_id, target_package_id, interest_auto, 
      ctime, mtime, is_deleted
      )
    values (#{accountId,jdbcType=INTEGER}, #{targetPackageId,jdbcType=INTEGER}, #{interestAuto,jdbcType=TINYINT}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=TINYINT}
      )
    <trim prefix="on duplicate key update" suffixOverrides=",">
      account_id = values(account_id),
      target_package_id = values(target_package_id),
      interest_auto = values(interest_auto),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      res_target_package_profession_interest_auto_upgrade
      (account_id,target_package_id,interest_auto,ctime,mtime,is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.accountId,jdbcType=INTEGER},
        #{item.targetPackageId,jdbcType=INTEGER},
        #{item.interestAuto,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      res_target_package_profession_interest_auto_upgrade
      (account_id,target_package_id,interest_auto,ctime,mtime,is_deleted)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.accountId,jdbcType=INTEGER},
        #{item.targetPackageId,jdbcType=INTEGER},
        #{item.interestAuto,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      account_id = values(account_id),
      target_package_id = values(target_package_id),
      interest_auto = values(interest_auto),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.adp.cpc.po.ad.ResTargetPackageProfessionInterestAutoUpgradePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into res_target_package_profession_interest_auto_upgrade
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        account_id,
      </if>
      <if test="targetPackageId != null">
        target_package_id,
      </if>
      <if test="interestAuto != null">
        interest_auto,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="targetPackageId != null">
        #{targetPackageId,jdbcType=INTEGER},
      </if>
      <if test="interestAuto != null">
        #{interestAuto,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="accountId != null">
        account_id = values(account_id),
      </if>
      <if test="targetPackageId != null">
        target_package_id = values(target_package_id),
      </if>
      <if test="interestAuto != null">
        interest_auto = values(interest_auto),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
    </trim>
  </insert>
</mapper>