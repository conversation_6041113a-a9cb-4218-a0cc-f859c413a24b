<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.adp.cpc.dao.ad.AdpLauCampaignNextdayBudgetDao">
  <resultMap id="BaseResultMap" type="com.bilibili.adp.cpc.po.ad.LauCampaignNextdayBudgetPo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="campaign_id" jdbcType="INTEGER" property="campaignId" />
    <result column="budget" jdbcType="BIGINT" property="budget" />
    <result column="budget_limit_type" jdbcType="TINYINT" property="budgetLimitType" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="budget_effective_time" jdbcType="TIMESTAMP" property="budgetEffectiveTime" />
    <result column="is_repeat" jdbcType="TINYINT" property="isRepeat" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, campaign_id, budget, budget_limit_type, is_deleted, ctime, mtime, budget_effective_time, 
    is_repeat
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.adp.cpc.po.ad.LauCampaignNextdayBudgetPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from lau_campaign_nextday_budget
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from lau_campaign_nextday_budget
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from lau_campaign_nextday_budget
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.adp.cpc.po.ad.LauCampaignNextdayBudgetPoExample">
    delete from lau_campaign_nextday_budget
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.adp.cpc.po.ad.LauCampaignNextdayBudgetPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_campaign_nextday_budget (campaign_id, budget, budget_limit_type, 
      is_deleted, ctime, mtime, 
      budget_effective_time, is_repeat)
    values (#{campaignId,jdbcType=INTEGER}, #{budget,jdbcType=BIGINT}, #{budgetLimitType,jdbcType=TINYINT}, 
      #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, 
      #{budgetEffectiveTime,jdbcType=TIMESTAMP}, #{isRepeat,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.adp.cpc.po.ad.LauCampaignNextdayBudgetPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_campaign_nextday_budget
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="campaignId != null">
        campaign_id,
      </if>
      <if test="budget != null">
        budget,
      </if>
      <if test="budgetLimitType != null">
        budget_limit_type,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="budgetEffectiveTime != null">
        budget_effective_time,
      </if>
      <if test="isRepeat != null">
        is_repeat,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="campaignId != null">
        #{campaignId,jdbcType=INTEGER},
      </if>
      <if test="budget != null">
        #{budget,jdbcType=BIGINT},
      </if>
      <if test="budgetLimitType != null">
        #{budgetLimitType,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="budgetEffectiveTime != null">
        #{budgetEffectiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isRepeat != null">
        #{isRepeat,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.adp.cpc.po.ad.LauCampaignNextdayBudgetPoExample" resultType="java.lang.Long">
    select count(*) from lau_campaign_nextday_budget
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update lau_campaign_nextday_budget
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.campaignId != null">
        campaign_id = #{record.campaignId,jdbcType=INTEGER},
      </if>
      <if test="record.budget != null">
        budget = #{record.budget,jdbcType=BIGINT},
      </if>
      <if test="record.budgetLimitType != null">
        budget_limit_type = #{record.budgetLimitType,jdbcType=TINYINT},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.budgetEffectiveTime != null">
        budget_effective_time = #{record.budgetEffectiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isRepeat != null">
        is_repeat = #{record.isRepeat,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update lau_campaign_nextday_budget
    set id = #{record.id,jdbcType=INTEGER},
      campaign_id = #{record.campaignId,jdbcType=INTEGER},
      budget = #{record.budget,jdbcType=BIGINT},
      budget_limit_type = #{record.budgetLimitType,jdbcType=TINYINT},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      budget_effective_time = #{record.budgetEffectiveTime,jdbcType=TIMESTAMP},
      is_repeat = #{record.isRepeat,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.adp.cpc.po.ad.LauCampaignNextdayBudgetPo">
    update lau_campaign_nextday_budget
    <set>
      <if test="campaignId != null">
        campaign_id = #{campaignId,jdbcType=INTEGER},
      </if>
      <if test="budget != null">
        budget = #{budget,jdbcType=BIGINT},
      </if>
      <if test="budgetLimitType != null">
        budget_limit_type = #{budgetLimitType,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="budgetEffectiveTime != null">
        budget_effective_time = #{budgetEffectiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isRepeat != null">
        is_repeat = #{isRepeat,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.adp.cpc.po.ad.LauCampaignNextdayBudgetPo">
    update lau_campaign_nextday_budget
    set campaign_id = #{campaignId,jdbcType=INTEGER},
      budget = #{budget,jdbcType=BIGINT},
      budget_limit_type = #{budgetLimitType,jdbcType=TINYINT},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      budget_effective_time = #{budgetEffectiveTime,jdbcType=TIMESTAMP},
      is_repeat = #{isRepeat,jdbcType=TINYINT}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.adp.cpc.po.ad.LauCampaignNextdayBudgetPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_campaign_nextday_budget (campaign_id, budget, budget_limit_type, 
      is_deleted, ctime, mtime, 
      budget_effective_time, is_repeat)
    values (#{campaignId,jdbcType=INTEGER}, #{budget,jdbcType=BIGINT}, #{budgetLimitType,jdbcType=TINYINT}, 
      #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, 
      #{budgetEffectiveTime,jdbcType=TIMESTAMP}, #{isRepeat,jdbcType=TINYINT})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      campaign_id = values(campaign_id),
      budget = values(budget),
      budget_limit_type = values(budget_limit_type),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      budget_effective_time = values(budget_effective_time),
      is_repeat = values(is_repeat),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      lau_campaign_nextday_budget
      (campaign_id,budget,budget_limit_type,is_deleted,ctime,mtime,budget_effective_time,is_repeat)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.campaignId,jdbcType=INTEGER},
        #{item.budget,jdbcType=BIGINT},
        #{item.budgetLimitType,jdbcType=TINYINT},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.budgetEffectiveTime,jdbcType=TIMESTAMP},
        #{item.isRepeat,jdbcType=TINYINT},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      lau_campaign_nextday_budget
      (campaign_id,budget,budget_limit_type,is_deleted,ctime,mtime,budget_effective_time,is_repeat)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.campaignId,jdbcType=INTEGER},
        #{item.budget,jdbcType=BIGINT},
        #{item.budgetLimitType,jdbcType=TINYINT},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.budgetEffectiveTime,jdbcType=TIMESTAMP},
        #{item.isRepeat,jdbcType=TINYINT},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      campaign_id = values(campaign_id),
      budget = values(budget),
      budget_limit_type = values(budget_limit_type),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      budget_effective_time = values(budget_effective_time),
      is_repeat = values(is_repeat),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.adp.cpc.po.ad.LauCampaignNextdayBudgetPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_campaign_nextday_budget
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="campaignId != null">
        campaign_id,
      </if>
      <if test="budget != null">
        budget,
      </if>
      <if test="budgetLimitType != null">
        budget_limit_type,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="budgetEffectiveTime != null">
        budget_effective_time,
      </if>
      <if test="isRepeat != null">
        is_repeat,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="campaignId != null">
        #{campaignId,jdbcType=INTEGER},
      </if>
      <if test="budget != null">
        #{budget,jdbcType=BIGINT},
      </if>
      <if test="budgetLimitType != null">
        #{budgetLimitType,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="budgetEffectiveTime != null">
        #{budgetEffectiveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isRepeat != null">
        #{isRepeat,jdbcType=TINYINT},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="campaignId != null">
        campaign_id = values(campaign_id),
      </if>
      <if test="budget != null">
        budget = values(budget),
      </if>
      <if test="budgetLimitType != null">
        budget_limit_type = values(budget_limit_type),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="budgetEffectiveTime != null">
        budget_effective_time = values(budget_effective_time),
      </if>
      <if test="isRepeat != null">
        is_repeat = values(is_repeat),
      </if>
    </trim>
  </insert>
</mapper>