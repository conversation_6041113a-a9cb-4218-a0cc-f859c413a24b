<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.adp.cpc.dao.ad.ResProfessionInterestCrowdsDao">
  <resultMap id="BaseResultMap" type="com.bilibili.adp.cpc.po.ad.ResProfessionInterestCrowdsPo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="crowd_id" jdbcType="INTEGER" property="crowdId" />
    <result column="level_one_id" jdbcType="INTEGER" property="levelOneId" />
    <result column="level_one_name" jdbcType="VARCHAR" property="levelOneName" />
    <result column="level_two_id" jdbcType="INTEGER" property="levelTwoId" />
    <result column="level_two_name" jdbcType="VARCHAR" property="levelTwoName" />
    <result column="level_three_id" jdbcType="INTEGER" property="levelThreeId" />
    <result column="level_three_name" jdbcType="VARCHAR" property="levelThreeName" />
    <result column="level_four_id" jdbcType="INTEGER" property="levelFourId" />
    <result column="level_four_name" jdbcType="VARCHAR" property="levelFourName" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
  </resultMap>
  <resultMap id="AliasBaseResultMap" type="com.bilibili.adp.cpc.po.ad.ResProfessionInterestCrowdsPo">
    <id column="res_profession_interest_crowds_id" jdbcType="INTEGER" property="id" />
    <result column="res_profession_interest_crowds_crowd_id" jdbcType="INTEGER" property="crowdId" />
    <result column="res_profession_interest_crowds_level_one_id" jdbcType="INTEGER" property="levelOneId" />
    <result column="res_profession_interest_crowds_level_one_name" jdbcType="VARCHAR" property="levelOneName" />
    <result column="res_profession_interest_crowds_level_two_id" jdbcType="INTEGER" property="levelTwoId" />
    <result column="res_profession_interest_crowds_level_two_name" jdbcType="VARCHAR" property="levelTwoName" />
    <result column="res_profession_interest_crowds_level_three_id" jdbcType="INTEGER" property="levelThreeId" />
    <result column="res_profession_interest_crowds_level_three_name" jdbcType="VARCHAR" property="levelThreeName" />
    <result column="res_profession_interest_crowds_level_four_id" jdbcType="INTEGER" property="levelFourId" />
    <result column="res_profession_interest_crowds_level_four_name" jdbcType="VARCHAR" property="levelFourName" />
    <result column="res_profession_interest_crowds_ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="res_profession_interest_crowds_mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="res_profession_interest_crowds_is_delete" jdbcType="TINYINT" property="isDelete" />
  </resultMap>
  <sql id="Alias_Base_Column_List">
    ${alias}.id as res_profession_interest_crowds_id, ${alias}.crowd_id as res_profession_interest_crowds_crowd_id, 
    ${alias}.level_one_id as res_profession_interest_crowds_level_one_id, ${alias}.level_one_name as res_profession_interest_crowds_level_one_name, 
    ${alias}.level_two_id as res_profession_interest_crowds_level_two_id, ${alias}.level_two_name as res_profession_interest_crowds_level_two_name, 
    ${alias}.level_three_id as res_profession_interest_crowds_level_three_id, ${alias}.level_three_name as res_profession_interest_crowds_level_three_name, 
    ${alias}.level_four_id as res_profession_interest_crowds_level_four_id, ${alias}.level_four_name as res_profession_interest_crowds_level_four_name, 
    ${alias}.ctime as res_profession_interest_crowds_ctime, ${alias}.mtime as res_profession_interest_crowds_mtime, 
    ${alias}.is_delete as res_profession_interest_crowds_is_delete
  </sql>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, crowd_id, level_one_id, level_one_name, level_two_id, level_two_name, level_three_id, 
    level_three_name, level_four_id, level_four_name, ctime, mtime, is_delete
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.adp.cpc.po.ad.ResProfessionInterestCrowdsPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from res_profession_interest_crowds
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from res_profession_interest_crowds
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from res_profession_interest_crowds
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.adp.cpc.po.ad.ResProfessionInterestCrowdsPoExample">
    delete from res_profession_interest_crowds
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.adp.cpc.po.ad.ResProfessionInterestCrowdsPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into res_profession_interest_crowds (crowd_id, level_one_id, level_one_name, 
      level_two_id, level_two_name, level_three_id, 
      level_three_name, level_four_id, level_four_name, 
      ctime, mtime, is_delete
      )
    values (#{crowdId,jdbcType=INTEGER}, #{levelOneId,jdbcType=INTEGER}, #{levelOneName,jdbcType=VARCHAR}, 
      #{levelTwoId,jdbcType=INTEGER}, #{levelTwoName,jdbcType=VARCHAR}, #{levelThreeId,jdbcType=INTEGER}, 
      #{levelThreeName,jdbcType=VARCHAR}, #{levelFourId,jdbcType=INTEGER}, #{levelFourName,jdbcType=VARCHAR}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{isDelete,jdbcType=TINYINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.adp.cpc.po.ad.ResProfessionInterestCrowdsPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into res_profession_interest_crowds
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="crowdId != null">
        crowd_id,
      </if>
      <if test="levelOneId != null">
        level_one_id,
      </if>
      <if test="levelOneName != null">
        level_one_name,
      </if>
      <if test="levelTwoId != null">
        level_two_id,
      </if>
      <if test="levelTwoName != null">
        level_two_name,
      </if>
      <if test="levelThreeId != null">
        level_three_id,
      </if>
      <if test="levelThreeName != null">
        level_three_name,
      </if>
      <if test="levelFourId != null">
        level_four_id,
      </if>
      <if test="levelFourName != null">
        level_four_name,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="crowdId != null">
        #{crowdId,jdbcType=INTEGER},
      </if>
      <if test="levelOneId != null">
        #{levelOneId,jdbcType=INTEGER},
      </if>
      <if test="levelOneName != null">
        #{levelOneName,jdbcType=VARCHAR},
      </if>
      <if test="levelTwoId != null">
        #{levelTwoId,jdbcType=INTEGER},
      </if>
      <if test="levelTwoName != null">
        #{levelTwoName,jdbcType=VARCHAR},
      </if>
      <if test="levelThreeId != null">
        #{levelThreeId,jdbcType=INTEGER},
      </if>
      <if test="levelThreeName != null">
        #{levelThreeName,jdbcType=VARCHAR},
      </if>
      <if test="levelFourId != null">
        #{levelFourId,jdbcType=INTEGER},
      </if>
      <if test="levelFourName != null">
        #{levelFourName,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.adp.cpc.po.ad.ResProfessionInterestCrowdsPoExample" resultType="java.lang.Long">
    select count(*) from res_profession_interest_crowds
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update res_profession_interest_crowds
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.crowdId != null">
        crowd_id = #{record.crowdId,jdbcType=INTEGER},
      </if>
      <if test="record.levelOneId != null">
        level_one_id = #{record.levelOneId,jdbcType=INTEGER},
      </if>
      <if test="record.levelOneName != null">
        level_one_name = #{record.levelOneName,jdbcType=VARCHAR},
      </if>
      <if test="record.levelTwoId != null">
        level_two_id = #{record.levelTwoId,jdbcType=INTEGER},
      </if>
      <if test="record.levelTwoName != null">
        level_two_name = #{record.levelTwoName,jdbcType=VARCHAR},
      </if>
      <if test="record.levelThreeId != null">
        level_three_id = #{record.levelThreeId,jdbcType=INTEGER},
      </if>
      <if test="record.levelThreeName != null">
        level_three_name = #{record.levelThreeName,jdbcType=VARCHAR},
      </if>
      <if test="record.levelFourId != null">
        level_four_id = #{record.levelFourId,jdbcType=INTEGER},
      </if>
      <if test="record.levelFourName != null">
        level_four_name = #{record.levelFourName,jdbcType=VARCHAR},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDelete != null">
        is_delete = #{record.isDelete,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update res_profession_interest_crowds
    set id = #{record.id,jdbcType=INTEGER},
      crowd_id = #{record.crowdId,jdbcType=INTEGER},
      level_one_id = #{record.levelOneId,jdbcType=INTEGER},
      level_one_name = #{record.levelOneName,jdbcType=VARCHAR},
      level_two_id = #{record.levelTwoId,jdbcType=INTEGER},
      level_two_name = #{record.levelTwoName,jdbcType=VARCHAR},
      level_three_id = #{record.levelThreeId,jdbcType=INTEGER},
      level_three_name = #{record.levelThreeName,jdbcType=VARCHAR},
      level_four_id = #{record.levelFourId,jdbcType=INTEGER},
      level_four_name = #{record.levelFourName,jdbcType=VARCHAR},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      is_delete = #{record.isDelete,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.adp.cpc.po.ad.ResProfessionInterestCrowdsPo">
    update res_profession_interest_crowds
    <set>
      <if test="crowdId != null">
        crowd_id = #{crowdId,jdbcType=INTEGER},
      </if>
      <if test="levelOneId != null">
        level_one_id = #{levelOneId,jdbcType=INTEGER},
      </if>
      <if test="levelOneName != null">
        level_one_name = #{levelOneName,jdbcType=VARCHAR},
      </if>
      <if test="levelTwoId != null">
        level_two_id = #{levelTwoId,jdbcType=INTEGER},
      </if>
      <if test="levelTwoName != null">
        level_two_name = #{levelTwoName,jdbcType=VARCHAR},
      </if>
      <if test="levelThreeId != null">
        level_three_id = #{levelThreeId,jdbcType=INTEGER},
      </if>
      <if test="levelThreeName != null">
        level_three_name = #{levelThreeName,jdbcType=VARCHAR},
      </if>
      <if test="levelFourId != null">
        level_four_id = #{levelFourId,jdbcType=INTEGER},
      </if>
      <if test="levelFourName != null">
        level_four_name = #{levelFourName,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.adp.cpc.po.ad.ResProfessionInterestCrowdsPo">
    update res_profession_interest_crowds
    set crowd_id = #{crowdId,jdbcType=INTEGER},
      level_one_id = #{levelOneId,jdbcType=INTEGER},
      level_one_name = #{levelOneName,jdbcType=VARCHAR},
      level_two_id = #{levelTwoId,jdbcType=INTEGER},
      level_two_name = #{levelTwoName,jdbcType=VARCHAR},
      level_three_id = #{levelThreeId,jdbcType=INTEGER},
      level_three_name = #{levelThreeName,jdbcType=VARCHAR},
      level_four_id = #{levelFourId,jdbcType=INTEGER},
      level_four_name = #{levelFourName,jdbcType=VARCHAR},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      is_delete = #{isDelete,jdbcType=TINYINT}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.adp.cpc.po.ad.ResProfessionInterestCrowdsPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into res_profession_interest_crowds (crowd_id, level_one_id, level_one_name, 
      level_two_id, level_two_name, level_three_id, 
      level_three_name, level_four_id, level_four_name, 
      ctime, mtime, is_delete
      )
    values (#{crowdId,jdbcType=INTEGER}, #{levelOneId,jdbcType=INTEGER}, #{levelOneName,jdbcType=VARCHAR}, 
      #{levelTwoId,jdbcType=INTEGER}, #{levelTwoName,jdbcType=VARCHAR}, #{levelThreeId,jdbcType=INTEGER}, 
      #{levelThreeName,jdbcType=VARCHAR}, #{levelFourId,jdbcType=INTEGER}, #{levelFourName,jdbcType=VARCHAR}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{isDelete,jdbcType=TINYINT}
      )
    <trim prefix="on duplicate key update" suffixOverrides=",">
      crowd_id = values(crowd_id),
      level_one_id = values(level_one_id),
      level_one_name = values(level_one_name),
      level_two_id = values(level_two_id),
      level_two_name = values(level_two_name),
      level_three_id = values(level_three_id),
      level_three_name = values(level_three_name),
      level_four_id = values(level_four_id),
      level_four_name = values(level_four_name),
      ctime = values(ctime),
      mtime = values(mtime),
      is_delete = values(is_delete),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      res_profession_interest_crowds
      (crowd_id,level_one_id,level_one_name,level_two_id,level_two_name,level_three_id,level_three_name,level_four_id,level_four_name,ctime,mtime,is_delete)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.crowdId,jdbcType=INTEGER},
        #{item.levelOneId,jdbcType=INTEGER},
        #{item.levelOneName,jdbcType=VARCHAR},
        #{item.levelTwoId,jdbcType=INTEGER},
        #{item.levelTwoName,jdbcType=VARCHAR},
        #{item.levelThreeId,jdbcType=INTEGER},
        #{item.levelThreeName,jdbcType=VARCHAR},
        #{item.levelFourId,jdbcType=INTEGER},
        #{item.levelFourName,jdbcType=VARCHAR},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDelete,jdbcType=TINYINT},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      res_profession_interest_crowds
      (crowd_id,level_one_id,level_one_name,level_two_id,level_two_name,level_three_id,level_three_name,level_four_id,level_four_name,ctime,mtime,is_delete)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.crowdId,jdbcType=INTEGER},
        #{item.levelOneId,jdbcType=INTEGER},
        #{item.levelOneName,jdbcType=VARCHAR},
        #{item.levelTwoId,jdbcType=INTEGER},
        #{item.levelTwoName,jdbcType=VARCHAR},
        #{item.levelThreeId,jdbcType=INTEGER},
        #{item.levelThreeName,jdbcType=VARCHAR},
        #{item.levelFourId,jdbcType=INTEGER},
        #{item.levelFourName,jdbcType=VARCHAR},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDelete,jdbcType=TINYINT},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      crowd_id = values(crowd_id),
      level_one_id = values(level_one_id),
      level_one_name = values(level_one_name),
      level_two_id = values(level_two_id),
      level_two_name = values(level_two_name),
      level_three_id = values(level_three_id),
      level_three_name = values(level_three_name),
      level_four_id = values(level_four_id),
      level_four_name = values(level_four_name),
      ctime = values(ctime),
      mtime = values(mtime),
      is_delete = values(is_delete),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.adp.cpc.po.ad.ResProfessionInterestCrowdsPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into res_profession_interest_crowds
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="crowdId != null">
        crowd_id,
      </if>
      <if test="levelOneId != null">
        level_one_id,
      </if>
      <if test="levelOneName != null">
        level_one_name,
      </if>
      <if test="levelTwoId != null">
        level_two_id,
      </if>
      <if test="levelTwoName != null">
        level_two_name,
      </if>
      <if test="levelThreeId != null">
        level_three_id,
      </if>
      <if test="levelThreeName != null">
        level_three_name,
      </if>
      <if test="levelFourId != null">
        level_four_id,
      </if>
      <if test="levelFourName != null">
        level_four_name,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="crowdId != null">
        #{crowdId,jdbcType=INTEGER},
      </if>
      <if test="levelOneId != null">
        #{levelOneId,jdbcType=INTEGER},
      </if>
      <if test="levelOneName != null">
        #{levelOneName,jdbcType=VARCHAR},
      </if>
      <if test="levelTwoId != null">
        #{levelTwoId,jdbcType=INTEGER},
      </if>
      <if test="levelTwoName != null">
        #{levelTwoName,jdbcType=VARCHAR},
      </if>
      <if test="levelThreeId != null">
        #{levelThreeId,jdbcType=INTEGER},
      </if>
      <if test="levelThreeName != null">
        #{levelThreeName,jdbcType=VARCHAR},
      </if>
      <if test="levelFourId != null">
        #{levelFourId,jdbcType=INTEGER},
      </if>
      <if test="levelFourName != null">
        #{levelFourName,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=TINYINT},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="crowdId != null">
        crowd_id = values(crowd_id),
      </if>
      <if test="levelOneId != null">
        level_one_id = values(level_one_id),
      </if>
      <if test="levelOneName != null">
        level_one_name = values(level_one_name),
      </if>
      <if test="levelTwoId != null">
        level_two_id = values(level_two_id),
      </if>
      <if test="levelTwoName != null">
        level_two_name = values(level_two_name),
      </if>
      <if test="levelThreeId != null">
        level_three_id = values(level_three_id),
      </if>
      <if test="levelThreeName != null">
        level_three_name = values(level_three_name),
      </if>
      <if test="levelFourId != null">
        level_four_id = values(level_four_id),
      </if>
      <if test="levelFourName != null">
        level_four_name = values(level_four_name),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="isDelete != null">
        is_delete = values(is_delete),
      </if>
    </trim>
  </insert>
</mapper>