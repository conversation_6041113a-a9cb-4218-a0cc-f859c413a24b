<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.adp.cpc.dao.ad.LauCreativeDarkSearchAuditRecordDao">
  <resultMap id="BaseResultMap" type="com.bilibili.adp.cpc.po.ad.LauCreativeDarkSearchAuditRecordPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="creative_id" jdbcType="INTEGER" property="creativeId" />
    <result column="event" jdbcType="TINYINT" property="event" />
    <result column="operator_username" jdbcType="VARCHAR" property="operatorUsername" />
    <result column="reject_reason" jdbcType="VARCHAR" property="rejectReason" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, creative_id, event, operator_username, reject_reason, is_deleted, ctime, mtime
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.adp.cpc.po.ad.LauCreativeDarkSearchAuditRecordPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from lau_creative_dark_search_audit_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from lau_creative_dark_search_audit_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from lau_creative_dark_search_audit_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.adp.cpc.po.ad.LauCreativeDarkSearchAuditRecordPoExample">
    delete from lau_creative_dark_search_audit_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.adp.cpc.po.ad.LauCreativeDarkSearchAuditRecordPo">
    insert into lau_creative_dark_search_audit_record (id, creative_id, event, 
      operator_username, reject_reason, is_deleted, 
      ctime, mtime)
    values (#{id,jdbcType=BIGINT}, #{creativeId,jdbcType=INTEGER}, #{event,jdbcType=TINYINT}, 
      #{operatorUsername,jdbcType=VARCHAR}, #{rejectReason,jdbcType=VARCHAR}, #{isDeleted,jdbcType=TINYINT}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.adp.cpc.po.ad.LauCreativeDarkSearchAuditRecordPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_creative_dark_search_audit_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="creativeId != null">
        creative_id,
      </if>
      <if test="event != null">
        event,
      </if>
      <if test="operatorUsername != null">
        operator_username,
      </if>
      <if test="rejectReason != null">
        reject_reason,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="creativeId != null">
        #{creativeId,jdbcType=INTEGER},
      </if>
      <if test="event != null">
        #{event,jdbcType=TINYINT},
      </if>
      <if test="operatorUsername != null">
        #{operatorUsername,jdbcType=VARCHAR},
      </if>
      <if test="rejectReason != null">
        #{rejectReason,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.adp.cpc.po.ad.LauCreativeDarkSearchAuditRecordPoExample" resultType="java.lang.Long">
    select count(*) from lau_creative_dark_search_audit_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update lau_creative_dark_search_audit_record
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.creativeId != null">
        creative_id = #{record.creativeId,jdbcType=INTEGER},
      </if>
      <if test="record.event != null">
        event = #{record.event,jdbcType=TINYINT},
      </if>
      <if test="record.operatorUsername != null">
        operator_username = #{record.operatorUsername,jdbcType=VARCHAR},
      </if>
      <if test="record.rejectReason != null">
        reject_reason = #{record.rejectReason,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update lau_creative_dark_search_audit_record
    set id = #{record.id,jdbcType=BIGINT},
      creative_id = #{record.creativeId,jdbcType=INTEGER},
      event = #{record.event,jdbcType=TINYINT},
      operator_username = #{record.operatorUsername,jdbcType=VARCHAR},
      reject_reason = #{record.rejectReason,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.adp.cpc.po.ad.LauCreativeDarkSearchAuditRecordPo">
    update lau_creative_dark_search_audit_record
    <set>
      <if test="creativeId != null">
        creative_id = #{creativeId,jdbcType=INTEGER},
      </if>
      <if test="event != null">
        event = #{event,jdbcType=TINYINT},
      </if>
      <if test="operatorUsername != null">
        operator_username = #{operatorUsername,jdbcType=VARCHAR},
      </if>
      <if test="rejectReason != null">
        reject_reason = #{rejectReason,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.adp.cpc.po.ad.LauCreativeDarkSearchAuditRecordPo">
    update lau_creative_dark_search_audit_record
    set creative_id = #{creativeId,jdbcType=INTEGER},
      event = #{event,jdbcType=TINYINT},
      operator_username = #{operatorUsername,jdbcType=VARCHAR},
      reject_reason = #{rejectReason,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.adp.cpc.po.ad.LauCreativeDarkSearchAuditRecordPo">
    insert into lau_creative_dark_search_audit_record (id, creative_id, event, 
      operator_username, reject_reason, is_deleted, 
      ctime, mtime)
    values (#{id,jdbcType=BIGINT}, #{creativeId,jdbcType=INTEGER}, #{event,jdbcType=TINYINT}, 
      #{operatorUsername,jdbcType=VARCHAR}, #{rejectReason,jdbcType=VARCHAR}, #{isDeleted,jdbcType=TINYINT}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      creative_id = values(creative_id),
      event = values(event),
      operator_username = values(operator_username),
      reject_reason = values(reject_reason),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      lau_creative_dark_search_audit_record
      (creative_id,event,operator_username,reject_reason,is_deleted,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.creativeId,jdbcType=INTEGER},
        #{item.event,jdbcType=TINYINT},
        #{item.operatorUsername,jdbcType=VARCHAR},
        #{item.rejectReason,jdbcType=VARCHAR},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      lau_creative_dark_search_audit_record
      (creative_id,event,operator_username,reject_reason,is_deleted,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.creativeId,jdbcType=INTEGER},
        #{item.event,jdbcType=TINYINT},
        #{item.operatorUsername,jdbcType=VARCHAR},
        #{item.rejectReason,jdbcType=VARCHAR},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      creative_id = values(creative_id),
      event = values(event),
      operator_username = values(operator_username),
      reject_reason = values(reject_reason),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.adp.cpc.po.ad.LauCreativeDarkSearchAuditRecordPo">
    insert into lau_creative_dark_search_audit_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="creativeId != null">
        creative_id,
      </if>
      <if test="event != null">
        event,
      </if>
      <if test="operatorUsername != null">
        operator_username,
      </if>
      <if test="rejectReason != null">
        reject_reason,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="creativeId != null">
        #{creativeId,jdbcType=INTEGER},
      </if>
      <if test="event != null">
        #{event,jdbcType=TINYINT},
      </if>
      <if test="operatorUsername != null">
        #{operatorUsername,jdbcType=VARCHAR},
      </if>
      <if test="rejectReason != null">
        #{rejectReason,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="creativeId != null">
        creative_id = values(creative_id),
      </if>
      <if test="event != null">
        event = values(event),
      </if>
      <if test="operatorUsername != null">
        operator_username = values(operator_username),
      </if>
      <if test="rejectReason != null">
        reject_reason = values(reject_reason),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
    </trim>
  </insert>
</mapper>