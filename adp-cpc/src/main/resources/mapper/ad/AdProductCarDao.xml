<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.adp.cpc.dao.ad.AdProductCarDao">
  <resultMap id="BaseResultMap" type="com.bilibili.adp.cpc.po.ad.AdProductCarPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="account_id" jdbcType="INTEGER" property="accountId" />
    <result column="library_id" jdbcType="BIGINT" property="libraryId" />
    <result column="ad_product_id" jdbcType="BIGINT" property="adProductId" />
    <result column="biz_status" jdbcType="TINYINT" property="bizStatus" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="brand" jdbcType="VARCHAR" property="brand" />
    <result column="model" jdbcType="VARCHAR" property="model" />
    <result column="first_category_code" jdbcType="BIGINT" property="firstCategoryCode" />
    <result column="second_category_code" jdbcType="BIGINT" property="secondCategoryCode" />
    <result column="third_category_code" jdbcType="BIGINT" property="thirdCategoryCode" />
    <result column="spu_code" jdbcType="BIGINT" property="spuCode" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
  </resultMap>
  <resultMap id="AliasBaseResultMap" type="com.bilibili.adp.cpc.po.ad.AdProductCarPo">
    <id column="ad_product_car_id" jdbcType="BIGINT" property="id" />
    <result column="ad_product_car_account_id" jdbcType="INTEGER" property="accountId" />
    <result column="ad_product_car_library_id" jdbcType="BIGINT" property="libraryId" />
    <result column="ad_product_car_ad_product_id" jdbcType="BIGINT" property="adProductId" />
    <result column="ad_product_car_biz_status" jdbcType="TINYINT" property="bizStatus" />
    <result column="ad_product_car_name" jdbcType="VARCHAR" property="name" />
    <result column="ad_product_car_brand" jdbcType="VARCHAR" property="brand" />
    <result column="ad_product_car_model" jdbcType="VARCHAR" property="model" />
    <result column="ad_product_car_first_category_code" jdbcType="BIGINT" property="firstCategoryCode" />
    <result column="ad_product_car_second_category_code" jdbcType="BIGINT" property="secondCategoryCode" />
    <result column="ad_product_car_third_category_code" jdbcType="BIGINT" property="thirdCategoryCode" />
    <result column="ad_product_car_spu_code" jdbcType="BIGINT" property="spuCode" />
    <result column="ad_product_car_is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ad_product_car_ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="ad_product_car_mtime" jdbcType="TIMESTAMP" property="mtime" />
  </resultMap>
  <sql id="Alias_Base_Column_List">
    ${alias}.id as ad_product_car_id, ${alias}.account_id as ad_product_car_account_id, 
    ${alias}.library_id as ad_product_car_library_id, ${alias}.ad_product_id as ad_product_car_ad_product_id, 
    ${alias}.biz_status as ad_product_car_biz_status, ${alias}.name as ad_product_car_name, 
    ${alias}.brand as ad_product_car_brand, ${alias}.model as ad_product_car_model, ${alias}.first_category_code as ad_product_car_first_category_code, 
    ${alias}.second_category_code as ad_product_car_second_category_code, ${alias}.third_category_code as ad_product_car_third_category_code, 
    ${alias}.spu_code as ad_product_car_spu_code, ${alias}.is_deleted as ad_product_car_is_deleted, 
    ${alias}.ctime as ad_product_car_ctime, ${alias}.mtime as ad_product_car_mtime
  </sql>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, account_id, library_id, ad_product_id, biz_status, name, brand, model, first_category_code, 
    second_category_code, third_category_code, spu_code, is_deleted, ctime, mtime
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.adp.cpc.po.ad.AdProductCarPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from ad_product_car
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ad_product_car
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from ad_product_car
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.adp.cpc.po.ad.AdProductCarPoExample">
    delete from ad_product_car
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.adp.cpc.po.ad.AdProductCarPo">
    insert into ad_product_car (id, account_id, library_id, 
      ad_product_id, biz_status, name, 
      brand, model, first_category_code, 
      second_category_code, third_category_code, spu_code, 
      is_deleted, ctime, mtime
      )
    values (#{id,jdbcType=BIGINT}, #{accountId,jdbcType=INTEGER}, #{libraryId,jdbcType=BIGINT}, 
      #{adProductId,jdbcType=BIGINT}, #{bizStatus,jdbcType=TINYINT}, #{name,jdbcType=VARCHAR}, 
      #{brand,jdbcType=VARCHAR}, #{model,jdbcType=VARCHAR}, #{firstCategoryCode,jdbcType=BIGINT}, 
      #{secondCategoryCode,jdbcType=BIGINT}, #{thirdCategoryCode,jdbcType=BIGINT}, #{spuCode,jdbcType=BIGINT}, 
      #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.adp.cpc.po.ad.AdProductCarPo">
    insert into ad_product_car
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="libraryId != null">
        library_id,
      </if>
      <if test="adProductId != null">
        ad_product_id,
      </if>
      <if test="bizStatus != null">
        biz_status,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="brand != null">
        brand,
      </if>
      <if test="model != null">
        model,
      </if>
      <if test="firstCategoryCode != null">
        first_category_code,
      </if>
      <if test="secondCategoryCode != null">
        second_category_code,
      </if>
      <if test="thirdCategoryCode != null">
        third_category_code,
      </if>
      <if test="spuCode != null">
        spu_code,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="libraryId != null">
        #{libraryId,jdbcType=BIGINT},
      </if>
      <if test="adProductId != null">
        #{adProductId,jdbcType=BIGINT},
      </if>
      <if test="bizStatus != null">
        #{bizStatus,jdbcType=TINYINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="brand != null">
        #{brand,jdbcType=VARCHAR},
      </if>
      <if test="model != null">
        #{model,jdbcType=VARCHAR},
      </if>
      <if test="firstCategoryCode != null">
        #{firstCategoryCode,jdbcType=BIGINT},
      </if>
      <if test="secondCategoryCode != null">
        #{secondCategoryCode,jdbcType=BIGINT},
      </if>
      <if test="thirdCategoryCode != null">
        #{thirdCategoryCode,jdbcType=BIGINT},
      </if>
      <if test="spuCode != null">
        #{spuCode,jdbcType=BIGINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.adp.cpc.po.ad.AdProductCarPoExample" resultType="java.lang.Long">
    select count(*) from ad_product_car
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update ad_product_car
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.accountId != null">
        account_id = #{record.accountId,jdbcType=INTEGER},
      </if>
      <if test="record.libraryId != null">
        library_id = #{record.libraryId,jdbcType=BIGINT},
      </if>
      <if test="record.adProductId != null">
        ad_product_id = #{record.adProductId,jdbcType=BIGINT},
      </if>
      <if test="record.bizStatus != null">
        biz_status = #{record.bizStatus,jdbcType=TINYINT},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.brand != null">
        brand = #{record.brand,jdbcType=VARCHAR},
      </if>
      <if test="record.model != null">
        model = #{record.model,jdbcType=VARCHAR},
      </if>
      <if test="record.firstCategoryCode != null">
        first_category_code = #{record.firstCategoryCode,jdbcType=BIGINT},
      </if>
      <if test="record.secondCategoryCode != null">
        second_category_code = #{record.secondCategoryCode,jdbcType=BIGINT},
      </if>
      <if test="record.thirdCategoryCode != null">
        third_category_code = #{record.thirdCategoryCode,jdbcType=BIGINT},
      </if>
      <if test="record.spuCode != null">
        spu_code = #{record.spuCode,jdbcType=BIGINT},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update ad_product_car
    set id = #{record.id,jdbcType=BIGINT},
      account_id = #{record.accountId,jdbcType=INTEGER},
      library_id = #{record.libraryId,jdbcType=BIGINT},
      ad_product_id = #{record.adProductId,jdbcType=BIGINT},
      biz_status = #{record.bizStatus,jdbcType=TINYINT},
      name = #{record.name,jdbcType=VARCHAR},
      brand = #{record.brand,jdbcType=VARCHAR},
      model = #{record.model,jdbcType=VARCHAR},
      first_category_code = #{record.firstCategoryCode,jdbcType=BIGINT},
      second_category_code = #{record.secondCategoryCode,jdbcType=BIGINT},
      third_category_code = #{record.thirdCategoryCode,jdbcType=BIGINT},
      spu_code = #{record.spuCode,jdbcType=BIGINT},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.adp.cpc.po.ad.AdProductCarPo">
    update ad_product_car
    <set>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=INTEGER},
      </if>
      <if test="libraryId != null">
        library_id = #{libraryId,jdbcType=BIGINT},
      </if>
      <if test="adProductId != null">
        ad_product_id = #{adProductId,jdbcType=BIGINT},
      </if>
      <if test="bizStatus != null">
        biz_status = #{bizStatus,jdbcType=TINYINT},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="brand != null">
        brand = #{brand,jdbcType=VARCHAR},
      </if>
      <if test="model != null">
        model = #{model,jdbcType=VARCHAR},
      </if>
      <if test="firstCategoryCode != null">
        first_category_code = #{firstCategoryCode,jdbcType=BIGINT},
      </if>
      <if test="secondCategoryCode != null">
        second_category_code = #{secondCategoryCode,jdbcType=BIGINT},
      </if>
      <if test="thirdCategoryCode != null">
        third_category_code = #{thirdCategoryCode,jdbcType=BIGINT},
      </if>
      <if test="spuCode != null">
        spu_code = #{spuCode,jdbcType=BIGINT},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.adp.cpc.po.ad.AdProductCarPo">
    update ad_product_car
    set account_id = #{accountId,jdbcType=INTEGER},
      library_id = #{libraryId,jdbcType=BIGINT},
      ad_product_id = #{adProductId,jdbcType=BIGINT},
      biz_status = #{bizStatus,jdbcType=TINYINT},
      name = #{name,jdbcType=VARCHAR},
      brand = #{brand,jdbcType=VARCHAR},
      model = #{model,jdbcType=VARCHAR},
      first_category_code = #{firstCategoryCode,jdbcType=BIGINT},
      second_category_code = #{secondCategoryCode,jdbcType=BIGINT},
      third_category_code = #{thirdCategoryCode,jdbcType=BIGINT},
      spu_code = #{spuCode,jdbcType=BIGINT},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.adp.cpc.po.ad.AdProductCarPo">
    insert into ad_product_car (id, account_id, library_id, 
      ad_product_id, biz_status, name, 
      brand, model, first_category_code, 
      second_category_code, third_category_code, spu_code, 
      is_deleted, ctime, mtime
      )
    values (#{id,jdbcType=BIGINT}, #{accountId,jdbcType=INTEGER}, #{libraryId,jdbcType=BIGINT}, 
      #{adProductId,jdbcType=BIGINT}, #{bizStatus,jdbcType=TINYINT}, #{name,jdbcType=VARCHAR}, 
      #{brand,jdbcType=VARCHAR}, #{model,jdbcType=VARCHAR}, #{firstCategoryCode,jdbcType=BIGINT}, 
      #{secondCategoryCode,jdbcType=BIGINT}, #{thirdCategoryCode,jdbcType=BIGINT}, #{spuCode,jdbcType=BIGINT}, 
      #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}
      )
    <trim prefix="on duplicate key update" suffixOverrides=",">
      account_id = values(account_id),
      library_id = values(library_id),
      ad_product_id = values(ad_product_id),
      biz_status = values(biz_status),
      name = values(name),
      brand = values(brand),
      model = values(model),
      first_category_code = values(first_category_code),
      second_category_code = values(second_category_code),
      third_category_code = values(third_category_code),
      spu_code = values(spu_code),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      ad_product_car
      (account_id,library_id,ad_product_id,biz_status,name,brand,model,first_category_code,second_category_code,third_category_code,spu_code,is_deleted,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.accountId,jdbcType=INTEGER},
        #{item.libraryId,jdbcType=BIGINT},
        #{item.adProductId,jdbcType=BIGINT},
        #{item.bizStatus,jdbcType=TINYINT},
        #{item.name,jdbcType=VARCHAR},
        #{item.brand,jdbcType=VARCHAR},
        #{item.model,jdbcType=VARCHAR},
        #{item.firstCategoryCode,jdbcType=BIGINT},
        #{item.secondCategoryCode,jdbcType=BIGINT},
        #{item.thirdCategoryCode,jdbcType=BIGINT},
        #{item.spuCode,jdbcType=BIGINT},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      ad_product_car
      (account_id,library_id,ad_product_id,biz_status,name,brand,model,first_category_code,second_category_code,third_category_code,spu_code,is_deleted,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.accountId,jdbcType=INTEGER},
        #{item.libraryId,jdbcType=BIGINT},
        #{item.adProductId,jdbcType=BIGINT},
        #{item.bizStatus,jdbcType=TINYINT},
        #{item.name,jdbcType=VARCHAR},
        #{item.brand,jdbcType=VARCHAR},
        #{item.model,jdbcType=VARCHAR},
        #{item.firstCategoryCode,jdbcType=BIGINT},
        #{item.secondCategoryCode,jdbcType=BIGINT},
        #{item.thirdCategoryCode,jdbcType=BIGINT},
        #{item.spuCode,jdbcType=BIGINT},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      account_id = values(account_id),
      library_id = values(library_id),
      ad_product_id = values(ad_product_id),
      biz_status = values(biz_status),
      name = values(name),
      brand = values(brand),
      model = values(model),
      first_category_code = values(first_category_code),
      second_category_code = values(second_category_code),
      third_category_code = values(third_category_code),
      spu_code = values(spu_code),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.adp.cpc.po.ad.AdProductCarPo">
    insert into ad_product_car
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="libraryId != null">
        library_id,
      </if>
      <if test="adProductId != null">
        ad_product_id,
      </if>
      <if test="bizStatus != null">
        biz_status,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="brand != null">
        brand,
      </if>
      <if test="model != null">
        model,
      </if>
      <if test="firstCategoryCode != null">
        first_category_code,
      </if>
      <if test="secondCategoryCode != null">
        second_category_code,
      </if>
      <if test="thirdCategoryCode != null">
        third_category_code,
      </if>
      <if test="spuCode != null">
        spu_code,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="libraryId != null">
        #{libraryId,jdbcType=BIGINT},
      </if>
      <if test="adProductId != null">
        #{adProductId,jdbcType=BIGINT},
      </if>
      <if test="bizStatus != null">
        #{bizStatus,jdbcType=TINYINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="brand != null">
        #{brand,jdbcType=VARCHAR},
      </if>
      <if test="model != null">
        #{model,jdbcType=VARCHAR},
      </if>
      <if test="firstCategoryCode != null">
        #{firstCategoryCode,jdbcType=BIGINT},
      </if>
      <if test="secondCategoryCode != null">
        #{secondCategoryCode,jdbcType=BIGINT},
      </if>
      <if test="thirdCategoryCode != null">
        #{thirdCategoryCode,jdbcType=BIGINT},
      </if>
      <if test="spuCode != null">
        #{spuCode,jdbcType=BIGINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="accountId != null">
        account_id = values(account_id),
      </if>
      <if test="libraryId != null">
        library_id = values(library_id),
      </if>
      <if test="adProductId != null">
        ad_product_id = values(ad_product_id),
      </if>
      <if test="bizStatus != null">
        biz_status = values(biz_status),
      </if>
      <if test="name != null">
        name = values(name),
      </if>
      <if test="brand != null">
        brand = values(brand),
      </if>
      <if test="model != null">
        model = values(model),
      </if>
      <if test="firstCategoryCode != null">
        first_category_code = values(first_category_code),
      </if>
      <if test="secondCategoryCode != null">
        second_category_code = values(second_category_code),
      </if>
      <if test="thirdCategoryCode != null">
        third_category_code = values(third_category_code),
      </if>
      <if test="spuCode != null">
        spu_code = values(spu_code),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
    </trim>
  </insert>
</mapper>