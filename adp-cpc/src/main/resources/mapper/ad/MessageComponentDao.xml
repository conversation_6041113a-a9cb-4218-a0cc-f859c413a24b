<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.adp.cpc.dao.ad.MessageComponentDao">
  <resultMap id="BaseResultMap" type="com.bilibili.adp.cpc.po.ad.MessageComponentPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="account_id" jdbcType="BIGINT" property="accountId" />
    <result column="ad_creative_id" jdbcType="BIGINT" property="adCreativeId" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="jump_url" jdbcType="VARCHAR" property="jumpUrl" />
    <result column="image_url" jdbcType="VARCHAR" property="imageUrl" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="message_type" jdbcType="TINYINT" property="messageType" />
    <result column="icon_url" jdbcType="VARCHAR" property="iconUrl" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="relate_page_id" jdbcType="BIGINT" property="relatePageId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, account_id, ad_creative_id, content, jump_url, image_url, is_deleted, ctime, 
    mtime, message_type, icon_url, name, relate_page_id
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.adp.cpc.po.ad.MessageComponentPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from message_component
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from message_component
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from message_component
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.adp.cpc.po.ad.MessageComponentPoExample">
    delete from message_component
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.adp.cpc.po.ad.MessageComponentPo">
    insert into message_component (id, account_id, ad_creative_id, 
      content, jump_url, image_url, 
      is_deleted, ctime, mtime, 
      message_type, icon_url, name, 
      relate_page_id)
    values (#{id,jdbcType=BIGINT}, #{accountId,jdbcType=BIGINT}, #{adCreativeId,jdbcType=BIGINT}, 
      #{content,jdbcType=VARCHAR}, #{jumpUrl,jdbcType=VARCHAR}, #{imageUrl,jdbcType=VARCHAR}, 
      #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, 
      #{messageType,jdbcType=TINYINT}, #{iconUrl,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, 
      #{relatePageId,jdbcType=BIGINT}
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.adp.cpc.po.ad.MessageComponentPo">
    insert into message_component
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="adCreativeId != null">
        ad_creative_id,
      </if>
      <if test="content != null">
        content,
      </if>
      <if test="jumpUrl != null">
        jump_url,
      </if>
      <if test="imageUrl != null">
        image_url,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="messageType != null">
        message_type,
      </if>
      <if test="iconUrl != null">
        icon_url,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="relatePageId != null">
        relate_page_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=BIGINT},
      </if>
      <if test="adCreativeId != null">
        #{adCreativeId,jdbcType=BIGINT},
      </if>
      <if test="content != null">
        #{content,jdbcType=VARCHAR},
      </if>
      <if test="jumpUrl != null">
        #{jumpUrl,jdbcType=VARCHAR},
      </if>
      <if test="imageUrl != null">
        #{imageUrl,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="messageType != null">
        #{messageType,jdbcType=TINYINT},
      </if>
      <if test="iconUrl != null">
        #{iconUrl,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="relatePageId != null">
        #{relatePageId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.adp.cpc.po.ad.MessageComponentPoExample" resultType="java.lang.Long">
    select count(*) from message_component
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update message_component
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.accountId != null">
        account_id = #{record.accountId,jdbcType=BIGINT},
      </if>
      <if test="record.adCreativeId != null">
        ad_creative_id = #{record.adCreativeId,jdbcType=BIGINT},
      </if>
      <if test="record.content != null">
        content = #{record.content,jdbcType=VARCHAR},
      </if>
      <if test="record.jumpUrl != null">
        jump_url = #{record.jumpUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.imageUrl != null">
        image_url = #{record.imageUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.messageType != null">
        message_type = #{record.messageType,jdbcType=TINYINT},
      </if>
      <if test="record.iconUrl != null">
        icon_url = #{record.iconUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.relatePageId != null">
        relate_page_id = #{record.relatePageId,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update message_component
    set id = #{record.id,jdbcType=BIGINT},
      account_id = #{record.accountId,jdbcType=BIGINT},
      ad_creative_id = #{record.adCreativeId,jdbcType=BIGINT},
      content = #{record.content,jdbcType=VARCHAR},
      jump_url = #{record.jumpUrl,jdbcType=VARCHAR},
      image_url = #{record.imageUrl,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      message_type = #{record.messageType,jdbcType=TINYINT},
      icon_url = #{record.iconUrl,jdbcType=VARCHAR},
      name = #{record.name,jdbcType=VARCHAR},
      relate_page_id = #{record.relatePageId,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.adp.cpc.po.ad.MessageComponentPo">
    update message_component
    <set>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=BIGINT},
      </if>
      <if test="adCreativeId != null">
        ad_creative_id = #{adCreativeId,jdbcType=BIGINT},
      </if>
      <if test="content != null">
        content = #{content,jdbcType=VARCHAR},
      </if>
      <if test="jumpUrl != null">
        jump_url = #{jumpUrl,jdbcType=VARCHAR},
      </if>
      <if test="imageUrl != null">
        image_url = #{imageUrl,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="messageType != null">
        message_type = #{messageType,jdbcType=TINYINT},
      </if>
      <if test="iconUrl != null">
        icon_url = #{iconUrl,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="relatePageId != null">
        relate_page_id = #{relatePageId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.adp.cpc.po.ad.MessageComponentPo">
    update message_component
    set account_id = #{accountId,jdbcType=BIGINT},
      ad_creative_id = #{adCreativeId,jdbcType=BIGINT},
      content = #{content,jdbcType=VARCHAR},
      jump_url = #{jumpUrl,jdbcType=VARCHAR},
      image_url = #{imageUrl,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      message_type = #{messageType,jdbcType=TINYINT},
      icon_url = #{iconUrl,jdbcType=VARCHAR},
      name = #{name,jdbcType=VARCHAR},
      relate_page_id = #{relatePageId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>