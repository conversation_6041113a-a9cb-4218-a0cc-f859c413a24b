<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.adp.cpc.dao.ad.ResAppPackageV2Dao">
  <resultMap id="BaseResultMap" type="com.bilibili.adp.cpc.po.ad.ResAppPackagePo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="account_id" jdbcType="INTEGER" property="accountId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="url" jdbcType="VARCHAR" property="url" />
    <result column="package_name" jdbcType="VARCHAR" property="packageName" />
    <result column="app_name" jdbcType="VARCHAR" property="appName" />
    <result column="platform" jdbcType="TINYINT" property="platform" />
    <result column="version" jdbcType="VARCHAR" property="version" />
    <result column="size" jdbcType="INTEGER" property="size" />
    <result column="md5" jdbcType="VARCHAR" property="md5" />
    <result column="icon_url" jdbcType="VARCHAR" property="iconUrl" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="internal_url" jdbcType="VARCHAR" property="internalUrl" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="platform_status" jdbcType="TINYINT" property="platformStatus" />
    <result column="developer_name" jdbcType="VARCHAR" property="developerName" />
    <result column="authority_url" jdbcType="VARCHAR" property="authorityUrl" />
    <result column="auth_code_list" jdbcType="VARCHAR" property="authCodeList" />
    <result column="apk_update_time" jdbcType="TIMESTAMP" property="apkUpdateTime" />
    <result column="privacy_policy" jdbcType="VARCHAR" property="privacyPolicy" />
    <result column="dmp_app_id" jdbcType="INTEGER" property="dmpAppId" />
    <result column="is_new_fly" jdbcType="TINYINT" property="isNewFly" />
    <result column="sub_title" jdbcType="VARCHAR" property="subTitle" />
    <result column="is_icon_valid" jdbcType="TINYINT" property="isIconValid" />
    <result column="device_app_store" jdbcType="VARCHAR" property="deviceAppStore" />
    <result column="copy_source_account_id" jdbcType="INTEGER" property="copySourceAccountId" />
    <result column="record_number" jdbcType="VARCHAR" property="recordNumber" />
  </resultMap>
  <resultMap id="AliasBaseResultMap" type="com.bilibili.adp.cpc.po.ad.ResAppPackagePo">
    <id column="res_app_package_id" jdbcType="INTEGER" property="id" />
    <result column="res_app_package_account_id" jdbcType="INTEGER" property="accountId" />
    <result column="res_app_package_name" jdbcType="VARCHAR" property="name" />
    <result column="res_app_package_url" jdbcType="VARCHAR" property="url" />
    <result column="res_app_package_package_name" jdbcType="VARCHAR" property="packageName" />
    <result column="res_app_package_app_name" jdbcType="VARCHAR" property="appName" />
    <result column="res_app_package_platform" jdbcType="TINYINT" property="platform" />
    <result column="res_app_package_version" jdbcType="VARCHAR" property="version" />
    <result column="res_app_package_size" jdbcType="INTEGER" property="size" />
    <result column="res_app_package_md5" jdbcType="VARCHAR" property="md5" />
    <result column="res_app_package_icon_url" jdbcType="VARCHAR" property="iconUrl" />
    <result column="res_app_package_ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="res_app_package_mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="res_app_package_is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="res_app_package_internal_url" jdbcType="VARCHAR" property="internalUrl" />
    <result column="res_app_package_status" jdbcType="TINYINT" property="status" />
    <result column="res_app_package_platform_status" jdbcType="TINYINT" property="platformStatus" />
    <result column="res_app_package_developer_name" jdbcType="VARCHAR" property="developerName" />
    <result column="res_app_package_authority_url" jdbcType="VARCHAR" property="authorityUrl" />
    <result column="res_app_package_auth_code_list" jdbcType="VARCHAR" property="authCodeList" />
    <result column="res_app_package_apk_update_time" jdbcType="TIMESTAMP" property="apkUpdateTime" />
    <result column="res_app_package_privacy_policy" jdbcType="VARCHAR" property="privacyPolicy" />
    <result column="res_app_package_dmp_app_id" jdbcType="INTEGER" property="dmpAppId" />
    <result column="res_app_package_is_new_fly" jdbcType="TINYINT" property="isNewFly" />
    <result column="res_app_package_sub_title" jdbcType="VARCHAR" property="subTitle" />
    <result column="res_app_package_is_icon_valid" jdbcType="TINYINT" property="isIconValid" />
    <result column="res_app_package_device_app_store" jdbcType="VARCHAR" property="deviceAppStore" />
    <result column="res_app_package_copy_source_account_id" jdbcType="INTEGER" property="copySourceAccountId" />
    <result column="res_app_package_record_number" jdbcType="VARCHAR" property="recordNumber" />
  </resultMap>
  <sql id="Alias_Base_Column_List">
    ${alias}.id as res_app_package_id, ${alias}.account_id as res_app_package_account_id, 
    ${alias}.name as res_app_package_name, ${alias}.url as res_app_package_url, ${alias}.package_name as res_app_package_package_name, 
    ${alias}.app_name as res_app_package_app_name, ${alias}.platform as res_app_package_platform, 
    ${alias}.version as res_app_package_version, ${alias}.size as res_app_package_size, 
    ${alias}.md5 as res_app_package_md5, ${alias}.icon_url as res_app_package_icon_url, 
    ${alias}.ctime as res_app_package_ctime, ${alias}.mtime as res_app_package_mtime, 
    ${alias}.is_deleted as res_app_package_is_deleted, ${alias}.internal_url as res_app_package_internal_url, 
    ${alias}.status as res_app_package_status, ${alias}.platform_status as res_app_package_platform_status, 
    ${alias}.developer_name as res_app_package_developer_name, ${alias}.authority_url as res_app_package_authority_url, 
    ${alias}.auth_code_list as res_app_package_auth_code_list, ${alias}.apk_update_time as res_app_package_apk_update_time, 
    ${alias}.privacy_policy as res_app_package_privacy_policy, ${alias}.dmp_app_id as res_app_package_dmp_app_id, 
    ${alias}.is_new_fly as res_app_package_is_new_fly, ${alias}.sub_title as res_app_package_sub_title, 
    ${alias}.is_icon_valid as res_app_package_is_icon_valid, ${alias}.device_app_store as res_app_package_device_app_store, 
    ${alias}.copy_source_account_id as res_app_package_copy_source_account_id, ${alias}.record_number as res_app_package_record_number
  </sql>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.bilibili.adp.cpc.po.ad.ResAppPackagePo">
    <result column="description" jdbcType="LONGVARCHAR" property="description" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, account_id, name, url, package_name, app_name, platform, version, size, md5, 
    icon_url, ctime, mtime, is_deleted, internal_url, status, platform_status, developer_name, 
    authority_url, auth_code_list, apk_update_time, privacy_policy, dmp_app_id, is_new_fly, 
    sub_title, is_icon_valid, device_app_store, copy_source_account_id, record_number
  </sql>
  <sql id="Blob_Column_List">
    description
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.bilibili.adp.cpc.po.ad.ResAppPackagePoExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from res_app_package
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByExample" parameterType="com.bilibili.adp.cpc.po.ad.ResAppPackagePoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from res_app_package
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from res_app_package
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from res_app_package
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.adp.cpc.po.ad.ResAppPackagePoExample">
    delete from res_app_package
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.adp.cpc.po.ad.ResAppPackagePo">
    insert into res_app_package (id, account_id, name, 
      url, package_name, app_name, 
      platform, version, size, 
      md5, icon_url, ctime, 
      mtime, is_deleted, internal_url, 
      status, platform_status, developer_name, 
      authority_url, auth_code_list, apk_update_time, 
      privacy_policy, dmp_app_id, is_new_fly, 
      sub_title, is_icon_valid, device_app_store, 
      copy_source_account_id, record_number, description
      )
    values (#{id,jdbcType=INTEGER}, #{accountId,jdbcType=INTEGER}, #{name,jdbcType=VARCHAR}, 
      #{url,jdbcType=VARCHAR}, #{packageName,jdbcType=VARCHAR}, #{appName,jdbcType=VARCHAR}, 
      #{platform,jdbcType=TINYINT}, #{version,jdbcType=VARCHAR}, #{size,jdbcType=INTEGER}, 
      #{md5,jdbcType=VARCHAR}, #{iconUrl,jdbcType=VARCHAR}, #{ctime,jdbcType=TIMESTAMP}, 
      #{mtime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=TINYINT}, #{internalUrl,jdbcType=VARCHAR}, 
      #{status,jdbcType=TINYINT}, #{platformStatus,jdbcType=TINYINT}, #{developerName,jdbcType=VARCHAR}, 
      #{authorityUrl,jdbcType=VARCHAR}, #{authCodeList,jdbcType=VARCHAR}, #{apkUpdateTime,jdbcType=TIMESTAMP}, 
      #{privacyPolicy,jdbcType=VARCHAR}, #{dmpAppId,jdbcType=INTEGER}, #{isNewFly,jdbcType=TINYINT}, 
      #{subTitle,jdbcType=VARCHAR}, #{isIconValid,jdbcType=TINYINT}, #{deviceAppStore,jdbcType=VARCHAR}, 
      #{copySourceAccountId,jdbcType=INTEGER}, #{recordNumber,jdbcType=VARCHAR}, #{description,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.adp.cpc.po.ad.ResAppPackagePo">
    insert into res_app_package
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="url != null">
        url,
      </if>
      <if test="packageName != null">
        package_name,
      </if>
      <if test="appName != null">
        app_name,
      </if>
      <if test="platform != null">
        platform,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="size != null">
        size,
      </if>
      <if test="md5 != null">
        md5,
      </if>
      <if test="iconUrl != null">
        icon_url,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="internalUrl != null">
        internal_url,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="platformStatus != null">
        platform_status,
      </if>
      <if test="developerName != null">
        developer_name,
      </if>
      <if test="authorityUrl != null">
        authority_url,
      </if>
      <if test="authCodeList != null">
        auth_code_list,
      </if>
      <if test="apkUpdateTime != null">
        apk_update_time,
      </if>
      <if test="privacyPolicy != null">
        privacy_policy,
      </if>
      <if test="dmpAppId != null">
        dmp_app_id,
      </if>
      <if test="isNewFly != null">
        is_new_fly,
      </if>
      <if test="subTitle != null">
        sub_title,
      </if>
      <if test="isIconValid != null">
        is_icon_valid,
      </if>
      <if test="deviceAppStore != null">
        device_app_store,
      </if>
      <if test="copySourceAccountId != null">
        copy_source_account_id,
      </if>
      <if test="recordNumber != null">
        record_number,
      </if>
      <if test="description != null">
        description,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="url != null">
        #{url,jdbcType=VARCHAR},
      </if>
      <if test="packageName != null">
        #{packageName,jdbcType=VARCHAR},
      </if>
      <if test="appName != null">
        #{appName,jdbcType=VARCHAR},
      </if>
      <if test="platform != null">
        #{platform,jdbcType=TINYINT},
      </if>
      <if test="version != null">
        #{version,jdbcType=VARCHAR},
      </if>
      <if test="size != null">
        #{size,jdbcType=INTEGER},
      </if>
      <if test="md5 != null">
        #{md5,jdbcType=VARCHAR},
      </if>
      <if test="iconUrl != null">
        #{iconUrl,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="internalUrl != null">
        #{internalUrl,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="platformStatus != null">
        #{platformStatus,jdbcType=TINYINT},
      </if>
      <if test="developerName != null">
        #{developerName,jdbcType=VARCHAR},
      </if>
      <if test="authorityUrl != null">
        #{authorityUrl,jdbcType=VARCHAR},
      </if>
      <if test="authCodeList != null">
        #{authCodeList,jdbcType=VARCHAR},
      </if>
      <if test="apkUpdateTime != null">
        #{apkUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="privacyPolicy != null">
        #{privacyPolicy,jdbcType=VARCHAR},
      </if>
      <if test="dmpAppId != null">
        #{dmpAppId,jdbcType=INTEGER},
      </if>
      <if test="isNewFly != null">
        #{isNewFly,jdbcType=TINYINT},
      </if>
      <if test="subTitle != null">
        #{subTitle,jdbcType=VARCHAR},
      </if>
      <if test="isIconValid != null">
        #{isIconValid,jdbcType=TINYINT},
      </if>
      <if test="deviceAppStore != null">
        #{deviceAppStore,jdbcType=VARCHAR},
      </if>
      <if test="copySourceAccountId != null">
        #{copySourceAccountId,jdbcType=INTEGER},
      </if>
      <if test="recordNumber != null">
        #{recordNumber,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.adp.cpc.po.ad.ResAppPackagePoExample" resultType="java.lang.Long">
    select count(*) from res_app_package
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update res_app_package
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.accountId != null">
        account_id = #{record.accountId,jdbcType=INTEGER},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.url != null">
        url = #{record.url,jdbcType=VARCHAR},
      </if>
      <if test="record.packageName != null">
        package_name = #{record.packageName,jdbcType=VARCHAR},
      </if>
      <if test="record.appName != null">
        app_name = #{record.appName,jdbcType=VARCHAR},
      </if>
      <if test="record.platform != null">
        platform = #{record.platform,jdbcType=TINYINT},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=VARCHAR},
      </if>
      <if test="record.size != null">
        size = #{record.size,jdbcType=INTEGER},
      </if>
      <if test="record.md5 != null">
        md5 = #{record.md5,jdbcType=VARCHAR},
      </if>
      <if test="record.iconUrl != null">
        icon_url = #{record.iconUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.internalUrl != null">
        internal_url = #{record.internalUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.platformStatus != null">
        platform_status = #{record.platformStatus,jdbcType=TINYINT},
      </if>
      <if test="record.developerName != null">
        developer_name = #{record.developerName,jdbcType=VARCHAR},
      </if>
      <if test="record.authorityUrl != null">
        authority_url = #{record.authorityUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.authCodeList != null">
        auth_code_list = #{record.authCodeList,jdbcType=VARCHAR},
      </if>
      <if test="record.apkUpdateTime != null">
        apk_update_time = #{record.apkUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.privacyPolicy != null">
        privacy_policy = #{record.privacyPolicy,jdbcType=VARCHAR},
      </if>
      <if test="record.dmpAppId != null">
        dmp_app_id = #{record.dmpAppId,jdbcType=INTEGER},
      </if>
      <if test="record.isNewFly != null">
        is_new_fly = #{record.isNewFly,jdbcType=TINYINT},
      </if>
      <if test="record.subTitle != null">
        sub_title = #{record.subTitle,jdbcType=VARCHAR},
      </if>
      <if test="record.isIconValid != null">
        is_icon_valid = #{record.isIconValid,jdbcType=TINYINT},
      </if>
      <if test="record.deviceAppStore != null">
        device_app_store = #{record.deviceAppStore,jdbcType=VARCHAR},
      </if>
      <if test="record.copySourceAccountId != null">
        copy_source_account_id = #{record.copySourceAccountId,jdbcType=INTEGER},
      </if>
      <if test="record.recordNumber != null">
        record_number = #{record.recordNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update res_app_package
    set id = #{record.id,jdbcType=INTEGER},
      account_id = #{record.accountId,jdbcType=INTEGER},
      name = #{record.name,jdbcType=VARCHAR},
      url = #{record.url,jdbcType=VARCHAR},
      package_name = #{record.packageName,jdbcType=VARCHAR},
      app_name = #{record.appName,jdbcType=VARCHAR},
      platform = #{record.platform,jdbcType=TINYINT},
      version = #{record.version,jdbcType=VARCHAR},
      size = #{record.size,jdbcType=INTEGER},
      md5 = #{record.md5,jdbcType=VARCHAR},
      icon_url = #{record.iconUrl,jdbcType=VARCHAR},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      internal_url = #{record.internalUrl,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=TINYINT},
      platform_status = #{record.platformStatus,jdbcType=TINYINT},
      developer_name = #{record.developerName,jdbcType=VARCHAR},
      authority_url = #{record.authorityUrl,jdbcType=VARCHAR},
      auth_code_list = #{record.authCodeList,jdbcType=VARCHAR},
      apk_update_time = #{record.apkUpdateTime,jdbcType=TIMESTAMP},
      privacy_policy = #{record.privacyPolicy,jdbcType=VARCHAR},
      dmp_app_id = #{record.dmpAppId,jdbcType=INTEGER},
      is_new_fly = #{record.isNewFly,jdbcType=TINYINT},
      sub_title = #{record.subTitle,jdbcType=VARCHAR},
      is_icon_valid = #{record.isIconValid,jdbcType=TINYINT},
      device_app_store = #{record.deviceAppStore,jdbcType=VARCHAR},
      copy_source_account_id = #{record.copySourceAccountId,jdbcType=INTEGER},
      record_number = #{record.recordNumber,jdbcType=VARCHAR},
      description = #{record.description,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update res_app_package
    set id = #{record.id,jdbcType=INTEGER},
      account_id = #{record.accountId,jdbcType=INTEGER},
      name = #{record.name,jdbcType=VARCHAR},
      url = #{record.url,jdbcType=VARCHAR},
      package_name = #{record.packageName,jdbcType=VARCHAR},
      app_name = #{record.appName,jdbcType=VARCHAR},
      platform = #{record.platform,jdbcType=TINYINT},
      version = #{record.version,jdbcType=VARCHAR},
      size = #{record.size,jdbcType=INTEGER},
      md5 = #{record.md5,jdbcType=VARCHAR},
      icon_url = #{record.iconUrl,jdbcType=VARCHAR},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      internal_url = #{record.internalUrl,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=TINYINT},
      platform_status = #{record.platformStatus,jdbcType=TINYINT},
      developer_name = #{record.developerName,jdbcType=VARCHAR},
      authority_url = #{record.authorityUrl,jdbcType=VARCHAR},
      auth_code_list = #{record.authCodeList,jdbcType=VARCHAR},
      apk_update_time = #{record.apkUpdateTime,jdbcType=TIMESTAMP},
      privacy_policy = #{record.privacyPolicy,jdbcType=VARCHAR},
      dmp_app_id = #{record.dmpAppId,jdbcType=INTEGER},
      is_new_fly = #{record.isNewFly,jdbcType=TINYINT},
      sub_title = #{record.subTitle,jdbcType=VARCHAR},
      is_icon_valid = #{record.isIconValid,jdbcType=TINYINT},
      device_app_store = #{record.deviceAppStore,jdbcType=VARCHAR},
      copy_source_account_id = #{record.copySourceAccountId,jdbcType=INTEGER},
      record_number = #{record.recordNumber,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.adp.cpc.po.ad.ResAppPackagePo">
    update res_app_package
    <set>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="url != null">
        url = #{url,jdbcType=VARCHAR},
      </if>
      <if test="packageName != null">
        package_name = #{packageName,jdbcType=VARCHAR},
      </if>
      <if test="appName != null">
        app_name = #{appName,jdbcType=VARCHAR},
      </if>
      <if test="platform != null">
        platform = #{platform,jdbcType=TINYINT},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=VARCHAR},
      </if>
      <if test="size != null">
        size = #{size,jdbcType=INTEGER},
      </if>
      <if test="md5 != null">
        md5 = #{md5,jdbcType=VARCHAR},
      </if>
      <if test="iconUrl != null">
        icon_url = #{iconUrl,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="internalUrl != null">
        internal_url = #{internalUrl,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="platformStatus != null">
        platform_status = #{platformStatus,jdbcType=TINYINT},
      </if>
      <if test="developerName != null">
        developer_name = #{developerName,jdbcType=VARCHAR},
      </if>
      <if test="authorityUrl != null">
        authority_url = #{authorityUrl,jdbcType=VARCHAR},
      </if>
      <if test="authCodeList != null">
        auth_code_list = #{authCodeList,jdbcType=VARCHAR},
      </if>
      <if test="apkUpdateTime != null">
        apk_update_time = #{apkUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="privacyPolicy != null">
        privacy_policy = #{privacyPolicy,jdbcType=VARCHAR},
      </if>
      <if test="dmpAppId != null">
        dmp_app_id = #{dmpAppId,jdbcType=INTEGER},
      </if>
      <if test="isNewFly != null">
        is_new_fly = #{isNewFly,jdbcType=TINYINT},
      </if>
      <if test="subTitle != null">
        sub_title = #{subTitle,jdbcType=VARCHAR},
      </if>
      <if test="isIconValid != null">
        is_icon_valid = #{isIconValid,jdbcType=TINYINT},
      </if>
      <if test="deviceAppStore != null">
        device_app_store = #{deviceAppStore,jdbcType=VARCHAR},
      </if>
      <if test="copySourceAccountId != null">
        copy_source_account_id = #{copySourceAccountId,jdbcType=INTEGER},
      </if>
      <if test="recordNumber != null">
        record_number = #{recordNumber,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.bilibili.adp.cpc.po.ad.ResAppPackagePo">
    update res_app_package
    set account_id = #{accountId,jdbcType=INTEGER},
      name = #{name,jdbcType=VARCHAR},
      url = #{url,jdbcType=VARCHAR},
      package_name = #{packageName,jdbcType=VARCHAR},
      app_name = #{appName,jdbcType=VARCHAR},
      platform = #{platform,jdbcType=TINYINT},
      version = #{version,jdbcType=VARCHAR},
      size = #{size,jdbcType=INTEGER},
      md5 = #{md5,jdbcType=VARCHAR},
      icon_url = #{iconUrl,jdbcType=VARCHAR},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      internal_url = #{internalUrl,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      platform_status = #{platformStatus,jdbcType=TINYINT},
      developer_name = #{developerName,jdbcType=VARCHAR},
      authority_url = #{authorityUrl,jdbcType=VARCHAR},
      auth_code_list = #{authCodeList,jdbcType=VARCHAR},
      apk_update_time = #{apkUpdateTime,jdbcType=TIMESTAMP},
      privacy_policy = #{privacyPolicy,jdbcType=VARCHAR},
      dmp_app_id = #{dmpAppId,jdbcType=INTEGER},
      is_new_fly = #{isNewFly,jdbcType=TINYINT},
      sub_title = #{subTitle,jdbcType=VARCHAR},
      is_icon_valid = #{isIconValid,jdbcType=TINYINT},
      device_app_store = #{deviceAppStore,jdbcType=VARCHAR},
      copy_source_account_id = #{copySourceAccountId,jdbcType=INTEGER},
      record_number = #{recordNumber,jdbcType=VARCHAR},
      description = #{description,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.adp.cpc.po.ad.ResAppPackagePo">
    update res_app_package
    set account_id = #{accountId,jdbcType=INTEGER},
      name = #{name,jdbcType=VARCHAR},
      url = #{url,jdbcType=VARCHAR},
      package_name = #{packageName,jdbcType=VARCHAR},
      app_name = #{appName,jdbcType=VARCHAR},
      platform = #{platform,jdbcType=TINYINT},
      version = #{version,jdbcType=VARCHAR},
      size = #{size,jdbcType=INTEGER},
      md5 = #{md5,jdbcType=VARCHAR},
      icon_url = #{iconUrl,jdbcType=VARCHAR},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      internal_url = #{internalUrl,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      platform_status = #{platformStatus,jdbcType=TINYINT},
      developer_name = #{developerName,jdbcType=VARCHAR},
      authority_url = #{authorityUrl,jdbcType=VARCHAR},
      auth_code_list = #{authCodeList,jdbcType=VARCHAR},
      apk_update_time = #{apkUpdateTime,jdbcType=TIMESTAMP},
      privacy_policy = #{privacyPolicy,jdbcType=VARCHAR},
      dmp_app_id = #{dmpAppId,jdbcType=INTEGER},
      is_new_fly = #{isNewFly,jdbcType=TINYINT},
      sub_title = #{subTitle,jdbcType=VARCHAR},
      is_icon_valid = #{isIconValid,jdbcType=TINYINT},
      device_app_store = #{deviceAppStore,jdbcType=VARCHAR},
      copy_source_account_id = #{copySourceAccountId,jdbcType=INTEGER},
      record_number = #{recordNumber,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.adp.cpc.po.ad.ResAppPackagePo">
    insert into res_app_package (id, account_id, name, 
      url, package_name, app_name, 
      platform, version, size, 
      md5, icon_url, ctime, 
      mtime, is_deleted, internal_url, 
      status, platform_status, developer_name, 
      authority_url, auth_code_list, apk_update_time, 
      privacy_policy, dmp_app_id, is_new_fly, 
      sub_title, is_icon_valid, device_app_store, 
      copy_source_account_id, record_number, description
      )
    values (#{id,jdbcType=INTEGER}, #{accountId,jdbcType=INTEGER}, #{name,jdbcType=VARCHAR}, 
      #{url,jdbcType=VARCHAR}, #{packageName,jdbcType=VARCHAR}, #{appName,jdbcType=VARCHAR}, 
      #{platform,jdbcType=TINYINT}, #{version,jdbcType=VARCHAR}, #{size,jdbcType=INTEGER}, 
      #{md5,jdbcType=VARCHAR}, #{iconUrl,jdbcType=VARCHAR}, #{ctime,jdbcType=TIMESTAMP}, 
      #{mtime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=TINYINT}, #{internalUrl,jdbcType=VARCHAR}, 
      #{status,jdbcType=TINYINT}, #{platformStatus,jdbcType=TINYINT}, #{developerName,jdbcType=VARCHAR}, 
      #{authorityUrl,jdbcType=VARCHAR}, #{authCodeList,jdbcType=VARCHAR}, #{apkUpdateTime,jdbcType=TIMESTAMP}, 
      #{privacyPolicy,jdbcType=VARCHAR}, #{dmpAppId,jdbcType=INTEGER}, #{isNewFly,jdbcType=TINYINT}, 
      #{subTitle,jdbcType=VARCHAR}, #{isIconValid,jdbcType=TINYINT}, #{deviceAppStore,jdbcType=VARCHAR}, 
      #{copySourceAccountId,jdbcType=INTEGER}, #{recordNumber,jdbcType=VARCHAR}, #{description,jdbcType=LONGVARCHAR}
      )
    <trim prefix="on duplicate key update" suffixOverrides=",">
      account_id = values(account_id),
      name = values(name),
      url = values(url),
      package_name = values(package_name),
      app_name = values(app_name),
      platform = values(platform),
      version = values(version),
      size = values(size),
      md5 = values(md5),
      icon_url = values(icon_url),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
      internal_url = values(internal_url),
      status = values(status),
      platform_status = values(platform_status),
      developer_name = values(developer_name),
      authority_url = values(authority_url),
      auth_code_list = values(auth_code_list),
      apk_update_time = values(apk_update_time),
      privacy_policy = values(privacy_policy),
      dmp_app_id = values(dmp_app_id),
      is_new_fly = values(is_new_fly),
      sub_title = values(sub_title),
      is_icon_valid = values(is_icon_valid),
      device_app_store = values(device_app_store),
      copy_source_account_id = values(copy_source_account_id),
      record_number = values(record_number),
      description = values(description),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      res_app_package
      (account_id,name,url,package_name,app_name,platform,version,size,md5,icon_url,ctime,mtime,is_deleted,internal_url,status,platform_status,developer_name,authority_url,auth_code_list,apk_update_time,privacy_policy,dmp_app_id,is_new_fly,sub_title,is_icon_valid,device_app_store,copy_source_account_id,record_number,description)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.accountId,jdbcType=INTEGER},
        #{item.name,jdbcType=VARCHAR},
        #{item.url,jdbcType=VARCHAR},
        #{item.packageName,jdbcType=VARCHAR},
        #{item.appName,jdbcType=VARCHAR},
        #{item.platform,jdbcType=TINYINT},
        #{item.version,jdbcType=VARCHAR},
        #{item.size,jdbcType=INTEGER},
        #{item.md5,jdbcType=VARCHAR},
        #{item.iconUrl,jdbcType=VARCHAR},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.internalUrl,jdbcType=VARCHAR},
        #{item.status,jdbcType=TINYINT},
        #{item.platformStatus,jdbcType=TINYINT},
        #{item.developerName,jdbcType=VARCHAR},
        #{item.authorityUrl,jdbcType=VARCHAR},
        #{item.authCodeList,jdbcType=VARCHAR},
        #{item.apkUpdateTime,jdbcType=TIMESTAMP},
        #{item.privacyPolicy,jdbcType=VARCHAR},
        #{item.dmpAppId,jdbcType=INTEGER},
        #{item.isNewFly,jdbcType=TINYINT},
        #{item.subTitle,jdbcType=VARCHAR},
        #{item.isIconValid,jdbcType=TINYINT},
        #{item.deviceAppStore,jdbcType=VARCHAR},
        #{item.copySourceAccountId,jdbcType=INTEGER},
        #{item.recordNumber,jdbcType=VARCHAR},
        #{item.description,jdbcType=LONGVARCHAR},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      res_app_package
      (account_id,name,url,package_name,app_name,platform,version,size,md5,icon_url,ctime,mtime,is_deleted,internal_url,status,platform_status,developer_name,authority_url,auth_code_list,apk_update_time,privacy_policy,dmp_app_id,is_new_fly,sub_title,is_icon_valid,device_app_store,copy_source_account_id,record_number,description)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.accountId,jdbcType=INTEGER},
        #{item.name,jdbcType=VARCHAR},
        #{item.url,jdbcType=VARCHAR},
        #{item.packageName,jdbcType=VARCHAR},
        #{item.appName,jdbcType=VARCHAR},
        #{item.platform,jdbcType=TINYINT},
        #{item.version,jdbcType=VARCHAR},
        #{item.size,jdbcType=INTEGER},
        #{item.md5,jdbcType=VARCHAR},
        #{item.iconUrl,jdbcType=VARCHAR},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.internalUrl,jdbcType=VARCHAR},
        #{item.status,jdbcType=TINYINT},
        #{item.platformStatus,jdbcType=TINYINT},
        #{item.developerName,jdbcType=VARCHAR},
        #{item.authorityUrl,jdbcType=VARCHAR},
        #{item.authCodeList,jdbcType=VARCHAR},
        #{item.apkUpdateTime,jdbcType=TIMESTAMP},
        #{item.privacyPolicy,jdbcType=VARCHAR},
        #{item.dmpAppId,jdbcType=INTEGER},
        #{item.isNewFly,jdbcType=TINYINT},
        #{item.subTitle,jdbcType=VARCHAR},
        #{item.isIconValid,jdbcType=TINYINT},
        #{item.deviceAppStore,jdbcType=VARCHAR},
        #{item.copySourceAccountId,jdbcType=INTEGER},
        #{item.recordNumber,jdbcType=VARCHAR},
        #{item.description,jdbcType=LONGVARCHAR},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      account_id = values(account_id),
      name = values(name),
      url = values(url),
      package_name = values(package_name),
      app_name = values(app_name),
      platform = values(platform),
      version = values(version),
      size = values(size),
      md5 = values(md5),
      icon_url = values(icon_url),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
      internal_url = values(internal_url),
      status = values(status),
      platform_status = values(platform_status),
      developer_name = values(developer_name),
      authority_url = values(authority_url),
      auth_code_list = values(auth_code_list),
      apk_update_time = values(apk_update_time),
      privacy_policy = values(privacy_policy),
      dmp_app_id = values(dmp_app_id),
      is_new_fly = values(is_new_fly),
      sub_title = values(sub_title),
      is_icon_valid = values(is_icon_valid),
      device_app_store = values(device_app_store),
      copy_source_account_id = values(copy_source_account_id),
      record_number = values(record_number),
      description = values(description),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.adp.cpc.po.ad.ResAppPackagePo">
    insert into res_app_package
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="url != null">
        url,
      </if>
      <if test="packageName != null">
        package_name,
      </if>
      <if test="appName != null">
        app_name,
      </if>
      <if test="platform != null">
        platform,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="size != null">
        size,
      </if>
      <if test="md5 != null">
        md5,
      </if>
      <if test="iconUrl != null">
        icon_url,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="internalUrl != null">
        internal_url,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="platformStatus != null">
        platform_status,
      </if>
      <if test="developerName != null">
        developer_name,
      </if>
      <if test="authorityUrl != null">
        authority_url,
      </if>
      <if test="authCodeList != null">
        auth_code_list,
      </if>
      <if test="apkUpdateTime != null">
        apk_update_time,
      </if>
      <if test="privacyPolicy != null">
        privacy_policy,
      </if>
      <if test="dmpAppId != null">
        dmp_app_id,
      </if>
      <if test="isNewFly != null">
        is_new_fly,
      </if>
      <if test="subTitle != null">
        sub_title,
      </if>
      <if test="isIconValid != null">
        is_icon_valid,
      </if>
      <if test="deviceAppStore != null">
        device_app_store,
      </if>
      <if test="copySourceAccountId != null">
        copy_source_account_id,
      </if>
      <if test="recordNumber != null">
        record_number,
      </if>
      <if test="description != null">
        description,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="url != null">
        #{url,jdbcType=VARCHAR},
      </if>
      <if test="packageName != null">
        #{packageName,jdbcType=VARCHAR},
      </if>
      <if test="appName != null">
        #{appName,jdbcType=VARCHAR},
      </if>
      <if test="platform != null">
        #{platform,jdbcType=TINYINT},
      </if>
      <if test="version != null">
        #{version,jdbcType=VARCHAR},
      </if>
      <if test="size != null">
        #{size,jdbcType=INTEGER},
      </if>
      <if test="md5 != null">
        #{md5,jdbcType=VARCHAR},
      </if>
      <if test="iconUrl != null">
        #{iconUrl,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="internalUrl != null">
        #{internalUrl,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="platformStatus != null">
        #{platformStatus,jdbcType=TINYINT},
      </if>
      <if test="developerName != null">
        #{developerName,jdbcType=VARCHAR},
      </if>
      <if test="authorityUrl != null">
        #{authorityUrl,jdbcType=VARCHAR},
      </if>
      <if test="authCodeList != null">
        #{authCodeList,jdbcType=VARCHAR},
      </if>
      <if test="apkUpdateTime != null">
        #{apkUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="privacyPolicy != null">
        #{privacyPolicy,jdbcType=VARCHAR},
      </if>
      <if test="dmpAppId != null">
        #{dmpAppId,jdbcType=INTEGER},
      </if>
      <if test="isNewFly != null">
        #{isNewFly,jdbcType=TINYINT},
      </if>
      <if test="subTitle != null">
        #{subTitle,jdbcType=VARCHAR},
      </if>
      <if test="isIconValid != null">
        #{isIconValid,jdbcType=TINYINT},
      </if>
      <if test="deviceAppStore != null">
        #{deviceAppStore,jdbcType=VARCHAR},
      </if>
      <if test="copySourceAccountId != null">
        #{copySourceAccountId,jdbcType=INTEGER},
      </if>
      <if test="recordNumber != null">
        #{recordNumber,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=LONGVARCHAR},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="accountId != null">
        account_id = values(account_id),
      </if>
      <if test="name != null">
        name = values(name),
      </if>
      <if test="url != null">
        url = values(url),
      </if>
      <if test="packageName != null">
        package_name = values(package_name),
      </if>
      <if test="appName != null">
        app_name = values(app_name),
      </if>
      <if test="platform != null">
        platform = values(platform),
      </if>
      <if test="version != null">
        version = values(version),
      </if>
      <if test="size != null">
        size = values(size),
      </if>
      <if test="md5 != null">
        md5 = values(md5),
      </if>
      <if test="iconUrl != null">
        icon_url = values(icon_url),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="internalUrl != null">
        internal_url = values(internal_url),
      </if>
      <if test="status != null">
        status = values(status),
      </if>
      <if test="platformStatus != null">
        platform_status = values(platform_status),
      </if>
      <if test="developerName != null">
        developer_name = values(developer_name),
      </if>
      <if test="authorityUrl != null">
        authority_url = values(authority_url),
      </if>
      <if test="authCodeList != null">
        auth_code_list = values(auth_code_list),
      </if>
      <if test="apkUpdateTime != null">
        apk_update_time = values(apk_update_time),
      </if>
      <if test="privacyPolicy != null">
        privacy_policy = values(privacy_policy),
      </if>
      <if test="dmpAppId != null">
        dmp_app_id = values(dmp_app_id),
      </if>
      <if test="isNewFly != null">
        is_new_fly = values(is_new_fly),
      </if>
      <if test="subTitle != null">
        sub_title = values(sub_title),
      </if>
      <if test="isIconValid != null">
        is_icon_valid = values(is_icon_valid),
      </if>
      <if test="deviceAppStore != null">
        device_app_store = values(device_app_store),
      </if>
      <if test="copySourceAccountId != null">
        copy_source_account_id = values(copy_source_account_id),
      </if>
      <if test="recordNumber != null">
        record_number = values(record_number),
      </if>
      <if test="description != null">
        description = values(description),
      </if>
    </trim>
  </insert>
</mapper>