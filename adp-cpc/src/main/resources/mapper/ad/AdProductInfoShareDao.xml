<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.adp.cpc.dao.ad.AdProductInfoShareDao">
  <resultMap id="BaseResultMap" type="com.bilibili.adp.cpc.po.ad.AdProductInfoSharePo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="ad_product_id" jdbcType="BIGINT" property="adProductId" />
    <result column="share_account_id" jdbcType="INTEGER" property="shareAccountId" />
    <result column="biz_status" jdbcType="INTEGER" property="bizStatus" />
    <result column="library_id" jdbcType="BIGINT" property="libraryId" />
    <result column="is_deleted" jdbcType="INTEGER" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="ad_product_name" jdbcType="VARCHAR" property="adProductName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, ad_product_id, share_account_id, biz_status, library_id, is_deleted, ctime, mtime, 
    ad_product_name
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.adp.cpc.po.ad.AdProductInfoSharePoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from ad_product_info_share
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ad_product_info_share
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from ad_product_info_share
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.adp.cpc.po.ad.AdProductInfoSharePoExample">
    delete from ad_product_info_share
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.adp.cpc.po.ad.AdProductInfoSharePo">
    insert into ad_product_info_share (id, ad_product_id, share_account_id, 
      biz_status, library_id, is_deleted, 
      ctime, mtime, ad_product_name
      )
    values (#{id,jdbcType=BIGINT}, #{adProductId,jdbcType=BIGINT}, #{shareAccountId,jdbcType=INTEGER}, 
      #{bizStatus,jdbcType=INTEGER}, #{libraryId,jdbcType=BIGINT}, #{isDeleted,jdbcType=INTEGER}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{adProductName,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.adp.cpc.po.ad.AdProductInfoSharePo">
    insert into ad_product_info_share
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="adProductId != null">
        ad_product_id,
      </if>
      <if test="shareAccountId != null">
        share_account_id,
      </if>
      <if test="bizStatus != null">
        biz_status,
      </if>
      <if test="libraryId != null">
        library_id,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="adProductName != null">
        ad_product_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="adProductId != null">
        #{adProductId,jdbcType=BIGINT},
      </if>
      <if test="shareAccountId != null">
        #{shareAccountId,jdbcType=INTEGER},
      </if>
      <if test="bizStatus != null">
        #{bizStatus,jdbcType=INTEGER},
      </if>
      <if test="libraryId != null">
        #{libraryId,jdbcType=BIGINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=INTEGER},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="adProductName != null">
        #{adProductName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.adp.cpc.po.ad.AdProductInfoSharePoExample" resultType="java.lang.Long">
    select count(*) from ad_product_info_share
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update ad_product_info_share
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.adProductId != null">
        ad_product_id = #{record.adProductId,jdbcType=BIGINT},
      </if>
      <if test="record.shareAccountId != null">
        share_account_id = #{record.shareAccountId,jdbcType=INTEGER},
      </if>
      <if test="record.bizStatus != null">
        biz_status = #{record.bizStatus,jdbcType=INTEGER},
      </if>
      <if test="record.libraryId != null">
        library_id = #{record.libraryId,jdbcType=BIGINT},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=INTEGER},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.adProductName != null">
        ad_product_name = #{record.adProductName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update ad_product_info_share
    set id = #{record.id,jdbcType=BIGINT},
      ad_product_id = #{record.adProductId,jdbcType=BIGINT},
      share_account_id = #{record.shareAccountId,jdbcType=INTEGER},
      biz_status = #{record.bizStatus,jdbcType=INTEGER},
      library_id = #{record.libraryId,jdbcType=BIGINT},
      is_deleted = #{record.isDeleted,jdbcType=INTEGER},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      ad_product_name = #{record.adProductName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.adp.cpc.po.ad.AdProductInfoSharePo">
    update ad_product_info_share
    <set>
      <if test="adProductId != null">
        ad_product_id = #{adProductId,jdbcType=BIGINT},
      </if>
      <if test="shareAccountId != null">
        share_account_id = #{shareAccountId,jdbcType=INTEGER},
      </if>
      <if test="bizStatus != null">
        biz_status = #{bizStatus,jdbcType=INTEGER},
      </if>
      <if test="libraryId != null">
        library_id = #{libraryId,jdbcType=BIGINT},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=INTEGER},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="adProductName != null">
        ad_product_name = #{adProductName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.adp.cpc.po.ad.AdProductInfoSharePo">
    update ad_product_info_share
    set ad_product_id = #{adProductId,jdbcType=BIGINT},
      share_account_id = #{shareAccountId,jdbcType=INTEGER},
      biz_status = #{bizStatus,jdbcType=INTEGER},
      library_id = #{libraryId,jdbcType=BIGINT},
      is_deleted = #{isDeleted,jdbcType=INTEGER},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      ad_product_name = #{adProductName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.adp.cpc.po.ad.AdProductInfoSharePo">
    insert into ad_product_info_share (id, ad_product_id, share_account_id, 
      biz_status, library_id, is_deleted, 
      ctime, mtime, ad_product_name
      )
    values (#{id,jdbcType=BIGINT}, #{adProductId,jdbcType=BIGINT}, #{shareAccountId,jdbcType=INTEGER}, 
      #{bizStatus,jdbcType=INTEGER}, #{libraryId,jdbcType=BIGINT}, #{isDeleted,jdbcType=INTEGER}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{adProductName,jdbcType=VARCHAR}
      )
    <trim prefix="on duplicate key update" suffixOverrides=",">
      ad_product_id = values(ad_product_id),
      share_account_id = values(share_account_id),
      biz_status = values(biz_status),
      library_id = values(library_id),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      ad_product_name = values(ad_product_name),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      ad_product_info_share
      (ad_product_id,share_account_id,biz_status,library_id,is_deleted,ctime,mtime,ad_product_name)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.adProductId,jdbcType=BIGINT},
        #{item.shareAccountId,jdbcType=INTEGER},
        #{item.bizStatus,jdbcType=INTEGER},
        #{item.libraryId,jdbcType=BIGINT},
        #{item.isDeleted,jdbcType=INTEGER},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.adProductName,jdbcType=VARCHAR},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      ad_product_info_share
      (ad_product_id,share_account_id,biz_status,library_id,is_deleted,ctime,mtime,ad_product_name)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.adProductId,jdbcType=BIGINT},
        #{item.shareAccountId,jdbcType=INTEGER},
        #{item.bizStatus,jdbcType=INTEGER},
        #{item.libraryId,jdbcType=BIGINT},
        #{item.isDeleted,jdbcType=INTEGER},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.adProductName,jdbcType=VARCHAR},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      ad_product_id = values(ad_product_id),
      share_account_id = values(share_account_id),
      biz_status = values(biz_status),
      library_id = values(library_id),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      ad_product_name = values(ad_product_name),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.adp.cpc.po.ad.AdProductInfoSharePo">
    insert into ad_product_info_share
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="adProductId != null">
        ad_product_id,
      </if>
      <if test="shareAccountId != null">
        share_account_id,
      </if>
      <if test="bizStatus != null">
        biz_status,
      </if>
      <if test="libraryId != null">
        library_id,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="adProductName != null">
        ad_product_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="adProductId != null">
        #{adProductId,jdbcType=BIGINT},
      </if>
      <if test="shareAccountId != null">
        #{shareAccountId,jdbcType=INTEGER},
      </if>
      <if test="bizStatus != null">
        #{bizStatus,jdbcType=INTEGER},
      </if>
      <if test="libraryId != null">
        #{libraryId,jdbcType=BIGINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=INTEGER},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="adProductName != null">
        #{adProductName,jdbcType=VARCHAR},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="adProductId != null">
        ad_product_id = values(ad_product_id),
      </if>
      <if test="shareAccountId != null">
        share_account_id = values(share_account_id),
      </if>
      <if test="bizStatus != null">
        biz_status = values(biz_status),
      </if>
      <if test="libraryId != null">
        library_id = values(library_id),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="adProductName != null">
        ad_product_name = values(ad_product_name),
      </if>
    </trim>
  </insert>
</mapper>