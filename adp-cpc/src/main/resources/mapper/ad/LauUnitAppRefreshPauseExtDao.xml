<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.adp.cpc.dao.ad.LauUnitAppRefreshPauseExtDao">
  <resultMap id="BaseResultMap" type="com.bilibili.adp.cpc.po.ad.LauUnitAppRefreshPausePo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="unit_id" jdbcType="INTEGER" property="unitId" />
    <result column="app_package_id" jdbcType="INTEGER" property="appPackageId" />
    <result column="page_id" jdbcType="BIGINT" property="pageId" />
    <result column="account_id" jdbcType="INTEGER" property="accountId" />
    <result column="deal_status" jdbcType="TINYINT" property="dealStatus" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="old_apk_url" jdbcType="VARCHAR" property="oldApkUrl" />
    <result column="new_apk_url" jdbcType="VARCHAR" property="newApkUrl" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, unit_id, app_package_id, page_id, account_id, deal_status, is_deleted, ctime, 
    mtime, old_apk_url, new_apk_url
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.adp.cpc.po.ad.LauUnitAppRefreshPausePoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from lau_unit_app_refresh_pause
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>

  <select id="selectMinIdByExample" parameterType="com.bilibili.adp.cpc.po.ad.LauUnitAppRefreshPausePoExample" resultType="Integer">
    select
    min(id)
    <include refid="Base_Column_List" />
    from lau_unit_app_refresh_pause
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>

  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from lau_unit_app_refresh_pause
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from lau_unit_app_refresh_pause
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.adp.cpc.po.ad.LauUnitAppRefreshPausePoExample">
    delete from lau_unit_app_refresh_pause
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.adp.cpc.po.ad.LauUnitAppRefreshPausePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_unit_app_refresh_pause (unit_id, app_package_id, page_id, 
      account_id, deal_status, is_deleted, 
      ctime, mtime, old_apk_url, 
      new_apk_url)
    values (#{unitId,jdbcType=INTEGER}, #{appPackageId,jdbcType=INTEGER}, #{pageId,jdbcType=BIGINT}, 
      #{accountId,jdbcType=INTEGER}, #{dealStatus,jdbcType=TINYINT}, #{isDeleted,jdbcType=TINYINT}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{oldApkUrl,jdbcType=VARCHAR}, 
      #{newApkUrl,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.adp.cpc.po.ad.LauUnitAppRefreshPausePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_unit_app_refresh_pause
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="unitId != null">
        unit_id,
      </if>
      <if test="appPackageId != null">
        app_package_id,
      </if>
      <if test="pageId != null">
        page_id,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="dealStatus != null">
        deal_status,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="oldApkUrl != null">
        old_apk_url,
      </if>
      <if test="newApkUrl != null">
        new_apk_url,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="unitId != null">
        #{unitId,jdbcType=INTEGER},
      </if>
      <if test="appPackageId != null">
        #{appPackageId,jdbcType=INTEGER},
      </if>
      <if test="pageId != null">
        #{pageId,jdbcType=BIGINT},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="dealStatus != null">
        #{dealStatus,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="oldApkUrl != null">
        #{oldApkUrl,jdbcType=VARCHAR},
      </if>
      <if test="newApkUrl != null">
        #{newApkUrl,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.adp.cpc.po.ad.LauUnitAppRefreshPausePoExample" resultType="java.lang.Long">
    select count(*) from lau_unit_app_refresh_pause
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update lau_unit_app_refresh_pause
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.unitId != null">
        unit_id = #{record.unitId,jdbcType=INTEGER},
      </if>
      <if test="record.appPackageId != null">
        app_package_id = #{record.appPackageId,jdbcType=INTEGER},
      </if>
      <if test="record.pageId != null">
        page_id = #{record.pageId,jdbcType=BIGINT},
      </if>
      <if test="record.accountId != null">
        account_id = #{record.accountId,jdbcType=INTEGER},
      </if>
      <if test="record.dealStatus != null">
        deal_status = #{record.dealStatus,jdbcType=TINYINT},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.oldApkUrl != null">
        old_apk_url = #{record.oldApkUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.newApkUrl != null">
        new_apk_url = #{record.newApkUrl,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update lau_unit_app_refresh_pause
    set id = #{record.id,jdbcType=INTEGER},
      unit_id = #{record.unitId,jdbcType=INTEGER},
      app_package_id = #{record.appPackageId,jdbcType=INTEGER},
      page_id = #{record.pageId,jdbcType=BIGINT},
      account_id = #{record.accountId,jdbcType=INTEGER},
      deal_status = #{record.dealStatus,jdbcType=TINYINT},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      old_apk_url = #{record.oldApkUrl,jdbcType=VARCHAR},
      new_apk_url = #{record.newApkUrl,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.adp.cpc.po.ad.LauUnitAppRefreshPausePo">
    update lau_unit_app_refresh_pause
    <set>
      <if test="unitId != null">
        unit_id = #{unitId,jdbcType=INTEGER},
      </if>
      <if test="appPackageId != null">
        app_package_id = #{appPackageId,jdbcType=INTEGER},
      </if>
      <if test="pageId != null">
        page_id = #{pageId,jdbcType=BIGINT},
      </if>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=INTEGER},
      </if>
      <if test="dealStatus != null">
        deal_status = #{dealStatus,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="oldApkUrl != null">
        old_apk_url = #{oldApkUrl,jdbcType=VARCHAR},
      </if>
      <if test="newApkUrl != null">
        new_apk_url = #{newApkUrl,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.adp.cpc.po.ad.LauUnitAppRefreshPausePo">
    update lau_unit_app_refresh_pause
    set unit_id = #{unitId,jdbcType=INTEGER},
      app_package_id = #{appPackageId,jdbcType=INTEGER},
      page_id = #{pageId,jdbcType=BIGINT},
      account_id = #{accountId,jdbcType=INTEGER},
      deal_status = #{dealStatus,jdbcType=TINYINT},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      old_apk_url = #{oldApkUrl,jdbcType=VARCHAR},
      new_apk_url = #{newApkUrl,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.adp.cpc.po.ad.LauUnitAppRefreshPausePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_unit_app_refresh_pause (unit_id, app_package_id, page_id, 
      account_id, deal_status, is_deleted, 
      ctime, mtime, old_apk_url, 
      new_apk_url)
    values (#{unitId,jdbcType=INTEGER}, #{appPackageId,jdbcType=INTEGER}, #{pageId,jdbcType=BIGINT}, 
      #{accountId,jdbcType=INTEGER}, #{dealStatus,jdbcType=TINYINT}, #{isDeleted,jdbcType=TINYINT}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{oldApkUrl,jdbcType=VARCHAR}, 
      #{newApkUrl,jdbcType=VARCHAR})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      unit_id = values(unit_id),
      app_package_id = values(app_package_id),
      page_id = values(page_id),
      account_id = values(account_id),
      deal_status = values(deal_status),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      old_apk_url = values(old_apk_url),
      new_apk_url = values(new_apk_url),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      lau_unit_app_refresh_pause
      (unit_id,app_package_id,page_id,account_id,deal_status,is_deleted,ctime,mtime,old_apk_url,new_apk_url)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.unitId,jdbcType=INTEGER},
        #{item.appPackageId,jdbcType=INTEGER},
        #{item.pageId,jdbcType=BIGINT},
        #{item.accountId,jdbcType=INTEGER},
        #{item.dealStatus,jdbcType=TINYINT},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.oldApkUrl,jdbcType=VARCHAR},
        #{item.newApkUrl,jdbcType=VARCHAR},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      lau_unit_app_refresh_pause
      (unit_id,app_package_id,page_id,account_id,deal_status,is_deleted,ctime,mtime,old_apk_url,new_apk_url)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.unitId,jdbcType=INTEGER},
        #{item.appPackageId,jdbcType=INTEGER},
        #{item.pageId,jdbcType=BIGINT},
        #{item.accountId,jdbcType=INTEGER},
        #{item.dealStatus,jdbcType=TINYINT},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.oldApkUrl,jdbcType=VARCHAR},
        #{item.newApkUrl,jdbcType=VARCHAR},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      unit_id = values(unit_id),
      app_package_id = values(app_package_id),
      page_id = values(page_id),
      account_id = values(account_id),
      deal_status = values(deal_status),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      old_apk_url = values(old_apk_url),
      new_apk_url = values(new_apk_url),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.adp.cpc.po.ad.LauUnitAppRefreshPausePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_unit_app_refresh_pause
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="unitId != null">
        unit_id,
      </if>
      <if test="appPackageId != null">
        app_package_id,
      </if>
      <if test="pageId != null">
        page_id,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="dealStatus != null">
        deal_status,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="oldApkUrl != null">
        old_apk_url,
      </if>
      <if test="newApkUrl != null">
        new_apk_url,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="unitId != null">
        #{unitId,jdbcType=INTEGER},
      </if>
      <if test="appPackageId != null">
        #{appPackageId,jdbcType=INTEGER},
      </if>
      <if test="pageId != null">
        #{pageId,jdbcType=BIGINT},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="dealStatus != null">
        #{dealStatus,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="oldApkUrl != null">
        #{oldApkUrl,jdbcType=VARCHAR},
      </if>
      <if test="newApkUrl != null">
        #{newApkUrl,jdbcType=VARCHAR},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="unitId != null">
        unit_id = values(unit_id),
      </if>
      <if test="appPackageId != null">
        app_package_id = values(app_package_id),
      </if>
      <if test="pageId != null">
        page_id = values(page_id),
      </if>
      <if test="accountId != null">
        account_id = values(account_id),
      </if>
      <if test="dealStatus != null">
        deal_status = values(deal_status),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="oldApkUrl != null">
        old_apk_url = values(old_apk_url),
      </if>
      <if test="newApkUrl != null">
        new_apk_url = values(new_apk_url),
      </if>
    </trim>
  </insert>
</mapper>