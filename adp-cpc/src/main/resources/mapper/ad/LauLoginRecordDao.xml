<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.adp.cpc.dao.ad.LauLoginRecordDao">
  <resultMap id="BaseResultMap" type="com.bilibili.adp.cpc.po.ad.LauLoginRecordPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="login_ip" jdbcType="VARCHAR" property="loginIp" />
    <result column="operator_id" jdbcType="BIGINT" property="operatorId" />
    <result column="operator_type" jdbcType="TINYINT" property="operatorType" />
    <result column="login_type" jdbcType="TINYINT" property="loginType" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
  </resultMap>
  <resultMap id="AliasBaseResultMap" type="com.bilibili.adp.cpc.po.ad.LauLoginRecordPo">
    <id column="lau_login_record_id" jdbcType="BIGINT" property="id" />
    <result column="lau_login_record_login_ip" jdbcType="VARCHAR" property="loginIp" />
    <result column="lau_login_record_operator_id" jdbcType="BIGINT" property="operatorId" />
    <result column="lau_login_record_operator_type" jdbcType="TINYINT" property="operatorType" />
    <result column="lau_login_record_login_type" jdbcType="TINYINT" property="loginType" />
    <result column="lau_login_record_ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="lau_login_record_mtime" jdbcType="TIMESTAMP" property="mtime" />
  </resultMap>
  <sql id="Alias_Base_Column_List">
    ${alias}.id as lau_login_record_id, ${alias}.login_ip as lau_login_record_login_ip, 
    ${alias}.operator_id as lau_login_record_operator_id, ${alias}.operator_type as lau_login_record_operator_type, 
    ${alias}.login_type as lau_login_record_login_type, ${alias}.ctime as lau_login_record_ctime, 
    ${alias}.mtime as lau_login_record_mtime
  </sql>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, login_ip, operator_id, operator_type, login_type, ctime, mtime
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.adp.cpc.po.ad.LauLoginRecordPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from lau_login_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from lau_login_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from lau_login_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.adp.cpc.po.ad.LauLoginRecordPoExample">
    delete from lau_login_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.adp.cpc.po.ad.LauLoginRecordPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_login_record (login_ip, operator_id, operator_type, 
      login_type, ctime, mtime
      )
    values (#{loginIp,jdbcType=VARCHAR}, #{operatorId,jdbcType=BIGINT}, #{operatorType,jdbcType=TINYINT}, 
      #{loginType,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.adp.cpc.po.ad.LauLoginRecordPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_login_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="loginIp != null">
        login_ip,
      </if>
      <if test="operatorId != null">
        operator_id,
      </if>
      <if test="operatorType != null">
        operator_type,
      </if>
      <if test="loginType != null">
        login_type,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="loginIp != null">
        #{loginIp,jdbcType=VARCHAR},
      </if>
      <if test="operatorId != null">
        #{operatorId,jdbcType=BIGINT},
      </if>
      <if test="operatorType != null">
        #{operatorType,jdbcType=TINYINT},
      </if>
      <if test="loginType != null">
        #{loginType,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.adp.cpc.po.ad.LauLoginRecordPoExample" resultType="java.lang.Long">
    select count(*) from lau_login_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update lau_login_record
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.loginIp != null">
        login_ip = #{record.loginIp,jdbcType=VARCHAR},
      </if>
      <if test="record.operatorId != null">
        operator_id = #{record.operatorId,jdbcType=BIGINT},
      </if>
      <if test="record.operatorType != null">
        operator_type = #{record.operatorType,jdbcType=TINYINT},
      </if>
      <if test="record.loginType != null">
        login_type = #{record.loginType,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update lau_login_record
    set id = #{record.id,jdbcType=BIGINT},
      login_ip = #{record.loginIp,jdbcType=VARCHAR},
      operator_id = #{record.operatorId,jdbcType=BIGINT},
      operator_type = #{record.operatorType,jdbcType=TINYINT},
      login_type = #{record.loginType,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.adp.cpc.po.ad.LauLoginRecordPo">
    update lau_login_record
    <set>
      <if test="loginIp != null">
        login_ip = #{loginIp,jdbcType=VARCHAR},
      </if>
      <if test="operatorId != null">
        operator_id = #{operatorId,jdbcType=BIGINT},
      </if>
      <if test="operatorType != null">
        operator_type = #{operatorType,jdbcType=TINYINT},
      </if>
      <if test="loginType != null">
        login_type = #{loginType,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.adp.cpc.po.ad.LauLoginRecordPo">
    update lau_login_record
    set login_ip = #{loginIp,jdbcType=VARCHAR},
      operator_id = #{operatorId,jdbcType=BIGINT},
      operator_type = #{operatorType,jdbcType=TINYINT},
      login_type = #{loginType,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.adp.cpc.po.ad.LauLoginRecordPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_login_record (login_ip, operator_id, operator_type, 
      login_type, ctime, mtime
      )
    values (#{loginIp,jdbcType=VARCHAR}, #{operatorId,jdbcType=BIGINT}, #{operatorType,jdbcType=TINYINT}, 
      #{loginType,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}
      )
    <trim prefix="on duplicate key update" suffixOverrides=",">
      login_ip = values(login_ip),
      operator_id = values(operator_id),
      operator_type = values(operator_type),
      login_type = values(login_type),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      lau_login_record
      (login_ip,operator_id,operator_type,login_type,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.loginIp,jdbcType=VARCHAR},
        #{item.operatorId,jdbcType=BIGINT},
        #{item.operatorType,jdbcType=TINYINT},
        #{item.loginType,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      lau_login_record
      (login_ip,operator_id,operator_type,login_type,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.loginIp,jdbcType=VARCHAR},
        #{item.operatorId,jdbcType=BIGINT},
        #{item.operatorType,jdbcType=TINYINT},
        #{item.loginType,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      login_ip = values(login_ip),
      operator_id = values(operator_id),
      operator_type = values(operator_type),
      login_type = values(login_type),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.adp.cpc.po.ad.LauLoginRecordPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_login_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="loginIp != null">
        login_ip,
      </if>
      <if test="operatorId != null">
        operator_id,
      </if>
      <if test="operatorType != null">
        operator_type,
      </if>
      <if test="loginType != null">
        login_type,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="loginIp != null">
        #{loginIp,jdbcType=VARCHAR},
      </if>
      <if test="operatorId != null">
        #{operatorId,jdbcType=BIGINT},
      </if>
      <if test="operatorType != null">
        #{operatorType,jdbcType=TINYINT},
      </if>
      <if test="loginType != null">
        #{loginType,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="loginIp != null">
        login_ip = values(login_ip),
      </if>
      <if test="operatorId != null">
        operator_id = values(operator_id),
      </if>
      <if test="operatorType != null">
        operator_type = values(operator_type),
      </if>
      <if test="loginType != null">
        login_type = values(login_type),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
    </trim>
  </insert>
</mapper>