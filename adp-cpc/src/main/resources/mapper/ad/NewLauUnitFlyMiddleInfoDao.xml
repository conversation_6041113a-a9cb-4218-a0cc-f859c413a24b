<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.adp.cpc.dao.ad.NewLauUnitFlyMiddleInfoDao">
  <resultMap id="BaseResultMap" type="com.bilibili.adp.cpc.po.ad.LauUnitFlyMiddleInfoPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="account_id" jdbcType="INTEGER" property="accountId" />
    <result column="unit_id" jdbcType="INTEGER" property="unitId" />
    <result column="promotion_purpose_type" jdbcType="INTEGER" property="promotionPurposeType" />
    <result column="dynamic_id" jdbcType="BIGINT" property="dynamicId" />
    <result column="dynamic_link" jdbcType="VARCHAR" property="dynamicLink" />
    <result column="sid" jdbcType="BIGINT" property="sid" />
    <result column="dynamic_type" jdbcType="TINYINT" property="dynamicType" />
    <result column="activity_link" jdbcType="VARCHAR" property="activityLink" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, account_id, unit_id, promotion_purpose_type, dynamic_id, dynamic_link, sid, dynamic_type, 
    activity_link, is_deleted, ctime, mtime
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.adp.cpc.po.ad.LauUnitFlyMiddleInfoPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from lau_unit_fly_middle_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from lau_unit_fly_middle_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from lau_unit_fly_middle_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.adp.cpc.po.ad.LauUnitFlyMiddleInfoPoExample">
    delete from lau_unit_fly_middle_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.adp.cpc.po.ad.LauUnitFlyMiddleInfoPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_unit_fly_middle_info (account_id, unit_id, promotion_purpose_type, 
      dynamic_id, dynamic_link, sid, 
      dynamic_type, activity_link, is_deleted, 
      ctime, mtime)
    values (#{accountId,jdbcType=INTEGER}, #{unitId,jdbcType=INTEGER}, #{promotionPurposeType,jdbcType=INTEGER}, 
      #{dynamicId,jdbcType=BIGINT}, #{dynamicLink,jdbcType=VARCHAR}, #{sid,jdbcType=BIGINT}, 
      #{dynamicType,jdbcType=TINYINT}, #{activityLink,jdbcType=VARCHAR}, #{isDeleted,jdbcType=TINYINT}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.adp.cpc.po.ad.LauUnitFlyMiddleInfoPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_unit_fly_middle_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        account_id,
      </if>
      <if test="unitId != null">
        unit_id,
      </if>
      <if test="promotionPurposeType != null">
        promotion_purpose_type,
      </if>
      <if test="dynamicId != null">
        dynamic_id,
      </if>
      <if test="dynamicLink != null">
        dynamic_link,
      </if>
      <if test="sid != null">
        sid,
      </if>
      <if test="dynamicType != null">
        dynamic_type,
      </if>
      <if test="activityLink != null">
        activity_link,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="unitId != null">
        #{unitId,jdbcType=INTEGER},
      </if>
      <if test="promotionPurposeType != null">
        #{promotionPurposeType,jdbcType=INTEGER},
      </if>
      <if test="dynamicId != null">
        #{dynamicId,jdbcType=BIGINT},
      </if>
      <if test="dynamicLink != null">
        #{dynamicLink,jdbcType=VARCHAR},
      </if>
      <if test="sid != null">
        #{sid,jdbcType=BIGINT},
      </if>
      <if test="dynamicType != null">
        #{dynamicType,jdbcType=TINYINT},
      </if>
      <if test="activityLink != null">
        #{activityLink,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.adp.cpc.po.ad.LauUnitFlyMiddleInfoPoExample" resultType="java.lang.Long">
    select count(*) from lau_unit_fly_middle_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update lau_unit_fly_middle_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.accountId != null">
        account_id = #{record.accountId,jdbcType=INTEGER},
      </if>
      <if test="record.unitId != null">
        unit_id = #{record.unitId,jdbcType=INTEGER},
      </if>
      <if test="record.promotionPurposeType != null">
        promotion_purpose_type = #{record.promotionPurposeType,jdbcType=INTEGER},
      </if>
      <if test="record.dynamicId != null">
        dynamic_id = #{record.dynamicId,jdbcType=BIGINT},
      </if>
      <if test="record.dynamicLink != null">
        dynamic_link = #{record.dynamicLink,jdbcType=VARCHAR},
      </if>
      <if test="record.sid != null">
        sid = #{record.sid,jdbcType=BIGINT},
      </if>
      <if test="record.dynamicType != null">
        dynamic_type = #{record.dynamicType,jdbcType=TINYINT},
      </if>
      <if test="record.activityLink != null">
        activity_link = #{record.activityLink,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update lau_unit_fly_middle_info
    set id = #{record.id,jdbcType=BIGINT},
      account_id = #{record.accountId,jdbcType=INTEGER},
      unit_id = #{record.unitId,jdbcType=INTEGER},
      promotion_purpose_type = #{record.promotionPurposeType,jdbcType=INTEGER},
      dynamic_id = #{record.dynamicId,jdbcType=BIGINT},
      dynamic_link = #{record.dynamicLink,jdbcType=VARCHAR},
      sid = #{record.sid,jdbcType=BIGINT},
      dynamic_type = #{record.dynamicType,jdbcType=TINYINT},
      activity_link = #{record.activityLink,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.adp.cpc.po.ad.LauUnitFlyMiddleInfoPo">
    update lau_unit_fly_middle_info
    <set>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=INTEGER},
      </if>
      <if test="unitId != null">
        unit_id = #{unitId,jdbcType=INTEGER},
      </if>
      <if test="promotionPurposeType != null">
        promotion_purpose_type = #{promotionPurposeType,jdbcType=INTEGER},
      </if>
      <if test="dynamicId != null">
        dynamic_id = #{dynamicId,jdbcType=BIGINT},
      </if>
      <if test="dynamicLink != null">
        dynamic_link = #{dynamicLink,jdbcType=VARCHAR},
      </if>
      <if test="sid != null">
        sid = #{sid,jdbcType=BIGINT},
      </if>
      <if test="dynamicType != null">
        dynamic_type = #{dynamicType,jdbcType=TINYINT},
      </if>
      <if test="activityLink != null">
        activity_link = #{activityLink,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.adp.cpc.po.ad.LauUnitFlyMiddleInfoPo">
    update lau_unit_fly_middle_info
    set account_id = #{accountId,jdbcType=INTEGER},
      unit_id = #{unitId,jdbcType=INTEGER},
      promotion_purpose_type = #{promotionPurposeType,jdbcType=INTEGER},
      dynamic_id = #{dynamicId,jdbcType=BIGINT},
      dynamic_link = #{dynamicLink,jdbcType=VARCHAR},
      sid = #{sid,jdbcType=BIGINT},
      dynamic_type = #{dynamicType,jdbcType=TINYINT},
      activity_link = #{activityLink,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.adp.cpc.po.ad.LauUnitFlyMiddleInfoPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_unit_fly_middle_info (account_id, unit_id, promotion_purpose_type, 
      dynamic_id, dynamic_link, sid, 
      dynamic_type, activity_link, is_deleted, 
      ctime, mtime)
    values (#{accountId,jdbcType=INTEGER}, #{unitId,jdbcType=INTEGER}, #{promotionPurposeType,jdbcType=INTEGER}, 
      #{dynamicId,jdbcType=BIGINT}, #{dynamicLink,jdbcType=VARCHAR}, #{sid,jdbcType=BIGINT}, 
      #{dynamicType,jdbcType=TINYINT}, #{activityLink,jdbcType=VARCHAR}, #{isDeleted,jdbcType=TINYINT}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      account_id = values(account_id),
      unit_id = values(unit_id),
      promotion_purpose_type = values(promotion_purpose_type),
      dynamic_id = values(dynamic_id),
      dynamic_link = values(dynamic_link),
      sid = values(sid),
      dynamic_type = values(dynamic_type),
      activity_link = values(activity_link),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      lau_unit_fly_middle_info
      (account_id,unit_id,promotion_purpose_type,dynamic_id,dynamic_link,sid,dynamic_type,activity_link,is_deleted,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.accountId,jdbcType=INTEGER},
        #{item.unitId,jdbcType=INTEGER},
        #{item.promotionPurposeType,jdbcType=INTEGER},
        #{item.dynamicId,jdbcType=BIGINT},
        #{item.dynamicLink,jdbcType=VARCHAR},
        #{item.sid,jdbcType=BIGINT},
        #{item.dynamicType,jdbcType=TINYINT},
        #{item.activityLink,jdbcType=VARCHAR},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      lau_unit_fly_middle_info
      (account_id,unit_id,promotion_purpose_type,dynamic_id,dynamic_link,sid,dynamic_type,activity_link,is_deleted,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.accountId,jdbcType=INTEGER},
        #{item.unitId,jdbcType=INTEGER},
        #{item.promotionPurposeType,jdbcType=INTEGER},
        #{item.dynamicId,jdbcType=BIGINT},
        #{item.dynamicLink,jdbcType=VARCHAR},
        #{item.sid,jdbcType=BIGINT},
        #{item.dynamicType,jdbcType=TINYINT},
        #{item.activityLink,jdbcType=VARCHAR},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      account_id = values(account_id),
      unit_id = values(unit_id),
      promotion_purpose_type = values(promotion_purpose_type),
      dynamic_id = values(dynamic_id),
      dynamic_link = values(dynamic_link),
      sid = values(sid),
      dynamic_type = values(dynamic_type),
      activity_link = values(activity_link),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.adp.cpc.po.ad.LauUnitFlyMiddleInfoPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_unit_fly_middle_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        account_id,
      </if>
      <if test="unitId != null">
        unit_id,
      </if>
      <if test="promotionPurposeType != null">
        promotion_purpose_type,
      </if>
      <if test="dynamicId != null">
        dynamic_id,
      </if>
      <if test="dynamicLink != null">
        dynamic_link,
      </if>
      <if test="sid != null">
        sid,
      </if>
      <if test="dynamicType != null">
        dynamic_type,
      </if>
      <if test="activityLink != null">
        activity_link,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="unitId != null">
        #{unitId,jdbcType=INTEGER},
      </if>
      <if test="promotionPurposeType != null">
        #{promotionPurposeType,jdbcType=INTEGER},
      </if>
      <if test="dynamicId != null">
        #{dynamicId,jdbcType=BIGINT},
      </if>
      <if test="dynamicLink != null">
        #{dynamicLink,jdbcType=VARCHAR},
      </if>
      <if test="sid != null">
        #{sid,jdbcType=BIGINT},
      </if>
      <if test="dynamicType != null">
        #{dynamicType,jdbcType=TINYINT},
      </if>
      <if test="activityLink != null">
        #{activityLink,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="accountId != null">
        account_id = values(account_id),
      </if>
      <if test="unitId != null">
        unit_id = values(unit_id),
      </if>
      <if test="promotionPurposeType != null">
        promotion_purpose_type = values(promotion_purpose_type),
      </if>
      <if test="dynamicId != null">
        dynamic_id = values(dynamic_id),
      </if>
      <if test="dynamicLink != null">
        dynamic_link = values(dynamic_link),
      </if>
      <if test="sid != null">
        sid = values(sid),
      </if>
      <if test="dynamicType != null">
        dynamic_type = values(dynamic_type),
      </if>
      <if test="activityLink != null">
        activity_link = values(activity_link),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
    </trim>
  </insert>
</mapper>