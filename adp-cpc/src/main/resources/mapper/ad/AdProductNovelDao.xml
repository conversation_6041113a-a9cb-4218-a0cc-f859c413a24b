<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.adp.cpc.dao.ad.AdProductNovelDao">
  <resultMap id="BaseResultMap" type="com.bilibili.adp.cpc.po.ad.AdProductNovelPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="account_id" jdbcType="INTEGER" property="accountId" />
    <result column="library_id" jdbcType="BIGINT" property="libraryId" />
    <result column="ad_product_id" jdbcType="BIGINT" property="adProductId" />
    <result column="biz_status" jdbcType="TINYINT" property="bizStatus" />
    <result column="novel_name" jdbcType="VARCHAR" property="novelName" />
    <result column="channel" jdbcType="VARCHAR" property="channel" />
    <result column="theme" jdbcType="VARCHAR" property="theme" />
    <result column="chapter_num" jdbcType="INTEGER" property="chapterNum" />
    <result column="words_num" jdbcType="VARCHAR" property="wordsNum" />
    <result column="update_status" jdbcType="VARCHAR" property="updateStatus" />
    <result column="author" jdbcType="VARCHAR" property="author" />
    <result column="brief" jdbcType="VARCHAR" property="brief" />
    <result column="chapter_price" jdbcType="VARCHAR" property="chapterPrice" />
    <result column="first_pay_chapter" jdbcType="VARCHAR" property="firstPayChapter" />
    <result column="realization_method" jdbcType="VARCHAR" property="realizationMethod" />
    <result column="first_category_code" jdbcType="BIGINT" property="firstCategoryCode" />
    <result column="second_category_code" jdbcType="BIGINT" property="secondCategoryCode" />
    <result column="third_category_code" jdbcType="BIGINT" property="thirdCategoryCode" />
    <result column="spu_code" jdbcType="BIGINT" property="spuCode" />
    <result column="sku_code" jdbcType="BIGINT" property="skuCode" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, account_id, library_id, ad_product_id, biz_status, novel_name, channel, theme, 
    chapter_num, words_num, update_status, author, brief, chapter_price, first_pay_chapter, 
    realization_method, first_category_code, second_category_code, third_category_code, 
    spu_code, sku_code, is_deleted, ctime, mtime
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.adp.cpc.po.ad.AdProductNovelPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from ad_product_novel
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ad_product_novel
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from ad_product_novel
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.adp.cpc.po.ad.AdProductNovelPoExample">
    delete from ad_product_novel
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.adp.cpc.po.ad.AdProductNovelPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ad_product_novel (account_id, library_id, ad_product_id, 
      biz_status, novel_name, channel, 
      theme, chapter_num, words_num, 
      update_status, author, brief, 
      chapter_price, first_pay_chapter, realization_method, 
      first_category_code, second_category_code, third_category_code, 
      spu_code, sku_code, is_deleted, 
      ctime, mtime)
    values (#{accountId,jdbcType=INTEGER}, #{libraryId,jdbcType=BIGINT}, #{adProductId,jdbcType=BIGINT}, 
      #{bizStatus,jdbcType=TINYINT}, #{novelName,jdbcType=VARCHAR}, #{channel,jdbcType=VARCHAR}, 
      #{theme,jdbcType=VARCHAR}, #{chapterNum,jdbcType=INTEGER}, #{wordsNum,jdbcType=VARCHAR}, 
      #{updateStatus,jdbcType=VARCHAR}, #{author,jdbcType=VARCHAR}, #{brief,jdbcType=VARCHAR}, 
      #{chapterPrice,jdbcType=VARCHAR}, #{firstPayChapter,jdbcType=VARCHAR}, #{realizationMethod,jdbcType=VARCHAR}, 
      #{firstCategoryCode,jdbcType=BIGINT}, #{secondCategoryCode,jdbcType=BIGINT}, #{thirdCategoryCode,jdbcType=BIGINT}, 
      #{spuCode,jdbcType=BIGINT}, #{skuCode,jdbcType=BIGINT}, #{isDeleted,jdbcType=TINYINT}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.adp.cpc.po.ad.AdProductNovelPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ad_product_novel
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        account_id,
      </if>
      <if test="libraryId != null">
        library_id,
      </if>
      <if test="adProductId != null">
        ad_product_id,
      </if>
      <if test="bizStatus != null">
        biz_status,
      </if>
      <if test="novelName != null">
        novel_name,
      </if>
      <if test="channel != null">
        channel,
      </if>
      <if test="theme != null">
        theme,
      </if>
      <if test="chapterNum != null">
        chapter_num,
      </if>
      <if test="wordsNum != null">
        words_num,
      </if>
      <if test="updateStatus != null">
        update_status,
      </if>
      <if test="author != null">
        author,
      </if>
      <if test="brief != null">
        brief,
      </if>
      <if test="chapterPrice != null">
        chapter_price,
      </if>
      <if test="firstPayChapter != null">
        first_pay_chapter,
      </if>
      <if test="realizationMethod != null">
        realization_method,
      </if>
      <if test="firstCategoryCode != null">
        first_category_code,
      </if>
      <if test="secondCategoryCode != null">
        second_category_code,
      </if>
      <if test="thirdCategoryCode != null">
        third_category_code,
      </if>
      <if test="spuCode != null">
        spu_code,
      </if>
      <if test="skuCode != null">
        sku_code,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="libraryId != null">
        #{libraryId,jdbcType=BIGINT},
      </if>
      <if test="adProductId != null">
        #{adProductId,jdbcType=BIGINT},
      </if>
      <if test="bizStatus != null">
        #{bizStatus,jdbcType=TINYINT},
      </if>
      <if test="novelName != null">
        #{novelName,jdbcType=VARCHAR},
      </if>
      <if test="channel != null">
        #{channel,jdbcType=VARCHAR},
      </if>
      <if test="theme != null">
        #{theme,jdbcType=VARCHAR},
      </if>
      <if test="chapterNum != null">
        #{chapterNum,jdbcType=INTEGER},
      </if>
      <if test="wordsNum != null">
        #{wordsNum,jdbcType=VARCHAR},
      </if>
      <if test="updateStatus != null">
        #{updateStatus,jdbcType=VARCHAR},
      </if>
      <if test="author != null">
        #{author,jdbcType=VARCHAR},
      </if>
      <if test="brief != null">
        #{brief,jdbcType=VARCHAR},
      </if>
      <if test="chapterPrice != null">
        #{chapterPrice,jdbcType=VARCHAR},
      </if>
      <if test="firstPayChapter != null">
        #{firstPayChapter,jdbcType=VARCHAR},
      </if>
      <if test="realizationMethod != null">
        #{realizationMethod,jdbcType=VARCHAR},
      </if>
      <if test="firstCategoryCode != null">
        #{firstCategoryCode,jdbcType=BIGINT},
      </if>
      <if test="secondCategoryCode != null">
        #{secondCategoryCode,jdbcType=BIGINT},
      </if>
      <if test="thirdCategoryCode != null">
        #{thirdCategoryCode,jdbcType=BIGINT},
      </if>
      <if test="spuCode != null">
        #{spuCode,jdbcType=BIGINT},
      </if>
      <if test="skuCode != null">
        #{skuCode,jdbcType=BIGINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.adp.cpc.po.ad.AdProductNovelPoExample" resultType="java.lang.Long">
    select count(*) from ad_product_novel
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update ad_product_novel
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.accountId != null">
        account_id = #{record.accountId,jdbcType=INTEGER},
      </if>
      <if test="record.libraryId != null">
        library_id = #{record.libraryId,jdbcType=BIGINT},
      </if>
      <if test="record.adProductId != null">
        ad_product_id = #{record.adProductId,jdbcType=BIGINT},
      </if>
      <if test="record.bizStatus != null">
        biz_status = #{record.bizStatus,jdbcType=TINYINT},
      </if>
      <if test="record.novelName != null">
        novel_name = #{record.novelName,jdbcType=VARCHAR},
      </if>
      <if test="record.channel != null">
        channel = #{record.channel,jdbcType=VARCHAR},
      </if>
      <if test="record.theme != null">
        theme = #{record.theme,jdbcType=VARCHAR},
      </if>
      <if test="record.chapterNum != null">
        chapter_num = #{record.chapterNum,jdbcType=INTEGER},
      </if>
      <if test="record.wordsNum != null">
        words_num = #{record.wordsNum,jdbcType=VARCHAR},
      </if>
      <if test="record.updateStatus != null">
        update_status = #{record.updateStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.author != null">
        author = #{record.author,jdbcType=VARCHAR},
      </if>
      <if test="record.brief != null">
        brief = #{record.brief,jdbcType=VARCHAR},
      </if>
      <if test="record.chapterPrice != null">
        chapter_price = #{record.chapterPrice,jdbcType=VARCHAR},
      </if>
      <if test="record.firstPayChapter != null">
        first_pay_chapter = #{record.firstPayChapter,jdbcType=VARCHAR},
      </if>
      <if test="record.realizationMethod != null">
        realization_method = #{record.realizationMethod,jdbcType=VARCHAR},
      </if>
      <if test="record.firstCategoryCode != null">
        first_category_code = #{record.firstCategoryCode,jdbcType=BIGINT},
      </if>
      <if test="record.secondCategoryCode != null">
        second_category_code = #{record.secondCategoryCode,jdbcType=BIGINT},
      </if>
      <if test="record.thirdCategoryCode != null">
        third_category_code = #{record.thirdCategoryCode,jdbcType=BIGINT},
      </if>
      <if test="record.spuCode != null">
        spu_code = #{record.spuCode,jdbcType=BIGINT},
      </if>
      <if test="record.skuCode != null">
        sku_code = #{record.skuCode,jdbcType=BIGINT},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update ad_product_novel
    set id = #{record.id,jdbcType=BIGINT},
      account_id = #{record.accountId,jdbcType=INTEGER},
      library_id = #{record.libraryId,jdbcType=BIGINT},
      ad_product_id = #{record.adProductId,jdbcType=BIGINT},
      biz_status = #{record.bizStatus,jdbcType=TINYINT},
      novel_name = #{record.novelName,jdbcType=VARCHAR},
      channel = #{record.channel,jdbcType=VARCHAR},
      theme = #{record.theme,jdbcType=VARCHAR},
      chapter_num = #{record.chapterNum,jdbcType=INTEGER},
      words_num = #{record.wordsNum,jdbcType=VARCHAR},
      update_status = #{record.updateStatus,jdbcType=VARCHAR},
      author = #{record.author,jdbcType=VARCHAR},
      brief = #{record.brief,jdbcType=VARCHAR},
      chapter_price = #{record.chapterPrice,jdbcType=VARCHAR},
      first_pay_chapter = #{record.firstPayChapter,jdbcType=VARCHAR},
      realization_method = #{record.realizationMethod,jdbcType=VARCHAR},
      first_category_code = #{record.firstCategoryCode,jdbcType=BIGINT},
      second_category_code = #{record.secondCategoryCode,jdbcType=BIGINT},
      third_category_code = #{record.thirdCategoryCode,jdbcType=BIGINT},
      spu_code = #{record.spuCode,jdbcType=BIGINT},
      sku_code = #{record.skuCode,jdbcType=BIGINT},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.adp.cpc.po.ad.AdProductNovelPo">
    update ad_product_novel
    <set>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=INTEGER},
      </if>
      <if test="libraryId != null">
        library_id = #{libraryId,jdbcType=BIGINT},
      </if>
      <if test="adProductId != null">
        ad_product_id = #{adProductId,jdbcType=BIGINT},
      </if>
      <if test="bizStatus != null">
        biz_status = #{bizStatus,jdbcType=TINYINT},
      </if>
      <if test="novelName != null">
        novel_name = #{novelName,jdbcType=VARCHAR},
      </if>
      <if test="channel != null">
        channel = #{channel,jdbcType=VARCHAR},
      </if>
      <if test="theme != null">
        theme = #{theme,jdbcType=VARCHAR},
      </if>
      <if test="chapterNum != null">
        chapter_num = #{chapterNum,jdbcType=INTEGER},
      </if>
      <if test="wordsNum != null">
        words_num = #{wordsNum,jdbcType=VARCHAR},
      </if>
      <if test="updateStatus != null">
        update_status = #{updateStatus,jdbcType=VARCHAR},
      </if>
      <if test="author != null">
        author = #{author,jdbcType=VARCHAR},
      </if>
      <if test="brief != null">
        brief = #{brief,jdbcType=VARCHAR},
      </if>
      <if test="chapterPrice != null">
        chapter_price = #{chapterPrice,jdbcType=VARCHAR},
      </if>
      <if test="firstPayChapter != null">
        first_pay_chapter = #{firstPayChapter,jdbcType=VARCHAR},
      </if>
      <if test="realizationMethod != null">
        realization_method = #{realizationMethod,jdbcType=VARCHAR},
      </if>
      <if test="firstCategoryCode != null">
        first_category_code = #{firstCategoryCode,jdbcType=BIGINT},
      </if>
      <if test="secondCategoryCode != null">
        second_category_code = #{secondCategoryCode,jdbcType=BIGINT},
      </if>
      <if test="thirdCategoryCode != null">
        third_category_code = #{thirdCategoryCode,jdbcType=BIGINT},
      </if>
      <if test="spuCode != null">
        spu_code = #{spuCode,jdbcType=BIGINT},
      </if>
      <if test="skuCode != null">
        sku_code = #{skuCode,jdbcType=BIGINT},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.adp.cpc.po.ad.AdProductNovelPo">
    update ad_product_novel
    set account_id = #{accountId,jdbcType=INTEGER},
      library_id = #{libraryId,jdbcType=BIGINT},
      ad_product_id = #{adProductId,jdbcType=BIGINT},
      biz_status = #{bizStatus,jdbcType=TINYINT},
      novel_name = #{novelName,jdbcType=VARCHAR},
      channel = #{channel,jdbcType=VARCHAR},
      theme = #{theme,jdbcType=VARCHAR},
      chapter_num = #{chapterNum,jdbcType=INTEGER},
      words_num = #{wordsNum,jdbcType=VARCHAR},
      update_status = #{updateStatus,jdbcType=VARCHAR},
      author = #{author,jdbcType=VARCHAR},
      brief = #{brief,jdbcType=VARCHAR},
      chapter_price = #{chapterPrice,jdbcType=VARCHAR},
      first_pay_chapter = #{firstPayChapter,jdbcType=VARCHAR},
      realization_method = #{realizationMethod,jdbcType=VARCHAR},
      first_category_code = #{firstCategoryCode,jdbcType=BIGINT},
      second_category_code = #{secondCategoryCode,jdbcType=BIGINT},
      third_category_code = #{thirdCategoryCode,jdbcType=BIGINT},
      spu_code = #{spuCode,jdbcType=BIGINT},
      sku_code = #{skuCode,jdbcType=BIGINT},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.adp.cpc.po.ad.AdProductNovelPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ad_product_novel (account_id, library_id, ad_product_id, 
      biz_status, novel_name, channel, 
      theme, chapter_num, words_num, 
      update_status, author, brief, 
      chapter_price, first_pay_chapter, realization_method, 
      first_category_code, second_category_code, third_category_code, 
      spu_code, sku_code, is_deleted, 
      ctime, mtime)
    values (#{accountId,jdbcType=INTEGER}, #{libraryId,jdbcType=BIGINT}, #{adProductId,jdbcType=BIGINT}, 
      #{bizStatus,jdbcType=TINYINT}, #{novelName,jdbcType=VARCHAR}, #{channel,jdbcType=VARCHAR}, 
      #{theme,jdbcType=VARCHAR}, #{chapterNum,jdbcType=INTEGER}, #{wordsNum,jdbcType=VARCHAR}, 
      #{updateStatus,jdbcType=VARCHAR}, #{author,jdbcType=VARCHAR}, #{brief,jdbcType=VARCHAR}, 
      #{chapterPrice,jdbcType=VARCHAR}, #{firstPayChapter,jdbcType=VARCHAR}, #{realizationMethod,jdbcType=VARCHAR}, 
      #{firstCategoryCode,jdbcType=BIGINT}, #{secondCategoryCode,jdbcType=BIGINT}, #{thirdCategoryCode,jdbcType=BIGINT}, 
      #{spuCode,jdbcType=BIGINT}, #{skuCode,jdbcType=BIGINT}, #{isDeleted,jdbcType=TINYINT}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      account_id = values(account_id),
      library_id = values(library_id),
      ad_product_id = values(ad_product_id),
      biz_status = values(biz_status),
      novel_name = values(novel_name),
      channel = values(channel),
      theme = values(theme),
      chapter_num = values(chapter_num),
      words_num = values(words_num),
      update_status = values(update_status),
      author = values(author),
      brief = values(brief),
      chapter_price = values(chapter_price),
      first_pay_chapter = values(first_pay_chapter),
      realization_method = values(realization_method),
      first_category_code = values(first_category_code),
      second_category_code = values(second_category_code),
      third_category_code = values(third_category_code),
      spu_code = values(spu_code),
      sku_code = values(sku_code),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      ad_product_novel
      (account_id,library_id,ad_product_id,biz_status,novel_name,channel,theme,chapter_num,words_num,update_status,author,brief,chapter_price,first_pay_chapter,realization_method,first_category_code,second_category_code,third_category_code,spu_code,sku_code,is_deleted,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.accountId,jdbcType=INTEGER},
        #{item.libraryId,jdbcType=BIGINT},
        #{item.adProductId,jdbcType=BIGINT},
        #{item.bizStatus,jdbcType=TINYINT},
        #{item.novelName,jdbcType=VARCHAR},
        #{item.channel,jdbcType=VARCHAR},
        #{item.theme,jdbcType=VARCHAR},
        #{item.chapterNum,jdbcType=INTEGER},
        #{item.wordsNum,jdbcType=VARCHAR},
        #{item.updateStatus,jdbcType=VARCHAR},
        #{item.author,jdbcType=VARCHAR},
        #{item.brief,jdbcType=VARCHAR},
        #{item.chapterPrice,jdbcType=VARCHAR},
        #{item.firstPayChapter,jdbcType=VARCHAR},
        #{item.realizationMethod,jdbcType=VARCHAR},
        #{item.firstCategoryCode,jdbcType=BIGINT},
        #{item.secondCategoryCode,jdbcType=BIGINT},
        #{item.thirdCategoryCode,jdbcType=BIGINT},
        #{item.spuCode,jdbcType=BIGINT},
        #{item.skuCode,jdbcType=BIGINT},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      ad_product_novel
      (account_id,library_id,ad_product_id,biz_status,novel_name,channel,theme,chapter_num,words_num,update_status,author,brief,chapter_price,first_pay_chapter,realization_method,first_category_code,second_category_code,third_category_code,spu_code,sku_code,is_deleted,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.accountId,jdbcType=INTEGER},
        #{item.libraryId,jdbcType=BIGINT},
        #{item.adProductId,jdbcType=BIGINT},
        #{item.bizStatus,jdbcType=TINYINT},
        #{item.novelName,jdbcType=VARCHAR},
        #{item.channel,jdbcType=VARCHAR},
        #{item.theme,jdbcType=VARCHAR},
        #{item.chapterNum,jdbcType=INTEGER},
        #{item.wordsNum,jdbcType=VARCHAR},
        #{item.updateStatus,jdbcType=VARCHAR},
        #{item.author,jdbcType=VARCHAR},
        #{item.brief,jdbcType=VARCHAR},
        #{item.chapterPrice,jdbcType=VARCHAR},
        #{item.firstPayChapter,jdbcType=VARCHAR},
        #{item.realizationMethod,jdbcType=VARCHAR},
        #{item.firstCategoryCode,jdbcType=BIGINT},
        #{item.secondCategoryCode,jdbcType=BIGINT},
        #{item.thirdCategoryCode,jdbcType=BIGINT},
        #{item.spuCode,jdbcType=BIGINT},
        #{item.skuCode,jdbcType=BIGINT},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      account_id = values(account_id),
      library_id = values(library_id),
      ad_product_id = values(ad_product_id),
      biz_status = values(biz_status),
      novel_name = values(novel_name),
      channel = values(channel),
      theme = values(theme),
      chapter_num = values(chapter_num),
      words_num = values(words_num),
      update_status = values(update_status),
      author = values(author),
      brief = values(brief),
      chapter_price = values(chapter_price),
      first_pay_chapter = values(first_pay_chapter),
      realization_method = values(realization_method),
      first_category_code = values(first_category_code),
      second_category_code = values(second_category_code),
      third_category_code = values(third_category_code),
      spu_code = values(spu_code),
      sku_code = values(sku_code),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.adp.cpc.po.ad.AdProductNovelPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ad_product_novel
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        account_id,
      </if>
      <if test="libraryId != null">
        library_id,
      </if>
      <if test="adProductId != null">
        ad_product_id,
      </if>
      <if test="bizStatus != null">
        biz_status,
      </if>
      <if test="novelName != null">
        novel_name,
      </if>
      <if test="channel != null">
        channel,
      </if>
      <if test="theme != null">
        theme,
      </if>
      <if test="chapterNum != null">
        chapter_num,
      </if>
      <if test="wordsNum != null">
        words_num,
      </if>
      <if test="updateStatus != null">
        update_status,
      </if>
      <if test="author != null">
        author,
      </if>
      <if test="brief != null">
        brief,
      </if>
      <if test="chapterPrice != null">
        chapter_price,
      </if>
      <if test="firstPayChapter != null">
        first_pay_chapter,
      </if>
      <if test="realizationMethod != null">
        realization_method,
      </if>
      <if test="firstCategoryCode != null">
        first_category_code,
      </if>
      <if test="secondCategoryCode != null">
        second_category_code,
      </if>
      <if test="thirdCategoryCode != null">
        third_category_code,
      </if>
      <if test="spuCode != null">
        spu_code,
      </if>
      <if test="skuCode != null">
        sku_code,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="libraryId != null">
        #{libraryId,jdbcType=BIGINT},
      </if>
      <if test="adProductId != null">
        #{adProductId,jdbcType=BIGINT},
      </if>
      <if test="bizStatus != null">
        #{bizStatus,jdbcType=TINYINT},
      </if>
      <if test="novelName != null">
        #{novelName,jdbcType=VARCHAR},
      </if>
      <if test="channel != null">
        #{channel,jdbcType=VARCHAR},
      </if>
      <if test="theme != null">
        #{theme,jdbcType=VARCHAR},
      </if>
      <if test="chapterNum != null">
        #{chapterNum,jdbcType=INTEGER},
      </if>
      <if test="wordsNum != null">
        #{wordsNum,jdbcType=VARCHAR},
      </if>
      <if test="updateStatus != null">
        #{updateStatus,jdbcType=VARCHAR},
      </if>
      <if test="author != null">
        #{author,jdbcType=VARCHAR},
      </if>
      <if test="brief != null">
        #{brief,jdbcType=VARCHAR},
      </if>
      <if test="chapterPrice != null">
        #{chapterPrice,jdbcType=VARCHAR},
      </if>
      <if test="firstPayChapter != null">
        #{firstPayChapter,jdbcType=VARCHAR},
      </if>
      <if test="realizationMethod != null">
        #{realizationMethod,jdbcType=VARCHAR},
      </if>
      <if test="firstCategoryCode != null">
        #{firstCategoryCode,jdbcType=BIGINT},
      </if>
      <if test="secondCategoryCode != null">
        #{secondCategoryCode,jdbcType=BIGINT},
      </if>
      <if test="thirdCategoryCode != null">
        #{thirdCategoryCode,jdbcType=BIGINT},
      </if>
      <if test="spuCode != null">
        #{spuCode,jdbcType=BIGINT},
      </if>
      <if test="skuCode != null">
        #{skuCode,jdbcType=BIGINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="accountId != null">
        account_id = values(account_id),
      </if>
      <if test="libraryId != null">
        library_id = values(library_id),
      </if>
      <if test="adProductId != null">
        ad_product_id = values(ad_product_id),
      </if>
      <if test="bizStatus != null">
        biz_status = values(biz_status),
      </if>
      <if test="novelName != null">
        novel_name = values(novel_name),
      </if>
      <if test="channel != null">
        channel = values(channel),
      </if>
      <if test="theme != null">
        theme = values(theme),
      </if>
      <if test="chapterNum != null">
        chapter_num = values(chapter_num),
      </if>
      <if test="wordsNum != null">
        words_num = values(words_num),
      </if>
      <if test="updateStatus != null">
        update_status = values(update_status),
      </if>
      <if test="author != null">
        author = values(author),
      </if>
      <if test="brief != null">
        brief = values(brief),
      </if>
      <if test="chapterPrice != null">
        chapter_price = values(chapter_price),
      </if>
      <if test="firstPayChapter != null">
        first_pay_chapter = values(first_pay_chapter),
      </if>
      <if test="realizationMethod != null">
        realization_method = values(realization_method),
      </if>
      <if test="firstCategoryCode != null">
        first_category_code = values(first_category_code),
      </if>
      <if test="secondCategoryCode != null">
        second_category_code = values(second_category_code),
      </if>
      <if test="thirdCategoryCode != null">
        third_category_code = values(third_category_code),
      </if>
      <if test="spuCode != null">
        spu_code = values(spu_code),
      </if>
      <if test="skuCode != null">
        sku_code = values(sku_code),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
    </trim>
  </insert>
</mapper>