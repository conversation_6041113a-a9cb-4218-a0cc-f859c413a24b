<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.adp.cpc.dao.ad.LauGameInfoExtDao">
  <resultMap id="BaseResultMap" type="com.bilibili.adp.cpc.po.ad.LauGameInfoPo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="game_base_id" jdbcType="INTEGER" property="gameBaseId" />
    <result column="game_name" jdbcType="VARCHAR" property="gameName" />
    <result column="bilibili_index" jdbcType="INTEGER" property="bilibiliIndex" />
    <result column="follower_num" jdbcType="INTEGER" property="followerNum" />
    <result column="booking_num" jdbcType="INTEGER" property="bookingNum" />
    <result column="comment_num" jdbcType="INTEGER" property="commentNum" />
    <result column="grade" jdbcType="DOUBLE" property="grade" />
    <result column="download_count" jdbcType="INTEGER" property="downloadCount" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="avid" jdbcType="BIGINT" property="avid" />
    <result column="duration" jdbcType="INTEGER" property="duration" />
    <result column="dmp_app_id" jdbcType="INTEGER" property="dmpAppId" />
    <result column="summary" jdbcType="VARCHAR" property="summary" />
    <result column="tags" jdbcType="VARCHAR" property="tags" />
    <result column="support_sub_pkg" jdbcType="INTEGER" property="supportSubPkg" />
    <result column="sub_pkg_status" jdbcType="TINYINT" property="subPkgStatus" />
    <result column="icon_url" jdbcType="VARCHAR" property="iconUrl" />
  </resultMap>
  <resultMap id="AliasBaseResultMap" type="com.bilibili.adp.cpc.po.ad.LauGameInfoPo">
    <id column="lau_game_info_id" jdbcType="INTEGER" property="id" />
    <result column="lau_game_info_game_base_id" jdbcType="INTEGER" property="gameBaseId" />
    <result column="lau_game_info_game_name" jdbcType="VARCHAR" property="gameName" />
    <result column="lau_game_info_bilibili_index" jdbcType="INTEGER" property="bilibiliIndex" />
    <result column="lau_game_info_follower_num" jdbcType="INTEGER" property="followerNum" />
    <result column="lau_game_info_booking_num" jdbcType="INTEGER" property="bookingNum" />
    <result column="lau_game_info_comment_num" jdbcType="INTEGER" property="commentNum" />
    <result column="lau_game_info_grade" jdbcType="DOUBLE" property="grade" />
    <result column="lau_game_info_download_count" jdbcType="INTEGER" property="downloadCount" />
    <result column="lau_game_info_is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="lau_game_info_ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="lau_game_info_mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="lau_game_info_avid" jdbcType="BIGINT" property="avid" />
    <result column="lau_game_info_duration" jdbcType="INTEGER" property="duration" />
    <result column="lau_game_info_dmp_app_id" jdbcType="INTEGER" property="dmpAppId" />
    <result column="lau_game_info_summary" jdbcType="VARCHAR" property="summary" />
    <result column="lau_game_info_tags" jdbcType="VARCHAR" property="tags" />
    <result column="lau_game_info_support_sub_pkg" jdbcType="INTEGER" property="supportSubPkg" />
    <result column="lau_game_info_sub_pkg_status" jdbcType="TINYINT" property="subPkgStatus" />
    <result column="lau_game_info_icon_url" jdbcType="VARCHAR" property="iconUrl" />
  </resultMap>
  <sql id="Alias_Base_Column_List">
    ${alias}.id as lau_game_info_id, ${alias}.game_base_id as lau_game_info_game_base_id,
    ${alias}.game_name as lau_game_info_game_name, ${alias}.bilibili_index as lau_game_info_bilibili_index,
    ${alias}.follower_num as lau_game_info_follower_num, ${alias}.booking_num as lau_game_info_booking_num,
    ${alias}.comment_num as lau_game_info_comment_num, ${alias}.grade as lau_game_info_grade,
    ${alias}.download_count as lau_game_info_download_count, ${alias}.is_deleted as lau_game_info_is_deleted,
    ${alias}.ctime as lau_game_info_ctime, ${alias}.mtime as lau_game_info_mtime, ${alias}.avid as lau_game_info_avid,
    ${alias}.duration as lau_game_info_duration, ${alias}.dmp_app_id as lau_game_info_dmp_app_id,
    ${alias}.summary as lau_game_info_summary, ${alias}.tags as lau_game_info_tags, ${alias}.support_sub_pkg as lau_game_info_support_sub_pkg,
    ${alias}.sub_pkg_status as lau_game_info_sub_pkg_status, ${alias}.icon_url as lau_game_info_icon_url
  </sql>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, game_base_id, game_name, bilibili_index, follower_num, booking_num, comment_num,
    grade, download_count, is_deleted, ctime, mtime, avid, duration, dmp_app_id, summary,
    tags, support_sub_pkg, sub_pkg_status, icon_url
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.adp.cpc.po.ad.LauGameInfoPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from lau_game_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from lau_game_info
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from lau_game_info
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.adp.cpc.po.ad.LauGameInfoPoExample">
    delete from lau_game_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.adp.cpc.po.ad.LauGameInfoPo">
    insert into lau_game_info (id, game_base_id, game_name,
                               bilibili_index, follower_num, booking_num,
                               comment_num, grade, download_count,
                               is_deleted, ctime, mtime,
                               avid, duration, dmp_app_id,
                               summary, tags, support_sub_pkg,
                               sub_pkg_status, icon_url)
    values (#{id,jdbcType=INTEGER}, #{gameBaseId,jdbcType=INTEGER}, #{gameName,jdbcType=VARCHAR},
            #{bilibiliIndex,jdbcType=INTEGER}, #{followerNum,jdbcType=INTEGER}, #{bookingNum,jdbcType=INTEGER},
            #{commentNum,jdbcType=INTEGER}, #{grade,jdbcType=DOUBLE}, #{downloadCount,jdbcType=INTEGER},
            #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP},
            #{avid,jdbcType=BIGINT}, #{duration,jdbcType=INTEGER}, #{dmpAppId,jdbcType=INTEGER},
            #{summary,jdbcType=VARCHAR}, #{tags,jdbcType=VARCHAR}, #{supportSubPkg,jdbcType=INTEGER},
            #{subPkgStatus,jdbcType=TINYINT}, #{iconUrl,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.adp.cpc.po.ad.LauGameInfoPo">
    insert into lau_game_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="gameBaseId != null">
        game_base_id,
      </if>
      <if test="gameName != null">
        game_name,
      </if>
      <if test="bilibiliIndex != null">
        bilibili_index,
      </if>
      <if test="followerNum != null">
        follower_num,
      </if>
      <if test="bookingNum != null">
        booking_num,
      </if>
      <if test="commentNum != null">
        comment_num,
      </if>
      <if test="grade != null">
        grade,
      </if>
      <if test="downloadCount != null">
        download_count,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="avid != null">
        avid,
      </if>
      <if test="duration != null">
        duration,
      </if>
      <if test="dmpAppId != null">
        dmp_app_id,
      </if>
      <if test="summary != null">
        summary,
      </if>
      <if test="tags != null">
        tags,
      </if>
      <if test="supportSubPkg != null">
        support_sub_pkg,
      </if>
      <if test="subPkgStatus != null">
        sub_pkg_status,
      </if>
      <if test="iconUrl != null">
        icon_url,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="gameBaseId != null">
        #{gameBaseId,jdbcType=INTEGER},
      </if>
      <if test="gameName != null">
        #{gameName,jdbcType=VARCHAR},
      </if>
      <if test="bilibiliIndex != null">
        #{bilibiliIndex,jdbcType=INTEGER},
      </if>
      <if test="followerNum != null">
        #{followerNum,jdbcType=INTEGER},
      </if>
      <if test="bookingNum != null">
        #{bookingNum,jdbcType=INTEGER},
      </if>
      <if test="commentNum != null">
        #{commentNum,jdbcType=INTEGER},
      </if>
      <if test="grade != null">
        #{grade,jdbcType=DOUBLE},
      </if>
      <if test="downloadCount != null">
        #{downloadCount,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="avid != null">
        #{avid,jdbcType=BIGINT},
      </if>
      <if test="duration != null">
        #{duration,jdbcType=INTEGER},
      </if>
      <if test="dmpAppId != null">
        #{dmpAppId,jdbcType=INTEGER},
      </if>
      <if test="summary != null">
        #{summary,jdbcType=VARCHAR},
      </if>
      <if test="tags != null">
        #{tags,jdbcType=VARCHAR},
      </if>
      <if test="supportSubPkg != null">
        #{supportSubPkg,jdbcType=INTEGER},
      </if>
      <if test="subPkgStatus != null">
        #{subPkgStatus,jdbcType=TINYINT},
      </if>
      <if test="iconUrl != null">
        #{iconUrl,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.adp.cpc.po.ad.LauGameInfoPoExample" resultType="java.lang.Long">
    select count(*) from lau_game_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update lau_game_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.gameBaseId != null">
        game_base_id = #{record.gameBaseId,jdbcType=INTEGER},
      </if>
      <if test="record.gameName != null">
        game_name = #{record.gameName,jdbcType=VARCHAR},
      </if>
      <if test="record.bilibiliIndex != null">
        bilibili_index = #{record.bilibiliIndex,jdbcType=INTEGER},
      </if>
      <if test="record.followerNum != null">
        follower_num = #{record.followerNum,jdbcType=INTEGER},
      </if>
      <if test="record.bookingNum != null">
        booking_num = #{record.bookingNum,jdbcType=INTEGER},
      </if>
      <if test="record.commentNum != null">
        comment_num = #{record.commentNum,jdbcType=INTEGER},
      </if>
      <if test="record.grade != null">
        grade = #{record.grade,jdbcType=DOUBLE},
      </if>
      <if test="record.downloadCount != null">
        download_count = #{record.downloadCount,jdbcType=INTEGER},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.avid != null">
        avid = #{record.avid,jdbcType=BIGINT},
      </if>
      <if test="record.duration != null">
        duration = #{record.duration,jdbcType=INTEGER},
      </if>
      <if test="record.dmpAppId != null">
        dmp_app_id = #{record.dmpAppId,jdbcType=INTEGER},
      </if>
      <if test="record.summary != null">
        summary = #{record.summary,jdbcType=VARCHAR},
      </if>
      <if test="record.tags != null">
        tags = #{record.tags,jdbcType=VARCHAR},
      </if>
      <if test="record.supportSubPkg != null">
        support_sub_pkg = #{record.supportSubPkg,jdbcType=INTEGER},
      </if>
      <if test="record.subPkgStatus != null">
        sub_pkg_status = #{record.subPkgStatus,jdbcType=TINYINT},
      </if>
      <if test="record.iconUrl != null">
        icon_url = #{record.iconUrl,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update lau_game_info
    set id = #{record.id,jdbcType=INTEGER},
    game_base_id = #{record.gameBaseId,jdbcType=INTEGER},
    game_name = #{record.gameName,jdbcType=VARCHAR},
    bilibili_index = #{record.bilibiliIndex,jdbcType=INTEGER},
    follower_num = #{record.followerNum,jdbcType=INTEGER},
    booking_num = #{record.bookingNum,jdbcType=INTEGER},
    comment_num = #{record.commentNum,jdbcType=INTEGER},
    grade = #{record.grade,jdbcType=DOUBLE},
    download_count = #{record.downloadCount,jdbcType=INTEGER},
    is_deleted = #{record.isDeleted,jdbcType=TINYINT},
    ctime = #{record.ctime,jdbcType=TIMESTAMP},
    mtime = #{record.mtime,jdbcType=TIMESTAMP},
    avid = #{record.avid,jdbcType=BIGINT},
    duration = #{record.duration,jdbcType=INTEGER},
    dmp_app_id = #{record.dmpAppId,jdbcType=INTEGER},
    summary = #{record.summary,jdbcType=VARCHAR},
    tags = #{record.tags,jdbcType=VARCHAR},
    support_sub_pkg = #{record.supportSubPkg,jdbcType=INTEGER},
    sub_pkg_status = #{record.subPkgStatus,jdbcType=TINYINT},
    icon_url = #{record.iconUrl,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.adp.cpc.po.ad.LauGameInfoPo">
    update lau_game_info
    <set>
      <if test="gameBaseId != null">
        game_base_id = #{gameBaseId,jdbcType=INTEGER},
      </if>
      <if test="gameName != null">
        game_name = #{gameName,jdbcType=VARCHAR},
      </if>
      <if test="bilibiliIndex != null">
        bilibili_index = #{bilibiliIndex,jdbcType=INTEGER},
      </if>
      <if test="followerNum != null">
        follower_num = #{followerNum,jdbcType=INTEGER},
      </if>
      <if test="bookingNum != null">
        booking_num = #{bookingNum,jdbcType=INTEGER},
      </if>
      <if test="commentNum != null">
        comment_num = #{commentNum,jdbcType=INTEGER},
      </if>
      <if test="grade != null">
        grade = #{grade,jdbcType=DOUBLE},
      </if>
      <if test="downloadCount != null">
        download_count = #{downloadCount,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="avid != null">
        avid = #{avid,jdbcType=BIGINT},
      </if>
      <if test="duration != null">
        duration = #{duration,jdbcType=INTEGER},
      </if>
      <if test="dmpAppId != null">
        dmp_app_id = #{dmpAppId,jdbcType=INTEGER},
      </if>
      <if test="summary != null">
        summary = #{summary,jdbcType=VARCHAR},
      </if>
      <if test="tags != null">
        tags = #{tags,jdbcType=VARCHAR},
      </if>
      <if test="supportSubPkg != null">
        support_sub_pkg = #{supportSubPkg,jdbcType=INTEGER},
      </if>
      <if test="subPkgStatus != null">
        sub_pkg_status = #{subPkgStatus,jdbcType=TINYINT},
      </if>
      <if test="iconUrl != null">
        icon_url = #{iconUrl,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.adp.cpc.po.ad.LauGameInfoPo">
    update lau_game_info
    set game_base_id = #{gameBaseId,jdbcType=INTEGER},
        game_name = #{gameName,jdbcType=VARCHAR},
        bilibili_index = #{bilibiliIndex,jdbcType=INTEGER},
        follower_num = #{followerNum,jdbcType=INTEGER},
        booking_num = #{bookingNum,jdbcType=INTEGER},
        comment_num = #{commentNum,jdbcType=INTEGER},
        grade = #{grade,jdbcType=DOUBLE},
        download_count = #{downloadCount,jdbcType=INTEGER},
        is_deleted = #{isDeleted,jdbcType=TINYINT},
        ctime = #{ctime,jdbcType=TIMESTAMP},
        mtime = #{mtime,jdbcType=TIMESTAMP},
        avid = #{avid,jdbcType=BIGINT},
        duration = #{duration,jdbcType=INTEGER},
        dmp_app_id = #{dmpAppId,jdbcType=INTEGER},
        summary = #{summary,jdbcType=VARCHAR},
        tags = #{tags,jdbcType=VARCHAR},
        support_sub_pkg = #{supportSubPkg,jdbcType=INTEGER},
        sub_pkg_status = #{subPkgStatus,jdbcType=TINYINT},
        icon_url = #{iconUrl,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.adp.cpc.po.ad.LauGameInfoPo">
    insert into lau_game_info (id, game_base_id, game_name,
    bilibili_index, follower_num, booking_num,
    comment_num, grade, download_count,
    is_deleted, ctime, mtime,
    avid, duration, dmp_app_id,
    summary, tags, support_sub_pkg,
    sub_pkg_status, icon_url)
    values (#{id,jdbcType=INTEGER}, #{gameBaseId,jdbcType=INTEGER}, #{gameName,jdbcType=VARCHAR},
    #{bilibiliIndex,jdbcType=INTEGER}, #{followerNum,jdbcType=INTEGER}, #{bookingNum,jdbcType=INTEGER},
    #{commentNum,jdbcType=INTEGER}, #{grade,jdbcType=DOUBLE}, #{downloadCount,jdbcType=INTEGER},
    #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP},
    #{avid,jdbcType=BIGINT}, #{duration,jdbcType=INTEGER}, #{dmpAppId,jdbcType=INTEGER},
    #{summary,jdbcType=VARCHAR}, #{tags,jdbcType=VARCHAR}, #{supportSubPkg,jdbcType=INTEGER},
    #{subPkgStatus,jdbcType=TINYINT}, #{iconUrl,jdbcType=VARCHAR})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      game_base_id = values(game_base_id),
      game_name = values(game_name),
      bilibili_index = values(bilibili_index),
      follower_num = values(follower_num),
      booking_num = values(booking_num),
      comment_num = values(comment_num),
      grade = values(grade),
      download_count = values(download_count),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      avid = values(avid),
      duration = values(duration),
      dmp_app_id = values(dmp_app_id),
      summary = values(summary),
      tags = values(tags),
      support_sub_pkg = values(support_sub_pkg),
      sub_pkg_status = values(sub_pkg_status),
      icon_url = values(icon_url),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into
    lau_game_info
    (game_base_id,game_name,bilibili_index,follower_num,booking_num,comment_num,grade,download_count,is_deleted,ctime,mtime,avid,duration,dmp_app_id,summary,tags,support_sub_pkg,sub_pkg_status,icon_url)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.gameBaseId,jdbcType=INTEGER},
        #{item.gameName,jdbcType=VARCHAR},
        #{item.bilibiliIndex,jdbcType=INTEGER},
        #{item.followerNum,jdbcType=INTEGER},
        #{item.bookingNum,jdbcType=INTEGER},
        #{item.commentNum,jdbcType=INTEGER},
        #{item.grade,jdbcType=DOUBLE},
        #{item.downloadCount,jdbcType=INTEGER},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.avid,jdbcType=BIGINT},
        #{item.duration,jdbcType=INTEGER},
        #{item.dmpAppId,jdbcType=INTEGER},
        #{item.summary,jdbcType=VARCHAR},
        #{item.tags,jdbcType=VARCHAR},
        #{item.supportSubPkg,jdbcType=INTEGER},
        #{item.subPkgStatus,jdbcType=TINYINT},
        #{item.iconUrl,jdbcType=VARCHAR},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into
    lau_game_info
    (game_base_id,game_name,bilibili_index,follower_num,booking_num,comment_num,grade,download_count,is_deleted,ctime,mtime,avid,duration,dmp_app_id,summary,tags,support_sub_pkg,sub_pkg_status,icon_url)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.gameBaseId,jdbcType=INTEGER},
        #{item.gameName,jdbcType=VARCHAR},
        #{item.bilibiliIndex,jdbcType=INTEGER},
        #{item.followerNum,jdbcType=INTEGER},
        #{item.bookingNum,jdbcType=INTEGER},
        #{item.commentNum,jdbcType=INTEGER},
        #{item.grade,jdbcType=DOUBLE},
        #{item.downloadCount,jdbcType=INTEGER},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.avid,jdbcType=BIGINT},
        #{item.duration,jdbcType=INTEGER},
        #{item.dmpAppId,jdbcType=INTEGER},
        #{item.summary,jdbcType=VARCHAR},
        #{item.tags,jdbcType=VARCHAR},
        #{item.supportSubPkg,jdbcType=INTEGER},
        #{item.subPkgStatus,jdbcType=TINYINT},
        #{item.iconUrl,jdbcType=VARCHAR},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      game_base_id = values(game_base_id),
      game_name = values(game_name),
      bilibili_index = values(bilibili_index),
      follower_num = values(follower_num),
      booking_num = values(booking_num),
      comment_num = values(comment_num),
      grade = values(grade),
      download_count = values(download_count),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      avid = values(avid),
      duration = values(duration),
      dmp_app_id = values(dmp_app_id),
      summary = values(summary),
      tags = values(tags),
      support_sub_pkg = values(support_sub_pkg),
      sub_pkg_status = values(sub_pkg_status),
      icon_url = values(icon_url),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.adp.cpc.po.ad.LauGameInfoPo">
    insert into lau_game_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="gameBaseId != null">
        game_base_id,
      </if>
      <if test="gameName != null">
        game_name,
      </if>
      <if test="bilibiliIndex != null">
        bilibili_index,
      </if>
      <if test="followerNum != null">
        follower_num,
      </if>
      <if test="bookingNum != null">
        booking_num,
      </if>
      <if test="commentNum != null">
        comment_num,
      </if>
      <if test="grade != null">
        grade,
      </if>
      <if test="downloadCount != null">
        download_count,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="avid != null">
        avid,
      </if>
      <if test="duration != null">
        duration,
      </if>
      <if test="dmpAppId != null">
        dmp_app_id,
      </if>
      <if test="summary != null">
        summary,
      </if>
      <if test="tags != null">
        tags,
      </if>
      <if test="supportSubPkg != null">
        support_sub_pkg,
      </if>
      <if test="subPkgStatus != null">
        sub_pkg_status,
      </if>
      <if test="iconUrl != null">
        icon_url,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="gameBaseId != null">
        #{gameBaseId,jdbcType=INTEGER},
      </if>
      <if test="gameName != null">
        #{gameName,jdbcType=VARCHAR},
      </if>
      <if test="bilibiliIndex != null">
        #{bilibiliIndex,jdbcType=INTEGER},
      </if>
      <if test="followerNum != null">
        #{followerNum,jdbcType=INTEGER},
      </if>
      <if test="bookingNum != null">
        #{bookingNum,jdbcType=INTEGER},
      </if>
      <if test="commentNum != null">
        #{commentNum,jdbcType=INTEGER},
      </if>
      <if test="grade != null">
        #{grade,jdbcType=DOUBLE},
      </if>
      <if test="downloadCount != null">
        #{downloadCount,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="avid != null">
        #{avid,jdbcType=BIGINT},
      </if>
      <if test="duration != null">
        #{duration,jdbcType=INTEGER},
      </if>
      <if test="dmpAppId != null">
        #{dmpAppId,jdbcType=INTEGER},
      </if>
      <if test="summary != null">
        #{summary,jdbcType=VARCHAR},
      </if>
      <if test="tags != null">
        #{tags,jdbcType=VARCHAR},
      </if>
      <if test="supportSubPkg != null">
        #{supportSubPkg,jdbcType=INTEGER},
      </if>
      <if test="subPkgStatus != null">
        #{subPkgStatus,jdbcType=TINYINT},
      </if>
      <if test="iconUrl != null">
        #{iconUrl,jdbcType=VARCHAR},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="gameBaseId != null">
        game_base_id = values(game_base_id),
      </if>
      <if test="gameName != null">
        game_name = values(game_name),
      </if>
      <if test="bilibiliIndex != null">
        bilibili_index = values(bilibili_index),
      </if>
      <if test="followerNum != null">
        follower_num = values(follower_num),
      </if>
      <if test="bookingNum != null">
        booking_num = values(booking_num),
      </if>
      <if test="commentNum != null">
        comment_num = values(comment_num),
      </if>
      <if test="grade != null">
        grade = values(grade),
      </if>
      <if test="downloadCount != null">
        download_count = values(download_count),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="avid != null">
        avid = values(avid),
      </if>
      <if test="duration != null">
        duration = values(duration),
      </if>
      <if test="dmpAppId != null">
        dmp_app_id = values(dmp_app_id),
      </if>
      <if test="summary != null">
        summary = values(summary),
      </if>
      <if test="tags != null">
        tags = values(tags),
      </if>
      <if test="supportSubPkg != null">
        support_sub_pkg = values(support_sub_pkg),
      </if>
      <if test="subPkgStatus != null">
        sub_pkg_status = values(sub_pkg_status),
      </if>
      <if test="iconUrl != null">
        icon_url = values(icon_url),
      </if>
    </trim>
  </insert>
</mapper>