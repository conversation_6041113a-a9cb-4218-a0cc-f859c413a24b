<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.adp.cpc.dao.ad.LauMiniGameDao">
  <resultMap id="BaseResultMap" type="com.bilibili.adp.cpc.po.ad.LauMiniGamePo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="origin_id" jdbcType="VARCHAR" property="originId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="game_url" jdbcType="VARCHAR" property="gameUrl" />
    <result column="state" jdbcType="TINYINT" property="state" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="account_id" jdbcType="INTEGER" property="accountId" />
    <result column="copy_source_account_id" jdbcType="INTEGER" property="copySourceAccountId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, origin_id, name, game_url, state, is_deleted, ctime, mtime, account_id, copy_source_account_id
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.adp.cpc.po.ad.LauMiniGamePoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from lau_mini_game
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from lau_mini_game
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from lau_mini_game
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.adp.cpc.po.ad.LauMiniGamePoExample">
    delete from lau_mini_game
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.adp.cpc.po.ad.LauMiniGamePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_mini_game (origin_id, name, game_url, 
      state, is_deleted, ctime, 
      mtime, account_id, copy_source_account_id
      )
    values (#{originId,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{gameUrl,jdbcType=VARCHAR}, 
      #{state,jdbcType=TINYINT}, #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, 
      #{mtime,jdbcType=TIMESTAMP}, #{accountId,jdbcType=INTEGER}, #{copySourceAccountId,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.adp.cpc.po.ad.LauMiniGamePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_mini_game
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="originId != null">
        origin_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="gameUrl != null">
        game_url,
      </if>
      <if test="state != null">
        state,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="copySourceAccountId != null">
        copy_source_account_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="originId != null">
        #{originId,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="gameUrl != null">
        #{gameUrl,jdbcType=VARCHAR},
      </if>
      <if test="state != null">
        #{state,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="copySourceAccountId != null">
        #{copySourceAccountId,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.adp.cpc.po.ad.LauMiniGamePoExample" resultType="java.lang.Long">
    select count(*) from lau_mini_game
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update lau_mini_game
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.originId != null">
        origin_id = #{record.originId,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.gameUrl != null">
        game_url = #{record.gameUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.state != null">
        state = #{record.state,jdbcType=TINYINT},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.accountId != null">
        account_id = #{record.accountId,jdbcType=INTEGER},
      </if>
      <if test="record.copySourceAccountId != null">
        copy_source_account_id = #{record.copySourceAccountId,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update lau_mini_game
    set id = #{record.id,jdbcType=INTEGER},
      origin_id = #{record.originId,jdbcType=VARCHAR},
      name = #{record.name,jdbcType=VARCHAR},
      game_url = #{record.gameUrl,jdbcType=VARCHAR},
      state = #{record.state,jdbcType=TINYINT},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      account_id = #{record.accountId,jdbcType=INTEGER},
      copy_source_account_id = #{record.copySourceAccountId,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.adp.cpc.po.ad.LauMiniGamePo">
    update lau_mini_game
    <set>
      <if test="originId != null">
        origin_id = #{originId,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="gameUrl != null">
        game_url = #{gameUrl,jdbcType=VARCHAR},
      </if>
      <if test="state != null">
        state = #{state,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=INTEGER},
      </if>
      <if test="copySourceAccountId != null">
        copy_source_account_id = #{copySourceAccountId,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.adp.cpc.po.ad.LauMiniGamePo">
    update lau_mini_game
    set origin_id = #{originId,jdbcType=VARCHAR},
      name = #{name,jdbcType=VARCHAR},
      game_url = #{gameUrl,jdbcType=VARCHAR},
      state = #{state,jdbcType=TINYINT},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      account_id = #{accountId,jdbcType=INTEGER},
      copy_source_account_id = #{copySourceAccountId,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.adp.cpc.po.ad.LauMiniGamePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_mini_game (origin_id, name, game_url, 
      state, is_deleted, ctime, 
      mtime, account_id, copy_source_account_id
      )
    values (#{originId,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{gameUrl,jdbcType=VARCHAR}, 
      #{state,jdbcType=TINYINT}, #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, 
      #{mtime,jdbcType=TIMESTAMP}, #{accountId,jdbcType=INTEGER}, #{copySourceAccountId,jdbcType=INTEGER}
      )
    <trim prefix="on duplicate key update" suffixOverrides=",">
      origin_id = values(origin_id),
      name = values(name),
      game_url = values(game_url),
      state = values(state),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      account_id = values(account_id),
      copy_source_account_id = values(copy_source_account_id),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      lau_mini_game
      (origin_id,name,game_url,state,is_deleted,ctime,mtime,account_id,copy_source_account_id)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.originId,jdbcType=VARCHAR},
        #{item.name,jdbcType=VARCHAR},
        #{item.gameUrl,jdbcType=VARCHAR},
        #{item.state,jdbcType=TINYINT},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.accountId,jdbcType=INTEGER},
        #{item.copySourceAccountId,jdbcType=INTEGER},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      lau_mini_game
      (origin_id,name,game_url,state,is_deleted,ctime,mtime,account_id,copy_source_account_id)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.originId,jdbcType=VARCHAR},
        #{item.name,jdbcType=VARCHAR},
        #{item.gameUrl,jdbcType=VARCHAR},
        #{item.state,jdbcType=TINYINT},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.accountId,jdbcType=INTEGER},
        #{item.copySourceAccountId,jdbcType=INTEGER},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      origin_id = values(origin_id),
      name = values(name),
      game_url = values(game_url),
      state = values(state),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      account_id = values(account_id),
      copy_source_account_id = values(copy_source_account_id),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.adp.cpc.po.ad.LauMiniGamePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_mini_game
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="originId != null">
        origin_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="gameUrl != null">
        game_url,
      </if>
      <if test="state != null">
        state,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="copySourceAccountId != null">
        copy_source_account_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="originId != null">
        #{originId,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="gameUrl != null">
        #{gameUrl,jdbcType=VARCHAR},
      </if>
      <if test="state != null">
        #{state,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="copySourceAccountId != null">
        #{copySourceAccountId,jdbcType=INTEGER},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="originId != null">
        origin_id = values(origin_id),
      </if>
      <if test="name != null">
        name = values(name),
      </if>
      <if test="gameUrl != null">
        game_url = values(game_url),
      </if>
      <if test="state != null">
        state = values(state),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="accountId != null">
        account_id = values(account_id),
      </if>
      <if test="copySourceAccountId != null">
        copy_source_account_id = values(copy_source_account_id),
      </if>
    </trim>
  </insert>
</mapper>