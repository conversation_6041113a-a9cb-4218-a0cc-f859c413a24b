<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.adp.cpc.dao.ad.AdProductBorrowLoanDao">
  <resultMap id="BaseResultMap" type="com.bilibili.adp.cpc.po.ad.AdProductBorrowLoanPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="account_id" jdbcType="INTEGER" property="accountId" />
    <result column="library_id" jdbcType="BIGINT" property="libraryId" />
    <result column="ad_product_id" jdbcType="BIGINT" property="adProductId" />
    <result column="biz_status" jdbcType="TINYINT" property="bizStatus" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="max_year_rate" jdbcType="VARCHAR" property="maxYearRate" />
    <result column="max_credit_limit" jdbcType="VARCHAR" property="maxCreditLimit" />
    <result column="average_credit_limit" jdbcType="VARCHAR" property="averageCreditLimit" />
    <result column="loan_term" jdbcType="VARCHAR" property="loanTerm" />
    <result column="loan_usage" jdbcType="VARCHAR" property="loanUsage" />
    <result column="daily_interest" jdbcType="VARCHAR" property="dailyInterest" />
    <result column="interest_type" jdbcType="VARCHAR" property="interestType" />
    <result column="rapayment_type" jdbcType="VARCHAR" property="rapaymentType" />
    <result column="early_repayment" jdbcType="VARCHAR" property="earlyRepayment" />
    <result column="early_repayment_penalty" jdbcType="VARCHAR" property="earlyRepaymentPenalty" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="first_category_code" jdbcType="BIGINT" property="firstCategoryCode" />
    <result column="second_category_code" jdbcType="BIGINT" property="secondCategoryCode" />
    <result column="third_category_code" jdbcType="BIGINT" property="thirdCategoryCode" />
    <result column="spu_code" jdbcType="BIGINT" property="spuCode" />
    <result column="sku_code" jdbcType="BIGINT" property="skuCode" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, account_id, library_id, ad_product_id, biz_status, name, max_year_rate, max_credit_limit, 
    average_credit_limit, loan_term, loan_usage, daily_interest, interest_type, rapayment_type, 
    early_repayment, early_repayment_penalty, remark, first_category_code, second_category_code, 
    third_category_code, spu_code, sku_code, is_deleted, ctime, mtime
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.adp.cpc.po.ad.AdProductBorrowLoanPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from ad_product_borrow_loan
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ad_product_borrow_loan
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from ad_product_borrow_loan
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.adp.cpc.po.ad.AdProductBorrowLoanPoExample">
    delete from ad_product_borrow_loan
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.adp.cpc.po.ad.AdProductBorrowLoanPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ad_product_borrow_loan (account_id, library_id, ad_product_id, 
      biz_status, name, max_year_rate, 
      max_credit_limit, average_credit_limit, loan_term, 
      loan_usage, daily_interest, interest_type, 
      rapayment_type, early_repayment, early_repayment_penalty, 
      remark, first_category_code, second_category_code, 
      third_category_code, spu_code, sku_code, 
      is_deleted, ctime, mtime
      )
    values (#{accountId,jdbcType=INTEGER}, #{libraryId,jdbcType=BIGINT}, #{adProductId,jdbcType=BIGINT}, 
      #{bizStatus,jdbcType=TINYINT}, #{name,jdbcType=VARCHAR}, #{maxYearRate,jdbcType=VARCHAR}, 
      #{maxCreditLimit,jdbcType=VARCHAR}, #{averageCreditLimit,jdbcType=VARCHAR}, #{loanTerm,jdbcType=VARCHAR}, 
      #{loanUsage,jdbcType=VARCHAR}, #{dailyInterest,jdbcType=VARCHAR}, #{interestType,jdbcType=VARCHAR}, 
      #{rapaymentType,jdbcType=VARCHAR}, #{earlyRepayment,jdbcType=VARCHAR}, #{earlyRepaymentPenalty,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR}, #{firstCategoryCode,jdbcType=BIGINT}, #{secondCategoryCode,jdbcType=BIGINT}, 
      #{thirdCategoryCode,jdbcType=BIGINT}, #{spuCode,jdbcType=BIGINT}, #{skuCode,jdbcType=BIGINT}, 
      #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.adp.cpc.po.ad.AdProductBorrowLoanPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ad_product_borrow_loan
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        account_id,
      </if>
      <if test="libraryId != null">
        library_id,
      </if>
      <if test="adProductId != null">
        ad_product_id,
      </if>
      <if test="bizStatus != null">
        biz_status,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="maxYearRate != null">
        max_year_rate,
      </if>
      <if test="maxCreditLimit != null">
        max_credit_limit,
      </if>
      <if test="averageCreditLimit != null">
        average_credit_limit,
      </if>
      <if test="loanTerm != null">
        loan_term,
      </if>
      <if test="loanUsage != null">
        loan_usage,
      </if>
      <if test="dailyInterest != null">
        daily_interest,
      </if>
      <if test="interestType != null">
        interest_type,
      </if>
      <if test="rapaymentType != null">
        rapayment_type,
      </if>
      <if test="earlyRepayment != null">
        early_repayment,
      </if>
      <if test="earlyRepaymentPenalty != null">
        early_repayment_penalty,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="firstCategoryCode != null">
        first_category_code,
      </if>
      <if test="secondCategoryCode != null">
        second_category_code,
      </if>
      <if test="thirdCategoryCode != null">
        third_category_code,
      </if>
      <if test="spuCode != null">
        spu_code,
      </if>
      <if test="skuCode != null">
        sku_code,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="libraryId != null">
        #{libraryId,jdbcType=BIGINT},
      </if>
      <if test="adProductId != null">
        #{adProductId,jdbcType=BIGINT},
      </if>
      <if test="bizStatus != null">
        #{bizStatus,jdbcType=TINYINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="maxYearRate != null">
        #{maxYearRate,jdbcType=VARCHAR},
      </if>
      <if test="maxCreditLimit != null">
        #{maxCreditLimit,jdbcType=VARCHAR},
      </if>
      <if test="averageCreditLimit != null">
        #{averageCreditLimit,jdbcType=VARCHAR},
      </if>
      <if test="loanTerm != null">
        #{loanTerm,jdbcType=VARCHAR},
      </if>
      <if test="loanUsage != null">
        #{loanUsage,jdbcType=VARCHAR},
      </if>
      <if test="dailyInterest != null">
        #{dailyInterest,jdbcType=VARCHAR},
      </if>
      <if test="interestType != null">
        #{interestType,jdbcType=VARCHAR},
      </if>
      <if test="rapaymentType != null">
        #{rapaymentType,jdbcType=VARCHAR},
      </if>
      <if test="earlyRepayment != null">
        #{earlyRepayment,jdbcType=VARCHAR},
      </if>
      <if test="earlyRepaymentPenalty != null">
        #{earlyRepaymentPenalty,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="firstCategoryCode != null">
        #{firstCategoryCode,jdbcType=BIGINT},
      </if>
      <if test="secondCategoryCode != null">
        #{secondCategoryCode,jdbcType=BIGINT},
      </if>
      <if test="thirdCategoryCode != null">
        #{thirdCategoryCode,jdbcType=BIGINT},
      </if>
      <if test="spuCode != null">
        #{spuCode,jdbcType=BIGINT},
      </if>
      <if test="skuCode != null">
        #{skuCode,jdbcType=BIGINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.adp.cpc.po.ad.AdProductBorrowLoanPoExample" resultType="java.lang.Long">
    select count(*) from ad_product_borrow_loan
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update ad_product_borrow_loan
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.accountId != null">
        account_id = #{record.accountId,jdbcType=INTEGER},
      </if>
      <if test="record.libraryId != null">
        library_id = #{record.libraryId,jdbcType=BIGINT},
      </if>
      <if test="record.adProductId != null">
        ad_product_id = #{record.adProductId,jdbcType=BIGINT},
      </if>
      <if test="record.bizStatus != null">
        biz_status = #{record.bizStatus,jdbcType=TINYINT},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.maxYearRate != null">
        max_year_rate = #{record.maxYearRate,jdbcType=VARCHAR},
      </if>
      <if test="record.maxCreditLimit != null">
        max_credit_limit = #{record.maxCreditLimit,jdbcType=VARCHAR},
      </if>
      <if test="record.averageCreditLimit != null">
        average_credit_limit = #{record.averageCreditLimit,jdbcType=VARCHAR},
      </if>
      <if test="record.loanTerm != null">
        loan_term = #{record.loanTerm,jdbcType=VARCHAR},
      </if>
      <if test="record.loanUsage != null">
        loan_usage = #{record.loanUsage,jdbcType=VARCHAR},
      </if>
      <if test="record.dailyInterest != null">
        daily_interest = #{record.dailyInterest,jdbcType=VARCHAR},
      </if>
      <if test="record.interestType != null">
        interest_type = #{record.interestType,jdbcType=VARCHAR},
      </if>
      <if test="record.rapaymentType != null">
        rapayment_type = #{record.rapaymentType,jdbcType=VARCHAR},
      </if>
      <if test="record.earlyRepayment != null">
        early_repayment = #{record.earlyRepayment,jdbcType=VARCHAR},
      </if>
      <if test="record.earlyRepaymentPenalty != null">
        early_repayment_penalty = #{record.earlyRepaymentPenalty,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.firstCategoryCode != null">
        first_category_code = #{record.firstCategoryCode,jdbcType=BIGINT},
      </if>
      <if test="record.secondCategoryCode != null">
        second_category_code = #{record.secondCategoryCode,jdbcType=BIGINT},
      </if>
      <if test="record.thirdCategoryCode != null">
        third_category_code = #{record.thirdCategoryCode,jdbcType=BIGINT},
      </if>
      <if test="record.spuCode != null">
        spu_code = #{record.spuCode,jdbcType=BIGINT},
      </if>
      <if test="record.skuCode != null">
        sku_code = #{record.skuCode,jdbcType=BIGINT},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update ad_product_borrow_loan
    set id = #{record.id,jdbcType=BIGINT},
      account_id = #{record.accountId,jdbcType=INTEGER},
      library_id = #{record.libraryId,jdbcType=BIGINT},
      ad_product_id = #{record.adProductId,jdbcType=BIGINT},
      biz_status = #{record.bizStatus,jdbcType=TINYINT},
      name = #{record.name,jdbcType=VARCHAR},
      max_year_rate = #{record.maxYearRate,jdbcType=VARCHAR},
      max_credit_limit = #{record.maxCreditLimit,jdbcType=VARCHAR},
      average_credit_limit = #{record.averageCreditLimit,jdbcType=VARCHAR},
      loan_term = #{record.loanTerm,jdbcType=VARCHAR},
      loan_usage = #{record.loanUsage,jdbcType=VARCHAR},
      daily_interest = #{record.dailyInterest,jdbcType=VARCHAR},
      interest_type = #{record.interestType,jdbcType=VARCHAR},
      rapayment_type = #{record.rapaymentType,jdbcType=VARCHAR},
      early_repayment = #{record.earlyRepayment,jdbcType=VARCHAR},
      early_repayment_penalty = #{record.earlyRepaymentPenalty,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      first_category_code = #{record.firstCategoryCode,jdbcType=BIGINT},
      second_category_code = #{record.secondCategoryCode,jdbcType=BIGINT},
      third_category_code = #{record.thirdCategoryCode,jdbcType=BIGINT},
      spu_code = #{record.spuCode,jdbcType=BIGINT},
      sku_code = #{record.skuCode,jdbcType=BIGINT},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.adp.cpc.po.ad.AdProductBorrowLoanPo">
    update ad_product_borrow_loan
    <set>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=INTEGER},
      </if>
      <if test="libraryId != null">
        library_id = #{libraryId,jdbcType=BIGINT},
      </if>
      <if test="adProductId != null">
        ad_product_id = #{adProductId,jdbcType=BIGINT},
      </if>
      <if test="bizStatus != null">
        biz_status = #{bizStatus,jdbcType=TINYINT},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="maxYearRate != null">
        max_year_rate = #{maxYearRate,jdbcType=VARCHAR},
      </if>
      <if test="maxCreditLimit != null">
        max_credit_limit = #{maxCreditLimit,jdbcType=VARCHAR},
      </if>
      <if test="averageCreditLimit != null">
        average_credit_limit = #{averageCreditLimit,jdbcType=VARCHAR},
      </if>
      <if test="loanTerm != null">
        loan_term = #{loanTerm,jdbcType=VARCHAR},
      </if>
      <if test="loanUsage != null">
        loan_usage = #{loanUsage,jdbcType=VARCHAR},
      </if>
      <if test="dailyInterest != null">
        daily_interest = #{dailyInterest,jdbcType=VARCHAR},
      </if>
      <if test="interestType != null">
        interest_type = #{interestType,jdbcType=VARCHAR},
      </if>
      <if test="rapaymentType != null">
        rapayment_type = #{rapaymentType,jdbcType=VARCHAR},
      </if>
      <if test="earlyRepayment != null">
        early_repayment = #{earlyRepayment,jdbcType=VARCHAR},
      </if>
      <if test="earlyRepaymentPenalty != null">
        early_repayment_penalty = #{earlyRepaymentPenalty,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="firstCategoryCode != null">
        first_category_code = #{firstCategoryCode,jdbcType=BIGINT},
      </if>
      <if test="secondCategoryCode != null">
        second_category_code = #{secondCategoryCode,jdbcType=BIGINT},
      </if>
      <if test="thirdCategoryCode != null">
        third_category_code = #{thirdCategoryCode,jdbcType=BIGINT},
      </if>
      <if test="spuCode != null">
        spu_code = #{spuCode,jdbcType=BIGINT},
      </if>
      <if test="skuCode != null">
        sku_code = #{skuCode,jdbcType=BIGINT},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.adp.cpc.po.ad.AdProductBorrowLoanPo">
    update ad_product_borrow_loan
    set account_id = #{accountId,jdbcType=INTEGER},
      library_id = #{libraryId,jdbcType=BIGINT},
      ad_product_id = #{adProductId,jdbcType=BIGINT},
      biz_status = #{bizStatus,jdbcType=TINYINT},
      name = #{name,jdbcType=VARCHAR},
      max_year_rate = #{maxYearRate,jdbcType=VARCHAR},
      max_credit_limit = #{maxCreditLimit,jdbcType=VARCHAR},
      average_credit_limit = #{averageCreditLimit,jdbcType=VARCHAR},
      loan_term = #{loanTerm,jdbcType=VARCHAR},
      loan_usage = #{loanUsage,jdbcType=VARCHAR},
      daily_interest = #{dailyInterest,jdbcType=VARCHAR},
      interest_type = #{interestType,jdbcType=VARCHAR},
      rapayment_type = #{rapaymentType,jdbcType=VARCHAR},
      early_repayment = #{earlyRepayment,jdbcType=VARCHAR},
      early_repayment_penalty = #{earlyRepaymentPenalty,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      first_category_code = #{firstCategoryCode,jdbcType=BIGINT},
      second_category_code = #{secondCategoryCode,jdbcType=BIGINT},
      third_category_code = #{thirdCategoryCode,jdbcType=BIGINT},
      spu_code = #{spuCode,jdbcType=BIGINT},
      sku_code = #{skuCode,jdbcType=BIGINT},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.adp.cpc.po.ad.AdProductBorrowLoanPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ad_product_borrow_loan (account_id, library_id, ad_product_id, 
      biz_status, name, max_year_rate, 
      max_credit_limit, average_credit_limit, loan_term, 
      loan_usage, daily_interest, interest_type, 
      rapayment_type, early_repayment, early_repayment_penalty, 
      remark, first_category_code, second_category_code, 
      third_category_code, spu_code, sku_code, 
      is_deleted, ctime, mtime
      )
    values (#{accountId,jdbcType=INTEGER}, #{libraryId,jdbcType=BIGINT}, #{adProductId,jdbcType=BIGINT}, 
      #{bizStatus,jdbcType=TINYINT}, #{name,jdbcType=VARCHAR}, #{maxYearRate,jdbcType=VARCHAR}, 
      #{maxCreditLimit,jdbcType=VARCHAR}, #{averageCreditLimit,jdbcType=VARCHAR}, #{loanTerm,jdbcType=VARCHAR}, 
      #{loanUsage,jdbcType=VARCHAR}, #{dailyInterest,jdbcType=VARCHAR}, #{interestType,jdbcType=VARCHAR}, 
      #{rapaymentType,jdbcType=VARCHAR}, #{earlyRepayment,jdbcType=VARCHAR}, #{earlyRepaymentPenalty,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR}, #{firstCategoryCode,jdbcType=BIGINT}, #{secondCategoryCode,jdbcType=BIGINT}, 
      #{thirdCategoryCode,jdbcType=BIGINT}, #{spuCode,jdbcType=BIGINT}, #{skuCode,jdbcType=BIGINT}, 
      #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}
      )
    <trim prefix="on duplicate key update" suffixOverrides=",">
      account_id = values(account_id),
      library_id = values(library_id),
      ad_product_id = values(ad_product_id),
      biz_status = values(biz_status),
      name = values(name),
      max_year_rate = values(max_year_rate),
      max_credit_limit = values(max_credit_limit),
      average_credit_limit = values(average_credit_limit),
      loan_term = values(loan_term),
      loan_usage = values(loan_usage),
      daily_interest = values(daily_interest),
      interest_type = values(interest_type),
      rapayment_type = values(rapayment_type),
      early_repayment = values(early_repayment),
      early_repayment_penalty = values(early_repayment_penalty),
      remark = values(remark),
      first_category_code = values(first_category_code),
      second_category_code = values(second_category_code),
      third_category_code = values(third_category_code),
      spu_code = values(spu_code),
      sku_code = values(sku_code),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      ad_product_borrow_loan
      (account_id,library_id,ad_product_id,biz_status,name,max_year_rate,max_credit_limit,average_credit_limit,loan_term,loan_usage,daily_interest,interest_type,rapayment_type,early_repayment,early_repayment_penalty,remark,first_category_code,second_category_code,third_category_code,spu_code,sku_code,is_deleted,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.accountId,jdbcType=INTEGER},
        #{item.libraryId,jdbcType=BIGINT},
        #{item.adProductId,jdbcType=BIGINT},
        #{item.bizStatus,jdbcType=TINYINT},
        #{item.name,jdbcType=VARCHAR},
        #{item.maxYearRate,jdbcType=VARCHAR},
        #{item.maxCreditLimit,jdbcType=VARCHAR},
        #{item.averageCreditLimit,jdbcType=VARCHAR},
        #{item.loanTerm,jdbcType=VARCHAR},
        #{item.loanUsage,jdbcType=VARCHAR},
        #{item.dailyInterest,jdbcType=VARCHAR},
        #{item.interestType,jdbcType=VARCHAR},
        #{item.rapaymentType,jdbcType=VARCHAR},
        #{item.earlyRepayment,jdbcType=VARCHAR},
        #{item.earlyRepaymentPenalty,jdbcType=VARCHAR},
        #{item.remark,jdbcType=VARCHAR},
        #{item.firstCategoryCode,jdbcType=BIGINT},
        #{item.secondCategoryCode,jdbcType=BIGINT},
        #{item.thirdCategoryCode,jdbcType=BIGINT},
        #{item.spuCode,jdbcType=BIGINT},
        #{item.skuCode,jdbcType=BIGINT},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      ad_product_borrow_loan
      (account_id,library_id,ad_product_id,biz_status,name,max_year_rate,max_credit_limit,average_credit_limit,loan_term,loan_usage,daily_interest,interest_type,rapayment_type,early_repayment,early_repayment_penalty,remark,first_category_code,second_category_code,third_category_code,spu_code,sku_code,is_deleted,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.accountId,jdbcType=INTEGER},
        #{item.libraryId,jdbcType=BIGINT},
        #{item.adProductId,jdbcType=BIGINT},
        #{item.bizStatus,jdbcType=TINYINT},
        #{item.name,jdbcType=VARCHAR},
        #{item.maxYearRate,jdbcType=VARCHAR},
        #{item.maxCreditLimit,jdbcType=VARCHAR},
        #{item.averageCreditLimit,jdbcType=VARCHAR},
        #{item.loanTerm,jdbcType=VARCHAR},
        #{item.loanUsage,jdbcType=VARCHAR},
        #{item.dailyInterest,jdbcType=VARCHAR},
        #{item.interestType,jdbcType=VARCHAR},
        #{item.rapaymentType,jdbcType=VARCHAR},
        #{item.earlyRepayment,jdbcType=VARCHAR},
        #{item.earlyRepaymentPenalty,jdbcType=VARCHAR},
        #{item.remark,jdbcType=VARCHAR},
        #{item.firstCategoryCode,jdbcType=BIGINT},
        #{item.secondCategoryCode,jdbcType=BIGINT},
        #{item.thirdCategoryCode,jdbcType=BIGINT},
        #{item.spuCode,jdbcType=BIGINT},
        #{item.skuCode,jdbcType=BIGINT},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      account_id = values(account_id),
      library_id = values(library_id),
      ad_product_id = values(ad_product_id),
      biz_status = values(biz_status),
      name = values(name),
      max_year_rate = values(max_year_rate),
      max_credit_limit = values(max_credit_limit),
      average_credit_limit = values(average_credit_limit),
      loan_term = values(loan_term),
      loan_usage = values(loan_usage),
      daily_interest = values(daily_interest),
      interest_type = values(interest_type),
      rapayment_type = values(rapayment_type),
      early_repayment = values(early_repayment),
      early_repayment_penalty = values(early_repayment_penalty),
      remark = values(remark),
      first_category_code = values(first_category_code),
      second_category_code = values(second_category_code),
      third_category_code = values(third_category_code),
      spu_code = values(spu_code),
      sku_code = values(sku_code),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.adp.cpc.po.ad.AdProductBorrowLoanPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ad_product_borrow_loan
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        account_id,
      </if>
      <if test="libraryId != null">
        library_id,
      </if>
      <if test="adProductId != null">
        ad_product_id,
      </if>
      <if test="bizStatus != null">
        biz_status,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="maxYearRate != null">
        max_year_rate,
      </if>
      <if test="maxCreditLimit != null">
        max_credit_limit,
      </if>
      <if test="averageCreditLimit != null">
        average_credit_limit,
      </if>
      <if test="loanTerm != null">
        loan_term,
      </if>
      <if test="loanUsage != null">
        loan_usage,
      </if>
      <if test="dailyInterest != null">
        daily_interest,
      </if>
      <if test="interestType != null">
        interest_type,
      </if>
      <if test="rapaymentType != null">
        rapayment_type,
      </if>
      <if test="earlyRepayment != null">
        early_repayment,
      </if>
      <if test="earlyRepaymentPenalty != null">
        early_repayment_penalty,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="firstCategoryCode != null">
        first_category_code,
      </if>
      <if test="secondCategoryCode != null">
        second_category_code,
      </if>
      <if test="thirdCategoryCode != null">
        third_category_code,
      </if>
      <if test="spuCode != null">
        spu_code,
      </if>
      <if test="skuCode != null">
        sku_code,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="libraryId != null">
        #{libraryId,jdbcType=BIGINT},
      </if>
      <if test="adProductId != null">
        #{adProductId,jdbcType=BIGINT},
      </if>
      <if test="bizStatus != null">
        #{bizStatus,jdbcType=TINYINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="maxYearRate != null">
        #{maxYearRate,jdbcType=VARCHAR},
      </if>
      <if test="maxCreditLimit != null">
        #{maxCreditLimit,jdbcType=VARCHAR},
      </if>
      <if test="averageCreditLimit != null">
        #{averageCreditLimit,jdbcType=VARCHAR},
      </if>
      <if test="loanTerm != null">
        #{loanTerm,jdbcType=VARCHAR},
      </if>
      <if test="loanUsage != null">
        #{loanUsage,jdbcType=VARCHAR},
      </if>
      <if test="dailyInterest != null">
        #{dailyInterest,jdbcType=VARCHAR},
      </if>
      <if test="interestType != null">
        #{interestType,jdbcType=VARCHAR},
      </if>
      <if test="rapaymentType != null">
        #{rapaymentType,jdbcType=VARCHAR},
      </if>
      <if test="earlyRepayment != null">
        #{earlyRepayment,jdbcType=VARCHAR},
      </if>
      <if test="earlyRepaymentPenalty != null">
        #{earlyRepaymentPenalty,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="firstCategoryCode != null">
        #{firstCategoryCode,jdbcType=BIGINT},
      </if>
      <if test="secondCategoryCode != null">
        #{secondCategoryCode,jdbcType=BIGINT},
      </if>
      <if test="thirdCategoryCode != null">
        #{thirdCategoryCode,jdbcType=BIGINT},
      </if>
      <if test="spuCode != null">
        #{spuCode,jdbcType=BIGINT},
      </if>
      <if test="skuCode != null">
        #{skuCode,jdbcType=BIGINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="accountId != null">
        account_id = values(account_id),
      </if>
      <if test="libraryId != null">
        library_id = values(library_id),
      </if>
      <if test="adProductId != null">
        ad_product_id = values(ad_product_id),
      </if>
      <if test="bizStatus != null">
        biz_status = values(biz_status),
      </if>
      <if test="name != null">
        name = values(name),
      </if>
      <if test="maxYearRate != null">
        max_year_rate = values(max_year_rate),
      </if>
      <if test="maxCreditLimit != null">
        max_credit_limit = values(max_credit_limit),
      </if>
      <if test="averageCreditLimit != null">
        average_credit_limit = values(average_credit_limit),
      </if>
      <if test="loanTerm != null">
        loan_term = values(loan_term),
      </if>
      <if test="loanUsage != null">
        loan_usage = values(loan_usage),
      </if>
      <if test="dailyInterest != null">
        daily_interest = values(daily_interest),
      </if>
      <if test="interestType != null">
        interest_type = values(interest_type),
      </if>
      <if test="rapaymentType != null">
        rapayment_type = values(rapayment_type),
      </if>
      <if test="earlyRepayment != null">
        early_repayment = values(early_repayment),
      </if>
      <if test="earlyRepaymentPenalty != null">
        early_repayment_penalty = values(early_repayment_penalty),
      </if>
      <if test="remark != null">
        remark = values(remark),
      </if>
      <if test="firstCategoryCode != null">
        first_category_code = values(first_category_code),
      </if>
      <if test="secondCategoryCode != null">
        second_category_code = values(second_category_code),
      </if>
      <if test="thirdCategoryCode != null">
        third_category_code = values(third_category_code),
      </if>
      <if test="spuCode != null">
        spu_code = values(spu_code),
      </if>
      <if test="skuCode != null">
        sku_code = values(sku_code),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
    </trim>
  </insert>
</mapper>