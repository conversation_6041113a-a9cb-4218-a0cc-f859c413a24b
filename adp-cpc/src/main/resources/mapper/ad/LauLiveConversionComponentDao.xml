<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.adp.cpc.dao.ad.LauLiveConversionComponentDao">
  <resultMap id="BaseResultMap" type="com.bilibili.adp.cpc.po.ad.LauLiveConversionComponentPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="room_id" jdbcType="BIGINT" property="roomId" />
    <result column="uid" jdbcType="BIGINT" property="uid" />
    <result column="account_id" jdbcType="INTEGER" property="accountId" />
    <result column="customer_id" jdbcType="INTEGER" property="customerId" />
    <result column="agent_id" jdbcType="INTEGER" property="agentId" />
    <result column="campaign_id" jdbcType="INTEGER" property="campaignId" />
    <result column="unit_id" jdbcType="INTEGER" property="unitId" />
    <result column="creative_id" jdbcType="INTEGER" property="creativeId" />
    <result column="card_title" jdbcType="VARCHAR" property="cardTitle" />
    <result column="button_text" jdbcType="VARCHAR" property="buttonText" />
    <result column="card_samll_image_url" jdbcType="VARCHAR" property="cardSamllImageUrl" />
    <result column="component_type" jdbcType="TINYINT" property="componentType" />
    <result column="mgk_page_id" jdbcType="BIGINT" property="mgkPageId" />
    <result column="conversion_url" jdbcType="VARCHAR" property="conversionUrl" />
    <result column="scene" jdbcType="VARCHAR" property="scene" />
    <result column="form_submit" jdbcType="INTEGER" property="formSubmit" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="tool_id" jdbcType="VARCHAR" property="toolId" />
    <result column="sub_title" jdbcType="VARCHAR" property="subTitle" />
    <result column="booking_outer_type" jdbcType="INTEGER" property="bookingOuterType" />
    <result column="sub_title_type" jdbcType="INTEGER" property="subTitleType" />
    <result column="mini_game_id" jdbcType="INTEGER" property="miniGameId" />
    <result column="business_tool_type" jdbcType="INTEGER" property="businessToolType" />
    <result column="ios_conversion_url" jdbcType="VARCHAR" property="iosConversionUrl" />
    <result column="android_conversion_url" jdbcType="VARCHAR" property="androidConversionUrl" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, room_id, uid, account_id, customer_id, agent_id, campaign_id, unit_id, creative_id, 
    card_title, button_text, card_samll_image_url, component_type, mgk_page_id, conversion_url, 
    scene, form_submit, status, ctime, mtime, tool_id, sub_title, booking_outer_type, 
    sub_title_type, mini_game_id, business_tool_type, ios_conversion_url, android_conversion_url
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.adp.cpc.po.ad.LauLiveConversionComponentPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from lau_live_conversion_component
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from lau_live_conversion_component
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from lau_live_conversion_component
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.adp.cpc.po.ad.LauLiveConversionComponentPoExample">
    delete from lau_live_conversion_component
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.adp.cpc.po.ad.LauLiveConversionComponentPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_live_conversion_component (room_id, uid, account_id, 
      customer_id, agent_id, campaign_id, 
      unit_id, creative_id, card_title, 
      button_text, card_samll_image_url, component_type, 
      mgk_page_id, conversion_url, scene, 
      form_submit, status, ctime, 
      mtime, tool_id, sub_title, 
      booking_outer_type, sub_title_type, mini_game_id, 
      business_tool_type, ios_conversion_url, android_conversion_url
      )
    values (#{roomId,jdbcType=BIGINT}, #{uid,jdbcType=BIGINT}, #{accountId,jdbcType=INTEGER}, 
      #{customerId,jdbcType=INTEGER}, #{agentId,jdbcType=INTEGER}, #{campaignId,jdbcType=INTEGER}, 
      #{unitId,jdbcType=INTEGER}, #{creativeId,jdbcType=INTEGER}, #{cardTitle,jdbcType=VARCHAR}, 
      #{buttonText,jdbcType=VARCHAR}, #{cardSamllImageUrl,jdbcType=VARCHAR}, #{componentType,jdbcType=TINYINT}, 
      #{mgkPageId,jdbcType=BIGINT}, #{conversionUrl,jdbcType=VARCHAR}, #{scene,jdbcType=VARCHAR}, 
      #{formSubmit,jdbcType=INTEGER}, #{status,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, 
      #{mtime,jdbcType=TIMESTAMP}, #{toolId,jdbcType=VARCHAR}, #{subTitle,jdbcType=VARCHAR}, 
      #{bookingOuterType,jdbcType=INTEGER}, #{subTitleType,jdbcType=INTEGER}, #{miniGameId,jdbcType=INTEGER}, 
      #{businessToolType,jdbcType=INTEGER}, #{iosConversionUrl,jdbcType=VARCHAR}, #{androidConversionUrl,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.adp.cpc.po.ad.LauLiveConversionComponentPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_live_conversion_component
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="roomId != null">
        room_id,
      </if>
      <if test="uid != null">
        uid,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="customerId != null">
        customer_id,
      </if>
      <if test="agentId != null">
        agent_id,
      </if>
      <if test="campaignId != null">
        campaign_id,
      </if>
      <if test="unitId != null">
        unit_id,
      </if>
      <if test="creativeId != null">
        creative_id,
      </if>
      <if test="cardTitle != null">
        card_title,
      </if>
      <if test="buttonText != null">
        button_text,
      </if>
      <if test="cardSamllImageUrl != null">
        card_samll_image_url,
      </if>
      <if test="componentType != null">
        component_type,
      </if>
      <if test="mgkPageId != null">
        mgk_page_id,
      </if>
      <if test="conversionUrl != null">
        conversion_url,
      </if>
      <if test="scene != null">
        scene,
      </if>
      <if test="formSubmit != null">
        form_submit,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="toolId != null">
        tool_id,
      </if>
      <if test="subTitle != null">
        sub_title,
      </if>
      <if test="bookingOuterType != null">
        booking_outer_type,
      </if>
      <if test="subTitleType != null">
        sub_title_type,
      </if>
      <if test="miniGameId != null">
        mini_game_id,
      </if>
      <if test="businessToolType != null">
        business_tool_type,
      </if>
      <if test="iosConversionUrl != null">
        ios_conversion_url,
      </if>
      <if test="androidConversionUrl != null">
        android_conversion_url,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="roomId != null">
        #{roomId,jdbcType=BIGINT},
      </if>
      <if test="uid != null">
        #{uid,jdbcType=BIGINT},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="customerId != null">
        #{customerId,jdbcType=INTEGER},
      </if>
      <if test="agentId != null">
        #{agentId,jdbcType=INTEGER},
      </if>
      <if test="campaignId != null">
        #{campaignId,jdbcType=INTEGER},
      </if>
      <if test="unitId != null">
        #{unitId,jdbcType=INTEGER},
      </if>
      <if test="creativeId != null">
        #{creativeId,jdbcType=INTEGER},
      </if>
      <if test="cardTitle != null">
        #{cardTitle,jdbcType=VARCHAR},
      </if>
      <if test="buttonText != null">
        #{buttonText,jdbcType=VARCHAR},
      </if>
      <if test="cardSamllImageUrl != null">
        #{cardSamllImageUrl,jdbcType=VARCHAR},
      </if>
      <if test="componentType != null">
        #{componentType,jdbcType=TINYINT},
      </if>
      <if test="mgkPageId != null">
        #{mgkPageId,jdbcType=BIGINT},
      </if>
      <if test="conversionUrl != null">
        #{conversionUrl,jdbcType=VARCHAR},
      </if>
      <if test="scene != null">
        #{scene,jdbcType=VARCHAR},
      </if>
      <if test="formSubmit != null">
        #{formSubmit,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="toolId != null">
        #{toolId,jdbcType=VARCHAR},
      </if>
      <if test="subTitle != null">
        #{subTitle,jdbcType=VARCHAR},
      </if>
      <if test="bookingOuterType != null">
        #{bookingOuterType,jdbcType=INTEGER},
      </if>
      <if test="subTitleType != null">
        #{subTitleType,jdbcType=INTEGER},
      </if>
      <if test="miniGameId != null">
        #{miniGameId,jdbcType=INTEGER},
      </if>
      <if test="businessToolType != null">
        #{businessToolType,jdbcType=INTEGER},
      </if>
      <if test="iosConversionUrl != null">
        #{iosConversionUrl,jdbcType=VARCHAR},
      </if>
      <if test="androidConversionUrl != null">
        #{androidConversionUrl,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.adp.cpc.po.ad.LauLiveConversionComponentPoExample" resultType="java.lang.Long">
    select count(*) from lau_live_conversion_component
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update lau_live_conversion_component
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.roomId != null">
        room_id = #{record.roomId,jdbcType=BIGINT},
      </if>
      <if test="record.uid != null">
        uid = #{record.uid,jdbcType=BIGINT},
      </if>
      <if test="record.accountId != null">
        account_id = #{record.accountId,jdbcType=INTEGER},
      </if>
      <if test="record.customerId != null">
        customer_id = #{record.customerId,jdbcType=INTEGER},
      </if>
      <if test="record.agentId != null">
        agent_id = #{record.agentId,jdbcType=INTEGER},
      </if>
      <if test="record.campaignId != null">
        campaign_id = #{record.campaignId,jdbcType=INTEGER},
      </if>
      <if test="record.unitId != null">
        unit_id = #{record.unitId,jdbcType=INTEGER},
      </if>
      <if test="record.creativeId != null">
        creative_id = #{record.creativeId,jdbcType=INTEGER},
      </if>
      <if test="record.cardTitle != null">
        card_title = #{record.cardTitle,jdbcType=VARCHAR},
      </if>
      <if test="record.buttonText != null">
        button_text = #{record.buttonText,jdbcType=VARCHAR},
      </if>
      <if test="record.cardSamllImageUrl != null">
        card_samll_image_url = #{record.cardSamllImageUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.componentType != null">
        component_type = #{record.componentType,jdbcType=TINYINT},
      </if>
      <if test="record.mgkPageId != null">
        mgk_page_id = #{record.mgkPageId,jdbcType=BIGINT},
      </if>
      <if test="record.conversionUrl != null">
        conversion_url = #{record.conversionUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.scene != null">
        scene = #{record.scene,jdbcType=VARCHAR},
      </if>
      <if test="record.formSubmit != null">
        form_submit = #{record.formSubmit,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.toolId != null">
        tool_id = #{record.toolId,jdbcType=VARCHAR},
      </if>
      <if test="record.subTitle != null">
        sub_title = #{record.subTitle,jdbcType=VARCHAR},
      </if>
      <if test="record.bookingOuterType != null">
        booking_outer_type = #{record.bookingOuterType,jdbcType=INTEGER},
      </if>
      <if test="record.subTitleType != null">
        sub_title_type = #{record.subTitleType,jdbcType=INTEGER},
      </if>
      <if test="record.miniGameId != null">
        mini_game_id = #{record.miniGameId,jdbcType=INTEGER},
      </if>
      <if test="record.businessToolType != null">
        business_tool_type = #{record.businessToolType,jdbcType=INTEGER},
      </if>
      <if test="record.iosConversionUrl != null">
        ios_conversion_url = #{record.iosConversionUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.androidConversionUrl != null">
        android_conversion_url = #{record.androidConversionUrl,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update lau_live_conversion_component
    set id = #{record.id,jdbcType=BIGINT},
      room_id = #{record.roomId,jdbcType=BIGINT},
      uid = #{record.uid,jdbcType=BIGINT},
      account_id = #{record.accountId,jdbcType=INTEGER},
      customer_id = #{record.customerId,jdbcType=INTEGER},
      agent_id = #{record.agentId,jdbcType=INTEGER},
      campaign_id = #{record.campaignId,jdbcType=INTEGER},
      unit_id = #{record.unitId,jdbcType=INTEGER},
      creative_id = #{record.creativeId,jdbcType=INTEGER},
      card_title = #{record.cardTitle,jdbcType=VARCHAR},
      button_text = #{record.buttonText,jdbcType=VARCHAR},
      card_samll_image_url = #{record.cardSamllImageUrl,jdbcType=VARCHAR},
      component_type = #{record.componentType,jdbcType=TINYINT},
      mgk_page_id = #{record.mgkPageId,jdbcType=BIGINT},
      conversion_url = #{record.conversionUrl,jdbcType=VARCHAR},
      scene = #{record.scene,jdbcType=VARCHAR},
      form_submit = #{record.formSubmit,jdbcType=INTEGER},
      status = #{record.status,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      tool_id = #{record.toolId,jdbcType=VARCHAR},
      sub_title = #{record.subTitle,jdbcType=VARCHAR},
      booking_outer_type = #{record.bookingOuterType,jdbcType=INTEGER},
      sub_title_type = #{record.subTitleType,jdbcType=INTEGER},
      mini_game_id = #{record.miniGameId,jdbcType=INTEGER},
      business_tool_type = #{record.businessToolType,jdbcType=INTEGER},
      ios_conversion_url = #{record.iosConversionUrl,jdbcType=VARCHAR},
      android_conversion_url = #{record.androidConversionUrl,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.adp.cpc.po.ad.LauLiveConversionComponentPo">
    update lau_live_conversion_component
    <set>
      <if test="roomId != null">
        room_id = #{roomId,jdbcType=BIGINT},
      </if>
      <if test="uid != null">
        uid = #{uid,jdbcType=BIGINT},
      </if>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=INTEGER},
      </if>
      <if test="customerId != null">
        customer_id = #{customerId,jdbcType=INTEGER},
      </if>
      <if test="agentId != null">
        agent_id = #{agentId,jdbcType=INTEGER},
      </if>
      <if test="campaignId != null">
        campaign_id = #{campaignId,jdbcType=INTEGER},
      </if>
      <if test="unitId != null">
        unit_id = #{unitId,jdbcType=INTEGER},
      </if>
      <if test="creativeId != null">
        creative_id = #{creativeId,jdbcType=INTEGER},
      </if>
      <if test="cardTitle != null">
        card_title = #{cardTitle,jdbcType=VARCHAR},
      </if>
      <if test="buttonText != null">
        button_text = #{buttonText,jdbcType=VARCHAR},
      </if>
      <if test="cardSamllImageUrl != null">
        card_samll_image_url = #{cardSamllImageUrl,jdbcType=VARCHAR},
      </if>
      <if test="componentType != null">
        component_type = #{componentType,jdbcType=TINYINT},
      </if>
      <if test="mgkPageId != null">
        mgk_page_id = #{mgkPageId,jdbcType=BIGINT},
      </if>
      <if test="conversionUrl != null">
        conversion_url = #{conversionUrl,jdbcType=VARCHAR},
      </if>
      <if test="scene != null">
        scene = #{scene,jdbcType=VARCHAR},
      </if>
      <if test="formSubmit != null">
        form_submit = #{formSubmit,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="toolId != null">
        tool_id = #{toolId,jdbcType=VARCHAR},
      </if>
      <if test="subTitle != null">
        sub_title = #{subTitle,jdbcType=VARCHAR},
      </if>
      <if test="bookingOuterType != null">
        booking_outer_type = #{bookingOuterType,jdbcType=INTEGER},
      </if>
      <if test="subTitleType != null">
        sub_title_type = #{subTitleType,jdbcType=INTEGER},
      </if>
      <if test="miniGameId != null">
        mini_game_id = #{miniGameId,jdbcType=INTEGER},
      </if>
      <if test="businessToolType != null">
        business_tool_type = #{businessToolType,jdbcType=INTEGER},
      </if>
      <if test="iosConversionUrl != null">
        ios_conversion_url = #{iosConversionUrl,jdbcType=VARCHAR},
      </if>
      <if test="androidConversionUrl != null">
        android_conversion_url = #{androidConversionUrl,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.adp.cpc.po.ad.LauLiveConversionComponentPo">
    update lau_live_conversion_component
    set room_id = #{roomId,jdbcType=BIGINT},
      uid = #{uid,jdbcType=BIGINT},
      account_id = #{accountId,jdbcType=INTEGER},
      customer_id = #{customerId,jdbcType=INTEGER},
      agent_id = #{agentId,jdbcType=INTEGER},
      campaign_id = #{campaignId,jdbcType=INTEGER},
      unit_id = #{unitId,jdbcType=INTEGER},
      creative_id = #{creativeId,jdbcType=INTEGER},
      card_title = #{cardTitle,jdbcType=VARCHAR},
      button_text = #{buttonText,jdbcType=VARCHAR},
      card_samll_image_url = #{cardSamllImageUrl,jdbcType=VARCHAR},
      component_type = #{componentType,jdbcType=TINYINT},
      mgk_page_id = #{mgkPageId,jdbcType=BIGINT},
      conversion_url = #{conversionUrl,jdbcType=VARCHAR},
      scene = #{scene,jdbcType=VARCHAR},
      form_submit = #{formSubmit,jdbcType=INTEGER},
      status = #{status,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      tool_id = #{toolId,jdbcType=VARCHAR},
      sub_title = #{subTitle,jdbcType=VARCHAR},
      booking_outer_type = #{bookingOuterType,jdbcType=INTEGER},
      sub_title_type = #{subTitleType,jdbcType=INTEGER},
      mini_game_id = #{miniGameId,jdbcType=INTEGER},
      business_tool_type = #{businessToolType,jdbcType=INTEGER},
      ios_conversion_url = #{iosConversionUrl,jdbcType=VARCHAR},
      android_conversion_url = #{androidConversionUrl,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.adp.cpc.po.ad.LauLiveConversionComponentPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_live_conversion_component (room_id, uid, account_id, 
      customer_id, agent_id, campaign_id, 
      unit_id, creative_id, card_title, 
      button_text, card_samll_image_url, component_type, 
      mgk_page_id, conversion_url, scene, 
      form_submit, status, ctime, 
      mtime, tool_id, sub_title, 
      booking_outer_type, sub_title_type, mini_game_id, 
      business_tool_type, ios_conversion_url, android_conversion_url
      )
    values (#{roomId,jdbcType=BIGINT}, #{uid,jdbcType=BIGINT}, #{accountId,jdbcType=INTEGER}, 
      #{customerId,jdbcType=INTEGER}, #{agentId,jdbcType=INTEGER}, #{campaignId,jdbcType=INTEGER}, 
      #{unitId,jdbcType=INTEGER}, #{creativeId,jdbcType=INTEGER}, #{cardTitle,jdbcType=VARCHAR}, 
      #{buttonText,jdbcType=VARCHAR}, #{cardSamllImageUrl,jdbcType=VARCHAR}, #{componentType,jdbcType=TINYINT}, 
      #{mgkPageId,jdbcType=BIGINT}, #{conversionUrl,jdbcType=VARCHAR}, #{scene,jdbcType=VARCHAR}, 
      #{formSubmit,jdbcType=INTEGER}, #{status,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, 
      #{mtime,jdbcType=TIMESTAMP}, #{toolId,jdbcType=VARCHAR}, #{subTitle,jdbcType=VARCHAR}, 
      #{bookingOuterType,jdbcType=INTEGER}, #{subTitleType,jdbcType=INTEGER}, #{miniGameId,jdbcType=INTEGER}, 
      #{businessToolType,jdbcType=INTEGER}, #{iosConversionUrl,jdbcType=VARCHAR}, #{androidConversionUrl,jdbcType=VARCHAR}
      )
    <trim prefix="on duplicate key update" suffixOverrides=",">
      room_id = values(room_id),
      uid = values(uid),
      account_id = values(account_id),
      customer_id = values(customer_id),
      agent_id = values(agent_id),
      campaign_id = values(campaign_id),
      unit_id = values(unit_id),
      creative_id = values(creative_id),
      card_title = values(card_title),
      button_text = values(button_text),
      card_samll_image_url = values(card_samll_image_url),
      component_type = values(component_type),
      mgk_page_id = values(mgk_page_id),
      conversion_url = values(conversion_url),
      scene = values(scene),
      form_submit = values(form_submit),
      status = values(status),
      ctime = values(ctime),
      mtime = values(mtime),
      tool_id = values(tool_id),
      sub_title = values(sub_title),
      booking_outer_type = values(booking_outer_type),
      sub_title_type = values(sub_title_type),
      mini_game_id = values(mini_game_id),
      business_tool_type = values(business_tool_type),
      ios_conversion_url = values(ios_conversion_url),
      android_conversion_url = values(android_conversion_url),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      lau_live_conversion_component
      (room_id,uid,account_id,customer_id,agent_id,campaign_id,unit_id,creative_id,card_title,button_text,card_samll_image_url,component_type,mgk_page_id,conversion_url,scene,form_submit,status,ctime,mtime,tool_id,sub_title,booking_outer_type,sub_title_type,mini_game_id,business_tool_type,ios_conversion_url,android_conversion_url)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.roomId,jdbcType=BIGINT},
        #{item.uid,jdbcType=BIGINT},
        #{item.accountId,jdbcType=INTEGER},
        #{item.customerId,jdbcType=INTEGER},
        #{item.agentId,jdbcType=INTEGER},
        #{item.campaignId,jdbcType=INTEGER},
        #{item.unitId,jdbcType=INTEGER},
        #{item.creativeId,jdbcType=INTEGER},
        #{item.cardTitle,jdbcType=VARCHAR},
        #{item.buttonText,jdbcType=VARCHAR},
        #{item.cardSamllImageUrl,jdbcType=VARCHAR},
        #{item.componentType,jdbcType=TINYINT},
        #{item.mgkPageId,jdbcType=BIGINT},
        #{item.conversionUrl,jdbcType=VARCHAR},
        #{item.scene,jdbcType=VARCHAR},
        #{item.formSubmit,jdbcType=INTEGER},
        #{item.status,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.toolId,jdbcType=VARCHAR},
        #{item.subTitle,jdbcType=VARCHAR},
        #{item.bookingOuterType,jdbcType=INTEGER},
        #{item.subTitleType,jdbcType=INTEGER},
        #{item.miniGameId,jdbcType=INTEGER},
        #{item.businessToolType,jdbcType=INTEGER},
        #{item.iosConversionUrl,jdbcType=VARCHAR},
        #{item.androidConversionUrl,jdbcType=VARCHAR},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      lau_live_conversion_component
      (room_id,uid,account_id,customer_id,agent_id,campaign_id,unit_id,creative_id,card_title,button_text,card_samll_image_url,component_type,mgk_page_id,conversion_url,scene,form_submit,status,ctime,mtime,tool_id,sub_title,booking_outer_type,sub_title_type,mini_game_id,business_tool_type,ios_conversion_url,android_conversion_url)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.roomId,jdbcType=BIGINT},
        #{item.uid,jdbcType=BIGINT},
        #{item.accountId,jdbcType=INTEGER},
        #{item.customerId,jdbcType=INTEGER},
        #{item.agentId,jdbcType=INTEGER},
        #{item.campaignId,jdbcType=INTEGER},
        #{item.unitId,jdbcType=INTEGER},
        #{item.creativeId,jdbcType=INTEGER},
        #{item.cardTitle,jdbcType=VARCHAR},
        #{item.buttonText,jdbcType=VARCHAR},
        #{item.cardSamllImageUrl,jdbcType=VARCHAR},
        #{item.componentType,jdbcType=TINYINT},
        #{item.mgkPageId,jdbcType=BIGINT},
        #{item.conversionUrl,jdbcType=VARCHAR},
        #{item.scene,jdbcType=VARCHAR},
        #{item.formSubmit,jdbcType=INTEGER},
        #{item.status,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.toolId,jdbcType=VARCHAR},
        #{item.subTitle,jdbcType=VARCHAR},
        #{item.bookingOuterType,jdbcType=INTEGER},
        #{item.subTitleType,jdbcType=INTEGER},
        #{item.miniGameId,jdbcType=INTEGER},
        #{item.businessToolType,jdbcType=INTEGER},
        #{item.iosConversionUrl,jdbcType=VARCHAR},
        #{item.androidConversionUrl,jdbcType=VARCHAR},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      room_id = values(room_id),
      uid = values(uid),
      account_id = values(account_id),
      customer_id = values(customer_id),
      agent_id = values(agent_id),
      campaign_id = values(campaign_id),
      unit_id = values(unit_id),
      creative_id = values(creative_id),
      card_title = values(card_title),
      button_text = values(button_text),
      card_samll_image_url = values(card_samll_image_url),
      component_type = values(component_type),
      mgk_page_id = values(mgk_page_id),
      conversion_url = values(conversion_url),
      scene = values(scene),
      form_submit = values(form_submit),
      status = values(status),
      ctime = values(ctime),
      mtime = values(mtime),
      tool_id = values(tool_id),
      sub_title = values(sub_title),
      booking_outer_type = values(booking_outer_type),
      sub_title_type = values(sub_title_type),
      mini_game_id = values(mini_game_id),
      business_tool_type = values(business_tool_type),
      ios_conversion_url = values(ios_conversion_url),
      android_conversion_url = values(android_conversion_url),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.adp.cpc.po.ad.LauLiveConversionComponentPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_live_conversion_component
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="roomId != null">
        room_id,
      </if>
      <if test="uid != null">
        uid,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="customerId != null">
        customer_id,
      </if>
      <if test="agentId != null">
        agent_id,
      </if>
      <if test="campaignId != null">
        campaign_id,
      </if>
      <if test="unitId != null">
        unit_id,
      </if>
      <if test="creativeId != null">
        creative_id,
      </if>
      <if test="cardTitle != null">
        card_title,
      </if>
      <if test="buttonText != null">
        button_text,
      </if>
      <if test="cardSamllImageUrl != null">
        card_samll_image_url,
      </if>
      <if test="componentType != null">
        component_type,
      </if>
      <if test="mgkPageId != null">
        mgk_page_id,
      </if>
      <if test="conversionUrl != null">
        conversion_url,
      </if>
      <if test="scene != null">
        scene,
      </if>
      <if test="formSubmit != null">
        form_submit,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="toolId != null">
        tool_id,
      </if>
      <if test="subTitle != null">
        sub_title,
      </if>
      <if test="bookingOuterType != null">
        booking_outer_type,
      </if>
      <if test="subTitleType != null">
        sub_title_type,
      </if>
      <if test="miniGameId != null">
        mini_game_id,
      </if>
      <if test="businessToolType != null">
        business_tool_type,
      </if>
      <if test="iosConversionUrl != null">
        ios_conversion_url,
      </if>
      <if test="androidConversionUrl != null">
        android_conversion_url,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="roomId != null">
        #{roomId,jdbcType=BIGINT},
      </if>
      <if test="uid != null">
        #{uid,jdbcType=BIGINT},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="customerId != null">
        #{customerId,jdbcType=INTEGER},
      </if>
      <if test="agentId != null">
        #{agentId,jdbcType=INTEGER},
      </if>
      <if test="campaignId != null">
        #{campaignId,jdbcType=INTEGER},
      </if>
      <if test="unitId != null">
        #{unitId,jdbcType=INTEGER},
      </if>
      <if test="creativeId != null">
        #{creativeId,jdbcType=INTEGER},
      </if>
      <if test="cardTitle != null">
        #{cardTitle,jdbcType=VARCHAR},
      </if>
      <if test="buttonText != null">
        #{buttonText,jdbcType=VARCHAR},
      </if>
      <if test="cardSamllImageUrl != null">
        #{cardSamllImageUrl,jdbcType=VARCHAR},
      </if>
      <if test="componentType != null">
        #{componentType,jdbcType=TINYINT},
      </if>
      <if test="mgkPageId != null">
        #{mgkPageId,jdbcType=BIGINT},
      </if>
      <if test="conversionUrl != null">
        #{conversionUrl,jdbcType=VARCHAR},
      </if>
      <if test="scene != null">
        #{scene,jdbcType=VARCHAR},
      </if>
      <if test="formSubmit != null">
        #{formSubmit,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="toolId != null">
        #{toolId,jdbcType=VARCHAR},
      </if>
      <if test="subTitle != null">
        #{subTitle,jdbcType=VARCHAR},
      </if>
      <if test="bookingOuterType != null">
        #{bookingOuterType,jdbcType=INTEGER},
      </if>
      <if test="subTitleType != null">
        #{subTitleType,jdbcType=INTEGER},
      </if>
      <if test="miniGameId != null">
        #{miniGameId,jdbcType=INTEGER},
      </if>
      <if test="businessToolType != null">
        #{businessToolType,jdbcType=INTEGER},
      </if>
      <if test="iosConversionUrl != null">
        #{iosConversionUrl,jdbcType=VARCHAR},
      </if>
      <if test="androidConversionUrl != null">
        #{androidConversionUrl,jdbcType=VARCHAR},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="roomId != null">
        room_id = values(room_id),
      </if>
      <if test="uid != null">
        uid = values(uid),
      </if>
      <if test="accountId != null">
        account_id = values(account_id),
      </if>
      <if test="customerId != null">
        customer_id = values(customer_id),
      </if>
      <if test="agentId != null">
        agent_id = values(agent_id),
      </if>
      <if test="campaignId != null">
        campaign_id = values(campaign_id),
      </if>
      <if test="unitId != null">
        unit_id = values(unit_id),
      </if>
      <if test="creativeId != null">
        creative_id = values(creative_id),
      </if>
      <if test="cardTitle != null">
        card_title = values(card_title),
      </if>
      <if test="buttonText != null">
        button_text = values(button_text),
      </if>
      <if test="cardSamllImageUrl != null">
        card_samll_image_url = values(card_samll_image_url),
      </if>
      <if test="componentType != null">
        component_type = values(component_type),
      </if>
      <if test="mgkPageId != null">
        mgk_page_id = values(mgk_page_id),
      </if>
      <if test="conversionUrl != null">
        conversion_url = values(conversion_url),
      </if>
      <if test="scene != null">
        scene = values(scene),
      </if>
      <if test="formSubmit != null">
        form_submit = values(form_submit),
      </if>
      <if test="status != null">
        status = values(status),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="toolId != null">
        tool_id = values(tool_id),
      </if>
      <if test="subTitle != null">
        sub_title = values(sub_title),
      </if>
      <if test="bookingOuterType != null">
        booking_outer_type = values(booking_outer_type),
      </if>
      <if test="subTitleType != null">
        sub_title_type = values(sub_title_type),
      </if>
      <if test="miniGameId != null">
        mini_game_id = values(mini_game_id),
      </if>
      <if test="businessToolType != null">
        business_tool_type = values(business_tool_type),
      </if>
      <if test="iosConversionUrl != null">
        ios_conversion_url = values(ios_conversion_url),
      </if>
      <if test="androidConversionUrl != null">
        android_conversion_url = values(android_conversion_url),
      </if>
    </trim>
  </insert>
</mapper>