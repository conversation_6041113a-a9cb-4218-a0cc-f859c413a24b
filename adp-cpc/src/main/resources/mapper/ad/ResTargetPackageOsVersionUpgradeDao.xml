<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.adp.cpc.dao.ad.ResTargetPackageOsVersionUpgradeDao">
  <resultMap id="BaseResultMap" type="com.bilibili.adp.cpc.po.ad.ResTargetPackageOsVersionUpgradePo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="target_package_id" jdbcType="INTEGER" property="targetPackageId" />
    <result column="os" jdbcType="INTEGER" property="os" />
    <result column="os_versions" jdbcType="VARCHAR" property="osVersions" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, target_package_id, os, os_versions, is_deleted, ctime, mtime
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.adp.cpc.po.ad.ResTargetPackageOsVersionUpgradePoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from res_target_package_os_version_upgrade
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from res_target_package_os_version_upgrade
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from res_target_package_os_version_upgrade
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.adp.cpc.po.ad.ResTargetPackageOsVersionUpgradePoExample">
    delete from res_target_package_os_version_upgrade
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.adp.cpc.po.ad.ResTargetPackageOsVersionUpgradePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into res_target_package_os_version_upgrade (target_package_id, os, os_versions, 
      is_deleted, ctime, mtime
      )
    values (#{targetPackageId,jdbcType=INTEGER}, #{os,jdbcType=INTEGER}, #{osVersions,jdbcType=VARCHAR}, 
      #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.adp.cpc.po.ad.ResTargetPackageOsVersionUpgradePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into res_target_package_os_version_upgrade
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="targetPackageId != null">
        target_package_id,
      </if>
      <if test="os != null">
        os,
      </if>
      <if test="osVersions != null">
        os_versions,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="targetPackageId != null">
        #{targetPackageId,jdbcType=INTEGER},
      </if>
      <if test="os != null">
        #{os,jdbcType=INTEGER},
      </if>
      <if test="osVersions != null">
        #{osVersions,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.adp.cpc.po.ad.ResTargetPackageOsVersionUpgradePoExample" resultType="java.lang.Long">
    select count(*) from res_target_package_os_version_upgrade
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update res_target_package_os_version_upgrade
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.targetPackageId != null">
        target_package_id = #{record.targetPackageId,jdbcType=INTEGER},
      </if>
      <if test="record.os != null">
        os = #{record.os,jdbcType=INTEGER},
      </if>
      <if test="record.osVersions != null">
        os_versions = #{record.osVersions,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update res_target_package_os_version_upgrade
    set id = #{record.id,jdbcType=BIGINT},
      target_package_id = #{record.targetPackageId,jdbcType=INTEGER},
      os = #{record.os,jdbcType=INTEGER},
      os_versions = #{record.osVersions,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.adp.cpc.po.ad.ResTargetPackageOsVersionUpgradePo">
    update res_target_package_os_version_upgrade
    <set>
      <if test="targetPackageId != null">
        target_package_id = #{targetPackageId,jdbcType=INTEGER},
      </if>
      <if test="os != null">
        os = #{os,jdbcType=INTEGER},
      </if>
      <if test="osVersions != null">
        os_versions = #{osVersions,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.adp.cpc.po.ad.ResTargetPackageOsVersionUpgradePo">
    update res_target_package_os_version_upgrade
    set target_package_id = #{targetPackageId,jdbcType=INTEGER},
      os = #{os,jdbcType=INTEGER},
      os_versions = #{osVersions,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.adp.cpc.po.ad.ResTargetPackageOsVersionUpgradePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into res_target_package_os_version_upgrade (target_package_id, os, os_versions, 
      is_deleted, ctime, mtime
      )
    values (#{targetPackageId,jdbcType=INTEGER}, #{os,jdbcType=INTEGER}, #{osVersions,jdbcType=VARCHAR}, 
      #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}
      )
    <trim prefix="on duplicate key update" suffixOverrides=",">
      target_package_id = values(target_package_id),
      os = values(os),
      os_versions = values(os_versions),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      res_target_package_os_version_upgrade
      (target_package_id,os,os_versions,is_deleted,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.targetPackageId,jdbcType=INTEGER},
        #{item.os,jdbcType=INTEGER},
        #{item.osVersions,jdbcType=VARCHAR},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      res_target_package_os_version_upgrade
      (target_package_id,os,os_versions,is_deleted,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.targetPackageId,jdbcType=INTEGER},
        #{item.os,jdbcType=INTEGER},
        #{item.osVersions,jdbcType=VARCHAR},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      target_package_id = values(target_package_id),
      os = values(os),
      os_versions = values(os_versions),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.adp.cpc.po.ad.ResTargetPackageOsVersionUpgradePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into res_target_package_os_version_upgrade
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="targetPackageId != null">
        target_package_id,
      </if>
      <if test="os != null">
        os,
      </if>
      <if test="osVersions != null">
        os_versions,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="targetPackageId != null">
        #{targetPackageId,jdbcType=INTEGER},
      </if>
      <if test="os != null">
        #{os,jdbcType=INTEGER},
      </if>
      <if test="osVersions != null">
        #{osVersions,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="targetPackageId != null">
        target_package_id = values(target_package_id),
      </if>
      <if test="os != null">
        os = values(os),
      </if>
      <if test="osVersions != null">
        os_versions = values(os_versions),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
    </trim>
  </insert>
</mapper>