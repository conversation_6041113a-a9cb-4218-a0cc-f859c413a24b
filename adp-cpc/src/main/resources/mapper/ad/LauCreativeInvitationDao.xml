<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.adp.cpc.dao.ad.LauCreativeInvitationDao">
  <resultMap id="BaseResultMap" type="com.bilibili.adp.cpc.po.ad.LauCreativeInvitationPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="creative_id" jdbcType="INTEGER" property="creativeId" />
    <result column="unit_id" jdbcType="INTEGER" property="unitId" />
    <result column="avid" jdbcType="BIGINT" property="avid" />
    <result column="place_type" jdbcType="INTEGER" property="placeType" />
    <result column="customized_click_url" jdbcType="VARCHAR" property="customizedClickUrl" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="invitation_status" jdbcType="TINYINT" property="invitationStatus" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
  </resultMap>
  <resultMap id="AliasBaseResultMap" type="com.bilibili.adp.cpc.po.ad.LauCreativeInvitationPo">
    <id column="lau_creative_invitation_id" jdbcType="BIGINT" property="id" />
    <result column="lau_creative_invitation_creative_id" jdbcType="INTEGER" property="creativeId" />
    <result column="lau_creative_invitation_unit_id" jdbcType="INTEGER" property="unitId" />
    <result column="lau_creative_invitation_avid" jdbcType="BIGINT" property="avid" />
    <result column="lau_creative_invitation_place_type" jdbcType="INTEGER" property="placeType" />
    <result column="lau_creative_invitation_customized_click_url" jdbcType="VARCHAR" property="customizedClickUrl" />
    <result column="lau_creative_invitation_is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="lau_creative_invitation_invitation_status" jdbcType="TINYINT" property="invitationStatus" />
    <result column="lau_creative_invitation_ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="lau_creative_invitation_mtime" jdbcType="TIMESTAMP" property="mtime" />
  </resultMap>
  <sql id="Alias_Base_Column_List">
    ${alias}.id as lau_creative_invitation_id, ${alias}.creative_id as lau_creative_invitation_creative_id, 
    ${alias}.unit_id as lau_creative_invitation_unit_id, ${alias}.avid as lau_creative_invitation_avid, 
    ${alias}.place_type as lau_creative_invitation_place_type, ${alias}.customized_click_url as lau_creative_invitation_customized_click_url, 
    ${alias}.is_deleted as lau_creative_invitation_is_deleted, ${alias}.invitation_status as lau_creative_invitation_invitation_status, 
    ${alias}.ctime as lau_creative_invitation_ctime, ${alias}.mtime as lau_creative_invitation_mtime
  </sql>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, creative_id, unit_id, avid, place_type, customized_click_url, is_deleted, invitation_status, 
    ctime, mtime
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.adp.cpc.po.ad.LauCreativeInvitationPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from lau_creative_invitation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from lau_creative_invitation
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from lau_creative_invitation
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.adp.cpc.po.ad.LauCreativeInvitationPoExample">
    delete from lau_creative_invitation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.adp.cpc.po.ad.LauCreativeInvitationPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_creative_invitation (creative_id, unit_id, avid, 
      place_type, customized_click_url, is_deleted, 
      invitation_status, ctime, mtime
      )
    values (#{creativeId,jdbcType=INTEGER}, #{unitId,jdbcType=INTEGER}, #{avid,jdbcType=BIGINT}, 
      #{placeType,jdbcType=INTEGER}, #{customizedClickUrl,jdbcType=VARCHAR}, #{isDeleted,jdbcType=TINYINT}, 
      #{invitationStatus,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.adp.cpc.po.ad.LauCreativeInvitationPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_creative_invitation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="creativeId != null">
        creative_id,
      </if>
      <if test="unitId != null">
        unit_id,
      </if>
      <if test="avid != null">
        avid,
      </if>
      <if test="placeType != null">
        place_type,
      </if>
      <if test="customizedClickUrl != null">
        customized_click_url,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="invitationStatus != null">
        invitation_status,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="creativeId != null">
        #{creativeId,jdbcType=INTEGER},
      </if>
      <if test="unitId != null">
        #{unitId,jdbcType=INTEGER},
      </if>
      <if test="avid != null">
        #{avid,jdbcType=BIGINT},
      </if>
      <if test="placeType != null">
        #{placeType,jdbcType=INTEGER},
      </if>
      <if test="customizedClickUrl != null">
        #{customizedClickUrl,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="invitationStatus != null">
        #{invitationStatus,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.adp.cpc.po.ad.LauCreativeInvitationPoExample" resultType="java.lang.Long">
    select count(*) from lau_creative_invitation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update lau_creative_invitation
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.creativeId != null">
        creative_id = #{record.creativeId,jdbcType=INTEGER},
      </if>
      <if test="record.unitId != null">
        unit_id = #{record.unitId,jdbcType=INTEGER},
      </if>
      <if test="record.avid != null">
        avid = #{record.avid,jdbcType=BIGINT},
      </if>
      <if test="record.placeType != null">
        place_type = #{record.placeType,jdbcType=INTEGER},
      </if>
      <if test="record.customizedClickUrl != null">
        customized_click_url = #{record.customizedClickUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.invitationStatus != null">
        invitation_status = #{record.invitationStatus,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update lau_creative_invitation
    set id = #{record.id,jdbcType=BIGINT},
      creative_id = #{record.creativeId,jdbcType=INTEGER},
      unit_id = #{record.unitId,jdbcType=INTEGER},
      avid = #{record.avid,jdbcType=BIGINT},
      place_type = #{record.placeType,jdbcType=INTEGER},
      customized_click_url = #{record.customizedClickUrl,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      invitation_status = #{record.invitationStatus,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.adp.cpc.po.ad.LauCreativeInvitationPo">
    update lau_creative_invitation
    <set>
      <if test="creativeId != null">
        creative_id = #{creativeId,jdbcType=INTEGER},
      </if>
      <if test="unitId != null">
        unit_id = #{unitId,jdbcType=INTEGER},
      </if>
      <if test="avid != null">
        avid = #{avid,jdbcType=BIGINT},
      </if>
      <if test="placeType != null">
        place_type = #{placeType,jdbcType=INTEGER},
      </if>
      <if test="customizedClickUrl != null">
        customized_click_url = #{customizedClickUrl,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="invitationStatus != null">
        invitation_status = #{invitationStatus,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.adp.cpc.po.ad.LauCreativeInvitationPo">
    update lau_creative_invitation
    set creative_id = #{creativeId,jdbcType=INTEGER},
      unit_id = #{unitId,jdbcType=INTEGER},
      avid = #{avid,jdbcType=BIGINT},
      place_type = #{placeType,jdbcType=INTEGER},
      customized_click_url = #{customizedClickUrl,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      invitation_status = #{invitationStatus,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.adp.cpc.po.ad.LauCreativeInvitationPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_creative_invitation (creative_id, unit_id, avid, 
      place_type, customized_click_url, is_deleted, 
      invitation_status, ctime, mtime
      )
    values (#{creativeId,jdbcType=INTEGER}, #{unitId,jdbcType=INTEGER}, #{avid,jdbcType=BIGINT}, 
      #{placeType,jdbcType=INTEGER}, #{customizedClickUrl,jdbcType=VARCHAR}, #{isDeleted,jdbcType=TINYINT}, 
      #{invitationStatus,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}
      )
    <trim prefix="on duplicate key update" suffixOverrides=",">
      creative_id = values(creative_id),
      unit_id = values(unit_id),
      avid = values(avid),
      place_type = values(place_type),
      customized_click_url = values(customized_click_url),
      is_deleted = values(is_deleted),
      invitation_status = values(invitation_status),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      lau_creative_invitation
      (creative_id,unit_id,avid,place_type,customized_click_url,is_deleted,invitation_status,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.creativeId,jdbcType=INTEGER},
        #{item.unitId,jdbcType=INTEGER},
        #{item.avid,jdbcType=BIGINT},
        #{item.placeType,jdbcType=INTEGER},
        #{item.customizedClickUrl,jdbcType=VARCHAR},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.invitationStatus,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      lau_creative_invitation
      (creative_id,unit_id,avid,place_type,customized_click_url,is_deleted,invitation_status,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.creativeId,jdbcType=INTEGER},
        #{item.unitId,jdbcType=INTEGER},
        #{item.avid,jdbcType=BIGINT},
        #{item.placeType,jdbcType=INTEGER},
        #{item.customizedClickUrl,jdbcType=VARCHAR},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.invitationStatus,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      creative_id = values(creative_id),
      unit_id = values(unit_id),
      avid = values(avid),
      place_type = values(place_type),
      customized_click_url = values(customized_click_url),
      is_deleted = values(is_deleted),
      invitation_status = values(invitation_status),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.adp.cpc.po.ad.LauCreativeInvitationPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_creative_invitation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="creativeId != null">
        creative_id,
      </if>
      <if test="unitId != null">
        unit_id,
      </if>
      <if test="avid != null">
        avid,
      </if>
      <if test="placeType != null">
        place_type,
      </if>
      <if test="customizedClickUrl != null">
        customized_click_url,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="invitationStatus != null">
        invitation_status,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="creativeId != null">
        #{creativeId,jdbcType=INTEGER},
      </if>
      <if test="unitId != null">
        #{unitId,jdbcType=INTEGER},
      </if>
      <if test="avid != null">
        #{avid,jdbcType=BIGINT},
      </if>
      <if test="placeType != null">
        #{placeType,jdbcType=INTEGER},
      </if>
      <if test="customizedClickUrl != null">
        #{customizedClickUrl,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="invitationStatus != null">
        #{invitationStatus,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="creativeId != null">
        creative_id = values(creative_id),
      </if>
      <if test="unitId != null">
        unit_id = values(unit_id),
      </if>
      <if test="avid != null">
        avid = values(avid),
      </if>
      <if test="placeType != null">
        place_type = values(place_type),
      </if>
      <if test="customizedClickUrl != null">
        customized_click_url = values(customized_click_url),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="invitationStatus != null">
        invitation_status = values(invitation_status),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
    </trim>
  </insert>
</mapper>