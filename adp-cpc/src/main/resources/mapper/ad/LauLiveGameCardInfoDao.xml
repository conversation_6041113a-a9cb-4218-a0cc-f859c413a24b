<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.adp.cpc.dao.ad.LauLiveGameCardInfoDao">
  <resultMap id="BaseResultMap" type="com.bilibili.adp.cpc.po.ad.LauLiveGameCardInfoPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="card_id" jdbcType="BIGINT" property="cardId" />
    <result column="card_name" jdbcType="VARCHAR" property="cardName" />
    <result column="card_type" jdbcType="TINYINT" property="cardType" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="game_card_icon" jdbcType="VARCHAR" property="gameCardIcon" />
    <result column="live_room_ids" jdbcType="VARCHAR" property="liveRoomIds" />
    <result column="active_time" jdbcType="VARCHAR" property="activeTime" />
  </resultMap>
  <resultMap id="AliasBaseResultMap" type="com.bilibili.adp.cpc.po.ad.LauLiveGameCardInfoPo">
    <id column="lau_live_game_card_info_id" jdbcType="BIGINT" property="id" />
    <result column="lau_live_game_card_info_card_id" jdbcType="BIGINT" property="cardId" />
    <result column="lau_live_game_card_info_card_name" jdbcType="VARCHAR" property="cardName" />
    <result column="lau_live_game_card_info_card_type" jdbcType="TINYINT" property="cardType" />
    <result column="lau_live_game_card_info_ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="lau_live_game_card_info_mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="lau_live_game_card_info_is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="lau_live_game_card_info_game_card_icon" jdbcType="VARCHAR" property="gameCardIcon" />
    <result column="lau_live_game_card_info_live_room_ids" jdbcType="VARCHAR" property="liveRoomIds" />
  </resultMap>
  <sql id="Alias_Base_Column_List">
    ${alias}.id as lau_live_game_card_info_id, ${alias}.card_id as lau_live_game_card_info_card_id,
    ${alias}.card_name as lau_live_game_card_info_card_name, ${alias}.card_type as lau_live_game_card_info_card_type,
    ${alias}.ctime as lau_live_game_card_info_ctime, ${alias}.mtime as lau_live_game_card_info_mtime,
    ${alias}.is_deleted as lau_live_game_card_info_is_deleted, ${alias}.game_card_icon as lau_live_game_card_info_game_card_icon,
    ${alias}.live_room_ids as lau_live_game_card_info_live_room_ids
  </sql>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, card_id, card_name, card_type, ctime, mtime, is_deleted, game_card_icon, live_room_ids, 
    active_time
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.adp.cpc.po.ad.LauLiveGameCardInfoPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from lau_live_game_card_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from lau_live_game_card_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from lau_live_game_card_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.adp.cpc.po.ad.LauLiveGameCardInfoPoExample">
    delete from lau_live_game_card_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.adp.cpc.po.ad.LauLiveGameCardInfoPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_live_game_card_info (card_id, card_name, card_type, 
      ctime, mtime, is_deleted, 
      game_card_icon, live_room_ids)
    values (#{cardId,jdbcType=BIGINT}, #{cardName,jdbcType=VARCHAR}, #{cardType,jdbcType=TINYINT}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=TINYINT}, 
      #{gameCardIcon,jdbcType=VARCHAR}, #{liveRoomIds,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.adp.cpc.po.ad.LauLiveGameCardInfoPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_live_game_card_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="cardId != null">
        card_id,
      </if>
      <if test="cardName != null">
        card_name,
      </if>
      <if test="cardType != null">
        card_type,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="gameCardIcon != null">
        game_card_icon,
      </if>
      <if test="liveRoomIds != null">
        live_room_ids,
      </if>
      <if test="activeTime != null">
        active_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="cardId != null">
        #{cardId,jdbcType=BIGINT},
      </if>
      <if test="cardName != null">
        #{cardName,jdbcType=VARCHAR},
      </if>
      <if test="cardType != null">
        #{cardType,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="gameCardIcon != null">
        #{gameCardIcon,jdbcType=VARCHAR},
      </if>
      <if test="liveRoomIds != null">
        #{liveRoomIds,jdbcType=VARCHAR},
      </if>
      <if test="activeTime != null">
        #{activeTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.adp.cpc.po.ad.LauLiveGameCardInfoPoExample" resultType="java.lang.Long">
    select count(*) from lau_live_game_card_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update lau_live_game_card_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.cardId != null">
        card_id = #{record.cardId,jdbcType=BIGINT},
      </if>
      <if test="record.cardName != null">
        card_name = #{record.cardName,jdbcType=VARCHAR},
      </if>
      <if test="record.cardType != null">
        card_type = #{record.cardType,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.gameCardIcon != null">
        game_card_icon = #{record.gameCardIcon,jdbcType=VARCHAR},
      </if>
      <if test="record.liveRoomIds != null">
        live_room_ids = #{record.liveRoomIds,jdbcType=VARCHAR},
      </if>
      <if test="record.activeTime != null">
        active_time = #{record.activeTime,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update lau_live_game_card_info
    set id = #{record.id,jdbcType=BIGINT},
      card_id = #{record.cardId,jdbcType=BIGINT},
      card_name = #{record.cardName,jdbcType=VARCHAR},
      card_type = #{record.cardType,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      game_card_icon = #{record.gameCardIcon,jdbcType=VARCHAR},
      live_room_ids = #{record.liveRoomIds,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.adp.cpc.po.ad.LauLiveGameCardInfoPo">
    update lau_live_game_card_info
    <set>
      <if test="cardId != null">
        card_id = #{cardId,jdbcType=BIGINT},
      </if>
      <if test="cardName != null">
        card_name = #{cardName,jdbcType=VARCHAR},
      </if>
      <if test="cardType != null">
        card_type = #{cardType,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="gameCardIcon != null">
        game_card_icon = #{gameCardIcon,jdbcType=VARCHAR},
      </if>
      <if test="liveRoomIds != null">
        live_room_ids = #{liveRoomIds,jdbcType=VARCHAR},
      </if>
      <if test="activeTime != null">
        active_time = #{activeTime,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.adp.cpc.po.ad.LauLiveGameCardInfoPo">
    update lau_live_game_card_info
    set card_id = #{cardId,jdbcType=BIGINT},
      card_name = #{cardName,jdbcType=VARCHAR},
      card_type = #{cardType,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      game_card_icon = #{gameCardIcon,jdbcType=VARCHAR},
      live_room_ids = #{liveRoomIds,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.adp.cpc.po.ad.LauLiveGameCardInfoPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_live_game_card_info (card_id, card_name, card_type,
      ctime, mtime, is_deleted,
      game_card_icon, live_room_ids)
    values (#{cardId,jdbcType=BIGINT}, #{cardName,jdbcType=VARCHAR}, #{cardType,jdbcType=TINYINT},
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=TINYINT},
      #{gameCardIcon,jdbcType=VARCHAR}, #{liveRoomIds,jdbcType=VARCHAR})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      card_id = values(card_id),
      card_name = values(card_name),
      card_type = values(card_type),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
      game_card_icon = values(game_card_icon),
      live_room_ids = values(live_room_ids),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into
      lau_live_game_card_info
      (card_id,card_name,card_type,ctime,mtime,is_deleted,game_card_icon,live_room_ids)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.cardId,jdbcType=BIGINT},
        #{item.cardName,jdbcType=VARCHAR},
        #{item.cardType,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.gameCardIcon,jdbcType=VARCHAR},
        #{item.liveRoomIds,jdbcType=VARCHAR},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into
      lau_live_game_card_info
      (card_id,card_name,card_type,ctime,mtime,is_deleted,game_card_icon,live_room_ids)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.cardId,jdbcType=BIGINT},
        #{item.cardName,jdbcType=VARCHAR},
        #{item.cardType,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.gameCardIcon,jdbcType=VARCHAR},
        #{item.liveRoomIds,jdbcType=VARCHAR},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      card_id = values(card_id),
      card_name = values(card_name),
      card_type = values(card_type),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
      game_card_icon = values(game_card_icon),
      live_room_ids = values(live_room_ids),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.adp.cpc.po.ad.LauLiveGameCardInfoPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_live_game_card_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="cardId != null">
        card_id,
      </if>
      <if test="cardName != null">
        card_name,
      </if>
      <if test="cardType != null">
        card_type,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="gameCardIcon != null">
        game_card_icon,
      </if>
      <if test="liveRoomIds != null">
        live_room_ids,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="cardId != null">
        #{cardId,jdbcType=BIGINT},
      </if>
      <if test="cardName != null">
        #{cardName,jdbcType=VARCHAR},
      </if>
      <if test="cardType != null">
        #{cardType,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="gameCardIcon != null">
        #{gameCardIcon,jdbcType=VARCHAR},
      </if>
      <if test="liveRoomIds != null">
        #{liveRoomIds,jdbcType=VARCHAR},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="cardId != null">
        card_id = values(card_id),
      </if>
      <if test="cardName != null">
        card_name = values(card_name),
      </if>
      <if test="cardType != null">
        card_type = values(card_type),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="gameCardIcon != null">
        game_card_icon = values(game_card_icon),
      </if>
      <if test="liveRoomIds != null">
        live_room_ids = values(live_room_ids),
      </if>
    </trim>
  </insert>
</mapper>