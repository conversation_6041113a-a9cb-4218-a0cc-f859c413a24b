<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.adp.cpc.dao.ad.ExtLauAccountNextdayBudgetDao">
  <resultMap id="BaseResultMap" type="com.bilibili.adp.launch.biz.pojo.LauAccountNextdayBudgetPo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="account_id" jdbcType="INTEGER" property="accountId" />
    <result column="budget" jdbcType="INTEGER" property="budget" />
    <result column="budget_limit_type" jdbcType="TINYINT" property="budgetLimitType" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="budget_effective_time" jdbcType="TIMESTAMP" property="budgetEffectiveTime" />
  </resultMap>
  <resultMap id="AliasBaseResultMap" type="com.bilibili.adp.launch.biz.pojo.LauAccountNextdayBudgetPo">
    <id column="lau_account_nextday_budget_id" jdbcType="INTEGER" property="id" />
    <result column="lau_account_nextday_budget_account_id" jdbcType="INTEGER" property="accountId" />
    <result column="lau_account_nextday_budget_budget" jdbcType="INTEGER" property="budget" />
    <result column="lau_account_nextday_budget_budget_limit_type" jdbcType="TINYINT" property="budgetLimitType" />
    <result column="lau_account_nextday_budget_is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="lau_account_nextday_budget_ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="lau_account_nextday_budget_mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="lau_account_nextday_budget_budget_effective_time" jdbcType="TIMESTAMP" property="budgetEffectiveTime" />
  </resultMap>
  <sql id="Alias_Base_Column_List">
    ${alias}.id as lau_account_nextday_budget_id, ${alias}.account_id as lau_account_nextday_budget_account_id,
    ${alias}.budget as lau_account_nextday_budget_budget, ${alias}.budget_limit_type as lau_account_nextday_budget_budget_limit_type,
    ${alias}.is_deleted as lau_account_nextday_budget_is_deleted, ${alias}.ctime as lau_account_nextday_budget_ctime,
    ${alias}.mtime as lau_account_nextday_budget_mtime, ${alias}.budget_effective_time as lau_account_nextday_budget_budget_effective_time
  </sql>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <select id="queryMinAndMaxIdByExample" parameterType="com.bilibili.adp.launch.biz.pojo.LauAccountNextdayBudgetPoExample"
          resultType="com.bilibili.report.platform.api.dto.MinAndMaxId">
    select
        min(id) minId, max(id) maxId
    from lau_account_nextday_budget
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
</mapper>