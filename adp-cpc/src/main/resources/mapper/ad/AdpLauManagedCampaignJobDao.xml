<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.adp.cpc.dao.ad.AdpLauManagedCampaignJobDao">
  <resultMap id="BaseResultMap" type="com.bilibili.adp.cpc.po.ad.LauManagedCampaignJobPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="account_id" jdbcType="INTEGER" property="accountId" />
    <result column="operator_name" jdbcType="VARCHAR" property="operatorName" />
    <result column="campaign_id" jdbcType="INTEGER" property="campaignId" />
    <result column="ocpx_target" jdbcType="TINYINT" property="ocpxTarget" />
    <result column="cost_price" jdbcType="INTEGER" property="costPrice" />
    <result column="is_long_term_launch" jdbcType="TINYINT" property="isLongTermLaunch" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="batch_no" jdbcType="INTEGER" property="batchNo" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="env" jdbcType="VARCHAR" property="env" />
    <result column="launch_type" jdbcType="TINYINT" property="launchType" />
    <result column="is_unit_budget_average" jdbcType="TINYINT" property="isUnitBudgetAverage" />
    <result column="creative_generate_type" jdbcType="TINYINT" property="creativeGenerateType" />
    <result column="prefer_scene" jdbcType="TINYINT" property="preferScene" />
    <result column="specific_scenes" jdbcType="VARCHAR" property="specificScenes" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="is_yellow_car" jdbcType="TINYINT" property="isYellowCar" />
    <result column="yellow_car_title" jdbcType="VARCHAR" property="yellowCarTitle" />
    <result column="qualification_package_id" jdbcType="INTEGER" property="qualificationPackageId" />
    <result column="customized_imp_url" jdbcType="VARCHAR" property="customizedImpUrl" />
    <result column="customized_click_url" jdbcType="VARCHAR" property="customizedClickUrl" />
    <result column="managed_version" jdbcType="TINYINT" property="managedVersion" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, account_id, operator_name, campaign_id, ocpx_target, cost_price, is_long_term_launch, 
    status, batch_no, ctime, mtime, env, launch_type, is_unit_budget_average, creative_generate_type, 
    prefer_scene, specific_scenes, description, is_yellow_car, yellow_car_title, qualification_package_id, 
    customized_imp_url, customized_click_url, managed_version
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.adp.cpc.po.ad.LauManagedCampaignJobPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from lau_managed_campaign_job
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from lau_managed_campaign_job
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from lau_managed_campaign_job
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.adp.cpc.po.ad.LauManagedCampaignJobPoExample">
    delete from lau_managed_campaign_job
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.adp.cpc.po.ad.LauManagedCampaignJobPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_managed_campaign_job (account_id, operator_name, campaign_id, 
      ocpx_target, cost_price, is_long_term_launch, 
      status, batch_no, ctime, 
      mtime, env, launch_type, 
      is_unit_budget_average, creative_generate_type, 
      prefer_scene, specific_scenes, description, 
      is_yellow_car, yellow_car_title, qualification_package_id, 
      customized_imp_url, customized_click_url, managed_version
      )
    values (#{accountId,jdbcType=INTEGER}, #{operatorName,jdbcType=VARCHAR}, #{campaignId,jdbcType=INTEGER}, 
      #{ocpxTarget,jdbcType=TINYINT}, #{costPrice,jdbcType=INTEGER}, #{isLongTermLaunch,jdbcType=TINYINT}, 
      #{status,jdbcType=TINYINT}, #{batchNo,jdbcType=INTEGER}, #{ctime,jdbcType=TIMESTAMP}, 
      #{mtime,jdbcType=TIMESTAMP}, #{env,jdbcType=VARCHAR}, #{launchType,jdbcType=TINYINT}, 
      #{isUnitBudgetAverage,jdbcType=TINYINT}, #{creativeGenerateType,jdbcType=TINYINT}, 
      #{preferScene,jdbcType=TINYINT}, #{specificScenes,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, 
      #{isYellowCar,jdbcType=TINYINT}, #{yellowCarTitle,jdbcType=VARCHAR}, #{qualificationPackageId,jdbcType=INTEGER}, 
      #{customizedImpUrl,jdbcType=VARCHAR}, #{customizedClickUrl,jdbcType=VARCHAR}, #{managedVersion,jdbcType=TINYINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.adp.cpc.po.ad.LauManagedCampaignJobPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_managed_campaign_job
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        account_id,
      </if>
      <if test="operatorName != null">
        operator_name,
      </if>
      <if test="campaignId != null">
        campaign_id,
      </if>
      <if test="ocpxTarget != null">
        ocpx_target,
      </if>
      <if test="costPrice != null">
        cost_price,
      </if>
      <if test="isLongTermLaunch != null">
        is_long_term_launch,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="batchNo != null">
        batch_no,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="env != null">
        env,
      </if>
      <if test="launchType != null">
        launch_type,
      </if>
      <if test="isUnitBudgetAverage != null">
        is_unit_budget_average,
      </if>
      <if test="creativeGenerateType != null">
        creative_generate_type,
      </if>
      <if test="preferScene != null">
        prefer_scene,
      </if>
      <if test="specificScenes != null">
        specific_scenes,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="isYellowCar != null">
        is_yellow_car,
      </if>
      <if test="yellowCarTitle != null">
        yellow_car_title,
      </if>
      <if test="qualificationPackageId != null">
        qualification_package_id,
      </if>
      <if test="customizedImpUrl != null">
        customized_imp_url,
      </if>
      <if test="customizedClickUrl != null">
        customized_click_url,
      </if>
      <if test="managedVersion != null">
        managed_version,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="operatorName != null">
        #{operatorName,jdbcType=VARCHAR},
      </if>
      <if test="campaignId != null">
        #{campaignId,jdbcType=INTEGER},
      </if>
      <if test="ocpxTarget != null">
        #{ocpxTarget,jdbcType=TINYINT},
      </if>
      <if test="costPrice != null">
        #{costPrice,jdbcType=INTEGER},
      </if>
      <if test="isLongTermLaunch != null">
        #{isLongTermLaunch,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="batchNo != null">
        #{batchNo,jdbcType=INTEGER},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="env != null">
        #{env,jdbcType=VARCHAR},
      </if>
      <if test="launchType != null">
        #{launchType,jdbcType=TINYINT},
      </if>
      <if test="isUnitBudgetAverage != null">
        #{isUnitBudgetAverage,jdbcType=TINYINT},
      </if>
      <if test="creativeGenerateType != null">
        #{creativeGenerateType,jdbcType=TINYINT},
      </if>
      <if test="preferScene != null">
        #{preferScene,jdbcType=TINYINT},
      </if>
      <if test="specificScenes != null">
        #{specificScenes,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="isYellowCar != null">
        #{isYellowCar,jdbcType=TINYINT},
      </if>
      <if test="yellowCarTitle != null">
        #{yellowCarTitle,jdbcType=VARCHAR},
      </if>
      <if test="qualificationPackageId != null">
        #{qualificationPackageId,jdbcType=INTEGER},
      </if>
      <if test="customizedImpUrl != null">
        #{customizedImpUrl,jdbcType=VARCHAR},
      </if>
      <if test="customizedClickUrl != null">
        #{customizedClickUrl,jdbcType=VARCHAR},
      </if>
      <if test="managedVersion != null">
        #{managedVersion,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.adp.cpc.po.ad.LauManagedCampaignJobPoExample" resultType="java.lang.Long">
    select count(*) from lau_managed_campaign_job
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update lau_managed_campaign_job
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.accountId != null">
        account_id = #{record.accountId,jdbcType=INTEGER},
      </if>
      <if test="record.operatorName != null">
        operator_name = #{record.operatorName,jdbcType=VARCHAR},
      </if>
      <if test="record.campaignId != null">
        campaign_id = #{record.campaignId,jdbcType=INTEGER},
      </if>
      <if test="record.ocpxTarget != null">
        ocpx_target = #{record.ocpxTarget,jdbcType=TINYINT},
      </if>
      <if test="record.costPrice != null">
        cost_price = #{record.costPrice,jdbcType=INTEGER},
      </if>
      <if test="record.isLongTermLaunch != null">
        is_long_term_launch = #{record.isLongTermLaunch,jdbcType=TINYINT},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.batchNo != null">
        batch_no = #{record.batchNo,jdbcType=INTEGER},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.env != null">
        env = #{record.env,jdbcType=VARCHAR},
      </if>
      <if test="record.launchType != null">
        launch_type = #{record.launchType,jdbcType=TINYINT},
      </if>
      <if test="record.isUnitBudgetAverage != null">
        is_unit_budget_average = #{record.isUnitBudgetAverage,jdbcType=TINYINT},
      </if>
      <if test="record.creativeGenerateType != null">
        creative_generate_type = #{record.creativeGenerateType,jdbcType=TINYINT},
      </if>
      <if test="record.preferScene != null">
        prefer_scene = #{record.preferScene,jdbcType=TINYINT},
      </if>
      <if test="record.specificScenes != null">
        specific_scenes = #{record.specificScenes,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.isYellowCar != null">
        is_yellow_car = #{record.isYellowCar,jdbcType=TINYINT},
      </if>
      <if test="record.yellowCarTitle != null">
        yellow_car_title = #{record.yellowCarTitle,jdbcType=VARCHAR},
      </if>
      <if test="record.qualificationPackageId != null">
        qualification_package_id = #{record.qualificationPackageId,jdbcType=INTEGER},
      </if>
      <if test="record.customizedImpUrl != null">
        customized_imp_url = #{record.customizedImpUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.customizedClickUrl != null">
        customized_click_url = #{record.customizedClickUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.managedVersion != null">
        managed_version = #{record.managedVersion,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update lau_managed_campaign_job
    set id = #{record.id,jdbcType=BIGINT},
      account_id = #{record.accountId,jdbcType=INTEGER},
      operator_name = #{record.operatorName,jdbcType=VARCHAR},
      campaign_id = #{record.campaignId,jdbcType=INTEGER},
      ocpx_target = #{record.ocpxTarget,jdbcType=TINYINT},
      cost_price = #{record.costPrice,jdbcType=INTEGER},
      is_long_term_launch = #{record.isLongTermLaunch,jdbcType=TINYINT},
      status = #{record.status,jdbcType=TINYINT},
      batch_no = #{record.batchNo,jdbcType=INTEGER},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      env = #{record.env,jdbcType=VARCHAR},
      launch_type = #{record.launchType,jdbcType=TINYINT},
      is_unit_budget_average = #{record.isUnitBudgetAverage,jdbcType=TINYINT},
      creative_generate_type = #{record.creativeGenerateType,jdbcType=TINYINT},
      prefer_scene = #{record.preferScene,jdbcType=TINYINT},
      specific_scenes = #{record.specificScenes,jdbcType=VARCHAR},
      description = #{record.description,jdbcType=VARCHAR},
      is_yellow_car = #{record.isYellowCar,jdbcType=TINYINT},
      yellow_car_title = #{record.yellowCarTitle,jdbcType=VARCHAR},
      qualification_package_id = #{record.qualificationPackageId,jdbcType=INTEGER},
      customized_imp_url = #{record.customizedImpUrl,jdbcType=VARCHAR},
      customized_click_url = #{record.customizedClickUrl,jdbcType=VARCHAR},
      managed_version = #{record.managedVersion,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.adp.cpc.po.ad.LauManagedCampaignJobPo">
    update lau_managed_campaign_job
    <set>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=INTEGER},
      </if>
      <if test="operatorName != null">
        operator_name = #{operatorName,jdbcType=VARCHAR},
      </if>
      <if test="campaignId != null">
        campaign_id = #{campaignId,jdbcType=INTEGER},
      </if>
      <if test="ocpxTarget != null">
        ocpx_target = #{ocpxTarget,jdbcType=TINYINT},
      </if>
      <if test="costPrice != null">
        cost_price = #{costPrice,jdbcType=INTEGER},
      </if>
      <if test="isLongTermLaunch != null">
        is_long_term_launch = #{isLongTermLaunch,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="batchNo != null">
        batch_no = #{batchNo,jdbcType=INTEGER},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="env != null">
        env = #{env,jdbcType=VARCHAR},
      </if>
      <if test="launchType != null">
        launch_type = #{launchType,jdbcType=TINYINT},
      </if>
      <if test="isUnitBudgetAverage != null">
        is_unit_budget_average = #{isUnitBudgetAverage,jdbcType=TINYINT},
      </if>
      <if test="creativeGenerateType != null">
        creative_generate_type = #{creativeGenerateType,jdbcType=TINYINT},
      </if>
      <if test="preferScene != null">
        prefer_scene = #{preferScene,jdbcType=TINYINT},
      </if>
      <if test="specificScenes != null">
        specific_scenes = #{specificScenes,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="isYellowCar != null">
        is_yellow_car = #{isYellowCar,jdbcType=TINYINT},
      </if>
      <if test="yellowCarTitle != null">
        yellow_car_title = #{yellowCarTitle,jdbcType=VARCHAR},
      </if>
      <if test="qualificationPackageId != null">
        qualification_package_id = #{qualificationPackageId,jdbcType=INTEGER},
      </if>
      <if test="customizedImpUrl != null">
        customized_imp_url = #{customizedImpUrl,jdbcType=VARCHAR},
      </if>
      <if test="customizedClickUrl != null">
        customized_click_url = #{customizedClickUrl,jdbcType=VARCHAR},
      </if>
      <if test="managedVersion != null">
        managed_version = #{managedVersion,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.adp.cpc.po.ad.LauManagedCampaignJobPo">
    update lau_managed_campaign_job
    set account_id = #{accountId,jdbcType=INTEGER},
      operator_name = #{operatorName,jdbcType=VARCHAR},
      campaign_id = #{campaignId,jdbcType=INTEGER},
      ocpx_target = #{ocpxTarget,jdbcType=TINYINT},
      cost_price = #{costPrice,jdbcType=INTEGER},
      is_long_term_launch = #{isLongTermLaunch,jdbcType=TINYINT},
      status = #{status,jdbcType=TINYINT},
      batch_no = #{batchNo,jdbcType=INTEGER},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      env = #{env,jdbcType=VARCHAR},
      launch_type = #{launchType,jdbcType=TINYINT},
      is_unit_budget_average = #{isUnitBudgetAverage,jdbcType=TINYINT},
      creative_generate_type = #{creativeGenerateType,jdbcType=TINYINT},
      prefer_scene = #{preferScene,jdbcType=TINYINT},
      specific_scenes = #{specificScenes,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      is_yellow_car = #{isYellowCar,jdbcType=TINYINT},
      yellow_car_title = #{yellowCarTitle,jdbcType=VARCHAR},
      qualification_package_id = #{qualificationPackageId,jdbcType=INTEGER},
      customized_imp_url = #{customizedImpUrl,jdbcType=VARCHAR},
      customized_click_url = #{customizedClickUrl,jdbcType=VARCHAR},
      managed_version = #{managedVersion,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.adp.cpc.po.ad.LauManagedCampaignJobPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_managed_campaign_job (account_id, operator_name, campaign_id, 
      ocpx_target, cost_price, is_long_term_launch, 
      status, batch_no, ctime, 
      mtime, env, launch_type, 
      is_unit_budget_average, creative_generate_type, 
      prefer_scene, specific_scenes, description, 
      is_yellow_car, yellow_car_title, qualification_package_id, 
      customized_imp_url, customized_click_url, managed_version
      )
    values (#{accountId,jdbcType=INTEGER}, #{operatorName,jdbcType=VARCHAR}, #{campaignId,jdbcType=INTEGER}, 
      #{ocpxTarget,jdbcType=TINYINT}, #{costPrice,jdbcType=INTEGER}, #{isLongTermLaunch,jdbcType=TINYINT}, 
      #{status,jdbcType=TINYINT}, #{batchNo,jdbcType=INTEGER}, #{ctime,jdbcType=TIMESTAMP}, 
      #{mtime,jdbcType=TIMESTAMP}, #{env,jdbcType=VARCHAR}, #{launchType,jdbcType=TINYINT}, 
      #{isUnitBudgetAverage,jdbcType=TINYINT}, #{creativeGenerateType,jdbcType=TINYINT}, 
      #{preferScene,jdbcType=TINYINT}, #{specificScenes,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, 
      #{isYellowCar,jdbcType=TINYINT}, #{yellowCarTitle,jdbcType=VARCHAR}, #{qualificationPackageId,jdbcType=INTEGER}, 
      #{customizedImpUrl,jdbcType=VARCHAR}, #{customizedClickUrl,jdbcType=VARCHAR}, #{managedVersion,jdbcType=TINYINT}
      )
    <trim prefix="on duplicate key update" suffixOverrides=",">
      account_id = values(account_id),
      operator_name = values(operator_name),
      campaign_id = values(campaign_id),
      ocpx_target = values(ocpx_target),
      cost_price = values(cost_price),
      is_long_term_launch = values(is_long_term_launch),
      status = values(status),
      batch_no = values(batch_no),
      ctime = values(ctime),
      mtime = values(mtime),
      env = values(env),
      launch_type = values(launch_type),
      is_unit_budget_average = values(is_unit_budget_average),
      creative_generate_type = values(creative_generate_type),
      prefer_scene = values(prefer_scene),
      specific_scenes = values(specific_scenes),
      description = values(description),
      is_yellow_car = values(is_yellow_car),
      yellow_car_title = values(yellow_car_title),
      qualification_package_id = values(qualification_package_id),
      customized_imp_url = values(customized_imp_url),
      customized_click_url = values(customized_click_url),
      managed_version = values(managed_version),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      lau_managed_campaign_job
      (account_id,operator_name,campaign_id,ocpx_target,cost_price,is_long_term_launch,status,batch_no,ctime,mtime,env,launch_type,is_unit_budget_average,creative_generate_type,prefer_scene,specific_scenes,description,is_yellow_car,yellow_car_title,qualification_package_id,customized_imp_url,customized_click_url,managed_version)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.accountId,jdbcType=INTEGER},
        #{item.operatorName,jdbcType=VARCHAR},
        #{item.campaignId,jdbcType=INTEGER},
        #{item.ocpxTarget,jdbcType=TINYINT},
        #{item.costPrice,jdbcType=INTEGER},
        #{item.isLongTermLaunch,jdbcType=TINYINT},
        #{item.status,jdbcType=TINYINT},
        #{item.batchNo,jdbcType=INTEGER},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.env,jdbcType=VARCHAR},
        #{item.launchType,jdbcType=TINYINT},
        #{item.isUnitBudgetAverage,jdbcType=TINYINT},
        #{item.creativeGenerateType,jdbcType=TINYINT},
        #{item.preferScene,jdbcType=TINYINT},
        #{item.specificScenes,jdbcType=VARCHAR},
        #{item.description,jdbcType=VARCHAR},
        #{item.isYellowCar,jdbcType=TINYINT},
        #{item.yellowCarTitle,jdbcType=VARCHAR},
        #{item.qualificationPackageId,jdbcType=INTEGER},
        #{item.customizedImpUrl,jdbcType=VARCHAR},
        #{item.customizedClickUrl,jdbcType=VARCHAR},
        #{item.managedVersion,jdbcType=TINYINT},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      lau_managed_campaign_job
      (account_id,operator_name,campaign_id,ocpx_target,cost_price,is_long_term_launch,status,batch_no,ctime,mtime,env,launch_type,is_unit_budget_average,creative_generate_type,prefer_scene,specific_scenes,description,is_yellow_car,yellow_car_title,qualification_package_id,customized_imp_url,customized_click_url,managed_version)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.accountId,jdbcType=INTEGER},
        #{item.operatorName,jdbcType=VARCHAR},
        #{item.campaignId,jdbcType=INTEGER},
        #{item.ocpxTarget,jdbcType=TINYINT},
        #{item.costPrice,jdbcType=INTEGER},
        #{item.isLongTermLaunch,jdbcType=TINYINT},
        #{item.status,jdbcType=TINYINT},
        #{item.batchNo,jdbcType=INTEGER},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.env,jdbcType=VARCHAR},
        #{item.launchType,jdbcType=TINYINT},
        #{item.isUnitBudgetAverage,jdbcType=TINYINT},
        #{item.creativeGenerateType,jdbcType=TINYINT},
        #{item.preferScene,jdbcType=TINYINT},
        #{item.specificScenes,jdbcType=VARCHAR},
        #{item.description,jdbcType=VARCHAR},
        #{item.isYellowCar,jdbcType=TINYINT},
        #{item.yellowCarTitle,jdbcType=VARCHAR},
        #{item.qualificationPackageId,jdbcType=INTEGER},
        #{item.customizedImpUrl,jdbcType=VARCHAR},
        #{item.customizedClickUrl,jdbcType=VARCHAR},
        #{item.managedVersion,jdbcType=TINYINT},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      account_id = values(account_id),
      operator_name = values(operator_name),
      campaign_id = values(campaign_id),
      ocpx_target = values(ocpx_target),
      cost_price = values(cost_price),
      is_long_term_launch = values(is_long_term_launch),
      status = values(status),
      batch_no = values(batch_no),
      ctime = values(ctime),
      mtime = values(mtime),
      env = values(env),
      launch_type = values(launch_type),
      is_unit_budget_average = values(is_unit_budget_average),
      creative_generate_type = values(creative_generate_type),
      prefer_scene = values(prefer_scene),
      specific_scenes = values(specific_scenes),
      description = values(description),
      is_yellow_car = values(is_yellow_car),
      yellow_car_title = values(yellow_car_title),
      qualification_package_id = values(qualification_package_id),
      customized_imp_url = values(customized_imp_url),
      customized_click_url = values(customized_click_url),
      managed_version = values(managed_version),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.adp.cpc.po.ad.LauManagedCampaignJobPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_managed_campaign_job
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        account_id,
      </if>
      <if test="operatorName != null">
        operator_name,
      </if>
      <if test="campaignId != null">
        campaign_id,
      </if>
      <if test="ocpxTarget != null">
        ocpx_target,
      </if>
      <if test="costPrice != null">
        cost_price,
      </if>
      <if test="isLongTermLaunch != null">
        is_long_term_launch,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="batchNo != null">
        batch_no,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="env != null">
        env,
      </if>
      <if test="launchType != null">
        launch_type,
      </if>
      <if test="isUnitBudgetAverage != null">
        is_unit_budget_average,
      </if>
      <if test="creativeGenerateType != null">
        creative_generate_type,
      </if>
      <if test="preferScene != null">
        prefer_scene,
      </if>
      <if test="specificScenes != null">
        specific_scenes,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="isYellowCar != null">
        is_yellow_car,
      </if>
      <if test="yellowCarTitle != null">
        yellow_car_title,
      </if>
      <if test="qualificationPackageId != null">
        qualification_package_id,
      </if>
      <if test="customizedImpUrl != null">
        customized_imp_url,
      </if>
      <if test="customizedClickUrl != null">
        customized_click_url,
      </if>
      <if test="managedVersion != null">
        managed_version,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="operatorName != null">
        #{operatorName,jdbcType=VARCHAR},
      </if>
      <if test="campaignId != null">
        #{campaignId,jdbcType=INTEGER},
      </if>
      <if test="ocpxTarget != null">
        #{ocpxTarget,jdbcType=TINYINT},
      </if>
      <if test="costPrice != null">
        #{costPrice,jdbcType=INTEGER},
      </if>
      <if test="isLongTermLaunch != null">
        #{isLongTermLaunch,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="batchNo != null">
        #{batchNo,jdbcType=INTEGER},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="env != null">
        #{env,jdbcType=VARCHAR},
      </if>
      <if test="launchType != null">
        #{launchType,jdbcType=TINYINT},
      </if>
      <if test="isUnitBudgetAverage != null">
        #{isUnitBudgetAverage,jdbcType=TINYINT},
      </if>
      <if test="creativeGenerateType != null">
        #{creativeGenerateType,jdbcType=TINYINT},
      </if>
      <if test="preferScene != null">
        #{preferScene,jdbcType=TINYINT},
      </if>
      <if test="specificScenes != null">
        #{specificScenes,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="isYellowCar != null">
        #{isYellowCar,jdbcType=TINYINT},
      </if>
      <if test="yellowCarTitle != null">
        #{yellowCarTitle,jdbcType=VARCHAR},
      </if>
      <if test="qualificationPackageId != null">
        #{qualificationPackageId,jdbcType=INTEGER},
      </if>
      <if test="customizedImpUrl != null">
        #{customizedImpUrl,jdbcType=VARCHAR},
      </if>
      <if test="customizedClickUrl != null">
        #{customizedClickUrl,jdbcType=VARCHAR},
      </if>
      <if test="managedVersion != null">
        #{managedVersion,jdbcType=TINYINT},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="accountId != null">
        account_id = values(account_id),
      </if>
      <if test="operatorName != null">
        operator_name = values(operator_name),
      </if>
      <if test="campaignId != null">
        campaign_id = values(campaign_id),
      </if>
      <if test="ocpxTarget != null">
        ocpx_target = values(ocpx_target),
      </if>
      <if test="costPrice != null">
        cost_price = values(cost_price),
      </if>
      <if test="isLongTermLaunch != null">
        is_long_term_launch = values(is_long_term_launch),
      </if>
      <if test="status != null">
        status = values(status),
      </if>
      <if test="batchNo != null">
        batch_no = values(batch_no),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="env != null">
        env = values(env),
      </if>
      <if test="launchType != null">
        launch_type = values(launch_type),
      </if>
      <if test="isUnitBudgetAverage != null">
        is_unit_budget_average = values(is_unit_budget_average),
      </if>
      <if test="creativeGenerateType != null">
        creative_generate_type = values(creative_generate_type),
      </if>
      <if test="preferScene != null">
        prefer_scene = values(prefer_scene),
      </if>
      <if test="specificScenes != null">
        specific_scenes = values(specific_scenes),
      </if>
      <if test="description != null">
        description = values(description),
      </if>
      <if test="isYellowCar != null">
        is_yellow_car = values(is_yellow_car),
      </if>
      <if test="yellowCarTitle != null">
        yellow_car_title = values(yellow_car_title),
      </if>
      <if test="qualificationPackageId != null">
        qualification_package_id = values(qualification_package_id),
      </if>
      <if test="customizedImpUrl != null">
        customized_imp_url = values(customized_imp_url),
      </if>
      <if test="customizedClickUrl != null">
        customized_click_url = values(customized_click_url),
      </if>
      <if test="managedVersion != null">
        managed_version = values(managed_version),
      </if>
    </trim>
  </insert>
</mapper>