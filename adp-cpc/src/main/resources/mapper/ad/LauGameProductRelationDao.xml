<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.adp.cpc.dao.ad.LauGameProductRelationDao">
    <resultMap id="BaseResultMap" type="com.bilibili.adp.cpc.po.ad.LauGameProductRelationPo">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Mon Apr 01 08:36:06 UTC 2024.
        -->
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="game_base_id" jdbcType="INTEGER" property="gameBaseId" />
        <result column="game_name" jdbcType="VARCHAR" property="gameName" />
        <result column="product_id" jdbcType="INTEGER" property="productId" />
        <result column="product_name" jdbcType="VARCHAR" property="productName" />
        <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
        <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
        <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
        <result column="operator" jdbcType="VARCHAR" property="operator" />
    </resultMap>
    <sql id="Example_Where_Clause">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Mon Apr 01 08:36:06 UTC 2024.
        -->
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Mon Apr 01 08:36:06 UTC 2024.
        -->
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Mon Apr 01 08:36:06 UTC 2024.
        -->
        `id`, `game_base_id`, `game_name`, `product_id`, `product_name`, `is_deleted`, `ctime`,
        `mtime`, `operator`
    </sql>
    <select id="selectByExample" parameterType="com.bilibili.adp.cpc.po.ad.LauGameProductRelationPoExample" resultMap="BaseResultMap">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Mon Apr 01 08:36:06 UTC 2024.
        -->
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List" />
        from lau_game_product_relation
        <if test="_parameter != null">
            <include refid="Example_Where_Clause" />
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        <if test="limit != null">
            <if test="offset != null">
                limit ${offset}, ${limit}
            </if>
            <if test="offset == null">
                limit ${limit}
            </if>
        </if>
    </select>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Mon Apr 01 08:36:06 UTC 2024.
        -->
        select
        <include refid="Base_Column_List" />
        from lau_game_product_relation
        where `id` = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Mon Apr 01 08:36:06 UTC 2024.
        -->
        delete from lau_game_product_relation
        where `id` = #{id,jdbcType=INTEGER}
    </delete>
    <delete id="deleteByExample" parameterType="com.bilibili.adp.cpc.po.ad.LauGameProductRelationPoExample">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Mon Apr 01 08:36:06 UTC 2024.
        -->
        delete from lau_game_product_relation
        <if test="_parameter != null">
            <include refid="Example_Where_Clause" />
        </if>
    </delete>
    <insert id="insert" parameterType="com.bilibili.adp.cpc.po.ad.LauGameProductRelationPo">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Mon Apr 01 08:36:06 UTC 2024.
        -->
        insert into lau_game_product_relation (`id`, `game_base_id`, `game_name`,
        `product_id`, `product_name`, `is_deleted`,
        `ctime`, `mtime`, `operator`
        )
        values (#{id,jdbcType=INTEGER}, #{gameBaseId,jdbcType=INTEGER}, #{gameName,jdbcType=VARCHAR},
        #{productId,jdbcType=INTEGER}, #{productName,jdbcType=VARCHAR}, #{isDeleted,jdbcType=TINYINT},
        #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{operator,jdbcType=VARCHAR}
        )
    </insert>
    <insert id="insertSelective" parameterType="com.bilibili.adp.cpc.po.ad.LauGameProductRelationPo">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Mon Apr 01 08:36:06 UTC 2024.
        -->
        insert into lau_game_product_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                `id`,
            </if>
            <if test="gameBaseId != null">
                `game_base_id`,
            </if>
            <if test="gameName != null">
                `game_name`,
            </if>
            <if test="productId != null">
                `product_id`,
            </if>
            <if test="productName != null">
                `product_name`,
            </if>
            <if test="isDeleted != null">
                `is_deleted`,
            </if>
            <if test="ctime != null">
                `ctime`,
            </if>
            <if test="mtime != null">
                `mtime`,
            </if>
            <if test="operator != null">
                `operator`,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=INTEGER},
            </if>
            <if test="gameBaseId != null">
                #{gameBaseId,jdbcType=INTEGER},
            </if>
            <if test="gameName != null">
                #{gameName,jdbcType=VARCHAR},
            </if>
            <if test="productId != null">
                #{productId,jdbcType=INTEGER},
            </if>
            <if test="productName != null">
                #{productName,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=TINYINT},
            </if>
            <if test="ctime != null">
                #{ctime,jdbcType=TIMESTAMP},
            </if>
            <if test="mtime != null">
                #{mtime,jdbcType=TIMESTAMP},
            </if>
            <if test="operator != null">
                #{operator,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <select id="countByExample" parameterType="com.bilibili.adp.cpc.po.ad.LauGameProductRelationPoExample" resultType="java.lang.Long">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Mon Apr 01 08:36:06 UTC 2024.
        -->
        select count(*) from lau_game_product_relation
        <if test="_parameter != null">
            <include refid="Example_Where_Clause" />
        </if>
    </select>
    <update id="updateByExampleSelective" parameterType="map">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Mon Apr 01 08:36:06 UTC 2024.
        -->
        update lau_game_product_relation
        <set>
            <if test="record.id != null">
                `id` = #{record.id,jdbcType=INTEGER},
            </if>
            <if test="record.gameBaseId != null">
                `game_base_id` = #{record.gameBaseId,jdbcType=INTEGER},
            </if>
            <if test="record.gameName != null">
                `game_name` = #{record.gameName,jdbcType=VARCHAR},
            </if>
            <if test="record.productId != null">
                `product_id` = #{record.productId,jdbcType=INTEGER},
            </if>
            <if test="record.productName != null">
                `product_name` = #{record.productName,jdbcType=VARCHAR},
            </if>
            <if test="record.isDeleted != null">
                `is_deleted` = #{record.isDeleted,jdbcType=TINYINT},
            </if>
            <if test="record.ctime != null">
                `ctime` = #{record.ctime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.mtime != null">
                `mtime` = #{record.mtime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.operator != null">
                `operator` = #{record.operator,jdbcType=VARCHAR},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause" />
        </if>
    </update>
    <update id="updateByExample" parameterType="map">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Mon Apr 01 08:36:06 UTC 2024.
        -->
        update lau_game_product_relation
        set `id` = #{record.id,jdbcType=INTEGER},
        `game_base_id` = #{record.gameBaseId,jdbcType=INTEGER},
        `game_name` = #{record.gameName,jdbcType=VARCHAR},
        `product_id` = #{record.productId,jdbcType=INTEGER},
        `product_name` = #{record.productName,jdbcType=VARCHAR},
        `is_deleted` = #{record.isDeleted,jdbcType=TINYINT},
        `ctime` = #{record.ctime,jdbcType=TIMESTAMP},
        `mtime` = #{record.mtime,jdbcType=TIMESTAMP},
        `operator` = #{record.operator,jdbcType=VARCHAR}
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause" />
        </if>
    </update>
    <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.adp.cpc.po.ad.LauGameProductRelationPo">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Mon Apr 01 08:36:06 UTC 2024.
        -->
        update lau_game_product_relation
        <set>
            <if test="gameBaseId != null">
                `game_base_id` = #{gameBaseId,jdbcType=INTEGER},
            </if>
            <if test="gameName != null">
                `game_name` = #{gameName,jdbcType=VARCHAR},
            </if>
            <if test="productId != null">
                `product_id` = #{productId,jdbcType=INTEGER},
            </if>
            <if test="productName != null">
                `product_name` = #{productName,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null">
                `is_deleted` = #{isDeleted,jdbcType=TINYINT},
            </if>
            <if test="ctime != null">
                `ctime` = #{ctime,jdbcType=TIMESTAMP},
            </if>
            <if test="mtime != null">
                `mtime` = #{mtime,jdbcType=TIMESTAMP},
            </if>
            <if test="operator != null">
                `operator` = #{operator,jdbcType=VARCHAR},
            </if>
        </set>
        where `id` = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.bilibili.adp.cpc.po.ad.LauGameProductRelationPo">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Mon Apr 01 08:36:06 UTC 2024.
        -->
        update lau_game_product_relation
        set `game_base_id` = #{gameBaseId,jdbcType=INTEGER},
        `game_name` = #{gameName,jdbcType=VARCHAR},
        `product_id` = #{productId,jdbcType=INTEGER},
        `product_name` = #{productName,jdbcType=VARCHAR},
        `is_deleted` = #{isDeleted,jdbcType=TINYINT},
        `ctime` = #{ctime,jdbcType=TIMESTAMP},
        `mtime` = #{mtime,jdbcType=TIMESTAMP},
        `operator` = #{operator,jdbcType=VARCHAR}
        where `id` = #{id,jdbcType=INTEGER}
    </update>
</mapper>