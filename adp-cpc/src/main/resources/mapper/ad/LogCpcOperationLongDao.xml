<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.adp.cpc.dao.ad.LogCpcOperationLongDao">
  <resultMap id="BaseResultMap" type="com.bilibili.adp.cpc.po.ad.LogCpcOperationLongPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="obj_id" jdbcType="BIGINT" property="objId" />
    <result column="table_name" jdbcType="VARCHAR" property="tableName" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="operator_username" jdbcType="VARCHAR" property="operatorUsername" />
    <result column="operator_type" jdbcType="TINYINT" property="operatorType" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="bilibili_username" jdbcType="VARCHAR" property="bilibiliUsername" />
    <result column="account_id" jdbcType="INTEGER" property="accountId" />
    <result column="system_type" jdbcType="TINYINT" property="systemType" />
    <result column="sales_type" jdbcType="TINYINT" property="salesType" />
    <result column="bid" jdbcType="BIGINT" property="bid" />
    <result column="app_key" jdbcType="VARCHAR" property="appKey" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.bilibili.adp.cpc.po.ad.LogCpcOperationLongPo">
    <result column="value" jdbcType="LONGVARCHAR" property="value" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, obj_id, table_name, type, operator_username, operator_type, ctime, mtime, is_deleted, 
    bilibili_username, account_id, system_type, sales_type, bid, app_key
  </sql>
  <sql id="Blob_Column_List">
    value
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.bilibili.adp.cpc.po.ad.LogCpcOperationLongPoExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from log_cpc_operation_long
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByExample" parameterType="com.bilibili.adp.cpc.po.ad.LogCpcOperationLongPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from log_cpc_operation_long
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from log_cpc_operation_long
    where id = #{id,jdbcType=BIGINT}
  </select>

  <select id="selectOldData"  parameterType="map" resultType="java.util.HashMap">
    select *
    from ${tableName}
    where ${idColumn}=#{id}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from log_cpc_operation_long
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.adp.cpc.po.ad.LogCpcOperationLongPoExample">
    delete from log_cpc_operation_long
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.adp.cpc.po.ad.LogCpcOperationLongPo">
    insert into log_cpc_operation_long (id, obj_id, table_name, 
      type, operator_username, operator_type, 
      ctime, mtime, is_deleted, 
      bilibili_username, account_id, system_type, 
      sales_type, bid, app_key, 
      value)
    values (#{id,jdbcType=BIGINT}, #{objId,jdbcType=BIGINT}, #{tableName,jdbcType=VARCHAR}, 
      #{type,jdbcType=TINYINT}, #{operatorUsername,jdbcType=VARCHAR}, #{operatorType,jdbcType=TINYINT}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=TINYINT}, 
      #{bilibiliUsername,jdbcType=VARCHAR}, #{accountId,jdbcType=INTEGER}, #{systemType,jdbcType=TINYINT}, 
      #{salesType,jdbcType=TINYINT}, #{bid,jdbcType=BIGINT}, #{appKey,jdbcType=VARCHAR}, 
      #{value,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.adp.cpc.po.ad.LogCpcOperationLongPo">
    insert into log_cpc_operation_long
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="objId != null">
        obj_id,
      </if>
      <if test="tableName != null">
        table_name,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="operatorUsername != null">
        operator_username,
      </if>
      <if test="operatorType != null">
        operator_type,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="bilibiliUsername != null">
        bilibili_username,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="systemType != null">
        system_type,
      </if>
      <if test="salesType != null">
        sales_type,
      </if>
      <if test="bid != null">
        bid,
      </if>
      <if test="appKey != null">
        app_key,
      </if>
      <if test="value != null">
        value,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="objId != null">
        #{objId,jdbcType=BIGINT},
      </if>
      <if test="tableName != null">
        #{tableName,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
      <if test="operatorUsername != null">
        #{operatorUsername,jdbcType=VARCHAR},
      </if>
      <if test="operatorType != null">
        #{operatorType,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="bilibiliUsername != null">
        #{bilibiliUsername,jdbcType=VARCHAR},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="systemType != null">
        #{systemType,jdbcType=TINYINT},
      </if>
      <if test="salesType != null">
        #{salesType,jdbcType=TINYINT},
      </if>
      <if test="bid != null">
        #{bid,jdbcType=BIGINT},
      </if>
      <if test="appKey != null">
        #{appKey,jdbcType=VARCHAR},
      </if>
      <if test="value != null">
        #{value,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.adp.cpc.po.ad.LogCpcOperationLongPoExample" resultType="java.lang.Long">
    select count(*) from log_cpc_operation_long
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update log_cpc_operation_long
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.objId != null">
        obj_id = #{record.objId,jdbcType=BIGINT},
      </if>
      <if test="record.tableName != null">
        table_name = #{record.tableName,jdbcType=VARCHAR},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=TINYINT},
      </if>
      <if test="record.operatorUsername != null">
        operator_username = #{record.operatorUsername,jdbcType=VARCHAR},
      </if>
      <if test="record.operatorType != null">
        operator_type = #{record.operatorType,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.bilibiliUsername != null">
        bilibili_username = #{record.bilibiliUsername,jdbcType=VARCHAR},
      </if>
      <if test="record.accountId != null">
        account_id = #{record.accountId,jdbcType=INTEGER},
      </if>
      <if test="record.systemType != null">
        system_type = #{record.systemType,jdbcType=TINYINT},
      </if>
      <if test="record.salesType != null">
        sales_type = #{record.salesType,jdbcType=TINYINT},
      </if>
      <if test="record.bid != null">
        bid = #{record.bid,jdbcType=BIGINT},
      </if>
      <if test="record.appKey != null">
        app_key = #{record.appKey,jdbcType=VARCHAR},
      </if>
      <if test="record.value != null">
        value = #{record.value,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update log_cpc_operation_long
    set id = #{record.id,jdbcType=BIGINT},
      obj_id = #{record.objId,jdbcType=BIGINT},
      table_name = #{record.tableName,jdbcType=VARCHAR},
      type = #{record.type,jdbcType=TINYINT},
      operator_username = #{record.operatorUsername,jdbcType=VARCHAR},
      operator_type = #{record.operatorType,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      bilibili_username = #{record.bilibiliUsername,jdbcType=VARCHAR},
      account_id = #{record.accountId,jdbcType=INTEGER},
      system_type = #{record.systemType,jdbcType=TINYINT},
      sales_type = #{record.salesType,jdbcType=TINYINT},
      bid = #{record.bid,jdbcType=BIGINT},
      app_key = #{record.appKey,jdbcType=VARCHAR},
      value = #{record.value,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update log_cpc_operation_long
    set id = #{record.id,jdbcType=BIGINT},
      obj_id = #{record.objId,jdbcType=BIGINT},
      table_name = #{record.tableName,jdbcType=VARCHAR},
      type = #{record.type,jdbcType=TINYINT},
      operator_username = #{record.operatorUsername,jdbcType=VARCHAR},
      operator_type = #{record.operatorType,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      bilibili_username = #{record.bilibiliUsername,jdbcType=VARCHAR},
      account_id = #{record.accountId,jdbcType=INTEGER},
      system_type = #{record.systemType,jdbcType=TINYINT},
      sales_type = #{record.salesType,jdbcType=TINYINT},
      bid = #{record.bid,jdbcType=BIGINT},
      app_key = #{record.appKey,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.adp.cpc.po.ad.LogCpcOperationLongPo">
    update log_cpc_operation_long
    <set>
      <if test="objId != null">
        obj_id = #{objId,jdbcType=BIGINT},
      </if>
      <if test="tableName != null">
        table_name = #{tableName,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=TINYINT},
      </if>
      <if test="operatorUsername != null">
        operator_username = #{operatorUsername,jdbcType=VARCHAR},
      </if>
      <if test="operatorType != null">
        operator_type = #{operatorType,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="bilibiliUsername != null">
        bilibili_username = #{bilibiliUsername,jdbcType=VARCHAR},
      </if>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=INTEGER},
      </if>
      <if test="systemType != null">
        system_type = #{systemType,jdbcType=TINYINT},
      </if>
      <if test="salesType != null">
        sales_type = #{salesType,jdbcType=TINYINT},
      </if>
      <if test="bid != null">
        bid = #{bid,jdbcType=BIGINT},
      </if>
      <if test="appKey != null">
        app_key = #{appKey,jdbcType=VARCHAR},
      </if>
      <if test="value != null">
        value = #{value,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.bilibili.adp.cpc.po.ad.LogCpcOperationLongPo">
    update log_cpc_operation_long
    set obj_id = #{objId,jdbcType=BIGINT},
      table_name = #{tableName,jdbcType=VARCHAR},
      type = #{type,jdbcType=TINYINT},
      operator_username = #{operatorUsername,jdbcType=VARCHAR},
      operator_type = #{operatorType,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      bilibili_username = #{bilibiliUsername,jdbcType=VARCHAR},
      account_id = #{accountId,jdbcType=INTEGER},
      system_type = #{systemType,jdbcType=TINYINT},
      sales_type = #{salesType,jdbcType=TINYINT},
      bid = #{bid,jdbcType=BIGINT},
      app_key = #{appKey,jdbcType=VARCHAR},
      value = #{value,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.adp.cpc.po.ad.LogCpcOperationLongPo">
    update log_cpc_operation_long
    set obj_id = #{objId,jdbcType=BIGINT},
      table_name = #{tableName,jdbcType=VARCHAR},
      type = #{type,jdbcType=TINYINT},
      operator_username = #{operatorUsername,jdbcType=VARCHAR},
      operator_type = #{operatorType,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      bilibili_username = #{bilibiliUsername,jdbcType=VARCHAR},
      account_id = #{accountId,jdbcType=INTEGER},
      system_type = #{systemType,jdbcType=TINYINT},
      sales_type = #{salesType,jdbcType=TINYINT},
      bid = #{bid,jdbcType=BIGINT},
      app_key = #{appKey,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.adp.cpc.po.ad.LogCpcOperationLongPo">
    insert into log_cpc_operation_long (id, obj_id, table_name, 
      type, operator_username, operator_type, 
      ctime, mtime, is_deleted, 
      bilibili_username, account_id, system_type, 
      sales_type, bid, app_key, 
      value)
    values (#{id,jdbcType=BIGINT}, #{objId,jdbcType=BIGINT}, #{tableName,jdbcType=VARCHAR}, 
      #{type,jdbcType=TINYINT}, #{operatorUsername,jdbcType=VARCHAR}, #{operatorType,jdbcType=TINYINT}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=TINYINT}, 
      #{bilibiliUsername,jdbcType=VARCHAR}, #{accountId,jdbcType=INTEGER}, #{systemType,jdbcType=TINYINT}, 
      #{salesType,jdbcType=TINYINT}, #{bid,jdbcType=BIGINT}, #{appKey,jdbcType=VARCHAR}, 
      #{value,jdbcType=LONGVARCHAR})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      obj_id = values(obj_id),
      table_name = values(table_name),
      type = values(type),
      operator_username = values(operator_username),
      operator_type = values(operator_type),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
      bilibili_username = values(bilibili_username),
      account_id = values(account_id),
      system_type = values(system_type),
      sales_type = values(sales_type),
      bid = values(bid),
      app_key = values(app_key),
      value = values(value),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      log_cpc_operation_long
      (obj_id,table_name,type,operator_username,operator_type,ctime,mtime,is_deleted,bilibili_username,account_id,system_type,sales_type,bid,app_key,value)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.objId,jdbcType=BIGINT},
        #{item.tableName,jdbcType=VARCHAR},
        #{item.type,jdbcType=TINYINT},
        #{item.operatorUsername,jdbcType=VARCHAR},
        #{item.operatorType,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.bilibiliUsername,jdbcType=VARCHAR},
        #{item.accountId,jdbcType=INTEGER},
        #{item.systemType,jdbcType=TINYINT},
        #{item.salesType,jdbcType=TINYINT},
        #{item.bid,jdbcType=BIGINT},
        #{item.appKey,jdbcType=VARCHAR},
        #{item.value,jdbcType=LONGVARCHAR},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      log_cpc_operation_long
      (obj_id,table_name,type,operator_username,operator_type,ctime,mtime,is_deleted,bilibili_username,account_id,system_type,sales_type,bid,app_key,value)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.objId,jdbcType=BIGINT},
        #{item.tableName,jdbcType=VARCHAR},
        #{item.type,jdbcType=TINYINT},
        #{item.operatorUsername,jdbcType=VARCHAR},
        #{item.operatorType,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.bilibiliUsername,jdbcType=VARCHAR},
        #{item.accountId,jdbcType=INTEGER},
        #{item.systemType,jdbcType=TINYINT},
        #{item.salesType,jdbcType=TINYINT},
        #{item.bid,jdbcType=BIGINT},
        #{item.appKey,jdbcType=VARCHAR},
        #{item.value,jdbcType=LONGVARCHAR},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      obj_id = values(obj_id),
      table_name = values(table_name),
      type = values(type),
      operator_username = values(operator_username),
      operator_type = values(operator_type),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
      bilibili_username = values(bilibili_username),
      account_id = values(account_id),
      system_type = values(system_type),
      sales_type = values(sales_type),
      bid = values(bid),
      app_key = values(app_key),
      value = values(value),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.adp.cpc.po.ad.LogCpcOperationLongPo">
    insert into log_cpc_operation_long
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="objId != null">
        obj_id,
      </if>
      <if test="tableName != null">
        table_name,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="operatorUsername != null">
        operator_username,
      </if>
      <if test="operatorType != null">
        operator_type,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="bilibiliUsername != null">
        bilibili_username,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="systemType != null">
        system_type,
      </if>
      <if test="salesType != null">
        sales_type,
      </if>
      <if test="bid != null">
        bid,
      </if>
      <if test="appKey != null">
        app_key,
      </if>
      <if test="value != null">
        value,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="objId != null">
        #{objId,jdbcType=BIGINT},
      </if>
      <if test="tableName != null">
        #{tableName,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
      <if test="operatorUsername != null">
        #{operatorUsername,jdbcType=VARCHAR},
      </if>
      <if test="operatorType != null">
        #{operatorType,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="bilibiliUsername != null">
        #{bilibiliUsername,jdbcType=VARCHAR},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="systemType != null">
        #{systemType,jdbcType=TINYINT},
      </if>
      <if test="salesType != null">
        #{salesType,jdbcType=TINYINT},
      </if>
      <if test="bid != null">
        #{bid,jdbcType=BIGINT},
      </if>
      <if test="appKey != null">
        #{appKey,jdbcType=VARCHAR},
      </if>
      <if test="value != null">
        #{value,jdbcType=LONGVARCHAR},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="objId != null">
        obj_id = values(obj_id),
      </if>
      <if test="tableName != null">
        table_name = values(table_name),
      </if>
      <if test="type != null">
        type = values(type),
      </if>
      <if test="operatorUsername != null">
        operator_username = values(operator_username),
      </if>
      <if test="operatorType != null">
        operator_type = values(operator_type),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="bilibiliUsername != null">
        bilibili_username = values(bilibili_username),
      </if>
      <if test="accountId != null">
        account_id = values(account_id),
      </if>
      <if test="systemType != null">
        system_type = values(system_type),
      </if>
      <if test="salesType != null">
        sales_type = values(sales_type),
      </if>
      <if test="bid != null">
        bid = values(bid),
      </if>
      <if test="appKey != null">
        app_key = values(app_key),
      </if>
      <if test="value != null">
        value = values(value),
      </if>
    </trim>
  </insert>
</mapper>