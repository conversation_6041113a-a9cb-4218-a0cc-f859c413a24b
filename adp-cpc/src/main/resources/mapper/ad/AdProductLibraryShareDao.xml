<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.adp.cpc.dao.ad.AdProductLibraryShareDao">
  <resultMap id="BaseResultMap" type="com.bilibili.adp.cpc.po.ad.AdProductLibrarySharePo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="library_id" jdbcType="BIGINT" property="libraryId" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="account_id" jdbcType="INTEGER" property="accountId" />
    <result column="biz_status" jdbcType="TINYINT" property="bizStatus" />
    <result column="belong_type" jdbcType="TINYINT" property="belongType" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, library_id, type, name, account_id, biz_status, belong_type, is_deleted, ctime, 
    mtime
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.adp.cpc.po.ad.AdProductLibrarySharePoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from ad_product_library_share
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ad_product_library_share
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from ad_product_library_share
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.adp.cpc.po.ad.AdProductLibrarySharePoExample">
    delete from ad_product_library_share
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.adp.cpc.po.ad.AdProductLibrarySharePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ad_product_library_share (library_id, type, name, 
      account_id, biz_status, belong_type, 
      is_deleted, ctime, mtime
      )
    values (#{libraryId,jdbcType=BIGINT}, #{type,jdbcType=INTEGER}, #{name,jdbcType=VARCHAR}, 
      #{accountId,jdbcType=INTEGER}, #{bizStatus,jdbcType=TINYINT}, #{belongType,jdbcType=TINYINT}, 
      #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.adp.cpc.po.ad.AdProductLibrarySharePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ad_product_library_share
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="libraryId != null">
        library_id,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="bizStatus != null">
        biz_status,
      </if>
      <if test="belongType != null">
        belong_type,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="libraryId != null">
        #{libraryId,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="bizStatus != null">
        #{bizStatus,jdbcType=TINYINT},
      </if>
      <if test="belongType != null">
        #{belongType,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.adp.cpc.po.ad.AdProductLibrarySharePoExample" resultType="java.lang.Long">
    select count(*) from ad_product_library_share
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update ad_product_library_share
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.libraryId != null">
        library_id = #{record.libraryId,jdbcType=BIGINT},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=INTEGER},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.accountId != null">
        account_id = #{record.accountId,jdbcType=INTEGER},
      </if>
      <if test="record.bizStatus != null">
        biz_status = #{record.bizStatus,jdbcType=TINYINT},
      </if>
      <if test="record.belongType != null">
        belong_type = #{record.belongType,jdbcType=TINYINT},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update ad_product_library_share
    set id = #{record.id,jdbcType=BIGINT},
      library_id = #{record.libraryId,jdbcType=BIGINT},
      type = #{record.type,jdbcType=INTEGER},
      name = #{record.name,jdbcType=VARCHAR},
      account_id = #{record.accountId,jdbcType=INTEGER},
      biz_status = #{record.bizStatus,jdbcType=TINYINT},
      belong_type = #{record.belongType,jdbcType=TINYINT},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.adp.cpc.po.ad.AdProductLibrarySharePo">
    update ad_product_library_share
    <set>
      <if test="libraryId != null">
        library_id = #{libraryId,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=INTEGER},
      </if>
      <if test="bizStatus != null">
        biz_status = #{bizStatus,jdbcType=TINYINT},
      </if>
      <if test="belongType != null">
        belong_type = #{belongType,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.adp.cpc.po.ad.AdProductLibrarySharePo">
    update ad_product_library_share
    set library_id = #{libraryId,jdbcType=BIGINT},
      type = #{type,jdbcType=INTEGER},
      name = #{name,jdbcType=VARCHAR},
      account_id = #{accountId,jdbcType=INTEGER},
      biz_status = #{bizStatus,jdbcType=TINYINT},
      belong_type = #{belongType,jdbcType=TINYINT},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.adp.cpc.po.ad.AdProductLibrarySharePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ad_product_library_share (library_id, type, name, 
      account_id, biz_status, belong_type, 
      is_deleted, ctime, mtime
      )
    values (#{libraryId,jdbcType=BIGINT}, #{type,jdbcType=INTEGER}, #{name,jdbcType=VARCHAR}, 
      #{accountId,jdbcType=INTEGER}, #{bizStatus,jdbcType=TINYINT}, #{belongType,jdbcType=TINYINT}, 
      #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}
      )
    <trim prefix="on duplicate key update" suffixOverrides=",">
      library_id = values(library_id),
      type = values(type),
      name = values(name),
      account_id = values(account_id),
      biz_status = values(biz_status),
      belong_type = values(belong_type),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      ad_product_library_share
      (library_id,type,name,account_id,biz_status,belong_type,is_deleted,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.libraryId,jdbcType=BIGINT},
        #{item.type,jdbcType=INTEGER},
        #{item.name,jdbcType=VARCHAR},
        #{item.accountId,jdbcType=INTEGER},
        #{item.bizStatus,jdbcType=TINYINT},
        #{item.belongType,jdbcType=TINYINT},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      ad_product_library_share
      (library_id,type,name,account_id,biz_status,belong_type,is_deleted,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.libraryId,jdbcType=BIGINT},
        #{item.type,jdbcType=INTEGER},
        #{item.name,jdbcType=VARCHAR},
        #{item.accountId,jdbcType=INTEGER},
        #{item.bizStatus,jdbcType=TINYINT},
        #{item.belongType,jdbcType=TINYINT},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      library_id = values(library_id),
      type = values(type),
      name = values(name),
      account_id = values(account_id),
      biz_status = values(biz_status),
      belong_type = values(belong_type),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.adp.cpc.po.ad.AdProductLibrarySharePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ad_product_library_share
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="libraryId != null">
        library_id,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="bizStatus != null">
        biz_status,
      </if>
      <if test="belongType != null">
        belong_type,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="libraryId != null">
        #{libraryId,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="bizStatus != null">
        #{bizStatus,jdbcType=TINYINT},
      </if>
      <if test="belongType != null">
        #{belongType,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="libraryId != null">
        library_id = values(library_id),
      </if>
      <if test="type != null">
        type = values(type),
      </if>
      <if test="name != null">
        name = values(name),
      </if>
      <if test="accountId != null">
        account_id = values(account_id),
      </if>
      <if test="bizStatus != null">
        biz_status = values(biz_status),
      </if>
      <if test="belongType != null">
        belong_type = values(belong_type),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
    </trim>
  </insert>
</mapper>