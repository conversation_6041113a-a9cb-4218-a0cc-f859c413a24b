<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.adp.cpc.dao.ad.LauDeductionInfoDao">
  <resultMap id="BaseResultMap" type="com.bilibili.adp.cpc.po.ad.LauDeductionInfoPo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="business_type" jdbcType="TINYINT" property="businessType" />
    <result column="group_time" jdbcType="TIMESTAMP" property="groupTime" />
    <result column="account_id" jdbcType="INTEGER" property="accountId" />
    <result column="crm_serial_number" jdbcType="VARCHAR" property="crmSerialNumber" />
    <result column="cost" jdbcType="DECIMAL" property="cost" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, business_id, business_type, group_time, account_id, crm_serial_number, cost, 
    status, ctime, mtime
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.adp.cpc.po.ad.LauDeductionInfoPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from lau_deduction_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from lau_deduction_info
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from lau_deduction_info
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.adp.cpc.po.ad.LauDeductionInfoPoExample">
    delete from lau_deduction_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.adp.cpc.po.ad.LauDeductionInfoPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_deduction_info (business_id, business_type, group_time, 
      account_id, crm_serial_number, cost, 
      status, ctime, mtime
      )
    values (#{businessId,jdbcType=INTEGER}, #{businessType,jdbcType=TINYINT}, #{groupTime,jdbcType=TIMESTAMP}, 
      #{accountId,jdbcType=INTEGER}, #{crmSerialNumber,jdbcType=VARCHAR}, #{cost,jdbcType=DECIMAL}, 
      #{status,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.adp.cpc.po.ad.LauDeductionInfoPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_deduction_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        business_id,
      </if>
      <if test="businessType != null">
        business_type,
      </if>
      <if test="groupTime != null">
        group_time,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="crmSerialNumber != null">
        crm_serial_number,
      </if>
      <if test="cost != null">
        cost,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="businessType != null">
        #{businessType,jdbcType=TINYINT},
      </if>
      <if test="groupTime != null">
        #{groupTime,jdbcType=TIMESTAMP},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="crmSerialNumber != null">
        #{crmSerialNumber,jdbcType=VARCHAR},
      </if>
      <if test="cost != null">
        #{cost,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.adp.cpc.po.ad.LauDeductionInfoPoExample" resultType="java.lang.Long">
    select count(*) from lau_deduction_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update lau_deduction_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.businessId != null">
        business_id = #{record.businessId,jdbcType=INTEGER},
      </if>
      <if test="record.businessType != null">
        business_type = #{record.businessType,jdbcType=TINYINT},
      </if>
      <if test="record.groupTime != null">
        group_time = #{record.groupTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.accountId != null">
        account_id = #{record.accountId,jdbcType=INTEGER},
      </if>
      <if test="record.crmSerialNumber != null">
        crm_serial_number = #{record.crmSerialNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.cost != null">
        cost = #{record.cost,jdbcType=DECIMAL},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update lau_deduction_info
    set id = #{record.id,jdbcType=INTEGER},
      business_id = #{record.businessId,jdbcType=INTEGER},
      business_type = #{record.businessType,jdbcType=TINYINT},
      group_time = #{record.groupTime,jdbcType=TIMESTAMP},
      account_id = #{record.accountId,jdbcType=INTEGER},
      crm_serial_number = #{record.crmSerialNumber,jdbcType=VARCHAR},
      cost = #{record.cost,jdbcType=DECIMAL},
      status = #{record.status,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.adp.cpc.po.ad.LauDeductionInfoPo">
    update lau_deduction_info
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="businessType != null">
        business_type = #{businessType,jdbcType=TINYINT},
      </if>
      <if test="groupTime != null">
        group_time = #{groupTime,jdbcType=TIMESTAMP},
      </if>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=INTEGER},
      </if>
      <if test="crmSerialNumber != null">
        crm_serial_number = #{crmSerialNumber,jdbcType=VARCHAR},
      </if>
      <if test="cost != null">
        cost = #{cost,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.adp.cpc.po.ad.LauDeductionInfoPo">
    update lau_deduction_info
    set business_id = #{businessId,jdbcType=INTEGER},
      business_type = #{businessType,jdbcType=TINYINT},
      group_time = #{groupTime,jdbcType=TIMESTAMP},
      account_id = #{accountId,jdbcType=INTEGER},
      crm_serial_number = #{crmSerialNumber,jdbcType=VARCHAR},
      cost = #{cost,jdbcType=DECIMAL},
      status = #{status,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.adp.cpc.po.ad.LauDeductionInfoPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_deduction_info (business_id, business_type, group_time, 
      account_id, crm_serial_number, cost, 
      status, ctime, mtime
      )
    values (#{businessId,jdbcType=INTEGER}, #{businessType,jdbcType=TINYINT}, #{groupTime,jdbcType=TIMESTAMP}, 
      #{accountId,jdbcType=INTEGER}, #{crmSerialNumber,jdbcType=VARCHAR}, #{cost,jdbcType=DECIMAL}, 
      #{status,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}
      )
    <trim prefix="on duplicate key update" suffixOverrides=",">
      business_id = values(business_id),
      business_type = values(business_type),
      group_time = values(group_time),
      account_id = values(account_id),
      crm_serial_number = values(crm_serial_number),
      cost = values(cost),
      status = values(status),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      lau_deduction_info
      (business_id,business_type,group_time,account_id,crm_serial_number,cost,status,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.businessId,jdbcType=INTEGER},
        #{item.businessType,jdbcType=TINYINT},
        #{item.groupTime,jdbcType=TIMESTAMP},
        #{item.accountId,jdbcType=INTEGER},
        #{item.crmSerialNumber,jdbcType=VARCHAR},
        #{item.cost,jdbcType=DECIMAL},
        #{item.status,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      lau_deduction_info
      (business_id,business_type,group_time,account_id,crm_serial_number,cost,status,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.businessId,jdbcType=INTEGER},
        #{item.businessType,jdbcType=TINYINT},
        #{item.groupTime,jdbcType=TIMESTAMP},
        #{item.accountId,jdbcType=INTEGER},
        #{item.crmSerialNumber,jdbcType=VARCHAR},
        #{item.cost,jdbcType=DECIMAL},
        #{item.status,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      business_id = values(business_id),
      business_type = values(business_type),
      group_time = values(group_time),
      account_id = values(account_id),
      crm_serial_number = values(crm_serial_number),
      cost = values(cost),
      status = values(status),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.adp.cpc.po.ad.LauDeductionInfoPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_deduction_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        business_id,
      </if>
      <if test="businessType != null">
        business_type,
      </if>
      <if test="groupTime != null">
        group_time,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="crmSerialNumber != null">
        crm_serial_number,
      </if>
      <if test="cost != null">
        cost,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="businessType != null">
        #{businessType,jdbcType=TINYINT},
      </if>
      <if test="groupTime != null">
        #{groupTime,jdbcType=TIMESTAMP},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="crmSerialNumber != null">
        #{crmSerialNumber,jdbcType=VARCHAR},
      </if>
      <if test="cost != null">
        #{cost,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="businessId != null">
        business_id = values(business_id),
      </if>
      <if test="businessType != null">
        business_type = values(business_type),
      </if>
      <if test="groupTime != null">
        group_time = values(group_time),
      </if>
      <if test="accountId != null">
        account_id = values(account_id),
      </if>
      <if test="crmSerialNumber != null">
        crm_serial_number = values(crm_serial_number),
      </if>
      <if test="cost != null">
        cost = values(cost),
      </if>
      <if test="status != null">
        status = values(status),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
    </trim>
  </insert>
</mapper>