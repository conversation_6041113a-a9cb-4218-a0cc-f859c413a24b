<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.adp.cpc.dao.ad.AccCouponUseQueueItemDao">
  <resultMap id="BaseResultMap" type="com.bilibili.adp.cpc.po.ad.AccCouponUseQueueItemPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="account_id" jdbcType="INTEGER" property="accountId" />
    <result column="queue_id" jdbcType="BIGINT" property="queueId" />
    <result column="coupon_id" jdbcType="BIGINT" property="couponId" />
    <result column="biz_status" jdbcType="INTEGER" property="bizStatus" />
    <result column="biz_sort" jdbcType="INTEGER" property="bizSort" />
    <result column="use_time" jdbcType="TIMESTAMP" property="useTime" />
    <result column="remove_time" jdbcType="TIMESTAMP" property="removeTime" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="bilibili_username" jdbcType="VARCHAR" property="bilibiliUsername" />
    <result column="parent_coupon_id" jdbcType="BIGINT" property="parentCouponId" />
    <result column="extra" jdbcType="VARCHAR" property="extra" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, account_id, queue_id, coupon_id, biz_status, biz_sort, use_time, remove_time, 
    is_deleted, remark, ctime, mtime, bilibili_username, parent_coupon_id, extra
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.adp.cpc.po.ad.AccCouponUseQueueItemPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from acc_coupon_use_queue_item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from acc_coupon_use_queue_item
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from acc_coupon_use_queue_item
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.adp.cpc.po.ad.AccCouponUseQueueItemPoExample">
    delete from acc_coupon_use_queue_item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.adp.cpc.po.ad.AccCouponUseQueueItemPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into acc_coupon_use_queue_item (account_id, queue_id, coupon_id, 
      biz_status, biz_sort, use_time, 
      remove_time, is_deleted, remark, 
      ctime, mtime, bilibili_username,
      parent_coupon_id, extra)
    values (#{accountId,jdbcType=INTEGER}, #{queueId,jdbcType=BIGINT}, #{couponId,jdbcType=BIGINT}, 
      #{bizStatus,jdbcType=INTEGER}, #{bizSort,jdbcType=INTEGER}, #{useTime,jdbcType=TIMESTAMP}, 
      #{removeTime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=TINYINT}, #{remark,jdbcType=VARCHAR}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{bilibiliUsername,jdbcType=VARCHAR},
      #{parentCouponId,jdbcType=BIGINT}, #{extra,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.adp.cpc.po.ad.AccCouponUseQueueItemPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into acc_coupon_use_queue_item
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        account_id,
      </if>
      <if test="queueId != null">
        queue_id,
      </if>
      <if test="couponId != null">
        coupon_id,
      </if>
      <if test="bizStatus != null">
        biz_status,
      </if>
      <if test="bizSort != null">
        biz_sort,
      </if>
      <if test="useTime != null">
        use_time,
      </if>
      <if test="removeTime != null">
        remove_time,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="bilibiliUsername != null">
        bilibili_username,
      </if>
      <if test="parentCouponId != null">
        parent_coupon_id,
      </if>
      <if test="extra != null">
        extra,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="queueId != null">
        #{queueId,jdbcType=BIGINT},
      </if>
      <if test="couponId != null">
        #{couponId,jdbcType=BIGINT},
      </if>
      <if test="bizStatus != null">
        #{bizStatus,jdbcType=INTEGER},
      </if>
      <if test="bizSort != null">
        #{bizSort,jdbcType=INTEGER},
      </if>
      <if test="useTime != null">
        #{useTime,jdbcType=TIMESTAMP},
      </if>
      <if test="removeTime != null">
        #{removeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="bilibiliUsername != null">
        #{bilibiliUsername,jdbcType=VARCHAR},
      </if>
      <if test="parentCouponId != null">
        #{parentCouponId,jdbcType=BIGINT},
      </if>
      <if test="extra != null">
        #{extra,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.adp.cpc.po.ad.AccCouponUseQueueItemPoExample" resultType="java.lang.Long">
    select count(*) from acc_coupon_use_queue_item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update acc_coupon_use_queue_item
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.accountId != null">
        account_id = #{record.accountId,jdbcType=INTEGER},
      </if>
      <if test="record.queueId != null">
        queue_id = #{record.queueId,jdbcType=BIGINT},
      </if>
      <if test="record.couponId != null">
        coupon_id = #{record.couponId,jdbcType=BIGINT},
      </if>
      <if test="record.bizStatus != null">
        biz_status = #{record.bizStatus,jdbcType=INTEGER},
      </if>
      <if test="record.bizSort != null">
        biz_sort = #{record.bizSort,jdbcType=INTEGER},
      </if>
      <if test="record.useTime != null">
        use_time = #{record.useTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.removeTime != null">
        remove_time = #{record.removeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.bilibiliUsername != null">
        bilibili_username = #{record.bilibiliUsername,jdbcType=VARCHAR},
      </if>
      <if test="record.parentCouponId != null">
        parent_coupon_id = #{record.parentCouponId,jdbcType=BIGINT},
      </if>
      <if test="record.extra != null">
        extra = #{record.extra,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update acc_coupon_use_queue_item
    set id = #{record.id,jdbcType=BIGINT},
      account_id = #{record.accountId,jdbcType=INTEGER},
      queue_id = #{record.queueId,jdbcType=BIGINT},
      coupon_id = #{record.couponId,jdbcType=BIGINT},
      biz_status = #{record.bizStatus,jdbcType=INTEGER},
      biz_sort = #{record.bizSort,jdbcType=INTEGER},
      use_time = #{record.useTime,jdbcType=TIMESTAMP},
      remove_time = #{record.removeTime,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      remark = #{record.remark,jdbcType=VARCHAR},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      bilibili_username = #{record.bilibiliUsername,jdbcType=VARCHAR},
      parent_coupon_id = #{record.parentCouponId,jdbcType=BIGINT},
      extra = #{record.extra,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.adp.cpc.po.ad.AccCouponUseQueueItemPo">
    update acc_coupon_use_queue_item
    <set>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=INTEGER},
      </if>
      <if test="queueId != null">
        queue_id = #{queueId,jdbcType=BIGINT},
      </if>
      <if test="couponId != null">
        coupon_id = #{couponId,jdbcType=BIGINT},
      </if>
      <if test="bizStatus != null">
        biz_status = #{bizStatus,jdbcType=INTEGER},
      </if>
      <if test="bizSort != null">
        biz_sort = #{bizSort,jdbcType=INTEGER},
      </if>
      <if test="useTime != null">
        use_time = #{useTime,jdbcType=TIMESTAMP},
      </if>
      <if test="removeTime != null">
        remove_time = #{removeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="bilibiliUsername != null">
        bilibili_username = #{bilibiliUsername,jdbcType=VARCHAR},
      </if>
      <if test="parentCouponId != null">
        parent_coupon_id = #{parentCouponId,jdbcType=BIGINT},
      </if>
      <if test="extra != null">
        extra = #{extra,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.adp.cpc.po.ad.AccCouponUseQueueItemPo">
    update acc_coupon_use_queue_item
    set account_id = #{accountId,jdbcType=INTEGER},
      queue_id = #{queueId,jdbcType=BIGINT},
      coupon_id = #{couponId,jdbcType=BIGINT},
      biz_status = #{bizStatus,jdbcType=INTEGER},
      biz_sort = #{bizSort,jdbcType=INTEGER},
      use_time = #{useTime,jdbcType=TIMESTAMP},
      remove_time = #{removeTime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      remark = #{remark,jdbcType=VARCHAR},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      bilibili_username = #{bilibiliUsername,jdbcType=VARCHAR},
      parent_coupon_id = #{parentCouponId,jdbcType=BIGINT},
      extra = #{extra,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.adp.cpc.po.ad.AccCouponUseQueueItemPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into acc_coupon_use_queue_item (account_id, queue_id, coupon_id, 
      biz_status, biz_sort, use_time, 
      remove_time, is_deleted, remark, 
      ctime, mtime, bilibili_username,
      parent_coupon_id, extra)
    values (#{accountId,jdbcType=INTEGER}, #{queueId,jdbcType=BIGINT}, #{couponId,jdbcType=BIGINT}, 
      #{bizStatus,jdbcType=INTEGER}, #{bizSort,jdbcType=INTEGER}, #{useTime,jdbcType=TIMESTAMP}, 
      #{removeTime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=TINYINT}, #{remark,jdbcType=VARCHAR}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{bilibiliUsername,jdbcType=VARCHAR},
      #{parentCouponId,jdbcType=BIGINT}, #{extra,jdbcType=VARCHAR})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      account_id = values(account_id),
      queue_id = values(queue_id),
      coupon_id = values(coupon_id),
      biz_status = values(biz_status),
      biz_sort = values(biz_sort),
      use_time = values(use_time),
      remove_time = values(remove_time),
      is_deleted = values(is_deleted),
      remark = values(remark),
      ctime = values(ctime),
      mtime = values(mtime),
      bilibili_username = values(bilibili_username),
      parent_coupon_id = values(parent_coupon_id),
      extra = values(extra),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      acc_coupon_use_queue_item
      (account_id,queue_id,coupon_id,biz_status,biz_sort,use_time,remove_time,is_deleted,remark,ctime,mtime,bilibili_username,parent_coupon_id,extra)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.accountId,jdbcType=INTEGER},
        #{item.queueId,jdbcType=BIGINT},
        #{item.couponId,jdbcType=BIGINT},
        #{item.bizStatus,jdbcType=INTEGER},
        #{item.bizSort,jdbcType=INTEGER},
        #{item.useTime,jdbcType=TIMESTAMP},
        #{item.removeTime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.remark,jdbcType=VARCHAR},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.bilibiliUsername,jdbcType=VARCHAR},
        #{item.parentCouponId,jdbcType=BIGINT},
        #{item.extra,jdbcType=VARCHAR},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      acc_coupon_use_queue_item
      (account_id,queue_id,coupon_id,biz_status,biz_sort,use_time,remove_time,is_deleted,remark,ctime,mtime,bilibili_username,parent_coupon_id,extra)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.accountId,jdbcType=INTEGER},
        #{item.queueId,jdbcType=BIGINT},
        #{item.couponId,jdbcType=BIGINT},
        #{item.bizStatus,jdbcType=INTEGER},
        #{item.bizSort,jdbcType=INTEGER},
        #{item.useTime,jdbcType=TIMESTAMP},
        #{item.removeTime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.remark,jdbcType=VARCHAR},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.bilibiliUsername,jdbcType=VARCHAR},
        #{item.parentCouponId,jdbcType=BIGINT},
        #{item.extra,jdbcType=VARCHAR},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      account_id = values(account_id),
      queue_id = values(queue_id),
      coupon_id = values(coupon_id),
      biz_status = values(biz_status),
      biz_sort = values(biz_sort),
      use_time = values(use_time),
      remove_time = values(remove_time),
      is_deleted = values(is_deleted),
      remark = values(remark),
      ctime = values(ctime),
      mtime = values(mtime),
      bilibili_username = values(bilibili_username),
      parent_coupon_id = values(parent_coupon_id),
      extra = values(extra),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.adp.cpc.po.ad.AccCouponUseQueueItemPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into acc_coupon_use_queue_item
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        account_id,
      </if>
      <if test="queueId != null">
        queue_id,
      </if>
      <if test="couponId != null">
        coupon_id,
      </if>
      <if test="bizStatus != null">
        biz_status,
      </if>
      <if test="bizSort != null">
        biz_sort,
      </if>
      <if test="useTime != null">
        use_time,
      </if>
      <if test="removeTime != null">
        remove_time,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="bilibiliUsername != null">
        bilibili_username,
      </if>
      <if test="parentCouponId != null">
        parent_coupon_id,
      </if>
      <if test="extra != null">
        extra,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="queueId != null">
        #{queueId,jdbcType=BIGINT},
      </if>
      <if test="couponId != null">
        #{couponId,jdbcType=BIGINT},
      </if>
      <if test="bizStatus != null">
        #{bizStatus,jdbcType=INTEGER},
      </if>
      <if test="bizSort != null">
        #{bizSort,jdbcType=INTEGER},
      </if>
      <if test="useTime != null">
        #{useTime,jdbcType=TIMESTAMP},
      </if>
      <if test="removeTime != null">
        #{removeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="bilibiliUsername != null">
        #{bilibiliUsername,jdbcType=VARCHAR},
      </if>
      <if test="parentCouponId != null">
        #{parentCouponId,jdbcType=BIGINT},
      </if>
      <if test="extra != null">
        #{extra,jdbcType=VARCHAR},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="accountId != null">
        account_id = values(account_id),
      </if>
      <if test="queueId != null">
        queue_id = values(queue_id),
      </if>
      <if test="couponId != null">
        coupon_id = values(coupon_id),
      </if>
      <if test="bizStatus != null">
        biz_status = values(biz_status),
      </if>
      <if test="bizSort != null">
        biz_sort = values(biz_sort),
      </if>
      <if test="useTime != null">
        use_time = values(use_time),
      </if>
      <if test="removeTime != null">
        remove_time = values(remove_time),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="remark != null">
        remark = values(remark),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="bilibiliUsername != null">
        bilibili_username = values(bilibili_username),
      </if>
      <if test="parentCouponId != null">
        parent_coupon_id = values(parent_coupon_id),
      </if>
      <if test="extra != null">
        extra = values(extra),
      </if>
    </trim>
  </insert>
</mapper>