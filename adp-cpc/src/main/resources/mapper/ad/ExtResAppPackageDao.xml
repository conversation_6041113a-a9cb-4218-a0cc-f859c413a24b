<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.adp.cpc.dao.ad.ExtResAppPackageDao">
  <resultMap id="BaseResultMap" type="com.bilibili.adp.resource.biz.pojo.ResAppPackagePo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="account_id" jdbcType="INTEGER" property="accountId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="url" jdbcType="VARCHAR" property="url" />
    <result column="package_name" jdbcType="VARCHAR" property="packageName" />
    <result column="app_name" jdbcType="VARCHAR" property="appName" />
    <result column="platform" jdbcType="TINYINT" property="platform" />
    <result column="version" jdbcType="VARCHAR" property="version" />
    <result column="size" jdbcType="INTEGER" property="size" />
    <result column="md5" jdbcType="VARCHAR" property="md5" />
    <result column="icon_url" jdbcType="VARCHAR" property="iconUrl" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="internal_url" jdbcType="VARCHAR" property="internalUrl" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="platform_status" jdbcType="TINYINT" property="platformStatus" />
    <result column="developer_name" jdbcType="VARCHAR" property="developerName" />
    <result column="authority_url" jdbcType="VARCHAR" property="authorityUrl" />
    <result column="auth_code_list" jdbcType="VARCHAR" property="authCodeList" />
    <result column="apk_update_time" jdbcType="TIMESTAMP" property="apkUpdateTime" />
    <result column="privacy_policy" jdbcType="VARCHAR" property="privacyPolicy" />
    <result column="dmp_app_id" jdbcType="INTEGER" property="dmpAppId" />
    <result column="is_new_fly" jdbcType="TINYINT" property="isNewFly" />
    <result column="sub_title" jdbcType="VARCHAR" property="subTitle" />
    <result column="is_icon_valid" jdbcType="TINYINT" property="isIconValid" />
  </resultMap>
  <resultMap id="AliasBaseResultMap" type="com.bilibili.adp.resource.biz.pojo.ResAppPackagePo">
    <id column="res_app_package_id" jdbcType="INTEGER" property="id" />
    <result column="res_app_package_account_id" jdbcType="INTEGER" property="accountId" />
    <result column="res_app_package_name" jdbcType="VARCHAR" property="name" />
    <result column="res_app_package_url" jdbcType="VARCHAR" property="url" />
    <result column="res_app_package_package_name" jdbcType="VARCHAR" property="packageName" />
    <result column="res_app_package_app_name" jdbcType="VARCHAR" property="appName" />
    <result column="res_app_package_platform" jdbcType="TINYINT" property="platform" />
    <result column="res_app_package_version" jdbcType="VARCHAR" property="version" />
    <result column="res_app_package_size" jdbcType="INTEGER" property="size" />
    <result column="res_app_package_md5" jdbcType="VARCHAR" property="md5" />
    <result column="res_app_package_icon_url" jdbcType="VARCHAR" property="iconUrl" />
    <result column="res_app_package_ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="res_app_package_mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="res_app_package_is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="res_app_package_internal_url" jdbcType="VARCHAR" property="internalUrl" />
    <result column="res_app_package_status" jdbcType="TINYINT" property="status" />
    <result column="res_app_package_platform_status" jdbcType="TINYINT" property="platformStatus" />
    <result column="res_app_package_developer_name" jdbcType="VARCHAR" property="developerName" />
    <result column="res_app_package_authority_url" jdbcType="VARCHAR" property="authorityUrl" />
    <result column="res_app_package_auth_code_list" jdbcType="VARCHAR" property="authCodeList" />
    <result column="res_app_package_apk_update_time" jdbcType="TIMESTAMP" property="apkUpdateTime" />
    <result column="res_app_package_privacy_policy" jdbcType="VARCHAR" property="privacyPolicy" />
    <result column="res_app_package_dmp_app_id" jdbcType="INTEGER" property="dmpAppId" />
    <result column="res_app_package_is_new_fly" jdbcType="TINYINT" property="isNewFly" />
    <result column="res_app_package_sub_title" jdbcType="VARCHAR" property="subTitle" />
    <result column="res_app_package_is_icon_valid" jdbcType="TINYINT" property="isIconValid" />
  </resultMap>
  <sql id="Alias_Base_Column_List">
    ${alias}.id as res_app_package_id, ${alias}.account_id as res_app_package_account_id,
    ${alias}.name as res_app_package_name, ${alias}.url as res_app_package_url, ${alias}.package_name as res_app_package_package_name,
    ${alias}.app_name as res_app_package_app_name, ${alias}.platform as res_app_package_platform,
    ${alias}.version as res_app_package_version, ${alias}.size as res_app_package_size,
    ${alias}.md5 as res_app_package_md5, ${alias}.icon_url as res_app_package_icon_url,
    ${alias}.ctime as res_app_package_ctime, ${alias}.mtime as res_app_package_mtime,
    ${alias}.is_deleted as res_app_package_is_deleted, ${alias}.internal_url as res_app_package_internal_url,
    ${alias}.status as res_app_package_status, ${alias}.platform_status as res_app_package_platform_status,
    ${alias}.developer_name as res_app_package_developer_name, ${alias}.authority_url as res_app_package_authority_url,
    ${alias}.auth_code_list as res_app_package_auth_code_list, ${alias}.apk_update_time as res_app_package_apk_update_time,
    ${alias}.privacy_policy as res_app_package_privacy_policy, ${alias}.dmp_app_id as res_app_package_dmp_app_id,
    ${alias}.is_new_fly as res_app_package_is_new_fly, ${alias}.sub_title as res_app_package_sub_title,
    ${alias}.is_icon_valid as res_app_package_is_icon_valid
  </sql>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.bilibili.adp.resource.biz.pojo.ResAppPackagePo">
    <result column="description" jdbcType="LONGVARCHAR" property="description" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>

  <select id="queryMaxIdByExample" parameterType="com.bilibili.adp.resource.biz.pojo.ResAppPackagePoExample"
          resultType="com.bilibili.report.platform.api.dto.MinAndMaxId">
    select
        min(id) minId, max(id) maxId
    from res_app_package
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
</mapper>