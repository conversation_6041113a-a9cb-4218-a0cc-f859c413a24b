<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.adp.cpc.dao.ad_data_write.LauUnitCreativeCoverClusteringDao">
  <resultMap id="BaseResultMap" type="com.bilibili.adp.cpc.po.ad_data_write.LauUnitCreativeCoverClusteringPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="image_md5" jdbcType="VARCHAR" property="imageMd5" />
    <result column="cluster_id" jdbcType="BIGINT" property="clusterId" />
    <result column="image_url" jdbcType="VARCHAR" property="imageUrl" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="image_id" jdbcType="BIGINT" property="imageId" />
    <result column="material_id" jdbcType="BIGINT" property="materialId" />
  </resultMap>
  <resultMap id="AliasBaseResultMap" type="com.bilibili.adp.cpc.po.ad_data_write.LauUnitCreativeCoverClusteringPo">
    <id column="lau_unit_creative_cover_clustering_id" jdbcType="BIGINT" property="id" />
    <result column="lau_unit_creative_cover_clustering_image_md5" jdbcType="VARCHAR" property="imageMd5" />
    <result column="lau_unit_creative_cover_clustering_cluster_id" jdbcType="BIGINT" property="clusterId" />
    <result column="lau_unit_creative_cover_clustering_image_url" jdbcType="VARCHAR" property="imageUrl" />
    <result column="lau_unit_creative_cover_clustering_ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="lau_unit_creative_cover_clustering_mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="lau_unit_creative_cover_clustering_image_id" jdbcType="BIGINT" property="imageId" />
    <result column="lau_unit_creative_cover_clustering_material_id" jdbcType="BIGINT" property="materialId" />
  </resultMap>
  <sql id="Alias_Base_Column_List">
    ${alias}.id as lau_unit_creative_cover_clustering_id, ${alias}.image_md5 as lau_unit_creative_cover_clustering_image_md5, 
    ${alias}.cluster_id as lau_unit_creative_cover_clustering_cluster_id, ${alias}.image_url as lau_unit_creative_cover_clustering_image_url, 
    ${alias}.ctime as lau_unit_creative_cover_clustering_ctime, ${alias}.mtime as lau_unit_creative_cover_clustering_mtime, 
    ${alias}.image_id as lau_unit_creative_cover_clustering_image_id, ${alias}.material_id as lau_unit_creative_cover_clustering_material_id
  </sql>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, image_md5, cluster_id, image_url, ctime, mtime, image_id, material_id
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.adp.cpc.po.ad_data_write.LauUnitCreativeCoverClusteringPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from lau_unit_creative_cover_clustering
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from lau_unit_creative_cover_clustering
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from lau_unit_creative_cover_clustering
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.adp.cpc.po.ad_data_write.LauUnitCreativeCoverClusteringPoExample">
    delete from lau_unit_creative_cover_clustering
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.adp.cpc.po.ad_data_write.LauUnitCreativeCoverClusteringPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_unit_creative_cover_clustering (image_md5, cluster_id, image_url, 
      ctime, mtime, image_id, 
      material_id)
    values (#{imageMd5,jdbcType=VARCHAR}, #{clusterId,jdbcType=BIGINT}, #{imageUrl,jdbcType=VARCHAR}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{imageId,jdbcType=BIGINT}, 
      #{materialId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.adp.cpc.po.ad_data_write.LauUnitCreativeCoverClusteringPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_unit_creative_cover_clustering
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="imageMd5 != null">
        image_md5,
      </if>
      <if test="clusterId != null">
        cluster_id,
      </if>
      <if test="imageUrl != null">
        image_url,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="imageId != null">
        image_id,
      </if>
      <if test="materialId != null">
        material_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="imageMd5 != null">
        #{imageMd5,jdbcType=VARCHAR},
      </if>
      <if test="clusterId != null">
        #{clusterId,jdbcType=BIGINT},
      </if>
      <if test="imageUrl != null">
        #{imageUrl,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="imageId != null">
        #{imageId,jdbcType=BIGINT},
      </if>
      <if test="materialId != null">
        #{materialId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.adp.cpc.po.ad_data_write.LauUnitCreativeCoverClusteringPoExample" resultType="java.lang.Long">
    select count(*) from lau_unit_creative_cover_clustering
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update lau_unit_creative_cover_clustering
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.imageMd5 != null">
        image_md5 = #{record.imageMd5,jdbcType=VARCHAR},
      </if>
      <if test="record.clusterId != null">
        cluster_id = #{record.clusterId,jdbcType=BIGINT},
      </if>
      <if test="record.imageUrl != null">
        image_url = #{record.imageUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.imageId != null">
        image_id = #{record.imageId,jdbcType=BIGINT},
      </if>
      <if test="record.materialId != null">
        material_id = #{record.materialId,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update lau_unit_creative_cover_clustering
    set id = #{record.id,jdbcType=BIGINT},
      image_md5 = #{record.imageMd5,jdbcType=VARCHAR},
      cluster_id = #{record.clusterId,jdbcType=BIGINT},
      image_url = #{record.imageUrl,jdbcType=VARCHAR},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      image_id = #{record.imageId,jdbcType=BIGINT},
      material_id = #{record.materialId,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.adp.cpc.po.ad_data_write.LauUnitCreativeCoverClusteringPo">
    update lau_unit_creative_cover_clustering
    <set>
      <if test="imageMd5 != null">
        image_md5 = #{imageMd5,jdbcType=VARCHAR},
      </if>
      <if test="clusterId != null">
        cluster_id = #{clusterId,jdbcType=BIGINT},
      </if>
      <if test="imageUrl != null">
        image_url = #{imageUrl,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="imageId != null">
        image_id = #{imageId,jdbcType=BIGINT},
      </if>
      <if test="materialId != null">
        material_id = #{materialId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.adp.cpc.po.ad_data_write.LauUnitCreativeCoverClusteringPo">
    update lau_unit_creative_cover_clustering
    set image_md5 = #{imageMd5,jdbcType=VARCHAR},
      cluster_id = #{clusterId,jdbcType=BIGINT},
      image_url = #{imageUrl,jdbcType=VARCHAR},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      image_id = #{imageId,jdbcType=BIGINT},
      material_id = #{materialId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.adp.cpc.po.ad_data_write.LauUnitCreativeCoverClusteringPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_unit_creative_cover_clustering (image_md5, cluster_id, image_url, 
      ctime, mtime, image_id, 
      material_id)
    values (#{imageMd5,jdbcType=VARCHAR}, #{clusterId,jdbcType=BIGINT}, #{imageUrl,jdbcType=VARCHAR}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{imageId,jdbcType=BIGINT}, 
      #{materialId,jdbcType=BIGINT})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      image_md5 = values(image_md5),
      cluster_id = values(cluster_id),
      image_url = values(image_url),
      ctime = values(ctime),
      mtime = values(mtime),
      image_id = values(image_id),
      material_id = values(material_id),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      lau_unit_creative_cover_clustering
      (image_md5,cluster_id,image_url,ctime,mtime,image_id,material_id)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.imageMd5,jdbcType=VARCHAR},
        #{item.clusterId,jdbcType=BIGINT},
        #{item.imageUrl,jdbcType=VARCHAR},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.imageId,jdbcType=BIGINT},
        #{item.materialId,jdbcType=BIGINT},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      lau_unit_creative_cover_clustering
      (image_md5,cluster_id,image_url,ctime,mtime,image_id,material_id)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.imageMd5,jdbcType=VARCHAR},
        #{item.clusterId,jdbcType=BIGINT},
        #{item.imageUrl,jdbcType=VARCHAR},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.imageId,jdbcType=BIGINT},
        #{item.materialId,jdbcType=BIGINT},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      image_md5 = values(image_md5),
      cluster_id = values(cluster_id),
      image_url = values(image_url),
      ctime = values(ctime),
      mtime = values(mtime),
      image_id = values(image_id),
      material_id = values(material_id),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.adp.cpc.po.ad_data_write.LauUnitCreativeCoverClusteringPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_unit_creative_cover_clustering
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="imageMd5 != null">
        image_md5,
      </if>
      <if test="clusterId != null">
        cluster_id,
      </if>
      <if test="imageUrl != null">
        image_url,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="imageId != null">
        image_id,
      </if>
      <if test="materialId != null">
        material_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="imageMd5 != null">
        #{imageMd5,jdbcType=VARCHAR},
      </if>
      <if test="clusterId != null">
        #{clusterId,jdbcType=BIGINT},
      </if>
      <if test="imageUrl != null">
        #{imageUrl,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="imageId != null">
        #{imageId,jdbcType=BIGINT},
      </if>
      <if test="materialId != null">
        #{materialId,jdbcType=BIGINT},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="imageMd5 != null">
        image_md5 = values(image_md5),
      </if>
      <if test="clusterId != null">
        cluster_id = values(cluster_id),
      </if>
      <if test="imageUrl != null">
        image_url = values(image_url),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="imageId != null">
        image_id = values(image_id),
      </if>
      <if test="materialId != null">
        material_id = values(material_id),
      </if>
    </trim>
  </insert>
</mapper>