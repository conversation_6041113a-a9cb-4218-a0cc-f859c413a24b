<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.adp.cpc.dao.ad_data_write.CreativeSmartTitleEventMapper">
  <resultMap id="BaseResultMap" type="com.bilibili.adp.cpc.po.ad_data_write.CreativeSmartTitleEventPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="account_id" jdbcType="BIGINT" property="accountId" />
    <result column="operate_id" jdbcType="BIGINT" property="operateId" />
    <result column="creative_mapping" jdbcType="VARCHAR" property="creativeMapping" />
    <result column="commerce_category_first_id" jdbcType="INTEGER" property="commerceCategoryFirstId" />
    <result column="commerce_category_second_id" jdbcType="INTEGER" property="commerceCategorySecondId" />
    <result column="end_window_time" jdbcType="TIMESTAMP" property="endWindowTime" />
    <result column="search_title_event" jdbcType="LONGVARCHAR" property="searchTitleEvent" />
    <result column="select_title_result" jdbcType="VARCHAR" property="selectTitleResult" />
  </resultMap>
  <resultMap id="AliasBaseResultMap" type="com.bilibili.adp.cpc.po.ad_data_write.CreativeSmartTitleEventPo">
    <id column="creative_smart_title_event_id" jdbcType="BIGINT" property="id" />
    <result column="creative_smart_title_event_ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="creative_smart_title_event_mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="creative_smart_title_event_account_id" jdbcType="BIGINT" property="accountId" />
    <result column="creative_smart_title_event_operate_id" jdbcType="BIGINT" property="operateId" />
    <result column="creative_smart_title_event_creative_mapping" jdbcType="VARCHAR" property="creativeMapping" />
    <result column="creative_smart_title_event_commerce_category_first_id" jdbcType="INTEGER" property="commerceCategoryFirstId" />
    <result column="creative_smart_title_event_commerce_category_second_id" jdbcType="INTEGER" property="commerceCategorySecondId" />
    <result column="creative_smart_title_event_end_window_time" jdbcType="TIMESTAMP" property="endWindowTime" />
    <result column="creative_smart_title_event_search_title_event" jdbcType="LONGVARCHAR" property="searchTitleEvent" />
    <result column="creative_smart_title_event_select_title_result" jdbcType="VARCHAR" property="selectTitleResult" />
  </resultMap>
  <sql id="Alias_Base_Column_List">
    ${alias}.id as creative_smart_title_event_id, ${alias}.ctime as creative_smart_title_event_ctime, 
    ${alias}.mtime as creative_smart_title_event_mtime, ${alias}.account_id as creative_smart_title_event_account_id, 
    ${alias}.operate_id as creative_smart_title_event_operate_id, ${alias}.creative_mapping as creative_smart_title_event_creative_mapping, 
    ${alias}.commerce_category_first_id as creative_smart_title_event_commerce_category_first_id, 
    ${alias}.commerce_category_second_id as creative_smart_title_event_commerce_category_second_id,
    ${alias}.search_title_event as creative_smart_title_event_search_title_event,
    ${alias}.end_window_time as creative_smart_title_event_end_window_time, ${alias}.select_title_result as creative_smart_title_event_select_title_result
  </sql>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.bilibili.adp.cpc.po.ad_data_write.CreativeSmartTitleEventPo">
    <result column="search_title_event" jdbcType="LONGVARCHAR" property="searchTitleEvent" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, ctime, mtime, account_id, operate_id, creative_mapping, commerce_category_first_id, 
    commerce_category_second_id, end_window_time, search_title_event, select_title_result
  </sql>
  <sql id="Blob_Column_List">
    search_title_event
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.bilibili.adp.cpc.po.ad_data_write.CreativeSmartTitleEventPoExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from creative_smart_title_event
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByExample" parameterType="com.bilibili.adp.cpc.po.ad_data_write.CreativeSmartTitleEventPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from creative_smart_title_event
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from creative_smart_title_event
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from creative_smart_title_event
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.adp.cpc.po.ad_data_write.CreativeSmartTitleEventPoExample">
    delete from creative_smart_title_event
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.adp.cpc.po.ad_data_write.CreativeSmartTitleEventPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into creative_smart_title_event (ctime, mtime, account_id, 
      operate_id, creative_mapping, commerce_category_first_id, 
      commerce_category_second_id, end_window_time, 
      select_title_result, search_title_event
      )
    values (#{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{accountId,jdbcType=BIGINT}, 
      #{operateId,jdbcType=BIGINT}, #{creativeMapping,jdbcType=VARCHAR}, #{commerceCategoryFirstId,jdbcType=INTEGER}, 
      #{commerceCategorySecondId,jdbcType=INTEGER}, #{endWindowTime,jdbcType=TIMESTAMP}, 
      #{selectTitleResult,jdbcType=VARCHAR}, #{searchTitleEvent,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.adp.cpc.po.ad_data_write.CreativeSmartTitleEventPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into creative_smart_title_event
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="operateId != null">
        operate_id,
      </if>
      <if test="creativeMapping != null">
        creative_mapping,
      </if>
      <if test="commerceCategoryFirstId != null">
        commerce_category_first_id,
      </if>
      <if test="commerceCategorySecondId != null">
        commerce_category_second_id,
      </if>
      <if test="endWindowTime != null">
        end_window_time,
      </if>
      <if test="selectTitleResult != null">
        select_title_result,
      </if>
      <if test="searchTitleEvent != null">
        search_title_event,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=BIGINT},
      </if>
      <if test="operateId != null">
        #{operateId,jdbcType=BIGINT},
      </if>
      <if test="creativeMapping != null">
        #{creativeMapping,jdbcType=VARCHAR},
      </if>
      <if test="commerceCategoryFirstId != null">
        #{commerceCategoryFirstId,jdbcType=INTEGER},
      </if>
      <if test="commerceCategorySecondId != null">
        #{commerceCategorySecondId,jdbcType=INTEGER},
      </if>
      <if test="endWindowTime != null">
        #{endWindowTime,jdbcType=TIMESTAMP},
      </if>
      <if test="selectTitleResult != null">
        #{selectTitleResult,jdbcType=VARCHAR},
      </if>
      <if test="searchTitleEvent != null">
        #{searchTitleEvent,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.adp.cpc.po.ad_data_write.CreativeSmartTitleEventPoExample" resultType="java.lang.Long">
    select count(*) from creative_smart_title_event
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update creative_smart_title_event
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.accountId != null">
        account_id = #{record.accountId,jdbcType=BIGINT},
      </if>
      <if test="record.operateId != null">
        operate_id = #{record.operateId,jdbcType=BIGINT},
      </if>
      <if test="record.creativeMapping != null">
        creative_mapping = #{record.creativeMapping,jdbcType=VARCHAR},
      </if>
      <if test="record.commerceCategoryFirstId != null">
        commerce_category_first_id = #{record.commerceCategoryFirstId,jdbcType=INTEGER},
      </if>
      <if test="record.commerceCategorySecondId != null">
        commerce_category_second_id = #{record.commerceCategorySecondId,jdbcType=INTEGER},
      </if>
      <if test="record.endWindowTime != null">
        end_window_time = #{record.endWindowTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.selectTitleResult != null">
        select_title_result = #{record.selectTitleResult,jdbcType=VARCHAR},
      </if>
      <if test="record.searchTitleEvent != null">
        search_title_event = #{record.searchTitleEvent,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update creative_smart_title_event
    set id = #{record.id,jdbcType=BIGINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      account_id = #{record.accountId,jdbcType=BIGINT},
      operate_id = #{record.operateId,jdbcType=BIGINT},
      creative_mapping = #{record.creativeMapping,jdbcType=VARCHAR},
      commerce_category_first_id = #{record.commerceCategoryFirstId,jdbcType=INTEGER},
      commerce_category_second_id = #{record.commerceCategorySecondId,jdbcType=INTEGER},
      end_window_time = #{record.endWindowTime,jdbcType=TIMESTAMP},
      select_title_result = #{record.selectTitleResult,jdbcType=VARCHAR},
      search_title_event = #{record.searchTitleEvent,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update creative_smart_title_event
    set id = #{record.id,jdbcType=BIGINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      account_id = #{record.accountId,jdbcType=BIGINT},
      operate_id = #{record.operateId,jdbcType=BIGINT},
      creative_mapping = #{record.creativeMapping,jdbcType=VARCHAR},
      commerce_category_first_id = #{record.commerceCategoryFirstId,jdbcType=INTEGER},
      commerce_category_second_id = #{record.commerceCategorySecondId,jdbcType=INTEGER},
      end_window_time = #{record.endWindowTime,jdbcType=TIMESTAMP},
      select_title_result = #{record.selectTitleResult,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.adp.cpc.po.ad_data_write.CreativeSmartTitleEventPo">
    update creative_smart_title_event
    <set>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=BIGINT},
      </if>
      <if test="operateId != null">
        operate_id = #{operateId,jdbcType=BIGINT},
      </if>
      <if test="creativeMapping != null">
        creative_mapping = #{creativeMapping,jdbcType=VARCHAR},
      </if>
      <if test="commerceCategoryFirstId != null">
        commerce_category_first_id = #{commerceCategoryFirstId,jdbcType=INTEGER},
      </if>
      <if test="commerceCategorySecondId != null">
        commerce_category_second_id = #{commerceCategorySecondId,jdbcType=INTEGER},
      </if>
      <if test="endWindowTime != null">
        end_window_time = #{endWindowTime,jdbcType=TIMESTAMP},
      </if>
      <if test="selectTitleResult != null">
        select_title_result = #{selectTitleResult,jdbcType=VARCHAR},
      </if>
      <if test="searchTitleEvent != null">
        search_title_event = #{searchTitleEvent,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.bilibili.adp.cpc.po.ad_data_write.CreativeSmartTitleEventPo">
    update creative_smart_title_event
    set ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      account_id = #{accountId,jdbcType=BIGINT},
      operate_id = #{operateId,jdbcType=BIGINT},
      creative_mapping = #{creativeMapping,jdbcType=VARCHAR},
      commerce_category_first_id = #{commerceCategoryFirstId,jdbcType=INTEGER},
      commerce_category_second_id = #{commerceCategorySecondId,jdbcType=INTEGER},
      end_window_time = #{endWindowTime,jdbcType=TIMESTAMP},
      select_title_result = #{selectTitleResult,jdbcType=VARCHAR},
      search_title_event = #{searchTitleEvent,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.adp.cpc.po.ad_data_write.CreativeSmartTitleEventPo">
    update creative_smart_title_event
    set ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      account_id = #{accountId,jdbcType=BIGINT},
      operate_id = #{operateId,jdbcType=BIGINT},
      creative_mapping = #{creativeMapping,jdbcType=VARCHAR},
      commerce_category_first_id = #{commerceCategoryFirstId,jdbcType=INTEGER},
      commerce_category_second_id = #{commerceCategorySecondId,jdbcType=INTEGER},
      end_window_time = #{endWindowTime,jdbcType=TIMESTAMP},
      select_title_result = #{selectTitleResult,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.adp.cpc.po.ad_data_write.CreativeSmartTitleEventPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into creative_smart_title_event (ctime, mtime, account_id, 
      operate_id, creative_mapping, commerce_category_first_id, 
      commerce_category_second_id, end_window_time, 
      select_title_result, search_title_event
      )
    values (#{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{accountId,jdbcType=BIGINT}, 
      #{operateId,jdbcType=BIGINT}, #{creativeMapping,jdbcType=VARCHAR}, #{commerceCategoryFirstId,jdbcType=INTEGER}, 
      #{commerceCategorySecondId,jdbcType=INTEGER}, #{endWindowTime,jdbcType=TIMESTAMP}, 
      #{selectTitleResult,jdbcType=VARCHAR}, #{searchTitleEvent,jdbcType=LONGVARCHAR}
      )
    <trim prefix="on duplicate key update" suffixOverrides=",">
      ctime = values(ctime),
      mtime = values(mtime),
      account_id = values(account_id),
      operate_id = values(operate_id),
      creative_mapping = values(creative_mapping),
      commerce_category_first_id = values(commerce_category_first_id),
      commerce_category_second_id = values(commerce_category_second_id),
      end_window_time = values(end_window_time),
      select_title_result = values(select_title_result),
      search_title_event = values(search_title_event),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      creative_smart_title_event
      (ctime,mtime,account_id,operate_id,creative_mapping,commerce_category_first_id,commerce_category_second_id,end_window_time,select_title_result,search_title_event)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.accountId,jdbcType=BIGINT},
        #{item.operateId,jdbcType=BIGINT},
        #{item.creativeMapping,jdbcType=VARCHAR},
        #{item.commerceCategoryFirstId,jdbcType=INTEGER},
        #{item.commerceCategorySecondId,jdbcType=INTEGER},
        #{item.endWindowTime,jdbcType=TIMESTAMP},
        #{item.selectTitleResult,jdbcType=VARCHAR},
        #{item.searchTitleEvent,jdbcType=LONGVARCHAR},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      creative_smart_title_event
      (ctime,mtime,account_id,operate_id,creative_mapping,commerce_category_first_id,commerce_category_second_id,end_window_time,select_title_result,search_title_event)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.accountId,jdbcType=BIGINT},
        #{item.operateId,jdbcType=BIGINT},
        #{item.creativeMapping,jdbcType=VARCHAR},
        #{item.commerceCategoryFirstId,jdbcType=INTEGER},
        #{item.commerceCategorySecondId,jdbcType=INTEGER},
        #{item.endWindowTime,jdbcType=TIMESTAMP},
        #{item.selectTitleResult,jdbcType=VARCHAR},
        #{item.searchTitleEvent,jdbcType=LONGVARCHAR},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      ctime = values(ctime),
      mtime = values(mtime),
      account_id = values(account_id),
      operate_id = values(operate_id),
      creative_mapping = values(creative_mapping),
      commerce_category_first_id = values(commerce_category_first_id),
      commerce_category_second_id = values(commerce_category_second_id),
      end_window_time = values(end_window_time),
      select_title_result = values(select_title_result),
      search_title_event = values(search_title_event),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.adp.cpc.po.ad_data_write.CreativeSmartTitleEventPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into creative_smart_title_event
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="operateId != null">
        operate_id,
      </if>
      <if test="creativeMapping != null">
        creative_mapping,
      </if>
      <if test="commerceCategoryFirstId != null">
        commerce_category_first_id,
      </if>
      <if test="commerceCategorySecondId != null">
        commerce_category_second_id,
      </if>
      <if test="endWindowTime != null">
        end_window_time,
      </if>
      <if test="selectTitleResult != null">
        select_title_result,
      </if>
      <if test="searchTitleEvent != null">
        search_title_event,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=BIGINT},
      </if>
      <if test="operateId != null">
        #{operateId,jdbcType=BIGINT},
      </if>
      <if test="creativeMapping != null">
        #{creativeMapping,jdbcType=VARCHAR},
      </if>
      <if test="commerceCategoryFirstId != null">
        #{commerceCategoryFirstId,jdbcType=INTEGER},
      </if>
      <if test="commerceCategorySecondId != null">
        #{commerceCategorySecondId,jdbcType=INTEGER},
      </if>
      <if test="endWindowTime != null">
        #{endWindowTime,jdbcType=TIMESTAMP},
      </if>
      <if test="selectTitleResult != null">
        #{selectTitleResult,jdbcType=VARCHAR},
      </if>
      <if test="searchTitleEvent != null">
        #{searchTitleEvent,jdbcType=LONGVARCHAR},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="accountId != null">
        account_id = values(account_id),
      </if>
      <if test="operateId != null">
        operate_id = values(operate_id),
      </if>
      <if test="creativeMapping != null">
        creative_mapping = values(creative_mapping),
      </if>
      <if test="commerceCategoryFirstId != null">
        commerce_category_first_id = values(commerce_category_first_id),
      </if>
      <if test="commerceCategorySecondId != null">
        commerce_category_second_id = values(commerce_category_second_id),
      </if>
      <if test="endWindowTime != null">
        end_window_time = values(end_window_time),
      </if>
      <if test="selectTitleResult != null">
        select_title_result = values(select_title_result),
      </if>
      <if test="searchTitleEvent != null">
        search_title_event = values(search_title_event),
      </if>
    </trim>
  </insert>
</mapper>