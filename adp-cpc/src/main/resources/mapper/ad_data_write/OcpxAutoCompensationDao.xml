<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.adp.cpc.dao.ad_data_write.OcpxAutoCompensationDao">
  <resultMap id="BaseResultMap" type="com.bilibili.adp.cpc.po.ad_data_write.OcpxAutoCompensationPo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="account_id" jdbcType="INTEGER" property="accountId" />
    <result column="unit_id" jdbcType="INTEGER" property="unitId" />
    <result column="period" jdbcType="INTEGER" property="period" />
    <result column="conv_num" jdbcType="INTEGER" property="convNum" />
    <result column="is_confidence" jdbcType="TINYINT" property="isConfidence" />
    <result column="is_violated" jdbcType="TINYINT" property="isViolated" />
    <result column="is_sent" jdbcType="TINYINT" property="isSent" />
    <result column="compensation" jdbcType="INTEGER" property="compensation" />
    <result column="log_date" jdbcType="TIMESTAMP" property="logDate" />
    <result column="version" jdbcType="TINYINT" property="version" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="unit_period_type" jdbcType="TINYINT" property="unitPeriodType" />
    <result column="need_pay_date" jdbcType="TIMESTAMP" property="needPayDate" />
  </resultMap>
  <resultMap id="AliasBaseResultMap" type="com.bilibili.adp.cpc.po.ad_data_write.OcpxAutoCompensationPo">
    <id column="ocpx_auto_compensation_id" jdbcType="INTEGER" property="id" />
    <result column="ocpx_auto_compensation_account_id" jdbcType="INTEGER" property="accountId" />
    <result column="ocpx_auto_compensation_unit_id" jdbcType="INTEGER" property="unitId" />
    <result column="ocpx_auto_compensation_period" jdbcType="INTEGER" property="period" />
    <result column="ocpx_auto_compensation_conv_num" jdbcType="INTEGER" property="convNum" />
    <result column="ocpx_auto_compensation_is_confidence" jdbcType="TINYINT" property="isConfidence" />
    <result column="ocpx_auto_compensation_is_violated" jdbcType="TINYINT" property="isViolated" />
    <result column="ocpx_auto_compensation_is_sent" jdbcType="TINYINT" property="isSent" />
    <result column="ocpx_auto_compensation_compensation" jdbcType="INTEGER" property="compensation" />
    <result column="ocpx_auto_compensation_log_date" jdbcType="TIMESTAMP" property="logDate" />
    <result column="ocpx_auto_compensation_version" jdbcType="TINYINT" property="version" />
    <result column="ocpx_auto_compensation_is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ocpx_auto_compensation_ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="ocpx_auto_compensation_mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="ocpx_auto_compensation_unit_period_type" jdbcType="TINYINT" property="unitPeriodType" />
    <result column="ocpx_auto_compensation_need_pay_date" jdbcType="TIMESTAMP" property="needPayDate" />
  </resultMap>
  <sql id="Alias_Base_Column_List">
    ${alias}.id as ocpx_auto_compensation_id, ${alias}.account_id as ocpx_auto_compensation_account_id, 
    ${alias}.unit_id as ocpx_auto_compensation_unit_id, ${alias}.period as ocpx_auto_compensation_period, 
    ${alias}.conv_num as ocpx_auto_compensation_conv_num, ${alias}.is_confidence as ocpx_auto_compensation_is_confidence, 
    ${alias}.is_violated as ocpx_auto_compensation_is_violated, ${alias}.is_sent as ocpx_auto_compensation_is_sent, 
    ${alias}.compensation as ocpx_auto_compensation_compensation, ${alias}.log_date as ocpx_auto_compensation_log_date, 
    ${alias}.version as ocpx_auto_compensation_version, ${alias}.is_deleted as ocpx_auto_compensation_is_deleted, 
    ${alias}.ctime as ocpx_auto_compensation_ctime, ${alias}.mtime as ocpx_auto_compensation_mtime, 
    ${alias}.unit_period_type as ocpx_auto_compensation_unit_period_type, ${alias}.need_pay_date as ocpx_auto_compensation_need_pay_date
  </sql>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, account_id, unit_id, period, conv_num, is_confidence, is_violated, is_sent, compensation, 
    log_date, version, is_deleted, ctime, mtime, unit_period_type, need_pay_date
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.adp.cpc.po.ad_data_write.OcpxAutoCompensationPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from ocpx_auto_compensation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ocpx_auto_compensation
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from ocpx_auto_compensation
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.adp.cpc.po.ad_data_write.OcpxAutoCompensationPoExample">
    delete from ocpx_auto_compensation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.adp.cpc.po.ad_data_write.OcpxAutoCompensationPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ocpx_auto_compensation (account_id, unit_id, period, 
      conv_num, is_confidence, is_violated, 
      is_sent, compensation, log_date, 
      version, is_deleted, ctime, 
      mtime, unit_period_type, need_pay_date
      )
    values (#{accountId,jdbcType=INTEGER}, #{unitId,jdbcType=INTEGER}, #{period,jdbcType=INTEGER}, 
      #{convNum,jdbcType=INTEGER}, #{isConfidence,jdbcType=TINYINT}, #{isViolated,jdbcType=TINYINT}, 
      #{isSent,jdbcType=TINYINT}, #{compensation,jdbcType=INTEGER}, #{logDate,jdbcType=TIMESTAMP}, 
      #{version,jdbcType=TINYINT}, #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, 
      #{mtime,jdbcType=TIMESTAMP}, #{unitPeriodType,jdbcType=TINYINT}, #{needPayDate,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.adp.cpc.po.ad_data_write.OcpxAutoCompensationPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ocpx_auto_compensation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        account_id,
      </if>
      <if test="unitId != null">
        unit_id,
      </if>
      <if test="period != null">
        period,
      </if>
      <if test="convNum != null">
        conv_num,
      </if>
      <if test="isConfidence != null">
        is_confidence,
      </if>
      <if test="isViolated != null">
        is_violated,
      </if>
      <if test="isSent != null">
        is_sent,
      </if>
      <if test="compensation != null">
        compensation,
      </if>
      <if test="logDate != null">
        log_date,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="unitPeriodType != null">
        unit_period_type,
      </if>
      <if test="needPayDate != null">
        need_pay_date,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="unitId != null">
        #{unitId,jdbcType=INTEGER},
      </if>
      <if test="period != null">
        #{period,jdbcType=INTEGER},
      </if>
      <if test="convNum != null">
        #{convNum,jdbcType=INTEGER},
      </if>
      <if test="isConfidence != null">
        #{isConfidence,jdbcType=TINYINT},
      </if>
      <if test="isViolated != null">
        #{isViolated,jdbcType=TINYINT},
      </if>
      <if test="isSent != null">
        #{isSent,jdbcType=TINYINT},
      </if>
      <if test="compensation != null">
        #{compensation,jdbcType=INTEGER},
      </if>
      <if test="logDate != null">
        #{logDate,jdbcType=TIMESTAMP},
      </if>
      <if test="version != null">
        #{version,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="unitPeriodType != null">
        #{unitPeriodType,jdbcType=TINYINT},
      </if>
      <if test="needPayDate != null">
        #{needPayDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.adp.cpc.po.ad_data_write.OcpxAutoCompensationPoExample" resultType="java.lang.Long">
    select count(*) from ocpx_auto_compensation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update ocpx_auto_compensation
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.accountId != null">
        account_id = #{record.accountId,jdbcType=INTEGER},
      </if>
      <if test="record.unitId != null">
        unit_id = #{record.unitId,jdbcType=INTEGER},
      </if>
      <if test="record.period != null">
        period = #{record.period,jdbcType=INTEGER},
      </if>
      <if test="record.convNum != null">
        conv_num = #{record.convNum,jdbcType=INTEGER},
      </if>
      <if test="record.isConfidence != null">
        is_confidence = #{record.isConfidence,jdbcType=TINYINT},
      </if>
      <if test="record.isViolated != null">
        is_violated = #{record.isViolated,jdbcType=TINYINT},
      </if>
      <if test="record.isSent != null">
        is_sent = #{record.isSent,jdbcType=TINYINT},
      </if>
      <if test="record.compensation != null">
        compensation = #{record.compensation,jdbcType=INTEGER},
      </if>
      <if test="record.logDate != null">
        log_date = #{record.logDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=TINYINT},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.unitPeriodType != null">
        unit_period_type = #{record.unitPeriodType,jdbcType=TINYINT},
      </if>
      <if test="record.needPayDate != null">
        need_pay_date = #{record.needPayDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update ocpx_auto_compensation
    set id = #{record.id,jdbcType=INTEGER},
      account_id = #{record.accountId,jdbcType=INTEGER},
      unit_id = #{record.unitId,jdbcType=INTEGER},
      period = #{record.period,jdbcType=INTEGER},
      conv_num = #{record.convNum,jdbcType=INTEGER},
      is_confidence = #{record.isConfidence,jdbcType=TINYINT},
      is_violated = #{record.isViolated,jdbcType=TINYINT},
      is_sent = #{record.isSent,jdbcType=TINYINT},
      compensation = #{record.compensation,jdbcType=INTEGER},
      log_date = #{record.logDate,jdbcType=TIMESTAMP},
      version = #{record.version,jdbcType=TINYINT},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      unit_period_type = #{record.unitPeriodType,jdbcType=TINYINT},
      need_pay_date = #{record.needPayDate,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.adp.cpc.po.ad_data_write.OcpxAutoCompensationPo">
    update ocpx_auto_compensation
    <set>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=INTEGER},
      </if>
      <if test="unitId != null">
        unit_id = #{unitId,jdbcType=INTEGER},
      </if>
      <if test="period != null">
        period = #{period,jdbcType=INTEGER},
      </if>
      <if test="convNum != null">
        conv_num = #{convNum,jdbcType=INTEGER},
      </if>
      <if test="isConfidence != null">
        is_confidence = #{isConfidence,jdbcType=TINYINT},
      </if>
      <if test="isViolated != null">
        is_violated = #{isViolated,jdbcType=TINYINT},
      </if>
      <if test="isSent != null">
        is_sent = #{isSent,jdbcType=TINYINT},
      </if>
      <if test="compensation != null">
        compensation = #{compensation,jdbcType=INTEGER},
      </if>
      <if test="logDate != null">
        log_date = #{logDate,jdbcType=TIMESTAMP},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="unitPeriodType != null">
        unit_period_type = #{unitPeriodType,jdbcType=TINYINT},
      </if>
      <if test="needPayDate != null">
        need_pay_date = #{needPayDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.adp.cpc.po.ad_data_write.OcpxAutoCompensationPo">
    update ocpx_auto_compensation
    set account_id = #{accountId,jdbcType=INTEGER},
      unit_id = #{unitId,jdbcType=INTEGER},
      period = #{period,jdbcType=INTEGER},
      conv_num = #{convNum,jdbcType=INTEGER},
      is_confidence = #{isConfidence,jdbcType=TINYINT},
      is_violated = #{isViolated,jdbcType=TINYINT},
      is_sent = #{isSent,jdbcType=TINYINT},
      compensation = #{compensation,jdbcType=INTEGER},
      log_date = #{logDate,jdbcType=TIMESTAMP},
      version = #{version,jdbcType=TINYINT},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      unit_period_type = #{unitPeriodType,jdbcType=TINYINT},
      need_pay_date = #{needPayDate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.adp.cpc.po.ad_data_write.OcpxAutoCompensationPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ocpx_auto_compensation (account_id, unit_id, period, 
      conv_num, is_confidence, is_violated, 
      is_sent, compensation, log_date, 
      version, is_deleted, ctime, 
      mtime, unit_period_type, need_pay_date
      )
    values (#{accountId,jdbcType=INTEGER}, #{unitId,jdbcType=INTEGER}, #{period,jdbcType=INTEGER}, 
      #{convNum,jdbcType=INTEGER}, #{isConfidence,jdbcType=TINYINT}, #{isViolated,jdbcType=TINYINT}, 
      #{isSent,jdbcType=TINYINT}, #{compensation,jdbcType=INTEGER}, #{logDate,jdbcType=TIMESTAMP}, 
      #{version,jdbcType=TINYINT}, #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, 
      #{mtime,jdbcType=TIMESTAMP}, #{unitPeriodType,jdbcType=TINYINT}, #{needPayDate,jdbcType=TIMESTAMP}
      )
    <trim prefix="on duplicate key update" suffixOverrides=",">
      account_id = values(account_id),
      unit_id = values(unit_id),
      period = values(period),
      conv_num = values(conv_num),
      is_confidence = values(is_confidence),
      is_violated = values(is_violated),
      is_sent = values(is_sent),
      compensation = values(compensation),
      log_date = values(log_date),
      version = values(version),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      unit_period_type = values(unit_period_type),
      need_pay_date = values(need_pay_date),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      ocpx_auto_compensation
      (account_id,unit_id,period,conv_num,is_confidence,is_violated,is_sent,compensation,log_date,version,is_deleted,ctime,mtime,unit_period_type,need_pay_date)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.accountId,jdbcType=INTEGER},
        #{item.unitId,jdbcType=INTEGER},
        #{item.period,jdbcType=INTEGER},
        #{item.convNum,jdbcType=INTEGER},
        #{item.isConfidence,jdbcType=TINYINT},
        #{item.isViolated,jdbcType=TINYINT},
        #{item.isSent,jdbcType=TINYINT},
        #{item.compensation,jdbcType=INTEGER},
        #{item.logDate,jdbcType=TIMESTAMP},
        #{item.version,jdbcType=TINYINT},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.unitPeriodType,jdbcType=TINYINT},
        #{item.needPayDate,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      ocpx_auto_compensation
      (account_id,unit_id,period,conv_num,is_confidence,is_violated,is_sent,compensation,log_date,version,is_deleted,ctime,mtime,unit_period_type,need_pay_date)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.accountId,jdbcType=INTEGER},
        #{item.unitId,jdbcType=INTEGER},
        #{item.period,jdbcType=INTEGER},
        #{item.convNum,jdbcType=INTEGER},
        #{item.isConfidence,jdbcType=TINYINT},
        #{item.isViolated,jdbcType=TINYINT},
        #{item.isSent,jdbcType=TINYINT},
        #{item.compensation,jdbcType=INTEGER},
        #{item.logDate,jdbcType=TIMESTAMP},
        #{item.version,jdbcType=TINYINT},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.unitPeriodType,jdbcType=TINYINT},
        #{item.needPayDate,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      account_id = values(account_id),
      unit_id = values(unit_id),
      period = values(period),
      conv_num = values(conv_num),
      is_confidence = values(is_confidence),
      is_violated = values(is_violated),
      is_sent = values(is_sent),
      compensation = values(compensation),
      log_date = values(log_date),
      version = values(version),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      unit_period_type = values(unit_period_type),
      need_pay_date = values(need_pay_date),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.adp.cpc.po.ad_data_write.OcpxAutoCompensationPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ocpx_auto_compensation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        account_id,
      </if>
      <if test="unitId != null">
        unit_id,
      </if>
      <if test="period != null">
        period,
      </if>
      <if test="convNum != null">
        conv_num,
      </if>
      <if test="isConfidence != null">
        is_confidence,
      </if>
      <if test="isViolated != null">
        is_violated,
      </if>
      <if test="isSent != null">
        is_sent,
      </if>
      <if test="compensation != null">
        compensation,
      </if>
      <if test="logDate != null">
        log_date,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="unitPeriodType != null">
        unit_period_type,
      </if>
      <if test="needPayDate != null">
        need_pay_date,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="unitId != null">
        #{unitId,jdbcType=INTEGER},
      </if>
      <if test="period != null">
        #{period,jdbcType=INTEGER},
      </if>
      <if test="convNum != null">
        #{convNum,jdbcType=INTEGER},
      </if>
      <if test="isConfidence != null">
        #{isConfidence,jdbcType=TINYINT},
      </if>
      <if test="isViolated != null">
        #{isViolated,jdbcType=TINYINT},
      </if>
      <if test="isSent != null">
        #{isSent,jdbcType=TINYINT},
      </if>
      <if test="compensation != null">
        #{compensation,jdbcType=INTEGER},
      </if>
      <if test="logDate != null">
        #{logDate,jdbcType=TIMESTAMP},
      </if>
      <if test="version != null">
        #{version,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="unitPeriodType != null">
        #{unitPeriodType,jdbcType=TINYINT},
      </if>
      <if test="needPayDate != null">
        #{needPayDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="accountId != null">
        account_id = values(account_id),
      </if>
      <if test="unitId != null">
        unit_id = values(unit_id),
      </if>
      <if test="period != null">
        period = values(period),
      </if>
      <if test="convNum != null">
        conv_num = values(conv_num),
      </if>
      <if test="isConfidence != null">
        is_confidence = values(is_confidence),
      </if>
      <if test="isViolated != null">
        is_violated = values(is_violated),
      </if>
      <if test="isSent != null">
        is_sent = values(is_sent),
      </if>
      <if test="compensation != null">
        compensation = values(compensation),
      </if>
      <if test="logDate != null">
        log_date = values(log_date),
      </if>
      <if test="version != null">
        version = values(version),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="unitPeriodType != null">
        unit_period_type = values(unit_period_type),
      </if>
      <if test="needPayDate != null">
        need_pay_date = values(need_pay_date),
      </if>
    </trim>
  </insert>
</mapper>