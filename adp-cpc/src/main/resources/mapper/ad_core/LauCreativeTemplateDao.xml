<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.adp.cpc.dao.ad_core.mybatis.LauCreativeTemplateDao">
  <resultMap id="BaseResultMap" type="com.bilibili.adp.cpc.po.ad.LauCreativeTemplatePo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="account_id" jdbcType="INTEGER" property="accountId" />
    <result column="unit_id" jdbcType="INTEGER" property="unitId" />
    <result column="creative_id" jdbcType="BIGINT" property="creativeId" />
    <result column="slot_group_id" jdbcType="INTEGER" property="slotGroupId" />
    <result column="template_id" jdbcType="INTEGER" property="templateId" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="biz_status" jdbcType="TINYINT" property="bizStatus" />
    <result column="reserved_price" jdbcType="INTEGER" property="reservedPrice" />
    <result column="reserve_rule_id" jdbcType="INTEGER" property="reserveRuleId" />
    <result column="creative_style" jdbcType="TINYINT" property="creativeStyle" />
    <result column="bus_mark_id" jdbcType="INTEGER" property="busMarkId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, account_id, unit_id, creative_id, slot_group_id, template_id, ctime, mtime, is_deleted, 
    biz_status, reserved_price, reserve_rule_id, creative_style, bus_mark_id
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.adp.cpc.po.ad.LauCreativeTemplatePoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from lau_creative_template
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from lau_creative_template
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from lau_creative_template
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.adp.cpc.po.ad.LauCreativeTemplatePoExample">
    delete from lau_creative_template
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.adp.cpc.po.ad.LauCreativeTemplatePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_creative_template (account_id, unit_id, creative_id, 
      slot_group_id, template_id, ctime, 
      mtime, is_deleted, biz_status, 
      reserved_price, reserve_rule_id, creative_style, 
      bus_mark_id)
    values (#{accountId,jdbcType=INTEGER}, #{unitId,jdbcType=INTEGER}, #{creativeId,jdbcType=BIGINT}, 
      #{slotGroupId,jdbcType=INTEGER}, #{templateId,jdbcType=INTEGER}, #{ctime,jdbcType=TIMESTAMP}, 
      #{mtime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=TINYINT}, #{bizStatus,jdbcType=TINYINT}, 
      #{reservedPrice,jdbcType=INTEGER}, #{reserveRuleId,jdbcType=INTEGER}, #{creativeStyle,jdbcType=TINYINT}, 
      #{busMarkId,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.adp.cpc.po.ad.LauCreativeTemplatePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_creative_template
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        account_id,
      </if>
      <if test="unitId != null">
        unit_id,
      </if>
      <if test="creativeId != null">
        creative_id,
      </if>
      <if test="slotGroupId != null">
        slot_group_id,
      </if>
      <if test="templateId != null">
        template_id,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="bizStatus != null">
        biz_status,
      </if>
      <if test="reservedPrice != null">
        reserved_price,
      </if>
      <if test="reserveRuleId != null">
        reserve_rule_id,
      </if>
      <if test="creativeStyle != null">
        creative_style,
      </if>
      <if test="busMarkId != null">
        bus_mark_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="unitId != null">
        #{unitId,jdbcType=INTEGER},
      </if>
      <if test="creativeId != null">
        #{creativeId,jdbcType=BIGINT},
      </if>
      <if test="slotGroupId != null">
        #{slotGroupId,jdbcType=INTEGER},
      </if>
      <if test="templateId != null">
        #{templateId,jdbcType=INTEGER},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="bizStatus != null">
        #{bizStatus,jdbcType=TINYINT},
      </if>
      <if test="reservedPrice != null">
        #{reservedPrice,jdbcType=INTEGER},
      </if>
      <if test="reserveRuleId != null">
        #{reserveRuleId,jdbcType=INTEGER},
      </if>
      <if test="creativeStyle != null">
        #{creativeStyle,jdbcType=TINYINT},
      </if>
      <if test="busMarkId != null">
        #{busMarkId,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.adp.cpc.po.ad.LauCreativeTemplatePoExample" resultType="java.lang.Long">
    select count(*) from lau_creative_template
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update lau_creative_template
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.accountId != null">
        account_id = #{record.accountId,jdbcType=INTEGER},
      </if>
      <if test="record.unitId != null">
        unit_id = #{record.unitId,jdbcType=INTEGER},
      </if>
      <if test="record.creativeId != null">
        creative_id = #{record.creativeId,jdbcType=BIGINT},
      </if>
      <if test="record.slotGroupId != null">
        slot_group_id = #{record.slotGroupId,jdbcType=INTEGER},
      </if>
      <if test="record.templateId != null">
        template_id = #{record.templateId,jdbcType=INTEGER},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.bizStatus != null">
        biz_status = #{record.bizStatus,jdbcType=TINYINT},
      </if>
      <if test="record.reservedPrice != null">
        reserved_price = #{record.reservedPrice,jdbcType=INTEGER},
      </if>
      <if test="record.reserveRuleId != null">
        reserve_rule_id = #{record.reserveRuleId,jdbcType=INTEGER},
      </if>
      <if test="record.creativeStyle != null">
        creative_style = #{record.creativeStyle,jdbcType=TINYINT},
      </if>
      <if test="record.busMarkId != null">
        bus_mark_id = #{record.busMarkId,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update lau_creative_template
    set id = #{record.id,jdbcType=BIGINT},
      account_id = #{record.accountId,jdbcType=INTEGER},
      unit_id = #{record.unitId,jdbcType=INTEGER},
      creative_id = #{record.creativeId,jdbcType=BIGINT},
      slot_group_id = #{record.slotGroupId,jdbcType=INTEGER},
      template_id = #{record.templateId,jdbcType=INTEGER},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      biz_status = #{record.bizStatus,jdbcType=TINYINT},
      reserved_price = #{record.reservedPrice,jdbcType=INTEGER},
      reserve_rule_id = #{record.reserveRuleId,jdbcType=INTEGER},
      creative_style = #{record.creativeStyle,jdbcType=TINYINT},
      bus_mark_id = #{record.busMarkId,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.adp.cpc.po.ad.LauCreativeTemplatePo">
    update lau_creative_template
    <set>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=INTEGER},
      </if>
      <if test="unitId != null">
        unit_id = #{unitId,jdbcType=INTEGER},
      </if>
      <if test="creativeId != null">
        creative_id = #{creativeId,jdbcType=BIGINT},
      </if>
      <if test="slotGroupId != null">
        slot_group_id = #{slotGroupId,jdbcType=INTEGER},
      </if>
      <if test="templateId != null">
        template_id = #{templateId,jdbcType=INTEGER},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="bizStatus != null">
        biz_status = #{bizStatus,jdbcType=TINYINT},
      </if>
      <if test="reservedPrice != null">
        reserved_price = #{reservedPrice,jdbcType=INTEGER},
      </if>
      <if test="reserveRuleId != null">
        reserve_rule_id = #{reserveRuleId,jdbcType=INTEGER},
      </if>
      <if test="creativeStyle != null">
        creative_style = #{creativeStyle,jdbcType=TINYINT},
      </if>
      <if test="busMarkId != null">
        bus_mark_id = #{busMarkId,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.adp.cpc.po.ad.LauCreativeTemplatePo">
    update lau_creative_template
    set account_id = #{accountId,jdbcType=INTEGER},
      unit_id = #{unitId,jdbcType=INTEGER},
      creative_id = #{creativeId,jdbcType=BIGINT},
      slot_group_id = #{slotGroupId,jdbcType=INTEGER},
      template_id = #{templateId,jdbcType=INTEGER},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      biz_status = #{bizStatus,jdbcType=TINYINT},
      reserved_price = #{reservedPrice,jdbcType=INTEGER},
      reserve_rule_id = #{reserveRuleId,jdbcType=INTEGER},
      creative_style = #{creativeStyle,jdbcType=TINYINT},
      bus_mark_id = #{busMarkId,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.adp.cpc.po.ad.LauCreativeTemplatePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_creative_template (account_id, unit_id, creative_id, 
      slot_group_id, template_id, ctime, 
      mtime, is_deleted, biz_status, 
      reserved_price, reserve_rule_id, creative_style, 
      bus_mark_id)
    values (#{accountId,jdbcType=INTEGER}, #{unitId,jdbcType=INTEGER}, #{creativeId,jdbcType=BIGINT}, 
      #{slotGroupId,jdbcType=INTEGER}, #{templateId,jdbcType=INTEGER}, #{ctime,jdbcType=TIMESTAMP}, 
      #{mtime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=TINYINT}, #{bizStatus,jdbcType=TINYINT}, 
      #{reservedPrice,jdbcType=INTEGER}, #{reserveRuleId,jdbcType=INTEGER}, #{creativeStyle,jdbcType=TINYINT}, 
      #{busMarkId,jdbcType=INTEGER})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      account_id = values(account_id),
      unit_id = values(unit_id),
      creative_id = values(creative_id),
      slot_group_id = values(slot_group_id),
      template_id = values(template_id),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
      biz_status = values(biz_status),
      reserved_price = values(reserved_price),
      reserve_rule_id = values(reserve_rule_id),
      creative_style = values(creative_style),
      bus_mark_id = values(bus_mark_id),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      lau_creative_template
      (account_id,unit_id,creative_id,slot_group_id,template_id,ctime,mtime,is_deleted,biz_status,reserved_price,reserve_rule_id,creative_style,bus_mark_id)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.accountId,jdbcType=INTEGER},
        #{item.unitId,jdbcType=INTEGER},
        #{item.creativeId,jdbcType=BIGINT},
        #{item.slotGroupId,jdbcType=INTEGER},
        #{item.templateId,jdbcType=INTEGER},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.bizStatus,jdbcType=TINYINT},
        #{item.reservedPrice,jdbcType=INTEGER},
        #{item.reserveRuleId,jdbcType=INTEGER},
        #{item.creativeStyle,jdbcType=TINYINT},
        #{item.busMarkId,jdbcType=INTEGER},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      lau_creative_template
      (account_id,unit_id,creative_id,slot_group_id,template_id,ctime,mtime,is_deleted,biz_status,reserved_price,reserve_rule_id,creative_style,bus_mark_id)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.accountId,jdbcType=INTEGER},
        #{item.unitId,jdbcType=INTEGER},
        #{item.creativeId,jdbcType=BIGINT},
        #{item.slotGroupId,jdbcType=INTEGER},
        #{item.templateId,jdbcType=INTEGER},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.bizStatus,jdbcType=TINYINT},
        #{item.reservedPrice,jdbcType=INTEGER},
        #{item.reserveRuleId,jdbcType=INTEGER},
        #{item.creativeStyle,jdbcType=TINYINT},
        #{item.busMarkId,jdbcType=INTEGER},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      account_id = values(account_id),
      unit_id = values(unit_id),
      creative_id = values(creative_id),
      slot_group_id = values(slot_group_id),
      template_id = values(template_id),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
      biz_status = values(biz_status),
      reserved_price = values(reserved_price),
      reserve_rule_id = values(reserve_rule_id),
      creative_style = values(creative_style),
      bus_mark_id = values(bus_mark_id),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.adp.cpc.po.ad.LauCreativeTemplatePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_creative_template
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        account_id,
      </if>
      <if test="unitId != null">
        unit_id,
      </if>
      <if test="creativeId != null">
        creative_id,
      </if>
      <if test="slotGroupId != null">
        slot_group_id,
      </if>
      <if test="templateId != null">
        template_id,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="bizStatus != null">
        biz_status,
      </if>
      <if test="reservedPrice != null">
        reserved_price,
      </if>
      <if test="reserveRuleId != null">
        reserve_rule_id,
      </if>
      <if test="creativeStyle != null">
        creative_style,
      </if>
      <if test="busMarkId != null">
        bus_mark_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="unitId != null">
        #{unitId,jdbcType=INTEGER},
      </if>
      <if test="creativeId != null">
        #{creativeId,jdbcType=BIGINT},
      </if>
      <if test="slotGroupId != null">
        #{slotGroupId,jdbcType=INTEGER},
      </if>
      <if test="templateId != null">
        #{templateId,jdbcType=INTEGER},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="bizStatus != null">
        #{bizStatus,jdbcType=TINYINT},
      </if>
      <if test="reservedPrice != null">
        #{reservedPrice,jdbcType=INTEGER},
      </if>
      <if test="reserveRuleId != null">
        #{reserveRuleId,jdbcType=INTEGER},
      </if>
      <if test="creativeStyle != null">
        #{creativeStyle,jdbcType=TINYINT},
      </if>
      <if test="busMarkId != null">
        #{busMarkId,jdbcType=INTEGER},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="accountId != null">
        account_id = values(account_id),
      </if>
      <if test="unitId != null">
        unit_id = values(unit_id),
      </if>
      <if test="creativeId != null">
        creative_id = values(creative_id),
      </if>
      <if test="slotGroupId != null">
        slot_group_id = values(slot_group_id),
      </if>
      <if test="templateId != null">
        template_id = values(template_id),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="bizStatus != null">
        biz_status = values(biz_status),
      </if>
      <if test="reservedPrice != null">
        reserved_price = values(reserved_price),
      </if>
      <if test="reserveRuleId != null">
        reserve_rule_id = values(reserve_rule_id),
      </if>
      <if test="creativeStyle != null">
        creative_style = values(creative_style),
      </if>
      <if test="busMarkId != null">
        bus_mark_id = values(bus_mark_id),
      </if>
    </trim>
  </insert>
</mapper>