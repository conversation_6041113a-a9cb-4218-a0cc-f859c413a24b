<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.adp.cpc.dao.ad_core.mybatis.NewLauCreativeFlyDynamicInfoDao">
  <resultMap id="BaseResultMap" type="com.bilibili.adp.cpc.po.ad.LauCreativeFlyDynamicInfoPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="account_id" jdbcType="INTEGER" property="accountId" />
    <result column="unit_id" jdbcType="INTEGER" property="unitId" />
    <result column="creative_id" jdbcType="INTEGER" property="creativeId" />
    <result column="dynamic_id" jdbcType="BIGINT" property="dynamicId" />
    <result column="like_count" jdbcType="VARCHAR" property="likeCount" />
    <result column="nickname" jdbcType="VARCHAR" property="nickname" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="sid" jdbcType="BIGINT" property="sid" />
    <result column="dynamic_up_mid" jdbcType="BIGINT" property="dynamicUpMid" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, account_id, unit_id, creative_id, dynamic_id, like_count, nickname, is_deleted, 
    ctime, mtime, sid, dynamic_up_mid
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.adp.cpc.po.ad.LauCreativeFlyDynamicInfoPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from lau_creative_fly_dynamic_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from lau_creative_fly_dynamic_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from lau_creative_fly_dynamic_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.adp.cpc.po.ad.LauCreativeFlyDynamicInfoPoExample">
    delete from lau_creative_fly_dynamic_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.adp.cpc.po.ad.LauCreativeFlyDynamicInfoPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_creative_fly_dynamic_info (account_id, unit_id, creative_id, 
      dynamic_id, like_count, nickname, 
      is_deleted, ctime, mtime, 
      sid, dynamic_up_mid)
    values (#{accountId,jdbcType=INTEGER}, #{unitId,jdbcType=INTEGER}, #{creativeId,jdbcType=INTEGER}, 
      #{dynamicId,jdbcType=BIGINT}, #{likeCount,jdbcType=VARCHAR}, #{nickname,jdbcType=VARCHAR}, 
      #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, 
      #{sid,jdbcType=BIGINT}, #{dynamicUpMid,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.adp.cpc.po.ad.LauCreativeFlyDynamicInfoPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_creative_fly_dynamic_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        account_id,
      </if>
      <if test="unitId != null">
        unit_id,
      </if>
      <if test="creativeId != null">
        creative_id,
      </if>
      <if test="dynamicId != null">
        dynamic_id,
      </if>
      <if test="likeCount != null">
        like_count,
      </if>
      <if test="nickname != null">
        nickname,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="sid != null">
        sid,
      </if>
      <if test="dynamicUpMid != null">
        dynamic_up_mid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="unitId != null">
        #{unitId,jdbcType=INTEGER},
      </if>
      <if test="creativeId != null">
        #{creativeId,jdbcType=INTEGER},
      </if>
      <if test="dynamicId != null">
        #{dynamicId,jdbcType=BIGINT},
      </if>
      <if test="likeCount != null">
        #{likeCount,jdbcType=VARCHAR},
      </if>
      <if test="nickname != null">
        #{nickname,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="sid != null">
        #{sid,jdbcType=BIGINT},
      </if>
      <if test="dynamicUpMid != null">
        #{dynamicUpMid,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.adp.cpc.po.ad.LauCreativeFlyDynamicInfoPoExample" resultType="java.lang.Long">
    select count(*) from lau_creative_fly_dynamic_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update lau_creative_fly_dynamic_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.accountId != null">
        account_id = #{record.accountId,jdbcType=INTEGER},
      </if>
      <if test="record.unitId != null">
        unit_id = #{record.unitId,jdbcType=INTEGER},
      </if>
      <if test="record.creativeId != null">
        creative_id = #{record.creativeId,jdbcType=INTEGER},
      </if>
      <if test="record.dynamicId != null">
        dynamic_id = #{record.dynamicId,jdbcType=BIGINT},
      </if>
      <if test="record.likeCount != null">
        like_count = #{record.likeCount,jdbcType=VARCHAR},
      </if>
      <if test="record.nickname != null">
        nickname = #{record.nickname,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.sid != null">
        sid = #{record.sid,jdbcType=BIGINT},
      </if>
      <if test="record.dynamicUpMid != null">
        dynamic_up_mid = #{record.dynamicUpMid,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update lau_creative_fly_dynamic_info
    set id = #{record.id,jdbcType=BIGINT},
      account_id = #{record.accountId,jdbcType=INTEGER},
      unit_id = #{record.unitId,jdbcType=INTEGER},
      creative_id = #{record.creativeId,jdbcType=INTEGER},
      dynamic_id = #{record.dynamicId,jdbcType=BIGINT},
      like_count = #{record.likeCount,jdbcType=VARCHAR},
      nickname = #{record.nickname,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      sid = #{record.sid,jdbcType=BIGINT},
      dynamic_up_mid = #{record.dynamicUpMid,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.adp.cpc.po.ad.LauCreativeFlyDynamicInfoPo">
    update lau_creative_fly_dynamic_info
    <set>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=INTEGER},
      </if>
      <if test="unitId != null">
        unit_id = #{unitId,jdbcType=INTEGER},
      </if>
      <if test="creativeId != null">
        creative_id = #{creativeId,jdbcType=INTEGER},
      </if>
      <if test="dynamicId != null">
        dynamic_id = #{dynamicId,jdbcType=BIGINT},
      </if>
      <if test="likeCount != null">
        like_count = #{likeCount,jdbcType=VARCHAR},
      </if>
      <if test="nickname != null">
        nickname = #{nickname,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="sid != null">
        sid = #{sid,jdbcType=BIGINT},
      </if>
      <if test="dynamicUpMid != null">
        dynamic_up_mid = #{dynamicUpMid,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.adp.cpc.po.ad.LauCreativeFlyDynamicInfoPo">
    update lau_creative_fly_dynamic_info
    set account_id = #{accountId,jdbcType=INTEGER},
      unit_id = #{unitId,jdbcType=INTEGER},
      creative_id = #{creativeId,jdbcType=INTEGER},
      dynamic_id = #{dynamicId,jdbcType=BIGINT},
      like_count = #{likeCount,jdbcType=VARCHAR},
      nickname = #{nickname,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      sid = #{sid,jdbcType=BIGINT},
      dynamic_up_mid = #{dynamicUpMid,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.adp.cpc.po.ad.LauCreativeFlyDynamicInfoPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_creative_fly_dynamic_info (account_id, unit_id, creative_id, 
      dynamic_id, like_count, nickname, 
      is_deleted, ctime, mtime, 
      sid, dynamic_up_mid)
    values (#{accountId,jdbcType=INTEGER}, #{unitId,jdbcType=INTEGER}, #{creativeId,jdbcType=INTEGER}, 
      #{dynamicId,jdbcType=BIGINT}, #{likeCount,jdbcType=VARCHAR}, #{nickname,jdbcType=VARCHAR}, 
      #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, 
      #{sid,jdbcType=BIGINT}, #{dynamicUpMid,jdbcType=BIGINT})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      account_id = values(account_id),
      unit_id = values(unit_id),
      creative_id = values(creative_id),
      dynamic_id = values(dynamic_id),
      like_count = values(like_count),
      nickname = values(nickname),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      sid = values(sid),
      dynamic_up_mid = values(dynamic_up_mid),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      lau_creative_fly_dynamic_info
      (account_id,unit_id,creative_id,dynamic_id,like_count,nickname,is_deleted,ctime,mtime,sid,dynamic_up_mid)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.accountId,jdbcType=INTEGER},
        #{item.unitId,jdbcType=INTEGER},
        #{item.creativeId,jdbcType=INTEGER},
        #{item.dynamicId,jdbcType=BIGINT},
        #{item.likeCount,jdbcType=VARCHAR},
        #{item.nickname,jdbcType=VARCHAR},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.sid,jdbcType=BIGINT},
        #{item.dynamicUpMid,jdbcType=BIGINT},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      lau_creative_fly_dynamic_info
      (account_id,unit_id,creative_id,dynamic_id,like_count,nickname,is_deleted,ctime,mtime,sid,dynamic_up_mid)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.accountId,jdbcType=INTEGER},
        #{item.unitId,jdbcType=INTEGER},
        #{item.creativeId,jdbcType=INTEGER},
        #{item.dynamicId,jdbcType=BIGINT},
        #{item.likeCount,jdbcType=VARCHAR},
        #{item.nickname,jdbcType=VARCHAR},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.sid,jdbcType=BIGINT},
        #{item.dynamicUpMid,jdbcType=BIGINT},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      account_id = values(account_id),
      unit_id = values(unit_id),
      creative_id = values(creative_id),
      dynamic_id = values(dynamic_id),
      like_count = values(like_count),
      nickname = values(nickname),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      sid = values(sid),
      dynamic_up_mid = values(dynamic_up_mid),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.adp.cpc.po.ad.LauCreativeFlyDynamicInfoPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_creative_fly_dynamic_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        account_id,
      </if>
      <if test="unitId != null">
        unit_id,
      </if>
      <if test="creativeId != null">
        creative_id,
      </if>
      <if test="dynamicId != null">
        dynamic_id,
      </if>
      <if test="likeCount != null">
        like_count,
      </if>
      <if test="nickname != null">
        nickname,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="sid != null">
        sid,
      </if>
      <if test="dynamicUpMid != null">
        dynamic_up_mid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="unitId != null">
        #{unitId,jdbcType=INTEGER},
      </if>
      <if test="creativeId != null">
        #{creativeId,jdbcType=INTEGER},
      </if>
      <if test="dynamicId != null">
        #{dynamicId,jdbcType=BIGINT},
      </if>
      <if test="likeCount != null">
        #{likeCount,jdbcType=VARCHAR},
      </if>
      <if test="nickname != null">
        #{nickname,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="sid != null">
        #{sid,jdbcType=BIGINT},
      </if>
      <if test="dynamicUpMid != null">
        #{dynamicUpMid,jdbcType=BIGINT},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="accountId != null">
        account_id = values(account_id),
      </if>
      <if test="unitId != null">
        unit_id = values(unit_id),
      </if>
      <if test="creativeId != null">
        creative_id = values(creative_id),
      </if>
      <if test="dynamicId != null">
        dynamic_id = values(dynamic_id),
      </if>
      <if test="likeCount != null">
        like_count = values(like_count),
      </if>
      <if test="nickname != null">
        nickname = values(nickname),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="sid != null">
        sid = values(sid),
      </if>
      <if test="dynamicUpMid != null">
        dynamic_up_mid = values(dynamic_up_mid),
      </if>
    </trim>
  </insert>
</mapper>