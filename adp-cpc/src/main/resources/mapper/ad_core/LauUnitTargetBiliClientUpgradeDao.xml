<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.adp.cpc.dao.ad_core.mybatis.LauUnitTargetBiliClientUpgradeDao">
  <resultMap id="BaseResultMap" type="com.bilibili.adp.cpc.po.ad.LauUnitTargetBiliClientUpgradePo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="unit_id" jdbcType="INTEGER" property="unitId" />
    <result column="os" jdbcType="INTEGER" property="os" />
    <result column="relation" jdbcType="TINYINT" property="relation" />
    <result column="smaller_version" jdbcType="INTEGER" property="smallerVersion" />
    <result column="larger_version" jdbcType="INTEGER" property="largerVersion" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, unit_id, os, relation, smaller_version, larger_version, is_deleted, ctime, mtime
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.adp.cpc.po.ad.LauUnitTargetBiliClientUpgradePoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from lau_unit_target_bili_client_upgrade
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from lau_unit_target_bili_client_upgrade
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from lau_unit_target_bili_client_upgrade
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.adp.cpc.po.ad.LauUnitTargetBiliClientUpgradePoExample">
    delete from lau_unit_target_bili_client_upgrade
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.adp.cpc.po.ad.LauUnitTargetBiliClientUpgradePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_unit_target_bili_client_upgrade (unit_id, os, relation, 
      smaller_version, larger_version, is_deleted, 
      ctime, mtime)
    values (#{unitId,jdbcType=INTEGER}, #{os,jdbcType=INTEGER}, #{relation,jdbcType=TINYINT}, 
      #{smallerVersion,jdbcType=INTEGER}, #{largerVersion,jdbcType=INTEGER}, #{isDeleted,jdbcType=TINYINT}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.adp.cpc.po.ad.LauUnitTargetBiliClientUpgradePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_unit_target_bili_client_upgrade
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="unitId != null">
        unit_id,
      </if>
      <if test="os != null">
        os,
      </if>
      <if test="relation != null">
        relation,
      </if>
      <if test="smallerVersion != null">
        smaller_version,
      </if>
      <if test="largerVersion != null">
        larger_version,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="unitId != null">
        #{unitId,jdbcType=INTEGER},
      </if>
      <if test="os != null">
        #{os,jdbcType=INTEGER},
      </if>
      <if test="relation != null">
        #{relation,jdbcType=TINYINT},
      </if>
      <if test="smallerVersion != null">
        #{smallerVersion,jdbcType=INTEGER},
      </if>
      <if test="largerVersion != null">
        #{largerVersion,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.adp.cpc.po.ad.LauUnitTargetBiliClientUpgradePoExample" resultType="java.lang.Long">
    select count(*) from lau_unit_target_bili_client_upgrade
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update lau_unit_target_bili_client_upgrade
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.unitId != null">
        unit_id = #{record.unitId,jdbcType=INTEGER},
      </if>
      <if test="record.os != null">
        os = #{record.os,jdbcType=INTEGER},
      </if>
      <if test="record.relation != null">
        relation = #{record.relation,jdbcType=TINYINT},
      </if>
      <if test="record.smallerVersion != null">
        smaller_version = #{record.smallerVersion,jdbcType=INTEGER},
      </if>
      <if test="record.largerVersion != null">
        larger_version = #{record.largerVersion,jdbcType=INTEGER},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update lau_unit_target_bili_client_upgrade
    set id = #{record.id,jdbcType=BIGINT},
      unit_id = #{record.unitId,jdbcType=INTEGER},
      os = #{record.os,jdbcType=INTEGER},
      relation = #{record.relation,jdbcType=TINYINT},
      smaller_version = #{record.smallerVersion,jdbcType=INTEGER},
      larger_version = #{record.largerVersion,jdbcType=INTEGER},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.adp.cpc.po.ad.LauUnitTargetBiliClientUpgradePo">
    update lau_unit_target_bili_client_upgrade
    <set>
      <if test="unitId != null">
        unit_id = #{unitId,jdbcType=INTEGER},
      </if>
      <if test="os != null">
        os = #{os,jdbcType=INTEGER},
      </if>
      <if test="relation != null">
        relation = #{relation,jdbcType=TINYINT},
      </if>
      <if test="smallerVersion != null">
        smaller_version = #{smallerVersion,jdbcType=INTEGER},
      </if>
      <if test="largerVersion != null">
        larger_version = #{largerVersion,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.adp.cpc.po.ad.LauUnitTargetBiliClientUpgradePo">
    update lau_unit_target_bili_client_upgrade
    set unit_id = #{unitId,jdbcType=INTEGER},
      os = #{os,jdbcType=INTEGER},
      relation = #{relation,jdbcType=TINYINT},
      smaller_version = #{smallerVersion,jdbcType=INTEGER},
      larger_version = #{largerVersion,jdbcType=INTEGER},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.adp.cpc.po.ad.LauUnitTargetBiliClientUpgradePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_unit_target_bili_client_upgrade (unit_id, os, relation, 
      smaller_version, larger_version, is_deleted, 
      ctime, mtime)
    values (#{unitId,jdbcType=INTEGER}, #{os,jdbcType=INTEGER}, #{relation,jdbcType=TINYINT}, 
      #{smallerVersion,jdbcType=INTEGER}, #{largerVersion,jdbcType=INTEGER}, #{isDeleted,jdbcType=TINYINT}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      unit_id = values(unit_id),
      os = values(os),
      relation = values(relation),
      smaller_version = values(smaller_version),
      larger_version = values(larger_version),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      lau_unit_target_bili_client_upgrade
      (unit_id,os,relation,smaller_version,larger_version,is_deleted,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.unitId,jdbcType=INTEGER},
        #{item.os,jdbcType=INTEGER},
        #{item.relation,jdbcType=TINYINT},
        #{item.smallerVersion,jdbcType=INTEGER},
        #{item.largerVersion,jdbcType=INTEGER},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      lau_unit_target_bili_client_upgrade
      (unit_id,os,relation,smaller_version,larger_version,is_deleted,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.unitId,jdbcType=INTEGER},
        #{item.os,jdbcType=INTEGER},
        #{item.relation,jdbcType=TINYINT},
        #{item.smallerVersion,jdbcType=INTEGER},
        #{item.largerVersion,jdbcType=INTEGER},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      unit_id = values(unit_id),
      os = values(os),
      relation = values(relation),
      smaller_version = values(smaller_version),
      larger_version = values(larger_version),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.adp.cpc.po.ad.LauUnitTargetBiliClientUpgradePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lau_unit_target_bili_client_upgrade
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="unitId != null">
        unit_id,
      </if>
      <if test="os != null">
        os,
      </if>
      <if test="relation != null">
        relation,
      </if>
      <if test="smallerVersion != null">
        smaller_version,
      </if>
      <if test="largerVersion != null">
        larger_version,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="unitId != null">
        #{unitId,jdbcType=INTEGER},
      </if>
      <if test="os != null">
        #{os,jdbcType=INTEGER},
      </if>
      <if test="relation != null">
        #{relation,jdbcType=TINYINT},
      </if>
      <if test="smallerVersion != null">
        #{smallerVersion,jdbcType=INTEGER},
      </if>
      <if test="largerVersion != null">
        #{largerVersion,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="unitId != null">
        unit_id = values(unit_id),
      </if>
      <if test="os != null">
        os = values(os),
      </if>
      <if test="relation != null">
        relation = values(relation),
      </if>
      <if test="smallerVersion != null">
        smaller_version = values(smaller_version),
      </if>
      <if test="largerVersion != null">
        larger_version = values(larger_version),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
    </trim>
  </insert>
</mapper>