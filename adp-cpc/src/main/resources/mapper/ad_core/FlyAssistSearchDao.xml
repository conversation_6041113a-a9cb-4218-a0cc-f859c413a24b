<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.adp.cpc.dao.ad_core.mybatis.FlyAssistSearchDao">
  <resultMap id="BaseResultMap" type="com.bilibili.adp.cpc.po.ad.FlyAssistSearchPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="unit_id" jdbcType="INTEGER" property="unitId" />
    <result column="is_search" jdbcType="INTEGER" property="isSearch" />
    <result column="search_end_date" jdbcType="TIMESTAMP" property="searchEndDate" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
  </resultMap>
  <resultMap id="AliasBaseResultMap" type="com.bilibili.adp.cpc.po.ad.FlyAssistSearchPo">
    <id column="fly_assist_search_id" jdbcType="BIGINT" property="id" />
    <result column="fly_assist_search_unit_id" jdbcType="INTEGER" property="unitId" />
    <result column="fly_assist_search_is_search" jdbcType="INTEGER" property="isSearch" />
    <result column="fly_assist_search_search_end_date" jdbcType="TIMESTAMP" property="searchEndDate" />
    <result column="fly_assist_search_ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="fly_assist_search_mtime" jdbcType="TIMESTAMP" property="mtime" />
  </resultMap>
  <sql id="Alias_Base_Column_List">
    ${alias}.id as fly_assist_search_id, ${alias}.unit_id as fly_assist_search_unit_id, 
    ${alias}.is_search as fly_assist_search_is_search, ${alias}.search_end_date as fly_assist_search_search_end_date, 
    ${alias}.ctime as fly_assist_search_ctime, ${alias}.mtime as fly_assist_search_mtime
  </sql>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, unit_id, is_search, search_end_date, ctime, mtime
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.adp.cpc.po.ad.FlyAssistSearchPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from fly_assist_search
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from fly_assist_search
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from fly_assist_search
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.adp.cpc.po.ad.FlyAssistSearchPoExample">
    delete from fly_assist_search
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.adp.cpc.po.ad.FlyAssistSearchPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into fly_assist_search (unit_id, is_search, search_end_date, 
      ctime, mtime)
    values (#{unitId,jdbcType=INTEGER}, #{isSearch,jdbcType=INTEGER}, #{searchEndDate,jdbcType=TIMESTAMP}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.adp.cpc.po.ad.FlyAssistSearchPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into fly_assist_search
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="unitId != null">
        unit_id,
      </if>
      <if test="isSearch != null">
        is_search,
      </if>
      <if test="searchEndDate != null">
        search_end_date,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="unitId != null">
        #{unitId,jdbcType=INTEGER},
      </if>
      <if test="isSearch != null">
        #{isSearch,jdbcType=INTEGER},
      </if>
      <if test="searchEndDate != null">
        #{searchEndDate,jdbcType=TIMESTAMP},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.adp.cpc.po.ad.FlyAssistSearchPoExample" resultType="java.lang.Long">
    select count(*) from fly_assist_search
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update fly_assist_search
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.unitId != null">
        unit_id = #{record.unitId,jdbcType=INTEGER},
      </if>
      <if test="record.isSearch != null">
        is_search = #{record.isSearch,jdbcType=INTEGER},
      </if>
      <if test="record.searchEndDate != null">
        search_end_date = #{record.searchEndDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update fly_assist_search
    set id = #{record.id,jdbcType=BIGINT},
      unit_id = #{record.unitId,jdbcType=INTEGER},
      is_search = #{record.isSearch,jdbcType=INTEGER},
      search_end_date = #{record.searchEndDate,jdbcType=TIMESTAMP},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.adp.cpc.po.ad.FlyAssistSearchPo">
    update fly_assist_search
    <set>
      <if test="unitId != null">
        unit_id = #{unitId,jdbcType=INTEGER},
      </if>
      <if test="isSearch != null">
        is_search = #{isSearch,jdbcType=INTEGER},
      </if>
      <if test="searchEndDate != null">
        search_end_date = #{searchEndDate,jdbcType=TIMESTAMP},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.adp.cpc.po.ad.FlyAssistSearchPo">
    update fly_assist_search
    set unit_id = #{unitId,jdbcType=INTEGER},
      is_search = #{isSearch,jdbcType=INTEGER},
      search_end_date = #{searchEndDate,jdbcType=TIMESTAMP},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.adp.cpc.po.ad.FlyAssistSearchPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into fly_assist_search (unit_id, is_search, search_end_date, 
      ctime, mtime)
    values (#{unitId,jdbcType=INTEGER}, #{isSearch,jdbcType=INTEGER}, #{searchEndDate,jdbcType=TIMESTAMP}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      unit_id = values(unit_id),
      is_search = values(is_search),
      search_end_date = values(search_end_date),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      fly_assist_search
      (unit_id,is_search,search_end_date,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.unitId,jdbcType=INTEGER},
        #{item.isSearch,jdbcType=INTEGER},
        #{item.searchEndDate,jdbcType=TIMESTAMP},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      fly_assist_search
      (unit_id,is_search,search_end_date,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.unitId,jdbcType=INTEGER},
        #{item.isSearch,jdbcType=INTEGER},
        #{item.searchEndDate,jdbcType=TIMESTAMP},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      unit_id = values(unit_id),
      is_search = values(is_search),
      search_end_date = values(search_end_date),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.adp.cpc.po.ad.FlyAssistSearchPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into fly_assist_search
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="unitId != null">
        unit_id,
      </if>
      <if test="isSearch != null">
        is_search,
      </if>
      <if test="searchEndDate != null">
        search_end_date,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="unitId != null">
        #{unitId,jdbcType=INTEGER},
      </if>
      <if test="isSearch != null">
        #{isSearch,jdbcType=INTEGER},
      </if>
      <if test="searchEndDate != null">
        #{searchEndDate,jdbcType=TIMESTAMP},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="unitId != null">
        unit_id = values(unit_id),
      </if>
      <if test="isSearch != null">
        is_search = values(is_search),
      </if>
      <if test="searchEndDate != null">
        search_end_date = values(search_end_date),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
    </trim>
  </insert>
</mapper>