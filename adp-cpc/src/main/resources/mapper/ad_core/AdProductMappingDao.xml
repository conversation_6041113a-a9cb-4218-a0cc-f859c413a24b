<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.adp.cpc.dao.ad_core.mybatis.AdProductMappingDao">
  <resultMap id="BaseResultMap" type="com.bilibili.adp.cpc.po.ad.AdProductMappingPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="ad_product_id" jdbcType="BIGINT" property="adProductId" />
    <result column="mapping_id" jdbcType="INTEGER" property="mappingId" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="belong_type" jdbcType="INTEGER" property="belongType" />
  </resultMap>
  <resultMap id="AliasBaseResultMap" type="com.bilibili.adp.cpc.po.ad.AdProductMappingPo">
    <id column="ad_product_mapping_id" jdbcType="BIGINT" property="id" />
    <result column="ad_product_mapping_ad_product_id" jdbcType="BIGINT" property="adProductId" />
    <result column="ad_product_mapping_mapping_id" jdbcType="INTEGER" property="mappingId" />
    <result column="ad_product_mapping_type" jdbcType="INTEGER" property="type" />
    <result column="ad_product_mapping_is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ad_product_mapping_ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="ad_product_mapping_mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="ad_product_mapping_belong_type" jdbcType="INTEGER" property="belongType" />
  </resultMap>
  <sql id="Alias_Base_Column_List">
    ${alias}.id as ad_product_mapping_id, ${alias}.ad_product_id as ad_product_mapping_ad_product_id, 
    ${alias}.mapping_id as ad_product_mapping_mapping_id, ${alias}.type as ad_product_mapping_type, 
    ${alias}.is_deleted as ad_product_mapping_is_deleted, ${alias}.ctime as ad_product_mapping_ctime, 
    ${alias}.mtime as ad_product_mapping_mtime, ${alias}.belong_type as ad_product_mapping_belong_type
  </sql>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, ad_product_id, mapping_id, type, is_deleted, ctime, mtime, belong_type
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.adp.cpc.po.ad.AdProductMappingPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from ad_product_mapping
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ad_product_mapping
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from ad_product_mapping
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.adp.cpc.po.ad.AdProductMappingPoExample">
    delete from ad_product_mapping
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.adp.cpc.po.ad.AdProductMappingPo">
    insert into ad_product_mapping (id, ad_product_id, mapping_id, 
      type, is_deleted, ctime, 
      mtime, belong_type)
    values (#{id,jdbcType=BIGINT}, #{adProductId,jdbcType=BIGINT}, #{mappingId,jdbcType=INTEGER}, 
      #{type,jdbcType=INTEGER}, #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, 
      #{mtime,jdbcType=TIMESTAMP}, #{belongType,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.adp.cpc.po.ad.AdProductMappingPo">
    insert into ad_product_mapping
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="adProductId != null">
        ad_product_id,
      </if>
      <if test="mappingId != null">
        mapping_id,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="belongType != null">
        belong_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="adProductId != null">
        #{adProductId,jdbcType=BIGINT},
      </if>
      <if test="mappingId != null">
        #{mappingId,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="belongType != null">
        #{belongType,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.adp.cpc.po.ad.AdProductMappingPoExample" resultType="java.lang.Long">
    select count(*) from ad_product_mapping
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update ad_product_mapping
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.adProductId != null">
        ad_product_id = #{record.adProductId,jdbcType=BIGINT},
      </if>
      <if test="record.mappingId != null">
        mapping_id = #{record.mappingId,jdbcType=INTEGER},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=INTEGER},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.belongType != null">
        belong_type = #{record.belongType,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update ad_product_mapping
    set id = #{record.id,jdbcType=BIGINT},
      ad_product_id = #{record.adProductId,jdbcType=BIGINT},
      mapping_id = #{record.mappingId,jdbcType=INTEGER},
      type = #{record.type,jdbcType=INTEGER},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      belong_type = #{record.belongType,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.adp.cpc.po.ad.AdProductMappingPo">
    update ad_product_mapping
    <set>
      <if test="adProductId != null">
        ad_product_id = #{adProductId,jdbcType=BIGINT},
      </if>
      <if test="mappingId != null">
        mapping_id = #{mappingId,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="belongType != null">
        belong_type = #{belongType,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.adp.cpc.po.ad.AdProductMappingPo">
    update ad_product_mapping
    set ad_product_id = #{adProductId,jdbcType=BIGINT},
      mapping_id = #{mappingId,jdbcType=INTEGER},
      type = #{type,jdbcType=INTEGER},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      belong_type = #{belongType,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.adp.cpc.po.ad.AdProductMappingPo">
    insert into ad_product_mapping (id, ad_product_id, mapping_id, 
      type, is_deleted, ctime, 
      mtime, belong_type)
    values (#{id,jdbcType=BIGINT}, #{adProductId,jdbcType=BIGINT}, #{mappingId,jdbcType=INTEGER}, 
      #{type,jdbcType=INTEGER}, #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, 
      #{mtime,jdbcType=TIMESTAMP}, #{belongType,jdbcType=INTEGER})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      ad_product_id = values(ad_product_id),
      mapping_id = values(mapping_id),
      type = values(type),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      belong_type = values(belong_type),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      ad_product_mapping
      (ad_product_id,mapping_id,type,is_deleted,ctime,mtime,belong_type)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.adProductId,jdbcType=BIGINT},
        #{item.mappingId,jdbcType=INTEGER},
        #{item.type,jdbcType=INTEGER},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.belongType,jdbcType=INTEGER},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      ad_product_mapping
      (ad_product_id,mapping_id,type,is_deleted,ctime,mtime,belong_type)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.adProductId,jdbcType=BIGINT},
        #{item.mappingId,jdbcType=INTEGER},
        #{item.type,jdbcType=INTEGER},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.belongType,jdbcType=INTEGER},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      ad_product_id = values(ad_product_id),
      mapping_id = values(mapping_id),
      type = values(type),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      belong_type = values(belong_type),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.adp.cpc.po.ad.AdProductMappingPo">
    insert into ad_product_mapping
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="adProductId != null">
        ad_product_id,
      </if>
      <if test="mappingId != null">
        mapping_id,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="belongType != null">
        belong_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="adProductId != null">
        #{adProductId,jdbcType=BIGINT},
      </if>
      <if test="mappingId != null">
        #{mappingId,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="belongType != null">
        #{belongType,jdbcType=INTEGER},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="adProductId != null">
        ad_product_id = values(ad_product_id),
      </if>
      <if test="mappingId != null">
        mapping_id = values(mapping_id),
      </if>
      <if test="type != null">
        type = values(type),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="belongType != null">
        belong_type = values(belong_type),
      </if>
    </trim>
  </insert>
</mapper>