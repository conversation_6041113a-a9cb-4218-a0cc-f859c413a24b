<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.adp.cpc.dao.ad_data_read.ArchiveTbPasswordCheckDao">
  <resultMap id="BaseResultMap" type="com.bilibili.adp.cpc.po.ad_data.ArchiveTbPasswordCheckPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="avid" jdbcType="BIGINT" property="avid" />
    <result column="log_date" jdbcType="DATE" property="logDate" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
  </resultMap>
  <resultMap id="AliasBaseResultMap" type="com.bilibili.adp.cpc.po.ad_data.ArchiveTbPasswordCheckPo">
    <id column="archive_tb_password_check_id" jdbcType="BIGINT" property="id" />
    <result column="archive_tb_password_check_avid" jdbcType="BIGINT" property="avid" />
    <result column="archive_tb_password_check_log_date" jdbcType="DATE" property="logDate" />
    <result column="archive_tb_password_check_ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="archive_tb_password_check_mtime" jdbcType="TIMESTAMP" property="mtime" />
  </resultMap>
  <sql id="Alias_Base_Column_List">
    ${alias}.id as archive_tb_password_check_id, ${alias}.avid as archive_tb_password_check_avid, 
    ${alias}.log_date as archive_tb_password_check_log_date, ${alias}.ctime as archive_tb_password_check_ctime, 
    ${alias}.mtime as archive_tb_password_check_mtime
  </sql>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, avid, log_date, ctime, mtime
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.adp.cpc.po.ad_data.ArchiveTbPasswordCheckPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from archive_tb_password_check
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from archive_tb_password_check
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from archive_tb_password_check
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.adp.cpc.po.ad_data.ArchiveTbPasswordCheckPoExample">
    delete from archive_tb_password_check
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.adp.cpc.po.ad_data.ArchiveTbPasswordCheckPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into archive_tb_password_check (avid, log_date, ctime, 
      mtime)
    values (#{avid,jdbcType=BIGINT}, #{logDate,jdbcType=DATE}, #{ctime,jdbcType=TIMESTAMP}, 
      #{mtime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.adp.cpc.po.ad_data.ArchiveTbPasswordCheckPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into archive_tb_password_check
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="avid != null">
        avid,
      </if>
      <if test="logDate != null">
        log_date,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="avid != null">
        #{avid,jdbcType=BIGINT},
      </if>
      <if test="logDate != null">
        #{logDate,jdbcType=DATE},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.adp.cpc.po.ad_data.ArchiveTbPasswordCheckPoExample" resultType="java.lang.Long">
    select count(*) from archive_tb_password_check
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update archive_tb_password_check
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.avid != null">
        avid = #{record.avid,jdbcType=BIGINT},
      </if>
      <if test="record.logDate != null">
        log_date = #{record.logDate,jdbcType=DATE},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update archive_tb_password_check
    set id = #{record.id,jdbcType=BIGINT},
      avid = #{record.avid,jdbcType=BIGINT},
      log_date = #{record.logDate,jdbcType=DATE},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.adp.cpc.po.ad_data.ArchiveTbPasswordCheckPo">
    update archive_tb_password_check
    <set>
      <if test="avid != null">
        avid = #{avid,jdbcType=BIGINT},
      </if>
      <if test="logDate != null">
        log_date = #{logDate,jdbcType=DATE},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.adp.cpc.po.ad_data.ArchiveTbPasswordCheckPo">
    update archive_tb_password_check
    set avid = #{avid,jdbcType=BIGINT},
      log_date = #{logDate,jdbcType=DATE},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.adp.cpc.po.ad_data.ArchiveTbPasswordCheckPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into archive_tb_password_check (avid, log_date, ctime, 
      mtime)
    values (#{avid,jdbcType=BIGINT}, #{logDate,jdbcType=DATE}, #{ctime,jdbcType=TIMESTAMP}, 
      #{mtime,jdbcType=TIMESTAMP})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      avid = values(avid),
      log_date = values(log_date),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      archive_tb_password_check
      (avid,log_date,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.avid,jdbcType=BIGINT},
        #{item.logDate,jdbcType=DATE},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      archive_tb_password_check
      (avid,log_date,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.avid,jdbcType=BIGINT},
        #{item.logDate,jdbcType=DATE},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      avid = values(avid),
      log_date = values(log_date),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.adp.cpc.po.ad_data.ArchiveTbPasswordCheckPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into archive_tb_password_check
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="avid != null">
        avid,
      </if>
      <if test="logDate != null">
        log_date,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="avid != null">
        #{avid,jdbcType=BIGINT},
      </if>
      <if test="logDate != null">
        #{logDate,jdbcType=DATE},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="avid != null">
        avid = values(avid),
      </if>
      <if test="logDate != null">
        log_date = values(log_date),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
    </trim>
  </insert>
</mapper>