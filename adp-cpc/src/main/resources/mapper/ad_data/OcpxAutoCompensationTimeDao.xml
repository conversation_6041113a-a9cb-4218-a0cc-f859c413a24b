<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.adp.cpc.dao.ad_data.OcpxAutoCompensationTimeDao">
  <resultMap id="BaseResultMap" type="com.bilibili.adp.cpc.po.ad_data.OcpxAutoCompensationTimePo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="account_id" jdbcType="INTEGER" property="accountId" />
    <result column="unit_id" jdbcType="INTEGER" property="unitId" />
    <result column="two_stage_time" jdbcType="TIMESTAMP" property="twoStageTime" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="unit_period_type" jdbcType="TINYINT" property="unitPeriodType" />
  </resultMap>
  <resultMap id="AliasBaseResultMap" type="com.bilibili.adp.cpc.po.ad_data.OcpxAutoCompensationTimePo">
    <id column="ocpx_auto_compensation_time_id" jdbcType="INTEGER" property="id" />
    <result column="ocpx_auto_compensation_time_account_id" jdbcType="INTEGER" property="accountId" />
    <result column="ocpx_auto_compensation_time_unit_id" jdbcType="INTEGER" property="unitId" />
    <result column="ocpx_auto_compensation_time_two_stage_time" jdbcType="TIMESTAMP" property="twoStageTime" />
    <result column="ocpx_auto_compensation_time_ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="ocpx_auto_compensation_time_mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="ocpx_auto_compensation_time_is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ocpx_auto_compensation_time_unit_period_type" jdbcType="TINYINT" property="unitPeriodType" />
  </resultMap>
  <sql id="Alias_Base_Column_List">
    ${alias}.id as ocpx_auto_compensation_time_id, ${alias}.account_id as ocpx_auto_compensation_time_account_id, 
    ${alias}.unit_id as ocpx_auto_compensation_time_unit_id, ${alias}.two_stage_time as ocpx_auto_compensation_time_two_stage_time, 
    ${alias}.ctime as ocpx_auto_compensation_time_ctime, ${alias}.mtime as ocpx_auto_compensation_time_mtime, 
    ${alias}.is_deleted as ocpx_auto_compensation_time_is_deleted, ${alias}.unit_period_type as ocpx_auto_compensation_time_unit_period_type
  </sql>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, account_id, unit_id, two_stage_time, ctime, mtime, is_deleted, unit_period_type
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.adp.cpc.po.ad_data.OcpxAutoCompensationTimePoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from ocpx_auto_compensation_time
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ocpx_auto_compensation_time
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from ocpx_auto_compensation_time
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.adp.cpc.po.ad_data.OcpxAutoCompensationTimePoExample">
    delete from ocpx_auto_compensation_time
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.adp.cpc.po.ad_data.OcpxAutoCompensationTimePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ocpx_auto_compensation_time (account_id, unit_id, two_stage_time, 
      ctime, mtime, is_deleted, 
      unit_period_type)
    values (#{accountId,jdbcType=INTEGER}, #{unitId,jdbcType=INTEGER}, #{twoStageTime,jdbcType=TIMESTAMP}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=TINYINT}, 
      #{unitPeriodType,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.adp.cpc.po.ad_data.OcpxAutoCompensationTimePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ocpx_auto_compensation_time
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        account_id,
      </if>
      <if test="unitId != null">
        unit_id,
      </if>
      <if test="twoStageTime != null">
        two_stage_time,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="unitPeriodType != null">
        unit_period_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="unitId != null">
        #{unitId,jdbcType=INTEGER},
      </if>
      <if test="twoStageTime != null">
        #{twoStageTime,jdbcType=TIMESTAMP},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="unitPeriodType != null">
        #{unitPeriodType,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.adp.cpc.po.ad_data.OcpxAutoCompensationTimePoExample" resultType="java.lang.Long">
    select count(*) from ocpx_auto_compensation_time
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update ocpx_auto_compensation_time
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.accountId != null">
        account_id = #{record.accountId,jdbcType=INTEGER},
      </if>
      <if test="record.unitId != null">
        unit_id = #{record.unitId,jdbcType=INTEGER},
      </if>
      <if test="record.twoStageTime != null">
        two_stage_time = #{record.twoStageTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.unitPeriodType != null">
        unit_period_type = #{record.unitPeriodType,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update ocpx_auto_compensation_time
    set id = #{record.id,jdbcType=INTEGER},
      account_id = #{record.accountId,jdbcType=INTEGER},
      unit_id = #{record.unitId,jdbcType=INTEGER},
      two_stage_time = #{record.twoStageTime,jdbcType=TIMESTAMP},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      unit_period_type = #{record.unitPeriodType,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.adp.cpc.po.ad_data.OcpxAutoCompensationTimePo">
    update ocpx_auto_compensation_time
    <set>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=INTEGER},
      </if>
      <if test="unitId != null">
        unit_id = #{unitId,jdbcType=INTEGER},
      </if>
      <if test="twoStageTime != null">
        two_stage_time = #{twoStageTime,jdbcType=TIMESTAMP},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="unitPeriodType != null">
        unit_period_type = #{unitPeriodType,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.adp.cpc.po.ad_data.OcpxAutoCompensationTimePo">
    update ocpx_auto_compensation_time
    set account_id = #{accountId,jdbcType=INTEGER},
      unit_id = #{unitId,jdbcType=INTEGER},
      two_stage_time = #{twoStageTime,jdbcType=TIMESTAMP},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      unit_period_type = #{unitPeriodType,jdbcType=TINYINT}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.adp.cpc.po.ad_data.OcpxAutoCompensationTimePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ocpx_auto_compensation_time (account_id, unit_id, two_stage_time, 
      ctime, mtime, is_deleted, 
      unit_period_type)
    values (#{accountId,jdbcType=INTEGER}, #{unitId,jdbcType=INTEGER}, #{twoStageTime,jdbcType=TIMESTAMP}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=TINYINT}, 
      #{unitPeriodType,jdbcType=TINYINT})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      account_id = values(account_id),
      unit_id = values(unit_id),
      two_stage_time = values(two_stage_time),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
      unit_period_type = values(unit_period_type),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      ocpx_auto_compensation_time
      (account_id,unit_id,two_stage_time,ctime,mtime,is_deleted,unit_period_type)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.accountId,jdbcType=INTEGER},
        #{item.unitId,jdbcType=INTEGER},
        #{item.twoStageTime,jdbcType=TIMESTAMP},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.unitPeriodType,jdbcType=TINYINT},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      ocpx_auto_compensation_time
      (account_id,unit_id,two_stage_time,ctime,mtime,is_deleted,unit_period_type)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.accountId,jdbcType=INTEGER},
        #{item.unitId,jdbcType=INTEGER},
        #{item.twoStageTime,jdbcType=TIMESTAMP},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.unitPeriodType,jdbcType=TINYINT},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      account_id = values(account_id),
      unit_id = values(unit_id),
      two_stage_time = values(two_stage_time),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
      unit_period_type = values(unit_period_type),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.adp.cpc.po.ad_data.OcpxAutoCompensationTimePo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ocpx_auto_compensation_time
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        account_id,
      </if>
      <if test="unitId != null">
        unit_id,
      </if>
      <if test="twoStageTime != null">
        two_stage_time,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="unitPeriodType != null">
        unit_period_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="unitId != null">
        #{unitId,jdbcType=INTEGER},
      </if>
      <if test="twoStageTime != null">
        #{twoStageTime,jdbcType=TIMESTAMP},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="unitPeriodType != null">
        #{unitPeriodType,jdbcType=TINYINT},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="accountId != null">
        account_id = values(account_id),
      </if>
      <if test="unitId != null">
        unit_id = values(unit_id),
      </if>
      <if test="twoStageTime != null">
        two_stage_time = values(two_stage_time),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="unitPeriodType != null">
        unit_period_type = values(unit_period_type),
      </if>
    </trim>
  </insert>
</mapper>