<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
                           http://www.springframework.org/schema/beans/spring-beans.xsd
                           http://www.springframework.org/schema/context
                           http://www.springframework.org/schema/context/spring-context.xsd">

    <context:component-scan base-package="com.bilibili.adp.cpc"/>

    <import resource="classpath:ad-stat-database-config.xml"/>
    <import resource="classpath:ad-data-read-database-config.xml"/>
    <import resource="classpath:ad-data-write-database-config.xml"/>
    <import resource="ad-business-cpc-database-conig.xml"/>
    <import resource="ad-aigc-read-database-config.xml"/>
</beans>