<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
  		http://www.springframework.org/schema/beans/spring-beans-3.1.xsd">

    <!-- ad_data只读库，无需事务 Begin-->
    <bean id="adAigcDataSource" class="com.mchange.v2.c3p0.ComboPooledDataSource">
        <property name="driverClass" value="${readonly.business_ad_aigc.jdbc.driver}"/>
        <property name="jdbcUrl" value="${readonly.business_ad_aigc.jdbc.url}"/>
        <property name="user" value="${readonly.business_ad_aigc.jdbc.username}"/>
        <property name="password" value="${readonly.business_ad_aigc.jdbc.password}"/>
        <property name="maxPoolSize" value="10"/>
        <property name="minPoolSize" value="1"/>
        <property name="maxIdleTime" value="7200"/>
        <property name="testConnectionOnCheckin" value="true"/>
        <property name="idleConnectionTestPeriod" value="5"/>
        <property name="preferredTestQuery" value="SELECT 1"/>
        <property name="checkoutTimeout" value="1800000"/>
    </bean>

    <bean id="adAigcSqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="adAigcDataSource"/>
        <property name="mapperLocations">
            <array>
                <value>classpath:mapper/ad_data_read/*.xml</value>
            </array>
        </property>
        <property name="plugins">
            <array>
                <ref bean="catExecutorMybatisPlugin"/>
            </array>
        </property>
    </bean>

    <bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="basePackage" value="com.bilibili.adp.cpc.dao.ad_aigc"/>
        <property name="sqlSessionFactoryBeanName" value="adAigcSqlSessionFactory"/>
    </bean>

    <bean id="catExecutorMybatisPlugin" class="com.bilibili.adp.cpc.plugin.CatExecutorMybatisPlugin"/>
</beans>
