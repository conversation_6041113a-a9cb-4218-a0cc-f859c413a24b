<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
  		http://www.springframework.org/schema/beans/spring-beans-3.1.xsd
  		http://www.springframework.org/schema/tx
        http://www.springframework.org/schema/tx/spring-tx-3.0.xsd">

    <bean id="adDataWriteDataSource" class="com.mchange.v2.c3p0.ComboPooledDataSource">
        <property name="driverClass" value="${ad.data.write.driver}"/>
        <property name="jdbcUrl" value="${ad.data.write.url}"/>
        <property name="user" value="${ad.data.write.username}"/>
        <property name="password" value="${ad.data.write.password}"/>
        <property name="maxPoolSize" value="10"/>
        <property name="minPoolSize" value="1"/>
        <property name="maxIdleTime" value="7200"/>
        <property name="testConnectionOnCheckin" value="true"/>
        <property name="idleConnectionTestPeriod" value="5"/>
        <property name="preferredTestQuery" value="SELECT 1"/>
        <property name="checkoutTimeout" value="1800000"/>
    </bean>

    <bean id="adDataWriteSqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="adDataWriteDataSource"/>
        <property name="mapperLocations">
            <array>
                <value>classpath:mapper/ad_data_write/*.xml</value>
            </array>
        </property>
        <property name="plugins">
            <array>
                <ref bean="catExecutorMybatisPlugin"/>
            </array>
        </property>
    </bean>

    <bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="basePackage" value="com.bilibili.adp.cpc.dao.ad_data_write"/>
        <property name="sqlSessionFactoryBeanName" value="adDataWriteSqlSessionFactory"/>
    </bean>

    <bean id="adDataTransactionManager"
          class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="adDataWriteDataSource"/>
    </bean>
    <bean id="catExecutorMybatisPlugin" class="com.bilibili.adp.cpc.plugin.CatExecutorMybatisPlugin"/>
</beans>