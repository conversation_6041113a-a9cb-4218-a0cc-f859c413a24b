package com.bilibili.adp.cpc.dao.querydsl.pos;

import javax.annotation.Generated;

/**
 * LauCustomerAgentCreativeMappingPo is a Querydsl bean type
 */
@Generated("com.querydsl.codegen.BeanSerializer")
public class LauCustomerAgentCreativeMappingPo {

    private Integer agentId;

    private Long avid;

    private Integer creativeAvidSource;

    private Integer creativeId;

    private java.sql.Timestamp ctime;

    private Integer customerId;

    private Integer id;

    private Integer isDeleted;

    private Long mid;

    private java.sql.Timestamp mtime;

    public Integer getAgentId() {
        return agentId;
    }

    public void setAgentId(Integer agentId) {
        this.agentId = agentId;
    }

    public Long getAvid() {
        return avid;
    }

    public void setAvid(Long avid) {
        this.avid = avid;
    }

    public Integer getCreativeAvidSource() {
        return creativeAvidSource;
    }

    public void setCreativeAvidSource(Integer creativeAvidSource) {
        this.creativeAvidSource = creativeAvidSource;
    }

    public Integer getCreativeId() {
        return creativeId;
    }

    public void setCreativeId(Integer creativeId) {
        this.creativeId = creativeId;
    }

    public java.sql.Timestamp getCtime() {
        return ctime;
    }

    public void setCtime(java.sql.Timestamp ctime) {
        this.ctime = ctime;
    }

    public Integer getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Integer customerId) {
        this.customerId = customerId;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Long getMid() {
        return mid;
    }

    public void setMid(Long mid) {
        this.mid = mid;
    }

    public java.sql.Timestamp getMtime() {
        return mtime;
    }

    public void setMtime(java.sql.Timestamp mtime) {
        this.mtime = mtime;
    }

    @Override
    public String toString() {
         return "agentId = " + agentId + ", avid = " + avid + ", creativeAvidSource = " + creativeAvidSource + ", creativeId = " + creativeId + ", ctime = " + ctime + ", customerId = " + customerId + ", id = " + id + ", isDeleted = " + isDeleted + ", mid = " + mid + ", mtime = " + mtime;
    }

}

