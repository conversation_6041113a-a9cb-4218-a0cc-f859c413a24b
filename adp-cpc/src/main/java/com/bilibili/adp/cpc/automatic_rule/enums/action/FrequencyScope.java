package com.bilibili.adp.cpc.automatic_rule.enums.action;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum FrequencyScope {
    DAILY(1, "每天", 0L),
    WEEKLY(2, "每周", 6L),
    ;
    private final int code;
    private final String desc;
    private final long days;

    public static long getDaysByCode(int code) {
        return Arrays.stream(values())
                .filter(frequencyScope -> frequencyScope.code == code)
                .findFirst()
                .map(FrequencyScope::getDays)
                .orElseThrow(IllegalArgumentException::new);
    }
}
