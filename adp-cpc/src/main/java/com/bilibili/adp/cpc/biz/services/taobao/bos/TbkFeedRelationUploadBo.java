package com.bilibili.adp.cpc.biz.services.taobao.bos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName TbkFeedRelationUploadBo
 * <AUTHOR>
 * @Date 2023/9/16 2:32 下午
 * @Version 1.0
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TbkFeedRelationUploadBo {
    private Integer accountId;
    private String accountName;
    private Long eventId;
    private Integer campaignId;
    private String campaignName;
    private Integer unitId;
    private String unitName;
    private Integer creativeId;
    private String creativeName;
    private Long videoId;
    private String promotionPurposeContent;
    private boolean isFlyArc;
}
