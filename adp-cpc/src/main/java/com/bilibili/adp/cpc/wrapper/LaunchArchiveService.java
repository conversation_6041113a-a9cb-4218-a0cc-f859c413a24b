package com.bilibili.adp.cpc.wrapper;

import com.bapis.archive.service.Arc;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.proxy.ArchiveServiceProxy;
import com.bilibili.adp.cpc.wrapper.bos.ArchiveWrapperBo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class LaunchArchiveService {

    @Resource
    private ArchiveServiceProxy archiveServiceProxy;


    public ArchiveWrapperBo getArchiveWrapper(Long avid) {
        if (!Utils.isPositive(avid)) return null;

        final Arc arc = archiveServiceProxy.arc(avid).getArc();
        return ArchiveWrapperBo.builder()
                .avid(avid)
                .mid(arc.getAuthor().getMid())
                .face(arc.getAuthor().getFace())
                .nickName(arc.getAuthor().getName())
                .cid(arc.getFirstCid())
                .title(arc.getTitle())
                .coverUrl(arc.getPic())
                .duration(arc.getDuration())
                .likeCount((long) arc.getStat().getLike())
                .playCount((long) arc.getStat().getView())
                .build();
    }
}
