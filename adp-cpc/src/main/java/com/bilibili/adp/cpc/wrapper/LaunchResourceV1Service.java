package com.bilibili.adp.cpc.wrapper;

import com.bapis.ad.location.*;
import com.bilibili.adp.account.dto.AccountDto;
import com.bilibili.adp.account.service.IQueryAccountService;
import com.bilibili.adp.common.enums.ChannelEnum;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.common.util.func.Functions;
import com.bilibili.adp.cpc.biz.services.account.config.AccountConfig;
import com.bilibili.adp.cpc.biz.services.recommend.dto.PopupInfoDto;
import com.bilibili.adp.cpc.core.LaunchCampaignService;
import com.bilibili.adp.cpc.core.LaunchUnitV1Service;
import com.bilibili.adp.cpc.core.constants.AdType;
import com.bilibili.adp.cpc.core.constants.AdvertisingMode;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauCampaignPo;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauUnitPo;
import com.bilibili.adp.cpc.enums.ad.PromotionPurposeType;
import com.bilibili.adp.cpc.utils.TimeUtils;
import com.bilibili.adp.cpc.wrapper.bos.ChannelAndSceneBo;
import com.bilibili.adp.resource.api.account.label.IAccountLabelService;
import com.bilibili.adp.resource.api.bus_mark_rule.IBusMarkRuleService;
import com.bilibili.adp.resource.api.common.BusMarkRuleAdSysEnum;
import com.bilibili.location.api.bus_mark.dto.BusMarkDto;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class LaunchResourceV1Service {
    private static final int UNDER_FRAME_SCENE = 10;
    private static final int SEARCH_SCENE = 7;

    private static final String AigcTitleGlobalOnboardingPopupKey
            = "show_aigc_title_global_onboarding_popup_%s";
    private static final String AigcTitleCreativePageTooltipCntKey
            = "show_aigc_title_creative_page_onboarding_tooltip_%s_cnt";
    private static final String AigcTitleCreativePageTooltipTodayShowKey
            = "show_aigc_title_creative_page_onboarding_tooltip_%s_today_show";
    private static final String AigcTitleFeatureUsedKey
            = "aigc_title_feature_used_%s";

    private static final int showAigcTitleGlobalPopupMarkType = 1;
    private static final int showAigcCreativePageTooltipMarkType = 2;
    private static final int useAigcTitleFeatureMarkType = 3;

    // FIXME 临时修复让线上成功记录redis，不频繁报弹窗，后面要把这个弹窗功能下掉
    private static final LocalDate popupEndDate = LocalDate.of(2025, 5, 15);


    @Value("#{'${story.slot_group_ids:290,291,330,331,357,358}'.split(',')}")
    private Set<Integer> storySlotGroupIds;
    @Value("#{'${story.bus_mark_ids:72,74}'.split(',')}")
    private Set<Integer> storyBusMarkIds;
    @Value("${story.fallback.bus_mark_id:72}")
    private Integer fallBackStoryBusMarkId;
    @Value("${underframe.bus_mark_id:79}")
    private Integer underframeBusMarkId;
    @Value("${content.fallback.bus_mark_id:123}")
    private Integer contentFallbackBusMarkId;
    private final LaunchUnitV1Service launchUnitV1Service;
    private final LaunchCampaignService launchCampaignService;
    private final IBusMarkRuleService busMarkRuleService;
    private final ResourceServiceGrpc.ResourceServiceBlockingStub resourceServiceBlockingStub;

    @Autowired
    private IQueryAccountService queryAccountService;
    @Autowired
    private AccountConfig accountConfig;
    @Autowired
    private IAccountLabelService accountLabelService;
    @Autowired
    private RedissonClient redissonClient;

    public LaunchResourceV1Service(LaunchUnitV1Service launchUnitV1Service,
                                   LaunchCampaignService launchCampaignService,
                                   IBusMarkRuleService busMarkRuleService,
                                   ResourceServiceGrpc.ResourceServiceBlockingStub resourceServiceBlockingStub) {
        this.launchUnitV1Service = launchUnitV1Service;
        this.launchCampaignService = launchCampaignService;
        this.busMarkRuleService = busMarkRuleService;
        this.resourceServiceBlockingStub = resourceServiceBlockingStub;
    }

    public Integer getContentFallbackBusMarkId() {
        return contentFallbackBusMarkId;
    }

    public boolean isUnderframe(Integer scene) {
        return Objects.equals(scene, UNDER_FRAME_SCENE);
    }

    public Map<Integer, String> getSceneMap() {
        return resourceServiceBlockingStub.listScenes(ListSceneRequest.newBuilder()
                        .addAllSlotGroupIds(Collections.emptyList())
                        .build())
                .getEntitiesList()
                .stream()
                .collect(Collectors.toMap(SceneEntity::getSceneId, SceneEntity::getSceneName, (x, y) -> y));
    }

    public List<ChannelAndSceneBo> listChannelAndScenes(Integer unitId, Integer isProgrammatic, Integer advertisingMode, boolean isMapiRequest) {
        final LauUnitPo unit = launchUnitV1Service.get(unitId);
        final LauCampaignPo campaign = launchCampaignService.get(unit.getCampaignId());
        int accountId = unit.getAccountId();
        final ResourceFilterRequest request = buildRequest(unit, Functions.integer2Boolean(isProgrammatic), false, campaign.getAdType(), advertisingMode, null, null);
        final FilterGivenTemplateIdsResponse response0 = resourceServiceBlockingStub.filterWithoutTemplateGroupIds(request);
        final List<Integer> slotGroupIds = response0.getEntitiesList()
                .stream()
                .map(ResourceEntity::getSlotGroupId)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(slotGroupIds)) return Collections.emptyList();

        final ListSceneResponse response1 = resourceServiceBlockingStub.listScenes(ListSceneRequest.newBuilder()
                .addAllSlotGroupIds(slotGroupIds)
                .build());
        final boolean isSearch = isSearch(campaign.getAdType());

        // https://www.tapd.cn/********/prong/stories/view/11********003122043
        // 账户一级行业为教育&必选账户&白名单内 同时满足时 才支持pc移动通投 这一创意的流量场景 产品 马珊大佬
        AccountDto accountDto = queryAccountService.getAccount(accountId);
        final boolean isEducation = Integer.valueOf(275).equals(accountDto.getCommerceCategoryFirstId());
        boolean hasPcMobileAllChannelLabel =
                accountLabelService.isAccountIdInLabel(accountId,
                        accountConfig.getMobilePcCreativeAllChannelLabelId());
        final boolean isEffectAd = Integer.valueOf(0).equals(accountDto.getAdStatus());
        boolean isSupportPcMobileAll = isEducation && hasPcMobileAllChannelLabel && isEffectAd;

        List<ChannelAndSceneBo> baseResult = response1.getEntitiesList()
                .stream()
                .filter(x -> isSearch == isSearchScene(x.getSceneId()))
                .collect(Collectors.groupingBy(SceneEntity::getChannelId))
                .values()
                .stream()
                .filter(x -> {
                    // 针对 pc通投场景 过滤
                    int channelId = x.get(0).getChannelId();
                    if (!ChannelEnum.PC_MOB_ALL.getCode().equals(channelId)) {
                        return true;
                    }
                    return isSupportPcMobileAll;
                })
                .map(x -> {
                    final SceneEntity sample = x.get(0);
                    return ChannelAndSceneBo.builder()
                            .channel(ChannelAndSceneBo.ChannelBo.builder()
                                    .channelId(sample.getChannelId())
                                    .channelName(sample.getChannelName())
                                    .build())
                            .scenes(x.stream()
                                    .sorted(Comparator.comparing(SceneEntity::getSceneId))
                                    .map(y -> ChannelAndSceneBo.SceneBo.builder()
                                            .sceneId(y.getSceneId())
                                            .sceneName(y.getSceneName())
                                            .build()).collect(Collectors.toList()))
                            .isValid(1)
                            .build();
                }).collect(Collectors.toList());

        // 追加了一个不能使用 只能看 告诉人家我们有通投这个选项了的逻辑 马珊
        long pcMobileAllChannelCount = baseResult.stream()
                .filter(result -> result.getChannel().getChannelId().equals(ChannelEnum.PC_MOB_ALL.getCode()))
                .count();
        if (!Utils.isPositive(pcMobileAllChannelCount)
                && isSupportPcMobileAll
                && !isMapiRequest
                && PromotionPurposeType.LANDING_PAGE.getCode() == unit.getPromotionPurposeType()
                && !AdvertisingMode.isNative(advertisingMode)) {
            ChannelAndSceneBo fakeInvalidShowResult = ChannelAndSceneBo.builder()
                    .channel(ChannelAndSceneBo.ChannelBo.builder()
                            .channelId(ChannelEnum.PC_MOB_ALL.getCode())
                            .channelName(ChannelEnum.PC_MOB_ALL.getDesc())
                            .build())
                    .scenes(Collections.emptyList())
                    .isValid(0)
                    .build();
            baseResult.add(fakeInvalidShowResult);
        }
        return baseResult;
    }

    public ResourceFilterRequest buildRequest(LauUnitPo unit,
                                              boolean isProgrammatic,
                                              boolean isPreferScene,
                                              Integer adType,
                                              Integer advertisingMode,
                                              Collection<Integer> scenes,
                                              Collection<Integer> templateGroupIds) {
        final ResourceFilterRequest.Builder builder = ResourceFilterRequest.newBuilder()
                .setAccountId(unit.getAccountId())
                .setPromotionPurposeType(unit.getPromotionPurposeType())
                .setSalesType(unit.getSalesType())
                .setIsCpa(Utils.isPositive(unit.getOcpcTarget()))
                .setIsPreferScene(isPreferScene)
                .setIsProgrammatic(isProgrammatic)
                .addAllTemplateGroupIds(Optional.ofNullable(templateGroupIds).orElse(Collections.emptyList()))
                .setAdvertisingMode(advertisingMode)
                .addAllScenes(Optional.ofNullable(scenes).orElse(Collections.emptyList()));
        if (isSearch(adType)) {
            builder.addAllScenes(Collections.singletonList(SEARCH_SCENE));
        }
        return builder.build();
    }

    public boolean isSearchScene(Integer x) {
        return Objects.equals(SEARCH_SCENE, x);
    }

    public boolean isSearch(Integer x) {
        return Objects.equals(AdType.SEARCH, x);
    }

    public boolean isStory(Integer slotGroupId) {
        return this.storySlotGroupIds.contains(slotGroupId);
    }

    public Integer getUnderframeBusMarkId() {
        return underframeBusMarkId;
    }

    public Integer getStoryBusMarkId(Integer accountId) {
        return busMarkRuleService.getValidBusMarkInAccount(accountId, BusMarkRuleAdSysEnum.CPC).stream()
                .map(BusMarkDto::getId)
                .filter(x -> storyBusMarkIds.contains(x))
                .sorted()
                .findAny()
                .orElse(fallBackStoryBusMarkId);
    }

    public PopupInfoDto getPopupInfo(Integer accountId, Integer type, Integer platform) {
        PopupInfoDto popInfoDto = PopupInfoDto.builder()
                .build();
        if (Objects.isNull(type)) {
            return popInfoDto;
        }
        try {
            if (type == 1) {
                platform = Objects.isNull(platform) ? 0 : platform;
                popInfoDto.setShow_ai_title_suggest_popup(showAiTitleSuggestPopup(accountId, platform));
            }
        } catch (Exception e) {
            log.error("getPopupInfo error", e);
        }
        return popInfoDto;
    }


    private static final long showAiTitleSuggestPopupTime = 1772350413000L;
    private static final String showAiTitleSuggestPopupKey = "show_ai_title_suggest_popup";

    private static final LocalDate showAiTitleSuggestPopupEndDate = LocalDate.of(2026, 3, 2);

    private boolean showAiTitleSuggestPopup(Integer accountId, Integer platform) {
        // 大于2027-03-01则不需要弹窗
        if (TimeUtils.nowTimestamp().getTime() >= showAiTitleSuggestPopupTime) {
            return false;
        }

        String key = showAiTitleSuggestPopupKey + accountId + "-" + platform;
        Integer count = getIntValue(key);

        RAtomicLong rBucket = redissonClient.getAtomicLong(key);
        if (!rBucket.isExists()) {
            rBucket.set(2L);
            rBucket.expire(ChronoUnit.DAYS.between(LocalDate.now(), showAiTitleSuggestPopupEndDate), TimeUnit.DAYS);
            return true;
        }

        if (rBucket.get() > 0) {
            rBucket.decrementAndGet();
            return true;
        }

        return false;
    }

    public void markPopup(Integer accountId, Integer markType) {
        switch (markType) {
            case showAigcTitleGlobalPopupMarkType:
                String key = String.format(AigcTitleGlobalOnboardingPopupKey, accountId);
                RBucket<Boolean> bucket = redissonClient.getBucket(key);
                bucket.set(true, ChronoUnit.DAYS.between(LocalDate.now(), popupEndDate), TimeUnit.DAYS);
                break;
            case showAigcCreativePageTooltipMarkType:
                String key1 = String.format(AigcTitleCreativePageTooltipTodayShowKey, accountId);
                String key2 = String.format(AigcTitleCreativePageTooltipCntKey, accountId);
                if (getBooleanValue(String.format(AigcTitleFeatureUsedKey, accountId))) {
                    log.info("already used aigc title feature.");
                    return;
                }

                RBucket<Boolean> bucket1 = redissonClient.getBucket(key1);
                if (bucket1.isExists() && bucket1.get()) {
                    log.info("today has shown, no need to mark.");
                    return;
                }
                LocalDateTime now = LocalDateTime.now();
                bucket1.set(true, Duration.between(now, now.toLocalDate().atTime(LocalTime.MAX)).getSeconds(), TimeUnit.SECONDS);

                RAtomicLong atomicLong = redissonClient.getAtomicLong(key2);
                if (!atomicLong.isExists()) {
                    atomicLong.set(0);
                    atomicLong.expire(6 * 31, TimeUnit.DAYS);
                }
                if (atomicLong.isExists() && atomicLong.get() >= 3) {
                    log.info("has shown over 3 times.");
                    return;
                }
                long newValue = atomicLong.incrementAndGet();
                log.info(String.format("[markPopup] key:%s, value:%d", key2, newValue));
                break;
            case useAigcTitleFeatureMarkType:
                String k = String.format(AigcTitleFeatureUsedKey, accountId);
                RBucket<Boolean> b = redissonClient.getBucket(k);
                b.set(true, ChronoUnit.DAYS.between(LocalDate.now(), popupEndDate), TimeUnit.DAYS);
                break;
        }
    }

    private int getIntValue(String key) {
        RAtomicLong atomicLong = redissonClient.getAtomicLong(key);
        if (!atomicLong.isExists()) {
            return 0;
        }
        // 将字符串类型的值转换为 int
        return (int) atomicLong.get();
    }

    private boolean getBooleanValue(String key) {
        RBucket<Boolean> bucket = redissonClient.getBucket(key);
        Boolean value = bucket.get();
        return value != null ? value : false;
    }

}
