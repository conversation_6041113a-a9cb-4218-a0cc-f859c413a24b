package com.bilibili.adp.cpc.databus.bos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 原生创意相关性审核消息体
 *
 * <AUTHOR>
 * @date 2024/3/13 14:29
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NativeCreativeRelativityAuditDatabusMessage {

    private List<NativeMsgItem> items;

    /**
     * 类型，默认稿件
     * @see com.bapis.ad.audit.NativeBodyType
     */
    private Integer type;
}
