package com.bilibili.adp.cpc.dao.querydsl;

import com.bilibili.adp.cpc.dao.querydsl.pos.LauAutomaticRuleObjectPo;
import com.querydsl.core.types.Path;
import com.querydsl.core.types.PathMetadata;
import com.querydsl.core.types.dsl.DateTimePath;
import com.querydsl.core.types.dsl.NumberPath;
import com.querydsl.sql.ColumnMetadata;

import javax.annotation.Generated;
import java.sql.Types;

import static com.querydsl.core.types.PathMetadataFactory.forVariable;




/**
 * QLauAutomaticRuleObject is a Querydsl query type for LauAutomaticRuleObjectPo
 */
@Generated("com.querydsl.sql.codegen.MetaDataSerializer")
public class QLauAutomaticRuleObject extends com.querydsl.sql.RelationalPathBase<LauAutomaticRuleObjectPo> {

    private static final long serialVersionUID = **********;

    public static final QLauAutomaticRuleObject lauAutomaticRuleObject = new QLauAutomaticRuleObject("lau_automatic_rule_object");

    public final NumberPath<Integer> accountId = createNumber("accountId", Integer.class);

    public final DateTimePath<java.sql.Timestamp> ctime = createDateTime("ctime", java.sql.Timestamp.class);

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final NumberPath<Integer> isDeleted = createNumber("isDeleted", Integer.class);

    public final DateTimePath<java.sql.Timestamp> mtime = createDateTime("mtime", java.sql.Timestamp.class);

    public final NumberPath<Integer> objectId = createNumber("objectId", Integer.class);

    public final NumberPath<Integer> objectType = createNumber("objectType", Integer.class);

    public final NumberPath<Long> ruleId = createNumber("ruleId", Long.class);

    public final com.querydsl.sql.PrimaryKey<LauAutomaticRuleObjectPo> primary = createPrimaryKey(id);

    public QLauAutomaticRuleObject(String variable) {
        super(LauAutomaticRuleObjectPo.class, forVariable(variable), "null", "lau_automatic_rule_object");
        addMetadata();
    }

    public QLauAutomaticRuleObject(String variable, String schema, String table) {
        super(LauAutomaticRuleObjectPo.class, forVariable(variable), schema, table);
        addMetadata();
    }

    public QLauAutomaticRuleObject(String variable, String schema) {
        super(LauAutomaticRuleObjectPo.class, forVariable(variable), schema, "lau_automatic_rule_object");
        addMetadata();
    }

    public QLauAutomaticRuleObject(Path<? extends LauAutomaticRuleObjectPo> path) {
        super(path.getType(), path.getMetadata(), "null", "lau_automatic_rule_object");
        addMetadata();
    }

    public QLauAutomaticRuleObject(PathMetadata metadata) {
        super(LauAutomaticRuleObjectPo.class, metadata, "null", "lau_automatic_rule_object");
        addMetadata();
    }

    public void addMetadata() {
        addMetadata(accountId, ColumnMetadata.named("account_id").withIndex(2).ofType(Types.INTEGER).withSize(10).notNull());
        addMetadata(ctime, ColumnMetadata.named("ctime").withIndex(7).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(id, ColumnMetadata.named("id").withIndex(1).ofType(Types.BIGINT).withSize(20).notNull());
        addMetadata(isDeleted, ColumnMetadata.named("is_deleted").withIndex(6).ofType(Types.TINYINT).withSize(3).notNull());
        addMetadata(mtime, ColumnMetadata.named("mtime").withIndex(8).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(objectId, ColumnMetadata.named("object_id").withIndex(5).ofType(Types.INTEGER).withSize(10).notNull());
        addMetadata(objectType, ColumnMetadata.named("object_type").withIndex(4).ofType(Types.TINYINT).withSize(3).notNull());
        addMetadata(ruleId, ColumnMetadata.named("rule_id").withIndex(3).ofType(Types.BIGINT).withSize(20).notNull());
    }

}

