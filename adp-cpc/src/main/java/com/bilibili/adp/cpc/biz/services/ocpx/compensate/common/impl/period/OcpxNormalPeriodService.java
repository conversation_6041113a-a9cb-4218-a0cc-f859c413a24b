package com.bilibili.adp.cpc.biz.services.ocpx.compensate.common.impl.period;

import com.bilibili.adp.common.util.GsonUtils;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.biz.services.ocpx.compensate.common.IOcpxAutoPayService;
import com.bilibili.adp.cpc.biz.services.ocpx.compensate.common.IOcpxAutoPayStatusService;
import com.bilibili.adp.cpc.biz.services.ocpx.compensate.common.IOcpxAutoPayTimeService;
import com.bilibili.adp.cpc.biz.services.ocpx.compensate.common.IPeriodService;
import com.bilibili.adp.cpc.biz.services.ocpx.compensate.common.dto.*;
import com.bilibili.adp.cpc.enums.ocpx.OcpxAutoPayPeriod;
import com.bilibili.adp.cpc.enums.ocpx.UnitPeriodTypeEnum;
import com.bilibili.adp.launch.api.common.OcpxAutoPayFloatStatus;
import com.bilibili.adp.launch.api.launch.dto.OcpxAutoPayStatusDto;
import com.bilibili.adp.launch.api.launch.dto.OcpxAutoPayTimeDto;
import com.google.common.collect.Lists;
import io.vavr.Function4;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.bilibili.adp.cpc.biz.services.ocpx.compensate.common.impl.period.CommonPeriodService.HAS_PAID;
import static com.bilibili.adp.cpc.biz.services.ocpx.compensate.common.impl.period.CommonPeriodService.NOT_PAID;

@Service
public class OcpxNormalPeriodService implements IPeriodService<OcpxAutoPayPeriod> {

    @Resource
    private CommonPeriodService commonPeriodService;
    @Resource
    private IOcpxAutoPayTimeService ocpxAutoPayTimeService;
    @Resource
    private IOcpxAutoPayStatusService ocpxAutoPayStatusService;
    @Resource
    private IOcpxAutoPayService ocpxAutoPayService;

    private final Logger JOB_LOGGER = LoggerFactory.getLogger(getClass());

    public Pair<Date, Date> getDaysAfterLastPeriod(Timestamp startTime) {
        // 比如今天是2022年10月20日
        // 返回结果 datePair = <10-3,10-5>
        return Pair.of(Utils.getBeginOfDay(Utils.getSomeDayAgo(startTime, 17)), Utils.getEndOfDay(Utils.getSomeDayAgo(startTime, 15)));
    }

    public Pair<Date, Date> startAndEndDay(OcpxAutoPayPeriod period,Timestamp startTime) {
        switch (period) {
            case PERIOD1:
                return Pair.of(Utils.getBeginOfDay(Utils.getSomeDayAgo(startTime, 2)), Utils.getEndOfDay(Utils.getSomeDayAgo(startTime, 0)));
            case PERIOD2:
                return Pair.of(Utils.getBeginOfDay(Utils.getSomeDayAgo(startTime, 5)), Utils.getEndOfDay(Utils.getSomeDayAgo(startTime, 3)));
            case PERIOD3:
                return Pair.of(Utils.getBeginOfDay(Utils.getSomeDayAgo(startTime, 8)), Utils.getEndOfDay(Utils.getSomeDayAgo(startTime, 6)));
            case PERIOD4:
                return Pair.of(Utils.getBeginOfDay(Utils.getSomeDayAgo(startTime, 11)), Utils.getEndOfDay(Utils.getSomeDayAgo(startTime, 9)));
            case PERIOD5:
                return Pair.of(Utils.getBeginOfDay(Utils.getSomeDayAgo(startTime, 14)), Utils.getEndOfDay(Utils.getSomeDayAgo(startTime, 12)));
            default:
                throw new IllegalArgumentException("不支持的周期: " + period);
        }
    }

    private Pair<Timestamp,OcpxAutoPayStatusDto> generateOcpxAutoPayStatusDtoByPeriod(OcpxAutoPayTimeDto dto,OcpxAutoPayPeriod period){
        // 根据two_stage_time和周期数，得到这个周期的开始结束时间（x～x+2）
        Timestamp thisPeriodBeginTime = Utils.getSomeDayAfter(new Timestamp(dto.getTwoStageTime().getTime()),3 * (period.getCode() - 1) );
        Timestamp thisPeriodEndTime = Utils.getSomeDayAfter(thisPeriodBeginTime,2);
        // 若生成周期结束时间 <= 10.15，尝试退款，并根据退款状态新增OcpxAutoPayStatusDto
        if(thisPeriodEndTime.getTime() <= Utils.getSomeDayBeforeToday(5).getTime()){
            return Pair.of(thisPeriodEndTime, OcpxAutoPayStatusDto.builder()
                    .accountId(dto.getAccountId())
                    .unitId(dto.getUnitId())
                    .period(period.getCode())
                    .status(OcpxAutoPayFloatStatus.IS_CONFIRMING.getCode())
                    .isPaid(NOT_PAID)
                    .build());
        } else {
            // 若10.16 <= 周期结束时间为 <= 10.19，之前的OcpxAutoPayStatusDto不变
            return Pair.of(new Timestamp(0), OcpxAutoPayStatusDto.builder()
                    .accountId(dto.getAccountId())
                    .unitId(dto.getUnitId())
                    .period(period.getCode())
                    .status(OcpxAutoPayFloatStatus.IS_CONFIRMING.getCode())
                    .isPaid(NOT_PAID)
                    .build());
        }
    }

    private List<OcpxAutoPayStatusDto> doInitP1AndP2(List<OcpxAutoPayTimeDto> autoPayTimeDtosByPeriod, OcpxAutoPayPeriod period, Boolean afterLastPeriod) {
        List<Integer> unitIds = autoPayTimeDtosByPeriod.stream().map(OcpxAutoPayTimeDto::getUnitId)
                .distinct()
                .collect(Collectors.toList());
        List<OcpxAutoPayStatusDto> autoPayStatusList = ocpxAutoPayStatusService.findAutoPayStatusByUnitIdList(unitIds);

        // autoPayStausMapByUnitIdAndStatus 可能为空（最初的时候）
        Map<Integer, Map<Integer, List<OcpxAutoPayStatusDto>>> autoPayStausMapByUnitIdAndStatus =
                autoPayStatusList.stream().collect(Collectors.groupingBy(OcpxAutoPayStatusDto::getUnitId, Collectors.groupingBy(OcpxAutoPayStatusDto::getPeriod)));
        return initP1AndP2.apply(autoPayTimeDtosByPeriod, autoPayStausMapByUnitIdAndStatus, period, afterLastPeriod);
    }

    /**
     * 对于刚进入某个周期的单元，状态都设置为 OcpxAutoPayFloatStatus.IS_PROTECTING
     * 对于进入非一周期的单元，需要将上个周期设置为 OcpxAutoPayFloatStatus.IS_CONFIRMING
     */
    private final Function4<List<OcpxAutoPayTimeDto>, Map<Integer, Map<Integer, List<OcpxAutoPayStatusDto>>>, OcpxAutoPayPeriod, Boolean, List<OcpxAutoPayStatusDto>> initP1AndP2 =
            (autoPayTimeDtoList, originAutoPayStatusDtoMap, period, afterLastPeriod) -> {
                List<OcpxAutoPayStatusDto> result = new ArrayList<>();
                // autoPayTimeDtoList是指的相对今天，进入第x周期的单元（看今天和two_stage_time的差值）
                // 第x周期状态改为"成本保障生效中"，第x-1周期状态（x > 1）改为"成本保障确认中"
                autoPayTimeDtoList.forEach(dto -> {
                    if (!afterLastPeriod) {
                        OcpxAutoPayStatusDto autoPayStatusDto;
                        // 除了第一期外，还要更新上一周期的状态
                        if (!OcpxAutoPayPeriod.PERIOD1.equals(period)) {
                            Integer lastPeriodCode = OcpxAutoPayPeriod.getPrePeriod(period).getCode();
                            List<OcpxAutoPayStatusDto> autoPayStatusDtoWithLastPList = originAutoPayStatusDtoMap.getOrDefault(dto.getUnitId(), new HashMap<>()).getOrDefault(lastPeriodCode, new ArrayList<>());
                            // 如果上一周期不存在状态，就说明是中途进入的，直接过滤
                            if (CollectionUtils.isEmpty(autoPayStatusDtoWithLastPList)
                                    || (autoPayStatusDto = autoPayStatusDtoWithLastPList.get(0)) == null) {
                                return;
                            }
                            // 如果上一周期的状态不是 OcpxAutoPayFloatStatus.IS_CONFIRMING，则更改
                            if (!Objects.equals(autoPayStatusDto.getStatus(), OcpxAutoPayFloatStatus.IS_CONFIRMING.getCode())) {
                                result.add(OcpxAutoPayStatusDto.builder()
                                        .accountId(dto.getAccountId())
                                        .unitId(dto.getUnitId())
                                        .period(lastPeriodCode)
                                        .status(OcpxAutoPayFloatStatus.IS_CONFIRMING.getCode())
                                        .isPaid(NOT_PAID)
                                        .build());
                            }
                        }
                        // 更改本周期的状态为 OcpxAutoPayFloatStatus.IS_PROTECTING
                        List<OcpxAutoPayStatusDto> autoPayStatusDtoList = originAutoPayStatusDtoMap.getOrDefault(dto.getUnitId(), new HashMap<>()).getOrDefault(period.getCode(), new ArrayList<>());
                        if (CollectionUtils.isEmpty(autoPayStatusDtoList)
                                || (autoPayStatusDto = autoPayStatusDtoList.get(0)) == null
                                || !Objects.equals(autoPayStatusDto.getStatus(), OcpxAutoPayFloatStatus.IS_PROTECTING.getCode())) {
                            result.add(OcpxAutoPayStatusDto.builder()
                                    .accountId(dto.getAccountId())
                                    .unitId(dto.getUnitId())
                                    .period(period.getCode())
                                    .status(OcpxAutoPayFloatStatus.IS_PROTECTING.getCode())
                                    .isPaid(NOT_PAID)
                                    .build());
                        }
                    } else {
                        // 如果是最后一个周期之后的3天内，则直接更改最后一个周期的状态
                        OcpxAutoPayStatusDto autoPayStatusDto;
                        List<OcpxAutoPayStatusDto> autoPayStatusDtoList = originAutoPayStatusDtoMap.getOrDefault(dto.getUnitId(), new HashMap<>()).getOrDefault(period.getCode(), new ArrayList<>());
                        if (CollectionUtils.isEmpty(autoPayStatusDtoList)
                                || (autoPayStatusDto = autoPayStatusDtoList.get(0)) == null) {
                            return;
                        }

                        if (!Objects.equals(autoPayStatusDto.getStatus(), OcpxAutoPayFloatStatus.IS_CONFIRMING.getCode())) {
                            result.add(OcpxAutoPayStatusDto.builder()
                                    .accountId(dto.getAccountId())
                                    .unitId(dto.getUnitId())
                                    .period(period.getCode())
                                    .status(OcpxAutoPayFloatStatus.IS_CONFIRMING.getCode())
                                    .isPaid(NOT_PAID)
                                    .build());
                        }
                    }
                });

                return result;
            };

    @Override
    public void handDelayedData(Timestamp startTime,boolean write) {
        // 今天是2022年10月20日,数据的表ocpx_auto_compensation会产出log_date=2022-10-17 00:00:00的数据（即10.15~10.17的应赔数据）
        // 主流程会查10.14~10.16是第1，2，3，4，5周期的应赔信息（10.19产出log_date=2022-10-16 00:00:00的数据）
        // 所有这里需要根据单元，查log_date <= 2022-10-15 00:00:00的数据，没赔过则进行赔付

        Timestamp ctimeStart = startTime;
        Timestamp ctimeEnd = startTime;
        Timestamp threeDayBeforeToday = Utils.getSomeDayBeforeToday(3);
        List<OcpxAutoPayTimeDto> ocpxAutoPayTimeDtos = ocpxAutoPayTimeService.findAllAutoPayTimeDtosByField(ctimeStart,ctimeEnd,
                threeDayBeforeToday,UnitPeriodTypeEnum.COMMON.getCode());
        // 黑白名单过滤
        ocpxAutoPayTimeDtos = commonPeriodService.filterWithBlackAndWhiteList(ocpxAutoPayTimeDtos);

        JOB_LOGGER.info("handDelayedData ocpxAutoPayTimeDtos size={}", ocpxAutoPayTimeDtos.size());

        if(CollectionUtils.isEmpty(ocpxAutoPayTimeDtos)){
            return;
        }
        // 找到没有第1周期状态的单元
        List<Integer> unitIds = ocpxAutoPayTimeDtos.stream().map(o->o.getUnitId()).collect(Collectors.toList());
        List<Integer> hasPeriod1UnitIds = ocpxAutoPayStatusService.findAutoPayStatusByUnitIdListAndPeriod(unitIds,1)
                .stream().map(o->o.getUnitId()).collect(Collectors.toList());
        unitIds.removeAll(hasPeriod1UnitIds);
        List<OcpxAutoPayTimeDto> notHasPeriod1UnitIdInfos = ocpxAutoPayTimeDtos.stream().filter(o->unitIds.contains(o.getUnitId())).collect(Collectors.toList());
        List<OcpxAutoPayStatusDto> result = new ArrayList<>();
        List<Pair<Timestamp,OcpxAutoPayStatusDto>> needRefundDtos = new ArrayList<>();

        JOB_LOGGER.info("handDelayedData notHasPeriod1UnitIdInfos size={}", notHasPeriod1UnitIdInfos.size());

        // 今天是2022年10月20日,
        // 不延迟，twoStageTime = 10.19，原功能可以存第1周期数据
        // 延迟1天，twoStageTime = 10.18，原功能可以存第1周期数据
        // 延迟2～4天，10.15 <= twoStageTime <= 10.17，需要补偿存第1周期数据
        // 延迟5～7天，10.12 <= twoStageTime <= 10.14，需要补偿存第1,2周期数据
        // 延迟8～10天，10.9 <= twoStageTime <= 10.11，需要补偿存第1,2,3周期数据
        // 延迟11～13天，10.6 <= twoStageTime <= 10.8，需要补偿存第1,2,3,4周期数据
        // 延迟14天以上，twoStageTime <= 10.5，需要补偿存第1,2,3,4,5周期数据
        for(OcpxAutoPayTimeDto dto : notHasPeriod1UnitIdInfos){
            // 延迟2天及以上，twoStageTime <= 10.17，需要补偿存第1周期数据
            if(dto.getTwoStageTime().getTime() <= Utils.getSomeDayBeforeToday(3).getTime()){
                Pair<Timestamp,OcpxAutoPayStatusDto> pair = this.generateOcpxAutoPayStatusDtoByPeriod(dto,OcpxAutoPayPeriod.PERIOD1);
                result.add(pair.getSecond());
                if(pair.getFirst().getTime() != 0) {
                    needRefundDtos.add(pair);
                }
            }
            // 延迟5天及以上，twoStageTime <= 10.14，需要补偿存第2周期数据
            if(dto.getTwoStageTime().getTime() <= Utils.getSomeDayBeforeToday(6).getTime()){
                Pair<Timestamp,OcpxAutoPayStatusDto> pair = this.generateOcpxAutoPayStatusDtoByPeriod(dto,OcpxAutoPayPeriod.PERIOD2);
                result.add(pair.getSecond());
                if(pair.getFirst().getTime() != 0) {
                    needRefundDtos.add(pair);
                }
            }
            // 延迟8天及以上，twoStageTime <= 10.11，需要补偿存第3周期数据
            if(dto.getTwoStageTime().getTime() <= Utils.getSomeDayBeforeToday(9).getTime()){
                Pair<Timestamp,OcpxAutoPayStatusDto> pair = this.generateOcpxAutoPayStatusDtoByPeriod(dto,OcpxAutoPayPeriod.PERIOD3);
                result.add(pair.getSecond());
                if(pair.getFirst().getTime() != 0) {
                    needRefundDtos.add(pair);
                }
            }
            // 延迟11天及以上，twoStageTime <= 10.8，需要补偿存第4周期数据
            if(dto.getTwoStageTime().getTime() <= Utils.getSomeDayBeforeToday(12).getTime()){
                Pair<Timestamp,OcpxAutoPayStatusDto> pair = this.generateOcpxAutoPayStatusDtoByPeriod(dto,OcpxAutoPayPeriod.PERIOD4);
                result.add(pair.getSecond());
                if(pair.getFirst().getTime() != 0) {
                    needRefundDtos.add(pair);
                }
            }
            // 延迟14天及以上，twoStageTime <= 10.5，需要补偿存第5周期数据
            if(dto.getTwoStageTime().getTime() <= Utils.getSomeDayBeforeToday(15).getTime()){
                Pair<Timestamp,OcpxAutoPayStatusDto> pair = this.generateOcpxAutoPayStatusDtoByPeriod(dto,OcpxAutoPayPeriod.PERIOD5);
                result.add(pair.getSecond());
                if(pair.getFirst().getTime() != 0) {
                    needRefundDtos.add(pair);
                }
            }
        }

        JOB_LOGGER.info("handDelayedData result size={}", result.size());
        JOB_LOGGER.info("handDelayedData needRefundDtos size={}", needRefundDtos.size());

        if(write) {
            // 新增状态为"成本保障确认中"的数据
            ocpxAutoPayStatusService.saveAutoPayStatus(result);
            // 需要退款的尝试退款，并更新状态
            commonPeriodService.doRefund(needRefundDtos);
        }
    }

    @Override
    public void handleP1P2Status(Timestamp startTime, List<OcpxAutoPayTimeDto> delayedPayDataList) {
        List<OcpxAutoPayStatusDto> needUpdateStatusWithP1P2List = new ArrayList<>();
        for (OcpxAutoPayPeriod period : OcpxAutoPayPeriod.values()) {
            // 比如今天是2022年10月20日
            // 第1周期 datePair = <10-18,10-20>
            // 第2周期 datePair = <10-15,10-17>
            // 第3周期 datePair = <10-12,10-14>
            // 第4周期 datePair = <10-9,10-11>
            // 第5周期 datePair = <10-6,10-8>
            Pair<Date, Date> datePair = this.startAndEndDay(period, startTime);
            // 查ocpx_auto_compensation_time表（数据组维护的表），two_stage_time在dataPair区间内的数据,
            // 比如，第一周期，查询two_stage_time在[2022-10-18 00:00:00,2022-10-20 23:59:59]区间内的数据,
            List<OcpxAutoPayTimeDto> autoPayTimeDtosByPeriod = ocpxAutoPayTimeService.findAllAutoPayTimeDtosByDaysAndUnitPeriod(
                    datePair.getFirst(), datePair.getSecond(), UnitPeriodTypeEnum.COMMON.getCode());
            // 加入延迟的赔付数据
            if (!CollectionUtils.isEmpty(delayedPayDataList)) {
                Date periodStartTime = datePair.getFirst();
                autoPayTimeDtosByPeriod.addAll(delayedPayDataList.stream().filter(o -> periodStartTime.compareTo(o.getTwoStageTime()) > 0)
                        .collect(Collectors.toList()));
            }
            // 黑白名单过滤
            autoPayTimeDtosByPeriod = commonPeriodService.filterWithBlackAndWhiteList(autoPayTimeDtosByPeriod);
            List<OcpxAutoPayStatusDto> ocpxAutoPayStatusDtos = doInitP1AndP2(autoPayTimeDtosByPeriod, period, false);
            // 放入需要更新的p1p2列表，可能是1(add),1(add)2(update),
            // 2(add)3(update),3(add)4(update),4(add)5(update)这些周期组合
            needUpdateStatusWithP1P2List.addAll(ocpxAutoPayStatusDtos);
            JOB_LOGGER.info("add or update status size of period {} : {}", period.getCode(), ocpxAutoPayStatusDtos.size());
        }

        // 比如今天是2022年10月20日，返回结果 datePair = <10-3,10-5>
        Pair<Date, Date> daysAfterLastPeriod = this.getDaysAfterLastPeriod(startTime);
        // 查出twoStageTime在[10-3 00:00:00,10-5 23:59:59]的记录
        List<OcpxAutoPayTimeDto> autoPayTimeDtoListAfterLastPeriod =
                ocpxAutoPayTimeService.findAllAutoPayTimeDtosByDaysAndUnitPeriod(daysAfterLastPeriod.getFirst(),
                        daysAfterLastPeriod.getSecond(),UnitPeriodTypeEnum.COMMON.getCode());
        // 黑白名单过滤
        autoPayTimeDtoListAfterLastPeriod =  commonPeriodService.filterWithBlackAndWhiteList(autoPayTimeDtoListAfterLastPeriod);
        List<OcpxAutoPayStatusDto> ocpxAutoPayStatusDtosWithLastPeriod = this.doInitP1AndP2(autoPayTimeDtoListAfterLastPeriod, OcpxAutoPayPeriod.getLastPeriod(), true);
        // 放入需要更新的p1p2列表，是5(update)
        needUpdateStatusWithP1P2List.addAll(ocpxAutoPayStatusDtosWithLastPeriod);
        JOB_LOGGER.info("add or update status size of period {} : {}", OcpxAutoPayPeriod.getLastPeriod().getCode(), ocpxAutoPayStatusDtosWithLastPeriod.size());

        // 保存初始化2个周期的数据
        ocpxAutoPayStatusService.saveAutoPayStatus(needUpdateStatusWithP1P2List);
    }

    @Override
    public boolean handlePayAndStatus(Timestamp startTime, List<OcpxAutoPayTimeDto> delayedPayDataList) {
        boolean needSendWechat = false;

        // 比如今天是2022年10月20日,数据的表ocpx_auto_compensation会产出log_date=2022-10-17 00:00:00的数据（即10.15~10.17的应赔数据）
        // 10.14进入二阶段的单元，即10.14~10.16是第1周期，查这个周期的应赔信息(10.19产出)
        // 10.11进入二阶段的单元，即10.14~10.16是第2周期，查这个周期的应赔信息(10.19产出)
        // 10.8进入二阶段的单元，即10.14~10.16是第3周期，查这个周期的应赔信息(10.19产出)
        // 10.5进入二阶段的单元，即10.14~10.16是第4周期，查这个周期的应赔信息(10.19产出)
        // 10.2进入二阶段的单元，即10.14~10.16是第5周期，查这个周期的应赔信息(10.19产出)
        for (OcpxAutoPayPeriod period : OcpxAutoPayPeriod.values()) {
            Integer date = period.getDate();

            List<OcpxAutoPayStatusDto> needBeUpdatedStatusDtoList = new ArrayList<>();
            List<OcpxAutoCompensationDto> needBeUpdatedPayDtoList = new ArrayList<>();
            Timestamp twoStageTimeEnd = Utils.getSomeDayAgo(startTime, date);
            List<OcpxAutoPayTimeDto> allAutoPayTimeDtosByDay = ocpxAutoPayTimeService.findAllAutoPayTimeDtosByTimeAndUnitPeriod(
                    twoStageTimeEnd, UnitPeriodTypeEnum.COMMON.getCode());
            // 加入延迟的赔付数据
            if (!CollectionUtils.isEmpty(delayedPayDataList)) {
                allAutoPayTimeDtosByDay.addAll(delayedPayDataList.stream().filter(o -> twoStageTimeEnd.compareTo(o.getTwoStageTime()) > 0)
                        .collect(Collectors.toList()));
            }
            // 黑白名单过滤
            allAutoPayTimeDtosByDay = commonPeriodService.filterWithBlackAndWhiteList(allAutoPayTimeDtosByDay);
            List<Integer> autoPayTimeUnitIdsByDay = allAutoPayTimeDtosByDay.stream().map(OcpxAutoPayTimeDto::getUnitId)
                    .distinct()
                    .collect(Collectors.toList());
            // 赔付状态表（投放端的表ocpx_auto_compensation_status）
            List<OcpxAutoPayStatusDto> autoPayStatusByUnitIdsAndPeriod = ocpxAutoPayStatusService.findAutoPayStatusByUnitIdListAndPeriod(autoPayTimeUnitIdsByDay, period.getCode());
            // 赔付源表未送达的数据（数据的表ocpx_auto_compensation）
            List<OcpxAutoCompensationDto> autoPayByUnitIdsAndPeriod = ocpxAutoPayService.getOcpxAutoPayDtoListByUnitIdsAndPeriod(autoPayTimeUnitIdsByDay, period.getCode(), commonPeriodService.NEWEST_VERSION,
                    commonPeriodService.NOT_SENT);
            // （单元，赔付状态ocpx_auto_compensation_status）
            Map<Integer, OcpxAutoPayStatusDto> autoPayStatusByUnitIdMap = autoPayStatusByUnitIdsAndPeriod.stream().collect(Collectors.toMap(OcpxAutoPayStatusDto::getUnitId, Function.identity()));

            // key -> accountId
            Map<Integer, List<Pair<OcpxAutoCompensationDto, OcpxAutoPayStatusDto>>> beSentToCrmMap = new HashMap<>();

            // 遍历赔付源表
            autoPayByUnitIdsAndPeriod.forEach(autoPayDto -> {
                Integer unitId = autoPayDto.getUnitId();
                Integer accountId = autoPayDto.getAccountId();

                OcpxAutoPayStatusDto autoPayStatusDto = autoPayStatusByUnitIdMap.get(unitId);
                if (autoPayStatusDto == null) {
                    return;
                }

                OcpxAutoPayStatusDto statusBeforeSendToCrm;
                // 未达到门槛，或者违规操作，需要更新状赔付态表为"成本保障已结束"或"成本保障失效"，并加入要更新赔付状态表的数组
                if ((statusBeforeSendToCrm = commonPeriodService.buildStatusBeforeSendToCrm(autoPayDto, autoPayStatusDto)) != null) {
                    needBeUpdatedStatusDtoList.add(statusBeforeSendToCrm);
                } else {
                    List<Pair<OcpxAutoCompensationDto, OcpxAutoPayStatusDto>> tempList = beSentToCrmMap.getOrDefault(accountId, new ArrayList<>());
                    tempList.add(Pair.of(autoPayDto, autoPayStatusDto));
                    beSentToCrmMap.put(accountId, tempList);
                }
            });

            // 调crm接口打款 + 发站内信
            List<Pair<OcpxAutoCompensationDto, OcpxAutoPayStatusDto>> afterSendToCrmList = commonPeriodService.sendToCrmAndBuildStatus(beSentToCrmMap);

            afterSendToCrmList.forEach(pair -> {
                // 加入要更新赔付源表的数组，送到状态为"已送达"
                needBeUpdatedPayDtoList.add(pair.getFirst());
                // 加入要更新赔付状态表的数组，支付状态为"已支付"或"未支付"
                needBeUpdatedStatusDtoList.add(pair.getSecond());
            });


            // 赔付源表未送达 + 已送达的所有单元
            List<OcpxAutoCompensationDto> autoPayDtoList = ocpxAutoPayService.getOcpxAutoPayDtoListByUnitIdsAndPeriod(autoPayTimeUnitIdsByDay, period.getCode(),
                    commonPeriodService.NEWEST_VERSION, null);
            // 赔付源表未送达 + 已送达的所有单元id
            List<Integer> autoPayUnitIds = autoPayDtoList.stream().map(OcpxAutoCompensationDto::getUnitId).collect(Collectors.toList());

            Map<Integer, Map<Integer, OcpxAutoCompensationDto>> sentAutoPayDtoList = autoPayDtoList.stream()
                    .filter(o -> o.getIsSent().equals(commonPeriodService.IS_SENT))
                    .collect(Collectors.groupingBy(OcpxAutoCompensationDto::getUnitId, Collectors.toMap(OcpxAutoCompensationDto::getPeriod, Function.identity(), (p, q) -> p)));

            for (Map.Entry<Integer, OcpxAutoPayStatusDto> entry : autoPayStatusByUnitIdMap.entrySet()) {
                Integer unitId = entry.getKey();
                OcpxAutoPayStatusDto originStatus = entry.getValue();
                // 在赔付状态表，但未在赔付源表中的单元id --> 该周期无支付数据
                if (!autoPayUnitIds.contains(unitId)) {
                    // 更新状赔付态表状态为"--"，并加入要更新赔付状态表的数组
                    OcpxAutoPayStatusDto changedStatus = new OcpxAutoPayStatusDto();
                    BeanUtils.copyProperties(originStatus, changedStatus);
                    // todo: 交集的状态
                    changedStatus.setStatus(OcpxAutoPayFloatStatus.NOT_JOINING.getCode());
                    needBeUpdatedStatusDtoList.add(changedStatus);
                } else {
                    // 在赔付状态表，且在赔付源表中的单元id --> 该周期有支付数据
                    OcpxAutoCompensationDto autoPayDto = sentAutoPayDtoList.getOrDefault(unitId, Collections.emptyMap()).get(originStatus.getPeriod());
                    if (autoPayDto != null) {
                        // 已送达的数据，更新状赔付态表状态为"已送达"，并加入要更新赔付状态表的数组
                        OcpxAutoPayStatusDto changedStatus = commonPeriodService.buildStatusBeforeSendToCrm(autoPayDto, originStatus);
                        if (changedStatus == null) {
                            changedStatus = new OcpxAutoPayStatusDto();
                            BeanUtils.copyProperties(originStatus, changedStatus);
                            if (StringUtils.hasText(originStatus.getBusinessId())) {
                                changedStatus.setIsPaid(HAS_PAID);
                                changedStatus.setStatus(OcpxAutoPayFloatStatus.HAS_PAID.getCode());
                            } else {
                                changedStatus.setIsPaid(NOT_PAID);
                                changedStatus.setStatus(OcpxAutoPayFloatStatus.HAS_FINISHED.getCode());
                            }
                        }
                        needBeUpdatedStatusDtoList.add(changedStatus);
                    }
                }
            }

            // 更新赔付态表&赔付源表
            ocpxAutoPayService.updateOrSavePayAndStatus(needBeUpdatedStatusDtoList, needBeUpdatedPayDtoList);

            // 有要更新赔付源表，发送企业微信
            if (!CollectionUtils.isEmpty(needBeUpdatedPayDtoList)) {
                needSendWechat = true;
            }
        }
        return needSendWechat;
    }

    @Override
    public void handleSendWechat(Timestamp startTime) {
        Date opDate = startTime;
        List<OcpxAutoCompensationDto> autoPayByLogDate = ocpxAutoPayService.getAutoPayByLogDateAndUnitPeriodType(
                Utils.getSomeDayAgo(new Timestamp(opDate.getTime()), 4),UnitPeriodTypeEnum.COMMON.getCode());// 当天的4天前
        if(CollectionUtils.isEmpty(autoPayByLogDate)){
            return;
        }
        List<Integer> commonUnitIds = autoPayByLogDate.stream().map(o->o.getUnitId()).collect(Collectors.toList());
        BigDecimal needPayAmount = Utils.fromFenToYuan(autoPayByLogDate.stream().mapToInt(OcpxAutoCompensationDto::getCompensation).sum());
        List<OcpxAutoPayStatusDto> autoPayStatusByPayDate = ocpxAutoPayStatusService.getAutoPayStatusByPayDate(opDate);
        autoPayStatusByPayDate = autoPayStatusByPayDate.stream().filter(o->commonUnitIds.contains(o.getUnitId()))
                .collect(Collectors.toList());
        BigDecimal actualPaySucceed = Utils.fromFenToYuan(autoPayStatusByPayDate.stream()
                .filter(dto -> Objects.equals(dto.getStatus(), OcpxAutoPayFloatStatus.HAS_PAID.getCode()))
                .mapToInt(OcpxAutoPayStatusDto::getCompensation)
                .sum());
        BigDecimal actualPayFail = Utils.fromFenToYuan(autoPayStatusByPayDate.stream()
                .filter(dto -> Objects.equals(dto.getStatus(), OcpxAutoPayFloatStatus.PAY_FAILED.getCode()))
                .mapToInt(OcpxAutoPayStatusDto::getCompensation)
                .sum());

        String beginDate = Utils.getTimestamp2String(Utils.getSomeDayAgo(new Timestamp(opDate.getTime()), 6), "yyyyMMdd");
        String endDate = Utils.getTimestamp2String(Utils.getSomeDayAgo(new Timestamp(opDate.getTime()), 4), "yyyyMMdd");
        WechatMarkdownContentDto markdownContentVo = WechatMarkdownContentDto.builder()
                .content(OcpxAutoPayToWechatDto.builder()
                        .date(beginDate + "-" + endDate)
                        .need_pay_amount(needPayAmount)
                        .actual_pay_succeed(actualPaySucceed)
                        .actual_pay_fail(actualPayFail)
                        .build().toContent(UnitPeriodTypeEnum.COMMON.getCode()))
                .build();
        if (actualPayFail != null && actualPayFail.compareTo(BigDecimal.ZERO) > 0) {
            markdownContentVo.setMentioned_list(Lists.newArrayList("@all"));
        }
        WechatMarkdownDto wechatMarkdownVo = WechatMarkdownDto.builder().msgtype("markdown").markdown(markdownContentVo).build();

        commonPeriodService.sendToWechat(commonPeriodService.wechatUrl, GsonUtils.toJson(wechatMarkdownVo));
        JOB_LOGGER.info("ocpxAutoPay of {} send to wechat successful. needPayAmount: {}, actualPaySucceed: {}, actualPayFail: {}",
                opDate, needPayAmount, actualPaySucceed, actualPayFail);
    }
}
