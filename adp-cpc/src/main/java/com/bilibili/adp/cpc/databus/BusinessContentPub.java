package com.bilibili.adp.cpc.databus;

import com.bilibili.adp.cpc.databus.bos.BusinessContentProducerMessageBo;
import com.bilibili.warp.databus.DatabusProperty;
import com.bilibili.warp.databus.DatabusTemplate;
import com.bilibili.warp.databus.Message;
import com.bilibili.warp.databus.PubResult;
import com.bilibili.warp.spring.boot.autoconfigure.databus.DatabusProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description
 * @date 5/15/24
 **/
@Slf4j
@Service
public class BusinessContentPub {

    @Resource
    private DatabusTemplate databusTemplate;

    public static final String BUSINESS_CONTENT = "business-content";
    private final String topic;
    private final String group;

    public BusinessContentPub(DatabusProperties databusProperties) {
        DatabusProperty databusProperty = databusProperties.getProperties().get(BUSINESS_CONTENT);
        this.topic = databusProperty.getTopic();
        this.group = databusProperty.getPub().getGroup();
    }

    public void pub(BusinessContentProducerMessageBo bo) {
        log.info("BusinessContentPub pub, msg={}", bo);

        // messageKey和value自定义，value会被配置的serializer序列化
        Message message = Message.Builder
                .of(bo.getContentId().toString(), bo)
                .build();
        // topic 和 group 按照申请的填写即可. 可以自行注入相关配置，用配置的方式来发送消息
        PubResult result = databusTemplate.pub(topic, group, message);
        if (result.isSuccess()) {
            log.info("pub msg success, msg={}", bo);
        } else {
            Throwable throwable = result.getThrowable();
            log.error("pub msg error ", throwable);
            throw new RuntimeException(throwable.getMessage());
        }
    }

}
