package com.bilibili.adp.cpc.splash_screen.creative.strategy.jump_url_macro.sale_goods;

import com.bilibili.adp.cpc.enums.ad.PromotionPurposeType;
import com.bilibili.adp.cpc.splash_screen.creative.strategy.bos.CreativeContext;
import com.bilibili.adp.cpc.splash_screen.creative.strategy.jump_url_macro.JumpUrlMacroStrategy;
import org.springframework.stereotype.Service;

@Service
public class SaleGoodsJumpUrlMacroStrategy extends JumpUrlMacroStrategy {
    @Override
    public Integer getPromotionPurposeType() {
        return PromotionPurposeType.SALE_GOODS.getCode();
    }

    @Override
    public String addMacro(CreativeContext context, String jumpUrl) {
        return super.addMacro(context, jumpUrl);
    }

    @Override
    public String removeMacro(String jumpUrl) {
        return super.removeMacro(jumpUrl);
    }
}
