package com.bilibili.adp.cpc.automatic_rule.service;

import com.bilibili.adp.cpc.automatic_rule.bos.ConditionBo;
import com.bilibili.adp.cpc.automatic_rule.constants.AutomaticRuleConstant;
import com.bilibili.adp.cpc.automatic_rule.converter.ConditionConverter;
import com.bilibili.adp.cpc.automatic_rule.enums.condition.ConditionType;
import com.bilibili.adp.cpc.automatic_rule.enums.condition.Options;
import com.bilibili.adp.cpc.automatic_rule.enums.condition.Subject;
import com.bilibili.adp.cpc.automatic_rule.enums.condition.ValueType;
import com.bilibili.adp.cpc.dao.querydsl.pos.LauAutomaticRuleConditionPo;
import com.bilibili.adp.cpc.utils.CommonFuncs;
import com.bilibili.adp.cpc.utils.RecDiffResult;
import com.bilibili.bjcom.querydsl.BaseQueryFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static com.bilibili.adp.cpc.dao.config.PlatformMySqlConfig.AD_BQF;
import static com.bilibili.adp.cpc.dao.querydsl.QLauAutomaticRuleCondition.lauAutomaticRuleCondition;

@Slf4j
@Service
public class ConditionService {
    @Resource(name = AD_BQF)
    private BaseQueryFactory adBqf;

    public static List<String> triggerConditions(List<LauAutomaticRuleConditionPo> conditions) {
        return conditions
                .stream()
                .map(ConditionService::triggerCondition)
                .collect(Collectors.toList());
    }

    public static String triggerCondition(LauAutomaticRuleConditionPo condition) {
        final String subjectDesc = Subject.getByCode(condition.getSubject()).getDesc();
        final String conditionTypeDesc = "等于";
        final String optionDesc = Options.getByCode(condition.getOptions()).getDesc();
        final String valueTypeMark = ValueType.getByCode(condition.getValueType()).getMark();
        return subjectDesc + conditionTypeDesc + optionDesc + condition.getValue() + valueTypeMark;
    }

    public static List<String> conditions(List<LauAutomaticRuleConditionPo> conditions) {
        return conditions
                .stream()
                .map(ConditionService::condition)
                .collect(Collectors.toList());
    }

    public static String condition(LauAutomaticRuleConditionPo condition) {
        final String subjectDesc = Subject.getByCode(condition.getSubject()).getDesc();
        final String conditionTypeDesc = ConditionType.getByCode(condition.getConditionType()).getDesc();
        final String optionDesc = Options.getByCode(condition.getOptions()).getDesc();
        final String valueTypeMark = ValueType.getByCode(condition.getValueType()).getMark();
        return subjectDesc + conditionTypeDesc + optionDesc + condition.getValue() + valueTypeMark;
    }

    public List<LauAutomaticRuleConditionPo> listConditionsByRuleId(Long ruleId) {
        return adBqf
                .selectFrom(lauAutomaticRuleCondition)
                .where(lauAutomaticRuleCondition.ruleId.eq(ruleId))
                .where(lauAutomaticRuleCondition.isDeleted.eq(0))
                .fetch();
    }

    public List<LauAutomaticRuleConditionPo> listConditionsByRuleIds(Collection<Long> ruleIds) {
        return adBqf
                .selectFrom(lauAutomaticRuleCondition)
                .where(lauAutomaticRuleCondition.ruleId.in(ruleIds))
                .where(lauAutomaticRuleCondition.isDeleted.eq(0))
                .fetch();
    }

    public void saveConditions(Integer accountId, Long ruleId, List<ConditionBo> conditions) {
        final List<LauAutomaticRuleConditionPo> existedPos = listConditionsByRuleIds(Collections.singletonList(ruleId));
        final List<LauAutomaticRuleConditionPo> newPos = conditions
                .stream()
                .map(condition -> ConditionConverter.MAPPER.bo2Po(accountId, ruleId, condition))
                .collect(Collectors.toList());
        RecDiffResult<LauAutomaticRuleConditionPo, Long> result = CommonFuncs.recDiff(existedPos, newPos, this::uk, LauAutomaticRuleConditionPo::getConditionId, LauAutomaticRuleConditionPo::setConditionId);
        CommonFuncs.handleRecDiff(result, adBqf, lauAutomaticRuleCondition, lauAutomaticRuleCondition.conditionId::in);
    }

    private String uk(LauAutomaticRuleConditionPo po) {
        return po.getAccountId() + "-" + po.getRuleId() + "-" + po.getConditionId();
    }

    public void deleteConditions(Integer accountId, Long ruleId) {
        adBqf.update(lauAutomaticRuleCondition)
                .set(lauAutomaticRuleCondition.isDeleted, AutomaticRuleConstant.RULE_IS_DELETED)
                .where(lauAutomaticRuleCondition.accountId.eq(accountId))
                .where(lauAutomaticRuleCondition.ruleId.eq(ruleId))
                .execute();
    }
}
