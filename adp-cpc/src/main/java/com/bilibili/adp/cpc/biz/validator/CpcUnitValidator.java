/**
 * <AUTHOR>
 * @date 2018年1月8日
 */

package com.bilibili.adp.cpc.biz.validator;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.bilibili.adp.account.dto.AccountAllInfoDto;
import com.bilibili.adp.account.dto.AccountBaseDto;
import com.bilibili.adp.account.service.IQueryAccountService;
import com.bilibili.adp.common.Constants;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.TargetRule;
import com.bilibili.adp.common.enums.*;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.exception.ServiceRuntimeException;
import com.bilibili.adp.common.util.CollectionHelper;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.biz.services.app.api.IAppPackageService;
import com.bilibili.adp.cpc.biz.services.campaign.dto.CpcCampaignDto;
import com.bilibili.adp.cpc.biz.services.creative.config.AccLabelConfig;
import com.bilibili.adp.cpc.biz.services.pickup.PickupOrderQuerier;
import com.bilibili.adp.cpc.biz.services.pickup.dto.PickupOrderDto;
import com.bilibili.adp.cpc.biz.services.target_package.api.ICrowdPackService;
import com.bilibili.adp.cpc.biz.services.target_package.api.IResProfessionInterestService;
import com.bilibili.adp.cpc.biz.services.target_package.api.IResTargetItemService;
import com.bilibili.adp.cpc.biz.services.unit.api.ICpcUnitService;
import com.bilibili.adp.cpc.biz.services.unit.dto.CpcUnitDto;
import com.bilibili.adp.cpc.biz.services.unit.dto.NewCpcUnitDto;
import com.bilibili.adp.cpc.biz.services.unit.dto.UpdateCpcUnitDto;
import com.bilibili.adp.cpc.core.constants.AdType;
import com.bilibili.adp.cpc.core.constants.BusinessDomain;
import com.bilibili.adp.cpc.dto.CpcUnitExtraTargetDto;
import com.bilibili.adp.cpc.dto.UnitTargetDto;
import com.bilibili.adp.cpc.dto.UnitTargetInstalledUserFilterDto;
import com.bilibili.adp.cpc.enums.AdpVersion;
import com.bilibili.adp.cpc.enums.ad.OcpcTargetEnum;
import com.bilibili.adp.cpc.enums.ad.PromotionPurposeType;
import com.bilibili.adp.cpc.po.ad.ResProfessionInterestCrowdsPo;
import com.bilibili.adp.cpc.splash_screen.unit.SplashScreenUnitService;
import com.bilibili.adp.launch.api.common.*;
import com.bilibili.adp.launch.api.launch.dto.GetBidCostParam;
import com.bilibili.adp.launch.api.launch.dto.LauProductGroupDto;
import com.bilibili.adp.launch.api.service.*;
import com.bilibili.adp.launch.api.unit.dto.DpaShopGoodsDto;
import com.bilibili.adp.launch.biz.ad_core.mybatis.UnitDao;
import com.bilibili.adp.launch.biz.common.LaunchUtil;
import com.bilibili.adp.launch.biz.config.UpOrderConfig;
import com.bilibili.adp.launch.biz.exception.LaunchExceptionCode;
import com.bilibili.adp.launch.biz.service.UnitBudgetCountComponent;
import com.bilibili.adp.launch.biz.service.account.LaunchAccountGroupService;
import com.bilibili.adp.passport.api.dto.ArchiveBase;
import com.bilibili.adp.passport.api.dto.ArchiveDetail;
import com.bilibili.adp.passport.api.dto.ArchivePartition;
import com.bilibili.adp.passport.api.dto.GameDto;
import com.bilibili.adp.passport.api.service.IGameCenterService;
import com.bilibili.adp.passport.biz.manager.ArchiveManager;
import com.bilibili.adp.resource.api.app_package.dto.AppPackageDto;
import com.bilibili.adp.resource.api.slot_group.IResSlotGroupService;
import com.bilibili.adp.resource.api.slot_group.ResSlotGroupBaseDto;
import com.bilibili.adp.resource.api.system.ISystemConfigService;
import com.bilibili.adp.resource.api.target_lau.dto.ResTargetItemDto;
import com.bilibili.adp.resource.api.targetmeta.TargetType;
import com.bilibili.bjcom.util.common.Arguments;
import com.bilibili.crm.platform.common.IsInnerEnum;
import com.bilibili.crm.platform.common.IsValid;
import com.bilibili.location.api.template.dto.TemplateDto;
import com.bilibili.mas.common.utils.Values;
import com.google.common.annotations.VisibleForTesting;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Predicate;
import java.util.regex.Matcher;
import java.util.stream.Collectors;

import static com.bilibili.adp.cpc.enums.ad.PromotionPurposeType.STORE_DIRECT_LAUNCH_SUPPORT_UNIT_PPT_SET;

/**
 * copy class
 * @see com.bilibili.adp.launch.biz.validator.CpcUnitValidator
 */
@Primary
@Component(value = "LaunchCpcUnitValidator")
@RequiredArgsConstructor
public class CpcUnitValidator {

    private static final Logger LOGGER = LoggerFactory.getLogger(CpcUnitValidator.class);
    private static final int PRIVATE_ORDER_INDEX = 12;

    @Value("${target.ageCustomize.min:18}")
    private int AGE_CUSTOMIZE_MIN;
    @Value("${target.ageCustomize.max:60}")
    private int AGE_CUSTOMIZE_MAX;

    @Value("${crow.package.count.limit:30}")
    private Integer crowPackageCountLimit;

    @Autowired
    private UnitDao unitDao;
    @Autowired
    private ICpcUnitService cpcUnitService;
    @Resource(name = "cpcAppPackageService")
    private IAppPackageService appPackageService;
    @Autowired
    private ICrowdPackService crowdPackService;
    @Autowired
    private ILauTagIdsService lauTagIdsService;
    @Autowired
    private IResSlotGroupService resSlotGroupService;
    @Autowired
    private ArchiveManager archiveManager;
    @Autowired
    private IQueryShopGoodsService queryShopGoodsService;
    @Autowired
    private IGameService gameService;
    @Autowired
    private IQueryAccountService queryAccountService;
    @Autowired
    private ISystemConfigService systemConfigService;
    @Autowired
    private PickupOrderQuerier pickupOrderQuerier;
    @Autowired
    private UpOrderConfig upOrderConfig;
    @Autowired
    private IGameCenterService gameCenterService;
    @Autowired
    private IHystrixDmpService hystrixDmpService;
    @Autowired
    private ILauProductGroupService lauProductGroupService;
    @Autowired
    private NoBidValidator nobidValidator;
    @Autowired
    private UnitBudgetCountComponent unitBudgetCountComponent;
    @Autowired
    private InstalledUserTargetValidator installedUserTargetValidator;
    @Autowired
    private IResTargetItemService resTargetItemService;
    @Autowired
    private IResProfessionInterestService resProfessionInterestService;
    @Autowired
    private LaunchAccountGroupService launchAccountGroupService;
    @Autowired
    private AccLabelConfig accLabelConfig;

    @Value("${cpc.fans.target.max:50}")
    private Integer cpcFansTargetMax = 50;

    @Value("#{'${cpc.can.ocpc.target.source.ids:1891,1898,2003,2030,2031,2336,2338,3164,3172,4356,4353}'.split(',')}")
    private List<Integer> canOcpcTargetSourceIds = Collections.emptyList();

    @Value("#{'${pc.ocpc.source.ids:272,278,284,429,133,137,154,155,156,244,246,248,250,252,254,256,258,260,262,264,266,268,270,274,276,280,282,286,288,290,292,294,296,407,409,410,424,425,426,427,430,1926,1927,1928,1891,1898,2003,2030,2031,2336,2338,3164,3172,3199,3200,30,35,37,41,43,45,2626,2627,2628,2629,4331}'.split(',')}")
    private List<Integer> allSupportedOcpcSourceIds;

    @Value("#{PropertySplitter.getInt2IntMap('${ocpx.accountGroup.target.map:{9:71,10:72,7:73,17:81,14:78,5:39}}')}")
    private Map<Integer, Integer> ocpxAccountGroupTargetMap = Collections.emptyMap();

    @Value("#{'${platform.unit.support.store.direct.launch.ocpc.targets:2,5,7,11}'.split(',')}")
    private List<Integer> supportStoreDirectLaunchOcpcTargets;

    private final SplashScreenUnitService splashScreenUnitService;

    public List<Integer> getSupportedOcpcSourceIds(Integer ocpcTarget, Integer accountId) {
        final List<Integer> containingSourceIds;
        if (Objects.isNull(ocpcTarget) || ocpcTarget.equals(0) || Objects.isNull(accountId) || accountId.equals(0)) {
            containingSourceIds = null;
        } else if (ocpcTarget.equals(OcpcTargetEnum.FORM_SUBMIT.getCode())) {
            // OCPC表单提交包含PC资源位
            containingSourceIds = allSupportedOcpcSourceIds;
        } else {
            // 其他OCPC类型不允许投PC资源位
            containingSourceIds = getCanOcpcTargetSourceIds();
        }
        return containingSourceIds;
    }

    public List<Integer> getCanOcpcTargetSourceIds() {
        return canOcpcTargetSourceIds;
    }

    public Map<Integer, Integer> getOcpxAccountGroupTargetMap() {
        return ocpxAccountGroupTargetMap;
    }

    /**
     * 动态广告, 单元频控
     **/
    @Value("#{PropertySplitter.getInt2IntMap('${lau.dynamic.unit.frequency.max:{1:2;2:14}}')}")
    private Map<Integer, Integer> dynamicUnitFrequencyLimit;

    public void createCpc(ResSlotGroupBaseDto slotGroup, TemplateDto templateDto, NewCpcUnitDto unit, CpcCampaignDto campaign, Integer businessDomain, Operator operator) throws ServiceException {
        // 已删除或已结束的计划下不可再创建新的单元
        if (!LaunchStatus.getByCode(campaign.getStatus()).isCanUpdate()) {
            throw new ServiceException(LaunchExceptionCode.CAMPAIGN_DELETED_UNIT_NOT_ALLOW_CREATE);
        }
        // 推广单元名称不能为空
        if (Strings.isNullOrEmpty(unit.getUnitName())
                || Strings.isNullOrEmpty(unit.getUnitName().trim())) {
            throw new ServiceException(LaunchExceptionCode.UNIT_NAME_NULL);
        }
        // 推广单元名称太长
        if (unit.getUnitName().length() > LaunchConstant.MAX_UNIT_NAME_LENGTH) {
            throw new ServiceException(LaunchExceptionCode.UNIT_NAME_TOO_LONG);
        }
        // 无效的日预算类型
        if (DailyBudgetType.getByCode(unit.getDailyBudgetType()) == null) {
            throw new ServiceException(LaunchExceptionCode.UNIT_DAILY_BUDGET_TYPE_VALID);
        }
        Assert.notNull(unit.getSalesType(), "售卖类型不能为空");

        final int adpVersion = unit.getAdpVersion();
        // 获取底价
        final Integer lowestBid;
        final AccountBaseDto accountBaseDto = queryAccountService.getAccountBaseDtoByIdFromCache(operator.getOperatorId());
        Assert.notNull(accountBaseDto, "该账号不存在");
        if (Objects.equals(campaign.getAdType(), AdType.SPLASH_SCREEN)) {
            lowestBid = splashScreenUnitService.lowestBidFen(operator.getOperatorId(), unit.getPromotionPurposeType(), unit.getSalesType());
        } else {
            // 如果是ocpc 三连单元的一阶段出价 不需要校验底价 随便改 大于0即可
            boolean cancelLowBid = SalesType.CPC.getCode() == unit.getSalesType()
                    && Utils.isPositive(unit.getOcpcTarget())
                    && BusinessDomain.CPC == businessDomain
                    && AdpVersion.isMiddle(unit.getAdpVersion());
            lowestBid = cancelLowBid ? 0 : cpcUnitService.getReservedPrice(GetBidCostParam.builder()
                    .accountId(operator.getOperatorId())
                    .slotGroupId(unit.getSlotGroup())
                    .salesType(unit.getSalesType())
                    .launchType(campaign.getPromotionPurposeType())
                    .build(), adpVersion);
        }

        // 老的版本
        if (AdpVersion.isLegacy(adpVersion)) {
            validateSlotSelected(unit.getSalesType(), unit.getSlotGroup(), operator);
            validateDynamicFrequency(templateDto, unit.getFrequencyUnit(), unit.getFrequencyLimit(), adpVersion);
            //oCPC校验
            List<Device> devices = getTargetDevices(unit.getTargetRules());

            // 优化目标校验
            this.validateOcpcTarget(campaign.getAccountId(), unit.getOcpcTarget(), slotGroup,
                    campaign.getPromotionPurposeType(), unit.getSalesType(), devices, unit.getTwoStageBid(), unit.getIsNoBid());
            Arguments.of(unit.getSmartIncrease()).in(Arrays.asList(0, 1, null), "智能起量参数错误");

            // 如果指定了 nobid 需要广告位组支持
            if (unit.getIsNoBid() != null && Utils.isPositive(unit.getIsNoBid())) {
                if (slotGroup != null && slotGroup.getSupportNobid() != null && !Utils.isPositive(slotGroup.getSupportNobid())) {
                    throw new ServiceRuntimeException("该广告位组不支持nobid，请检查！");
                }
            }
        }

        // 非 nobid, 出价必须大于当前基价(nobid 出价不限制)
        if (!Utils.isPositive(unit.getIsNoBid())) {
            if (lowestBid.compareTo(unit.getCostPrice()) > 0) {
                throw new ServiceException(LaunchExceptionCode.UNIT_CPC_BID_TOO_LOWER.getCode(),
                        String.format(LaunchExceptionCode.UNIT_CPC_BID_TOO_LOWER.getMessage(), Utils.fromFenToYuan(lowestBid)));
            }
        }
        // nobid 不允许深度转化出价(第二目标竞价)
        if (Utils.isPositive(unit.getIsNoBid()) && Utils.isPositive(unit.getOcpxTargetTwoBid())) {
            throw new ServiceRuntimeException("nobid 不支持双出价");
        }
//      this.checkOcpcDeep3233(unit.getOcpxTargetTwo(),unit.getOcpxTargetTwoBid());

        // 无效的频次单元
        if (FrequencyUnit.getByCode(unit.getFrequencyUnit()) == null) {
            throw new ServiceException(LaunchExceptionCode.UNIT_FREQUENCY_UNIT_VALID);
        }
        // 频次限制必须大于零
        if (unit.getFrequencyLimit() != null && unit.getFrequencyLimit() <= 0) {
            throw new ServiceException(LaunchExceptionCode.UNIT_FREQUENCY_LIMIT_VALID);
        }

        // 验证参数是否符合应用商店直投要求
        validateStoreDirectLaunch(campaign.getPromotionPurposeType(), unit.getIsStoreDirectLaunch(), unit.getOcpcTarget(), unit.getAppPackageId());

        // 日预算方式为手动
        if (unit.getDailyBudgetType() == DailyBudgetType.MANUAL.getCode()) {
            Assert.isTrue(unit.getBudget().compareTo(campaign.getBudget()) <= 0, "单元预算不能超过计划预算");
        }

        if (PromotionPurposeType.SHOP_GOODS.getCode() == campaign.getPromotionPurposeType()) {
            Assert.notNull(unit.getShopGoodsId(), "商品ID不可为空");
            DpaShopGoodsDto shopGoodsDto = queryShopGoodsService.getDpaShopGoodsByGoodsId(unit.getShopGoodsId());
            Assert.notNull(shopGoodsDto, "该商品不存在");
            Assert.isTrue(ShopGoodsStatusEnum.ON_SHELF.getCode().equals(shopGoodsDto.getStatus()), "该商品尚未上架，请核实");
            Assert.isTrue(shopGoodsDto.getStock() > 0, "该商品库存不足，请核实");
        }

        if (Objects.equals(PromotionPurposeType.APP_DOWNLOAD.getCode(), campaign.getPromotionPurposeType())) {
            // 此处不改写死，后面会根据 unit.app_package_id查应用的实际 platform type
//            unit.setGamePlatformType(GamePlatformTypeEnum.IOS.getCode());
        }

        if (PromotionPurposeType.ON_SHELF_GAME.getCode() == campaign.getPromotionPurposeType()) {
            Assert.notNull(unit.getGameBaseId(), "游戏ID不可为空");
            List<Device> targetDevices = getTargetDevices(unit.getTargetRules());
            Arguments.of(targetDevices).notEmpty("请选择且只选择同一类设备平台定向");
            //外广用户在安卓游戏推广目的下 不允许选择ios定向
            if (Objects.equals(IsInnerEnum.OUTER.getCode(), accountBaseDto.getIsInner())
                    && Device.IOSES.containsAll(targetDevices)) {
                throw new ServiceRuntimeException("请选择安卓设备定向");
            }
            if (Device.IOSES.containsAll(targetDevices)) {
                unit.setGamePlatformType(GamePlatformTypeEnum.IOS.getCode());
            } else if (Device.ANDROIDS.containsAll(targetDevices)) {
                unit.setGamePlatformType(GamePlatformTypeEnum.ANDROID.getCode());
            } else {
                throw new ServiceRuntimeException("请选择同一类型的设备平台定向");
            }

            if (IsValid.TRUE.getCode().equals(accountBaseDto.getIsInner())) {
                GameDto gameDto = gameCenterService.getGameDtoByIdPlatform(unit.getGameBaseId(), unit.getGamePlatformType());
                Assert.isTrue(gameDto.getIsOnline(), "该游戏在所选设备平台未上架");
            } else {
                List<Long> cpMids = queryAccountService.getGameCpMidIdsByAccountId(campaign.getAccountId());
                gameService.validateSignedGameById(cpMids, unit.getGameBaseId(), unit.getGamePlatformType());
            }
        }

        Assert.notNull(unit.getSalesType(), "计费类型不能为空");
        SalesType.getByCode(unit.getSalesType());

        validateDate(unit);
        validateLaunchTime(unit.getLaunchTime());
        validateUnitCount(campaign.getCampaignId());

        UnitTargetDto unitTargetDto = UnitTargetDto.builder()
                .slotGroupId(unit.getSlotGroup())
                .tags(unit.getTags())
                .videoTag(unit.getVideoTag())
                .crowdPackIds(unit.getCrowdPackIds())
                .excludeCrowdPackIds(unit.getExcludeCrowdPackIds())
                .businessInterestIds(unit.getBusinessInterestIds())
                .targetVideoIds(unit.getTargetVideoIds())
                .targetContentLabels(unit.getTargetContentLabels())
                .otherCrowdPackIdsGroup(unit.getOtherCrowdPackIdsGroup())
                .targetRules(unit.getTargetRules())
                .promotionPurposeType(campaign.getPromotionPurposeType())
                .installedUserFilter(unit.getInstalledUserFilter())
                .ocpcTarget(unit.getOcpcTarget() == null ? 0 : unit.getOcpcTarget())
                .professionInterestIds(unit.getProfessionInterestIds())
                .build();
        // 校验定向
        validateTarget(operator.getOperatorId(), unitTargetDto, adpVersion);
        validateExtraTarget(unit.getExtraTarget());

        Long mid = Objects.nonNull(unit.getLongMid()) ? unit.getLongMid() : unit.getMid().longValue();

        // 校验稿件内容
        validateArchiveContent(PromotionPurposeType.getByCode(campaign.getPromotionPurposeType()),
                mid, unit.getVideoId(), unit.getLaunchVideoType());

        // 校验商品组
        validateProductGroup(campaign.getPromotionPurposeType(), unit.getProductGroupId(), campaign.getAccountId());

        // nobid 的出价方式将 nobidMax 清空
        if (!Utils.isPositive(unit.getIsNoBid())) {
            unit.setNoBidMax(0);
        }
    }

    private void validateDynamicFrequency(TemplateDto templateDto, Integer frequencyUnit, Integer frequencyLimit, Integer adpVersion) throws ServiceException {
        if (templateDto.getIsDynamicArea()
                && frequencyLimit != null
                && frequencyLimit.compareTo(dynamicUnitFrequencyLimit.get(frequencyUnit)) > 0
                // 视频版位合并去掉对频控的校验
                && !AdpVersion.isVideoMerged(adpVersion)) {
            throw new IllegalArgumentException(LaunchExceptionCode.UNIT_FREQUENCY_TOO_LARGE.getMessage());
        }
    }

    private void validateExtraTarget(CpcUnitExtraTargetDto extraTarget) {
        if (extraTarget == null) {
            return;
        }
        Assert.isTrue(CollectionHelper.getSize(extraTarget.getIncludeTheirsFans()) <= cpcFansTargetMax, "粉丝定向包含UP主数不能超过" + cpcFansTargetMax);
        Assert.isTrue(CollectionHelper.getSize(extraTarget.getExcludeTheirsFans()) <= cpcFansTargetMax, "粉丝定向排除UP主数不能超过" + cpcFansTargetMax);
        if (!CollectionUtils.isEmpty(extraTarget.getIncludeTheirsFans()) && !CollectionUtils.isEmpty(extraTarget.getExcludeTheirsFans())) {
            Assert.isTrue(extraTarget.getIncludeTheirsFans().stream().noneMatch(extraTarget.getExcludeTheirsFans()::contains), "存在重复粉丝定向");
        }
    }

    /**
     * 校验 ocpc 的优化目标
     *
     * @param accountId
     * @param ocpcTarget
     * @param slotGroupBaseDto
     * @param purposeType
     * @param salesType
     * @param devices
     * @param twoStageBid
     */
    private void validateOcpcTarget(Integer accountId, Integer ocpcTarget, ResSlotGroupBaseDto slotGroupBaseDto,
                                    Integer purposeType, Integer salesType, List<Device> devices, Integer twoStageBid, Integer isNoBid) {
        LOGGER.info("validateOcpcTarget ocpcTarget: [{}], slotGroupBaseDto: [{}], purposeType: [{}], salesType: [{}], devices: [{}], twoStageBid: [{}]", ocpcTarget, slotGroupBaseDto, purposeType, salesType, devices, twoStageBid);
        if (!Utils.isPositive(ocpcTarget)) {
            return;
        }

        // cpc
        if (SalesType.CPC.getCode() != salesType) {
            OcpcTargetEnum ocpcTargetEnum = OcpcTargetEnum.getByCode(ocpcTarget);
            Assert.notNull(ocpcTargetEnum, "暂不支持该优化目标");

            PromotionPurposeType promotionPurposeType = PromotionPurposeType.getByCode(purposeType);
            if (!CollectionUtils.isEmpty(ocpcTargetEnum.getSupportPurposeTypes())) {
                Assert.isTrue(ocpcTargetEnum.getSupportPurposeTypes().containsAll(EnumSet.of(promotionPurposeType)), "投放目的为" + promotionPurposeType.getDesc() + "的类型，不支持" + ocpcTargetEnum.getDesc() + "优化");
            }

            if (!CollectionUtils.isEmpty(canOcpcTargetSourceIds) && !CollectionUtils.isEmpty(allSupportedOcpcSourceIds)) {
                Assert.notEmpty(Utils.getIntersection(slotGroupBaseDto.getSlotIds(), getSupportedOcpcSourceIds(ocpcTarget, accountId)), "该位置不支持oCPC(M)优化");
            }

            ChannelEnum channelEnum = ChannelEnum.getByCode(slotGroupBaseDto.getChannelId());
            if (!CollectionUtils.isEmpty(ocpcTargetEnum.getSupportPlatforms())) {
                Assert.isTrue(ocpcTargetEnum.getSupportPlatforms().contains(channelEnum), channelEnum.getDesc() + "不支持" + ocpcTargetEnum.getDesc() + "优化");
            }

            SalesType salesTypeEnum = SalesType.getByCode(salesType);
            if (!CollectionUtils.isEmpty(ocpcTargetEnum.getSupportSalesTypes())) {
                Assert.isTrue(ocpcTargetEnum.getSupportSalesTypes().contains(salesTypeEnum), "出价方式为" + salesTypeEnum.getDesc() + "不支持" + ocpcTargetEnum.getDesc() + "优化");
            }

            if (!CollectionUtils.isEmpty(ocpcTargetEnum.getSupportDevices())) {
                Assert.isTrue(ocpcTargetEnum.getSupportDevices().containsAll(devices), "存在设备定向不支持" + ocpcTargetEnum.getDesc() + "优化");
            }

            AccountAllInfoDto accountAllInfo = queryAccountService.getAccountAllInfoFromCache(accountId);
            Integer accountGroupId = ocpxAccountGroupTargetMap.get(ocpcTargetEnum.getCode());

            if (Utils.isPositive(accountGroupId)) {
                //内容起飞不管用户组都可以投ocpm用户关注
                if(accountAllInfo.getAccountDto().getIsSupportContent()!=null
                        && (accountAllInfo.getAccountDto().getIsSupportContent()==1
                        || accountAllInfo.getAccountDto().getAllowCashPay() ==1)
                        && ocpcTarget!=null
                        && ocpcTarget==OcpcTargetEnum.USER_FOLLOW.getCode()){
                    //不做校验
                }else {
                    Assert.isTrue(!CollectionUtils.isEmpty(accountAllInfo.getAccountGroupDtos()) &&
                                    accountAllInfo.getAccountGroupDtos().stream().anyMatch(ag -> accountGroupId.equals(ag.getId())),
                            "该oCPC(M)" + ocpcTargetEnum.getDesc() + "需要用户组" + accountGroupId + "的权限");
                }
            }
        }

        if (!Utils.isPositive(isNoBid)) {
            Assert.isTrue(Utils.isPositive(twoStageBid), "oCPC(M)第二阶段目标转化成本必须大于0元");
        }
    }

    private void validateBusinessInterest(List<Integer> businessInterestIds) {
        if (!CollectionUtils.isEmpty(businessInterestIds)) {
            Map<Integer, String> crowdPackNameMap;
            // todo sjc 限流？？？？
            Assert.isTrue(!hystrixDmpService.isPeopleGroupCircuitBreakerOpen(), "暂不支持该操作，请稍后重试");
            try {
                crowdPackNameMap = crowdPackService.getCrowdPackNameMapInIdsDirectly(businessInterestIds);
            } catch (Exception e) {
                LOGGER.error("getCrowdPackNameMapInIdsError", e);
                businessInterestIds.forEach(id -> Assert.notNull(id, "商业兴趣分类不能包含空的ID"));
                return;
            }
            Assert.isTrue(crowdPackNameMap.keySet().containsAll(businessInterestIds), "存在无效的商业兴趣分类");
        }
    }

    private void validateContentLabelVideoTarget(Integer slotGroupId, List<Long> targetVideoIds, List<String> targetContentLabels) throws ServiceException {
        if (!CollectionUtils.isEmpty(targetContentLabels)
                && !CollectionUtils.isEmpty(targetVideoIds)) {
            Assert.isTrue(false, "不可同时定向内容标签与视频");
        }

        if (isSupportContentLabelAndVideoIdTarget(slotGroupId)) {
            if (CollectionUtils.isEmpty(targetContentLabels)
                    && CollectionUtils.isEmpty(targetVideoIds)) {
                Assert.isTrue(false, "内容定向不可为空");
            }

            if (!CollectionUtils.isEmpty(targetVideoIds)) {
                Map<Long, ArchiveDetail> archives = null;
                try {
                    archives = archiveManager.getArchivesByAids(targetVideoIds);
                } catch (Exception e) {
                    LOGGER.error("invoker archiveManager.getArchivesByAids failed", e);
                    Assert.isTrue(false, "视频定向失败，请刷新后重试");
                }
                Assert.isTrue(archives.size() == targetVideoIds.size(), "存在非法的视频ID");
            }
        }
    }

    public boolean isSupportContentLabelAndVideoIdTarget(Integer slotGroupId) {
        String contentVideoSlotGroups = systemConfigService.getValueByItem(SystemConfig.CPC_CONTENT_TARGET_SLOT_GROUPS);
        List contentVideoSlotGroupList = JSONArray.parseObject(contentVideoSlotGroups, List.class);
        return !CollectionUtils.isEmpty(contentVideoSlotGroupList)
                && contentVideoSlotGroupList.contains(slotGroupId);
    }

    public void updateCpc(UpdateCpcUnitDto unit, CpcUnitDto oldUnit, TemplateDto templateDto, CpcCampaignDto campaign, Operator operator) throws ServiceException {
        // 已删除或已结束的单元不可再编辑
        if (LaunchStatus.DELETE.equals(LaunchStatus.getByCode(oldUnit.getStatus()))
                || LaunchStatus.FINISH.equals(LaunchStatus.getByCode(oldUnit.getStatus()))) {
            throw new ServiceException(LaunchExceptionCode.UNIT_NOT_UPDATE);
        }
        // 推广单元名称不能为空
        if (Strings.isNullOrEmpty(unit.getUnitName())
                || Strings.isNullOrEmpty(unit.getUnitName().trim())) {
            throw new ServiceException(LaunchExceptionCode.UNIT_NAME_NULL);
        }
        if (unit.getUnitName().length() > LaunchConstant.MAX_UNIT_NAME_LENGTH) {
            throw new ServiceException(LaunchExceptionCode.UNIT_NAME_TOO_LONG);
        }
        if (FrequencyUnit.getByCode(unit.getFrequencyUnit()) == null) {
            throw new ServiceException(LaunchExceptionCode.UNIT_FREQUENCY_UNIT_VALID);
        }
        if (unit.getFrequencyLimit() != null && unit.getFrequencyLimit() <= 0) {
            throw new ServiceException(LaunchExceptionCode.UNIT_FREQUENCY_LIMIT_VALID);
        }

        //如果应用包有更改，用新包验证,否则用旧包
        if (unit.getAppPackageId() != null) {
            validateStoreDirectLaunch(campaign.getPromotionPurposeType(), unit.getIsStoreDirectLaunch(), unit.getOcpcTarget(), unit.getAppPackageId());
        } else {
            validateStoreDirectLaunch(campaign.getPromotionPurposeType(), unit.getIsStoreDirectLaunch(), unit.getOcpcTarget(), oldUnit.getAppPackageId());
        }

        final int adpVersion = oldUnit.getAdpVersion();
        if (AdpVersion.isLegacy(adpVersion)) {
            validateDynamicFrequency(templateDto, unit.getFrequencyUnit(), unit.getFrequencyLimit(), adpVersion);
            List<Device> devices = getTargetDevices(unit.getTargetRules());
            ResSlotGroupBaseDto resSlotGroup = resSlotGroupService.getGroupById(oldUnit.getSlotGroup());

            // 优化目标校验
            this.validateOcpcTarget(campaign.getAccountId(), unit.getOcpcTarget(), resSlotGroup,
                    campaign.getPromotionPurposeType(), oldUnit.getSalesType(), devices, unit.getTwoStageBid(), unit.getIsNoBid());

            // 如果指定了 nobid 需要广告位组支持
            if (unit.getIsNoBid() != null && Utils.isPositive(unit.getIsNoBid())) {
                if (resSlotGroup != null && resSlotGroup.getSupportNobid() != null && !Utils.isPositive(resSlotGroup.getSupportNobid())) {
                    throw new ServiceRuntimeException("该广告位组不支持nobid，请检查！");
                }
            }
        }

        // nobid 不允许深度转化出价(第二目标竞价)
        if (Utils.isPositive(unit.getIsNoBid()) && Utils.isPositive(unit.getOcpxTargetTwoBid())) {
            throw new ServiceRuntimeException("nobid 不支持双出价");
        }
        // nobid 计价方式是否可以切换
        nobidValidator.validateSalesTypeForNoBid(unit, oldUnit, campaign.getPromotionPurposeType());

//        this.checkOcpcDeep3233(unit.getOcpxTargetTwo(),unit.getOcpxTargetTwoBid());

        //三连推广平台校验
        if(AdpVersion.isMiddle(adpVersion)){
            //会员购编辑校验
            if (PromotionPurposeType.SHOP_GOODS.getCode() == campaign.getPromotionPurposeType()) {
                Assert.isTrue(Utils.isPositive(unit.getShopGoodsId()), "商品ID不可为空");
                AccountBaseDto accountBaseDto = queryAccountService.getAccountBaseDtoById(campaign.getAccountId());
                Assert.notNull(accountBaseDto, "当前账号不存在");
                DpaShopGoodsDto shopGoodsDto = queryShopGoodsService.getDpaShopGoodsByGoodsId(unit.getShopGoodsId());
                Assert.notNull(shopGoodsDto, "当前商品不存在");
                Assert.isTrue(ShopGoodsStatusEnum.ON_SHELF.getCode().equals(shopGoodsDto.getStatus()), "三连推广会员购编辑-当前商品尚未上架，请核实");
                Assert.isTrue(shopGoodsDto.getStock() > 0, "当前商品库存不足，请核实");
            }
        }

        // 获取底价
        final Integer lowestBid;
        if (Objects.equals(campaign.getAdType(), AdType.SPLASH_SCREEN)) {
            lowestBid = splashScreenUnitService.lowestBidFen(operator.getOperatorId(), unit.getPromotionPurposeType(), unit.getSalesType());
        } else {
            // 如果是ocpc 三连单元的一阶段出价 不需要校验底价 随便改 大于0即可
            boolean cancelLowBid = SalesType.CPC.getCode() == unit.getSalesType()
                    && Utils.isPositive(unit.getOcpcTarget())
                    && BusinessDomain.CPC == oldUnit.getBusinessDomain()
                    && AdpVersion.isMiddle(unit.getAdpVersion());
            lowestBid = cancelLowBid ? 0 : cpcUnitService.getReservedPrice(GetBidCostParam.builder()
                    .accountId(operator.getOperatorId())
                    .slotGroupId(oldUnit.getSlotGroup())
                    .salesType(oldUnit.getSalesType())
                    .launchType(campaign.getPromotionPurposeType())
                    .build(), adpVersion);
        }

        // 非 nobid, 出价必须大于当前基价(nobid 出价不限制)
        if (!Utils.isPositive(unit.getIsNoBid())) {
            if (lowestBid.compareTo(unit.getCostPrice()) > 0) {
                throw new ServiceException(LaunchExceptionCode.UNIT_CPC_BID_TOO_LOWER.getCode(),
                        String.format(LaunchExceptionCode.UNIT_CPC_BID_TOO_LOWER.getMessage(), Utils.fromFenToYuan(lowestBid)));
            }
        }

        // 日预算方式为手动
        if (unit.getDailyBudgetType() == DailyBudgetType.MANUAL.getCode()) {
            Assert.isTrue(unit.getBudget().compareTo(campaign.getBudget()) <= 0, "单元预算不能超过计划预算");
        }

        validateUpdateDate(unit, oldUnit);
        validateLaunchTime(unit.getLaunchTime());

        UnitTargetDto unitTargetDto = UnitTargetDto.builder()
                .slotGroupId(oldUnit.getSlotGroup())
                .tags(unit.getTags())
                .videoTag(unit.getVideoTag())
                .crowdPackIds(unit.getCrowdPackIds())
                .excludeCrowdPackIds(unit.getExcludeCrowdPackIds())
                .businessInterestIds(unit.getBusinessInterestIds())
                .targetVideoIds(unit.getTargetVideoIds())
                .targetContentLabels(unit.getTargetContentLabels())
                .otherCrowdPackIdsGroup(unit.getOtherCrowdPackIdsGroup())
                .targetRules(unit.getTargetRules())
                .promotionPurposeType(campaign.getPromotionPurposeType())
                .installedUserFilter(unit.getInstalledUserFilter())
                .ocpcTarget(unit.getOcpcTarget() == null ? 0 : unit.getOcpcTarget())
                .professionInterestIds(unit.getProfessionInterestIds())
                .build();

        // 定向校验
        validateTarget(operator.getOperatorId(), unitTargetDto, adpVersion);
        validateExtraTarget(unit.getExtraTarget());
        validateAppPackage(unit.getAppPackageId());

        validateProductGroup(campaign.getPromotionPurposeType(), unit.getProductGroupId(), campaign.getAccountId());

        // nobid 的出价方式将 nobidMax 清空
        if (!Utils.isPositive(unit.getIsNoBid())) {
            unit.setNoBidMax(0);
        }
    }

    /**
     * 检查 nobid 单元预算情况和修改次数
     *
     * @param campaign
     */
    public void validateNobidUnitBudgetAndCount(Integer unitId, Integer dailyBudgetType, Integer isNobid,
                                                CpcCampaignDto campaign) {
        if (Utils.isPositive(campaign.getIsManaged())) {
            return;
        }
        if (dailyBudgetType == DailyBudgetType.MANUAL.getCode()) {
            // NOBID单元当日预算修改次数不得超过 3 次
            unitBudgetCountComponent.checkNobidUnitBudgetUpdateCount(unitId, isNobid);
            // 校验 nobid 单元的当日预算情况
            nobidValidator.checkNoBidUnitBudgetIfNecessary(campaign.getCampaignId(), campaign.getBudget());
        }
    }

    public List<Device> getTargetDevices(List<TargetRule> targetRules) {
        return CollectionUtils.isEmpty(targetRules) ? Collections.emptyList() :
                targetRules.stream().filter(getTargetRulePredicate(TargetType.OS))
                        .map(TargetRule::getValueIds).flatMap(Collection::stream).distinct()
                        .map(Device::getByTargetId).collect(Collectors.toList());
    }

    // 新版定向包有相同的校验逻辑 见ResTargetPackageRuleUpgradeServiceImpl
    public void validateTarget(Integer accountId, UnitTargetDto unitTargetDto, int adpVersion) throws ServiceException {
        Assert.notNull(accountId, "账号ID不可为空");
        Assert.notNull(unitTargetDto, "定向信息不可为空");

        // 校验定向规则
        this.validateTargetRules(accountId, unitTargetDto.getTargetRules(), unitTargetDto.getOcpcTarget());
        // tag定向(视频关键词兴趣)
        this.validateTags(unitTargetDto.getTags());

        this.validateTags(unitTargetDto.getVideoTag() == null ? Collections.emptyList() : unitTargetDto.getVideoTag().getTags());

        this.validateCrowdPack(unitTargetDto.getCrowdPackIds(), unitTargetDto.getExcludeCrowdPackIds(), accountId, unitTargetDto.getOtherCrowdPackIdsGroup());

        this.validateBusinessInterest(unitTargetDto.getBusinessInterestIds());
        if (AdpVersion.isLegacy(adpVersion)) {
            this.validateContentLabelVideoTarget(unitTargetDto.getSlotGroupId(), unitTargetDto.getTargetVideoIds(), unitTargetDto.getTargetContentLabels());
        }

        validateInstalledUserFilterTarget(unitTargetDto.getPromotionPurposeType(), unitTargetDto.getInstalledUserFilter());

        validateVideoSecondPartition(unitTargetDto.getVideoSecondPartition());

        validateProfessionInterestCrowd(unitTargetDto.getProfessionInterestIds());

//        List<Integer> osIds = TargetUtils.getValueIdsByTargetType(unitTargetDto.getTargetRules(), TargetType.OS.getCode());
//        osVersionValidator.process(null, osIds);
//        biliClientVersionValidator.process(null, osIds);
    }

    /**
     * @param promotionPurposeType
     * @param installedUserFilterDto
     */
    private void validateInstalledUserFilterTarget(Integer promotionPurposeType, UnitTargetInstalledUserFilterDto installedUserFilterDto) {
        if (installedUserFilterDto == null) {
            return;
        }

        installedUserTargetValidator.validateInstalledUserFilterTarget(promotionPurposeType,
                installedUserFilterDto.getFilterType());

    }

    /**
     * 检验定向
     *
     * @param targetRules
     * @param ocpcTarget
     */
    private void validateTargetRules(Integer accountId, List<TargetRule> targetRules, Integer ocpcTarget) {
        if (CollectionUtils.isEmpty(targetRules)) {
            return;
        }

        validateAllTargetRules(targetRules);

        List<Device> devices = getTargetDevices(targetRules);
        long deviceBrand = targetRules.stream().filter(getTargetRulePredicate(TargetType.DEVICE_BRAND)).count();
        Assert.isTrue(deviceBrand == 0 || devices.isEmpty() || devices.contains(Device.ANDROID), "定向不包含安卓时不支持设备品牌定向");

        long appCategory = targetRules.stream().filter(getTargetRulePredicate(TargetType.APP_CATEGORY)).count();
        Assert.isTrue(appCategory == 0 || devices.isEmpty() || devices.contains(Device.ANDROID), "定向不包含安卓时不支持APP分类定向");

        long convertedUserFilter = targetRules.stream().filter(getTargetRulePredicate(TargetType.CONVERTED_USER_FILTER)).count();
        if (convertedUserFilter > 0 && ocpcTarget != null) {
            //为什么要加ocpcTarget != null的判断，因为这个还被别的地方引用了，没有传ocpcTarget
            Assert.isTrue(ocpcTarget.compareTo(0) > 0, "已转化用户过滤仅支持oCPC计费方式");
        }

        // 校验自定义年龄定向
        validateAgeCustomizeTarget(targetRules);
    }

    private void validateIntelligentMass() {

    }

    /**
     * 检验自定义年龄定向
     *
     * @param targetRules
     */
    @VisibleForTesting
    public void validateAgeCustomizeTarget(List<TargetRule> targetRules) {
        if (CollectionUtils.isEmpty(targetRules)) {
            return;
        }

        // 自定义年龄定向
        Optional<TargetRule> ageCustomizeTargetOption = targetRules.stream().filter(t -> t.getRuleType() == TargetType.AGE_CUSTOMIZE.getCode()).findFirst();
        Optional<TargetRule> ageTargetOption = targetRules.stream().filter(t -> t.getRuleType() == TargetType.AGE.getCode()).findFirst();
        // 没有自定义年龄
        if (!ageCustomizeTargetOption.isPresent()) {
            return;
        }

        TargetRule ageCustomizeTargetRule = ageCustomizeTargetOption.get();
        List<Integer> valueIdsOfAgeCustomize = ageCustomizeTargetRule.getValueIds();

        LOGGER.info("validateAgeCustomizeTarget, valueIdsOfAgeCustomize:{}", JSON.toJSONString(valueIdsOfAgeCustomize));
        if (CollectionUtils.isEmpty(valueIdsOfAgeCustomize)) {
            return;
        }

        // 上下限可以设定18-60（包含18、60）
        if (valueIdsOfAgeCustomize.size() < 2) {
            throw new ServiceRuntimeException("自定义年龄定向参数错误!");
        }
        if (valueIdsOfAgeCustomize.get(0) < AGE_CUSTOMIZE_MIN) {
            throw new ServiceRuntimeException("自定义年龄不能小于" + AGE_CUSTOMIZE_MIN + "岁!");
        }
        if (valueIdsOfAgeCustomize.get(1) > AGE_CUSTOMIZE_MAX) {
            throw new ServiceRuntimeException("自定义年龄不能大于" + AGE_CUSTOMIZE_MAX + "岁!");
        }
        // 年龄段最窄为两岁
        if (valueIdsOfAgeCustomize.get(1) - valueIdsOfAgeCustomize.get(0) <= 1){
            throw new ServiceRuntimeException("自定义年龄段最窄为两岁!");
        }

        // 自定义年龄与旧的年龄段不能同时填写
        if (ageTargetOption.isPresent()) {
            List<Integer> valueIdsOfAge = ageTargetOption.get().getValueIds();
            LOGGER.info("validateAgeCustomizeTarget, valueIdsOfAge:{}", JSON.toJSONString(valueIdsOfAge));

            if (!CollectionUtils.isEmpty(valueIdsOfAge) && valueIdsOfAge.stream().anyMatch(t -> Utils.isPositive(t))) {
                throw new ServiceRuntimeException("自定义年龄与旧的年龄段不能同时填写!");
            }
        }
    }

    private Predicate<TargetRule> getTargetRulePredicate(TargetType appCategory) {
        return tr -> appCategory.getCode() == tr.getRuleType()
                && !CollectionUtils.isEmpty(tr.getValueIds())
                && !tr.getValueIds().containsAll(Lists.newArrayList(-1));
    }

    /**
     * 校验单元投放日期
     *
     * @param unit
     * @throws ServiceException
     */
    private void validateDate(NewCpcUnitDto unit) throws ServiceException {
        String beginDate = unit.getLaunchBeginDate();
        String endDate = unit.getLaunchEndDate();

        // 开始时间格式
        if (!LaunchUtil.isValidDate(beginDate, Constants.YYYY_MM_DD)) {
            throw new ServiceException(LaunchExceptionCode.UNIT_BEGIN_DATE_VALID);
        }
        // 开始日期必须大于等于当前日期
        if (LaunchUtil.compareDate(beginDate, LaunchUtil.getStringDate(0)) < 0) {
            throw new ServiceException(LaunchExceptionCode.UNIT_BEGIN_DATE_LESS_THAN_CURRENT_DATE);
        }

        // 结束时间可以为空
        if (Strings.isNullOrEmpty(endDate)) {
            return;
        }

        // 结束时间格式
        if (!LaunchUtil.isValidDate(endDate, Constants.YYYY_MM_DD)) {
            throw new ServiceException(LaunchExceptionCode.UNIT_END_DATE_VALID);
        }
        // 结束日期必须大于等于当前日期
        if (LaunchUtil.compareDate(endDate, LaunchUtil.getStringDate(0)) < 0) {
            throw new ServiceException(LaunchExceptionCode.UNIT_END_DATE_LESS_THAN_CURRENT_DATE);
        }
        // 结束日期必须大于等于开始日期
        if (LaunchUtil.compareDate(beginDate, endDate) > 0) {
            throw new ServiceException(LaunchExceptionCode.UNIT_END_DATE_LESS_THAN_BEGIN_DATE);
        }
    }

    private void validateSlotSelected(Integer salesType, Integer slotGroupId, Operator operator) throws ServiceException {
        if (slotGroupId == null || slotGroupId < 1) {
            throw new ServiceException(LaunchExceptionCode.UNIT_SLOT_GROUP_NO_EXIST);
        }

        if (OperatorType.PERSON_FLY == operator.getOperatorType()) {
            Assert.isTrue(upOrderConfig.getAllSlotGroupIds().contains(slotGroupId)
                    || upOrderConfig.getLiveSlotGroupId().equals(slotGroupId)
                    || upOrderConfig.getFlySlotArchiveStory().equals(slotGroupId), "所选的广告位组不属于该用户可用的广告位组");
            return;
        }

        List<Integer> slotGroupIds = resSlotGroupService.getValidGroupBySysType(salesType, operator.getOperatorId());

        Assert.notEmpty(slotGroupIds, "该用户没有有效的广告位组");
        Assert.isTrue(slotGroupIds.contains(slotGroupId), "所选的广告位组不属于该用户可用的广告位组");
    }

    private void validateLaunchTime(String launchTime) throws ServiceException {
        if (!Strings.isNullOrEmpty(launchTime)) {
            Matcher m = Constants.LAUNCH_TIME_PATTERN.matcher(launchTime);
            if (launchTime.length() != Constants.LAUNCH_TIME_LENGTH || !m.matches()) {
                throw new ServiceException(LaunchExceptionCode.UNIT_LAUNCH_TIME_VALID);
            }
        }
    }

    private void validateCrowdPack(List<Integer> crowdPackIds, List<Integer> excludeCrowdPackIds,
                                   Integer accountId, List<List<Integer>> otherCrowdPackIdsGroup) throws ServiceException {
        Set<Integer> cpIdSet = Sets.newHashSet();

        if (!CollectionUtils.isEmpty(crowdPackIds)) {
            Assert.isTrue(crowdPackIds.size() <= crowPackageCountLimit, "人群包上限:" + crowPackageCountLimit);
            cpIdSet.addAll(crowdPackIds);
        }

        if (!CollectionUtils.isEmpty(excludeCrowdPackIds)) {
            Assert.isTrue(excludeCrowdPackIds.size() <= crowPackageCountLimit, "指定人群包上限:" + crowPackageCountLimit);
            cpIdSet.addAll(excludeCrowdPackIds);
        }

        if (!CollectionUtils.isEmpty(otherCrowdPackIdsGroup)) {
            cpIdSet.addAll(otherCrowdPackIdsGroup
                    .stream()
                    .filter(list -> !CollectionUtils.isEmpty(list))
                    .flatMap(list -> list.stream())
                    .distinct()
                    .collect(Collectors.toList()));
        }

        //  https://www.tapd.bilibili.co/********/prong/stories/view/11********002919213
        //  指定了一个复合人群包后，支持使用排除，但不可排除复合人群包，可排除多个普通人群包；
        //  排除了一个复合人群包后，不支持使用指定；
        //  crowdPackService.validateCrowdPack(cpIdSet, accountId)是一个比较宽松的校验，暂时不改动
        crowdPackService.validateCrowdPack(cpIdSet, accountId);
    }

    /**
     * 校验计划下单元数
     *
     * @param campaignId
     * @throws ServiceException
     */
    private void validateUnitCount(Integer campaignId) throws ServiceException {
        // 计划下最大单元数 999
        int count = unitDao.getUnitCountInStatusByCampaignId(campaignId, Lists.newArrayList());
        if (count >= LaunchConstant.MAX_UNIT_NUM) {
            throw new ServiceException(LaunchExceptionCode.UNIT_NUM_EXCEED_MAX);
        }
    }

    private void validateUpdateDate(UpdateCpcUnitDto unit, CpcUnitDto oldUnit) throws ServiceException {
        String beginDate = unit.getLaunchBeginDate();
        String endDate = unit.getLaunchEndDate();

        if (Strings.isNullOrEmpty(endDate) && LaunchUtil.getDays(oldUnit.getLaunchEndDate(), oldUnit.getLaunchBeginDate()) > 50 * 365) {
            unit.setLaunchEndDate(oldUnit.getLaunchEndDate());
            endDate = oldUnit.getLaunchEndDate();
        } else if (Strings.isNullOrEmpty(endDate)) {
            endDate = LaunchUtil.getStringDate(Calendar.YEAR, 100);
            unit.setLaunchEndDate(endDate);
        }

        if (!LaunchUtil.isValidDate(beginDate, Constants.YYYY_MM_DD)) {
            throw new ServiceException(LaunchExceptionCode.UNIT_BEGIN_DATE_VALID);
        }
        if (!LaunchUtil.isValidDate(endDate, Constants.YYYY_MM_DD)) {
            throw new ServiceException(LaunchExceptionCode.UNIT_END_DATE_VALID);
        }
        if (LaunchUtil.compareDate(beginDate, endDate) > 0) {
            throw new ServiceException(LaunchExceptionCode.UNIT_END_DATE_LESS_THAN_BEGIN_DATE);
        }

        String oldBeginDate = oldUnit.getLaunchBeginDate();
        String oldEndDate = oldUnit.getLaunchEndDate();
        String currentDate = LaunchUtil.getStringDate(0);

        if (LaunchUtil.compareDate(oldEndDate, currentDate) < 0) {//投放已结�
            if (LaunchUtil.compareDate(beginDate, oldBeginDate) != 0) {//开始日期不可更�
                throw new ServiceException(LaunchExceptionCode.UNIT_ALREADY_CLOSE_BEGIN_DATE_NOT_EQUAL);
            }
            if (LaunchUtil.compareDate(endDate, oldEndDate) != 0
                    && LaunchUtil.compareDate(endDate, currentDate) < 0) {//结束日期修改后必须大于等于当前日�
                throw new ServiceException(LaunchExceptionCode.UNIT_ALREADY_CLOSE_END_DATE_LESS_THAN_CURRENT_DATE);
            }
        } else if (LaunchUtil.compareDate(oldBeginDate, currentDate) > 0) {//投放未结束，且未开�
            if (LaunchUtil.compareDate(beginDate, currentDate) < 0) {//开始日期修改后必须大于等于当前日期
                throw new ServiceException(LaunchExceptionCode.UNIT_NOT_START_BEGIN_DATE_LESS_THAN_CURRENT_DATE);
            }
            if (LaunchUtil.compareDate(endDate, currentDate) < 0) {//结束日期修改后必须大于等于当前日�
                throw new ServiceException(LaunchExceptionCode.UNIT_NOT_START_END_DATE_LESS_THAN_CURRENT_DATE);
            }
        } else {//投放已开始，未结�
            if (LaunchUtil.compareDate(beginDate, oldBeginDate) != 0) {//开始日期不可更�
                throw new ServiceException(LaunchExceptionCode.UNIT_ALREADY_START_BEGIN_DATE_NOT_CHANGE);
            }
            if (LaunchUtil.compareDate(endDate, currentDate) < 0) {//修改后的结束日期必须大于等于当前日期
                throw new ServiceException(LaunchExceptionCode.UNIT_ALREADY_START_END_DATE_LESS_THAN_CURRENT_DATE);
            }
        }
    }

    private void validateAppPackage(Integer appPackageId) {
        if (!Utils.isPositive(appPackageId)) return;

        final AppPackageDto dto = appPackageService.load(appPackageId);
        Assert.notNull(dto, "应用包不存在");
    }

    private void validateTags(List<String> tags) throws ServiceException {
        if (CollectionUtils.isEmpty(tags)) {
            return;
        }

        lauTagIdsService.validateTags(tags);
    }

    public void validateArchiveContent(PromotionPurposeType ppt, Long mid, Long videoId, Integer launchVideoType) throws ServiceException {
        // 推广目的不是: 投稿内容，不校验
        if (!PromotionPurposeType.ARCHIVE_CONTENT.equals(ppt)) {
            return;
        }

        Assert.notNull(mid, "mid不能为空");
        Assert.notNull(videoId, "视频id不能为空");
        Assert.notNull(launchVideoType, "视频商业类型不能为空");

        LaunchVideoType type = LaunchVideoType.getByCode(launchVideoType);

        // 非商业内容 || 代投
        if (LaunchVideoType.NOT_BUSINESS.equals(type)||LaunchVideoType.PROXY.equals(type)) {
            ArchiveDetail archiveDetail = archiveManager.queryArchivesByAid(videoId);
            Assert.notNull(archiveDetail, "获取该视频详情失败");

            ArchiveBase archive = archiveDetail.getArchive();
            Integer attribute = Values.zeroIfNull(archive.getAttribute());

            Assert.isTrue(archive.getOrder_id() == null || archive.getOrder_id() == 0, "含商业内容的视频不可投放");
            Assert.isTrue(Utils.getIntegerBit(attribute, PRIVATE_ORDER_INDEX) == 0, "含商业内容的视频不可投放");

            List<PickupOrderDto> pickupOrderDtos = pickupOrderQuerier.searchPickupOrder(videoId);

            Map<Long,PickupOrderDto> flowerFireAidMap = CollectionUtils.isEmpty(pickupOrderDtos) ? new HashMap<>() :
                    pickupOrderDtos.stream()
                            .collect(Collectors.toMap(PickupOrderDto::getAvId, a -> a, (k1,k2)->k1));
            Assert.isTrue(!flowerFireAidMap.containsKey(videoId) ||
                    flowerFireAidMap.get(videoId).getLongUpperMid().equals(archiveDetail.getArchive().getMid()),
                    "含商业内容的视频不可投放");
        }
    }

    /**
     * 验证参数是否符合应用商店直投要求
     *
     * @param promotionPurposeType 推广目的
     * @param isStoreDirectLaunch 是否应用商店直投
     * @param ocpcTarget ocpc优化目标
     * @param appPackageId app包Id
     * @throws ServiceException
     */
    private void validateStoreDirectLaunch(Integer promotionPurposeType, Integer isStoreDirectLaunch, Integer ocpcTarget, Integer appPackageId) throws ServiceException {
        boolean supportStoreDirectLaunchPromotionPurposeType = STORE_DIRECT_LAUNCH_SUPPORT_UNIT_PPT_SET.contains(promotionPurposeType);
        if (supportStoreDirectLaunchPromotionPurposeType
                && UnitIsStoreDirectLaunchType.YES.getCode().equals(isStoreDirectLaunch)) {
            Assert.notNull(appPackageId, "应用包不能为空");
            AppPackageDto appPackageDto = appPackageService.load(appPackageId);
            //推广目的为应用下载，应用包平台为安卓，勾选了应用商店直投且选择了ocpc投放(ocpcTarget>0)，且推广目的为应用推广/商品目录
            // 但优化目标未选择为应用激活、订单提交、用户注册、应用内付费则抛出异常，取消更新
            if (AppPlatformType.ANDROID.getCode().equals(appPackageDto.getPlatform())
                    && ocpcTarget > 0
                    && (PromotionPurposeType.APP_DOWNLOAD.getCode() == promotionPurposeType || PromotionPurposeType.GOODS_CATALOG.getCode() == promotionPurposeType)
                    && !supportStoreDirectLaunchOcpcTargets.contains(ocpcTarget)) {
                throw new ServiceException(LaunchExceptionCode.UNIT_OCPC_TARGET_NOT_MATCH_STORE_DIRECT_LAUNCH);
            }
        }
    }

    /**
     * 校验商品组
     *
     * @param promotionPurposeType
     * @param productGroupId
     * @param accountId
     */
    public void validateProductGroup(Integer promotionPurposeType, Integer productGroupId, Integer accountId) {
        // 商品目录才需要校验
        if (!new Integer(PromotionPurposeType.GOODS_CATALOG.getCode()).equals(promotionPurposeType)) {
            return;
        }

        if (Utils.isPositive(productGroupId)) {
            // 查询商品组是否存在
            LauProductGroupDto groupDto = lauProductGroupService.getGroupById(productGroupId, accountId);
            Assert.notNull(groupDto, "商品组不存在");
        }
    }

    private final static int RES_TARGET_PLATFORM_ANDROID = 399;

    private final static int RES_TARGET_INTELLIGENT_MASS_DEVICE = 635;

    public void validateOsMustOnlyAndroid(List<Integer> os){
        Assert.isTrue(!CollectionUtils.isEmpty(os) && os.size() == 1 &&
                os.contains(RES_TARGET_PLATFORM_ANDROID),"安卓游戏、品牌传播（选择了安卓游戏）推广目的下，仅支持安卓设备定向");
    }

    public void validateIntelligentMassNotDevice(List<Integer> os){
        Assert.isTrue(!os.contains(RES_TARGET_INTELLIGENT_MASS_DEVICE),"安卓游戏推广目的下，不支持智能放量突破设备");
    }

    public void validateAllTargetRules(List<TargetRule> targetRules) {
        if (CollectionUtils.isEmpty(targetRules)) {
            return;
        }

        List<Integer> targetRuleIds = targetRules.stream()
                .filter(Objects::nonNull)
                .map(TargetRule::getValueIds)
                .filter(ids -> !CollectionUtils.isEmpty(ids))
                .flatMap(Collection::stream)
                .filter(id -> !Objects.equals(-1, id))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(targetRuleIds)) {
            return;
        }

        Assert.isTrue(targetRuleIds.stream().allMatch(Objects::nonNull),
                "定向条件具体id不可为null");

        Set<Integer> queryIdSet = new HashSet<>(targetRuleIds);

        Map<Integer, List<ResTargetItemDto>> validTargetRootItemMap =
                resTargetItemService.getValidTargetRootItemMap(queryIdSet);

        targetRules.forEach(targetRule -> {
            if (CollectionUtils.isEmpty(targetRule.getValueIds())) {
                return;
            }
            List<Integer> itemIds = targetRule.getValueIds().stream()
                    .filter(id -> !Objects.equals(-1, id))
                    .collect(Collectors.toList());
            Integer targetType = targetRule.getRuleType();
            validateByType(itemIds, targetType, validTargetRootItemMap);
        });
    }

    private void validateByType(List<Integer> itemIds,
                                Integer targetType,
                                Map<Integer, List<ResTargetItemDto>> validTargetRootItemMap) {
        if (CollectionUtils.isEmpty(itemIds)) {
            return;
        }

        // 产品要求 只校验视频分区定向@chill
        //定向迭代、老三连单元创编加上地域定向校验，旧批量操作加上地域定向校验
        if (Objects.equals(TargetType.VIDEO_PARTITION.getCode(), targetType)
        || Objects.equals(TargetType.AREA.getCode(), targetType)
        ) {
            String targetTypeName = TargetType.getByCode(targetType).getName();
            List<ResTargetItemDto> resTargetItemBos = validTargetRootItemMap.get(targetType);
            Assert.notEmpty(resTargetItemBos, "不存在对应的定向, targetType:" + targetTypeName);

            Set<Integer> resTargetItemIdSet = resTargetItemBos.stream()
                    .map(ResTargetItemDto::getId)
                    .collect(Collectors.toSet());

            Assert.isTrue(resTargetItemIdSet.containsAll(itemIds), targetTypeName + "定向部分id不存在");
        }
    }

    @SneakyThrows
    private void validateVideoSecondPartition(List<Integer> videoSecondPartition) {
        if (CollectionUtils.isEmpty(videoSecondPartition)) {
            return;
        }
        List<ArchivePartition> allVideoPartitions = archiveManager.getAllVideoPartitions();
        // 是否只认最底层id？
        Set<Integer> allVideoPartitionIdSet = allVideoPartitions.stream()
                .map(ArchivePartition::getId)
                .collect(Collectors.toSet());
        Assert.isTrue(allVideoPartitionIdSet.containsAll(videoSecondPartition), "部分视频分区兴趣定向不存在");

    }

    private void validateProfessionInterestCrowd(List<Integer> professionInterestIds) {
        if (CollectionUtils.isEmpty(professionInterestIds)) {
            return;
        }

        List<ResProfessionInterestCrowdsPo> resProfessionInterestCrowdsPos =
                resProfessionInterestService.allValidProfessionInterest();
        Set<Integer> validProfessionInterestIdSet = resProfessionInterestCrowdsPos.stream()
                .map(ResProfessionInterestCrowdsPo::getCrowdId)
                .collect(Collectors.toSet());
        Assert.isTrue(validProfessionInterestIdSet.containsAll(professionInterestIds), "部分行业兴趣人群不存在");
    }


}
