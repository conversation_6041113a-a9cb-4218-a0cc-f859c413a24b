package com.bilibili.adp.cpc.wrapper.bos;

import com.bilibili.adp.cpc.core.bos.CreativeComponentBo;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CreativeWrapperBo {
    @ApiModelProperty("创意id")
    private Integer creativeId;
    @ApiModelProperty("名称")
    private String creativeName;
    @ApiModelProperty("描述")
    private String description;
    @ApiModelProperty("长描述")
    private String extDescription;
    @ApiModelProperty("跳转链接")
    private String jumpUrl;
    @ApiModelProperty("建站页id")
    private Long mgkPageId;
    @ApiModelProperty("唤起链接")
    private String schemeUrl;
    @ApiModelProperty("商业标")
    private Integer busMarkId;
    @ApiModelProperty("资质id列表")
    private List<Integer> qualificationIds;

    @ApiModelProperty("创意资质包ID")
    private Integer qualificationPackageId;

    @ApiModelProperty("转化组件列表")
    private List<CreativeComponentBo> components;
    @ApiModelProperty("多物料(程序化创意)")
    private MultiMaterialWrapperBo multiMaterial;
    @ApiModelProperty("是否为mapi来源创建")
    private Integer flag;
}
