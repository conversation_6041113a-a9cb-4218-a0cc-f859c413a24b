package com.bilibili.adp.cpc.splash_screen.creative.converter;

import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauSplashScreenCreativeButtonPo;
import com.bilibili.adp.cpc.splash_screen.creative.bos.SplashScreenButton;
import com.bilibili.adp.cpc.splash_screen.creative.enums.ButtonStyle;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper
public interface ButtonConverter {
    ButtonConverter MAPPER = Mappers.getMapper(ButtonConverter.class);

    @Mapping(target = "textColorDay", source = "buttonStyle.textColor")
    @Mapping(target = "textColorNight", source = "buttonStyle.textColor")
    @Mapping(target = "bgColorDay", source = "buttonStyle.bgColor")
    @Mapping(target = "bgColorNight", source = "buttonStyle.bgColor")
    @Mapping(target = "buttonId", ignore = true)
    @Mapping(target = "ctime", ignore = true)
    @Mapping(target = "isDeleted", ignore = true)
    @Mapping(target = "mtime", ignore = true)
    LauSplashScreenCreativeButtonPo composeButton(Integer creativeId, ButtonStyle buttonStyle, SplashScreenButton button, String relatedIds);
}
