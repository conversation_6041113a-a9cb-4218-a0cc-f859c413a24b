package com.bilibili.adp.cpc.dao.querydsl_clickhouse.pos;

import javax.annotation.Generated;

/**
 * DwsFlowProgrammaticCreativeStatL1hr1dPo is a Querydsl bean type
 */
@Generated("com.querydsl.codegen.BeanSerializer")
public class DwsFlowProgrammaticCreativeStatL1hr1dPo {

    private String accountId;

    private Long accountSubscribe;

    private Long actionValid;

    private Long active;

    private String adType;

    private Long androidFirstActive;

    private Long appCallup;

    private Long apply;

    private Long click;

    private Long clueValid;

    private Long commentClick;

    private String concatMaterialId;

    private Float cost;

    private String creativeId;

    private Long credit;

    private Long downloadSuccess;

    private Long enterpriseAccountSubscribe;

    private Long fanIncrease;

    private Long firstCommentCopy;

    private Long firstOrderPlace;

    private Long formSubmit;

    private Long formUserCost;

    private Long gameActiveApi;

    private Long gameSubscribe;

    private Long installSuccess;

    private Long iosFirstActive;

    private java.sql.Date logDate;

    private String logHour;

    private Long lpCallup;

    private Long lpCallupSucc;

    private Long lpCallupSuccStay;

    private String materialConcact4Id;

    private String materialTypeId1;

    private String materialTypeId2;

    private String materialTypeId3;

    private String materialTypeId4;

    private String materialTypeId8;

    private Long orderPay;

    private Long orderPayCount;

    private Long orderPlace;

    private Long orderPlaceCount;

    private Long pv;

    private Long retention;

    private String srcId;

    private String unitId;

    private Long userCost;

    private Long userRegister;

    private Long videoPlay;

    private Long withdrawDeposits;

    private Long wxAddFans;

    private Long wxCopy;

    public String getAccountId() {
        return accountId;
    }

    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    public Long getAccountSubscribe() {
        return accountSubscribe;
    }

    public void setAccountSubscribe(Long accountSubscribe) {
        this.accountSubscribe = accountSubscribe;
    }

    public Long getActionValid() {
        return actionValid;
    }

    public void setActionValid(Long actionValid) {
        this.actionValid = actionValid;
    }

    public Long getActive() {
        return active;
    }

    public void setActive(Long active) {
        this.active = active;
    }

    public String getAdType() {
        return adType;
    }

    public void setAdType(String adType) {
        this.adType = adType;
    }

    public Long getAndroidFirstActive() {
        return androidFirstActive;
    }

    public void setAndroidFirstActive(Long androidFirstActive) {
        this.androidFirstActive = androidFirstActive;
    }

    public Long getAppCallup() {
        return appCallup;
    }

    public void setAppCallup(Long appCallup) {
        this.appCallup = appCallup;
    }

    public Long getApply() {
        return apply;
    }

    public void setApply(Long apply) {
        this.apply = apply;
    }

    public Long getClick() {
        return click;
    }

    public void setClick(Long click) {
        this.click = click;
    }

    public Long getClueValid() {
        return clueValid;
    }

    public void setClueValid(Long clueValid) {
        this.clueValid = clueValid;
    }

    public Long getCommentClick() {
        return commentClick;
    }

    public void setCommentClick(Long commentClick) {
        this.commentClick = commentClick;
    }

    public String getConcatMaterialId() {
        return concatMaterialId;
    }

    public void setConcatMaterialId(String concatMaterialId) {
        this.concatMaterialId = concatMaterialId;
    }

    public Float getCost() {
        return cost;
    }

    public void setCost(Float cost) {
        this.cost = cost;
    }

    public String getCreativeId() {
        return creativeId;
    }

    public void setCreativeId(String creativeId) {
        this.creativeId = creativeId;
    }

    public Long getCredit() {
        return credit;
    }

    public void setCredit(Long credit) {
        this.credit = credit;
    }

    public Long getDownloadSuccess() {
        return downloadSuccess;
    }

    public void setDownloadSuccess(Long downloadSuccess) {
        this.downloadSuccess = downloadSuccess;
    }

    public Long getEnterpriseAccountSubscribe() {
        return enterpriseAccountSubscribe;
    }

    public void setEnterpriseAccountSubscribe(Long enterpriseAccountSubscribe) {
        this.enterpriseAccountSubscribe = enterpriseAccountSubscribe;
    }

    public Long getFanIncrease() {
        return fanIncrease;
    }

    public void setFanIncrease(Long fanIncrease) {
        this.fanIncrease = fanIncrease;
    }

    public Long getFirstCommentCopy() {
        return firstCommentCopy;
    }

    public void setFirstCommentCopy(Long firstCommentCopy) {
        this.firstCommentCopy = firstCommentCopy;
    }

    public Long getFirstOrderPlace() {
        return firstOrderPlace;
    }

    public void setFirstOrderPlace(Long firstOrderPlace) {
        this.firstOrderPlace = firstOrderPlace;
    }

    public Long getFormSubmit() {
        return formSubmit;
    }

    public void setFormSubmit(Long formSubmit) {
        this.formSubmit = formSubmit;
    }

    public Long getFormUserCost() {
        return formUserCost;
    }

    public void setFormUserCost(Long formUserCost) {
        this.formUserCost = formUserCost;
    }

    public Long getGameActiveApi() {
        return gameActiveApi;
    }

    public void setGameActiveApi(Long gameActiveApi) {
        this.gameActiveApi = gameActiveApi;
    }

    public Long getGameSubscribe() {
        return gameSubscribe;
    }

    public void setGameSubscribe(Long gameSubscribe) {
        this.gameSubscribe = gameSubscribe;
    }

    public Long getInstallSuccess() {
        return installSuccess;
    }

    public void setInstallSuccess(Long installSuccess) {
        this.installSuccess = installSuccess;
    }

    public Long getIosFirstActive() {
        return iosFirstActive;
    }

    public void setIosFirstActive(Long iosFirstActive) {
        this.iosFirstActive = iosFirstActive;
    }

    public java.sql.Date getLogDate() {
        return logDate;
    }

    public void setLogDate(java.sql.Date logDate) {
        this.logDate = logDate;
    }

    public String getLogHour() {
        return logHour;
    }

    public void setLogHour(String logHour) {
        this.logHour = logHour;
    }

    public Long getLpCallup() {
        return lpCallup;
    }

    public void setLpCallup(Long lpCallup) {
        this.lpCallup = lpCallup;
    }

    public Long getLpCallupSucc() {
        return lpCallupSucc;
    }

    public void setLpCallupSucc(Long lpCallupSucc) {
        this.lpCallupSucc = lpCallupSucc;
    }

    public Long getLpCallupSuccStay() {
        return lpCallupSuccStay;
    }

    public void setLpCallupSuccStay(Long lpCallupSuccStay) {
        this.lpCallupSuccStay = lpCallupSuccStay;
    }

    public String getMaterialConcact4Id() {
        return materialConcact4Id;
    }

    public void setMaterialConcact4Id(String materialConcact4Id) {
        this.materialConcact4Id = materialConcact4Id;
    }

    public String getMaterialTypeId1() {
        return materialTypeId1;
    }

    public void setMaterialTypeId1(String materialTypeId1) {
        this.materialTypeId1 = materialTypeId1;
    }

    public String getMaterialTypeId2() {
        return materialTypeId2;
    }

    public void setMaterialTypeId2(String materialTypeId2) {
        this.materialTypeId2 = materialTypeId2;
    }

    public String getMaterialTypeId3() {
        return materialTypeId3;
    }

    public void setMaterialTypeId3(String materialTypeId3) {
        this.materialTypeId3 = materialTypeId3;
    }

    public String getMaterialTypeId4() {
        return materialTypeId4;
    }

    public void setMaterialTypeId4(String materialTypeId4) {
        this.materialTypeId4 = materialTypeId4;
    }

    public String getMaterialTypeId8() {
        return materialTypeId8;
    }

    public void setMaterialTypeId8(String materialTypeId8) {
        this.materialTypeId8 = materialTypeId8;
    }

    public Long getOrderPay() {
        return orderPay;
    }

    public void setOrderPay(Long orderPay) {
        this.orderPay = orderPay;
    }

    public Long getOrderPayCount() {
        return orderPayCount;
    }

    public void setOrderPayCount(Long orderPayCount) {
        this.orderPayCount = orderPayCount;
    }

    public Long getOrderPlace() {
        return orderPlace;
    }

    public void setOrderPlace(Long orderPlace) {
        this.orderPlace = orderPlace;
    }

    public Long getOrderPlaceCount() {
        return orderPlaceCount;
    }

    public void setOrderPlaceCount(Long orderPlaceCount) {
        this.orderPlaceCount = orderPlaceCount;
    }

    public Long getPv() {
        return pv;
    }

    public void setPv(Long pv) {
        this.pv = pv;
    }

    public Long getRetention() {
        return retention;
    }

    public void setRetention(Long retention) {
        this.retention = retention;
    }

    public String getSrcId() {
        return srcId;
    }

    public void setSrcId(String srcId) {
        this.srcId = srcId;
    }

    public String getUnitId() {
        return unitId;
    }

    public void setUnitId(String unitId) {
        this.unitId = unitId;
    }

    public Long getUserCost() {
        return userCost;
    }

    public void setUserCost(Long userCost) {
        this.userCost = userCost;
    }

    public Long getUserRegister() {
        return userRegister;
    }

    public void setUserRegister(Long userRegister) {
        this.userRegister = userRegister;
    }

    public Long getVideoPlay() {
        return videoPlay;
    }

    public void setVideoPlay(Long videoPlay) {
        this.videoPlay = videoPlay;
    }

    public Long getWithdrawDeposits() {
        return withdrawDeposits;
    }

    public void setWithdrawDeposits(Long withdrawDeposits) {
        this.withdrawDeposits = withdrawDeposits;
    }

    public Long getWxAddFans() {
        return wxAddFans;
    }

    public void setWxAddFans(Long wxAddFans) {
        this.wxAddFans = wxAddFans;
    }

    public Long getWxCopy() {
        return wxCopy;
    }

    public void setWxCopy(Long wxCopy) {
        this.wxCopy = wxCopy;
    }

    @Override
    public String toString() {
         return "accountId = " + accountId + ", accountSubscribe = " + accountSubscribe + ", actionValid = " + actionValid + ", active = " + active + ", adType = " + adType + ", androidFirstActive = " + androidFirstActive + ", appCallup = " + appCallup + ", apply = " + apply + ", click = " + click + ", clueValid = " + clueValid + ", commentClick = " + commentClick + ", concatMaterialId = " + concatMaterialId + ", cost = " + cost + ", creativeId = " + creativeId + ", credit = " + credit + ", downloadSuccess = " + downloadSuccess + ", enterpriseAccountSubscribe = " + enterpriseAccountSubscribe + ", fanIncrease = " + fanIncrease + ", firstCommentCopy = " + firstCommentCopy + ", firstOrderPlace = " + firstOrderPlace + ", formSubmit = " + formSubmit + ", formUserCost = " + formUserCost + ", gameActiveApi = " + gameActiveApi + ", gameSubscribe = " + gameSubscribe + ", installSuccess = " + installSuccess + ", iosFirstActive = " + iosFirstActive + ", logDate = " + logDate + ", logHour = " + logHour + ", lpCallup = " + lpCallup + ", lpCallupSucc = " + lpCallupSucc + ", lpCallupSuccStay = " + lpCallupSuccStay + ", materialConcact4Id = " + materialConcact4Id + ", materialTypeId1 = " + materialTypeId1 + ", materialTypeId2 = " + materialTypeId2 + ", materialTypeId3 = " + materialTypeId3 + ", materialTypeId4 = " + materialTypeId4 + ", materialTypeId8 = " + materialTypeId8 + ", orderPay = " + orderPay + ", orderPayCount = " + orderPayCount + ", orderPlace = " + orderPlace + ", orderPlaceCount = " + orderPlaceCount + ", pv = " + pv + ", retention = " + retention + ", srcId = " + srcId + ", unitId = " + unitId + ", userCost = " + userCost + ", userRegister = " + userRegister + ", videoPlay = " + videoPlay + ", withdrawDeposits = " + withdrawDeposits + ", wxAddFans = " + wxAddFans + ", wxCopy = " + wxCopy;
    }

}

