package com.bilibili.adp.cpc.biz.services.unit;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bapis.cpm.dmp.*;
import com.bilibili.adp.account.dto.AccountAllInfoDto;
import com.bilibili.adp.account.dto.AccountBaseDto;
import com.bilibili.adp.account.dto.AccountDto;
import com.bilibili.adp.account.service.IQueryAccountService;
import com.bilibili.adp.common.Constants;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.bean.TargetRule;
import com.bilibili.adp.common.config.DataSourceHolder;
import com.bilibili.adp.common.config.DataSourceTypeEnum;
import com.bilibili.adp.common.enums.*;
import com.bilibili.adp.common.enums.professional_fly.GdPlusFinishFlagEnum;
import com.bilibili.adp.common.enums.professional_fly.RecommendTypeEnum;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.exception.ServiceExtraException;
import com.bilibili.adp.common.exception.ServiceRuntimeException;
import com.bilibili.adp.common.util.*;
import com.bilibili.adp.common.util.func.Functions;
import com.bilibili.adp.common.util.func.RecDiffResult;
import com.bilibili.adp.cpc.biz.bos.archive.PgcArchiveBo;
import com.bilibili.adp.cpc.biz.bos.unit.QueryUnitBo;
import com.bilibili.adp.cpc.biz.services.account.LaunchAccountV1Service;
import com.bilibili.adp.cpc.biz.services.account.config.AccountConfig;
import com.bilibili.adp.cpc.biz.services.account.dto.log.UnitLogDto;
import com.bilibili.adp.cpc.biz.services.app.api.IAppPackageService;
import com.bilibili.adp.cpc.biz.services.archive.ArchiveService;
import com.bilibili.adp.cpc.biz.services.archive.MainArcAtmosphereService;
import com.bilibili.adp.cpc.biz.services.campaign.FlyCampaignService;
import com.bilibili.adp.cpc.biz.services.campaign.api.ICpcCampaignService;
import com.bilibili.adp.cpc.biz.services.campaign.api.ILauCampaignService;
import com.bilibili.adp.cpc.biz.services.campaign.dto.CpcCampaignDto;
import com.bilibili.adp.cpc.biz.services.campaign.dto.QueryCpcCampaignDto;
import com.bilibili.adp.cpc.biz.services.creative.*;
import com.bilibili.adp.cpc.biz.services.creative.bos.JumpInfoBo;
import com.bilibili.adp.cpc.biz.services.creative.config.AccLabelConfig;
import com.bilibili.adp.cpc.biz.services.creative.config.CreativePositionConfig;
import com.bilibili.adp.cpc.biz.services.dynamic.DynamicContentMap;
import com.bilibili.adp.cpc.biz.services.dynamic.DynamicLinkProc;
import com.bilibili.adp.cpc.biz.services.dynamic.DynamicQuerierProc;
import com.bilibili.adp.cpc.biz.services.dynamic.FlyDynamicService;
import com.bilibili.adp.cpc.biz.services.game.AdpCpcGameService;
import com.bilibili.adp.cpc.biz.services.game.LaunchUnitGameV2Service;
import com.bilibili.adp.cpc.biz.services.live.AdpCpcLiveReserveService;
import com.bilibili.adp.cpc.biz.services.live.bos.LiveReservationInfoBo;
import com.bilibili.adp.cpc.biz.services.log.DbTable;
import com.bilibili.adp.cpc.biz.services.log.ILogOperateNewService;
import com.bilibili.adp.cpc.biz.services.offline.LaunchOfflineService;
import com.bilibili.adp.cpc.biz.services.target_package.api.ICrowdPackService;
import com.bilibili.adp.cpc.biz.services.target_package.api.IResTargetItemService;
import com.bilibili.adp.cpc.biz.services.target_package.api.upgrade.IResTargetPackageCrowdUpgradeService;
import com.bilibili.adp.cpc.biz.services.target_package.api.upgrade.IResTargetPackageExtraTargetService;
import com.bilibili.adp.cpc.biz.services.target_package.api.upgrade.IResTargetPackageRuleUpgradeService;
import com.bilibili.adp.cpc.biz.services.target_package.api.upgrade.IResTargetPackageUpgradeService;
import com.bilibili.adp.cpc.biz.services.target_package.api.upgrade.dto.*;
import com.bilibili.adp.cpc.biz.services.unit.api.ILauBidTwoStageConfigService;
import com.bilibili.adp.cpc.biz.services.unit.api.ILauUnitTargetProfessionInterestAutoService;
import com.bilibili.adp.cpc.biz.services.unit.api.ILauUnitTargetProfessionInterestService;
import com.bilibili.adp.cpc.biz.services.unit.api.IQueryShopGoodsService;
import com.bilibili.adp.cpc.biz.services.unit.bos.*;
import com.bilibili.adp.cpc.biz.services.unit.dto.*;
import com.bilibili.adp.cpc.biz.validator.CpcUnitValidator;
import com.bilibili.adp.cpc.biz.validator.NoBidValidator;
import com.bilibili.adp.cpc.core.LaunchUnitGoodsService;
import com.bilibili.adp.cpc.core.LaunchUnitV1Service;
import com.bilibili.adp.cpc.core.constants.BusinessDomain;
import com.bilibili.adp.cpc.core.constants.CommonSwitchEnum;
import com.bilibili.adp.cpc.core.constants.IsInterestAuto;
import com.bilibili.adp.cpc.dao.ad.AdpLauUnitNextdayBudgetDao;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauUnitExtraPo;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauUnitGoodsPo;
import com.bilibili.adp.cpc.dao.querydsl.pos.LauGameInfoPo;
import com.bilibili.adp.cpc.dao.querydsl.pos.LauUnitPgcArchivePo;
import com.bilibili.adp.cpc.dto.*;
import com.bilibili.adp.cpc.enums.AdpVersion;
import com.bilibili.adp.cpc.enums.ForecastConvertUserFilterEnum;
import com.bilibili.adp.cpc.enums.InstalledUserFilterEnum;
import com.bilibili.adp.cpc.enums.InstalledUserTargetContentEnum;
import com.bilibili.adp.cpc.enums.ad.OcpcTargetEnum;
import com.bilibili.adp.cpc.enums.ad.PromotionPurposeType;
import com.bilibili.adp.cpc.enums.ad.UnitCountEnum;
import com.bilibili.adp.cpc.enums.ad_product.AdProductBindTypeEnum;
import com.bilibili.adp.cpc.po.ad.LauUnitFlyMiddleInfoPo;
import com.bilibili.adp.cpc.po.ad.LauUnitNextdayBudgetPo;
import com.bilibili.adp.cpc.po.ad.LauUnitNextdayBudgetPoExample;
import com.bilibili.adp.cpc.proxy.MainCommunityTagProxy;
import com.bilibili.adp.cpc.repo.AdProductRepo;
import com.bilibili.adp.cpc.repo.LauUnitFlyMiddleInfoRepo;
import com.bilibili.adp.cpc.repo.LauUnitPgcArchiveRepo;
import com.bilibili.adp.cpc.repo.UnitGoodsRepo;
import com.bilibili.adp.cpc.utils.AccountCostPriceBean;
import com.bilibili.adp.cpc.utils.TargetUtils;
import com.bilibili.adp.launch.api.common.*;
import com.bilibili.adp.launch.api.common.enums.FlagEnums;
import com.bilibili.adp.launch.api.creative.dto.CreativeSlotGroupTemplateDto;
import com.bilibili.adp.launch.api.creative.dto.LauSubjectDto;
import com.bilibili.adp.launch.api.flyPro.dto.enums.RoomPromoteScenesEnum;
import com.bilibili.adp.launch.api.flyPro.dto.v2.FlyCampaignDetailDto;
import com.bilibili.adp.launch.api.flyPro.dto.v2.FlyGdPlusUnitScheduleMappingDto;
import com.bilibili.adp.launch.api.flyPro.dto.v2.SplitDaysImpressDto;
import com.bilibili.adp.launch.api.launch.dto.GetBidCostParam;
import com.bilibili.adp.launch.api.launch.dto.LauCreativeTemplateBo;
import com.bilibili.adp.launch.api.launch.dto.QueryBidCostConfigParam;
import com.bilibili.adp.launch.api.launch.query.QueryBidTwoStageConfigParam;
import com.bilibili.adp.launch.api.launch.query.QueryLauCreativeTemplateBo;
import com.bilibili.adp.launch.api.minigame.dto.MiniGameMapDto;
import com.bilibili.adp.launch.api.minigame.dto.MiniGameTargetDto;
import com.bilibili.adp.launch.api.service.*;
import com.bilibili.adp.launch.api.unit.dto.LauTagIdsDto;
import com.bilibili.adp.launch.api.unit.dto.Unit;
import com.bilibili.adp.launch.api.unit.dto.UnitTargetTagDto;
import com.bilibili.adp.launch.api.unit.dto.UnitUpdateBidPriceQueryDto;
import com.bilibili.adp.launch.biz.ad_core.mybatis.*;
import com.bilibili.adp.launch.biz.ad_core.querydsl.dos.LauUnitDo;
import com.bilibili.adp.launch.biz.ad_core.querydsl.dos.LauUnitGameDo;
import com.bilibili.adp.launch.biz.ad_core.querydsl.dos.LauUnitTargetInstalledUserFilterDo;
import com.bilibili.adp.launch.biz.ad_data.dao.ResTargetMinigameMapDao;
import com.bilibili.adp.launch.biz.ad_data.po.ResTargetMinigameMapPo;
import com.bilibili.adp.launch.biz.ad_data.po.ResTargetMinigameMapPoExample;
import com.bilibili.adp.launch.biz.adp.unit.bos.LauUnitGameBo;
import com.bilibili.adp.launch.biz.common.LaunchUtil;
import com.bilibili.adp.launch.biz.common.OcpxMode;
import com.bilibili.adp.launch.biz.exception.LaunchExceptionCode;
import com.bilibili.adp.launch.biz.flyPro.FlyProLaunchService;
import com.bilibili.adp.launch.biz.flyPro.v2.FlyGdPlusDelegate;
import com.bilibili.adp.launch.biz.lau_dao.*;
import com.bilibili.adp.launch.biz.po.UnitTargetRulePo;
import com.bilibili.adp.launch.biz.pojo.*;
import com.bilibili.adp.launch.biz.query.qos.QLauUnitBudget;
import com.bilibili.adp.launch.biz.repo.LauUnitRepo;
import com.bilibili.adp.launch.biz.service.UnitBudgetCountComponent;
import com.bilibili.adp.launch.biz.service.unit.LaunchUnitLowestBidService;
import com.bilibili.adp.launch.biz.service.unit.LaunchUnitService;
import com.bilibili.adp.launch.biz.validator.FlyGdUnitValidator;
import com.bilibili.adp.legacy.LauCampaignDto;
import com.bilibili.adp.log.bean.DiffItem;
import com.bilibili.adp.log.dto.OperationType;
import com.bilibili.adp.log.service.ILogService;
import com.bilibili.adp.log.service.v6.ILogCpcOperationService;
import com.bilibili.adp.log.service.v6.bo.LogCpcOperationBo;
import com.bilibili.adp.passport.api.dto.ArchiveDetail;
import com.bilibili.adp.passport.api.dto.ArchiveVideoInfoDetailDto;
import com.bilibili.adp.passport.api.dto.GameDto;
import com.bilibili.adp.passport.api.dto.UserInfoDto;
import com.bilibili.adp.passport.api.service.IGameCenterService;
import com.bilibili.adp.passport.biz.manager.ArchiveManager;
import com.bilibili.adp.passport.biz.manager.LiveBroadcastHttpService;
import com.bilibili.adp.passport.biz.manager.bean.LiveBroadcastRoomInfo;
import com.bilibili.adp.passport.biz.service.PassportService;
import com.bilibili.adp.resource.api.account.label.IAccountLabelService;
import com.bilibili.adp.resource.api.app_package.dto.AppPackageDto;
import com.bilibili.adp.resource.api.slot_group.*;
import com.bilibili.adp.resource.api.target_lau.dto.ResTargetItemDto;
import com.bilibili.adp.resource.api.target_package.upgrade.dto.TargetPackageExtraTargetDto;
import com.bilibili.adp.resource.api.targetmeta.TargetType;
import com.bilibili.adp.resource.biz.pojo.ResAppPackagePo;
import com.bilibili.adp.resource.biz.pojo.ResAppPackagePoExample;
import com.bilibili.adp.resource.biz.res_dao.ResAppPackageDao;
import com.bilibili.adp.v6.enums.SupportAuto;
import com.bilibili.bjcom.querydsl.BaseQueryFactory;
import com.bilibili.bjcom.querydsl.clause.BaseQuery;
import com.bilibili.bjcom.querydsl.paging.Pager;
import com.bilibili.bjcom.util.common.Arguments;
import com.bilibili.brand.api.soa.service.ISoaStockService;
import com.bilibili.bsi.api.dto.SendMessageDto;
import com.bilibili.bsi.api.soa.IBsiSoaMessageService;
import com.bilibili.bsi.common.enums.BsiMessageType;
import com.bilibili.bvid.BVIDUtils;
import com.bilibili.crm.platform.common.IsInnerEnum;
import com.bilibili.crm.platform.common.IsValid;
import com.bilibili.dmp.service.ISoaTargetTagsService;
import com.bilibili.dmp.soa.entity.TargetTagsAppBuildExpression;
import com.bilibili.location.api.service.query.IQueryTemplateService;
import com.bilibili.location.api.template.dto.TemplateDto;
import com.bilibili.mas.common.utils.Values;
import com.bilibili.report.platform.api.dto.StatUnitDto;
import com.bilibili.report.platform.api.soa.ISoaAdStatAccountService;
import com.bilibili.report.platform.api.soa.ISoaStatCampaignService;
import com.bilibili.report.platform.api.soa.ISoaStatUnitService;
import com.bilibili.sycpb.acc.api.dict.common.YesNoEnum;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Event;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Splitter;
import com.google.common.base.Strings;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import com.querydsl.core.QueryFlag;
import io.github.resilience4j.timelimiter.TimeLimiterRegistry;
import lombok.Data;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import net.logstash.logback.encoder.org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.unidal.tuple.Triple;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.bilibili.adp.common.Constants.UNIT_ID;
import static com.bilibili.adp.common.Constants.UNIT_NAME;
import static com.bilibili.adp.common.enums.SalesType.PLATFORM_SALES_TYPES;
import static com.bilibili.adp.common.util.Utils.HUNDRED;
import static com.bilibili.adp.cpc.dao.ad_core.querydsl.QLauCampaign.lauCampaign;
import static com.bilibili.adp.cpc.dao.ad_core.querydsl.QLauCreativeTemplate.lauCreativeTemplate;
import static com.bilibili.adp.cpc.dao.ad_core.querydsl.QLauUnit.lauUnit;
import static com.bilibili.adp.cpc.dao.ad_core.querydsl.QLauUnitExtra.lauUnitExtra;
import static com.bilibili.adp.cpc.dao.ad_core.querydsl.QLauUnitGame.lauUnitGame;
import static com.bilibili.adp.cpc.dao.ad_core.querydsl.QLauUnitTargetTag.lauUnitTargetTag;
import static com.bilibili.adp.cpc.dao.config.PlatformMySqlConfig.*;
import static com.bilibili.adp.launch.api.common.UnitUpdateBidPriceTypeEnum.OCPX_OPTIMIZE;
import static com.bilibili.adp.launch.biz.ad_core.querydsl.QLauUnitTargetArchive.lauUnitTargetArchive;
import static com.bilibili.adp.launch.biz.ad_core.querydsl.QLauUnitTargetExtraCrowdPack.lauUnitTargetExtraCrowdPack;
import static com.bilibili.adp.launch.biz.ad_core.querydsl.QLauUnitTargetInstalledUserFilter.lauUnitTargetInstalledUserFilter;

/**
 * copy class
 *
 * @see com.bilibili.adp.launch.biz.service.CpcUnitServiceDelegate
 */
@Slf4j
@Primary
@Service(value = "LaunchCpcUnitServiceDelegate")
@RequiredArgsConstructor
@Data
public class CpcUnitServiceDelegate {
    private static final Logger LOGGER = LoggerFactory.getLogger(CpcUnitServiceDelegate.class);

    public static final String ANDROID_REFRESH_ID_CURSOR = "android_refresh_id_cursor";
    
    @Value("${platform.account.label.general.professionInterest:801}")
    private Integer supportProfessionInterest;

    @Value("${platform.account.label.general.professionInterestAuto:800}")
    private Integer supportProfessionInterestAuto;
    @Autowired
    private ILauUnitTargetProfessionInterestService professionInterestService;
    @Autowired
    private ILauUnitTargetProfessionInterestAutoService professionInterestAutoService;

    @Autowired
    private ResTargetMinigameMapDao miniGameInfoDao;
    @Autowired
    private UnitMiniGameTargetDao unitMiniGameTargetDao;
    @Autowired
    private LauUnitDao lauUnitDao;
    @Autowired
    private LauUnitRepo lauUnitRepo;
    @Autowired
    private ExtLauUnitDao extLauUnitDao;
    @Autowired
    private ExtLauUnitTargetCrowdPackDao extLauUnitTargetCrowdPackDao;
    @Autowired
    private LauUnitTargetCrowdPackDao lauUnitTargetCrowdPackDao;
    @Autowired
    private LauCampaignDao lauCampaignDao;
    @Autowired
    private LauUnitArchiveVideoDao unitArchiveVideoDao;
    @Autowired
    private LauUnitTargetExtraDao unitTargetExtraDao;
    @Autowired
    private AdpLauUnitNextdayBudgetDao lauUnitNextdayBudgetDao;

    @Autowired
    private CpcUnitValidator unitValidator;
    @Autowired
    private NoBidValidator nobidValidator;
    @Autowired
    private CpcCreativeServiceDelegate creativeDelegate;
    @Autowired
    private IResTargetItemService resTargetItemService;
    @Autowired
    private IResTargetPackageUpgradeService resTargetPackageUpgradeService;
    @Autowired
    private IResTargetPackageRuleUpgradeService resTargetPackageRuleUpgradeService;
    @Autowired
    private IResTargetPackageCrowdUpgradeService resTargetPackageCrowdUpgradeService;
    @Autowired
    private IResTargetPackageExtraTargetService resTargetPackageExtraTargetService;

    @Autowired
    private AdpLauUnitService lauUnitService;
    @Autowired
    private ILauCampaignService lauCampaignService;
    @Autowired
    private LauBudgetService lauBudgetService;
    @Autowired
    private ILauCreativeService lauCreativeService;
    @Autowired
    private ILauTagIdsService lauTagIdsService;
    @Autowired
    private ILogService logService;
    @Autowired
    private ILogOperateNewService logOperateService;

    @Autowired
    private IUnitTargetRuleService unitTargetRuleService;
    @Autowired
    private ISoaStatUnitService statUnitService;
    @Autowired
    private ISoaStatCampaignService statCampaignService;
    @Autowired
    private ISoaAdStatAccountService statAccountService;
    @Autowired
    private ICpcCampaignService cpcCampaignService;
    @Autowired
    private ICrowdPackService crowdPackService;
    @Resource(name = "cpcAppPackageService")
    private IAppPackageService appPackageService;
    @Autowired
    private ExtUnitTargetVideoDao extUnitTargetVideoDao;
    @Autowired
    private LauUnitTargetVideoDao lauUnitTargetVideoDao;
    @Autowired
    private ExtUnitTargetContentLabelDao extUnitTargetContentLabelDao;
    @Autowired
    private LauUnitTargetContentLabelDao lauUnitTargetContentLabelDao;
    @Autowired
    private LauUnitShopGoodsDao lauUnitShopGoodsDao;
    @Autowired
    private LauUnitGameDao lauUnitGameDao;
    @Autowired
    private IQueryShopGoodsService queryShopGoodsService;
    @Autowired
    private IGameCenterService gameCenterService;
    @Autowired
    private IQueryAccountService queryAccountService;
    @Autowired
    private LauUnitTargetBusinessInterestDao lauUnitTargetBusinessInterestDao;
    @Autowired
    private IResSlotGroupService resSlotGroupService;
    @Autowired
    private ILauBidConfigService lauBidConfigService;
    @Autowired
    private IBsiSoaMessageService bsiSoaMessageService;
    @Autowired
    private LauUnitTargetExtraDao lauUnitTargetExtraDao;
    @Autowired
    private IHystrixDmpService hystrixDmpService;
    @Autowired
    private LiveBroadcastHttpService liveBroadcastHttpService;
    @Autowired
    private LauSubjectService lauSubjectService;
    @Autowired
    private ArchiveManager archiveManager;
    @Autowired
    private ILauCreativeTemplateService lauCreativeTemplateService;
    @Autowired
    private LauUnitArchiveVideoDao lauUnitArchiveVideoDao;
    @Autowired
    private FlyCampaignService flyCampaignService;
    @Autowired
    private LauBudgetServiceDelegate lauBudgetServiceDelegate;
    @Autowired
    private IQueryTemplateService queryTemplateService;
    @Autowired
    private IAccountLabelService accountLabelService;
    @Resource
    private ResAppPackageDao resAppPackageDao;
    @Autowired
    private CreativePositionConfig creativePositionConfig;

    @Autowired
    private UnitCostService unitCostService;
    @Resource(name = AD_CORE_BQF)
    private BaseQueryFactory adCoreBqf;
    @Resource(name = AD_BQF)
    private BaseQueryFactory adBqf;
    @Autowired
    private ILauBidTwoStageConfigService lauBidTwoStageConfigService;
    @Autowired
    private UnitNameUpdator unitNameUpdator;
    @Autowired
    private UnitBudgetCountComponent unitBudgetCountComponent;
    @Autowired
    private ISoaStatUnitService soaStatUnitService;
    @Autowired
    private TimeLimiterRegistry timeLimiterRegistry;
    @Autowired
    private AccountConfig accountConfig;
    @Autowired
    private LauUnitTargetOsVersionUpgradeProc lauUnitTargetOsVersionUpgradeProc;
    @Autowired
    private LauUnitTargetBiliClientUpgradeProc lauUnitTargetBiliClientUpgradeProc;
    @Autowired
    private AdProductRepo adProductRepo;
    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private AdpCpcGameService adpCpcGameService;

    @Autowired
    private AdpCpcLauUnitGameService adpCpcLauUnitGameService;

    @Autowired
    private CpcUnitService cpcUnitService;

    @Autowired
    private MiddleFlyUnitService middleFlyUnitService;

    @Autowired
    private FlyDynamicService dynamicService;

    @Autowired
    private LauUnitExtraService unitExtraService;

    @Autowired
    private ILogCpcOperationService logCpcOperationService;

    @Autowired
    @Qualifier("stringValueRedisTemplate")
    private RedisTemplate<String, String> redisTemplate;

    private final QLauUnitBudget qLauUnitBudget = QLauUnitBudget.lauUnitBudget;

    private final LaunchOfflineService launchOfflineService;

    @Value("${platform.cpc.unit.crowd.forecast.default.ctr:0.0056}")
    private BigDecimal defaultCTR;
    @Value("${platform.cpc.unit.crowd.forecast.myGeoShowCount:10000}")
    private Integer myGeoShowCount;
    @Value("${platform.cpc.unit.crowd.forecast.statGeoShowCount:5000}")
    private Integer statGeoShowCount;
    @Value("${platform.unit.target.package.upgrade.total.limit:1000}")
    private Integer targetPackageUpgradeBindTotalLimit;
    @Value("${platform.unit.target.package.upgrade.single.limit:100}")
    private Integer targetPackageUpgradeBindSingleLimit;

    @Value("${fly.brand.spread.account.ocpm.label.id:804}")
    private Integer flyBrandSpreadAccountOcpmLabel;

    /**
     * playPagePageIds
     * 动态广告, 单元频控
     **/
    @Value("#{PropertySplitter.getInt2IntMap('${lau.dynamic.unit.frequency.max:{1:2;2:14}}')}")
    private Map<Integer, Integer> dynamicUnitFrequencyLimit;
    @Value("#{PropertySplitter.listInt('${platform.unit.play.page.page_ids:151,152,153}')}")
    private List<Integer> playPagePageIds;

    /**
     * 静态inline广告位组
     */
    @Value("#{'${static.inline.slot.group.ids:366}'.split(',')}")
    private List<Integer> staticInlineSlotGroupIds = Collections.emptyList();

    /**
     * 静态动态流广告位组
     */
    @Value("#{'${static.dynamic.flow.slot.group.ids:388}'.split(',')}")
    private List<Integer> staticDynamicFlowSlotGroupIds = Collections.emptyList();


    @Value("${platform.unit.launchTimeAheadMinutes:10}")
    private Integer launchTimeAheadMinutes;

    @Value("${platform.unit.batch.update.bid.max.size:40}")
    private Integer batchUpdateUnitBidMaxSize;

    private final Integer dynamicMinTwoStageBid = 10;

    @Value("${unit.fly.video.play.lowest.bid:5}")
    private Integer videoPlayTwoStageBid;

    //起飞cpc信息流底价
    private Integer flyCpcInfoMinPrice = 50;

    //起飞cpc播放页底价
    private Integer flyCpcPlayMinPrice = 20;

    //专业起飞直播间底价
    private Integer flyProLiveRoomMinPrice = 500;

    //起飞动态CPM底价（仅信息流）
    private Integer flyDynamicCpmMinPrice = 500;

    //起飞动态CPC底价（仅信息流）
    private Integer flyDynamicCpcMinPrice = 50;

    //静态inline广告位组底价
    private Integer staticInlineMinPrice = 3000;

    //静态动态流广告位组底价
    private Integer staticDynamicFlowMinPrice = 1000;

    //起飞动态CPM底价
    private Integer flyActivityCpmMinPrice = 500;

    //起飞活动CPC底价
    private Integer flyActivityCpcMinPrice = 50;

    /**
     * 竖屏视频流底价
     */
    private final int storyMinPrice = 1000;

    /**
     * iPad相关推荐底价
     */
    private final int iPadRecommendMinPrice = 400;

    /**
     * PC首页推广栏底价
     */
    private final int pcIndexMinPrice = 100;

    /**
     * PC相关推荐底价
     */
    private final int pcPlayMinPrice = 100;

    /**
     * 稿件投框下底价
     */
    private final int flyArchiveUnderBoxMinPrice = 400;

    /**
     * 稿件投Tv inline底价
     */
    private final int flyArchiveTvInlineMinPrice = 3000;
    /**
     * 稿件投搜索底价
     */
    private final int flyArchiveSearchMinPrice = 400;
    /**
     * 稿件PC投搜索底价
     */
    private final int flyArchivePcSearchMinPrice = 0;
    /**
     * 明投搜索底价
     */
    private final int flyChooseSearchMinPrice = 0;
    /**
     * iPAD搜索底价
     */
    private final int flyIPadSearchMinPrice = 0;
    /**
     * ogv cpm 大卡底价
     */
    private final int ogvMsgFlowBigMinPrice = 3000;
    /**
     * ogv cpm 小卡底价
     */
    private final int ogvMsgFlowMinPrice = 500;
    /**
     * ogv cpc底价
     */
    private final int ogvCpcMinPrice = 20;

    /**
     * 商品 低价 0.1元
     */
    private final int GOODS_MIN_PRICE = 10;

    private List<Integer> recommendTypeList = Arrays.asList(RecommendTypeEnum.CORE_UPS.getCode()
            , RecommendTypeEnum.QUALITY_UPS.getCode(), RecommendTypeEnum.POTENTIAL_UPS.getCode());

    private List<Integer> PLATFORM_IOS_LIST = Arrays.asList(AppPlatformType.IOS.getCode(), AppPlatformType.IPAD.getCode(), AppPlatformType.IPHONE.getCode());

    private static final Integer REFRESH_ACCOUNT_NEXTDAY_BUDGET_SIZE = 200;

    private static final String OPEN_UNIT_ACCOUNT_REDIS_KEY_PREFIX = "open_api_unit_account_count:";
    private static final String OPEN_UNIT_ACCOUNT_TODAY_REDIS_KEY_PREFIX = "open_api_today_unit_account_count:";
    private static final String CIPHERTEXT_SEPERATOR = ",";

    @Autowired
    private LaunchUnitGameV2Service launchUnitGameService;
    @Autowired
    private DoFlyCreativePositionService doFlyCreativePositionService;
    @Autowired
    private LaunchUnitLowestBidService launchUnitLowestBidService;
    @Autowired
    private PassportService passportService;
    @Autowired
    private FlyGdPlusDelegate flyGdPlusDelegate;
    @Autowired
    private FlyGdUnitValidator flyGdUnitValidator;
    @Autowired
    private ISoaStockService stockService;
    @Autowired
    private LaunchAccountV1Service launchAccountService;
    @Autowired
    private MainCommunityTagProxy mainCommunityTagProxy;
    @Autowired
    private LaunchUnitService launchUnitService;
    @Autowired
    private LauUnitFlyMiddleInfoRepo lauUnitFlyMiddleInfoRepo;
    @Autowired
    private DynamicQuerierProc dynamicQuerierProc;
    @Autowired
    private AdpCpcLiveReserveService adpCpcLiveReserveService;
    @Autowired
    private LaunchUnitGoodsService launchUnitGoodsService;
    @Autowired
    private MainArcAtmosphereService mainArcAtmosphereService;
    @Autowired
    private AccLabelConfig accLabelConfig;
    @Autowired
    private LaunchJumpUrlService launchJumpUrlService;
    @Autowired
    private DynamicLinkProc dynamicLinkProc;
    @Autowired
    private MiddleFlyCreativeService middleFlyCreativeService;
    @Autowired
    private UnitGoodsRepo unitGoodsRepo;
    @Autowired
    private LauUnitPgcArchiveRepo lauUnitPgcArchiveRepo;
    @Autowired
    private ArchiveService archiveService;
    @Autowired
    private UnitAccelerateService unitAccelerateService;
    @Resource
    private AdpCpcCreativePubInfoService cpcCreativePubInfoService;

    @Autowired
    private ISoaTargetTagsService soaTargetTagsService;

    @Value("${account.labels.exclude:419}")
    private Set<Integer> accountLabelsExclude;

    private static final String MATERIAL_VIDEO_FROM = "vupload";

    private static final Long MATERIAL_VIDEO_PAGE = 0l;

    public final List<Integer> flyOcpmList = Arrays.asList(
            OcpcTargetEnum.USER_FOLLOW.getCode(),
            OcpcTargetEnum.DYNAMIC_DETAIL_BROWSE.getCode(),
            OcpcTargetEnum.VIDEO_PLAY.getCode(),
            OcpcTargetEnum.FIRST_COMMENT_COPY.getCode(),
            OcpcTargetEnum.UNDER_BOX_LINK_CLICK.getCode(),
            OcpcTargetEnum.ACTIVITY_PAGE_PULL_UP.getCode(),
            OcpcTargetEnum.COMMENT_CLICK.getCode(),
            OcpcTargetEnum.SHOPPING_CART.getCode(),
            OcpcTargetEnum.LP_CALL_UP_SUCCESS.getCode(),
            OcpcTargetEnum.GOODS_TRANSACTION.getCode(),
            OcpcTargetEnum.LIVE_CALLUP.getCode(),
            OcpcTargetEnum.PAID_IN_24H_ROI.getCode(),
            OcpcTargetEnum.PAID_IN_7D_COST.getCode(),
            OcpcTargetEnum.FORM_SUBMIT.getCode()
    );

    public final List<Integer> ocpcList = Arrays.asList(
            OcpcTargetEnum.PAID_IN_24H_ROI.getCode(),
            OcpcTargetEnum.PAID_IN_7D_COST.getCode()
    );


    private Integer userFollowTwoStageBidBusiness = 1000;

    private Integer underBoxLinkClickTwoStageBid = 500;

    private Integer firstCommentCopyTwoStageBid = 500;

    private Integer commentLickTwoStageBid = 500;

    private Integer shoppingCartTwoStageBid = 500;

    private Integer storyYellowCallupTwoStageBid = 1000;

    private Integer orderPostTwoStageBid = 2000;

    private Integer orderPostTwoStageBid4Daihuo = 500;

    private Integer liveReserveTwoStageBid = 100;
    @Value("${twoStageBid.liveCallUp.general:1200}")
    private Integer generalLiveCallUpTwoStageBid;

    @Value("${twoStageBid.form.submit:100}")
    private Integer formSubmitTwoStageBid;

    @Value("${twoStageBid.liveCallUp.special:[{\"accountId\":979520,\"bidPrice\": 2000}]}")
    private String specialLiveCallUpTwoStageBid;

    @Value("${brand.spread.paid.in.24h.roi:1}")
    // 表示 1%
    private Integer brandSpreadPaidIn24HROILostBid;

    @Value("${brand.spread.paid.in.7d.cost:1000}")
    private Integer brandSpreadPaidIn7DCostLostBid;
    private List<AccountCostPriceBean> specialLiveCallUpTwoStageBidList;
    private Map<Integer, Integer> specialLiveCallUpTwoStageBidMap;
    @Autowired
    private LaunchUnitV1Service launchUnitV1Service;

    @PostConstruct
    public void init() {
        specialLiveCallUpTwoStageBidList = new GsonBuilder().create().fromJson(specialLiveCallUpTwoStageBid,
                new TypeToken<List<AccountCostPriceBean>>() {
                }.getType());

        specialLiveCallUpTwoStageBidMap = specialLiveCallUpTwoStageBidList.stream().collect(Collectors.toMap(t -> t.getAccountId(),
                t -> t.getBidPrice(), (t1, t2) -> t2));
    }

    public void simpleUpdateCpcUnit(UnitSimpleUpdateBo updateBo) {
        Assert.notNull(updateBo.getUnitId(), "unit id can not be null");
        LauUnitPo po = LauUnitPo.builder()
                .unitId(updateBo.getUnitId())
                .launchBeginDate(updateBo.getLaunchBeginDate())
                .launchEndDate(updateBo.getLaunchEndDate())
                .launchTime(updateBo.getLaunchTime())
                .build();
        lauUnitDao.updateByPrimaryKeySelective(po);
    }


    public Integer updateDirectLaunch(Integer unitId) {
        LauUnitPo po = LauUnitPo.builder()
                .unitId(unitId)
                .isStoreDirectLaunch(1)
                .build();
        int result = lauUnitDao.updateByPrimaryKeySelective(po);
        LauUnitExtraPo unitExtraPo = unitExtraService.getByUnitId(unitId);
        if (!Objects.isNull(unitExtraPo)) {
            UpdateCpcUnitDto unit = UpdateCpcUnitDto.builder()
                    .unitId(unitId)
                    .deviceAppStore("1,2,4,5")
                    .appPackageId(unitExtraPo.getAndroidAppPackageId())
                    .accountId(unitExtraPo.getAccountId())
                    .isStoreDirectLaunch(1).build();

            unitExtraService.saveUnitExtraPo(unit, unit.getUnitId());
        }
        return result;
    }

    @Transactional(value = AD_CORE_TM, rollbackFor = Exception.class)
    public int createCpcUnit(NewCpcUnitDto unit, Operator operator) throws ServiceException {
        LOGGER.info("createCpcUnit unit:{}, operator:{}", unit, operator);

        Assert.notNull(unit, "单元信息不可为空");
        Assert.isTrue(!Operator.validateParamIsNull(operator), "操作人不可为空");

        // 检查小游戏定向配置是否错误
        validMiniGameConfig(unit.getIncludeMiniGameIds(), unit.getExcludeMiniGameIds());

        // 没有传adpVersion的情况默认为旧版
        if (unit.getAdpVersion() == null) unit.setAdpVersion(AdpVersion.LEGACY.getKey());

        CpcCampaignDto campaign = lauCampaignService.loadCpcCampaignDto(unit.getCampaignId());
        PromotionPurposeType cppt = PromotionPurposeType.getByCode(campaign.getPromotionPurposeType());

        //只有中台会传这个字段
        PromotionPurposeType uppt = unit.getUnitPromotionPurposeType() == null ?
                cppt : PromotionPurposeType.getByCode(unit.getUnitPromotionPurposeType());
        LOGGER.info("createCpcUnit campaign:{}", campaign);

        Integer subjectId = null;
        LauSubjectDto dto = null;
        boolean isReserveLiveRoom = false; // 预约直播间

        // 获取账号标签
        List<Integer> accountLabelIdList = accountLabelService.getLabelIdsByAccountId(operator.getOperatorId());
        // 非三连校验二阶段出价
        checkFlyCreateUnitBidTwoStageMinPrice(unit, campaign, operator);
        // 三连校验二阶段出价
        checkFlyMiddleCreateUnitBidTwoStageMinPrice(unit, campaign, accountLabelIdList, operator);

        //品牌传播
        if (PromotionPurposeType.BRAND_SPREAD.equals(cppt)) {
            Assert.isTrue(PromotionPurposeType.BRAND_SPREAD_SUPPORT_UNIT_PPT_SET.contains(uppt), "单元推广目的传参错误");
        }
        //直播间
        if (PromotionPurposeType.LIVE_ROOM.equals(cppt)) {
            //起飞直播预约，是推稿件和物料素材无关
            if (Integer.valueOf(1).equals(unit.getIsNewFly())) {
                FlyCampaignDetailDto flyCampaignDetailDto = flyCampaignService.getFlyCampaignById(unit.getCampaignId());
                // 直播预约
                if (RoomPromoteScenesEnum.RESERVE.getCode().equals(flyCampaignDetailDto.getRoomPromoteScenes())) {
                    isReserveLiveRoom = true;
                    // 有优化目标，设置默认的出价
                    if (Utils.isPositive(unit.getOcpcTarget())) {
                        unit.setCostPrice(FlyProLaunchService.getFlyUnitDefaultCostPrice(unit.getIsNoBid()));
                    }
                }
            }
            if (Objects.equals(uppt, PromotionPurposeType.LIVE_RESERVE)) {
                isReserveLiveRoom = true;
            }

            if (!isReserveLiveRoom) {
                LiveBroadcastRoomInfo roomInfo = null;
                Integer materialId = Integer.valueOf(unit.getMaterialId());

                Map<Integer, LiveBroadcastRoomInfo> liveIdMapRoom = liveBroadcastHttpService.queryLiveBroadcastRoomInfoByIds(Arrays.asList(materialId));
                if (!CollectionUtils.isEmpty(liveIdMapRoom)) {
                    roomInfo = liveIdMapRoom.values().stream().findFirst().orElse(null);
                }
                Assert.notNull(roomInfo, "指定的直播间不存在");
                Assert.notNull(roomInfo.getRoomId(), "直播间id为空");
                Assert.isTrue(roomInfo.getRoomId().equals(materialId), "请使用直播间实际的房间号");

                // roomId 作为素材
                dto = lauSubjectService.getOrAddSubjectWhenNotExist(roomInfo.getRoomId().toString(), LauSubjectType.LIVE_ROOM, roomInfo.getAreaV2Id());

                Assert.isTrue(dto != null && dto.getId() != null, "推广类型为直播间的计划下新建单元时标的物创建失败");
                subjectId = dto.getId();
            }
        }
        // 账号推广
        if (PromotionPurposeType.ENTERPRISE_PROMOTION.equals(cppt)) {
            if (AdpVersion.isMiddle(unit.getAdpVersion())) {
                Assert.isTrue(PromotionPurposeType.ENTERPRISE_PROMOTION_SUPPORT_UNIT_PPT_SET.contains(uppt), "单元推广目的传参错误");
                if (PromotionPurposeType.ENTERPRISE_PROMOTION.equals(uppt)) {
                    // 账号推广，企业号校验
                    unitCostService.enterpriseUnitCostCheck(unit, operator);
                }
            } else {
                // 账号推广，企业号校验
                unitCostService.enterpriseUnitCostCheck(unit, operator);
            }
        }

        ResSlotGroupBaseDto slotGroup = null;

        if (unit.getFrequencyLimit() == null || unit.getFrequencyLimit() == 0) {
            unit.setFrequencyLimit(Integer.MAX_VALUE);
        }

        TemplateDto templateDto = null;

        // legacy版本，根据单元 slotGroup 获取模板
        if (AdpVersion.isLegacy(unit.getAdpVersion())) {
            // 获取单元上的广告位组
            slotGroup = resSlotGroupService.getGroupById(unit.getSlotGroup());

            // 根据广告位组推广目的获取模板
            templateDto = getTemplateDto(unit.getSlotGroup(), campaign.getPromotionPurposeType());
            LOGGER.info("createCpcUnit cardType:{}", templateDto.getCardType());

            if (unit.getFrequencyLimit() == null || unit.getFrequencyLimit() == 0) {
                if (templateDto.getIsDynamicArea()) {
                    unit.setFrequencyLimit(dynamicUnitFrequencyLimit.get(unit.getFrequencyUnit()));
                }
            }
        }
        // 校验 nobid: 推广目的，账号标签
        NobidValidateParam param = NobidValidateParam.builder()
                .nobidMax(unit.getNoBidMax()).adpVersion(unit.getAdpVersion()).isNoBid(unit.getIsNoBid())
                .accountLabelIdList(accountLabelIdList).ocpcTarget(unit.getOcpcTarget()).campaign(campaign)
                .unitPromotionPurposeType(uppt.getCode())
                .build();
        //【个人起飞】切商业架构需求 https://www.tapd.bilibili.co/********/prong/stories/view/11********002969535
        final boolean isPersonalFly = launchAccountService.isPersonalFly(operator.getOperatorId());
        nobidValidator.validateNoBid(param, isPersonalFly);
        // 获取 businessDomain
        final int businessDomain = launchAccountService.getBusinessDomain(unit.getAccountId(), unit.getIsMiddleAd(), unit, cppt);
        // 单元校验
        unitValidator.createCpc(slotGroup, templateDto, unit, campaign, businessDomain, operator);

        unit.setStatus(SwitchStatus.STARTED.getCode());
        unit.setUnitStatus(UnitStatus.NOT_START.getCode());
        // 安卓游戏 && cps包，进行空包判断
        if ((PromotionPurposeType.ON_SHELF_GAME.equals(cppt) || PromotionPurposeType.BRAND_SPREAD.equals(cppt))
                && !Objects.isNull(unit.getGameBaseId()) && unit.getGameBaseId() != 0 && AdpCpcGameService.CPS_PKG.equals(unit.getSubPkg())) {
            List<LauGameInfoPo> lauGameInfoPos = adpCpcGameService.fetchGames(Collections.singletonList(unit.getGameBaseId()));
            LauGameInfoPo lauGameInfoPo = lauGameInfoPos.stream().filter(x -> x.getIsDeleted().equals(IsDeleted.VALID.getCode())).collect(Collectors.toList()).get(0);
            if (lauGameInfoPo.getSubPkgStatus() == AdpCpcGameService.CPS_PKG_EFFECTIVE_INVALID) {
                // 空包时
                unit.setStatus(SwitchStatus.STOPED.getCode());
                unit.setUnitStatus(UnitStatus.PAUSED.getCode());
            }
        }
        unit.setAccountId(campaign.getAccountId());
        unit.setLaunchTime(Strings.isNullOrEmpty(unit.getLaunchTime()) ? Constants.LAUNCH_TIME : unit.getLaunchTime());

        // 日预算自动，设置为计划的预算
        if (unit.getDailyBudgetType() == DailyBudgetType.AUTO.getCode()) {
            unit.setBudget(campaign.getBudget());
        }

        if (Strings.isNullOrEmpty(unit.getLaunchEndDate())) {
            unit.setLaunchEndDate(LaunchUtil.getStringDate(Calendar.YEAR, 100));
        }

        // 单元 dto -> po(里面有ocpx两阶段竞价逻辑)
        //LauUnitPo po = cpcUnitDtoToPo(unit);
        com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauUnitPo po = cpcUnitDtoToPo(unit);

        // 不传默认日预算
        if (unit.getBudgetType() == null) {
            po.setBudgetType(BudgetType.DAILY.getCode());
            // 个人起飞为总预算
            if (OperatorType.PERSON_FLY.equals(operator.getOperatorType())) {
                po.setBudgetType(BudgetType.TOTAL.getCode());
            }
        } else {
            po.setBudgetType(unit.getBudgetType());
        }

        // 非 middle 单元推广目的为计划的; middle 单元推广目的为页面选择的
        po.setPromotionPurposeType(campaign.getPromotionPurposeType());
        if (AdpVersion.isMiddle(unit.getAdpVersion())) {
            po.setPromotionPurposeType(unit.getUnitPromotionPurposeType());
        }
        po.setChannelId(slotGroup == null ? null : slotGroup.getChannelId());
        po.setIsHistory(0);
        po.setSubjectId(subjectId); // 目前只有直播才有

        po.setIsGdPlus(unit.getIsGdPlus());
        po.setGdPlusOptimizeModel(unit.getOptimizeModel());
        po.setGdPlusOptimizeTarget(unit.getOptimizeTarget());

        po.setBusinessDomain(businessDomain);

        // 是否高优单元，目前只有 游戏卡 / 游戏活动可以可以设置这个
        // 其他单元默认 = 0，非高优单元
        po.setIsHighPriority(unit.getIsHighPriority());

        // 创建单元
        //lauUnitDao.insertSelective(po);
        //Integer unitId = po.getUnitId();
        Integer unitId = adCoreBqf.insert(lauUnit).insertGetKey(po);
        unitExtraService.saveUnitExtraPo(unit, unitId);

        // 保存单元预算
        lauBudgetService.insertUnitBudget(operator, unitId, unit.getBudget());

        unitValidator.validateNobidUnitBudgetAndCount(unitId, unit.getDailyBudgetType(), unit.getIsNoBid(), campaign);

        // 保存单元定向信息
        addCpcUnitTarget(operator, unit, unitId);

        // 需要保存会员购商品信息
        insertUnitShopGoods(campaign.getAccountId(), unitId, unit.getShopGoodsId(), uppt);

        // ios 包的情况才需要
        SearchingGameResult searchingGameResult = trySearchingGameBaseId(campaign.getPromotionPurposeType(), unit.getAppPackageId());
        if (searchingGameResult != null) {
            unit.setGamePlatformType(searchingGameResult.getPlatformType());

            // android 包
            if (Objects.equals(searchingGameResult.getPlatformType(), GamePlatformTypeEnum.IOS.getCode())) {
                if (Utils.isPositive(searchingGameResult.getGameId())) {
                    unit.setGameBaseId(searchingGameResult.getGameId());
                    // 设置为 app package 实际的 platform
                    unit.setGamePlatformType(searchingGameResult.getPlatformType());
                }
            }
        }
        // 需要保存游戏的(android游戏 和 应用推广的ios)
        insertUnitGame(campaign.getAccountId(), unitId, unit.getGameBaseId(), unit.getGamePlatformType(), unit.getSubPkg(), uppt);

        Long mid = Objects.nonNull(unit.getLongMid()) ? unit.getLongMid() : unit.getMid().longValue();
        if (Utils.isPositive(unit.getVideoId())) {
            // 需要保存投稿信息的
            saveArchiveContent(unitId, mid, unit.getVideoId(), uppt, unit.getLaunchVideoType(), isReserveLiveRoom);
        }

        insertUnitCreateLog(operator, unit, unitId, dto, businessDomain);

        // 绑定新版定向包的单元 原来的单元映射保存为无定向条件
        boolean isBindUpgradeTargetPackage = Utils.isPositive(unit.getTargetPackageId());

        CpcUnitExtraTargetDto extraTarget = isBindUpgradeTargetPackage ? null : unit.getExtraTarget();
        saveExtraTarget(unitId, extraTarget, uppt, operator);

        // 记录单元游戏定向配置
        List<String> includeMiniGameIds = isBindUpgradeTargetPackage ? null : unit.getIncludeMiniGameIds();
        UnitMiniGameTargetPo miniGameTargetPo = new UnitMiniGameTargetPo();
        miniGameTargetPo.setUnitId(Long.valueOf(unitId));
        if (!CollectionUtils.isEmpty(includeMiniGameIds)) {
            miniGameTargetPo.setIncludeGameIds(String.join(",", includeMiniGameIds));
        } else {
            miniGameTargetPo.setIncludeGameIds("");
        }

        List<String> excludeMiniGameIds = isBindUpgradeTargetPackage ? null : unit.getExcludeMiniGameIds();
        if (!CollectionUtils.isEmpty(excludeMiniGameIds)) {
            miniGameTargetPo.setExcludeGameIds(String.join(",", excludeMiniGameIds));
        } else {
            miniGameTargetPo.setExcludeGameIds("");
        }

        LOGGER.info("新建单元小游戏定向配置: miniGameTargetPo: {}", JSON.toJSONString(miniGameTargetPo));

        //todo 下线小游戏定向
        //unitMiniGameTargetDao.insert(miniGameTargetPo);

        if (Utils.isPositive(unit.getVideoId())) {
            mainArcAtmosphereService.handleMainArcAtmosphere(Lists.newArrayList(unit.getVideoId()), operator.getOperatorId(), businessDomain);
        }

        Long oldTargetPackageMtime = unit.getTargetPackageMtime();
        // 若绑定新版定向包 乐观锁
        if (Utils.isPositive(oldTargetPackageMtime)) {
            Long currentTargetPackageMtime = resTargetPackageUpgradeService.load(unit.getTargetPackageId(), true).getMtime().getTime();
            Assert.isTrue(oldTargetPackageMtime.equals(currentTargetPackageMtime), "当前绑定定向包已被修改，请刷新页面重试");
        }
        adProductRepo.batchUpdateProductMapping(unit.getSdpaProductIds(), unitId,
                AdProductBindTypeEnum.UNIT.getCode(), true);

        //保存单元额外信息
        Integer smartKeyWord = Objects.isNull(unit.getSmartKeyWord()) ? CommonSwitchEnum.ENABLED.getCode() : unit.getSmartKeyWord();
        saveSmartKeySwitch(smartKeyWord, operator, unitId);

        return unitId;
    }

    private boolean checkIsContentAccount(Integer accountId) {
        AccountAllInfoDto accountAllInfoDto = queryAccountService.getAccountAllInfoFromCache(accountId);
        if (accountAllInfoDto != null) {
            AccountDto accountDto = accountAllInfoDto.getAccountDto();
            if (accountDto != null) {
                return (Integer.valueOf(1).equals(accountDto.getIsSupportContent()));
            }
        }
        return false;
    }

    /**
     * 非三连校验二阶段出价
     *
     * @param unit
     * @param campaign
     * @param operator
     * @throws ServiceException
     */
    private void checkFlyCreateUnitBidTwoStageMinPrice(NewCpcUnitDto unit, CpcCampaignDto campaign, Operator operator) throws ServiceException {
        if (AdpVersion.isMiddle(unit.getAdpVersion())) {
            return;
        }
        PromotionPurposeType cppt = PromotionPurposeType.getByCode(campaign.getPromotionPurposeType());
        //只有中台会传这个字段
        PromotionPurposeType uppt = unit.getUnitPromotionPurposeType() == null ?
                cppt : PromotionPurposeType.getByCode(unit.getUnitPromotionPurposeType());
        //品牌传播
        if (PromotionPurposeType.BRAND_SPREAD.equals(cppt)) {
            if (PromotionPurposeType.ARCHIVE_CONTENT.equals(uppt)) {
                unitCostService.archiveUnitCostCheck4Insert(unit, campaign, operator);
            }
            if (PromotionPurposeType.DYNAMIC.equals(uppt)) {
                unitCostService.dynamicUnitCostCheck(unit);
            }
            if (PromotionPurposeType.ACTIVITY.equals(uppt)) {
                unitCostService.activityUnitCostCheck(unit);
            }
        }
        // 投稿内容
        if (PromotionPurposeType.ARCHIVE_CONTENT.equals(cppt) && campaign.getIsManaged() == 0) {
            unitCostService.archiveUnitCostCheck4Insert(unit, campaign, operator);
        }
        // 动态
        if (PromotionPurposeType.DYNAMIC.equals(cppt)) {
            unitCostService.dynamicUnitCostCheck(unit);
        }
        // 活动
        if (PromotionPurposeType.ACTIVITY.equals(cppt)) {
            unitCostService.activityUnitCostCheck(unit);
        }
    }

    /**
     * 三连校验二阶段出价
     *
     * @param unit
     * @param campaign
     * @param accountLabelIdList
     * @param operator
     * @throws ServiceException
     */
    private void checkFlyMiddleCreateUnitBidTwoStageMinPrice(NewCpcUnitDto unit,
                                                             CpcCampaignDto campaign,
                                                             List<Integer> accountLabelIdList,
                                                             Operator operator) throws ServiceException {
        if (!AdpVersion.isMiddle(unit.getAdpVersion()) || !Utils.isPositive(unit.getOcpcTarget())) {
            return;
        }
        PromotionPurposeType cppt = PromotionPurposeType.getByCode(campaign.getPromotionPurposeType());
        //只有中台会传这个字段
        PromotionPurposeType uppt = unit.getUnitPromotionPurposeType() == null ?
                cppt : PromotionPurposeType.getByCode(unit.getUnitPromotionPurposeType());
        Integer salesType = unit.getSalesType();
        // 中台商业起飞
        boolean isMiddleFly = checkIsMiddleFly4CreateUnit(cppt, campaign.getIsManaged(), uppt, salesType);
        if (!isMiddleFly) {
            return;
        }

        GetBidCostParam queryFlyBidTwoStageParam = GetBidCostParam.builder()
                .accountId(operator.getOperatorId())
                .salesType(salesType)
                .accountLabelIdList(accountLabelIdList)
                .build();
        Integer flyLowestBidTwoStagePrice;
        final AccountBaseDto accountBaseDto = queryAccountService.getAccountBaseDtoByIdFromCache(operator.getOperatorId());
        if (Utils.isPositive(accountBaseDto.getIsSupportContent())) {
            flyLowestBidTwoStagePrice = cpcUnitService.getContentFlyLowestBid(campaign.getPromotionPurposeType(), unit.getPromotionPurposeType(), unit.getOcpcTarget());
        } else {
            flyLowestBidTwoStagePrice = getFlyBanner4LowestCost4Unit(queryFlyBidTwoStageParam, unit.getOcpcTarget());
        }

        OcpcTargetEnum ocpcTargetEnum = OcpcTargetEnum.getByCode(unit.getOcpcTarget());
        String ocpcTargetDesc = Objects.isNull(ocpcTargetEnum) ? "" : ocpcTargetEnum.getDesc();

        if (unit.getIsNoBid() == null || unit.getIsNoBid().equals(YesNoEnum.NO.getCode())) {
            Assert.isTrue(unit.getTwoStageBid() >= flyLowestBidTwoStagePrice,
                    "起飞单元" + ocpcTargetDesc + "单价成本不能低于" + Utils.fromFenToYuan(flyLowestBidTwoStagePrice) + "元");
        }
        // 补充校验
        boolean isBrandSpreadArchive = PromotionPurposeType.BRAND_SPREAD.equals(cppt)
                && PromotionPurposeType.ARCHIVE_CONTENT.equals(uppt);
        boolean isArchiveContent = PromotionPurposeType.ARCHIVE_CONTENT.equals(cppt) && (campaign.getIsManaged() == 0);
        boolean isEnterpriseArchive = PromotionPurposeType.ENTERPRISE_PROMOTION.equals(cppt)
                && PromotionPurposeType.ARCHIVE_CONTENT.equals(uppt);
        if (isBrandSpreadArchive || isArchiveContent || isEnterpriseArchive) {
            unitCostService.checkCreateUnitArchiveContentCampaign(unit, campaign);
        }
        unit.setCostPrice(FlyProLaunchService.getFlyUnitDefaultCostPrice(unit.getIsNoBid()));
    }

    private boolean checkIsMiddleFly4CreateUnit(PromotionPurposeType campaignPpt,
                                                Integer campaignIsManaged,
                                                PromotionPurposeType unitPpt,
                                                Integer salesType) {
        //品牌传播
        if (PromotionPurposeType.BRAND_SPREAD.equals(campaignPpt)
                || PromotionPurposeType.DYNAMIC.equals(campaignPpt)
                || PromotionPurposeType.ACTIVITY.equals(campaignPpt)) {
            return true;
        }
        // 直播间
        if (PromotionPurposeType.LIVE_ROOM.equals(campaignPpt) &&
                SalesType.CPM.getCode() == salesType) {
            return true;
        }
        //企业号下投稿内容
        if (PromotionPurposeType.ENTERPRISE_PROMOTION.equals(campaignPpt) &&
                PromotionPurposeType.ARCHIVE_CONTENT.equals(unitPpt)) {
            return true;
        }
        // 投稿内容
        if (PromotionPurposeType.ARCHIVE_CONTENT.equals(campaignPpt)
                && Integer.valueOf(0).equals(campaignIsManaged)) {
            return true;
        }
        // OGV
        if (PromotionPurposeType.OGV.equals(unitPpt)) {
            return true;
        }
        return false;
    }

    private boolean checkIsMiddleFly4UpdateUnit(PromotionPurposeType unitPpt) {
        //品牌传播 动态 活动
        if (PromotionPurposeType.ARCHIVE_CONTENT.equals(unitPpt)
                || PromotionPurposeType.DYNAMIC.equals(unitPpt)
                || PromotionPurposeType.ACTIVITY.equals(unitPpt)
                || PromotionPurposeType.OGV.equals(unitPpt)) {
            return true;
        }
        return false;
    }

    private SearchingGameResult trySearchingGameBaseId(Integer promotionPurposeType, Integer appPackageId) {
        SearchingGameResult searchingGameResult = SearchingGameResult.builder().appPackageId(appPackageId).build();
        if (!Objects.equals(promotionPurposeType, PromotionPurposeType.APP_DOWNLOAD.getCode())) return null;

        Assert.isTrue(Utils.isPositive(appPackageId), "未指定appId");
        final Map<Integer, AppPackageDto> appPackageMap = appPackageService.getAllMapInPrimaryKeys(Collections.singletonList(appPackageId));
        Assert.isTrue(!CollectionUtils.isEmpty(appPackageMap), "查询app失败");
        final AppPackageDto appPackage = appPackageMap.get(appPackageId);
        Assert.notNull(appPackage, "查询app失败");

        // 见枚举: com.bilibili.adp.common.enums.AppPlatformType
        if (PLATFORM_IOS_LIST.contains(appPackage.getPlatform())) {
            searchingGameResult.setPlatformType(GamePlatformTypeEnum.IOS.getCode());
        } else if (Objects.equals(appPackage.getPlatform(), AppPlatformType.ANDROID.getCode())) {
            searchingGameResult.setPlatformType(GamePlatformTypeEnum.ANDROID.getCode());
        }

        final Map<String, Integer> name2IdMap = launchUnitGameService.getGameName2GameIdMap();
        Integer gameId = name2IdMap.get(appPackage.getAppName());

        searchingGameResult.setGameId(gameId);
        return searchingGameResult;
    }

    private TemplateDto getTemplateDto(Integer slotGroup, Integer promotionPurposeType) {
        List<ResSlotGroupTemplateMappingDto> slotGroups = resSlotGroupService
                .querySlotGroupTemplateMappingInSlotGroupIds(QueryTemplateLaunchTypeMappingDto
                        .builder()
                        .slotGroupId(slotGroup)
                        .promotionPurposeType(promotionPurposeType)
                        .build());

        Assert.isTrue(!CollectionUtils.isEmpty(slotGroups), LaunchExceptionCode.CREATIVE_UNIT_GROUP_NO_TEMPLATE.getMessage());

        List<TemplateDto> templates = slotGroups.get(0).getTemplates();
        Assert.isTrue(!CollectionUtils.isEmpty(templates), LaunchExceptionCode.CREATIVE_UNIT_GROUP_NO_TEMPLATE.getMessage());

        return templates.stream().findFirst().orElse(null);
    }

    private void insertUnitGame(Integer accountId, Integer unitId, Integer gameBaseId, Integer gamePlatformType, Integer subPkg, PromotionPurposeType promotionPurposeType) {
        // 如果不是安卓游戏/IOS也没有匹配到, 就什么都不做
        if (!Utils.isPositive(gameBaseId)) return;

        Assert.notNull(gamePlatformType, "游戏平台类型不存在");

        LauUnitGamePo record = new LauUnitGamePo();
        record.setAccountId(accountId);
        record.setUnitId(unitId);
        record.setGameBaseId(gameBaseId);
        record.setPlatformType(gamePlatformType);
        record.setSubPkg(subPkg);
        lauUnitGameDao.insertSelective(record);
    }

    private void insertUnitShopGoods(Integer accountId, Integer unitId, Integer shopGoodsId, PromotionPurposeType promotionPurposeType) {
        if (PromotionPurposeType.SHOP_GOODS.equals(promotionPurposeType)) {
            AccountBaseDto accountBaseDto = queryAccountService.getAccountBaseDtoById(accountId);
            Assert.notNull(accountBaseDto, "该账号不存在");

            // 查询会员购商品信息
            DpaShopGoodsDto shopGoodsDto = queryShopGoodsService.getDpaShopGoodsByGoodsId(shopGoodsId);
            Assert.notNull(shopGoodsDto, "该商品已不存在");

            LauUnitShopGoodsPo record = new LauUnitShopGoodsPo();
            record.setAccountId(accountId);
            record.setMid(accountBaseDto.getMid());
            record.setUnitId(unitId);
            record.setGoodsId(shopGoodsId);
            record.setProductId(shopGoodsDto.getProductId());
            record.setProductType(shopGoodsDto.getProductType());
            record.setShopId(shopGoodsDto.getEnteringShopId());
            lauUnitShopGoodsDao.insertSelective(record);
        }
    }

    @Transactional(value = AD_TM, rollbackFor = Exception.class)
    public void updateUnitShopGoods(Integer adpVersion, CpcCampaignDto campaign, Integer unitId, Integer shopGoodsId) {
        if (AdpVersion.isMiddle(adpVersion)) {
            if (PromotionPurposeType.SHOP_GOODS.getCode() == campaign.getPromotionPurposeType()) {
                Integer accountId = campaign.getAccountId();
                AccountBaseDto accountBaseDto = queryAccountService.getAccountBaseDtoById(accountId);
                Assert.notNull(accountBaseDto, "编辑时该账号不存在");

                DpaShopGoodsDto shopGoodsDto = queryShopGoodsService.getDpaShopGoodsByGoodsId(shopGoodsId);
                Assert.notNull(shopGoodsDto, "编辑时该商品已不存在");


                LauUnitShopGoodsPo unitShopGoodsPo = this.getShopGoodsIdByUnitId(unitId);
                Integer id = unitShopGoodsPo.getId();
                Assert.isTrue(Utils.isPositive(id), "编辑时该单元不存在商品投放");

                LauUnitShopGoodsPo record = new LauUnitShopGoodsPo();
                record.setId(id);
                record.setAccountId(accountId);
                record.setMid(accountBaseDto.getMid());
                record.setUnitId(unitId);
                record.setShopId(shopGoodsDto.getEnteringShopId());
                record.setGoodsId(shopGoodsId);
                record.setProductId(shopGoodsDto.getProductId());
                record.setProductType(shopGoodsDto.getProductType());

                lauUnitShopGoodsDao.updateByPrimaryKeySelective(record);
            }
        }

    }

    @Transactional(value = AD_CORE_TM, rollbackFor = Exception.class)
    public void updateCpcUnit(UpdateCpcUnitDto unit, Operator operator) throws ServiceException {
        LOGGER.info("updateCpcUnit unit:{}, operator:{}", unit, operator);

        Assert.isTrue(!Operator.validateParamIsNull(operator), "操作人不可为空");
        Assert.notNull(unit, "单元信息不可为空");

        // 检查小游戏定向配置是否错误
        validMiniGameConfig(unit.getIncludeMiniGameIds(), unit.getExcludeMiniGameIds());

        CpcUnitDto oldUnit = loadCpcUnit(unit.getUnitId());
        CpcCampaignDto campaign = lauCampaignService.loadCpcCampaignDto(oldUnit.getCampaignId());

        if (IsValid.TRUE.getCode().equals(oldUnit.getIsGdPlus())) {
            Assert.isTrue(!GdPlusFinishFlagEnum.CREATING.getCode().equals(oldUnit.getGdPlusFinishFlag()), "保量单元在创建中，不允许修改");
        }

        final int adpVersion = oldUnit.getAdpVersion();

        TemplateDto templateDto = null;

        if (AdpVersion.isLegacy(adpVersion)) {
            ResSlotGroupBaseDto slotGroupBaseDto = resSlotGroupService.getGroupById(unit.getSlotGroup());
            templateDto = getTemplateDto(unit.getSlotGroup(), campaign.getPromotionPurposeType());
            LOGGER.info("updateCpcUnit cardType:{}", templateDto.getCardType());
            if (unit.getFrequencyLimit() == null || unit.getFrequencyLimit() == 0) {
                if (templateDto.getIsDynamicArea()) {
                    unit.setFrequencyLimit(dynamicUnitFrequencyLimit.get(unit.getFrequencyUnit()));
                }
            }

            // 如果指定了 nobid 需要广告位组支持
            if (unit.getIsNoBid() != null && Utils.isPositive(unit.getIsNoBid())) {
                if (slotGroupBaseDto != null && slotGroupBaseDto.getSupportNobid() != null && !Utils.isPositive(slotGroupBaseDto.getSupportNobid())) {
                    throw new ServiceRuntimeException("该广告位组不支持nobid，请检查！");
                }
            }
        }

        //新版也需要校验频控
        if (AdpVersion.isMergedInGeneral(adpVersion)) {
            List<CreativeSlotGroupTemplateDto> creativeTemplateDos = adCoreBqf.selectFrom(lauCreativeTemplate)
                    .where(lauCreativeTemplate.unitId.in(unit.getUnitId()))
                    .where(lauCreativeTemplate.isDeleted.eq(IsDeleted.VALID.getCode())).fetch(CreativeSlotGroupTemplateDto.class);
            if (!CollectionUtils.isEmpty(creativeTemplateDos)) {
                List<TemplateDto> templateDtoList = queryTemplateService.getValidTemplatesInIds(creativeTemplateDos.stream().map(CreativeSlotGroupTemplateDto::getTemplateId).distinct().collect(Collectors.toList()));
                Assert.isTrue(!CollectionUtils.isEmpty(templateDtoList), LaunchExceptionCode.CREATIVE_TEMPLATE_NO_EXIST.getMessage());
                //单元下包含动态模板组，则不允许将频控修改为超过上限
                if (templateDtoList.stream().anyMatch(TemplateDto::getIsDynamicArea)) {
                    //FrequencyLimit不为空则校验，为空则设置默认值
                    if (unit.getFrequencyLimit() == null || unit.getFrequencyLimit() == 0) {
                        // 视频版位合并默认频控写最大值
                        if (AdpVersion.isMerged(adpVersion)) {
                            unit.setFrequencyLimit(dynamicUnitFrequencyLimit.get(unit.getFrequencyUnit()));
                            Assert.isTrue(unit.getFrequencyLimit() <= dynamicUnitFrequencyLimit.get(unit.getFrequencyUnit()), "动态页资源位频控必须小于等于" + dynamicUnitFrequencyLimit.get(unit.getFrequencyUnit()) + "，请修改后提交");
                        } else {
                            unit.setFrequencyLimit(Integer.MAX_VALUE);
                        }
                    }
                }
            }
        }

        if (unit.getFrequencyLimit() == null || unit.getFrequencyLimit() == 0) {
            unit.setFrequencyLimit(Integer.MAX_VALUE);
        }

        List<Integer> accountLabelIdList = accountLabelService.getLabelIdsByAccountId(operator.getOperatorId());

        // 非三连校验二阶段出价
        checkUpdateUnitBidTwoStageCostPrice(oldUnit, unit, campaign, operator);
        // 三连校验二阶段出价
        checkMiddleUpdateUnitBidTwoStageMinPrice(campaign, oldUnit, unit, accountLabelIdList, operator);

        // nobid 校验
        NobidValidateParam param = NobidValidateParam.builder()
                .nobidMax(unit.getNoBidMax()).adpVersion(unit.getAdpVersion()).isNoBid(unit.getIsNoBid())
                .accountLabelIdList(accountLabelIdList).ocpcTarget(unit.getOcpcTarget()).campaign(campaign)
                .unitPromotionPurposeType(unit.getPromotionPurposeType())
                .build();
        //【个人起飞】切商业架构需求 https://www.tapd.bilibili.co/********/prong/stories/view/11********002969535
        final boolean isPersonalFly = launchAccountService.isPersonalFly(operator.getOperatorId());
        nobidValidator.validateNoBid(param, isPersonalFly);
        // 校验单元信息
        unitValidator.updateCpc(unit, oldUnit, templateDto, campaign, operator);

        // 日预算自动，设置为计划的预算
        if (unit.getDailyBudgetType().equals(DailyBudgetType.AUTO.getCode())) {
            unit.setBudget(campaign.getBudget());
        }

        //LauUnitPo po = cpcUnitDtoToPo(unit);
        com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauUnitPo po = cpcUnitDtoToPo(unit);

        if (PromotionPurposeType.DYNAMIC.getCode() == po.getPromotionPurposeType()) {
            // 图文动态，需要校验动态的状态
            LauUnitFlyMiddleInfoPo lauUnitFlyMiddleInfoPo = middleFlyUnitService.queryLauUnitFlyMiddleInfo(po.getUnitId());
            if (lauUnitFlyMiddleInfoPo != null) {
                DynamicContentMap.DynamicState state = dynamicService.getDynamicState(lauUnitFlyMiddleInfoPo.getDynamicId());
                if (!Objects.isNull(state)) {
                    // 若创意已删除，则不更新unit
                    Assert.isTrue(!Functions.integer2Boolean(Optional.ofNullable(state.getIsDeleted()).orElse(0L).intValue()), "动态已删除");
                }
            }
        }

        lauBudgetService.updateUnitBudget(operator, unit.getUnitId(), unit.getBudget());
        if (!(PromotionPurposeType.DYNAMIC.getCode() == unit.getPromotionPurposeType()) && LaunchUnitService.isFlyNativeLandingPage(oldUnit.getSalesType(), oldUnit.getOcpcTarget())) {
            po.setPromotionPurposeType(PromotionPurposeType.ARCHIVE_CONTENT.getCode());
        }
        //lauUnitDao.updateByPrimaryKeySelective(po);
        adCoreBqf.update(lauUnit).updateBean(po);

        unitExtraService.saveUnitExtraPo(unit, unit.getUnitId());
        unitValidator.validateNobidUnitBudgetAndCount(unit.getUnitId(), unit.getDailyBudgetType(), unit.getIsNoBid(), campaign);

        if (unit.getAppPackageId() != null
                && unit.getAppPackageId().compareTo(oldUnit.getAppPackageId()) != 0) {

            AppPackageDto appInfo = appPackageService.load(unit.getAppPackageId());
            lauCreativeService.updateCreativeButtonCopyJumpUrl(operator,
                    oldUnit.getUnitId(),
                    Strings.isNullOrEmpty(appInfo.getInternalUrl()) ? appInfo.getUrl() : appInfo.getInternalUrl());
        }

        // 绑定新版定向包的单元 原来的单元映射保存为无定向条件
        boolean isBindUpgradeTargetPackage = Utils.isPositive(unit.getTargetPackageId());
        List<List<Integer>> otherCrowdPackIdsGroup = isBindUpgradeTargetPackage ? Collections.emptyList() : unit.getOtherCrowdPackIdsGroup();
        List<Integer> crowdPackIds = isBindUpgradeTargetPackage ? Collections.emptyList() : unit.getCrowdPackIds(),
                excludePackIds = isBindUpgradeTargetPackage ? Collections.emptyList() : unit.getExcludeCrowdPackIds(),
                extraCrowdPackIds = isBindUpgradeTargetPackage ? Collections.emptyList() : unit.getExtraCrowdPackIds();
        updateCrowdPackIds(unit.getUnitId(), crowdPackIds, excludePackIds, otherCrowdPackIdsGroup, extraCrowdPackIds);

        List<String> tags = isBindUpgradeTargetPackage ? Collections.emptyList() : unit.getTags();
        Integer isFuzzyTags = isBindUpgradeTargetPackage ? 0 : unit.getIsFuzzyTags();
        try {
            lauTagIdsService.updateContentTagIds(unit.getUnitId(), LauTargetTagEnum.UNIT.getCode(), oldUnit.getTags(), tags, isFuzzyTags, oldUnit.getIsFuzzyTags());
            lauTagIdsService.updateTags(unit.getUnitId(), LauTargetTagEnum.UNIT.getCode(), unit.getVideoTag());
        } catch (Exception e) {
            LOGGER.error("update update tags encounter an exception: " + ExceptionUtils.getStackTrace(e));
        }

        unitTargetRuleService.update(operator, unit.getUnitId(), unit.getTargetRules());
        List<Integer> osIds = TargetUtils.getValueIdsByTargetType(unit.getTargetRules(), TargetType.OS.getCode());

        updateCreativeLaunchDate(oldUnit, unit);

        // todo 下线商业兴趣
        //List<Integer> businessInterestIds = isBindUpgradeTargetPackage ? Collections.emptyList() : unit.getBusinessInterestIds();
        //this.saveBusinessInterestTarget(unit.getUnitId(), businessInterestIds);

        List<Integer> labelIdsByAccountId = accountLabelService.getLabelIdsByAccountId(operator.getOperatorId());
//        if (labelIdsByAccountId.contains(supportProfessionInterest)){

        //在行业兴趣人群白名单才处理行业兴趣
        //全量
        professionInterestService.saveProfessionInterestTarget(unit.getUnitId(), unit.getProfessionInterestIds());

        if (IsInterestAuto.IS_AUTO == unit.getProfessionInterestAuto()) {
            professionInterestAutoService.update(unit.getUnitId());
        } else {
            professionInterestAutoService.deleteProfessionInterestAutoByUnitId(unit.getUnitId());
        }

        CpcUnitExtraTargetDto extraTarget = isBindUpgradeTargetPackage ? null : unit.getExtraTarget();
        this.saveExtraTarget(unit.getUnitId(), extraTarget, PromotionPurposeType.getByCode(campaign.getPromotionPurposeType()), operator);

        UnitTargetInstalledUserFilterDto installedUserFilterDto = isBindUpgradeTargetPackage ? null : unit.getInstalledUserFilter();
        saveInstalledUserFilterTarget(unit.getUnitId(), installedUserFilterDto);

        // 操作系统版本和bili客户端版本保存
        lauUnitTargetOsVersionUpgradeProc.save(unit.getUnitId(), unit.getOsVersion(), osIds);
        lauUnitTargetBiliClientUpgradeProc.save(unit.getUnitId(), unit.getBiliClientVersion(), osIds);

        // 编辑单元更新保留价状态
        updateReservedPriceStatus(adpVersion, oldUnit.getUnitId(), unit.getCostPrice(), unit.getIsNoBid());

        //三连推广编辑更新会员购
        updateUnitShopGoods(adpVersion, campaign, oldUnit.getUnitId(), unit.getShopGoodsId());

        // 更新小游戏定向配置
        UnitMiniGameTargetPoExample miniGameExample = new UnitMiniGameTargetPoExample();
        UnitMiniGameTargetPoExample.Criteria criteria = miniGameExample.createCriteria();
        criteria.andUnitIdEqualTo(Long.valueOf(unit.getUnitId()));
        List<UnitMiniGameTargetPo> dbRecords = unitMiniGameTargetDao.selectByExample(miniGameExample);

        UnitMiniGameTargetPo miniGameTargetPo = new UnitMiniGameTargetPo();
        miniGameTargetPo.setUnitId(Long.valueOf(unit.getUnitId()));
        if (!CollectionUtils.isEmpty(dbRecords)) {
            miniGameTargetPo = dbRecords.get(0);
        }

        List<String> includeMiniGameIds = isBindUpgradeTargetPackage ? null : unit.getIncludeMiniGameIds();
        if (includeMiniGameIds != null) {
            if (!CollectionUtils.isEmpty(includeMiniGameIds)) {
                miniGameTargetPo.setIncludeGameIds(String.join(",", includeMiniGameIds));
            } else {
                miniGameTargetPo.setIncludeGameIds("");
            }
        }

        List<String> excludeMiniGameIds = isBindUpgradeTargetPackage ? null : unit.getExcludeMiniGameIds();
        if (excludeMiniGameIds != null) {
            if (!CollectionUtils.isEmpty(excludeMiniGameIds)) {
                miniGameTargetPo.setExcludeGameIds(String.join(",", excludeMiniGameIds));
            } else {
                miniGameTargetPo.setExcludeGameIds("");
            }
        }

        // 由于保存单元的时候游戏不能改动, 所以游戏id会传空, 只有在ios的情况下做这个操作
        if (Objects.equals(campaign.getPromotionPurposeType(), PromotionPurposeType.APP_DOWNLOAD.getCode())) {
            SearchingGameResult searchingGameResult = trySearchingGameBaseId(campaign.getPromotionPurposeType(), unit.getAppPackageId());

            if (searchingGameResult != null && Objects.equals(searchingGameResult.getPlatformType(), GamePlatformTypeEnum.IOS.getCode())) {
                if (Utils.isPositive(searchingGameResult.getGameId())) {
                    launchUnitGameService.insertOrUpdate(LauUnitGameBo.builder()
                            .accountId(campaign.getAccountId())
                            .unitId(unit.getUnitId())
                            .gameBaseId(searchingGameResult.getGameId())
                            .platformType(searchingGameResult.getPlatformType())
                            .build());
                } else {
                    launchUnitGameService.deleteIfExists(unit.getUnitId());
                }
            }
        }

        // 2021-12-15增加
        // 用于修复 「安卓游戏」 推广目的，创建单元时，
        // 设备定向选择了 IOS设备，导致后续无法更新安卓设备定向的数据
        // 目前在创建单元时 已经对「安卓游戏」做了设备限制
        if (Objects.equals(campaign.getPromotionPurposeType(), PromotionPurposeType.ON_SHELF_GAME.getCode())) {
            AccountBaseDto accountBaseDto = queryAccountService.getAccountBaseDtoByIdFromCache(campaign.getAccountId());
            List<Device> targetDevices = unitValidator.getTargetDevices(unit.getTargetRules());
            //外广用户在安卓游戏推广目的下 不允许选择ios定向
            if (Objects.equals(IsInnerEnum.OUTER.getCode(), accountBaseDto.getIsInner())
                    && Device.IOSES.containsAll(targetDevices)) {
                throw new ServiceRuntimeException("请选择安卓设备定向");
            }
            Integer platformType;
            if (Device.IOSES.containsAll(targetDevices)) {
                platformType = GamePlatformTypeEnum.IOS.getCode();
            } else if (Device.ANDROIDS.containsAll(targetDevices)) {
                platformType = GamePlatformTypeEnum.ANDROID.getCode();
            } else {
                throw new ServiceRuntimeException("请选择同一类型的设备平台定向");
            }
            launchUnitGameService.insertOrUpdate(LauUnitGameBo.builder()
                    .accountId(campaign.getAccountId())
                    .unitId(unit.getUnitId())
                    .platformType(platformType)
                    .build());
        }

        //unitMiniGameTargetDao.insertUpdateSelective(miniGameTargetPo);

        // 动态直播预约信息
        processDynamicLiveReserve(unit);

        adProductRepo.batchUpdateProductMapping(unit.getSdpaProductIds(), unit.getUnitId(),
                AdProductBindTypeEnum.UNIT.getCode(), false);

        this.saveUnitUpdateLog(operator, unit, oldUnit);

        Long oldTargetPackageMtime = unit.getTargetPackageMtime();
        // 若绑定新版定向包 乐观锁
        if (Utils.isPositive(oldTargetPackageMtime)) {
            Long currentTargetPackageMtime = resTargetPackageUpgradeService.load(unit.getTargetPackageId(), true).getMtime().getTime();
            Assert.isTrue(oldTargetPackageMtime.equals(currentTargetPackageMtime), "当前绑定定向包已被修改，请刷新页面重试");
        }

        //智能拓流开关
        Integer smartKeyWord = Objects.isNull(unit.getSmartKeyWord()) ? CommonSwitchEnum.ENABLED.getCode() : unit.getSmartKeyWord();
        saveSmartKeySwitch(smartKeyWord, operator, unit.getUnitId());
    }

    /**
     * @param unit
     */
    private void processDynamicLiveReserve(UpdateCpcUnitDto unit) {
        // 更新动态关联的直播预约 sid
        if (unit.getPromotionPurposeType() != null && PromotionPurposeType.DYNAMIC.getCode() == unit.getPromotionPurposeType()) {
            LauUnitFlyMiddleInfoPo lauUnitFlyMiddleInfoPo = lauUnitFlyMiddleInfoRepo.fetchByUnitId(unit.getUnitId());
            if (lauUnitFlyMiddleInfoPo != null) {
                NewDynamicDto newDynamicDto = dynamicQuerierProc.fetchDynamicInfo(lauUnitFlyMiddleInfoPo.getDynamicId());
                if (newDynamicDto != null && Utils.isPositive(newDynamicDto.getSid())) {
                    lauUnitFlyMiddleInfoRepo.updateSid(lauUnitFlyMiddleInfoPo.getId(), lauUnitFlyMiddleInfoPo.getDynamicId(), newDynamicDto.getSid());
                }
            }
        }
    }

    /**
     * 校验非三连起飞单元更新时二阶段转化出价逻辑
     */
    private void checkUpdateUnitBidTwoStageCostPrice(CpcUnitDto oldUnit,
                                                     UpdateCpcUnitDto unit,
                                                     CpcCampaignDto campaign,
                                                     Operator operator) throws ServiceException {
        if (AdpVersion.isMiddle(unit.getAdpVersion())) {
            return;
        }
        //稿件
        if (Objects.equals(PromotionPurposeType.ARCHIVE_CONTENT.getCode(), oldUnit.getPromotionPurposeType())) {
            unitCostService.archiveUnitCostCheck4Update(unit, campaign, operator);
        }

        //动态
        if (Objects.equals(PromotionPurposeType.DYNAMIC.getCode(), oldUnit.getPromotionPurposeType())) {
            unitCostService.dynamicUnitCostCheck(unit);
        }

        //活动
        if (Objects.equals(PromotionPurposeType.ACTIVITY.getCode(), oldUnit.getPromotionPurposeType())) {
            unitCostService.activityUnitCostCheck(unit);
        }
    }

    /**
     * 三连校验二阶段出价
     *
     * @param campaign
     * @param oldUnit
     * @param unit
     * @param accountLabelIdList
     * @param operator
     */
    private void checkMiddleUpdateUnitBidTwoStageMinPrice(CpcCampaignDto campaign,
                                                          CpcUnitDto oldUnit,
                                                          UpdateCpcUnitDto unit,
                                                          List<Integer> accountLabelIdList,
                                                          Operator operator) {
        if (!AdpVersion.isMiddle(unit.getAdpVersion()) || !Utils.isPositive(unit.getOcpcTarget())) {
            return;
        }
        PromotionPurposeType uppt = PromotionPurposeType.getByCode(oldUnit.getPromotionPurposeType());
        Integer salesType = unit.getSalesType();
        // 中台商业起飞
        boolean isMiddleFly = checkIsMiddleFly4UpdateUnit(uppt);
        if (!isMiddleFly) {
            return;
        }
        GetBidCostParam queryFlyBidTwoStageParam = GetBidCostParam.builder()
                .accountId(operator.getOperatorId())
                .salesType(salesType)
                .accountLabelIdList(accountLabelIdList)
                .build();
        Integer flyLowestBidTwoStagePrice;
        final AccountBaseDto accountBaseDto = queryAccountService.getAccountBaseDtoByIdFromCache(operator.getOperatorId());
        if (Utils.isPositive(accountBaseDto.getIsSupportContent())) {
            flyLowestBidTwoStagePrice = cpcUnitService.getContentFlyLowestBid(campaign.getPromotionPurposeType(), unit.getPromotionPurposeType(), unit.getOcpcTarget());
        } else {
            flyLowestBidTwoStagePrice = getFlyBanner4LowestCost4Unit(queryFlyBidTwoStageParam, unit.getOcpcTarget());
        }

        OcpcTargetEnum ocpcTargetEnum = OcpcTargetEnum.getByCode(unit.getOcpcTarget());
        String ocpcTargetDesc = Objects.isNull(ocpcTargetEnum) ? "" : ocpcTargetEnum.getDesc();

        if (unit.getIsNoBid() == null || unit.getIsNoBid().equals(YesNoEnum.NO.getCode())) {
            Assert.isTrue(unit.getTwoStageBid() >= flyLowestBidTwoStagePrice,
                    "起飞单元" + ocpcTargetDesc + "单价成本不能低于" + Utils.fromFenToYuan(flyLowestBidTwoStagePrice) + "元");
            unit.setCostPrice(FlyProLaunchService.getFlyUnitDefaultCostPrice(unit.getIsNoBid()));
        }
    }

    @Transactional(value = AD_CORE_TM, rollbackFor = Exception.class)
    public Set<Integer> batchEnable(Operator operator, List<Integer> unitIds) throws ServiceException {

        LOGGER.info("batchEnable operator:{}, unitIds:{}", operator, unitIds);
        Assert.isTrue(!Operator.validateParamIsNull(operator), "操作人信息不全");

        if (CollectionUtils.isEmpty(unitIds)) {
            return new HashSet<>();
        }

        List<LauUnitPo> lauUnitPos = validateUnitIds(operator.getOperatorId(), unitIds, UnitStatus.VALID.getCode());
        // 校验闪屏直播间开播
        validateSplashScreen(operator.getOperatorId(), lauUnitPos);

        unitIds = lauUnitPos.stream().map(LauUnitPo::getUnitId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(unitIds)) {
            return Collections.emptySet();
        }
        return batchUpdateUnitValidStatus(operator, unitIds);
    }

    private void validateSplashScreen(Integer accountId, List<LauUnitPo> lauUnitPos) throws ServiceException {
        boolean isPersonalFly = launchAccountService.isPersonalFly(accountId);
        if (isPersonalFly) {
            return;
        }

        List<Integer> campaignIds = lauUnitPos.stream()
                .filter(po -> PromotionPurposeType.GOODS_LIVE.getCode() == po.getPromotionPurposeType())
                .map(LauUnitPo::getCampaignId)
                .distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(campaignIds)) {
            return;
        }
        QueryCpcCampaignDto query = QueryCpcCampaignDto.builder()
                .campaignIds(campaignIds)
                .promotionPurposeType(PromotionPurposeType.BRAND_SPREAD.getCode())
                .campaignAdType(CampaignAdType.SPLASH_SCREEN.getCode())
                .build();
        List<CpcCampaignDto> cpcCampaignDtos = cpcCampaignService.queryCpcCampaign(query);
        if (CollectionUtils.isEmpty(cpcCampaignDtos)) {
            return;
        }

        Map<Integer, Integer> campaignId2ValueMap = cpcCampaignDtos.stream().collect(Collectors.toMap(CpcCampaignDto::getCampaignId, CpcCampaignDto::getCampaignId));
        List<Integer> checkLiveShowUnitSubjectIds = new ArrayList<>();
        Map<Integer, LauUnitPo> subjectId2LauUnitPoMap = new HashMap<>();
        lauUnitPos.forEach(po -> {
            if (PromotionPurposeType.GOODS_LIVE.getCode() == po.getPromotionPurposeType() && campaignId2ValueMap.get(po.getCampaignId()) != null) {
                checkLiveShowUnitSubjectIds.add(po.getSubjectId());
            }
            subjectId2LauUnitPoMap.put(po.getSubjectId(), po);
        });
        Map<Integer, String> subjectId2MaterialIdMap = lauSubjectService.querySubjectMaterialRel(checkLiveShowUnitSubjectIds, LauSubjectType.LIVE_ROOM);
        if (MapUtils.isEmpty(subjectId2MaterialIdMap)) {
            return;
        }

        List<Integer> roomIds = subjectId2MaterialIdMap.values().stream().map(Integer::valueOf).distinct().collect(Collectors.toList());
        Map<Integer, LiveBroadcastRoomInfo> roomId2LiveRoomInfoMap = liveBroadcastHttpService.queryLiveBroadcastRoomInfoByIds(roomIds);

        // 未开播不可启用
        for (Integer subjectId : checkLiveShowUnitSubjectIds) {
            LauUnitPo lauUnitPo = subjectId2LauUnitPoMap.get(subjectId);
            String materialId = subjectId2MaterialIdMap.get(subjectId);
            if (!StringUtils.hasText(materialId)) {
                throw new ServiceExtraException(LaunchExceptionCode.FAIL_OPERATION.getCode(), "单元(" + lauUnitPo.getUnitId() + ")绑定直播间id异常");
            }
            Integer roomId = Integer.valueOf(materialId);
            LiveBroadcastRoomInfo roomInfo = roomId2LiveRoomInfoMap.get(roomId);
            if (roomInfo == null || roomInfo.getIsShow() == null || roomInfo.getIsShow() == 0) {
                throw new ServiceExtraException(LaunchExceptionCode.FAIL_OPERATION.getCode(), "单元(" + lauUnitPo.getUnitId() + ")绑定直播间未开播");
            }
        }
    }

    @Transactional(value = AD_CORE_TM, rollbackFor = Exception.class)
    public Set<Integer> batchPause(Operator operator, List<Integer> unitIds) throws ServiceException {

        LOGGER.info("batchPause operator:{}, unitIds:{}", operator, unitIds);
        Assert.isTrue(!Operator.validateParamIsNull(operator), "操作人信息不全");

        if (CollectionUtils.isEmpty(unitIds)) {
            return new HashSet<>();
        }

        validateUnitIds(operator.getOperatorId(), unitIds, UnitStatus.PAUSED.getCode());
        Set<Integer> unitIdSet = batchUpdateStatus(operator, unitIds, LaunchStatus.STOP.getCode(), UnitStatus.PAUSED.getCode());

        return unitIdSet;
    }

    @Transactional(value = AD_CORE_TM, rollbackFor = Exception.class)
    public Set<Integer> batchEnd(Operator operator, List<Integer> unitIds) throws ServiceException {

        LOGGER.info("batchEnable operator:{}, unitIds:{}", operator, unitIds);
        Assert.isTrue(!Operator.validateParamIsNull(operator), "操作人信息不全");

        if (CollectionUtils.isEmpty(unitIds)) {
            return new HashSet<>();
        }

        validateUnitIds(operator.getOperatorId(), unitIds, UnitStatus.FINISHED.getCode());

        Set<Integer> updatedIds = batchUpdateStatus(operator, unitIds, LaunchStatus.FINISH.getCode(), UnitStatus.FINISHED.getCode());
        creativeDelegate.batchEndInUnitIds(operator, unitIds);

        return updatedIds;
    }

    @Transactional(value = AD_CORE_TM, rollbackFor = Exception.class)
    public Set<Integer> batchDelete(Operator operator, List<Integer> unitIds) throws ServiceException {

        LOGGER.info("batchEnable operator:{}, unitIds:{}", operator, unitIds);
        Assert.isTrue(!Operator.validateParamIsNull(operator), "操作人信息不全");

        if (CollectionUtils.isEmpty(unitIds)) {
            return new HashSet<>();
        }

        List<LauUnitPo> lauUnitPos = validateUnitIds(operator.getOperatorId(), unitIds, UnitStatus.DELETED.getCode());
        //起飞GD+投放中的单元的不可删除
        List<LauUnitPo> flyGdPlusUnits = lauUnitPos.stream().filter(o -> IsValid.TRUE.getCode().equals(o.getIsGdPlus())).collect(Collectors.toList());
        flyGdUnitValidator.validateDelete(flyGdPlusUnits);

        // 批量删除单元，status=删除3,unit_status=删除4
        Set<Integer> updatedIds = batchUpdateStatus(operator, unitIds, LaunchStatus.DELETE.getCode(), UnitStatus.DELETED.getCode());

        // 批量删除单元下的创意
        creativeDelegate.batchDeleteInUnitIds(operator, unitIds);

        // 删除单元中小游戏定向配置
        if (!CollectionUtils.isEmpty(unitIds)) {
            UnitMiniGameTargetPoExample example = new UnitMiniGameTargetPoExample();
            UnitMiniGameTargetPoExample.Criteria criteria = example.createCriteria();

            List<Long> deleteUnitIds = unitIds.stream().map(item -> (long) item).collect(Collectors.toList());
            criteria.andUnitIdIn(deleteUnitIds);
            unitMiniGameTargetDao.deleteByExample(example);

            //删除行业产品库的关联
            adProductRepo.deleteProductMapping(unitIds, AdProductBindTypeEnum.UNIT.getCode());
        }

        logService.insertLog(DbTable.LAUN_UNIT_DATE.getName(), OperationType.DELETE, operator,
                String.format("unitIds: %s, isDeleted: %d.", unitIds, IsDeleted.DELETED.getCode()));


        if (!CollectionUtils.isEmpty(flyGdPlusUnits)) {
            List<Integer> gdPlusUnitIds = flyGdPlusUnits.stream().map(LauUnitPo::getUnitId).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(gdPlusUnitIds)) {
                List<Integer> scheduleIds = flyGdPlusDelegate.queryValidScheduleIdsByUnitIds(gdPlusUnitIds);
                if (!CollectionUtils.isEmpty(scheduleIds)) {
                    //异步释放GD+单元
                    new Thread(() -> {
                        try {
                            stockService.deleteBatchScheduleIds(gdPlusUnitIds, scheduleIds, operator);
                        } catch (Exception e) {
                            LOGGER.error("stockService.deleteBatchScheduleIds fail, msg = {}", e.getMessage());
                        }
                    }).start();
                }
            }
        }

        return updatedIds;
    }

    @Transactional(value = AD_CORE_TM, rollbackFor = Exception.class)
    public Set<Integer> batchAppRefreshPause(Operator operator, List<Integer> unitIds) throws ServiceException {

        LOGGER.info("batchEnable operator:{}, unitIds:{}", operator, unitIds);
        Assert.isTrue(!Operator.validateParamIsNull(operator), "操作人信息不全");

        if (CollectionUtils.isEmpty(unitIds)) {
            return new HashSet<>();
        }

        return batchUpdateStatus(operator, unitIds, LaunchStatus.STOP.getCode(),
                UnitStatus.APP_REFRESH.getCode());
    }

    @Transactional(value = AD_CORE_TM, rollbackFor = Exception.class)
    public void batchEndInCampaignIds(Operator operator, List<Integer> campaignIds) throws ServiceException {

        LOGGER.info("batchEnable operator:{}, campaignIds:{}", operator, campaignIds);
        Assert.isTrue(!Operator.validateParamIsNull(operator), "操作人信息不全");

        if (CollectionUtils.isEmpty(campaignIds)) {
            return;
        }

        List<Integer> unitIds = getCpcUnitQuery(QueryCpcUnitDto
                .builder()
                .accountId(operator.getOperatorId())
                .campaignIds(campaignIds)
                .notUnitStatusList(Arrays.asList(UnitStatus.FINISHED.getCode(), UnitStatus.DELETED.getCode()))
                .build())
                .select(lauUnit.unitId)
                .fetch();

        batchUpdateStatus(operator, unitIds, LaunchStatus.FINISH.getCode(), UnitStatus.FINISHED.getCode());

        creativeDelegate.batchEndInUnitIds(operator, unitIds);
    }

    @Transactional(value = AD_CORE_TM, rollbackFor = Exception.class)
    public void batchDeleteInCampaignIds(Operator operator, List<Integer> campaignIds) throws ServiceException {

        LOGGER.info("batchEnable operator:{}, campaignIds:{}", operator, campaignIds);
        Assert.isTrue(!Operator.validateParamIsNull(operator), "操作人信息不全");

        if (CollectionUtils.isEmpty(campaignIds)) {
            return;
        }

        List<Integer> unitIds = getCpcUnitQuery(QueryCpcUnitDto
                .builder()
                .accountId(operator.getOperatorId())
                .campaignIds(campaignIds)
                .notUnitStatus(UnitStatus.DELETED.getCode())
                .build())
                .select(lauUnit.unitId)
                .fetch();

        batchUpdateStatus(operator, unitIds, LaunchStatus.DELETE.getCode(), UnitStatus.DELETED.getCode());

        creativeDelegate.batchDeleteInUnitIds(operator, unitIds);

        logService.insertLog(DbTable.LAUN_UNIT_DATE.getName(), OperationType.DELETE, operator,
                String.format("unitIds: %s, isDeleted: %d.", unitIds, IsDeleted.DELETED.getCode()));
    }

    /**
     * 修改计划下的单元预算(针对自动更新预算的单元)
     *
     * @param campaignId
     * @param budget
     * @param operator
     * @throws ServiceException
     */
    @Transactional(value = AD_CORE_TM, rollbackFor = Exception.class)
    public void updateBudgetWithCampaignId(Integer campaignId, Long budget, Operator operator) throws ServiceException {
        Assert.notNull(campaignId, "计划id不能为空");
        Assert.notNull(budget, "预算不能为空");

        // 获取计划下自动修改日预算的单元 list
        LauUnitPoExample example = new LauUnitPoExample();
        example.or()
                .andCampaignIdEqualTo(campaignId)
                .andDailyBudgetTypeEqualTo(DailyBudgetType.AUTO.getCode());

        List<LauUnitPo> pos = lauUnitDao.selectByExample(example);

        if (CollectionUtils.isEmpty(pos)) {
            return;
        }

        List<LauUnitBudgetPo> lauUnitBudgetPoList = new ArrayList<>();
        //修改计划下的单元预算改为批处理，不再校验单元预算
        for (LauUnitPo unitPo : pos) {
            //lauBudgetService.updateUnitBudget(operator, unitPo.getUnitId(), budget);

            // 需要修改的计划预算与单元的预算不一样
            if (budget.compareTo(unitPo.getBudget()) != 0) {
                LauUnitBudgetPo lauUnitBudgetPo = new LauUnitBudgetPo();
                lauUnitBudgetPo.setBudget(budget);
                lauUnitBudgetPo.setUnitId(unitPo.getUnitId());
                lauUnitBudgetPo.setEffectiveTime(Utils.getNow());
                /*lauUnitBudgetPo.setIsDeleted(IsDeleted.VALID.getCode());
                lauUnitBudgetPo.setCtime(Utils.getNow());
                lauUnitBudgetPo.setMtime(Utils.getNow());*/
                lauUnitBudgetPoList.add(lauUnitBudgetPo);
            }
        }

        // 获取计划下自动修改日预算的单元情况保存
        if (!CollectionUtils.isEmpty(lauUnitBudgetPoList)) {
            adBqf.insert(qLauUnitBudget).insertBeans(lauUnitBudgetPoList);
            //lauUnitBudgetDao.insertBatch(lauUnitBudgetPoList);
        }

        // 获取计划下自动修改日预算的单元的预算修改为计划的预算
        LauUnitPo po = new LauUnitPo();
        po.setBudget(budget);
        lauUnitDao.updateByExampleSelective(po, example);
    }

    /**
     * 修改当日预算
     *
     * @param unitId
     * @param budget          单元需要修改成的当日预算
     * @param dailyBudgetType
     * @param operator
     * @param gdPlusJobUpdate
     * @throws ServiceException
     */
    @Transactional(value = AD_CORE_TM, rollbackFor = Exception.class)
    public void updateBudget(Integer unitId, BigDecimal budget, Integer dailyBudgetType, Operator operator, boolean gdPlusJobUpdate) throws ServiceException {
        CpcUnitDto unit = loadCpcUnit(unitId);

        if (AdpVersion.isLegacyFly(unit.getAdpVersion())) {
            launchOfflineService.validateOfflineStage(unit.getAccountId(), false);
        }

        if (!gdPlusJobUpdate) {
            Assert.isTrue(unit.getAccountId().equals(operator.getOperatorId()), "不能操作不属于你的单元");
        }

        Assert.isTrue(Objects.equals(0, unit.getIsManaged()), "托管单元不能修改预算");
        CpcCampaignDto campaign = cpcCampaignService.loadCpcCampaignDto(unit.getCampaignId());

        // 手动-指定预算，自动-计划预算(不会超过计划的预算)
        Long lBudget = dailyBudgetType != null && dailyBudgetType.equals(DailyBudgetType.MANUAL.getCode()) ? Utils.fromYuanToFen(budget) : campaign.getBudget();

        Assert.isTrue(lBudget.compareTo(campaign.getBudget()) <= 0, "单元预算不能超过计划预算");

        LauUnitPo po = new LauUnitPo();
        po.setUnitId(unitId);
        po.setDailyBudgetType(dailyBudgetType);
        po.setBudget(lBudget);

        // 包含了1.2倍的校验逻辑
        lauBudgetService.updateUnitBudget(operator, unitId, lBudget);

        // 修改单元上的预算
        lauUnitDao.updateByPrimaryKeySelective(po);

        // NOBID单元当日预算修改次数不得超过 3 次
        unitBudgetCountComponent.checkNobidUnitBudgetUpdateCount(unitId, unit.getIsNoBid());

        // 校验 nobid 单元的当日预算情况
        nobidValidator.checkNoBidUnitBudgetIfNecessary(unit.getCampaignId(), campaign.getBudget());

        logOperateService.addUpdateLog(DbTable.LAU_UNIT,
                operator,
                Unit.builder().unitId(unitId).budget(lBudget).build(),
                unitId);
    }

    /**
     * 修改单元次日预算
     *
     * @param unitId
     * @param budget
     * @param isRepeat
     * @param operator
     * @throws ServiceException
     */
    @Transactional(value = AD_TM, rollbackFor = Exception.class)
    public void updateNextdayBudget(Integer unitId, BigDecimal budget, Integer isRepeat, Operator operator) throws ServiceException {
        CpcUnitDto unit = loadCpcUnit(unitId);
        if (AdpVersion.isLegacyFly(unit.getAdpVersion())) {
            launchOfflineService.validateOfflineStage(unit.getAccountId(), false);
        }

        Assert.isTrue(unit.getAccountId().equals(operator.getOperatorId()), "不能操作不属于你的单元");

        Assert.isTrue(Objects.equals(0, unit.getIsManaged()), "托管单元不能修改预算");
        Long lBudget = Utils.fromYuanToFen(budget);

        long budgetLimit = lauUnitService.getUnitBudgetLimit(operator.getOperatorId());
        Assert.isTrue(lBudget.compareTo(budgetLimit) >= 0, "单元次日预算不可低于" + Utils.fromFenToYuan(budgetLimit) + "元");

        // 计划日次预算
        Long campaignNextDayBudget = 0L;
        NewLauCampaignNextdayBudgetDto campaignNextdayBudgetDto = lauBudgetService.getCampaignNextdayBudgetDto(unit.getCampaignId());
        if (campaignNextdayBudgetDto != null) {
            campaignNextDayBudget = campaignNextdayBudgetDto.getNextdayBudget();
            Assert.isTrue(lBudget.compareTo(campaignNextdayBudgetDto.getNextdayBudget()) <= 0, "单元次日预算不能超过计划次日预算");
        } else {
            //计划未设置次日预算时，单元次日预算不能比计划当日预算大
            CpcCampaignDto campaign = cpcCampaignService.loadCpcCampaignDto(unit.getCampaignId());
            campaignNextDayBudget = campaign.getBudget();
            Assert.isTrue(lBudget.compareTo(campaign.getBudget()) <= 0, "单元次日预算不能比计划日预算大，请先设置计划次日预算或修改计划当日预算");
        }

        LauUnitNextdayBudgetPoExample example = new LauUnitNextdayBudgetPoExample();
        example.or()
                .andUnitIdEqualTo(unitId)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        Timestamp budgetEffectiveTime = Timestamp.valueOf(LocalDate.now().plusDays(1).atTime(LocalTime.MIDNIGHT));
        LauUnitNextdayBudgetPo po = new LauUnitNextdayBudgetPo();
        po.setBudget(lBudget);
        po.setBudgetEffectiveTime(budgetEffectiveTime);
        po.setIsRepeat(isRepeat != null ? isRepeat : 0);
        UnitLogDto newLogEntity = UnitLogDto.builder()
                .unitId(unitId)
                .isRepeat(isRepeat)
                .nextdayBudget(lBudget).build();

        List<LauUnitNextdayBudgetPo> poList = lauUnitNextdayBudgetDao.selectByExample(example);
        // 单元没有次日预算，则新增；否则更新
        if (CollectionUtils.isEmpty(poList)) {
            // insert
            po.setUnitId(unitId);
            lauUnitNextdayBudgetDao.insertSelective(po);
            logOperateService.addInsertLog(DbTable.LAU_UNIT,
                    operator,
                    newLogEntity,
                    unitId);
        } else {
            // update
            LauUnitNextdayBudgetPo oldNextdayBudgetPo = poList.get(0);
            Assert.isTrue(budgetEffectiveTime.equals(oldNextdayBudgetPo.getBudgetEffectiveTime()), "次日预算处理中，请稍后重试");
            po.setId(oldNextdayBudgetPo.getId());
            lauUnitNextdayBudgetDao.updateByPrimaryKeySelective(po);

            UnitLogDto oldLogEntity = UnitLogDto.builder().unitId(unitId).nextdayBudget(oldNextdayBudgetPo.getBudget()).build();
            logOperateService.addUpdateLog(DbTable.LAU_UNIT,
                    operator,
                    oldLogEntity,
                    newLogEntity,
                    unitId);
        }

        // 校验 nobid 单元的次日预算情况
        nobidValidator.checkNoBidUnitNextDayBudgetIfNecessary(unit.getCampaignId(), campaignNextDayBudget);
    }

    /**
     * 修改当日预算
     *
     * @param unitIds
     * @param budget          单元需要修改成的当日预算
     * @param dailyBudgetType
     * @param operator
     * @throws ServiceException
     */
    @Transactional(value = AD_CORE_TM, rollbackFor = Exception.class)
    public void batchUpdateBudget(List<Integer> unitIds, BigDecimal budget, Integer dailyBudgetType, Operator operator) throws ServiceException {
        LOGGER.info("batchUpdateBudget unitIds:{}, budget:{}, dailyBudgetType:{}, operator:{}", unitIds, budget, dailyBudgetType, operator);

        if (CollectionUtils.isEmpty(unitIds)) {
            return;
        }

        for (Integer unitId : unitIds) {
            try {
                this.updateBudget(unitId, budget, dailyBudgetType, operator, false);
            } catch (Exception e) {
                LOGGER.error("batchUpdateBudget exception unitId:{}: ", unitId, e);
                throw new ServiceException("修改单元[" + unitId + "]预算失败：" + e.getMessage());
            }
        }
    }

    /**
     * 批量修改单元预算
     *
     * @param dto
     * @param operator
     * @throws ServiceException
     */
    @Transactional(value = AD_CORE_TM, rollbackFor = Exception.class)
    public void batchUpdateCpcUnitBudget(NewBatchUpdateCpcUnitBudgetDto dto, Operator operator) throws ServiceException {
        LOGGER.info("batchUpdateCpcUnitBudget dto:{}, operator:{}", dto, operator);

        //预算生效类型未指定时或不是指定类型时默认设置为当日预算
        BudgetEffectType budgetEffectType = BudgetEffectType.getByCode(dto.getEffectType()) == null ? BudgetEffectType.CURRENTDAY : BudgetEffectType.getByCode(dto.getEffectType());

        // 当日预算
        if (budgetEffectType == BudgetEffectType.CURRENTDAY) {
            batchUpdateBudget(dto.getUnitIds(), dto.getBudget(), dto.getDailyBudgetType(), operator);
            return;
        }
        // 次日预算
        if (budgetEffectType == BudgetEffectType.NEXTDAY) {
            batchUpdateNextdayBudget(dto.getUnitIds(), dto.getBudget(), dto.getIsRepeat(), operator);
        }

    }

    @Transactional(value = AD_CORE_TM, rollbackFor = Exception.class)
    public void batchBindTargetPackageUpgrade(UnitBatchBindTargetPackageUpgradeBo batchBindBo) {
        validateTargetPackageUpgradeBatchBindDto(batchBindBo);
        Integer targetPackageId = batchBindBo.getTargetPackageId();
        Integer accountId = batchBindBo.getOperator().getOperatorId();

        // 校验定向包
        QueryTargetPackageUpgradeDto queryDto = QueryTargetPackageUpgradeDto.builder()
                .id(batchBindBo.getTargetPackageId())
                .build();
        List<TargetPackageUpgradeDto> targetPackageUpgradeDtos = resTargetPackageUpgradeService.query(queryDto);
        Assert.notEmpty(targetPackageUpgradeDtos, "该定向包不存在");
        TargetPackageUpgradeDto targetPackageUpgradeDto = targetPackageUpgradeDtos.get(0);
        Assert.isTrue(targetPackageUpgradeDto.getAccountId().equals(accountId),
                "您不能操作不属于当前账户的定向包");

        List<Integer> bindUnitIdList = CollectionUtils.isEmpty(batchBindBo.getBindUnitIdList()) ?
                Collections.emptyList() : batchBindBo.getBindUnitIdList();
        List<Integer> unbindUnitIdList = CollectionUtils.isEmpty(batchBindBo.getUnbindUnitIdList()) ?
                Collections.emptyList() : batchBindBo.getUnbindUnitIdList();
        // 最多100个 目前解绑和绑定互斥
        List<Integer> needSaveUnitIdList = Stream.of(bindUnitIdList, unbindUnitIdList)
                .filter(list -> !CollectionUtils.isEmpty(list))
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
        // 没有需要更新的单元 直接返回
        if (CollectionUtils.isEmpty(needSaveUnitIdList)) {
            return;
        }

        // 校验所有单元合法
//        QueryCpcUnitDto queryExistUnitDto = QueryCpcUnitDto.builder()
//                .accountId(accountId)
//                .unitIds(needSaveUnitIdList)
//                .adpVersions(Lists.newArrayList(AdpVersion.MIDDLE.getKey()))
//                .salesTypes(PLATFORM_SALES_TYPES)
//                .unitStatusList(UnitStatus.NON_DELETED_NON_HISTORY_UNIT_STATUS_LIST)
//                .build();
//        List<CpcUnitDto> unitDtoList = queryCpcUnit(queryExistUnitDto);
        QueryUnitBo query = QueryUnitBo.builder()
                .accountIds(Collections.singletonList(accountId))
                .unitIds(needSaveUnitIdList)
                .adpVersions(AdpVersion.MIDDLE_AND_MERGE_LIST)
                .salesTypes(PLATFORM_SALES_TYPES)
                .unitStatusList(UnitStatus.NON_DELETED_NON_HISTORY_UNIT_STATUS_LIST)
                .build();
        List<CpcUnitDto> unitDtoList = launchUnitV1Service.listUnits(query);
        Assert.isTrue(unitDtoList.size() == needSaveUnitIdList.size(),
                "您不能绑定不属于自己的单元或非三连推广单元");
        Assert.isTrue(unitDtoList.stream()
                        .allMatch(unitDto ->
                                unitDto.getAdpVersion().equals(targetPackageUpgradeDto.getAdpVersion())),
                "新三连定向包只能用于新三连单元 老三连定向包只能用于老三连单元");
        List<Integer> bindedUnitIdList = unitDtoList.stream()
                .filter(unit -> unit.getTargetPackageId().equals(targetPackageId))
                .map(CpcUnitDto::getUnitId)
                .collect(Collectors.toList());


        List<Integer> needExpandUnitIds = bindUnitIdList.stream()
                .filter(unitId -> !bindedUnitIdList.contains(unitId))
                .collect(Collectors.toList());
        List<Integer> needRemoveUnitIds = unbindUnitIdList.stream()
                .filter(bindedUnitIdList::contains)
                .collect(Collectors.toList());
        // 没有需要更新的单元 直接返回
        if (CollectionUtils.isEmpty(needExpandUnitIds)
                && CollectionUtils.isEmpty(needRemoveUnitIds)) {
            return;
        }

        boolean hasManagedUnit = unitDtoList.stream()
                .anyMatch(unitDto -> Utils.isPositive(unitDto.getIsManaged()));
        Assert.isTrue(!hasManagedUnit, "您不能在此界面绑定/解绑托管单元");

        // 检查新版定向包将总共绑定的数量
        QueryCpcUnitDto queryTargetPackageTotalBindedDto = QueryCpcUnitDto.builder()
                .accountId(accountId)
                .targetPackageId(targetPackageId)
                .adpVersions(AdpVersion.MIDDLE_AND_MERGE_LIST)
                .salesTypes(PLATFORM_SALES_TYPES)
                .unitStatusList(UnitStatus.NON_DELETED_NON_HISTORY_UNIT_STATUS_LIST)
                .build();
        List<Integer> targetPackageTotalBindedUnitIds = queryCpcUnitIds(queryTargetPackageTotalBindedDto);
        Integer currentTotalBindNum =
                targetPackageTotalBindedUnitIds.size() + needExpandUnitIds.size() - needRemoveUnitIds.size();
        Assert.isTrue(currentTotalBindNum <= targetPackageUpgradeBindTotalLimit,
                "单个新版定向包总共最多绑定" + targetPackageUpgradeBindTotalLimit
                        + "个单元, 目前绑定了" + currentTotalBindNum + "个单元");


        if (!CollectionUtils.isEmpty(needExpandUnitIds)) {
            List<CpcUnitDto> needExpandUnitDtos = unitDtoList.stream()
                    .filter(unitDto -> needExpandUnitIds.contains(unitDto.getUnitId()))
                    .collect(Collectors.toList());
            // 最终校验单元 前面校验过数量了 这里校验单元最多不会超过100个
            resTargetPackageRuleUpgradeService.validateBatchBindTargetRules(
                    targetPackageId, targetPackageUpgradeDto.getPromotionPurposeType(), needExpandUnitDtos);

            // 更新单元
            LauUnitPoExample bindExample = new LauUnitPoExample();
            bindExample.or().andUnitIdIn(needExpandUnitIds);
            LauUnitPo bindPo = new LauUnitPo();
            bindPo.setTargetPackageId(targetPackageId);
            lauUnitDao.updateByExampleSelective(bindPo, bindExample);
        }

        if (!CollectionUtils.isEmpty(needRemoveUnitIds)) {
            LauUnitPoExample unbindExample = new LauUnitPoExample();
            unbindExample.or().andUnitIdIn(needRemoveUnitIds);
            LauUnitPo unbindPo = new LauUnitPo();
            unbindPo.setTargetPackageId(0);
            lauUnitDao.updateByExampleSelective(unbindPo, unbindExample);
        }

        // 批量写入日志
        recordBatchBindTargetPackageUpgradeLog(unitDtoList, targetPackageId, batchBindBo.getOperator(), needExpandUnitIds, needRemoveUnitIds);
    }

    private void recordBatchBindTargetPackageUpgradeLog(List<CpcUnitDto> unitDtoList,
                                                        Integer targetPackageId,
                                                        Operator operator,
                                                        List<Integer> needExpandUnitIds,
                                                        List<Integer> needRemoveUnitIds) {
        if (CollectionUtils.isEmpty(unitDtoList)) {
            return;
        }

        // 写入日志 理论最多一百条
        unitDtoList.forEach(unitDto -> {
            Integer unitId = unitDto.getUnitId();
            CpcUnitDto oldUnitLogEntity = CpcUnitDto.builder()
                    .unitId(unitId)
                    .targetPackageId(unitDto.getTargetPackageId())
                    .build();
            CpcUnitDto updateUnitLogEntity = CpcUnitDto.builder()
                    .unitId(unitId)
                    .build();
            if (!CollectionUtils.isEmpty(needExpandUnitIds)
                    && needExpandUnitIds.contains(unitId)) {
                updateUnitLogEntity.setTargetPackageId(targetPackageId);
                logOperateService.addUpdateLog(
                        DbTable.LAU_UNIT, operator, oldUnitLogEntity, updateUnitLogEntity, unitId);
            } else if (!CollectionUtils.isEmpty(needRemoveUnitIds)
                    && needRemoveUnitIds.contains(unitId)) {
                updateUnitLogEntity.setTargetPackageId(0);
                logOperateService.addUpdateLog(
                        DbTable.LAU_UNIT, operator, oldUnitLogEntity, updateUnitLogEntity, unitId);
            }
        });
    }

    private void validateTargetPackageUpgradeBatchBindDto(UnitBatchBindTargetPackageUpgradeBo batchBindDto) {
        Assert.isTrue(Utils.isPositive(batchBindDto.getTargetPackageId()), "绑定定向包id不可为空");
        Assert.notNull(batchBindDto.getOperator(), "操作人不可为空");
        Assert.isTrue(CollectionUtils.isEmpty(batchBindDto.getBindUnitIdList())
                || CollectionUtils.isEmpty(batchBindDto.getUnbindUnitIdList()), "解绑和绑定单元不能同时进行");
        if (!CollectionUtils.isEmpty(batchBindDto.getBindUnitIdList())
                || !CollectionUtils.isEmpty(batchBindDto.getUnbindUnitIdList())) {
            int batchBindSize = CollectionUtils.isEmpty(batchBindDto.getBindUnitIdList()) ?
                    0 : batchBindDto.getBindUnitIdList().size();
            int batchUnbindSize = CollectionUtils.isEmpty(batchBindDto.getUnbindUnitIdList()) ?
                    0 : batchBindDto.getUnbindUnitIdList().size();
            Integer total = batchBindSize + batchUnbindSize;
            Assert.isTrue(total <= targetPackageUpgradeBindSingleLimit,
                    "单次批量操作绑定解绑单元数总计不能超过" + targetPackageUpgradeBindSingleLimit
                            + "个单元，当前总数为" + total + "个");
        } else {
            throw new IllegalArgumentException("当前无有效绑定/解绑操作内容");
        }
    }

    @Transactional(value = AD_CORE_TM, rollbackFor = Exception.class)
    public Integer updateCpcUnitName(UpdateUnitNameDto dto, Operator operator) throws ServiceException {
        LOGGER.info("updateCpcUnitName dto:{}, operator:{}", dto, operator);

        return unitNameUpdator.updateUnitName(dto, operator);
    }

    @Transactional(value = AD_TM, rollbackFor = Exception.class)
    public void deleteCpcUnitNextdayBudget(Integer unitId, Operator operator) throws ServiceException {
        LOGGER.info("deleteCpcUnitNextdayBudget unitId:{}, operator:{}", unitId, operator);
        LauUnitPo unitPo = lauUnitDao.selectByPrimaryKey(unitId);
        if (unitPo == null) {
            return;
        }

        LauUnitNextdayBudgetPoExample example = new LauUnitNextdayBudgetPoExample();
        example.or().andUnitIdEqualTo(unitId)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());

        List<LauUnitNextdayBudgetPo> poList = lauUnitNextdayBudgetDao.selectByExample(example);
        if (CollectionUtils.isEmpty(poList)) {
            return;
        }

        // 添加预算生效时间校验
        Timestamp budgetEffectiveTime = Timestamp.valueOf(LocalDate.now().plusDays(1).atTime(LocalTime.MIDNIGHT));
        LauUnitNextdayBudgetPo oldBudgetPo = poList.get(0);
        Assert.isTrue(budgetEffectiveTime.equals(oldBudgetPo.getBudgetEffectiveTime()), "次日预算处理中，请稍后重试");

        LauUnitNextdayBudgetPo po = new LauUnitNextdayBudgetPo();
        po.setIsDeleted(IsDeleted.DELETED.getCode());

        lauUnitNextdayBudgetDao.updateByExampleSelective(po, example);

        Unit oldLogDto = Unit.builder().unitId(unitId).nextdayBudget(oldBudgetPo.getBudget()).build();

        Unit newLogDto = Unit.builder().unitId(unitId).nextdayBudget(0l).build();

        logOperateService.addDeleteLog(DbTable.LAU_UNIT,
                operator,
                oldLogDto,
                newLogDto,
                unitId);
    }

    /**
     * 批量修改单元次日预算
     *
     * @param unitIds
     * @param budget
     * @param isRepeat 是否重复
     * @param operator
     * @throws ServiceException
     */
    @Transactional(value = AD_TM, rollbackFor = Exception.class)
    public void batchUpdateNextdayBudget(List<Integer> unitIds, BigDecimal budget, Integer isRepeat,
                                         Operator operator) throws ServiceException {
        LOGGER.info("batchUpdateNextdayBudget unitIds:{}, budget:{}, dailyBudgetType:{}, isRepeat:{}, operator:{}", unitIds,
                budget, isRepeat, operator);

        if (CollectionUtils.isEmpty(unitIds)) {
            return;
        }

        for (Integer unitId : unitIds) {
            try {
                updateNextdayBudget(unitId, budget, isRepeat, operator);
            } catch (Exception e) {
                LOGGER.error("batchUpdateBudget exception unitId:{}: ", unitId, e);
                throw new ServiceException("修改单元[" + unitId + "]预算失败：" + e.getMessage());
            }
        }
    }

    @Transactional(value = AD_CORE_TM, rollbackFor = Exception.class)
    public void updateCostPrice(Integer unitId, BigDecimal costPrice, Operator operator) throws ServiceException {
        Assert.notNull(unitId, "单元id不能为空");
        Assert.notNull(costPrice, "出价不能为空");
        //防止越界之后出价变成0.1这种情况
        Assert.isTrue(costPrice.multiply(HUNDRED).longValue() < Integer.MAX_VALUE, "出价过高异常");

        Assert.isTrue(!Operator.validateParamIsNull(operator), "操作人信息不完整");

        CpcUnitDto unit = loadCpcUnit(unitId);
        Integer intCostPrice = Utils.fromYuanToIntFen(costPrice);
        Assert.isTrue(unit.getAccountId().equals(operator.getOperatorId()), "不能操作不属于你的单元");

        if (Objects.equals(PromotionPurposeType.ARCHIVE_CONTENT.getCode(), unit.getPromotionPurposeType())) {
            if (Utils.isPositive(unit.getOcpcTarget()) && !Utils.isPositive(unit.getCostPrice())) {
                intCostPrice = FlyProLaunchService.getFlyUnitDefaultCostPrice(unit.getIsNoBid());
            }
        }

        Integer lowBid = getLowestCost(GetBidCostParam.builder()
                .accountId(unit.getAccountId())
                .slotGroupId(unit.getSlotGroup())
                .salesType(unit.getSalesType())
                .launchType(unit.getPromotionPurposeType())
                .build());

        Assert.isTrue(intCostPrice.compareTo(lowBid) >= 0, "出价能不低于底价" + Utils.fromFenToYuan(lowBid) + "元");

        // NOBID单元不支持修改出价
        if (YesNoEnum.YES.getCode().equals(unit.getIsNoBid())) {
            throw new ServiceRuntimeException("NOBID单元不支持修改出价/目标转化出价");
        }

        LauUnitPo po = new LauUnitPo();
        po.setUnitId(unitId);
        po.setCostPrice(intCostPrice);

        lauUnitDao.updateByPrimaryKeySelective(po);

        logOperateService.addUpdateLog(DbTable.LAU_UNIT,
                operator,
                Unit.builder().unitId(unitId).costPrice(intCostPrice).budget(unit.getBudget()).build(),
                unitId);
    }

    @Transactional(value = AD_CORE_TM, rollbackFor = Exception.class)
    public void batchUpdateCostPrice(List<Integer> unitIds, BigDecimal costPrice, Operator operator) throws ServiceException {
        LOGGER.info("batchUpdateCostPrice unitIds:{}, costPrice:{}, operator:{}", unitIds, costPrice, operator);

        if (CollectionUtils.isEmpty(unitIds)) {
            return;
        }

        for (Integer unitId : unitIds) {
            try {
                updateCostPrice(unitId, costPrice, operator);
            } catch (Exception e) {
                LOGGER.error("batchUpdateCostPrice exception unitId:{}:", unitId, e);
                throw new ServiceException("修改单元[" + unitId + "]出价失败：" + e.getMessage());
            }
        }
    }

    @Transactional(value = AD_TM, rollbackFor = Exception.class)
    public void updateAppIdForAccount(Operator operator, Integer appId) {
        Assert.notNull(operator.getOperatorId(), "账号id不能为空");
        Assert.notNull(appId);

        LauUnitPoExample example = new LauUnitPoExample();
        example.or()
                .andAccountIdEqualTo(operator.getOperatorId());

        LauUnitPo po = new LauUnitPo();
        po.setAppPackageId(appId);
        po.setPromotionPurposeType(PromotionPurposeType.APP_DOWNLOAD.getCode());

        lauUnitDao.updateByExampleSelective(po, example);

        LauCampaignPoExample campaignExample = new LauCampaignPoExample();
        campaignExample.or()
                .andAccountIdEqualTo(operator.getOperatorId());

        LauCampaignPo cPo = new LauCampaignPo();
        cPo.setPromotionPurposeType(PromotionPurposeType.APP_DOWNLOAD.getCode());

        lauCampaignDao.updateByExampleSelective(cPo, campaignExample);
    }

    public void refreshIosValidApps() {
        try {
            refreshIosApps();
        } catch (Exception e) {
            LOGGER.error("refreshIosApps error" + Throwables.getStackTraceAsString(e));
        }
    }

    public void refreshAndroidApp() {
        Integer lastMaxId = 0;
        String cursorStr = redisTemplate.opsForValue().get(ANDROID_REFRESH_ID_CURSOR);
        if (null != cursorStr && Integer.parseInt(cursorStr) > 0) {
            lastMaxId = Integer.parseInt(cursorStr);
        }
        while (true) {
            ResAppPackagePoExample example = new ResAppPackagePoExample();
            example.or().andIdGreaterThan(lastMaxId);
            example.setOrderByClause("id");
            example.setLimit(1000);
            List<ResAppPackagePo> poList = resAppPackageDao.selectByExample(example);
            if (CollectionUtils.isEmpty(poList)) {
                //  redis set 0
                redisTemplate.opsForValue().set(ANDROID_REFRESH_ID_CURSOR, "0");
                break;
            }
            lastMaxId = poList.get(poList.size() - 1).getId();
            redisTemplate.opsForValue().set(ANDROID_REFRESH_ID_CURSOR, String.valueOf(lastMaxId));

            List<Integer> appIdList = poList.stream()
                    .filter(appPo -> appPo.getIsDeleted().equals(IsDeleted.VALID.getCode())
                            && appPo.getStatus().equals(AppPackageStatus.VALID.getCode())
                            && appPo.getPlatformStatus().equals(AppPackagePlatformStatus.VALID.getCode())
                            && AppAddType.URL == AppAddType.getEnum(appPo.getAddType())
                            && AppPlatformType.ANDROID.getCode().equals(appPo.getPlatform()))
                    .map(ResAppPackagePo::getId)
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(appIdList)) {
                continue;
            }

            for (Integer appId : appIdList) {
//                long count = extLauUnitDao.countValidUnitByAppPackageId(appId);
//                if (count > 0) {
//                    appPackageService.refreshApk(appId);
//                }
                appPackageService.refreshApk(appId);
            }
        }
    }

    private void refreshIosApps() {
        Integer lastMaxId = 0;
        while (true) {
            ResAppPackagePoExample example = new ResAppPackagePoExample();
            example.or().andIdGreaterThan(lastMaxId);
            example.setOrderByClause("id");
            example.setLimit(1000);
            List<ResAppPackagePo> poList = resAppPackageDao.selectByExample(example);
            if (CollectionUtils.isEmpty(poList)) {
                break;
            }
            lastMaxId = poList.get(poList.size() - 1).getId();

            List<ResAppPackagePo> iosApkList = poList.stream()
                    .filter(appPo -> appPo.getIsDeleted().equals(IsDeleted.VALID.getCode())
                            && appPo.getStatus().equals(AppPackageStatus.VALID.getCode())
                            && appPo.getPlatformStatus().equals(AppPackagePlatformStatus.VALID.getCode())
                            && (AppPlatformType.IPAD.getCode().equals(appPo.getPlatform()) ||
                            AppPlatformType.IOS.getCode().equals(appPo.getPlatform()) ||
                            AppPlatformType.IPHONE.getCode().equals(appPo.getPlatform())))
                    .collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(iosApkList)) {
                LOGGER.info("refreshAllValidApps iosApkListIds={}", iosApkList.stream()
                        .map(ResAppPackagePo::getId).collect(Collectors.toList()));
                //ios
                iosApkList.forEach(resAppPackagePo -> {
                    try {
                        long count = extLauUnitDao.countValidUnitByAppPackageId(resAppPackagePo.getId());
                        Thread.sleep(100);
                        if (count > 0) {
                            appPackageService.addTask(resAppPackagePo.getUrl(), Operator.builder()
                                    .operatorId(resAppPackagePo.getAccountId()).operatorName(OperatorType.SYSTEM.getName())
                                    .operatorType(OperatorType.SYSTEM).build(), true, resAppPackagePo.getId());
                        }
                    } catch (Exception e) {
                        LOGGER.error("refreshAllValidApps ios error apk info [{}] error [{}]", resAppPackagePo,
                                Throwables.getStackTraceAsString(e));
                    }
                });
                LOGGER.info("refreshAllValidApps ios done!");
            }
        }
    }

    public int refreshStatus(int gtUnitId, int perSize) {
        long __startTime = System.currentTimeMillis();
        long __endTime = __startTime;

        // 按unitId从小到大查询前n条
        List<LauUnitPo> pos = this.getCpcUnitQuery(QueryCpcUnitDto
                        .builder()
                        .gtUnitId(gtUnitId)
                        .page(Page.valueOf(1, perSize))
                        .orderBy("unit_id asc")
                        .salesTypes(SalesType.PLATFORM_SALES_TYPES)
                        // 未删除和未结束的单元
                        .unitStatusList(UnitStatus.REFRESH_UNIT_STATUS_LIST)
                        .build())
                .fetch(LauUnitPo.class);

        LOGGER.info("RefreshUnitStatus need refresh count:{}.", pos.size());

        // 如果查询数据不足perSize则返回MAX_VALUE不再执行任务
        int maxUnitId;
        if (pos.size() < perSize) {
            LOGGER.info("RefreshUnitStatus get updateUnitDto list is less than perSize={}", perSize);
            maxUnitId = Integer.MAX_VALUE;
        } else {
            maxUnitId = pos.stream().mapToInt(LauUnitPo::getUnitId).max().orElse(Integer.MAX_VALUE);
        }

        //查GD+的投放时间
        List<Integer> unitIds = pos.stream().filter(o -> IsValid.TRUE.getCode().equals(o.getIsGdPlus()))
                .map(LauUnitPo::getUnitId).collect(Collectors.toList());
        Triple<List<Integer>, List<Integer>, List<Integer>> gdPlusTriple = Triple.from(Lists.newArrayList(), Lists.newArrayList(), Lists.newArrayList());
        List<Integer> finishLaunchUnitIds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(unitIds)) {
            //查出每个单元的排期
            Map<Integer, List<Timestamp>> unitSchedules = flyGdPlusDelegate.queryValidUnitsSchedules(unitIds);
            Triple<List<Integer>, List<Integer>, List<Integer>> unitIdTriple = flyGdPlusDelegate.get3UnitIds(unitSchedules);
            gdPlusTriple.setFirst(unitIdTriple.getFirst());
            gdPlusTriple.setMiddle(unitIdTriple.getMiddle());
            gdPlusTriple.setLast(unitIdTriple.getLast());

            finishLaunchUnitIds.addAll(gdPlusTriple.getLast());
        }

        // 根据排期提取完成的单元的 ids
        List<Integer> completedUnitIds = pos
                .stream()
                .filter(po -> {
                    if (IsValid.TRUE.getCode().equals(po.getIsGdPlus())) {
                        return finishLaunchUnitIds.contains(po.getUnitId());
                    } else {
                        return LaunchUtil.isUnitFinished(po.getLaunchEndDate(), po.getLaunchTime());
                    }
                })
                .map(LauUnitPo::getUnitId)
                .collect(Collectors.toList());

        LOGGER.info("RefreshUnitStatus completedUnitIds count:{}.", completedUnitIds.size());
        batchUpdateStatus(completedUnitIds, LaunchStatus.STOP.getCode(), UnitStatus.COMPLETED.getCode());

        __endTime = System.currentTimeMillis();
        LOGGER.info("RefreshUnitStatus update completed units successfully(used time: {}ms).", __endTime - __startTime);

        // 非暂停且未完成的单元
        pos = pos.stream()
                .filter(po -> po.getUnitStatus() != UnitStatus.PAUSED.getCode()
                        && !completedUnitIds.contains(po.getUnitId())
                        && po.getUnitStatus() != UnitStatus.APP_REFRESH.getCode())
                .collect(Collectors.toList());

        // 查询该区间投放时间大于今天的单元
        List<LauUnitPo> allCompletedPos = new ArrayList<>(pos);
        LOGGER.info("RefreshUnitStatus remain non-completedUnits.size:{}.", pos.size());
        int page = 1;
        while (true) {
            List<LauUnitPo> perCompletedPos = getCpcUnitQuery(QueryCpcUnitDto
                    .builder()
                    .gtUnitId(gtUnitId)
                    .eltUnitId(maxUnitId)
                    .page(Page.valueOf(page++, perSize))
                    // 已完成
                    .unitStatus(UnitStatus.COMPLETED.getCode())
                    // 单元结束时间 >= 今天
                    .beginDate(Utils.getTimestamp2String(Utils.getToday()))
                    .build())
                    .fetch(LauUnitPo.class);
            allCompletedPos.addAll(perCompletedPos);

            LOGGER.info("RefreshUnitStatus batchUpdateValidStatus list.size:{}.", allCompletedPos.size());

            try {
                batchUpdateValidStatus(allCompletedPos, gdPlusTriple);
            } catch (Exception e) {
                LOGGER.info("RefreshUnitStatus encounter an exception:{}.", Throwables.getStackTraceAsString(e));
            }

            if (perCompletedPos.size() < perSize) {
                LOGGER.info("RefreshUnitStatus batchUpdateValidStatus list is less than perSize={}", perSize);
                break;
            }
            allCompletedPos.clear();
        }

        __endTime = System.currentTimeMillis();
        LOGGER.info("RefreshUnitStatus finish(used time: {}ms)!", __endTime - __startTime);
        return maxUnitId;
    }

    public CpcUnitDto loadCpcUnit(int unitId) throws ServiceException {
        return this.loadCpcUnit(unitId, false, false, false, false, false);
    }

    public CpcUnitDto loadCpcUnit(int unitId, boolean needDynamicInfo, boolean needOgvInfo, boolean needGoodsInfo,
                                  boolean needReserveInfo, boolean isWithPriceCoefficient) throws ServiceException {
        //LauUnitPo po = lauUnitDao.selectByPrimaryKey(unitId);
        com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauUnitPo po = adCoreBqf.selectFrom(lauUnit).where(lauUnit.unitId.eq(unitId)).fetchFirst();
        // final AccountDto account = queryAccountService.getAccount(po.getAccountId());

        Assert.notNull(po, "单元不存在");
        Assert.isTrue(po.getIsDeleted() == IsDeleted.VALID.getCode(), "单元不存在");

        CpcUnitDto unit = cpcUnitPoToDto(po);

        // 这里的赋值应该是冗余了
        // 冗不动了
        if (!Utils.isPositive(unit.getTargetPackageId())) {
            unit.setTargetRules(unitTargetRuleService.getTargetRules(unitId));
            setCrowdPackIds(unit);
        }
        // 当天单元预算剩余修改次数

        // 装填定向信息
        loadUnitTarget(unit);

        long count = lauBudgetService.getBudgetRecordCountByUnitId(unitId);
        unit.setBudgetRemainingModifyTimes(LaunchConstant.UNIT_BUDGET_UPDATE_LIMIT - count);

        Cat.logEvent("other", "loadCpcUnit", Event.SUCCESS, "other info start");

        // 应用包
        if (unit.getPromotionPurposeType() != null && Utils.isPositive(unit.getAppPackageId())) {
            unit.setAppInfo(appPackageService.load(unit.getAppPackageId()));
            Cat.logEvent("other", "loadCpcUnit", Event.SUCCESS, "app");
        }

        boolean isSupportContentLabelAndVideoIdTarget = unitValidator.isSupportContentLabelAndVideoIdTarget(po.getSlotGroup());
        if (isSupportContentLabelAndVideoIdTarget) {
            unit.setTargetContentLabels(this.getContentLabelTargetsByUnitId(unitId));
            unit.setTargetVideoIds(this.getVideoIdTargetsByUnitId(unitId));
            Cat.logEvent("other", "loadCpcUnit", Event.SUCCESS, "target video ids");
        }

        // 会员购
        if (PromotionPurposeType.SHOP_GOODS.getCode() == unit.getPromotionPurposeType()) {
            LauUnitShopGoodsPo unitShopGoodsPo = this.getShopGoodsIdByUnitId(unitId);
            DpaShopGoodsDto shopGoodsDto = queryShopGoodsService.getDpaShopGoodsByGoodsId(unitShopGoodsPo.getGoodsId());
            unit.setShopGoodsDto(shopGoodsDto);
            Cat.logEvent("other", "loadCpcUnit", Event.SUCCESS, "会员购");
        }

        // android 游戏
        LauUnitGamePo unitGamePo = this.getGameBaseIdByUnitId(unitId);
        if (Objects.nonNull(unitGamePo)) {
            GameDto gameDto = gameCenterService.getGameDtoById(unitGamePo.getGameBaseId());
            unit.setGameDto(gameDto);
            unit.setGameBaseId(gameDto == null ? 0 : gameDto.getGameBaseId());
            unit.setGamePlatformType(unitGamePo.getPlatformType());
            unit.setSubPkg(unitGamePo.getSubPkg());
            Cat.logEvent("other", "loadCpcUnit", Event.SUCCESS, "game center");
        }

        // 稿件
        if (PromotionPurposeType.ARCHIVE_CONTENT.getCode() == unit.getPromotionPurposeType()) {
            LauUnitArchiveVideoPo archiveVideoPo = getArchiveVideoPoByUnitId(unitId);

            if (archiveVideoPo != null) {
                unit.setMid(archiveVideoPo.getMid().intValue());
                unit.setLongMid(archiveVideoPo.getMid());
                unit.setLaunchVideoType(archiveVideoPo.getLaunchVideoType());
                unit.setVideoId(archiveVideoPo.getVideoId());
                unit.setRealMid(archiveVideoPo.getRealMid());
                unit.setCid(archiveVideoPo.getCid());
            }
            if (unit.getVideoId() != null) {
                try {
                    unit.setArchiveDetail(archiveManager.queryArchivesByAid(unit.getVideoId()));
                    Cat.logEvent("other", "loadCpcUnit", Event.SUCCESS, "archive");
                } catch (Exception e) {
                    // do nothing, maybe print some log
                }
            }

        }
        if (PromotionPurposeType.LIVE_ROOM.getCode() == unit.getPromotionPurposeType()) {
            LauUnitArchiveVideoPo archiveVideoPo = getArchiveVideoPoByUnitId(unitId);
            if (archiveVideoPo != null) {
                unit.setMid(archiveVideoPo.getMid().intValue());
                unit.setLongMid(archiveVideoPo.getMid());
                unit.setLaunchVideoType(archiveVideoPo.getLaunchVideoType());
                unit.setVideoId(archiveVideoPo.getVideoId());
                unit.setRealMid(archiveVideoPo.getRealMid());
            }
        }

        // 直播推广-直播间
        if (PromotionPurposeType.LIVE_ROOM.getCode() == unit.getPromotionPurposeType() && unit.getSubjectId() != null) {
            unit.setLauSubject(lauSubjectService.getLauSubjectById(unit.getSubjectId(), LauSubjectType.LIVE_ROOM));
            Cat.logEvent("other", "loadCpcUnit", Event.SUCCESS, "live room");
        }

        // 动态
        if (needDynamicInfo && PromotionPurposeType.DYNAMIC.getCode() == unit.getPromotionPurposeType()) {
            LauUnitFlyMiddleInfoPo lauUnitFlyMiddleInfoPo = lauUnitFlyMiddleInfoRepo.fetchByUnitId(unitId);
            if (lauUnitFlyMiddleInfoPo != null) {
                DynamicBo dynamicBo = middleFlyCreativeService.queryDynamicBo(lauUnitFlyMiddleInfoPo.getDynamicId());
                if (dynamicBo != null) {
                    final JumpInfoBo jumpInfo = launchJumpUrlService.dynamicJumpInfo(dynamicBo.isNewVersion(), dynamicBo.getDynamicId());
                    dynamicBo.setJumpInfoBo(jumpInfo);
                    String jumlUrl = dynamicLinkProc.spliceDynamicSkipLinkByEnv(dynamicBo.getDynamicId());
                    dynamicBo.setOuterJumlUrl(jumlUrl);
                    unit.setDynamic(dynamicBo);
                }
                Cat.logEvent("other", "loadCpcUnit", Event.SUCCESS, "dynamic");
            }
        }

        // ogv
        if (needOgvInfo && Objects.equals(unit.getPromotionPurposeType(), PromotionPurposeType.OGV.getCode())) {
            LauUnitPgcArchivePo lauUnitPgcArchivePo = lauUnitPgcArchiveRepo.queryByUnitId(unitId);
            if (lauUnitPgcArchivePo != null) {
                Map<Long, PgcArchiveBo> pgcArchivesByAids = archiveService.getPgcArcsByAids(Collections.singletonList(lauUnitPgcArchivePo.getAid()));
                PgcArchiveBo pgcArchiveBo = pgcArchivesByAids.get(lauUnitPgcArchivePo.getAid());
                unit.setPgcArchiveBo(pgcArchiveBo);
                Cat.logEvent("other", "loadCpcUnit", Event.SUCCESS, "ogv");
            }
        }

        // 直播预约
        if (needReserveInfo) {
            LiveReservationInfoBo liveReservationInfoBo = adpCpcLiveReserveService.fetchUnitLiveReserveInfo(unitId);
            if (liveReservationInfoBo != null) {
                unit.setLiveReserve(liveReservationInfoBo);
                Cat.logEvent("other", "loadCpcUnit", Event.SUCCESS, "live reserve");
            }
        }
        // 直播带货商品
        if (needGoodsInfo) {
            LauUnitGoodsPo lauUnitGoodsPo = unitGoodsRepo.queryListByUnitId(unitId);
            if (lauUnitGoodsPo != null) {
                GoodsBo goodsBo = launchUnitGoodsService.getGoods(unit.getAccountId(), lauUnitGoodsPo.getItemId());
                unit.setGoods(goodsBo);
                Cat.logEvent("other", "loadCpcUnit", Event.SUCCESS, "goods");
            }
        }

        //GD+单元
        if (IsValid.TRUE.getCode().equals(unit.getIsGdPlus())) {
            Pair<List<SplitDaysImpressDto>, Integer> gdPair = flyGdPlusDelegate.queryValidScheduleDtosByUnitId(unit.getUnitId());
            unit.setSplitDaysImpressDtos(gdPair.getLeft());
            unit.setGdPlusUnitLaunchingStatus(gdPair.getRight());
        }

        unit.setIsLongTermLaunch(unit.getIsLongTermLaunch());
        // 游戏卡 || 游戏活动卡
        if (Objects.equals(unit.getPromotionPurposeType(), PromotionPurposeType.GAME_CARD.getCode())
                || Objects.equals(unit.getPromotionPurposeType(), PromotionPurposeType.GAME_ACTIVITY_CARD.getCode())) {
            GameCardUnitTargetDto gameCardUnitTargetDto = getGameCardUnitTargetDto(unit.getUnitId());
            unit.setGameCardUnitTargets(gameCardUnitTargetDto);
        }

        unit.setSdpaProducts(adProductRepo.getProductsByMappingIdsV2(unitId,
                AdProductBindTypeEnum.UNIT.getCode()));

        //智能拓词开关
        unit.setSmartKeyWord(getSmartKeySwitch(unitId));

        if (isWithPriceCoefficient) {
            LauUnitExtraPo extraPo = adCoreBqf.selectFrom(lauUnitExtra)
                    .where(lauUnitExtra.unitId.eq(unitId))
                    .fetchFirst();
            if (extraPo != null) {
                unit.setSearchFirstPriceCoefficient(extraPo.getSearchFirstPriceCoefficient());
            }
        }
        return unit;
    }

    private void loadUnitTarget(CpcUnitDto unit) throws ServiceException {
        int unitId = unit.getUnitId();

        // 单元绑定了定向包，加载定向包id对应的定向信息
        boolean isBindUpgradeTargetPackage = Utils.isPositive(unit.getTargetPackageId());
        if (isBindUpgradeTargetPackage) {
            loadTargetPackageUpgradeTarget(unit);
            return;
        }

        // 单元没绑定了定向包，加载单元定向规则表的信息
        unit.setTargetRules(unitTargetRuleService.getTargetRules(unitId));
        setCrowdPackIds(unit);
        unit.setExtraTarget(getExtraTarget(unitId));

        // todo 下线商业兴趣
        unit.setBusinessInterestIds(this.getBusinessInterestTargetsByUnitId(unitId));
        List<Integer> labelIdsByAccountId = accountLabelService.getLabelIdsByAccountId(unit.getAccountId());
//        if (!labelIdsByAccountId.contains(supportProfessionInterest)){
//            //不在行业兴趣人群白名单不展示professionInterest
//            unit.setProfessionInterestIds(Collections.emptyList());
//        }else {
//            unit.setProfessionInterestIds(professionInterestService.getProfessionInterestTargetsByUnitId(unitId));
//        }
        //全量
        unit.setProfessionInterestIds(professionInterestService.getProfessionInterestTargetsByUnitId(unitId));


        Integer isInterestAuto = Objects.isNull(professionInterestAutoService.selectInterestAuto(unitId)) ? IsInterestAuto.NO_AUTO : IsInterestAuto.IS_AUTO;
        unit.setProfessionInterestAuto(isInterestAuto);

        LauTagIdsDto userTagDto = lauTagIdsService.getByLauId(unitId, LauTargetTagEnum.UNIT.getCode(), LauTagType.USER.getCode());
        unit.setIsFuzzyTags(userTagDto == null ? 0 : userTagDto.getTagTargetType());
        unit.setTags(userTagDto == null ? Collections.emptyList() : userTagDto.getTags());

        LauTagIdsDto videoTagDto = lauTagIdsService.getByLauId(unitId, LauTargetTagEnum.UNIT.getCode(), LauTagType.VIDEO.getCode());
        unit.setVideoTag(videoTagDto == null ? UnitTargetTagDto.getEmpty() : UnitTargetTagDto
                .builder()
                .tags(videoTagDto.getTags())
                .isFuzzyTags(videoTagDto.getTagTargetType())
                .type(LauTagType.getByCode(videoTagDto.getTagType()))
                .build());
        MiniGameTargetDto miniGameTargetDto = getUnitMiniGameTarget(unitId);
        unit.setIncludeMiniGameIds(miniGameTargetDto == null ? Collections.emptyList() : miniGameTargetDto.getIncludeGameIds());
        unit.setExcludeMiniGameIds(miniGameTargetDto == null ? Collections.emptyList() : miniGameTargetDto.getExcludeGameIds());

        unit.setInstalledUserFilter(getUnitTargetInstalledUserFilterTargetByUnitId(unitId));

        // 操作系统版本和bili客户端版本
        TargetUpgradeOsVersionDto osVersionDto = lauUnitTargetOsVersionUpgradeProc.queryOsVersion(unitId);
        TargetUpgradeBiliClientVersionDto biliClientVersionDto = lauUnitTargetBiliClientUpgradeProc.queryBiliClient(unitId);
        unit.setOsVersion(osVersionDto);
        unit.setBiliClientVersion(biliClientVersionDto);
    }

    private void loadTargetPackageUpgradeTarget(CpcUnitDto unit) {
        Integer targetPackageId = unit.getTargetPackageId();
        Assert.isTrue(Utils.isPositive(targetPackageId), "定向包不存在");

        TargetPackageUpgradeDto targetPackageUpgradeDto = resTargetPackageUpgradeService.load(targetPackageId, false);
        Assert.isTrue(Objects.nonNull(targetPackageUpgradeDto), "定向包不存在");

        unit.setTargetPackageName(targetPackageUpgradeDto.getName());

        unit.setTargetRules(targetPackageUpgradeDto.getTargetRules());
        unit.setCrowdPackIds(targetPackageUpgradeDto.getCrowdPackIds());
        unit.setExcludeCrowdPackIds(targetPackageUpgradeDto.getExcludeCrowdPackIds());
        unit.setOtherCrowdPackIdsGroup(targetPackageUpgradeDto.getOtherCrowdPackIdsGroup());
        unit.setExtraCrowdPackIds(targetPackageUpgradeDto.getExtraCrowdPackIds());

        TargetPackageExtraTargetDto extraTarget = targetPackageUpgradeDto.getExtraTarget();
        CpcUnitExtraTargetDto extraTargetResult = CpcUnitExtraTargetDto.builder()
                .browse(extraTarget.getBrowse())
                .fansRelation(extraTarget.getFansRelation())
                .includeTheirsFans(extraTarget.getIncludeTheirsFans())
                .excludeTheirsFans(extraTarget.getExcludeTheirsFans())
                .interaction(extraTarget.getInteraction())
                .videoSecondPartition(extraTarget.getVideoSecondPartition())
                .recommendType(extraTarget.getRecommendType())
                .build();
        unit.setExtraTarget(extraTargetResult);

        unit.setBusinessInterestIds(targetPackageUpgradeDto.getBusinessInterestIds());

        unit.setIsFuzzyTags(targetPackageUpgradeDto.getIsFuzzyTags());
        unit.setTags(targetPackageUpgradeDto.getTags());
        unit.setVideoTag(UnitTargetTagDto.builder()
                .tags(targetPackageUpgradeDto.getVideoTag().getTags())
                .isFuzzyTags(targetPackageUpgradeDto.getVideoTag().getIsFuzzyTags())
                .type(targetPackageUpgradeDto.getVideoTag().getType())
                .build());

        List<String> includeMiniGameIds = targetPackageUpgradeDto.getIncludeMiniGameIds(),
                excludeMinigameIds = targetPackageUpgradeDto.getExcludeMiniGameIds();
        unit.setIncludeMiniGameIds(includeMiniGameIds);
        unit.setExcludeMiniGameIds(excludeMinigameIds);

        unit.setProfessionInterestAuto(targetPackageUpgradeDto.getProfessionInterestAuto());
        unit.setProfessionInterestIds(targetPackageUpgradeDto.getProfessionInterest());

        TargetPackageInstalledUserFilterDto targetPackageInstalledUserFilterDto = targetPackageUpgradeDto.getTargetPackageInstalledUserFilterDto();
        UnitTargetInstalledUserFilterDto installedUserFilterDto = new UnitTargetInstalledUserFilterDto();
        installedUserFilterDto.setFilterType(targetPackageInstalledUserFilterDto.getFilterType());
        installedUserFilterDto.setTargetContent(targetPackageInstalledUserFilterDto.getTargetContent());
        unit.setInstalledUserFilter(installedUserFilterDto);
        // 操作系统版本和bili客户端版本
        unit.setOsVersion(targetPackageUpgradeDto.getOsVersion());
        unit.setBiliClientVersion(targetPackageUpgradeDto.getBiliClientVersion());
    }

    @SneakyThrows
    public List<Integer> getSingleUnitOsTarget(Integer unitId, Integer targetPackageId) {

        if (!Utils.isPositive(unitId)) {
            return Collections.emptyList();
        }
        List<TargetRule> targetRules;
        if (Utils.isPositive(targetPackageId)) {
            targetRules = resTargetPackageRuleUpgradeService.getTargetByResTargetPackageId(targetPackageId);
        } else {
            targetRules = unitTargetRuleService.getTargetRules(unitId);
        }

        return targetRules.stream()
                .filter(targetRule -> TargetType.OS.getCode() == targetRule.getRuleType())
                .map(TargetRule::getValueIds)
                .filter(idList -> !CollectionUtils.isEmpty(idList))
                .flatMap(Collection::stream)
                .collect(Collectors.toList());

    }

    private GameCardUnitTargetDto getGameCardUnitTargetDto(@org.springframework.lang.NonNull Integer unitId) {
        List<Long> avids = adCoreBqf.select(lauUnitTargetArchive.avid)
                .from(lauUnitTargetArchive).where(lauUnitTargetArchive.unitId.eq(unitId))
                .where(lauUnitTargetArchive.isDeleted.eq(IsDeleted.VALID.getCode())).fetch();
        List<UnitTargetRuleArchiveDto> archives = avids.stream()
                .map(
                        avid -> UnitTargetRuleArchiveDto.builder()
                                .avid(avid)
                                .bvid(BVIDUtils.avToBv(avid))
                                .build()).collect(Collectors.toList());
        List<Long> tagIds = adCoreBqf.select(lauUnitTargetTag.tagId)
                .from(lauUnitTargetTag)
                .where(lauUnitTargetTag.unitId.eq(unitId))
                .where(lauUnitTargetTag.isDeleted.eq(IsDeleted.VALID.getCode())).fetch();
        List<UnitTargetRuleTagDto> tags = CollectionUtils.isEmpty(tagIds) ? Collections.emptyList() :
                mainCommunityTagProxy.getTagsMap(tagIds).values().stream()
                        .map(
                                tag -> UnitTargetRuleTagDto.builder()
                                        .tagId(tag.getId())
                                        .tagName(tag.getName())
                                        .build()
                        ).collect(Collectors.toList());

        int targetType;
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(tagIds)) {
            targetType = GameCardUnitTargetTypeEnum.TAG.getCode();
        } else if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(avids)) {
            targetType = GameCardUnitTargetTypeEnum.VIDEO.getCode();
        } else {
            targetType = GameCardUnitTargetTypeEnum.CATEGORY.getCode();
        }
        return GameCardUnitTargetDto.builder()
                .unitId(unitId)
                .targetType(targetType)
                .archive(archives)
                .tag(tags)
                .build();
    }

    public GameDto getGameDtoByUnitId(Integer unitId) {
        Assert.notNull(unitId, "单元id不能为空");

        LauUnitGamePo unitGamePo = this.getGameBaseIdByUnitId(unitId);
        return gameCenterService.getGameDtoById(unitGamePo.getGameBaseId());
    }

    public Integer getGameIdByUnitId(Integer unitId) {
        Assert.notNull(unitId, "单元id不能为空");

        LauUnitGamePo unitGamePo = this.getGameBaseIdByUnitId(unitId);
        return unitGamePo.getGameBaseId();
    }

    /**
     * 预估
     *
     * @param forecastDto
     * @return
     * @throws ServiceException
     */
    public MiddleCpcUnitForecastDto forecast(UnitForecastDto forecastDto) throws ServiceException {
        LOGGER.info("CpcUnitServiceDelegate.forecast:{}", forecastDto);

        // 重新映射视频分区人群预估
        if (Objects.nonNull(forecastDto) && Objects.nonNull(forecastDto.getTarget()) && !CollectionUtils.isEmpty(forecastDto.getTarget().getCategorys())) {
            final Map<Integer, Integer> map = resTargetItemService.getItemByTargetType(TargetType.CATEGORY.getCode())
                    .stream()
                    .collect(Collectors.toMap(ResTargetItemDto::getId, x -> Integer.parseInt(x.getMappingContent())));

            final List<Integer> mappingValueList = forecastDto.getTarget().getCategorys()
                    .stream()
                    .map(map::get)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            forecastDto.getTarget().setCategorys(mappingValueList);
        }

        Assert.notNull(forecastDto, "预估参数不能为空");
        UnitTargetDto target = forecastDto.getTarget();
        Assert.notNull(target, "定向信息不能为空");
        MiddleCpcUnitForecastDto result = MiddleCpcUnitForecastDto.getEmpty();
        Integer slotGroupId = target.getSlotGroupId();

        List<Integer> slotGroupIdList;
        if (slotGroupId == null) {
            // 版位收敛, adp单元和广告位组没有绑定
            // 找到账号可用的所有广告组id, 然后找对应的广告位id
            //必选版位收敛
            slotGroupIdList = resSlotGroupService.getValidGroupInSysTypes(Utils.isPositive(forecastDto.getSalesType())
                            ? Collections.singletonList(forecastDto.getSalesType())
                            : Arrays.asList(SalesType.CPC.getCode(), SalesType.CPM.getCode()),
                    forecastDto.getOperator().getOperatorId());
        } else {
            // 旧版
            slotGroupIdList = Lists.newArrayList(slotGroupId);
        }

        try {
            // 曝光人群预估
            long startQueryTime = System.currentTimeMillis();
            CountCoverPeopleReply crowdCount = this.getMaxCrowdCount(forecastDto, slotGroupIdList, forecastDto.getOperator());
            Long maxCrowdCount = crowdCount.getTotal();
            LOGGER.info("CpcUnitServiceDelegate.forecast.maxCrowdCount: [{}], cost: [{}] ms", maxCrowdCount, System.currentTimeMillis() - startQueryTime);
            result.setMax_crowd_count(maxCrowdCount);
            result.setMax_impression_count(Values.zeroIfNull(result.getMax_crowd_count()) *
                    (forecastDto.getFrequencyLimit() == null ? 3 : forecastDto.getFrequencyLimit()));
            result.setType(crowdCount.getEstimateStatus());
        } catch (Exception e) {
            result.setMax_impression_count(0L);
            result.setMax_crowd_count(0L);
            LOGGER.error("人群UV覆盖预估失败", e);
        }

        LOGGER.info("CpcUnitServiceDelegate.forecast result:{}", result);
        return result;
    }

    private BigDecimal getCpcBid(BigDecimal ecpm, BigDecimal averageCTR) {
        return this.divideFailToZero(ecpm, averageCTR.multiply(BigDecimal.valueOf(1000)), 2);
    }

    private BigDecimal divideFailToZero(BigDecimal dividend, BigDecimal divisor, int scale) {
        return dividend != null && divisor != null && divisor.compareTo(BigDecimal.ZERO) != 0 ? dividend.divide(divisor, scale, 4) : BigDecimal.ZERO;
    }

    private CountCoverPeopleReply getMaxCrowdCount(UnitForecastDto forecastDto, @NonNull List<Integer> slotGroupIdList, Operator operator) throws Exception {
        UnitTargetDto target = forecastDto.getTarget();

        // 获取投放时间段的小时最多的一天
        List<Integer> hours = CollectionUtils.isEmpty(forecastDto.getLaunch_time()) ? Collections.emptyList() :
                forecastDto.getLaunch_time().stream().max(Comparator.comparingInt(CollectionHelper::getSize))
                        .orElse(Collections.emptyList()).stream().map(Integer::valueOf).collect(Collectors.toList());

        // 定向项明细，注意自定义年龄没有明细
        List<TargetRule> targetRules = target.getTargetRules();

        // 过滤出不含有自定义年龄定向
        List<TargetRule> notCustomizeTargetRules = targetRules.stream().filter(t -> TargetType.AGE_CUSTOMIZE.getCode() != t.getRuleType()).collect(Collectors.toList());

        List<Integer> itemIds = CollectionUtils.isEmpty(notCustomizeTargetRules) ? Collections.emptyList() :
                notCustomizeTargetRules.stream().map(TargetRule::getValueIds)
                        .filter(ids -> !CollectionUtils.isEmpty(ids)).flatMap(Collection::stream).collect(Collectors.toList());
        List<ResTargetItemDto> targetItemsDtos = CollectionUtils.isEmpty(itemIds) ? Collections.emptyList() :
                resTargetItemService.getTargetItemsInIds(itemIds);

        // 包含人群包
        List<Integer> includeGroups = new ArrayList<>();
        Optional.ofNullable(target.getCrowdPackIds())
                .ifPresent(includeGroups::addAll);
        if (!CollectionUtils.isEmpty(target.getOtherCrowdPackIdsGroup())) {
            target.getOtherCrowdPackIdsGroup().forEach(others -> {
                if (!CollectionUtils.isEmpty(others)) {
                    includeGroups.addAll(others);
                }
            });
        }
        List<Integer> excludeGroups = new ArrayList<>();
        Optional.ofNullable(target.getExcludeCrowdPackIds())
                .ifPresent(excludeGroups::addAll);


        // 游戏卡独有定向
        GameCardUnitTargetDto gameCardUnitTargets = forecastDto.getGameCardUnitTargets();
        //标签定向
        List<String> gameCardVideoTags = Optional.ofNullable(gameCardUnitTargets)
                .map(GameCardUnitTargetDto::getTag)
                .map(Collection::stream)
                .map(stream -> stream.map(UnitTargetRuleTagDto::getTagName).collect(Collectors.toList()))
                .orElse(Collections.emptyList());
        //视频定向
        List<String> bvIds = Optional.ofNullable(gameCardUnitTargets)
                .map(GameCardUnitTargetDto::getArchive)
                .map(Collection::stream)
                .map(stream -> stream.map(UnitTargetRuleArchiveDto::getBvid).collect(Collectors.toList()))
                .orElse(Collections.emptyList());

        // 此处调用dmp 用的不是 id，而是 mapping content
        List<Integer> osIds = extractTargets(targetItemsDtos, notCustomizeTargetRules, TargetType.OS, dto -> Integer.valueOf(dto.getMappingContent()), dto -> true);
        // 构建操作系统参数
        Map<Integer, CountCoverPeopleOsVer> osVer = buildOsVersionsV2(target, osIds);

        // 构建bili客户端参数
        Map<Integer, CountCoverPeopleAppVer> appVer = buildBiliClientVersionsV2(target, osIds);

        //areaType
        TargetRule areaTypeTargetRule = targetRules.stream().filter(r -> r.getRuleType() == TargetType.AREA_TYPE.getCode()).findFirst().orElse(null);
        Integer areaType = Objects.nonNull(areaTypeTargetRule) && !CollectionUtils.isEmpty(areaTypeTargetRule.getValueIds())
                ? areaTypeTargetRule.getValueIds().get(0)
                : 0;
        List<String> normalVideoTags = Optional.ofNullable(target.getVideoTag())
                .map(UnitTargetTagDto::getTags)
                .orElse(Collections.emptyList());
        List<String> videoTags = Stream.of(gameCardVideoTags, normalVideoTags)
                .flatMap(List::stream)
                .collect(Collectors.toList());

        //app组合，包含app和排除app
        List<Integer> appListReq = new ArrayList<>();
        List<Integer> appList = extractTargets(targetItemsDtos, notCustomizeTargetRules, TargetType.APP_CATEGORY, dto -> Integer.valueOf(dto.getMappingContent()), dto -> true);
        Optional.ofNullable(appList).ifPresent(appListReq::addAll);
        List<Integer> excludeAppList = new ArrayList<>();

        //已安装用户
        if (Objects.nonNull(target.getInstalledUserFilter()) && !CollectionUtils.isEmpty(target.getInstalledUserFilter().getTargetContent())) {
            InstalledUserFilterEnum userFilterEnum = InstalledUserFilterEnum.getByCode(target.getInstalledUserFilter().getFilterType());
            List<Integer> targetContent = target.getInstalledUserFilter().getTargetContent();
            switch (userFilterEnum) {
                case TARGET_INSTALLED:
                    appListReq.addAll(targetContent);
                    break;
                case FILTER:
                    excludeAppList.addAll(targetContent);
                    break;
                case TARGET:
                    if (CollectionUtils.isEmpty(targetContent)) {
                        break;
                    }
                    List<InstalledUserTargetContentEnum> contentEnum = targetContent.stream().map(InstalledUserTargetContentEnum::getByCode).collect(Collectors.toList());
                    List<Integer> appIds = contentEnum.stream().map(InstalledUserTargetContentEnum::getAppId).collect(Collectors.toList());
                    Optional.ofNullable(appIds).ifPresent(appListReq::addAll);
                    break;
            }

        }

        //用户转化过滤处理
        Map<Integer, Integer> convertFilter = conversionFilter(targetRules, forecastDto);

        //定向-城市，地域定向发展划分
        List<String> citiesRequest = new ArrayList<>();
        List<String> cities = extractTargets(targetItemsDtos, notCustomizeTargetRules, TargetType.AREA, ResTargetItemDto::getName, dto -> AreaSubType.CITY.getCode().equals(dto.getSubType()));
        List<String> citiesLevel = extractTargets(targetItemsDtos, notCustomizeTargetRules, TargetType.AREA_LEVEL, ResTargetItemDto::getName, dto -> AreaSubType.CITY.getCode().equals(dto.getSubType()));
        Optional.ofNullable(cities).ifPresent(citiesRequest::addAll);
        Optional.ofNullable(citiesLevel).ifPresent(citiesRequest::addAll);

        //定向-省份，地域定向发展划分
        List<String> provinceRequest = new ArrayList<>();
        List<String> areaProvince = extractTargets(targetItemsDtos, notCustomizeTargetRules, TargetType.AREA, ResTargetItemDto::getName, dto -> AreaSubType.PROVINCE.getCode().equals(dto.getSubType()));
        List<String> areaLevelProvince = extractTargets(targetItemsDtos, notCustomizeTargetRules, TargetType.AREA_LEVEL, ResTargetItemDto::getName, dto -> AreaSubType.PROVINCE.getCode().equals(dto.getSubType()));
        Optional.ofNullable(areaProvince).ifPresent(provinceRequest::addAll);
        Optional.ofNullable(areaLevelProvince).ifPresent(provinceRequest::addAll);

        //视频分区，视频分区兴趣
        List<Integer> videoPartition = forecastDto.getTarget().getVideoPartition();
        List<ResTargetItemDto> targetItemsInIds = resTargetItemService.getTargetItemsInIds(videoPartition);
        List<Integer> videoParList = targetItemsInIds.stream().map(r -> Integer.parseInt(r.getMappingContent())).collect(Collectors.toList());
        List<Integer> videoSecondPartition = forecastDto.getTarget().getVideoSecondPartition();
        List<Integer> videoForecastRequest = new ArrayList<>();
        Optional.of(videoParList).ifPresent(videoForecastRequest::addAll);
        Optional.of(videoSecondPartition).ifPresent(videoForecastRequest::addAll);

        CountCoverPeopleReq.Builder builder = CountCoverPeopleReq.newBuilder()
                .addAllHours(hours)
                .addAllVideoTidInterests(videoForecastRequest.stream().filter(Objects::nonNull).map(Object::toString).collect(Collectors.toList()))
                .addAllIncludeGroups(includeGroups)
                .addAllExcludeGroups(excludeGroups)
                .addAllSlotGroupIds(slotGroupIdList)
                .addAllAges(extractTargets(targetItemsDtos, notCustomizeTargetRules, TargetType.AGE, dto -> Integer.valueOf(dto.getMappingContent()), dto -> true))
                .addAllApps(appListReq)
                .addAllGenders(extractTargets(targetItemsDtos, notCustomizeTargetRules, TargetType.GENDER, dto -> Integer.valueOf(dto.getMappingContent()), dto -> true))
                .addAllOs(osIds)
                .addAllNetworks(extractTargets(targetItemsDtos, notCustomizeTargetRules, TargetType.NETWORK, dto -> Integer.valueOf(dto.getMappingContent()), dto -> true))
                .addAllMobileBrands(extractTargets(targetItemsDtos, notCustomizeTargetRules, TargetType.DEVICE_BRAND, ResTargetItemDto::getMappingContent, dto -> true))
                .addAllCities(citiesRequest)
                .addAllProvince(provinceRequest)
                .setAreaType(areaType)
                .addAllPhonePrice(extractTargets(targetItemsDtos, notCustomizeTargetRules, TargetType.PHONE_PRICE, dto -> Integer.valueOf(dto.getMappingContent()), dto -> true))
                .putAllOsVer(osVer)
                .putAllAppVer(appVer)
                .addAllBvIds(bvIds)
                .addAllExcludeInstallApps(excludeAppList);
        //判断convertFilter是否为空map，不为空再赋值
        if (!convertFilter.isEmpty()) {
            convertFilter.forEach(builder::putConversionFilter);
        }

        Optional.ofNullable(target.getProfessionInterestIds()).ifPresent(builder::addAllBussInterests);
        Optional.ofNullable(target.getIncludeTheirsFans()).ifPresent(builder::addAllIncludeFollowUps);
        Optional.ofNullable(target.getExcludeTheirsFans()).ifPresent(builder::addAllExcludeFollowUps);
        Optional.ofNullable(target.getCategorys()).ifPresent(x -> builder.addAllVideoTids(x.stream().filter(Objects::nonNull).map(Object::toString).collect(Collectors.toList())));

        if (recommendTypeList.contains(target.getRecommendType())) {
            Pair<Long, List<Long>> pair = cpcCampaignService.getTmpUpInfoByType(PromotionPurposeType.ARCHIVE_CONTENT, operator, target.getRecommendType());
            Assert.notNull(pair, "dmp接口返回的up主id数量为0，不合法");
            Assert.notEmpty(pair.getRight(), "dmp接口返回的up主id数量为0，不合法");
            Assert.isTrue(pair.getRight().size() <= 50, "dmp接口返回的up主id数量超过50，不合法");
            builder.addAllIncludeFollowUps(pair.getRight());
            builder.addAllExcludeFollowUps(Collections.singletonList(pair.getLeft()));
        } else {
            Assert.isTrue(target.getRecommendType() == null || target.getRecommendType() == 0,
                    "recommend_type输入错误");
        }

        CountCoverPeopleReq request = builder.build();
        LOGGER.info("CpcUnitServiceDelegate.getMaxCrowdCount.request: [{}]", request);
        if (operator.getOperatorId().equals(3)) {
            LOGGER.info("hystrixDmpService.getMaxCrowdCountFromDmp req={}", request);
        }
        //long ans = hystrixDmpService.getMaxCrowdCountFromDmp(request);

        CountCoverPeopleReply countCoverPeopleReply = crowdPackService.maxCrowdCount(request);
        Long ans = countCoverPeopleReply.getTotal();
        TargetEstimateStatusEnum estimateStatus = countCoverPeopleReply.getEstimateStatus();
        if (operator.getOperatorId().equals(3)) {
            LOGGER.info("hystrixDmpService.getMaxCrowdCountFromDmp ans={}", ans);
        }
        return CountCoverPeopleReply.newBuilder().setTotal(ans).setEstimateStatus(estimateStatus).build();
    }

    private Map<Integer, Integer> conversionFilter(List<TargetRule> targetRules, UnitForecastDto forecastDto) {
        try {

            Map<Integer, Integer> convertMap = new HashMap<>();
            //账户id
            Integer operatorId = forecastDto.getOperator().getOperatorId();
            AccountDto account = queryAccountService.getAccount(operatorId);

            //公司组id
            Integer companyGroupId = account.getCompanyGroupId();
            //品牌id
            Integer productId = account.getProductId();

            List<TargetRule> convertUserFilter = targetRules.stream().filter(r -> r.getRuleType() == TargetType.CONVERTED_USER_FILTER.getCode()).collect(Collectors.toList());
            Integer convertId = !CollectionUtils.isEmpty(convertUserFilter) && !CollectionUtils.isEmpty(convertUserFilter.get(0).getValueIds())
                    ? convertUserFilter.get(0).getValueIds().get(0)
                    : 0;
            if (!Utils.isPositive(convertId)) {
                return convertMap;
            }

            Optional<ForecastConvertUserFilterEnum> userFilterEnumOptional = ForecastConvertUserFilterEnum.getByCodeOptional(convertId);
            if (userFilterEnumOptional.isPresent()) {
                ForecastConvertUserFilterEnum forecastConvertUserFilterEnum = userFilterEnumOptional.get();
                switch (forecastConvertUserFilterEnum) {
                    case BRAND:
                        convertMap.put(forecastConvertUserFilterEnum.getType(), productId);
                        break;
                    case COMPANY:
                        convertMap.put(forecastConvertUserFilterEnum.getType(), companyGroupId);
                        break;
                    case ACCOUNT:
                        convertMap.put(forecastConvertUserFilterEnum.getType(), operatorId);
                        break;
                    case CAMP:
                        convertMap.put(forecastConvertUserFilterEnum.getType(), forecastDto.getCampaignId());
                        break;
                    case UNIT:
                        //判断forecastDto.getUnitId()是否为空，不为空再put
                        Optional.ofNullable(forecastDto.getUnitId()).ifPresent(x -> convertMap.put(forecastConvertUserFilterEnum.getType(), x));
                        break;
                }
            }
            return convertMap;
        } catch (Exception e) {
            log.error("forecast conversionFilter err ", e);
            throw new ServiceRuntimeException(LaunchExceptionCode.ILLEGAL_PARAM.getMessage());
        }
    }

    public static Map<Integer, List<TargetTagsAppBuildExpression>> buildBiliClientVersions(UnitTargetDto target, List<Integer> osIds) {
        Map<Integer, List<TargetTagsAppBuildExpression>> appVer = new HashMap<>();
        TargetUpgradeBiliClientVersionDto biliClientVersionDto = target.getBiliClientVersion();
        if (biliClientVersionDto == null) {
            return appVer;
        }

        appVer.put(1, convertBiliClientVersion(osIds, Device.ANDROID.getCode() + 1, biliClientVersionDto.getAndroidBiliClientVersion()));
        appVer.put(2, convertBiliClientVersion(osIds, Device.IPHONE.getCode() + 1, biliClientVersionDto.getIphoneBiliClientVersion()));
        appVer.put(3, convertBiliClientVersion(osIds, Device.IPAD.getCode() + 1, biliClientVersionDto.getIpadBiliClientVersion()));
        return appVer;
    }

    public static Map<Integer, CountCoverPeopleAppVer> buildBiliClientVersionsV2(UnitTargetDto target, List<Integer> osIds) {
        Map<Integer, CountCoverPeopleAppVer> appVer = new HashMap<>();
        TargetUpgradeBiliClientVersionDto biliClientVersionDto = target.getBiliClientVersion();
        if (biliClientVersionDto == null) {
            return appVer;
        }

        appVer.put(1, convertBiliClientVersionV2(osIds, Device.ANDROID.getCode() + 1, biliClientVersionDto.getAndroidBiliClientVersion()));
        appVer.put(2, convertBiliClientVersionV2(osIds, Device.IPHONE.getCode() + 1, biliClientVersionDto.getIphoneBiliClientVersion()));
        appVer.put(3, convertBiliClientVersionV2(osIds, Device.IPAD.getCode() + 1, biliClientVersionDto.getIpadBiliClientVersion()));
        return appVer;
    }

    public static List<TargetTagsAppBuildExpression> convertBiliClientVersion(List<Integer> osIds, Integer deviceTargetId, SingleBiliClientVersionDto androidBiliClientVersion) {
        List<TargetTagsAppBuildExpression> biliClientVersions = new ArrayList<>();
        TargetTagsAppBuildExpression appBuildExpression = new TargetTagsAppBuildExpression();

        if (androidBiliClientVersion == null || androidBiliClientVersion.getRelation() == null) {
            return biliClientVersions;
        }

        // 填写了，但是不包含
        if (TargetUtils.isOsVersionFilled(osIds) && !osIds.contains(deviceTargetId)) {

        } else {
            // com.bilibili.adp.cpc.enums.TargetRelationEnum dmp 定义一致
            appBuildExpression.setExp(androidBiliClientVersion.getRelation());
            appBuildExpression.setMinBuild(androidBiliClientVersion.getSmallerVersion());
            appBuildExpression.setMaxBuild(androidBiliClientVersion.getLargerVersion());
        }
        biliClientVersions.add(appBuildExpression);
        return biliClientVersions;
    }

    public static CountCoverPeopleAppVer convertBiliClientVersionV2(List<Integer> osIds, Integer deviceTargetId, SingleBiliClientVersionDto androidBiliClientVersion) {
        CountCoverPeopleAppVer.Builder biliClientVersionsBuilder = CountCoverPeopleAppVer.newBuilder();
        CountCoverPeopleAppBuildExpression.Builder appExpBuilder = CountCoverPeopleAppBuildExpression.newBuilder();

        if (androidBiliClientVersion == null || androidBiliClientVersion.getRelation() == null) {
            return biliClientVersionsBuilder.build();
        }

        // 填写了，但是不包含
        if (TargetUtils.isOsVersionFilled(osIds) && !osIds.contains(deviceTargetId)) {

        } else {
            // com.bilibili.adp.cpc.enums.TargetRelationEnum dmp 定义一致
            appExpBuilder.setExp(androidBiliClientVersion.getRelation());
            appExpBuilder.setMinBuild(Utils.isPositive(androidBiliClientVersion.getSmallerVersion()) ? androidBiliClientVersion.getSmallerVersion() : 0);
            appExpBuilder.setMaxBuild(Utils.isPositive(androidBiliClientVersion.getLargerVersion()) ? androidBiliClientVersion.getLargerVersion() : Integer.MAX_VALUE);
        }
        biliClientVersionsBuilder.addItems(appExpBuilder.build());
        return biliClientVersionsBuilder.build();
    }

    public static Map<Integer, List<Integer>> buildOsVersions(UnitTargetDto target, List<Integer> osIds) {
        TargetUpgradeOsVersionDto osVersion = target.getOsVersion();
        Map<Integer, List<Integer>> osVer = new HashMap<>();
        if (osVersion == null) {
            return osVer;
        }
        // dmp用的res_tart_item的mapping_content，不是Device code，相差1
        osVer.put(1, getOsVersions(osIds, osVersion.getAndroidOsVersions(), Device.ANDROID.getCode() + 1));
        osVer.put(2, getOsVersions(osIds, osVersion.getIphoneOsVersions(), Device.IPHONE.getCode() + 1));
        osVer.put(3, getOsVersions(osIds, osVersion.getIpadOsVersions(), Device.IPAD.getCode() + 1));
        return osVer;
    }

    public static Map<Integer, CountCoverPeopleOsVer> buildOsVersionsV2(UnitTargetDto target, List<Integer> osIds) {
        TargetUpgradeOsVersionDto osVersion = target.getOsVersion();
        Map<Integer, CountCoverPeopleOsVer> osVer = new HashMap<>();
        if (osVersion == null) {
            return osVer;
        }
        // dmp用的res_tart_item的mapping_content，不是Device code，相差1
        osVer.put(1, getOsVersionsV2(osIds, osVersion.getAndroidOsVersions(), Device.ANDROID.getCode() + 1));
        osVer.put(2, getOsVersionsV2(osIds, osVersion.getIphoneOsVersions(), Device.IPHONE.getCode() + 1));
        osVer.put(3, getOsVersionsV2(osIds, osVersion.getIpadOsVersions(), Device.IPAD.getCode() + 1));
        return osVer;
    }

    public static List<Integer> getOsVersions(List<Integer> osIds, List<Integer> pointedOsVersions,
                                              Integer deviceTargetId) {

        List<Integer> osVersions = new ArrayList<>();
        if (CollectionUtils.isEmpty(pointedOsVersions)) {
            return osVersions;
        }

        // 填写了，但是不包含
        if (TargetUtils.isOsVersionFilled(osIds) && !osIds.contains(deviceTargetId)) {

        } else {
            osVersions = pointedOsVersions;
            // 查询 os 的版本枚举
        }
        return osVersions;
    }

    public static CountCoverPeopleOsVer getOsVersionsV2(List<Integer> osIds, List<Integer> pointedOsVersions,
                                                        Integer deviceTargetId) {

        CountCoverPeopleOsVer.Builder builder = CountCoverPeopleOsVer.newBuilder();
        if (CollectionUtils.isEmpty(pointedOsVersions)) {
            return builder.build();
        }

        // 填写了，但是不包含
        if (TargetUtils.isOsVersionFilled(osIds) && !osIds.contains(deviceTargetId)) {

        } else {
            builder.addAllVer(pointedOsVersions);
            // 查询 os 的版本枚举
        }
        return builder.build();
    }

    /**
     * 抽取定向列表
     *
     * @param targetItemsDtos
     * @param targetRules
     * @param type
     * @param mapping
     * @param subFilter
     * @param <R>
     * @return
     */
    private <R> List<R> extractTargets(List<ResTargetItemDto> targetItemsDtos, List<TargetRule> targetRules, TargetType type, Function<ResTargetItemDto, R> mapping, Predicate<ResTargetItemDto> subFilter) {
        if (CollectionUtils.isEmpty(targetItemsDtos) || CollectionUtils.isEmpty(targetRules)) {
            return Collections.emptyList();
        }
        //  从 targetItemsDtos 过滤出满足某种条件的定向列表
        return targetItemsDtos.stream().filter(dto -> targetRules.stream()
                        .filter(rules -> type.getCode() == rules.getRuleType() && !CollectionUtils.isEmpty(rules.getValueIds()))
                        .map(TargetRule::getValueIds)
                        .findFirst().orElse(Collections.emptyList())
                        .contains(dto.getId()))
                .filter(subFilter)
                .map(mapping)
                .collect(Collectors.toList());
    }

    public Integer getLowestCost(GetBidCostParam param) {
        return lauBidConfigService.getMinBidCost(param).multiply(HUNDRED).intValue();
    }

    public Integer getFlyLowestCost(GetBidCostParam param, Integer ocpcTarget) {
        //后面加了资源位，起飞底价不再从配置文件中获取
        final int st = param.getSalesType();
        //起飞OCPM，在创意层没有底价校验，底价写为0
        if (ocpcTarget != null && ocpcTarget > 0) {
            return 0;
        }
        //起飞cpm底价
        if (ObjectUtils.nullSafeEquals(SalesType.CPM.getCode(), st)) {
            Integer slotGroupId = param.getSlotGroupId();
            //稿件
            if (ObjectUtils.nullSafeEquals(param.getLaunchType(), PromotionPurposeType.ARCHIVE_CONTENT.getCode())) {
                //inline
                if (staticInlineSlotGroupIds.contains(slotGroupId)) {
                    return staticInlineMinPrice;
                }
                //动态流
                if (staticDynamicFlowSlotGroupIds.contains(slotGroupId)) {
                    return staticDynamicFlowMinPrice;
                }
                //story
                if (creativePositionConfig.getStorySlotGroupIds().contains(slotGroupId)) {
                    return storyMinPrice;
                }
                //iPad相关推荐
                if (creativePositionConfig.getIPadRecommendSlotGroupIds().contains(slotGroupId)) {
                    return iPadRecommendMinPrice;
                }
                //PC首页推广栏
                if (creativePositionConfig.getPcIndexSlotGroupIds().contains(slotGroupId)) {
                    return pcIndexMinPrice;
                }
                //PC相关推荐
                if (creativePositionConfig.getPcPlaySlotGroupIds().contains(slotGroupId)) {
                    return pcPlayMinPrice;
                }
                //框下
                if (creativePositionConfig.getFlySlotArchiveUnderBox().equals(slotGroupId)) {
                    return flyArchiveUnderBoxMinPrice;
                }
            }
            //直播间
            if (ObjectUtils.nullSafeEquals(param.getLaunchType(), PromotionPurposeType.LIVE_ROOM.getCode())) {
                return flyProLiveRoomMinPrice;
            }
            //动态
            if (ObjectUtils.nullSafeEquals(param.getLaunchType(), PromotionPurposeType.DYNAMIC.getCode())) {
                return flyDynamicCpmMinPrice;
            }
            //活动
            if (ObjectUtils.nullSafeEquals(param.getLaunchType(), PromotionPurposeType.ACTIVITY.getCode())) {
                return flyActivityCpmMinPrice;
            }
        }
        //起飞cpc底价
        if (ObjectUtils.nullSafeEquals(SalesType.CPC.getCode(), st)) {
            Integer slotGroupId = param.getSlotGroupId();
            //稿件
            if (ObjectUtils.nullSafeEquals(param.getLaunchType(), PromotionPurposeType.ARCHIVE_CONTENT.getCode())) {
                if (!CollectionUtils.isEmpty(playPagePageIds) && playPagePageIds.contains(slotGroupId)) {
                    return flyCpcPlayMinPrice;
                } else {
                    return flyCpcInfoMinPrice;
                }
            }
            //直播间
            if (ObjectUtils.nullSafeEquals(param.getLaunchType(), PromotionPurposeType.LIVE_ROOM.getCode())) {
                return flyCpcInfoMinPrice;
            }
            //动态
            if (ObjectUtils.nullSafeEquals(param.getLaunchType(), PromotionPurposeType.DYNAMIC.getCode())) {
                return flyDynamicCpcMinPrice;
            }
            //活动
            if (ObjectUtils.nullSafeEquals(param.getLaunchType(), PromotionPurposeType.ACTIVITY.getCode())) {
                return flyActivityCpcMinPrice;
            }
        }
        //CPM 稿件 信息流&播放页
        return lauBidConfigService.getMinBidCost(param).multiply(HUNDRED).intValue();
    }

    /**
     * 根据规则获取广告位组的底价
     *
     * @param param
     * @param ocpcTarget
     * @return
     */
    public Integer getFlyBanner4LowestCost4Creative(GetBidCostParam param, Integer ocpcTarget) {
        //起飞OCPM，在创意层没有底价校验，底价写为0
        if (ocpcTarget != null && ocpcTarget > 0) {
            return 0;
        }
        // 根据规则获取广告位组的底价
        return this.getFlyBanner4LowestCost4First4Creative(param);
    }

    /**
     * 根据账号标签获取实际二阶段转化出价 目前仅起飞用
     *
     * @param param
     * @param ocpcTarget
     * @return
     */
    public Integer getFlyBanner4LowestCost4SecondByAccountLabel(GetBidCostParam param, Integer ocpcTarget) {
        //起飞OCPM，在创意层没有底价校验，底价写为0
        if (ocpcTarget != null && ocpcTarget > 0) {
            List<Integer> accountLabelIdList = param.getAccountLabelIdList();
            QueryBidTwoStageConfigParam queryParam = QueryBidTwoStageConfigParam.builder()
                    .systemType(SystemType.FLY.getCode())
                    .ocpcTarget(ocpcTarget)
                    .accountLabelIdList(accountLabelIdList)
                    .isValid(YesNoEnum.YES.getCode())
                    .salesType(param.getSalesType())
                    .build();
            // 获取实际二阶段转化出价 目前仅起飞用
            List<Integer> result = lauBidTwoStageConfigService.getFinalBidTwoStagePrices(queryParam);
            return result.stream().max(Comparator.comparing(x -> x)).orElse(0);
        }
        return 0;
    }

    /**
     * 获取起飞单元底价
     *
     * @param param
     * @param ocpcTarget
     * @return
     */
    public Integer getFlyBanner4LowestCost4Unit(GetBidCostParam param, Integer ocpcTarget) {
        if (Objects.equals(param.getLaunchType(), PromotionPurposeType.GOODS.getCode())) {
            return GOODS_MIN_PRICE;
        }
        //起飞OCPM，在创意层没有底价校验，底价写为0
        if (ocpcTarget != null && ocpcTarget > 0) {
            final Integer accountId = param.getAccountId();
            if (Objects.nonNull(accountId)) {
                final boolean isPersonalFly = launchAccountService.isPersonalFly(accountId);
                if (isPersonalFly) {
                    //个人起飞增加蓝链点击目标需求夹带
                    //有问题问@颜汐
                    if (Objects.equals(OcpcTargetEnum.LIVE_ENTRY.getCode(), ocpcTarget)) {
                        return 0;
                    }
                    //起飞涨粉托管重构&增加OCPM出价
                    //https://doc.weixin.qq.com/doc/w3_AekA4AaxACYpy2SFWeDRimbkS0ghd?scode=ANYAEAdoABEjsEWk0tAekA4AaxACY
                    //有问题问@颜汐
                    if (Objects.equals(OcpcTargetEnum.USER_FOLLOW.getCode(), ocpcTarget)) {
                        return 100;
                    }
                }
            }
            // 根据起飞 ocpm 优化目标获取第二竞价底价
            Integer originMinSecondBidPrice = Optional.ofNullable(this.getFlyBanner4LowestCost4Second(ocpcTarget,
                    accountId, param.getAccountLabelIdList())).orElse(0),
                    // 根据账号标签获取实际二阶段转化出价
                    minSecondBidPriceByAccountLabel = this.getFlyBanner4LowestCost4SecondByAccountLabel(param, ocpcTarget);
            if (Utils.isPositive(minSecondBidPriceByAccountLabel)) {
                // 优先使用标签底价
                return minSecondBidPriceByAccountLabel;
            }
            return originMinSecondBidPrice;
        }
        return this.getFlyBannerLowestCost(param);
    }

    public Map<Integer, Integer> getFlyBanner4LowestSecondBidCostMap4UnitList(List<CpcUnitDto> unitDtoList, List<Integer> accountLabelList, Integer accountId) {
        List<Integer> campaignIds = unitDtoList.stream()
                .map(CpcUnitDto::getCampaignId)
                .distinct()
                .collect(Collectors.toList());
        Map<Integer, LauCampaignDto> campaignIdMap = lauCampaignService.getCampaignDtoMapInIds(campaignIds);
        unitDtoList = unitDtoList.stream()
                .filter(unitDto -> Utils.isPositive(unitDto.getOcpcTarget()) && AdpVersion.isMiddle(unitDto.getAdpVersion()))
                .filter(unitDto -> {
                    // 获取计划
                    LauCampaignDto campaignDto = campaignIdMap.get(unitDto.getCampaignId());
                    Integer campaignPromotionPurposeType = campaignDto.getPromotionPurposeType();
                    // 校验是否需要二阶段出价校验 目前看前三者是起飞 稿件部分是起飞 动态活动是起飞
                    boolean brandSpread = PromotionPurposeType.BRAND_SPREAD.getCode() == campaignPromotionPurposeType,
                            liveRoom = PromotionPurposeType.LIVE_ROOM.getCode() == campaignPromotionPurposeType
                                    && SalesType.CPM.getCode() == unitDto.getSalesType(),
                            enterprisePromotion = PromotionPurposeType.ENTERPRISE_PROMOTION.getCode() == campaignPromotionPurposeType
                                    && PromotionPurposeType.ARCHIVE_CONTENT.getCode() == unitDto.getPromotionPurposeType(),
                            archiveContent = PromotionPurposeType.ARCHIVE_CONTENT.getCode() == unitDto.getPromotionPurposeType(),
                            dynamic = PromotionPurposeType.DYNAMIC.getCode() == unitDto.getPromotionPurposeType(),
                            ogv = PromotionPurposeType.OGV.getCode() == unitDto.getPromotionPurposeType(),
                            roi = (PromotionPurposeType.LANDING_PAGE.getCode() == unitDto.getPromotionPurposeType() || PromotionPurposeType.APP_DOWNLOAD.getCode() == unitDto.getPromotionPurposeType() || PromotionPurposeType.ON_SHELF_GAME.getCode() == unitDto.getPromotionPurposeType())
                                    && SalesType.CPC.getCode() == unitDto.getSalesType()
                                    && (OcpcTargetEnum.PAID_IN_24H_ROI.getCode() == unitDto.getOcpcTarget() || OcpcTargetEnum.PAID_IN_7D_COST.getCode() == unitDto.getOcpcTarget()),
                            activity = PromotionPurposeType.ACTIVITY.getCode() == unitDto.getPromotionPurposeType();
                    return brandSpread || liveRoom || enterprisePromotion || archiveContent || dynamic || ogv || activity || roi;
                }).collect(Collectors.toList());
        Map<Integer, List<Integer>> finalBidTwoStageUnitPriceMap = lauBidTwoStageConfigService.getFinalBidTwoStageUnitPriceMap(unitDtoList, accountLabelList, SystemType.FLY.getCode());
        Map<Integer, Integer> resultMap = new HashMap<>();
        unitDtoList.forEach(unitDto -> {
            List<Integer> minTwoStagePriceWithAccountLabelList = finalBidTwoStageUnitPriceMap.getOrDefault(unitDto.getUnitId(), Collections.emptyList());
            if (!CollectionUtils.isEmpty(minTwoStagePriceWithAccountLabelList)) {
                resultMap.put(unitDto.getUnitId(), minTwoStagePriceWithAccountLabelList.stream().max(Comparator.comparing(x -> x)).orElse(0));
            } else {
                resultMap.put(unitDto.getUnitId(),
                        Optional.ofNullable(this.getFlyBanner4LowestCost4Second(unitDto.getOcpcTarget(), accountId, accountLabelList)).orElse(0));
            }
        });
        return resultMap;
    }

    /**
     * 获取起飞最低价
     *
     * @param param
     * @return
     */
    public Integer getFlyBannerLowestCost(GetBidCostParam param) {
        //CPM
        if (param.getSalesType() != null && param.getSalesType() == SalesType.CPM.getCode()) {
            return Constants.FLY_CPM_LOWEST_PRICE_FEN;
        } else {
            //CPC
            //动态
            if (param.getLaunchType() != null && param.getLaunchType().equals(PromotionPurposeType.DYNAMIC.getCode())) {
                return flyDynamicCpcMinPrice;
            } else {
                //直播间
                if (Objects.equals(param.getLaunchType(), PromotionPurposeType.LIVE_ROOM.getCode())) {
                    return flyCpcInfoMinPrice;
                } else {
                    //稿件
                    return Constants.FLY_CPC_LOWEST_PRICE_FEN;
                }
            }
        }
    }

    /**
     * 获取底价
     * 1. 老必选，lau_bid_cost_config
     * 2. 起飞，写死的规则
     * 3. 新的必选，lau_unit_lowest_bid_rule
     *
     * @param param
     * @param adpVersion
     * @return
     */
    public int getReservedPrice(GetBidCostParam param, int adpVersion) {
        // 历史数据走老底价配置
        if (AdpVersion.isLegacy(adpVersion)) return getLowestCost(param);

        final Integer aid = param.getAccountId();
        final Integer st = param.getSalesType();
        final Integer ppt = param.getLaunchType();

        // 走起飞配置
        if (AdpVersion.isFlyBanner2(adpVersion)
                || AdpVersion.isLegacyFlyBanner4(adpVersion)
                || (AdpVersion.isMiddle(adpVersion)
                && (Objects.equals(ppt, PromotionPurposeType.ARCHIVE_CONTENT.getCode())
                || Objects.equals(ppt, PromotionPurposeType.DYNAMIC.getCode())
                || Objects.equals(ppt, PromotionPurposeType.ENTERPRISE_PROMOTION.getCode())
                || (Objects.equals(ppt, PromotionPurposeType.LIVE_ROOM.getCode())
                && Objects.equals(SalesType.CPM.getCode(), st))))) return getFlyBannerLowestCost(param);

        // 走必选的简单配置
        return launchUnitLowestBidService.getLowestBidFen(aid, st);
    }


    public boolean canIgnoreReservedPrice(int accountId) {
        final Set<Integer> aidSet = lauBidConfigService.queryBidConfigDetailPage(QueryBidCostConfigParam.builder()
                        .withTargets(true)
                        .ruleIds(Constants.IGNORE_RESERVED_PRICE_RULES)
                        .build(), Pager.LIST_ONLY)
                .getRows()
                .stream()
                .filter(x -> x.getStatus() == 1)
                .flatMap(x -> x.getTargetIds().stream())
                .collect(Collectors.toSet());
        return aidSet.contains(accountId);
    }

    Map<Integer, LauUnitBaseDto> getUnitMapInCrowdPackIds(List<Integer> crowdPackIds) {
        List<Integer> unitIds = getUnitIdsByCrowdPackIds(crowdPackIds);

        if (CollectionUtils.isEmpty(unitIds)) {
            return Collections.emptyMap();
        }

        List<LauUnitPo> pos = getLightPosInIds(unitIds);

        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyMap();
        }

        pos = pos.stream()
                .filter(po -> UnitStatus.VALID_UNIT_STATUS_LIST.contains(po.getUnitStatus()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyMap();
        }

        List<Integer> accountIds = pos.stream().map(LauUnitPo::getAccountId).distinct().collect(Collectors.toList());

        Map<Integer, AccountBaseDto> accountMap = queryAccountService.getAccountBaseDtoMapInIds(accountIds);

        return buildLauUnitBaseDtoMap(pos, accountMap);
    }

    public List<Integer> getUnitIdsInCrowdPackIds(List<Integer> crowdPackIds) {
        if (CollectionUtils.isEmpty(crowdPackIds)) {
            return Collections.emptyList();
        }
        List<Integer> unitIds = getUnitIdsByCrowdPackIds(crowdPackIds);
        if (CollectionUtils.isEmpty(unitIds)) {
            return Collections.emptyList();
        }
        QueryCpcUnitDto queryDto = QueryCpcUnitDto.builder()
                .unitIds(unitIds)
                .unitStatusList(UnitStatus.VALID_UNIT_STATUS_LIST)
                .page(Page.valueOf(1, 10))
                .build();
        return queryCpcUnitIds(queryDto);
    }

    public boolean checkCrowdPackIsUsing(Integer crowdPackId) {
        if (!Utils.isPositive(crowdPackId)) {
            return false;
        }

        // 检查新版定向包绑定关系
        List<Integer> targetPackageUpgradeIds =
                resTargetPackageUpgradeService.getTargetPackageIdsInCrowdPackIds(Lists.newArrayList(crowdPackId));
        if (!CollectionUtils.isEmpty(targetPackageUpgradeIds)) {
            return true;
        }

        List<Integer> unitIds = getUnitIdsByCrowdPackIds(Lists.newArrayList(crowdPackId));

        // 没有绑定任何单元
        if (CollectionUtils.isEmpty(unitIds)) {
            return false;
        }

        List<Integer> bindingUnitIds = CollectionHelper.callInBatches(unitIds, LaunchConstant.QUERY_BATCH_SIZE, unitIdList -> {
            QueryCpcUnitDto queryDto = QueryCpcUnitDto.builder()
                    .unitIds(unitIdList)
                    .unitStatusList(UnitStatus.VALID_UNIT_STATUS_LIST)
                    .page(Page.valueOf(1, 1))
                    .build();
            return queryCpcUnitIds(queryDto);
        });

        return !CollectionUtils.isEmpty(bindingUnitIds);
    }

    private Map<Integer, LauUnitBaseDto> buildLauUnitBaseDtoMap(List<LauUnitPo> pos, Map<Integer, AccountBaseDto> accountMap) {
        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyMap();
        }

        return pos.stream().collect(Collectors.toMap(LauUnitPo::getUnitId, po -> buildLauUnitBaseDto(po, accountMap.get(po.getAccountId()))));
    }

    private LauUnitBaseDto buildLauUnitBaseDto(LauUnitPo po, AccountBaseDto account) {
        LauUnitBaseDto dto = new LauUnitBaseDto();
        BeanUtils.copyProperties(po, dto);

        if (account != null) {
            dto.setAccountName(account.getUsername());
        }

        return dto;
    }

    private LauUnitGamePo getGameBaseIdByUnitId(Integer unitId) {
        LauUnitGamePoExample example = new LauUnitGamePoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andUnitIdEqualTo(unitId);

        List<LauUnitGamePo> pos = lauUnitGameDao.selectByExample(example);
        if (CollectionUtils.isEmpty(pos)) return null;

        return pos.get(0);
    }

    private LauUnitShopGoodsPo getShopGoodsIdByUnitId(Integer unitId) {
        LauUnitShopGoodsPoExample example = new LauUnitShopGoodsPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andUnitIdEqualTo(unitId);

        List<LauUnitShopGoodsPo> pos = lauUnitShopGoodsDao.selectByExample(example);
        Assert.notEmpty(pos, "该单元不存在商品投放");
        return pos.get(0);
    }

    public List<LauUnitShopGoodsPo> getShopGoodsIdByUnitIds(List<Integer> unitIds) {
        if (CollectionUtils.isEmpty(unitIds)) {
            return Collections.EMPTY_LIST;
        }
        LauUnitShopGoodsPoExample example = new LauUnitShopGoodsPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andUnitIdIn(unitIds);

        List<LauUnitShopGoodsPo> pos = lauUnitShopGoodsDao.selectByExample(example);
        return pos;
    }

    private List<Integer> getBusinessInterestTargetsByUnitId(Integer unitId) {
        LauUnitTargetBusinessInterestPoExample example = new LauUnitTargetBusinessInterestPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andUnitIdEqualTo(unitId);

        List<LauUnitTargetBusinessInterestPo> lauUnitTargetBusinessInterestPos = lauUnitTargetBusinessInterestDao.selectByExample(example);
        if (CollectionUtils.isEmpty(lauUnitTargetBusinessInterestPos)) {
            return Collections.emptyList();
        }

        return lauUnitTargetBusinessInterestPos.stream().map(LauUnitTargetBusinessInterestPo::getInterestId).collect(Collectors.toList());
    }

    private List<String> getContentLabelTargetsByUnitId(Integer unitId) {
        LauUnitTargetContentLabelPoExample example = new LauUnitTargetContentLabelPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andUnitIdEqualTo(unitId);

        List<LauUnitTargetContentLabelPo> lauUnitTargetContentLabelPos = lauUnitTargetContentLabelDao.selectByExample(example);
        if (CollectionUtils.isEmpty(lauUnitTargetContentLabelPos)) {
            return Collections.emptyList();
        }

        return lauUnitTargetContentLabelPos.stream().map(LauUnitTargetContentLabelPo::getContentLabel).collect(Collectors.toList());
    }

    private List<Long> getVideoIdTargetsByUnitId(Integer unitId) {
        LauUnitTargetVideoPoExample example = new LauUnitTargetVideoPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andUnitIdEqualTo(unitId);

        List<LauUnitTargetVideoPo> lauUnitTargetVideoPos = lauUnitTargetVideoDao.selectByExample(example);
        if (CollectionUtils.isEmpty(lauUnitTargetVideoPos)) {
            return Collections.emptyList();
        }

        return lauUnitTargetVideoPos.stream().map(LauUnitTargetVideoPo::getVideoId).collect(Collectors.toList());
    }

    public List<Integer> queryDistinctAppPackageIdList(QueryCpcUnitDto query) {
        List<Integer> appPackageIdList = getCpcUnitQuery(query)
                .where(lauUnit.appPackageId.gt(0))
                .select(lauUnit.appPackageId)
                .distinct()
                .fetch();
        return appPackageIdList.stream()
                .filter(Utils::isPositive)
                .collect(Collectors.toList());
    }

    public List<Integer> queryCpcUnitIds(QueryCpcUnitDto query) {
        return getCpcUnitQuery(query).select(lauUnit.unitId).fetch();
    }

    public Map<Integer, CpcUnitDto> getUnitMapInIds(List<Integer> unitIds) {
        if (CollectionUtils.isEmpty(unitIds)) {
            return Collections.emptyMap();
        }

        //List<CpcUnitDto> dtos = queryCpcUnit(QueryCpcUnitDto.builder().unitIds(unitIds).build());
        QueryUnitBo query = QueryUnitBo.builder()
                .unitIds(unitIds)
                .build();
        List<CpcUnitDto> dtos = launchUnitV1Service.listUnits(query);
        if (CollectionUtils.isEmpty(dtos)) {
            return Collections.emptyMap();
        }

        return dtos.stream().collect(Collectors.toMap(CpcUnitDto::getUnitId, dto -> dto));
    }

    public void validateTarget(Integer accountId, UnitTargetDto unitTargetDto) throws ServiceException {
        validMiniGameConfig(unitTargetDto.getInclude_mini_game_ids(), unitTargetDto.getExclude_mini_game_ids());

        // 这个是暴露出去的api, 不知道谁用了, 暂时保持不变, 只能校验旧版
        unitValidator.validateTarget(accountId, unitTargetDto, AdpVersion.LEGACY.getKey());
    }

    /**
     * 获取计划下手动单元最大的单元预算
     *
     * @param campaignId
     * @return
     */
    public long getMaxCpcUnitBudgetByCampaignId(Integer campaignId) {
        LauUnitPoExample example = new LauUnitPoExample();
        example.or()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andUnitStatusIn(UnitStatus.VALID_UNIT_STATUS_LIST)
                .andDailyBudgetTypeEqualTo(DailyBudgetType.MANUAL.getCode())
                .andCampaignIdEqualTo(campaignId);

        example.setOrderByClause("budget desc");
        example.setLimit(1);

        List<LauUnitPo> pos = lauUnitDao.selectByExample(example);

        if (CollectionUtils.isEmpty(pos)) {
            return 0L;
        }

        return pos.get(0).getBudget() == null ? 0L : pos.get(0).getBudget();
    }

    public long getMaxCpcUnitNextdayBudgetByCampaignId(Integer campaignId) {
        LauUnitPoExample example = new LauUnitPoExample();
        example.or()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andUnitStatusIn(UnitStatus.VALID_UNIT_STATUS_LIST)
                .andDailyBudgetTypeEqualTo(DailyBudgetType.MANUAL.getCode())
                .andCampaignIdEqualTo(campaignId);

        List<LauUnitPo> pos = lauUnitDao.selectByExample(example);

        if (CollectionUtils.isEmpty(pos)) {
            return 0L;
        }

        LauUnitNextdayBudgetPoExample nextdayBudgetPoExample = new LauUnitNextdayBudgetPoExample();
        nextdayBudgetPoExample.or()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andUnitIdIn(pos.stream().map(LauUnitPo::getUnitId).collect(Collectors.toList()));

        nextdayBudgetPoExample.setOrderByClause("budget desc");
        nextdayBudgetPoExample.setLimit(1);
        List<LauUnitNextdayBudgetPo> nextdayBudgetPoList = lauUnitNextdayBudgetDao.selectByExample(nextdayBudgetPoExample);

        if (CollectionUtils.isEmpty(nextdayBudgetPoList)) {
            return 0L;
        }

        return nextdayBudgetPoList.get(0).getBudget() == null ? 0L : nextdayBudgetPoList.get(0).getBudget();
    }

    public BaseQuery<com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauUnitPo> getCpcUnitQuery(QueryCpcUnitDto query) {
        Arguments.of(query).notNull("查询参数不能为空");

        BaseQuery<com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauUnitPo> lauUnitDoBaseQuery = adCoreBqf.selectFrom(lauUnit)
                .where(lauUnit.isDeleted.eq(IsDeleted.VALID.getCode()))
                .whereIfNotNull(query.getIsNewFly(), lauUnit.isNewFly::eq)
                .whereIfNotNull(query.getAccountId(), lauUnit.accountId::eq)
                .whereIfNotNull(query.getCampaignId(), lauUnit.campaignId::eq)
                .whereIfNotNull(query.getUnitId(), lauUnit.unitId::eq)
                .whereIfNotNull(query.getGtUnitId(), lauUnit.unitId::gt)
                .whereIfNotNull(query.getEltUnitId(), lauUnit.unitId::loe)
                .whereIfNotNull(query.getSalesType(), lauUnit.salesType::eq)
                .whereIfNotNull(query.getUnitStatus(), lauUnit.unitStatus::eq)
                .whereIfNotNull(query.getNotUnitStatus(), lauUnit.unitStatus::ne)
                .whereIfNotNull(query.getSlotGroupId(), lauUnit.slotGroup::eq)
                .whereIfNotNull(query.getGtAccountId(), lauUnit.accountId::gt)
                .whereIfNotNull(query.getEltAccountId(), lauUnit.accountId::loe)
                .whereIfNotNull(query.getIsMiddleAd(), lauUnit.isMiddleAd::eq)
                .whereIfNotNull(query.getIsManaged(), lauUnit.isManaged::eq)
                //只查ocpc -> ocpc_target > 0
                .whereIfTrue(query.isOnlyOcpc(), () -> lauUnit.ocpcTarget.gt(0))
                //只查cpc -> ocpc_target = 0
                .whereIfTrue(query.isOnlyCpc(), () -> lauUnit.ocpcTarget.eq(0))
                .whereIfNotEmpty(query.getAccountIds(), lauUnit.accountId::in)
                .whereIfNotEmpty(query.getCampaignIds(), lauUnit.campaignId::in)
                .whereIfNotEmpty(query.getUnitIds(), lauUnit.unitId::in)
                .whereIfNotEmpty(query.getSalesTypes(), lauUnit.salesType::in)
                .whereIfNotEmpty(query.getUnitStatusList(), lauUnit.unitStatus::in)
                .whereIfNotEmpty(query.getNotUnitStatusList(), lauUnit.unitStatus::notIn)
                .whereIfNotEmpty(query.getSlotGroupIds(), lauUnit.slotGroup::in)
                .whereIfNotEmpty(query.getFlags(), lauUnit.flag::in)
                // 新增 ocpc 目标
                .whereIfNotNull(query.getOcpcTarget(), lauUnit.ocpcTarget::eq)
                .whereIfNotEmpty(query.getOcpcTargets(), lauUnit.ocpcTarget::in)
                // 高优单元
                .whereIfNotNull(query.getIsHighPriority(), lauUnit.isHighPriority::eq)

                .whereIfHasText(query.getLikeUnitName(), lauUnit.unitName::contains)
                .whereIfHasText(query.getUnitName(), lauUnit.unitName::eq)
                // 单元结束时间 >= beginDate
                .whereIfHasText(query.getBeginDate(), lauUnit.launchEndDate::goe)
                // 单元开始时间 <= endDate
                .whereIfHasText(query.getEndDate(), lauUnit.launchBeginDate::loe)
                .whereIfNotEmpty(query.getAdpVersions(), lauUnit.adpVersion::in)
                .whereIfNotNull(query.getTargetPackageId(), lauUnit.targetPackageId::eq)
                .whereIfNotEmpty(query.getSubjectIds(), lauUnit.subjectId::in)

                .whereIfNotNull(query.getCreateBeginTime(), time -> lauUnit.ctime.goe(new Timestamp(time)))
                .whereIfNotNull(query.getCreateEndTime(), time -> lauUnit.ctime.loe(new Timestamp(time)))
                .whereIfTrue(query.isOnlyShowHasAppPackageIdUnit(), () -> lauUnit.appPackageId.gt(0))
                .whereIfTrue(query.isOnlyShowOcpcOcpmUnit(), () -> lauUnit.ocpcTarget.gt(0))
                .whereIfTrue(query.isOnlyShowParentUnit(), () -> lauUnit.parentUnitId.eq(0))
                .orderByIfHasText(query.getOrderBy())
                .offsetIfNotNull(query.getPage() == null ? null : query.getPage().getOffset())
                .limitIfNotNull(query.getPage() == null ? null : query.getPage().getLimit());

        return lauUnitDoBaseQuery;
    }

    /**
     * 保存单元定向
     *
     * @param operator
     * @param unit
     * @param unitId
     * @throws ServiceException
     */
    private void addCpcUnitTarget(Operator operator, NewCpcUnitDto unit, Integer unitId) throws ServiceException {
        // 绑定新版定向包的单元 原来的单元映射保存为无定向条件
        boolean isBindUpgradeTargetPackage = Utils.isPositive(unit.getTargetPackageId());

        List<TargetRule> targetRules = isBindUpgradeTargetPackage ? Collections.emptyList() : unit.getTargetRules();
        List<Integer> osIds = TargetUtils.getValueIdsByTargetType(targetRules, TargetType.OS.getCode());
        // 创建单元定向规则
        unitTargetRuleService.create(operator, unitId, targetRules);

        // 保存单元人群包
        List<List<Integer>> otherCrowdPackIdsGroup = isBindUpgradeTargetPackage ? Collections.emptyList() : unit.getOtherCrowdPackIdsGroup();
        List<Integer> crowdPackIds = isBindUpgradeTargetPackage ? Collections.emptyList() : unit.getCrowdPackIds(),
                excludePackIds = isBindUpgradeTargetPackage ? Collections.emptyList() : unit.getExcludeCrowdPackIds(),
                extraCrowdPackIds = isBindUpgradeTargetPackage ? Collections.emptyList() : unit.getExtraCrowdPackIds();
        insertCrowdPackIds(unitId, crowdPackIds, excludePackIds, otherCrowdPackIdsGroup, extraCrowdPackIds);

        List<String> tags = isBindUpgradeTargetPackage ? Collections.emptyList() : unit.getTags();
        Integer isFuzzyTags = isBindUpgradeTargetPackage ? 0 : Optional.ofNullable(unit.getIsFuzzyTags()).orElse(0);
        try {
            lauTagIdsService.addContentTagIds(unitId, LauTargetTagEnum.UNIT.getCode(), tags, isFuzzyTags);
            lauTagIdsService.addTags(unitId, LauTargetTagEnum.UNIT.getCode(), unit.getVideoTag());
        } catch (Exception e) {
            LOGGER.error("create: add tags encounter an exception: " + ExceptionUtils.getStackTrace(e));
        }

        //下线商业兴趣
        //List<Integer> businessInterestIds = isBindUpgradeTargetPackage ? Collections.emptyList() : unit.getBusinessInterestIds();
        //this.saveBusinessInterestTarget(unitId, businessInterestIds);
        professionInterestService.saveProfessionInterestTarget(unitId, unit.getProfessionInterestIds());
        if (IsInterestAuto.IS_AUTO == unit.getProfessionInterestAuto()) {
            professionInterestAutoService.save(unitId, operator);
        }

        UnitTargetInstalledUserFilterDto installedUserFilterDto = isBindUpgradeTargetPackage ? null : unit.getInstalledUserFilter();
        saveInstalledUserFilterTarget(unitId, installedUserFilterDto);

        // 操作系统版本和bili客户端版本保存
        lauUnitTargetOsVersionUpgradeProc.save(unitId, unit.getOsVersion(), osIds);
        lauUnitTargetBiliClientUpgradeProc.save(unitId, unit.getBiliClientVersion(), osIds);
    }

    public void saveInstalledUserFilterTarget(Integer unitId, UnitTargetInstalledUserFilterDto installedUserFilterDto) {
        //deleteInstalledUserFilterTargetByUnitId(unitId);
        if (installedUserFilterDto == null) {
            LauUnitTargetInstalledUserFilterDo installedUserFilterDo = new LauUnitTargetInstalledUserFilterDo();
            installedUserFilterDo.setFilterType(InstalledUserFilterEnum.UNLIMIT.getCode());
            installedUserFilterDo.setUnitId(unitId);
            installedUserFilterDo.setIsDeleted(IsDeleted.VALID.getCode());
            adCoreBqf.insertMysql(lauUnitTargetInstalledUserFilter).insertDuplicateUpdate(installedUserFilterDo);
            return;
        }
        LauUnitTargetInstalledUserFilterDo installedUserFilterDo = new LauUnitTargetInstalledUserFilterDo();
        installedUserFilterDo.setFilterType(installedUserFilterDto.getFilterType());
        installedUserFilterDo.setUnitId(unitId);
        installedUserFilterDo.setIsDeleted(IsDeleted.VALID.getCode());

        if (InstalledUserFilterEnum.TARGET.getCode().compareTo(installedUserFilterDto.getFilterType()) == 0) {
            JSONArray jsonArray = new JSONArray();
            jsonArray.addAll(CollectionUtils.isEmpty(installedUserFilterDto.getTargetContent()) ? Lists.newArrayList(-1) : installedUserFilterDto.getTargetContent());
            installedUserFilterDo.setTargetContent(jsonArray.toString());
        } else {
            JSONArray jsonArray = new JSONArray();
            jsonArray.addAll(Lists.newArrayList(-1));
            installedUserFilterDo.setTargetContent(jsonArray.toString());
        }
        adCoreBqf.insertMysql(lauUnitTargetInstalledUserFilter).insertDuplicateUpdate(installedUserFilterDo);
    }

    private void deleteInstalledUserFilterTargetByUnitId(Integer unitId) {
        adCoreBqf.update(lauUnitTargetInstalledUserFilter)
                .set(lauUnitTargetInstalledUserFilter.isDeleted, IsDeleted.DELETED.getCode())
                .where(lauUnitTargetInstalledUserFilter.unitId.eq(unitId))
                .execute();
    }

    private UnitTargetInstalledUserFilterDto getUnitTargetInstalledUserFilterTargetByUnitId(Integer unitId) {
        LauUnitTargetInstalledUserFilterDo filterDo = adCoreBqf.selectFrom(lauUnitTargetInstalledUserFilter)
                .where(lauUnitTargetInstalledUserFilter.unitId.eq(unitId))
                .where(lauUnitTargetInstalledUserFilter.isDeleted.eq(IsDeleted.VALID.getCode()))
                .fetchFirst();
        if (filterDo == null) {
            return UnitTargetInstalledUserFilterDto.getEmpty();
        }

        return UnitTargetInstalledUserFilterDto.builder()
                .filterType(filterDo.getFilterType())
                .targetContent(StringUtils.isEmpty(filterDo.getTargetContent()) ? Collections.emptyList() : JSONArray.parseArray(filterDo.getTargetContent(), Integer.class))
                .build();
    }

    private String getBusinessInterestTargetUk(LauUnitTargetBusinessInterestPo po) {
        return po.getUnitId() + "-" + po.getSecondCategoryId();
    }

    public void saveBusinessInterestTarget(Integer unitId, List<Integer> businessInterestIds) {
        final LauUnitTargetBusinessInterestPoExample cond = new LauUnitTargetBusinessInterestPoExample();
        cond.or().andUnitIdEqualTo(unitId);
        final List<LauUnitTargetBusinessInterestPo> existingPos = lauUnitTargetBusinessInterestDao.selectByExample(cond);
        final List<LauUnitTargetBusinessInterestPo> newPos = buildLauUnitTargetBusinessInterestPo(businessInterestIds, unitId);
        final RecDiffResult<LauUnitTargetBusinessInterestPo, Integer> result = Functions.recDiff(existingPos, newPos, this::getBusinessInterestTargetUk, LauUnitTargetBusinessInterestPo::getId, LauUnitTargetBusinessInterestPo::setId);
        for (LauUnitTargetBusinessInterestPo po : result.getNewRecords()) {
            lauUnitTargetBusinessInterestDao.insertSelective(po);
        }
        for (LauUnitTargetBusinessInterestPo po : result.getChangedRecords()) {
            lauUnitTargetBusinessInterestDao.updateByPrimaryKeySelective(po);
        }
        for (Integer key : result.getOfflineRecordKeys()) {
            lauUnitTargetBusinessInterestDao.deleteByPrimaryKey(key);
        }
    }

    private void deleteVideoIdsByUnitId(Integer unitId) {
        LauUnitTargetVideoPo record = new LauUnitTargetVideoPo();
        record.setIsDeleted(IsDeleted.DELETED.getCode());

        LauUnitTargetVideoPoExample example = new LauUnitTargetVideoPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andUnitIdEqualTo(unitId);

        lauUnitTargetVideoDao.updateByExampleSelective(record, example);
    }

    private CpcUnitDto cpcUnitPoToDto(LauUnitPo po) {
        CpcUnitDto dto = new CpcUnitDto();
        BeanUtils.copyProperties(po, dto);

        if (!Strings.isNullOrEmpty(po.getTags())) {
            dto.setTags(Splitter.on(",").omitEmptyStrings().trimResults().splitToList(po.getTags()));
        } else {
            dto.setTags(Collections.emptyList());
        }
        return dto;
    }

    private CpcUnitDto cpcUnitPoToDto(com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauUnitPo po) {
        CpcUnitDto dto = new CpcUnitDto();
        BeanUtils.copyProperties(po, dto);

        if (!Strings.isNullOrEmpty(po.getTags())) {
            dto.setTags(Splitter.on(",").omitEmptyStrings().trimResults().splitToList(po.getTags()));
        } else {
            dto.setTags(Collections.emptyList());
        }
        return dto;
    }

    public List<CpcUnitDto> getSpecifyUnit(QueryCpcUnitDto query) {
        LauUnitPoExample example = new LauUnitPoExample();
        example.or()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andCampaignIdEqualTo(query.getCampaignId())
                .andUnitStatusIn(query.getUnitStatusList());
        example.setLimit(query.getLimit());
        example.setOrderByClause(query.getOrderBy());
        List<LauUnitPo> lauUnitPos = lauUnitDao.selectByExample(example);

        List<CpcUnitDto> dtos = cpcUnitPosToDtos(lauUnitPos);
        queryPostHandle(query, dtos);
        return dtos;
    }

    public List<CpcUnitDto> cpcUnitPosToDtos(List<LauUnitPo> pos) {
        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyList();
        }

        return pos.stream().map(this::cpcUnitPoToDto).collect(Collectors.toList());
    }

    public List<CpcUnitDto> cpcUnitPosToDtosNew(List<com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauUnitPo> pos) {
        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyList();
        }

        return pos.stream().map(this::cpcUnitPoToDto).collect(Collectors.toList());
    }

    /**
     * 单元 dto -> po(里面有ocpx两阶段竞价逻辑)
     *
     * @param unit
     * @return
     */
    private com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauUnitPo cpcUnitDtoToPo(NewCpcUnitDto unit) {

        //LauUnitPo entity = new LauUnitPo();
        com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauUnitPo entity = new com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauUnitPo();
        BeanUtils.copyProperties(unit, entity);
        // 设置 copx 两阶段竞价信息
        updateOcpxStageTwoInfo(entity, unit.getOcpcTarget(), unit.getOcpxTargetTwo(), unit.getTwoStageBid(), unit.getOcpxTargetTwoBid());

        entity.setTags("");
        entity.setCreativeDisplayMode(CreativeDisplayMode.OPTIMIZATION.getCode());
        // oCPC(M)二阶段出价
        entity.setTwoStageBid(Utils.isPositive(unit.getOcpcTarget()) && Utils.isPositive(unit.getTwoStageBid()) ? unit.getTwoStageBid() : 0);
        entity.setFlag(unit.getSource());
        return entity;
    }

    /**
     * 设置 copx 两阶段竞价信息
     *
     * @param po
     * @param targetOne    第一目标
     * @param targetTwo    第二目标
     * @param targetOneBid oCPC第一目标出价
     * @param targetTwoBid oCPC第二目标出价
     */
    private void updateOcpxStageTwoInfo(com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauUnitPo po, Integer targetOne, Integer targetTwo, Integer targetOneBid, Integer targetTwoBid) {
        // 二阶段竞价
        if (Utils.isPositive(targetTwo)) {
            po.setOcpxTargetTwo(targetTwo);

            if (Utils.isPositive(targetTwoBid)) {
                // 不是PAID_IN_24H_ROI的深度目标
                if (!OcpcTargetEnum.ROI_24H_VALUE_LIST.contains(targetTwo)) {
                    Assert.isTrue(Utils.isPositive(targetOneBid) && targetOneBid < targetTwoBid, "双出价的第一目标出价必须低于第二目标出价");
                }
                // 双出价
                po.setOcpxTargetTwoBid(targetTwoBid);
                po.setOcpxMode(OcpxMode.DUAL_TARGETS_MANUAL_BID.getCode());
            } else {
                // 第二目标自动出价
                po.setOcpxTargetTwoBid(0);
                po.setOcpxMode(OcpxMode.DUAL_TARGETS_AUTO_BID.getCode());
            }
        }
        // 一阶段竞价
        else if (Utils.isPositive(targetOne)) {
            // 单目标(单目标不存在自动优化)
            po.setOcpxTargetTwo(0);
            po.setOcpxTargetTwoBid(0);
            po.setOcpxMode(OcpxMode.SINGLE_TARGET.getCode());
        }
        // 非oCPX
        else {
            po.setOcpcTarget(0);
            po.setTwoStageBid(0);
            po.setOcpxTargetTwo(0);
            po.setOcpxTargetTwoBid(0);
            po.setOcpxMode(OcpxMode.NONE.getCode());
        }

    }

    private com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauUnitPo cpcUnitDtoToPo(UpdateCpcUnitDto unit) {

        //LauUnitPo entity = new LauUnitPo();
        com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauUnitPo entity = new com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauUnitPo();
        BeanUtils.copyProperties(unit, entity);
        updateOcpxStageTwoInfo(entity, unit.getOcpcTarget(), unit.getOcpxTargetTwo(), unit.getTwoStageBid(), unit.getOcpxTargetTwoBid());

        // 不可以更新adp的版本
        entity.setAdpVersion(null);

        entity.setTags("");
        entity.setLaunchTime(Strings.isNullOrEmpty(unit.getLaunchTime()) ? Constants.LAUNCH_TIME : unit.getLaunchTime());
        entity.setTwoStageBid(Utils.isPositive(unit.getOcpcTarget()) && Utils.isPositive(unit.getTwoStageBid()) ? unit.getTwoStageBid() : 0);
        entity.setGdPlusOptimizeModel(unit.getOptimizeModel());
        entity.setGdPlusOptimizeTarget(unit.getOptimizeTarget());
        return entity;
    }

    private void insertUnitCreateLog(Operator operator,
                                     NewCpcUnitDto unit,
                                     Integer newUnitId,
                                     LauSubjectDto subjectDto,
                                     Integer businessDomain) {
        UnitTargetRulePo unitTargetPo = LaunchUtil.getUnitTargetRuleEntity(newUnitId, unit.getTargetRules());

        CpcUnitDto logDto = new CpcUnitDto();
        BeanUtils.copyProperties(unit, logDto);
        if (subjectDto != null) {
            logDto.setSubjectId(subjectDto.getId());
            logDto.setLauSubject(subjectDto);
        }

        if (IsValid.TRUE.getCode().equals(unit.getIsGdPlus())) {
            logDto.setGdPlusOptimizeModel(unit.getOptimizeModel());
            logDto.setGdPlusOptimizeTarget(unit.getOptimizeTarget());
        }

        // 深度目标二阶段出价
        if (OcpcTargetEnum.PAID_IN_24H_ROI.getCode().equals(unit.getOcpxTargetTwo())) {
            logDto.setOcpxTargetTwoBid4Log(Utils.handleROI(unit.getOcpxTargetTwoBid()).toString());
        } else {
            if (Utils.isPositive(unit.getOcpxTargetTwoBid())) {
                logDto.setOcpxTargetTwoBid4Log(Utils.fromFenToYuan(unit.getOcpxTargetTwoBid().longValue()).toString());
            }
        }
        logDto.setOcpxTargetTwoBid(null);

        if (AdpVersion.isLegacy(unit.getAdpVersion())) {
            // 旧版adp在计划层级记录
            logDto.setSpeedMode(null);
        }

        // 三连Ocpx单元一阶段出价不再落日志
        if (Utils.isPositive(unit.getOcpcTarget())
                && AdpVersion.isMiddle(unit.getAdpVersion())) {
            logDto.setCostPrice(null);
        }

        //智能拓词开关
        logDto.setSmartKeyWord(unit.getSmartKeyWord());
        if (unit.getTargetExpand() != null) {
            logDto.setTargetExpand(unit.getTargetExpand());
        }
        if (unit.getSearchPriceCoefficient() != null) {
            logDto.setSearchPriceCoefficient(unit.getSearchPriceCoefficient());
        }

        logOperateService.addInsertLog(DbTable.LAU_UNIT, operator, Arrays.asList(logDto, unitTargetPo), newUnitId);
    }

    private void updateCreativeLaunchDate(CpcUnitDto oldUnit, UpdateCpcUnitDto updateUnitDto) {
        if (!oldUnit.getLaunchBeginDate().equals(updateUnitDto.getLaunchBeginDate())
                || !oldUnit.getLaunchEndDate().equals(updateUnitDto.getLaunchEndDate())) {

            if (AdpVersion.isLegacy(oldUnit.getAdpVersion())) {
                // 旧版adp在计划层级记录
                updateUnitDto.setSpeedMode(null);
            }


            lauCreativeService.updateCreativeLaunchDate(oldUnit.getUnitId(),
                    Utils.string2Date(updateUnitDto.getLaunchBeginDate()), Utils.string2Date(updateUnitDto.getLaunchEndDate()));
        }
    }

    private void saveUnitUpdateLog(Operator operator, UpdateCpcUnitDto updateUnitDto, CpcUnitDto oldUnit) {
        oldUnit.setStatus(null);

        UnitTargetRulePo newUnitTargetPo = LaunchUtil.getUnitTargetRuleEntity(updateUnitDto.getUnitId(), updateUnitDto.getTargetRules());
        UnitTargetRulePo oldUnitTargetPo = LaunchUtil.getUnitTargetRuleEntity(oldUnit.getUnitId(), oldUnit.getTargetRules());

        CpcUnitDto updateLogDto = new CpcUnitDto();
        BeanUtils.copyProperties(updateUnitDto, updateLogDto);

        if (IsValid.TRUE.getCode().equals(oldUnit.getIsGdPlus())) {
            updateLogDto.setGdPlusOptimizeModel(updateUnitDto.getOptimizeModel());
            updateLogDto.setGdPlusOptimizeTarget(updateUnitDto.getOptimizeTarget());
        }


        // 深度目标二阶段出价 old log
        if (OcpcTargetEnum.PAID_IN_24H_ROI.getCode().equals(oldUnit.getOcpxTargetTwo())) {
            oldUnit.setOcpxTargetTwoBid4Log(Utils.handleROI(oldUnit.getOcpxTargetTwoBid()).toString());
        } else {
            if (Utils.isPositive(oldUnit.getOcpxTargetTwo())) {
                oldUnit.setOcpxTargetTwoBid4Log(Utils.fromFenToYuan(oldUnit.getOcpxTargetTwoBid().longValue()).toString());
            }
        }

        // 深度目标二阶段出价 new log
        if (OcpcTargetEnum.PAID_IN_24H_ROI.getCode().equals(updateUnitDto.getOcpxTargetTwo())) {
            updateLogDto.setOcpxTargetTwoBid4Log(Utils.handleROI(updateUnitDto.getOcpxTargetTwoBid()).toString());
        } else {
            if (Utils.isPositive(updateUnitDto.getOcpxTargetTwo())) {
                updateLogDto.setOcpxTargetTwoBid4Log(Utils.fromFenToYuan(updateUnitDto.getOcpxTargetTwoBid().longValue()).toString());
            }
        }

        oldUnit.setOcpxTargetTwoBid(null);
        updateLogDto.setOcpxTargetTwoBid(null);

        logOperateService.addUpdateLog(DbTable.LAU_UNIT, operator, Arrays.asList(oldUnit, oldUnitTargetPo), Arrays.asList(updateLogDto, newUnitTargetPo), oldUnit.getUnitId());
    }

    public void updateCrowdPackIds(Integer unitId, List<Integer> cpIds, List<Integer> excludeCpIds, List<List<Integer>> otherCpIdsGroup, List<Integer> extraCpIds) throws ServiceException {
        deleteCrowdPackByUnitId(unitId);

        insertCrowdPackIds(unitId, cpIds, excludeCpIds, otherCpIdsGroup, extraCpIds);
    }

    private void deleteCrowdPackByUnitId(Integer unitId) {
        LauUnitTargetCrowdPackPoExample example = new LauUnitTargetCrowdPackPoExample();
        example.or().andUnitIdEqualTo(unitId).andIsDeletedEqualTo(IsDeleted.VALID.getCode());

        LauUnitTargetCrowdPackPo po = new LauUnitTargetCrowdPackPo();
        po.setIsDeleted(IsDeleted.DELETED.getCode());

        lauUnitTargetCrowdPackDao.updateByExampleSelective(po, example);
        //扩展种子人群包
        adCoreBqf
                .delete(lauUnitTargetExtraCrowdPack)
                .where(lauUnitTargetExtraCrowdPack.unitId.eq(unitId)).execute();
    }

    /**
     * 保存单元人群包
     *
     * @param unitId
     * @param cpIds           人群包 ids
     * @param excludeCpIds    排除的
     * @param otherCpIdsGroup
     * @param extraCpIds
     * @throws ServiceException
     */
    private void insertCrowdPackIds(Integer unitId, List<Integer> cpIds, List<Integer> excludeCpIds, List<List<Integer>> otherCpIdsGroup, List<Integer> extraCpIds) throws ServiceException {
        if (CollectionUtils.isEmpty(cpIds) && CollectionUtils.isEmpty(excludeCpIds) && CollectionUtils.isEmpty(extraCpIds)) {
            return;
        }

        List<LauUnitTargetCrowdPackPo> inPos = CollectionUtils.isEmpty(cpIds) ? Collections.emptyList() : cpIds
                .stream()
                .map(cpId -> buildTCPPo(unitId, cpId, UnitTargetCrowdPackType.INCLUDE.getCode()))
                .collect(Collectors.toList());

        List<LauUnitTargetCrowdPackPo> exPos = CollectionUtils.isEmpty(excludeCpIds) ? Collections.emptyList() : excludeCpIds
                .stream()
                .map(cpId -> buildTCPPo(unitId, cpId, UnitTargetCrowdPackType.EXCLUDE.getCode()))
                .collect(Collectors.toList());

        List<LauUnitTargetCrowdPackPo> pos = Lists.newArrayList();
        pos.addAll(inPos);
        pos.addAll(exPos);

        if (!CollectionUtils.isEmpty(otherCpIdsGroup)) {
            List<LauUnitTargetCrowdPackPo> otherPos = null;

            for (int i = 0; i < otherCpIdsGroup.size(); i++) {
                List<Integer> otherCpIds = otherCpIdsGroup.get(i);
                int groupId = i + 1;

                otherPos = CollectionUtils.isEmpty(otherCpIds) ? Collections.emptyList() : otherCpIds
                        .stream()
                        .map(cpId -> buildTCPPo(unitId, cpId, UnitTargetCrowdPackType.INCLUDE.getCode(), groupId))
                        .collect(Collectors.toList());

                pos.addAll(otherPos);
            }
        }
        if (!CollectionUtils.isEmpty(pos)) {
            extLauUnitTargetCrowdPackDao.batchSave(pos);
        }

        if (!CollectionUtils.isEmpty(extraCpIds)) {
            List<LauUnitTargetCrowdPackPo> extraCrowdPacks = extraCpIds
                    .stream()
                    .map(cpId -> LauUnitTargetCrowdPackPo.builder().unitId(unitId).crowdPackId(cpId).build())
                    .collect(Collectors.toList());
            adCoreBqf.insert(lauUnitTargetExtraCrowdPack).insertBeans(extraCrowdPacks);
        }
    }

    private LauUnitTargetCrowdPackPo buildTCPPo(Integer unitId, Integer cpId, Integer type) {
        LauUnitTargetCrowdPackPo po = new LauUnitTargetCrowdPackPo();

        po.setUnitId(unitId);
        po.setCrowdPackId(cpId);
        po.setType(type);
        po.setGroupId(LaunchConstant.UNIT_CROWD_PACK_GROUP_ID_DEFAULT);

        return po;
    }

    private LauUnitTargetCrowdPackPo buildTCPPo(Integer unitId, Integer cpId, Integer type, Integer groupId) {
        LauUnitTargetCrowdPackPo po = new LauUnitTargetCrowdPackPo();

        po.setUnitId(unitId);
        po.setCrowdPackId(cpId);
        po.setType(type);
        po.setGroupId(groupId);

        return po;
    }

    private List<LauUnitPo> validateUnitIds(Integer accountId, List<Integer> unitIds, Integer unitStatus) throws ServiceException {
        LauUnitPoExample example = new LauUnitPoExample();
        LauUnitPoExample.Criteria criteria = example.createCriteria();
        if (accountId != 0) {
            criteria.andAccountIdEqualTo(accountId);
        }
        criteria.andUnitIdIn(unitIds);

        List<LauUnitPo> pos = lauUnitDao.selectByExample(example);

        if (pos.size() != unitIds.size()) {
            throw new ServiceException(LaunchExceptionCode.UNIT_IDS_NO_PERMISSION_OPERATION);
        }

        pos = validateUpdateUnitStaus(pos, unitStatus);

        return pos;
    }

    private List<LauUnitPo> validateUpdateUnitStaus(List<LauUnitPo> pos, Integer unitStatus) throws ServiceException {
        switch (UnitStatus.getByCode(unitStatus)) {
            case PAUSED:
                boolean allPersonalFly = pos
                        .stream()
                        .allMatch(po -> Objects.equals(po.getBusinessDomain(), BusinessDomain.PERSONAL_FLY));
                //如果是个人起飞 则不校验
                if (allPersonalFly) {
                    return pos;
                }
                Pair<Boolean, LauUnitPo> pausedPair = LaunchUtil.ObjectKeyMustInList(pos, UnitStatus.TO_PAUSED_UNIT_STATUS_LIST, LauUnitPo::getUnitStatus);

                //起飞GD+单元不可暂停
                List<LauUnitPo> flyGdPlusUnits = pos.stream().filter(o -> IsValid.TRUE.getCode().equals(o.getIsGdPlus())).collect(Collectors.toList());
                flyGdUnitValidator.validatePause(flyGdPlusUnits);

                if (!pausedPair.getLeft()) {
                    Map<String, Object> extraMsg = new HashMap<>();
                    LauUnitPo lauUnitPo = pausedPair.getRight();
                    if (lauUnitPo != null && lauUnitPo.getUnitId() != null && lauUnitPo.getUnitName() != null) {
                        extraMsg.put(UNIT_ID, lauUnitPo.getUnitId());
                        extraMsg.put(UNIT_NAME, lauUnitPo.getUnitName());
                        throw new ServiceExtraException(LaunchExceptionCode.UNIT_CANNOT_TO_BE_PAUSED, extraMsg);
                    } else {
                        throw new ServiceException(LaunchExceptionCode.UNIT_CANNOT_TO_BE_PAUSED);
                    }
                }

                break;
            case VALID:
                Pair<Boolean, LauUnitPo> validPair = LaunchUtil.ObjectKeyMustInList(pos, UnitStatus.TO_VALID_UNIT_STATUS_LIST, LauUnitPo::getUnitStatus);
                // 非暂停状态的单元不能启用
                if (!validPair.getLeft()) {
                    Map<String, Object> extraMsg = new HashMap<>();
                    LauUnitPo lauUnitPo = validPair.getRight();
                    if (lauUnitPo != null && lauUnitPo.getUnitId() != null && lauUnitPo.getUnitName() != null) {
                        extraMsg.put(UNIT_ID, lauUnitPo.getUnitId());
                        extraMsg.put(UNIT_NAME, lauUnitPo.getUnitName());
                        throw new ServiceExtraException(LaunchExceptionCode.UNIT_CANNOT_TO_BE_VALID, extraMsg);
                    } else {
                        throw new ServiceException(LaunchExceptionCode.UNIT_CANNOT_TO_BE_VALID);
                    }
                }

                Map<Integer, com.bilibili.adp.cpc.biz.services.unit.bos.LauUnitGameBo> unitGameMap = adpCpcLauUnitGameService.getSubPkgMap(pos.stream().map(LauUnitPo::getUnitId).collect(Collectors.toList()));
                List<Integer> cpsGameBaseIds = unitGameMap.values().stream().filter(x -> AdpCpcGameService.CPS_PKG == x.getSubPkg()).map(com.bilibili.adp.cpc.biz.services.unit.bos.LauUnitGameBo::getGameBaseId).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(cpsGameBaseIds)) {
                    try {
                        // cps的情况下才需要做空包筛选
                        List<LauGameInfoPo> lauGameInfoPos = adpCpcGameService.fetchGames(cpsGameBaseIds);
                        Map<Integer, Integer> gameBaseIdEffectiveMap = lauGameInfoPos.stream().collect(Collectors.toMap(
                                LauGameInfoPo::getGameBaseId,
                                LauGameInfoPo::getSubPkgStatus
                        ));
                        pos = pos.stream().filter(x -> {
                            Integer gameBaseId = unitGameMap.get(x.getUnitId()).getGameBaseId();
                            Integer effective = gameBaseIdEffectiveMap.get(gameBaseId);
                            if (effective == 0) {
                                // cps空包，需要筛选掉
                                return false;
                            } else {
                                return true;
                            }
                        }).collect(Collectors.toList());
                    } catch (Exception e) {
                        log.error("validateUpdateUnitStatus, 批量更新unit状态为 valid 时报错 {}", e);
                    }
                }

                break;
            case FINISHED:
                Pair<Boolean, LauUnitPo> finishedPair = LaunchUtil.ObjectKeyMustNotInList(pos, UnitStatus.NOT_TO_FINISHED_UNIT_STATUS_LIST, LauUnitPo::getUnitStatus);
                // deleted 的不允许删除
                if (!finishedPair.getLeft()) {
                    Map<String, Object> extraMsg = new HashMap<>();
                    LauUnitPo lauUnitPo = finishedPair.getRight();
                    if (lauUnitPo != null && lauUnitPo.getUnitId() != null && lauUnitPo.getUnitName() != null) {
                        extraMsg.put(UNIT_ID, lauUnitPo.getUnitId());
                        extraMsg.put(UNIT_NAME, lauUnitPo.getUnitName());
                        throw new ServiceExtraException(LaunchExceptionCode.UNIT_CANNOT_TO_BE_FINISHED, extraMsg);
                    } else {
                        throw new ServiceException(LaunchExceptionCode.UNIT_CANNOT_TO_BE_FINISHED);
                    }
                }

                break;
            default:
                break;
        }
        return pos;
    }

    private Set<Integer> batchUpdateUnitValidStatus(Operator operator, List<Integer> unitIds) throws ServiceException {

        LauUnitPoExample example = new LauUnitPoExample();
        example.or()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andUnitIdIn(unitIds);

        List<LauUnitPo> pos = lauUnitDao.selectByExample(example);

        return batchUpdateValidStatus(pos, operator, Triple.from(Lists.newArrayList(), Lists.newArrayList(), Lists.newArrayList()));
    }

    public void batchUpdateValidStatus(List<LauUnitPo> pos, Triple gdPlusTriple) throws ServiceException {
        batchUpdateValidStatus(pos, null, gdPlusTriple);
    }

    public Set<Integer> batchUpdateValidStatus(List<LauUnitPo> pos, Operator operator,
                                               Triple<List<Integer>, List<Integer>, List<Integer>> gdPlusTriple) throws ServiceException {
        Set<Integer> result = new HashSet<>();

        if (CollectionUtils.isEmpty(pos)) {
            return result;
        }

        Map<Integer, BigDecimal> budgetMap = getUnitBudgetMapInUnitIds(pos);

        List<Integer> notStartIds = Lists.newArrayList();
        List<Integer> budgetExceedIds = Lists.newArrayList();
        List<Integer> validIds = Lists.newArrayList();
        List<Integer> notInLaunchTimeIds = Lists.newArrayList();

        List<Integer> gdPlusNoLaunchUnitIds = gdPlusTriple.getFirst();
        List<Integer> gdPlusLaunchingUnitIds = gdPlusTriple.getMiddle();
        List<Integer> gdPlusFinishLaunchUnitIds = gdPlusTriple.getLast();

        for (LauUnitPo po : pos) {

            boolean isStart;
            boolean isBudgetExceed;
            boolean isInLaunchTime;
            boolean isAheadInLaunchTime = false;
            boolean isFinished;

            //如果是GD+单元，根据flyGdPlusDelegate.get3UnitIds的GD+单元状态（待投放，投放中，投放完成）来更新单元状态
            if (IsValid.TRUE.getCode().equals(po.getIsGdPlus())) {
                isStart = !gdPlusNoLaunchUnitIds.contains(po.getUnitId());
                isBudgetExceed = false;
                isInLaunchTime = gdPlusLaunchingUnitIds.contains(po.getUnitId());
                isFinished = gdPlusFinishLaunchUnitIds.contains(po.getUnitId());
            } else {
                //非GD+单元，根据单元表的开始时间、结束时间来判断更新单元状态
                isStart = LaunchUtil.isUnitStarted(po.getLaunchBeginDate(), po.getLaunchTime());
                isBudgetExceed = isBudgetExceed(po, budgetMap);
                isInLaunchTime = LaunchUtil.isInLaunchTime(po.getLaunchBeginDate(), po.getLaunchEndDate(), po.getLaunchTime(), Utils.getNow());
                isAheadInLaunchTime = Objects.equals(po.getPromotionPurposeType(), PromotionPurposeType.LIVE_ROOM.getCode())
                        && LaunchUtil.isInLaunchTime(po.getLaunchBeginDate(), po.getLaunchEndDate(), po.getLaunchTime(), Utils.getSomeMinuteAfter(Utils.getNow(), launchTimeAheadMinutes));
                isFinished = LaunchUtil.isUnitFinished(po.getLaunchEndDate(), po.getLaunchTime());
            }

            // 开始时间未开始 && 状态不是未开始
            if (!isStart && po.getUnitStatus() != UnitStatus.NOT_START.getCode()) {
                notStartIds.add(po.getUnitId());
            }
            // 已经开始了 && 超预算了 && 当前不是超预算状态
            else if (isStart && isBudgetExceed && po.getUnitStatus() != UnitStatus.BUDGET_EXCEED.getCode()) {
                budgetExceedIds.add(po.getUnitId());
            }
            // 未超预算 & (在投放中间段内 || isAheadInLaunchTime) & 当前不是有效
            else if (!isBudgetExceed && (isInLaunchTime || isAheadInLaunchTime) && po.getUnitStatus() != UnitStatus.VALID.getCode()) {
                validIds.add(po.getUnitId());
            }
            // 开始了 & 未超预算 & 不在投放时间段 & 未结束 & 当前不是【不在投放时段】状态
            else if (isStart && !isBudgetExceed && !isInLaunchTime && !isFinished && po.getUnitStatus() != UnitStatus.NOT_IN_LAUNCH_TIME.getCode()) {
                notInLaunchTimeIds.add(po.getUnitId());
            }
        }

        LOGGER.info("batchUpdateUnitValidStatus not start:{}, budget exceed:{}, valid:{}, not in launch time:{}.", notStartIds.size(), budgetExceedIds.size(), validIds.size(), notInLaunchTimeIds.size());

        if (operator != null) {
            batchUpdateStatus(operator, notStartIds, LaunchStatus.START.getCode(), UnitStatus.NOT_START.getCode());
            batchUpdateStatus(operator, budgetExceedIds, LaunchStatus.START.getCode(), UnitStatus.BUDGET_EXCEED.getCode());
            batchUpdateStatus(operator, validIds, LaunchStatus.START.getCode(), UnitStatus.VALID.getCode());
            batchUpdateStatus(operator, notInLaunchTimeIds, LaunchStatus.START.getCode(), UnitStatus.NOT_IN_LAUNCH_TIME.getCode());
        } else {
            batchUpdateStatus(notStartIds, LaunchStatus.START.getCode(), UnitStatus.NOT_START.getCode());
            batchUpdateStatus(budgetExceedIds, LaunchStatus.START.getCode(), UnitStatus.BUDGET_EXCEED.getCode());
            batchUpdateStatus(validIds, LaunchStatus.START.getCode(), UnitStatus.VALID.getCode());
            batchUpdateStatus(notInLaunchTimeIds, LaunchStatus.START.getCode(), UnitStatus.NOT_IN_LAUNCH_TIME.getCode());

            sendBudgetExceedMsg(budgetExceedIds);
        }

        result.addAll(notStartIds);
        result.addAll(budgetExceedIds);
        result.addAll(validIds);
        result.addAll(notInLaunchTimeIds);
        return result;
    }

    private Map<Integer, BigDecimal> getUnitBudgetMapInUnitIds(List<LauUnitPo> pos) {
        Map<Integer, BigDecimal> dResult = Maps.newHashMap();

        List<Integer> dayBudgetUnitIds = pos
                .stream()
                .map(LauUnitPo::getUnitId)
                .collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(dayBudgetUnitIds)) {
            Timestamp today = Utils.getToday();
            List<StatUnitDto> dayBudgetDtos;
            try {
                dayBudgetDtos = statUnitService.getInUnitIdsGroupByTime(dayBudgetUnitIds, today, Utils.getEndOfDay(today));
            } catch (Exception e) {
                LOGGER.error("getUnitBudgetMapInUnitIdsError", e);
                return Collections.emptyMap();
            }

            if (!CollectionUtils.isEmpty(dayBudgetDtos)) {
                dayBudgetDtos.forEach(dto -> {
                    dResult.put(dto.getUnitId(), dto.getCost());
                });
            }
        }

        return dResult;
    }

    boolean isBudgetExceed(LauUnitPo unit, Map<Integer, BigDecimal> budgetMap) {
        long unitBudget = unit.getBudget();
        long actualBudget = Utils.fromYuanToFen(budgetMap.get(unit.getUnitId()));

        return unitBudget <= actualBudget;
    }

    Set<Integer> batchUpdateStatus(Operator operator, List<Integer> unitIds, Integer status, Integer unitStatus) {
        LOGGER.info("batchUpdateStatus unitIds:{}, status:{}, unitStatus:{}", JSONObject.toJSONString(unitIds), status, unitStatus);
        CollectionHelper.processInBatches(unitIds, 1000, ids -> {
            LauUnitPoExample example = new LauUnitPoExample();
            LauUnitPoExample.Criteria criteria = example.or()
                    .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                    .andUnitIdIn(ids);
            if (operator.getOperatorId() != 0) {
                criteria.andAccountIdEqualTo(operator.getOperatorId());
            }

            LauUnitPo po = new LauUnitPo();
            po.setStatus(status);
            po.setUnitStatus(unitStatus);

            lauUnitDao.updateByExampleSelective(po, example);
        });

        logOperateService.addBatchUpdateStatusLog(DbTable.LAU_UNIT, operator,
                CpcUnitDto.builder().status(status).unitStatus(unitStatus).build(), unitIds);

        if (UnitStatus.VALID.getCode() != status) {
            unitAccelerateService.finishUnitAccelerate(unitIds, operator);
        }
        return new HashSet<>(unitIds);
    }

    void batchUpdateStatus(List<Integer> unitIds, Integer status, Integer unitStatus) {
        Timestamp now = Utils.getNow();
        CollectionHelper.processInBatches(unitIds, 1000, ids -> {
            LauUnitPoExample example = new LauUnitPoExample();
            example.or()
                    .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                    .andUnitIdIn(ids);

            LauUnitPo po = new LauUnitPo();
            po.setStatus(status);
            po.setUnitStatus(unitStatus);
            po.setUnitStatusMtime(now);

            lauUnitDao.updateByExampleSelective(po, example);
        });

        if (UnitStatus.VALID.getCode() != status) {
            unitAccelerateService.finishUnitAccelerate(unitIds, null);
        }
    }

    private void withBudgetRemainingModifyTimes(List<CpcUnitDto> dtos) {
        if (CollectionUtils.isEmpty(dtos)) {
            return;
        }
        List<Integer> unitIds = dtos.stream().map(CpcUnitDto::getUnitId).collect(Collectors.toList());
        Map<Integer, Long> budgetRecordCountMap = unitBudgetCountComponent.getBudgetRecordCountMapInUnitIds(unitIds);

        for (CpcUnitDto dto : dtos) {
            if (budgetRecordCountMap.containsKey(dto.getUnitId())) {
                dto.setBudgetRemainingModifyTimes(LaunchConstant.UNIT_BUDGET_UPDATE_LIMIT -
                        budgetRecordCountMap.getOrDefault(dto.getUnitId(), 0L));
            }
        }
    }

    private void withBeforeBudget(List<CpcUnitDto> dtos, QueryCpcUnitDto query) {
        if (CollectionUtils.isEmpty(dtos)) {
            return;
        }
        List<Integer> unitIds = dtos.stream().map(CpcUnitDto::getUnitId).collect(Collectors.toList());
        Map<Integer, BigDecimal> beforeBudgetMap = lauUnitService.getUnitBudgetMapInIdsAndTime(unitIds, this.getQueryTime(query.getBeginDate(), query.getEndDate()));

        for (CpcUnitDto dto : dtos) {
            dto.setBeforeBudget(beforeBudgetMap.getOrDefault(dto.getUnitId(), BigDecimal.ZERO));
        }
    }

    private void withAppPackge(List<CpcUnitDto> dtos) {
        if (CollectionUtils.isEmpty(dtos)) {
            return;
        }
        List<Integer> appPackageIds = dtos
                .stream()
                .filter(dto -> dto.getPromotionPurposeType() == PromotionPurposeType.APP_DOWNLOAD.getCode())
                .map(CpcUnitDto::getAppPackageId)
                .distinct()
                .collect(Collectors.toList());

        Map<Integer, AppPackageDto> appPackageMap = appPackageService.getAllMapInPrimaryKeys(appPackageIds);

        for (CpcUnitDto dto : dtos) {
            dto.setAppInfo(appPackageMap.get(dto.getAppPackageId()));
        }
    }

    private void withSlotGroupDto(List<CpcUnitDto> dtos) {
        if (CollectionUtils.isEmpty(dtos)) {
            return;
        }
        List<Integer> slotGroupIds = dtos
                .stream()
                .map(CpcUnitDto::getSlotGroup)
                .distinct()
                .collect(Collectors.toList());

        List<SlotGroupSimpleDto> slotGroups = resSlotGroupService.getSlotGroupByIds(slotGroupIds);
        Map<Integer, SlotGroupSimpleDto> slotGroupMap = slotGroups.stream()
                .collect(Collectors.toMap(SlotGroupSimpleDto::getId, sg -> sg, Utils::first));

        for (CpcUnitDto dto : dtos) {
            dto.setSlotGroupDto(slotGroupMap.get(dto.getSlotGroup()));
        }
    }

    private void withCampaign(List<CpcUnitDto> dtos) {
        if (CollectionUtils.isEmpty(dtos)) {
            return;
        }
        List<Integer> campaignIds = dtos.stream().filter(dto -> dto.getCampaign() == null)
                .map(CpcUnitDto::getCampaignId).distinct().collect(Collectors.toList());
        Map<Integer, CpcCampaignDto> campaignMap = cpcCampaignService.getCampaignMapInCampaignIds(campaignIds);

        for (CpcUnitDto dto : dtos) {
            if (dto.getCampaign() == null) {
                dto.setCampaign(campaignMap.get(dto.getCampaignId()));
            }
        }
    }

    private void withGdPlus(List<CpcUnitDto> dtos) {
        if (CollectionUtils.isEmpty(dtos)) {
            return;
        }
        List<Integer> unitIds = dtos.stream().map(CpcUnitDto::getUnitId).distinct().collect(Collectors.toList());
        Map<Integer, String> unitMap = flyGdPlusDelegate.queryUnitsScheduleStrs(unitIds);

        for (CpcUnitDto dto : dtos) {
            dto.setGdPlusLaunchDate(unitMap.get(dto.getUnitId()));
        }
    }

    public void queryPostHandle(QueryCpcUnitDto query, List<CpcUnitDto> dtos) {
        if (CollectionUtils.isEmpty(dtos)) {
            return;
        }
        List<Integer> targetPackageUpgradeIds = dtos.stream()
                .map(CpcUnitDto::getTargetPackageId)
                .filter(Utils::isPositive)
                .distinct()
                .collect(Collectors.toList());
        Set<Integer> notHasTargetUpgradeUnitIds = dtos.stream()
                .filter(dto -> !Utils.isPositive(dto.getTargetPackageId()))
                .map(CpcUnitDto::getUnitId)
                .collect(Collectors.toSet());

        if (query.isWithCampaign()) {
            withCampaign(dtos);
        }

        if (query.isWithBudgetRemainingModifyTimes()) {
            withBudgetRemainingModifyTimes(dtos);
        }

        if (query.isWithBeforeBudget()) {
            withBeforeBudget(dtos, query);
        }

        if (query.isWithAppPackge()) {
            withAppPackge(dtos);
        }

        if (query.isWithSlotGroupDto()) {
            withSlotGroupDto(dtos);
        }

        if (query.isNeedParseEndDate()) {
            for (CpcUnitDto dto : dtos) {
                if (LaunchUtil.getDays(dto.getLaunchEndDate(), dto.getLaunchBeginDate()) >= LaunchConstant.MAX_UNIT_LAUNCH_DAY_LENGTH) {
                    dto.setLaunchEndDate("-");
                }
            }
        }

        if (query.isWithGdPlus()) {
            withGdPlus(dtos);
        }
        withGameCardInfos(dtos);
        if (query.isWithExtraTarget()) {
            Map<Integer, CpcUnitExtraTargetDto> extraUpgradeTargetMap = getExtraUpgradeTargetMap(targetPackageUpgradeIds);
            Map<Integer, CpcUnitExtraTargetDto> extraTargetMap = getExtraTargetMap(Lists.newArrayList(notHasTargetUpgradeUnitIds));
            dtos.forEach(dto -> {
                if (notHasTargetUpgradeUnitIds.contains(dto.getUnitId())) {
                    dto.setExtraTarget(extraTargetMap.get(dto.getUnitId()));
                } else {
                    dto.setExtraTarget(extraUpgradeTargetMap.get(dto.getTargetPackageId()));
                }
            });
        }
        if (query.isWithTargetRules()) {
            final Map<Integer, List<TargetRule>> map = unitTargetRuleService.getTargetRulesMapInUnitIds(new ArrayList<>(notHasTargetUpgradeUnitIds));
            Map<Integer, List<TargetRule>> upgradeMap = resTargetPackageRuleUpgradeService.getTargetRulesMapInResTargetPackageIds(targetPackageUpgradeIds);
            for (CpcUnitDto dto : dtos) {
                if (notHasTargetUpgradeUnitIds.contains(dto.getUnitId())) {
                    dto.setTargetRules(map.get(dto.getUnitId()));
                } else {
                    dto.setTargetRules(upgradeMap.get(dto.getTargetPackageId()));
                }
            }
        }

        // 拼接 video id
        if (query.isWithVideoId()) {
            List<Integer> unitIds = dtos.stream().map(t -> t.getUnitId()).collect(Collectors.toList());
            LauUnitArchiveVideoPoExample example = new LauUnitArchiveVideoPoExample();
            example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andUnitIdIn(unitIds);
            List<LauUnitArchiveVideoPo> pos = unitArchiveVideoDao.selectByExample(example);
            Map<Integer, Long> videoMap = pos.stream().collect(Collectors.toMap(t -> t.getUnitId(), t -> t.getVideoId()));
            dtos.stream().forEach(t -> {
                t.setVideoId(videoMap.getOrDefault(t.getUnitId(), 0L));
            });
        }

        // 拼接 图片
        if (query.isWithPriceCoefficient()) {
            List<Integer> unitIds = dtos.stream().map(CpcUnitDto::getUnitId).collect(Collectors.toList());


            List<LauUnitExtraPo> pos = adCoreBqf.selectFrom(lauUnitExtra)
                    .where(lauUnitExtra.unitId.in(unitIds))
                    .fetch();
            Map<Integer, Integer> priceMap = pos.stream().collect(Collectors.toMap(LauUnitExtraPo::getUnitId, LauUnitExtraPo::getSearchFirstPriceCoefficient));
            dtos.forEach(t -> {
                t.setSearchFirstPriceCoefficient(priceMap.getOrDefault(t.getUnitId(), 0));
            });
        }
    }

    private void withGameCardInfos(List<CpcUnitDto> dtos) {
        List<Integer> needGameInfoUnitIds = new ArrayList<>();
        List<Integer> needGameCardTargetInfoUnitIds = new ArrayList<>();
        dtos.forEach(dto -> {
            if (Objects.equals(PromotionPurposeType.GAME_CARD.getCode(), dto.getPromotionPurposeType())) {
                needGameInfoUnitIds.add(dto.getUnitId());
                needGameCardTargetInfoUnitIds.add(dto.getUnitId());
            }
            if (Objects.equals(PromotionPurposeType.GAME_ACTIVITY_CARD.getCode(), dto.getPromotionPurposeType())) {
                needGameCardTargetInfoUnitIds.add(dto.getUnitId());
            }
        });

        Map<Integer, LauUnitGameDo> unitGameMap = adCoreBqf
                .selectFrom(lauUnitGame)
                .where(lauUnitGame.unitId.in(needGameInfoUnitIds))
                .fetch(LauUnitGameDo.class)
                .stream()
                .collect(Collectors.toMap(LauUnitGameDo::getUnitId, Function.identity()));
        Map<Integer, List<Long>> tagTargetMap = adCoreBqf
                .select(lauUnitTargetTag.unitId, lauUnitTargetTag.tagId)
                .from(lauUnitTargetTag)
                .where(lauUnitTargetTag.unitId.in(needGameCardTargetInfoUnitIds))
                .where(lauUnitTargetTag.isDeleted.eq(IsDeleted.VALID.getCode()))
                .fetch()
                .stream()
                .collect(Collectors.groupingBy(t -> t.get(lauUnitTargetTag.unitId), Collectors.mapping(t -> t.get(lauUnitTargetTag.tagId), Collectors.toList())));
        Map<Integer, List<Long>> archiveTargetMap = adBqf
                .select(lauUnitTargetArchive.unitId, lauUnitTargetArchive.avid)
                .from(lauUnitTargetArchive)
                .where(lauUnitTargetArchive.unitId.in(needGameCardTargetInfoUnitIds))
                .where(lauUnitTargetArchive.isDeleted.eq(IsDeleted.VALID.getCode()))
                .fetch()
                .stream()
                .collect(Collectors.groupingBy(t -> t.get(lauUnitTargetArchive.unitId), Collectors.mapping(t -> t.get(lauUnitTargetArchive.avid), Collectors.toList())));

        dtos.forEach(dto -> {
            Optional<LauUnitGameDo> unitGameOptional = Optional.ofNullable(unitGameMap.get(dto.getUnitId()));
            unitGameOptional.ifPresent(unitGame -> {
                dto.setGameBaseId(unitGame.getGameBaseId());
                dto.setGamePlatformType(unitGame.getPlatformType());
                GameDto game = gameCenterService.getGameDtoByIdPlatform(unitGame.getGameBaseId(), unitGame.getPlatformType());
                dto.setGameDto(game);
            });
            dto.setGameCardUnitTargets(GameCardUnitTargetDto.builder().build());
            Optional<List<Long>> tagsOptional = Optional.ofNullable(tagTargetMap.get(dto.getUnitId()));
            tagsOptional.ifPresent(tagIds -> {
                List<UnitTargetRuleTagDto> tagTargets = mainCommunityTagProxy.getTagsMap(tagIds)
                        .values()
                        .stream()
                        .map(tag -> UnitTargetRuleTagDto.builder()
                                .tagId(tag.getId())
                                .tagName(tag.getName())
                                .build())
                        .collect(Collectors.toList());
                dto.getGameCardUnitTargets().setTag(tagTargets);
            });

            Optional<List<Long>> archivesOptional = Optional.ofNullable(archiveTargetMap.get(dto.getUnitId()));
            archivesOptional.ifPresent(avids -> {
                List<UnitTargetRuleArchiveDto> archiveTargets = avids
                        .stream()
                        .map(avid -> UnitTargetRuleArchiveDto.builder()
                                .avid(avid)
                                .bvid(BVIDUtils.avToBv(avid))
                                .build())
                        .collect(Collectors.toList());
                dto.getGameCardUnitTargets().setArchive(archiveTargets);
            });
        });
    }


    private Timestamp getQueryTime(String beginDate, String endDate) {
        if (Strings.isNullOrEmpty(beginDate) || Strings.isNullOrEmpty(endDate)) {
            return null;
        }

        Timestamp startTime = Utils.getTimestamp(beginDate);
        Timestamp endTime = Utils.getTimestamp(endDate);

        if (Utils.getBeginOfDay(startTime).compareTo(Utils.getBeginOfDay(endTime)) == 0) {
            return Utils.getEndOfDay(startTime);
        } else {
            return null;
        }
    }

    private void setCrowdPackIds(CpcUnitDto unit) {
        Map<Integer, Map<Integer, List<Integer>>> cpMap = lauUnitService.getCrowdPackIdsMapByTypeAndGroupId(unit.getUnitId());

        List<List<Integer>> includeCpIdsGroup = Lists.newArrayList(cpMap.getOrDefault(UnitTargetCrowdPackType.INCLUDE.getCode(), Collections.emptyMap()).values());

        if (CollectionUtils.isEmpty(includeCpIdsGroup)) {
            unit.setCrowdPackIds(Collections.emptyList());
            unit.setOtherCrowdPackIdsGroup(Arrays.asList(Collections.emptyList()));
        } else {
            unit.setCrowdPackIds(includeCpIdsGroup.get(LaunchConstant.UNIT_CROWD_PACK_GROUP_ID_DEFAULT));

            includeCpIdsGroup.remove(LaunchConstant.UNIT_CROWD_PACK_GROUP_ID_DEFAULT);
            unit.setOtherCrowdPackIdsGroup(includeCpIdsGroup);
        }

        unit.setExcludeCrowdPackIds(cpMap.getOrDefault(UnitTargetCrowdPackType.EXCLUDE.getCode(), Collections.emptyMap())
                .getOrDefault(LaunchConstant.UNIT_CROWD_PACK_GROUP_ID_DEFAULT, Collections.emptyList()));
        //填充 扩展种子人群包
        List<Integer> extraCrowdPackIds = adCoreBqf
                .select(lauUnitTargetExtraCrowdPack.crowdPackId)
                .from(lauUnitTargetExtraCrowdPack)
                .where(lauUnitTargetExtraCrowdPack.unitId.eq(unit.getUnitId()))
                .fetch();
        unit.setExtraCrowdPackIds(extraCrowdPackIds);
    }

    public LauUnitPo getById(Integer unitId) {
        Assert.notNull(unitId, "单元ID不可为空");

        LauUnitPo po = lauUnitDao.selectByPrimaryKey(unitId);

        Assert.notNull(po, "单元不存在");

        return po;
    }

    public List<LauUnitPo> getByIds(List<Integer> unitIds) {
        Assert.notNull(unitIds, "单元ID不可为空");
        LauUnitPoExample example = new LauUnitPoExample();
        example.createCriteria().andUnitIdIn(unitIds);
        List<LauUnitPo> lauUnitPos = lauUnitDao.selectByExample(example);

        Assert.notNull(lauUnitPos, "单元不存在");

        return lauUnitPos;
    }

    public List<LauUnitPo> getLauUnitPosByCampaignId(Integer campaignId) {
        LauUnitPoExample example = new LauUnitPoExample();
        example.or()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andCampaignIdEqualTo(campaignId);
        return lauUnitDao.selectByExample(example);
    }

    private final static Integer GD_PLUS_FAIL_MSG_LENGTH = 64;

    @Transactional(value = AD_CORE_TM, rollbackFor = Exception.class)
    public Integer updateUnitGdPlusFinishFlagAndScheduleMapping(LauUnitPo po, GdPlusFinishFlagEnum toFinishFlag, String reason,
                                                                List<Timestamp> deleteScheduleDates,
                                                                List<FlyGdPlusUnitScheduleMappingDto> flyGdPlusUnitScheduleMappingDtoList,
                                                                Operator operator) {
        Assert.notNull(po, "未查询到单元信息");
        Assert.isTrue(toFinishFlag.checkValidFromStatus(po.getGdPlusFinishFlag())
                , "状态翻转失败 " + po.getGdPlusFinishFlag() + " ->" + toFinishFlag.getCode());
        LauUnitPo updatePo = LauUnitPo.builder()
                .gdPlusFinishFlag(toFinishFlag.getCode())
                .gdPlusFailMsg(reason == null
                        ? "" : (reason.length() > GD_PLUS_FAIL_MSG_LENGTH
                        ? reason.substring(0, GD_PLUS_FAIL_MSG_LENGTH) : reason))
                .build();
        //是新建当天的单元，更新预算
        if (Integer.valueOf(1).equals(po.getGdPlusIsToday()) && GdPlusFinishFlagEnum.CREATED == toFinishFlag) {
            if (!CollectionUtils.isEmpty(flyGdPlusUnitScheduleMappingDtoList)) {
                updatePo.setBudget(flyGdPlusUnitScheduleMappingDtoList.get(0).getPrice());
            }
        }
        LauUnitPoExample example = new LauUnitPoExample();
        example.or()
                .andUnitIdEqualTo(po.getUnitId())
                .andGdPlusFinishFlagEqualTo(po.getGdPlusFinishFlag())
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        lauUnitDao.updateByExampleSelective(updatePo, example);

        //创建成功，执行删除,新增单元关联排期
        if (GdPlusFinishFlagEnum.CREATED == toFinishFlag) {
            //先查出来
            String oldValue = this.generateLogValue(po.getUnitId());

            //执行操作
            if (!CollectionUtils.isEmpty(deleteScheduleDates)) {
                flyGdPlusDelegate.deleteByUnitIdAndDate(po.getUnitId(), deleteScheduleDates);
            }
            if (!CollectionUtils.isEmpty(flyGdPlusUnitScheduleMappingDtoList)) {
                flyGdPlusDelegate.batchInsertData(this.flyGdPlusMappingDos2Dtos(flyGdPlusUnitScheduleMappingDtoList));
            }

            //再查出来
            String newValue = this.generateLogValue(po.getUnitId());

            List<DiffItem> diffs = Lists.newArrayList();
            diffs.add(DiffItem.builder()
                    .key("schedule")
                    .desc("排期")
                    .oldValue(oldValue)
                    .newValue(newValue)
                    .build());

            //写日志
            this.insertScheduleLog(operator, po.getUnitId(), diffs);
        }
        return 1;
    }

    private String generateLogValue(Integer unitId) {
        List<FlyGdPlusUnitScheduleMappingPo> data = flyGdPlusDelegate.queryValidSchedulePosByUnitIdOrderByScheduleDate(unitId);
        StringBuilder value = new StringBuilder();
        if (CollectionUtils.isEmpty(data)) {
            return value.toString();
        }
        for (FlyGdPlusUnitScheduleMappingPo o : data) {
            String date = TimeUtils.getTimestampStr(o.getScheduleDate());
            Long cpm = o.getShowCpm();
            value.append(date + ":" + cpm + "cpm,");
        }
        return value.substring(0, value.length() - 1);
    }

    private final static Integer BILI_USER_NAME_LENGTH = 32;

    private final static Integer OPERATOR_NANE_LENGTH = 32;

    private void insertScheduleLog(Operator operator, Integer unitId, List<DiffItem> diffs) {
        LogCpcOperationBo bo = LogCpcOperationBo.builder()
                .operatorUsername(operator.getOperatorName() == null
                        ? "" : (operator.getOperatorName().length() > OPERATOR_NANE_LENGTH
                        ? operator.getOperatorName().substring(0, OPERATOR_NANE_LENGTH) : operator.getOperatorName()))
                .operatorType(operator.getOperatorType().getCode())
                .tableName("lau_unit")
                .type(StringUtils.isEmpty(diffs.get(0).getOldValue()) ?
                        OperationType.INSERT.getCode() : OperationType.UPDATE.getCode())
                .value(JSONObject.toJSONString(diffs))
                .objId(unitId.longValue())
                .bilibiliUsername(operator.getBilibiliUserName() == null
                        ? "" : (operator.getBilibiliUserName().length() > BILI_USER_NAME_LENGTH
                        ? operator.getBilibiliUserName().substring(0, BILI_USER_NAME_LENGTH) : operator.getBilibiliUserName()))
                .accountId(operator.getOperatorId() == null ? 0 : operator.getOperatorId())
                .build();
        logCpcOperationService.insert(bo);
    }

    private List<FlyGdPlusUnitScheduleMappingPo> flyGdPlusMappingDos2Dtos(List<FlyGdPlusUnitScheduleMappingDto> dtos) {
        if (CollectionUtils.isEmpty(dtos)) {
            return Collections.emptyList();
        }
        return dtos.stream().map(dto -> {
            FlyGdPlusUnitScheduleMappingPo po = new FlyGdPlusUnitScheduleMappingPo();
            BeanUtils.copyProperties(dto, po);
            return po;
        }).collect(Collectors.toList());
    }

    public Map<Integer, Map<Integer, List<Integer>>> getAccountId2ShopGoodsId2UnitIdsMapByUnitId(List<Integer> haveShopGoodsUnitIds) {
        if (CollectionUtils.isEmpty(haveShopGoodsUnitIds)) {
            return Collections.emptyMap();
        }

        List<LauUnitShopGoodsPo> unitShopGoodsPos = CollectionHelper.callInBatches(haveShopGoodsUnitIds, 100, ids -> {
            LauUnitShopGoodsPoExample example = new LauUnitShopGoodsPoExample();
            example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                    .andUnitIdIn(ids);
            return lauUnitShopGoodsDao.selectByExample(example);
        });

        if (CollectionUtils.isEmpty(unitShopGoodsPos)) {
            return Collections.emptyMap();
        }

        return unitShopGoodsPos.stream().collect(Collectors.groupingBy(LauUnitShopGoodsPo::getAccountId, Collectors.groupingBy(LauUnitShopGoodsPo::getGoodsId, Collectors.mapping(LauUnitShopGoodsPo::getUnitId, Collectors.toList()))));
    }

    public List<LauUnitShopGoodsPo> getShopGoodsPoListByStartId(Integer startId) {
        LauUnitShopGoodsPoExample example = new LauUnitShopGoodsPoExample();
        example.or().andIdGreaterThan(startId);
        example.setOrderByClause("id asc");
        example.setLimit(1000);
        return lauUnitShopGoodsDao.selectByExample(example);
    }

    public List<Integer> getUnitIdsByGoodsId(Integer goodsId, Integer startUnitId) {
        LauUnitShopGoodsPoExample exm = new LauUnitShopGoodsPoExample();
        exm.or().andGoodsIdEqualTo(goodsId)
                .andUnitIdGreaterThan(startUnitId)
                .andIsDeletedEqualTo(0);
        exm.setOrderByClause("unit_id asc");
        exm.setLimit(100);
        List<LauUnitShopGoodsPo> pos = lauUnitShopGoodsDao.selectByExample(exm);
        return pos.stream()
                .map(LauUnitShopGoodsPo::getUnitId)
                .collect(Collectors.toList());
    }

    public Map<Integer, CpcLightUnitDto> queryLightUnitMap(QueryCpcUnitDto query) {
        List<CpcLightUnitDto> pos = getCpcUnitQuery(query).fetch(CpcLightUnitDto.class);

        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyMap();
        }

        return pos.stream().collect(Collectors.toMap(CpcLightUnitDto::getUnitId, Function.identity()));
    }

    public List<CpcLightUnitDto> queryLightUnits(QueryCpcUnitDto query) {
        return getCpcUnitQuery(query).fetch(CpcLightUnitDto.class);
    }

    public PageResult<CpcLightUnitDto> queryLightUnitByPage(QueryCpcUnitDto query) {
        BaseQuery<com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauUnitPo> baseQuery = getCpcUnitQuery(query);
        List<CpcLightUnitDto> resultDtos = baseQuery.fetch(CpcLightUnitDto.class);
        BaseQuery<com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauUnitPo> countQuery = getCpcUnitQuery(query);
        Long total = countQuery.fetchCount();
        return PageResult.<CpcLightUnitDto>builder().records(resultDtos).total(total.intValue()).build();
    }

    public List<LauUnitPo> getPosInIds(List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }

        LauUnitPoExample example = new LauUnitPoExample();
        example.or()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andUnitIdIn(ids);

        List<LauUnitPo> pos = lauUnitDao.selectByExample(example);

        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyList();
        }

        return pos;
    }

    private Map<Integer, LauUnitPo> getLightPoMapInIds(List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyMap();
        }

        List<LauUnitPo> pos = CollectionHelper.callInBatches(ids, LaunchConstant.QUERY_BATCH_SIZE, idList -> {
            LauUnitPoExample example = new LauUnitPoExample();
            example.or()
                    .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                    .andUnitIdIn(idList);

            return extLauUnitDao.queryLightByExample(example);
        });

        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyMap();
        }

        return pos.stream().collect(Collectors.toMap(LauUnitPo::getUnitId, u -> u));
    }

    private List<LauUnitPo> getLightPosInIds(List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }

        List<LauUnitPo> pos = CollectionHelper.callInBatches(ids, LaunchConstant.QUERY_BATCH_SIZE, idList -> {
            LauUnitPoExample example = new LauUnitPoExample();
            example.or()
                    .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                    .andUnitIdIn(idList);

            return extLauUnitDao.queryLightByExample(example);
        });

        return pos;
    }

    protected Map<Integer, Map<Integer, List<Integer>>> getAccountId2GameBaseId2UnitIdsMap(List<Integer> haveGameUnitIds) {
        if (CollectionUtils.isEmpty(haveGameUnitIds)) {
            return Collections.emptyMap();
        }

        List<LauUnitGamePo> unitGamePos = CollectionHelper.callInBatches(haveGameUnitIds, 100, uids -> {
            LauUnitGamePoExample example = new LauUnitGamePoExample();
            example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                    .andUnitIdIn(uids);
            return lauUnitGameDao.selectByExample(example);
        });

        return unitGamePos.stream().collect(Collectors.groupingBy(LauUnitGamePo::getAccountId,
                Collectors.groupingBy(LauUnitGamePo::getGameBaseId,
                        Collectors.mapping(LauUnitGamePo::getUnitId, Collectors.toList()))));
    }

    private LauUnitArchiveVideoPo getArchiveInfoBeforeSaveArchiveContent(Long videoId) throws ServiceException {
        ArchiveDetail archiveDetail = archiveManager.queryArchivesByAid(videoId);
        Long realMid = Optional.ofNullable(archiveDetail).map(p -> p.getArchive())
                .map(p -> p.getMid()).orElse(0L);
        Integer subTid = Optional.ofNullable(archiveDetail).map(p -> p.getArchive())
                .map(p -> p.getTid()).orElse(0);

        List<ArchiveVideoInfoDetailDto> archiveVideoInfoDetailDtos = passportService.getAllArchiveInfoByAids(Arrays.asList(videoId));
        Long cid = 0L;
        if (!CollectionUtils.isEmpty(archiveVideoInfoDetailDtos)) {
            ArchiveVideoInfoDetailDto archiveVideoInfoDetailDto = archiveVideoInfoDetailDtos.get(0);
            cid = Optional.ofNullable(archiveVideoInfoDetailDto).map(p -> p.getCid()).orElse(0L);
        }

        String realNickName = "";
        if (realMid > 0) {
            UserInfoDto userInfoDto = passportService.getUserByMid(realMid);
            realNickName = Optional.ofNullable(userInfoDto).map(p -> p.getName())
                    .orElse("").replaceAll("[^\\u0000-\\uFFFF]", "");
            if (realNickName.length() > 64) {
                realNickName = realNickName.substring(0, 64);
            }
        }
        return LauUnitArchiveVideoPo.builder()
                .realMid(realMid)
                .subTid(subTid)
                .cid(cid)
                .realNickname(realNickName)
                .build();
    }

    @SneakyThrows
    public void saveArchiveContent(Integer unitId, Long videoId) {
        final LauUnitArchiveVideoPo po = this.getArchiveInfoBeforeSaveArchiveContent(videoId);
        po.setUnitId(unitId);
        po.setMid(po.getRealMid());
        po.setVideoId(videoId);
        po.setLaunchVideoType(LaunchVideoType.BUSINESS.getCode());
        po.setIsDeleted(IsDeleted.VALID.getCode());
        po.setVideoFrom(MATERIAL_VIDEO_FROM);
        po.setVideoPage(MATERIAL_VIDEO_PAGE);
        po.setHeadLink("https://space.bilibili.com/" + po.getRealMid());
        launchUnitService.saveUnitArchive(po);
    }

    private void saveArchiveContent(Integer unitId, Long mid, Long videoId, PromotionPurposeType uppt,
                                    Integer launchVideoType, boolean isReserveLiveRoom) throws ServiceException {
        if (PromotionPurposeType.ARCHIVE_CONTENT.equals(uppt) ||
                (PromotionPurposeType.LIVE_ROOM.equals(uppt) && isReserveLiveRoom)) {
            LOGGER.info("saveArchiveContent unitId:{}, mid:{}, videoId:{}, launchVideoType:{}", unitId, mid, videoId, launchVideoType);

            Assert.notNull(mid, "mid不能为空");
            Assert.notNull(videoId, "视频id不能为空");
            Assert.notNull(launchVideoType, "投放视频类型不能为空");

            //存单元-稿件前获取稿件信息
            LauUnitArchiveVideoPo archiveInfo = this.getArchiveInfoBeforeSaveArchiveContent(videoId);

            LauUnitArchiveVideoPo po = new LauUnitArchiveVideoPo();
            po.setUnitId(unitId);
            po.setMid(mid);
            po.setVideoId(videoId);
            po.setLaunchVideoType(launchVideoType);
            po.setIsDeleted(IsDeleted.VALID.getCode());
            po.setVideoFrom(MATERIAL_VIDEO_FROM);
            po.setVideoPage(MATERIAL_VIDEO_PAGE);

            po.setRealMid(archiveInfo.getRealMid());
            po.setSubTid(archiveInfo.getSubTid());
            po.setHeadLink("https://space.bilibili.com/" + archiveInfo.getRealMid());
            po.setCid(archiveInfo.getCid());
            po.setRealNickname(archiveInfo.getRealNickname());

            unitArchiveVideoDao.insertUpdateSelective(po);
        }
    }

    public void saveArchiveContentByUnitIds(List<Integer> unitIds, Long videoId, Integer launchVideoType) throws ServiceException {

        Assert.notNull(videoId, "视频id不能为空");
        Assert.notNull(launchVideoType, "投放视频类型不能为空");

        //存单元-稿件前获取稿件信息
        LauUnitArchiveVideoPo archiveInfo = this.getArchiveInfoBeforeSaveArchiveContent(videoId);

        LauUnitArchiveVideoPo po = new LauUnitArchiveVideoPo();
        po.setVideoId(videoId);
        po.setLaunchVideoType(launchVideoType);

        po.setRealMid(archiveInfo.getRealMid());
        po.setSubTid(archiveInfo.getSubTid());
        po.setHeadLink("https://space.bilibili.com/" + archiveInfo.getRealMid());
        po.setCid(archiveInfo.getCid());
        po.setRealNickname(archiveInfo.getRealNickname());

        LauUnitArchiveVideoPoExample example = new LauUnitArchiveVideoPoExample();
        example.or().andUnitIdIn(unitIds)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());

        unitArchiveVideoDao.updateByExampleSelective(po, example);
    }

    private String getLauUnitTargetExtraUk(LauUnitTargetExtraPo po) {
        return po.getUnitId() + "-" + po.getUnitTargetType();
    }

    public void saveExtraTarget(Integer unitId, CpcUnitExtraTargetDto extraTarget, PromotionPurposeType uppt, Operator operator) {
        LOGGER.info("saveExtraTarget unitId:{}, extraTarget:{}", unitId, extraTarget);

        Pair<Long, List<Long>> pair = null;
        if (extraTarget != null) {
            if (recommendTypeList.contains(extraTarget.getRecommendType())) {
                Assert.isTrue(PromotionPurposeType.ARCHIVE_CONTENT.equals(uppt), "只有投稿内容的推荐类型可以选取投放粉丝推荐包");
                Assert.isTrue(CollectionUtils.isEmpty(extraTarget.getIncludeTheirsFans()), "选取了投放粉丝推荐包，不能手动填写包含up主粉丝");
                Assert.isTrue(CollectionUtils.isEmpty(extraTarget.getExcludeTheirsFans()), "选取了投放粉丝推荐包，不能手动填写排除up主粉丝");
                pair = cpcCampaignService.getTmpUpInfoByType(uppt, operator, extraTarget.getRecommendType());
                Assert.notNull(pair, "dmp接口返回的up主id数量为0，不合法");
                Assert.notEmpty(pair.getRight(), "dmp接口返回的up主id数量为0，不合法");
                Assert.isTrue(pair.getRight().size() <= 50, "dmp接口返回的up主id数量超过50，不合法");
            } else {
                Assert.isTrue(extraTarget.getRecommendType() == null || extraTarget.getRecommendType() == 0,
                        "recommend_type输入错误");
            }
        }

        final LauUnitTargetExtraPoExample cond = new LauUnitTargetExtraPoExample();
        cond.or().andUnitIdEqualTo(unitId);
        final List<LauUnitTargetExtraPo> existingPos = lauUnitTargetExtraDao.selectByExample(cond);
        final List<LauUnitTargetExtraPo> newPos = buildUnitExtraTargetPos(unitId, extraTarget, pair);
        final RecDiffResult<LauUnitTargetExtraPo, Integer> result = Functions.recDiff(existingPos, newPos, this::getLauUnitTargetExtraUk, LauUnitTargetExtraPo::getId, LauUnitTargetExtraPo::setId);
        for (LauUnitTargetExtraPo po : result.getNewRecords()) {
            lauUnitTargetExtraDao.insertSelective(po);
        }
        for (Integer id : result.getOfflineRecordKeys()) {
            lauUnitTargetExtraDao.deleteByPrimaryKey(id);
        }
        for (LauUnitTargetExtraPo po : result.getChangedRecords()) {
            lauUnitTargetExtraDao.updateByPrimaryKeySelective(po);
        }
    }

    private List<LauUnitTargetExtraPo> buildUnitExtraTargetPos(Integer unitId, CpcUnitExtraTargetDto extraTarget, Pair<Long, List<Long>> pair) {
        if (extraTarget == null) {
            return Collections.emptyList();
        }

        List<LauUnitTargetExtraPo> pos = Lists.newArrayList();

        ObjectUtils.setInteger(extraTarget::getBrowse, b -> pos.add(buildExtraTargetPo(unitId, UnitTargetType.BROWSE.getCode(), String.valueOf(b))));
        ObjectUtils.setInteger(extraTarget::getFansRelation, fr -> pos.add(buildExtraTargetPo(unitId, UnitTargetType.FANS_RELATION.getCode(), String.valueOf(fr))));
        ObjectUtils.setInteger(extraTarget::getInteraction, i -> pos.add(buildExtraTargetPo(unitId, UnitTargetType.INTERACTION.getCode(), String.valueOf(i))));
        ObjectUtils.setList(extraTarget::getVideoSecondPartition, vsp -> pos.add(buildExtraTargetPo(unitId, UnitTargetType.VIDEO_SECOND_PARTITION.getCode(), Utils.collectionToString(vsp))));

        if (extraTarget != null) {
            if (recommendTypeList.contains(extraTarget.getRecommendType())) {
                pos.add(buildExtraTargetPoWithRecommendType(unitId, UnitTargetType.INCLUDE_THEIRS_FANS.getCode(),
                        Utils.collectionToString(pair.getRight()), extraTarget.getRecommendType()));
                pos.add(buildExtraTargetPo(unitId, UnitTargetType.EXCLUDE_THEIRS_FANS.getCode(), String.valueOf(pair.getLeft())));
            } else {
                ObjectUtils.setList(extraTarget::getIncludeTheirsFans, value -> pos.add(buildExtraTargetPo(unitId, UnitTargetType.INCLUDE_THEIRS_FANS.getCode(), Utils.collectionToString(value))));
                ObjectUtils.setList(extraTarget::getExcludeTheirsFans, value -> pos.add(buildExtraTargetPo(unitId, UnitTargetType.EXCLUDE_THEIRS_FANS.getCode(), Utils.collectionToString(value))));
            }
        }
        return pos;
    }

    private LauUnitTargetExtraPo buildExtraTargetPo(Integer unitId, Integer type, String value) {
        LauUnitTargetExtraPo po = new LauUnitTargetExtraPo();

        po.setUnitId(unitId);
        po.setUnitTargetType(type);
        po.setUnitTargetValue(value);
        po.setIsDeleted(IsDeleted.VALID.getCode());
        po.setRecommendType(RecommendTypeEnum.NONE.getCode());
        return po;
    }

    private LauUnitTargetExtraPo buildExtraTargetPoWithRecommendType(Integer unitId, Integer type, String value, Integer recommendType) {
        LauUnitTargetExtraPo po = this.buildExtraTargetPo(unitId, type, value);
        po.setRecommendType(recommendType);
        return po;
    }

    @SuppressWarnings("unchecked")
    private CpcUnitExtraTargetDto getExtraTarget(Integer unitId) {
        LauUnitTargetExtraPoExample example = new LauUnitTargetExtraPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andUnitIdEqualTo(unitId);

        CpcUnitExtraTargetDto extraTarget = CpcUnitExtraTargetDto.getEmpty();
        extraTarget.setRecommendType(RecommendTypeEnum.NONE.getCode());

        List<LauUnitTargetExtraPo> pos = unitTargetExtraDao.selectByExample(example);

        if (CollectionUtils.isEmpty(pos)) {
            return extraTarget;
        }

        //默认0
        int recommendType = RecommendTypeEnum.NONE.getCode();
        for (LauUnitTargetExtraPo po : pos) {

            ObjectUtils.equalThenSetString(UnitTargetType.BROWSE, UnitTargetType.getByCode(po.getUnitTargetType()),
                    po::getUnitTargetValue, str -> extraTarget.setBrowse((Integer) UnitTargetType.BROWSE.parse(str)));

            ObjectUtils.equalThenSetString(UnitTargetType.FANS_RELATION, UnitTargetType.getByCode(po.getUnitTargetType()),
                    po::getUnitTargetValue, str -> extraTarget.setFansRelation((Integer) UnitTargetType.FANS_RELATION.parse(str)));

            ObjectUtils.equalThenSetString(UnitTargetType.INTERACTION, UnitTargetType.getByCode(po.getUnitTargetType()),
                    po::getUnitTargetValue, str -> extraTarget.setInteraction((Integer) UnitTargetType.INTERACTION.parse(str)));

            ObjectUtils.equalThenSetString(UnitTargetType.VIDEO_SECOND_PARTITION, UnitTargetType.getByCode(po.getUnitTargetType()),
                    po::getUnitTargetValue, str -> extraTarget.setVideoSecondPartition((List<Integer>) UnitTargetType.VIDEO_SECOND_PARTITION.parse(str)));

            //有投放粉丝的定向，需要返回粉丝来源
            if (UnitTargetType.INCLUDE_THEIRS_FANS == UnitTargetType.getByCode(po.getUnitTargetType())) {
                extraTarget.setIncludeTheirsFans((List<Long>) UnitTargetType.INCLUDE_THEIRS_FANS.parse(po.getUnitTargetValue()));
                recommendType = po.getRecommendType();
            }

            ObjectUtils.equalThenSetString(UnitTargetType.EXCLUDE_THEIRS_FANS, UnitTargetType.getByCode(po.getUnitTargetType()),
                    po::getUnitTargetValue, str -> extraTarget.setExcludeTheirsFans((List<Long>) UnitTargetType.EXCLUDE_THEIRS_FANS.parse(str)));

        }
        extraTarget.setRecommendType(recommendType);
        //粉丝来源在列表中，隐藏包含粉丝、排除粉丝列表
        if (recommendTypeList.contains(recommendType)) {
            extraTarget.setIncludeTheirsFans(new ArrayList<>());
            extraTarget.setExcludeTheirsFans(new ArrayList<>());
        }

        return extraTarget;
    }

    @SuppressWarnings("unchecked")
    private Map<Integer, CpcUnitExtraTargetDto> getExtraTargetMap(List<Integer> unitIds) {
        if (CollectionUtils.isEmpty(unitIds)) {
            return Collections.emptyMap();
        }
        LauUnitTargetExtraPoExample example = new LauUnitTargetExtraPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andUnitIdIn(unitIds);
        List<LauUnitTargetExtraPo> pos = unitTargetExtraDao.selectByExample(example);

        Map<Integer, List<LauUnitTargetExtraPo>> poMap = pos.stream()
                .collect(Collectors.groupingBy(LauUnitTargetExtraPo::getUnitId));
        Map<Integer, CpcUnitExtraTargetDto> resultMap = new HashMap<>();

        unitIds.forEach(unitId -> {
            CpcUnitExtraTargetDto extraTarget = CpcUnitExtraTargetDto.getEmpty();
            extraTarget.setRecommendType(RecommendTypeEnum.NONE.getCode());

            List<LauUnitTargetExtraPo> singleUnitPos = poMap.getOrDefault(unitId, Collections.emptyList());
            //默认0
            int recommendType = RecommendTypeEnum.NONE.getCode();
            for (LauUnitTargetExtraPo po : singleUnitPos) {

                ObjectUtils.equalThenSetString(UnitTargetType.BROWSE, UnitTargetType.getByCode(po.getUnitTargetType()),
                        po::getUnitTargetValue, str -> extraTarget.setBrowse((Integer) UnitTargetType.BROWSE.parse(str)));

                ObjectUtils.equalThenSetString(UnitTargetType.FANS_RELATION, UnitTargetType.getByCode(po.getUnitTargetType()),
                        po::getUnitTargetValue, str -> extraTarget.setFansRelation((Integer) UnitTargetType.FANS_RELATION.parse(str)));

                ObjectUtils.equalThenSetString(UnitTargetType.INTERACTION, UnitTargetType.getByCode(po.getUnitTargetType()),
                        po::getUnitTargetValue, str -> extraTarget.setInteraction((Integer) UnitTargetType.INTERACTION.parse(str)));

                ObjectUtils.equalThenSetString(UnitTargetType.VIDEO_SECOND_PARTITION, UnitTargetType.getByCode(po.getUnitTargetType()),
                        po::getUnitTargetValue, str -> extraTarget.setVideoSecondPartition((List<Integer>) UnitTargetType.VIDEO_SECOND_PARTITION.parse(str)));

                //有投放粉丝的定向，需要返回粉丝来源
                if (UnitTargetType.INCLUDE_THEIRS_FANS == UnitTargetType.getByCode(po.getUnitTargetType())) {
                    extraTarget.setIncludeTheirsFans((List<Long>) UnitTargetType.INCLUDE_THEIRS_FANS.parse(po.getUnitTargetValue()));
                    recommendType = po.getRecommendType();
                }

                ObjectUtils.equalThenSetString(UnitTargetType.EXCLUDE_THEIRS_FANS, UnitTargetType.getByCode(po.getUnitTargetType()),
                        po::getUnitTargetValue, str -> extraTarget.setExcludeTheirsFans((List<Long>) UnitTargetType.EXCLUDE_THEIRS_FANS.parse(str)));

            }
            extraTarget.setRecommendType(recommendType);
            //粉丝来源在列表中，隐藏包含粉丝、排除粉丝列表
            if (recommendTypeList.contains(recommendType)) {
                extraTarget.setIncludeTheirsFans(new ArrayList<>());
                extraTarget.setExcludeTheirsFans(new ArrayList<>());
            }
            resultMap.put(unitId, extraTarget);
        });
        return resultMap;
    }

    private Map<Integer, CpcUnitExtraTargetDto> getExtraUpgradeTargetMap(List<Integer> targetPackageUpgradeIds) {
        if (CollectionUtils.isEmpty(targetPackageUpgradeIds)) {
            return Collections.emptyMap();
        }
        Map<Integer, TargetPackageExtraTargetDto> resultBaseMap = resTargetPackageExtraTargetService.getTargetPackageExtraTargetDoMapByTargetPackageIds(targetPackageUpgradeIds);
        Map<Integer, CpcUnitExtraTargetDto> resultMap = new HashMap<>();
        resultBaseMap.keySet().forEach(key -> {
            TargetPackageExtraTargetDto value = resultBaseMap.get(key);
            CpcUnitExtraTargetDto resultValue = CpcUnitExtraTargetDto.builder()
                    .fansRelation(value.getFansRelation())
                    .includeTheirsFans(value.getIncludeTheirsFans())
                    .excludeTheirsFans(value.getExcludeTheirsFans())
                    .interaction(value.getInteraction())
                    .browse(value.getBrowse())
                    .videoSecondPartition(value.getVideoSecondPartition())
                    .recommendType(value.getRecommendType())
                    .build();
            resultMap.put(key, resultValue);
        });
        return resultMap;
    }

    public LauUnitArchiveVideoPo getArchiveVideoPoByUnitId(Integer unitId) {
        LauUnitArchiveVideoPoExample example = new LauUnitArchiveVideoPoExample();

        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andUnitIdEqualTo(unitId);

        List<LauUnitArchiveVideoPo> pos = unitArchiveVideoDao.selectByExample(example);

        if (CollectionUtils.isEmpty(pos)) {
            return null;
        }

        return pos.get(0);
    }

    private List<LauUnitTargetBusinessInterestPo> buildLauUnitTargetBusinessInterestPo(List<Integer> businessInterestIds, Integer unitId) {
        if (CollectionUtils.isEmpty(businessInterestIds)) {
            return Collections.emptyList();
        }
        return businessInterestIds.stream().map(bi -> {
            LauUnitTargetBusinessInterestPo po = new LauUnitTargetBusinessInterestPo();
            po.setSecondCategoryId(bi);
            po.setInterestId(bi);
            po.setUnitId(unitId);
            return po;
        }).collect(Collectors.toList());
    }

    private List<Integer> getUnitIdsByCrowdPackIds(List<Integer> crowdPackIds) {
        if (CollectionUtils.isEmpty(crowdPackIds)) {
            return Collections.emptyList();
        }

        Set<Integer> unitIds = Sets.newHashSet();

        LauUnitTargetCrowdPackPoExample example = new LauUnitTargetCrowdPackPoExample();
        example.or()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andCrowdPackIdIn(crowdPackIds);

        List<LauUnitTargetCrowdPackPo> pos = lauUnitTargetCrowdPackDao.selectByExample(example);

        if (!CollectionUtils.isEmpty(pos)) {
            unitIds.addAll(pos.stream().map(LauUnitTargetCrowdPackPo::getUnitId).distinct().collect(Collectors.toList()));
        }

        LauUnitTargetBusinessInterestPoExample businessExample = new LauUnitTargetBusinessInterestPoExample();
        businessExample.or()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andInterestIdIn(crowdPackIds);

        List<LauUnitTargetBusinessInterestPo> bPos = lauUnitTargetBusinessInterestDao.selectByExample(businessExample);

        if (!CollectionUtils.isEmpty(bPos)) {
            unitIds.addAll(bPos.stream().map(LauUnitTargetBusinessInterestPo::getUnitId).distinct().collect(Collectors.toList()));
        }

        return Lists.newArrayList(unitIds);
    }

    private void sendBudgetExceedMsg(List<Integer> unitIds) {
        if (CollectionUtils.isEmpty(unitIds)) {
            return;
        }

        // 子单元不发送bis消息
        List<Integer> parentUnitIds = new ArrayList<>();
        List<LauUnitExtraPo> unitExtraPos = unitExtraService.getByUnitIds(unitIds);
        for (LauUnitExtraPo unitExtraPo : unitExtraPos) {
            if (unitExtraPo.getParentUnitId() == 0) {
                parentUnitIds.add(unitExtraPo.getUnitId());
            }
        }

        List<LauUnitPo> units = getPosInIds(parentUnitIds);
        for (LauUnitPo unit : units) {
            sendBisMessage(unit.getAccountId(),
                    String.format(LaunchConstant.UNIT_BUDGET_EXCEED_MESSAGE_TITLE, unit.getUnitName()),
                    LaunchConstant.UNIT_BUDGET_EXCEED_MESSAGE_MSG);
        }
    }

    private void sendBisMessage(Integer accountId, String title, String msg) {
        bsiSoaMessageService.sendMessage(SendMessageDto
                .builder()
                .msgText(msg)
                .msgType(BsiMessageType.ACCOUNT)
                .senderId(0)
                .senderName(Operator.SYSTEM.getOperatorName())
                .system(SystemType.CPM.getCode())
                .title(title)
                .receiverIds(Arrays.asList(accountId))
                .build());
    }

    public List<OcpcTargetDto> getOcpcTargetByAccountId(Integer accountId,
                                                        Integer purposeType,
                                                        List<Integer> osList,
                                                        Integer salesType,
                                                        Integer slotGroupId) {
        Assert.notNull(accountId, "账号ID不可为空");
        Assert.notNull(purposeType, "投放目的内容不可为空");
        Assert.notNull(salesType, "竞价类型不可为空");
        //  Assert.notNull(slotGroupId, "广告位组ID不可为空");

        AccountAllInfoDto accountAllInfo = queryAccountService.getAccountAllInfoFromCache(accountId);

        OcpcTargetEnum[] ocpcTargetEnums = OcpcTargetEnum.values();
        List<Device> devices = CollectionUtils.isEmpty(osList) ? Collections.emptyList() :
                osList.stream().map(Device::getByTargetId).distinct().collect(Collectors.toList());

        List<OcpcTargetDto> ocpcTargetDtos = Lists.newArrayListWithCapacity(ocpcTargetEnums.length);
        ResSlotGroupBaseDto resSlotGroup = slotGroupId == null ? null : resSlotGroupService.getGroupById(slotGroupId);
        for (OcpcTargetEnum ocpcTargetEnum : ocpcTargetEnums) {

            //首条评论复制,框下链接点击不在旧版起飞出现
            if (OcpcTargetEnum.UNDER_BOX_LINK_CLICK == ocpcTargetEnum ||
                    OcpcTargetEnum.FIRST_COMMENT_COPY == ocpcTargetEnum) {
                continue;
            }

            // 根据推广目的和 OcpcTargetEnum 支持的推广目的，找到匹配的 OcpcTargetEnum
            PromotionPurposeType promotionPurposeType = PromotionPurposeType.getByCode(purposeType);
            if (!CollectionUtils.isEmpty(ocpcTargetEnum.getSupportPurposeTypes())) {
                if (!ocpcTargetEnum.getSupportPurposeTypes().contains(promotionPurposeType)) {
                    LOGGER.info("ocpcTargetEnum:{},promotionPurposeType: false", ocpcTargetEnum.getCode());
                    continue;
                }
            }

            // 根据 supportPurposeTypes 过滤 OcpcTargetEnum
            if (!CollectionUtils.isEmpty(ocpcTargetEnum.getSupportPurposeTypes())) {
                if (!ocpcTargetEnum.getSupportPurposeTypes().contains(promotionPurposeType)) {
                    LOGGER.info("ocpcTargetEnum:{},promotionPurposeType: false", ocpcTargetEnum.getCode());
                    continue;
                }
            }

            // 根据 getSupportSalesTypes 过滤 OcpcTargetEnum
            SalesType salesTypeEnum = SalesType.getByCode(salesType);
            if (!CollectionUtils.isEmpty(ocpcTargetEnum.getSupportSalesTypes())) {
                if (!ocpcTargetEnum.getSupportSalesTypes().contains(salesTypeEnum)) {
                    LOGGER.info("ocpcTargetEnum:{},salesType: false", ocpcTargetEnum.getCode());
                    continue;
                }
            }

            // 根据 getSupportDevices 过滤 OcpcTargetEnum
            if (!CollectionUtils.isEmpty(ocpcTargetEnum.getSupportDevices())) {
                if (CollectionUtils.isEmpty(devices) || !ocpcTargetEnum.getSupportDevices().containsAll(devices)) {
                    LOGGER.info("ocpcTargetEnum:{},devices: false", ocpcTargetEnum.getCode());
                    continue;
                }
            }

            // 根据 canOcpcTargetSourceIds 过滤 OcpcTargetEnum
            if (Objects.nonNull(resSlotGroup) && !CollectionUtils.isEmpty(unitValidator.getCanOcpcTargetSourceIds())
                    && CollectionUtils.isEmpty(Utils.getIntersection(resSlotGroup.getSlotIds(), unitValidator.getCanOcpcTargetSourceIds()))) {
                LOGGER.info("ocpcTargetEnum:{},canOcpcTargetSourceIds: false", ocpcTargetEnum.getCode());
                continue;
            }

            if (Objects.nonNull(resSlotGroup)) {
                ChannelEnum channelEnum = ChannelEnum.getByCode(resSlotGroup.getChannelId());
                if (!CollectionUtils.isEmpty(ocpcTargetEnum.getSupportPlatforms())) {
                    if (!ocpcTargetEnum.getSupportPlatforms().contains(channelEnum)) {
                        LOGGER.info("ocpcTargetEnum:{},channelEnum: false", ocpcTargetEnum.getCode());
                        continue;
                    }
                }
            }

            Integer accountGroupId = unitValidator.getOcpxAccountGroupTargetMap().get(ocpcTargetEnum.getCode());

            if (Utils.isPositive(accountGroupId)) {
                //内容起飞账号
                if (accountAllInfo.getAccountDto().getIsSupportContent() != null && accountAllInfo.getAccountDto().getIsSupportContent() == 1) {
                    if (OcpcTargetEnum.USER_FOLLOW.equals(ocpcTargetEnum)) {
                        //用户关注，直接添加
                    } else {
                        //非用户关注，账号必须在相关账号组id里
                        if (CollectionUtils.isEmpty(accountAllInfo.getAccountGroupDtos()) ||
                                accountAllInfo.getAccountGroupDtos().stream().noneMatch(ag -> accountGroupId.equals(ag.getId()))) {
                            LOGGER.info("ocpcTargetEnum:{},accountGroup: false", ocpcTargetEnum.getCode());
                            continue;
                        }
                    }
                } else {
                    //非内容起飞账号，账号必须在相关账号组id里
                    if (CollectionUtils.isEmpty(accountAllInfo.getAccountGroupDtos()) ||
                            accountAllInfo.getAccountGroupDtos().stream().noneMatch(ag -> accountGroupId.equals(ag.getId()))) {
                        LOGGER.info("ocpcTargetEnum:{},accountGroup: false", ocpcTargetEnum.getCode());
                        continue;
                    }
                }
            }

            if (Objects.equals(ocpcTargetEnum, OcpcTargetEnum.UNDEFINED)) {
                continue;
            }

            ocpcTargetDtos.add(OcpcTargetDto.builder()
                    .id(ocpcTargetEnum.getCode())
                    .name(ocpcTargetEnum.getDesc())
                    .enable(false)
                    .build());
        }

        return ocpcTargetDtos;
    }

    @Transactional(value = AD_CORE_TM, rollbackFor = Exception.class)
    public void batchUpdateBidPrice(Operator operator, List<Integer> unitIds, BigDecimal bidPrice, BigDecimal twoStageBid) throws ServiceException {
        LOGGER.info("batchUpdateBidPrice param operator: [{}], unitIds: [{}], bidPrice: [{}], twoStageBid: [{}]", operator, unitIds, bidPrice, twoStageBid);
        Assert.isTrue(!Operator.validateParamIsNull(operator), "操作人不可为空");
        Assert.notEmpty(unitIds, "单元ID不可为空");
        LOGGER.info("batchUpdateBidPrice unit size {}", unitIds.size());
        Assert.isTrue(unitIds.size() <= batchUpdateUnitBidMaxSize, "批量修改单元出价最多不可超过" + batchUpdateUnitBidMaxSize + "个");

        //List<CpcUnitDto> unitDtos = this.queryCpcUnit(QueryCpcUnitDto.builder().unitIds(unitIds).build());
        QueryUnitBo query = QueryUnitBo.builder()
                .unitIds(unitIds)
                .build();
        List<CpcUnitDto> unitDtos = launchUnitV1Service.listUnits(query);
        Assert.isTrue(!CollectionUtils.isEmpty(unitDtos) && unitDtos.size() == unitIds.size(), "单元ID不合法");
        Map<Integer, Map<Integer, Set<Integer>>> accountId2SalesType2OcpxTargetMap = unitDtos.stream()
                .collect(Collectors.groupingBy(CpcUnitDto::getAccountId,
                        Collectors.groupingBy(CpcUnitDto::getSalesType,
                                Collectors.mapping(CpcUnitDto::getOcpcTarget, Collectors.toSet()))));

        Set<Integer> accountIds = accountId2SalesType2OcpxTargetMap.keySet();
        Assert.isTrue(Collections.singleton(operator.getOperatorId()).containsAll(accountIds), "您不能操作不属于您的单元");

        Map<Integer, Set<Integer>> salesType2OcpxTargetMap = accountId2SalesType2OcpxTargetMap.getOrDefault(operator.getOperatorId(), Collections.emptyMap());

        Set<Integer> salesTypes = salesType2OcpxTargetMap.keySet();
        Assert.isTrue(salesTypes.size() == 1, "请选择同样出价方式的单元");

        List<Integer> ocpxTargets = salesType2OcpxTargetMap.values().stream().flatMap(Collection::stream).distinct().collect(Collectors.toList());
        if (ocpxTargets.stream().noneMatch(Utils::isPositive)) {
            //全部未开启ocpx
            Assert.isTrue(Utils.isPositive(bidPrice), "单元出价不可为空");
            this.batchUpdateBidPrice(operator, Utils.fromYuanToIntFen(bidPrice), null, unitDtos);
        } else if (ocpxTargets.stream().allMatch(Utils::isPositive)) {
            //全部开启了ocpx
            Assert.isTrue(Utils.isPositive(bidPrice), "oCPX第一阶段出价不可为空");
            Assert.isTrue(Utils.isPositive(twoStageBid), "oCPX第二阶段目标转化成本必须大于0元");
            //投稿内容开启了ocpm,修改涨粉成本校验
            if (unitDtos.stream().map(CpcUnitDto::getPromotionPurposeType).collect(Collectors.toList()).contains(PromotionPurposeType.ARCHIVE_CONTENT.getCode())) {
                List<Integer> targetList = unitDtos.stream().map(CpcUnitDto::getOcpcTarget).collect(Collectors.toList());
                if (targetList.contains(OcpcTargetEnum.USER_FOLLOW.getCode())) {
                    unitCostService.checkUserFollowTwoStageBid(operator.getOperatorId(), Utils.fromYuanToIntFen(twoStageBid));
                }
                if (targetList.contains(OcpcTargetEnum.VIDEO_PLAY.getCode())) {
                    Assert.isTrue(twoStageBid.compareTo(new BigDecimal(String.valueOf(videoPlayTwoStageBid * 1.0 / 100))) >= 0, "播放单价成本不能低于0.2元");
                }
            }
            //动态开启了ocpm
            if (unitDtos.stream().map(CpcUnitDto::getPromotionPurposeType).collect(Collectors.toList()).contains(PromotionPurposeType.DYNAMIC.getCode())) {
                List<Integer> targetList = unitDtos.stream().map(CpcUnitDto::getOcpcTarget).collect(Collectors.toList());
                if (targetList.contains(OcpcTargetEnum.DYNAMIC_DETAIL_BROWSE.getCode())) {
                    Assert.isTrue(twoStageBid.compareTo(new BigDecimal(String.valueOf(dynamicMinTwoStageBid * 1.0 / 100))) >= 0, "动态单价成本不能低于0.5元");
                }
            }
            this.batchUpdateBidPrice(operator, Utils.fromYuanToIntFen(bidPrice), Utils.fromYuanToIntFen(twoStageBid), unitDtos);
        } else {
            Assert.isTrue(false, "不支持对已开启oCPX及未开启oCPX的单元同时修改出价");
        }
    }

    private void batchUpdateBidPrice(Operator operator, Integer intBidPrice, Integer twoStageBid, List<CpcUnitDto> unitDtos) throws ServiceException {
        for (CpcUnitDto unit : unitDtos) {
            try {
                // 如果是ocpc 三连单元的一阶段出价 不需要校验底价 随便改 大于0即可
                boolean cancelLowBid = SalesType.CPC.getCode() == unit.getSalesType()
                        && Utils.isPositive(unit.getOcpcTarget())
                        && BusinessDomain.CPC == unit.getBusinessDomain()
                        && AdpVersion.isMiddle(unit.getAdpVersion());
                Integer lowBid = cancelLowBid ? 0 : getReservedPrice(GetBidCostParam.builder()
                        .accountId(unit.getAccountId())
                        .slotGroupId(unit.getSlotGroup())
                        .salesType(unit.getSalesType())
                        .launchType(unit.getPromotionPurposeType())
                        .build(), unit.getAdpVersion());

                Assert.isTrue(intBidPrice.compareTo(lowBid) >= 0, "出价不能低于底价" + Utils.fromFenToYuan(lowBid) + "元");

                LauUnitPo po = LauUnitPo.builder().unitId(unit.getUnitId()).costPrice(intBidPrice).twoStageBid(twoStageBid).build();

                lauUnitDao.updateByPrimaryKeySelective(po);

                // 编辑单元更新保留价状态
                final int av = unit.getAdpVersion();
                updateReservedPriceStatus(av, unit.getUnitId(), intBidPrice, unit.getIsNoBid());

                CpcUnitDto unitLog = CpcUnitDto.builder().unitId(unit.getUnitId()).costPrice(intBidPrice).twoStageBid(twoStageBid).build();
                logOperateService.addUpdateLog(DbTable.LAU_UNIT, operator, unitLog, unit.getUnitId());
            } catch (Exception e) {
                LOGGER.error("batchUpdateBidPrice exception unitId: [{}]", unit.getUnitId(), e);
                throw new ServiceException("修改单元[" + unit.getUnitId() + "]出价失败");
            }
        }
    }

    @Transactional(value = AD_CORE_TM, rollbackFor = Exception.class)
    public void batchUpdateBidPriceNew(Operator operator, UnitUpdateBidPriceDto updateDto) throws ServiceException {
        LOGGER.info("batchUpdateBidPrice new param: operator:{}, updateDto:{}", operator, updateDto);
        // 前置校验
        List<Integer> unitIds = updateDto.getUnitPriceDtos().stream()
                .map(UnitPriceDto::getUnitId)
                .collect(Collectors.toList());
        Assert.isTrue(!Operator.validateParamIsNull(operator), "操作人不可为空");
        Assert.notEmpty(unitIds, "单元ID不可为空");
        LOGGER.info("batchUpdateBidPrice new unit size {}", unitIds.size());
        Assert.isTrue(unitIds.size() <= batchUpdateUnitBidMaxSize, "批量修改单元出价最多不可超过"
                + batchUpdateUnitBidMaxSize + "个");

        //List<CpcUnitDto> unitDtos = this.queryCpcUnit(QueryCpcUnitDto.builder().unitIds(unitIds).build());
        QueryUnitBo query = QueryUnitBo.builder()
                .unitIds(unitIds)
                .build();
        List<CpcUnitDto> unitDtos = launchUnitV1Service.listUnits(query);
        Assert.isTrue(!CollectionUtils.isEmpty(unitDtos) && unitDtos.size() == unitIds.size(), "单元ID不合法");

        for (CpcUnitDto unitDto : unitDtos) {
            if (AdpVersion.isLegacyFly(unitDto.getAdpVersion())) {
                launchOfflineService.validateOfflineStage(unitDto.getAccountId(), false);
            }
        }

        final Map<Integer, LauCampaignDto> campaignMap = lauCampaignService.getCampaignDtoMapInIds(unitDtos.stream().map(CpcUnitDto::getCampaignId).distinct().collect(Collectors.toList()));

        Map<Integer, Map<Integer, Set<Integer>>> accountId2SalesType2OcpxTargetMap = unitDtos.stream()
                .collect(Collectors.groupingBy(CpcUnitDto::getAccountId,
                        Collectors.groupingBy(CpcUnitDto::getSalesType,
                                Collectors.mapping(CpcUnitDto::getOcpcTarget, Collectors.toSet()))));

        Set<Integer> accountIds = accountId2SalesType2OcpxTargetMap.keySet();
        Assert.isTrue(Collections.singleton(operator.getOperatorId()).containsAll(accountIds), "您不能操作不属于您的单元");

        Map<Integer, Set<Integer>> salesType2OcpxTargetMap = accountId2SalesType2OcpxTargetMap.getOrDefault(operator.getOperatorId(), Collections.emptyMap());

        Set<Integer> salesTypes = salesType2OcpxTargetMap.keySet();
        List<Integer> ocpxModes = unitDtos.stream().map(CpcUnitDto::getOcpxMode).distinct().collect(Collectors.toList());
        if (ocpxModes.size() != 1) {
            if (UnitUpdateBidPriceTypeEnum.OCPX_DEEP_OPTIMIZE.getCode().equals(updateDto.getUpdateType())) {
                throw new IllegalArgumentException("无深度转化目标或者自动优化目标的单元无法修改深度转化出价");
            }
            //支持批量修改ocpm目标转化出价
//            if(!Objects.equals(updateDto.getSalesType(), SalesType.CPM.getCode())){
//                throw new IllegalArgumentException("不支持批量修改OCPX转化类型不同——包含双目标不同出价方式和单目标的单元");
//            }
        }
        // 前置校验结束
        // NOBID单元不支持修改出价
        for (CpcUnitDto unitDto : unitDtos) {
            if (YesNoEnum.YES.getCode().equals(unitDto.getIsNoBid())) {
                throw new ServiceRuntimeException("NOBID单元不支持修改出价/目标转化出价");
            }
            Assert.isTrue(!Utils.isPositive(unitDto.getIsManaged()), "不允许修改托管计划下的出价");
        }

        Set<Integer> ocpxTargets = salesType2OcpxTargetMap.values().stream().flatMap(Collection::stream).collect(Collectors.toSet());
        Set<Integer> ocpxTargetTwoSet = unitDtos.stream().map(CpcUnitDto::getOcpxTargetTwo).collect(Collectors.toSet());
        String errorMsg = getBatchUpdateBidPriceErrorMsg(updateDto.getSalesType(), updateDto.getUpdateType(), updateDto.getUnitPriceDtos(), unitDtos, false);
        Assert.isTrue(StringUtils.isEmpty(errorMsg), errorMsg);
        if (ocpxTargets.stream().noneMatch(Utils::isPositive)) {
            //全部未开启OCPX
            Assert.isTrue(salesTypes.size() == 1, "请选择同样出价方式的单元");
            Assert.isTrue(salesTypes.contains(updateDto.getSalesType()), "出价方式与单元不符");
            Assert.isTrue(UnitUpdateBidPriceTypeEnum.CPX.getCode().equals(updateDto.getUpdateType()), "未开启OCPX,不能修改OCPX出价");
            batchUpdateBidPriceExecute(operator, updateDto, unitDtos);
        } else if (ocpxTargets.stream().allMatch(Utils::isPositive)) {
            // 全部开启了OCPX
            //Assert.isTrue(salesTypes.contains(SalesType.CPC.getCode()), "目前仅CPC支持批量修改OCPX出价");
            if (UnitUpdateBidPriceTypeEnum.OCPX_DEEP_OPTIMIZE.getCode().equals(updateDto.getUpdateType())) {
                Assert.isTrue(ocpxModes.stream().allMatch(mode -> OcpxMode.DUAL_TARGETS_MANUAL_BID.getCode().equals(mode)), "仅开启了OCPX手动双出价的单元支持修改深度转化出价");
                if (ocpxTargetTwoSet.contains(OcpcTargetEnum.PAID_IN_24H_ROI.getCode())
                        || ocpxTargetTwoSet.contains(OcpcTargetEnum.PAID_IN_7D_COST.getCode())
                        || ocpxTargetTwoSet.contains(OcpcTargetEnum.IN_APP_CHARGE_24H_ROI.getCode())) {
                    Assert.isTrue(ocpxTargetTwoSet.size() == 1, "深度优化目标为24小时ROI/七日付费/24小时变现roi的单元不支持和其他优化目标的单元一起修改深度转化出价");
                }
            }

            if (UnitUpdateBidPriceTypeEnum.OCPX_OPTIMIZE.getCode().equals(updateDto.getUpdateType())) {
                // labelId=804时，支持品牌传播下，单元层选择稿件的时候，可以选择 PAID_IN_24H_ROI、PAID_IN_7D_COST。以前这种俩只能在 ocpx_target_two 中被选择
                if (ocpxTargets.contains(OcpcTargetEnum.PAID_IN_24H_ROI.getCode())
                        || ocpxTargets.contains(OcpcTargetEnum.PAID_IN_7D_COST.getCode())
                        || ocpxTargets.contains(OcpcTargetEnum.IN_APP_CHARGE_24H_ROI.getCode())) {
                    Assert.isTrue(ocpxTargets.size() == 1, "优化目标为24小时ROI/七日付费的单元，不支持和其他优化目标的单元一起修改目标转化出价");
                }
            }

            if (UnitUpdateBidPriceTypeEnum.COST_PRICE_LIST.contains(updateDto.getUpdateType())) {
                Assert.isTrue(SalesType.CPM.getCode() != updateDto.getSalesType(), "不允许修改OCPM单元的出价");
            }

            // 如果修改了单元第二阶段出价 进行之后的校验
            if (!OCPX_OPTIMIZE.getCode().equals(updateDto.getUpdateType())) {
                batchUpdateBidPriceExecute(operator, updateDto, unitDtos);
                return;
            }

            Map<Integer, BigDecimal> priceUnitIdMap = updateDto.getUnitPriceDtos()
                    .stream().collect(Collectors.toMap(UnitPriceDto::getUnitId, UnitPriceDto::getValue, (exist, replace) -> replace));
            unitDtos.forEach(unitDto -> {
                // 三连推广单元不校验
                if (AdpVersion.isMiddle(unitDto.getAdpVersion())) {
                    return;
                }
                BigDecimal value = priceUnitIdMap.get(unitDto.getUnitId());
                if (Objects.isNull(value)) {
                    return;
                }
                // 投稿内容开启了ocpm,修改涨粉成本校验
                Integer ocpcTarget = unitDto.getOcpcTarget();
                if (unitDto.getPromotionPurposeType().equals(PromotionPurposeType.ARCHIVE_CONTENT.getCode())) {
                    if (OcpcTargetEnum.USER_FOLLOW.getCode().equals(ocpcTarget)) {
                        unitCostService.checkUserFollowTwoStageBid(operator.getOperatorId(), value.intValue());
                    }
                    if (OcpcTargetEnum.VIDEO_PLAY.getCode().equals(ocpcTarget)) {
                        Assert.isTrue(value.compareTo(BigDecimal.valueOf(videoPlayTwoStageBid)) >= 0, "播放单价成本不能低于0.2元");
                    }
                }

                // 动态开启了ocpm
                if (unitDto.getPromotionPurposeType().equals(PromotionPurposeType.DYNAMIC.getCode())) {
                    if (OcpcTargetEnum.DYNAMIC_DETAIL_BROWSE.getCode().equals(ocpcTarget)) {
                        Assert.isTrue(value.compareTo(BigDecimal.valueOf(dynamicMinTwoStageBid)) >= 0, "动态单价成本不能低于0.5元");
                    }
                }
            });
            // 修改二阶段转化出价校验 目前仅校验三连推广的起飞单元
            List<Integer> accountLabelIdList = accountLabelService.getLabelIdsByAccountId(operator.getOperatorId());
            if (UnitUpdateBidPriceTypeEnum.OCPX_OPTIMIZE.getCode().equals(updateDto.getUpdateType())) {
                final AccountBaseDto accountBaseDto = queryAccountService.getAccountBaseDtoByIdFromCache(operator.getOperatorId());
                Map<Integer, Integer> flyBanner4LowestSecondBidCostMap = getFlyBanner4LowestSecondBidCostMap4UnitList(unitDtos, accountLabelIdList, operator.getOperatorId());
                unitDtos.forEach(unitDto -> {
                    if (!AdpVersion.isMiddle(unitDto.getAdpVersion())) {
                        return;
                    }
                    BigDecimal value = priceUnitIdMap.get(unitDto.getUnitId());
                    final LauCampaignDto campaignDto = campaignMap.get(unitDto.getCampaignId());
                    Integer flyLowestBidTwoStagePrice;
                    if (Utils.isPositive(accountBaseDto.getIsSupportContent())) {
                        flyLowestBidTwoStagePrice = cpcUnitService.getContentFlyLowestBid(campaignDto.getPromotionPurposeType(), unitDto.getPromotionPurposeType(), unitDto.getOcpcTarget());
                    } else {
                        flyLowestBidTwoStagePrice = flyBanner4LowestSecondBidCostMap.getOrDefault(unitDto.getUnitId(), 0);
                    }
                    OcpcTargetEnum ocpcTargetEnum = OcpcTargetEnum.getByCode(unitDto.getOcpcTarget());
                    String ocpcTargetDesc = Objects.isNull(ocpcTargetEnum) ? "" : ocpcTargetEnum.getDesc();
                    Assert.isTrue(value.compareTo(BigDecimal.valueOf(flyLowestBidTwoStagePrice)) >= 0, "单元" + unitDto.getUnitId() + ocpcTargetDesc + "单价成本不能低于" + Utils.fromFenToYuan(flyLowestBidTwoStagePrice)
                            + (OcpcTargetEnum.ROI_24H_VALUE_LIST.contains(unitDto.getOcpcTarget()) ? "" : "元"));
                });
            }
            // 修改辅助出价校验,目前仅支持在号白名单内 + 稿件 + O订单提交的单元
            assistSearchCheck(operator.getOperatorId(), accountLabelIdList, unitDtos, updateDto);
            // 执行改价
            batchUpdateBidPriceExecute(operator, updateDto, unitDtos);
        } else {
            throw new IllegalArgumentException("不支持对已开启OCPX及未开启oCPX的单元同时修改出价");
        }
    }

    private void assistSearchCheck(Integer accountId, List<Integer> accountLabelIdList, List<CpcUnitDto> unitDtos, UnitUpdateBidPriceDto updateDto) {
        if (UnitUpdateBidPriceTypeEnum.ASSIST_SEARCH.getCode().equals(updateDto.getUpdateType())) {
            Assert.isTrue(accLabelConfig.supportFlyDingdanyouhuaAssist(accountId, accountLabelIdList), "账号不在订单优化辅助白名单");
            unitDtos.forEach(unitDto -> {
                Assert.isTrue(AdpVersion.isMiddle(unitDto.getAdpVersion()), "只能操作三连单元");
                Assert.isTrue(Objects.equals(PromotionPurposeType.ARCHIVE_CONTENT.getCode(), unitDto.getPromotionPurposeType()), "只能操作稿件单元");
                Assert.isTrue(Objects.equals(SalesType.CPM.getCode(), unitDto.getSalesType()) &&
                                Objects.equals(OcpcTargetEnum.GOODS_TRANSACTION.getCode(), unitDto.getOcpcTarget())
                        , "只能操作oCPM订单提交单元");
            });
        }
    }

    public void batchUpdateBidPriceExecute(Operator operator, UnitUpdateBidPriceDto updateDto, List<CpcUnitDto> unitDtos) throws ServiceException {
        UnitUpdateBidPriceTypeEnum.getByCode(updateDto.getUpdateType());
        Map<Integer, BigDecimal> unitPriceIdMap = updateDto.getUnitPriceDtos().stream()
                .collect(Collectors.toMap(UnitPriceDto::getUnitId, UnitPriceDto::getValue, (exist, replace) -> exist));
        List<Integer> unitIds = unitDtos.stream().map(o -> o.getUnitId()).collect(Collectors.toList());
//        List<CpcUnitDto> cpcUnitDtos = this.queryCpcUnit(QueryCpcUnitDto.builder()
//                .unitIds(unitIds)
//                .build());
        QueryUnitBo query = QueryUnitBo.builder()
                .unitIds(unitIds)
                .build();
        List<CpcUnitDto> cpcUnitDtos = launchUnitV1Service.listUnits(query);
        Map<Integer, CpcUnitDto> unitMap = cpcUnitDtos.stream().collect(Collectors.toMap(o -> o.getUnitId(), Function.identity()));
        for (CpcUnitDto unit : unitDtos) {
            try {
                BigDecimal price = unitPriceIdMap.get(unit.getUnitId());
                CpcUnitDto oldUnitDto = unitMap.get(unit.getUnitId());
                if (Objects.isNull(price)) {
                    continue;
                }
                Integer intPrice = 0;
                // 改PAID_IN_24H_ROI，PAID_IN_7D_COST的深度优化目标出价,price乘100,就是分后面2位小数
                if (UnitUpdateBidPriceTypeEnum.OCPX_DEEP_OPTIMIZE.getCode().equals(updateDto.getUpdateType()) &&
                        OcpcTargetEnum.ROI_24H_VALUE_LIST.contains(unit.getOcpxTargetTwo())) {
                    intPrice = new BigDecimal("100").multiply(price).intValue();
                } else {
                    if (OcpcTargetEnum.ROI_24H_VALUE_LIST.contains(unit.getOcpcTarget())) {
                        // "品牌传播下非电商优化目标迭代建设"，优化目标可以选择 24小时付费ROI，此时需要精确到万分之一。前端传来的值，单位是百分之一。数据库里存的是万分之一
                        intPrice = new BigDecimal("100").multiply(price).intValue();
                    } else {
                        intPrice = price.intValue();
                    }
                }
                LauUnitPo po = LauUnitPo.builder().unitId(unit.getUnitId()).build();
                CpcUnitDto oldunitLog = CpcUnitDto.builder().unitId(unit.getUnitId()).build();
                CpcUnitDto newUnitLog = CpcUnitDto.builder().unitId(unit.getUnitId()).build();
                if (UnitUpdateBidPriceTypeEnum.COST_PRICE_LIST.contains(updateDto.getUpdateType())) {
                    // 如果是ocpc 三连单元的一阶段出价 不需要校验底价 随便改 大于0即可
                    boolean cancelLowBid = SalesType.CPC.getCode() == unit.getSalesType()
                            && Utils.isPositive(unit.getOcpcTarget())
                            && BusinessDomain.CPC == unit.getBusinessDomain()
                            && AdpVersion.isMiddle(unit.getAdpVersion());
                    Integer lowBid = cancelLowBid ? 0 : getReservedPrice(GetBidCostParam.builder()
                            .accountId(unit.getAccountId())
                            .slotGroupId(unit.getSlotGroup())
                            .salesType(unit.getSalesType())
                            .launchType(unit.getPromotionPurposeType())
                            .build(), unit.getAdpVersion());
                    Assert.isTrue(intPrice.compareTo(lowBid) >= 0, "单元" + unit.getUnitId() + "出价不能低于底价" + Utils.fromFenToYuan(lowBid) + "元");
                    if (oldUnitDto.getSalesType().equals(SalesType.CPC.getCode()) && !Utils.isPositive(oldUnitDto.getOcpcTarget())) {
                        Assert.isTrue(intPrice <= 2000, "当前出价过高，为了您的资金安全，请查正");
                    }
                    po.setCostPrice(intPrice);
                    newUnitLog.setCostPrice(intPrice);
                    oldunitLog.setCostPrice(oldUnitDto.getCostPrice());
                } else if (UnitUpdateBidPriceTypeEnum.OCPX_OPTIMIZE.getCode().equals(updateDto.getUpdateType())) {
                    Assert.isTrue(Utils.isPositive(intPrice), "oCPX第二阶段目标转化成本必须大于0元");
                    po.setTwoStageBid(intPrice);
                    if (OcpcTargetEnum.ROI_24H_VALUE_LIST.contains(unit.getOcpcTarget())) {
                        newUnitLog.setTwoStageBid4Log(Utils.handleROI(intPrice).toString());
                        oldunitLog.setTwoStageBid4Log(Utils.handleROI(oldUnitDto.getTwoStageBid()).toString());
                    } else {
                        newUnitLog.setTwoStageBid(intPrice);
                        oldunitLog.setTwoStageBid(oldUnitDto.getTwoStageBid());
                    }
                } else if (UnitUpdateBidPriceTypeEnum.OCPX_DEEP_OPTIMIZE.getCode().equals(updateDto.getUpdateType())) {
                    // 24小时付费ROI 不需要校验: oCPX第二目标出价必须高于第一目标出价
                    if (!OcpcTargetEnum.ROI_24H_VALUE_LIST.contains(unit.getOcpxTargetTwo())) {
                        Assert.isTrue(intPrice > unit.getTwoStageBid(), "oCPX第二目标出价必须高于第一目标出价");
                    }

                    po.setOcpxTargetTwoBid(intPrice);
                    // 改PAID_IN_24H_ROI，PAID_IN_7D_COST的深度优化目标出价,日志4位小数（元）
                    if (OcpcTargetEnum.ROI_24H_VALUE_LIST.contains(unit.getOcpxTargetTwo())) {
                        newUnitLog.setOcpxTargetTwoBid4Log(Utils.handleROI(intPrice).toString());
                        oldunitLog.setOcpxTargetTwoBid4Log(Utils.handleROI(oldUnitDto.getOcpxTargetTwoBid()).toString());
                    } else {
                        if (Utils.isPositive(intPrice)) {
                            newUnitLog.setOcpxTargetTwoBid4Log(Utils.fromFenToYuan(intPrice.longValue()).toString());
                        }
                        if (Utils.isPositive(oldUnitDto.getOcpxTargetTwoBid())) {
                            oldunitLog.setOcpxTargetTwoBid4Log(Utils.fromFenToYuan(oldUnitDto.getOcpxTargetTwoBid().longValue()).toString());
                        }
                    }
                } else if (UnitUpdateBidPriceTypeEnum.ASSIST_SEARCH.getCode().equals(updateDto.getUpdateType())) {
                    po.setAssistPrice(intPrice);
                    newUnitLog.setAssistPrice(intPrice);
                    oldunitLog.setAssistPrice(oldUnitDto.getAssistPrice());
                }

                // 改PAID_IN_24H_ROI，PAID_IN_7D_COST的深度优化目标出价,price单位是1/100分
                lauUnitDao.updateByPrimaryKeySelective(po);

                // 编辑单元更新保留价状态
                if (UnitUpdateBidPriceTypeEnum.COST_PRICE_LIST.contains(updateDto.getUpdateType())) {
                    final int av = unit.getAdpVersion();
                    updateReservedPriceStatus(av, unit.getUnitId(), intPrice, unit.getIsNoBid());
                }
                logOperateService.addUpdateLog(DbTable.LAU_UNIT, operator, oldunitLog, newUnitLog, unit.getUnitId());
            } catch (Exception e) {
                LOGGER.error("batchUpdateBidPrice exception unitId: [{}]", unit.getUnitId(), e);
                throw new ServiceException("修改单元[" + unit.getUnitId() + "]出价失败:" + e.getMessage());
            }
        }
    }

    public UnitPriceListDto getBatchUpdateBidPrice(Operator operator, UnitUpdateBidPriceQueryDto queryDto) {
        // 前置校验
        List<Integer> unitIds = queryDto.getUnitIds();
        Assert.isTrue(!Operator.validateParamIsNull(operator), "操作人不可为空");
        Assert.notEmpty(unitIds, "单元ID不可为空");
        //List<CpcUnitDto> unitDtos = this.queryCpcUnit(QueryCpcUnitDto.builder().unitIds(unitIds).build());
        QueryUnitBo query = QueryUnitBo.builder()
                .unitIds(unitIds)
                .build();
        List<CpcUnitDto> unitDtos = launchUnitV1Service.listUnits(query);
        // 获取当前单元价格
        List<UnitPriceDto> currPriceDtos = getCurrCpcUnitPrice(queryDto.getUpdateType(), unitDtos);
        // 计算新价格
        List<UnitPriceDto> updatePriceDtos = calculateCpcUnitUpdatePrice(queryDto, currPriceDtos);
        String errorMsg = getBatchUpdateBidPriceErrorMsg(queryDto.getSalesType(), queryDto.getUpdateType(), updatePriceDtos, unitDtos, true);
        return UnitPriceListDto.builder()
                .errorMsg(errorMsg)
                .priceDtoList(updatePriceDtos)
                .build();
    }

    public List<UnitPriceDto> getCurrCpcUnitPrice(Integer updateType, List<CpcUnitDto> unitDtos) {
        Set<Integer> ocpxModes = unitDtos.stream().map(CpcUnitDto::getOcpxMode).collect(Collectors.toSet());
        UnitUpdateBidPriceTypeEnum updateTypeEnum = UnitUpdateBidPriceTypeEnum.getByCode(updateType);
        switch (updateTypeEnum) {
            case CPX:
                return UnitPriceDto.convertListFromUnitDtoCostPrice(unitDtos);
            case OCPX_FIRST:
                return UnitPriceDto.convertListFromUnitDtoCostPrice(unitDtos);
            case OCPX_OPTIMIZE:
                // 优化目标出价
                return UnitPriceDto.convertListFromUnitDtoTwoStageBid(unitDtos);
            case OCPX_DEEP_OPTIMIZE:
                // 二阶段出价
                return convertListFromUnitDtoOcpxTargetTwoBid(unitDtos);
            case ASSIST_SEARCH:
                return UnitPriceDto.convertListFromUnitDtoAssist(unitDtos);
            default:
                throw new IllegalArgumentException("修改出价类型错误");
        }
    }

    // 24roi 7日付费存的是1/100 分 要做对应处理
    private List<UnitPriceDto> convertListFromUnitDtoOcpxTargetTwoBid(List<CpcUnitDto> unitDtoList) {
        return unitDtoList.stream()
                .map(this::convertFromUnitDtoOcpxTargetTwoBid)
                .collect(Collectors.toList());
    }

    private UnitPriceDto convertFromUnitDtoOcpxTargetTwoBid(CpcUnitDto unitDto) {
        boolean needDivide = OcpcTargetEnum.ROI_24H_VALUE_LIST.contains(unitDto.getOcpxTargetTwo());
        BigDecimal value = new BigDecimal(unitDto.getOcpxTargetTwoBid().toString());
        if (needDivide) {
            value = value.divide(BigDecimal.valueOf(100), 0, RoundingMode.HALF_UP);
        }
        return UnitPriceDto.builder()
                .unitId(unitDto.getUnitId())
                .value(value)
                .build();
    }

    public List<UnitPriceDto> calculateCpcUnitUpdatePrice(UnitUpdateBidPriceQueryDto updateDto, List<UnitPriceDto> priceDtos) {
        UnitUpdateBidPriceValueTypeEnum updateValueTypeEnum = UnitUpdateBidPriceValueTypeEnum.getByCode(updateDto.getUpdateValueType());
        switch (updateValueTypeEnum) {
            case VALUE:
                return priceDtos.stream().map(priceDto -> {
                    return UnitPriceDto.builder()
                            .unitId(priceDto.getUnitId())
                            .value(updateDto.getUpdateValue())
                            .build();
                }).collect(Collectors.toList());
            case DIFF_VALUE:
                return priceDtos.stream().map(priceDto -> {
                    BigDecimal updateValue = priceDto.getValue().add(updateDto.getUpdateValue());
                    if (Utils.isPositive(updateDto.getLimit())) {
                        boolean priceRise = Utils.isPositive(updateDto.getUpdateValue());
                        // 校验上下限
                        if (priceRise
                                && Utils.isPositive(updateDto.getLimit())
                                && updateValue.compareTo(updateDto.getLimit()) > 0) {
                            updateValue = updateDto.getLimit();

                        } else if (!priceRise
                                && Utils.isPositive(updateDto.getLimit())
                                && updateValue.compareTo(updateDto.getLimit()) < 0) {
                            updateValue = updateDto.getLimit();
                        }
                    }
                    return UnitPriceDto.builder()
                            .unitId(priceDto.getUnitId())
                            .value(updateValue)
                            .build();
                }).collect(Collectors.toList());
            case DIFF_VALUE_PERCENT:
                return priceDtos.stream().map(priceDto -> {
                    BigDecimal updateValue = priceDto.getValue().multiply(BigDecimal.ONE.add(updateDto.getUpdateValue()));
                    if (Utils.isPositive(updateDto.getLimit())) {
                        boolean priceRise = Utils.isPositive(updateDto.getUpdateValue());
                        // 校验上下限
                        if (priceRise
                                && Utils.isPositive(updateDto.getLimit())
                                && updateValue.compareTo(updateDto.getLimit()) > 0) {
                            updateValue = updateDto.getLimit();

                        } else if (!priceRise
                                && Utils.isPositive(updateDto.getLimit())
                                && updateValue.compareTo(updateDto.getLimit()) < 0) {
                            updateValue = updateDto.getLimit();
                        }
                    }
                    return UnitPriceDto.builder()
                            .unitId(priceDto.getUnitId())
                            .value(updateValue)
                            .build();
                }).collect(Collectors.toList());
            default:
                throw new IllegalArgumentException("修改出价数据类型错误");
        }
    }

    public String getBatchUpdateBidPriceErrorMsg(Integer salesType,
                                                 Integer updateType,
                                                 List<UnitPriceDto> priceDtos,
                                                 List<CpcUnitDto> unitDtos,
                                                 boolean needCheckLowBid) {
        Map<Integer, CpcUnitDto> unitIdMap = unitDtos.stream()
                .collect(Collectors.toMap(CpcUnitDto::getUnitId, Function.identity()));
        UnitUpdateBidPriceTypeEnum updateTypeEnum = UnitUpdateBidPriceTypeEnum.getByCode(updateType);
        if (needCheckLowBid
                && UnitUpdateBidPriceTypeEnum.COST_PRICE_LIST.contains(updateTypeEnum.getCode())) {
            String reversedMsg = "";
            reversedMsg = getCpcUnitReversedPriceErrorMsg(priceDtos, unitIdMap);
            if (!StringUtils.isEmpty(reversedMsg)) {
                return reversedMsg;
            }
        }

        switch (updateTypeEnum) {
            case CPX:
                return getUnitCostPriceErrorMsg(salesType, priceDtos);
            case OCPX_FIRST:
                return getUnitCostPriceErrorMsg(salesType, priceDtos);
            case OCPX_OPTIMIZE:
                return getUnitTwoStageBidErrorMsg(priceDtos, unitIdMap);
            case OCPX_DEEP_OPTIMIZE:
                return getUnitOcpxTargetTwoBidErrorMsg(priceDtos, unitIdMap);
            case ASSIST_SEARCH:
                // 暂时没有最高价，最低价校验
                return "";
            default:
                throw new IllegalArgumentException("修改出价类型错误");
        }
    }

    private String getCpcUnitReversedPriceErrorMsg(List<UnitPriceDto> priceDtos, Map<Integer, CpcUnitDto> unitIdMap) {
        Map<Integer, String> lowBidUnitMap = new HashMap<>();
        for (UnitPriceDto priceDto : priceDtos) {
            Integer intBidPrice = priceDto.getValue().intValue();
            CpcUnitDto unitDto = unitIdMap.get(priceDto.getUnitId());
            if (Objects.nonNull(unitDto)) {
                Integer lowBid = getReservedPrice(GetBidCostParam.builder()
                        .accountId(unitDto.getAccountId())
                        .slotGroupId(unitDto.getSlotGroup())
                        .salesType(unitDto.getSalesType())
                        .launchType(unitDto.getPromotionPurposeType())
                        .build(), unitDto.getAdpVersion());
                if (!lowBidUnitMap.containsKey(lowBid) && intBidPrice.compareTo(lowBid) < 0) {
                    lowBidUnitMap.put(lowBid, unitDto.getUnitName());
                }
            }
        }
        if (!CollectionUtils.isEmpty(lowBidUnitMap)) {
            StringBuilder sb = new StringBuilder();
            lowBidUnitMap.keySet().forEach(lowBid -> {
                String unitName = lowBidUnitMap.get(lowBid);
                sb.append("单元").append(unitName).append("等出价不能低于底价").append(Utils.fromFenToYuan(lowBid)).append("元\n");
            });
            return sb.toString();
        }
        return "";
    }

    private String getUnitCostPriceErrorMsg(Integer salesType, List<UnitPriceDto> priceDtos) {
        if (SalesType.CPC.getCode() == salesType) {
            for (UnitPriceDto priceDto : priceDtos) {
                BigDecimal updateValue = priceDto.getValue();
                if (updateValue.compareTo(BigDecimal.valueOf(2000L)) > 0) {
                    return "当前出价过高，为了您的资金安全，请查正";
                }
            }
        }
        return "";
    }

    private String getUnitTwoStageBidErrorMsg(List<UnitPriceDto> priceDtos, Map<Integer, CpcUnitDto> unitIdMap) {
        for (UnitPriceDto priceDto : priceDtos) {
            BigDecimal updateValue = priceDto.getValue();
            CpcUnitDto unitDto = unitIdMap.get(priceDto.getUnitId());
            if (!Utils.isPositive(unitDto.getOcpxMode())) {
                return "单元" + unitDto.getUnitName() + "等未开启OCPX,不能修改OCPX出价";
            }
            Assert.notNull(unitDto, "单元id不合法");
            if (OcpxMode.DUAL_TARGETS_MANUAL_BID.getCode().equals(unitDto.getOcpxMode())
                    && updateValue.compareTo(new BigDecimal(unitDto.getOcpxTargetTwoBid())) > 0) {
                // PAID_IN_24H_ROI 不要这个校验
                // updateValue=0代表自动出价, 不做校验
                if (!OcpcTargetEnum.ROI_24H_VALUE_LIST.contains(unitDto.getOcpxTargetTwo()) && updateValue.compareTo(BigDecimal.ZERO) > 0) {
                    return "单元" + unitDto.getUnitName() + "等目标转化出价不能大于深度转化出价";
                }
            }
        }
        return "";
    }

    private String getUnitOcpxTargetTwoBidErrorMsg(List<UnitPriceDto> priceDtos, Map<Integer, CpcUnitDto> unitIdMap) {
        for (UnitPriceDto priceDto : priceDtos) {
            BigDecimal updateValue = priceDto.getValue();
            CpcUnitDto unitDto = unitIdMap.get(priceDto.getUnitId());
            Assert.notNull(unitDto, "单元id不合法");
            if (updateValue.compareTo(new BigDecimal(unitDto.getTwoStageBid())) < 0) {
                // PAID_IN_24H_ROI 不要这个校验
                if (!OcpcTargetEnum.ROI_24H_VALUE_LIST.contains(unitDto.getOcpxTargetTwo())) {
                    return "单元" + unitDto.getUnitName() + "等深度转化出价不能低于目标转化出价";
                }
            }
        }
        return "";
    }

    public Map<Integer, LiveBroadcastRoomInfo> queryUnitLiveRoomRel(List<Integer> unitIds) {
        if (!CollectionUtils.isEmpty(unitIds)) {
            Map<Integer, Integer> unitLiveRoomRel = getUnitLiveRoomRel(unitIds);
            if (!CollectionUtils.isEmpty(unitLiveRoomRel)) {
                List<Integer> roomIds = unitLiveRoomRel.values().stream().distinct().collect(Collectors.toList());

                Map<Integer, LiveBroadcastRoomInfo> roomInfoMap = lauSubjectService.queryRoomInfoMap(roomIds);
                return Utils.mapTransform(unitLiveRoomRel, roomInfoMap);
            }
        }
        return Collections.emptyMap();
    }

    public Map<Integer, String> queryUnitLiveRoomCoverMap(List<Integer> unitIds) {
        if (CollectionUtils.isEmpty(unitIds)) {
            return Collections.emptyMap();
        }
        Map<Integer, Long> unitLiveRoomRel = getUnitLiveRoomRel(unitIds)
                .entrySet()
                .stream()
                .collect(Collectors.toMap(Map.Entry::getKey, e -> e.getValue().longValue()));
        if (CollectionUtils.isEmpty(unitLiveRoomRel)) {
            return Collections.emptyMap();
        }

        List<Long> roomIds = new ArrayList(unitLiveRoomRel.values());

        Map<Long, String> roomIdCoverMap =
                lauSubjectService.queryRoomCoverMap(roomIds);

        if (CollectionUtils.isEmpty(roomIdCoverMap)) {
            return Collections.emptyMap();
        }

        return Utils.mapTransform(unitLiveRoomRel, roomIdCoverMap);
    }

    public Map<Integer, Integer> getUnitLiveRoomRel(List<Integer> unitIds) {
        if (!CollectionUtils.isEmpty(unitIds)) {
            QueryCpcUnitDto queryDto = QueryCpcUnitDto.builder()
                    .unitIds(unitIds)
                    .build();
            List<LauUnitDo> unitPos = getCpcUnitQuery(queryDto).select(lauUnit.unitId, lauUnit.subjectId).fetch(LauUnitDo.class);

            Map<Integer, Integer> unitSubjectMap = unitPos.stream()
                    .filter(x -> Utils.isPositive(x.getSubjectId()))
                    .collect(Collectors.toMap(LauUnitDo::getUnitId, LauUnitDo::getSubjectId));
            if (!CollectionUtils.isEmpty(unitSubjectMap.values())) {
                Map<Integer, Integer> subjectMaterialRel = lauSubjectService.querySubjectMaterialRel(new ArrayList<>(unitSubjectMap.values()), LauSubjectType.LIVE_ROOM)
                        .entrySet().stream()
                        .collect(Collectors.toMap(Map.Entry::getKey, e -> Integer.valueOf(e.getValue())));
                return Utils.mapTransform(unitSubjectMap, subjectMaterialRel);
            }
        }
        return Collections.emptyMap();
    }

    public void updateReservedPriceStatus(int adpVersion, int unitId, int bidPrice, Integer isNobid) {
        if (!AdpVersion.isLegacy(adpVersion)) {
            final List<LauCreativeTemplateBo> list = lauCreativeTemplateService.getByQuery(QueryLauCreativeTemplateBo.builder()
                    .unitIdList(Collections.singletonList(unitId))
                    .build());
            list.forEach(x -> {
                if (isNobid != null && Utils.isPositive(isNobid)) {
                    x.setBizStatus(Constants.BIZ_STATUS_OK);
                } else {
                    if (bidPrice < x.getReservedPrice()) {
                        x.setBizStatus(Constants.BIZ_STATUS_UNDER_RESERVED_PRICE);
                    } else {
                        x.setBizStatus(Constants.BIZ_STATUS_OK);
                    }
                }
            });
            lauCreativeTemplateService.batchUpdateById(list);
        }
    }

    public List<LauUnitArchiveVideoPo> getLauUnitArchiveVideoByVideoId(Long videoId) {
        LauUnitArchiveVideoPoExample example = new LauUnitArchiveVideoPoExample();
        example.or().andVideoIdEqualTo(videoId).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<LauUnitArchiveVideoPo> lauUnitArchiveVideoPos = lauUnitArchiveVideoDao.selectByExample(example);
        if (CollectionUtils.isEmpty(lauUnitArchiveVideoPos)) {
            return Collections.emptyList();
        }
        return lauUnitArchiveVideoPos;
    }

    public List<LauUnitArchiveVideoPo> getLauUnitArchiveVideoByMid(Long mid) {
        LauUnitArchiveVideoPoExample example = new LauUnitArchiveVideoPoExample();
        example.or().andMidEqualTo(mid).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<LauUnitArchiveVideoPo> lauUnitArchiveVideoPos = lauUnitArchiveVideoDao.selectByExample(example);
        if (CollectionUtils.isEmpty(lauUnitArchiveVideoPos)) {
            return Collections.emptyList();
        }
        return lauUnitArchiveVideoPos;
    }

    public List<LauUnitArchiveVideoPo> getLauUnitArchiveVideoByUnitId(Integer unitId) {
        LauUnitArchiveVideoPoExample example = new LauUnitArchiveVideoPoExample();
        example.or().andUnitIdEqualTo(unitId).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<LauUnitArchiveVideoPo> lauUnitArchiveVideoPos = lauUnitArchiveVideoDao.selectByExample(example);
        if (CollectionUtils.isEmpty(lauUnitArchiveVideoPos)) {
            return Collections.emptyList();
        }
        return lauUnitArchiveVideoPos;
    }

    public List<LauUnitArchiveVideoPo> getLauUnitArchiveVideoByUnitIds(List<Integer> unitIds) {
        LauUnitArchiveVideoPoExample example = new LauUnitArchiveVideoPoExample();
        example.or().andUnitIdIn(unitIds).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<LauUnitArchiveVideoPo> lauUnitArchiveVideoPos = lauUnitArchiveVideoDao.selectByExample(example);
        if (CollectionUtils.isEmpty(lauUnitArchiveVideoPos)) {
            return Collections.emptyList();
        }
        return lauUnitArchiveVideoPos;
    }

    /**
     * 根据单元查询关联设置的小游戏定向设置
     */
    public UnitMiniGameTargetPo getUnitMiniGameTargetInfo(int unitId) {
        UnitMiniGameTargetPoExample miniGameExample = new UnitMiniGameTargetPoExample();
        UnitMiniGameTargetPoExample.Criteria criteria = miniGameExample.createCriteria();
        criteria.andUnitIdEqualTo((long) unitId);
        List<UnitMiniGameTargetPo> dbRecords = unitMiniGameTargetDao.selectByExample(miniGameExample);

        return (CollectionUtils.isEmpty(dbRecords)) ? null : dbRecords.get(0);
    }

    /**
     * 根据小游戏名称查询小游戏信息
     */
    public List<MiniGameMapDto> queryMiniGameMapByNames(List<String> names) {
        ResTargetMinigameMapPoExample example = new ResTargetMinigameMapPoExample();
        ResTargetMinigameMapPoExample.Criteria criteria = example.createCriteria();
        criteria.andNameIn(names);

        List<ResTargetMinigameMapPo> dbRecords = miniGameInfoDao.selectByExample(example);
        if (!CollectionUtils.isEmpty(dbRecords)) {
            return dbRecords.stream().map(record -> {
                MiniGameMapDto dto = new MiniGameMapDto();
                dto.setAppId(record.getAppId());
                dto.setName(record.getName());
                return dto;
            }).collect(Collectors.toList());
        }

        return new ArrayList<>(0);
    }

    /**
     * 根据小游戏id查询小游戏信息
     */
    public List<MiniGameMapDto> queryMiniGameMapByAppIds(List<String> ids) {
        ResTargetMinigameMapPoExample example = new ResTargetMinigameMapPoExample();
        ResTargetMinigameMapPoExample.Criteria criteria = example.createCriteria();
        criteria.andAppIdIn(ids);

        List<ResTargetMinigameMapPo> dbRecords = miniGameInfoDao.selectByExample(example);
        if (!CollectionUtils.isEmpty(dbRecords)) {
            return dbRecords.stream().map(record -> {
                MiniGameMapDto dto = new MiniGameMapDto();
                dto.setAppId(record.getAppId());
                dto.setName(record.getName());
                return dto;
            }).collect(Collectors.toList());
        }

        return new ArrayList<>(0);
    }

    /**
     * 检查小游戏定向配置是否错存在问题
     */
    private void validMiniGameConfig(List<String> includeMiniGameIds, List<String> excludeMiniGameIds) throws ServiceException {
        if (!CollectionUtils.isEmpty(includeMiniGameIds) && !CollectionUtils.isEmpty(excludeMiniGameIds)) {
            Set<String> includeIdSet = new HashSet<>(includeMiniGameIds);
            Set<String> excludeIdSet = new HashSet<>(excludeMiniGameIds);
            includeIdSet.retainAll(excludeIdSet);

            if (!includeIdSet.isEmpty()) {
                throw new ServiceException("小游戏定向配置错误,定向和排除不能包含相同游戏");
            }
        }
    }

    public MiniGameTargetDto getUnitMiniGameTarget(int unitId) {
        UnitMiniGameTargetPo po = getUnitMiniGameTargetInfo(unitId);
        if (po == null) return null;

        LOGGER.info("unit mini game db record: {}", JSON.toJSONString(po));

        List<String> includeGameIdList = new ArrayList<>();
        List<String> excludeGameIdList = new ArrayList<>();

        String includeGameIds = po.getIncludeGameIds();
        if (includeGameIds != null && !includeGameIds.isEmpty()) {
            String[] includeIds = includeGameIds.split(",");
            includeGameIdList.addAll(Arrays.asList(includeIds));
        }

        String excludeGameIds = po.getExcludeGameIds();
        if (excludeGameIds != null && !excludeGameIds.isEmpty()) {
            String[] excludeIds = excludeGameIds.split(",");
            excludeGameIdList.addAll(Arrays.asList(excludeIds));
        }


        MiniGameTargetDto result = new MiniGameTargetDto();
        result.setUnitId(unitId);
        result.setIncludeGameIds(includeGameIdList);
        result.setExcludeGameIds(excludeGameIdList);

        return result;
    }

    public CpcUnitDto simpleLoadCpcUnit(Integer unitId) {
        LauUnitPo lauUnitPo = this.getById(unitId);
        CpcUnitDto cpcUnitDto = new CpcUnitDto();
        BeanUtils.copyProperties(lauUnitPo, cpcUnitDto);
        return cpcUnitDto;
    }

    public void refreshUnitNextdayBudget(List<Integer> unitIds) {
        org.springframework.data.util.Pair<Boolean, Integer> result = refreshUnitNextdayBudget(0,
                REFRESH_ACCOUNT_NEXTDAY_BUDGET_SIZE, unitIds);
        while (result.getFirst()) {
            result = refreshUnitNextdayBudget(result.getSecond(), REFRESH_ACCOUNT_NEXTDAY_BUDGET_SIZE, unitIds);
        }
    }

    public org.springframework.data.util.Pair<Boolean, Integer> refreshUnitNextdayBudget(Integer unitNextdayBudgetIdGt, Integer batchSize, List<Integer> unitIds) {
        try {
            LOGGER.info("更新单元次日预算。unitNextdayBudgetIdGt:{}, batchSize:{}, unitIds:{}", unitNextdayBudgetIdGt,
                    batchSize, JSON.toJSONString(unitIds));
            // 获取一批次日预算数据，order by id asc
            LauUnitNextdayBudgetPoExample example = new LauUnitNextdayBudgetPoExample();
            LauUnitNextdayBudgetPoExample.Criteria criteria = example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                    // 指定生效日期
                    .andBudgetEffectiveTimeEqualTo(Utils.getBeginOfDay(Utils.getNow()))
                    .andIdGreaterThan(unitNextdayBudgetIdGt);

            // 指定单元 id 的次日预算
            if (!CollectionUtils.isEmpty(unitIds)) {
                criteria.andUnitIdIn(unitIds);
            }

            example.setOrderByClause(LaunchConstant.ID_ASC);
            example.setLimit(batchSize);
            List<LauUnitNextdayBudgetPo> unitNextdayBudgetPoList = lauUnitNextdayBudgetDao.selectByExample(example);
            if (CollectionUtils.isEmpty(unitNextdayBudgetPoList)) {
                return org.springframework.data.util.Pair.of(false, -1);
            }

            // 处理这批次日预算
            batchUpdateUnitNextdayBudget(unitNextdayBudgetPoList);

            // 处理的大小 == 批次大小，需要继续处理
            boolean needContinue = unitNextdayBudgetPoList.size() == batchSize;
            // 返回这批最大的 id
            return org.springframework.data.util.Pair.of(needContinue, needContinue ? CollectionUtils.lastElement(unitNextdayBudgetPoList).getId() : -1);
        } catch (Throwable e) {
            LOGGER.error("更新单元次日预算失败", e);
            return org.springframework.data.util.Pair.of(true, unitNextdayBudgetIdGt + batchSize);
        }
    }

    public void batchUpdateUnitNextdayBudget(List<LauUnitNextdayBudgetPo> poList) {
        Map<Integer, List<LauUnitNextdayBudgetPo>> poMap = poList.stream().collect(Collectors.groupingBy(LauUnitNextdayBudgetPo::getUnitId, Collectors.toList()));
        poMap.entrySet().stream().forEach(entry -> {
            Integer unitId = entry.getKey();
            try {

                List<LauUnitNextdayBudgetPo> nextdayBudgetPoList = entry.getValue();
                if (CollectionUtils.isEmpty(nextdayBudgetPoList)) {
                    return;
                }
                LauUnitNextdayBudgetPo nextdayBudgetPo = nextdayBudgetPoList.get(0);

                ((CpcUnitServiceDelegate) AopContext.currentProxy()).updateUnitNextdayBudget(unitId, nextdayBudgetPo);

            } catch (Throwable e) {
                LOGGER.error("更新单元id:" + unitId + "次日预算失败", e);
            }
        });
    }

    @Transactional(transactionManager = AD_TM, rollbackFor = Exception.class)
    public void updateUnitNextdayBudget(Integer unitId, LauUnitNextdayBudgetPo nextdayBudgetPo) {
        LauUnitPo unitPo = lauUnitDao.selectByPrimaryKey(unitId);
        if (unitPo == null) {
            return;
        }
        long oldBudget = unitPo.getBudget();

        unitPo.setBudget(nextdayBudgetPo.getBudget());
        // 日预算
        unitPo.setBudgetType(BudgetType.DAILY.getCode());
        // 设置为手动
        unitPo.setDailyBudgetType(DailyBudgetType.MANUAL.getCode());
        unitPo.setMtime(Utils.getNow());
        // 将次日预算信息保存到单元预算上
        lauUnitDao.updateByPrimaryKeySelective(unitPo);
        lauBudgetServiceDelegate.insertUnitBudget(unitId, nextdayBudgetPo.getBudget());

        // 删除次日预算
        if (Utils.isPositive(nextdayBudgetPo.getIsRepeat())) {
            nextdayBudgetPo.setBudgetEffectiveTime(Utils.getSomeDayAfter(nextdayBudgetPo.getBudgetEffectiveTime(), 1));
            lauUnitNextdayBudgetDao.updateByPrimaryKey(nextdayBudgetPo);
        } else {
            // 删除次日预算
            nextdayBudgetPo.setIsDeleted(IsDeleted.DELETED.getCode());
            lauUnitNextdayBudgetDao.updateByPrimaryKey(nextdayBudgetPo);
        }

        if (unitPo.getIsNewFly() == 1) {
            CpcUnitDto oldUnit = CpcUnitDto.builder().unitId(unitId).targetRules(new LinkedList<>()).budget(oldBudget).build();
            UpdateCpcUnitDto newUnit =
                    UpdateCpcUnitDto.builder().unitId(unitId).targetRules(new LinkedList<>()).budget(nextdayBudgetPo.getBudget()).build();
            saveUnitUpdateLog(Operator.SYSTEM, newUnit, oldUnit);
        }
        // 修改预算日志
        Unit newLogEntity = Unit
                .builder()
                .nextdayBudget(nextdayBudgetPo.getBudget())
                .build();
        Unit oldLogEntity = Unit
                .builder()
                .nextdayBudget(unitPo.getBudget())
                .build();
        logOperateService.addDeleteLog(DbTable.LAU_UNIT, Operator.SYSTEM, oldLogEntity, newLogEntity, unitId);
    }

    /**
     * 获取账户下单元的优化目标 lau_unit
     *
     * @param accountId
     * @return
     */
    public List<Integer> listOcpcTargets(Integer accountId) {
        Assert.notNull(accountId, "账户id不能为空");
        return adCoreBqf
                .selectDistinct(lauUnit.ocpcTarget)
                .from(lauUnit)
                .where(lauUnit.accountId.eq(accountId))
                .fetch();
    }

    /**
     * 根据规则获取广告位组的底价
     *
     * @param param
     * @return
     */
    private Integer getFlyBanner4LowestCost4First4Creative(GetBidCostParam param) {
        final int st = param.getSalesType();
        //起飞cpm底价
        if (ObjectUtils.nullSafeEquals(SalesType.CPM.getCode(), st)) {
            Integer slotGroupId = param.getSlotGroupId();
            //稿件
            if (ObjectUtils.nullSafeEquals(param.getLaunchType(), PromotionPurposeType.ARCHIVE_CONTENT.getCode())) {
                //大卡
                if (creativePositionConfig.getFlySlotArchiveBig().equals(slotGroupId)) {
                    return staticInlineMinPrice;
                }
                //动态
                if (creativePositionConfig.getFlySlotArchiveDynamic().equals(slotGroupId)) {
                    return staticDynamicFlowMinPrice;
                }
                //story
                if (creativePositionConfig.getFlySlotArchiveStory().equals(slotGroupId)) {
                    return storyMinPrice;
                }
                //iPad相关推荐
                if (creativePositionConfig.getFlySlotArchiveIPad().equals(slotGroupId)) {
                    return iPadRecommendMinPrice;
                }
                //PC首页推广栏
                if (creativePositionConfig.getFlySlotArchivePcIndex().equals(slotGroupId)) {
                    return pcIndexMinPrice;
                }
                //PC相关推荐
                if (creativePositionConfig.getFlySlotArchivePcPlay().equals(slotGroupId)) {
                    return pcPlayMinPrice;
                }
                //框下
                if (creativePositionConfig.getFlySlotArchiveUnderBox().equals(slotGroupId)) {
                    return flyArchiveUnderBoxMinPrice;
                }
                //TV inline
                if (creativePositionConfig.getFlySlotArchiveTvInline().equals(slotGroupId)) {
                    return flyArchiveTvInlineMinPrice;
                }
                //搜索点击播放场景
                if (creativePositionConfig.getFlySlotSearch().equals(slotGroupId)) {
                    return flyArchiveSearchMinPrice;
                }
                //搜索自动播放场景
                if (creativePositionConfig.getFlySlotSearchAuto().equals(slotGroupId)) {
                    return flyArchiveSearchMinPrice;
                }
                //PC搜索点击播放场景
                if (creativePositionConfig.getFlyPcSlotSearch().equals(slotGroupId)) {
                    return flyArchivePcSearchMinPrice;
                }
                //手动搜索
                if (creativePositionConfig.getFlySlotChooseSearch().equals(slotGroupId)) {
                    return flyChooseSearchMinPrice;
                }
                //iPAD搜索
                if (creativePositionConfig.getFlyIPadSearchSlotGroupId().equals(slotGroupId)) {
                    return flyIPadSearchMinPrice;
                }
            }
            //直播间
            if (ObjectUtils.nullSafeEquals(param.getLaunchType(), PromotionPurposeType.LIVE_ROOM.getCode())) {
                return flyProLiveRoomMinPrice;
            }
            //动态
            if (ObjectUtils.nullSafeEquals(param.getLaunchType(), PromotionPurposeType.DYNAMIC.getCode())) {
                //手动搜索
                if (creativePositionConfig.getFlySlotChooseSearch().equals(slotGroupId)) {
                    return flyChooseSearchMinPrice;
                }
                return flyDynamicCpmMinPrice;
            }
            //活动
            if (ObjectUtils.nullSafeEquals(param.getLaunchType(), PromotionPurposeType.ACTIVITY.getCode())) {
                //手动搜索
                if (creativePositionConfig.getFlySlotChooseSearch().equals(slotGroupId)) {
                    return flyChooseSearchMinPrice;
                }
                return flyActivityCpmMinPrice;
            }
            //ogv
            if (ObjectUtils.nullSafeEquals(param.getLaunchType(), PromotionPurposeType.OGV.getCode())) {
                //大卡
                if (creativePositionConfig.getFlySlotOgvBig().equals(slotGroupId)) {
                    return ogvMsgFlowBigMinPrice;
                }
                //小卡
                if (creativePositionConfig.getFlySlotOgvSmall().equals(slotGroupId)) {
                    return ogvMsgFlowMinPrice;
                }
                //手动搜索
                if (creativePositionConfig.getFlySlotChooseSearch().equals(slotGroupId)) {
                    return flyChooseSearchMinPrice;
                }

            }
            if (Objects.equals(param.getLaunchType(), PromotionPurposeType.GOODS.getCode())) {
                return GOODS_MIN_PRICE;
            }

        }
        //起飞cpc底价
        if (ObjectUtils.nullSafeEquals(SalesType.CPC.getCode(), st)) {
            Integer slotGroupId = param.getSlotGroupId();
            //稿件
            if (ObjectUtils.nullSafeEquals(param.getLaunchType(), PromotionPurposeType.ARCHIVE_CONTENT.getCode())) {
                //播放页
                if (creativePositionConfig.getFlySlotArchivePlay().equals(slotGroupId) ||
                        creativePositionConfig.getFlySlotArchivePlayUnderBox().equals(slotGroupId)) {
                    return flyCpcPlayMinPrice;
                } else {
                    //信息流
                    return flyCpcInfoMinPrice;
                }
            }
            //直播间
            if (ObjectUtils.nullSafeEquals(param.getLaunchType(), PromotionPurposeType.LIVE_ROOM.getCode())) {
                return flyCpcInfoMinPrice;
            }
            //动态
            if (ObjectUtils.nullSafeEquals(param.getLaunchType(), PromotionPurposeType.DYNAMIC.getCode())) {
                return flyDynamicCpcMinPrice;
            }
            //活动
            if (ObjectUtils.nullSafeEquals(param.getLaunchType(), PromotionPurposeType.ACTIVITY.getCode())) {
                return flyActivityCpcMinPrice;
            }
            if (ObjectUtils.nullSafeEquals(param.getLaunchType(), PromotionPurposeType.OGV.getCode())) {
                return ogvCpcMinPrice;
            }
        }
        //CPM 稿件 信息流&播放页
        return lauBidConfigService.getMinBidCost(param).multiply(HUNDRED).intValue();
    }

    /**
     * 根据起飞 ocpm 优化目标获取第二竞价底价
     * 写死的配置关系: 优化目标对应第二竞价底价
     *
     * @param ocpcTarget
     * @return
     */
    public Integer getOcpcLowesBid(Integer ocpcTarget, Integer accountId, List<Integer> accountLabels) {
        if (!ocpcList.contains(ocpcTarget)) {
            return null;
        }
        if (!CollectionUtils.isEmpty(accountLabels) && accountLabels.contains(flyBrandSpreadAccountOcpmLabel) && OcpcTargetEnum.PAID_IN_24H_ROI.getCode().equals(ocpcTarget)) {
            return brandSpreadPaidIn24HROILostBid;
        }
        if (!CollectionUtils.isEmpty(accountLabels) && accountLabels.contains(flyBrandSpreadAccountOcpmLabel) && OcpcTargetEnum.PAID_IN_7D_COST.getCode().equals(ocpcTarget)) {
            return brandSpreadPaidIn7DCostLostBid;
        }
        return 0;
    }

    /**
     * 根据起飞 ocpm 优化目标获取第二竞价底价
     * 写死的配置关系: 优化目标对应第二竞价底价
     *
     * @param ocpmTarget
     * @return
     */
    public Integer getFlyBanner4LowestCost4Second(Integer ocpmTarget, Integer accountId, List<Integer> accountLabels) {
        if (!flyOcpmList.contains(ocpmTarget)) {
            return 0;
        }

        if (OcpcTargetEnum.FORM_SUBMIT.getCode().equals(ocpmTarget)) {
            return formSubmitTwoStageBid;
        }
        if (OcpcTargetEnum.USER_FOLLOW.getCode().equals(ocpmTarget)) {
            return userFollowTwoStageBidBusiness;
        }
        if (OcpcTargetEnum.VIDEO_PLAY.getCode().equals(ocpmTarget)) {
            return videoPlayTwoStageBid;
        }
        if (OcpcTargetEnum.DYNAMIC_DETAIL_BROWSE.getCode().equals(ocpmTarget)) {
            return dynamicMinTwoStageBid;
        }
        if (OcpcTargetEnum.FIRST_COMMENT_COPY.getCode().equals(ocpmTarget)) {
            return firstCommentCopyTwoStageBid;
        }
        if (OcpcTargetEnum.UNDER_BOX_LINK_CLICK.getCode().equals(ocpmTarget)) {
            return underBoxLinkClickTwoStageBid;
        }

        if (OcpcTargetEnum.ACTIVITY_PAGE_PULL_UP.getCode().equals(ocpmTarget)) {
            int activityPagePullUpMinPrice = 50;
            return activityPagePullUpMinPrice;
        }
        if (OcpcTargetEnum.COMMENT_CLICK.getCode().equals(ocpmTarget)) {
            return commentLickTwoStageBid;
        }
        if (OcpcTargetEnum.SHOPPING_CART.getCode().equals(ocpmTarget)) {
            return shoppingCartTwoStageBid;
        }
        if (OcpcTargetEnum.LP_CALL_UP_SUCCESS.getCode().equals(ocpmTarget)) {
            return storyYellowCallupTwoStageBid;
        }
        if (OcpcTargetEnum.GOODS_TRANSACTION.getCode().equals(ocpmTarget)) {
            AccountBaseDto accountBaseDto = queryAccountService.getAccountBaseDtoById(accountId);
            if (accountBaseDto != null && Integer.valueOf(311).equals(accountBaseDto.getDepartmentId())) {
                return orderPostTwoStageBid4Daihuo;
            } else {
                return orderPostTwoStageBid;
            }
        }
        if (OcpcTargetEnum.LIVE_CALLUP.getCode().equals(ocpmTarget)) {
            // 是否配置了特殊出价
            if (specialLiveCallUpTwoStageBidMap.containsKey(accountId)) {
                return specialLiveCallUpTwoStageBidMap.get(accountId);
            } else {
                return generalLiveCallUpTwoStageBid;
            }
        }
        if (OcpcTargetEnum.LIVE_RESERVE.getCode().equals(ocpmTarget)) {
            return liveReserveTwoStageBid;
        }
        if (!CollectionUtils.isEmpty(accountLabels) && accountLabels.contains(flyBrandSpreadAccountOcpmLabel) && OcpcTargetEnum.PAID_IN_24H_ROI.getCode().equals(ocpmTarget)) {
            return brandSpreadPaidIn24HROILostBid;
        }
        if (!CollectionUtils.isEmpty(accountLabels) && accountLabels.contains(flyBrandSpreadAccountOcpmLabel) && OcpcTargetEnum.PAID_IN_7D_COST.getCode().equals(ocpmTarget)) {
            return brandSpreadPaidIn7DCostLostBid;
        }
        return null;
    }

    public void doUnBindUnitTargetPackage(List<Integer> unitIds) {
        if (CollectionUtils.isEmpty(unitIds)) {
            return;
        }
        LauUnitPoExample example = new LauUnitPoExample();
        example.or().andUnitIdIn(unitIds);
        lauUnitDao.updateByExampleSelective(LauUnitPo.builder()
                .targetPackageId(0)
                .build(), example);
    }

    // tmp
    public void updateUnitOcpxTargetTwoBid(List<Integer> unitIdList) {
        List<com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauUnitPo> updatePos =
                adCoreBqf.selectFrom(lauUnit)
                        .where(lauUnit.unitId.in(unitIdList))
                        .fetch().stream().map(lauUnitPo -> {
                            com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauUnitPo updatePo = new com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauUnitPo();
                            updatePo.setUnitId(lauUnitPo.getUnitId());
                            updatePo.setOcpxTargetTwoBid(lauUnitPo.getOcpxTargetTwoBid() / 100);
                            return updatePo;
                        }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(updatePos)) {
            return;
        }

        adCoreBqf.update(lauUnit)
                .updateBeans(updatePos);
    }

    public void saveSmartKeySwitch(Integer smartKeyWord, Operator operator, Integer unitId) {
        LauUnitExtraPo po = adCoreBqf.select(lauUnitExtra)
                .from(lauUnitExtra)
                .where(lauUnitExtra.unitId.eq(unitId)).fetchOne();

        //新增
        if (Objects.isNull(po)) {
            LauUnitExtraPo addPo = new LauUnitExtraPo();
            addPo.setUnitId(unitId);
            addPo.setAccountId(operator.getOperatorId());
            addPo.setSmartKeyWord(smartKeyWord);

            adCoreBqf.insert(lauUnitExtra).insertBean(addPo);
            return;
        }

        //修改
        Integer poSmartKeyWord = po.getSmartKeyWord();
        //相等不需要做修改
        if (smartKeyWord.equals(poSmartKeyWord)) {
            return;
        }

        adCoreBqf.update(lauUnitExtra)
                .set(lauUnitExtra.smartKeyWord, smartKeyWord)
                .where(lauUnitExtra.unitId.eq(unitId)).execute();

    }

    public Integer getSmartKeySwitch(Integer unitId) {
        LauUnitExtraPo po = adCoreBqf.select(lauUnitExtra)
                .from(lauUnitExtra)
                .where(lauUnitExtra.unitId.eq(unitId)).fetchOne();

        //空返回默认值
        if (Objects.isNull(po)) {
            return CommonSwitchEnum.ENABLED.getCode();
        }

        return po.getSmartKeyWord();
    }

    public Integer refreshUnitNumByAccountId(Integer accountId, Integer type) {
        String redisKeyPrefix = UnitCountEnum.getByCode(type).getTotalRedisKeyPrefix();
        Integer count = getUnitNumByAccountId(accountId, type);
        String redisKey = redisKeyPrefix + accountId;
        redisTemplate.opsForValue().set(redisKey, count.toString());
        return count;
    }

    public void incrUnitNumByAccountId(Integer accountId, Long num, Integer type) {
        String redisKeyPrefix = UnitCountEnum.getByCode(type).getTotalRedisKeyPrefix();
        String redisKey = redisKeyPrefix + accountId;
        redisTemplate.opsForValue().increment(redisKey, num);
    }

    public Integer getUnitNumByAccountIdWithCache(Integer accountId, Integer type) {
        String redisKeyPrefix = UnitCountEnum.getByCode(type).getTotalRedisKeyPrefix();
        String redisKey = redisKeyPrefix + accountId;
        String countStr = redisTemplate.opsForValue().get(redisKey);
        if (StringUtils.isEmpty(countStr)) {
            return refreshUnitNumByAccountId(accountId, type);
        }
        return Integer.parseInt(countStr);
    }

    public Integer getUnitNumByAccountIdWithCacheForLog(Integer accountId, Integer type) {
        String redisKeyPrefix = UnitCountEnum.getByCode(type).getTotalRedisKeyPrefix();
        String redisKey = redisKeyPrefix + accountId;
        String countStr = redisTemplate.opsForValue().get(redisKey);
        return StringUtils.hasText(countStr) ? Integer.parseInt(countStr) : 0;
    }

    private Integer getUnitNumByAccountId(Integer accountId, Integer type) {
//        String dataSourceType = DataSourceHolder.getDataSourceType();
//        DataSourceHolder.chooseDataSourceType(DataSourceTypeEnum.READ);
//        try {
//            return ((CpcUnitServiceDelegate) AopContext.currentProxy()).doGetUnitNumByAccountId(accountId, type);
//        } finally {
//            DataSourceTypeEnum dataSourceTypeEnum = Objects.equals(DataSourceTypeEnum.READ.getCode(), dataSourceType) ? DataSourceTypeEnum.READ : DataSourceTypeEnum.WRITE;
//            DataSourceHolder.chooseDataSourceType(dataSourceTypeEnum);
//        }

        // 从库延迟过大 现在这里先用主库
        return doGetUnitNumByAccountId(accountId, type);
    }

    public Integer doGetUnitNumByAccountId(Integer accountId, Integer type) {
        if (Objects.equals(UnitCountEnum.SANLIAN.getCode(), type)) {
            return adCoreBqf.select(lauUnit.count())
                    .from(lauUnit)
                    .where(lauUnit.accountId.eq(accountId))
                    .where(lauUnit.isDeleted.eq(0))
                    .where(lauUnit.status.ne(com.bilibili.adp.cpc.enums.LaunchStatus.DELETE.getCode()))
                    .where(lauUnit.adpVersion.in(AdpVersion.MIDDLE_AND_MERGE_LIST))
                    .where(lauUnit.parentUnitId.eq(0))
                    .addFlag(QueryFlag.Position.BEFORE_FILTERS, " FORCE INDEX (idx_account_id)")
                    .fetchFirst().intValue();

        } else if (Objects.equals(UnitCountEnum.AUTO_PARENT_UNIT.getCode(), type)) {
            List<Integer> autoCampaignIds = adCoreBqf.select(lauCampaign.campaignId)
                    .from(lauCampaign)
                    .where(lauCampaign.accountId.eq(accountId))
                    .where(lauCampaign.status.ne(com.bilibili.adp.cpc.enums.LaunchStatus.DELETE.getCode()))
                    .where(lauCampaign.isDeleted.eq(0))
                    .where(lauCampaign.adpVersion.eq(AdpVersion.CPC_FLY_MERGE.getKey()))
                    .where(lauCampaign.supportAuto.eq(SupportAuto.AUTO.getCode()))
                    .fetch();
            if (CollectionUtils.isEmpty(autoCampaignIds)) {
                return 0;
            }

            return adCoreBqf.select(lauUnit.count())
                    .from(lauUnit)
                    .where(lauUnit.accountId.eq(accountId))
                    .where(lauUnit.campaignId.in(autoCampaignIds))
                    .where(lauUnit.isDeleted.eq(0))
                    .where(lauUnit.status.ne(com.bilibili.adp.cpc.enums.LaunchStatus.DELETE.getCode()))
                    .where(lauUnit.adpVersion.eq(AdpVersion.CPC_FLY_MERGE.getKey()))
                    .where(lauUnit.parentUnitId.eq(0))
                    .addFlag(QueryFlag.Position.BEFORE_FILTERS, " FORCE INDEX (idx_campaign_id)")
                    .fetchFirst().intValue();
        }
        return 0;
    }

    public Integer refreshTodayUnitNumByAccountId(Integer accountId, String todayStr, Integer type) {
        String redisKeyPrefix = UnitCountEnum.getByCode(type).getTodayRedisKeyPrefix();
        Integer count = getTodayUnitNumByAccountId(accountId, todayStr, type);
        String redisKey = redisKeyPrefix + accountId + CIPHERTEXT_SEPERATOR + todayStr;
        redisTemplate.opsForValue().set(redisKey, count.toString(), 24L, TimeUnit.HOURS);
        return count;
    }

    public void incrTodayUnitNumByAccountId(Integer accountId, String todayStr, Long num, Integer type) {
        String redisKeyPrefix = UnitCountEnum.getByCode(type).getTodayRedisKeyPrefix();
        String redisKey = redisKeyPrefix + accountId + CIPHERTEXT_SEPERATOR + todayStr;
        redisTemplate.opsForValue().increment(redisKey, num);
        redisTemplate.expire(redisKey, 24L, TimeUnit.HOURS);
    }

    public Integer getTodayUnitNumByAccountIdWithCache(Integer accountId, String todayStr, Integer type) {
        String redisKeyPrefix = UnitCountEnum.getByCode(type).getTodayRedisKeyPrefix();
        String redisKey = redisKeyPrefix + accountId + CIPHERTEXT_SEPERATOR + todayStr;
        String countStr = redisTemplate.opsForValue().get(redisKey);
        if (StringUtils.isEmpty(countStr)) {
            return refreshTodayUnitNumByAccountId(accountId, todayStr, type);
        }
        return Integer.parseInt(countStr);
    }

    private Integer getTodayUnitNumByAccountId(Integer accountId, String todayStr, Integer type) {
        String dataSourceType = DataSourceHolder.getDataSourceType();
        DataSourceHolder.chooseDataSourceType(DataSourceTypeEnum.READ);
        try {
            return ((CpcUnitServiceDelegate) AopContext.currentProxy()).doGetTodayUnitNumByAccountId(accountId, todayStr, type);
        } finally {
            DataSourceTypeEnum dataSourceTypeEnum = Objects.equals(DataSourceTypeEnum.READ.getCode(), dataSourceType) ? DataSourceTypeEnum.READ : DataSourceTypeEnum.WRITE;
            DataSourceHolder.chooseDataSourceType(dataSourceTypeEnum);
        }
    }

    @Transactional(value = AD_CORE_TM, propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public Integer doGetTodayUnitNumByAccountId(Integer accountId, String todayStr, Integer type) {
        if (Objects.equals(UnitCountEnum.OPEN_API.getCode(), type)) {
            LauUnitPoExample example = new LauUnitPoExample();
            example.or()
                    .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                    .andCtimeGreaterThanOrEqualTo(TimeUtils.todayStart())
                    .andCtimeLessThanOrEqualTo(TimeUtils.todayEnd())
                    .andAccountIdEqualTo(accountId)
                    .andFlagEqualTo(FlagEnums.OPEN_API.getId())
                    .andStatusNotEqualTo(com.bilibili.adp.cpc.enums.LaunchStatus.DELETE.getCode());

            return (int)lauUnitDao.countByExample(example);
        } else if (Objects.equals(UnitCountEnum.SANLIAN.getCode(), type)) {
            // 无降级 如有需要后续doris新版本接口支持以后考虑查doris 理论上binlog缓存已经足够
            return 0;
        } else if (Objects.equals(UnitCountEnum.AUTO_PARENT_UNIT.getCode(), type)) {
            // 无降级 目前查询逻辑过于复杂 目测降级后性能有问题
            return 0;
        }
        return 0;
    }

    public List<LauUnitPo> getUnitsByAccount(List<Integer> accountIds, Integer adpVersion, Integer ppt, Integer minUnitId, Integer limit) {
        LauUnitPoExample example = new LauUnitPoExample();
        example.or()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andAccountIdIn(accountIds)
                .andUnitStatusNotIn(Arrays.asList(UnitStatus.FINISHED.getCode(), UnitStatus.DELETED.getCode(), UnitStatus.COMPLETED.getCode()))
                .andAdpVersionEqualTo(adpVersion)
                .andPromotionPurposeTypeEqualTo(ppt)
                .andUnitIdGreaterThan(minUnitId);
        example.setLimit(limit);

        example.setOrderByClause("unit_id asc");
        return lauUnitDao.selectByExample(example);
    }

}
