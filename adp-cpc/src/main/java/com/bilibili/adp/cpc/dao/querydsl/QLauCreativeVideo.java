package com.bilibili.adp.cpc.dao.querydsl;

import static com.querydsl.core.types.PathMetadataFactory.*;
import com.bilibili.adp.cpc.dao.querydsl.pos.LauCreativeVideoPo;


import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;

import com.querydsl.sql.ColumnMetadata;
import java.sql.Types;




/**
 * QLauCreativeVideo is a Querydsl query type for LauCreativeVideoPo
 */
@Generated("com.querydsl.sql.codegen.MetaDataSerializer")
public class QLauCreativeVideo extends com.querydsl.sql.RelationalPathBase<LauCreativeVideoPo> {

    private static final long serialVersionUID = -644623376;

    public static final QLauCreativeVideo lauCreativeVideo = new QLauCreativeVideo("lau_creative_video");

    public final NumberPath<Long> avid = createNumber("avid", Long.class);

    public final NumberPath<Integer> bizId = createNumber("bizId", Integer.class);

    public final NumberPath<Long> cid = createNumber("cid", Long.class);

    public final StringPath cover = createString("cover");

    public final NumberPath<Integer> creativeId = createNumber("creativeId", Integer.class);

    public final DateTimePath<java.sql.Timestamp> ctime = createDateTime("ctime", java.sql.Timestamp.class);

    public final NumberPath<Integer> duration = createNumber("duration", Integer.class);

    public final NumberPath<Integer> dynamicTime = createNumber("dynamicTime", Integer.class);

    public final NumberPath<Integer> height = createNumber("height", Integer.class);

    public final NumberPath<Integer> id = createNumber("id", Integer.class);

    public final NumberPath<Integer> isDeleted = createNumber("isDeleted", Integer.class);

    public final StringPath md5 = createString("md5");

    public final NumberPath<Integer> mgkVideoId = createNumber("mgkVideoId", Integer.class);

    public final DateTimePath<java.sql.Timestamp> mtime = createDateTime("mtime", java.sql.Timestamp.class);

    public final NumberPath<Integer> orientation = createNumber("orientation", Integer.class);

    public final NumberPath<Integer> page = createNumber("page", Integer.class);

    public final NumberPath<Integer> size = createNumber("size", Integer.class);

    public final StringPath source = createString("source");

    public final StringPath videoUrl = createString("videoUrl");

    public final NumberPath<Integer> width = createNumber("width", Integer.class);

    public final com.querydsl.sql.PrimaryKey<LauCreativeVideoPo> primary = createPrimaryKey(id);

    public QLauCreativeVideo(String variable) {
        super(LauCreativeVideoPo.class, forVariable(variable), "null", "lau_creative_video");
        addMetadata();
    }

    public QLauCreativeVideo(String variable, String schema, String table) {
        super(LauCreativeVideoPo.class, forVariable(variable), schema, table);
        addMetadata();
    }

    public QLauCreativeVideo(String variable, String schema) {
        super(LauCreativeVideoPo.class, forVariable(variable), schema, "lau_creative_video");
        addMetadata();
    }

    public QLauCreativeVideo(Path<? extends LauCreativeVideoPo> path) {
        super(path.getType(), path.getMetadata(), "null", "lau_creative_video");
        addMetadata();
    }

    public QLauCreativeVideo(PathMetadata metadata) {
        super(LauCreativeVideoPo.class, metadata, "null", "lau_creative_video");
        addMetadata();
    }

    public void addMetadata() {
        addMetadata(avid, ColumnMetadata.named("avid").withIndex(7).ofType(Types.BIGINT).withSize(20).notNull());
        addMetadata(bizId, ColumnMetadata.named("biz_id").withIndex(4).ofType(Types.INTEGER).withSize(10).notNull());
        addMetadata(cid, ColumnMetadata.named("cid").withIndex(8).ofType(Types.BIGINT).withSize(20).notNull());
        addMetadata(cover, ColumnMetadata.named("cover").withIndex(6).ofType(Types.VARCHAR).withSize(128).notNull());
        addMetadata(creativeId, ColumnMetadata.named("creative_id").withIndex(2).ofType(Types.INTEGER).withSize(10).notNull());
        addMetadata(ctime, ColumnMetadata.named("ctime").withIndex(19).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(duration, ColumnMetadata.named("duration").withIndex(16).ofType(Types.INTEGER).withSize(10).notNull());
        addMetadata(dynamicTime, ColumnMetadata.named("dynamic_time").withIndex(17).ofType(Types.INTEGER).withSize(10).notNull());
        addMetadata(height, ColumnMetadata.named("height").withIndex(12).ofType(Types.INTEGER).withSize(10).notNull());
        addMetadata(id, ColumnMetadata.named("id").withIndex(1).ofType(Types.INTEGER).withSize(10).notNull());
        addMetadata(isDeleted, ColumnMetadata.named("is_deleted").withIndex(18).ofType(Types.TINYINT).withSize(3).notNull());
        addMetadata(md5, ColumnMetadata.named("md5").withIndex(14).ofType(Types.VARCHAR).withSize(32).notNull());
        addMetadata(mgkVideoId, ColumnMetadata.named("mgk_video_id").withIndex(3).ofType(Types.INTEGER).withSize(10).notNull());
        addMetadata(mtime, ColumnMetadata.named("mtime").withIndex(20).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(orientation, ColumnMetadata.named("orientation").withIndex(13).ofType(Types.INTEGER).withSize(10).notNull());
        addMetadata(page, ColumnMetadata.named("page").withIndex(9).ofType(Types.INTEGER).withSize(10).notNull());
        addMetadata(size, ColumnMetadata.named("size").withIndex(15).ofType(Types.INTEGER).withSize(10).notNull());
        addMetadata(source, ColumnMetadata.named("source").withIndex(10).ofType(Types.VARCHAR).withSize(16).notNull());
        addMetadata(videoUrl, ColumnMetadata.named("video_url").withIndex(5).ofType(Types.VARCHAR).withSize(128).notNull());
        addMetadata(width, ColumnMetadata.named("width").withIndex(11).ofType(Types.INTEGER).withSize(10).notNull());
    }

}

