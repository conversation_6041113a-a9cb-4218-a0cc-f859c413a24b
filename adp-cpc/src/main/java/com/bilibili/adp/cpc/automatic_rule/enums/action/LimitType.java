package com.bilibili.adp.cpc.automatic_rule.enums.action;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum LimitType {
    UNLIMITED(0, "不限"),
    SPECIFIED(1, "指定上限"),
    ;

    private final int code;
    private final String desc;

    public static LimitType getByCode(int code) {
        return Arrays.stream(values())
                .filter(limitType -> limitType.code == code)
                .findFirst()
                .orElseThrow(IllegalArgumentException::new);
    }
}
