package com.bilibili.adp.cpc.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 * @date 2024/2/22 18:27
 */
@Configuration
@Slf4j
public class ArchiveBizAnchorThreadPoolConfig {


    @Bean
    public ThreadPoolTaskExecutor archiveBizAnchorThreadPoolExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(20);
        executor.setKeepAliveSeconds(300);
        executor.setMaxPoolSize(30);
        executor.setQueueCapacity(20);
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setThreadFactory(r -> new Thread(r, "archive_biz_anchor_query_" + r.hashCode()));
        log.info("archiveBizAnchorThreadPoolExecutor create ok...");
        return executor;
    }
}
