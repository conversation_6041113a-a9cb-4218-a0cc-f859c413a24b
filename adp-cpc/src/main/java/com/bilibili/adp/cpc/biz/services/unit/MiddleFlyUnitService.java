package com.bilibili.adp.cpc.biz.services.unit;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.GsonUtils;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.common.util.func.Functions;
import com.bilibili.adp.cpc.biz.services.dynamic.DynamicContentMap;
import com.bilibili.adp.cpc.biz.services.dynamic.DynamicLinkProc;
import com.bilibili.adp.cpc.biz.services.dynamic.FlyDynamicService;
import com.bilibili.adp.cpc.biz.services.live.AdpCpcLiveReserveService;
import com.bilibili.adp.cpc.biz.services.live.bos.LiveReservationInfoBo;
import com.bilibili.adp.cpc.biz.services.search_ad_unit.EffectAdSearchAdUnitService;
import com.bilibili.adp.cpc.biz.services.unit.bos.MiddleDynamicBo;
import com.bilibili.adp.cpc.biz.services.unit.bos.MiddleFlyUnitBo;
import com.bilibili.adp.cpc.biz.services.unit.dto.CpcUnitDto;
import com.bilibili.adp.cpc.dao.ad.NewLauUnitFlyMiddleInfoDao;
import com.bilibili.adp.cpc.dto.MiddleFlyUnitCreateDto;
import com.bilibili.adp.cpc.enums.ad.CampaignAdType;
import com.bilibili.adp.cpc.enums.ad.PromotionPurposeType;
import com.bilibili.adp.cpc.po.ad.LauUnitFlyMiddleInfoPo;
import com.bilibili.adp.cpc.po.ad.LauUnitFlyMiddleInfoPoExample;
import com.bilibili.adp.launch.api.flyPro.dto.enums.DynamicTypeEnum;
import com.bilibili.adp.passport.biz.manager.bean.dynamic.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * copy class
 * @see com.bilibili.adp.launch.biz.middle.fly.MiddleFlyUnitService
 */
@Primary
@Slf4j
@Service(value = "LaunchMiddleFlyUnitService")
public class MiddleFlyUnitService {

    @Autowired
    private NewLauUnitFlyMiddleInfoDao newLauUnitFlyMiddleInfoDao;
    @Autowired
    private FlyDynamicService flyDynamicService;
    @Autowired
    private EffectAdSearchAdUnitService effectAdSearchAdUnitService;
    @Autowired
    private AdpCpcLiveReserveService adpCpcLiveReserveService;
    @Autowired
    private DynamicLinkProc dynamicLinkProc;

    public MiddleFlyUnitBo getUnitDetail(String cpcUnitDtoStr, Integer accountId, Integer adType) throws ServiceException {
        CpcUnitDto unit = GsonUtils.toObject(cpcUnitDtoStr, CpcUnitDto.class);
        Assert.isTrue(accountId.equals(unit.getAccountId()), "您不能操作不属于您的单元");

        MiddleFlyUnitBo middleFlyUnitDto = MiddleFlyUnitBo.builder()
                .cpcUnitDto(unit)
                .build();

        PromotionPurposeType promotionPurposeType = PromotionPurposeType.getByCode(unit.getPromotionPurposeType());
        switch (promotionPurposeType) {
            case DYNAMIC: // 动态
                LauUnitFlyMiddleInfoPo po = this.queryLauUnitFlyMiddleInfo(unit.getUnitId());

                middleFlyUnitDto.setDynamicId(po == null ? 0L : po.getDynamicId());
                middleFlyUnitDto.setDynamicType(po == null ? 0 : po.getDynamicType());
                middleFlyUnitDto.setDynamicLink(po == null ? "" : po.getDynamicLink());
                middleFlyUnitDto.setDynamicSkipLink(dynamicLinkProc.spliceDynamicSkipLinkByEnv(po.getDynamicId()));
                MiddleDynamicBo middleDynamicBo = new MiddleDynamicBo();

                // 获取动态直播预约信息
                if (Utils.isPositive(po.getSid())) {
                    LiveReservationInfoBo liveReservationInfoBo = adpCpcLiveReserveService.queryBiliLiveReserveInfo(po.getSid());
                    middleDynamicBo.setSid(po.getSid());
                    middleDynamicBo.setLiveReservationInfoBo(liveReservationInfoBo);
                }

                if(DynamicTypeEnum.CHOOSE_DYNAMIC.getCode().equals(po.getDynamicType())) {
                    DynamicContentMap.DynamicState dynamicState = flyDynamicService.getDynamicState(po.getDynamicId());
                    if (!Objects.isNull(dynamicState)){
                        Assert.isTrue(!Functions.integer2Boolean(Optional.ofNullable(dynamicState.getIsDeleted()).orElse(0L).intValue()), "动态已删除");
                    }

                    DynamicDetailInfo dynamicDetailInfo = flyDynamicService.getDynamicById(po.getDynamicId());
                    Desc desc = Optional.ofNullable(dynamicDetailInfo.getData())
                            .map(p -> p.getCard()).map(p -> p.getDesc()).orElse(Desc.builder().build());
                    UserProfileInfo userProfileInfo = Optional.ofNullable(dynamicDetailInfo.getData())
                            .map(p -> p.getCard()).map(p -> p.getDesc()).map(p -> p.getUser_profile()).map(p -> p.getInfo())
                            .orElse(UserProfileInfo.builder()
                                    .uid(0L)
                                    .uname("")
                                    .face("")
                                    .build());
                    String card = Optional.ofNullable(dynamicDetailInfo.getData())
                            .map(p -> p.getCard()).map(p -> p.getCard()).orElse("");
                    CardCard cardCard = JSON.parseObject(card, CardCard.class);

                    if (cardCard != null) {
                        Item item = cardCard.getItem();
                        if (item != null) {
                            middleDynamicBo.setDynamicContent(item.getDescription());
                            List<Picture> pictures = item.getPictures();
                            if (!CollectionUtils.isEmpty(pictures)) {
                                middleDynamicBo.setDynamicImages(pictures.stream().map(p -> p.getImg_src()).collect(Collectors.toList()));
                            }

                        }
                    }
                    middleDynamicBo.setDynamicId(po.getDynamicId());
                    middleDynamicBo.setFace(userProfileInfo.getFace());
                    middleDynamicBo.setNickname(userProfileInfo.getUname());
                    middleDynamicBo.setPubTime(new Timestamp(desc.getTimestamp() * 1000));
                }
                middleFlyUnitDto.setMiddleDynamicBo(middleDynamicBo);
                break;
            case ACTIVITY: // 活动
                LauUnitFlyMiddleInfoPo activityLinkPo = this.queryLauUnitFlyMiddleInfo(unit.getUnitId());
                middleFlyUnitDto.setActivityLink(activityLinkPo == null ? "" : activityLinkPo.getActivityLink());
                break;
            default:
                break;
        }
        // 搜索广告，查关键词
        if (CampaignAdType.SEARCH.getCode().equals(adType) || CampaignAdType.ALL.getCode().equals(adType)) {
            middleFlyUnitDto.setUnitKeywordsBo(effectAdSearchAdUnitService.fetch(unit.getUnitId()));
        }
        return middleFlyUnitDto;
    }

    public LauUnitFlyMiddleInfoPo queryLauUnitFlyMiddleInfo(Integer unitId) {
        LauUnitFlyMiddleInfoPoExample example = new LauUnitFlyMiddleInfoPoExample();
        example.or().andUnitIdEqualTo(unitId);
        List<LauUnitFlyMiddleInfoPo> pos = newLauUnitFlyMiddleInfoDao.selectByExample(example);
        if (CollectionUtils.isEmpty(pos)) {
            return null;
        }
        return pos.get(0);
    }

    /**
     * 保存中台起飞单元信息(动态和活动的才需要)
     * @param dto
     */
    public void insertLauUnitFlyMiddleInfo(MiddleFlyUnitCreateDto dto) {
        // 动态 || 活动
        if (dto.getPromotionPurposeType() == PromotionPurposeType.DYNAMIC.getCode() || (dto.getPromotionPurposeType() == PromotionPurposeType.ACTIVITY.getCode())) {
            LauUnitFlyMiddleInfoPo po = LauUnitFlyMiddleInfoPo.builder()
                    .accountId(dto.getAccountId())
                    .unitId(dto.getUnitId())
                    .sid(dto.getSid())
                    .promotionPurposeType(dto.getPromotionPurposeType())
                    .build();
            // 动态
            if (dto.getPromotionPurposeType() == PromotionPurposeType.DYNAMIC.getCode()){
                po.setDynamicType(dto.getDynamicType());
                po.setDynamicId(DynamicTypeEnum.CHOOSE_DYNAMIC.getCode().equals(dto.getDynamicType()) ? dto.getDynamicId() : 0L);
                po.setDynamicLink(dto.getDynamicLink());
            }
            // 活动
            else {
                po.setActivityLink(dto.getActivityLink());
            }
            newLauUnitFlyMiddleInfoDao.insertSelective(po);
        }
    }


    private String generateLikeCount(Integer like) {
        if (like == null || like == 0) {
            return "-";
        }
        if (like > 10000) {
            return like / 10000 + "." + like / 1000 % 10 + "万";
        }
        return like.toString();
    }

    private String generateNickName(String nickname) {
        if (!org.apache.commons.lang3.StringUtils.isBlank(nickname) && nickname.length() > 6) {
            return nickname.substring(0, 6) + "...";
        }
        return nickname;
    }
}
