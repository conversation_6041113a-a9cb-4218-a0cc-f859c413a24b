package com.bilibili.adp.cpc.dao.querydsl.pos;

import javax.annotation.Generated;

/**
 * AdTaskRuleConfigPo is a Querydsl bean type
 */
@Generated("com.querydsl.codegen.BeanSerializer")
public class AdTaskRuleConfigPo {

    private Integer conditionCode;

    private Integer conditionExpression;

    private String conditionName;

    private String convTarget;

    private java.sql.Timestamp ctime;

    private Long id;

    private Integer isDeleted;

    private java.sql.Timestamp mtime;

    private Long taskId;

    private String value;

    public Integer getConditionCode() {
        return conditionCode;
    }

    public void setConditionCode(Integer conditionCode) {
        this.conditionCode = conditionCode;
    }

    public Integer getConditionExpression() {
        return conditionExpression;
    }

    public void setConditionExpression(Integer conditionExpression) {
        this.conditionExpression = conditionExpression;
    }

    public String getConditionName() {
        return conditionName;
    }

    public void setConditionName(String conditionName) {
        this.conditionName = conditionName;
    }

    public String getConvTarget() {
        return convTarget;
    }

    public void setConvTarget(String convTarget) {
        this.convTarget = convTarget;
    }

    public java.sql.Timestamp getCtime() {
        return ctime;
    }

    public void setCtime(java.sql.Timestamp ctime) {
        this.ctime = ctime;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public java.sql.Timestamp getMtime() {
        return mtime;
    }

    public void setMtime(java.sql.Timestamp mtime) {
        this.mtime = mtime;
    }

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    @Override
    public String toString() {
         return "conditionCode = " + conditionCode + ", conditionExpression = " + conditionExpression + ", conditionName = " + conditionName + ", convTarget = " + convTarget + ", ctime = " + ctime + ", id = " + id + ", isDeleted = " + isDeleted + ", mtime = " + mtime + ", taskId = " + taskId + ", value = " + value;
    }

}

