package com.bilibili.adp.cpc.wrapper.bos;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ArchiveWrapperBo {
    @ApiModelProperty("主站用户id")
    private Long mid;
    @ApiModelProperty("主站用户头像")
    private String face;
    @ApiModelProperty("主站用户昵称")
    private String nickName;
    @ApiModelProperty("稿件id")
    private Long avid;
    @ApiModelProperty("稿件对应的视频云id")
    private Long cid;
    @ApiModelProperty("稿件标题")
    private String title;
    @ApiModelProperty("稿件封面url")
    private String coverUrl;
    @ApiModelProperty("播放数")
    private Long playCount;
    @ApiModelProperty("点赞数")
    private Long likeCount;
    @ApiModelProperty("播放时长(秒)")
    private Long duration;
}
