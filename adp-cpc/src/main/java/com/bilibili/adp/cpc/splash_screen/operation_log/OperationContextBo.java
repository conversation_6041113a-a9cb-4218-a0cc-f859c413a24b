package com.bilibili.adp.cpc.splash_screen.operation_log;

import com.bilibili.adp.cpc.compare.ChangeBo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OperationContextBo {
    // 参数
    private String tableName;
    private Integer objId;
    private Integer operationType;
    private OperatorBo operator;
    // 中间结果
    private List<ChangeBo> changes;
    private boolean reAudit;
}
