package com.bilibili.adp.cpc.splash_screen.creative.bos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SplashScreenTarget {
    //地域定向
    private String area;

    //性别定向
    private String gender;

    //年龄定向
    private String age;

    //平台选择
    private String platform;

    //设备品牌
    private String deviceBrand;

    //包含人群包
    private String crowdPacks;

    //排除人群包
    private String excludeCrowdPacks;
}
