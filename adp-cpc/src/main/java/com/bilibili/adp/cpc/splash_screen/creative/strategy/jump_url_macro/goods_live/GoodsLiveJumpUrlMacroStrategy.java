package com.bilibili.adp.cpc.splash_screen.creative.strategy.jump_url_macro.goods_live;

import com.bilibili.adp.cpc.enums.ad.PromotionPurposeType;
import com.bilibili.adp.cpc.splash_screen.creative.strategy.bos.CreativeContext;
import com.bilibili.adp.cpc.splash_screen.creative.strategy.jump_url_macro.IJumpUrlMacroStrategy;
import org.springframework.stereotype.Service;
import org.springframework.web.util.UriComponentsBuilder;

@Service
public class GoodsLiveJumpUrlMacroStrategy implements IJumpUrlMacroStrategy {
    private static final String FROM = "from";

    private static final String FROM_VALUE = "__JUMPFROM__";

    private static final String EXTRA_JUMP_FROM = "extra_jump_from";

    private static final String EXTRA_JUMP_FROM_VALUE = "__JUMPFROM__";

    private static final String LAUNCH_TYPE = "launch_type";

    private static final String LAUNCH_TYPE_VALUE = "cpm_shangye";

    private static final String LAUNCH_ID = "launch_id";
    private static final String LAUNCH_ID_VALUE = "__CREATIVEID__";

    private static final String SESSION_ID = "session_id";
    private static final String SESSION_ID_VALUE = "__REQUESTID__";

    private static final String SOURCE_ID = "source_id";
    private static final String SOURCE_ID_VALUE = "__SOURCEID__";

    private static final String RESOURCE_ID = "resource_id";
    private static final String RESOURCE_ID_VALUE = "__RESOURCEID__";

    private static final String CREATIVE_ID = "creative_id";
    private static final String LINKED_CREATIVE_ID = "linked_creative_id";



    private static final String TRACK_ID = "track_id";
    private static final String TRACK_ID_VALUE = "__TRACKID__";

    private static final String FROM_SPMID = "from_spmid";
    private static final String FROM_SPMID_VALUE = "__FROMSPMID__";

    private static final String TRACKID = "trackid";
    private static final String TRACKID_VALUE = "__FROMTRACKID__";

    private static final String REQUEST_ID = "request_id";
    private static final String REQUEST_ID_VALUE = "__REQUESTID__";
    private static final String CAID = "caid";
    private static final String CAID_VALUE = "__CAID__";


    //=&creative_id=140370057&linked_creative_id=140370057
    @Override
    public Integer getPromotionPurposeType() {
        return PromotionPurposeType.GOODS_LIVE.getCode();
    }

    @Override
    public String addMacro(CreativeContext context, String jumpUrl) {
        final UriComponentsBuilder builder = UriComponentsBuilder.fromUriString(jumpUrl);
        builder.replaceQueryParam(FROM, FROM_VALUE);
        builder.replaceQueryParam(EXTRA_JUMP_FROM, EXTRA_JUMP_FROM_VALUE);
        builder.replaceQueryParam(LAUNCH_TYPE, LAUNCH_TYPE_VALUE);
        builder.replaceQueryParam(LAUNCH_ID, LAUNCH_ID_VALUE);
        builder.replaceQueryParam(SESSION_ID, SESSION_ID_VALUE);
        builder.replaceQueryParam(SOURCE_ID, SOURCE_ID_VALUE);
        builder.replaceQueryParam(RESOURCE_ID, RESOURCE_ID_VALUE);
        builder.replaceQueryParam(TRACK_ID, TRACK_ID_VALUE);
        builder.replaceQueryParam(FROM_SPMID, FROM_SPMID_VALUE);
        builder.replaceQueryParam(TRACKID, TRACKID_VALUE);
        builder.replaceQueryParam(REQUEST_ID, REQUEST_ID_VALUE);
        builder.replaceQueryParam(CAID, CAID_VALUE);
        return builder.build().toUriString();
    }

    @Override
    public String removeMacro(String jumpUrl) {
        final UriComponentsBuilder builder = UriComponentsBuilder.fromUriString(jumpUrl);
        builder.replaceQueryParam(FROM);
        builder.replaceQueryParam(EXTRA_JUMP_FROM);
        builder.replaceQueryParam(LAUNCH_TYPE);
        builder.replaceQueryParam(LAUNCH_ID);
        builder.replaceQueryParam(SESSION_ID);
        builder.replaceQueryParam(SOURCE_ID);
        builder.replaceQueryParam(RESOURCE_ID);
        builder.replaceQueryParam(CREATIVE_ID);
        builder.replaceQueryParam(LINKED_CREATIVE_ID);
        builder.replaceQueryParam(TRACK_ID);
        builder.replaceQueryParam(FROM_SPMID);
        builder.replaceQueryParam(TRACKID);
        builder.replaceQueryParam(REQUEST_ID);
        builder.replaceQueryParam(CAID);
        return builder.build().toUriString();
    }
}
