package com.bilibili.adp.cpc.splash_screen.creative.bos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SplashScreenCreative {
    /**
     * 账户id
     */
    private Integer accountId;
    /**
     * 计划id
     */
    private Integer campaignId;
    /**
     * 单元id
     */
    private Integer unitId;
    /**
     * 创意id
     */
    private Integer creativeId;
    /**
     * 创意名称
     */
    private String creativeName;
    /**
     * 跳转类型 1-跳转活动页 2-唤起第三方应用
     */
    private Integer navigationType;
    /**
     * 跳转链接类型 1-链接 5-落地页
     */
    private Integer jumpType;
    /**
     * 跳转链接
     */
    private String jumpUrl;
    /**
     * 唤起链接
     */
    private String schemaUrl;
    /**
     * 展示监控链接
     */
    private String customizedImpUrl;
    /**
     * 点击监控链接
     */
    private String customizedClickUrl;
    /**
     * 资质id列表
     */
    private List<Integer> qualificationIds;
    /**
     * 资质包id
     */
    private Integer qualificationPackageId;
    /**
     * 按钮类型：0-扭一扭 1-全屏滑动
     */
    private Integer interactType;
    /**
     * 应用包名
     */
    private String appPackageName;
    private List<SplashScreenImage> images;
    /**
     * 跳转引导文案
     */
    private String jumpGuideContent;
    /**
     * 跳转图片链接
     */
    private String jumpImageUrl;
    /**
     * 跳转图片md5
     */
    private String jumpImageMd5;
    /**
     * 唤起引导文案
     */
    private String schemaGuideContent;
    /**
     * 唤起图片链接
     */
    private String schemaImageUrl;
    /**
     * 唤起图片md5
     */
    private String schemaImageMd5;
    //region 主创意所需默认值
    /**
     * 结束时间
     */
    private LocalDate beginTime;
    /**
     * 开始时间
     */
    private LocalDate endTime;
    /**
     * 售卖类型
     */
    private Integer salesType;
    /**
     * 广告标
     */
    private Integer cmMark;
    /**
     * 广告版本
     */
    private Integer adpVersion;
    /**
     * 是否中台广告
     */
    private Integer isMiddleAd;
    //endregion
    //region 闪屏创意所需默认值
    /**
     * 素材点击区域 0-常规区域 1-全素材区域
     */
    private Integer clickArea;
    /**
     * 是否可跳过: 0-否 1-是
     */
    private Integer isSkip;
    /**
     * 展示时长
     */
    private Integer duration;
    /**
     * 广告标和跳过按钮位置样式:0-默认样式,广告标右上,跳过按钮右下 1-实验样式,广告标右上,跳过按钮右下(虽然和0样式相同,但是0表示维持以前的默认配置,位置和按钮大小都不做变更) 2-广告标左上，跳过按钮右上
     */
    private Integer markWithSkipStyle;
    /**
     * 跳过按钮占屏幕高度
     */
    private Float skipButtonHeight;
    //endregion 默认填充字段
    private Long mgkPageId;
    private Integer adVersionControlId;
    private Integer status;
    private Integer creativeStatus;
    private Integer auditStatus;
}
