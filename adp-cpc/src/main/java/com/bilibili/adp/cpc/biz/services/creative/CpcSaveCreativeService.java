package com.bilibili.adp.cpc.biz.services.creative;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bapis.ad.mgk.*;
import com.bapis.ad.mgk.page.group.PageGroupSource;
import com.bapis.archive.service.ArcReply;
import com.bilibili.adp.account.dto.AccountBaseDto;
import com.bilibili.adp.account.service.IQueryAccountService;
import com.bilibili.adp.common.Constants;
import com.bilibili.adp.common.DbTable;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.enums.*;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.exception.ServiceRuntimeException;
import com.bilibili.adp.common.util.CollectionHelper;
import com.bilibili.adp.common.util.ObjectUtils;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.common.util.func.Functions;
import com.bilibili.adp.common.util.func.InsertDeleteResult;
import com.bilibili.adp.common.util.func.QueryDslFunctions;
import com.bilibili.adp.cpc.biz.bos.component.LauStoryComponentBo;
import com.bilibili.adp.cpc.biz.bos.creative.CreativeLandingPageBo;
import com.bilibili.adp.cpc.biz.bos.creative.CreativeTabBo;
import com.bilibili.adp.cpc.biz.bos.creative.ShadowCreativeBo;
import com.bilibili.adp.cpc.biz.converter.creative.*;
import com.bilibili.adp.cpc.biz.services.creative.bos.AddMacroBo;
import com.bilibili.adp.cpc.biz.services.creative.bos.LauCreativeTemplateBo;
import com.bilibili.adp.cpc.biz.services.creative.config.CreativePositionConfig;
import com.bilibili.adp.cpc.biz.services.creative.config.FlyBusMarkConfig;
import com.bilibili.adp.cpc.biz.services.creative.dto.CpcCreativeDto;
import com.bilibili.adp.cpc.biz.services.creative.event.CreativeAuditEvent;
import com.bilibili.adp.cpc.biz.services.creative.native_ad.NativeService;
import com.bilibili.adp.cpc.biz.services.danmaku.AdpCpcSelfDanmakuGroupMappingService;
import com.bilibili.adp.cpc.biz.services.dynamic.LauDynamicService;
import com.bilibili.adp.cpc.biz.services.hidden.HiddenTemplateConverter;
import com.bilibili.adp.cpc.biz.services.hidden.HiddenTemplateService;
import com.bilibili.adp.cpc.biz.services.hidden.bos.HiddenContextBo;
import com.bilibili.adp.cpc.biz.services.material.AdpCpcLauMaterialBiliVideoService;
import com.bilibili.adp.cpc.biz.services.minigame.CpmAdpCreativeMiniGameMappingService;
import com.bilibili.adp.cpc.biz.services.page_group.api.ILaunchMgkPageGroupService;
import com.bilibili.adp.cpc.biz.services.page_group.bind.api.ILaunchMgkPageGroupBindService;
import com.bilibili.adp.cpc.biz.services.page_group.bind.bos.LaunchPageGroupCreativeBindBo;
import com.bilibili.adp.cpc.biz.services.page_group.bos.LaunchPageGroupBo;
import com.bilibili.adp.cpc.biz.services.page_group.bos.QueryLaunchPageGroupBo;
import com.bilibili.adp.cpc.biz.services.unit.CpcUnitServiceDelegate;
import com.bilibili.adp.cpc.biz.services.unit.api.ICpcUnitService;
import com.bilibili.adp.cpc.biz.services.unit.dto.CpcUnitDto;
import com.bilibili.adp.cpc.biz.validator.CpcCreativeValidator;
import com.bilibili.adp.cpc.config.GameCenterConfiguration;
import com.bilibili.adp.cpc.core.*;
import com.bilibili.adp.cpc.core.bos.CreativeButtonBo;
import com.bilibili.adp.cpc.core.bos.CreativeImageBo;
import com.bilibili.adp.cpc.core.constants.BusinessDomain;
import com.bilibili.adp.cpc.core.converter.CreativeFlyDynamicInfoConverter;
import com.bilibili.adp.cpc.dao.ad_core.mybatis.NewLauCreativeFlyDynamicInfoDao;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauCreativeComponentPo;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauCreativeNativeArchiveRelativityPo;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauCreativeTabPo;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauShadowCreativePo;
import com.bilibili.adp.cpc.dto.CpcCreativeDetailDto;
import com.bilibili.adp.cpc.dto.DpaShopGoodsDto;
import com.bilibili.adp.cpc.enums.AdpVersion;
import com.bilibili.adp.cpc.enums.JumpType;
import com.bilibili.adp.cpc.enums.ad.OcpcTargetEnum;
import com.bilibili.adp.cpc.enums.ad.PromotionPurposeType;
import com.bilibili.adp.cpc.po.ad.LauCreativeFlyDynamicInfoPo;
import com.bilibili.adp.cpc.po.ad.LauCreativeFlyDynamicInfoPoExample;
import com.bilibili.adp.cpc.po.ad.LauUnitFlyMiddleInfoPo;
import com.bilibili.adp.cpc.proxy.ArchiveServiceProxy;
import com.bilibili.adp.cpc.repo.LauCreativeNativeArchiveRelativityRepo;
import com.bilibili.adp.cpc.repo.LauUnitFlyMiddleInfoRepo;
import com.bilibili.adp.cpc.splash_screen.creative.service.SplashScreenCreativeExtraService;
import com.bilibili.adp.cpc.utils.CommonFuncs;
import com.bilibili.adp.cpc.utils.MgkLandingPageParserUtils;
import com.bilibili.adp.cpc.utils.RecDiffResult;
import com.bilibili.adp.launch.api.common.*;
import com.bilibili.adp.launch.api.creative.dto.*;
import com.bilibili.adp.launch.api.flyPro.dto.enums.DynamicTypeEnum;
import com.bilibili.adp.launch.api.launch.dto.BizCreativeMappingDto;
import com.bilibili.adp.launch.api.launch.dto.GetBidCostParam;
import com.bilibili.adp.launch.api.launch.dto.MgkVideoDto;
import com.bilibili.adp.launch.api.service.ICpcLaunchService;
import com.bilibili.adp.launch.api.service.ICreativeCommonService;
import com.bilibili.adp.launch.api.service.ILauBizCreativeMappingService;
import com.bilibili.adp.launch.api.service.validation.IValidationService;
import com.bilibili.adp.launch.biz.ad_core.mybatis.LauCreativeButtonCopyDao;
import com.bilibili.adp.launch.biz.ad_core.mybatis.LauCreativeImageDao;
import com.bilibili.adp.launch.biz.ad_core.mybatis.LauUnitCreativeDao;
import com.bilibili.adp.launch.biz.common.LaunchUtil;
import com.bilibili.adp.launch.biz.config.UpOrderConfig;
import com.bilibili.adp.launch.biz.exception.LaunchExceptionCode;
import com.bilibili.adp.launch.biz.flyPro.v2.FlyProUnderBoxDelegate;
import com.bilibili.adp.launch.biz.lau_dao.*;
import com.bilibili.adp.launch.biz.pojo.*;
import com.bilibili.adp.launch.biz.query.dos.LauCreativeSceneDo;
import com.bilibili.adp.launch.biz.service.CreativeTrackadfProc;
import com.bilibili.adp.launch.biz.service.account.LaunchAccountGroupService;
import com.bilibili.adp.launch.biz.service.account.LaunchBrandInfoService;
import com.bilibili.adp.launch.biz.service.account.bos.LauAccountInfoBo;
import com.bilibili.adp.launch.biz.service.component.LaunchComponentService;
import com.bilibili.adp.launch.biz.service.creative.LaunchUrlDecorationService;
import com.bilibili.adp.launch.biz.service.creative.bos.LauCreativeMiniGameMappingBo;
import com.bilibili.adp.launch.biz.service.creative.bos.RequestParam;
import com.bilibili.adp.launch.biz.service.dynamic.LaunchDynamicService;
import com.bilibili.adp.launch.biz.service.resource.LaunchResourceService;
import com.bilibili.adp.launch.biz.service.resource.bos.LauResourceStyleTripletWithSlotGroup;
import com.bilibili.adp.launch.biz.service.unit.LaunchUnitSceneService;
import com.bilibili.adp.launch.biz.service.unit.bos.LauUnitSceneBo;
import com.bilibili.adp.log.service.ILogOperateService;
import com.bilibili.adp.passport.api.dto.ReplySubjectDto;
import com.bilibili.adp.passport.api.enums.ReplySubjectMonitorEnum;
import com.bilibili.adp.passport.api.enums.ReplySubjectStateEnum;
import com.bilibili.adp.passport.api.param.QueryReplySubjectParam;
import com.bilibili.adp.passport.api.param.ReplySubjectRegisterParam;
import com.bilibili.adp.passport.api.param.SetReplySubjectStateParam;
import com.bilibili.adp.passport.api.service.IReplyService;
import com.bilibili.adp.resource.api.account.label.IAccountLabelService;
import com.bilibili.adp.resource.api.app_package.dto.AppPackageDto;
import com.bilibili.adp.resource.api.bus_mark_rule.IBusMarkRuleService;
import com.bilibili.adp.resource.api.cm.mark.CmMarkDto;
import com.bilibili.adp.resource.api.cm.mark.ICmMarkService;
import com.bilibili.adp.resource.api.common.BusMarkRuleAdSysEnum;
import com.bilibili.adp.resource.api.common.ResUtils;
import com.bilibili.adp.resource.api.slot_group.IResSlotGroupService;
import com.bilibili.adp.resource.api.slot_group.QueryTemplateLaunchTypeMappingDto;
import com.bilibili.adp.resource.api.slot_group.ResSlotGroupBaseDto;
import com.bilibili.adp.resource.api.slot_group.ResSlotGroupTemplateMappingDto;
import com.bilibili.adp.resource.api.system.ISystemConfigService;
import com.bilibili.adp.util.common.AdpCatUtils;
import com.bilibili.bjcom.querydsl.BaseQueryFactory;
import com.bilibili.crm.platform.common.IsValid;
import com.bilibili.location.api.bus_mark.dto.BusMarkDto;
import com.bilibili.location.api.cardtype.dto.CreativeStyle;
import com.bilibili.location.api.common.TemplateUtils;
import com.bilibili.location.api.service.ITemplateGroupService;
import com.bilibili.location.api.service.ITemplateService;
import com.bilibili.location.api.service.query.IQueryButtonCopyService;
import com.bilibili.location.api.service.template.bos.LocTemplateCardTypeConfigBo;
import com.bilibili.location.api.template.dto.ButtonCopyDto;
import com.bilibili.location.api.template.dto.QueryTemplateGroupDto;
import com.bilibili.location.api.template.dto.TemplateDto;
import com.bilibili.location.api.template.dto.TemplateGroupBo;
import com.bilibili.location.common.ButtonCopyTypeEnum;
import com.bilibili.location.common.CmMarkEnum;
import com.bilibili.location.common.bean.LocCreativeBean;
import com.bilibili.location.common.bean.LocImageBean;
import com.bilibili.location.common.util.LocUtils;
import com.bilibili.mas.common.utils.Values;
import com.bilibili.mgk.platform.api.landing_page.dto.TemplatePageDto;
import com.bilibili.mgk.platform.api.video_library.soa.ISoaVideoLibraryService;
import com.bilibili.mgk.platform.common.LandingPageTypeEnum;
import com.bilibili.mgk.platform.common.MgkConstants;
import com.dianping.cat.Cat;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Joiner;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.bilibili.adp.common.Constants.*;
import static com.bilibili.adp.cpc.dao.ad_core.querydsl.QLauCreativeImage.lauCreativeImage;
import static com.bilibili.adp.cpc.dao.ad_core.querydsl.QLauCreativeTab.lauCreativeTab;
import static com.bilibili.adp.cpc.dao.ad_core.querydsl.QLauCreativeTemplate.lauCreativeTemplate;
import static com.bilibili.adp.cpc.dao.ad_core.querydsl.QLauUnit.lauUnit;
import static com.bilibili.adp.cpc.dao.ad_core.querydsl.QLauUnitCreative.lauUnitCreative;
import static com.bilibili.adp.cpc.dao.config.PlatformMySqlConfig.*;
import static com.bilibili.adp.launch.biz.query.qos.QLauCreativeScene.lauCreativeScene;
import static com.bilibili.adp.launch.biz.service.component.LaunchComponentService.*;
import static com.bilibili.location.common.util.LocUtils.parseCommaSplitStr;


/**
 * copy class
 *
 * @see com.bilibili.adp.launch.biz.service.CpcSaveCreativeService
 */
@Slf4j
@Primary
@Service(value = "AdpCpcSaveCreativeService")
@RequiredArgsConstructor
public class CpcSaveCreativeService {
    // 创作推广标
    public static final int BUS_MARK_CONTENT = 54;
    public static final int BUS_MARK_CM = 1;
    private static final String FROM_CPC = "bcg_%s___CREATIVEID__";
    private static final String LANDING_PAGE_QUERY_GAME_VALUE = "cpc___SOURCEID_____CREATIVEID_____ADTYPE_____REQUESTID__";
    private static final String LIVE_ROOM_HIDDEN_PARAM_KEY_FROM = "from";
    private static final String LIVE_ROOM_HIDDEN_PARAM_VALUE_FROM = "__JUMPFROM__";
    private static final String LIVE_ROOM_HIDDEN_PARAM_KEY_EXTRA_JUMP_FROM = "extra_jump_from";
    private static final String LIVE_ROOM_HIDDEN_PARAM_VALUE_EXTRA_JUMP_FROM = "__JUMPFROM__";
    private static final String LIVE_ROOM_HIDDEN_PARAM_KEY_LAUNCH_TYPE = "launch_type";
    private static final String LIVE_ROOM_HIDDEN_PARAM_VALUE_LAUNCH_TYPE = "cpm_shangye";
    private static final String LIVE_ROOM_HIDDEN_PARAM_KEY_LAUNCH_ID = "launch_id";
    private static final String LIVE_ROOM_HIDDEN_PARAM_VALUE_LAUNCH_ID = "__CREATIVEID__";
    private static final String LIVE_ROOM_HIDDEN_PARAM_KEY_SESSION_ID = "session_id";
    private static final String LIVE_ROOM_HIDDEN_PARAM_VALUE_SESSION_ID = "__REQUESTID__";

    public static final String SOURCE_FROM = "sourcefrom";
    public static final String SOURCE_FROM_BILI_GAME_COMMENT = "**********";
    public static final String SOURCE_FROM_BILI_GAME_ANCHOR = "**********";

    public static final String SOURCE_FROM_BILI_APPLET_COMMENT = "**********";
    public static final String SOURCE_FROM_BILI_APPLET_ANCHOR = "**********";

    public static final String ACCOUNT_ID = "account_id";
    public static final String __ACCOUNT_ID__ = "__ACCOUNTID__";
    public static final String CAMPAIGN_ID = "campaign_id";
    public static final String __CAMPAIGN_ID__ = "__CAMPAIGNID__";
    public static final String UNIT_ID = "unit_id";
    public static final String __UNIT_ID__ = "__UNITID__";
    public static final String CREATIVE_ID = "creative_id";
    public static final String __CREATIVE_ID__ = "__CREATIVEID__";
    public static final String BV_ID = "bvid";
    public static final String __BV_ID__ = "__BVID__";

    private static final String MACRO_FROM = "{_FROM_}";
    private static final String AREA_PLAINTEXT = "智能地域";
    private static final String GENDER_PLAINTEXT = "智能性别";
    private static final String DEVICE_PLAINTEXT = "智能设备";
    private static final String AGE_PLAINTEXT = "智能年龄";
    private static final String AREA_CIPHERTEXT = "area";
    private static final String GENDER_CIPHERTEXT = "gender";
    private static final String DEVICE_CIPHERTEXT = "device";
    private static final String AGE_CIPHERTEXT = "age";
    private static final int ARCHIVE_FIRST_CATEGORY = 10;
    private static final int ARCHIVE_SECOND_CATEGORY = 95;
    private static final int ARCHIVE_THIRD_CATEGORY = 0;
    private static final Pattern IOS_PATTERN = Pattern.compile("http(s?)://(itunes\\.apple\\.com|apps\\.apple\\.com)/.+");
    private static final List<CreativeStyle> SORTED_STYLE = Arrays.asList(CreativeStyle.VIDEO, CreativeStyle.GIF, CreativeStyle.AVID, CreativeStyle.IMAGE);
    private final LaunchCreativeFlyDynamicInfoService launchCreativeFlyDynamicInfoService;
    private final LaunchUnitSceneV1Service launchUnitSceneV1Service;
    // story视频卡
    private final Integer cardTypeStory = 53;
    private final Integer publicAdMark = 121;
    private final Integer publicAdMarkReplace = 145;
    @Autowired
    protected ICreativeCommonService creativeCommonService;
    @Value("${up.cm.mark:2}")
    private int ARCHIVE_CM_MARK = 2;
    @Value("${up.ad.tag:UP主自荐}")
    private String ARCHIVE_AD_MARK;
    @Autowired
    private LauUnitCreativeDao lauUnitCreativeDao;
    @Autowired
    private LauCreativeButtonCopyDao lauCreativeButtonCopyDao;
    @Autowired
    private LauCreativeExtraProc lauCreativeExtraProc;
    @Autowired
    private LauCreativeImageDao lauCreativeImageDao;
    @Autowired
    private LauCreativeVideoDao lauCreativeVideoDao;
    @Autowired
    private LauCreativeLayoutDao lauCreativeLayoutDao;
    @Autowired
    private ExtCreativeLayoutDao extCreativeLayoutDao;
    @Autowired
    private LauCreativeDanmakuDao lauCreativeDanmakuDao;
    @Autowired
    private ExtLauCreativeDanmakuDao extLauCreativeDanmakuDao;
    @Autowired
    private ExtLauCreativeBusinessCategoryDao extLauCreativeBusinessCategoryDao;
    @Autowired
    private LauCreativeAutoDao lauCreativeAutoDao;
    @Autowired
    private LauCreativeMonitoringDao lauCreativeMonitoringDao;
    @Autowired
    private CpcUnitServiceDelegate unitDelegate;
    @Autowired
    private CpcCreativeValidator validator;
    @Autowired
    private IQueryButtonCopyService queryButtonCopyService;
    @Autowired
    private ILogOperateService logOperateService;
    @Autowired
    private IResSlotGroupService resSlotGroupService;
    @Autowired
    private ICmMarkService cmMarkService;
    @Autowired
    private ISystemConfigService systemConfigService;
    @Autowired
    private IQueryAccountService queryAccountService;
    @Autowired
    private IReplyService replyService;
    @Autowired
    private LauCreativeShareDao creativeShareDao;
    @Autowired
    private CpmAdpCreativeMiniGameMappingService lauCreativeMiniGameMappingService;
    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;
    @Autowired
    private IBusMarkRuleService busMarkRuleService;
    @Autowired
    private ICpcLaunchService cpcLaunchService;
    @Autowired
    private GameCenterConfiguration gameCenterConfiguration;
    @Autowired
    private ITemplateGroupService templateGroupService;
    @Value("${feeds.video.creative.dynamicTime:5000}")
    private Integer dynamicTime;
    @Autowired
    private ICpcUnitService cpcUnitService;
    @Resource(name = AD_BQF)
    private BaseQueryFactory adBqf;

    @Resource(name = AD_CORE_BQF)
    private BaseQueryFactory adCoreBqf;


    @Autowired
    private CpcCreativeServiceDelegate creativeServiceDelegate;
    @Value("#{PropertySplitter.getInt2IntMap('${lau.dynamic.unit.frequency.max:{1:2;2:14}}')}")
    private Map<Integer, Integer> dynamicUnitFrequencyLimit;
    @Autowired
    private NewLauCreativeFlyDynamicInfoDao newLauCreativeFlyDynamicInfoDao;
    @Autowired
    private LauUnitFlyMiddleInfoRepo lauUnitFlyMiddleInfoRepo;
    @Autowired
    private LaunchResourceService launchResourceService;
    @Autowired
    private LaunchAccountGroupService launchAccountGroupService;
    @Autowired
    private ITemplateService templateService;
    @Autowired
    private LaunchUnitSceneService launchUnitSceneService;
    @Autowired
    private ISoaVideoLibraryService videoLibraryService;
    @Autowired
    private ILauBizCreativeMappingService lauBizCreativeMappingService;
    @Autowired
    private IValidationService validationService;
    @Autowired
    private FlyProUnderBoxDelegate flyProUnderBoxDelegate;
    @Autowired
    private AdpCpcLauMaterialBiliVideoService biliVideoService;
    @Autowired
    private LaunchBrandInfoService launchBrandInfoService;
    @Autowired
    private LaunchDynamicService launchDynamicService;
    @Autowired
    private LaunchUrlDecorationService launchUrlDecorationService;
    @Autowired
    private LaunchCreativeLandingPageService launchCreativeLandingPageService;
    @Autowired
    private UpOrderConfig upOrderConfig;
    @Autowired
    private CreativeTrackadfProc creativeTrackadfProc;
    @Autowired
    private LaunchComponentService launchComponentService;
    @Autowired
    private AdpCpcCreativeComponentService adpCpcCreativeComponentService;
    @Autowired
    private StoryComponentService storyComponentService;
    @Autowired
    private LaunchCreativeService launchCreativeService;
    @Autowired
    private LauCreativeExtraService lauCreativeExtraService;
    @Autowired
    private LaunchShadowCreativeService launchShadowCreativeService;
    @Autowired
    private LaunchCreativeImageService launchCreativeImageService;
    @Autowired
    private LaunchCreativeButtonService launchCreativeButtonService;
    @Autowired
    private LaunchCreativeComponentService launchCreativeComponentService;
    @Autowired
    private LaunchJumpUrlService launchJumpUrlService;
    @Autowired
    private LaunchCreativeTabService launchCreativeTabService;
    @Autowired
    private LandingPageServiceGrpc.LandingPageServiceBlockingStub landingPageServiceBlockingStub;
    @Autowired
    private CreativePositionConfig creativePositionConfig;
    @Autowired
    private FlyBusMarkConfig flyBusMarkConfig;
    @Autowired
    private ILaunchMgkPageGroupBindService launchMgkPageGroupBindService;
    @Autowired
    private ILaunchMgkPageGroupService launchMgkPageGroupService;
    @Autowired
    private AdpCpcSelfDanmakuGroupMappingService adpCpcSelfDanmakuGroupMappingService;
    @Resource
    private IAccountLabelService accountLabelService;
    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private HiddenTemplateService hiddenTemplateService;

    @Autowired
    private ArchiveServiceProxy archiveServiceProxy;

    @Autowired
    private SplashScreenCreativeExtraService creativeExtraService;
    @Autowired
    private NativeService nativeService;
    @Autowired
    private LauCreativeNativeArchiveRelativityRepo lauCreativeNativeArchiveRelativityRepo;
    @Autowired
    private LauDynamicService lauDynamicService;
    /**
     * 起飞火箭标
     */
    @Value("${fly.rocket.mark.id:123}")
    private Integer rocketBusMarkId;

    @Value("${ppt.shop_goods.label:349}")
    private Integer pptShopGoodsLabelId;

    @Value("${ppt.on_shelf_game.label:347}")
    private Integer pptOnShelfGameLabelId;

    @Value("${effect.ad.shadow.creative.label.id:504}")
    private Integer effectAdShadowCreativeLabelId;

    /**
     * 保存单元创意列表
     *
     * @param operator
     * @param saveCreativeDtos
     * @param deleteOtherInUnit
     * @return
     * @throws ServiceException
     */
    @Transactional(value = AD_CORE_TM, rollbackFor = Exception.class)
    public List<Integer> save(Operator operator, List<CpcCreativeDetailDto> saveCreativeDtos, boolean deleteOtherInUnit) throws ServiceException {
        log.info("SaveCreative save: operator={}, saveCreativeDtos={}", operator, JSON.toJSONString(saveCreativeDtos));
        Assert.isTrue(!Operator.validateParamIsNull(operator), "操作人不能为空");
        Assert.isTrue(!CollectionUtils.isEmpty(saveCreativeDtos), "创意列表不能为空");
        final CpcCreativeDetailDto sample = saveCreativeDtos.get(0);

        // 没有传adpVersion的都按照旧版处理
        // 没有传isNewFly的都按照旧版处理
        saveCreativeDtos.forEach(x -> {
            if (x.getAdpVersion() == null) x.setAdpVersion(AdpVersion.LEGACY.getKey());
            if (x.getIsNewFly() == null) x.setIsNewFly(IsValid.FALSE.getCode());
        });

        // 创意的单元信息
        List<Integer> unitIds = saveCreativeDtos.stream().map(CpcCreativeDetailDto::getUnitId).distinct().collect(Collectors.toList());
        Assert.isTrue(unitIds.size() == 1, "只能保存同一个单元下的创意");
        Integer unitId = unitIds.get(0);
        CpcUnitDto unit = unitDelegate.loadCpcUnit(unitId);
        Assert.notNull(unit, "创意所选单元不存在");
        Assert.isTrue(operator.getOperatorId().equals(unit.getAccountId()), "您只能在自己的单元下新建创意");
        AdpCatUtils.logEventAndLog("单元信息", "单元信息", "SaveCreative,unit=%s", JSON.toJSONString(unit));

        // 获取单元 adpVersion
        final Integer adpVersion = unit.getAdpVersion();

        // 是否遗留版本(0)
        final boolean isAdpVersionLegacy = AdpVersion.isLegacy(adpVersion);
        // 是否合并版本(1)
        final boolean isAdpVersionMerged = AdpVersion.isMerged(adpVersion);
        // (是否视频合并版本(3) 或 中台(5))
        final boolean isAdpVersionMergedVideo = AdpVersion.isVideoMerged(adpVersion);

        // 账号信息
        AccountBaseDto account = queryAccountService.getAccountBaseDtoById(unit.getAccountId());

        List<ResSlotGroupTemplateMappingDto> slotGroupTemplateMappingDtos = Collections.emptyList();
        // 视频版位合并的功能
        List<LauResourceStyleTripletWithSlotGroup> availableTriplets = Collections.emptyList();

        AdpCatUtils.logEventAndLog("获取模板组", "获取模板组start", "unitId=%s", unitId);
        // 模板组信息，统一使用模板组代替模板
        Map<Integer, TemplateGroupBo> templateGroupMap = Collections.emptyMap();
        Map<Integer, TemplateDto> templateMap = Collections.emptyMap();

        // 是否遗留版本(0)
        if (isAdpVersionLegacy) {
            // 从单元上获取模板
            templateGroupMap = getTemplateMapByUnit(unit);
            saveCreativeDtos.forEach(dto -> dto.setTemplateGroupId(dto.getTemplateId()));
        }
        // 是否合并版本(1) || (是否视频合并版本(3) 或 中台(5))
        else if (isAdpVersionMerged || isAdpVersionMergedVideo) {
            final List<Integer> templateIds;

            // 是否合并版本(1)
            if (isAdpVersionMerged) {
                slotGroupTemplateMappingDtos = cpcUnitService.getSlotGroupTemplatesByUnit(unit);
                log.info("SaveCreative:slotGroupTemplateMappingDtos.size:{}", CollectionHelper.getSize(slotGroupTemplateMappingDtos));
                templateIds = ResUtils.getSlotGroupMappingTemplateIds(slotGroupTemplateMappingDtos);
            }
            // (是否视频合并版本(3) 或 中台(5))
            else {
                // 视频版位合并（获取推广目的广告位组模板关系列表）
                availableTriplets = launchResourceService.getAvailableTriplets(unit.getAccountId(), unit.getUnitId(), sample.getPreferScene(), sample.getChannelId(), sample.getSceneIds(), 0);
                final Map<Integer, List<LauResourceStyleTripletWithSlotGroup>> validationMap = availableTriplets.stream()
                        .collect(Collectors.groupingBy(x -> x.getLauResourceStyleTriplet().getSlotGroupId()));
                validationMap.forEach((k, v) -> {
                    if (v.size() > 1) throw new IllegalArgumentException("广告位组" + k + "对应多个模板, 请调整内容关联类型配置");
                });
                templateIds = availableTriplets.stream()
                        .map(x -> x.getLauResourceStyleTriplet().getTemplateId())
                        .collect(Collectors.toList());
            }
            Assert.notEmpty(templateIds, "你没有该单元可用的广告位组与模板");

            // 新版单元下的创意强制走新版逻辑
            List<Integer> creativeTemplateGroupIds = saveCreativeDtos.stream()
                    // 设置 adpVersion 和 templateId（消费，可以修改内部值）
                    .peek(dto -> dto.setAdpVersion(adpVersion))
                    .peek(dto -> dto.setTemplateId(dto.getTemplateGroupId()))
                    .map(dto -> Objects.requireNonNull(dto.getTemplateGroupId(), "模板组ID不能为空"))
                    .distinct().collect(Collectors.toList());
            log.info("SaveCreative:creativeTemplateGroupIds:{}", creativeTemplateGroupIds);

            // 获取模板组信息
            List<TemplateGroupBo> templateGroupDetailDtos = templateGroupService.queryTemplateGroupDetail(
                    QueryTemplateGroupDto.builder().templateGroupIds(creativeTemplateGroupIds)
                            .templateIds(templateIds)
                            .withTemplatesInQueryTemplateIds(true)
                            .withUnavailable(true)
                            .build());
            log.info("SaveCreative:templateGroupDetailDtos.size={}", CollectionHelper.getSize(templateGroupDetailDtos));

            if (isAdpVersionMerged) {
                // 旧版过滤版本
                templateGroupMap = templateGroupDetailDtos.stream()
                        .filter(x -> x.getBoundVersions().contains(TemplateGroupVersion.MERGE.getKey().toString()))
                        .collect(Collectors.toMap(TemplateGroupBo::getId, Function.identity()));
            } else {
                // 新版过滤版本和白名单
                final Collection<Integer> accountLabelIds = launchAccountGroupService.getAccountLabelIds(unit.getAccountId());
                templateGroupMap = templateGroupDetailDtos.stream()
                        .filter(x -> {
                            if (!x.getBoundVersions().contains(TemplateGroupVersion.VIDEO_MERGE.getKey().toString()))
                                return false;
                            final String boundAccountLabelIds = x.getBoundAccountLabelIds();
                            if (StringUtils.isBlank(boundAccountLabelIds)) return true;
                            return accountLabelIds.stream().map(Object::toString).anyMatch(boundAccountLabelIds::contains);
                        }).collect(Collectors.toMap(TemplateGroupBo::getId, Function.identity()));
                templateMap = templateService.getValidTemplatesInIds(templateIds)
                        .stream()
                        .collect(Collectors.toMap(TemplateDto::getTemplateId, Function.identity()));
            }

            validateDynamicAreaCreative(unit.getFrequencyUnit(), unit.getFrequencyLimit(), templateGroupMap.entrySet().stream()
                    .filter(entry -> creativeTemplateGroupIds.contains(entry.getKey()))
                    .map(entry -> entry.getValue())
                    .collect(Collectors.toList()), adpVersion);
        }

        // 过滤出 pc 的模板
        final Set<Integer> pcTemplateIds;
        // (是否视频合并版本(3) 或 中台(5))
        if (isAdpVersionMergedVideo) {
            pcTemplateIds = availableTriplets.stream()
                    .filter(x -> Objects.equals(x.getSlotGroupFullBo().getChannelId(), ChannelEnum.PC.getCode()))
                    .map(x -> x.getLauResourceStyleTriplet().getTemplateId())
                    .collect(Collectors.toSet());
        }
        // 其他版本
        else {
            pcTemplateIds = slotGroupTemplateMappingDtos.stream()
                    .flatMap(x -> {
                        final ResSlotGroupBaseDto slotGroup = x.getSlotGroup();
                        if (Objects.isNull(slotGroup)) return Stream.empty();
                        if (Objects.equals(slotGroup.getChannelId(), ChannelEnum.PC.getCode())) {
                            final List<TemplateDto> templates = x.getTemplates();
                            if (CollectionUtils.isEmpty(templates)) return Stream.empty();
                            return templates.stream()
                                    .map(TemplateDto::getTemplateId)
                                    .filter(Objects::nonNull);
                        }
                        return Stream.empty();
                    }).collect(Collectors.toSet());
        }

        // enterprise mid 和 品牌信息处理
        final Long enterpriseMid = saveCreativeDtos.get(0).getEnterpriseMid();
        final Integer brandInfoId = saveCreativeDtos.get(0).getBrandInfoId();
        if (Utils.isPositive(enterpriseMid)) {
            if (!Objects.equals(enterpriseMid, unit.getEnterpriseMid())) {
                saveUnit(unitId, enterpriseMid);
                unit.setEnterpriseMid(enterpriseMid);
            }
            if (!Objects.equals(unit.getBrandInfoId(), 0)) {
                launchBrandInfoService.saveUnitBrandInfo(unitId, 0);
                unit.setBrandInfoId(0);
            }
        } else if (Utils.isPositive(brandInfoId)) {
            Assert.isTrue(launchBrandInfoService.isBrandInfoValid(unit.getAccountId(), brandInfoId), "品牌标非法");
            if (!Objects.equals(brandInfoId, unit.getBrandInfoId())) {
                launchBrandInfoService.saveUnitBrandInfo(unitId, brandInfoId);
            }
            if (!Objects.equals(0L, unit.getEnterpriseMid())) {
                saveUnit(unitId, 0L);
                unit.setEnterpriseMid(0L);
            }
        }

        // 转换成创意列表(拼接其他字段)
        List<CpcCreativeDto> creativeList = convertCreativeDto2List(saveCreativeDtos, unit, templateGroupMap, pcTemplateIds, Collections.emptyMap(), account);

        // 个人起飞story，不校验
        if (!upOrderConfig.getFlySlotArchiveStory().equals(unit.getSlotGroup())) {
            validator.createCreative(saveCreativeDtos, unit, account, creativeList, templateGroupMap, Collections.emptyMap());
        }

        // 修改的创意 ids
        List<Integer> reserveIds = creativeList.stream()
                .filter(dto -> Utils.isPositive(dto.getCreativeId()))
                .map(CpcCreativeDto::getCreativeId)
                .collect(Collectors.toList());

        // 获取修改前已存在创意 map
        Map<Integer, CpcCreativeDto> reserveCreativeMap = getMapInIds(reserveIds);
        if (!CollectionUtils.isEmpty(reserveCreativeMap)) {
            //todo 设置旧创意的promotion purpose type 后面保存按钮会用到，目前没有想到比较好的方式 后续修改
            reserveCreativeMap.values().forEach(creative -> creative.setPromotionPurposeType(unit.getPromotionPurposeType()));
        }

        // 创意原有的story组件
        Map<Integer, LauStoryComponentBo> oldStoryComponentMap = adpCpcCreativeComponentService.fetchMapOnlyStory(unit.getAccountId(), reserveIds);
        // 创意现有的story组件
        Map<Integer, LauStoryComponentBo> newStoryComponentMap = new HashMap<>();
        creativeList.stream().forEach(o -> {
            List<CreativeComponentBo> components = o.getComponents();
            if (components != null) {
                CreativeComponentBo creativeComponentBo = components.stream().filter(p -> (p.getType() == STORY_COMMON || p.getType() == STORY_COUPON || p.getType() == STORY_IMAGE))
                        .findFirst().orElse(null);
                if (creativeComponentBo != null) {
                    LauStoryComponentBo storyComponentBo = storyComponentService.getBo4Creative(creativeComponentBo.getId(), creativeComponentBo.getType());
                    if (storyComponentBo != null) {
                        newStoryComponentMap.put(o.getCreativeId(), storyComponentBo);
                    }
                }
            }
        });

        if (deleteOtherInUnit) {
            deleteCreativeByUnitId(unit.getUnitId(), reserveIds);
        }

        // (是否视频合并版本(3) 或 中台(5))
        if (isAdpVersionMergedVideo) {
            // 保存 lau_unit_scene
            launchUnitSceneService.save(LauUnitSceneBo.from(unitId, sample.getChannelId(), sample.getSceneIds()));
        }

        // 保存创意列表
        List<Integer> savedCreativeIds = saveCreative(operator, unit, creativeList, templateMap, templateGroupMap, reserveCreativeMap, account, slotGroupTemplateMappingDtos, availableTriplets, operator.getOperatorName());

        // for promotionPurposeContent compare
        for (CpcCreativeDto cpcCreativeDto : creativeList) {
            cpcCreativeDto.setPromotionPurposeContent(JumpTypeEnum.getByCode(cpcCreativeDto.getJumpType())
                    .parseLaunchUrl(cpcCreativeDto.getPromotionPurposeContent()));
        }

        // 创意新增日志
        addCreativeInsertLog(operator, creativeList, reserveCreativeMap, savedCreativeIds, oldStoryComponentMap, newStoryComponentMap);

        return savedCreativeIds;
    }

    /**
     * 保存单元创意列表
     *
     * @param operator
     * @param saveCreativeDtos
     * @param deleteOtherInUnit
     * @return
     * @throws ServiceException
     */
    @Transactional(value = AD_CORE_TM, rollbackFor = Exception.class)
    public List<Integer> saveNew(Operator operator, List<CpcCreativeDetailDto> saveCreativeDtos, boolean deleteOtherInUnit,
                                 Map<String, com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauCreativeExtraPo> seasonMap) throws ServiceException {
        log.info("SaveCreative saveNew: operator={}, saveCreativeDtos={}", operator, JSON.toJSONString(saveCreativeDtos));
        Assert.isTrue(!Operator.validateParamIsNull(operator), "操作人不能为空");
        Assert.isTrue(!CollectionUtils.isEmpty(saveCreativeDtos), "创意列表不能为空");
        final CpcCreativeDetailDto sample = saveCreativeDtos.get(0);

        // 没有传adpVersion的都按照旧版处理
        // 没有传isNewFly的都按照旧版处理
        saveCreativeDtos.forEach(x -> {
            if (x.getAdpVersion() == null) x.setAdpVersion(AdpVersion.LEGACY.getKey());
            if (x.getIsNewFly() == null) x.setIsNewFly(IsValid.FALSE.getCode());
        });

        // 创意的单元信息
        List<Integer> unitIds = saveCreativeDtos.stream().map(CpcCreativeDetailDto::getUnitId).distinct().collect(Collectors.toList());
        Assert.isTrue(unitIds.size() == 1, "只能保存同一个单元下的创意");
        Integer unitId = unitIds.get(0);
        CpcUnitDto unit = unitDelegate.loadCpcUnit(unitId);
        Assert.notNull(unit, "创意所选单元不存在");
        Assert.isTrue(operator.getOperatorId().equals(unit.getAccountId()), "您只能在自己的单元下新建创意");
        AdpCatUtils.logEventAndLog("单元信息", "单元信息", "SaveCreative,unit=%s", JSON.toJSONString(unit));
        Assert.isTrue(!AdpVersion.isCpcFlyMerge(unit.getAdpVersion()), "不允许使用当前接口保存新三连创意");
        // 获取单元 adpVersion
        final Integer adpVersion = unit.getAdpVersion();

        // 是否遗留版本(0)
        final boolean isAdpVersionLegacy = AdpVersion.isLegacy(adpVersion);
        // 是否合并版本(1)
        final boolean isAdpVersionMerged = AdpVersion.isMerged(adpVersion);
        // (是否视频合并版本(3) 或 中台(5))
        final boolean isAdpVersionMergedVideo = AdpVersion.isVideoMerged(adpVersion);
        // 是否中台(5)
        final boolean isAdpVersionMiddle = AdpVersion.isMiddle(adpVersion);

        // 账号信息
        AccountBaseDto account = queryAccountService.getAccountBaseDtoById(unit.getAccountId());

        List<ResSlotGroupTemplateMappingDto> slotGroupTemplateMappingDtos = Collections.emptyList();
        // 视频版位合并的功能
        List<LauResourceStyleTripletWithSlotGroup> availableTriplets = Collections.emptyList();

        AdpCatUtils.logEventAndLog("获取模板组", "获取模板组start", "unitId=%s", unitId);
        // 模板组信息，统一使用模板组代替模板
        Map<Integer, TemplateGroupBo> templateGroupMap = Collections.emptyMap();
        Map<Integer, TemplateDto> templateMap = Collections.emptyMap();

        final Collection<Integer> accountLabelIds = launchAccountGroupService.getAccountLabelIds(unit.getAccountId());
        launchUnitSceneV1Service.validateSelectedScenes(accountLabelIds, saveCreativeDtos.get(0).getSceneIds());

        // 是否遗留版本(0)
        if (isAdpVersionLegacy) {
            // 从单元上获取模板
            templateGroupMap = getTemplateMapByUnit(unit);
            saveCreativeDtos.forEach(dto -> dto.setTemplateGroupId(dto.getTemplateId()));
        }
        // 是否合并版本(1) || (是否视频合并版本(3) 或 中台(5))
        else if (isAdpVersionMerged || isAdpVersionMergedVideo) {
            final List<Integer> templateIds;

            // 是否合并版本(1)
            if (isAdpVersionMerged) {
                slotGroupTemplateMappingDtos = cpcUnitService.getSlotGroupTemplatesByUnit(unit);
                log.info("SaveCreative:slotGroupTemplateMappingDtos.size:{}", CollectionHelper.getSize(slotGroupTemplateMappingDtos));
                templateIds = ResUtils.getSlotGroupMappingTemplateIds(slotGroupTemplateMappingDtos);
            }
            // (是否视频合并版本(3) 或 中台(5))
            else {
                // 视频版位合并（获取推广目的广告位组模板关系列表）
                availableTriplets = launchResourceService.getAvailableTriplets(unit.getAccountId(), unit.getUnitId(), sample.getPreferScene(), sample.getChannelId(), sample.getSceneIds(), 0);
                final Map<Integer, List<LauResourceStyleTripletWithSlotGroup>> validationMap = availableTriplets.stream()
                        .collect(Collectors.groupingBy(x -> x.getLauResourceStyleTriplet().getSlotGroupId()));
                validationMap.forEach((k, v) -> {
                    if (v.size() > 1) throw new IllegalArgumentException("广告位组" + k + "对应多个模板, 请调整内容关联类型配置");
                });
                templateIds = availableTriplets.stream()
                        .map(x -> x.getLauResourceStyleTriplet().getTemplateId())
                        .collect(Collectors.toList());
            }
            Assert.notEmpty(templateIds, "你没有该单元可用的广告位组与模板");

            // 新版单元下的创意强制走新版逻辑
            List<Integer> creativeTemplateGroupIds = saveCreativeDtos.stream()
                    // 设置 adpVersion 和 templateId（消费，可以修改内部值）
                    .peek(dto -> dto.setAdpVersion(adpVersion))
                    .peek(dto -> dto.setTemplateId(dto.getTemplateGroupId()))
                    .map(dto -> Objects.requireNonNull(dto.getTemplateGroupId(), "模板组ID不能为空"))
                    .distinct().collect(Collectors.toList());
            log.info("SaveCreative:creativeTemplateGroupIds:{}", creativeTemplateGroupIds);

            // 获取模板组信息
            List<TemplateGroupBo> templateGroupDetailDtos = templateGroupService.queryTemplateGroupDetail(
                    QueryTemplateGroupDto.builder().templateGroupIds(creativeTemplateGroupIds)
                            .templateIds(templateIds)
                            .withTemplatesInQueryTemplateIds(true)
                            .withUnavailable(true)
                            .build());
            log.info("SaveCreative:templateGroupDetailDtos.size={}", CollectionHelper.getSize(templateGroupDetailDtos));

            if (isAdpVersionMerged) {
                // 旧版过滤版本
                templateGroupMap = templateGroupDetailDtos.stream()
                        .filter(x -> x.getBoundVersions().contains(TemplateGroupVersion.MERGE.getKey().toString()))
                        .collect(Collectors.toMap(TemplateGroupBo::getId, Function.identity()));
            } else {
                templateGroupMap = templateGroupDetailDtos.stream()
                        .filter(x -> {
                            if (!x.getBoundVersions().contains(TemplateGroupVersion.VIDEO_MERGE.getKey().toString()))
                                return false;
                            final String boundAccountLabelIds = x.getBoundAccountLabelIds();
                            if (StringUtils.isBlank(boundAccountLabelIds)) return true;
                            List<Integer> boundAccountLabelIdValList = LocUtils.parseCommaSplitStr(boundAccountLabelIds);
                            return accountLabelIds.stream().anyMatch(boundAccountLabelIdValList::contains);
                        })
                        .filter(x -> judgeTemplateGroupByAdvertisingMode(isAdpVersionMiddle, x.getAdvertisingModes(), sample.getAdvertisingMode()))
                        .collect(Collectors.toMap(TemplateGroupBo::getId, Function.identity()));
                templateMap = templateService.getValidTemplatesInIds(templateIds)
                        .stream()
                        .collect(Collectors.toMap(TemplateDto::getTemplateId, Function.identity()));
            }

            validateDynamicAreaCreative(unit.getFrequencyUnit(), unit.getFrequencyLimit(), templateGroupMap.entrySet().stream()
                    .filter(entry -> creativeTemplateGroupIds.contains(entry.getKey()))
                    .map(entry -> entry.getValue())
                    .collect(Collectors.toList()), adpVersion);
        }

        // 过滤出 pc 的模板
        final Set<Integer> pcTemplateIds;
        // (是否视频合并版本(3) 或 中台(5))
        if (isAdpVersionMergedVideo) {
            pcTemplateIds = availableTriplets.stream()
                    .filter(x -> Objects.equals(x.getSlotGroupFullBo().getChannelId(), ChannelEnum.PC.getCode()))
                    .map(x -> x.getLauResourceStyleTriplet().getTemplateId())
                    .collect(Collectors.toSet());
        }
        // 其他版本
        else {
            pcTemplateIds = slotGroupTemplateMappingDtos.stream()
                    .flatMap(x -> {
                        final ResSlotGroupBaseDto slotGroup = x.getSlotGroup();
                        if (Objects.isNull(slotGroup)) return Stream.empty();
                        if (Objects.equals(slotGroup.getChannelId(), ChannelEnum.PC.getCode())) {
                            final List<TemplateDto> templates = x.getTemplates();
                            if (CollectionUtils.isEmpty(templates)) return Stream.empty();
                            return templates.stream()
                                    .map(TemplateDto::getTemplateId)
                                    .filter(Objects::nonNull);
                        }
                        return Stream.empty();
                    }).collect(Collectors.toSet());
        }

        // enterprise mid 和 品牌信息处理
        final Long enterpriseMid = saveCreativeDtos.get(0).getEnterpriseMid();
        final Integer brandInfoId = saveCreativeDtos.get(0).getBrandInfoId();
        if (Utils.isPositive(enterpriseMid)) {
            if (!Objects.equals(enterpriseMid, unit.getEnterpriseMid())) {
                saveUnit(unitId, enterpriseMid);
                unit.setEnterpriseMid(enterpriseMid);
            }
            if (!Objects.equals(unit.getBrandInfoId(), 0)) {
                launchBrandInfoService.saveUnitBrandInfo(unitId, 0);
                unit.setBrandInfoId(0);
            }
        } else if (Utils.isPositive(brandInfoId)) {
            Assert.isTrue(launchBrandInfoService.isBrandInfoValid(unit.getAccountId(), brandInfoId), "品牌标非法");
            if (!Objects.equals(brandInfoId, unit.getBrandInfoId())) {
                launchBrandInfoService.saveUnitBrandInfo(unitId, brandInfoId);
            }
            if (!Objects.equals(0L, unit.getEnterpriseMid())) {
                saveUnit(unitId, 0L);
                unit.setEnterpriseMid(0L);
            }
        }

        // 落地页组
        List<Long> pageGroupIdList = saveCreativeDtos.stream()
                .filter(saveCreativeDto -> JumpType.isPageGroupJumpType(saveCreativeDto.getJumpType()))
                .map(saveCreativeDto -> Long.parseLong(saveCreativeDto.getPromotionPurposeContent()))
                .filter(Utils::isPositive)
                .distinct()
                .collect(Collectors.toList());
        QueryLaunchPageGroupBo queryPageGroupBo = QueryLaunchPageGroupBo.builder()
                .accountId(unit.getAccountId())
                .pageGroupIdList(pageGroupIdList)
                .build();
        List<LaunchPageGroupBo> launchPageGroupBoList = Collections.emptyList();
        if (!CollectionUtils.isEmpty(pageGroupIdList)) {
            launchPageGroupBoList = launchMgkPageGroupService.queryPageGroup(queryPageGroupBo);
        }
        Map<Long, LaunchPageGroupBo> launchPageGroupBoMap = launchPageGroupBoList.stream()
                .collect(Collectors.toMap(LaunchPageGroupBo::getGroupId, Function.identity()));

        // 转换成创意列表(拼接其他字段)
        List<CpcCreativeDto> creativeList = convertCreativeDto2List(saveCreativeDtos, unit, templateGroupMap, pcTemplateIds, launchPageGroupBoMap, account);

        // 个人起飞story，不校验
        if (!upOrderConfig.getFlySlotArchiveStory().equals(unit.getSlotGroup())) {
            validator.createCreative(saveCreativeDtos, unit, account, creativeList, templateGroupMap, launchPageGroupBoMap);
        }

        // 修改的创意 ids
        List<Integer> reserveIds = creativeList.stream()
                .filter(dto -> Utils.isPositive(dto.getCreativeId()))
                .map(CpcCreativeDto::getCreativeId)
                .collect(Collectors.toList());

        // 获取修改前已存在创意 map
        Map<Integer, CpcCreativeDto> reserveCreativeMap = getMapInIds(reserveIds);
        if (!CollectionUtils.isEmpty(reserveCreativeMap)) {
            //todo 设置旧创意的promotion purpose type 后面保存按钮会用到，目前没有想到比较好的方式 后续修改
            reserveCreativeMap.values().forEach(creative -> creative.setPromotionPurposeType(unit.getPromotionPurposeType()));
        }

        // 创意原有的story组件
        Map<Integer, LauStoryComponentBo> oldStoryComponentMap = adpCpcCreativeComponentService.fetchMapOnlyStory(unit.getAccountId(), reserveIds);
        // 创意现有的story组件
        Map<Integer, LauStoryComponentBo> newStoryComponentMap = new HashMap<>();
        creativeList.stream().forEach(o -> {
            List<CreativeComponentBo> components = o.getComponents();
            if (components != null) {
                CreativeComponentBo creativeComponentBo = components.stream().filter(p -> (p.getType() == STORY_COMMON || p.getType() == STORY_COUPON || p.getType() == STORY_IMAGE))
                        .findFirst().orElse(null);
                if (creativeComponentBo != null) {
                    LauStoryComponentBo storyComponentBo = storyComponentService.getBo4Creative(creativeComponentBo.getId(), creativeComponentBo.getType());
                    if (storyComponentBo != null) {
                        newStoryComponentMap.put(o.getCreativeId(), storyComponentBo);
                    }
                }
            }
        });

        if (deleteOtherInUnit) {
            deleteCreativeByUnitId(unit.getUnitId(), reserveIds);
        }

        // (是否视频合并版本(3) 或 中台(5))
        if (isAdpVersionMergedVideo) {
            // 保存 lau_unit_scene
            launchUnitSceneService.save(LauUnitSceneBo.from(unitId, sample.getChannelId(), sample.getSceneIds()));
        }

        final HiddenContextBo hiddenContextBo = HiddenContextBo.builder()
                .isInnerAccount(Utils.isPositive(account.getIsInner()))
                .salesType(unit.getSalesType())
                .firstOcpxTarget(unit.getOcpcTarget())
                .build();
        if (AdvertisingMode.nativeContentMode(sample.getAdvertisingMode())) {
            hiddenContextBo.setIsNativeMode(true);
            final ArcReply reply = archiveServiceProxy.arc(sample.getBilibiliVideo().getAvId());
            hiddenContextBo.setHasHorizontalArchive(reply.getArc().getDimension().getWidth() > reply.getArc().getDimension().getHeight());
        } else {
            hiddenContextBo.setIsNativeMode(false);
            hiddenContextBo.setHasHorizontalArchive(false);
        }
        // 保存创意列表
        List<Integer> savedCreativeIds = saveCreativeNew(operator, unit, creativeList, templateMap, templateGroupMap, reserveCreativeMap, account, slotGroupTemplateMappingDtos, availableTriplets, operator.getOperatorName(), launchPageGroupBoMap, hiddenContextBo
        , seasonMap);

        // for promotionPurposeContent compare
        for (CpcCreativeDto cpcCreativeDto : creativeList) {
            cpcCreativeDto.setPromotionPurposeContent(JumpTypeEnum.getByCode(cpcCreativeDto.getJumpType())
                    .parseLaunchUrl(cpcCreativeDto.getPromotionPurposeContent()));
        }

        // 创意新增日志
        addCreativeInsertLog(operator, creativeList, reserveCreativeMap, savedCreativeIds, oldStoryComponentMap, newStoryComponentMap);

        // 更新落地页组使用状态
        launchMgkPageGroupService.batchUpdatePageGroupUsing(pageGroupIdList);

        return savedCreativeIds;
    }

    private void saveUnit(Integer unitId, Long enterpriseMid) {
        adCoreBqf.update(lauUnit)
                .where(lauUnit.unitId.eq(unitId))
                .set(lauUnit.enterpriseMid, enterpriseMid)
                .execute();
    }

    //新版下检验动态的投放频控
    private void validateDynamicAreaCreative(Integer unitFrequencyUnit, Integer unitFrequencyLimit, List<TemplateGroupBo> templateGroupBoList, Integer adpVersion) {

        if (CollectionUtils.isEmpty(templateGroupBoList)) {
            return;
        }
        //查找是否有创意的模板是动态
        boolean isAnyCreativeSupportDynamicArea = templateGroupBoList.stream().anyMatch(TemplateGroupBo::getIsDynamicArea);
        // 视频版位合并去掉对频控的校验
        if (isAnyCreativeSupportDynamicArea && !AdpVersion.isVideoMerged(adpVersion)) {
            Assert.isTrue(unitFrequencyLimit <= dynamicUnitFrequencyLimit.get(unitFrequencyUnit), "动态页资源位频控必须小于等于" + dynamicUnitFrequencyLimit.get(unitFrequencyUnit) + "，请返回单元层级重新设置");
        }

    }

    // 中台版本校验内容投放
    private boolean judgeTemplateGroupByAdvertisingMode(boolean isMiddle, String advertisingModeStr, Integer advertisingMode) {
        if (!isMiddle) {
            return true;
        }
        if (StringUtils.isEmpty(advertisingModeStr)) {
            return false;
        }
        List<Integer> advertisingModes = parseCommaSplitStr(advertisingModeStr);
        if (CollectionUtils.isEmpty(advertisingModes)
                || !advertisingModes.contains(advertisingMode)) {
            return false;
        }
        return true;
    }

    /**
     * 从单元上获取模板
     *
     * @param unit
     * @return
     * @throws ServiceException
     */
    private Map<Integer, TemplateGroupBo> getTemplateMapByUnit(CpcUnitDto unit) throws ServiceException {
        List<ResSlotGroupTemplateMappingDto> slotGroups = resSlotGroupService
                .querySlotGroupTemplateMappingInSlotGroupIds(QueryTemplateLaunchTypeMappingDto
                        .builder()
                        .slotGroupId(unit.getSlotGroup())
                        .promotionPurposeType(unit.getPromotionPurposeType())
                        .build());

        Assert.isTrue(!CollectionUtils.isEmpty(slotGroups), LaunchExceptionCode.CREATIVE_UNIT_GROUP_NO_TEMPLATE.getMessage());

        List<TemplateDto> templates = slotGroups.get(0).getTemplates();
        Assert.isTrue(!CollectionUtils.isEmpty(templates), LaunchExceptionCode.CREATIVE_UNIT_GROUP_NO_TEMPLATE.getMessage());

        return templates.stream().distinct().collect(Collectors.toMap(TemplateDto::getTemplateId, TemplateUtils::template2TemplateGroup));
    }

    /**
     * 转换成创意列表(拼接其他字段)
     *
     * @param saveCreativeDtos
     * @param unit
     * @param templateMap
     * @param pcTemplateIds
     * @param accountDto
     * @return
     * @throws ServiceException
     */
    private List<CpcCreativeDto> convertCreativeDto2List(List<CpcCreativeDetailDto> saveCreativeDtos,
                                                         CpcUnitDto unit,
                                                         Map<Integer, TemplateGroupBo> templateMap,
                                                         Set<Integer> pcTemplateIds,
                                                         Map<Long, LaunchPageGroupBo> launchPageGroupBoMap,
                                                         AccountBaseDto accountDto) throws ServiceException {
        AdpCatUtils.logEventAndLog("convertCreativeDto2List", "convertCreativeDto2List", "unitId:%s", unit.getUnitId());

        // 过滤出按钮文案
        List<Integer> buttonCopyIds = saveCreativeDtos
                .stream()
                .filter(info -> info.getAttachType() != null &&
                        CreativeAttachTypeEnum.BUTTON_COPY.getCode() == info.getAttachType() &&
                        Utils.isPositive(info.getButtonCopyId()))
                .map(CpcCreativeDetailDto::getButtonCopyId)
                .distinct()
                .collect(Collectors.toList());

        Map<Integer, ButtonCopyDto> buttonCopyMap = queryButtonCopyService.getMapInds(buttonCopyIds);
        // 获取账户角标
        Map<Integer, CmMarkDto> cmMarkMap = cmMarkService.getCmMarkMapByAccountId(unit.getAccountId());
        log.info("saveCreativeDto2List:buttonCopyMap={}", JSON.toJSONString(buttonCopyMap));
        log.info("saveCreativeDto2List:cmMarkMap={}", JSON.toJSONString(cmMarkMap));

        // buildCreativeDto
        return saveCreativeDtos.stream()
                .map(saveCreativeDto -> buildCreativeDto(unit, saveCreativeDto, templateMap, cmMarkMap, buttonCopyMap, launchPageGroupBoMap, pcTemplateIds, accountDto))
                .collect(Collectors.toList());
    }

    private CpcCreativeDto buildCreativeDto(CpcUnitDto unit,
                                            CpcCreativeDetailDto saveCreativeDto,
                                            Map<Integer, TemplateGroupBo> templateMap,
                                            Map<Integer, CmMarkDto> cmMarkMap,
                                            Map<Integer, ButtonCopyDto> buttonCopyMap,
                                            Map<Long, LaunchPageGroupBo> launchPageGroupBoMap,
                                            Set<Integer> pcTemplateIds,
                                            AccountBaseDto accountDto) {
        TemplateGroupBo templateGroupBo = templateMap.get(saveCreativeDto.getTemplateGroupId());
        Assert.notNull(templateGroupBo, "该模板(组)不支持:" + saveCreativeDto.getTemplateGroupId());

        // 视频合并版本(3) 或 中台(5): 处理商业标
        if (!AdpVersion.isVideoMerged(unit.getAdpVersion())) {
            // 视频版位合并商业标逻辑变更, 跳过这个校验
            resolveBusMark(unit, saveCreativeDto, templateGroupBo);
            // todo: 此处得到了创意的商业标
        }

        String landingPageUrl = getActualLandingPageUrl(saveCreativeDto.getPromotionPurposeContent(),
                IsValid.TRUE.getCode().equals(templateGroupBo.getIsSupportVideoId()), unit.getChannelId(), saveCreativeDto.getVideoId());

        saveCreativeDto.setPromotionPurposeContent(landingPageUrl);

        // useH5看着没用了 pcTemplateIds现在也不准了
        final int useH5;
        final List<Integer> templateIds = templateGroupBo.getTemplateIdList();
        if (CollectionUtils.isEmpty(templateIds)) {
            useH5 = 0;
        } else {
            // PC资源位一定要使用H5
            useH5 = templateIds.stream().anyMatch(pcTemplateIds::contains) ? 1 : 0;
        }


        //落地页目前会返回两个url，分别存入 promotion_purpose_content 和 promotion_purpose_content_secondary
        ValidateAndGetPageReply landingPage = this.validateJumpTypeAndGetPromotionPurposeContent(saveCreativeDto.getJumpType(), landingPageUrl);
        Long pageGroupId = JumpType.isPageGroupJumpType(saveCreativeDto.getJumpType()) ?
                Long.parseLong(saveCreativeDto.getPromotionPurposeContent()) : null;
        boolean isContentLaunch = Utils.isPositive(saveCreativeDto.getAdvertisingMode());
        int adVersionControlId = judgeAdVersionControlId(pageGroupId, isContentLaunch, launchPageGroupBoMap, landingPage);
        String promotionPurposeContent = formatPageGroupPromotionPurposeContent(saveCreativeDto.getJumpType(), landingPage.getJumpUrl());

        Integer busMarkId = saveCreativeDto.getBusMarkId();
        // 三连 无商业标
        if (AdpVersion.isMiddle(unit.getAdpVersion()) && !Utils.isPositive(busMarkId)) {
            busMarkId = Utils.isPositive(saveCreativeDto.getAdvertisingMode()) ? BUS_MARK_CONTENT : BUS_MARK_CM;
        }

        CpcCreativeDto.CpcCreativeDtoBuilder creativeBuilder = CpcCreativeDto.builder()
                .creativeId(saveCreativeDto.getCreativeId())
                // 创意类型
                .creativeType(CollectionHelper.getFirst(templateGroupBo.getCreativeTypes()))
                .orderId(0)
                .adpVersion(saveCreativeDto.getAdpVersion())
                .preferScene(saveCreativeDto.getPreferScene())
                .scenes(saveCreativeDto.getScenes())
                .jumpType(saveCreativeDto.getJumpType())
                .campaignId(unit.getCampaignId())
                .accountId(unit.getAccountId())
                .unitId(unit.getUnitId())
                // 模板
                .template(templateGroupBo)
                .salesType(unit.getSalesType())
                .cmMark(saveCreativeDto.getCmMark())
                .cmMarkDesc(cmMarkMap.getOrDefault(saveCreativeDto.getCmMark(), CmMarkDto.builder().name("广告").build()).getName())
                .creativeName(saveCreativeDto.getCreativeName())
                .promotionPurposeContent(promotionPurposeContent)
                .promotionPurposeContentSecondary(landingPage.getJumpUrlSecondary())
                .pageGroupId(pageGroupId)
                .isPageGroup(Utils.isPositive(pageGroupId) ? 1 : 0)
                .customizedClickUrl(StringUtils.trimToEmpty(saveCreativeDto.getCustomizedClickUrl()))
                .customizedImpUrl(StringUtils.trimToEmpty(saveCreativeDto.getCustomizedImpUrl()))
                .title(genWildcard(StringUtils.trimToEmpty(saveCreativeDto.getTitle()), saveCreativeDto.getWildcardDesc()))
                .wildcardDesc(saveCreativeDto.getWildcardDesc())
                // 模板id
                .templateId(saveCreativeDto.getTemplateId())
                .templateGroupId(saveCreativeDto.getTemplateGroupId())
                // 图片
                .imageDtos(buildImageWithMd5(saveCreativeDto.getImageDtos()))
                .extImageUrl(saveCreativeDto.getExtImageUrl())
                .extImageMd5(Strings.isNullOrEmpty(saveCreativeDto.getExtImageHash()) ? "" : getMd5FromHash(saveCreativeDto.getExtImageHash()))
                .videoUrl(saveCreativeDto.getVideoUrl())
                .description(StringUtils.trimToEmpty(saveCreativeDto.getDescription()))
                .extDescription(StringUtils.trimToEmpty(saveCreativeDto.getExtDescription()))
                // 视频 id
                .videoId(saveCreativeDto.getVideoId())
                .mgkVideoId(saveCreativeDto.getMgkVideoId())
                .miniGameId(saveCreativeDto.getMiniGameId())
                .miniGameUrl(saveCreativeDto.getMiniGameUrl())
                .buttonCopy(saveCreativeDto.getButtonCopy())
                .firstCategoryId(saveCreativeDto.getFirstCategoryId())
                .secondCategoryId(saveCreativeDto.getSecondCategoryId())
                .tags(CollectionUtils.isEmpty(saveCreativeDto.getTags())
                        ? Collections.EMPTY_LIST : Lists.newArrayList(saveCreativeDto.getTags()))
                .beginTime(LaunchUtil.getString2Date(unit.getLaunchBeginDate()))
                .endTime(LaunchUtil.getString2Date(unit.getLaunchEndDate()))
                .attachType(saveCreativeDto.getAttachType())
                .buttonCopyId(saveCreativeDto.getButtonCopyId())
                .buttonCopyUrl(saveCreativeDto.getButtonCopyUrl())
                .promotionPurposeType(unit.getPromotionPurposeType())
                // 创意图片
                .imageUrl(CollectionUtils.isEmpty(saveCreativeDto.getImageDtos()) ? "" : saveCreativeDto.getImageDtos().get(0).getUrl())
                .imageMd5(CollectionUtils.isEmpty(saveCreativeDto.getImageDtos()) ? "" : getMd5FromImageDto(saveCreativeDto.getImageDtos().get(0)))
                .isAutoFill(saveCreativeDto.getIsAutoFill())
                .schemeUrl(saveCreativeDto.getSchemeUrl())
                .danmakus(saveCreativeDto.getDanmakus())
                .mgkPageId(landingPage.getPageId())
                .mgkPageStatus(landingPage.getPageStatusValue())
                .adVersionControllId(adVersionControlId)
                .buFirstCategoryId(saveCreativeDto.getBuFirstCategoryId())
                .buSecondCategoryId(saveCreativeDto.getBuSecondCategoryId())
                .buThirdCategoryId(saveCreativeDto.getBuThirdCategoryId())
                .adMark(saveCreativeDto.getAdMark())
                .busMarkId(busMarkId)
                .shareState(saveCreativeDto.getShareState())
                .shareTitle(saveCreativeDto.getShareTitle())
                .shareSubtitle(saveCreativeDto.getShareSubtitle())
                .shareImageUrl(saveCreativeDto.getShareImageUrl())
                .shareImageMd5(Strings.isNullOrEmpty(saveCreativeDto.getShareImageHash()) ? "" : getMd5FromHash(saveCreativeDto.getShareImageHash()))
                .creativeStyle(saveCreativeDto.getCreativeStyle())
                .creativeMonitoring(trimCreativeMonitoring(saveCreativeDto.getCreativeMonitoring()))
                .bilibiliUserId(saveCreativeDto.getBilibiliUserId())
                .isNewFly(saveCreativeDto.getIsNewFly())
                .flag(saveCreativeDto.getFlag())
                .brandInfoId(saveCreativeDto.getBrandInfoId())
                .bilibiliVideoBo(saveCreativeDto.getBilibiliVideo())
                .dynamicId(saveCreativeDto.getDynamicId())
                .dynamicType(saveCreativeDto.getDynamicType())
                .likeCount(saveCreativeDto.getLikeCount())
                .nickName(saveCreativeDto.getNickName())
                .flyCopy(saveCreativeDto.isFlyCopy())
                .sourceCreativeIdAuditStatus(saveCreativeDto.getSourceCreativeIdAuditStatus())
                .sourceCreativeIdCreativeStatus(saveCreativeDto.getSourceCreativeIdCreativeStatus())
                .advertisingMode(saveCreativeDto.getAdvertisingMode())
                .industryId(saveCreativeDto.getIndustryId())
                .tabId(saveCreativeDto.getTabId())
                .isGdPlus(saveCreativeDto.getIsGdPlus())
                .isMiddleAd(saveCreativeDto.getIsMiddleAd())
                .qualificationPackageId(saveCreativeDto.getQualificationPackageId())
                .components(saveCreativeDto.getCreativeComponents())
                .imageMacroType(saveCreativeDto.getImageMacroType())
                .imageMacroPlaceHolder(saveCreativeDto.getImageMacroPlaceHolder())
                .danmakuGroupId(saveCreativeDto.getDanmakuGroupId())
                ;


        if (templateGroupBo.getIsDynamicArea()) {
            creativeBuilder.dynamic(buildDynamicDto(saveCreativeDto));
        }

        CpcCreativeDto creative = creativeBuilder.build();

        // 投稿内容，如果没有设置分区，设置默认
        if (unit.getPromotionPurposeType().equals(PromotionPurposeType.ARCHIVE_CONTENT.getCode())) {
            if (creative.getBuFirstCategoryId() == 0) {
                creative.setBuFirstCategoryId(ARCHIVE_FIRST_CATEGORY);
            }
            if (creative.getBuSecondCategoryId() == 0) {
                creative.setBuSecondCategoryId(ARCHIVE_SECOND_CATEGORY);
            }
            if (creative.getBuThirdCategoryId() == 0) {
                creative.setBuThirdCategoryId(ARCHIVE_THIRD_CATEGORY);
            }
            if (ARCHIVE_CM_MARK == CmMarkEnum.UP_CAMPAIGN.getCode()) {
                creative.setCmMark(ARCHIVE_CM_MARK);
                creative.setAdMark(ARCHIVE_AD_MARK);
            } else {
                // 商业标写死
                if (Utils.isPositive(creative.getCmMark())) {
                    creative.setCmMark(ARCHIVE_CM_MARK);
                }
                if (Strings.isNullOrEmpty(creative.getAdMark())) {
                    creative.setAdMark(ARCHIVE_AD_MARK);
                }
            }
        }

        // 直播间
        if (unit.getPromotionPurposeType().equals(PromotionPurposeType.LIVE_ROOM.getCode()) && saveCreativeDto.getRefreshType() != null) {
            CreativeRefreshType.getByCode(saveCreativeDto.getRefreshType());
            CpcCreativeAutoDto autoDto = new CpcCreativeAutoDto();
            autoDto.setRefreshType(saveCreativeDto.getRefreshType());
            creative.setAutoDto(autoDto);
        }

        // 添加游戏点击监控链接
        if (Utils.isPositive(unit.getGameBaseId())) {
            final CpcCreativeMonitoringDto cpcCreativeMonitoringDto = genGameHiddenMonitoring(unit.getGameBaseId(), unit.getGamePlatformType(), unit.getSubPkg(), Objects.equals(accountDto.getIsInner(), 1));
            if (CollectionUtils.isEmpty(creative.getCreativeMonitoring())) {
                creative.setCreativeMonitoring(Collections.singletonList(cpcCreativeMonitoringDto));
            } else {
                creative.getCreativeMonitoring().add(cpcCreativeMonitoringDto);
            }
        }

        // 填充默认创意样式
        fillCreativeStyleDefault(creative, templateGroupBo);

        resolveButtonCopy(creative, buttonCopyMap, templateGroupBo, unit);

        return creative;
    }

    public CpcCreativeMonitoringDto genGameHiddenMonitoring(Integer gameBaseId, Integer gamePlatformType, Integer subPkg, boolean isInner) {
        CpcCreativeMonitoringDto creativeMonitoringDto = new CpcCreativeMonitoringDto();
        creativeMonitoringDto.setType(CreativeMonitoringType.GAME_CLICK_MONITOR.getCode());

        String url = gameCenterConfiguration.getGameMonitorUrl().replace("game_base_id=&platform_type=",
                "game_base_id=" + gameBaseId + "&platform_type=" + gamePlatformType);
        // 内广增加sourcetype=inner参数
        if (isInner) {
            url = url + "&sourcetype=inner";
        }
        // 标记广告包的参数
        url += "&sycp_pkg_type=" + subPkg;
        url = launchUrlDecorationService.decorate(url, RequestParam.GAME_HIDDEN_MONITOR_PARAMS);
        url =  UriComponentsBuilder.fromUriString(url)
                .replaceQueryParam("sycp_ip", "__IPV4__")
                .replaceQueryParam("sycp_ip_before", "__IP__")
                .replaceQueryParam("sycp_android_id", "__ANDROIDID__")
                .build(false).toString();
        creativeMonitoringDto.setUrls(Collections.singletonList(url));
        return creativeMonitoringDto;
    }

    /**
     * 处理商业标
     *
     * @param unit
     * @param saveCreativeDto
     * @param template
     */
    private void resolveBusMark(CpcUnitDto unit, CpcCreativeDetailDto saveCreativeDto, TemplateGroupBo template) {
        log.info("resolveBusMark for CpcCreativeDetailDto [{}]", JSON.toJSONString(saveCreativeDto));

        //起飞gd+默认使用火箭标
        if (IsValid.TRUE.getCode().equals(unit.getIsGdPlus())) {
            saveCreativeDto.setBusMarkId(rocketBusMarkId);
            return;
        }

        // 根据帐号和cardType获取有效的商业标
        List<BusMarkDto> markList = busMarkRuleService.getValidBusMarkList(unit.getAccountId(), BusMarkRuleAdSysEnum.CPC, template.getCardTypes());
        log.info("getValidBusMarkList accountId: [{}], cardTypes: [{}], markList: [{}]", unit.getAccountId(), template.getCardTypes(), markList);

        // 账号没有商业标
        if (CollectionUtils.isEmpty(markList)) {
            Assert.isTrue(!Utils.isPositive(saveCreativeDto.getBusMarkId()), "由于当前没有效的商业标列表，故无法添加商业标");
            saveCreativeDto.setBusMarkId(0);
        }
        // 账号存在商业标
        else {
            List<Integer> markIds = markList.stream().map(BusMarkDto::getId).collect(Collectors.toList());

            // 没有添加商业标 || 账号下的商业标包含添加的创意的商业标
            boolean isSuccessMarkInput = !Utils.isPositive(saveCreativeDto.getBusMarkId())
                    || markIds.contains(saveCreativeDto.getBusMarkId());
            log.info("isSuccessMarkInput: [{}], busMarkId: [{}]", isSuccessMarkInput, saveCreativeDto.getBusMarkId());
            Assert.isTrue(isSuccessMarkInput, "无效的商业标");

            // 没有添加商业标
            if (!Utils.isPositive(saveCreativeDto.getBusMarkId())) {
                Cat.logEvent("BusMarkId_NULL", "UNIT-" + unit.getUnitId());

                // 账号下存在商业标，取第一个
                if (markIds.size() > 0) {
                    saveCreativeDto.setBusMarkId(markIds.get(0));
                } else {
                    throw new IllegalArgumentException("无有效的商业标");
                }
            }
        }
    }

    public ValidateAndGetPageReply validateJumpTypeAndGetPromotionPurposeContent(Integer jumpType, String promotionPurposeContent) {
        log.info("validateJumpTypeAndGetPromotionPurposeContent jumpType:[{}], promotionPurposeContent: [{}], channelId: [{}]", jumpType, promotionPurposeContent);
        // 如果没有传jump type, 给默认值1
        if (jumpType == null) {
            jumpType = JumpTypeEnum.LINK.getCode();
        }
        JumpTypeEnum jumpTypeEnum = JumpTypeEnum.getByCode(jumpType);
//        Assert.isTrue(jumpTypeEnum.validateUrlIsValid(promotionPurposeContent), "跳转类型与链接值不匹配");
        return this.validatePageIdAndGetLandingPage(jumpTypeEnum, promotionPurposeContent);
    }

    public ValidateAndGetPageReply validatePageIdAndGetLandingPage(JumpTypeEnum jumpType,
                                                                   String promotionPurposeContent) {
        //校验页面ID
        if (JumpTypeEnum.MGK_PAGE_ID.equals(jumpType)) {
            return landingPageServiceBlockingStub.validatePageIdAndGetPage(ValidateAndGetPageReq.newBuilder().setPageId(Long.parseLong(promotionPurposeContent)).build());
        } else if (promotionPurposeContent.contains(MgkConstants.H5_LANDING_PAGE.replace(MgkConstants.HTTPS_SCHEME, ""))) {
            Assert.isTrue(promotionPurposeContent.startsWith(MgkConstants.HTTPS_SCHEME), "请使用https开头的落地页链接");
            Long mgkH5PageId = Long.valueOf(JumpTypeEnum.MGK_PAGE_ID.getGameOrPageIdFromUrl(promotionPurposeContent));
            MgkPagesStatusReply mgkPagesStatusReply = landingPageServiceBlockingStub.mgkPageStatus(MgkPagesStatusReq.newBuilder().addMgkPageId(mgkH5PageId).build());
            MgkPageStatus pagesStatus = mgkPagesStatusReply.getPagesStatusOrDefault(mgkH5PageId, MgkPageStatus.UNKNOWN);
            Assert.isTrue(Arrays.asList(MgkPageStatus.WAIT_AUDIT, MgkPageStatus.PUBLISHED).contains(pagesStatus), "非发布状态或待审核的页面不可选择");
            return ValidateAndGetPageReply
                    .newBuilder()
                    .setPageId(mgkH5PageId)
                    .setType(0)
                    .setTemplateStyle(0)
                    .setJumpUrl(promotionPurposeContent)
                    .setAdVersionControlId(0)
                    .build();
        } else {
            return ValidateAndGetPageReply
                    .newBuilder()
                    .setPageId(0)
                    .setType(0)
                    .setTemplateStyle(0)
                    .setJumpUrl(promotionPurposeContent)
                    .setAdVersionControlId(0)
                    .build();
        }
    }

    public Integer judgeAdVersionControlId(Long pageGroupId,
                                           boolean isContentLaunch,
                                           Map<Long, LaunchPageGroupBo> launchPageGroupBoMap,
                                           ValidateAndGetPageReply landingPage) {
        if (Utils.isPositive(pageGroupId)) {
            LaunchPageGroupBo pageGroupBo = launchPageGroupBoMap.get(pageGroupId);
            if (Objects.nonNull(pageGroupBo)) {
                if (isContentLaunch) {
                    return com.bilibili.adp.cpc.biz.constants.Constants.LAUNCH_PAGE_GROUP_CONTENT_LAUNCH_AD_VERSION_CONTROL_ID;
                }
                // 建站落地页组 有固定的adVersionControlId
                return PageGroupSource.MGK_SOURCE_VALUE == pageGroupBo.getGroupSource() ?
                        com.bilibili.adp.cpc.biz.constants.Constants.LAUNCH_PAGE_GROUP_MGK_SOURCE_AD_VERSION_CONTROL_ID : 0;
            }
        }
        return landingPage.getAdVersionControlId();
    }

    public String formatPageGroupPromotionPurposeContent(Integer jumpType, String ppc) {
        if (!JumpTypeEnum.MGK_PAGE_GROUP.getCode().equals(jumpType)
                && !JumpTypeEnum.THIRD_PARTY_PAGE_GROUP.getCode().equals(jumpType)) {
            return ppc;
        }
        return JumpTypeEnum.getByCode(jumpType).getLaunchUrl(ppc);
    }

    public String getActualLandingPageUrl(String landingPageUrl,
                                          boolean isSupportVideo, Integer channelId, Long videoId) {
        if (isSupportVideo) {

            if (!Strings.isNullOrEmpty(landingPageUrl)) {
                return landingPageUrl;
            } else if (MOB_CHANNEL_ID.equals(channelId)) {
                return Constants.VIDEO_PREFIX + videoId;
            } else if (PC_CHANNEL_ID.equals(channelId)) {
                return Constants.PC_VIDEO_PREFIX + videoId;
            } else {
                return Constants.PC_VIDEO_PREFIX + videoId;
            }
        }

        return StringUtils.trimToEmpty(landingPageUrl);
    }

    public String genWildcard(String title, CpcCreativeWildcard wildcardDesc) {
        if (Strings.isNullOrEmpty(title) || wildcardDesc == null) {
            return title;
        }

        String result = title;

        if (!Strings.isNullOrEmpty(wildcardDesc.getArea())) {
            Assert.isTrue(wildcardDesc.getArea().length() < 5, "地域长度大于4");
            result = result.replaceAll(AREA_PLAINTEXT, AREA_CIPHERTEXT + "," + wildcardDesc.getArea().trim());
        }

        if (!Strings.isNullOrEmpty(wildcardDesc.getAge())) {
            Assert.isTrue(wildcardDesc.getAge().length() < 7, "年龄长度大于6");
            result = result.replaceAll(AGE_PLAINTEXT, AGE_CIPHERTEXT + "," + wildcardDesc.getAge().trim());
        }

        if (!Strings.isNullOrEmpty(wildcardDesc.getDevice())) {
            Assert.isTrue(wildcardDesc.getDevice().length() < 3, "设备长度大于2");
            result = result.replaceAll(DEVICE_PLAINTEXT, DEVICE_CIPHERTEXT + "," + wildcardDesc.getDevice().trim());
        }

        if (!Strings.isNullOrEmpty(wildcardDesc.getGender())) {
            Assert.isTrue(wildcardDesc.getGender().length() < 2, "性别长度大于1");
            result = result.replaceAll(GENDER_PLAINTEXT, GENDER_CIPHERTEXT + "," + wildcardDesc.getGender().trim());
        }

        return result;
    }

    public List<ImageDto> buildImageWithMd5(List<ImageDto> imageDtos) {
        if (CollectionUtils.isEmpty(imageDtos)) {
            return Collections.emptyList();
        }

        return imageDtos.stream().peek(dto -> {
            dto.setMd5(getMd5FromImageDto(dto));
        }).collect(Collectors.toList());
    }

    public String getMd5FromImageDto(ImageDto dto) {
        if (Strings.isNullOrEmpty(dto.getMd5())) {
            return getMd5FromHash(dto.getHash());
        }
        return dto.getMd5();
    }

    public String getMd5FromHash(String hash) {
        if (Strings.isNullOrEmpty(hash)) {
            return "";
        }
        String decodeHash = new String(Base64.decodeBase64(hash));
        ImageHash imageHash = JSON.parseObject(decodeHash, ImageHash.class);
        return imageHash.getMd5();
    }

    public List<CpcCreativeMonitoringDto> trimCreativeMonitoring(List<CpcCreativeMonitoringDto> from) {
        List<CpcCreativeMonitoringDto> to = Collections.emptyList();
        if (!CollectionUtils.isEmpty(from)) {
            to = from.stream()
                    .map(this::trimCreativeMonitoring)
                    .filter(Optional::isPresent)
                    .map(Optional::get)
                    .collect(Collectors.toList());
        }
        return to;
    }

    private Optional<CpcCreativeMonitoringDto> trimCreativeMonitoring(CpcCreativeMonitoringDto from) {
        boolean containMonitoring = false;
        if (!CollectionUtils.isEmpty(from.getUrls())) {
            containMonitoring = from.getUrls().stream().anyMatch(StringUtils::isNotBlank);
        }
        if (containMonitoring) {
            List<String> urls = from.getUrls().stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
            return Optional.of(CpcCreativeMonitoringDto.builder().type(from.getType()).urls(urls).build());
        }
        return Optional.empty();
    }

    private CpcCreativeDynamicDto buildDynamicDto(CpcCreativeDetailDto saveCreativeDto) {
        CpcCreativeDynamicDto dynamicDto = CpcCreativeDynamicDto.builder()
                .titleLink(saveCreativeDto.getTitleLink())
                .titleLinkName(saveCreativeDto.getTitleLinkName())
                .forwardState(saveCreativeDto.getForwardState())
                .replyState(saveCreativeDto.getReplyState())
                .replyMonitor(saveCreativeDto.getReplyMonitor())
                .build();

        // 禁止评论的评论区，设置默认审核状态为先审后发。兼容后续无需设置审核状态，即可直接开启评论区
        if (ReplySubjectStateEnum.CLOSE.getCode().equals(dynamicDto.getReplyState())
                && dynamicDto.getReplyMonitor() == null) {
            dynamicDto.setReplyMonitor(ReplySubjectMonitorEnum.AUDIT_FIRST.getCode());
        }
        return dynamicDto;
    }

    /**
     * 填充默认创意样式
     * 优先级“inline视频>GIF图文>静态视频>静态图文” 默认
     *
     * @param creative
     * @param template
     */
    private void fillCreativeStyleDefault(CpcCreativeDto creative, TemplateGroupBo template) {
        if (creative.getCreativeStyle() == null) {
            List<CreativeStyle> supportedStyles = template.getCreativeStyles() == null ? Collections.emptyList() : template.getCreativeStyles();
            CreativeStyle defaultStyle = SORTED_STYLE.stream().filter(supportedStyles::contains).findFirst().orElse(CreativeStyle.IMAGE);
            creative.setCreativeStyle(defaultStyle.getCode());
        }
    }

    private void resolveButtonCopy(CpcCreativeDto creative, Map<Integer, ButtonCopyDto> buttonCopyMap,
                                   TemplateGroupBo templateDto, CpcUnitDto unit) {
        if (creative.getAttachType() != null && CreativeAttachTypeEnum.BUTTON_COPY.getCode() == creative.getAttachType()) {
            ButtonCopyDto buttonCopyDto = buttonCopyMap.get(creative.getButtonCopyId());
            Assert.notNull(buttonCopyDto, "该按钮文案不存在");
            creative.setButtonCopy(buttonCopyDto.getContent());

            LaunchButtonTypeEnum launchButtonTypeEnum = getButtonType(PromotionPurposeType.getByCode(unit.getPromotionPurposeType()), ButtonCopyTypeEnum.getByCode(buttonCopyDto.getType()), creative.getButtonCopyUrl());

            if (LaunchButtonTypeEnum.APP_DOWNLOAD.equals(launchButtonTypeEnum)) {
                AppPackageDto appInfo = unit.getAppInfo();
                creative.setButtonCopyUrl(Strings.isNullOrEmpty(creative.getButtonCopyUrl()) ? "" : creative.getButtonCopyUrl());
                creative.setAppDownloadUrl(appInfo != null ? (Strings.isNullOrEmpty(unit.getAppInfo().getInternalUrl()) ? appInfo.getUrl() : appInfo.getInternalUrl()) : "");
            }

        } else if (IsValid.TRUE.getCode().equals(templateDto.getIsSupportButton())) {
            creative.setButtonCopy(Constants.DEFAULT_BUTTON_COPY);
        } else {
            creative.setButtonCopy("");
            creative.setButtonCopyUrl("");
        }
    }

    public LaunchButtonTypeEnum getButtonType(PromotionPurposeType promotionPurposeType, ButtonCopyTypeEnum buttonCopyTypeEnum, String jumpUrl) {
        if (PromotionPurposeType.LANDING_PAGE.equals(promotionPurposeType) ||
                PromotionPurposeType.SALE_GOODS.equals(promotionPurposeType)) {
            if (Strings.isNullOrEmpty(jumpUrl)
                    || jumpUrl.startsWith(Constants.BILIBILI_SCHEME)) {
                //落地页
                return LaunchButtonTypeEnum.LANDING_PAGE;
            } else {
                //APP唤醒
                return LaunchButtonTypeEnum.APP_AWAKEN;
            }
        } else if (PromotionPurposeType.APP_DOWNLOAD.equals(promotionPurposeType)) {
            if (ButtonCopyTypeEnum.JUMP_LINK.equals(buttonCopyTypeEnum)) {
                if (Strings.isNullOrEmpty(jumpUrl)
                        || jumpUrl.startsWith(Constants.BILIBILI_SCHEME)) {
                    //落地页
                    return LaunchButtonTypeEnum.LANDING_PAGE;
                } else {
                    //APP唤醒
                    return LaunchButtonTypeEnum.APP_AWAKEN;
                }
            } else {
                //APP下载
                return LaunchButtonTypeEnum.APP_DOWNLOAD;
            }
        } else if (PromotionPurposeType.LIVE_ROOM.equals(promotionPurposeType)) {
            if (ButtonCopyTypeEnum.LIVE_BOOKING == buttonCopyTypeEnum) {
                return LaunchButtonTypeEnum.LIVE_RESERVE;
            }
        }

        return LaunchButtonTypeEnum.LANDING_PAGE;
    }

    public Map<Integer, CpcCreativeDto> getMapInIds(List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyMap();
        }

        List<CpcCreativeDto> dtos = creativeServiceDelegate.queryCpcCreativeDto(QueryCpcCreativeDto
                .builder()
                .creativeIds(ids)
                .withButtonCopy(true)
                .withImages(true)
                .withVideo(true)
                .withDanmaku(true)
                .withBusinessCategory(true)
                .withDynamic(true)
                .withAutoInfo(true)
                .withMonitoring(true)
                .withUnit(true)
                .withFlyUnderBox(true)
                .withFlyScenes(true)
                .withCreativeTab(true)
                .withPageGroup(true)
                .withDanmakuGroup(true)
                .build());

        if (CollectionUtils.isEmpty(dtos)) {
            return Collections.emptyMap();
        }

        return dtos.stream().collect(Collectors.toMap(CpcCreativeDto::getCreativeId, dto -> dto));
    }

    /**
     * 删除单元下 reserveIds 以外的创意
     *
     * @param unitId
     * @param reserveIds
     */
    @Transactional(value = AD_CORE_TM, rollbackFor = Exception.class)
    public void deleteCreativeByUnitId(Integer unitId, List<Integer> reserveIds) {
        log.info("try to deleteCreativeByUnitId with unitId={} reserveIds={}", unitId, reserveIds);
        AdpCatUtils.logEventAndLog("deleteCreativeByUnitId", "deleteCreativeByUnitId", "unitId=%s,reserveIds=%s",
                unitId, JSON.toJSONString(reserveIds));
        Assert.notNull(unitId, "单元id不能为空");

        LauUnitCreativePoExample example = new LauUnitCreativePoExample();
        LauUnitCreativePoExample.Criteria c = example.or()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andCreativeStatusIn(CreativeStatus.NON_DELETE_CREATIVE_STATUS_LIST)
                .andUnitIdEqualTo(unitId);

        if (!CollectionUtils.isEmpty(reserveIds)) {
            c.andCreativeIdNotIn(reserveIds);
        }

        List<LauUnitCreativePo> pos = lauUnitCreativeDao.selectByExample(example);

        if (CollectionUtils.isEmpty(pos)) {
            return;
        }

        // 软删除创意
        List<Integer> deleteCreativeIds = pos.stream().map(LauUnitCreativePo::getCreativeId).collect(Collectors.toList());

        // 校验落地页组审核任务情况
        launchMgkPageGroupBindService.checkCreativePageGroupAuditing(deleteCreativeIds, Collections.emptyMap());

        LauUnitCreativePoExample deleteExample = new LauUnitCreativePoExample();
        deleteExample.or()
                .andUnitIdEqualTo(unitId)
                .andCreativeIdIn(deleteCreativeIds);

        LauUnitCreativePo po = new LauUnitCreativePo();
        po.setCreativeStatus(CreativeStatus.DELETED.getCode());
        po.setStatus(LaunchStatus.DELETE.getCode());

        lauUnitCreativeDao.updateByExampleSelective(po, deleteExample);

        launchShadowCreativeService.deleteByCreativeIds(deleteCreativeIds);

        // 删除了不好恢复创意，暂不删除
        /*deleteCreativeImageInCreativeIds(deleteCreativeIds);
        deleteCreativeVideoInCreativeIds(deleteCreativeIds);
        deleteCreativeButtonCopyInCreativeIds(deleteCreativeIds);
        deleteCreativeLayoutInCreativeIds(deleteCreativeIds);
        deleteCreativeShareInCreativeIds(deleteCreativeIds);
        deleteCreativeAutoInCreativeIds(deleteCreativeIds);
        deleteCreativeMonitoringInCreativeIds(deleteCreativeIds);*/
        // 有级联拒审逻辑 保证性能 这里先删除
        launchMgkPageGroupBindService.unbindPageGroup(deleteCreativeIds);
        adpCpcSelfDanmakuGroupMappingService.clearAllDanmakuGroupMappingFromDeleteCreativeIds(deleteCreativeIds);
    }

    private void deleteCreativeImageInCreativeIds(List<Integer> creativeIds) {
        if (CollectionUtils.isEmpty(creativeIds)) {
            return;
        }

        LauCreativeImagePoExample example = new LauCreativeImagePoExample();
        example.or()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andCreativeIdIn(creativeIds);

        LauCreativeImagePo po = new LauCreativeImagePo();
        po.setIsDeleted(IsDeleted.DELETED.getCode());

        lauCreativeImageDao.updateByExampleSelective(po, example);
    }

    private void deleteCreativeVideoInCreativeIds(List<Integer> creativeIds) {
        if (CollectionUtils.isEmpty(creativeIds)) {
            return;
        }

        LauCreativeVideoPoExample example = new LauCreativeVideoPoExample();
        example.or()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andCreativeIdIn(creativeIds);

        LauCreativeVideoPo po = new LauCreativeVideoPo();
        po.setIsDeleted(IsDeleted.DELETED.getCode());

        lauCreativeVideoDao.updateByExampleSelective(po, example);
    }

    private void deleteCreativeButtonCopyInCreativeIds(List<Integer> creativeIds) {
        if (CollectionUtils.isEmpty(creativeIds)) {
            return;
        }

        LauCreativeButtonCopyPoExample example = new LauCreativeButtonCopyPoExample();
        example.or()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andCreativeIdIn(creativeIds);

        LauCreativeButtonCopyPo po = new LauCreativeButtonCopyPo();
        po.setIsDeleted(IsDeleted.DELETED.getCode());

        lauCreativeButtonCopyDao.updateByExampleSelective(po, example);
    }

    private void deleteCreativeLayoutInCreativeIds(List<Integer> creativeIds) {
        log.info("deleteCreativeLayoutInCreativeIds, creativeIds:{}", JSON.toJSONString(creativeIds));
        if (CollectionUtils.isEmpty(creativeIds)) {
            return;
        }

        LauCreativeLayoutPoExample example = new LauCreativeLayoutPoExample();
        example.or()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andCreativeIdIn(creativeIds);

        LauCreativeLayoutPo po = new LauCreativeLayoutPo();
        po.setIsDeleted(IsDeleted.DELETED.getCode());

        lauCreativeLayoutDao.updateByExampleSelective(po, example);
    }

    private void deleteCreativeShareInCreativeIds(List<Integer> creativeIds) {
        if (CollectionUtils.isEmpty(creativeIds)) {
            return;
        }

        LauCreativeSharePoExample example = new LauCreativeSharePoExample();
        example.or()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andCreativeIdIn(creativeIds);

        LauCreativeSharePo po = new LauCreativeSharePo();
        po.setIsDeleted(IsDeleted.DELETED.getCode());

        creativeShareDao.updateByExampleSelective(po, example);
    }

    private void deleteCreativeAutoInCreativeIds(List<Integer> creativeIds) {
        if (CollectionUtils.isEmpty(creativeIds)) {
            return;
        }

        LauCreativeAutoPoExample example = new LauCreativeAutoPoExample();
        example.or()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andCreativeIdIn(creativeIds);

        LauCreativeAutoPo po = new LauCreativeAutoPo();
        po.setIsDeleted(IsDeleted.DELETED.getCode());

        lauCreativeAutoDao.updateByExampleSelective(po, example);
    }

    public void addCreativeInsertLog(Operator operator, List<CpcCreativeDto> creativeList, Map<Integer, CpcCreativeDto> reserveCreativeMap,
                                     List<Integer> savedCreativeIds, Map<Integer, LauStoryComponentBo> oldStoryComponentMap,
                                     Map<Integer, LauStoryComponentBo> newStoryComponentMap) {

        addCreativeInsertLogByOperationDto(operator,
                // 新创意
                creativeList.stream().map(dto -> creativeServiceDelegate.creativeDtoToOperationDto(
                        dto, newStoryComponentMap.get(dto.getCreativeId()))).collect(Collectors.toList()),
                // 老创意
                reserveCreativeMap.entrySet().stream().collect(
                        Collectors.toMap(Map.Entry::getKey, x -> creativeServiceDelegate.creativeDtoToOperationDto(
                                x.getValue(), oldStoryComponentMap.get(x.getKey())))),
                savedCreativeIds
        );
    }

    private void addCreativeInsertLogByOperationDto(Operator operator, List<CpcCreativeOperationDto> creativeList, Map<Integer,
            CpcCreativeOperationDto> reserveCreativeMap, List<Integer> savedCreativeIds) {
        List<CpcCreativeOperationDto> updateCreatives = creativeList
                .stream()
                .filter(c -> !CollectionUtils.isEmpty(reserveCreativeMap)
                        && reserveCreativeMap.containsKey(c.getCreativeId()))
                .collect(Collectors.toList());

        creativeList.removeAll(updateCreatives);

        if (!CollectionUtils.isEmpty(creativeList)) {
            List<Integer> newCreativeIds = savedCreativeIds
                    .stream()
                    .filter(id -> CollectionUtils.isEmpty(reserveCreativeMap)
                            || !reserveCreativeMap.containsKey(id))
                    .collect(Collectors.toList());

            logOperateService.addBatchInsertLog(DbTable.LAU_UNIT_CREATIVE, operator, creativeList, newCreativeIds, false);
        }

        if (!CollectionUtils.isEmpty(reserveCreativeMap)) {
            List<Integer> updateCreativeIds = Lists.newArrayList(reserveCreativeMap.keySet());
            List<CpcCreativeOperationDto> oldCreatives = Lists.newArrayList(reserveCreativeMap.values());

            oldCreatives.forEach(c -> {
                c.setTitle(genWildcard(StringUtils.trimToEmpty(c.getTitle()), c.getWildcardDesc()));
            });

            oldCreatives.sort(Comparator.comparing(CpcCreativeOperationDto::getCreativeId));
            updateCreatives.sort(Comparator.comparing(CpcCreativeOperationDto::getCreativeId));
            updateCreativeIds.sort(Comparator.naturalOrder());

            logOperateService.addBatchUpdateLog(DbTable.LAU_UNIT_CREATIVE, operator, oldCreatives, updateCreatives, updateCreativeIds);
        }
    }

    /**
     * 保存创意列表
     *
     * @param operator
     * @param unit
     * @param creativeList                 需要保存到创意列表
     * @param templateMap
     * @param templateGroupMap
     * @param reserveCreatives             获取修改前已存在创意 map
     * @param account
     * @param slotGroupTemplateMappingDtos
     * @param availableTriplets
     * @param operatorName
     * @return
     * @throws ServiceException
     */
    private List<Integer> saveCreative(
            Operator operator,
            CpcUnitDto unit,
            List<CpcCreativeDto> creativeList,
            Map<Integer, TemplateDto> templateMap,
            Map<Integer, TemplateGroupBo> templateGroupMap,
            Map<Integer, CpcCreativeDto> reserveCreatives,
            AccountBaseDto account,
            List<ResSlotGroupTemplateMappingDto> slotGroupTemplateMappingDtos,
            List<LauResourceStyleTripletWithSlotGroup> availableTriplets,
            String operatorName) throws ServiceException {
        List<Integer> savedCreativeIds = Lists.newArrayList();
        AdpCatUtils.logEventAndLog("saveCreatives", "saveCreatives start", "unitId=%s", unit.getUnitId());

        final boolean supportContainerLandingPage = launchCreativeLandingPageService.supportContainerLandingPage(account.getAccountId());
        final boolean supportContainerLandingPageH5 = launchCreativeLandingPageService.supportContainerLandingPageH5(account.getAccountId());

        // mapi可能数据不含mid 需要手动填充
        List<BilibiliVideoBo> bilibiliVideoBoList = creativeList.stream()
                .map(CpcCreativeDto::getBilibiliVideoBo)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        biliVideoService.fillMid(bilibiliVideoBoList);

        for (CpcCreativeDto creative : creativeList) {
            AdpCatUtils.logEventAndLog("保存一个创意", "save creative start", "creativeId=%s", creative.getCreativeId());

            TemplateGroupBo template = templateGroupMap.get(creative.getTemplateGroupId());

            // 修改的创意获取老的创意
            CpcCreativeDto oldCreative = reserveCreatives.get(creative.getCreativeId());

            // 构建 creative po
            LauUnitCreativePo creativePo = convertNewCreativeToPo(creative, oldCreative, unit, account);
            log.info("Save creative LauUnitCreativePo {}, CpcUnitDto {}, oldCreative {}", JSON.toJSONString(creativePo), unit, oldCreative);

            //原生内容投放需要设置 avid 和 tab 名称
            if (AdvertisingMode.nativeContentMode(creative.getAdvertisingMode())) {
                // 处理视频物料
                dealBilibiliVideo(creative, creativePo);
                creativePo.setJumpType(JumpTypeEnum.LINK.getCode());
            }
            // 常规投放
            else {
                final boolean isArchive = Objects.equals(unit.getPromotionPurposeType(), PromotionPurposeType.ARCHIVE_CONTENT.getCode());
                // 非投稿内容
                if (!isArchive) {
                    final boolean supportBiliVideo = Functions.integer2Boolean(template.getIsSupportVideo()) && Objects.nonNull(creative.getBilibiliVideoBo()) && Utils.isPositive(creative.getBilibiliVideoBo().getAvId());
                    if (supportBiliVideo) {
                        dealBilibiliVideo(creative, creativePo);
                    } else {
                        // 当素材从稿件变成inline视频时，要将次字段置为0
                        creativePo.setMaterialVideoId(0L);
                        creativePo.setVideoId(0L);
                    }
                }
            }
            int oldId = creativePo.getCreativeId() != null && creativePo.getCreativeId() > 0 ? creativePo.getCreativeId() : 0;

            if (oldCreative != null) {
                Assert.isTrue(oldCreative.getAccountId().equals(unit.getAccountId()), "你不能操作不属于您的创意");
            }

            //是否插入
            boolean isInsert = Objects.isNull(creativePo.getCreativeId());

            boolean isForceLiveReserveButton = false;
            // 直播预约对于无视客户的选择, 对于包含88卡的广告强行加一个按钮
            if (Objects.equals(unit.getPromotionPurposeType(), PromotionPurposeType.LIVE_RESERVE.getCode())) {
                final List<Integer> cardTypes = template.getCardTypes();
                if (!CollectionUtils.isEmpty(cardTypes) && cardTypes.contains(88)) {
                    isForceLiveReserveButton = true;
                    creative.setButtonCopyId(32);
                    creative.setButtonCopy("预约直播");
                    creative.setButtonCopyUrl("");
                }
            }

            // 拼接创意 trackadf
            creativePo.setTrackadf(creativeTrackadfProc.spliceCreateTrackadfIfNecessary(creative.getCreativeId(), account, creativePo.getPromotionPurposeContent()));

            // 新增或者修改创意
            this.saveOrUpdatePo(creativePo, unit.getAccountId(), unit.getUnitId());

            // 创建创意待审核记录
            if (Objects.equals(AuditStatus.INIT.getCode(), creativePo.getAuditStatus())) {
                creativeServiceDelegate.addCreativeAuditStat(creativePo.getCreativeId(), com.bilibili.adp.common.enums.CreativeAuditEvent.AUDIT, operatorName);
            }
            int newCreativeId = oldId > 0 ? oldId : creativePo.getCreativeId();
            log.info("get creative after save newCreativeId {}, creativePo {}", newCreativeId, JSON.toJSONString(creativePo));

            LauUnitCreativePo updatedPo = lauUnitCreativeDao.selectByPrimaryKey(newCreativeId);
            Assert.isTrue(updatedPo != null &&
                            updatedPo.getAccountId() != null && updatedPo.getAccountId().equals(unit.getAccountId()) &&
                            updatedPo.getTitle().equals(creative.getTitle()) &&
                            updatedPo.getDescription().equals(creative.getDescription()),
                    "保存创意失败，请稍后重试");

            // 创意落地页分享
            saveCreativeShare(creative, newCreativeId);

            // 修改创意的情况保存创意自动更新记录
            saveCreativeAuto(creative, newCreativeId);

            if (Boolean.TRUE.equals(creative.getTemplate().getIsDynamicArea())) {
                // 动态模板，注册动态
                saveRegisterCreativeDynamic(newCreativeId, creative, oldCreative, unit);
            }

            // 创意视频映射
            saveCreativeIdBizIdMapping(newCreativeId, creative.getJumpType(), creative.getMgkPageId());

            final Collection<ImageDto> oldCreativeImages;
            if (Objects.isNull(oldCreative) || Objects.isNull(oldCreative.getImageDtos())) {
                oldCreativeImages = Collections.emptyList();
            } else {
                oldCreativeImages = oldCreative.getImageDtos();
            }
            // 保存创意图片
            saveCreativeImages(unit.getUnitId(), newCreativeId, Optional.ofNullable(creative.getImageDtos()).orElse(Collections.emptyList()), oldCreativeImages);

            // 删除创意视频
            if (oldCreative != null && !Utils.isPositive(creative.getMgkVideoId())) {
                deleteCreativeVideo(oldCreative.getCreativeId());
            }
            // 保存创意视频云视频
            saveCreativeVideo(newCreativeId, creative.getMgkVideoId());

            //button copy
            List<ButtonCopyDto> validateButtonCopyDtos = template.getButtonCopyDtos();
            Map<Integer, Integer> buttonCopyId2TypeMap = CollectionUtils.isEmpty(validateButtonCopyDtos) ? Collections.emptyMap() : validateButtonCopyDtos.stream().collect(Collectors.toMap(ButtonCopyDto::getId, ButtonCopyDto::getType));

            if (isForceLiveReserveButton) {
                saveLiveReserveButtonCopy(unit.getUnitId(), newCreativeId);
            } else {
                saveCreativeButtonCopy(unit, creative, IsValid.TRUE.getCode().equals(template.getIsSupportButton()),
                        newCreativeId, buttonCopyId2TypeMap.get(creative.getButtonCopyId()));
            }

            saveDanmaku(newCreativeId, creative.getDanmakus());
            saveCreativeMonitoring(newCreativeId, creative);

            String formatedJumpUrl;
            String formatedUrlSecondary = StringUtils.EMPTY;
            String formatedTabJumpUrl = StringUtils.EMPTY;
            final boolean isSubPkg = !Objects.isNull(unit.getSubPkg()) && Functions.integer2Boolean(unit.getSubPkg() > 1 ? 0 : unit.getSubPkg());
            // 内容投放
            if (AdvertisingMode.nativeContentMode(creative.getAdvertisingMode())) {
                boolean containsWildcard = Objects.nonNull(creative.getWildcardDesc()) && Stream.of(creative.getWildcardDesc().getArea(),
                        creative.getWildcardDesc().getGender(),
                        creative.getWildcardDesc().getDevice(),
                        creative.getWildcardDesc().getAge())
                        .anyMatch(StringUtils::isNotBlank);

                formatedJumpUrl = buildNativeContentJumpUrl(true, containsWildcard, newCreativeId, creative.getBilibiliVideoBo().getAvId().toString());
                //降级连接保存在tab中就行
                //H5使用建站连接
                String tabJumpUrl = creative.getPromotionPurposeContent();
                //如果是小程序 则需要用小程序降级连接
                if (MgkLandingPageParserUtils.isMiniApp(creative.getPromotionPurposeContent())) {
                    tabJumpUrl = creative.getPromotionPurposeContentSecondary();
                }
                formatedTabJumpUrl = getFormatedJumpUrl(creative.getAdvertisingMode(), newCreativeId, unit, tabJumpUrl, account, template, isSubPkg);
            } else {
                formatedJumpUrl = getFormatedJumpUrl(creative.getAdvertisingMode(), newCreativeId, unit, creative.getPromotionPurposeContent(), account, template, isSubPkg);
                formatedUrlSecondary = getFormatedJumpUrl(creative.getAdvertisingMode(), newCreativeId, unit, creative.getPromotionPurposeContentSecondary(), account, template, isSubPkg);
            }

            saveFormatedJumpUrl(newCreativeId, formatedJumpUrl, formatedUrlSecondary);

            // 保存创意 extra 信息
            Integer ppcType = lauCreativeExtraService.generatePpcType(formatedJumpUrl, unit.getPromotionPurposeType(), unit.getBusinessDomain(), creative.getVideoId(), creative.getAdvertisingMode());
            LauCreativeExtraPo creativeExtraPo = LauCreativeExtraPo.builder()
                    .creativeId(newCreativeId).accountId(creative.getAccountId())
                    .campaignId(creative.getCampaignId()).unitId(creative.getUnitId())
                    .qualificationPackageId(creative.getQualificationPackageId())
                    .ppcType(ppcType)
                    .build();
            lauCreativeExtraProc.saveCreativeExtra(creativeExtraPo, newCreativeId);

            // 保存创意联投需要的信息
            if (Objects.equals(PromotionPurposeType.LANDING_PAGE.getCode(), unit.getPromotionPurposeType())
                    || Objects.equals(PromotionPurposeType.ON_SHELF_GAME.getCode(), unit.getPromotionPurposeType())
                    || Objects.equals(PromotionPurposeType.APP_DOWNLOAD.getCode(), unit.getPromotionPurposeType())
                    || Objects.equals(PromotionPurposeType.SALE_GOODS.getCode(), unit.getPromotionPurposeType())) {
                launchCreativeLandingPageService.saveLandingPageExtraInfo(operator, !AdvertisingMode.nativeContentMode(creative.getAdvertisingMode()) && supportContainerLandingPage, supportContainerLandingPageH5, unit.getPromotionPurposeType(), newCreativeId, creativePo.getMgkPageId(), unit.getGameBaseId(), unit.getAppPackageId(), formatedJumpUrl);
            }

            // 保存微信小游戏绑定关系
            if (AdpVersion.isMiddle(creative.getAdpVersion())
                    && !lauCreativeMiniGameMappingService.checkMappingIsEqual(newCreativeId, creative.getMiniGameId(), creative.getMiniGameUrl())) {
                LauCreativeMiniGameMappingBo saveBo = LauCreativeMiniGameMappingBo.builder()
                        .accountId(operator.getOperatorId())
                        .creativeId(newCreativeId)
                        .miniGameId(creative.getMiniGameId())
                        .gameUrl(creative.getMiniGameUrl())
                        .build();
                lauCreativeMiniGameMappingService.saveCreativeMiniGameMapping(saveBo);
            }

            //insert layout
            saveCreativLayout(creative, template, newCreativeId, formatedJumpUrl);

            saveCreativeBusinessCategory(newCreativeId, creative.getBuFirstCategoryId(), creative.getBuSecondCategoryId(), creative.getBuThirdCategoryId());

            savedCreativeIds.add(newCreativeId);

            // 处理加急审核情况
            processUrgentAudit(unit.getAccountId(), creative, oldCreative, creativePo.getAuditStatus(), newCreativeId);

            // 保存创意模板关系
            saveSlotGroupTemplateMapping(unit, creative, templateGroupMap.get(creative.getTemplateGroupId()), templateMap, slotGroupTemplateMappingDtos, availableTriplets, newCreativeId, false, null);

            // 保存创意场景
            saveCreativeScenes(creative, newCreativeId);

            //原生内容投放时 需要额外保存tab信息
            if (AdvertisingMode.nativeContentMode(creative.getAdvertisingMode())) {
                TabEnum tab = TabEnum.getByTabId(creative.getTabId());
                //注：原生内容投放的创意，formatedJumpUrl是对应的tab jump url
                //创意本身的跳转连接是根据video id构造的跳转连接
                saveCreativeTab(account.getAccountId(), unit.getUnitId(), newCreativeId, tab, creative.getJumpType(), formatedTabJumpUrl);

                String trackadf = creativeTrackadfProc.spliceCreateTrackadfIfNecessary(creative.getCreativeId(), account, formatedTabJumpUrl);

                // 内容投放的 trackadf 需要根据 formatedJumpUrl
                LauUnitCreativePo newCreativePo = LauUnitCreativePo.builder()
                        .creativeId(creativePo.getCreativeId())
                        .trackadf(trackadf)
                        .build();
                lauUnitCreativeDao.updateByPrimaryKeySelective(newCreativePo);
            }

            //专业起飞复制，自动审核(通过&驳回)
            if (isInsert && unit.getIsNewFly() == 1 && creative.isFlyCopy()) {
                creativeCommonService.autoAuditCopyCreative(unit.getUnitId(), creativePo.getCreativeId(), creative.getSourceCreativeIdAuditStatus());
                //暂停创意
                if (ObjectUtils.nullSafeEquals(creative.getSourceCreativeIdCreativeStatus(), CreativeStatus.PAUSED.getCode())) {
                    LauUnitCreativePoExample example = new LauUnitCreativePoExample();
                    example.or()
                            .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                            .andAccountIdEqualTo(account.getAccountId())
                            .andCreativeIdEqualTo(creativePo.getCreativeId());
                    LauUnitCreativePo po = new LauUnitCreativePo();
                    po.setStatus(LaunchStatus.STOP.getCode());
                    po.setCreativeStatus(CreativeStatus.PAUSED.getCode());
                    lauUnitCreativeDao.updateByExampleSelective(po, example);
                }
                //再查出来,写日志
                LauUnitCreativePo po = lauUnitCreativeDao.selectByPrimaryKey(creativePo.getCreativeId());
                creative.setAuditStatus(po.getAuditStatus());
                creative.setCreativeStatus(po.getCreativeStatus());
                creative.setStatus(po.getStatus());
            } else {
                //专业起飞，新建直播间创意，自动审核
                if (isInsert && unit.getIsNewFly() == 1 && unit.getPromotionPurposeType() == PromotionPurposeType.LIVE_ROOM.getCode()) {
                    creativeCommonService.autoAuditLiveRoomCreative(unit.getUnitId(), creativePo.getCreativeId());
                }
            }

            //动态保存lau_creative_fly_dynamic_info
            if (ObjectUtils.nullSafeEquals(DynamicTypeEnum.CHOOSE_DYNAMIC.getCode(), creative.getDynamicType())) {
                this.saveDynamicInfo(isInsert, account.getAccountId(), unit.getUnitId(), newCreativeId,
                        creative.getDynamicId(), creative.getLikeCount(), creative.getNickName());
            }
            launchComponentService.saveCreativeComponent(creative.getAccountId(), creative.getCampaignId(), creative.getUnitId(), newCreativeId, Optional.ofNullable(creative.getComponents()).orElse(Collections.emptyList()));
        }

        List<Long> dynamicIds = creativeList.stream().map(t -> t.getDynamicId()).filter(Objects::nonNull).filter(Utils::isPositive).distinct().collect(Collectors.toList());
        lauDynamicService.processDynamics(dynamicIds);

        // 删除未使用的创意模板(估计有bug在兜底)
        deleteUnUsedCreativeTemplate(unit.getUnitId(), savedCreativeIds);
        log.info("Return creativeIds {}", savedCreativeIds);
        return savedCreativeIds;
    }

    @SneakyThrows
    private List<Integer> saveCreativeNew(
            Operator operator,
            CpcUnitDto unit,
            List<CpcCreativeDto> creativeList,
            Map<Integer, TemplateDto> templateMap,
            Map<Integer, TemplateGroupBo> templateGroupMap,
            Map<Integer, CpcCreativeDto> reserveCreatives,
            AccountBaseDto account,
            List<ResSlotGroupTemplateMappingDto> slotGroupTemplateMappingDtos,
            List<LauResourceStyleTripletWithSlotGroup> availableTriplets,
            String operatorName,
            Map<Long, LaunchPageGroupBo> launchPageGroupBoMap,
            HiddenContextBo hiddenContextBo,
            Map<String, com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauCreativeExtraPo> seasonMap) throws ServiceException {
        List<Integer> savedCreativeIds = new ArrayList<>();
        final int accountId = account.getAccountId();
        final int campaignId = unit.getCampaignId();
        final int unitId = unit.getUnitId();
        final int promotionPurposeType = unit.getPromotionPurposeType();
        final int salesType = unit.getSalesType();
        final String materialId = Optional.ofNullable(unit.getLauSubject()).map(o->o.getMaterialId()).orElse("");
        // 影子创意账号标签 504
        final boolean canSaveShadowCreative = launchAccountGroupService.isAccountWithLabel(accountId, effectAdShadowCreativeLabelId);
        // 单元下各个创意的影子创意
        final Map<Integer, ShadowCreativeBo> shadowCreativeMap = launchShadowCreativeService.shadowCreatives(unitId);

        // mapi可能数据不含mid 需要手动填充
        List<BilibiliVideoBo> bilibiliVideoBoList = creativeList.stream()
                .map(CpcCreativeDto::getBilibiliVideoBo)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        biliVideoService.fillMid(bilibiliVideoBoList);

        List<Integer> creativeIds = creativeList.stream().map(t -> t.getCreativeId()).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        Map<Integer, LauCreativeNativeArchiveRelativityPo> creativeNativeArchiveRelativityPoMap = lauCreativeNativeArchiveRelativityRepo.queryNativeArchiveRelativityPoMap(creativeIds);
        Set<Long> cmVideoSet = biliVideoService.getCmVideoSet(creativeList.stream()
                .filter(x -> !Objects.isNull(x.getBilibiliVideoBo()))
                .map(x -> Optional.ofNullable(x.getBilibiliVideoBo().getAvId()).orElse(0L))
                .filter(x -> x > 0L).collect(Collectors.toList()));

        for (CpcCreativeDto creative : creativeList) {
            hiddenContextBo.setTemplateGroupIds(Collections.singletonList(creative.getTemplateGroupId()));
            TemplateGroupBo templateGroup = templateGroupMap.get(creative.getTemplateGroupId());
            final Map<Integer, ButtonCopyDto> buttonMap = CollectionUtils.isEmpty(templateGroup.getButtonCopyDtos()) ?
                    Collections.emptyMap() : templateGroup.getButtonCopyDtos()
                    .stream()
                    .collect(Collectors.toMap(ButtonCopyDto::getId, Function.identity()));
            // 修改的创意获取老的创意
            CpcCreativeDto oldCreative = reserveCreatives.get(creative.getCreativeId());
            Integer originalAuditStatus = Optional.ofNullable(oldCreative)
                    .map(CpcCreativeDto::getAuditStatus)
                    .orElse(AuditStatus.UNKNOWN.getCode());
            // 构建 creative po
            Integer bilibiliUserId = Optional.ofNullable(creative.getBilibiliUserId()).orElseGet(() -> UserType.getByCode(account.getUserType()) == UserType.PERSONAL_FLY ? CreativeSourceType.PERSONAL_FLY.getCode() : CreativeSourceType.UNKNOWN.getCode());
            List<ImageDto> imageDtos = creative.getImageDtos();
            Long materialVideoId;
            Long videoId;
            final boolean isArchive = Objects.equals(promotionPurposeType, PromotionPurposeType.ARCHIVE_CONTENT.getCode());
            final boolean supportBiliVideo = Functions.integer2Boolean(templateGroup.getIsSupportVideo())
                    && Objects.nonNull(creative.getBilibiliVideoBo())
                    && Utils.isPositive(creative.getBilibiliVideoBo().getAvId());
            final boolean needSaveBilibiliVideo = AdvertisingMode.nativeContentMode(creative.getAdvertisingMode())
                    || (!isArchive && supportBiliVideo);
            if (needSaveBilibiliVideo) {
                materialVideoId = creative.getBilibiliVideoBo().getAvId();
                videoId = creative.getBilibiliVideoBo().getAvId();
                biliVideoService.save(creative.getBilibiliVideoBo(), cmVideoSet.contains(creative.getBilibiliVideoBo().getAvId()));
                // 如果前端没传封面, 就写进去, 防止引擎拿不到封面
                if (CollectionUtils.isEmpty(creative.getImageDtos())) {
                    // 获取稿件的封面
                    final ImageDto image = ImageDto.builder()
                            .url(creative.getBilibiliVideoBo().getCoverUrl())
                            .md5(creative.getBilibiliVideoBo().getCoverMd5())
                            .build();
                    imageDtos = Collections.singletonList(image);
                }
            } else {
                materialVideoId = 0L;
                videoId = 0L;
            }
            final boolean isMgkH5LandingPage = creative.getPromotionPurposeContent().contains(MgkConstants.H5_LANDING_PAGE.replace(MgkConstants.HTTP_SCHEME, ""));
            //todo 我觉得这段逻辑是有问题的 主要是针对内容投放, 因为内容投放前端操作选的落地页 完全有可能是 建站H5落地页。这种情况下，lau_unit
            final Long mgkPageId;
            if (!Utils.isPositive(creative.getMgkPageId()) && isMgkH5LandingPage) {
                mgkPageId = Long.valueOf(JumpTypeEnum.MGK_PAGE_ID.getGameOrPageIdFromUrl(creative.getPromotionPurposeContent()));
            } else {
                mgkPageId = creative.getMgkPageId();
            }
            //landingpage type 不能为1 校验 类型为1的落地页已下线
            if (Utils.isPositive(mgkPageId)) {
                ValidateAndGetPageReply validateAndGetPageReply = landingPageServiceBlockingStub.validatePageIdAndGetPage(ValidateAndGetPageReq.newBuilder()
                        .setPageId(mgkPageId)
                        .build());
                Assert.isTrue(!LandingPageTypeEnum.H5.getCode().equals(validateAndGetPageReply.getType()), "H5落地页类型已下线");
            }
            //落地页链接类型
            Integer landingPageJumpType = creative.getJumpType();
            if (AdvertisingMode.nativeContentMode(creative.getAdvertisingMode())) {
                landingPageJumpType = JumpTypeEnum.LINK.getCode();
            }
            //原生落地页链接类型
            Integer nativeLandingPageJumpType = creative.getJumpType();
            // 按钮相关逻辑 构造按钮
            ButtonCopyDto buttonCopy = buttonMap.getOrDefault(creative.getButtonCopyId(), ButtonCopyDto.builder().build());
            // 创意主表上的按钮文案，我理解应该是没用了，但是历史逻辑使用这个来判断是否需要推审
            String buttonCopyUrl = creative.getButtonCopyUrl();
            String customizedClickUrl = creative.getCustomizedClickUrl();
            // 直播预约对于无视客户的选择, 对于包含88卡的广告强行加一个按钮
            final boolean forceUseLiveReserveButton = Objects.equals(promotionPurposeType, PromotionPurposeType.LIVE_RESERVE.getCode())
                    && !CollectionUtils.isEmpty(templateGroup.getCardTypes())
                    && templateGroup.getCardTypes().contains(88);
            if (forceUseLiveReserveButton) {
                buttonCopy = LaunchCreativeButtonService.LIVE_RESERVE_BUTTON;
                buttonCopyUrl = StringUtils.EMPTY;
                customizedClickUrl = StringUtils.EMPTY;
            }
            Integer buttonCopyId = buttonCopy.getId();
            Integer buttonCopyType = buttonCopy.getType();
            //之前com.bilibili.adp.cpc.biz.services.creative.CpcSaveCreativeService.resolveButtonCopy此方法已经设置过按钮文案，故直接使用
            String buttonCopyContent = creative.getButtonCopy();
            final String imageUrl, imageMd5;
            if (StringUtils.isNotBlank(creative.getImageMacroPlaceHolder())) {
                imageUrl = creative.getImageMacroPlaceHolder();
                imageMd5 = "";
                // 前端不方便传, 后端自行添加, get的时候需要再去掉
                final ImageDto macroImageDto = new ImageDto();
                macroImageDto.setUrl(imageUrl);
                macroImageDto.setMd5(imageMd5);
                imageDtos = new ArrayList<>(imageDtos);
                imageDtos.add(macroImageDto);
            } else if (!CollectionUtils.isEmpty(imageDtos)) {
                final ImageDto sampleImageDto = imageDtos.get(0);
                imageUrl = sampleImageDto.getUrl();
                imageMd5 = sampleImageDto.getMd5();
            } else {
                imageUrl = "";
                imageMd5 = "";
            }
            creative.setAdpVersion(unit.getAdpVersion());
            creative.setIsMiddleAd(1);
            creative.setBilibiliUserId(bilibiliUserId);
            creative.setIsHistory(0);
            creative.setMgkPageId(mgkPageId);
            creative.setImageDtos(imageDtos);
            creative.setMaterialVideoId(materialVideoId);
            creative.setVideoId(videoId);
            creative.setButtonCopyId(buttonCopyId);
            creative.setButtonCopyType(buttonCopyType);
            creative.setButtonCopy(buttonCopyContent);
            creative.setImageUrl(imageUrl);
            creative.setImageMd5(imageMd5);
            //创意是否新建
            boolean isNewCreative = Objects.isNull(oldCreative);
            //没有旧创意，获取不到影子创意，自然不可能存在影子创意
            final ShadowCreativeBo prevShadowCreative = shadowCreativeMap.get(Optional.ofNullable(oldCreative).map(CpcCreativeDto::getCreativeId).orElse(0));
            final boolean existedShadowCreative = Objects.nonNull(prevShadowCreative);
            final boolean skipAudit = Objects.equals(promotionPurposeType, PromotionPurposeType.GOODS_CATALOG.getCode());
            //是否需要推审
            final boolean needReAudit;
            if (skipAudit) {
                needReAudit = false;
            } else if (!canSaveShadowCreative || !existedShadowCreative) {
                needReAudit = launchCreativeService.creativeNeedReAudit(creative, oldCreative);
            } else {
                needReAudit = launchCreativeService.creativeNeedReAudit(creative, prevShadowCreative);
            }
            //是否需要保存影子创意 = (有保存影子创意的权限 && 更新创意 && 原创意审核通过 && 需要重新审核) || (存在一个待审核的影子创意)
            final boolean needSaveShadowCreative = (canSaveShadowCreative && !isNewCreative && originalAuditStatus == com.bilibili.adp.cpc.core.constants.AuditStatus.PASSED && needReAudit)
                    || (Objects.nonNull(prevShadowCreative) && Objects.equals(prevShadowCreative.getAuditStatus(), com.bilibili.adp.cpc.core.constants.AuditStatus.AUDITING));
            // 生成创意 po 并填充状态
            com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauUnitCreativePo creativePo = LaunchCreativeService.generatePoAndModifyStatus(creative, oldCreative, needSaveShadowCreative, needReAudit, skipAudit);

            ShadowCreativeBo.ShadowCreativeBoBuilder shadowCreativeBuilder = ShadowCreativeBo.builder();
            final CreativeButtonBo buttonBo = launchCreativeButtonService.generateCreativeButton(unitId, promotionPurposeType, buttonCopyId, buttonCopyType, buttonCopyContent, buttonCopyUrl, customizedClickUrl);
            final List<CreativeImageBo> imageBos = Optional.ofNullable(imageDtos)
                    .orElseGet(Collections::emptyList)
                    .stream()
                    .map(CreativeImageConverter.MAPPER::imageDto2ImageBo)
                    .collect(Collectors.toList());
            final List<CreativeComponentBo> componentBos = Optional.ofNullable(creative.getComponents())
                    .orElseGet(Collections::emptyList);
            TemplatePageDto mgkTemplatePage = null;
            final int creativeId;

            // 创意主表
            if (isNewCreative) {
                creativeId = adCoreBqf.insert(lauUnitCreative).insertGetKey(creativePo);
            } else {
                creativeId = oldCreative.getCreativeId();
                // 影子创意的逻辑下，这些表再保存创意的时候都不能动，永远保持这些表里的数据为审核通过的状态，延迟到审核通过的时候再来更改
                adCoreBqf.update(lauUnitCreative).updateBean(creativePo);
            }

            com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauCreativeExtraPo lauCreativeExtraPo = new com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauCreativeExtraPo();
            lauCreativeExtraPo.setSeasonAvid(0L);
            lauCreativeExtraPo.setSeasonId(0L);
            log.info("seasonMap={}", JSONObject.toJSONString(seasonMap));
            if (!CollectionUtils.isEmpty(seasonMap) && seasonMap.get(creative.getPromotionPurposeContent()) != null){
                lauCreativeExtraPo = seasonMap.get(creative.getPromotionPurposeContent());
            }
            lauCreativeExtraPo.setCreativeId(creativeId);
            lauCreativeExtraPo.setCampaignId(creativePo.getCampaignId());
            lauCreativeExtraPo.setUnitId(creativePo.getUnitId());
            lauCreativeExtraPo.setAccountId(creativePo.getAccountId());
            creativeExtraService.save(creativeId, lauCreativeExtraPo);

            //原生落地页bo 对应lau_creative_tab表 jump_type、jump_url
            CreativeTabBo tabBo = CreativeTabBo.getDefaultInstance();
            //联投落地页bo 对应lau_creative_landing_page container_page_id、container_url、container_secondary_url
            CreativeLandingPageBo landingPage = CreativeLandingPageBo.getDefaultInstance();
            //todo 目前这段逻辑一定需要 creative id. 用于构造原生落地页的宏参数 后续不需要creative id的时候 这段逻辑可以提前，不需要再根据creative id update
            String formattedJumpUrl;
            String formattedJumpUrlSecondary = StringUtils.EMPTY;
            String formattedNativeJumpUrl = StringUtils.EMPTY;
            String trackadf;
            final boolean isSupportSeller = Objects.nonNull(account.getIsSupportSeller()) && Functions.integer2Boolean(account.getIsSupportSeller());
            final boolean isInner = Objects.nonNull(account.getIsSupportSeller()) && Functions.integer2Boolean(account.getIsInner());
            final boolean isSubPkg = Objects.nonNull(unit.getSubPkg()) && Functions.integer2Boolean(unit.getSubPkg() > 1 ? 0 : unit.getSubPkg());
            final List<Integer> templateIds = CollectionUtils.isEmpty(templateGroup.getTemplateIdList()) ? Collections.emptyList() : templateGroup.getTemplateIdList();
            final AddMacroBo addMacroBo = AddMacroBo.builder()
                    .adpVersion(unit.getAdpVersion())
                    .promotionPurposeType(promotionPurposeType)
                    .salesType(salesType)
                    .isSupportSeller(isSupportSeller)
                    .isInner(isInner)
                    .advertisingMode(creative.getAdvertisingMode())
                    .templateGroupId(templateGroup.getId())
                    .templateIds(templateIds)
                    .isSubPkg(isSubPkg)
                    .materialId(materialId)
                    .pkgType(unit.getSubPkg())
                    .build();

            // 内容投放
            if (AdvertisingMode.nativeContentMode(creative.getAdvertisingMode())) {
                boolean containsWildcard = Objects.nonNull(creative.getWildcardDesc()) && Stream.of(creative.getWildcardDesc().getArea(),
                        creative.getWildcardDesc().getGender(),
                        creative.getWildcardDesc().getDevice(),
                        creative.getWildcardDesc().getAge())
                        .anyMatch(StringUtils::isNotBlank);
                formattedJumpUrl = launchJumpUrlService.generateNativeJumpUrl(creative.getBilibiliVideoBo().getAvId(), creativeId, true, containsWildcard, Utils.isPositive(creative.getPageGroupId()));
                //降级连接保存在tab中就行
                //H5使用建站连接
                String nativeJumpUrl = creative.getPromotionPurposeContent();
                //如果是小程序 则需要用小程序降级连接
                if (MgkLandingPageParserUtils.isMiniApp(creative.getPromotionPurposeContent())) {
                    nativeJumpUrl = creative.getPromotionPurposeContentSecondary();
                }
                formattedNativeJumpUrl = launchJumpUrlService.getFormattedJumpUrl(nativeJumpUrl, addMacroBo);
                trackadf = creativeTrackadfProc.spliceCreateTrackadfIfNecessary(creativeId, account, formattedNativeJumpUrl);
                tabBo.setJumpType(nativeLandingPageJumpType);
                tabBo.setJumpUrl(formattedNativeJumpUrl);
            } else {
                formattedJumpUrl = launchJumpUrlService.getFormattedJumpUrl(creative.getPromotionPurposeContent(), addMacroBo);
                formattedJumpUrlSecondary = launchJumpUrlService.getFormattedJumpUrl(creative.getPromotionPurposeContentSecondary(), addMacroBo);
                trackadf = creativeTrackadfProc.spliceCreateTrackadfIfNecessary(creativeId, account, formattedJumpUrl);
            }
            // 创意联投需要的信息
            final boolean needSaveLandingPageExtraInfo = Objects.equals(PromotionPurposeType.LANDING_PAGE.getCode(), promotionPurposeType)
                    || Objects.equals(PromotionPurposeType.ON_SHELF_GAME.getCode(), promotionPurposeType)
                    || Objects.equals(PromotionPurposeType.APP_DOWNLOAD.getCode(), promotionPurposeType)
                    || Objects.equals(PromotionPurposeType.SALE_GOODS.getCode(), promotionPurposeType);
            // 创意联投落地页组
            int canJointLaunchPageGroup = 0;

            if (needSaveLandingPageExtraInfo) {
                final boolean supportContainer = !AdvertisingMode.nativeContentMode(creative.getAdvertisingMode()) && launchCreativeLandingPageService.supportContainerLandingPage(account.getAccountId());
                final boolean supportH5 = launchCreativeLandingPageService.supportContainerLandingPageH5(account.getAccountId());
                mgkTemplatePage = launchCreativeLandingPageService.getMgkTemplatePage(operator, supportContainer, supportH5, promotionPurposeType, creative.getMgkPageId(), unit.getGameBaseId(), unit.getAppPackageId(), formattedJumpUrl, creative.getPageGroupId());
                if (Objects.nonNull(mgkTemplatePage)) {
                    landingPage.setContainerPageId(mgkTemplatePage.getMgkPageId());
                    landingPage.setContainerUrl(mgkTemplatePage.getLaunchUrl());
                    landingPage.setContainerSecondaryUrl(mgkTemplatePage.getLaunchUrlSecondary());
                }
                Integer groupSource = Utils.isPositive(creative.getPageGroupId())
                        && Objects.nonNull(launchPageGroupBoMap.get(creative.getPageGroupId())) ?
                        launchPageGroupBoMap.get(creative.getPageGroupId()).getGroupSource() : null;
                canJointLaunchPageGroup =
                        launchCreativeLandingPageService.judgeNeedSaveJointLaunchPageGroup(supportContainer, supportH5, creativePo.getMgkPageId(), unit.getGameBaseId(), unit.getAppPackageId(), creative.getPageGroupId(), groupSource);
            }

            //不需要保存影子创意时 直接保存创意相关信息
            //需要保存影子创意时 则保存至lau_shadow_creative表 json形式
            if (!needSaveShadowCreative) {
                //保存落地页信息(lau_unit_creative)
                launchJumpUrlService.saveCreativePromotionPurposeContentAndTrackadf(creativeId, formattedJumpUrl, formattedJumpUrlSecondary, trackadf);
                //保存按钮(lau_creative_button)
                final List<com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauCreativeButtonCopyPo> buttons = Stream.of(buttonBo).filter(Objects::nonNull).map(button -> CreativeButtonConverter.MAPPER.bo2Po(unitId, creativeId, button)).collect(Collectors.toList());
                launchCreativeButtonService.saveByCreativeId(creativeId, buttons);
                //保存图片(lau_creative_imagve)
                final List<com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauCreativeImagePo> images = imageBos.stream().filter(Objects::nonNull).map(image -> CreativeImageConverter.MAPPER.bo2Po(unitId, creativeId, image)).collect(Collectors.toList());
                launchCreativeImageService.saveByCreativeId(creativeId, images);
                //保存组件(lau_creative_component)
                final List<LauCreativeComponentPo> components = componentBos.stream().filter(Objects::nonNull).map(component -> CreativeComponentConverter.MAPPER.bo2Po(accountId, campaignId, unitId, creativeId, component)).collect(Collectors.toList());
                launchCreativeComponentService.saveByCreativeId(creativeId, components);
                //保存原生落地页信息(lau_creative_tab)
                if (AdvertisingMode.nativeContentMode(creative.getAdvertisingMode())) {
                    final List<LauCreativeTabPo> tabs = Stream.of(tabBo).filter(Objects::nonNull).map(tab -> CreativeTabConverter.MAPPER.bo2Po(accountId, unitId, (long) creativeId, tab)).collect(Collectors.toList());
                    launchCreativeTabService.saveByCreativeId(creativeId, tabs);
                }
                //保存创意联投信息(lau_creative_landing_page)
                if (needSaveLandingPageExtraInfo) {
                    launchCreativeLandingPageService.saveLandingPageExtraInfo(accountId, campaignId, unitId, creativeId, mgkTemplatePage);
                }
                LaunchPageGroupCreativeBindBo bindBo = LaunchPageGroupCreativeBindBo.builder()
                        .creativeId(creativeId)
                        .accountId(accountId)
                        .groupId(creative.getPageGroupId())
                        .canJointLaunch(canJointLaunchPageGroup)
                        .build();
                launchMgkPageGroupBindService.saveCreativePageGroup(bindBo);
            }
            else {
                int creativeStatus = creative.getMgkPageStatus() == MgkPageStatus.WAIT_AUDIT_VALUE ?
                        com.bilibili.adp.cpc.core.constants.CreativeStatus.LANDING_PAGE_AUDITING :
                        com.bilibili.adp.cpc.core.constants.CreativeStatus.AUDITING;
                shadowCreativeBuilder.accountId(accountId);
                shadowCreativeBuilder.campaignId(campaignId);
                shadowCreativeBuilder.unitId(unitId);
                shadowCreativeBuilder.creativeId(creativeId);
                shadowCreativeBuilder.promotionPurposeType(promotionPurposeType);
                shadowCreativeBuilder.advertisingMode(creative.getAdvertisingMode());
                shadowCreativeBuilder.jumpType(landingPageJumpType);
                shadowCreativeBuilder.jumpUrl(formattedJumpUrl);
                shadowCreativeBuilder.jumpUrlSecondary(formattedJumpUrlSecondary);
                shadowCreativeBuilder.title(creative.getTitle());
                shadowCreativeBuilder.description(creative.getDescription());
                shadowCreativeBuilder.extDescription(creative.getExtDescription());
                shadowCreativeBuilder.mgkPageId(mgkPageId);
                shadowCreativeBuilder.videoId(videoId);
                shadowCreativeBuilder.cmMark(creative.getCmMark());
                shadowCreativeBuilder.busMark(creative.getBusMarkId());
                shadowCreativeBuilder.button(buttonBo);
                shadowCreativeBuilder.images(imageBos);
                shadowCreativeBuilder.components(componentBos);
                shadowCreativeBuilder.tab(tabBo);
                shadowCreativeBuilder.landingPage(landingPage);
                shadowCreativeBuilder.flyExtInfo(null);
                shadowCreativeBuilder.trackadf(trackadf);
                shadowCreativeBuilder.auditStatus(com.bilibili.adp.cpc.core.constants.AuditStatus.AUDITING);
                shadowCreativeBuilder.creativeStatus(creativeStatus);
                shadowCreativeBuilder.pageGroupId(creative.getPageGroupId());
                shadowCreativeBuilder.isPageGroup(creative.getIsPageGroup());
                shadowCreativeBuilder.canJointLaunchPageGroup(canJointLaunchPageGroup);
                ShadowCreativeBo shadowCreativeBo = shadowCreativeBuilder.build();
                String shadowCreative = objectMapper.writeValueAsString(shadowCreativeBo);
                //保存影子创意
                final List<LauShadowCreativePo> shadowCreatives = Stream.of(shadowCreativeBo).filter(Objects::nonNull).map(bo -> ShadowCreativeConverter.MAPPER.bo2Po(bo, shadowCreative)).collect(Collectors.toList());
                launchShadowCreativeService.saveByCreativeId(creativeId, shadowCreatives);
            }

            LauUnitCreativePo logPptPo = lauUnitCreativeDao.selectByPrimaryKey(creativeId);
            log.info("ppt:{}",logPptPo.getPromotionPurposeContent());
            //endregion
            // 创建创意待审核记录
            if (Objects.equals(AuditStatus.INIT.getCode(), creativePo.getAuditStatus())) {
                creativeServiceDelegate.addCreativeAuditStat(creativePo.getCreativeId(), com.bilibili.adp.common.enums.CreativeAuditEvent.AUDIT, operatorName);
            }
            // 创意落地页分享
            saveCreativeShare(creative, creativeId);
            // 修改创意的情况保存创意自动更新记录
            saveCreativeAuto(creative, creativeId);
            if (Boolean.TRUE.equals(creative.getTemplate().getIsDynamicArea())) {
                // 动态模板，注册动态
                saveRegisterCreativeDynamic(creativeId, creative, oldCreative, unit);
            }

            // 直播间，原生推审，默认通过
            if (Objects.equals(promotionPurposeType, PromotionPurposeType.LIVE_ROOM.getCode())  && !Utils.isPositive(creative.getBilibiliUserId())) {
                nativeService.saveNativeDataIfNecessary(creativeNativeArchiveRelativityPoMap, creative, true, creativeId, false, operator, null, unit);
            }

            // 弹幕组件
            adpCpcSelfDanmakuGroupMappingService.saveCreativeDanmakuGroupMapping(creativeId, creative.getDanmakuGroupId(), accountId);
            // 创意视频映射
            saveCreativeIdBizIdMapping(creativeId, creative.getJumpType(), creative.getMgkPageId());

            // 保存创意 extra 信息
            Integer ppcType = lauCreativeExtraService.generatePpcType(formattedJumpUrl, unit.getPromotionPurposeType(), unit.getBusinessDomain(), creative.getVideoId(), creative.getAdvertisingMode());
            LauCreativeExtraPo creativeExtraPo = LauCreativeExtraPo.builder()
                    .creativeId(creativeId)
                    .accountId(creative.getAccountId())
                    .campaignId(creative.getCampaignId())
                    .unitId(creative.getUnitId())
                    .qualificationPackageId(creative.getQualificationPackageId())
                    .imageMacroType(creative.getImageMacroType())
                    .imageMacro(creative.getImageMacroPlaceHolder())
                    .ppcType(ppcType)
                    .build();
            lauCreativeExtraProc.saveCreativeExtra(creativeExtraPo, creativeId);

            // 删除创意视频
            if (oldCreative != null && !Utils.isPositive(creative.getMgkVideoId())) {
                deleteCreativeVideo(oldCreative.getCreativeId());
            }
            // 保存创意视频云视频
            saveCreativeVideo(creativeId, creative.getMgkVideoId());
            saveCreativeMonitoring(creativeId, creative);

            // 保存微信小游戏绑定关系
            if (AdpVersion.isMiddle(creative.getAdpVersion()) && !lauCreativeMiniGameMappingService.checkMappingIsEqual(creativeId, creative.getMiniGameId(), creative.getMiniGameUrl())) {
                LauCreativeMiniGameMappingBo saveBo = LauCreativeMiniGameMappingBo.builder()
                        .accountId(operator.getOperatorId())
                        .creativeId(creativeId)
                        .miniGameId(creative.getMiniGameId())
                        .gameUrl(creative.getMiniGameUrl())
                        .build();
                lauCreativeMiniGameMappingService.saveCreativeMiniGameMapping(saveBo);
            }
            //insert layout
            //saveCreativeLayout(creative, templateGroup, creativeId, formattedJumpUrl);
            //创意分类
            saveCreativeBusinessCategory(creativeId, creative.getBuFirstCategoryId(), creative.getBuSecondCategoryId(), creative.getBuThirdCategoryId());
            // 处理加急审核情况
            processUrgentAudit(unit.getAccountId(), creative, oldCreative, creativePo.getAuditStatus(), creativeId);
            // 保存创意模板关系
            final Boolean noBusMark = accountLabelService.isAccountIdInLabel(accountId, creativePositionConfig.getNoBusMarkLabelId());
            saveSlotGroupTemplateMapping(unit, creative, templateGroupMap.get(creative.getTemplateGroupId()), templateMap, slotGroupTemplateMappingDtos, availableTriplets, creativeId, noBusMark, hiddenContextBo);
            // 保存创意场景
            saveCreativeScenes(creative, creativeId);
            //专业起飞复制，自动审核(通过&驳回)
            if (isNewCreative && unit.getIsNewFly() == 1 && creative.isFlyCopy()) {
                creativeCommonService.autoAuditCopyCreative(unit.getUnitId(), creativePo.getCreativeId(), creative.getSourceCreativeIdAuditStatus());
                //暂停创意
                if (ObjectUtils.nullSafeEquals(creative.getSourceCreativeIdCreativeStatus(), CreativeStatus.PAUSED.getCode())) {
                    LauUnitCreativePoExample example = new LauUnitCreativePoExample();
                    example.or()
                            .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                            .andAccountIdEqualTo(account.getAccountId())
                            .andCreativeIdEqualTo(creativePo.getCreativeId());
                    LauUnitCreativePo po = new LauUnitCreativePo();
                    po.setStatus(LaunchStatus.STOP.getCode());
                    po.setCreativeStatus(CreativeStatus.PAUSED.getCode());
                    lauUnitCreativeDao.updateByExampleSelective(po, example);
                }
                //再查出来,写日志
                LauUnitCreativePo po = lauUnitCreativeDao.selectByPrimaryKey(creativePo.getCreativeId());
                creative.setAuditStatus(po.getAuditStatus());
                creative.setCreativeStatus(po.getCreativeStatus());
                creative.setStatus(po.getStatus());
            } else {
                //专业起飞，新建直播间创意，自动审核
                if (isNewCreative && unit.getIsNewFly() == 1 && promotionPurposeType == PromotionPurposeType.LIVE_ROOM.getCode()) {
                    creativeCommonService.autoAuditLiveRoomCreative(unit.getUnitId(), creativePo.getCreativeId());
                }
            }
            //动态保存lau_creative_fly_dynamic_info
            if (ObjectUtils.nullSafeEquals(DynamicTypeEnum.CHOOSE_DYNAMIC.getCode(), creative.getDynamicType())) {
                this.saveDynamicInfo(isNewCreative, account.getAccountId(), unitId, creativeId,
                        creative.getDynamicId(), creative.getLikeCount(), creative.getNickName());
            }
            savedCreativeIds.add(creativeId);
        }

        List<Long> dynamicIds = creativeList.stream().map(t -> t.getDynamicId()).filter(Objects::nonNull).filter(Utils::isPositive).distinct().collect(Collectors.toList());
        lauDynamicService.processDynamics(dynamicIds);
        // 删除未使用的创意模板(估计有bug在兜底)
        deleteUnUsedCreativeTemplate(unitId, savedCreativeIds);

        log.info("Return creativeIds {}", savedCreativeIds);
        return savedCreativeIds;
    }

    /**
     * 处理视频物料
     *
     * @param creative
     * @param creativePo
     */
    private void dealBilibiliVideo(CpcCreativeDto creative, LauUnitCreativePo creativePo) {
        // 视频物料保存 lau_material_bilibili_video
        biliVideoService.save(creative.getBilibiliVideoBo(), creative.getAccountId());

        // 保存企业号稿件id, 暂时双写, 后面再去掉
        creativePo.setMaterialVideoId(creative.getBilibiliVideoBo().getAvId());
        creativePo.setVideoId(creative.getBilibiliVideoBo().getAvId());

        // 如果前端没传封面, 就写进去, 防止引擎拿不到封面
        if (CollectionUtils.isEmpty(creative.getImageDtos())) {

            // 获取稿件的封面
            final ImageDto image = ImageDto.builder()
                    .url(creative.getBilibiliVideoBo().getCoverUrl())
                    .md5(creative.getBilibiliVideoBo().getCoverMd5())
                    .build();
            creative.setImageDtos(Collections.singletonList(image));
        }
    }

    public String buildNativeContentJumpUrl(boolean newNative, boolean containsWildcard, int newCreativeId, String avid) {
        String archiveLink = Constants.VIDEO_PREFIX + avid;
        UriComponentsBuilder uriComponentsBuilder = UriComponentsBuilder.fromUriString(archiveLink)
                .queryParam(source_id, __SOURCEID__)
                .queryParam(resource_id, __RESOURCEID__)
                .queryParam(creative_id, __CREATIVEID__)
                .queryParam(linked_creative_id, newCreativeId)
//                .queryParam(track_id, __TRACKID__)
                .queryParam(FROM_SPMID, __FROMSPMID__)
                .queryParam(trackid, __FROMTRACKID__)
                .queryParam(request_id, __REQUESTID__)
                .queryParam(caid, __CAID__)
                .queryParam(biz_extra, BIZ_EXTRA);
        if (!newNative) {
            uriComponentsBuilder.queryParam(extension_tab_on, EXTENSION_TAB_ON);
        }
        if (containsWildcard) {
            uriComponentsBuilder.queryParam(title_encode, __TITLE_ENCODE__);

        }
        return uriComponentsBuilder.build().toString();
    }

    private void saveFormatedJumpUrl(int newCreativeId, String formatedJumpUrl, String formatedUrlSecondary) {
        log.info("saveFormatedJumpUrl, creativeId:{}, formatedJumpUrl:{}, formatedUrlSecondary:{}", newCreativeId,
                formatedJumpUrl, formatedUrlSecondary);

        LauUnitCreativePo po = new LauUnitCreativePo();
        po.setCreativeId(newCreativeId);
        po.setPromotionPurposeContent(formatedJumpUrl);
        po.setPromotionPurposeContentSecondary(formatedUrlSecondary);
        lauUnitCreativeDao.updateByPrimaryKeySelective(po);
    }

    /**
     * @param newCreativeId 创意id
     * @param tabEunm       tab名称
     * @param jumpUrl       tab跳转连接
     * @description 原生内容投放保存tab相关信息
     * <AUTHOR>
     * @date 2021-09-27 9:39 下午
     */
    private void saveCreativeTab(int accountId, int unitId, int newCreativeId, TabEnum tabEunm, int jumpType, String jumpUrl) {
        log.info("saveCreativeTab, creativeId:{}, tabEunm:{}", newCreativeId, JSON.toJSONString(tabEunm));
        Assert.isTrue(!IOS_PATTERN.matcher(jumpUrl).matches(), "请填写H5链接，不支持手工填写applestore地址");
        LauCreativeTabPo oldTab = adCoreBqf
                .selectFrom(lauCreativeTab)
                .where(lauCreativeTab.creativeId.eq((long) newCreativeId))
                .fetchFirst();
        LauCreativeTabPo tab = new LauCreativeTabPo();
        tab.setAccountId(accountId);
        tab.setUnitId(unitId);
        tab.setCreativeId((long) newCreativeId);
        tab.setTabId(tabEunm.getTabId());
        tab.setTabName(tabEunm.getTabName());
        tab.setIndustryId(tabEunm.getIndustryId());
        tab.setIndustryName(tabEunm.getIndustryName());
        tab.setJumpType(jumpType);
        tab.setJumpUrl(jumpUrl);
        if (Objects.isNull(oldTab)) {
            adCoreBqf.insert(lauCreativeTab).insertBean(tab);
        } else {
            tab.setId(oldTab.getId());
            adCoreBqf.update(lauCreativeTab).updateBean(tab);
        }

        AdpCatUtils.logEventAndLog("saveCreativeTab", "saveCreativeTab", "unitId=%s,newCreativeId=%s", unitId,
                newCreativeId);
    }

    /**
     * 创意视频映射 lau_biz_creative_mapping
     *
     * @param newCreativeId
     * @param jumpType
     * @param mgkPageId
     */
    public void saveCreativeIdBizIdMapping(int newCreativeId, Integer jumpType, Long mgkPageId) {
        log.info("saveCreativeIdBizIdMapping newCreativeId:{}, jumpType:{}, mgkPageId:{}", newCreativeId, jumpType
                , mgkPageId);
        if (!JumpTypeEnum.MGK_PAGE_ID.getCode().equals(jumpType)) {
            return;
        }
        // 根据page_id获取biz_id
        Integer bizId = videoLibraryService.getBizIdByPageId(mgkPageId);

        if (!Utils.isPositive(bizId)) {
            return;
        }
        // 新建 biz_id 和 creative_id 映射(lau_biz_creative_mapping)
        insertOrUpdateBizCreativeMapping(newCreativeId, bizId);
    }

    private void insertOrUpdateBizCreativeMapping(int newCreativeId, Integer bizId) {
        List<BizCreativeMappingDto> pos = lauBizCreativeMappingService.getBizCreativeMappingDtosByCreativeIds(Lists.newArrayList(newCreativeId));
        BizCreativeMappingDto dto = BizCreativeMappingDto.builder().bizId(bizId).creativeId(newCreativeId).build();
        if (CollectionUtils.isEmpty(pos)) {
            // 插入
            lauBizCreativeMappingService.insert(dto);
            return;
        }
        if (!bizId.equals(pos.get(0).getBizId())) {
            // 更新
            dto.setId(pos.get(0).getId());
            lauBizCreativeMappingService.updateById(dto);
        }
    }

    public void saveDynamicInfo(Boolean isInsert, Integer accountId, Integer unitId, Integer creativeId,
                                Long dynamicId, String likeCount, String nickName) {
        // 获取单元层级动态挂的直播预约 sid，同步到创意层级
        LauUnitFlyMiddleInfoPo flyMiddleInfoPo = lauUnitFlyMiddleInfoRepo.fetchByDynamicId(dynamicId);
        Long sid = 0L;
        if (flyMiddleInfoPo != null) {
            sid = flyMiddleInfoPo.getSid();
        }
        log.info("saveDynamicInfo, isInsert:{}, unitId:{}, creativeId:{}, dynamicId:{}, sid:{}", isInsert, unitId,
                creativeId, dynamicId, sid);
        if (isInsert) {
            newLauCreativeFlyDynamicInfoDao.insertSelective(LauCreativeFlyDynamicInfoPo.builder()
                    .accountId(accountId)
                    .creativeId(creativeId)
                    .unitId(unitId)
                    .dynamicId(dynamicId)
                    .likeCount(likeCount)
                    .nickname(nickName)
                    .sid(sid)
                    .build());
        } else {
            // 同步动态 sid
            LauCreativeFlyDynamicInfoPoExample example = new LauCreativeFlyDynamicInfoPoExample();
            example.or().andCreativeIdEqualTo(creativeId).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
            List<LauCreativeFlyDynamicInfoPo> pos = newLauCreativeFlyDynamicInfoDao.selectByExample(example);
            Assert.isTrue(!CollectionUtils.isEmpty(pos) && pos.size() == 1, "动态表错误");
            LauCreativeFlyDynamicInfoPo po = pos.get(0);
            po.setLikeCount(likeCount);
            po.setSid(sid);
            po.setNickname(nickName);
            po.setMtime(Utils.getNow());
            newLauCreativeFlyDynamicInfoDao.updateByPrimaryKeySelective(po);
        }
    }

    /**
     * com.bilibili.adp.cpc.biz.services.creative.CpcSaveCreativeService#saveDynamicInfo(java.lang.Boolean, java.lang.Integer, java.lang.Integer, java.lang.Integer, java.lang.Long, java.lang.String, java.lang.String) 复制而来，
     * 新增起飞动态负反馈需要动态up mid
     * 原方法调用地方太多
     * @param isInsert
     * @param accountId
     * @param unitId
     * @param creativeId
     * @param dynamicId
     * @param dynamicUpMid
     * @param likeCount
     * @param nickname
     */
    public void saveDynamicInfo(Boolean isInsert, Integer accountId, Integer unitId, Integer creativeId, Long dynamicId, Long dynamicUpMid, String likeCount, String nickname) {
        // 获取单元层级动态挂的直播预约 sid，同步到创意层级
        LauUnitFlyMiddleInfoPo flyMiddleInfoPo = lauUnitFlyMiddleInfoRepo.fetchByDynamicId(dynamicId);
        Long sid = 0L;
        if (flyMiddleInfoPo != null) {
            sid = flyMiddleInfoPo.getSid();
        }
        log.info("saveDynamicInfo, isInsert:{}, unitId:{}, creativeId:{}, dynamicId:{}, sid:{}", isInsert, unitId, creativeId, dynamicId, sid);
        if (isInsert) {
            com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauCreativeFlyDynamicInfoPo po = CreativeFlyDynamicInfoConverter.MAPPER.toPo(accountId, unitId, creativeId, dynamicId, dynamicUpMid, likeCount, nickname, sid);
            launchCreativeFlyDynamicInfoService.save(po);
        } else {
            // 同步动态 sid
            com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauCreativeFlyDynamicInfoPo lauCreativeFlyDynamicInfoPo = launchCreativeFlyDynamicInfoService.get(creativeId);
            Assert.notNull(lauCreativeFlyDynamicInfoPo, "动态表错误");
            lauCreativeFlyDynamicInfoPo.setLikeCount(likeCount);
            lauCreativeFlyDynamicInfoPo.setSid(sid);
            lauCreativeFlyDynamicInfoPo.setNickname(nickname);
            lauCreativeFlyDynamicInfoPo.setMtime(Utils.getNow());
            launchCreativeFlyDynamicInfoService.update(lauCreativeFlyDynamicInfoPo);
        }
    }
    /**
     * 保存创意图片(lau_creative_image)
     *
     * @param unitId
     * @param creativeId
     * @param newBos
     * @param existingBos
     */
    public void saveCreativeImages(Integer unitId, Integer creativeId, Collection<ImageDto> newBos, Collection<ImageDto> existingBos) {
        AdpCatUtils.logEventAndLog("saveCreativeImages", "saveCreativeImages start", "creativeId=%s", creativeId);

        final InsertDeleteResult<ImageDto> result = QueryDslFunctions.getInsertDeleteResult(newBos, existingBos);

        // 存在需要删除的，软删
        if (!CollectionUtils.isEmpty(result.getDeleteData())) {
            final List<Integer> ids = result.getDeleteData()
                    .stream()
                    .map(ImageDto::getId)
                    .collect(Collectors.toList());
            adCoreBqf.update(lauCreativeImage)
                    .where(lauCreativeImage.id.in(ids))
                    .set(lauCreativeImage.isDeleted, IsDeleted.DELETED.getCode())
                    .execute();
        }


        // 存在新增的，则新增
        if (!CollectionUtils.isEmpty(result.getInsertData())) {
            final List<LauCreativeImagePo> creativeImages = result.getInsertData()
                    .stream()
                    .map(x -> {
                        final LauCreativeImagePo po = new LauCreativeImagePo();
                        po.setCreativeId(creativeId);
                        po.setImageMd5(x.getMd5());
                        po.setImageUrl(x.getUrl());
                        po.setUnitId(unitId);
                        if (Objects.isNull(x.getMgkMediaId())) {
                            po.setMgkMediaId(0L);
                        } else {
                            po.setMgkMediaId(x.getMgkMediaId());
                        }
                        if (Objects.isNull(x.getMgkTemplateId())) {
                            po.setMgkTemplateId(0);
                        } else {
                            po.setMgkTemplateId(x.getMgkTemplateId());
                        }
                        return po;
                    }).collect(Collectors.toList());
            adCoreBqf.insert(lauCreativeImage).insertBeans(creativeImages);
        }
    }

    public void batchSaveCreativeImage(int unitId, int creativeId, Collection<ImageDto> images) {
        final List<LauCreativeImagePo> pos = images.stream()
                .map(x -> {
                    final LauCreativeImagePo po = new LauCreativeImagePo();
                    po.setId(x.getId());
                    po.setCreativeId(creativeId);
                    po.setImageMd5(x.getMd5());
                    po.setImageUrl(x.getUrl());
                    po.setUnitId(unitId);
                    if (Objects.isNull(x.getMgkMediaId())) {
                        po.setMgkMediaId(0L);
                    } else {
                        po.setMgkMediaId(x.getMgkMediaId());
                    }
                    if (Objects.isNull(x.getMgkTemplateId())) {
                        po.setMgkTemplateId(0);
                    } else {
                        po.setMgkTemplateId(x.getMgkTemplateId());
                    }
                    return po;
                }).collect(Collectors.toList());
        adCoreBqf.insertMysql(lauCreativeImage).insertDuplicateUpdates(pos);
    }

    /**
     * 保存创意场景列表
     *
     * @param creative
     * @param newCreativeId
     */
    public void saveCreativeScenes(CpcCreativeDto creative, Integer newCreativeId) {
        AdpCatUtils.logEventAndLog("saveCreativeScenes", "saveCreativeScenes", "scenes=%s,newCreativeId=%s",
                JSON.toJSONString(creative.getScenes()), newCreativeId);

        // 是否合并版本(1) || 是否视频合并版本(3) 或 中台(5)
        if (AdpVersion.isMergedInGeneral(creative.getAdpVersion())) {
            // 软删老的创意场景
            adBqf.update(lauCreativeScene)
                    .set(lauCreativeScene.isDeleted, IsDeleted.DELETED.getCode())
                    .where(lauCreativeScene.creativeId.eq(newCreativeId.longValue()))
                    .execute();

            // 没有新场景
            if (CollectionUtils.isEmpty(creative.getScenes())) {
                return;
            }

            // 插入新的创意场景列表
            adBqf.insertMysql(lauCreativeScene).insertDuplicateUpdates(
                    creative.getScenes().stream().map(scene -> {
                        LauCreativeSceneDo lauCreativeSceneDo = new LauCreativeSceneDo();
                        lauCreativeSceneDo.setCreativeId(newCreativeId.longValue());
                        lauCreativeSceneDo.setAccountId(creative.getAccountId());
                        lauCreativeSceneDo.setUnitId(creative.getUnitId());
                        lauCreativeSceneDo.setScene(scene);
                        lauCreativeSceneDo.setIsDeleted(IsDeleted.VALID.getCode());
                        return lauCreativeSceneDo;
                    }).collect(Collectors.toList()));
        }
    }

    /**
     * 删除未使用的创意模板
     *
     * @param unitId
     * @param creativeIds 这批保存成功的创意 ids
     */
    public void deleteUnUsedCreativeTemplate(int unitId, Collection<Integer> creativeIds) {
        log.info("deleteUnUsedCreativeTemplate, unitId:{}, creativeIds:{}", unitId, JSON.toJSONString(creativeIds));
        final long countByUnitId = adCoreBqf.from(lauCreativeTemplate)
                .where(lauCreativeTemplate.unitId.eq(unitId))
                .fetchCount();
        if (countByUnitId == 0) return;

        // 如果没有创意ids，删除单元下所有创意模板
        if (CollectionUtils.isEmpty(creativeIds)) {
            adCoreBqf.delete(lauCreativeTemplate)
                    .where(lauCreativeTemplate.unitId.eq(unitId))
                    .execute();
            return;
        }

        final List<Long> longCreativeIds = creativeIds.stream()
                .map(x -> (long) x)
                .collect(Collectors.toList());

        // 删除已经存在的创意模板中不在 creativeIds 范围内的创意模板(估计上面保存创意模板有bug，这里做了兜底)
        long count = adCoreBqf.delete(lauCreativeTemplate)
                .where(lauCreativeTemplate.unitId.eq(unitId)
                        .and(lauCreativeTemplate.creativeId.notIn(longCreativeIds)))
                .execute();
        AdpCatUtils.logEventAndLog("删除未使用的创意模板", "deleteUnUsedCreativeTemplate", "unitId=%s,count=%s", unitId, count);
    }

    /**
     * 保存创意模板列表
     *
     * @param unit
     * @param creative
     * @param templateGroupBo
     * @param templateMap
     * @param slotGroupTemplateMappingDtos
     * @param availableTriplets
     * @param newCreativeId
     */
    private void saveSlotGroupTemplateMapping(
            CpcUnitDto unit,
            CpcCreativeDto creative,
            TemplateGroupBo templateGroupBo,
            Map<Integer, TemplateDto> templateMap,
            List<ResSlotGroupTemplateMappingDto> slotGroupTemplateMappingDtos,
            List<LauResourceStyleTripletWithSlotGroup> availableTriplets,
            Integer newCreativeId,
            Boolean noBusMark,
            HiddenContextBo hiddenContextBo) {
        AdpCatUtils.logEventAndLog("saveSlotGroupTemplateMapping", "saveSlotGroupTemplateMapping", "newCreativeId=%s",
                newCreativeId);

        // 非版位合并不写这个表
        if (AdpVersion.isLegacy(creative.getAdpVersion())) return;

        // 已存在的 LauCreativeTemplateBos
        final List<LauCreativeTemplateBo> existingBos = adCoreBqf.selectFrom(lauCreativeTemplate)
                .where(lauCreativeTemplate.creativeId.eq(newCreativeId.longValue()))
                .fetch(LauCreativeTemplateBo.class);

        final List<LauCreativeTemplateBo> newBos; // 需要新增的创意模板列表

        // 根据情况准备创意模板列表
        // 是否视频合并版本(3) 或 中台(5)
        if (AdpVersion.isVideoMerged(creative.getAdpVersion())) {
            // 视频版位合并的情况
            final HashSet<Integer> templateIdSet = new HashSet<>(templateGroupBo.getTemplateIdList());
            final boolean canAddStoryResource = Objects.nonNull(creative.getBilibiliVideoBo()) && Utils.isPositive(creative.getBilibiliVideoBo().getAvId());
            newBos = availableTriplets.stream()
                    // pcApp资源位仅表单提交优化目标支持，其他过滤对应slotGroupId
                    .filter(x -> OcpcTargetEnum.FORM_SUBMIT.getCode().equals(unit.getOcpcTarget()) ||
                            (!creativePositionConfig.getPcAppPcIndexSlotGroupId().equals(x.getLauResourceStyleTriplet().getSlotGroupId()) && !creativePositionConfig.getPcAppPcPlaySlotGroupId().equals(x.getLauResourceStyleTriplet().getSlotGroupId())))
                    .flatMap(x -> {
                        final Integer templateId = x.getLauResourceStyleTriplet().getTemplateId();
                        if (!templateIdSet.contains(templateId)) return Stream.empty();

                        final TemplateDto template = templateMap.get(templateId);
                        if (Objects.isNull(template)) return Stream.empty();

                        final LauCreativeTemplateBo lauCreativeTemplateBo = LauCreativeTemplateBo.builder()
                                .creativeId(newCreativeId.longValue())
                                .accountId(creative.getAccountId())
                                .unitId(creative.getUnitId())
                                .slotGroupId(x.getLauResourceStyleTriplet().getSlotGroupId())
                                .templateId(templateId)
                                .busMarkId(creative.getBusMarkId())
                                .creativeStyle(template.getFallBackCreativeStyle())
                                .build();

                        // story目前仅支持稿件
                        final boolean isStory = launchResourceService.isStory(x.getLauResourceStyleTriplet().getSlotGroupId());
                        if (isStory) {
                            // 无法投story的情况, 需要去掉这个数据
                            if (!canAddStoryResource) return Stream.empty();

                            // 可以投story的情况
                            if (noBusMark) {
                                lauCreativeTemplateBo.setBusMarkId(flyBusMarkConfig.getStoryNoBusMarkId());
                                return Stream.of(lauCreativeTemplateBo);
                            }

                            final List<BusMarkDto> busMarks = busMarkRuleService.getValidBusMarkList(unit.getAccountId(), BusMarkRuleAdSysEnum.CPC, Collections.singletonList(template.getCardType()));
                            if (CollectionUtils.isEmpty(busMarks)) {
                                // 没有找到可用的广告标, 使用story默认广告标
                                lauCreativeTemplateBo.setBusMarkId(launchResourceService.getFallBackStoryBusMarkId());
                                return Stream.of(lauCreativeTemplateBo);
                            }

                            for (BusMarkDto busMark : busMarks) {
                                if (launchResourceService.isStoryBusMarkId(busMark.getId())) {
                                    // 使用第一个找到的story广告标
                                    lauCreativeTemplateBo.setBusMarkId(busMark.getId());
                                    return Stream.of(lauCreativeTemplateBo);
                                }
                            }
                            // 如果还是没找到, 使用story默认广告标
                            lauCreativeTemplateBo.setBusMarkId(launchResourceService.getFallBackStoryBusMarkId());
                            return Stream.of(lauCreativeTemplateBo);
                        }
                        // 非story位置的情况
                        if (noBusMark) {
                            lauCreativeTemplateBo.setBusMarkId(flyBusMarkConfig.getNonStoryNoBusMarkId());
                        }
                        if (creativePositionConfig.getPcAppPcIndexSlotGroupId().equals(x.getLauResourceStyleTriplet().getSlotGroupId())) {
                            lauCreativeTemplateBo.setBusMarkId(flyBusMarkConfig.getPcAppIndexBusMarkId());
                        }
                        if (creativePositionConfig.getPcAppPcPlaySlotGroupId().equals(x.getLauResourceStyleTriplet().getSlotGroupId())) {
                            lauCreativeTemplateBo.setBusMarkId(flyBusMarkConfig.getPcAppPlayBusMarkId());
                        }
                        return Stream.of(lauCreativeTemplateBo);
                    }).collect(Collectors.toList());
            if (canAddStoryResource) {
                // 版位通投1期: https://www.tapd.cn/67874887/prong/stories/view/1167874887004352994
                Assert.isTrue(!CollectionUtils.isEmpty(newBos), "保存的模板不能为空");
                final LauCreativeTemplateBo sample = newBos.get(0);
                if (Objects.equals(54, creative.getTemplateGroupId())) {
                    final LauCreativeTemplateBo bo = HiddenTemplateConverter.MAPPER.copy(sample, 684, 565, 74, 7);
                    newBos.add(bo);
                } else if (Objects.equals(55, creative.getTemplateGroupId())) {
                    final LauCreativeTemplateBo bo = HiddenTemplateConverter.MAPPER.copy(sample, 685, 565, 74, 7);
                    newBos.add(bo);
                } else if (Objects.equals(76, creative.getTemplateGroupId())) {
                    final LauCreativeTemplateBo bo = HiddenTemplateConverter.MAPPER.copy(sample, 686, 565, 74, 7);
                    newBos.add(bo);
                } else if (Objects.equals(90, creative.getTemplateGroupId())) {
                    final LauCreativeTemplateBo bo = HiddenTemplateConverter.MAPPER.copy(sample, 687, 565, 74, 7);
                    newBos.add(bo);
                } else if (Objects.equals(91, creative.getTemplateGroupId())) {
                    final LauCreativeTemplateBo bo = HiddenTemplateConverter.MAPPER.copy(sample, 688, 565, 74, 7);
                    newBos.add(bo);
                } else if (Objects.equals(61, creative.getTemplateGroupId())) {
                    final LauCreativeTemplateBo bo = HiddenTemplateConverter.MAPPER.copy(sample, 689, 566, 54, 3);
                    newBos.add(bo);
                }
            }
        } else {
            final List<ResSlotGroupTemplateMappingDto> creativeSlotGroupTemplates = slotGroupTemplateMappingDtos.stream()
                    .filter(dto -> dto.getSlotGroup() != null)
                    .filter(dto -> {
                        if (Objects.equals(PreferScene.PREFER_SCENE.getCode(), creative.getPreferScene())) {
                            return Objects.equals(dto.getSlotGroup().getPreferScene(), PreferScene.PREFER_SCENE.getCode());
                        } else {
                            return CollectionHelper.contains(creative.getScenes(), dto.getSlotGroup().getScene());
                        }
                    }).filter(dto -> CollectionHelper.isNotEmpty(dto.getTemplates()))
                    .filter(dto -> CollectionHelper.contains(templateGroupBo.getTemplateIdList(), dto.getTemplates().get(0).getTemplateId()))
                    .collect(Collectors.toList());
            log.info("saveSlotGroupTemplateMapping:creativeSlotGroupTemplates.size={}", CollectionHelper.getSize(creativeSlotGroupTemplates));
            if (CollectionUtils.isEmpty(creativeSlotGroupTemplates)) {
                return;
            }
            newBos = creativeSlotGroupTemplates.stream()
                    .map(x -> LauCreativeTemplateBo.builder()
                            .creativeId(newCreativeId.longValue())
                            .accountId(creative.getAccountId())
                            .unitId(creative.getUnitId())
                            .slotGroupId(x.getSlotGroupId())
                            .templateId(x.getTemplates().get(0).getTemplateId())
                            .creativeStyle(creative.getCreativeStyle())
                            .busMarkId(creative.getBusMarkId())
                            .build())
                    .collect(Collectors.toList());
        }

        if (launchAccountGroupService.isAccountWithLabel(unit.getAccountId(), creativePositionConfig.getPublicServiceAdLabel())) {
            List<Integer> templateIds = newBos.stream().map(LauCreativeTemplateBo::getTemplateId).collect(Collectors.toList());
            List<LocTemplateCardTypeConfigBo> templateCardTypes = templateService.getTemplateCardTypes(templateIds);
            Map<Integer, Integer> tidCardTypeMap = templateCardTypes.stream().collect(Collectors.toMap(
                    LocTemplateCardTypeConfigBo::getTemplateId,
                    LocTemplateCardTypeConfigBo::getCardType,
                    (integer, integer2) -> integer2
            ));
            if (!CollectionUtils.isEmpty(tidCardTypeMap)){
                newBos.forEach(x -> {
                    Integer cardType = tidCardTypeMap.get(x.getTemplateId());
                    if (cardTypeStory.equals(cardType) && publicAdMark.equals(creative.getBusMarkId())){
                        x.setBusMarkId(publicAdMarkReplace);
                    }
                });
            }
        }


        newBos.forEach(x -> {
            // 是否特殊的动态排除模板
            if (launchResourceService.isSpecialDynamicExclusive(x.getTemplateId())) {
                // 动态有一个特殊逻辑
                x.setBusMarkId(launchResourceService.getSpecialDynamicBusMark());
            }

            // 如果是ocpc 三连单元的一阶段出价 不需要校验底价 随便改 大于0即可
            boolean cancelLowBid = SalesType.CPC.getCode() == unit.getSalesType()
                    && Utils.isPositive(unit.getOcpcTarget())
                    && BusinessDomain.CPC == unit.getBusinessDomain()
                    && AdpVersion.isMiddle(unit.getAdpVersion());
            final Integer reservedPrice = cancelLowBid ? 0 : Optional.ofNullable(cpcUnitService.getLowestCost(GetBidCostParam.builder()
                    .accountId(x.getAccountId())
                    .slotGroupId(x.getSlotGroupId())
                    .launchType(unit.getPromotionPurposeType())
                    .salesType(unit.getSalesType())
                    .build())).orElse(0);
            x.setReservedPrice(reservedPrice);

            if (unit.getIsNoBid() != null && Utils.isPositive(unit.getIsNoBid())) {
                x.setBizStatus(BIZ_STATUS_OK);
            } else {
                x.setBizStatus(unit.getCostPrice() < reservedPrice ? BIZ_STATUS_UNDER_RESERVED_PRICE : BIZ_STATUS_OK);
            }
        });
        if (!CollectionUtils.isEmpty(newBos)) {
            final List<LauCreativeTemplateBo> hiddenTemplateBos = hiddenTemplateService.addHiddenTemplate(hiddenContextBo, newBos.get(0));
            newBos.addAll(hiddenTemplateBos);
        }
        final RecDiffResult<LauCreativeTemplateBo, Long> result = CommonFuncs.recDiff(existingBos, newBos, LauCreativeTemplateBo::getSlotGroupId, LauCreativeTemplateBo::getId, CommonFuncs.getDefaultBiFunction(LauCreativeTemplateBo::getId, LauCreativeTemplateBo::setId));
        CommonFuncs.handleRecDiff(result, adCoreBqf, lauCreativeTemplate, lauCreativeTemplate.id::in);
    }

    /**
     * convert to creative po(未落库)
     *
     * @param newCreative
     * @param oldCreative
     * @param unit
     * @param account
     * @return
     */
    public LauUnitCreativePo convertNewCreativeToPo(CpcCreativeDto newCreative, CpcCreativeDto oldCreative,
                                                    CpcUnitDto unit, AccountBaseDto account) {

        LauUnitCreativePo entity = new LauUnitCreativePo();

        BeanUtils.copyProperties(newCreative, entity);
        entity.setExtImageUrl(Strings.isNullOrEmpty(newCreative.getExtImageUrl()) ? "" : newCreative.getExtImageUrl());
        entity.setExtImageMd5(Strings.isNullOrEmpty(newCreative.getExtImageMd5()) ? "" : newCreative.getExtImageMd5());
        entity.setCreativeJson("");
        entity.setCategoryFirstId(newCreative.getFirstCategoryId());
        entity.setCategorySecondId(newCreative.getSecondCategoryId());
        entity.setCmMark(newCreative.getCmMark());
        entity.setBusMarkId(newCreative.getBusMarkId());
        entity.setTags(Joiner.on(",").skipNulls().join(newCreative.getTags()));
        if (newCreative.getBilibiliUserId() != null) {
            entity.setBilibiliUserId(newCreative.getBilibiliUserId());
        } else {
            entity.setBilibiliUserId(UserType.getByCode(account.getUserType()) == UserType.PERSONAL_FLY ? CreativeSourceType.PERSONAL_FLY.getCode() : CreativeSourceType.UNKNOWN.getCode());
        }
        entity.setStyleAbility(newCreative.getCreativeStyle());

        // 老的不存在，新增
        if (oldCreative == null) {
            // 待审核
            entity.setAuditStatus(AuditStatus.INIT.getCode());
            // 开启
            entity.setStatus(LaunchStatus.START.getCode());
            // 审核中
            entity.setCreativeStatus(CreativeStatus.AUDITING.getCode());

            newCreative.setAuditStatus(AuditStatus.INIT.getCode());
            newCreative.setStatus(LaunchStatus.START.getCode());
            newCreative.setCreativeStatus(CreativeStatus.AUDITING.getCode());
        }
        // 老的创意存在，修改
        else {
            // 需要重新审核
            if (isNeedReaudit(newCreative, oldCreative, unit, account)) {
                // 重新审核需要的状态
                entity.setAuditStatus(AuditStatus.INIT.getCode());
                newCreative.setAuditStatus(AuditStatus.INIT.getCode());

                if (oldCreative.getStatus() == LaunchStatus.START.getCode()) {
                    entity.setCreativeStatus(CreativeStatus.AUDITING.getCode());
                    newCreative.setCreativeStatus(CreativeStatus.AUDITING.getCode());
                } else {
                    entity.setCreativeStatus(oldCreative.getCreativeStatus());
                    newCreative.setCreativeStatus(oldCreative.getCreativeStatus());
                }
            }
            // 不需要重新审核
            else {
                entity.setCreativeStatus(oldCreative.getCreativeStatus());
                entity.setAuditStatus(oldCreative.getAuditStatus());
                newCreative.setCreativeStatus(oldCreative.getCreativeStatus());
                newCreative.setAuditStatus(oldCreative.getAuditStatus());
            }

            entity.setStatus(oldCreative.getStatus());
            newCreative.setStatus(oldCreative.getStatus());
        }

        if (!Utils.isPositive(newCreative.getMgkPageId())
                && newCreative.getPromotionPurposeContent().contains(MgkConstants.H5_LANDING_PAGE.replace(MgkConstants.HTTP_SCHEME, ""))) {
            entity.setMgkPageId(Long.valueOf(JumpTypeEnum.MGK_PAGE_ID.getGameOrPageIdFromUrl(newCreative.getPromotionPurposeContent())));
        }

        entity.setIsHistory(0);
        return entity;
    }

    /**
     * 新增或者修改单元上创意信息
     *
     * @param po
     * @param accountId
     * @param unitId
     */
    public void saveOrUpdatePo(LauUnitCreativePo po, Integer accountId, Integer unitId) {
        if (Utils.isPositive(po.getCreativeId())) {
            Integer creativeId = po.getCreativeId();

            LauUnitCreativePoExample example = new LauUnitCreativePoExample();
            example.or()
                    .andAccountIdEqualTo(accountId)
                    .andUnitIdEqualTo(unitId)
                    .andCreativeIdEqualTo(creativeId);

            lauUnitCreativeDao.updateByExampleSelective(po, example);
        } else {
            lauUnitCreativeDao.insertSelective(po);
        }
        AdpCatUtils.logEventAndLog("saveOrUpdateCreative", "saveOrUpdateCreative", "creativeId=%s", po.getCreativeId());
    }

    public void updateAuditStatusAndCreativeStatus(Integer creativeId, Integer auditStatus, Integer creativeStatus) {
        lauUnitCreativeDao.updateByPrimaryKeySelective(LauUnitCreativePo.builder()
                .creativeId(creativeId)
                .auditStatus(auditStatus)
                .creativeStatus(creativeStatus)
                .build());
    }

    public void updateCreativeImageUrlAndMd5(Integer creativeId, String url, String md5) {
        lauUnitCreativeDao.updateByPrimaryKeySelective(LauUnitCreativePo.builder()
                .creativeId(creativeId)
                .imageUrl(url)
                .imageMd5(md5)
                .build());
    }

    public void saveCreativeShare(CpcCreativeDto creative, int newCreativeId) {
        log.info("save creative share, creativeId {}", newCreativeId);

        if (creative.getShareState() != null) {
            LauCreativeSharePo sharePo = LauCreativeSharePo.builder()
                    .creativeId(newCreativeId)
                    .state(creative.getShareState())
                    .title(creative.getShareTitle())
                    .subtitle(creative.getShareSubtitle())
                    .imageUrl(creative.getShareImageUrl())
                    .imageMd5(creative.getShareImageMd5())
                    .build();
            log.info("save creative share, creativeId {}, sharePo {}", newCreativeId, JSON.toJSONString(sharePo));
            creativeShareDao.insertUpdateSelective(sharePo);
        }
    }

    /**
     * 保存创意自动更新表
     *
     * @param creative
     * @param newCreativeId
     */
    public void saveCreativeAuto(CpcCreativeDto creative, int newCreativeId) {
        if (creative.getAutoDto() != null) {
            LauCreativeAutoPo autoPo = LauCreativeAutoPo.builder()
                    .creativeId(newCreativeId)
                    .refreshType(creative.getAutoDto().getRefreshType())
                    .build();
            log.info("save creative auto, creativeId {}, autoPo {}", newCreativeId, JSON.toJSONString(autoPo));
            lauCreativeAutoDao.insertUpdateSelective(autoPo);
        }
    }

    private void saveRegisterCreativeDynamic(int newCreativeId, CpcCreativeDto creative, CpcCreativeDto oldCreative, CpcUnitDto unit) {
        LauCreativeDynamicPo dynamicPo = convertNewCreativeToDynamicPo(creative, oldCreative, newCreativeId);
        log.info("save creative dynamic, creativeId {}, dynamicPo {}", newCreativeId, JSON.toJSONString(dynamicPo));
        AdpCatUtils.logEventAndLog("saveRegisterCreativeDynamic", "saveRegisterCreativeDynamic start", "creativeId=%s", newCreativeId);

        final Long mid;

        if (Utils.isPositive(unit.getEnterpriseMid())) {
            // 优先使用单元绑定的空间mid
            mid = unit.getEnterpriseMid();
        } else if (Utils.isPositive(creative.getBrandInfoId())) {
            // 最后使用品牌标, 如果品牌标绑定了mid, 就使用这个mid, 否则mid为0
            final LauAccountInfoBo brandInfoBo = launchBrandInfoService.get(creative.getAccountId(), creative.getBrandInfoId());
            Assert.notNull(brandInfoBo, "选定的品牌信息非法");
            mid = Optional.ofNullable(brandInfoBo.getMid()).orElse(0L);
        } else {
            throw new ServiceRuntimeException("此计划未选择品牌信息，不支持创建动态广告的创意");
        }
        launchDynamicService.registerOrUpdate(newCreativeId, mid, creative.getAccountId(), creative.getTitle(), creative.getDynamic().getForwardState(), creative.getDynamic().getReplyState(), creative.getDynamic().getReplyMonitor());
        log.info("save & register creative dynamic success, creativeId {}, dynamicPo {}", newCreativeId, JSON.toJSONString(creative.getDynamic()));
    }

    private LauCreativeDynamicPo convertNewCreativeToDynamicPo(CpcCreativeDto creative, CpcCreativeDto oldCreative, int creativeId) {
        return LauCreativeDynamicPo.builder()
                .id(oldCreative == null ? null : oldCreative.getDynamic().getDynamicId())
                .creativeId(creativeId)
                .forwardState(creative.getDynamic().getForwardState())
                .linkUrl(creative.getDynamic().getTitleLink())
                .linkName(creative.getDynamic().getTitleLinkName())
                .isDeleted(IsDeleted.VALID.getCode())
                .build();
    }

    public void deleteOldICreativeImage(List<ImageDto> oldImageDtoList, List<ImageDto> newImageDtoList) {
        Set<Integer> existImageIdList = (CollectionUtils.isEmpty(newImageDtoList) ? Collections.emptySet()
                : newImageDtoList.stream().map(ImageDto::getId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet()));
        List<ImageDto> oldImageList = (!CollectionUtils.isEmpty(oldImageDtoList) ? oldImageDtoList
                : Collections.emptyList());
        List<Integer> deleteImageIdList = oldImageList.stream()
                .filter(oldImageDto -> !existImageIdList.contains(oldImageDto.getId()))
                .map(ImageDto::getId)
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(deleteImageIdList)) {
            LauCreativeImagePo updatePo = new LauCreativeImagePo();
            updatePo.setIsDeleted(IsDeleted.DELETED.getCode());
            LauCreativeImagePoExample example = new LauCreativeImagePoExample();
            example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                    .andIdIn(deleteImageIdList);
            lauCreativeImageDao.updateByExampleSelective(updatePo, example);
        }
    }

    /**
     * 保存创意视频云视频 lau_creative_video
     *
     * @param creativeId
     * @param mgkVideoId
     */
    public void saveCreativeVideo(Integer creativeId, int mgkVideoId) {
        AdpCatUtils.logEventAndLog("saveCreativeVideo", "saveCreativeVideo start", "creativeId=%s," +
                "mgkVideoId", creativeId, mgkVideoId);
        if (mgkVideoId > 0) {
            // 查询视频
            MgkVideoDto mgkVideoDto = cpcLaunchService.getMgkVideoDto(mgkVideoId);

            LauCreativeVideoPo lauCreativeVideoPo = new LauCreativeVideoPo();
            lauCreativeVideoPo.setBizId(mgkVideoDto.getBizId());
            lauCreativeVideoPo.setCover(mgkVideoDto.getCover());
            lauCreativeVideoPo.setCreativeId(creativeId);
            lauCreativeVideoPo.setDuration(mgkVideoDto.getDuration());
            lauCreativeVideoPo.setHeight(mgkVideoDto.getHeight());
            lauCreativeVideoPo.setMd5(mgkVideoDto.getMd5());
            lauCreativeVideoPo.setMgkVideoId(mgkVideoDto.getId());
            lauCreativeVideoPo.setSize(mgkVideoDto.getSize());
            lauCreativeVideoPo.setVideoUrl(mgkVideoDto.getVideoUrl());
            lauCreativeVideoPo.setWidth(mgkVideoDto.getWidth());
            lauCreativeVideoPo.setSource("vupload");
            lauCreativeVideoPo.setDynamicTime(dynamicTime);
            lauCreativeVideoPo.setIsDeleted(IsDeleted.VALID.getCode());
            // 横屏竖屏
            lauCreativeVideoPo.setOrientation(VideoOrientationEnum
                    .getBySize(mgkVideoDto.getWidth(), mgkVideoDto.getHeight()).getCode());
            lauCreativeVideoDao.insertUpdateSelective(lauCreativeVideoPo);
        }
    }

    private void deleteCreativeVideo(Integer creativeId) {
        AdpCatUtils.logEventAndLog("deleteCreativeVideo", "deleteCreativeVideo start", "creativeId=%s", creativeId);
        if (creativeId > 0) {
            LauCreativeVideoPoExample example = new LauCreativeVideoPoExample();
            example.or().andCreativeIdEqualTo(creativeId).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
            lauCreativeVideoDao.updateByExampleSelective(LauCreativeVideoPo.builder().isDeleted(IsDeleted.DELETED.getCode()).build(), example);
        }
    }

    private LauCreativeButtonCopyPo getCreativeButtonCopyByCreativeId(Integer creativeId) {
        LauCreativeButtonCopyPoExample example = new LauCreativeButtonCopyPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andCreativeIdEqualTo(creativeId);
        List<LauCreativeButtonCopyPo> poList = lauCreativeButtonCopyDao.selectByExample(example);

        return CollectionUtils.isEmpty(poList) ? null : poList.get(0);
    }

    void saveLiveReserveButtonCopy(Integer unitId, Integer creativeId) {
        log.info("saveLiveReserveButtonCopy, unitId:{},creativeId:{}", unitId, creativeId);

        LauCreativeButtonCopyPo oldPo = getCreativeButtonCopyByCreativeId(creativeId);

        final LauCreativeButtonCopyPo po = new LauCreativeButtonCopyPo();
        po.setButtonType(6);
        po.setUnitId(unitId);
        po.setCreativeId(creativeId);
        po.setButtonCopyId(32);
        po.setJumpUrl("");
        po.setCustomizedUrl("");
        po.setExtendUrl("");

        if (oldPo != null) {
            po.setId(oldPo.getId());
            lauCreativeButtonCopyDao.updateByPrimaryKeySelective(po);
        } else {
            lauCreativeButtonCopyDao.insertSelective(po);
        }
    }

    void saveCreativeButtonCopy(CpcUnitDto unit, CpcCreativeDto creative, boolean isSupportButton, int newCreativeId,
                                Integer buttonCopyType) {
        log.info("saveCreativeButton, unitId:{},creativeId:{}", unit.getUnitId(), newCreativeId);

        LauCreativeButtonCopyPo oldPo = getCreativeButtonCopyByCreativeId(newCreativeId);

        if (isSupportButton
                && CreativeAttachTypeEnum.BUTTON_COPY.getCode() == creative.getAttachType()) {

            Assert.notNull(buttonCopyType, "按钮文案类型不可为空");

            LauCreativeButtonCopyPo buttonCopyPo = new LauCreativeButtonCopyPo();

            String jumpUrl = creative.getButtonCopyUrl();
            LaunchButtonTypeEnum launchButtonTypeEnum = this.getButtonType(PromotionPurposeType.getByCode(unit.getPromotionPurposeType()), ButtonCopyTypeEnum.getByCode(buttonCopyType), jumpUrl);

            if (LaunchButtonTypeEnum.APP_DOWNLOAD.equals(launchButtonTypeEnum)) {
                jumpUrl = Strings.isNullOrEmpty(unit.getAppInfo().getInternalUrl()) ? unit.getAppInfo().getUrl() : unit.getAppInfo().getInternalUrl();
                buttonCopyPo.setExtendUrl(creative.getButtonCopyUrl() == null ? "" : creative.getButtonCopyUrl());
            } else {
                buttonCopyPo.setExtendUrl("");
            }

            buttonCopyPo.setButtonType(launchButtonTypeEnum.getCode());
            buttonCopyPo.setUnitId(unit.getUnitId());
            buttonCopyPo.setCreativeId(newCreativeId);
            buttonCopyPo.setButtonCopyId(creative.getButtonCopyId());
            buttonCopyPo.setJumpUrl(Strings.isNullOrEmpty(jumpUrl) ? "" : jumpUrl);
            buttonCopyPo.setCustomizedUrl(Values.emptyIfNull(creative.getCustomizedClickUrl()));

            if (oldPo != null) {
                buttonCopyPo.setId(oldPo.getId());
                lauCreativeButtonCopyDao.updateByPrimaryKeySelective(buttonCopyPo);
            } else {
                lauCreativeButtonCopyDao.insertSelective(buttonCopyPo);
            }
        } else {
            if (oldPo != null) {
                LauCreativeButtonCopyPo updatePo = LauCreativeButtonCopyPo.builder()
                        .id(oldPo.getId())
                        .isDeleted(IsDeleted.DELETED.getCode())
                        .build();
                lauCreativeButtonCopyDao.updateByPrimaryKeySelective(updatePo);
            }
        }
    }

    private void deleteCreativeButtonCopyByCreativeId(Integer creativeId) {
        log.info("deleteCreativeButtonCopyByCreativeId, creativeId:{}", creativeId);

        LauCreativeButtonCopyPoExample example = new LauCreativeButtonCopyPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andCreativeIdEqualTo(creativeId);

        LauCreativeButtonCopyPo record = new LauCreativeButtonCopyPo();
        record.setIsDeleted(IsDeleted.DELETED.getCode());
        lauCreativeButtonCopyDao.updateByExampleSelective(record, example);
    }

    public void saveDanmaku(Integer creativeId, List<String> danmakus) {
        log.info("saveDanmaku, creativeId:{}", creativeId);

        deleteDanmaku(creativeId);

        if (CollectionUtils.isEmpty(danmakus)) {
            return;
        }

        Assert.notNull(creativeId, "创意id不能为空");

        List<LauCreativeDanmakuPo> pos = danmakus
                .stream()
                .map(danmaku -> {
                    LauCreativeDanmakuPo po = new LauCreativeDanmakuPo();

                    po.setCreativeId(creativeId);
                    po.setDanmaku(danmaku);

                    return po;
                })
                .collect(Collectors.toList());

        extLauCreativeDanmakuDao.batchInsert(pos);
        log.info("saveDanmaku, creativeId:{}, size:{}", creativeId, pos.size());

    }

    private void deleteDanmaku(Integer creativeId) {
        log.info("deleteDanmaku, creativeId:{}", creativeId);

        LauCreativeDanmakuPoExample example = new LauCreativeDanmakuPoExample();
        example.or()
                .andCreativeIdEqualTo(creativeId)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());

        LauCreativeDanmakuPo po = new LauCreativeDanmakuPo();
        po.setIsDeleted(IsDeleted.DELETED.getCode());

        lauCreativeDanmakuDao.updateByExampleSelective(po, example);
    }

    private String creativeMonitoringUk(LauCreativeMonitoringPo po) {
        return po.getUrl() + "-" + po.getType().toString();
    }

    public void saveCreativeMonitoring(Integer creativeId, CpcCreativeDto creativeDto) {
        log.info("saveCreativeMonitoring, creativeId:{}", creativeId);

        Assert.notNull(creativeId, "创意id不能为空");

        final LauCreativeMonitoringPoExample cond = new LauCreativeMonitoringPoExample();
        cond.or().andCreativeIdEqualTo(Long.valueOf(creativeId));
        final List<LauCreativeMonitoringPo> existingPos = lauCreativeMonitoringDao.selectByExample(cond);
        final List<LauCreativeMonitoringPo> pos = creativeDto.getCreativeMonitoring()
                .stream()
                .map(x -> monitorDtoToPos(creativeDto.getUnitId(), creativeId, x))
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
        final RecDiffResult<LauCreativeMonitoringPo, Integer> result = CommonFuncs.recDiff(existingPos, pos,
                this::creativeMonitoringUk, LauCreativeMonitoringPo::getId, LauCreativeMonitoringPo::setId);
        for (Integer id : result.getOfflineRecordKeys()) {
            lauCreativeMonitoringDao.deleteByPrimaryKey(id);
        }
        for (LauCreativeMonitoringPo po : result.getNewRecords()) {
            lauCreativeMonitoringDao.insertSelective(po);
        }
        for (LauCreativeMonitoringPo po : result.getChangedRecords()) {
            lauCreativeMonitoringDao.updateByPrimaryKeySelective(po);
        }
    }

    private List<LauCreativeMonitoringPo> monitorDtoToPos(Integer unitId, Integer creativeId, CpcCreativeMonitoringDto monitoringDto) {
        List<LauCreativeMonitoringPo> result = Collections.emptyList();
        if (!CollectionUtils.isEmpty(monitoringDto.getUrls())) {
            result = monitoringDto.getUrls().stream()
                    .map(url -> LauCreativeMonitoringPo.builder().unitId(unitId)
                            .creativeId(Long.valueOf(creativeId)).type(monitoringDto.getType()).url(url).build())
                    .collect(Collectors.toList());
        }
        return result;
    }

    public String getFormatedJumpUrl(Integer advertisingMode, Integer creativeId, CpcUnitDto unit, String landingPageUrl, AccountBaseDto account, TemplateGroupBo templateGroupBo, boolean isSubPkg) {
        if (StringUtils.isEmpty(landingPageUrl)) {
            return StringUtils.EMPTY;
        }
        String formatedUrl = landingPageUrl;
        if (unit.getPromotionPurposeType().equals(PromotionPurposeType.LIVE_ROOM.getCode())) {
            formatedUrl = decorateLiveRoomUrl(landingPageUrl);
        }
        Boolean supportShopGoods = accountLabelService.isAccountIdInLabel(account.getAccountId(), pptShopGoodsLabelId);
        if (supportShopGoods) {
            formatedUrl = getShopJumpUrl(creativeId, unit, formatedUrl, account);
        }
        Boolean supportShelfGame = accountLabelService.isAccountIdInLabel(account.getAccountId(), pptOnShelfGameLabelId);
        if (supportShelfGame && Objects.equals(PromotionPurposeType.ON_SHELF_GAME.getCode(), unit.getPromotionPurposeType())
                && Utils.isPositive(unit.getGameBaseId())) {
            boolean isInner = Objects.equals(account.getIsInner(), IsValid.TRUE.getCode());
            final String gameHiddenParam;
            if (AdvertisingMode.nativeContentMode(advertisingMode)) {
                gameHiddenParam = LaunchConstant.GAME_HIDDEN_PARAM_VALUE_9788;
            } else {
                if (AdpVersion.isLegacy(unit.getAdpVersion()) && LaunchConstant.TEMPLATE_ID_SET_9777.contains(templateGroupBo.getId())
                        || AdpVersion.isMergedInGeneral(unit.getAdpVersion()) && templateGroupBo.getTemplateIdList().stream().anyMatch(LaunchConstant.TEMPLATE_ID_SET_9777::contains)) {
                    gameHiddenParam = LaunchConstant.GAME_HIDDEN_PARAM_VALUE_9777;
                } else {
                    gameHiddenParam = LaunchConstant.GAME_HIDDEN_PARAM_VALUE_9782;
                }
            }
            formatedUrl = decorateGameJumpUrl(formatedUrl, gameHiddenParam, isInner, isSubPkg, unit.getSubPkg());
        }
        if (MgkLandingPageParserUtils.isH5(landingPageUrl) || MgkLandingPageParserUtils.isMiniApp(landingPageUrl)) {
            formatedUrl = UriComponentsBuilder.fromUriString(landingPageUrl)
                    .query(Constants.MGK_PAGE_MACRO_PARAM)
                    .build(false).toUriString();
        }
        if (Strings.isNullOrEmpty(formatedUrl) || formatedUrl.equals(landingPageUrl)) {
            return landingPageUrl;
        }
        // 系统内置请求参数去重复, 防止由于用户误操作导致下游解析失败
        formatedUrl = validationService.filterRedundantReservedUrlParams(formatedUrl);
        return formatedUrl;
    }

    private String getShopJumpUrl(Integer creativeId, CpcUnitDto unit, String landingPageUrl, AccountBaseDto account) {
        String shopGoodContentUrl = null;
        Boolean supportShopGoods = accountLabelService.isAccountIdInLabel(account.getAccountId(), pptShopGoodsLabelId);
        if (unit.getPromotionPurposeType() == null || !supportShopGoods) {
            return landingPageUrl;
        }

        if (unit.getPromotionPurposeType().equals(PromotionPurposeType.LANDING_PAGE.getCode()) ||
                unit.getPromotionPurposeType().equals(PromotionPurposeType.SALE_GOODS.getCode())) {

            shopGoodContentUrl = UriComponentsBuilder.fromUriString(landingPageUrl)
                    .replaceQueryParam(LaunchConstant.QUERY_FROM_CPC_KEY, String.format(FROM_CPC, unit.getSalesType()))
                    .build(false).toUriString().replace("from={_FROM_}", "");
        } else if (unit.getPromotionPurposeType().equals(PromotionPurposeType.SHOP_GOODS.getCode())) {
            DpaShopGoodsDto shopGood = unit.getShopGoodsDto();
            log.info("saveShopJumpUrl creativeId:{}, shopGood:{}", creativeId, shopGood);
            Assert.notNull(shopGood, "单元商品不存在");

            // 强制替换from参数
            shopGoodContentUrl = Strings.isNullOrEmpty(landingPageUrl) ? "" :
                    UriComponentsBuilder.fromUriString(landingPageUrl)
                            .replaceQueryParam("from", String.format(FROM_CPC, unit.getSalesType()))
                            .build()
                            .toUriString();
        }

        if (Strings.isNullOrEmpty(shopGoodContentUrl)) {
            return landingPageUrl;
        }

        return shopGoodContentUrl;
    }

    public String decorateLiveRoomUrl(String url) {
        return UriComponentsBuilder.fromUriString(url)
                .replaceQueryParam(LIVE_ROOM_HIDDEN_PARAM_KEY_FROM, LIVE_ROOM_HIDDEN_PARAM_VALUE_FROM)
                .replaceQueryParam(LIVE_ROOM_HIDDEN_PARAM_KEY_EXTRA_JUMP_FROM, LIVE_ROOM_HIDDEN_PARAM_VALUE_EXTRA_JUMP_FROM)
                .replaceQueryParam(LIVE_ROOM_HIDDEN_PARAM_KEY_LAUNCH_TYPE, LIVE_ROOM_HIDDEN_PARAM_VALUE_LAUNCH_TYPE)
                .replaceQueryParam(LIVE_ROOM_HIDDEN_PARAM_KEY_LAUNCH_ID, LIVE_ROOM_HIDDEN_PARAM_VALUE_LAUNCH_ID)
                .replaceQueryParam(LIVE_ROOM_HIDDEN_PARAM_KEY_SESSION_ID, LIVE_ROOM_HIDDEN_PARAM_VALUE_SESSION_ID)
                .replaceQueryParam(track_id, __TRACKID__)
                .build(false)
                .toString();
    }

    public String decorateGameJumpUrl(String promotionPurposeContent, String gameHiddenParam, boolean isInner, boolean isSubPkg, Integer pkgType) {
        if (StringUtils.isEmpty(promotionPurposeContent)) {
            return StringUtils.EMPTY;
        }
        final UriComponentsBuilder builder = UriComponentsBuilder.fromUriString(promotionPurposeContent);
        if (!isInner) {
            //仅对外广添加source参数
            //仅对外广添加sourceFrom参数
            builder.replaceQueryParam(LaunchConstant.LANDING_PAGE_QUERY_GAME_KEY, LaunchConstant.LANDING_PAGE_QUERY_GAME_VALUE);
            builder.replaceQueryParam(LaunchConstant.GAME_HIDDEN_PARAM_KEY, gameHiddenParam);
            // 广告包
            if (isSubPkg) {
                builder.replaceQueryParam("channelId", "1")
                        .replaceQueryParam("channelExtra", "");
            }
            // cps包
            if (Integer.valueOf(LaunchJumpUrlService.ON_SHELF_GAME_MACRO_VALUE_CHANNEL_ID_CPS).equals(pkgType)){
                builder.replaceQueryParam(LaunchJumpUrlService.ON_SHELF_GAME_MACRO_KEY_CHANNEL_ID, LaunchJumpUrlService.ON_SHELF_GAME_MACRO_VALUE_CHANNEL_ID_CPS)
                        .replaceQueryParam(LaunchJumpUrlService.ON_SHELF_GAME_MACRO_KEY_CHANNEL_EXTRA, LaunchJumpUrlService.ON_SHELF_GAME_MACRO_VALUE_CHANNEL_EXTRA);
            }
        }
        final String ppc = builder.build(false).toUriString();
        if (StringUtils.isEmpty(ppc)) {
            return promotionPurposeContent;
        }
        return ppc;
    }

    public String decorateGameJumpUrl(String promotionPurposeContent, String gameHiddenParam, boolean isInner, boolean isSubPkg) {
        return decorateGameJumpUrl(promotionPurposeContent, gameHiddenParam, isInner,isSubPkg, Functions.boolean2Integer(isSubPkg));
    }

    /**
     * 保存创意布局关联表
     *
     * @param creative
     * @param template
     * @param newCreativeId
     * @param formatedJumpUrl
     * @throws ServiceException
     */
    public void saveCreativLayout(CpcCreativeDto creative, TemplateGroupBo template, int newCreativeId, String formatedJumpUrl) throws ServiceException {
        log.info("saveCreativLayout, creativeId:{}", newCreativeId);
        // 没有模板 || 模板不支持动态布局，删除动态布局
        if (template == null || !IsValid.TRUE.getCode().equals(template.getIsSupportDynamicLayout())) {
            this.deleteCreativeLayoutInCreativeIds(Collections.singletonList(newCreativeId));
        }
        // 否则，
        else {
            // 查询已有的布局
            List<LauCreativeImagePo> creativeImagePos = creativeServiceDelegate.getCreativeImagesByCreativeId(newCreativeId);
            LocCreativeBean lcb = LocCreativeBean
                    .builder()
                    .buttonCopy(Utils.getString(creative.getButtonCopy()))
                    .buttonCopyUrl(Utils.getString(creative.getButtonCopyUrl()))
                    .buttonReportUrls(Collections.emptyList())
                    .cmMark(Utils.getInteger(creative.getCmMark()))
                    .cmMarkDesc(Utils.getString(creative.getCmMarkDesc()))
                    .creativeType(creative.getCreativeType())
                    .customizedClickUrl(Utils.getString(creative.getCustomizedClickUrl()))
                    .customizedImpUrl(Utils.getString(creative.getCustomizedImpUrl()))
                    .description(Utils.getString(creative.getDescription()))
                    .extDescription(Utils.getString(creative.getExtDescription()))
                    .extImageUrl(Utils.getString(creative.getExtImageUrl()))
                    .imageBeans(this.buildLocImageBeans(creativeImagePos, creative.getImageDtos()))
                    .promotionPurposeContent(formatedJumpUrl)
                    .schemeUrl(Utils.getString(creative.getSchemeUrl()))
                    .title(Utils.getString(creative.getTitle()))
                    .build();

            if (!Strings.isNullOrEmpty(creative.getAppDownloadUrl())) {
                lcb.setButtonCopyUrl(creative.getAppDownloadUrl());
            }

            String layoutJson = "";
            List<LauCreativeLayoutPo> layoutPos = Lists.newArrayList();

            // android 布局
            if (!Strings.isNullOrEmpty(CollectionHelper.getFirst(template.getAndroidLayouts()))) {
                LauCreativeLayoutPo androidLayoutPo = new LauCreativeLayoutPo();
                androidLayoutPo.setCreativeId(newCreativeId);

                layoutJson = LocUtils.getLayoutJson(lcb, CollectionHelper.getFirst(template.getAndroidLayouts()));

                androidLayoutPo.setType(LayoutTypeEnum.ANDROID.getCode());
                androidLayoutPo.setLayout(layoutJson);

                layoutPos.add(androidLayoutPo);
            }

            // ios 布局
            if (!Strings.isNullOrEmpty(CollectionHelper.getFirst(template.getFieldMappings()))) {
                LauCreativeLayoutPo iosLayoutPo = new LauCreativeLayoutPo();
                iosLayoutPo.setCreativeId(newCreativeId);

                layoutJson = LocUtils.getLayoutJson(lcb, CollectionHelper.getFirst(template.getFieldMappings()));

                iosLayoutPo.setType(LayoutTypeEnum.IOS.getCode());
                iosLayoutPo.setLayout(layoutJson);

                layoutPos.add(iosLayoutPo);
            }

            // 批量保存布局
            if (!CollectionUtils.isEmpty(layoutPos)) {
                log.info("layout batchSave, size:{}", layoutPos.size());
                extCreativeLayoutDao.batchSave(layoutPos);
            }

        }
    }

    private List<LocImageBean> buildLocImageBeans(List<LauCreativeImagePo> imagePos, List<ImageDto> imageDtos) {
        if (CollectionUtils.isEmpty(imageDtos)) {
            return Collections.emptyList();
        }

        List<LocImageBean> imageBeanList = Lists.newArrayListWithCapacity(imageDtos.size());
        for (int i = 0; i < imageDtos.size(); i++) {
            ImageDto imageDto = imageDtos.get(i);
            imageBeanList.add(LocImageBean.builder()
                    .id(imagePos.get(i).getId())
                    .url(imageDto.getUrl())
                    .jumpUrl(Strings.isNullOrEmpty(imageDto.getJumpUrl()) ? "" : imageDto.getJumpUrl().trim())
                    .callUpUrl(Strings.isNullOrEmpty(imageDto.getCallUpUrl()) ? "" : imageDto.getCallUpUrl().trim())
                    .clickReportUrls(CollectionUtils.isEmpty(imageDto.getClickReportUrls()) ? Collections.emptyList() : imageDto.getClickReportUrls())
                    .build());
        }

        return imageBeanList;
    }

    public void saveCreativeBusinessCategory(Integer creativeId, Integer firstCategoryId, Integer secondCategoryId, Integer thirdCategoryId) {
        log.info("savaCreativeBusinessCategory creativeId:{}, firstCategoryId:{}, secondCategoryId:{}, thirdCategoryId:{}",
                creativeId, firstCategoryId, secondCategoryId, thirdCategoryId);

        if (firstCategoryId == null || firstCategoryId.equals(0)) {
            log.info("savaCreativeBusinessCategory skip");
            return;
        }

        LauCreativeBusinessCategoryPo po = new LauCreativeBusinessCategoryPo();

        po.setCreativeId(creativeId);
        po.setFirstCategoryId(firstCategoryId);
        po.setSecondCategoryId(secondCategoryId);
        po.setThirdCategoryId(thirdCategoryId);

        extLauCreativeBusinessCategoryDao.save(po);
    }

    /**
     * 处理加急审核情况
     *
     * @param currentAccountId
     * @param creative
     * @param oldCreative
     * @param currentAuditStatus
     * @param newCreativeId
     */
    public void processUrgentAudit(Integer currentAccountId, CpcCreativeDto creative, CpcCreativeDto oldCreative, Integer currentAuditStatus, int newCreativeId) {
        log.info("processUrgentAudit, creativeId:{}", newCreativeId);
        // 从配置获取: 创意加急审核账号列表
        String urgentAuditAccountIdsStr = systemConfigService.getValueByItem(SystemConfig.CREATIVE_URGENT_AUDIT_ACCOUNTIDS);
        List<Integer> urgentAuditAccountIds = JSON.parseArray(urgentAuditAccountIdsStr, Integer.class);

        // 当前账号为加急审核的账号 && (新增的创意 || (修改的 && 老状态不是待审核 && 新状态为待审核))
        boolean isNeedUrgentAudit = !CollectionUtils.isEmpty(urgentAuditAccountIds) && urgentAuditAccountIds.contains(currentAccountId) &&
                (oldCreative == null || !oldCreative.getAuditStatus().equals(AuditStatus.INIT.getCode()) && currentAuditStatus.equals(AuditStatus.INIT.getCode()));
        if (isNeedUrgentAudit) {
            // 发送消息，异步审核
            applicationEventPublisher.publishEvent(new CreativeAuditEvent(this, creative, newCreativeId, AuditStatus.INIT));
        }
    }

    /**
     * 是否需要重新审核
     *
     * @param newCreative
     * @param oldCreative
     * @param unit
     * @param account
     * @return
     */
    private boolean isNeedReaudit(CpcCreativeDto newCreative, CpcCreativeDto oldCreative,
                                  CpcUnitDto unit, AccountBaseDto account) {
        if (oldCreative == null) {
            return true;
        }

        if (!isEqual(newCreative.getTitle(), genWildcard(oldCreative.getTitle(), oldCreative.getWildcardDesc())) ||
                !isEqual(newCreative.getDescription(), oldCreative.getDescription()) ||
                !isEqual(newCreative.getButtonCopy(), oldCreative.getButtonCopy()) ||
                !isEqual(newCreative.getButtonCopyUrl(), oldCreative.getButtonCopyUrl()) ||
                !isEqual(parsePromotionContentUrl(JumpTypeEnum.getByCode(newCreative.getJumpType()).parseLaunchUrl(newCreative.getPromotionPurposeContent()), unit, account),
                        parsePromotionContentUrl(oldCreative.getPromotionPurposeContent(), unit, account)) ||
                !isEqual(newCreative.getExtDescription(), oldCreative.getExtDescription()) ||
                !isEqual(newCreative.getBusMarkId(), oldCreative.getBusMarkId()) ||
                !isEqual(newCreative.getCmMark(), oldCreative.getCmMark()) ||
                !yellowCarEquals(Utils.isPositive(newCreative.getIsYellowCar()), Utils.isPositive(oldCreative.getIsYellowCar()), newCreative.getYellowCarTitle(), oldCreative.getYellowCarTitle())
                || LaunchComponentService.componentsNeedReAudit(oldCreative.getComponents(), newCreative.getComponents())
        ) {
            return true;
        }
        if (!CollectionHelper.isEquals(oldCreative.getDanmakus(), newCreative.getDanmakus())) {
            return true;
        }


        return !isImagesEqual(newCreative.getImageDtos(), oldCreative.getImageDtos());
    }


    private boolean yellowCarEquals(Boolean newIsYellowCar, Boolean oldIsYellowCar,
                                    String newYellowCarTitle, String oldYellowCarTitle) {
        boolean realNewIsYellowCar = Boolean.TRUE.equals(newIsYellowCar) ? true : false;
        boolean realOldIsYellowCar = Boolean.TRUE.equals(oldIsYellowCar) ? true : false;
        String realNewYellowCarTitle = StringUtils.isNotEmpty(newYellowCarTitle) && realNewIsYellowCar ? newYellowCarTitle : "";
        String realOldYellowCarTitle = StringUtils.isNotEmpty(oldYellowCarTitle) && realOldIsYellowCar ? oldYellowCarTitle : "";
        if (realNewIsYellowCar == realOldIsYellowCar && realNewYellowCarTitle.equals(realOldYellowCarTitle)) {
            return true;
        }
        return false;
    }


    private boolean isEqual(Integer newInt, Integer oldInt) {
        boolean isNewEmpty = Objects.isNull(newInt);
        boolean isOldEmpty = Objects.isNull(oldInt);

        if (isNewEmpty && isOldEmpty) {
            return true;
        }

        if (!isNewEmpty && isOldEmpty) {
            return false;
        }

        if (isNewEmpty && !isOldEmpty) {
            return false;
        }

        return newInt.equals(oldInt);
    }

    private boolean isEqual(String newStr, String oldStr) {
        boolean isNewEmpty = Strings.isNullOrEmpty(newStr);
        boolean isOldEmpty = Strings.isNullOrEmpty(oldStr);

        if (isNewEmpty && isOldEmpty) {
            return true;
        }

        if (!isNewEmpty && isOldEmpty) {
            return false;
        }

        if (isNewEmpty && !isOldEmpty) {
            return false;
        }

        return newStr.equals(oldStr);
    }

    private boolean isImagesEqual(List<ImageDto> newImages, List<ImageDto> oldImages) {
        boolean isNewImagesEmpty = CollectionUtils.isEmpty(newImages);
        boolean isOldImagesEmpty = CollectionUtils.isEmpty(oldImages);

        if (isNewImagesEmpty && isOldImagesEmpty) {
            return true;
        }

        if (!isNewImagesEmpty && isOldImagesEmpty) {
            return false;
        }

        if (isNewImagesEmpty) {
            return false;
        }

        Map<Integer, ImageDto> oldImageMap = oldImages.stream().collect(Collectors.toMap(ImageDto::getId, dto -> dto));

        for (ImageDto newImage : newImages) {
            ImageDto oldImage = oldImageMap.get(newImage.getId());

            if (oldImage == null || !oldImage.getMd5().equals(newImage.getMd5())) {
                return false;
            }
        }

        return true;
    }

    private String parsePromotionContentUrl(String url, CpcUnitDto unit, AccountBaseDto account) {
        Boolean supportShopGoods = accountLabelService.isAccountIdInLabel(account.getAccountId(), pptShopGoodsLabelId);
        if (supportShopGoods && (unit.getPromotionPurposeType().equals(PromotionPurposeType.LANDING_PAGE.getCode()) ||
                unit.getPromotionPurposeType().equals(PromotionPurposeType.SALE_GOODS.getCode()))) {
            return UriComponentsBuilder.fromUriString(url)
                    .replaceQueryParam(LaunchConstant.QUERY_FROM_CPC_KEY, Collections.emptyList()).build(false).toUriString();
        }
        if (unit.getPromotionPurposeType().equals(PromotionPurposeType.ARCHIVE_CONTENT.getCode())) {
            return UriComponentsBuilder.fromUriString(url)
                    .replaceQueryParam(Constants.FROM_SPMID, Collections.emptyList())
                    .replaceQueryParam(Constants.TRACKID, Collections.emptyList())
                    .replaceQueryParam(Constants.track_id)
                    .build(false).toUriString();
        }
        return url;
    }

    public void saveInviteLink(Boolean isInsert, Long avId, Long realMid, Integer accountId, Integer campaignId, Integer unitId, Integer creativeId,
                               FlyProUnderBoxSaveDto flyProUnderBox) {
        if (isInsert) {
            flyProUnderBoxDelegate.insertCreativeUnderBoxMapping(avId, realMid, accountId, campaignId, unitId, creativeId, flyProUnderBox);
        } else {
            List<FlyVideoUnderBoxMappingPo> pos = flyProUnderBoxDelegate.queryByCreativeId(creativeId);
            if (!CollectionUtils.isEmpty(pos)) {
                flyProUnderBoxDelegate.updateCreativeUnderBoxMapping(pos.get(0), flyProUnderBox);
            } else {
                //之前有邀约链接，现在邀约链接没了，需要填写框下信息
                flyProUnderBoxDelegate.insertCreativeUnderBoxMapping(avId, realMid, accountId, campaignId, unitId, creativeId, flyProUnderBox);
            }
        }
    }

    public void flyRegisterOrUpdate(Integer creativeId, Long mid, Integer accountId, String title, Integer forwardState, Integer replySubjectState, Integer replyMonitorState) {
        flyRegisterOrUpdateReplySubject(creativeId, replySubjectState, replyMonitorState);
    }

    // 注册或更新评论区
    public void flyRegisterOrUpdateReplySubject(Integer creativeId, Integer replySubjectState, Integer replyMonitorState) {
        if (Objects.isNull(replySubjectState)) return;

        Assert.isTrue(ReplySubjectStateEnum.commonEnumKV.containsKey(replySubjectState), "评论状态非法");
        Assert.isTrue(ReplySubjectMonitorEnum.commonEnumKV.containsKey(replyMonitorState), "评论监控状态非法");

        final long longCreativeId = creativeId.longValue();
        final Map<Long, ReplySubjectDto> map = getSubjectMapInCreativeIds(longCreativeId);
        if (map.containsKey(longCreativeId)) {
            replyService.setReplySubjectState(SetReplySubjectStateParam.builder()
                    .oid(longCreativeId)
                    .state(replySubjectState)
                    .build());
        } else {
            replyService.registerReplySubject(ReplySubjectRegisterParam.builder()
                    .oid(longCreativeId)
                    .mid(0L)
                    .state(replySubjectState)
                    .monitor(replyMonitorState)
                    .build());
        }
    }

    private Map<Long, ReplySubjectDto> getSubjectMapInCreativeIds(Long creativeId) {
        final QueryReplySubjectParam param = QueryReplySubjectParam.builder()
                .oids(Collections.singletonList(creativeId))
                .build();
        return replyService.queryReplySubjectsMap(param);
    }
}