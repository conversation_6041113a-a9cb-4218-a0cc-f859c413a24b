package com.bilibili.adp.cpc.config;

import com.bilibili.collage.api.service.ISchoolConfigService;
import com.bilibili.collage.api.soa.ISoaCollageMediaService;
import com.bilibili.commercialorder.soa.adauth.service.ISoaAdAuth4AdpService;
import com.bilibili.commercialorder.soa.order.service.ISoaCmInviteAdService;
import com.bilibili.commercialorder.soa.order.service.ISoaCmOrderService;
import com.bilibili.commercialorder.soa.session.service.ISoaSessionService;
import com.bilibili.crm.platform.soa.*;
import com.bilibili.crm.platform.soa.coupon.ISoaCouponService;
import com.bilibili.crm.platform.soa.wallet.ISoaQueryWalletLogService;
import com.bilibili.dmp.service.ISoaTargetTagsService;
import com.bilibili.location.api.service.query.IQueryButtonCopyService;
import com.bilibili.mgk.platform.api.archive.soa.ISoaMgkCmSpaceService;
import com.bilibili.mgk.platform.api.landing_page.soa.ISoaLandingPageService;
import com.bilibili.mgk.platform.api.video_library.soa.ISoaVideoLibraryService;
import com.bilibili.report.platform.api.soa.ISoaConversionStatService;
import org.apache.http.client.HttpClient;
import org.apache.http.client.params.ClientPNames;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.impl.conn.tsccm.ThreadSafeClientConnManager;
import org.apache.http.params.CoreConnectionPNames;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.remoting.httpinvoker.HttpComponentsHttpInvokerRequestExecutor;
import org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean;
import org.springframework.stereotype.Component;

@Component("cpcHttpInvokerImporter")
public class HttpInvokerImporter {
    private static final String ADP_CPC_REQUEST_EXECUTOR = "CPC_ADP_REQUEST_EXECUTOR";

    @Value("${adp.httpInvoker.connectTimeout:2000}")
    private int connectTimeout;

    @Value("${adp.httpInvoker.readTimeout:5000}")
    private int readTimeout;

    @Value("${adp.httpInvoker.defaultMaxPerRoute:20}")
    private int defaultMaxPerRoute;

    /**
     * 用于事务型接口，快速返回，避免过多的请求停留在服务器上。
     */
    @Bean(ADP_CPC_REQUEST_EXECUTOR)
    @SuppressWarnings("deprecation")
    public HttpComponentsHttpInvokerRequestExecutor shortHttpInvokerRequestExecutor() {
        HttpComponentsHttpInvokerRequestExecutor executor = new HttpComponentsHttpInvokerRequestExecutor();
        ThreadSafeClientConnManager connManager = new ThreadSafeClientConnManager();
        connManager.setMaxTotal(80);
        connManager.setDefaultMaxPerRoute(defaultMaxPerRoute);
        HttpClient httpClient = new DefaultHttpClient(connManager);
        httpClient.getParams().setLongParameter(ClientPNames.CONN_MANAGER_TIMEOUT, 5000);
        httpClient.getParams().setParameter(CoreConnectionPNames.SO_TIMEOUT, 10000);
        executor.setHttpClient(httpClient);
        executor.setConnectTimeout(connectTimeout);
        executor.setReadTimeout(readTimeout);
        return executor;
    }

    @Value("${crm.service.url}")
    private String crmServiceUrl;

    @Value("${location.service.url}")
    private String locationServiceUrl;

    @Value("${mgk.service.url}")
    private String mgkServiceUrl;

    @Value("${commercial-order.service.url:http://cm.bilibili.co/commercialorder/api/service}")
    private String commercialOrderServiceUrl;

    @Value("${report.service.url:http://cm.bilibili.co/report/api/service}")
    private String reportServiceUrl;

    @Bean
    public HttpInvokerProxyFactoryBean soaAgentService(@Qualifier(ADP_CPC_REQUEST_EXECUTOR) HttpComponentsHttpInvokerRequestExecutor executor) {
        HttpInvokerProxyFactoryBean bean = new HttpInvokerProxyFactoryBean();
        bean.setServiceUrl(crmServiceUrl + "/agentService");
        bean.setServiceInterface(ISoaAgentService.class);
        bean.setHttpInvokerRequestExecutor(executor);
        return bean;
    }

    @Bean
    public HttpInvokerProxyFactoryBean soaCompanyGroupService(@Qualifier(ADP_CPC_REQUEST_EXECUTOR) HttpComponentsHttpInvokerRequestExecutor executor) {
        HttpInvokerProxyFactoryBean bean = new HttpInvokerProxyFactoryBean();
        bean.setServiceUrl(crmServiceUrl + "/soaCompanyGroupService");
        bean.setServiceInterface(ISoaCompanyGroupService.class);
        bean.setHttpInvokerRequestExecutor(executor);
        return bean;
    }

    @Bean
    public HttpInvokerProxyFactoryBean soaCustomerService(@Qualifier(ADP_CPC_REQUEST_EXECUTOR) HttpComponentsHttpInvokerRequestExecutor executor){
        HttpInvokerProxyFactoryBean bean = new HttpInvokerProxyFactoryBean();
        bean.setServiceUrl(crmServiceUrl + "/soaCustomerService");
        bean.setServiceInterface(ISoaCustomerService.class);
        bean.setHttpInvokerRequestExecutor(executor);
        return bean;
    }

    @Bean
    public HttpInvokerProxyFactoryBean soaQueryWalletLogService(@Qualifier(ADP_CPC_REQUEST_EXECUTOR) HttpComponentsHttpInvokerRequestExecutor executor) {
        HttpInvokerProxyFactoryBean bean = new HttpInvokerProxyFactoryBean();
        bean.setServiceUrl(crmServiceUrl + "/soaQueryWalletLogService");
        bean.setServiceInterface(ISoaQueryWalletLogService.class);
        bean.setHttpInvokerRequestExecutor(executor);
        return bean;
    }

    @Bean
    public HttpInvokerProxyFactoryBean soaCouponService(@Qualifier(ADP_CPC_REQUEST_EXECUTOR) HttpComponentsHttpInvokerRequestExecutor executor) {
        HttpInvokerProxyFactoryBean bean = new HttpInvokerProxyFactoryBean();
        bean.setServiceUrl(crmServiceUrl + "/soaCouponService");
        bean.setServiceInterface(ISoaCouponService.class);
        bean.setHttpInvokerRequestExecutor(executor);
        return bean;
    }

    @Bean
    public HttpInvokerProxyFactoryBean soaAccountService(@Qualifier(ADP_CPC_REQUEST_EXECUTOR) HttpComponentsHttpInvokerRequestExecutor executor) {
        HttpInvokerProxyFactoryBean bean = new HttpInvokerProxyFactoryBean();
        bean.setServiceUrl(crmServiceUrl + "/accountService");
        bean.setServiceInterface(ISoaAccountService.class);
        bean.setHttpInvokerRequestExecutor(executor);
        return bean;
    }

    @Bean
    public HttpInvokerProxyFactoryBean soaQueryAccountService(@Qualifier(ADP_CPC_REQUEST_EXECUTOR) HttpComponentsHttpInvokerRequestExecutor executor) {
        HttpInvokerProxyFactoryBean bean = new HttpInvokerProxyFactoryBean();
        bean.setServiceUrl(crmServiceUrl + "/queryAccountService");
        bean.setServiceInterface(ISoaQueryAccountService.class);
        bean.setHttpInvokerRequestExecutor(executor);
        return bean;
    }

    @Bean
    public HttpInvokerProxyFactoryBean soaAccountLabelService(@Qualifier(ADP_CPC_REQUEST_EXECUTOR) HttpComponentsHttpInvokerRequestExecutor executor) {
        HttpInvokerProxyFactoryBean bean = new HttpInvokerProxyFactoryBean();
        bean.setServiceUrl(crmServiceUrl + "/soaAccountLabelService");
        bean.setServiceInterface(ISoaAccountLabelService.class);
        bean.setHttpInvokerRequestExecutor(executor);
        return bean;
    }

    @Bean
    public HttpInvokerProxyFactoryBean collageMediaService(@Qualifier(ADP_CPC_REQUEST_EXECUTOR) HttpComponentsHttpInvokerRequestExecutor executor) {
        HttpInvokerProxyFactoryBean bean = new HttpInvokerProxyFactoryBean();
        bean.setServiceUrl(mgkServiceUrl + "/collageMediaService");
        bean.setServiceInterface(ISoaCollageMediaService.class);
        bean.setHttpInvokerRequestExecutor(executor);
        return bean;
    }

    @Bean
    public HttpInvokerProxyFactoryBean mgkLandingPageService(@Qualifier(ADP_CPC_REQUEST_EXECUTOR) HttpComponentsHttpInvokerRequestExecutor executor) {
        HttpInvokerProxyFactoryBean bean = new HttpInvokerProxyFactoryBean();
        bean.setServiceUrl(mgkServiceUrl + "/mgkLandingPageService");
        bean.setServiceInterface(ISoaLandingPageService.class);
        bean.setHttpInvokerRequestExecutor(executor);
        return bean;
    }

    @Bean
    public HttpInvokerProxyFactoryBean videoLibraryService(@Qualifier(ADP_CPC_REQUEST_EXECUTOR) HttpComponentsHttpInvokerRequestExecutor executor) {
        HttpInvokerProxyFactoryBean bean = new HttpInvokerProxyFactoryBean();
        bean.setServiceUrl(mgkServiceUrl + "/videoLibraryService");
        bean.setServiceInterface(ISoaVideoLibraryService.class);
        bean.setHttpInvokerRequestExecutor(executor);
        return bean;
    }

    @Bean
    public HttpInvokerProxyFactoryBean soaQueryButtonService(@Qualifier(ADP_CPC_REQUEST_EXECUTOR) HttpComponentsHttpInvokerRequestExecutor executor) {
        HttpInvokerProxyFactoryBean bean = new HttpInvokerProxyFactoryBean();
        bean.setServiceUrl(locationServiceUrl + "/service/queryButtonCopyService");
        bean.setServiceInterface(IQueryButtonCopyService.class);
        bean.setHttpInvokerRequestExecutor(executor);
        return bean;
    }

    @Bean
    public HttpInvokerProxyFactoryBean soaAdAuth4AdpService(@Qualifier(ADP_CPC_REQUEST_EXECUTOR) HttpComponentsHttpInvokerRequestExecutor executor) {
        HttpInvokerProxyFactoryBean bean = new HttpInvokerProxyFactoryBean();
        bean.setServiceUrl(commercialOrderServiceUrl + "/soaAdAuth4AdpService");
        bean.setServiceInterface(ISoaAdAuth4AdpService.class);
        bean.setHttpInvokerRequestExecutor(executor);
        return bean;
    }

    @Bean
    public HttpInvokerProxyFactoryBean soaPickupSessionService(@Qualifier(ADP_CPC_REQUEST_EXECUTOR) HttpComponentsHttpInvokerRequestExecutor executor) {
        HttpInvokerProxyFactoryBean bean = new HttpInvokerProxyFactoryBean();
        bean.setServiceUrl(commercialOrderServiceUrl + "/soaSessionService");
        bean.setServiceInterface(ISoaSessionService.class);
        bean.setHttpInvokerRequestExecutor(executor);
        return bean;
    }

    @Bean
    public HttpInvokerProxyFactoryBean soaCmOrderService(@Qualifier(ADP_CPC_REQUEST_EXECUTOR) HttpComponentsHttpInvokerRequestExecutor executor) {
        HttpInvokerProxyFactoryBean bean = new HttpInvokerProxyFactoryBean();
        bean.setServiceUrl(commercialOrderServiceUrl + "/soaCmOrderService");
        bean.setServiceInterface(ISoaCmOrderService.class);
        bean.setHttpInvokerRequestExecutor(executor);
        return bean;
    }

    @Bean
    public HttpInvokerProxyFactoryBean soaCmInviteAdService(@Qualifier(ADP_CPC_REQUEST_EXECUTOR) HttpComponentsHttpInvokerRequestExecutor executor) {
        HttpInvokerProxyFactoryBean bean = new HttpInvokerProxyFactoryBean();
        bean.setServiceUrl(commercialOrderServiceUrl + "/soaCmInviteAdService");
        bean.setServiceInterface(ISoaCmInviteAdService.class);
        bean.setHttpInvokerRequestExecutor(executor);
        return bean;
    }

    @Bean
    public HttpInvokerProxyFactoryBean soaMgkCmSpaceService(@Qualifier(ADP_CPC_REQUEST_EXECUTOR) HttpComponentsHttpInvokerRequestExecutor executor) {
        HttpInvokerProxyFactoryBean bean = new HttpInvokerProxyFactoryBean();
        bean.setServiceUrl(mgkServiceUrl + "/mgkCmSpaceService");
        bean.setServiceInterface(ISoaMgkCmSpaceService.class);
        bean.setHttpInvokerRequestExecutor(executor);
        return bean;
    }

    @Bean
    public HttpInvokerProxyFactoryBean soaISoaConversionStatService(@Qualifier(ADP_CPC_REQUEST_EXECUTOR) HttpComponentsHttpInvokerRequestExecutor executor) {
        HttpInvokerProxyFactoryBean proxyFactoryBean = new HttpInvokerProxyFactoryBean();
        proxyFactoryBean.setServiceUrl(reportServiceUrl + "/soaConversionStatService");
        proxyFactoryBean.setServiceInterface(ISoaConversionStatService.class);
        proxyFactoryBean.setHttpInvokerRequestExecutor(executor);
        return proxyFactoryBean;
    }

    @Value("${dmp.service.url:http://cm.bilibili.co/dmp/api/service}")
    private String dmpServiceUrl;

    @Bean
    public HttpInvokerProxyFactoryBean soaTargetTagsService(@Qualifier(ADP_CPC_REQUEST_EXECUTOR) HttpComponentsHttpInvokerRequestExecutor executor) {
        HttpInvokerProxyFactoryBean proxyFactoryBean = new HttpInvokerProxyFactoryBean();
        proxyFactoryBean.setServiceUrl(dmpServiceUrl + "/targetTagsService");
        proxyFactoryBean.setServiceInterface(ISoaTargetTagsService.class);
        proxyFactoryBean.setHttpInvokerRequestExecutor(executor);
        return proxyFactoryBean;
    }

    @Bean
    public HttpInvokerProxyFactoryBean soaFinanceService(@Qualifier(ADP_CPC_REQUEST_EXECUTOR) HttpComponentsHttpInvokerRequestExecutor executor) {
        HttpInvokerProxyFactoryBean bean = new HttpInvokerProxyFactoryBean();
        bean.setServiceUrl(crmServiceUrl + "/crmFinanceService");
        bean.setServiceInterface(ISoaFinanceService.class);
        bean.setHttpInvokerRequestExecutor(executor);
        return bean;
    }

    @Bean
    public HttpInvokerProxyFactoryBean soaSchoolConfigService(@Qualifier(ADP_CPC_REQUEST_EXECUTOR) HttpComponentsHttpInvokerRequestExecutor executor) {
        HttpInvokerProxyFactoryBean bean = new HttpInvokerProxyFactoryBean();
        bean.setServiceUrl(mgkServiceUrl + "/schoolService");
        bean.setServiceInterface(ISchoolConfigService.class);
        bean.setHttpInvokerRequestExecutor(executor);
        return bean;
    }
}
