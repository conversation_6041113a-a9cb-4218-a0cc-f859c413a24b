package com.bilibili.adp.cpc.splash_screen.creative.converter;

import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauSplashScreenCreativePo;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauUnitCreativePo;
import com.bilibili.adp.cpc.splash_screen.creative.bos.SplashScreenCreative;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper
public interface CreativeConverter {
    CreativeConverter MAPPER = Mappers.getMapper(CreativeConverter.class);
    @Mapping(target = "styleAbility", constant = "1")
    @Mapping(target = "isHistory", constant = "0")
    @Mapping(target = "templateId", constant = "0")
    @Mapping(target = "creativeType", constant = "1")
    @Mapping(target = "videoUrl", ignore = true)
    @Mapping(target = "videoId", ignore = true)
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "underFrameAuditFlag", ignore = true)
    @Mapping(target = "trackadf", ignore = true)
    @Mapping(target = "titleId", ignore = true)
    @Mapping(target = "title", ignore = true)
    @Mapping(target = "templateGroupId", ignore = true)
    @Mapping(target = "tags", ignore = true)
    @Mapping(target = "reason", ignore = true)
    @Mapping(target = "promotionPurposeContentSecondary", ignore = true)
    @Mapping(target = "progMiscElemAuditStatus", ignore = true)
    @Mapping(target = "progAuditStatus", ignore = true)
    @Mapping(target = "preferScene", ignore = true)
    @Mapping(target = "orderId", ignore = true)
    @Mapping(target = "modifyOfflineCreativeId", ignore = true)
    @Mapping(target = "materialVideoId", ignore = true)
    @Mapping(target = "materialId", ignore = true)
    @Mapping(target = "isVideoBind", ignore = true)
    @Mapping(target = "isTag", ignore = true)
    @Mapping(target = "isRecheck", ignore = true)
    @Mapping(target = "isProgrammatic", ignore = true)
    @Mapping(target = "isPageGroup", ignore = true)
    @Mapping(target = "isNewFly", ignore = true)
    @Mapping(target = "isMark", ignore = true)
    @Mapping(target = "isManaged", ignore = true)
    @Mapping(target = "isGdPlus", ignore = true)
    @Mapping(target = "isAutoFill", ignore = true)
    @Mapping(target = "imageUrl", ignore = true)
    @Mapping(target = "imageMd5", ignore = true)
    @Mapping(target = "flowWeightState", ignore = true)
    @Mapping(target = "flag", ignore = true)
    @Mapping(target = "extImageUrl", ignore = true)
    @Mapping(target = "extImageMd5", ignore = true)
    @Mapping(target = "extDescription", ignore = true)
    @Mapping(target = "description", ignore = true)
    @Mapping(target = "creativeJson", ignore = true)
    @Mapping(target = "categorySecondId", ignore = true)
    @Mapping(target = "categoryFirstId", ignore = true)
    @Mapping(target = "buttonCopy", ignore = true)
    @Mapping(target = "busMarkId", ignore = true)
    @Mapping(target = "bilibiliUserId", ignore = true)
    @Mapping(target = "autoAuditFlag", ignore = true)
    @Mapping(target = "advertisingMode", ignore = true)
    @Mapping(target = "adMark", ignore = true)
    @Mapping(target = "adVersionControllId", source = "adVersionControlId")
    @Mapping(target = "promotionPurposeContent", source = "splashScreenCreative.jumpUrl")
    @Mapping(target = "schemeUrl", source = "splashScreenCreative.schemaUrl")
    @Mapping(target = "isDeleted", ignore = true)
    @Mapping(target = "ctime", ignore = true)
    @Mapping(target = "mtime", ignore = true)
    LauUnitCreativePo bo2CreativePo(SplashScreenCreative splashScreenCreative);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "isDeleted", ignore = true)
    @Mapping(target = "ctime", ignore = true)
    @Mapping(target = "mtime", ignore = true)
    LauSplashScreenCreativePo bo2SplashScreenCreativePo(SplashScreenCreative splashScreenCreative);
}
