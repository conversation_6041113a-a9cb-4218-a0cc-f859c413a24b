package com.bilibili.adp.cpc.account_diagnosis.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;



@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DiagnosisReportColumnBo {
    private Integer fixed;
    private Integer type;
    private Integer code;
    private Integer parentCode;
    private String name;
    private String key;
    private String description;
    private Integer isPositionSensitive;
    private Integer isSelectable;
    private String menuScope;
    private List<DiagnosisReportColumnBo> children;
}
