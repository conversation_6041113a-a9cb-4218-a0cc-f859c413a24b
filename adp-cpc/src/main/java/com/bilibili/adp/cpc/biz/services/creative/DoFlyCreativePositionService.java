package com.bilibili.adp.cpc.biz.services.creative;

import com.alibaba.fastjson.JSON;
import com.bapis.archive.service.Arc;
import com.bapis.archive.service.ArcRequest;
import com.bilibili.adp.account.dto.AccountAllInfoDto;
import com.bilibili.adp.account.dto.AccountBaseDto;
import com.bilibili.adp.account.dto.AccountDto;
import com.bilibili.adp.account.service.IQueryAccountService;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.enums.SalesType;
import com.bilibili.adp.common.enums.SystemConfig;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.GsonUtils;
import com.bilibili.adp.common.util.ObjectUtils;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.biz.services.account.LaunchAccountV1Service;
import com.bilibili.adp.cpc.biz.services.account.config.AccountConfig;
import com.bilibili.adp.cpc.biz.services.archive.ArchiveService;
import com.bilibili.adp.cpc.biz.services.campaign.FlyCampaignService;
import com.bilibili.adp.cpc.biz.services.campaign.api.ICpcCampaignService;
import com.bilibili.adp.cpc.biz.services.campaign.dto.CpcCampaignDto;
import com.bilibili.adp.cpc.biz.services.creative.config.AccLabelConfig;
import com.bilibili.adp.cpc.biz.services.creative.config.CreativePositionConfig;
import com.bilibili.adp.cpc.biz.services.creative.dto.CpcCreativeDto;
import com.bilibili.adp.cpc.biz.services.creative.dynamic.AbstractFlyDynamicDarkLaunchProc;
import com.bilibili.adp.cpc.biz.services.creative.dynamic.FlyDynamicDarkLaunchProcFactory;
import com.bilibili.adp.cpc.biz.services.pickup.PickupOrderQuerier;
import com.bilibili.adp.cpc.biz.services.pickup.dto.PickupOrderDto;
import com.bilibili.adp.cpc.biz.services.unit.CpcUnitService;
import com.bilibili.adp.cpc.biz.services.unit.FlyAccountUnitJudger;
import com.bilibili.adp.cpc.biz.services.unit.FlyUnitService;
import com.bilibili.adp.cpc.biz.services.unit.UnitFillService;
import com.bilibili.adp.cpc.biz.services.unit.dto.CpcUnitDto;
import com.bilibili.adp.cpc.config.flypro.SearchPositionConfig;
import com.bilibili.adp.cpc.core.constants.AdType;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauCampaignPo;
import com.bilibili.adp.cpc.dto.CpcCreativeDetailDto;
import com.bilibili.adp.cpc.enums.AdpVersion;
import com.bilibili.adp.cpc.enums.SpecificScenesEnum;
import com.bilibili.adp.cpc.enums.SpecifyScenesMappingSystemConfigEnum;
import com.bilibili.adp.cpc.enums.ad.CampaignAdType;
import com.bilibili.adp.cpc.enums.ad.LaunchTargetEnum;
import com.bilibili.adp.cpc.enums.ad.OcpcTargetEnum;
import com.bilibili.adp.cpc.enums.ad.PromotionPurposeType;
import com.bilibili.adp.cpc.enums.effectad.unit.UnitLiveLaunchType;
import com.bilibili.adp.cpc.proxy.ArchiveServiceProxy;
import com.bilibili.adp.cpc.repo.OuterLauCampaignRepo;
import com.bilibili.adp.launch.api.flyPro.dto.enums.CoverTypeEnum;
import com.bilibili.adp.launch.api.flyPro.dto.enums.ImageSpecificationEnum;
import com.bilibili.adp.launch.api.flyPro.dto.enums.RoomPromoteScenesEnum;
import com.bilibili.adp.launch.api.flyPro.dto.enums.ScenesEnum;
import com.bilibili.adp.launch.api.flyPro.dto.v2.*;
import com.bilibili.adp.launch.biz.config.FlyPermissionConfig;
import com.bilibili.adp.launch.biz.fly.FlyArchiveService;
import com.bilibili.adp.launch.biz.service.unit.LaunchUnitService;
import com.bilibili.adp.passport.api.dto.ArchiveDetail;
import com.bilibili.adp.passport.biz.manager.ArchiveManager;
import com.bilibili.adp.resource.api.account.label.IAccountLabelService;
import com.bilibili.adp.resource.api.slot_group.IResSlotGroupService;
import com.bilibili.adp.resource.api.slot_group.QueryTemplateLaunchTypeMappingDto;
import com.bilibili.adp.resource.api.slot_group.ResSlotGroupTemplateMappingDto;
import com.bilibili.adp.resource.api.system.ISystemConfigService;
import com.bilibili.bjcom.querydsl.BaseQueryFactory;
import com.bilibili.location.api.service.ITemplateService;
import com.bilibili.location.api.template.dto.TemplateDto;
import com.bilibili.mas.common.utils.Values;
import com.bilibili.mgk.platform.common.utils.NumberUtils;
import com.google.common.collect.Lists;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.bilibili.adp.common.enums.SystemConfig.FLY_PREFER_SCENES_LIST;
import static com.bilibili.adp.cpc.dao.ad_core.querydsl.QLauCreativeFlyExtInfo.lauCreativeFlyExtInfo;
import static com.bilibili.adp.cpc.dao.config.PlatformMySqlConfig.AD_CORE_BQF;
import static com.bilibili.adp.cpc.enums.FlyBanner4CoverEnum.*;
import static com.bilibili.adp.cpc.enums.SpecificScenesEnum.MSG_FLOW;
import static com.bilibili.adp.cpc.enums.SpecificScenesEnum.MSG_FLOW_4_3;
import static com.bilibili.location.api.cardtype.dto.CreativeStyle.GIF;

/**
 * copy class
 * @see com.bilibili.adp.launch.biz.service.DoFlyCreativePositionService
 */
@Primary
@Service(value = "LaunchDoFlyCreativePositionService")
@Slf4j
@RequiredArgsConstructor
public class DoFlyCreativePositionService {
    @Autowired
    private ISystemConfigService systemConfigService;
    @Autowired
    private IResSlotGroupService resSlotGroupService;
    @Autowired
    private IQueryAccountService queryAccountService;
    @Autowired
    private IAccountLabelService accountLabelService;
    @Autowired
    private CreativePositionConfig creativePositionConfig;
    @Autowired
    private ArchiveServiceProxy archiveServiceProxy;
    @Autowired
    private FlyPermissionConfig flyPermissionConfig;
    @Autowired
    private CpcUnitService cpcUnitService;
    @Autowired
    private FlyCampaignService flyCampaignService;
    @Value("${media.source.department.id:110}")
    private Integer mediaSourceDepartmentId;
    @Value("${daihuo.department.id:311}")
    private Integer daihuoDepartmentId;
    @Value("${daihuo.hidden.ott.account.label.id:574}")
    private Integer daihuoHiddenOttAccountLabelId;
    @Value("${daihuo.hidden.ott.slot.group.id:468}")
    public Integer daihuoHiddenOttSlotGroupId;
    @Value("${story.no.cover.label.id:600}")
    public Integer storyNoCoverLabelId;
    @Autowired
    private ArchiveManager archiveManager;
    @Autowired
    private FlyUnitService flyUnitService;
    @Autowired
    private ICpcCampaignService cpcCampaignService;
    @Autowired
    private PickupOrderQuerier pickupOrderQuerier;
    @Autowired
    private FlyArchiveService flyArchiveService;
    /**
     * 内容起飞接搜搜配置项
     */
    @Autowired
    SearchPositionConfig searchPositionConfig;

    @Autowired
    private UnitFillService unitFillService;
    @Autowired
    private AccLabelConfig accLabelConfig;

    @Resource(name = AD_CORE_BQF)
    private BaseQueryFactory adCoreBqf;

    @Autowired
    private OuterLauCampaignRepo outerLauCampaignRepo;
    @Autowired
    private FlyAccountUnitJudger flyUnitJudger;
    private final LaunchAccountV1Service launchAccountService;
    private final FlyDynamicDarkLaunchProcFactory flyDynamicDarkLaunchProcFactory;
    private final AccountConfig accountConfig;
    private final ArchiveService archiveService;
    @Autowired
    private ITemplateService templateService;

    public static final Set<Integer> PERSON_UP_LIVE_OCPC_TARGET = Stream.of(OcpcTargetEnum.LIVE_ENTRY.getCode(),
            OcpcTargetEnum.LIVE_CALLUP.getCode()).collect(Collectors.toSet());

    private Boolean accountIdInLabel(Integer accountId, Integer labelId) {
        return accountLabelService.isAccountIdsInLabels(Collections.singletonList(accountId),
                Collections.singletonList(labelId));
    }

    private Boolean labelListContainsLabelId(List<Integer> accountLabelList, Integer labelId){
        return accountLabelList!=null && accountLabelList.contains(labelId);
    }

    /**
     * 获取页面选中的类型（0-优选，1-信息流，2-播放页，3-信息流大卡，4-动态流 5-story 6-iPAD）和广告位组的映射关系
     *
     * @return
     */
    public Map<Integer, List<ResSlotGroupTemplateMappingDto>> getFlySlotGroupIdAndTemplateMap(Integer accountId) {
        Map<Integer, List<ResSlotGroupTemplateMappingDto>> map = new HashMap<>();
        // 优选支持的广告位组&模板
        List<ResSlotGroupTemplateMappingDto> preference = this.getSlotGroupTemplateMappingList(FLY_PREFER_SCENES_LIST);
        // 账号不在story黑名单里
        if (!this.accountIdInLabel(accountId, creativePositionConfig.getStoryBlackListLabelId())) {
            // 支持story
            preference.addAll(resSlotGroupService.querySlotGroupTemplateMappingInSlotGroupIds(QueryTemplateLaunchTypeMappingDto
                    .builder()
                    .slotGroupIds(creativePositionConfig.getStorySlotGroupIds())
                    .build()));
        }
        map.put(0, preference);
        Arrays.stream(SpecifyScenesMappingSystemConfigEnum.values())
                .forEachOrdered(sceneConfig -> map.put(sceneConfig.getScene().getCode(), getSlotGroupTemplateMappingList(sceneConfig.getConfig())));
        return map;
    }

    public List<ResSlotGroupTemplateMappingDto> getSlotGroupTemplateMappingList(SystemConfig systemConfig) {
        return resSlotGroupService.querySlotGroupTemplateMappingInSlotGroupIds(QueryTemplateLaunchTypeMappingDto
                .builder()
                .slotGroupIds(JSON.parseArray(systemConfigService.getValueByItem(systemConfig), Integer.class))
                .build());
    }

    /**
     * 选择可投放的广告位组，且会过滤掉因为稿件的autoplay=0影响的广告位组
     *
     * @param map
     * @param creative
     * @param launchTarget
     * @param operator
     * @return
     */
    public List<ResSlotGroupTemplateMappingDto> getResSlotGroupTemplateMappingDtoListAndThenFilterWithAutoPlay(
            Map<Integer, List<ResSlotGroupTemplateMappingDto>> map,
            CpcCreativeDto creative,
            Integer launchTarget,
            Operator operator,
            Integer roomPromoteScenes
    ) {
        log.info("getResSlotGroupTemplateMappingDtoListAndThenFilterWithAutoPlay creative={}  map={}", creative, GsonUtils.toJson(map));
        List<ResSlotGroupTemplateMappingDto> list = getResSlotGroupTemplateMappingDtoList(map, creative, launchTarget, operator,roomPromoteScenes);
        if (!Arrays.asList(
                LaunchTargetEnum.ACCOUNT_GROWTH.getCode(),
                LaunchTargetEnum.TRAFFIC_BOOST.getCode()).contains(launchTarget)) {
            //非稿件
            return list;
        }
        Arc arc = archiveServiceProxy.arc(creative.getVideoId()).getArc();
        log.info("getSlotGroupTemplateMappingList with aid={} arc={}", creative.getVideoId(), arc);
        if (archiveService.isAutoPlay(operator.getOperatorId(), arc)) {
            return list;
        }
        List<ResSlotGroupTemplateMappingDto> updatedList = new ArrayList<>();
        for (ResSlotGroupTemplateMappingDto dto : list) {
            List<Integer> canIgnoreSlotGroupIds = creativePositionConfig.getCheck4AutoPlayIgnoreSlotGroups();
            if (!canIgnoreSlotGroupIds.contains(dto.getSlotGroupId())) {
                updatedList.add(dto);
            } else {
                log.info("getSlotGroupTemplateMappingList with aid={} ignore slot group id={} ", dto.getSlotGroupId());
            }
        }
        return updatedList;
    }

    /**
     * 从map获取场景要投的slotGroupId和templateId
     *
     * @param map
     * @param creative
     * @param launchTarget
     * @param operator
     * @return
     */
    public List<ResSlotGroupTemplateMappingDto> getResSlotGroupTemplateMappingDtoList(
            Map<Integer, List<ResSlotGroupTemplateMappingDto>> map,
            CpcCreativeDto creative,
            Integer launchTarget,
            Operator operator,
            Integer roomPromoteScenes){
        //如果是直播预约，直接返回直播间预约。和map无关
        if(RoomPromoteScenesEnum.RESERVE.getCode().equals(roomPromoteScenes)){
            return resSlotGroupService.querySlotGroupTemplateMappingInSlotGroupIds(QueryTemplateLaunchTypeMappingDto
                    .builder()
                    .slotGroupIds(Arrays.asList(creativePositionConfig.getFlySlotLiveReserve()))
                    .build());
        }

        this.checkScene(creative.getFlyScenesType(), creative.getFlySpecificScenesType(), operator);

        List<ResSlotGroupTemplateMappingDto> resSlotGroupTemplateMappingDtos;
        if (ScenesEnum.PREFER_SCENES.getCode().equals(creative.getFlyScenesType())) {
            //优选场景的可用广告位组
            resSlotGroupTemplateMappingDtos = new ArrayList<>(map.get(0));
        } else {
            //创意的指定场景list->该指定场景的可用广告位组list
            resSlotGroupTemplateMappingDtos = Arrays.stream(SpecificScenesEnum.values())
                    .filter(scene -> scene != SpecificScenesEnum.UNKNOWN)
                    .filter(scene -> creative.getFlySpecificScenesType().contains(scene.getCode()))
                    .map(scene -> map.get(scene.getCode()))
                    .flatMap(Collection::stream)
                    .collect(Collectors.toList());
        }
        LaunchTargetEnum launchTargetEnum = LaunchTargetEnum.getByCode(launchTarget);
        //计划的种类进行筛选
        switch (launchTargetEnum) {
            case ACCOUNT_GROWTH:
                resSlotGroupTemplateMappingDtos = resSlotGroupTemplateMappingDtos.stream()
                        .filter(o -> creativePositionConfig.getFlyContentSlotGroupIds().contains(o.getSlotGroupId())
                                || creativePositionConfig.getFlyBigCardSlotGroupIds().contains(o.getSlotGroupId())
                                || creativePositionConfig.getFlyDynamicFlowSlotGroupIds().contains(o.getSlotGroupId())
                                || creativePositionConfig.getStorySlotGroupIds().contains(o.getSlotGroupId())
                                || creativePositionConfig.getIPadRecommendSlotGroupIds().contains(o.getSlotGroupId())
                                || creativePositionConfig.getPcIndexSlotGroupIds().contains(o.getSlotGroupId())
                                || creativePositionConfig.getPcPlaySlotGroupIds().contains(o.getSlotGroupId()))
                        .filter(o -> PromotionPurposeType.ARCHIVE_CONTENT.getCode() == o.getLaunchType()).collect(Collectors.toList());
                break;
            case TRAFFIC_BOOST:
                resSlotGroupTemplateMappingDtos = resSlotGroupTemplateMappingDtos.stream()
                        .filter(o -> creativePositionConfig.getFlyBusinessSlotGroupIds().contains(o.getSlotGroupId())
                                || creativePositionConfig.getFlyBigCardSlotGroupIds().contains(o.getSlotGroupId())
                                || creativePositionConfig.getFlyDynamicFlowSlotGroupIds().contains(o.getSlotGroupId())
                                || creativePositionConfig.getStorySlotGroupIds().contains(o.getSlotGroupId())
                                || creativePositionConfig.getIPadRecommendSlotGroupIds().contains(o.getSlotGroupId())
                                || creativePositionConfig.getPcIndexSlotGroupIds().contains(o.getSlotGroupId())
                                || creativePositionConfig.getPcPlaySlotGroupIds().contains(o.getSlotGroupId()))
                        .filter(o -> PromotionPurposeType.ARCHIVE_CONTENT.getCode() == o.getLaunchType()).collect(Collectors.toList());
                break;
            case LIVE_PROMOTION:
            case BUSINESS_LIVE_PROMOTION:
                resSlotGroupTemplateMappingDtos = resSlotGroupTemplateMappingDtos.stream()
                        .filter(o -> creativePositionConfig.getFlyLiveRoomSlotGroupIds().contains(o.getSlotGroupId()))
                        .filter(o -> PromotionPurposeType.LIVE_ROOM.getCode() == o.getLaunchType()).collect(Collectors.toList());
                break;
            case DYNAMIC:
                AccountAllInfoDto accountAllInfoDto = this.queryAccountService.getAccountAllInfoFromCache(operator.getOperatorId());
                if (ObjectUtils.nullSafeEquals(1, accountAllInfoDto.getAccountDto().getIsSupportContent())) {
                    resSlotGroupTemplateMappingDtos = resSlotGroupTemplateMappingDtos.stream()
                            .filter(o -> creativePositionConfig.getFlyContentDynamicSlotGroupIds().contains(o.getSlotGroupId()))
                            .filter(o -> PromotionPurposeType.DYNAMIC.getCode() == o.getLaunchType()).collect(Collectors.toList());
                }
                if (ObjectUtils.nullSafeEquals(1, accountAllInfoDto.getAccountDto().getIsSupportFly())) {
                    resSlotGroupTemplateMappingDtos = resSlotGroupTemplateMappingDtos.stream()
                            .filter(o -> creativePositionConfig.getFlyBusinessDynamicSlotGroupIds().contains(o.getSlotGroupId()))
                            .filter(o -> PromotionPurposeType.DYNAMIC.getCode() == o.getLaunchType()).collect(Collectors.toList());
                }
                break;
            case ACTIVITY:
                resSlotGroupTemplateMappingDtos = resSlotGroupTemplateMappingDtos.stream()
                        .filter(o -> creativePositionConfig.getActivitySlotGroupIds().contains(o.getSlotGroupId()))
                        .filter(o -> PromotionPurposeType.ACTIVITY.getCode() == o.getLaunchType()).collect(Collectors.toList());
            default:
                break;
        }

        //静态广告位组
        if (CoverTypeEnum.STATIC.getCode().equals(creative.getCoverType())) {
            resSlotGroupTemplateMappingDtos = resSlotGroupTemplateMappingDtos.stream()
                    .filter(o -> creativePositionConfig.getStaticSlotGroupIds().contains(o.getSlotGroupId()) || creativePositionConfig.getStorySlotGroupIds().contains(o.getSlotGroupId()))
                    .collect(Collectors.toList());
        } else
            //GIF广告位组
            if (CoverTypeEnum.GIF.getCode().equals(creative.getCoverType())) {
                resSlotGroupTemplateMappingDtos = resSlotGroupTemplateMappingDtos.stream()
                        .filter(o -> creativePositionConfig.getGifSlotGroupIds().contains(o.getSlotGroupId())).collect(Collectors.toList());
            } else
                //静态inline广告位组
                if (CoverTypeEnum.STATIC_INLINE.getCode().equals(creative.getCoverType())) {
                    resSlotGroupTemplateMappingDtos = resSlotGroupTemplateMappingDtos.stream()
                            .filter(o -> creativePositionConfig.getStaticInlineSlotGroupIds().contains(o.getSlotGroupId())).collect(Collectors.toList());
                } else
                    //静态动态流广告位组
                    if (CoverTypeEnum.STATIC_DYNAMIC_FLOW.getCode().equals(creative.getCoverType())) {
                        resSlotGroupTemplateMappingDtos = resSlotGroupTemplateMappingDtos.stream()
                                .filter(o -> creativePositionConfig.getStaticDynamicFlowSlotGroupIds().contains(o.getSlotGroupId())).collect(Collectors.toList());
                    } else if (CoverTypeEnum.STORY.getCode().equals(creative.getCoverType())) {
                        //story广告位组
                        resSlotGroupTemplateMappingDtos = resSlotGroupTemplateMappingDtos.stream()
                                .filter(o -> creativePositionConfig.getStorySlotGroupIds().contains(o.getSlotGroupId())).collect(Collectors.toList());
                    } else if (CoverTypeEnum.IPAD_RECOMMEND.getCode().equals(creative.getCoverType())) {
                        //iPAD广告位组
                        resSlotGroupTemplateMappingDtos = resSlotGroupTemplateMappingDtos.stream()
                                .filter(o -> creativePositionConfig.getIPadRecommendSlotGroupIds().contains(o.getSlotGroupId())).collect(Collectors.toList());
                    } else if (CoverTypeEnum.PC_INDEX.getCode().equals(creative.getCoverType())) {
                        resSlotGroupTemplateMappingDtos = resSlotGroupTemplateMappingDtos.stream()
                                .filter(o -> creativePositionConfig.getPcIndexSlotGroupIds().contains(o.getSlotGroupId())).collect(Collectors.toList());
                    } else if (CoverTypeEnum.PC_PLAY.getCode().equals(creative.getCoverType())) {
                        resSlotGroupTemplateMappingDtos = resSlotGroupTemplateMappingDtos.stream()
                                .filter(o -> creativePositionConfig.getPcPlaySlotGroupIds().contains(o.getSlotGroupId())).collect(Collectors.toList());
                    }
        return resSlotGroupTemplateMappingDtos;

    }

    /**
     * 根据模板生成虚拟模板组
     *
     * @param templateDtos
     * @return
     */
    public VirtualTemplateGroupDto doGenerateVirtualTemplateGroup(List<TemplateDto> templateDtos) {
        if (CollectionUtils.isEmpty(templateDtos)) {
            return VirtualTemplateGroupDto.builder()
                    .templateDtos(templateDtos)
                    .cardTypes(Collections.emptyList())
                    .isFillTitle(false)
                    .titleMinLength(0)
                    .titleMaxLength(0)
                    .isFillDesc(false)
                    .descMinLength(0)
                    .descMaxLength(0)
                    .isFillExtDesc(false)
                    .extDescMinLength(0)
                    .extDescMaxLength(0)
                    .isSupportImage(false)
                    .isSupportExtImage(false)
                    .isSupportVideoId(false)
                    .isSupportGif(false)
                    .build();
        }
        Set<Integer> cardSet = new HashSet<>();
        Boolean isFillTitle = false;
        Integer titleMinLength = 0;
        Integer titleMaxLength = 0;
        Boolean isFillDesc = false;
        Integer descMinLength = 0;
        Integer descMaxLength = 0;
        ;
        Boolean isFillExtDesc = false;
        Integer extDescMinLength = 0;
        Integer extDescMaxLength = 0;
        Boolean isSupportImage = false;
        Integer imageWidth = 0;
        Integer imageHeight = 0;
        Integer imageKbLimit = 0;
        Boolean isSupportExtImage = false;
        Boolean isSupportVideoId = false;
        //支持gif是且的关系
        Boolean isSupportGif = true;

        //虚拟模板组，取并集
        for (TemplateDto o : templateDtos) {
            cardSet.add(o.getCardType());
            isFillTitle = o.getIsFillTitle() || isFillTitle;
            //titleMinLength = Math.max(o.getTitleMinLength(), titleMinLength);
            //titleMaxLength = Math.min(o.getTitleMaxLength(), titleMaxLength);
            isFillDesc = o.getIsFillDesc() || isFillDesc;
            descMinLength = Math.max(o.getDescMinLength(), descMinLength);
            descMaxLength = Math.max(o.getDescMaxLength(), descMaxLength);
            isFillExtDesc = o.getIsFillExtDesc() || isFillExtDesc;
            extDescMinLength = Math.max(o.getExtDescMinLength(), extDescMinLength);
            extDescMaxLength = Math.max(o.getExtDescMaxLength(), extDescMaxLength);
            isSupportImage = o.getIsSupportImage() || isSupportImage;
            imageWidth = Math.max(o.getImageWidth(), imageWidth);
            imageHeight = Math.max(o.getImageHeight(), imageHeight);
            imageKbLimit = Math.max(o.getImageKbLimit(), imageKbLimit);
            isSupportExtImage = o.getIsSupportExtImage() || isSupportExtImage;
            isSupportVideoId = o.getIsSupportVideoId() || isSupportVideoId;
            isSupportGif = o.getCreativeStyles().contains(GIF) && isSupportGif;
        }
        // 抄的运营后台，其他地方不想改了 有提到再说吧 com.bilibili.adp.manager.portal.webapi.location.TemplateGroupController#mergeTemplates
        if (templateDtos.stream().anyMatch(TemplateDto::getIsFillTitle)) {
            isFillTitle = Boolean.TRUE;
            final List<TemplateDto> data = templateDtos.stream()
                    .filter(TemplateDto::getIsFillTitle)
                    .collect(Collectors.toList());

            // 标题的最小长度取最大值
            titleMinLength = sortAndGetFirst(data, TemplateDto::getTitleMinLength, false);
            // 标题的最大长度取最小值
            titleMaxLength = sortAndGetFirst(data, TemplateDto::getTitleMaxLength, true);
            Assert.isTrue(titleMinLength <= titleMaxLength, "模板组绑定模板的标题长度冲突");
        } else {
            isFillTitle = Boolean.FALSE;
        }
        return VirtualTemplateGroupDto.builder()
                .templateDtos(templateDtos)
                .isFillTitle(isFillTitle)
                .titleMinLength(titleMinLength)
                .titleMaxLength(titleMaxLength)
                .isFillDesc(isFillDesc)
                .descMinLength(descMinLength)
                .descMaxLength(descMaxLength)
                .isFillExtDesc(isFillExtDesc)
                .extDescMinLength(extDescMinLength)
                .extDescMaxLength(extDescMaxLength)
                .isSupportImage(isSupportImage)
                .imageWidth(imageWidth)
                .imageHeight(imageHeight)
                .imageKbLimit(imageKbLimit)
                .isSupportExtImage(isSupportExtImage)
                .isSupportVideoId(isSupportVideoId)
                .isSupportGif(isSupportGif)
                .cardTypes(new ArrayList<>(cardSet))
                .build();
    }

    private static<T> Integer sortAndGetFirst(@NonNull Collection<T> data, @NonNull Function<T, Integer> getField, @NonNull Boolean asc) {
        final Stream<Integer> stream = data.stream()
                .map(getField);
        if (asc) return stream.min(Integer::compareTo).orElseThrow(() -> new RuntimeException("最小值不存在"));
        return stream.max(Integer::compareTo).orElseThrow(() -> new RuntimeException("最大值不存在"));
    }

    public VirtualTemplateGroupDto generateVirtualTemplateGroup(Map<Integer, List<ResSlotGroupTemplateMappingDto>> map,
                                                                CpcCreativeDto creative,
                                                                Integer launchTarget,
                                                                Operator operator,Integer roomPromoteScenes) {
        List<ResSlotGroupTemplateMappingDto> resSlotGroupTemplateMappingDtos =
                this.getResSlotGroupTemplateMappingDtoListAndThenFilterWithAutoPlay(map, creative, launchTarget, operator,roomPromoteScenes);
        List<TemplateDto> templateDtos = new ArrayList<>();
        resSlotGroupTemplateMappingDtos.forEach(o -> {
            templateDtos.add(o.getTemplates().get(0));
        });
        VirtualTemplateGroupDto virtualTemplateGroupDto = this.doGenerateVirtualTemplateGroup(templateDtos);
        virtualTemplateGroupDto.setResSlotGroupTemplateMappingDtos(resSlotGroupTemplateMappingDtos);
        return virtualTemplateGroupDto;
    }


    public List<Integer> getFlyTemplateIds(Integer launchScenes, List<Integer> specificScenesList, Integer launchTarget, Operator operator) {

        // 获取广告位组和模板 ids
        return this.getFlySlotGroupIdAndTemplateIdList(launchScenes, specificScenesList, launchTarget, operator).stream()
                .map(o -> o.getTemplates().get(0).getTemplateId()).collect(Collectors.toList());
    }


    private List<ResSlotGroupTemplateMappingDto> getFlySlotGroupIdAndTemplateIdList(Integer launchScenes, List<Integer> specificScenesList,
                                                                                    Integer launchTarget, Operator operator) {
        LaunchTargetEnum launchTargetEnum = LaunchTargetEnum.getByCode(launchTarget);
        Pair<List<Integer>, Integer> pair = this.getFlyBannerSlotGroupIdsAndPromotionPurpose(launchTargetEnum,
                operator.getOperatorId(), true);
        List<Integer> slotGroupIds = pair.getLeft();
        Integer promotionPurpose = pair.getRight();

        this.checkScene(launchScenes, specificScenesList, operator);

        List<Integer> filterList = new ArrayList<>();

        // 优选场景
        if (ScenesEnum.PREFER_SCENES.getCode().equals(launchScenes)) {
            //优选支持的广告位组
            filterList.addAll(JSON.parseArray(systemConfigService.getValueByItem(FLY_PREFER_SCENES_LIST), Integer.class));
            // 账号不在story黑名单里
            if (!this.accountIdInLabel(operator.getOperatorId(), creativePositionConfig.getStoryBlackListLabelId())) {
                filterList.addAll(creativePositionConfig.getStorySlotGroupIds());
            }
        } else {
            Arrays.stream(SpecifyScenesMappingSystemConfigEnum.values())
                    .forEach(config -> {
                        if (specificScenesList.contains(config.getScene().getCode())) {
                            List<Integer> msgList =
                                    JSON.parseArray(systemConfigService.getValueByItem(config.getConfig()), Integer.class);
                            filterList.addAll(filterSlotGroupIds(msgList, slotGroupIds));
                        }
                    });
        }
        return resSlotGroupService
                .querySlotGroupTemplateMappingInSlotGroupIds(QueryTemplateLaunchTypeMappingDto
                        .builder()
                        .slotGroupIds(filterList)
                        .promotionPurposeType(promotionPurpose)
                        .build());
    }

    private List<Integer> filterSlotGroupIds(List<Integer> list, List<Integer> slotGroupIds) {
        return list.stream().filter(o -> slotGroupIds.contains(o)).collect(Collectors.toList());
    }

    public List<FlyProSimpleCreativeStyleDto> doGetFlyProCreativeStyleDto(Integer launchScenes, List<Integer> specificScenesList,
                                                                          Integer launchTarget, Operator operator) {
        this.checkScene(launchScenes, specificScenesList, operator);

        LaunchTargetEnum launchTargetEnum = LaunchTargetEnum.getByCode(launchTarget);

        List<Integer> slotGroupIds = this.getFlyBannerSlotGroupIdsAndPromotionPurpose(launchTargetEnum,
                operator.getOperatorId(), true).getLeft();

        if (ScenesEnum.PREFER_SCENES.getCode().equals(launchScenes)) {
            //优选只支持静态封面
            return Arrays.asList(FlyProSimpleCreativeStyleDto.builder()
                    .code(CoverTypeEnum.STATIC.getCode())
                    .value(CoverTypeEnum.STATIC.getName())
                    .build());
        } else {
            List<List<FlyProSimpleCreativeStyleDto>> arr = Arrays.stream(SpecifyScenesMappingSystemConfigEnum.values()).map(sceneConfig -> {
                if (specificScenesList.contains(sceneConfig.getScene().getCode())) {
                    List<Integer> msgList = JSON.parseArray(systemConfigService.getValueByItem(sceneConfig.getConfig()),
                            Integer.class);

                    //story的广告位组,补上静态广告位组做过滤
                    if (SpecifyScenesMappingSystemConfigEnum.STORY == sceneConfig) {
                        msgList.addAll(creativePositionConfig.getStaticSlotGroupIds());
                    }

                    List<Integer> filterMsgList = filterSlotGroupIds(msgList, slotGroupIds);

                    return getFlyProCreativeStyleBySlotGroupIds(filterMsgList);
                }
                return null;
            }).filter(Objects::nonNull).collect(Collectors.toList());
            //各个场景支持的封面取交集
            return Utils.findCommonInArrays(arr);
        }
    }

    private List<FlyProSimpleCreativeStyleDto> getFlyProCreativeStyleBySlotGroupIds(List<Integer> list) {
        List<FlyProSimpleCreativeStyleDto> ans = new ArrayList<>();
        //存在静态广告位组
        if (CollectionUtils.containsAny(creativePositionConfig.getStaticSlotGroupIds(), list)) {
            ans.add(FlyProSimpleCreativeStyleDto.builder()
                    .code(CoverTypeEnum.STATIC.getCode())
                    .value(CoverTypeEnum.STATIC.getName())
                    .build());
        }
        //存在GIF广告位组
        if (CollectionUtils.containsAny(creativePositionConfig.getGifSlotGroupIds(), list)) {
            ans.add(FlyProSimpleCreativeStyleDto.builder()
                    .code(CoverTypeEnum.GIF.getCode())
                    .value(CoverTypeEnum.GIF.getName())
                    .build());
        }
        //存在大卡inline广告位组
        if (CollectionUtils.containsAny(creativePositionConfig.getStaticInlineSlotGroupIds(), list)) {
            ans.add(FlyProSimpleCreativeStyleDto.builder()
                    .code(CoverTypeEnum.STATIC_INLINE.getCode())
                    .value(CoverTypeEnum.STATIC_INLINE.getName())
                    .build());
        }
        //存在动态inline广告位组
        if (CollectionUtils.containsAny(creativePositionConfig.getStaticDynamicFlowSlotGroupIds(), list)) {
            ans.add(FlyProSimpleCreativeStyleDto.builder()
                    .code(CoverTypeEnum.STATIC_DYNAMIC_FLOW.getCode())
                    .value(CoverTypeEnum.STATIC_DYNAMIC_FLOW.getName())
                    .build());
        }
        //存在story广告位组
        if (CollectionUtils.containsAny(creativePositionConfig.getStorySlotGroupIds(), list)) {
            ans.add(FlyProSimpleCreativeStyleDto.builder()
                    .code(CoverTypeEnum.STORY.getCode())
                    .value(CoverTypeEnum.STORY.getName())
                    .build());
        }
        //存在iPAD广告位组
        if (CollectionUtils.containsAny(creativePositionConfig.getIPadRecommendSlotGroupIds(), list)) {
            ans.add(FlyProSimpleCreativeStyleDto.builder()
                    .code(CoverTypeEnum.IPAD_RECOMMEND.getCode())
                    .value(CoverTypeEnum.IPAD_RECOMMEND.getName())
                    .build());
        }
        //存在静态广告位组
        if (CollectionUtils.containsAny(creativePositionConfig.getPcIndexSlotGroupIds(), list)) {
            ans.add(FlyProSimpleCreativeStyleDto.builder()
                    .code(CoverTypeEnum.PC_INDEX.getCode())
                    .value(CoverTypeEnum.PC_INDEX.getName())
                    .build());
        }
        //存在静态广告位组
        if (CollectionUtils.containsAny(creativePositionConfig.getPcPlaySlotGroupIds(), list)) {
            ans.add(FlyProSimpleCreativeStyleDto.builder()
                    .code(CoverTypeEnum.PC_PLAY.getCode())
                    .value(CoverTypeEnum.PC_PLAY.getName())
                    .build());
        }
        return ans;
    }

    public List<FlyProCreativeStyleDto> getAllFlyProCreativeStyle(Integer launchTarget) {
        LaunchTargetEnum launchTargetEnum = LaunchTargetEnum.getByCode(launchTarget);
        Assert.isTrue(launchTargetEnum != null, "投放标的不正确");
        int promotionPurposeType = -1;
        //筛选可用广告位组
        switch (launchTargetEnum) {
            case ACCOUNT_GROWTH:
            case TRAFFIC_BOOST:
                promotionPurposeType = 7;
                break;
            case LIVE_PROMOTION:
            case BUSINESS_LIVE_PROMOTION:
                promotionPurposeType = 8;
                break;
            case DYNAMIC:
                promotionPurposeType = 13;
                break;
            case ACTIVITY:
                promotionPurposeType = PromotionPurposeType.ACTIVITY.getCode();
            default:
                break;
        }
        List<FlyProCreativeStyleDto> ans = new ArrayList<>();
        //静态广告位组
        ans.add(buildCreativeStyleByVirtualTemplate(creativePositionConfig.getStaticSlotGroupIds(), promotionPurposeType, CoverTypeEnum.STATIC,
                ImageSpecificationEnum.IMAGE_960_600_300));
        //GIF广告位组
        ans.add(buildCreativeStyleByVirtualTemplate(creativePositionConfig.getGifSlotGroupIds(), promotionPurposeType, CoverTypeEnum.GIF,
                ImageSpecificationEnum.GIF_320_200_300_5000));
        //静态inline广告位组
        ans.add(buildCreativeStyleByVirtualTemplate(creativePositionConfig.getStaticInlineSlotGroupIds(), promotionPurposeType, CoverTypeEnum.STATIC_INLINE,
                ImageSpecificationEnum.IMAGE_960_540_300));
        //静态动态流广告位组
        ans.add(buildCreativeStyleByVirtualTemplate(creativePositionConfig.getStaticDynamicFlowSlotGroupIds(), promotionPurposeType, CoverTypeEnum.STATIC_DYNAMIC_FLOW,
                ImageSpecificationEnum.IMAGE_960_540_300));
        //story
        ans.add(buildCreativeStyleByVirtualTemplate(creativePositionConfig.getStorySlotGroupIds(), promotionPurposeType,
                CoverTypeEnum.STORY, ImageSpecificationEnum.NONE));
        //iPad相关推荐
        ans.add(buildCreativeStyleByVirtualTemplate(creativePositionConfig.getIPadRecommendSlotGroupIds(), promotionPurposeType,
                CoverTypeEnum.IPAD_RECOMMEND,
                ImageSpecificationEnum.IMAGE_960_540_300));
        //PC首页推广栏
        ans.add(buildCreativeStyleByVirtualTemplate(creativePositionConfig.getPcIndexSlotGroupIds(), promotionPurposeType,
                CoverTypeEnum.PC_INDEX,
                ImageSpecificationEnum.IMAGE_960_540_300));
        //PC相关播放页
        ans.add(buildCreativeStyleByVirtualTemplate(creativePositionConfig.getPcPlaySlotGroupIds(), promotionPurposeType,
                CoverTypeEnum.PC_PLAY,
                ImageSpecificationEnum.IMAGE_960_540_300));
        return ans;
    }

    private FlyProCreativeStyleDto buildCreativeStyleByVirtualTemplate(List<Integer> groupIds, int promotionPurposeType, CoverTypeEnum coverType,
                                                                       ImageSpecificationEnum imageSpecification) {
        return buildCreativeStyle(coverType, imageSpecification, getVirtualTemplate(groupIds, promotionPurposeType));
    }

    private VirtualTemplateGroupDto getVirtualTemplate(List<Integer> groupIds, int promotionPurposeType) {
        List<ResSlotGroupTemplateMappingDto> resSlotGroupTemplateMapping = resSlotGroupService
                .querySlotGroupTemplateMappingInSlotGroupIds(QueryTemplateLaunchTypeMappingDto.builder()
                        .slotGroupIds(groupIds)
                        .promotionPurposeType(promotionPurposeType)
                        .build());
        List<TemplateDto> template = resSlotGroupTemplateMapping.stream().map(o -> o.getTemplates().get(0)).collect(Collectors.toList());
        return doGenerateVirtualTemplateGroup(template);
    }

    private FlyProCreativeStyleDto buildCreativeStyle(CoverTypeEnum coverType,
                                                      ImageSpecificationEnum imageSpecification,
                                                      VirtualTemplateGroupDto virtualTemplateGroup) {

        FlyProCreativeStyleDto.FlyProCreativeStyleDtoBuilder builder = FlyProCreativeStyleDto.builder()
                .code(coverType.getCode())
                .value(coverType.getName())
                .supportImage(false)
                .supportGif(false)
                .supportTitle(virtualTemplateGroup.getIsFillTitle())
                .titleMinLength(virtualTemplateGroup.getTitleMinLength())
                .titleMaxLength(virtualTemplateGroup.getTitleMaxLength())
                .supportDesc(virtualTemplateGroup.getIsFillDesc())
                .descMinLength(virtualTemplateGroup.getDescMinLength())
                .descMaxLength(virtualTemplateGroup.getDescMaxLength());
        if (imageSpecification.isSupportImage()) {
            builder.supportImage(true)
                    .supportGif(false)
                    .imageHeight(imageSpecification.getHeight())
                    .imageWidth(imageSpecification.getWidth())
                    .imageKbLimit(imageSpecification.getKbLimit());
        }
        if (imageSpecification.isSupportGif()) {
            builder.supportGif(true)
                    .supportImage(false)
                    .gifHeight(imageSpecification.getHeight())
                    .gifWidth(imageSpecification.getWidth())
                    .gifMilliSecondLimit(imageSpecification.getGifMilliSecondLimit())
                    .gifkbLimit(imageSpecification.getKbLimit());
        }

        return builder.build();
    }

    public Pair<List<Integer>, Integer> getFlyBannerSlotGroupIdsAndPromotionPurpose(LaunchTargetEnum launchTargetEnum,
                                                                                    Integer accountId, boolean onlyCpm) {
        //筛选可用广告位组
        List<Integer> slotGroupIdList = new ArrayList<>();
        int promotionPurpose = -1;
        switch (launchTargetEnum) {
            case ACCOUNT_GROWTH:
                slotGroupIdList.addAll(creativePositionConfig.getFlyContentSlotGroupIds());
                if (onlyCpm) {
                    slotGroupIdList.addAll(creativePositionConfig.getFlyBigCardSlotGroupIds());
                    slotGroupIdList.addAll(creativePositionConfig.getFlyDynamicFlowSlotGroupIds());
                    slotGroupIdList.addAll(creativePositionConfig.getStorySlotGroupIds());
                    slotGroupIdList.addAll(creativePositionConfig.getIPadRecommendSlotGroupIds());
                    slotGroupIdList.addAll(creativePositionConfig.getPcIndexSlotGroupIds());
                    slotGroupIdList.addAll(creativePositionConfig.getPcPlaySlotGroupIds());
                }
                promotionPurpose = PromotionPurposeType.ARCHIVE_CONTENT.getCode();
                break;
            case TRAFFIC_BOOST:
                slotGroupIdList.addAll(creativePositionConfig.getFlyBusinessSlotGroupIds());
                if (onlyCpm) {
                    slotGroupIdList.addAll(creativePositionConfig.getFlyBigCardSlotGroupIds());
                    slotGroupIdList.addAll(creativePositionConfig.getFlyDynamicFlowSlotGroupIds());
                    slotGroupIdList.addAll(creativePositionConfig.getStorySlotGroupIds());
                    slotGroupIdList.addAll(creativePositionConfig.getIPadRecommendSlotGroupIds());
                    slotGroupIdList.addAll(creativePositionConfig.getPcIndexSlotGroupIds());
                    slotGroupIdList.addAll(creativePositionConfig.getPcPlaySlotGroupIds());
                }
                promotionPurpose = PromotionPurposeType.ARCHIVE_CONTENT.getCode();
                break;
            case LIVE_PROMOTION:
            case BUSINESS_LIVE_PROMOTION:
                slotGroupIdList.addAll(creativePositionConfig.getFlyLiveRoomSlotGroupIds());
                promotionPurpose = PromotionPurposeType.LIVE_ROOM.getCode();
                break;
            case DYNAMIC:
                AccountAllInfoDto accountAllInfoDto = queryAccountService.getAccountAllInfoFromCache(accountId);
                if (ObjectUtils.nullSafeEquals(1, accountAllInfoDto.getAccountDto().getIsSupportContent())) {
                    slotGroupIdList.addAll(creativePositionConfig.getFlyContentDynamicSlotGroupIds());
                }
                if (ObjectUtils.nullSafeEquals(1, accountAllInfoDto.getAccountDto().getIsSupportFly())) {
                    slotGroupIdList.addAll(creativePositionConfig.getFlyBusinessDynamicSlotGroupIds());
                }
                promotionPurpose = PromotionPurposeType.DYNAMIC.getCode();
                break;
            case ACTIVITY:
                slotGroupIdList.addAll(creativePositionConfig.getActivitySlotGroupIds());
                promotionPurpose = PromotionPurposeType.ACTIVITY.getCode();
                break;
            default:
                break;
        }
        return Pair.of(slotGroupIdList, promotionPurpose);
    }

    /**
     * 场景校验
     */
    private void checkScene(Integer launchScenes, List<Integer> specificScenesList, Operator operator) {
        //场景选择校验
        Assert.isTrue(ScenesEnum.PREFER_SCENES.getCode().equals(launchScenes) ||
                ScenesEnum.SPECIFIC_SCENES.getCode().equals(launchScenes), "场景选择错误");
        //指定场景选择校验
        if (specificScenesList.contains(SpecificScenesEnum.MSG_FLOW_BIG_CARD.getCode())) {
            Assert.isTrue(!accountLabelService.isAccountIdsInLabels(Arrays.asList(operator.getOperatorId()),
                    Arrays.asList(creativePositionConfig.getFlyBigCardBlackMarkId())), "在信息流大卡黑名单的账号不能投信息流大卡");
        }
        if (specificScenesList.contains(SpecificScenesEnum.DYNAMIC_FLOW.getCode())) {
            Assert.isTrue(accountLabelService.isAccountIdsInLabels(Arrays.asList(operator.getOperatorId()),
                    Arrays.asList(creativePositionConfig.getFlyDynamicFlowMarkId())), "没有动态流权限的账号不能投动态流");
        }
        if (specificScenesList.contains(SpecificScenesEnum.STORY.getCode())) {
            Assert.isTrue(!accountLabelService.isAccountIdsInLabels(Collections.singletonList(operator.getOperatorId()),
                    Collections.singletonList(creativePositionConfig.getStoryBlackListLabelId())), "该账号不支持投竖屏视频");
        }
        if (specificScenesList.contains(SpecificScenesEnum.IPAD_RECOMMEND.getCode())) {
            Assert.isTrue(accountLabelService.isAccountIdsInLabels(Collections.singletonList(operator.getOperatorId()),
                    Collections.singletonList(creativePositionConfig.getIPadMarkId())), "没有iPad推荐权限的账号不能投iPad推荐");
        }
        if (specificScenesList.contains(SpecificScenesEnum.PC_INDEX.getCode())) {
            Assert.isTrue(accountLabelService.isAccountIdsInLabels(Collections.singletonList(operator.getOperatorId()),
                    Collections.singletonList(creativePositionConfig.getPcIndexMarkId())), "没有PC首页推广栏权限的账号不能投PC首页推广栏");
        }
        if (specificScenesList.contains(SpecificScenesEnum.PC_PLAY.getCode())) {
            Assert.isTrue(accountLabelService.isAccountIdsInLabels(Collections.singletonList(operator.getOperatorId()),
                    Collections.singletonList(creativePositionConfig.getPcPageMarkId())), "没有PC相关播放页权限的账号不能投PC相关播放页");
        }
        if(specificScenesList.contains(SpecificScenesEnum.UNDER_BOX.getCode())){
            Assert.isTrue(accountLabelService.isAccountIdsInLabels(Collections.singletonList(operator.getOperatorId()),
                    Arrays.asList(creativePositionConfig.getUnderBoxMarkId(), creativePositionConfig.getUnderBoxCmOrderId())),"没有起飞投框下权限的账号不能投框下");
        }
        int checkBox = specificScenesList.contains(SpecificScenesEnum.MSG_FLOW.getCode())
                || specificScenesList.contains(SpecificScenesEnum.PLAY_PAGE.getCode())
                || specificScenesList.contains(SpecificScenesEnum.STORY.getCode()) ? 1 : 0;
        final int[] radio = {0};
        Arrays.stream(SpecificScenesEnum.values())
                .forEach(scene -> {
                    if (scene.getType().equals(1) && specificScenesList.contains(scene.getCode())) {
                        radio[0]++;
                    }
                });
        Assert.isTrue(checkBox + radio[0] <= 1, "指定场景选择错误");
    }

    /**
     * 获取场景属性
     *
     * @param dto
     * @param unitId
     * @return
     * @throws ServiceException
     */
    public List<FlyProAttributeDto> getAllFlyProCreativeStyle(FlyProSelectScenesDto dto, Integer unitId,
                                                              Integer accountId) throws ServiceException {
        CpcUnitDto unit = cpcUnitService.loadCpcUnit(unitId);
        unitFillService.fillUnit(unit);
        FlyCampaignDetailDto flyCampaignDetailDto = flyCampaignService.getFlyCampaignById(unit.getCampaignId());
        CpcCampaignDto cpcCampaignDto = cpcCampaignService.loadCpcCampaignDto(unit.getCampaignId());

        if (!(PromotionPurposeType.DYNAMIC.getCode() == unit.getPromotionPurposeType()) && LaunchUnitService.isFlyNativeLandingPage(unit.getSalesType(), unit.getOcpcTarget())) {
            Assert.isTrue(Utils.isPositive(dto.getVideoId()), "请先选择视频");
            unit.setVideoId(dto.getVideoId());
        }
        //校验前端传的值
        this.checkFlyProSelectScenesDto(dto, PromotionPurposeType.getByCode(unit.getPromotionPurposeType()));
        //获取要投放的具体位置
        List<Integer> positions = this.generatePositions(dto,unit,accountId);
        //根据位置获取广告位组 + 模板
        Map<Integer,TemplateDto> slotGroupTemplateMap = this.getSlotGroupTemplateListByPositions(accountId, unit.getIsNoBid(), positions, unit.getOcpcTarget(),
                PromotionPurposeType.getByCode(unit.getPromotionPurposeType()),flyCampaignDetailDto.getRoomPromoteScenes());
        //根据模板列表构建虚拟模板组（相同尺寸作为一个模板组，同模板组属性取并集）
        return this.generateFlyProAttributeDtosByMap(slotGroupTemplateMap, cpcCampaignDto.getAdType());
    }

    // 给三连用的方法，
    // 针对story场景做特殊处理
    // 针对小卡做特殊处理
    public List<FlyProAttributeDto> getAllFlyProCreativeStyle4Mid(FlyProSelectScenesDto dto, CpcUnitDto unit,
                                                              Integer accountId) throws ServiceException {
        final boolean isManaged = Objects.equals(unit.getIsManaged(), 1);
        FlyCampaignDetailDto flyCampaignDetailDto = flyCampaignService.getFlyCampaignById(unit.getCampaignId());
        CpcCampaignDto cpcCampaignDto = cpcCampaignService.loadCpcCampaignDto(unit.getCampaignId());

        if (!(PromotionPurposeType.DYNAMIC.getCode() == unit.getPromotionPurposeType()) && LaunchUnitService.isFlyNativeLandingPage(unit.getSalesType(), unit.getOcpcTarget())) {
            Assert.isTrue(Utils.isPositive(dto.getVideoId()), "请先选择视频");
            unit.setVideoId(dto.getVideoId());
        }
        // 校验前端传的值
        this.checkFlyProSelectScenesDto(dto, PromotionPurposeType.getByCode(unit.getPromotionPurposeType()));

        // 获取要投放的具体位置
        List<Integer> positions = this.generatePositions(dto,unit,accountId);

        // 根据位置获取广告位组 + 模板
        Map<Integer,TemplateDto> slotGroupTemplateMap = this.getSlotGroupTemplateListByPositions(accountId, unit.getIsNoBid(), positions, unit.getOcpcTarget(),
                PromotionPurposeType.getByCode(unit.getPromotionPurposeType()),flyCampaignDetailDto.getRoomPromoteScenes());

        // 自动播放场景强制需要封面
        boolean foreNeedCover4Auto = false;

        // 根据模板列表构建虚拟模板组（相同尺寸作为一个模板组，同模板组属性取并集）
        return this.generateFlyProAttributeDtosByMap(accountId, unit.getUnitId(), slotGroupTemplateMap,cpcCampaignDto.getAdType(), foreNeedCover4Auto, isManaged,unit.getOcpcTarget());
    }

    public List<FlyProAttributeDto> getAllFlyProCreativeStyle4MidForManaged(FlyProSelectScenesDto dto, Integer accountId, Integer isNobid,
                                                                  Integer salesType, Integer ocpcTarget, Integer promotionPurposeType) throws ServiceException {
        // 校验前端传的值
        this.checkFlyProSelectScenesDto(dto, PromotionPurposeType.getByCode(promotionPurposeType));
        // 获取要投放的具体位置
        CpcUnitDto unit = CpcUnitDto.builder()
                .adpVersion(AdpVersion.MIDDLE.getKey())
                .isNoBid(isNobid)
                .videoId(dto.getVideoId())
                .salesType(salesType)
                .ocpcTarget(ocpcTarget)
                .promotionPurposeType(promotionPurposeType)
                .build();
        List<Integer> positions = this.generatePositions(dto, unit, accountId);

        // 根据位置获取广告位组 + 模板
        Map<Integer,TemplateDto> slotGroupTemplateMap = this.getSlotGroupTemplateListByPositions(accountId, isNobid,
                positions, ocpcTarget, PromotionPurposeType.getByCode(promotionPurposeType), null);

        // 根据模板列表构建虚拟模板组（相同尺寸作为一个模板组，同模板组属性取并集）
        return this.generateFlyProAttributeDtosByMap(accountId, 0, slotGroupTemplateMap, AdType.ALL, false,
                true, ocpcTarget);
    }


    private List<FlyProAttributeDto> generateFlyProAttributeDtosByMap(Map<Integer, TemplateDto> map, Integer adType) {
        List<TemplateDto> templates16_10 = new ArrayList<>();
        List<TemplateDto> templates16_9 = new ArrayList<>();
        List<TemplateDto> templates4_3 = new ArrayList<>();
        map.forEach((k, v) -> {
            // 是特定的小卡模板，记到templates4_3
            if (v.getTemplateId().equals(creativePositionConfig.getTemplateFlySlotSmallCard43())){
                templates4_3.add(v);
            } else {
                // 支持图片的，根据模板的图片宽高分组
                if (v.getIsSupportImage()) {
                    if (v.getImageHeight() * 16 == v.getImageWidth() * 9) {
                        templates16_9.add(v);
                    }
                    if (v.getImageHeight() * 16 == v.getImageWidth() * 10) {
                        templates16_10.add(v);
                    }
                }
                // 不支持图片
                else {
                    //story算在16：9里
                    templates16_9.add(v);
                }
            }
        });
        List<FlyProAttributeDto> ans = new ArrayList<>();
        Boolean has16_10 = false;
        Boolean has16_9 = false;
        Boolean has4_3 = false;
        if (!CollectionUtils.isEmpty(templates16_10)) {
            VirtualTemplateGroupDto virtualTemplateGroupDto16_10 = this.doGenerateVirtualTemplateGroup(templates16_10);
            FlyProAttributeDto flyProAttributeDto = this.virtualTemplateGroupDto2FlyProAttributeDto(virtualTemplateGroupDto16_10);
            flyProAttributeDto.setKey(SIZE_16_10.getCode());
            flyProAttributeDto.setValue(SIZE_16_10.getName());
            if(CampaignAdType.SEARCH.getCode().equals(adType)) {
                flyProAttributeDto.setLabel("小图");
            }else {
                flyProAttributeDto.setLabel("点击播放场景");
            }
            has16_10 = true;
            ans.add(flyProAttributeDto);
        }
        if (!CollectionUtils.isEmpty(templates16_9)) {
            VirtualTemplateGroupDto virtualTemplateGroupDto16_9 = this.doGenerateVirtualTemplateGroup(templates16_9);
            FlyProAttributeDto flyProAttributeDto = this.virtualTemplateGroupDto2FlyProAttributeDto(virtualTemplateGroupDto16_9);
            flyProAttributeDto.setKey(SIZE_16_9.getCode());
            flyProAttributeDto.setValue(SIZE_16_9.getName());
            flyProAttributeDto.setLabel("自动播放场景");
            has16_9 = true;
            ans.add(flyProAttributeDto);
        }
        if (!CollectionUtils.isEmpty(templates4_3)) {
            VirtualTemplateGroupDto virtualTemplateGroupDto16_10 = this.doGenerateVirtualTemplateGroup(templates4_3);
            FlyProAttributeDto flyProAttributeDto = this.virtualTemplateGroupDto2FlyProAttributeDto(virtualTemplateGroupDto16_10);
            flyProAttributeDto.setKey(SIZE_4_3.getCode());
            flyProAttributeDto.setValue(SIZE_4_3.getName());
            flyProAttributeDto.setLabel("点击播放场景");
            has4_3 = true;
            ans.add(flyProAttributeDto);
        }
        log.info("generateFlyProAttributeDtosByMap, has16_10:{}, has16_9:{}, has4_3:{}", has16_10, has16_9, has4_3);
        return ans;
    }

    /**
     * 根据模板列表构建虚拟模板组（相同尺寸作为一个模板组，同模板组属性取并集）
     *
     * @param map
     * @return
     */
    private List<FlyProAttributeDto> generateFlyProAttributeDtosByMap(Integer accountId, Integer unitId, Map<Integer, TemplateDto> map, Integer adType,
                                                                      boolean forceNeedCover4Auto, boolean isManaged,Integer ocpcTarget) {
        List<TemplateDto> templates16_10 = new ArrayList<>();
        List<TemplateDto> templates16_9 = new ArrayList<>();
        List<TemplateDto> templates4_3 = new ArrayList<>();
        map.forEach((k, v) -> {
            // 是特定的小卡模板，记到templates4_3
            if (v.getTemplateId().equals(creativePositionConfig.getTemplateFlySlotSmallCard43())){
                templates4_3.add(v);
            } else {
                // 支持图片的，根据模板的图片宽高分组
                if (v.getIsSupportImage()) {
                    if (v.getImageHeight() * 16 == v.getImageWidth() * 9) {
                        templates16_9.add(v);
                    }
                    if (v.getImageHeight() * 16 == v.getImageWidth() * 10) {
                        templates16_10.add(v);
                    }
                }
                // 不支持图片
                else {
                    // "oCPM框下链接点击"暗投story算在16:10
                    if(OcpcTargetEnum.UNDER_BOX_LINK_CLICK.getCode().equals(ocpcTarget) &&
                            creativePositionConfig.getTemplateDarkFlySlotStory().equals(v.getTemplateId())){
                        templates16_10.add(v);
                    } else{
                        // 其它story算在16：9里
                        templates16_9.add(v);
                    }
                }
            }
        });
        List<FlyProAttributeDto> ans = new ArrayList<>();
        boolean has16_10 = false;
        boolean has16_9 = false;
        boolean has4_3 = false;
        //非常特殊的逻辑
        //【三连商业起飞】自动播放点击播放创意合并(即强行16:9的模板设置16:10)
        //https://www.tapd.bilibili.co/********/prong/stories/view/11********002930136

        List<Integer> coverTypes = new ArrayList<>();
        if (Utils.isPositive(unitId)) {
            coverTypes = adCoreBqf
                    .select(lauCreativeFlyExtInfo.banner4CoverType)
                    .from(lauCreativeFlyExtInfo)
                    .where(lauCreativeFlyExtInfo.unitId.eq(unitId))
                    .fetch();
        }
        final boolean isSceneMerge;
        //新创意 看账号标签 返回模板信息
        if (CollectionUtils.isEmpty(coverTypes)) {
            //isSceneMerge = accountIdInLabel(accountId, sceneMergeConfig.getSceneMergeLabelId());
            isSceneMerge = true;
        //旧创意看coverTypes是否全部是SCENE_MERGE
        } else {
            isSceneMerge = coverTypes
                    .stream()
                    .distinct()
                    .allMatch(SCENE_MERGE.getCode()::equals);
        }

        //场景合并情况下，16:10 和 4:3互斥
        if (isSceneMerge) {
            if (!CollectionUtils.isEmpty(templates16_9)) {
                templates16_9.forEach(templateDto -> {
                    templateDto.setImageWidth(960);
                    templateDto.setImageHeight(600);
                });
            }
            final List<TemplateDto> sceneMergeTemplates = Stream.of(templates16_9, templates16_10)
                    .flatMap(List::stream)
                    .collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(sceneMergeTemplates)) {
                final VirtualTemplateGroupDto sceneMergeVirtualTemplateGroupDto = this.doGenerateVirtualTemplateGroup(sceneMergeTemplates);
                final FlyProAttributeDto flyProAttributeDto = this.virtualTemplateGroupDto2FlyProAttributeDto(sceneMergeVirtualTemplateGroupDto);
                flyProAttributeDto.setKey(SCENE_MERGE.getCode());
                flyProAttributeDto.setValue(SCENE_MERGE.getName());
                if (CampaignAdType.SEARCH.getCode().equals(adType)) {
                    flyProAttributeDto.setLabel("小图");
                } else {
                    flyProAttributeDto.setLabel(SCENE_MERGE.getName());
                }
                has16_10 = true;
                // 场景合并的attribute强制支持封面
                if (forceNeedCover4Auto) {
                    flyProAttributeDto.set_support_image(true);
                }
                ans.add(flyProAttributeDto);
            } else if (!CollectionUtils.isEmpty(templates4_3)) {
                VirtualTemplateGroupDto virtualTemplateGroupDto16_10 = this.doGenerateVirtualTemplateGroup(templates4_3);
                FlyProAttributeDto flyProAttributeDto = this.virtualTemplateGroupDto2FlyProAttributeDto(virtualTemplateGroupDto16_10);
                flyProAttributeDto.setKey(SIZE_4_3.getCode());
                flyProAttributeDto.setValue(SIZE_4_3.getName());
                has4_3 = true;
                ans.add(flyProAttributeDto);
            }
            log.info("generateFlyProAttributeDtosByMap, has16_10:{}, has16_9, has4_3:{}", has16_10, has16_9, has4_3);
        } else {
            if (!CollectionUtils.isEmpty(templates16_10)) {
                VirtualTemplateGroupDto virtualTemplateGroupDto16_10 = this.doGenerateVirtualTemplateGroup(templates16_10);
                FlyProAttributeDto flyProAttributeDto = this.virtualTemplateGroupDto2FlyProAttributeDto(virtualTemplateGroupDto16_10);
                flyProAttributeDto.setKey(SIZE_16_10.getCode());
                flyProAttributeDto.setValue(SIZE_16_10.getName());
                if (CampaignAdType.SEARCH.getCode().equals(adType)) {
                    flyProAttributeDto.setLabel("小图");
                } else {
                    flyProAttributeDto.setLabel("点击播放场景");
                }
                has16_10 = true;
                ans.add(flyProAttributeDto);
            }
            if (!CollectionUtils.isEmpty(templates16_9)) {
                VirtualTemplateGroupDto virtualTemplateGroupDto16_9 = this.doGenerateVirtualTemplateGroup(templates16_9);
                FlyProAttributeDto flyProAttributeDto = this.virtualTemplateGroupDto2FlyProAttributeDto(virtualTemplateGroupDto16_9);
                flyProAttributeDto.setKey(SIZE_16_9.getCode());
                flyProAttributeDto.setValue(SIZE_16_9.getName());
                flyProAttributeDto.setLabel("自动播放场景");
                has16_9 = true;
                // 自动播放场景的attribute强制支持封面
                if (forceNeedCover4Auto) {
                    flyProAttributeDto.set_support_image(true);
                    flyProAttributeDto.setImage_width(960);
                    flyProAttributeDto.setImage_height(540);
                }
                ans.add(flyProAttributeDto);
            }
            if (!CollectionUtils.isEmpty(templates4_3)) {
                VirtualTemplateGroupDto virtualTemplateGroupDto16_10 = this.doGenerateVirtualTemplateGroup(templates4_3);
                FlyProAttributeDto flyProAttributeDto = this.virtualTemplateGroupDto2FlyProAttributeDto(virtualTemplateGroupDto16_10);
                flyProAttributeDto.setKey(SIZE_4_3.getCode());
                flyProAttributeDto.setValue(SIZE_4_3.getName());
                flyProAttributeDto.setLabel("点击播放场景");
                has4_3 = true;
                ans.add(flyProAttributeDto);
            }
            log.info("generateFlyProAttributeDtosByMap, has16_10:{}, has16_9, has4_3:{}", has16_10, has16_9, has4_3);
        }
        return ans;
    }

    private FlyProAttributeDto virtualTemplateGroupDto2FlyProAttributeDto(VirtualTemplateGroupDto dto) {
        return FlyProAttributeDto.builder()
                ._support_image(dto.getIsSupportImage())
                .image_height(dto.getImageHeight())
                .image_width(dto.getImageWidth())
                .image_kb_limit(dto.getImageKbLimit())
                ._support_gif(dto.getIsSupportGif())
                .gif_height(dto.getIsSupportGif() ? 200 : 0)
                .gif_width(dto.getIsSupportGif() ? 320 : 0)
                .gif_kb_limit(dto.getIsSupportGif() ? 300 : 0)
                ._support_title(dto.getIsFillTitle())
                .title_min_length(dto.getTitleMinLength())
                .title_max_length(dto.getTitleMaxLength())
                ._support_desc(dto.getIsFillDesc())
                .desc_min_length(dto.getDescMinLength())
                .desc_max_length(dto.getDescMaxLength())
                .build();
    }

    public VirtualTemplateGroupDto flyProAttributeDto2virtualTemplateGroupDto(FlyProAttributeDto dto) {
        if (dto == null) {
            return null;
        }

        return VirtualTemplateGroupDto.builder()
                .isSupportImage(dto.get_support_image())
                .isSupportGif(dto.get_support_gif())
                .isFillTitle(dto.get_support_title())
                .titleMinLength(dto.getTitle_min_length())
                .titleMaxLength(dto.getTitle_max_length())
                .isFillDesc(dto.get_support_desc())
                .descMinLength(dto.getDesc_min_length())
                .descMaxLength(dto.getDesc_max_length())
                .build();
    }

    /**
     * 根据推广目的,场景,优化目标等获取配置的 slotGroupId
     * 其实就是不同推广目的下的不同场景配置了不同的 slotGroupId(写死了代码)
     *
     * @param position
     * @param ocpcTarget
     * @param ppt
     * @param roomPromoteScenes
     * @return
     */
    private List<Integer> position2SlotGroupId(boolean isPersonalFly, Integer position,Integer ocpcTarget,PromotionPurposeType ppt,Integer roomPromoteScenes) {
        SpecificScenesEnum specificScenesEnum = SpecificScenesEnum.getByCode(position);
        List<Integer> ans = new ArrayList<>();
        if (Objects.equals(specificScenesEnum, SpecificScenesEnum.STORY_HIDDEN_EXP)) {
            ans.add(creativePositionConfig.getStoryHiddenExpSlotGroupId());
            return ans;
        }
        // 商品
        if (ppt == PromotionPurposeType.GOODS) {
            switch (specificScenesEnum) {
                case MSG_FLOW:
                    //
                    if (Objects.equals(OcpcTargetEnum.CLICK.getCode(), ocpcTarget)) {
                        ans.add(creativePositionConfig.getGoodsClickMsgFlowSlotGroupId());
                    } else {
                        ans.add(creativePositionConfig.getGoodsMsgFlowSlotGroupId());
                    }
                    break;
                case PLAY_PAGE:
                    ans.add(creativePositionConfig.getGoodsPlayPageSlotGroupId());
                    break;
                case UNDER_BOX:
                    ans.add(creativePositionConfig.getGoodsUnderBoxSlotGroupId());
                    break;
            }
        }
        // 直播带货
        if (ppt == PromotionPurposeType.GOODS_LIVE) {
            switch (specificScenesEnum) {
                case MSG_FLOW:
                    if (isPersonalFly) {
                        if (PERSON_UP_LIVE_OCPC_TARGET.contains(ocpcTarget)){
                            ans.add(creativePositionConfig.getPersonalUpLiveEntryMsgFlowSlotGroupId());
                        }
                    } else {
                        ans.add(creativePositionConfig.getGoodsLiveMsgFlowSlotGroupId());
                    }
                    break;
                case STORY:
                    if (isPersonalFly) {
                        if (PERSON_UP_LIVE_OCPC_TARGET.contains(ocpcTarget)) {
                            ans.add(creativePositionConfig.getPersonalUpLiveEntryStorySlotGroupId());
                        }
                    } else {
                        ans.add(creativePositionConfig.getGoodsLiveStorySlotGroupId());
                    }
                    break;
                case UNDER_BOX:
                    if (isPersonalFly) {
                        ans.add(creativePositionConfig.getPersonalGoodsLiveUnderBoxSlotGroupId());
                    } else {
                        ans.add(creativePositionConfig.getGoodsLiveUnderBoxSlotGroupId());
                    }
                    break;
                case PLAY_PAGE:
                    if (isPersonalFly) {
//                        ans.add(creativePositionConfig.getPersonalGoodsLivePlayPageSlotGroupId());
                    } else {
                        ans.add(creativePositionConfig.getGoodsLivePlayPageSlotGroupId());
                    }
                    break;
                case SEARCH:
                    ans.add(creativePositionConfig.getGoodsLiveSearchSlotGroupId());
                    break;
                case MSG_FLOW_BIG_CARD:
                    if (isPersonalFly) {
                        ans.add(creativePositionConfig.getPersonalGoodsLiveBigCardSlotGroupId());
                    }
                    break;
                case IPAD_RECOMMEND:
                    if (isPersonalFly) {
                        ans.add(creativePositionConfig.getPersonalGoodsLiveIpadRecommendSlotGroupId());
                    }
                    break;
                default:
                    break;
            }
        }
        // 稿件内容
        if (ppt == PromotionPurposeType.ARCHIVE_CONTENT) {
            switch (specificScenesEnum) {
                // 信息流
                case MSG_FLOW:
                    if (isPersonalFly) {
                        if (Objects.equals(OcpcTargetEnum.VIDEO_PLAY.getCode(), ocpcTarget)) {
                            ans.add(creativePositionConfig.getPersonalUpVideoPlayMsgFlowSlotGroupId());
                        }
                        if (Objects.equals(OcpcTargetEnum.USER_FOLLOW.getCode(), ocpcTarget)) {
                            ans.add(creativePositionConfig.getPersonalUpUserFollowMsgFlowSlotGroupId());
                        }
                        if (Objects.equals(OcpcTargetEnum.COMMENT_CLICK.getCode(), ocpcTarget)) {
                            ans.add(creativePositionConfig.getPersonalUpCommentClickMsgFlowSlotGroupId());
                        }
                        if (Objects.equals(OcpcTargetEnum.FORM_PAID.getCode(), ocpcTarget)) {
                            ans.add(creativePositionConfig.getPersonalUpFormPaidMsgFlowSlotGroupId());
                        }
                        if (Objects.equals(OcpcTargetEnum.VIDEO_ENGAGE.getCode(), ocpcTarget)) {
                            ans.add(creativePositionConfig.getPersonalUpVideoEngageMsgFlowSlotGroupId());
                        }
                        if (Objects.equals(OcpcTargetEnum.GOODS_TRANSACTION.getCode(), ocpcTarget)) {
                            ans.add(creativePositionConfig.getPersonalUpVideoEngageMsgFlowSlotGroupId());
                        }

                    } else {
                        //oCPM框下链接点击投小卡，特殊广告位组
                        if (OcpcTargetEnum.UNDER_BOX_LINK_CLICK.getCode().equals(ocpcTarget)) {
                            ans.add(creativePositionConfig.getFlySlotArchiveSmallUnderBox());
                        } else {
                            ans.add(creativePositionConfig.getFlySlotArchiveSmall());
                        }
                    }
                    break;
                case PLAY_PAGE:
                    if (isPersonalFly) {
                        ans.add(creativePositionConfig.getPersonalArchiveContentPlayPageSlotGroupId());
                    } else {
                        //oCPM框下链接点击投播放页，特殊广告位组
                        if (OcpcTargetEnum.UNDER_BOX_LINK_CLICK.getCode().equals(ocpcTarget)) {
                            ans.add(creativePositionConfig.getFlySlotArchivePlayUnderBox());
                        } else {
                            ans.add(creativePositionConfig.getFlySlotArchivePlay());
                        }

                        // pc客户端资源位额外添加
                        if (OcpcTargetEnum.VIDEO_PLAY.getCode().equals(ocpcTarget) || OcpcTargetEnum.USER_FOLLOW.getCode().equals(ocpcTarget)) {
                            ans.add(creativePositionConfig.getPcAppPcPlaySlotGroupId());
                        }
                    }
                    break;
                case MSG_FLOW_BIG_CARD:
                    if (isPersonalFly) {
                        ans.add(creativePositionConfig.getPersonalArchiveContentBigCardSlotGroupId());
                    } else {
                        ans.add(creativePositionConfig.getFlySlotArchiveBig());
                    }
                    break;
                case DYNAMIC_FLOW:
                    if (isPersonalFly){
                        if (Objects.equals(OcpcTargetEnum.VIDEO_PLAY.getCode(), ocpcTarget)) {
                            ans.add(creativePositionConfig.getPersonalDynamicFlowSlotGroupId());
                        }
                        if (Objects.equals(OcpcTargetEnum.COMMENT_CLICK.getCode(), ocpcTarget)) {
                            ans.add(creativePositionConfig.getPersonalDynamicFlowSlotGroupId());
                        }
                        if (Objects.equals(OcpcTargetEnum.VIDEO_ENGAGE.getCode(), ocpcTarget)) {
                            ans.add(creativePositionConfig.getPersonalDynamicFlowSlotGroupId());
                        }
                    }else {
                        ans.add(creativePositionConfig.getFlySlotArchiveDynamic());
                    }

                    break;
                case STORY:
                    if (isPersonalFly) {
                        if (Objects.equals(OcpcTargetEnum.VIDEO_PLAY.getCode(), ocpcTarget)) {
                            ans.add(creativePositionConfig.getPersonalUpVideoPlayStorySlotGroupId());
                        }
                        if (Objects.equals(OcpcTargetEnum.USER_FOLLOW.getCode(), ocpcTarget)) {
                            ans.add(creativePositionConfig.getPersonalUpUserFollowStorySlotGroupId());
                        }
                        if (Objects.equals(OcpcTargetEnum.COMMENT_CLICK.getCode(), ocpcTarget)) {
                            ans.add(creativePositionConfig.getPersonalUpCommentClickStorySlotGroupId());
                        }
                        if (Objects.equals(OcpcTargetEnum.VIDEO_ENGAGE.getCode(), ocpcTarget)) {
                            ans.add(creativePositionConfig.getPersonalUpVideoEngageStoryGroupId());
                        }
                        if (Objects.equals(OcpcTargetEnum.GOODS_TRANSACTION.getCode(), ocpcTarget)) {
                            ans.add(creativePositionConfig.getPersonalUpCommentClickStorySlotGroupId());
                        }
                    } else {
                        ans.add(creativePositionConfig.getFlySlotArchiveStory());
                    }
                    break;
                case IPAD_RECOMMEND:
                    if (isPersonalFly) {
                        ans.add(creativePositionConfig.getPersonalArchiveContentIpadRecommendSlotGroupId());
                    } else {
                        ans.add(creativePositionConfig.getFlySlotArchiveIPad());
                    }
                    break;
                case PC_INDEX:
                    if (isPersonalFly){
                        if (Objects.equals(OcpcTargetEnum.VIDEO_PLAY.getCode(), ocpcTarget)) {
                            ans.add(creativePositionConfig.getPersonalArchivePcIndexOneSlotGroupId());
                            ans.add(creativePositionConfig.getPersonalArchivePcIndexTwoSlotGroupId());
                        }
                        if (Objects.equals(OcpcTargetEnum.USER_FOLLOW.getCode(), ocpcTarget)) {
                            ans.add(creativePositionConfig.getPersonalArchivePcIndexOneSlotGroupId());
                            ans.add(creativePositionConfig.getPersonalArchivePcIndexTwoSlotGroupId());
                        }
                        if (Objects.equals(OcpcTargetEnum.VIDEO_ENGAGE.getCode(), ocpcTarget)) {
                            ans.add(creativePositionConfig.getPersonalArchivePcIndexOneSlotGroupId());
                            ans.add(creativePositionConfig.getPersonalArchivePcIndexTwoSlotGroupId());
                        }
                    }else {
                        ans.add(creativePositionConfig.getFlySlotArchivePcIndex());
                    }


                    // pc客户端资源位额外添加
                    if (OcpcTargetEnum.VIDEO_PLAY.getCode().equals(ocpcTarget) || OcpcTargetEnum.USER_FOLLOW.getCode().equals(ocpcTarget)) {
                        ans.add(creativePositionConfig.getPcAppPcIndexSlotGroupId());
                    }
                    break;
                case PC_PLAY:
                    if (isPersonalFly) {
                        if (Objects.equals(OcpcTargetEnum.VIDEO_PLAY.getCode(), ocpcTarget)) {
                            ans.add(creativePositionConfig.getPersonalArchivePcPlayOneSlotGroupId());
                            ans.add(creativePositionConfig.getPersonalArchivePcPlayTwoSlotGroupId());
                        }
                        if (Objects.equals(OcpcTargetEnum.USER_FOLLOW.getCode(), ocpcTarget)) {
                            ans.add(creativePositionConfig.getPersonalArchivePcPlayOneSlotGroupId());
                            ans.add(creativePositionConfig.getPersonalArchivePcPlayTwoSlotGroupId());
                        }
                        if (Objects.equals(OcpcTargetEnum.VIDEO_ENGAGE.getCode(), ocpcTarget)) {
                            ans.add(creativePositionConfig.getPersonalArchivePcPlayOneSlotGroupId());
                            ans.add(creativePositionConfig.getPersonalArchivePcPlayTwoSlotGroupId());
                        }
                    }else {
                        ans.add(creativePositionConfig.getFlySlotArchivePcPlay());
                    }

                    break;
                case UNDER_BOX:
                    if (isPersonalFly) {
                        ans.add(creativePositionConfig.getPersonalArchiveContentUnderBoxSlotGroupId());
                    } else {
                        ans.add(creativePositionConfig.getFlySlotArchiveUnderBox());
                    }
                    break;
                case TV_INLINE:
                    // tv-inline 广告位组
                    ans.add(creativePositionConfig.getFlySlotArchiveTvInline());
                    break;
                case TV_HIDDEN:
                    ans.add(daihuoHiddenOttSlotGroupId);
                    break;
                case SEARCH:
                    ans.add(creativePositionConfig.getFlySlotSearch());
                    break;
                case SEARCH_AUTO:
                    ans.add(creativePositionConfig.getFlySlotSearchAuto());
                    break;
                case PC_SEARCH:
                    if (isPersonalFly){

                    }else {
                        ans.add(creativePositionConfig.getFlyPcSlotSearch());
                    }

                    break;
                case CHOOSE_SEARCH:
                    ans.add(creativePositionConfig.getFlySlotChooseSearch());
                    break;
                case MSG_FLOW_ANDROID_PAD:
                    ans.add(creativePositionConfig.getMsgFlowAndroidPadSlotGroupId());
                    break;
                case PLAY_PAGE_ANDROID_PAD:
                    ans.add(creativePositionConfig.getPlayPageAndroidPadSlotGroupId());
                    break;
//                case UNDERFRAME_HIDDEN:
//                    ans.add(creativePositionConfig.getUnderFrameHiddenSlotGroupId());
//                    break;
                case MSG_FLOW_4_3:
                    ans.add(creativePositionConfig.getSmallCard43SlotGroupId());
                    break;
                case STORY_HIDDEN:
                    ans.add(creativePositionConfig.getStoryHiddenSlotGroupIdForOcpmUnderBox());
                    break;
                case IPAD_SEARCH:
                    ans.add(creativePositionConfig.getFlyIPadSearchSlotGroupId());
                    break;
                case DYNAMIC_FEEDS_HIDDEN:
                    ans.add(creativePositionConfig.getDynamicFeedsHiddenSlotGroupId());
                    break;
                default:
                    break;
            }
        }

        // 直播间
        if (ppt == PromotionPurposeType.LIVE_ROOM) {
            switch (specificScenesEnum) {
                case MSG_FLOW:
                    ans.add(creativePositionConfig.getFlySlotLiveSmall());
                    break;
                case MSG_FLOW_BIG_CARD:
                    if(RoomPromoteScenesEnum.RESERVE.getCode().equals(roomPromoteScenes)) {
                        ans.add(creativePositionConfig.getFlySlotLiveReserve());
                    }else{
                        ans.add(creativePositionConfig.getFlySlotLiveBig());
                    }
                    break;
                case MSG_FLOW_ANDROID_PAD:
                    ans.add(creativePositionConfig.getMsgFlowAndroidPadSlotGroupId());
                    break;
                case PLAY_PAGE_ANDROID_PAD:
                    ans.add(creativePositionConfig.getPlayPageAndroidPadSlotGroupId());
                default:
                    break;
            }
        }

        // 动态
        if (ppt == PromotionPurposeType.DYNAMIC) {
            switch (specificScenesEnum) {
                case MSG_FLOW:
                    // 动态-直播预约优化目标，广告位组 xx 模板:553
                    if (OcpcTargetEnum.LIVE_RESERVE.getCode().equals(ocpcTarget)) {
                        ans.add(creativePositionConfig.getFlySlotGroupDynamicLiveReserveSmall());
                    } else {
                        ans.add(creativePositionConfig.getFlySlotDynamicSmall());
                    }
                    break;
                case CHOOSE_SEARCH:
                    ans.add(creativePositionConfig.getFlySlotChooseSearch());
                    break;
            }
        }
        // 活动
        if (ppt == PromotionPurposeType.ACTIVITY) {
            switch (specificScenesEnum) {
                case MSG_FLOW:
                    ans.add(creativePositionConfig.getFlySlotActivitySmall());
                    break;
                case PLAY_PAGE:
                    ans.add(creativePositionConfig.getFlySlotActivityPlay());
                    break;
                case CHOOSE_SEARCH:
                    ans.add(creativePositionConfig.getFlySlotChooseSearch());
                    break;
            }
        }
        // OGV
        if (ppt == PromotionPurposeType.OGV) {
            switch (specificScenesEnum) {
                // 信息流小卡
                case MSG_FLOW:
                    ans.add(creativePositionConfig.getFlySlotOgvSmall());
                    break;
                case MSG_FLOW_BIG_CARD:
                    ans.add(creativePositionConfig.getFlySlotOgvBig());
                    break;
                case CHOOSE_SEARCH:
                    ans.add(creativePositionConfig.getFlySlotChooseSearch());
                    break;
                case TV_INLINE:
                    ans.add(creativePositionConfig.getFlySlotOgvTvLine());
                    break;
            }
        }
        return ans;
    }

    /**
     * 根据场景相关参数获取 Map<slotGroupId, TemplateDto>
     * 一个 slotGroupId 有多个模板的，取第一个
     *
     * @param positions 场景位置列表，其实就是场景 SpecificScenesEnum
     * @param ocpcTarget
     * @param ppt
     * @param roomPromoteScenes
     * @return
     */
    public Map<Integer,TemplateDto>  getSlotGroupTemplateListByPositions(Integer accountId,
                                                                         Integer isNoBid,
                                                                         List<Integer> positions,
                                                                         Integer ocpcTarget,
                                                                         PromotionPurposeType ppt,
                                                                         Integer roomPromoteScenes){
        Map<Integer, TemplateDto> map = new HashMap<>();
        // 所有场景的 slotGroupIds
        List<Integer> slotGroupIds = new ArrayList<>();
        if (CollectionUtils.isEmpty(positions)){
            return map;
        }

        final boolean isPersonalFly = launchAccountService.isPersonalFly(accountId);
        boolean hasStoryHiddenExp = false;
        // 根据场景获取 slotGroupId，并收集所有场景的 slotGroupIds
        for (Integer position : positions) {
            // 根据推广目的,场景位置枚举 SpecificScenesEnum ,优化目标等获取配置的 slotGroupId
            final List<Integer> eachSlotGroupIdList = this.position2SlotGroupId(isPersonalFly, position, ocpcTarget, ppt, roomPromoteScenes); // 场景和点位的对照关系 内容起飞和商业起飞用的同一套
            for (Integer slotGroupId : eachSlotGroupIdList) {
                if (!Utils.isPositive(slotGroupId)) {
                    continue;
                }
                // 根据场景获取 slotGroupId
                if (!isPersonalFly && Objects.equals(slotGroupId, creativePositionConfig.getStoryHiddenExpSlotGroupId())) {
                    hasStoryHiddenExp = true;
                } else {
                    slotGroupIds.add(slotGroupId);
                }
            }
        }

        // 根据 slotGroupIds 和推广目的查询模板列表
        List<ResSlotGroupTemplateMappingDto> resSlotGroupTemplateMapping = resSlotGroupService
                .querySlotGroupTemplateMappingInSlotGroupIds(QueryTemplateLaunchTypeMappingDto.builder()
                        .slotGroupIds(slotGroupIds)
                        .promotionPurposeType(ppt.getCode())
                        .isNobid(isNoBid.equals(1) ? 1 : null)
                        .build());

        // 将 slotGroupIds 的每个 slotGroupId 的模板列表取第一个模板转成 Map<slotGroupId, TemplateDto>
        resSlotGroupTemplateMapping.stream().forEach(o -> {
            TemplateDto t = o.getTemplates().get(0);
            map.put(o.getSlotGroupId(), t);
        });
        if (hasStoryHiddenExp) {
            final TemplateDto templateDto = templateService.load(creativePositionConfig.getStoryHiddenExpFlyTemplateId());
            if (Objects.nonNull(templateDto)) {
                map.put(creativePositionConfig.getStoryHiddenExpSlotGroupId(), templateDto);
            }
        }

        //个人起飞暗投搜素，https://doc.weixin.qq.com/doc/w3_ATYAmwauAH4LCpVS1e4RqmAqT3n88?scode=ANYAEAdoABEDRqTgp6ARoASwZIAC8
        Integer personalSearchSlotGroupId = creativePositionConfig.getPersonalSearchSlotGroupId();
        Integer personalPcSearchSlotGroupId = creativePositionConfig.getPersonalPcSearchSlotGroupId();
        Integer personalSearchTemplateId = creativePositionConfig.getPersonalSearchTemplateId();
        Integer personalPcSearchTemplateId = creativePositionConfig.getPersonalPcSearchTemplateId();

        if (isPersonalFly) {
            ArrayList<Integer> personalSearchIds = Lists.newArrayList(personalSearchTemplateId, personalPcSearchTemplateId);
            Map<Integer, TemplateDto> templateMapByTemplateIds = templateService.getTemplateMapByTemplateIds(personalSearchIds);
            TemplateDto searchTemps = templateMapByTemplateIds.getOrDefault(personalSearchTemplateId, null);
            TemplateDto pcSearchTemps = templateMapByTemplateIds.getOrDefault(personalPcSearchTemplateId, null);
            switch (ppt){
                case GOODS_LIVE:
                    if(Objects.nonNull(searchTemps)){
                        map.put(personalSearchSlotGroupId, searchTemps);
                    }
                    break;
                case ARCHIVE_CONTENT:
                    if(Objects.nonNull(searchTemps)){
                        map.put(personalSearchSlotGroupId, searchTemps);
                    }
                    if(Objects.nonNull(pcSearchTemps)){
                        map.put(personalPcSearchSlotGroupId, pcSearchTemps);
                    }
                    break;
            }
        }

        return map;
    }

    private List<Integer> generateCommonPosition() {
        List<Integer> ans = new ArrayList<>();
        ans.add(SpecificScenesEnum.MSG_FLOW.getCode());
        ans.add(SpecificScenesEnum.PLAY_PAGE.getCode());
        return ans;
    }

    private List<Integer> generateSmallCardPosition() {
        List<Integer> ans = new ArrayList<>();
        ans.add(SpecificScenesEnum.MSG_FLOW.getCode());
        return ans;
    }

    private List<Integer> generateGoodsPosition(Integer ocpmTarget) {
        // 如果是 【点击】优化目标，则是tab3内广投流 只允许信息流小卡
        //https://www.tapd.bilibili.co/********/prong/stories/view/11********003052740
        List<Integer> ans = new ArrayList<>();
        ans.add(SpecificScenesEnum.MSG_FLOW.getCode());

        // 改成: 点击与非点击优化目标都有这些
        ans.add(SpecificScenesEnum.PLAY_PAGE.getCode());
        ans.add(SpecificScenesEnum.UNDER_BOX.getCode());
        return ans;
    }

    private List<Integer> generateSmallAndBigCardPosition(Integer accountId) {
        List<Integer> ans = new ArrayList<>();
        ans.add(SpecificScenesEnum.MSG_FLOW.getCode());
        ans.add(SpecificScenesEnum.MSG_FLOW_BIG_CARD.getCode());
        if (this.accountIdInLabel(accountId, creativePositionConfig.getAndroidPadScenesLabelId())) {
            ans.add(SpecificScenesEnum.MSG_FLOW_ANDROID_PAD.getCode());
            ans.add(SpecificScenesEnum.PLAY_PAGE_ANDROID_PAD.getCode());
        }
        return ans;
    }

    private List<Integer> generateArchive4MobileScene(FlyProSelectScenesDto dto, CpcUnitDto unit, Integer accountId,
                                                      PromotionPurposeType promotionPurposeType, List<Integer> labelIds){
        // 选了搜索明投场景
        if(dto.getMultiSpecificScene().contains(SpecificScenesEnum.CHOOSE_SEARCH.getCode())){
            return dto.getMultiSpecificScene();
        }
        boolean isMiddleAd = AdpVersion.isMiddle(unit.getAdpVersion());
        List<Integer> ans = new ArrayList<>(dto.getMultiSpecificScene());

        // 起飞直播间，指定场景支持
        if (PromotionPurposeType.GOODS_LIVE == promotionPurposeType) {
            if (SalesType.CPM.getCode() == unit.getSalesType()) {
                if (isMiddleAd) {
                    // 起飞直播间暗投搜索
                    if (!CollectionUtils.isEmpty(labelIds) && labelIds.contains(creativePositionConfig.getGoodsLiveSearchPreferScenesLabelId())) {
                        ans.add(SpecificScenesEnum.SEARCH.getCode());
                    }
                }
            }
        }

        // 投稿内容
        if (PromotionPurposeType.ARCHIVE_CONTENT == promotionPurposeType) {
            if (SalesType.CPM.getCode() == unit.getSalesType()) {
                Integer ocpcTarget = unit.getOcpcTarget();
                // CPM || 播放 || 涨粉 || 评论点击
                if (Integer.valueOf(0).equals(ocpcTarget) ||
                        OcpcTargetEnum.VIDEO_PLAY.getCode().equals(ocpcTarget) ||
                        OcpcTargetEnum.USER_FOLLOW.getCode().equals(ocpcTarget) ||
                        OcpcTargetEnum.COMMENT_CLICK.getCode().equals(ocpcTarget)){
                    this.judgeMobileScenceDarkSearch(false,isMiddleAd,accountId,ans,ocpcTarget,labelIds);
                } else {
                    // 除了 cpm、播放、涨粉、评论点击
                    this.judgeMobileScenceDarkSearch(true,isMiddleAd,accountId,ans,ocpcTarget,labelIds);
                }
                // 内容起飞专用
                if (!isMiddleAd && (
                            Integer.valueOf(0).equals(ocpcTarget) ||
                            OcpcTargetEnum.VIDEO_PLAY.getCode().equals(ocpcTarget) ||
                            OcpcTargetEnum.USER_FOLLOW.getCode().equals(ocpcTarget) ||
                            OcpcTargetEnum.FIRST_COMMENT_COPY.getCode().equals(ocpcTarget)
                        )
                ) {
                    Integer whiteLabelId = searchPositionConfig.getFlyContentSearchWhiteLabel();
                    boolean isWhite = whiteLabelId == 0 || this.accountIdInLabel(accountId, whiteLabelId);
                    if (isWhite && SpecificScenesEnum.getClickSceneInt().stream().anyMatch(ans::contains)) {
                        ans.add(SpecificScenesEnum.SEARCH.getCode());
                    }
                    if (isWhite && SpecificScenesEnum.getAutoSceneInt().stream().anyMatch(ans::contains)) {
                        ans.add(SpecificScenesEnum.SEARCH_AUTO.getCode());
                    }

                }
            }
        }
        //带货动态tab暗投 https://www.tapd.cn/********/prong/stories/view/11********004158849
        final boolean addDynamicFeedsHidden = addDynamicFeedsHidden(labelIds, promotionPurposeType.getCode(), unit.getOcpcTarget(), ans);
        if (addDynamicFeedsHidden) {
            ans.add(SpecificScenesEnum.DYNAMIC_FEEDS_HIDDEN.getCode());
        }
        if (ans.contains(SpecificScenesEnum.MSG_FLOW.getCode()) && !ans.contains(SpecificScenesEnum.STORY.getCode()) && NumberUtils.isPositive(unit.getVideoId())) {
            ans.add(SpecificScenesEnum.STORY_HIDDEN_EXP.getCode());
        }
        return ans;
    }

    private boolean addDynamicFeedsHidden(List<Integer> labelIds, Integer promotionPurposeType, Integer ocpxTarget, List<Integer> ans) {
        final boolean accountValid = labelIds.contains(accountConfig.getDynamicFeedsHiddenLabelId());
        final boolean promotionPurposeTypeValid = Objects.equals(promotionPurposeType, PromotionPurposeType.ARCHIVE_CONTENT.getCode());
        final List<Integer> dynamicFeedsHiddenOcpxTargets = Arrays.asList(
            OcpcTargetEnum.GOODS_TRANSACTION.getCode(),
            OcpcTargetEnum.VIDEO_PLAY.getCode(),
            OcpcTargetEnum.COMMENT_CLICK.getCode()
        );
        final boolean ocpxTargetValid = dynamicFeedsHiddenOcpxTargets.contains(ocpxTarget);
        final boolean sceneValid = ans.contains(SpecificScenesEnum.MSG_FLOW.getCode()) || ans.contains(SpecificScenesEnum.MSG_FLOW_BIG_CARD.getCode());
        return promotionPurposeTypeValid && ocpxTargetValid && accountValid && sceneValid;
    }

    // 处理指定场景的暗投搜索位
    private void judgeMobileScenceDarkSearch(boolean otherOcpm,boolean isMiddleAd,Integer accountId,List<Integer> ans,
                                             Integer ocpcTarget,List<Integer> labelIds){
        // 三连指定场景，且在4比3小卡白名单
        if(isMiddleAd && this.accountIdInSmallCard43WhiteList(labelIds)){
            List<Integer> positions16_10 = ans.stream()
                    .filter(o->SpecificScenesEnum.getClickSceneInt().contains(o))
                    .collect(Collectors.toList());
            // 点击播放场景只选了小卡
            if(positions16_10.size() == 1 && positions16_10.contains(MSG_FLOW.getCode())){
                // 转换position
                ans.set(0,MSG_FLOW_4_3.getCode());
            }
        }
        Integer labelId;
        if (otherOcpm) {
            labelId = creativePositionConfig.getSearchOtherOcpmLabelId();
        } else {
            labelId = creativePositionConfig.getSearchMobileScenesLabelId();
        }
        // 选到了点击播放场景(如果有了MSG_FLOW_4_3，肯定就没有)
        if (isMiddleAd && SpecificScenesEnum.getClickSceneInt().stream().anyMatch(o -> ans.contains(o))) {
            if(labelIds.contains(labelId)) {
                ans.add(SpecificScenesEnum.SEARCH.getCode());
            }
            // 手动选了PC首页推广栏
            if(ans.contains(SpecificScenesEnum.PC_INDEX.getCode())) {
                ans.add(SpecificScenesEnum.PC_SEARCH.getCode());
            }
            // 手动选了iPAD相关推荐页
            if(ans.contains(SpecificScenesEnum.IPAD_RECOMMEND.getCode())) {
                ans.add(SpecificScenesEnum.IPAD_SEARCH.getCode());
            }
        }
        // 选到了自动播放场景
        if (isMiddleAd && SpecificScenesEnum.getAutoSceneInt().stream().anyMatch(o -> ans.contains(o))) {
            if(labelIds.contains(labelId)) {
                // 暗投搜索自动播放
                ans.add(SpecificScenesEnum.SEARCH_AUTO.getCode());
            }
        }
//        // 带货部门，department id = 311， 指定场景选到了点击播放场景 没选框下 则暗投框下(请保持这段逻辑在最下面)
//        // (如果有了MSG_FLOW_4_3，肯定就没有)
//        if (isMiddleAd
//                && OcpcTargetEnum.COMMENT_CLICK.getCode().equals(ocpcTarget)
//                && SpecificScenesEnum.getClickSceneInt().stream().anyMatch(ans::contains)
//                && !ans.contains(SpecificScenesEnum.UNDER_BOX.getCode())) {
//            Integer departmentId = queryAccountService.getAccount(accountId).getDepartmentId();
//            if (Objects.equals(departmentId, goodsVideoDepartmentId)) {
//                ans.add(SpecificScenesEnum.UNDERFRAME_HIDDEN.getCode());
//            }
//        }
        // "框下链接点击"的oCPM目标，指定场景选到了点击播放场景  暗投story
        if (isMiddleAd
                && OcpcTargetEnum.UNDER_BOX_LINK_CLICK.getCode().equals(ocpcTarget)
                && SpecificScenesEnum.getClickSceneInt().stream().anyMatch(ans::contains)) {
            if(labelIds.contains(creativePositionConfig.getOcpmUnderBoxClickDarkStoryLabelId())) {
                ans.add(SpecificScenesEnum.STORY_HIDDEN.getCode());
            }
        }
    }

    private List<Integer> generateArchiveCpmPosition(boolean canAutoPlay, boolean supportUnderBox,
                                                     boolean isMiddleAd,List<Integer> accountLabelIdList) {
        List<Integer> ans = new ArrayList<>();
        ans.add(SpecificScenesEnum.MSG_FLOW.getCode());
        ans.add(SpecificScenesEnum.PLAY_PAGE.getCode());
        //信息流大卡黑名单
        if (!this.labelListContainsLabelId(accountLabelIdList,creativePositionConfig.getFlyBigCardBlackMarkId())
                && canAutoPlay) {
            ans.add(SpecificScenesEnum.MSG_FLOW_BIG_CARD.getCode());
        }
        //动态流白名单
        if (this.labelListContainsLabelId(accountLabelIdList,creativePositionConfig.getFlyDynamicFlowMarkId())
                && canAutoPlay) {
            ans.add(SpecificScenesEnum.DYNAMIC_FLOW.getCode());
        }
        //story黑名单
        if (!this.labelListContainsLabelId(accountLabelIdList, creativePositionConfig.getStoryBlackListLabelId())
                && canAutoPlay) {
            ans.add(SpecificScenesEnum.STORY.getCode());
        }

        ans.add(SpecificScenesEnum.IPAD_RECOMMEND.getCode());
        ans.add(SpecificScenesEnum.IPAD_SEARCH.getCode());

        if (!this.labelListContainsLabelId(accountLabelIdList, creativePositionConfig.getPcForbidLabelId())) {
            // pc场景添加黑名单
            ans.add(SpecificScenesEnum.PC_INDEX.getCode());
            ans.add(SpecificScenesEnum.PC_PLAY.getCode());
            if (isMiddleAd){
                // PC搜索
                ans.add(SpecificScenesEnum.PC_SEARCH.getCode());
            }
        }

        //框下白名单
        if (supportUnderBox) {
            ans.add(SpecificScenesEnum.UNDER_BOX.getCode());
        }
        // tv inline 白名单
        if (this.labelListContainsLabelId(accountLabelIdList, creativePositionConfig.getFlyTvInlineId()) && canAutoPlay) {
            //【三连推广】商业起飞稿件自动入ott库送审 https://www.tapd.bilibili.co/********/prong/stories/view/11********002898874
            ans.add(SpecificScenesEnum.TV_INLINE.getCode());
//            ValidateAdCardResp validateAdCardResp = grpcManager.validateAdCard(ValidateAdCardReq.newBuilder()
//                    .setOtype(AdCardType.AdUgc)
//                    .setOid(avid)
//                    .build());
//            if (Objects.equals(validateAdCardResp.getValid(), Boolean.TRUE)) {
//                ans.add(SpecificScenesEnum.TV_INLINE.getCode());
//            }
        }
        if (this.labelListContainsLabelId(accountLabelIdList, creativePositionConfig.getAndroidPadScenesLabelId())) {
            ans.add(SpecificScenesEnum.MSG_FLOW_ANDROID_PAD.getCode());
            ans.add(SpecificScenesEnum.PLAY_PAGE_ANDROID_PAD.getCode());
        }
        List<Integer> lastAns = new ArrayList<>(ans);
        if (isMiddleAd) {
            // 因为肯定会投小卡和播放页，暗投搜索点击播放
            ans.add(SpecificScenesEnum.SEARCH.getCode());
            // 有自动播放场景，暗投搜索自动播放
            if(SpecificScenesEnum.getAutoSceneInt().stream().anyMatch(lastAns::contains) &&
                    this.labelListContainsLabelId(accountLabelIdList, creativePositionConfig.getSearchPreferScenesLabelId())) {
                // 搜索自动播放
                ans.add(SpecificScenesEnum.SEARCH_AUTO.getCode());
            }
        } else {// 内容起飞
            /**
             * 先判断是否白名单，whiteLabelId为0时无白名单
             * 然后判断，选择的场景是否包含自动播放场景
             */
            Integer whiteLabelId = searchPositionConfig.getFlyContentSearchWhiteLabel();
            if (whiteLabelId == 0 || this.labelListContainsLabelId(accountLabelIdList, whiteLabelId)) {
                ans.add(SpecificScenesEnum.SEARCH.getCode());   // 信息流小法必有点击搜索
                if (SpecificScenesEnum.getAutoSceneInt().stream().anyMatch(lastAns::contains)) {
                    ans.add(SpecificScenesEnum.SEARCH_AUTO.getCode());
                }
            }

        }
        return ans;
    }

    private List<Integer> generateArchiveOCpmPosition(Integer accountId, Long avid, Integer ocpcTarget,boolean canAutoPlay,
                                                      boolean supportUnderBox, boolean isMiddleAd,List<Integer> accountLabelIdList) {
        log.info("=====> generateArchiveOCpmPosition, accountId:{},avid:{},ocpcTarget:{},canAutoPlay:{}," +
                "supportUnderBox:{}, isMiddleAd:{}", accountId, avid, ocpcTarget, canAutoPlay, supportUnderBox, isMiddleAd);
        List<Integer> ans = new ArrayList<>();
        ans.add(SpecificScenesEnum.MSG_FLOW.getCode());
        ans.add(SpecificScenesEnum.PLAY_PAGE.getCode());
        if (LaunchUnitService.isFlyNativeLandingPage(SalesType.CPM.getKey(), ocpcTarget)) {
            //story黑名单
            if (!this.labelListContainsLabelId(accountLabelIdList,creativePositionConfig.getStoryBlackListLabelId())
                    && canAutoPlay) {
                ans.add(SpecificScenesEnum.STORY.getCode());
            }
            //信息流大卡黑名单
            if (!this.labelListContainsLabelId(accountLabelIdList, creativePositionConfig.getFlyBigCardBlackMarkId())
                    && canAutoPlay) {
                ans.add(SpecificScenesEnum.MSG_FLOW_BIG_CARD.getCode());
            }
            //框下白名单
            if (supportUnderBox && isMiddleAd) {
                ans.add(SpecificScenesEnum.UNDER_BOX.getCode());
            }
        }

        // PAID_IN_24H_ROI、PAID_IN_7D_COST的投放位置为  信息流小卡、播放页、框下、story。
        if (OcpcTargetEnum.PAID_IN_24H_ROI.getCode().equals(ocpcTarget) || OcpcTargetEnum.PAID_IN_7D_COST.getCode().equals(ocpcTarget)){
            //story黑名单
            if (!this.labelListContainsLabelId(accountLabelIdList, creativePositionConfig.getStoryBlackListLabelId())
                    && canAutoPlay) {
                ans.add(SpecificScenesEnum.STORY.getCode());
            }
            //框下白名单
            if (supportUnderBox) {
                ans.add(SpecificScenesEnum.UNDER_BOX.getCode());
            }
        }
        if (OcpcTargetEnum.GOODS_TRANSACTION.getCode().equals(ocpcTarget)){
            //story黑名单
            if (!this.labelListContainsLabelId(accountLabelIdList, creativePositionConfig.getStoryBlackListLabelId())
                    && canAutoPlay) {
                ans.add(SpecificScenesEnum.STORY.getCode());
            }
            if (this.labelListContainsLabelId(accountLabelIdList, creativePositionConfig.getAndroidPadScenesLabelId())) {
                ans.add(SpecificScenesEnum.MSG_FLOW_ANDROID_PAD.getCode());
                ans.add(SpecificScenesEnum.PLAY_PAGE_ANDROID_PAD.getCode());
            }
            //信息流大卡黑名单
            if (!this.labelListContainsLabelId(accountLabelIdList, creativePositionConfig.getFlyBigCardBlackMarkId()) && canAutoPlay) {
                ans.add(SpecificScenesEnum.MSG_FLOW_BIG_CARD.getCode());
            }
            //带货动态tab暗投 https://www.tapd.cn/********/prong/stories/view/11********004158849
            if (isMiddleAd && accountLabelIdList.contains(accountConfig.getDynamicFeedsHiddenLabelId())) {
                ans.add(SpecificScenesEnum.DYNAMIC_FEEDS_HIDDEN.getCode());
            }
        }
        if (OcpcTargetEnum.VIDEO_LIKE.getCode().equals(ocpcTarget)) {
            //信息流大卡黑名单
            if (!this.labelListContainsLabelId(accountLabelIdList, creativePositionConfig.getFlyBigCardBlackMarkId())
                    && canAutoPlay) {
                ans.add(SpecificScenesEnum.MSG_FLOW_BIG_CARD.getCode());
            }
            //动态流白名单
            if (this.labelListContainsLabelId(accountLabelIdList, creativePositionConfig.getFlyDynamicFlowMarkId())
                    && this.labelListContainsLabelId(accountLabelIdList, flyPermissionConfig.getFlyOcpmVideoPlayDynamicFlowId())
                    && canAutoPlay) {
                ans.add(SpecificScenesEnum.DYNAMIC_FLOW.getCode());
            }
            //story黑名单
            if (!this.labelListContainsLabelId(accountLabelIdList, creativePositionConfig.getStoryBlackListLabelId())
                    && canAutoPlay) {
                ans.add(SpecificScenesEnum.STORY.getCode());
            }
            //iPAD白名单
            if (this.labelListContainsLabelId(accountLabelIdList, flyPermissionConfig.getFlyOcpmVideoPlayIpadId())) {
                ans.add(SpecificScenesEnum.IPAD_RECOMMEND.getCode());
                ans.add(SpecificScenesEnum.IPAD_SEARCH.getCode());
            }

            //框下白名单
            if (supportUnderBox) {
                ans.add(SpecificScenesEnum.UNDER_BOX.getCode());
            }
            if (this.labelListContainsLabelId(accountLabelIdList, creativePositionConfig.getAndroidPadScenesLabelId())) {
                ans.add(SpecificScenesEnum.MSG_FLOW_ANDROID_PAD.getCode());
                ans.add(SpecificScenesEnum.PLAY_PAGE_ANDROID_PAD.getCode());
            }
            // 优选场景肯定有点击播放场景
            if (isMiddleAd && this.labelListContainsLabelId(accountLabelIdList, creativePositionConfig.getSearchOcpmVideoPlayLabelId())) {
                // 搜索点击播放
                ans.add(SpecificScenesEnum.SEARCH.getCode());
            }
            // 优选场景选到了自动播放场景
            List<Integer> lastAns = new ArrayList<>(ans);
            if (isMiddleAd && SpecificScenesEnum.getAutoSceneInt().stream().anyMatch(lastAns::contains) ){
                // 在白名单
                if(this.labelListContainsLabelId(accountLabelIdList, creativePositionConfig.getSearchPreferScenesLabelId())) {
                    // 搜索自动播放
                    ans.add(SpecificScenesEnum.SEARCH_AUTO.getCode());
                }
            }
        }

        //评论点击
        if (OcpcTargetEnum.COMMENT_CLICK.getCode().equals(ocpcTarget)){
            //信息流大卡黑名单
            if (!this.labelListContainsLabelId(accountLabelIdList, creativePositionConfig.getFlyBigCardBlackMarkId()) && canAutoPlay) {
                ans.add(SpecificScenesEnum.MSG_FLOW_BIG_CARD.getCode());
            }
            //框下白名单
            if (supportUnderBox && isMiddleAd) {
                ans.add(SpecificScenesEnum.UNDER_BOX.getCode());
            }
            //【三连推广】「品牌传播下」-「唤起」和「评论链接点击」优化目标可投场景扩展 <a href="https://www.tapd.bilibili.co/********/prong/stories/view/11********002793178">...</a>
            //story黑名单
            if (!this.labelListContainsLabelId(accountLabelIdList, creativePositionConfig.getStoryBlackListLabelId()) && canAutoPlay) {
                ans.add(SpecificScenesEnum.STORY.getCode());
            }
            if (this.labelListContainsLabelId(accountLabelIdList, creativePositionConfig.getAndroidPadScenesLabelId())) {
                ans.add(SpecificScenesEnum.MSG_FLOW_ANDROID_PAD.getCode());
                ans.add(SpecificScenesEnum.PLAY_PAGE_ANDROID_PAD.getCode());
            }
            // 优选场景肯定有点击播放场景
            if (isMiddleAd && this.labelListContainsLabelId(accountLabelIdList, creativePositionConfig.getSearchCommentClickLabelId())) {
                // 搜索点击播放
                ans.add(SpecificScenesEnum.SEARCH.getCode());
            }
            // 优选场景选到了自动播放场景
            List<Integer> lastAns = new ArrayList<>(ans);
            if (isMiddleAd && SpecificScenesEnum.getAutoSceneInt().stream().anyMatch(o->lastAns.contains(o)) ){
                // 在白名单
                if(this.labelListContainsLabelId(accountLabelIdList, creativePositionConfig.getSearchPreferScenesLabelId())) {
                    // 搜索自动播放
                    ans.add(SpecificScenesEnum.SEARCH_AUTO.getCode());
                }
            }
            //带货动态tab暗投 https://www.tapd.cn/********/prong/stories/view/11********004158849
            if (isMiddleAd && accountLabelIdList.contains(accountConfig.getDynamicFeedsHiddenLabelId())) {
                ans.add(SpecificScenesEnum.DYNAMIC_FEEDS_HIDDEN.getCode());
            }
        }

        // 框下链接点击
        if (OcpcTargetEnum.UNDER_BOX_LINK_CLICK.getCode().equals(ocpcTarget)) {
            //框下白名单
            if (supportUnderBox && isMiddleAd) {
                ans.add(SpecificScenesEnum.UNDER_BOX.getCode());
            }
            // 暗投story
            if(this.labelListContainsLabelId(accountLabelIdList, creativePositionConfig.getOcpmUnderBoxClickDarkStoryLabelId())
                    && canAutoPlay && isMiddleAd){
                ans.add(SpecificScenesEnum.STORY_HIDDEN.getCode());
            }
        }
        //小黄车点击
        if (OcpcTargetEnum.SHOPPING_CART.getCode().equals(ocpcTarget)){
            ans = new ArrayList<>();
            //story黑名单
            if (!this.labelListContainsLabelId(accountLabelIdList, creativePositionConfig.getStoryBlackListLabelId())
                    && canAutoPlay) {
                ans.add(SpecificScenesEnum.STORY.getCode());
            }
        }

        // 店铺调起成功
        if (OcpcTargetEnum.LP_CALL_UP_SUCCESS.getCode().equals(ocpcTarget)){
            ans = new ArrayList<>();
            //【三连推广】「品牌传播下」-「唤起」和「评论链接点击」优化目标可投场景扩展 <a href="https://www.tapd.bilibili.co/********/prong/stories/view/11********002793178">...</a>
            ans.add(SpecificScenesEnum.MSG_FLOW.getCode());
            ans.add(SpecificScenesEnum.PLAY_PAGE.getCode());
            //信息流大卡黑名单
            if (!this.labelListContainsLabelId(accountLabelIdList, creativePositionConfig.getFlyBigCardBlackMarkId()) && canAutoPlay) {
                ans.add(SpecificScenesEnum.MSG_FLOW_BIG_CARD.getCode());
            }
            //框下白名单
            if (supportUnderBox && isMiddleAd) {
                ans.add(SpecificScenesEnum.UNDER_BOX.getCode());
            }
            //story黑名单
            if (!this.labelListContainsLabelId(accountLabelIdList, creativePositionConfig.getStoryBlackListLabelId()) && canAutoPlay) {
                ans.add(SpecificScenesEnum.STORY.getCode());
            }
            if (this.labelListContainsLabelId(accountLabelIdList, creativePositionConfig.getAndroidPadScenesLabelId())) {
                ans.add(SpecificScenesEnum.MSG_FLOW_ANDROID_PAD.getCode());
                ans.add(SpecificScenesEnum.PLAY_PAGE_ANDROID_PAD.getCode());
            }
        }

        //oCPM播放
        if (OcpcTargetEnum.VIDEO_PLAY.getCode().equals(ocpcTarget)) {
            //信息流大卡黑名单
            if (!this.labelListContainsLabelId(accountLabelIdList, creativePositionConfig.getFlyBigCardBlackMarkId())
                    && canAutoPlay) {
                ans.add(SpecificScenesEnum.MSG_FLOW_BIG_CARD.getCode());
            }
            //动态流白名单
            if (this.labelListContainsLabelId(accountLabelIdList, creativePositionConfig.getFlyDynamicFlowMarkId())
                    && this.labelListContainsLabelId(accountLabelIdList, flyPermissionConfig.getFlyOcpmVideoPlayDynamicFlowId())
                    && canAutoPlay) {
                ans.add(SpecificScenesEnum.DYNAMIC_FLOW.getCode());
            }
            //story黑名单
            if (!this.labelListContainsLabelId(accountLabelIdList, creativePositionConfig.getStoryBlackListLabelId())
                    && canAutoPlay) {
                ans.add(SpecificScenesEnum.STORY.getCode());
            }
            //iPAD白名单
            if (this.labelListContainsLabelId(accountLabelIdList, flyPermissionConfig.getFlyOcpmVideoPlayIpadId())) {
                ans.add(SpecificScenesEnum.IPAD_RECOMMEND.getCode());
                ans.add(SpecificScenesEnum.IPAD_SEARCH.getCode());
            }
            // PC黑名单
            if (!this.labelListContainsLabelId(accountLabelIdList,  creativePositionConfig.getPcForbidLabelId())) {
                //PC首页推广栏白名单
                if (this.labelListContainsLabelId(accountLabelIdList, flyPermissionConfig.getFlyOcpmVideoPlayPcIndexId())) {
                    ans.add(SpecificScenesEnum.PC_INDEX.getCode());
                    // PC搜索点击播放
                    if (isMiddleAd){
                        ans.add(SpecificScenesEnum.PC_SEARCH.getCode());
                    }
                }
                //PC相关播放页白名单
                if (this.labelListContainsLabelId(accountLabelIdList,  flyPermissionConfig.getFlyOcpmVideoPlayPcPageId())) {
                    ans.add(SpecificScenesEnum.PC_PLAY.getCode());
                }
            }

            //框下白名单
            if (supportUnderBox) {
                ans.add(SpecificScenesEnum.UNDER_BOX.getCode());
            }
            // tv inline 白名单
            final boolean isTvInline = this.labelListContainsLabelId(accountLabelIdList,  creativePositionConfig.getFlyTvInlineId()) && canAutoPlay;
            final boolean isHiddenOtt = this.labelListContainsLabelId(accountLabelIdList, daihuoHiddenOttAccountLabelId) && Objects.equals(ocpcTarget, OcpcTargetEnum.VIDEO_PLAY.getCode());
            //【三连推广】商业起飞稿件自动入ott库送审 https://www.tapd.bilibili.co/********/prong/stories/view/11********002898874
            if (isTvInline) {
                ans.add(SpecificScenesEnum.TV_INLINE.getCode());
            }
            if (isHiddenOtt) {
                ans.add(SpecificScenesEnum.TV_HIDDEN.getCode());
            }
            if (this.labelListContainsLabelId(accountLabelIdList, creativePositionConfig.getAndroidPadScenesLabelId())) {
                ans.add(SpecificScenesEnum.MSG_FLOW_ANDROID_PAD.getCode());
                ans.add(SpecificScenesEnum.PLAY_PAGE_ANDROID_PAD.getCode());
            }
            // 优选场景肯定有点击播放场景
//            if (isTvInline || isHiddenOtt) {
//                ValidateAdCardResp validateAdCardResp = grpcManager.validateAdCard(ValidateAdCardReq.newBuilder()
//                        .setOtype(AdCardType.AdUgc)
//                        .setOid(avid)
//                        .build());
//                if (Objects.equals(validateAdCardResp.getValid(), Boolean.TRUE)) {
//                    if (isTvInline) {
//                        ans.add(SpecificScenesEnum.TV_INLINE.getCode());
//                    }
//                    if (isHiddenOtt) {
//                        ans.add(SpecificScenesEnum.TV_HIDDEN.getCode());
//                    }
//                }
//            }
            // 搜索OCPM播放白名单
            if (isMiddleAd && this.labelListContainsLabelId(accountLabelIdList, creativePositionConfig.getSearchOcpmVideoPlayLabelId())) {
                // 搜索点击播放
                ans.add(SpecificScenesEnum.SEARCH.getCode());
            }
            // 优选场景选到了自动播放场景
            List<Integer> lastAns = new ArrayList<>(ans);
            if (isMiddleAd && SpecificScenesEnum.getAutoSceneInt().stream().anyMatch(lastAns::contains) ){
                // 在白名单
                if(this.labelListContainsLabelId(accountLabelIdList, creativePositionConfig.getSearchPreferScenesLabelId())) {
                    // 搜索自动播放
                    ans.add(SpecificScenesEnum.SEARCH_AUTO.getCode());
                }
            }
            if (!isMiddleAd) {
                // 内容起飞 优选场景 暗投搜索
                Integer whiteLabelId = searchPositionConfig.getFlyContentSearchWhiteLabel();
                if (whiteLabelId == 0 || this.labelListContainsLabelId(accountLabelIdList, whiteLabelId)) {
                    ans.add(SpecificScenesEnum.SEARCH.getCode()); // 肯定投小卡，必有点击播放
                    if (SpecificScenesEnum.getAutoSceneInt().stream().anyMatch(lastAns::contains)) {
                        ans.add(SpecificScenesEnum.SEARCH_AUTO.getCode());
                    }
                }
            }
            //带货动态tab暗投 https://www.tapd.cn/********/prong/stories/view/11********004158849
            if (isMiddleAd && accountLabelIdList.contains(accountConfig.getDynamicFeedsHiddenLabelId())) {
                ans.add(SpecificScenesEnum.DYNAMIC_FEEDS_HIDDEN.getCode());
            }
        }
        //oCPM涨粉
        if (OcpcTargetEnum.USER_FOLLOW.getCode().equals(ocpcTarget)) {
            //信息流大卡黑名单
            if (!this.labelListContainsLabelId(accountLabelIdList, creativePositionConfig.getFlyBigCardBlackMarkId())
                    && canAutoPlay) {
                ans.add(SpecificScenesEnum.MSG_FLOW_BIG_CARD.getCode());
            }
            //动态流白名单
            if (this.labelListContainsLabelId(accountLabelIdList, creativePositionConfig.getFlyDynamicFlowMarkId())
                    && this.labelListContainsLabelId(accountLabelIdList, flyPermissionConfig.getFlyOcpmUserFollowDynamicFlowId())
                    && canAutoPlay) {
                ans.add(SpecificScenesEnum.DYNAMIC_FLOW.getCode());
            }
            //story黑名单
            if (!this.labelListContainsLabelId(accountLabelIdList, creativePositionConfig.getStoryBlackListLabelId())
                    && canAutoPlay) {
                ans.add(SpecificScenesEnum.STORY.getCode());
            }
            //iPAD白名单
            if (this.labelListContainsLabelId(accountLabelIdList, flyPermissionConfig.getFlyOcpmUserFollowIpadId())) {
                ans.add(SpecificScenesEnum.IPAD_RECOMMEND.getCode());
                ans.add(SpecificScenesEnum.IPAD_SEARCH.getCode());
            }
            if (!this.labelListContainsLabelId(accountLabelIdList, creativePositionConfig.getPcForbidLabelId())) {
                //PC首页推广栏白名单
                if (this.labelListContainsLabelId(accountLabelIdList, flyPermissionConfig.getFlyOcpmUserFollowPcIndexId())) {
                    ans.add(SpecificScenesEnum.PC_INDEX.getCode());
                }
                //PC相关播放页白名单
                if (this.labelListContainsLabelId(accountLabelIdList, flyPermissionConfig.getFlyOcpmUserFollowPcPageId())) {
                    ans.add(SpecificScenesEnum.PC_PLAY.getCode());
                }
                if (isMiddleAd){
                    // PC搜索点击播放
                    ans.add(SpecificScenesEnum.PC_SEARCH.getCode());
                }
            }
            //框下白名单
            if (supportUnderBox) {
                ans.add(SpecificScenesEnum.UNDER_BOX.getCode());
            }
            if (this.labelListContainsLabelId(accountLabelIdList, creativePositionConfig.getAndroidPadScenesLabelId())) {
                ans.add(SpecificScenesEnum.MSG_FLOW_ANDROID_PAD.getCode());
                ans.add(SpecificScenesEnum.PLAY_PAGE_ANDROID_PAD.getCode());
            }
            // 优选场景肯定有点击播放场景
            if (isMiddleAd && this.labelListContainsLabelId(accountLabelIdList, creativePositionConfig.getSearchOcpmUserFollowLabelId())) {
                // 搜索点击播放
                ans.add(SpecificScenesEnum.SEARCH.getCode());
            }
            // 优选场景选到了自动播放场景
            List<Integer> lastAns = new ArrayList<>(ans);
            if (isMiddleAd && SpecificScenesEnum.getAutoSceneInt().stream().anyMatch(lastAns::contains) ){
                // 在白名单
                if(this.labelListContainsLabelId(accountLabelIdList, creativePositionConfig.getSearchPreferScenesLabelId())) {
                    // 搜索自动播放
                    ans.add(SpecificScenesEnum.SEARCH_AUTO.getCode());
                }
            }
            if (!isMiddleAd) {
                // 内容起飞 优选场景 暗投搜索
                Integer whiteLabelId = searchPositionConfig.getFlyContentSearchWhiteLabel();
                if (whiteLabelId == 0 || this.labelListContainsLabelId(accountLabelIdList, whiteLabelId)) {
                    ans.add(SpecificScenesEnum.SEARCH.getCode());
                    if (SpecificScenesEnum.getAutoSceneInt().stream().anyMatch(lastAns::contains)) {
                        ans.add(SpecificScenesEnum.SEARCH_AUTO.getCode());
                    }
                }
            }
        }
        // 首条评论复制
        if (OcpcTargetEnum.FIRST_COMMENT_COPY.getCode().equals(ocpcTarget)) {
            if (!isMiddleAd) {
                // 内容起飞 优选场景 暗投搜索
                Integer whiteLabelId = searchPositionConfig.getFlyContentSearchWhiteLabel();
                if (whiteLabelId == 0 || this.labelListContainsLabelId(accountLabelIdList, whiteLabelId)) {
                    ans.add(SpecificScenesEnum.SEARCH.getCode());
                }
            }
        }
        // 17-应用内访问
        if (OcpcTargetEnum.APP_CALLUP.getCode().equals(ocpcTarget)) {
            if (supportUnderBox) {
                ans.add(SpecificScenesEnum.UNDER_BOX.getCode());
            }
        }
        // https://www.tapd.bilibili.co/my_worktable?source_user=**********&workspace_id=********&workitem_type=story&workitem_id=11********003006335#&filter_close=true
        // 35-完件仅支持投放story
        if (OcpcTargetEnum.APPLY.getCode().equals(ocpcTarget)) {
            ans = new ArrayList<>();
            ans.add(SpecificScenesEnum.MSG_FLOW.getCode());
            if (canAutoPlay) {
                ans.add(SpecificScenesEnum.STORY.getCode());
            }
        }
        // 不是播放、涨粉、蓝链、互动，根据标签来判断是否需要暗投搜索
        if (!OcpcTargetEnum.VIDEO_PLAY.getCode().equals(ocpcTarget) &&
                !OcpcTargetEnum.USER_FOLLOW.getCode().equals(ocpcTarget) &&
                !OcpcTargetEnum.COMMENT_CLICK.getCode().equals(ocpcTarget) &&
                !OcpcTargetEnum.VIDEO_LIKE.getCode().equals(ocpcTarget)){
            List<Integer> lastAns = new ArrayList<>(ans);
            // 点击播放场景
            if (isMiddleAd && SpecificScenesEnum.getClickSceneInt().stream().anyMatch(lastAns::contains)) {
                // 在白名单
                if(this.labelListContainsLabelId(accountLabelIdList, creativePositionConfig.getSearchOtherOcpmLabelId())){
                    // 搜索点击播放
                    ans.add(SpecificScenesEnum.SEARCH.getCode());
                }
            }
            // 自动播放场景
            if (isMiddleAd && SpecificScenesEnum.getAutoSceneInt().stream().anyMatch(lastAns::contains)){
                // 在白名单
                if(this.labelListContainsLabelId(accountLabelIdList, creativePositionConfig.getSearchOtherOcpmLabelId())) {
                    // 搜索自动播放
                    ans.add(SpecificScenesEnum.SEARCH_AUTO.getCode());
                }
            }
        }

        // 框下链接点击,log打印ans
        if (OcpcTargetEnum.UNDER_BOX_LINK_CLICK.getCode().equals(ocpcTarget)) {
            log.info("=====> generateArchiveOCpmPosition, accountId:{},avid:{},ans:{}",
                    accountId, avid,JSON.toJSON(ans));
        }

        if (OcpcTargetEnum.LIVE_RESERVE.getCode().equals(ocpcTarget)) {
            //信息流大卡黑名单
            if (!this.labelListContainsLabelId(accountLabelIdList, creativePositionConfig.getFlyBigCardBlackMarkId())
                    && canAutoPlay) {
                ans.add(SpecificScenesEnum.MSG_FLOW_BIG_CARD.getCode());
            }
            //动态流白名单
            if (this.labelListContainsLabelId(accountLabelIdList, creativePositionConfig.getFlyDynamicFlowMarkId())
                    && this.labelListContainsLabelId(accountLabelIdList, flyPermissionConfig.getFlyOcpmVideoPlayDynamicFlowId())
                    && canAutoPlay) {
                ans.add(SpecificScenesEnum.DYNAMIC_FLOW.getCode());
            }
            //iPAD白名单
            if (this.labelListContainsLabelId(accountLabelIdList, flyPermissionConfig.getFlyOcpmVideoPlayIpadId())) {
                ans.add(SpecificScenesEnum.IPAD_RECOMMEND.getCode());
                ans.add(SpecificScenesEnum.IPAD_SEARCH.getCode());
            }
            // PC黑名单
            if (!this.labelListContainsLabelId(accountLabelIdList,  creativePositionConfig.getPcForbidLabelId())) {
                //PC首页推广栏白名单
                if (this.labelListContainsLabelId(accountLabelIdList, flyPermissionConfig.getFlyOcpmVideoPlayPcIndexId())) {
                    ans.add(SpecificScenesEnum.PC_INDEX.getCode());
                    // PC搜索点击播放
                    if (isMiddleAd){
                        ans.add(SpecificScenesEnum.PC_SEARCH.getCode());
                    }
                }
                //PC相关播放页白名单
                if (this.labelListContainsLabelId(accountLabelIdList,  flyPermissionConfig.getFlyOcpmVideoPlayPcPageId())) {
                    ans.add(SpecificScenesEnum.PC_PLAY.getCode());
                }
            }

            //框下白名单
            if (supportUnderBox) {
                ans.add(SpecificScenesEnum.UNDER_BOX.getCode());
            }
            // tv inline 白名单
            final boolean isTvInline = this.labelListContainsLabelId(accountLabelIdList,  creativePositionConfig.getFlyTvInlineId()) && canAutoPlay;
            final boolean isHiddenOtt = this.labelListContainsLabelId(accountLabelIdList, daihuoHiddenOttAccountLabelId) && Objects.equals(ocpcTarget, OcpcTargetEnum.VIDEO_PLAY.getCode());
            //【三连推广】商业起飞稿件自动入ott库送审 https://www.tapd.bilibili.co/********/prong/stories/view/11********002898874
            if (isTvInline) {
                ans.add(SpecificScenesEnum.TV_INLINE.getCode());
            }
            if (isHiddenOtt) {
                ans.add(SpecificScenesEnum.TV_HIDDEN.getCode());
            }
            if (this.labelListContainsLabelId(accountLabelIdList, creativePositionConfig.getAndroidPadScenesLabelId())) {
                ans.add(SpecificScenesEnum.MSG_FLOW_ANDROID_PAD.getCode());
                ans.add(SpecificScenesEnum.PLAY_PAGE_ANDROID_PAD.getCode());
            }
            // 搜索OCPM播放白名单
            if (isMiddleAd && this.labelListContainsLabelId(accountLabelIdList, creativePositionConfig.getSearchOcpmVideoPlayLabelId())) {
                // 搜索点击播放
                ans.add(SpecificScenesEnum.SEARCH.getCode());
            }
            // 优选场景选到了自动播放场景
            List<Integer> lastAns = new ArrayList<>(ans);
            if (isMiddleAd && SpecificScenesEnum.getAutoSceneInt().stream().anyMatch(lastAns::contains) ){
                // 在白名单
                if(this.labelListContainsLabelId(accountLabelIdList, creativePositionConfig.getSearchPreferScenesLabelId())) {
                    // 搜索自动播放
                    ans.add(SpecificScenesEnum.SEARCH_AUTO.getCode());
                }
            }
            if (!isMiddleAd) {
                // 内容起飞 优选场景 暗投搜索
                Integer whiteLabelId = searchPositionConfig.getFlyContentSearchWhiteLabel();
                if (whiteLabelId == 0 || this.labelListContainsLabelId(accountLabelIdList, whiteLabelId)) {
                    ans.add(SpecificScenesEnum.SEARCH.getCode()); // 肯定投小卡，必有点击播放
                    if (SpecificScenesEnum.getAutoSceneInt().stream().anyMatch(lastAns::contains)) {
                        ans.add(SpecificScenesEnum.SEARCH_AUTO.getCode());
                    }
                }
            }
            //带货动态tab暗投 https://www.tapd.cn/********/prong/stories/view/11********004158849
            if (isMiddleAd && accountLabelIdList.contains(accountConfig.getDynamicFeedsHiddenLabelId())) {
                ans.add(SpecificScenesEnum.DYNAMIC_FEEDS_HIDDEN.getCode());
            }
        }
        return ans;
    }

    public boolean judgeIsOcpmArcBlueVCmOrderUnderBox(Integer accountId, Long avid, Long mid, Integer ocpcTarget) {
        // 调用方是专业起飞
        boolean isBlueV = judgeIsBlueVOrCmOrder(accountId, avid, mid, true, false),
                isCmOrder = judgeIsBlueVOrCmOrder(accountId, avid, mid, false, true);
        return this.judgeIsOcpmArcBlueVCmOrderUnderBox4FlyPro(ocpcTarget, isBlueV, isCmOrder);
    }

    public boolean judgeIsOcpmArcBlueVCmOrderUnderBox4FlyPro(Integer ocpcTarget, boolean isBlueV, boolean isCmOrder) {
        final Set<Integer> flyNativeLandingPageTargets = Stream.of(OcpcTargetEnum.FORM_SUBMIT, OcpcTargetEnum.WX_COPY, OcpcTargetEnum.GAME_RESERVE, OcpcTargetEnum.APP_ACTIVE, OcpcTargetEnum.LP_CALL_UP_SUCCESS)
                .map(OcpcTargetEnum::getCode)
                .collect(Collectors.toSet());
        List<Integer> cmOrderOcpcTargets = Lists.newArrayList(OcpcTargetEnum.UNDER_BOX_LINK_CLICK.getCode(), OcpcTargetEnum.COMMENT_CLICK.getCode());
        // 商单稿件
        if (isCmOrder && cmOrderOcpcTargets.contains(ocpcTarget)) {
            return true;
        }
        // 蓝V稿件
        else if (isBlueV && OcpcTargetEnum.COMMENT_CLICK.getCode().equals(ocpcTarget)) {
            return true;
        } else if (flyNativeLandingPageTargets.contains(ocpcTarget)) {
            return true;
        }
        //【三连推广】「品牌传播下」-「唤起」和「评论链接点击」优化目标可投场景扩展 <a href="https://www.tapd.bilibili.co/********/prong/stories/view/11********002793178">...</a>
        if (Objects.equals(ocpcTarget, OcpcTargetEnum.COMMENT_CLICK.getCode())) {
            return isCmOrder || isBlueV;
        }

        if (Objects.equals(ocpcTarget, OcpcTargetEnum.LP_CALL_UP_SUCCESS.getCode())) {
            return isCmOrder || isBlueV;
        }
        return false;
    }

    // 判断非核心ocpcTarget（核心是指cpm，oCPM涨粉，oCPM播放）是否支持投框下
    public boolean judgeNotCoreTargetSupportUnderBox4Middle(Integer ocpcTarget) {
        final Set<Integer> flyNativeLandingPageTargets = Stream.of(OcpcTargetEnum.FORM_SUBMIT, OcpcTargetEnum.WX_COPY, OcpcTargetEnum.GAME_RESERVE, OcpcTargetEnum.APP_ACTIVE, OcpcTargetEnum.LP_CALL_UP_SUCCESS)
                .map(OcpcTargetEnum::getCode)
                .collect(Collectors.toSet());
        List<Integer> cmOrderOcpcTargets = Lists.newArrayList(OcpcTargetEnum.UNDER_BOX_LINK_CLICK.getCode(), OcpcTargetEnum.COMMENT_CLICK.getCode());
        // 商单稿件
        if (cmOrderOcpcTargets.contains(ocpcTarget)) {
            return true;
        } else if (flyNativeLandingPageTargets.contains(ocpcTarget)) {
            return true;
        }
        //【三连推广】「品牌传播下」-「唤起」和「评论链接点击」优化目标可投场景扩展 <a href="https://www.tapd.bilibili.co/********/prong/stories/view/11********002793178">...</a>
        if (Objects.equals(ocpcTarget, OcpcTargetEnum.COMMENT_CLICK.getCode())) {
            return true;
        }

        if (Objects.equals(ocpcTarget, OcpcTargetEnum.LP_CALL_UP_SUCCESS.getCode())) {
            return true;
        }
        return false;
    }
    /**
     * 生成位置(SpecificScenesEnum)
     *
     * @param dto
     * @param unit
     * @param accountId
     * @return SpecificScenesEnum 场景位置枚举
     */
    public List<Integer> generatePositions(FlyProSelectScenesDto dto, CpcUnitDto unit, Integer accountId) {
        return generatePositions(dto, unit, accountId, false);
    }

    /**
     * 根据场景类型和推广目的获取场景 ids(优选/指定/其他)
     *
     * 1. 优选，根据规则生成场景列表
     * 2. 指定场景
     * 3. 其他场景
     *
     * @param dto
     * @param unit
     * @param accountId
     * @param supportUnderBox
     * @return SpecificScenesEnum
     */
    public List<Integer> generatePositions(FlyProSelectScenesDto dto, CpcUnitDto unit, Integer accountId, boolean supportUnderBox) {
        PromotionPurposeType promotionPurposeType = PromotionPurposeType.getByCode(unit.getPromotionPurposeType());
        // 查询acc_account_label_mapping表，获取账号的全部标签
        // 查询一次后，后面尽量不再查询acc_account_label_mapping表
        List<Integer> accountLabelIdList = accountLabelService.getLabelIdsByAccountId(accountId);
        // 优选场景
        if (ScenesEnum.PREFER_SCENES.getCode().equals(dto.getLaunchScene())) {
            if (PromotionPurposeType.GOODS == promotionPurposeType) {
                return this.generateGoodsPosition(unit.getOcpcTarget());
            }
            // 直播推广
            if (PromotionPurposeType.LIVE_ROOM == promotionPurposeType) {
                return this.generateSmallAndBigCardPosition(accountId);
            }
            // 活动
            if (PromotionPurposeType.ACTIVITY == promotionPurposeType) {
                return this.generateCommonPosition();
            }
            // 动态
            if (PromotionPurposeType.DYNAMIC == promotionPurposeType) {
                return this.generateSmallCardPosition();
            }
            // 直播带货
            if (PromotionPurposeType.GOODS_LIVE == promotionPurposeType) {
                return this.generateLiveCommonPosition(unit.getGoodsLive().getLaunchType(), accountId, unit, accountLabelIdList);
            }
            // 投稿内容
            if (PromotionPurposeType.ARCHIVE_CONTENT == promotionPurposeType) {
                if (SalesType.CPM.getCode() == unit.getSalesType()) {
                    Arc arc = archiveServiceProxy.arc(unit.getVideoId()).getArc();
                    boolean canAutoPlay = archiveService.isAutoPlay(accountId, arc);
                    boolean isMiddleAd = AdpVersion.isMiddle(unit.getAdpVersion());
                    //稿件CPM
                    if (unit.getOcpcTarget() <= 0) {
                        return this.generateArchiveCpmPosition(canAutoPlay, supportUnderBox, isMiddleAd, accountLabelIdList);
                    } else {
                        //稿件oCPM
                        return this.generateArchiveOCpmPosition(accountId, arc.getAid(), unit.getOcpcTarget(), canAutoPlay,
                                supportUnderBox, isMiddleAd, accountLabelIdList);
                    }
                } else {
                    //稿件CPC
                    return this.generateCommonPosition();
                }
            }
            if (PromotionPurposeType.OGV == promotionPurposeType) {
                List<Integer> positions = Lists.newArrayList(SpecificScenesEnum.MSG_FLOW.getCode(), SpecificScenesEnum.MSG_FLOW_BIG_CARD.getCode());
//                Map<Long, EpisodeInfoProto> protoMap = archiveService.doGetEpisodeInfosByAids(Lists.newArrayList(dto.getVideoId()));
//                EpisodeInfoProto proto = protoMap.get(dto.getVideoId());
//                if(ottService.isOttScene(accountId, unit.getSalesType(), proto.getEpisodeId())){
//                    positions.add(SpecificScenesEnum.TV_INLINE.getCode());
//                }
                //【三连推广】商业起飞稿件自动入ott库送审 https://www.tapd.bilibili.co/********/prong/stories/view/11********002898874
                final boolean isTvInline = this.labelListContainsLabelId(accountLabelIdList,creativePositionConfig.getFlyTvInlineId());
                if (isTvInline) {
                    positions.add(SpecificScenesEnum.TV_INLINE.getCode());
                }
                return positions;
            }
        }

        // 指定场景
        if (ScenesEnum.MOBILE_SCENES.getCode().equals(dto.getLaunchScene())) {
            return this.generateArchive4MobileScene(dto,unit,accountId,promotionPurposeType, accountLabelIdList);
        }
        // 其它场景（已废弃）
        if (ScenesEnum.PC_SCENES.getCode().equals(dto.getLaunchScene())) {
            return Collections.singletonList(dto.getSingleSpecificScene());
        }
        return Collections.emptyList();
    }

    /**
     * 生成直播带货的场景
     * 逻辑同 获取场景接口 com.bilibili.adp.advertiser.portal.webapi.flyPro.v2.FlyProCreativeNewBannerController#generateLiveOcpmCommonScenesVo(java.lang.Integer)
     *
     * @param launchType
     * @return
     */
    private List<Integer> generateLiveCommonPosition(Integer launchType, Integer accountId, CpcUnitDto unit,
                                                     List<Integer> labelIds) {
        List<Integer> position = new ArrayList<>();
        // 如果单元层级为直播间直投，则展示信息流小卡，竖版信息 支持其他场景
        if (launchType == UnitLiveLaunchType.LIVE_DIRECT_LAUNCH_VALUE) {
            position.add(SpecificScenesEnum.MSG_FLOW.getCode());
            position.add(SpecificScenesEnum.STORY.getCode());
            if(!CollectionUtils.isEmpty(labelIds) && labelIds.contains(creativePositionConfig.getGoodsLivePlayPagePreferScenesLabelId())) {
                position.add(SpecificScenesEnum.PLAY_PAGE.getCode());
            }
            if(!CollectionUtils.isEmpty(labelIds) && labelIds.contains(creativePositionConfig.getGoodsLiveUnderBoxScenesLabelId())) {
                position.add(SpecificScenesEnum.UNDER_BOX.getCode());
            }

        }

        //如果单元层级为短视频引流，则只展示竖版信息流
        if (launchType == UnitLiveLaunchType.ARC_DRAINAGE_LAUNCH_VALUE) {
            position.add(SpecificScenesEnum.STORY.getCode());
        }
        return position;
    }

    private void checkFlyProSelectScenesDto(FlyProSelectScenesDto dto, PromotionPurposeType ppt) {
        Assert.isTrue(ScenesEnum.PREFER_SCENES.getCode().equals(dto.getLaunchScene()) ||
                ScenesEnum.MOBILE_SCENES.getCode().equals(dto.getLaunchScene()) ||
                ScenesEnum.PC_SCENES.getCode().equals(dto.getLaunchScene()), "投放场景选择错误");
        if (ScenesEnum.MOBILE_SCENES.getCode().equals(dto.getLaunchScene())) {
            Assert.isTrue(!CollectionUtils.isEmpty(dto.getMultiSpecificScene()), "多选场景不能为空");
            Assert.isTrue(SpecificScenesEnum.getMultiSpecificSceneInt().containsAll(dto.getMultiSpecificScene()),
                    "多选场景错误");
        }
        if (ScenesEnum.PC_SCENES.getCode().equals(dto.getLaunchScene())) {
            Assert.notNull(dto.getSingleSpecificScene(), "单选场景不能为空");
        }
    }

    /**
     * 构建 FlyProSelectScenesDto
     *
     * @param dto
     * @return
     */
    public FlyProSelectScenesDto creativeDto2FlyProSelectScenesDto(CpcCreativeDto dto) {
        Integer unitScenesType = dto.getFlyBannerUnitScenesType();
        List<Integer> unitSpecificScenesType = dto.getFlyBannerUnitSpecificScenesType();

        return FlyProSelectScenesDto.builder()
                .videoId(dto.getVideoId())
                .launchScene(unitScenesType)
                .multiSpecificScene(ScenesEnum.MOBILE_SCENES.getCode().equals(unitScenesType) ? unitSpecificScenesType : Collections.EMPTY_LIST)
                .singleSpecificScene(ScenesEnum.PC_SCENES.getCode().equals(unitScenesType) ? unitSpecificScenesType.get(0) : 0)
                .build();
    }

    public Boolean dynamicLaunchSmallCard(CpcUnitDto unit, CpcCreativeDto creative, Operator operator) {
        FlyProSelectScenesDto flyProSelectScenesDto = this.creativeDto2FlyProSelectScenesDto(creative);
        List<Integer> positions = this.generatePositions(flyProSelectScenesDto, unit, operator.getOperatorId());
        return positions.contains(SpecificScenesEnum.DYNAMIC_FLOW.getCode()) || positions.contains(SpecificScenesEnum.DYNAMIC_FEEDS_HIDDEN.getCode());
    }

    /**
     * 单元下两种类型创意(16_10, 16_9)投的具体位置信息 Map<广告位组id, 模板>
     *
     * @param unit
     * @param details
     * @param operator
     * @param flyCampaignDetailDto
     * @return
     */
    public FlyProUnitPositionsDto creativeDetails2slotGroupIdAndTemplateDto(CpcUnitDto unit, List<CpcCreativeDetailDto> details,
                                                                            Operator operator, FlyCampaignDetailDto flyCampaignDetailDto){
        final Integer launchTarget = flyCampaignDetailDto.getLaunchTarget();
        final Integer roomPromoteScenes = flyCampaignDetailDto.getRoomPromoteScenes();
        CpcCreativeDetailDto detail = details.get(0);
        CpcCreativeDto creative = new CpcCreativeDto();
        BeanUtils.copyProperties(detail, creative);

        // 转成 flyProSelectScenesDto
        FlyProSelectScenesDto flyProSelectScenesDto = this.creativeDto2FlyProSelectScenesDto(creative);

        AccountAllInfoDto accountAllInfoDto = queryAccountService.getAccountAllInfoFromCache(flyCampaignDetailDto.getAccountId());
        AccountDto accountDto = accountAllInfoDto.getAccountDto();

        // 三连托管的情况
        boolean isMiddle = Integer.valueOf(5).equals(unit.getAdpVersion());
        final boolean supportUnderBox = flySupportUnderBox(accountDto, flyCampaignDetailDto.getAvId(), unit.getSalesType(), unit.getOcpcTarget(),isMiddle);

        // 根据场景类型获取场景 ids(优选/指定/其他)
        List<Integer> positions = this.generatePositions(flyProSelectScenesDto, unit, operator.getOperatorId(), supportUnderBox);

        // 将场景类型的场景列表按 16_10, 16_9 分组
        List<Integer> positions_16_10 = positions.stream().filter(o -> SpecificScenesEnum.getClickSceneInt().contains(o)).collect(Collectors.toList());
        List<Integer> positions_16_9 = positions.stream().filter(o -> SpecificScenesEnum.getAutoSceneInt().contains(o)).collect(Collectors.toList());

        // 获取16_10（点击播放场景）, 16_9（自动播放场景）的 Map<slotGroupId，TemplateDto>
        Map<Integer,TemplateDto> position_map_16_10 = this.getSlotGroupTemplateListByPositions(accountDto.getAccountId(), unit.getIsNoBid(), positions_16_10, unit.getOcpcTarget(),LaunchTargetEnum.getByCode(launchTarget).getPromotionPurposeType(),roomPromoteScenes);
        Map<Integer,TemplateDto> position_map_16_9 = this.getSlotGroupTemplateListByPositions(accountDto.getAccountId(), unit.getIsNoBid(), positions_16_9, unit.getOcpcTarget(),LaunchTargetEnum.getByCode(launchTarget).getPromotionPurposeType(),roomPromoteScenes);
        String authorName = "";
        boolean existGif = false;
        for(CpcCreativeDetailDto dto : details){
            if(!CollectionUtils.isEmpty(dto.getImageDtos()) &&
                    !StringUtils.isEmpty(dto.getImageDtos().get(0).getUrl()) &&
                    org.apache.commons.lang.StringUtils.endsWith(dto.getImageDtos().get(0).getUrl(), "webp")){
                existGif = true;
                break;
            }
        }
        CpcCampaignDto virtualCpcCampaignDto = CpcCampaignDto.builder()
                .adType(CampaignAdType.ALL.getCode())
                .build();

        // 根据场景相关参数获取 Map<slotGroupId, TemplateDto>
        return FlyProUnitPositionsDto.builder()
                .position_map_16_10(position_map_16_10)
                .position_map_16_9(position_map_16_9)
                .authorName(authorName)
                .build();
    }

    /**
     * 单元下两种类型创意(16_10, 16_9)投的具体位置信息 Map<广告位组id, 模板>
     *
     * @param unit
     * @param details
     * @param operator
     * @param promotionPurposeType
     * @param roomPromoteScenes
     * @param isMiddleNotManaged 是否三连非托管，即自定义创意才暗投，先不管托管
     * @return
     */
    public FlyProUnitPositionsDto middleCreativeDetails2slotGroupIdAndTemplateDto(CpcUnitDto unit, List<CpcCreativeDetailDto> details,
                                                                                  Operator operator, Integer promotionPurposeType,
                                                                                  Integer roomPromoteScenes, Boolean isMiddleNotManaged, AccountBaseDto account) {
        CpcCreativeDetailDto detail = details.get(0);
        CpcCreativeDto creative = new CpcCreativeDto();
        BeanUtils.copyProperties(detail, creative);

        // 转成 flyProSelectScenesDto
        FlyProSelectScenesDto flyProSelectScenesDto = this.creativeDto2FlyProSelectScenesDto(creative);

        AccountAllInfoDto accountAllInfoDto = queryAccountService.getAccountAllInfoFromCache(unit.getAccountId());
        AccountDto accountDto = accountAllInfoDto.getAccountDto();
        final CpcCampaignDto cpcCampaignDto = cpcCampaignService.loadCpcCampaignDto(unit.getCampaignId());
        final boolean supportUnderBox = middleFlySupportUnderBox(accountDto, String.valueOf(details.get(0).getVideoId()), unit.getSalesType(), unit.getOcpcTarget(), cpcCampaignDto.getPromotionPurposeType());

        // 根据场景类型获取场景 ids(优选/指定/其他)
        List<Integer> positions = this.generatePositions(flyProSelectScenesDto,unit,operator.getOperatorId(), supportUnderBox);

        // 将场景类型的场景列表按 16_10（点击播放场景）, 16_9（自动播放场景） 分组
        List<Integer> positions_16_10 = positions.stream().filter(o->SpecificScenesEnum.getClickSceneInt().contains(o)).collect(Collectors.toList());
        List<Integer> positions_16_9 = positions.stream().filter(o->SpecificScenesEnum.getAutoSceneInt().contains(o)).collect(Collectors.toList());
        List<Integer> positions_4_3 = positions.stream().filter(o->SpecificScenesEnum.get4_3SceneInt().contains(o)).collect(Collectors.toList());

        // 获取16_10（点击播放场景）, 16_9（自动播放场景）的 Map<slotGroupId，TemplateDto>
        Map<Integer,TemplateDto> position_map_16_10 = this.getSlotGroupTemplateListByPositions(accountDto.getAccountId(), unit.getIsNoBid(), positions_16_10, unit.getOcpcTarget(),PromotionPurposeType.getByCode(promotionPurposeType),roomPromoteScenes);
        Map<Integer,TemplateDto> position_map_16_9 = this.getSlotGroupTemplateListByPositions(accountDto.getAccountId(), unit.getIsNoBid(), positions_16_9, unit.getOcpcTarget(),PromotionPurposeType.getByCode(promotionPurposeType),roomPromoteScenes);
        Map<Integer,TemplateDto> position_map_4_3 = this.getSlotGroupTemplateListByPositions(accountDto.getAccountId(), unit.getIsNoBid(), positions_4_3, unit.getOcpcTarget(),PromotionPurposeType.getByCode(promotionPurposeType),roomPromoteScenes);
        String authorName = "";

        // 指定场景story没有封面暗投处理
        specificStoryAndNoImagesDarkProcess(unit, details, position_map_16_9);

        // 动态暗投处理
        if (isMiddleNotManaged != null && isMiddleNotManaged) {
            AbstractFlyDynamicDarkLaunchProc dynamicDarkLaunchProc = flyDynamicDarkLaunchProcFactory.getDynamicDarkLaunchProc(unit.getPromotionPurposeType());
            Optional.ofNullable(dynamicDarkLaunchProc).ifPresent(t -> t.processDynamicDarkLaunchSlotGroupAndTemplate(unit, details, position_map_16_10, accountDto.getUserType()));
        }

        // 根据场景相关参数获取 Map<slotGroupId, TemplateDto>
        return FlyProUnitPositionsDto.builder()
                // 根据场景相关参数获取 Map<slotGroupId, TemplateDto>
                .position_map_16_10(position_map_16_10)
                .position_map_16_9(position_map_16_9)
                .position_map_4_3(position_map_4_3)
                .authorName(authorName)
                .build();
    }

    /**
     * 指定场景story没有封面暗投处理
     *
     * @param details
     * @param position_map_16_9
     */
    private void specificStoryAndNoImagesDarkProcess(CpcUnitDto cpcUnitDto, List<CpcCreativeDetailDto> details, Map<Integer, TemplateDto> position_map_16_9) {
        LauCampaignPo lauCampaignPo = outerLauCampaignRepo.queryCampaignById(cpcUnitDto.getCampaignId());

        // 没有标签不处理
//        if (!this.accountIdInLabel(accountId, creativePositionConfig.getLabelIdStoryDarkFlySlotSearchClick())) {
//            return;
//        }
        if (!flyUnitJudger.isFlyUnit(lauCampaignPo.getPromotionPurposeType(), cpcUnitDto.getPromotionPurposeType())) {
            return;
        }

        // 存在指定场景 story，没有封面的情况，暗投搜索点击
        boolean existPointStorySceneAndNoCover =
                details.stream().anyMatch(flyCreativeDto -> isSpecificStoryAndNoImages(flyCreativeDto));
        log.info("specificStoryAndNoImagesDarkProcess, existPointStorySceneAndNoCover:{},position_map_16_9.size:{}",
                existPointStorySceneAndNoCover, position_map_16_9.size());
        if (existPointStorySceneAndNoCover && !CollectionUtils.isEmpty(position_map_16_9)) {
            List<Integer> darkSlotGroupIds = Arrays.asList(creativePositionConfig.getSlotGroupIdStoryDarkFlySlotSearchClick());

            // 根据 slotGroupIds 和推广目的查询模板列表
            List<ResSlotGroupTemplateMappingDto> resSlotGroupTemplateMapping = resSlotGroupService
                    .querySlotGroupTemplateMappingInSlotGroupIds(QueryTemplateLaunchTypeMappingDto.builder()
                            .slotGroupIds(darkSlotGroupIds)
                            .promotionPurposeType(PromotionPurposeType.ARCHIVE_CONTENT.getCode())
                            .build());
            // 将 slotGroupIds 的每个 slotGroupId 的模板列表取第一个模板转成 Map<slotGroupId, TemplateDto>
            if (!CollectionUtils.isEmpty(resSlotGroupTemplateMapping)) {
                log.info("specificStoryAndNoImagesDarkProcess, existPointStorySceneAndNoCover:{},position_map_16_9" +
                                ".size:{}, darkLaunch:{}",
                        existPointStorySceneAndNoCover, position_map_16_9.size(), resSlotGroupTemplateMapping.size());
                resSlotGroupTemplateMapping.stream().forEach(o -> {
                    position_map_16_9.put(o.getSlotGroupId(), o.getTemplates().get(0));
                });
            }
        }
    }

    /**
     * 是否指定场景 & 没有封面
     * @param cpcCreativeDetailDto
     * @return
     */
    private static boolean isSpecificStoryAndNoImages(CpcCreativeDetailDto cpcCreativeDetailDto) {
        return ScenesEnum.MOBILE_SCENES.getCode().equals(cpcCreativeDetailDto.getFlyBannerUnitScenesType()) &&
                !CollectionUtils.isEmpty(cpcCreativeDetailDto.getFlyBannerUnitSpecificScenesType()) &&
                cpcCreativeDetailDto.getFlyBannerUnitSpecificScenesType().size() == 1 &&
                cpcCreativeDetailDto.getFlyBannerUnitSpecificScenesType().contains(SpecificScenesEnum.STORY.getCode()) &&
                CollectionUtils.isEmpty(cpcCreativeDetailDto.getImageDtos());
    }

    // 点击播放场景map后处理
    private void postProcessPositionMap16_10(Map<Integer,TemplateDto> position_map_16_10,Boolean isPrefer,
                                             boolean canAutoPlay,boolean supportUnderBox,boolean isHorizontal){
        if(CollectionUtils.isEmpty(position_map_16_10)){
            return;
        }
        Set<Integer> slotGroupIds = position_map_16_10.keySet();
        List<Integer> darkSlotGroupIds = new ArrayList<>();
        //自动播放
        if(canAutoPlay) {
            // 暗投到自动播放场景（story+inline）
            darkSlotGroupIds.add(creativePositionConfig.getDarkFlySlotStory());
            if(isHorizontal) {
                darkSlotGroupIds.add(creativePositionConfig.getDarkFlySlotBig());
            }
        }
        // 指定场景投至点击播放场景的其他未勾选的资源位
        if(!isPrefer){
            // 未包含小卡
            if(!slotGroupIds.contains(creativePositionConfig.getFlySlotArchiveSmall())){
                darkSlotGroupIds.add(creativePositionConfig.getDarkFlySlotSmall());
            }
            // 未包含播放页
            if(!slotGroupIds.contains(creativePositionConfig.getFlySlotArchivePlay())){
                darkSlotGroupIds.add(creativePositionConfig.getDarkFlySlotPlay());
            }
            // 未包含框下
            if(!slotGroupIds.contains(creativePositionConfig.getFlySlotArchiveUnderBox()) && supportUnderBox){
                darkSlotGroupIds.add(creativePositionConfig.getDarkFlySlotUnderBox());
            }
            // 未包含pc首页推广栏
            if(!slotGroupIds.contains(creativePositionConfig.getFlySlotArchivePcIndex())){
                darkSlotGroupIds.add(creativePositionConfig.getDarkFlySlotPcIndex());
            }
            // 未包含pc相关推荐
            if(!slotGroupIds.contains(creativePositionConfig.getFlySlotArchivePcPlay())){
                darkSlotGroupIds.add(creativePositionConfig.getDarkFlySlotPcPlay());
            }
            // 未包含iPad相关推荐
            if(!slotGroupIds.contains(creativePositionConfig.getFlySlotArchiveIPad())){
                darkSlotGroupIds.add(creativePositionConfig.getDarkFlySlotIPad());
            }
        }
        if(!CollectionUtils.isEmpty(darkSlotGroupIds)) {
            // 根据 slotGroupIds 和推广目的查询模板列表
            List<ResSlotGroupTemplateMappingDto> resSlotGroupTemplateMapping = resSlotGroupService
                    .querySlotGroupTemplateMappingInSlotGroupIds(QueryTemplateLaunchTypeMappingDto.builder()
                            .slotGroupIds(darkSlotGroupIds)
                            .promotionPurposeType(PromotionPurposeType.ARCHIVE_CONTENT.getCode())
                            .build());
            // 将 slotGroupIds 的每个 slotGroupId 的模板列表取第一个模板转成 Map<slotGroupId, TemplateDto>
            resSlotGroupTemplateMapping.stream().forEach(o -> {
                TemplateDto t = o.getTemplates().get(0);
                position_map_16_10.put(o.getSlotGroupId(), t);
            });
        }
    }

    // 自动播放场景map后处理
    private void postProcessPositionMap16_9(Map<Integer,TemplateDto> position_map_16_9,Boolean isPrefer,
                                            boolean canAutoPlay,boolean supportUnderBox,boolean isHorizontal){
        if(CollectionUtils.isEmpty(position_map_16_9)){
            return;
        }
        Set<Integer> slotGroupIds = position_map_16_9.keySet();
        List<Integer> darkSlotGroupIds = new ArrayList<>();
        // 暗投到点击播放场景
        darkSlotGroupIds.add(creativePositionConfig.getDarkFlySlotSmall());
        darkSlotGroupIds.add(creativePositionConfig.getDarkFlySlotPlay());
        if(supportUnderBox) {
            darkSlotGroupIds.add(creativePositionConfig.getDarkFlySlotUnderBox());
        }
        darkSlotGroupIds.add(creativePositionConfig.getDarkFlySlotPcIndex());
        darkSlotGroupIds.add(creativePositionConfig.getDarkFlySlotPcPlay());
        darkSlotGroupIds.add(creativePositionConfig.getDarkFlySlotIPad());
        // 指定场景
        if(!isPrefer){
            // 未包含大卡
            if(!slotGroupIds.contains(creativePositionConfig.getFlySlotArchiveBig()) && canAutoPlay && isHorizontal){
                darkSlotGroupIds.add(creativePositionConfig.getDarkFlySlotBig());
            }
            // 未包含story
            if(!slotGroupIds.contains(creativePositionConfig.getFlySlotArchiveStory()) && canAutoPlay){
                darkSlotGroupIds.add(creativePositionConfig.getDarkFlySlotStory());
            }
        }
        if(!CollectionUtils.isEmpty(darkSlotGroupIds)) {
            // 根据 slotGroupIds 和推广目的查询模板列表
            List<ResSlotGroupTemplateMappingDto> resSlotGroupTemplateMapping = resSlotGroupService
                    .querySlotGroupTemplateMappingInSlotGroupIds(QueryTemplateLaunchTypeMappingDto.builder()
                            .slotGroupIds(darkSlotGroupIds)
                            .promotionPurposeType(PromotionPurposeType.ARCHIVE_CONTENT.getCode())
                            .build());
            // 将 slotGroupIds 的每个 slotGroupId 的模板列表取第一个模板转成 Map<slotGroupId, TemplateDto>
            if (!CollectionUtils.isEmpty(darkSlotGroupIds)) {
                resSlotGroupTemplateMapping.stream().forEach(o -> {
                    TemplateDto t = o.getTemplates().get(0);
                    position_map_16_9.put(o.getSlotGroupId(), t);
                });
            }
        }
    }


    @SneakyThrows
    public boolean flySupportUnderBox(AccountDto accountDto, String avid, Integer salesType, Integer ocpmTarget,boolean isMiddle) {
        // 非稿件
        if (!StringUtils.hasText(avid)) return false;

        // 非cpm
        if (!Objects.equals(salesType, SalesType.CPM.getCode())) return false;

        // 有目标且目标非播放/涨粉
        if (!Objects.equals(ocpmTarget, 0) && !Objects.equals(ocpmTarget, OcpcTargetEnum.VIDEO_PLAY.getCode()) && !Objects.equals(ocpmTarget, OcpcTargetEnum.USER_FOLLOW.getCode())) return false;

        // 非商业起飞账户
        if (!Objects.equals(accountDto.getIsSupportFly(), 1)) return false;

        // 账户部门为【媒介资源预占】
        if (Objects.equals(accountDto.getDepartmentId(), mediaSourceDepartmentId)) return false;

        final ArchiveDetail archiveDetail = archiveManager.queryArchivesByAid(Long.valueOf(avid));

        // 非三连，校验蓝V商单相关
        if(!isMiddle) {
            // 非账户在白名单内
            final boolean blueVUnderBox = this.accountIdInLabel(accountDto.getAccountId(), creativePositionConfig.getUnderBoxMarkId());
            final boolean cmOrderUnderBox = this.accountIdInLabel(accountDto.getAccountId(), creativePositionConfig.getUnderBoxCmOrderId());
            if (!blueVUnderBox && !cmOrderUnderBox) return false;

            // 非蓝V稿件, 非商单
            if (!isBlueVOrCmOrder(accountDto.getAccountId(), Long.valueOf(avid), archiveDetail, blueVUnderBox, cmOrderUnderBox))
                return false;
        }

        // 是联合投稿
        Integer attribute = Values.zeroIfNull(archiveDetail.getArchive().getAttribute());
        if (Objects.equals(Utils.getIntegerBit(attribute, 24), 1)) return false;

        return true;
    }

    @SneakyThrows
    private boolean judgeIsBlueVOrCmOrder(Integer accountId, Long avid, Long mid, boolean blueVUnderBox, boolean cmOrderUnderBox) {
        if (!Utils.isPositive(avid) || !Utils.isPositive(mid)) {
            return false;
        }
        if (blueVUnderBox) {
            final List<Long> blueMids = flyUnitService.getBlueMidsByAccountId(accountId);
            if (blueMids.contains(mid)) {
                return true;
            }
        }
        if (cmOrderUnderBox) {
            List<PickupOrderDto> list = pickupOrderQuerier.searchPickupOrder(avid);
            return !CollectionUtils.isEmpty(list);
        }
        return false;
    }

    @SneakyThrows
    private boolean isBlueVOrCmOrder(Integer accountId, Long avid, ArchiveDetail detail, boolean blueVUnderBox, boolean cmOrderUnderBox) {
        if (!Utils.isPositive(avid)) {
            return false;
        }
        if (blueVUnderBox) {
            final List<Long> blueMids = flyUnitService.getBlueMidsByAccountId(accountId);
            if (blueMids.contains(detail.getArchive().getMid())) return true;
        }
        if (cmOrderUnderBox) {
            List<PickupOrderDto> list = pickupOrderQuerier.searchPickupOrder(avid);
            return !CollectionUtils.isEmpty(list);
        }
        return false;
    }

    @SneakyThrows
    public boolean middleFlySupportUnderBox(AccountDto accountDto, String avid, Integer salesType, Integer ocpmTarget, Integer ppt) {
        if(Objects.equals(ppt, PromotionPurposeType.BRAND_SPREAD.getCode()) &&
                Objects.equals(ocpmTarget, OcpcTargetEnum.APP_CALLUP.getCode())){
            if(this.accountIdInLabel(accountDto.getAccountId(), accLabelConfig.getSupportAppCallupLabelId())){
                return true;
            }
        }

        // 非稿件
        if (!StringUtils.hasText(avid) || !Utils.isPositive(Long.valueOf(avid))) {
            return false;
        }
        ArchiveDetail archiveDetail = archiveManager.queryArchivesByAid(Long.valueOf(avid));

        // 账户部门为【媒介资源预占】
        if (Objects.equals(accountDto.getDepartmentId(), mediaSourceDepartmentId)) return false;

        // 是联合投稿
        Integer attribute = Values.zeroIfNull(archiveDetail.getArchive().getAttribute());
        if (Objects.equals(Utils.getIntegerBit(attribute, 24), 1)) return false;

        // 评论链接点击、框下链接点击、微信复制等oCPM目标
        if (SalesType.CPM.getCode() == salesType && Utils.isPositive(ocpmTarget) && this.judgeNotCoreTargetSupportUnderBox4Middle(ocpmTarget)) {
            return true;
        }
        if (Objects.equals(ppt, PromotionPurposeType.BRAND_SPREAD.getCode())) {
            // 非cpm
            if (!Objects.equals(salesType, SalesType.CPM.getCode())) {
                return false;
            }
            // 有目标且非播放
            if (!Objects.equals(ocpmTarget, 0) && !Objects.equals(ocpmTarget, OcpcTargetEnum.VIDEO_PLAY.getCode())) {
                return false;
            }

            return true;

        } else if (Objects.equals(ppt, PromotionPurposeType.ENTERPRISE_PROMOTION.getCode())) {
            // 目标非涨粉
            return Objects.equals(ocpmTarget, OcpcTargetEnum.USER_FOLLOW.getCode());
        }
        return false;
    }

    private boolean accountIdInSmallCard43WhiteList(List<Integer> labelIds){
        return labelIds.contains(creativePositionConfig.getFlySmallCard43LabelId());
    }

    public boolean preferenceSlotStory(Integer accountId, Long avid, Integer ocpcTarget){
        Arc arc = archiveServiceProxy.arc(avid).getArc();
        boolean canAutoPlay = archiveService.isAutoPlay(accountId, arc);
        // 查询acc_account_label_mapping表，获取账号的全部标签
        // 查询一次后，后面尽量不再查询acc_account_label_mapping表
        List<Integer> accountLabelIdList = accountLabelService.getLabelIdsByAccountId(accountId);
        // supportUnderBox,isMiddleAd不重要
        if(ocpcTarget !=null && ocpcTarget == 0) {
            List<Integer> positions = this.generateArchiveCpmPosition(canAutoPlay, false,
                    true,accountLabelIdList);
            return positions.contains(SpecificScenesEnum.STORY.getCode());
        }
        //稿件oCPM
        if(ocpcTarget !=null && ocpcTarget > 0) {
            List<Integer> positions = this.generateArchiveOCpmPosition(accountId, avid, ocpcTarget, canAutoPlay,
                    false, true,accountLabelIdList);
            return positions.contains(SpecificScenesEnum.STORY.getCode());
        }
        return false;
    }
}
