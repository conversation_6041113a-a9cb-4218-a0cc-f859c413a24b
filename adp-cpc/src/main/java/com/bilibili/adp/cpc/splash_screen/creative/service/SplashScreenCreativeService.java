package com.bilibili.adp.cpc.splash_screen.creative.service;

import com.bapis.ad.mgk.LaunchPage;
import com.bapis.ad.mgk.ValidateAndGetPageReply;
import com.bilibili.adp.account.dto.AccountDto;
import com.bilibili.adp.account.service.IQueryAccountService;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.bean.TargetRule;
import com.bilibili.adp.common.enums.AuditStatus;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.enums.JumpTypeEnum;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.Page;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.biz.services.account.LaunchAccountV1Service;
import com.bilibili.adp.cpc.biz.services.campaign.api.ILauCampaignService;
import com.bilibili.adp.cpc.biz.services.common.SimpleBFSService;
import com.bilibili.adp.cpc.biz.services.common.bos.BFSInfoBo;
import com.bilibili.adp.cpc.biz.services.creative.CpcSaveCreativeService;
import com.bilibili.adp.cpc.biz.services.misc.AdpCpcMgkService;
import com.bilibili.adp.cpc.biz.services.misc.bos.QueryLandingPageBo;
import com.bilibili.adp.cpc.biz.services.target_package.api.IResTargetItemService;
import com.bilibili.adp.cpc.biz.services.unit.api.ICpcUnitService;
import com.bilibili.adp.cpc.compare.ChangeBo;
import com.bilibili.adp.cpc.compare.constants.MetaType;
import com.bilibili.adp.cpc.compare.constants.SplashScreenCreativeCompareMeta;
import com.bilibili.adp.cpc.core.LaunchCreativeService;
import com.bilibili.adp.cpc.core.LaunchUnitV1Service;
import com.bilibili.adp.cpc.core.constants.CreativeStatus;
import com.bilibili.adp.cpc.core.constants.LaunchStatus;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.*;
import com.bilibili.adp.cpc.dao.querydsl.pos.AccAccountPo;
import com.bilibili.adp.cpc.dao.querydsl.pos.LauCreativeMonitoringPo;
import com.bilibili.adp.cpc.enums.CreativeMonitorType;
import com.bilibili.adp.cpc.enums.MaterialType;
import com.bilibili.adp.cpc.enums.ad.PromotionPurposeType;
import com.bilibili.adp.cpc.splash_screen.creative.bos.*;
import com.bilibili.adp.cpc.splash_screen.creative.converter.CreativeConverter;
import com.bilibili.adp.cpc.splash_screen.creative.converter.ImageConverter;
import com.bilibili.adp.cpc.splash_screen.creative.converter.MonitoringConverter;
import com.bilibili.adp.cpc.splash_screen.creative.enums.ButtonType;
import com.bilibili.adp.cpc.splash_screen.creative.enums.ImageRatio;
import com.bilibili.adp.cpc.splash_screen.creative.enums.InteractStyle;
import com.bilibili.adp.cpc.splash_screen.creative.strategy.bos.CreativeContext;
import com.bilibili.adp.cpc.splash_screen.operation_log.IOperationLogService;
import com.bilibili.adp.cpc.splash_screen.operation_log.OperationContextBo;
import com.bilibili.adp.cpc.splash_screen.operation_log.OperatorBo;
import com.bilibili.adp.cpc.splash_screen.operation_log.enums.OperationType;
import com.bilibili.adp.cpc.splash_screen.operator.OperatorConverter;
import com.bilibili.adp.launch.api.creative.dto.CpcCreativeMonitoringDto;
import com.bilibili.adp.resource.api.targetmeta.TargetType;
import com.bilibili.bjcom.querydsl.BaseQueryFactory;
import com.bilibili.dmp.service.ISoaGroupService;
import com.bilibili.dmp.soa.entity.PeopleGroup;
import com.bilibili.location.api.service.IBusMarkService;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.sql.Timestamp;
import java.text.MessageFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.bilibili.adp.cpc.dao.config.PlatformMySqlConfig.*;
import static com.bilibili.adp.cpc.dao.ad_core.querydsl.QLauSplashScreenCreative.lauSplashScreenCreative;
import static com.bilibili.adp.cpc.dao.ad_core.querydsl.QLauUnitCreative.lauUnitCreative;

@Slf4j
@Service
@RequiredArgsConstructor
public class SplashScreenCreativeService {
    private static final String BFS_PATH = "effect_ad/splash_screen";
    private static final String WEBP_SUFFIX = "@.webp";
    private static final String WEBP_EXT = "webp";
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    /**
     * 效果闪屏 默认广告标 = 1，0 无， 1 广告， 2 推广
     */
    private static final int CM_MARK = 1;
    /**
     * 广告版本
     */
    private static final int ADP_VERSION = 5;
    /**
     * 是否中台广告
     */
    private static final int IS_MIDDLE_AD = 1;
    /**
     * 素材点击区域 0-常规区域 1-全素材区域
     */
    private static final int CLICK_AREA = 0;
    /**
     * 是否可跳过: 0-否 1-是
     */
    private static final int IS_SKIP = 1;
    /**
     * 展示时长：3s 5s
     * 图片3s
     * 视频5s
     */
    private static final int DURATION = 3;
    /**
     * 广告标和跳过按钮位置样式:0-默认样式,广告标右上,跳过按钮右下 1-实验样式,广告标右上,跳过按钮右下(虽然和0样式相同,但是0表示维持以前的默认配置,位置和按钮大小都不做变更) 2-广告标左上，跳过按钮右上
     */
    private static final int MARK_WITH_SKIP_STYLE = 0;
    /**
     * 跳过按钮占屏幕高度
     */
    private static final float SKIP_BUTTON_HEIGHT = 0.0557F;

    @Resource(name = AD_CORE_BQF)
    private BaseQueryFactory adCoreBqf;

    @Resource(name = "okHttpClient")
    private  OkHttpClient okHttpClient;


    @Value("${effect.ssa.query.size.limit:3000}")
    private Integer creativeSizeLimit;
    private final SimpleBFSService simpleBFSService;
    private final AdpCpcMgkService adpCpcMgkService;
    private final IQueryAccountService queryAccountService;
    private final ICpcUnitService lauUnitService;
    private final ILauCampaignService campaignService;
    private final IBusMarkService busMarkService;
    private final ISoaGroupService dmpCrowdPackService;
    private final LaunchAccountV1Service launchAccountV1Service;
    private final LaunchUnitV1Service launchUnitV1Service;
    private final LaunchCreativeService launchCreativeService;
    private final CpcSaveCreativeService cpcSaveCreativeService;
    private final SplashScreenContextService splashScreenContextService;
    private final SplashScreenCreativeJumpUrlService splashScreenCreativeJumpUrlService;
    private final SplashScreenCreativeExtraService splashScreenCreativeExtraService;
    private final SplashScreenCreativeQualificationService splashScreenCreativeQualificationService;
    private final SplashScreenCreativeMonitoringService splashScreenCreativeMonitoringService;
    private final SplashScreenCreativeImageService splashScreenCreativeImageService;
    private final SplashScreenCreativeButtonService splashScreenCreativeButtonService;
    private final SplashScreenCreativeTemplateService splashScreenCreativeTemplateService;
    private final IResTargetItemService resTargetItemService;
    private final IOperationLogService operationLogService;

    private static LocalDate str2ld(String dateStr) {
        return LocalDate.parse(dateStr, DATE_FORMATTER);
    }
    public List<LaunchPage> landingPage(Integer accountId, Integer unitId) {
        QueryLandingPageBo build = QueryLandingPageBo.builder()
                .accountId(accountId)
                .unitId(unitId)
                .build();
        return adpCpcMgkService.getMgkLandingPages(build);
    }

    @SneakyThrows
    public SplashScreenImage uploadImage(Integer ratio, MultipartFile multipartFile) {
        SplashScreenImage.SplashScreenImageBuilder builder = SplashScreenImage.builder();
        try (final InputStream is = multipartFile.getInputStream()) {
            final String ext = simpleBFSService.getExt(multipartFile.getOriginalFilename());
            final byte[] bytes = IOUtils.toByteArray(is);
            final BFSInfoBo bfsInfoBo = simpleBFSService.upload(BFS_PATH, multipartFile.getContentType(), bytes, ext);
            final String url = bfsInfoBo.getUrl();
            final Request request = new Request.Builder()
                    .header(HttpHeaders.CONNECTION, "close")
                    .get()
                    .url(url + WEBP_SUFFIX)
                    .build();
            try (Response response = okHttpClient.newCall(request).execute()) {
                Assert.notNull(response.body(), "获取webp失败");
                final byte[] bytes1 = response.body().bytes();
                final MediaType mediaType = response.body().contentType();
                log.info("content type from bfs :{}", Optional.ofNullable(mediaType).map(MediaType::toString));
                final String contentType = Optional.ofNullable(mediaType).map(MediaType::toString).orElse(multipartFile.getContentType());
                final BFSInfoBo webpBfsInfo = simpleBFSService.upload(BFS_PATH, contentType, bytes1, WEBP_EXT);
                builder.url(webpBfsInfo.getUrl());
                builder.md5(webpBfsInfo.getMd5());
            }
            try (final InputStream inputStream = new ByteArrayInputStream(bytes)){
                final BufferedImage bufferedImage = ImageIO.read(inputStream);
                Assert.notNull(bufferedImage, "");
                final ImageRatio imageRatio = ImageRatio.getByCode(ratio);
                final boolean validRatio = imageRatio.getWidth() == bufferedImage.getWidth() && imageRatio.getHeight() == bufferedImage.getHeight();
                Assert.isTrue(validRatio, "尺寸错误");
                builder.width(bufferedImage.getWidth());
                builder.height(bufferedImage.getHeight());
            }
        }
        return builder.build();
    }

    /**
     * 参考
     * SELECT * from ssa_splash_screen where id  = 19990
     * SELECT * from ssa_splash_screen_button where splash_screen_id  = 19990 and platform = 1 and is_deleted = 0
     * SELECT * from ssa_splash_screen_jump_info where splash_screen_id  = 19990 and platform_id = 1 and is_deleted = 0
     * SELECT * from ssa_splash_screen_image where splash_screen_id  = 19990 and platform_id = 1
     */
    @Transactional(transactionManager = AD_CORE_TM, rollbackFor = Exception.class)
    public List<Integer> save(OperatorBo operator, SplashScreenUnitCreatives unitCreatives) {
        final Integer unitId = unitCreatives.getUnitId();
        final SplashScreenUnitCreatives preUnitCreatives = getUnitCreatives(unitId);
        final Map<Integer, SplashScreenCreative> prevSplashScreenCreativeMap = preUnitCreatives
                .getCreatives()
                .stream()
                .collect(Collectors.toMap(SplashScreenCreative::getCreativeId, Function.identity()));
        final List<Integer> existingCreativeIds = unitCreatives
                .getCreatives()
                .stream()
                .map(SplashScreenCreative::getCreativeId)
                .filter(Utils::isPositive)
                .collect(Collectors.toList());
        final List<LauUnitCreativePo> existingCreatives = launchCreativeService.list(unitId);
        final Map<Integer, LauUnitCreativePo> existingCreativeMap = existingCreatives
                .stream()
                .collect(Collectors.toMap(LauUnitCreativePo::getCreativeId, Function.identity()));
        delete(unitId, existingCreativeIds);
        final List<Integer> creativeIds = new ArrayList<>();
        final LauUnitPo lauUnitPo = launchUnitV1Service.get(unitId);
        final Integer promotionPurposeType = lauUnitPo.getPromotionPurposeType();
        final Integer accountId = lauUnitPo.getAccountId();
        final Integer campaignId = lauUnitPo.getCampaignId();
        final AccAccountPo accAccountPo = launchAccountV1Service.get(accountId);
        final CreativeContext context = CreativeContext.builder()
                .accAccountPo(accAccountPo)
                .lauUnitPo(lauUnitPo)
                .build();
        splashScreenContextService.buildContext(promotionPurposeType, context);
        Assert.notNull(lauUnitPo, MessageFormat.format("非法单元id : [{0,number,#}]", lauUnitPo.getUnitId()));
        for (SplashScreenCreative curSplashScreenCreative : unitCreatives.getCreatives()) {
            //先排个序 用宽度排序，闪屏图片是 1600 * 2560 和 1280 * 2560 谨防前端不按顺序传 导致对比失误
            curSplashScreenCreative.getImages().sort(Comparator.comparingInt(SplashScreenImage::getWidth).reversed());
            final LauUnitCreativePo existingCreativePo = existingCreativeMap.get(curSplashScreenCreative.getCreativeId());
            final SplashScreenCreative prevSplashScreenCreative = prevSplashScreenCreativeMap.get(curSplashScreenCreative.getCreativeId());
            final List<ChangeBo> baseChanges = operationLogService.changes(prevSplashScreenCreative, curSplashScreenCreative, SplashScreenCreativeCompareMeta.META_MAP, MetaType.BASE.getCode(), true);
            final boolean needReAudit = baseChanges.stream().anyMatch(ChangeBo::isReAudit);
            Integer auditStatus;
            Integer creativeStatus;
            Integer status;
            if (Objects.isNull(existingCreativePo)) {
                auditStatus = com.bilibili.adp.cpc.core.constants.AuditStatus.AUDITING;
                creativeStatus = com.bilibili.adp.cpc.core.constants.CreativeStatus.AUDITING;
                status = com.bilibili.adp.cpc.core.constants.LaunchStatus.STARTED;
            } else {
                if (needReAudit) {
                    auditStatus = com.bilibili.adp.cpc.core.constants.AuditStatus.AUDITING;
                    if (existingCreativePo.getStatus() == com.bilibili.adp.cpc.core.constants.LaunchStatus.STARTED) {
                        creativeStatus = com.bilibili.adp.cpc.core.constants.CreativeStatus.AUDITING;
                    } else {
                        creativeStatus = existingCreativePo.getCreativeStatus();
                    }
                } else {
                    auditStatus = existingCreativePo.getAuditStatus();
                    creativeStatus = existingCreativePo.getCreativeStatus();
                }
                status = existingCreativePo.getStatus();
            }
            ValidateAndGetPageReply pageReply = splashScreenCreativeJumpUrlService.getPageReply(curSplashScreenCreative.getJumpType(), curSplashScreenCreative.getJumpUrl());
            final Long pageId = pageReply.getPageId();
            final int adVersionControlId = pageReply.getAdVersionControlId();
            final String jumpUrl = pageReply.getJumpUrl();
            final String formattedJumpUrl = splashScreenCreativeJumpUrlService.addMacro(promotionPurposeType, context, jumpUrl);
            final Integer interactType = curSplashScreenCreative.getInteractType();
            curSplashScreenCreative.setAccountId(accountId);
            curSplashScreenCreative.setCampaignId(campaignId);
            curSplashScreenCreative.setUnitId(unitId);
            curSplashScreenCreative.setJumpUrl(formattedJumpUrl);
            curSplashScreenCreative.setBeginTime(str2ld(lauUnitPo.getLaunchBeginDate()));
            curSplashScreenCreative.setEndTime(str2ld(lauUnitPo.getLaunchEndDate()));
            curSplashScreenCreative.setSalesType(lauUnitPo.getSalesType());
            curSplashScreenCreative.setCmMark(CM_MARK);
            curSplashScreenCreative.setAdpVersion(ADP_VERSION);
            curSplashScreenCreative.setIsMiddleAd(IS_MIDDLE_AD);
            curSplashScreenCreative.setClickArea(CLICK_AREA);
            curSplashScreenCreative.setIsSkip(IS_SKIP);
            curSplashScreenCreative.setDuration(DURATION);
            curSplashScreenCreative.setMarkWithSkipStyle(MARK_WITH_SKIP_STYLE);
            curSplashScreenCreative.setSkipButtonHeight(SKIP_BUTTON_HEIGHT);
            curSplashScreenCreative.setMgkPageId(pageId);
            curSplashScreenCreative.setAdVersionControlId(adVersionControlId);
            curSplashScreenCreative.setStatus(status);
            curSplashScreenCreative.setCreativeStatus(creativeStatus);
            curSplashScreenCreative.setAuditStatus(auditStatus);
            //主创意
            final LauUnitCreativePo creativePo = CreativeConverter.MAPPER.bo2CreativePo(curSplashScreenCreative);
            final Integer creativeId;
            if (Utils.isPositive(creativePo.getCreativeId())) {
                creativeId = creativePo.getCreativeId();
                adCoreBqf.update(lauUnitCreative).updateBean(creativePo);
            } else {
                creativeId = adCoreBqf.insert(lauUnitCreative).insertGetKey(creativePo);
                updatePromotionPurposeContent(context, formattedJumpUrl, creativeId);
            }
            //闪屏创意
            curSplashScreenCreative.setCreativeId(creativeId);
            final LauSplashScreenCreativePo splashScreenCreativePo = CreativeConverter.MAPPER.bo2SplashScreenCreativePo(curSplashScreenCreative);
            final LauSplashScreenCreativePo existingPo = get(creativeId);
            if (Objects.nonNull(existingPo)) {
                splashScreenCreativePo.setId(existingPo.getId());
                adCoreBqf.update(lauSplashScreenCreative).updateBean(splashScreenCreativePo);
            } else {
                adCoreBqf.insert(lauSplashScreenCreative).insertBean(splashScreenCreativePo);
            }
            splashScreenCreativeQualificationService.save(accountId, campaignId, unitId, creativeId, curSplashScreenCreative.getQualificationPackageId(), curSplashScreenCreative.getQualificationIds());
            final List<LauSplashScreenCreativeImagePo> creativeImagesPos = SplashScreenCreativeImageService.generatePos(creativeId, curSplashScreenCreative.getImages());
            splashScreenCreativeImageService.save(creativeId, creativeImagesPos);
            splashScreenCreativeButtonService.save(curSplashScreenCreative);
            splashScreenCreativeTemplateService.save(interactType, accountId, campaignId, unitId, creativeId);
            // 安卓游戏 额外塞一个游戏中心监控链接
            if (Objects.equals(PromotionPurposeType.ON_SHELF_GAME.getCode(), lauUnitPo.getPromotionPurposeType())) {
                final boolean isInner = Objects.equals(1, accAccountPo.getIsInner());
                final LauUnitGamePo lauUnitGamePo = context.getLauUnitGamePo();
                final CpcCreativeMonitoringDto cpcCreativeMonitoringDto = cpcSaveCreativeService.genGameHiddenMonitoring(lauUnitGamePo.getGameBaseId(), lauUnitGamePo.getPlatformType(), lauUnitGamePo.getSubPkg(), isInner);
                final SplashScreenMonitoring splashScreenMonitoring = MonitoringConverter.MAPPER.dto2Bo(cpcCreativeMonitoringDto);
                final List<LauCreativeMonitoringPo> creativeMonitoringPos = SplashScreenCreativeMonitoringService.generatePos(unitId, creativeId, Collections.singletonList(splashScreenMonitoring));
                splashScreenCreativeMonitoringService.save(creativeId, creativeMonitoringPos);
            }
            creativeIds.add(creativeId);
            // 保存操作日志
            Integer operationType = Objects.isNull(prevSplashScreenCreative) ? OperationType.INSERT : OperationType.UPDATE;
            final List<ChangeBo> statusChanges = operationLogService.changes(prevSplashScreenCreative, curSplashScreenCreative, SplashScreenCreativeCompareMeta.META_MAP, MetaType.STATUS.getCode(), true);
            final List<ChangeBo> changes = Stream.of(baseChanges, statusChanges).flatMap(List::stream).collect(Collectors.toList());
            final OperationContextBo ctx = OperatorConverter.MAPPER.toContext("lau_unit_creative", creativeId, operationType, operator, changes);
            operationLogService.save(Collections.singletonList(ctx));
        }
        return creativeIds;
    }

    public SplashScreenUnitCreatives getUnitCreatives(Integer unitId) {
        final LauUnitPo lauUnitPo = launchUnitV1Service.get(unitId);
        final Integer promotionPurposeType = lauUnitPo.getPromotionPurposeType();
        final List<LauUnitCreativePo> creativePos = launchCreativeService.list(unitId);
        if (CollectionUtils.isEmpty(creativePos)) {
            return SplashScreenUnitCreatives.builder()
                    .unitId(unitId)
                    .creatives(Collections.emptyList())
                    .build();
        }
        final Map<Integer, LauUnitCreativePo> creativeMap = creativePos
                .stream()
                .collect(Collectors.toMap(LauUnitCreativePo::getCreativeId, Function.identity()));
        final List<LauSplashScreenCreativePo> splashScreenCreativePos = list(unitId);
        if (CollectionUtils.isEmpty(splashScreenCreativePos)) {
            return SplashScreenUnitCreatives.builder()
                    .unitId(unitId)
                    .creatives(Collections.emptyList())
                    .build();
        }
        final Map<Integer, LauSplashScreenCreativePo> splashScreenCreativeMap = splashScreenCreativePos
                .stream()
                .collect(Collectors.toMap(LauSplashScreenCreativePo::getCreativeId, Function.identity()));
        final Set<Integer> creativeIds = creativeMap.keySet();
        final List<LauCreativeExtraPo> extraPos = splashScreenCreativeExtraService.list(creativeIds);
        final Map<Integer, LauCreativeExtraPo> creativeExtraMap = extraPos
                .stream()
                .collect(Collectors.toMap(LauCreativeExtraPo::getCreativeId, Function.identity()));
        final List<LauCreativeQualificationPo> qualificationPos = splashScreenCreativeQualificationService.list(creativeIds);
        final Map<Integer, List<LauCreativeQualificationPo>> creativeQualificationsMap = qualificationPos
                .stream()
                .collect(Collectors.groupingBy(LauCreativeQualificationPo::getCreativeId));
        final List<LauSplashScreenCreativeImagePo> imagePos = splashScreenCreativeImageService.list(creativeIds);
        final Map<Integer, List<LauSplashScreenCreativeImagePo>> creativeImagesMap = imagePos
                .stream()
                .collect(Collectors.groupingBy(LauSplashScreenCreativeImagePo::getCreativeId));
        final List<LauSplashScreenCreativeButtonPo> buttonPos = splashScreenCreativeButtonService.list(creativeIds);
        final Map<Integer, List<LauSplashScreenCreativeButtonPo>> creativeButtonsMap = buttonPos
                .stream()
                .collect(Collectors.groupingBy(LauSplashScreenCreativeButtonPo::getCreativeId));
        final List<SplashScreenCreative> splashScreenCreatives = creativeIds
                .stream()
                .map(creativeId -> {
                    LauUnitCreativePo creative = creativeMap.get(creativeId);
                    String jumpUrlWithoutMacro = splashScreenCreativeJumpUrlService.removeMacro(promotionPurposeType, creative.getPromotionPurposeContent());
                    String jumpUrl = splashScreenCreativeJumpUrlService.parse(creative.getJumpType(), jumpUrlWithoutMacro);
                    LauSplashScreenCreativePo splashScreenCreative = splashScreenCreativeMap.get(creativeId);
                    Integer qualificationPackageId = Optional.ofNullable(creativeExtraMap.get(creativeId)).map(LauCreativeExtraPo::getQualificationPackageId).orElse(0);
                    List<Integer> qualificationIds = creativeQualificationsMap.getOrDefault(creativeId, Collections.emptyList())
                            .stream()
                            .map(LauCreativeQualificationPo::getQualificationId)
                            .collect(Collectors.toList());
                    List<LauSplashScreenCreativeImagePo> images = creativeImagesMap.getOrDefault(creativeId, Collections.emptyList())
                            .stream()
                            //顺序可能有问题 先排个序 用宽度排序，闪屏图片是1600 * 2560 和 1280 * 2560 这样也能保证前端拿到的是10:16的图片在前
                            .sorted(Comparator.comparingInt(LauSplashScreenCreativeImagePo::getWidth).reversed())
                            .collect(Collectors.toList());
                    List<LauSplashScreenCreativeButtonPo> buttons = creativeButtonsMap.getOrDefault(creativeId, Collections.emptyList());
                    Optional<LauSplashScreenCreativeButtonPo> lottieButton = buttons.stream().filter(button -> Objects.equals(button.getButtonType(), ButtonType.LOTTIE_BUTTON.getCode())).findFirst();
                    Optional<LauSplashScreenCreativeButtonPo> customizedContentButton = buttons.stream().filter(button -> Objects.equals(button.getButtonType(), ButtonType.CUSTOMIZED_CONTENT_BUTTON.getCode())).findFirst();
                    return SplashScreenCreative.builder()
                            .accountId(creative.getAccountId())
                            .campaignId(creative.getCampaignId())
                            .unitId(creative.getUnitId())
                            .creativeId(creative.getCreativeId())
                            .creativeName(creative.getCreativeName())
                            .jumpType(creative.getJumpType())
                            .jumpUrl(jumpUrl)
                            .schemaUrl(creative.getSchemeUrl())
                            .customizedImpUrl(creative.getCustomizedImpUrl())
                            .customizedClickUrl(creative.getCustomizedClickUrl())
                            .qualificationPackageId(qualificationPackageId)
                            .qualificationIds(qualificationIds)
                            .navigationType(splashScreenCreative.getNavigationType())
                            .interactType(splashScreenCreative.getInteractType())
                            .appPackageName(splashScreenCreative.getAppPackageName())
                            .images(images.stream().map(ImageConverter.MAPPER::po2Bo).collect(Collectors.toList()))
                            .jumpImageUrl(lottieButton.map(LauSplashScreenCreativeButtonPo::getJumpImageUrl).orElse(""))
                            .jumpImageMd5(lottieButton.map(LauSplashScreenCreativeButtonPo::getJumpImageMd5).orElse(""))
                            .schemaImageUrl(lottieButton.map(LauSplashScreenCreativeButtonPo::getSchemaImageUrl).orElse(""))
                            .schemaImageMd5(lottieButton.map(LauSplashScreenCreativeButtonPo::getSchemaImageMd5).orElse(""))
                            .jumpGuideContent(customizedContentButton.map(LauSplashScreenCreativeButtonPo::getJumpGuideContent).orElse(""))
                            .schemaGuideContent(customizedContentButton.map(LauSplashScreenCreativeButtonPo::getSchemaGuideContent).orElse(""))
                            .status(creative.getStatus())
                            .creativeStatus(creative.getCreativeStatus())
                            .auditStatus(creative.getAuditStatus())
                            .build();
                })
                .sorted(Comparator.comparingInt(SplashScreenCreative::getCreativeId))
                .collect(Collectors.toList());
        return SplashScreenUnitCreatives.builder()
                .unitId(unitId)
                .creatives(splashScreenCreatives)
                .build();
    }

    public LauSplashScreenCreativePo get(Integer creativeId) {
        return adCoreBqf.selectFrom(lauSplashScreenCreative)
                .where(lauSplashScreenCreative.creativeId.eq(creativeId))
                .fetchFirst();
    }


    public List<LauSplashScreenCreativePo> getByCreativeIds(List<Integer> creativeIds) {
        return adCoreBqf.selectFrom(lauSplashScreenCreative)
                .where(lauSplashScreenCreative.creativeId.in(creativeIds))
                .fetch();
    }

    public PageResult<SplashScreenBaseCreative> querySsaCreativeByPage(QuerySplashScreenCreative query, int page, int size){

        //因为创意主表没有标识能表明这个创意是闪屏创意,只能先根据时间查询闪屏附表，然后再用创意id查询主表
        List<Integer> ssaCreativeIds = CollectionUtils.isEmpty(query.getCreativeIds()) ?
                getValidSsaCreativeIds(query.getTimeFrom(), query.getTimeTo())
                : query.getCreativeIds();

       //防止in的范围太大,把数据库打挂
        Assert.isTrue(ssaCreativeIds.size() < creativeSizeLimit,
               "您当前查询的数据量过大,请缩小时间范围后再尝试!");
        Page pageBean = Page.valueOf(page, size);
        final long count = adCoreBqf.selectFrom(lauUnitCreative)
                .where(lauUnitCreative.creativeId.in(ssaCreativeIds))
                .whereIfNotNull(query.getAuditStatus(), lauUnitCreative.auditStatus::eq)
                .whereIfNotNull(query.getCreativeStatus(), lauUnitCreative.creativeStatus::eq)
                .whereIfNotNull(query.getAccountId(), lauUnitCreative.accountId::eq)
                .where(lauUnitCreative.isDeleted.eq(IsDeleted.VALID.getCode()))
                .fetchCount();
        if(!Utils.isPositive(count)){
            return PageResult.emptyPageResult();
        }

        final List<LauUnitCreativePo> creativePos = adCoreBqf.selectFrom(lauUnitCreative)
                .where(lauUnitCreative.creativeId.in(ssaCreativeIds))
                .whereIfNotNull(query.getAuditStatus(), lauUnitCreative.auditStatus::eq)
                .whereIfNotNull(query.getCreativeStatus(), lauUnitCreative.creativeStatus::eq)
                .whereIfNotNull(query.getAccountId(), lauUnitCreative.accountId::eq)
                .where(lauUnitCreative.isDeleted.eq(IsDeleted.VALID.getCode())).orderBy("mtime desc")
                .offset(pageBean.getOffset()).limit(pageBean.getLimit()).fetch();

        final List<SplashScreenBaseCreative> baseCreatives = buildSsaBaseCreative(creativePos);

       return PageResult.<SplashScreenBaseCreative>builder().total(Math.toIntExact(count))
               .records(baseCreatives).build();
    }

    public List<Integer> getValidSsaCreativeIds(Timestamp beginTime, Timestamp endTime){
        List<LauSplashScreenCreativePo> ssaCreativePos = getValidSsaCreative(beginTime, endTime);
        if(CollectionUtils.isEmpty(ssaCreativePos)){
            return new ArrayList<>();
        }

        return ssaCreativePos.stream().map(LauSplashScreenCreativePo::getCreativeId)
                .distinct().collect(Collectors.toList());
    }

    private List<LauSplashScreenCreativePo> getValidSsaCreative(Timestamp beginTime, Timestamp endTime){
        Assert.notNull(beginTime, "查询开始时间不能为空");
        Assert.notNull(endTime, "查询结束时间不能为空");
        return adCoreBqf.selectFrom(lauSplashScreenCreative)
                .where(lauSplashScreenCreative.mtime.loe(endTime))
                .where(lauSplashScreenCreative.mtime.goe(beginTime))
                .where(lauSplashScreenCreative.isDeleted.eq(IsDeleted.VALID.getCode())).fetch();
    }

    private List<SplashScreenBaseCreative> buildSsaBaseCreative(List<LauUnitCreativePo> creativePos){
        final List<Integer> accountIds = creativePos.stream().map(LauUnitCreativePo::getAccountId).distinct()
                .collect(Collectors.toList());
        final List<Integer> creativeIds = creativePos.stream().map(LauUnitCreativePo::getCreativeId)
                .collect(Collectors.toList());
        final Map<Integer, AccountDto> accountDtoMap = queryAccountService
                .getAccountDtoMapInAccountIds(accountIds);
        final Map<Integer, LauSplashScreenCreativeImagePo> creativeId2Image = splashScreenCreativeImageService
                .get1to2Image(creativeIds);
        return creativePos.stream().map(creativePo->{
            SplashScreenBaseCreative baseCreative = new SplashScreenBaseCreative();
            BeanUtils.copyProperties(creativePo, baseCreative);
            AccountDto accountDto = accountDtoMap.get(creativePo.getAccountId());
            baseCreative.setAccountName(accountDto.getUsername());
            baseCreative.setAuditStatusDesc(AuditStatus.getByCode(creativePo.getAuditStatus()).getName());
            baseCreative.setCreativeStatusDesc(com.bilibili.adp.launch.api.common.
                    CreativeStatus.getByCode(creativePo.getCreativeStatus()).getDesc());
            baseCreative.setJumpTypeDesc(JumpTypeEnum.getByCode(creativePo.getJumpType()).getDesc());
            baseCreative.setBeginTime(creativePo.getBeginTime().toString());
            baseCreative.setEndTime(creativePo.getEndTime().toString());
            LauSplashScreenCreativeImagePo imagePo = creativeId2Image.getOrDefault(creativePo.getCreativeId(),
                    new LauSplashScreenCreativeImagePo());
            baseCreative.setBaseImageUrl(imagePo.getUrl());
            return baseCreative;
        }).collect(Collectors.toList());
    }


    public List<LauSplashScreenCreativePo> list(Integer unitId) {
        return adCoreBqf.selectFrom(lauSplashScreenCreative)
                .where(lauSplashScreenCreative.unitId.eq(unitId))
                .where(lauSplashScreenCreative.isDeleted.eq(0))
                .fetch();
    }

    public void delete(Integer unitId, List<Integer> existingCreativeIds) {
        adCoreBqf.update(lauUnitCreative)
                .set(lauUnitCreative.isDeleted, 1)
                .set(lauUnitCreative.creativeStatus, CreativeStatus.DELETED)
                .set(lauUnitCreative.status, LaunchStatus.DELETED)
                .where(lauUnitCreative.unitId.eq(unitId))
                .whereIfNotEmpty(existingCreativeIds, lauUnitCreative.creativeId::notIn)
                .execute();
        adCoreBqf.update(lauSplashScreenCreative)
                .set(lauSplashScreenCreative.isDeleted, 1)
                .where(lauSplashScreenCreative.unitId.eq(unitId))
                .whereIfNotEmpty(existingCreativeIds, lauSplashScreenCreative.creativeId::notIn)
                .execute();
    }

    public SplashScreenCreativeDetail querySsaDetail(int creative) throws ServiceException {
        Assert.isTrue(creative > 0, "创意id不能为0");
        LauSplashScreenCreativePo ssaCreative = get(creative);
        Assert.notNull(ssaCreative, "创意id" + creative + "查找不到有效的闪屏创意");

        LauUnitCreativePo creativePo = adCoreBqf.selectFrom(lauUnitCreative)
                .where(lauUnitCreative.creativeId.eq(creative))
                .where(lauUnitCreative.isDeleted.eq(IsDeleted.VALID.getCode()))
                .fetchFirst();
        Assert.notNull(creativePo, "创意id" + creative + "查找不到有效的闪屏创意");
        SplashScreenCreativeDetail detail = new SplashScreenCreativeDetail();
        BeanUtils.copyProperties(creativePo, detail);
        detail.setJumpTypeDesc(JumpTypeEnum.getByCode(creativePo.getJumpType()).getDesc());
        detail.setIsSkip(ssaCreative.getIsSkip());
        detail.setDuration(ssaCreative.getDuration());
        detail.setMaterialType(MaterialType.IMAGE.getCode());
        detail.setJumpTypeDesc(JumpTypeEnum.getByCode(creativePo.getJumpType()).getDesc());
        detail.setBeginTime(creativePo.getBeginTime().toString());
        detail.setEndTime(creativePo.getEndTime().toString());
        if(creativePo.getBusMarkId() > 0){
            detail.setBusMarkName(busMarkService.getBusMark(creativePo.getBusMarkId()).getName());
        }
        List<LauCreativeMonitoringPo> monitoringPos = splashScreenCreativeMonitoringService.list(creative);
        if(!CollectionUtils.isEmpty(monitoringPos)){
            List<LauCreativeMonitoringPo> customizedImpUrlList = monitoringPos.stream()
                    .filter(monitoringPo-> CreativeMonitorType.VIEW_MONITOR.getCode()
                            .equals(monitoringPo.getType())).collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(customizedImpUrlList)){
                detail.setCustomizedImpUrl(customizedImpUrlList.get(0).getUrl());
            }
            List<LauCreativeMonitoringPo> customizedClickUrlList = monitoringPos.stream()
                    .filter(monitoringPo-> CreativeMonitorType.CLICK_MONITOR.getCode()
                            .equals(monitoringPo.getType())).collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(customizedClickUrlList)){
                detail.setCustomizedClickUrl(customizedClickUrlList.get(0).getUrl());
            }
        }
        List<LauSplashScreenCreativeButtonPo> buttonPos = splashScreenCreativeButtonService.list(creative);
        if(!CollectionUtils.isEmpty(buttonPos)){
            //文案按钮
            LauSplashScreenCreativeButtonPo contentButton = buttonPos.stream()
                    .max(Comparator.comparing(LauSplashScreenCreativeButtonPo::getSeq))
                    .orElse(new LauSplashScreenCreativeButtonPo());
            detail.setJumpGuideContent(contentButton.getJumpGuideContent());
            detail.setSchemaGuideContent(contentButton.getSchemaGuideContent());
            //动效按钮
            LauSplashScreenCreativeButtonPo dynamicButton = buttonPos.stream()
                    .min(Comparator.comparing(LauSplashScreenCreativeButtonPo::getSeq))
                    .orElse(new LauSplashScreenCreativeButtonPo());
            detail.setJumpImageUrl(dynamicButton.getJumpImageUrl());
            detail.setJumpImageMd5(dynamicButton.getJumpImageMd5());
            detail.setSchemaImageUrl(dynamicButton.getSchemaImageUrl());
            detail.setSchemaImageMd5(dynamicButton.getSchemaImageMd5());
            detail.setInteractStyle(dynamicButton.getInteractStyle());
            detail.setInteractStyleDesc(InteractStyle.getByCode(dynamicButton.getInteractStyle()).getDesc());
        }

        var unitDto = lauUnitService.loadCpcUnit(creativePo.getUnitId());
        detail.setUnitName(unitDto.getUnitName());
        detail.setTarget(buildSsaTarget(unitDto.getTargetRules(), unitDto.getCrowdPackIds(), unitDto.getExcludeCrowdPackIds()));

        var campaignDto = campaignService.getCampaignDtoById(creativePo.getCampaignId());
        detail.setCampaignName(campaignDto.getCampaignName());
        return detail;
    }

    private SplashScreenTarget buildSsaTarget(List<TargetRule> targetRules, List<Integer> crowdPackIds,
                                              List<Integer> excludeCrowdPackIds){
        SplashScreenTarget target = new SplashScreenTarget();
        if(!CollectionUtils.isEmpty(targetRules)) {
            List<Integer> targetIds = targetRules.stream()
                    .map(TargetRule::getValueIds).filter(valueIds -> !CollectionUtils.isEmpty(valueIds))
                    .flatMap(Collection::stream).collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(targetIds)) {
                Map<Integer, String> item2Name = resTargetItemService.getTargetItemId2NameMapInIds(targetIds);
                targetRules.forEach(targetRule -> {
                    switch (TargetType.getByCode(targetRule.getRuleType())) {
                        case AGE:
                            target.setAge(processTargets(targetRule.getValueIds(), item2Name));
                            break;
                        case GENDER:
                            target.setGender(processTargets(targetRule.getValueIds(), item2Name));
                            break;
                        case AREA:
                            target.setArea(processTargets(targetRule.getValueIds(), item2Name));
                            break;
                        case OS:
                            target.setPlatform(processTargets(targetRule.getValueIds(), item2Name));
                            break;
                        case DEVICE_BRAND:
                            target.setDeviceBrand(processTargets(targetRule.getValueIds(), item2Name));
                            break;
                        default:
                            break;
                    }
                });
            }
        }
        if(!CollectionUtils.isEmpty(crowdPackIds)){
            List<PeopleGroup> peopleGroups = dmpCrowdPackService.getPeopleGroupByIds(crowdPackIds);
            if(!CollectionUtils.isEmpty(peopleGroups)){
                target.setCrowdPacks(peopleGroups.stream().map(PeopleGroup::getGroupName)
                        .collect(Collectors.toList()).toString());
            }
        }
        if(!CollectionUtils.isEmpty(excludeCrowdPackIds)){
            List<PeopleGroup> peopleGroups = dmpCrowdPackService.getPeopleGroupByIds(excludeCrowdPackIds);
            if(!CollectionUtils.isEmpty(peopleGroups)){
                target.setExcludeCrowdPacks(peopleGroups.stream().map(PeopleGroup::getGroupName)
                        .collect(Collectors.toList()).toString());
            }
        }
        return target;
    }

    public String processTargets(List<Integer> valuesIds, Map<Integer, String> item2Name) {
        return CollectionUtils.isEmpty(valuesIds) ?
                "" : valuesIds.stream().map(valuesId-> item2Name.getOrDefault(valuesId,
                "")).collect(Collectors.joining(","));
    }


    private void updatePromotionPurposeContent(CreativeContext context, String promotionPurposeContent, Integer creativeId) {
        if (context != null && context.getLauUnitPo() != null && PromotionPurposeType.GOODS_LIVE.getCode() == context.getLauUnitPo().getPromotionPurposeType()) {
            final UriComponentsBuilder builder = UriComponentsBuilder.fromUriString(promotionPurposeContent);
            builder.replaceQueryParam("creative_id", creativeId);
            builder.replaceQueryParam("linked_creative_id", creativeId);
            String ppc = builder.toUriString();

            adCoreBqf.update(lauUnitCreative).set(lauUnitCreative.promotionPurposeContent, ppc).where(lauUnitCreative.creativeId.eq(creativeId));
        }
    }
}
