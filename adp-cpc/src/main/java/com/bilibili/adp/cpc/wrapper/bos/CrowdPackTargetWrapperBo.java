package com.bilibili.adp.cpc.wrapper.bos;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CrowdPackTargetWrapperBo {
    private List<Integer> includedCrowdPackIds;
    private List<Integer> excludedCrowdPackIds;
}
