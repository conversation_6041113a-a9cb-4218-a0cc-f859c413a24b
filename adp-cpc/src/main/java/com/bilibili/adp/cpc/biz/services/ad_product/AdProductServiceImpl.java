package com.bilibili.adp.cpc.biz.services.ad_product;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.amazonaws.services.s3.model.PutObjectResult;
import com.bapis.ad.account.crm.acc.CrmAccountServiceGrpc;
import com.bapis.cpm.bdata.service.BdataBrandItem;
import com.bapis.datacenter.service.oneservice.*;
import com.bapis.infra.service.sequence.BusinessZoneReq;
import com.bapis.infra.service.sequence.IDZoneReply;
import com.bapis.infra.service.sequence.SeqZoneGrpc;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.exception.ServiceRuntimeException;
import com.bilibili.adp.common.oss.BOSSUtils;
import com.bilibili.adp.common.util.Page;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.biz.bos.account.AccountProductBo;
import com.bilibili.adp.cpc.biz.converter.product.AdProductPoConvertor;
import com.bilibili.adp.cpc.biz.services.account.LaunchAccountV1Service;
import com.bilibili.adp.cpc.biz.services.account.NewCorpAccountProc;
import com.bilibili.adp.cpc.biz.services.ad_product.dto.*;
import com.bilibili.adp.cpc.biz.services.ad_product.strategy.AdProductImportBase;
import com.bilibili.adp.cpc.biz.services.ad_product.strategy.AdProductImportFactory;
import com.bilibili.adp.cpc.biz.services.bdata.BdataService;
import com.bilibili.adp.cpc.biz.services.game.api.ILauMiniGameService;
import com.bilibili.adp.cpc.biz.services.game.bos.LauMiniGameDto;
import com.bilibili.adp.cpc.biz.services.log.DbTable;
import com.bilibili.adp.cpc.biz.services.log.ILogOperateForLongService;
import com.bilibili.adp.cpc.biz.services.log.ILogOperateNewService;
import com.bilibili.adp.cpc.biz.services.unit.AdpLauUnitService;
import com.bilibili.adp.cpc.core.constants.IsDeleted;
import com.bilibili.adp.cpc.core.constants.OperationType;
import com.bilibili.adp.cpc.dao.querydsl.pos.*;
import com.bilibili.adp.cpc.dao.querydsl_cpc.pos.AdProductMappingCPCPo;
import com.bilibili.adp.cpc.dao.querydsl_cpc.pos.AdProductTotalInfoCPCPo;
import com.bilibili.adp.cpc.databus.bos.SdpaGameProductDatabusBo;
import com.bilibili.adp.cpc.dto.LauUnitBaseDto;
import com.bilibili.adp.cpc.enums.ad_product.*;
import com.bilibili.adp.cpc.po.ad.*;
import com.bilibili.adp.cpc.proxy.MainCommonArchSequenceProxy;
import com.bilibili.adp.cpc.repo.AdProductRepo;
import com.bilibili.adp.cpc.repo.LauUnitCreativeRepo;
import com.bilibili.adp.cpc.repo.NewAccAccountRepo;
import com.bilibili.adp.cpc.utils.EffectiveAdExcelUtils;
import com.bilibili.adp.launch.biz.ad_core.querydsl.dos.LauCreativeMiniGameMappingDo;
import com.bilibili.adp.launch.biz.pojo.LauUnitCreativePo;
import com.bilibili.adp.launch.biz.repo.LauUnitRepo;
import com.bilibili.adp.log.service.v6.ILogCpcOperationService;
import com.bilibili.adp.log.service.v6.bo.LogCpcOperationBo;
import com.bilibili.adp.log.service.v6.bo.QueryLogCpcOperationBo;
import com.bilibili.adp.web.framework.core.Context;
import com.bilibili.mgk.platform.common.utils.TimeUtil;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Sets;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;
import pleiades.venus.starter.rpc.client.RPCClient;

import javax.annotation.Resource;
import java.io.IOException;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.bilibili.adp.cpc.config.AppConfig.AD_BUSINESS_CPC_TM;
import static com.bilibili.adp.cpc.dao.config.PlatformMySqlConfig.AD_TM;

/**
 * <AUTHOR>
 * @date 2023/10/30 11:59
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class AdProductServiceImpl implements IAdProductService {

    private final AdProductRepo adProductRepo;
    private final AdProductImportFactory adProductFactory;
    private final ILogOperateNewService logOperateService;
    private final ILogOperateForLongService logOperateForLongService;
    private final SDPAParseExcelAndCheckService sdpaParseExcelAndCheckService;
    private final ILogCpcOperationService logCpcOperationService;
    private final BdataService bdataService;
    private final NewCorpAccountProc newCorpAccountProc;
    private final LauUnitRepo lauUnitRepo;
    private final AdpLauUnitService adpLauUnitService;
    private final ILauMiniGameService lauMiniGameService;
    private final LauUnitCreativeRepo lauUnitCreativeRepo;
    private final NewAccAccountRepo newAccAccountRepo;
    private final LaunchAccountV1Service launchAccountV1Service;
    private final RedissonClient redissonClient;

    private final AdProductCheckInfoHelper adProductCheckInfoHelper;

    @Resource
    private MainCommonArchSequenceProxy mainCommonArchSequenceProxy;

    @RPCClient("datacenter.oneservice.akuya-dispatch-service")
    private OneServiceOpenApiManagerGrpc.OneServiceOpenApiManagerBlockingStub oneServiceOpenApiCustomerBlockingStub;

    @Value("${bfs.cpc.sdpa.dir:sdpa_maual_import}")
    private String dir;


    @Value("${cpm-adp.product.id.gen.token:bc8570e9016f38a91e03eab35817aa93}")
    private String productIdGenToken;

    @Value("${game.united.first.industry.id:1142}")
    private Integer gameUnitedFirstIndustryId;

    private static final Integer DARK_DEFAULT_ACCOUNT_ID = 0;
    public static final String GAME_UNIQUE_PREFIX = "game_";

    private static final Long sdpaGameFirstCategoryId = 105L;
    private static final Long sdpaMiniGameSecondCategoryId = 1050060L;
    private static final Long sdpaMobileGameSecondCategoryId = 1050061L;
    private static final Long sdpaBiliGameSecondCategoryId = 1050062L;
    private static final String SDPA_MINI_GAME_PREFIX = "sdpa_mini_game";
    private static final String SDPA_SHARE_LIBRARY_ID_PREFIX = "sdpa_share_library_id_";

    private static final String GOODS_PREFIX = "goods_";

    @Override
    public List<AdProductCategoryDto> getProductCategoryList(Long pCode, int level) {
        AdProductCategoryLevelEnum.getByCode(level);

        List<AdProductCategoryPo> categoryPos = adProductRepo.getProductCategoryList(pCode, level);
        return AdProductPoConvertor.MAPPER.convertCategoryPos2Dtos(categoryPos);
    }

    @Override
    public Map<Integer, List<AdProductItemDto>> getItemsByTypes(List<Integer> types) {
        Assert.notEmpty(types, "枚举类型不能为空");
        types.forEach(AdProductItemTypeEnum::getByCode);

        List<AdProductItemPo> itemPos = adProductRepo.getItemsByType(types);
        var dtoS = AdProductPoConvertor.MAPPER.convertItemPos2Dtos(itemPos);
        return CollectionUtils.isEmpty(dtoS) ? new HashMap<>()
                : dtoS.stream().collect(Collectors.groupingBy(AdProductItemDto::getType,
                Collectors.collectingAndThen(Collectors.toList(), list -> list.stream().sorted(Comparator.comparing(AdProductItemDto::getSortOrder)).collect(Collectors.toList()))));
    }

    @Override
    public List<AdProductSpuDto> querySpu(long thirdCategoryCode) {
        var spuPos = adProductRepo.querySpu(null, thirdCategoryCode);
        if (CollectionUtils.isEmpty(spuPos)) {
            return new ArrayList<>();
        }
        var skuPos = adProductRepo.querySku(null, spuPos.stream()
                .map(AdProductSpuPo::getCode).collect(Collectors.toList()));

        var spuDtos = spuPos.stream().map(spuPo -> AdProductSpuDto.builder()
                .spuCode(spuPo.getCode()).spuName(spuPo.getName()).build()).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(skuPos)) {
            var spuCode2SkuList = skuPos.stream().collect(Collectors
                    .groupingBy(AdProductSkuPo::getSpuCode, Collectors.toList()));
            spuDtos = spuDtos.stream().peek(spuDto -> {
                var skuPoList = spuCode2SkuList.get(spuDto.getSpuCode());
                if (!CollectionUtils.isEmpty(skuPoList)) {
                    spuDto.setSkuDtoS(skuPoList.stream().map(sku -> AdProductSkuDto.builder().skuCode(sku.getCode())
                            .skuName(sku.getName()).build()).collect(Collectors.toList()));
                }
            }).collect(Collectors.toList());
        }

        return spuDtos;
    }

    @Override
    @Transactional(value = AD_TM, rollbackFor = Exception.class)
    public long createProductLibrary(Operator operator, AdProductLibraryDto libraryDto, Boolean isMapiRequest) {
        Assert.notNull(libraryDto, "商品库信息不能为空");
        AdProductLibraryTypeEnum.getByCode(libraryDto.getType());

        var accountId = operator.getOperatorId();
        checkName(accountId, libraryDto.getName());

        Set<Integer> libraryTypeSet = Sets.newHashSet(libraryDto.getType());
        adProductCheckInfoHelper.checkAccountInfo(libraryTypeSet, accountId);
        adProductCheckInfoHelper.checkLibraryInfo(libraryDto);


        var libraryId = adProductRepo.insertLibrary(accountId, libraryDto.getName(), libraryDto.getType(),
                AdProductCreateTypeEnum.BY_INTERFACE.getCode());
        logOperateService.addInsertLog(DbTable.AD_PRODUCT_LIBRARY, operator, libraryDto,
                Math.toIntExact(libraryId));
        return libraryId;
    }

    @Override
    @Transactional(value = AD_TM, rollbackFor = Exception.class)
    public void updateProductLibrary(Operator operator, AdProductLibraryDto libraryDto, Boolean isMapiRequest) {
        var accountId = operator.getOperatorId();
        checkName(accountId, libraryDto.getName());

        var old = adProductRepo.getLibraries(accountId, Lists.newArrayList(libraryDto.getLibraryId()));
        Assert.notEmpty(old, "您不能操作非当前账号的商品库");
        var oldOne = old.get(0);

        Set<Integer> libraryTypeSet = Sets.newHashSet(oldOne.getType());
        adProductCheckInfoHelper.checkAccountInfo(libraryTypeSet, accountId);
        adProductCheckInfoHelper.checkLibraryInfo(libraryDto);


        if (oldOne.getName().equals(libraryDto.getName())) {
            return;
        }
        logOperateService.addUpdateLog(DbTable.AD_PRODUCT_LIBRARY, operator, libraryDto,
                Math.toIntExact(libraryDto.getLibraryId()));
        adProductRepo.updateLibrary(libraryDto.getLibraryId(), libraryDto.getName());
    }

    @Override
    @Transactional(value = AD_TM, rollbackFor = Exception.class)
    public void addOrShareProductLibrary(Operator operator, Long libraryId, List<Integer> accountIds, Boolean isMapiRequest, Integer bizStatus) {

        RLock lock = redissonClient.getLock(SDPA_SHARE_LIBRARY_ID_PREFIX + "_" + libraryId);
        boolean tryLock = lock.tryLock();

        if (BooleanUtils.isNotTrue(tryLock)) {
            log.error("addOrShareProductLibrary is processing operator = {} , libraryId = {} , accountIds = {}", JSON.toJSONString(operator), libraryId, JSON.toJSONString(accountIds));
            throw new IllegalArgumentException("商品库正在被操作中，请稍后再试");
        }

        try {
            if (Objects.isNull(bizStatus)) {
                bizStatus = AdProductStatusEnum.DISABLE.getCode();
            }

            adProductCheckInfoHelper.checkMapiSdpaShareLibraryAccount(operator.getOperatorId(), accountIds);


            var allAuthorityAccountIds = queryAuthorityAccountList(libraryId,
                    operator.getOperatorId());

            List<Integer> inc = new ArrayList<>();
            List<Integer> dec = new ArrayList<>();
            List<Integer> exist = new ArrayList<>();
            if (CollectionUtils.isEmpty(accountIds)) {
                dec.addAll(allAuthorityAccountIds);
            } else if (CollectionUtils.isEmpty(allAuthorityAccountIds)) {
                inc.addAll(accountIds);
            } else {
                accountIds.forEach(accountId -> {
                    if (!allAuthorityAccountIds.contains(accountId)) {
                        inc.add(accountId);
                    }
                });
                allAuthorityAccountIds.forEach(accountId -> {
                    if (!accountIds.contains(accountId)) {
                        dec.add(accountId);
                    }
                    if (accountIds.contains(accountId)) {
                        exist.add(accountId);
                    }
                });
            }

            if (!CollectionUtils.isEmpty(dec)) {
                cancelShareProductLibrary(operator, libraryId, dec);
            }
            if (!CollectionUtils.isEmpty(inc)) {
                shareProductLibrary(operator, libraryId, inc, bizStatus);
            }
            if (CollectionUtils.isNotEmpty(exist)) {
                processExistShareProductLibrary(operator, libraryId, exist, bizStatus);
            }
        } catch (Exception e) {
            throw e;
        } finally {
            if (lock.isLocked()) {
                lock.unlock();
            }
        }
    }

    public void shareProductLibrary(Operator operator, Long libraryId, List<Integer> shareAccountIds, Integer bizStatus) {
        Assert.notEmpty(shareAccountIds, "被分享账号不能为空");
        var old = adProductRepo.getLibraries(operator.getOperatorId(), Lists.newArrayList(libraryId));
        Assert.notEmpty(old, "您不能操作非当前账号的商品库");

        adProductRepo.shareLibrary(libraryId, shareAccountIds, bizStatus);
    }

    public void cancelShareProductLibrary(Operator operator, Long libraryId, List<Integer> cancelAccountIds) {
        var old = adProductRepo.getLibraries(operator.getOperatorId(), Lists.newArrayList(libraryId));
        Assert.notEmpty(old, "您不能操作非当前账号的商品库");

        Assert.isTrue(!cancelAccountIds.contains(operator.getOperatorId()), "当前账号商品库归属于您,不能被取消分享");

        var basePos = adProductRepo.getProductsByLibraryIdV2(Lists.newArrayList(libraryId));

        //如果有关联单元
        var productIds = basePos.stream().map(AdProductTotalInfoPo::getAdProductId).collect(Collectors.toList());
        var mappingPos = adProductRepo.getProductMappingByProductIds(productIds,
                AdProductBindTypeEnum.UNIT.getCode());

        List<Integer> unitIds = mappingPos.stream().map(AdProductMappingPo::getMappingId).collect(Collectors.toList());
        List<LauUnitBaseDto> unitBaseList = adpLauUnitService.getBaseDtosInIds(unitIds);
        Set<Integer> bindUnitAccountSet = unitBaseList.stream().map(LauUnitBaseDto::getAccountId).collect(Collectors.toSet());

        List<Long> deletedShareLibraryIdList = new ArrayList<>();
        List<Long> deletedProductMappingIdList = new ArrayList<>();

        for (Integer accountId : cancelAccountIds) {

            if (bindUnitAccountSet.contains(accountId)) {
                List<Integer> bindingUnitIdList = unitBaseList.stream()
                        .filter(unitBaseDto -> unitBaseDto.getAccountId().equals(accountId))
                        .map(LauUnitBaseDto::getUnitId).collect(Collectors.toList());

                throw new IllegalArgumentException("取消分享失败，请确保取消分享产品库内产品未关联单元," +
                        "关联单元的账户有:" + accountId + "关联的单元有" + JSON.toJSONString(bindingUnitIdList));
            }

//            adProductRepo.deleteProductShareLibrary(Lists.newArrayList(libraryId), accountId);
            List<Long> shareLibraryIdList = adProductRepo.selectProductShareLibrary(Lists.newArrayList(libraryId), accountId);
            if (CollectionUtils.isNotEmpty(shareLibraryIdList)) {
                deletedShareLibraryIdList.addAll(shareLibraryIdList);
            }
            if (CollectionUtils.isEmpty(basePos)) {
                continue;
            }

//            adProductRepo.deleteProductMapping(productIds, accountId, AdProductBindTypeEnum.ACCOUNT.getCode());
            List<Long> productMappingIdList = adProductRepo.selectProductMapping(productIds, accountId, AdProductBindTypeEnum.ACCOUNT.getCode());
            if (CollectionUtils.isNotEmpty(productMappingIdList)) {
                deletedProductMappingIdList.addAll(productMappingIdList);
            }
        }
        adProductRepo.deleteProductShareLibrary(deletedShareLibraryIdList);
        adProductRepo.deleteProductMapping(deletedProductMappingIdList);
        adProductRepo.deleteAdProductInfoShare(cancelAccountIds, Collections.singletonList(libraryId));

    }

    public void processExistShareProductLibrary(Operator operator, Long libraryId, List<Integer> existAccountIds, Integer bizStatus) {

        List<AdProductTotalInfoPo> productsList = adProductRepo.getProductsByLibraryIdV2(Collections.singletonList(libraryId));
        if (CollectionUtils.isEmpty(productsList)) {
            return;
        }
        List<Long> productIdList = productsList.stream().map(AdProductTotalInfoPo::getAdProductId).collect(Collectors.toList());
        adProductRepo.updateShareProductStatusByLibraryId(libraryId, existAccountIds, bizStatus);


        if (Objects.equals(bizStatus, AdProductStatusEnum.ENABLE.getCode())) {
            // 启用模式：需要确保每个 productId x accountId 组合只有一条非软删的记录
            processEnableProductMapping(productIdList, existAccountIds);
        } else {
            // 不启用模式：直接软删除所有匹配记录
            adProductRepo.updateProductMappingIsDeleted(productIdList, existAccountIds, IsDeleted.DELETED);
        }
    }

    /**
     * 处理启用产品映射关系（批量操作版本）
     * 确保每个 productId x accountId 组合只有一条非软删的记录
     */
    private void processEnableProductMapping(List<Long> productIdList, List<Integer> accountIdList) {
        // 1. 批量查询所有相关的映射记录
        List<AdProductMappingPo> allExistingMappings = batchQueryProductMappings(productIdList, accountIdList);

        // 2. 按 productId + accountId 分组
        Map<String, List<AdProductMappingPo>> mappingGroups = allExistingMappings.stream()
            .collect(Collectors.groupingBy(mapping ->
                mapping.getAdProductId() + "_" + mapping.getMappingId()));

        // 3. 分析需要的操作
        List<AdProductMappingPo> toInsert = new ArrayList<>();
        List<Long> toRecover = new ArrayList<>();

        for (Integer accountId : accountIdList) {
            for (Long productId : productIdList) {
                String key = productId + "_" + accountId;
                List<AdProductMappingPo> mappings = mappingGroups.get(key);

                if (CollectionUtils.isEmpty(mappings)) {
                    // 没有记录，需要创建新记录
                    AdProductMappingPo newMapping = AdProductMappingPo.builder()
                        .adProductId(productId)
                        .mappingId(accountId)
                        .type(AdProductBindTypeEnum.ACCOUNT.getCode())
                        .belongType(AdProductBelongTypeEnum.USER_TYPE_IN.getCode())
                        .isDeleted(IsDeleted.VALID)
                        .ctime(new Timestamp(System.currentTimeMillis()))
                        .mtime(new Timestamp(System.currentTimeMillis()))
                        .build();
                    toInsert.add(newMapping);
                } else {
                    // 有记录，检查是否已有非软删的记录
                    List<AdProductMappingPo> validMappings = mappings.stream()
                        .filter(mapping -> Objects.equals(mapping.getIsDeleted(), IsDeleted.VALID))
                        .collect(Collectors.toList());

                    if (CollectionUtils.isEmpty(validMappings)) {
                        // 没有非软删的记录，恢复最新的一条
                        AdProductMappingPo latestMapping = mappings.stream()
                            .max(Comparator.comparing(AdProductMappingPo::getMtime))
                            .orElse(null);

                        if (latestMapping != null) {
                            toRecover.add(latestMapping.getId());
                        }
                    }
                    // 如果已有非软删的记录，不需要做任何操作
                }
            }
        }

        // 4. 批量执行操作
        if (CollectionUtils.isNotEmpty(toInsert)) {
            batchInsertProductMappings(toInsert);
        }
        if (CollectionUtils.isNotEmpty(toRecover)) {
            adProductRepo.recoverProductMappingByIds(toRecover);
        }
    }

    /**
     * 批量查询产品映射记录
     */
    private List<AdProductMappingPo> batchQueryProductMappings(List<Long> productIdList, List<Integer> accountIdList) {
        return adProductRepo.batchQueryProductMappings(productIdList, accountIdList);
    }

    /**
     * 批量插入产品映射记录
     */
    private void batchInsertProductMappings(List<AdProductMappingPo> mappings) {
        adProductRepo.batchInsertProductMappings(mappings);
    }

    @Override
    @Transactional(value = AD_TM, rollbackFor = Exception.class)
    public Map<String, Long> createProduct(Operator operator, AdProductDto productDto, Boolean isMapiRequest) {
        var accountId = operator.getOperatorId();
        var libraryId = productDto.getLibraryId();
        var old = adProductRepo.getLibraries(accountId, Lists.newArrayList(libraryId));
        Assert.notEmpty(old, "您不能操作非当前账号的商品库");
        var oldOne = old.get(0);
        var type = oldOne.getType();


        Set<Integer> libraryTypeSet = Sets.newHashSet(type);
        adProductCheckInfoHelper.checkAccountInfo(libraryTypeSet, accountId);


        Map<String, Long> adProductIdNameMap = new HashMap<>();

        List<AdProductTotalInfoPo> basePos = adProductRepo.getProductsByLibraryIdV2(Lists.newArrayList(libraryId));
        List<String> productNames = CollectionUtils.isEmpty(basePos) ? new ArrayList<>()
                : basePos.stream().map(AdProductTotalInfoPo::getAdProductName).collect(Collectors.toList());


        if (AdProductLibraryTypeEnum.COURSE.getCode().equals(type)) {
            var educationList = productDto.getEducationDtos();
            Assert.notEmpty(educationList, "教育产品信息不能为空");
            List<Long> spuCodeList = educationList.stream().map(SdpaProductEducationDto::getSpuCode).distinct().collect(Collectors.toList());
            Map<Long, String> spuCodeNameMap = adProductRepo.querySpuList(spuCodeList).stream().collect(Collectors.toMap(AdProductSpuPo::getCode, AdProductSpuPo::getName));
            AccountProductBo accountProductBo = newCorpAccountProc.queryAccountProduct(accountId);

            for (SdpaProductEducationDto sdpaProductEducationDto : educationList) {
                sdpaProductEducationDto.setAccountId(accountId);
                sdpaProductEducationDto.setLibraryId(libraryId);
                sdpaProductEducationDto.setSpuName(spuCodeNameMap.getOrDefault(sdpaProductEducationDto.getSpuCode(), ""));
                sdpaProductEducationDto.setBrandName(accountProductBo.getProductName());
            }

            validateProductNames(educationList, productNames);
            supplyCategoryName(educationList);
            adProductIdNameMap.putAll(supplyAdProductIdWithNameIdMap(educationList));
            List<Long> idList = adProductRepo.insertCourseV2(educationList);
            logOperateForLongService.addBatchInsertLogWithParentClass(DbTable.AD_PRODUCT_TOTAL_INFO, operator, educationList, idList);

        } else if (AdProductLibraryTypeEnum.BORROW_LOAN.getCode().equals(type)) {
            var borrowLoanList = productDto.getLoanDtos();
            Assert.notEmpty(borrowLoanList, "借贷产品信息不能为空");

            List<Long> spuCodeList = borrowLoanList.stream().map(SdpaProductLoanDto::getSpuCode).distinct().collect(Collectors.toList());
            Map<Long, String> spuCodeNameMap = adProductRepo.querySpuList(spuCodeList).stream().collect(Collectors.toMap(AdProductSpuPo::getCode, AdProductSpuPo::getName));

            for (SdpaProductLoanDto sdpaProductLoanDto : borrowLoanList) {
                sdpaProductLoanDto.setAccountId(accountId);
                sdpaProductLoanDto.setLibraryId(libraryId);
                sdpaProductLoanDto.setSpuName(spuCodeNameMap.getOrDefault(sdpaProductLoanDto.getSpuCode(), ""));
            }

            for (SdpaProductLoanDto sdpaProductLoanDto : borrowLoanList) {
                if (Objects.isNull(sdpaProductLoanDto.getLoanUsage())) {
                    sdpaProductLoanDto.setLoanUsage("");
                }
                if (Objects.isNull(sdpaProductLoanDto.getInterestType())) {
                    sdpaProductLoanDto.setInterestType("");
                }
                if (Objects.isNull(sdpaProductLoanDto.getRapaymentType())) {
                    sdpaProductLoanDto.setRapaymentType("");
                }
                if (Objects.isNull(sdpaProductLoanDto.getEarlyRepayment())) {
                    sdpaProductLoanDto.setEarlyRepayment("");
                }
                if (Objects.isNull(sdpaProductLoanDto.getEarlyRepaymentPenalty())) {
                    sdpaProductLoanDto.setEarlyRepaymentPenalty("");
                }
            }

            validateProductNames(borrowLoanList, productNames);
            supplyCategoryName(borrowLoanList);
            adProductIdNameMap.putAll(supplyAdProductIdWithNameIdMap(borrowLoanList));
            List<Long> idList = adProductRepo.insertLoanV2(borrowLoanList);
            logOperateForLongService.addBatchInsertLogWithParentClass(DbTable.AD_PRODUCT_TOTAL_INFO, operator, borrowLoanList, idList);

        } else if (AdProductLibraryTypeEnum.NOVEL.getCode().equals(type)) {
            var novelList = productDto.getNovelDtos();
            Assert.notEmpty(novelList, "小说产品信息不能为空");


            for (SdpaProductNovelDto sdpaProductNovelDto : novelList) {
                sdpaProductNovelDto.setAccountId(accountId);
                sdpaProductNovelDto.setLibraryId(libraryId);
            }

            adProductCheckInfoHelper.checkNovelInfo(novelList, Boolean.TRUE);
            validateProductNames(novelList, productNames);
            supplyCategoryName(novelList);
            adProductIdNameMap.putAll(supplyAdProductIdWithNameIdMap(novelList));
            List<Long> idList = adProductRepo.insertNovelV2(novelList);
            logOperateForLongService.addBatchInsertLogWithParentClass(DbTable.AD_PRODUCT_TOTAL_INFO, operator, novelList, idList);

        } else if (AdProductLibraryTypeEnum.SHORT_FILM.getCode().equals(type)) {
            List<SdpaShortFilmDto> shortFilmDtos = productDto.getShortFilmDtos();
            Assert.notEmpty(shortFilmDtos, "短剧信息不能为空");

            for (SdpaShortFilmDto shortFilmDto : shortFilmDtos) {
                Assert.isTrue(org.apache.commons.collections4.CollectionUtils.isNotEmpty(shortFilmDto.getLinkArray()), "短剧链路不能为空");
                shortFilmDto.setAccountId(accountId);
                shortFilmDto.setLibraryId(libraryId);
            }
            adProductCheckInfoHelper.checkShortFilmInfo(shortFilmDtos, Boolean.TRUE);
            validateProductNames(shortFilmDtos, productNames);
            supplyCategoryName(shortFilmDtos);
            adProductIdNameMap.putAll(supplyAdProductIdWithNameIdMap(shortFilmDtos));
            List<Long> idList = adProductRepo.insertShortFilmV2(shortFilmDtos);
            logOperateForLongService.addBatchInsertLogWithParentClass(DbTable.AD_PRODUCT_TOTAL_INFO, operator, shortFilmDtos, idList);

        } else if (AdProductLibraryTypeEnum.CAR.getCode().equals(type)) {
            List<SdpaProductCarDto> carDtos = productDto.getCarDtos();
            Assert.notEmpty(carDtos, "汽车信息不能为空");

            List<Long> spuCodeList = carDtos.stream().map(SdpaProductCarDto::getSpuCode).distinct().collect(Collectors.toList());
            Map<Long, String> spuCodeNameMap = adProductRepo.querySpuList(spuCodeList).stream().collect(Collectors.toMap(AdProductSpuPo::getCode, AdProductSpuPo::getName));
            List<BdataBrandItem> allCarBrandInfo = bdataService.getAllCarBrandInfo();
            Map<Long, String> brandIdNameMap = allCarBrandInfo.stream().collect(Collectors.toMap(BdataBrandItem::getBrandId, BdataBrandItem::getBrandName));

            List<String> modelList = carDtos.stream().map(SdpaProductCarDto::getAdProductName).distinct().collect(Collectors.toList());
            Map<String, MapValue> carExtraInfoFromHive = getCarExtraInfoFromHive(modelList);

            for (SdpaProductCarDto carDto : carDtos) {
                MapValue mapValue = carExtraInfoFromHive.get(carDto.getAdProductName());
                if (Objects.nonNull(mapValue)) {
                    String fuelType = mapValue.getValueMap().get("fuel_type_str");
                    String priceRange = mapValue.getValueMap().get("price_range");
                    carDto.setFuelType(fuelType);
                    carDto.setPriceRange(priceRange);
                }
                carDto.setAccountId(accountId);
                carDto.setLibraryId(libraryId);
                carDto.setSpuName(spuCodeNameMap.getOrDefault(carDto.getSpuCode(), ""));
                carDto.setBrandName(brandIdNameMap.get(Long.valueOf(carDto.getBrand())));
            }

            validateProductNames(carDtos, productNames);
            supplyCategoryName(carDtos);
            adProductIdNameMap.putAll(supplyAdProductIdWithNameIdMap(carDtos));
            List<Long> idList = adProductRepo.insertCarV2(carDtos);

            logOperateForLongService.addBatchInsertLogWithParentClass(DbTable.AD_PRODUCT_TOTAL_INFO, operator, carDtos, idList);
        } else if (Objects.equals(AdProductLibraryTypeEnum.E_COMMERCE.getCode(), type)) {

            List<SdpaProductECommerceDto> eCommerceDtos = productDto.getECommerceDtos();
            Assert.notEmpty(eCommerceDtos, "电商信息不能为空");

            List<Long> spuCodeList = eCommerceDtos.stream().map(SdpaProductECommerceDto::getSpuCode).distinct().collect(Collectors.toList());
            Map<Long, String> spuCodeNameMap = adProductRepo.querySpuList(spuCodeList).stream().collect(Collectors.toMap(AdProductSpuPo::getCode, AdProductSpuPo::getName));

            for (SdpaProductECommerceDto eCommerceDto : eCommerceDtos) {
                eCommerceDto.setAccountId(accountId);
                eCommerceDto.setLibraryId(libraryId);
                eCommerceDto.setSpuName(spuCodeNameMap.getOrDefault(eCommerceDto.getSpuCode(), ""));
            }

            adProductCheckInfoHelper.checkECommerceInfo(eCommerceDtos, Boolean.TRUE);

            validateProductNames(eCommerceDtos, productNames);
            supplyCategoryName(eCommerceDtos);
            adProductIdNameMap.putAll(supplyAdProductIdWithNameIdMap(eCommerceDtos));
            List<Long> idList = adProductRepo.insertECommerceV2(eCommerceDtos);

            logOperateForLongService.addBatchInsertLogWithParentClass(DbTable.AD_PRODUCT_TOTAL_INFO, operator, eCommerceDtos, idList);


        } else if (Objects.equals(AdProductLibraryTypeEnum.MEDICAL.getCode(), type)) {

            List<SdpaProductMedicalDto> medicalDtos = productDto.getMedicalDtos();
            Assert.notEmpty(medicalDtos, "医疗产品信息不能为空");

            List<Long> spuCodeList = medicalDtos.stream().map(SdpaProductMedicalDto::getSpuCode).distinct().collect(Collectors.toList());
            Map<Long, String> spuCodeNameMap = adProductRepo.querySpuList(spuCodeList).stream().collect(Collectors.toMap(AdProductSpuPo::getCode, AdProductSpuPo::getName));

            List<Long> skuCodeList = medicalDtos.stream().map(SdpaProductMedicalDto::getSkuCode).distinct().collect(Collectors.toList());
            Map<Long, String> skuCodeNameMap = adProductRepo.querySkuList(skuCodeList).stream().collect(Collectors.toMap(AdProductSkuPo::getCode, AdProductSkuPo::getName));

            for (SdpaProductMedicalDto medicalDto : medicalDtos) {
                medicalDto.setAccountId(accountId);
                medicalDto.setLibraryId(libraryId);
                medicalDto.setSpuName(spuCodeNameMap.getOrDefault(medicalDto.getSpuCode(), ""));
                medicalDto.setSkuName(skuCodeNameMap.getOrDefault(medicalDto.getSkuCode(), ""));
            }

            adProductCheckInfoHelper.checkMedicalInfo(medicalDtos, Boolean.TRUE);

            validateProductNames(medicalDtos, productNames);
            supplyCategoryName(medicalDtos);
            adProductIdNameMap.putAll(supplyAdProductIdWithNameIdMap(medicalDtos));
            List<Long> idList = adProductRepo.insertMedicalV2(medicalDtos);

            logOperateForLongService.addBatchInsertLogWithParentClass(DbTable.AD_PRODUCT_TOTAL_INFO, operator, medicalDtos, idList);

        } else if (Objects.equals(AdProductLibraryTypeEnum.HOUSE.getCode(), type)) {

            List<SdpaProductHouseDto> houseDtos = productDto.getHouseDtos();
            Assert.notEmpty(houseDtos, "房产产品信息不能为空");

            List<Long> spuCodeList = houseDtos.stream().map(SdpaProductHouseDto::getSpuCode).distinct().collect(Collectors.toList());
            Map<Long, String> spuCodeNameMap = adProductRepo.querySpuList(spuCodeList).stream().collect(Collectors.toMap(AdProductSpuPo::getCode, AdProductSpuPo::getName));

            List<Long> skuCodeList = houseDtos.stream().map(SdpaProductHouseDto::getSkuCode).distinct().collect(Collectors.toList());
            Map<Long, String> skuCodeNameMap = adProductRepo.querySkuList(skuCodeList).stream().collect(Collectors.toMap(AdProductSkuPo::getCode, AdProductSkuPo::getName));

            for (SdpaProductHouseDto houseDto : houseDtos) {
                houseDto.setAccountId(accountId);
                houseDto.setLibraryId(libraryId);
                houseDto.setSpuName(spuCodeNameMap.getOrDefault(houseDto.getSpuCode(), ""));
                houseDto.setSkuName(skuCodeNameMap.getOrDefault(houseDto.getSkuCode(), ""));
            }

            adProductCheckInfoHelper.checkHouseInfo(houseDtos, Boolean.TRUE);

            validateProductNames(houseDtos, productNames);
            supplyCategoryName(houseDtos);
            adProductIdNameMap.putAll(supplyAdProductIdWithNameIdMap(houseDtos));
            List<Long> idList = adProductRepo.insertHouseV2(houseDtos);

            logOperateForLongService.addBatchInsertLogWithParentClass(DbTable.AD_PRODUCT_TOTAL_INFO, operator, houseDtos, idList);

        }

        List<Integer> accountIdList = adProductRepo.queryShareLibraryAccountIdList(libraryId);
        if (CollectionUtils.isNotEmpty(accountIdList)) {
            adProductRepo.addShareInfo(adProductIdNameMap, accountIdList, libraryId);
        }

        return adProductIdNameMap;
    }

    @Override
    @Transactional(value = AD_BUSINESS_CPC_TM, rollbackFor = Exception.class)
    public Map<String, Long> createProductByDark(AdProductDto productDto) {
        Integer accountId = DARK_DEFAULT_ACCOUNT_ID;

        List<SdpaProductEducationDto> educationList = productDto.getEducationDtos();
        List<SdpaProductLoanDto> borrowLoanList = productDto.getLoanDtos();
        List<SdpaProductNovelDto> novelList = productDto.getNovelDtos();
        List<SdpaShortFilmDto> shortFilmDtos = productDto.getShortFilmDtos();
        List<SdpaProductCarDto> carDtos = productDto.getCarDtos();
        List<SdpaProductGoodsDarkDto> goodsDtos = productDto.getGoodsDtos();
        List<SdpaProductMedicalDto> medicalDtos = productDto.getMedicalDtos();
        List<SdpaProductHouseDto> houseDtos = productDto.getHouseDtos();

        List<AdProductTotalInfoCPCPo> existAdProduct = queryExistDarkCpcPo(productDto);
        Map<String, Long> existUniqueKeyAdProductIdMap = existAdProduct.stream().collect(Collectors.toMap(AdProductTotalInfoCPCPo::getUniqueKey, AdProductTotalInfoCPCPo::getAdProductId));
        Set<String> existUniqueKeySet = existAdProduct.stream().map(AdProductTotalInfoCPCPo::getUniqueKey).collect(Collectors.toSet());

        Map<String, Long> uniqueAdProductIdMap = new HashMap<>();
        uniqueAdProductIdMap.putAll(existUniqueKeyAdProductIdMap);

        if (CollectionUtils.isNotEmpty(educationList)) {
            for (SdpaProductEducationDto sdpaProductEducationDto : educationList) {
                sdpaProductEducationDto.setAccountId(accountId);
            }
            supplyCategoryName(educationList);

            Map<String, Long> goodsUniqueAdProductIdMap = supplyAdProductId(educationList);
            uniqueAdProductIdMap.putAll(goodsUniqueAdProductIdMap);
            adProductRepo.insertCourseDarkV2(educationList);

        } else if (CollectionUtils.isNotEmpty(borrowLoanList)) {

            for (SdpaProductLoanDto sdpaProductLoanDto : borrowLoanList) {
                sdpaProductLoanDto.setAccountId(accountId);
            }

            for (SdpaProductLoanDto sdpaProductLoanDto : borrowLoanList) {
                if (Objects.isNull(sdpaProductLoanDto.getLoanUsage())) {
                    sdpaProductLoanDto.setLoanUsage("");
                }
                if (Objects.isNull(sdpaProductLoanDto.getInterestType())) {
                    sdpaProductLoanDto.setInterestType("");
                }
                if (Objects.isNull(sdpaProductLoanDto.getRapaymentType())) {
                    sdpaProductLoanDto.setRapaymentType("");
                }
                if (Objects.isNull(sdpaProductLoanDto.getEarlyRepayment())) {
                    sdpaProductLoanDto.setEarlyRepayment("");
                }
                if (Objects.isNull(sdpaProductLoanDto.getEarlyRepaymentPenalty())) {
                    sdpaProductLoanDto.setEarlyRepaymentPenalty("");
                }

            }
            supplyCategoryName(borrowLoanList);
            Map<String, Long> goodsUniqueAdProductIdMap = supplyAdProductId(borrowLoanList);
            uniqueAdProductIdMap.putAll(goodsUniqueAdProductIdMap);
            adProductRepo.insertLoanDarkV2(borrowLoanList);

        } else if (CollectionUtils.isNotEmpty(novelList)) {

            for (SdpaProductNovelDto sdpaProductNovelDto : novelList) {
                sdpaProductNovelDto.setAccountId(accountId);
            }

            supplyCategoryName(novelList);
            Map<String, Long> goodsUniqueAdProductIdMap = supplyAdProductId(novelList);
            uniqueAdProductIdMap.putAll(goodsUniqueAdProductIdMap);
            adProductRepo.insertNovelDarkV2(novelList);

        } else if (CollectionUtils.isNotEmpty(shortFilmDtos)) {

            for (SdpaShortFilmDto shortFilmDto : shortFilmDtos) {
                Assert.isTrue(org.apache.commons.collections4.CollectionUtils.isNotEmpty(shortFilmDto.getLinkArray()), "短剧链路不能为空");
                Assert.isTrue(shortFilmDto.getMinTopUp().compareTo(shortFilmDto.getMaxTopUp()) < 0, "最小充值金额不能大于最大充值金额");
                shortFilmDto.setAccountId(accountId);
            }

            supplyCategoryName(shortFilmDtos);
            Map<String, Long> goodsUniqueAdProductIdMap = supplyAdProductId(shortFilmDtos);
            uniqueAdProductIdMap.putAll(goodsUniqueAdProductIdMap);
            adProductRepo.insertShortFilmDarkV2(shortFilmDtos);

        } else if (CollectionUtils.isNotEmpty(carDtos)) {

            List<Long> spuCodeList = carDtos.stream().map(SdpaProductCarDto::getSpuCode).distinct().collect(Collectors.toList());
            Map<Long, String> spuCodeNameMap = adProductRepo.querySpuList(spuCodeList).stream().collect(Collectors.toMap(AdProductSpuPo::getCode, AdProductSpuPo::getName));
            List<BdataBrandItem> allCarBrandInfo = bdataService.getAllCarBrandInfo();
            Map<Long, String> brandIdNameMap = allCarBrandInfo.stream().collect(Collectors.toMap(BdataBrandItem::getBrandId, BdataBrandItem::getBrandName));

            List<String> modelList = carDtos.stream().map(SdpaProductCarDto::getAdProductName).distinct().collect(Collectors.toList());
            Map<String, MapValue> carExtraInfoFromHive = getCarExtraInfoFromHive(modelList);


            for (SdpaProductCarDto carDto : carDtos) {
                MapValue mapValue = carExtraInfoFromHive.get(carDto.getAdProductName());
                if (Objects.nonNull(mapValue)) {
                    String fuelType = mapValue.getValueMap().get("fuel_type_str");
                    String priceRange = mapValue.getValueMap().get("price_range");
                    carDto.setFuelType(fuelType);
                    carDto.setPriceRange(priceRange);
                }
                carDto.setAccountId(accountId);
                carDto.setSpuName(spuCodeNameMap.getOrDefault(carDto.getSpuCode(), ""));
                carDto.setBrandName(brandIdNameMap.get(Long.valueOf(carDto.getBrand())));
            }

            supplyCategoryName(carDtos);
            Map<String, Long> goodsUniqueAdProductIdMap = supplyAdProductId(carDtos);
            uniqueAdProductIdMap.putAll(goodsUniqueAdProductIdMap);
            adProductRepo.insertCarDarkV2(carDtos);

        } else if (CollectionUtils.isNotEmpty(goodsDtos)) {
            List<SdpaProductGoodsDarkDto> insertList = new ArrayList<>();
            List<SdpaProductGoodsDarkDto> updateList = new ArrayList<>();
            for (SdpaProductGoodsDarkDto goodsDto : goodsDtos) {
                goodsDto.setAccountId(accountId);
                if (existUniqueKeySet.contains(goodsDto.getUniqueKey())) {
                    goodsDto.setProductId(existUniqueKeyAdProductIdMap.get(goodsDto.getUniqueKey()));
                    updateList.add(goodsDto);
                } else {
                    insertList.add(goodsDto);
                }
            }

            Map<String, Long> goodsUniqueAdProductIdMap = supplyGoodsAdProductId(insertList);
            uniqueAdProductIdMap.putAll(goodsUniqueAdProductIdMap);
            adProductRepo.insertGoodsDarkV2(insertList);
            adProductRepo.updateGoodsDarkV2(updateList);
        }
        return uniqueAdProductIdMap;
    }

    @Override
    @Transactional(value = AD_BUSINESS_CPC_TM, rollbackFor = Exception.class)
    public void createProductMappingDark(SdpaProductMappingAddDto sdpaProductMappingAddDto) {
        List<SdpaProductMappingDarkDto> sdpaProductAddMappingList = sdpaProductMappingAddDto.getSdpaProductMappingList();

        sdpaProductAddMappingList = new ArrayList<>(sdpaProductAddMappingList.stream()
                .collect(Collectors.toMap(mapping -> Lists.newArrayList(mapping.getUniqueKey(), mapping.getType(), mapping.getMappingId()), Function.identity(), (t1, t2) -> t1))
                .values());

        List<String> uniqueKeyList = sdpaProductAddMappingList.stream().map(SdpaProductMappingDarkDto::getUniqueKey).distinct().collect(Collectors.toList());

        //校验uniqueKey是否存在
        List<AdProductTotalInfoCPCPo> adProductTotalInfoCPCPoList = adProductRepo.queryAdProductTotalInfoCPCPoList(uniqueKeyList);
        Map<String, Long> uniqueKeyAdProductIdMap = adProductTotalInfoCPCPoList.stream().collect(Collectors.toMap(AdProductTotalInfoCPCPo::getUniqueKey, AdProductTotalInfoCPCPo::getAdProductId));

        Set<String> existUniqueKeySet = uniqueKeyAdProductIdMap.keySet();
        if (existUniqueKeySet.size() < uniqueKeyList.size()) {
            List<String> notExistUniqueKey = uniqueKeyList.stream().filter(uniqueKey -> !existUniqueKeySet.contains(uniqueKey)).collect(Collectors.toList());
            throw new IllegalArgumentException("有 unique_key 不存在, unique_key=" + JSON.toJSONString(notExistUniqueKey));
        } else {
            for (SdpaProductMappingDarkDto sdpaProductMappingDarkDto : sdpaProductAddMappingList) {
                sdpaProductMappingDarkDto.setAdProductId(uniqueKeyAdProductIdMap.get(sdpaProductMappingDarkDto.getUniqueKey()));
            }
        }

        List<Integer> mappingIds = sdpaProductAddMappingList.stream().map(SdpaProductMappingDarkDto::getMappingId).distinct().collect(Collectors.toList());

        List<AdProductMappingCPCPo> adProductMappingCPCPoExistList = adProductRepo.queryAdProductMappingCpcPoListByMappingIds(mappingIds);

        Map<ArrayList<Object>, AdProductMappingCPCPo> adProductMappingCPCPoAccountExistMap = adProductMappingCPCPoExistList.stream()
                .collect(Collectors.toMap(adProductMappingCPCPo -> Lists.newArrayList(adProductMappingCPCPo.getAdProductId(), adProductMappingCPCPo.getMappingId(), adProductMappingCPCPo.getType()), Function.identity(), (t1, t2) -> t1));

        Map<ArrayList<Object>, AdProductMappingCPCPo> adProductMappingCPCPoUnitExistMap = adProductMappingCPCPoExistList.stream()
                .collect(Collectors.toMap(adProductMappingCPCPo -> Lists.newArrayList(adProductMappingCPCPo.getMappingId(), adProductMappingCPCPo.getType()), Function.identity(), (t1, t2) -> t1));


        List<SdpaProductMappingDarkDto> insertList = new ArrayList<>();
        List<SdpaProductMappingDarkDto> updateList = new ArrayList<>();

        for (SdpaProductMappingDarkDto sdpaProductMappingDarkDto : sdpaProductAddMappingList) {
            ArrayList<Object> mappingAccountUniqueList = Lists.newArrayList(sdpaProductMappingDarkDto.getAdProductId(), sdpaProductMappingDarkDto.getMappingId(), sdpaProductMappingDarkDto.getType());
            ArrayList<Object> mappingUnitUniqueList = Lists.newArrayList(sdpaProductMappingDarkDto.getMappingId(), sdpaProductMappingDarkDto.getType());
            if (Objects.equals(sdpaProductMappingDarkDto.getType(), AdProductBindTypeEnum.ACCOUNT.getCode())) {
                if (!adProductMappingCPCPoAccountExistMap.containsKey(mappingAccountUniqueList)) {
                    insertList.add(sdpaProductMappingDarkDto);
                }
            }

            if (Objects.equals(sdpaProductMappingDarkDto.getType(), AdProductBindTypeEnum.UNIT.getCode())) {
                if (!adProductMappingCPCPoUnitExistMap.containsKey(mappingUnitUniqueList)) {
                    insertList.add(sdpaProductMappingDarkDto);
                } else {
                    updateList.add(sdpaProductMappingDarkDto);
                }
            }

        }

        adProductRepo.insertAdProductMappingCPCPoList(insertList);
        for (SdpaProductMappingDarkDto sdpaProductMappingDarkDto : updateList) {
            adProductRepo.updateProductMappingDark(sdpaProductMappingDarkDto);
        }


    }

    @Override
    @Transactional(value = AD_TM, rollbackFor = Exception.class)
    public void updateProduct(Operator operator, AdProductUpdateDto updateDto, Boolean isMapiRequest) {

        Assert.notNull(updateDto, "产品更新信息不能为空");

        var education = updateDto.getEducationDto();
        var loan = updateDto.getLoanDto();
        var novel = updateDto.getNovelDto();
        var shortFilm = updateDto.getShortFilmDto();
        SdpaProductCarDto car = updateDto.getCarDto();
        SdpaProductECommerceDto eCommerceDto = updateDto.getECommerceDto();
        SdpaProductMedicalDto medicalDto = updateDto.getMedicalDto();
        SdpaProductHouseDto houseDto = updateDto.getHouseDto();


        if (education != null) {
            var id = updateCheck(education.getProductId(), education.getAdProductName(), operator);
            AdProductTotalInfoPo productBaseByIdV2 = adProductRepo.getProductBaseByIdV2(education.getProductId());
            SdpaProductEducationDto oldSdpaProductEducationDto = convertToSdpaProductEducationDto(productBaseByIdV2);
            logOperateForLongService.addUpdateLogWithParentClass(DbTable.AD_PRODUCT_TOTAL_INFO, operator, oldSdpaProductEducationDto, education, id);
            adProductRepo.updateCourseV2(education);
        } else if (loan != null) {
            var id = updateCheck(loan.getProductId(), loan.getAdProductName(), operator);
            AdProductTotalInfoPo productBaseByIdV2 = adProductRepo.getProductBaseByIdV2(loan.getProductId());
            SdpaProductLoanDto oldSdpaProductLoanDto = convertToSdpaProductLoanDto(productBaseByIdV2);
            logOperateForLongService.addUpdateLogWithParentClass(DbTable.AD_PRODUCT_TOTAL_INFO, operator, oldSdpaProductLoanDto, loan, id);
            adProductRepo.updateLoanV2(loan);
        } else if (novel != null) {

            Set<Integer> libraryTypeSet = Sets.newHashSet(AdProductLibraryTypeEnum.NOVEL.getCode());
            adProductCheckInfoHelper.checkAccountInfo(libraryTypeSet, operator.getOperatorId());
            var id = updateCheck(novel.getProductId(), novel.getAdProductName(), operator);
            AdProductTotalInfoPo productBaseByIdV2 = adProductRepo.getProductBaseByIdV2(novel.getProductId());
            SdpaProductNovelDto oldSdpaProductNovelDto = convertToSdpaProductNovelDto(productBaseByIdV2);

            adProductCheckInfoHelper.checkNovelInfo(Collections.singletonList(novel), Boolean.FALSE);
            adProductCheckInfoHelper.checkNovelUpdate(oldSdpaProductNovelDto, novel);

            logOperateForLongService.addUpdateLogWithParentClass(DbTable.AD_PRODUCT_TOTAL_INFO, operator, oldSdpaProductNovelDto, novel, id);
            adProductRepo.updateNovelV2(novel);
        } else if (null != shortFilm) {

            Set<Integer> libraryTypeSet = Sets.newHashSet(AdProductLibraryTypeEnum.SHORT_FILM.getCode());
            adProductCheckInfoHelper.checkAccountInfo(libraryTypeSet, operator.getOperatorId());
            var id = updateCheck(shortFilm.getProductId(), shortFilm.getAdProductName(), operator);
            AdProductTotalInfoPo productBaseByIdV2 = adProductRepo.getProductBaseByIdV2(shortFilm.getProductId());
            SdpaShortFilmDto oldSdpaShortFilmDto = convertToSdpaProductShortFilmDto(productBaseByIdV2);

            adProductCheckInfoHelper.checkShortFilmInfo(Collections.singletonList(shortFilm), Boolean.FALSE);
            adProductCheckInfoHelper.checkShortFilmUpdate(oldSdpaShortFilmDto, shortFilm);

            logOperateForLongService.addUpdateLogWithParentClass(DbTable.AD_PRODUCT_TOTAL_INFO, operator, oldSdpaShortFilmDto, shortFilm, id);
            adProductRepo.updateShortFilmV2(shortFilm);
        } else if (Objects.nonNull(car)) {
            Long id = updateCheck(car.getProductId(), car.getAdProductName(), operator);
            AdProductTotalInfoPo productBaseByIdV2 = adProductRepo.getProductBaseByIdV2(car.getProductId());
            SdpaProductCarDto oldSdpaProductCarDto = convertToSdpaProductCarDto(productBaseByIdV2);
            List<BdataBrandItem> allCarBrandInfo = bdataService.getAllCarBrandInfo();
            Map<Long, String> brandIdNameMap = allCarBrandInfo.stream().collect(Collectors.toMap(BdataBrandItem::getBrandId, BdataBrandItem::getBrandName));
            oldSdpaProductCarDto.setBrandName(brandIdNameMap.get(Long.valueOf(oldSdpaProductCarDto.getBrand())));
            car.setBrandName(brandIdNameMap.get(Long.valueOf(car.getBrand())));

            Map<Long, String> spuCodeNameMap = adProductRepo.querySpuList(Collections.singletonList(car.getSpuCode())).stream().collect(Collectors.toMap(AdProductSpuPo::getCode, AdProductSpuPo::getName));
            car.setSpuName(spuCodeNameMap.getOrDefault(car.getSpuCode(), ""));
            Map<String, MapValue> carExtraInfoFromHive = getCarExtraInfoFromHive(Collections.singletonList(car.getAdProductName()));

            MapValue mapValue = carExtraInfoFromHive.get(car.getAdProductName());
            if (Objects.nonNull(mapValue)) {
                String fuelType = mapValue.getValueMap().get("fuel_type_str");
                String priceRange = mapValue.getValueMap().get("price_range");
                car.setFuelType(fuelType);
                car.setPriceRange(priceRange);
            }

            logOperateForLongService.addUpdateLogWithParentClass(DbTable.AD_PRODUCT_TOTAL_INFO, operator, oldSdpaProductCarDto, car, id);
            adProductRepo.updateCarV2(car);
        } else if (Objects.nonNull(eCommerceDto)) {

            Set<Integer> libraryTypeSet = Sets.newHashSet(AdProductLibraryTypeEnum.E_COMMERCE.getCode());
            adProductCheckInfoHelper.checkAccountInfo(libraryTypeSet, operator.getOperatorId());
            var id = updateCheck(eCommerceDto.getProductId(), eCommerceDto.getAdProductName(), operator);
            AdProductTotalInfoPo productBaseByIdV2 = adProductRepo.getProductBaseByIdV2(eCommerceDto.getProductId());
            SdpaProductECommerceDto oldSdpaProductECommerceDto = convertToSdpaProductECommerceDto(productBaseByIdV2);

            adProductCheckInfoHelper.checkECommerceUpdate(oldSdpaProductECommerceDto, eCommerceDto);
            adProductCheckInfoHelper.checkECommerceInfo(Collections.singletonList(eCommerceDto), Boolean.FALSE);

            logOperateForLongService.addUpdateLogWithParentClass(DbTable.AD_PRODUCT_TOTAL_INFO, operator, oldSdpaProductECommerceDto, eCommerceDto, id);
            adProductRepo.updateECommerceV2(eCommerceDto);
        } else if (Objects.nonNull(medicalDto)) {

            Set<Integer> libraryTypeSet = Sets.newHashSet(AdProductLibraryTypeEnum.MEDICAL.getCode());
            adProductCheckInfoHelper.checkAccountInfo(libraryTypeSet, operator.getOperatorId());
            var id = updateCheck(medicalDto.getProductId(), medicalDto.getAdProductName(), operator);
            AdProductTotalInfoPo productBaseByIdV2 = adProductRepo.getProductBaseByIdV2(medicalDto.getProductId());
            SdpaProductMedicalDto oldSdpaProductMedicalDto = convertToSdpaProductMedicalDto(productBaseByIdV2);

            adProductCheckInfoHelper.checkMedicalInfo(Collections.singletonList(medicalDto), Boolean.FALSE);

            logOperateForLongService.addUpdateLogWithParentClass(DbTable.AD_PRODUCT_TOTAL_INFO, operator, oldSdpaProductMedicalDto, medicalDto, id);
            adProductRepo.updateMedicalV2(medicalDto);
        } else if (Objects.nonNull(houseDto)) {

            Set<Integer> libraryTypeSet = Sets.newHashSet(AdProductLibraryTypeEnum.HOUSE.getCode());
            adProductCheckInfoHelper.checkAccountInfo(libraryTypeSet, operator.getOperatorId());
            var id = updateCheck(houseDto.getProductId(), houseDto.getAdProductName(), operator);
            AdProductTotalInfoPo productBaseByIdV2 = adProductRepo.getProductBaseByIdV2(houseDto.getProductId());
            SdpaProductHouseDto oldSdpaProductHouseDto = convertToSdpaProductHouseDto(productBaseByIdV2);

            adProductCheckInfoHelper.checkHouseInfo(Collections.singletonList(houseDto), Boolean.FALSE);

            logOperateForLongService.addUpdateLogWithParentClass(DbTable.AD_PRODUCT_TOTAL_INFO, operator, oldSdpaProductHouseDto, houseDto, id);
            adProductRepo.updateHouseV2(houseDto);
        }
    }

    @Override
    @Transactional(value = AD_BUSINESS_CPC_TM, rollbackFor = Exception.class)
    public void updateProductByDark(AdProductUpdateDarkDto updateDto) {

        Assert.notNull(updateDto, "产品更新信息不能为空");

        List<SdpaProductEducationDto> educationList = updateDto.getEducationDtoList();
        List<SdpaProductLoanDto> loanList = updateDto.getLoanDtoList();
        List<SdpaProductNovelDto> novelList = updateDto.getNovelDtoList();
        List<SdpaShortFilmDto> shortFilmList = updateDto.getShortFilmDtoList();
        List<SdpaProductCarDto> carList = updateDto.getCarDtoList();
        List<SdpaProductGoodsDarkDto> goodsList = updateDto.getGoodsDtoList();

        if (CollectionUtils.isNotEmpty(educationList)) {
            //check方法里顺便查了一下po 就让他把productId带过来了，方便后面更新
            var id = darkUpdateCheck(educationList);
            adProductRepo.updateCourseDarkV2(educationList);
        } else if (CollectionUtils.isNotEmpty(loanList)) {
            var id = darkUpdateCheck(loanList);
            adProductRepo.updateLoanDarkV2(loanList);
        } else if (CollectionUtils.isNotEmpty(novelList)) {
            var id = darkUpdateCheck(novelList);
            adProductRepo.updateNovelDarkV2(novelList);
        } else if (CollectionUtils.isNotEmpty(shortFilmList)) {
            var id = darkUpdateCheck(shortFilmList);
            adProductRepo.updateShortFilmDarkV2(shortFilmList);
        } else if (CollectionUtils.isNotEmpty(carList)) {
            darkUpdateCheck(carList);
            adProductRepo.updateCarDarkV2(carList);
        } else if (CollectionUtils.isNotEmpty(goodsList)) {
            darkUpdateCheck(goodsList);
            adProductRepo.updateGoodsDarkV2(goodsList);
        }
    }

    @Override
    public void updateProductMappingByDark(AdProductMappingUpdateDarkDto updateDto) {
        if (Objects.equals(updateDto.getType(), AdProductBindTypeEnum.ACCOUNT.getCode()) && Objects.isNull(updateDto.getOldUniqueKey())) {
            throw new IllegalArgumentException("账户绑定情况下 旧的产品唯一标识不能为空");
        }

        List<String> uniqueKeyList = new ArrayList<>();
        uniqueKeyList.add(updateDto.getNewUniqueKey());
        if (Objects.equals(updateDto.getType(), AdProductBindTypeEnum.ACCOUNT.getCode())) {
            uniqueKeyList.add(updateDto.getOldUniqueKey());
        }
        List<AdProductTotalInfoCPCPo> adProductTotalInfoCPCPoList = adProductRepo.queryAdProductTotalInfoCPCPoList(uniqueKeyList);
        Map<String, Long> uniqueKeyAdProductIdMap = adProductTotalInfoCPCPoList.stream().collect(Collectors.toMap(AdProductTotalInfoCPCPo::getUniqueKey, AdProductTotalInfoCPCPo::getAdProductId));

        Long newAdProductId = uniqueKeyAdProductIdMap.get(updateDto.getNewUniqueKey());
        if (Objects.isNull(newAdProductId)) {
            throw new IllegalArgumentException("新的产品唯一标识对应adProductId不存在");
        }

        Long oldAdProductId = uniqueKeyAdProductIdMap.get(updateDto.getOldUniqueKey());
        if (Objects.equals(updateDto.getType(), AdProductBindTypeEnum.ACCOUNT.getCode())) {
            if (Objects.isNull(oldAdProductId)) {
                throw new IllegalArgumentException("旧的产品唯一标识对应adProductId不存在");
            }
            List<AdProductMappingCPCPo> adProductMappingCPCPoList = adProductRepo.queryAdProductMappingCpcPoList(oldAdProductId, updateDto.getMappingId());
            if (CollectionUtils.isEmpty(adProductMappingCPCPoList)) {
                throw new IllegalArgumentException("旧的产品唯一标识和mappingId对应的关联关系不存在");
            }
        }


        adProductRepo.updateMappingDarkV2(oldAdProductId, newAdProductId, updateDto.getMappingId(), updateDto.getType());


    }

    private Long updateCheck(Long productId, String name, Operator operator) {
        var accountId = operator.getOperatorId();
        Assert.notNull(productId, "产品id不能为空");
        var product = adProductRepo.getProductBaseByIdV2(productId);
        if (!product.getAdProductName().equals(name)) {
            //变更了名字，判断是否重名
            var sameLibraryProduct = adProductRepo
                    .getProductsByLibraryIdV2(Lists.newArrayList(product.getLibraryId()));
            sameLibraryProduct.forEach(pd -> Assert.isTrue(!pd.getAdProductName().equals(name),
                    "不能修改产品名称为当前产品库已存在的名称"));
        }
        Assert.notNull(product, "找不到产品id" + productId + "对应的产品信息");
        Assert.isTrue(product.getAccountId().equals(accountId),
                "您没有权限操作不属于您的产品");
        return product.getAdProductId();
    }

    private <T extends SdpaProductCommonDto> String darkUpdateCheck(List<T> sdpaProductList) {

        Set<String> uniqueKeySet = sdpaProductList.stream()
                .map(SdpaProductCommonDto::getUniqueKey)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());
        if (uniqueKeySet.size() < sdpaProductList.size()) {
            throw new IllegalArgumentException("产品唯一标识不能为空或有重复");
        }

        List<AdProductTotalInfoCPCPo> adProductTotalInfoCPCPoList = adProductRepo.queryAdProductTotalInfoCPCPoList(new ArrayList<>(uniqueKeySet));
        Set<String> queryUniqueKeySet = adProductTotalInfoCPCPoList.stream().map(AdProductTotalInfoCPCPo::getUniqueKey).collect(Collectors.toSet());

        List<String> notQueriedUniqueKeyList = uniqueKeySet.stream().filter(uniqueKey -> !queryUniqueKeySet.contains(uniqueKey)).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(notQueriedUniqueKeyList)) {
            throw new IllegalArgumentException("产品唯一标识不存在:" + JSON.toJSONString(notQueriedUniqueKeyList));
        }

        List<String> notDarkProductUniqueKeyList = adProductTotalInfoCPCPoList.stream()
                .filter(adProductTotalInfoCPCPo -> !Objects.equals(adProductTotalInfoCPCPo.getBelongType(), AdProductBelongTypeEnum.DARK.getCode()))
                .map(AdProductTotalInfoCPCPo::getUniqueKey)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(notDarkProductUniqueKeyList)) {
            throw new IllegalArgumentException("产品唯一标识不是暗投产品:" + JSON.toJSONString(notDarkProductUniqueKeyList));
        }

        //处理一下productId 直接带下去

        Map<String, Long> uniqueKeyAdProductIdMap = adProductTotalInfoCPCPoList.stream().collect(Collectors.toMap(AdProductTotalInfoCPCPo::getUniqueKey, AdProductTotalInfoCPCPo::getAdProductId));
        for (T sdpaProduct : sdpaProductList) {
            sdpaProduct.setProductId(uniqueKeyAdProductIdMap.get(sdpaProduct.getUniqueKey()));
        }
        return null;

    }

    @Override
    @Transactional(value = AD_TM, rollbackFor = Exception.class)
    public void updateProductStatus(Operator operator, Long productId, Integer bizStatus, List<Long> productIdList) {
        var accountId = operator.getOperatorId();

        Set<Long> updateProductIdSet = new HashSet<>();
        if (Objects.nonNull(productId)) {
            updateProductIdSet.add(productId);
        }
        if (CollectionUtils.isNotEmpty(productIdList)) {
            updateProductIdSet.addAll(productIdList);
        }
        List<Long> updateProductIdList = new ArrayList<>(updateProductIdSet);

        Assert.notEmpty(updateProductIdList, "产品id不能为空");
        Assert.notNull(bizStatus, "产品状态不能为空");

        List<AdProductTotalInfoPo> productBaseByIdListV2 = adProductRepo.getProductBaseByIdListV2(updateProductIdList);
        Assert.notEmpty(productBaseByIdListV2, "找不到产品id" + JSON.toJSONString(updateProductIdList) + "对应的产品信息");
        List<AdProductTotalInfoPo> notAccountProductList = productBaseByIdListV2.stream()
                .filter(product -> !Objects.equals(product.getAccountId(), accountId))
                .collect(Collectors.toList());

        Assert.isTrue(CollectionUtils.isEmpty(notAccountProductList), "您没有权限操作不属于您的产品");

        if (AdProductStatusEnum.DISABLE.getCode().equals(bizStatus)) {
            adProductRepo.checkDeleteOrDisableProduct(updateProductIdList);
        }

        List<AdProductTotalInfoPo> newProductBaseInfoList = productBaseByIdListV2.stream().peek(product -> product.setBizStatus(bizStatus)).collect(Collectors.toList());
        List<Long> adProductIdList = newProductBaseInfoList.stream().map(AdProductTotalInfoPo::getAdProductId).collect(Collectors.toList());

        logOperateForLongService.addBatchUpdateLog(DbTable.AD_PRODUCT_TOTAL_INFO, operator,
                productBaseByIdListV2, newProductBaseInfoList, adProductIdList);
        adProductRepo.updateProductListStatusV2(updateProductIdList, bizStatus);

        if (Objects.equals(bizStatus, AdProductStatusEnum.DISABLE.getCode())) {
            adProductRepo.deleteProductMappingByProductIdListAndAccountId(updateProductIdList, accountId);
        } else if (Objects.equals(bizStatus, AdProductStatusEnum.ENABLE.getCode())) {
            adProductRepo.addProductMappingByProductIdListAndAccountId(updateProductIdList, accountId);
        }
    }

    @Override
    public void updateShareProductStatus(Operator operator, Long productId, Integer bizStatus, List<Long> productIdList) {
        var accountId = operator.getOperatorId();

        Set<Long> updateProductIdSet = new HashSet<>();
        if (Objects.nonNull(productId)) {
            updateProductIdSet.add(productId);
        }
        if (CollectionUtils.isNotEmpty(productIdList)) {
            updateProductIdSet.addAll(productIdList);
        }
        List<Long> updateProductIdList = new ArrayList<>(updateProductIdSet);

        Assert.notNull(updateProductIdList, "产品id不能为空");
        Assert.notNull(bizStatus, "产品状态不能为空");

        List<AdProductTotalInfoPo> productBaseByIdListV2 = adProductRepo.getProductBaseByIdListV2(updateProductIdList);
        Assert.notEmpty(productBaseByIdListV2, "找不到产品id" + JSON.toJSONString(updateProductIdList) + "对应的产品信息");

        if (AdProductStatusEnum.DISABLE.getCode().equals(bizStatus)) {
            adProductRepo.checkDeleteOrDisableProductSingleStatus(updateProductIdList, accountId);
        }

        List<AdProductTotalInfoPo> newProductBaseInfoList = productBaseByIdListV2.stream().peek(product -> product.setBizStatus(bizStatus)).collect(Collectors.toList());
        List<Long> adProductIdList = newProductBaseInfoList.stream().map(AdProductTotalInfoPo::getAdProductId).collect(Collectors.toList());

        logOperateForLongService.addBatchUpdateLog(DbTable.AD_PRODUCT_TOTAL_INFO, operator,
                productBaseByIdListV2, newProductBaseInfoList, adProductIdList);
        adProductRepo.updateShareProductListStatus(accountId, updateProductIdList, bizStatus);

        if (Objects.equals(bizStatus, AdProductStatusEnum.DISABLE.getCode())) {
            adProductRepo.deleteProductMappingByProductIdListAndAccountId(updateProductIdList, accountId);
        } else if (Objects.equals(bizStatus, AdProductStatusEnum.ENABLE.getCode())) {
            adProductRepo.addProductMappingByProductIdListAndAccountId(updateProductIdList, accountId);
        }
    }

    private void checkName(Integer accountId, String name) {
        Assert.isTrue(StringUtils.isNoneBlank(name), "商品库名称不能为空");
        List<AdProductLibraryPo> libraryPos = adProductRepo
                .getLibraries(accountId, null);
        if (!CollectionUtils.isEmpty(libraryPos)) {
            libraryPos.forEach(po ->
                    Assert.isTrue(!name.equals(po.getName()), "商品库名称不能重复"));
        }
    }

    @Override
    public PageResult<AdProductLibraryDto> queryLibraries(AdProductLibraryQueryDto queryDto) {
        adProductCheckInfoHelper.checkAccountInfo(Sets.newHashSet(queryDto.getType()), queryDto.getAccountId());

        var pageResult = adProductRepo.queryAllLibraries(queryDto);
        if (pageResult.getTotal() == 0) {
            return PageResult.EMPTY_PAGE_RESULT;
        }
        var librarySharePos = pageResult.getRecords();
        var libraryIds = librarySharePos.stream().map(AdProductLibrarySharePo::getLibraryId)
                .distinct().collect(Collectors.toList());

        var libraryPos = adProductRepo.getLibraries(null, libraryIds);
        var id2Account = libraryPos.stream().collect(Collectors.toMap(AdProductLibraryPo::getId,
                AdProductLibraryPo::getAccountId));

        var products = adProductRepo.getProductsByLibraryIdV2(libraryIds);
        var library2ProductCount = products.stream().collect(Collectors
                .groupingBy(AdProductTotalInfoPo::getLibraryId,
                        Collectors.counting()));

        var libraryDtoS = AdProductPoConvertor.MAPPER
                .convertLibrarySharePos2Dtos(pageResult.getRecords());
        libraryDtoS = libraryDtoS.stream().peek(libraryDto -> {
            var createAccountId = id2Account.get(libraryDto.getLibraryId());
            var productCount = library2ProductCount.getOrDefault(libraryDto.getLibraryId(), 0L);
            libraryDto.setCreateAccount(createAccountId);
            libraryDto.setProductCount(Math.toIntExact(productCount));
        }).collect(Collectors.toList());

        return PageResult.<AdProductLibraryDto>builder().total(pageResult.getTotal())
                .records(libraryDtoS).build();
    }

    @Override
    public AdProductLibraryDto getLibraryDetail(Integer accountId, Long libraryId) {
        Assert.isTrue(Utils.isPositive(libraryId), "产品库id不能为空");
        var librarys = adProductRepo.getShareLibrariesByAccountId(accountId, libraryId);
        Assert.notEmpty(librarys, "产品库不存在");
        return AdProductPoConvertor.MAPPER.convertShareLibraryPo2Dto(librarys.get(0));
    }

    @Override
    public PageResult<AdProductBaseDto> queryProduct(AdProductQueryDto queryDto) {
        Assert.notNull(queryDto.getAccountId(), "账户id不能为空");
        Assert.notNull(queryDto.getLibraryId(), "商品库id不能为空");
        var library = adProductRepo.getShareLibrariesByAccountId(queryDto.getAccountId(),
                queryDto.getLibraryId());
        Assert.notEmpty(library, "您不能操作您无权限的商品库");
        AdProductLibrarySharePo adProductLibrarySharePo = library.get(0);
        PageResult<AdProductTotalInfoPo> pageResult = null;
        List<AdProductTotalInfoPo> resultList = new ArrayList<>();
        if (Objects.equals(adProductLibrarySharePo.getBelongType(), AdProductLibraryBelongTypeEnum.OWNER.getCode())) {
            pageResult = adProductRepo.queryProductBaseV2(queryDto);
            resultList = pageResult.getRecords();
            if (pageResult.getTotal() == 0) {
                return PageResult.EMPTY_PAGE_RESULT;
            }
        } else {
            pageResult = adProductRepo.queryShareProductBaseV2(queryDto);
            resultList = pageResult.getRecords();
            if (pageResult.getTotal() == 0) {
                return PageResult.EMPTY_PAGE_RESULT;
            }
        }
        List<Long> categoryCodeList = new ArrayList<>();
        resultList.forEach(result -> {
            categoryCodeList.add(result.getFirstCategoryId());
            categoryCodeList.add(result.getSecondCategoryId());
            categoryCodeList.add(result.getThirdCategoryId());
        });
        var categoryCode2Name = adProductRepo.getCategoryCode2Name(categoryCodeList);
        return PageResult.<AdProductBaseDto>builder().total(pageResult.getTotal())
                .records(resultList.stream().map(result -> AdProductBaseDto.builder().productId(result.getAdProductId())
                        .mtime(TimeUtil.timestampToIsoDateStr(result.getMtime()))
                        .type(result.getLibraryType()).name(result.getAdProductName()).bizStatus(result.getBizStatus())
                        .firstCategoryName(categoryCode2Name.getOrDefault(result.getFirstCategoryId(), ""))
                        .secondCategoryName(categoryCode2Name.getOrDefault(result.getSecondCategoryId(), ""))
                        .thirdCategoryName(categoryCode2Name.getOrDefault(result.getThirdCategoryId(), ""))
                        .adMainImgUrl(result.getAdMainImgUrl())
                        .build()).collect(Collectors.toList())).build();
    }

    @Override
    public List<Integer> queryAuthorityAccountList(Long libraryId, Integer curAccountId) {
        Assert.isTrue(Utils.isPositive(libraryId), "产品库id不能为空");
        return adProductRepo.queryAuthorityAccountList(libraryId, curAccountId);
    }

    @Override
    public AdProductDetailDto getProductDetail(Long productId) {
        Assert.notNull(productId, "产品Id不能为空");

        var basePo = adProductRepo.getProductBaseByIdV2(productId);
        Assert.notNull(basePo, "查询不到对应的产品信息");

        AdProductDetailDto detailDto = new AdProductDetailDto();
        if (AdProductLibraryTypeEnum.COURSE.getCode().equals(basePo.getLibraryType())) {
            SdpaProductEducationDto sdpaProductEducationDto = convertToSdpaProductEducationDto(basePo);
            detailDto.setEducationDto(sdpaProductEducationDto);
        } else if (AdProductLibraryTypeEnum.BORROW_LOAN.getCode().equals(basePo.getLibraryType())) {
            SdpaProductLoanDto sdpaProductLoanDto = convertToSdpaProductLoanDto(basePo);
            detailDto.setLoanDto(sdpaProductLoanDto);
        } else if (AdProductLibraryTypeEnum.NOVEL.getCode().equals(basePo.getLibraryType())) {
            SdpaProductNovelDto sdpaProductNovelDto = convertToSdpaProductNovelDto(basePo);
            detailDto.setNovelDto(sdpaProductNovelDto);
        } else if (AdProductLibraryTypeEnum.SHORT_FILM.getCode().equals(basePo.getLibraryType())) {
            SdpaShortFilmDto sdpaShortFilmDto = convertToSdpaProductShortFilmDto(basePo);
            detailDto.setShortFilmDto(sdpaShortFilmDto);
        } else if (AdProductLibraryTypeEnum.CAR.getCode().equals(basePo.getLibraryType())) {
            SdpaProductCarDto sdpaProductCarDto = convertToSdpaProductCarDto(basePo);
            List<BdataBrandItem> allCarBrandInfo = bdataService.getAllCarBrandInfo();
            allCarBrandInfo.stream().filter(brandItem -> Objects.equals(String.valueOf(brandItem.getBrandId()), (sdpaProductCarDto.getBrand())))
                    .findFirst().ifPresent(brandItem -> sdpaProductCarDto.setBrandName(brandItem.getBrandName()));
            detailDto.setCarDto(sdpaProductCarDto);
        } else if (AdProductLibraryTypeEnum.E_COMMERCE.getCode().equals(basePo.getLibraryType())) {
            SdpaProductECommerceDto sdpaProductECommerceDto = convertToSdpaProductECommerceDto(basePo);
            detailDto.setECommerceDto(sdpaProductECommerceDto);
        } else if (AdProductLibraryTypeEnum.MEDICAL.getCode().equals(basePo.getLibraryType())) {
            SdpaProductMedicalDto sdpaProductMedicalDto = convertToSdpaProductMedicalDto(basePo);
            detailDto.setMedicalDto(sdpaProductMedicalDto);
        } else if (AdProductLibraryTypeEnum.HOUSE.getCode().equals(basePo.getLibraryType())) {
            SdpaProductHouseDto sdpaProductHouseDto = convertToSdpaProductHouseDto(basePo);
            detailDto.setHouseDto(sdpaProductHouseDto);
        } else {
            log.error("unknown type");
        }
        return detailDto;
    }

    @Override
    public AdProductDetailDarkDto getProductDetailDark(List<String> uniqueKeys) {
        AdProductDetailDarkDto adProductDetailDarkDto = new AdProductDetailDarkDto();

        List<SdpaProductEducationDto> educationDtoList = new ArrayList<>();
        List<SdpaProductLoanDto> loanDtoList = new ArrayList<>();
        List<SdpaProductNovelDto> novelDtoList = new ArrayList<>();
        List<SdpaShortFilmDto> shortFilmDtoList = new ArrayList<>();
        List<SdpaProductCarDto> carDtoList = new ArrayList<>();
        List<SdpaProductGoodsDarkDto> goodsDtoList = new ArrayList<>();
        List<SdpaProductMedicalDto> medicalDtoList = new ArrayList<>();
        List<SdpaProductHouseDto> houseDtoList = new ArrayList<>();
        adProductDetailDarkDto.setEducationDtoList(educationDtoList);
        adProductDetailDarkDto.setLoanDtoList(loanDtoList);
        adProductDetailDarkDto.setNovelDtoList(novelDtoList);
        adProductDetailDarkDto.setShortFilmDtoList(shortFilmDtoList);
        adProductDetailDarkDto.setCarDtoList(carDtoList);
        adProductDetailDarkDto.setGoodsDtoList(goodsDtoList);
        adProductDetailDarkDto.setMedicalDtoList(medicalDtoList);
        adProductDetailDarkDto.setHouseDtoList(houseDtoList);

        List<AdProductTotalInfoCPCPo> adProductTotalInfoCPCPoList = adProductRepo.queryAdProductTotalInfoCPCPoList(uniqueKeys);

        for (AdProductTotalInfoCPCPo adProductTotalInfoCPCPo : adProductTotalInfoCPCPoList) {

            if (AdProductLibraryTypeEnum.COURSE.getCode().equals(adProductTotalInfoCPCPo.getLibraryType())) {
                SdpaProductEducationDto sdpaProductEducationDto = convertToSdpaProductEducationDto(adProductTotalInfoCPCPo);
                educationDtoList.add(sdpaProductEducationDto);
            } else if (AdProductLibraryTypeEnum.BORROW_LOAN.getCode().equals(adProductTotalInfoCPCPo.getLibraryType())) {
                SdpaProductLoanDto sdpaProductLoanDto = convertToSdpaProductLoanDto(adProductTotalInfoCPCPo);
                loanDtoList.add(sdpaProductLoanDto);
            } else if (AdProductLibraryTypeEnum.NOVEL.getCode().equals(adProductTotalInfoCPCPo.getLibraryType())) {
                SdpaProductNovelDto sdpaProductNovelDto = convertToSdpaProductNovelDto(adProductTotalInfoCPCPo);
                novelDtoList.add(sdpaProductNovelDto);
            } else if (AdProductLibraryTypeEnum.SHORT_FILM.getCode().equals(adProductTotalInfoCPCPo.getLibraryType())) {
                SdpaShortFilmDto sdpaShortFilmDto = convertToSdpaProductShortFilmDto(adProductTotalInfoCPCPo);
                shortFilmDtoList.add(sdpaShortFilmDto);
            } else if (AdProductLibraryTypeEnum.CAR.getCode().equals(adProductTotalInfoCPCPo.getLibraryType())) {
                SdpaProductCarDto sdpaProductCarDto = convertToSdpaProductCarDto(adProductTotalInfoCPCPo);
                List<BdataBrandItem> allCarBrandInfo = bdataService.getAllCarBrandInfo();
                allCarBrandInfo.stream().filter(brandItem -> Objects.equals(String.valueOf(brandItem.getBrandId()), (sdpaProductCarDto.getBrand())))
                        .findFirst().ifPresent(brandItem -> sdpaProductCarDto.setBrandName(brandItem.getBrandName()));
                carDtoList.add(sdpaProductCarDto);
            } else if (AdProductLibraryTypeEnum.GOODS.getCode().equals(adProductTotalInfoCPCPo.getLibraryType())) {
                SdpaProductGoodsDarkDto sdpaProductGoodsDarkDto = convertToSdpaProductGoodsDarkDto(adProductTotalInfoCPCPo);
                adProductDetailDarkDto.getGoodsDtoList().add(sdpaProductGoodsDarkDto);
            } else if (AdProductLibraryTypeEnum.MEDICAL.getCode().equals(adProductTotalInfoCPCPo.getLibraryType())) {
                SdpaProductMedicalDto sdpaProductMedicalDto = convertToSdpaProductMedicalDto(adProductTotalInfoCPCPo);
                medicalDtoList.add(sdpaProductMedicalDto);
            } else if (AdProductLibraryTypeEnum.HOUSE.getCode().equals(adProductTotalInfoCPCPo.getLibraryType())) {
                SdpaProductHouseDto sdpaProductHouseDto = convertToSdpaProductHouseDto(adProductTotalInfoCPCPo);
                houseDtoList.add(sdpaProductHouseDto);
            }
        }

        return adProductDetailDarkDto;
    }

    public List<SdpaProductMappingDarkDto> getProductMappingDark(List<String> uniqueKeys) {
        List<AdProductTotalInfoCPCPo> adProductTotalInfoCPCPoList = adProductRepo.queryAdProductTotalInfoCPCPoList(uniqueKeys);

        Map<Long, String> adProductIdUniqueKeyMap = adProductTotalInfoCPCPoList.stream().collect(Collectors.toMap(AdProductTotalInfoCPCPo::getAdProductId, AdProductTotalInfoCPCPo::getUniqueKey));
        List<Long> adProductIdList = adProductTotalInfoCPCPoList.stream().map(AdProductTotalInfoCPCPo::getAdProductId).collect(Collectors.toList());

        List<AdProductMappingCPCPo> adProductMappingCPCPoList = adProductRepo.queryAdProductMappingDark(adProductIdList);
        List<SdpaProductMappingDarkDto> sdpaProductMappingDarkDtoList = new ArrayList<>();
        for (AdProductMappingCPCPo adProductMappingCPCPo : adProductMappingCPCPoList) {
            SdpaProductMappingDarkDto sdpaProductMappingDarkDto = new SdpaProductMappingDarkDto();
            sdpaProductMappingDarkDto.setUniqueKey(adProductIdUniqueKeyMap.get(adProductMappingCPCPo.getAdProductId()));
            sdpaProductMappingDarkDto.setMappingId(adProductMappingCPCPo.getMappingId());
            sdpaProductMappingDarkDto.setType(adProductMappingCPCPo.getType());
            sdpaProductMappingDarkDto.setAdProductId(adProductMappingCPCPo.getAdProductId());
            sdpaProductMappingDarkDtoList.add(sdpaProductMappingDarkDto);
        }

        return sdpaProductMappingDarkDtoList;
    }

    public AdProductDetailListDto queryProductByAccountId(Integer accountId, Long adProductId, Long libraryId, Integer libraryType) {
        AdProductDetailListDto adProductDetailListDto = AdProductDetailListDto.builder()
                .educationDtoList(new ArrayList<>())
                .loanDtoList(new ArrayList<>())
                .novelDtoList(new ArrayList<>())
                .shortFilmDtoList(new ArrayList<>())
                .carDtoList(new ArrayList<>())
                .eCommerceDtoList(new ArrayList<>())
                .medicalDtoList(new ArrayList<>())
                .houseDtoList(new ArrayList<>())
                .build();


        List<AdProductLibrarySharePo> adProductLibrarySharePos = adProductRepo.queryAccountAllLibraryIds(accountId);

        List<Long> libraryIdList = adProductLibrarySharePos.stream()
                .filter(adProductLibrarySharePo -> !Objects.nonNull(libraryId) || libraryId.equals(adProductLibrarySharePo.getLibraryId()))
                .filter(adProductLibrarySharePo -> !Objects.nonNull(libraryType) || libraryType.equals(adProductLibrarySharePo.getType()))
                .map(AdProductLibrarySharePo::getLibraryId)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(libraryIdList)) {
            return new AdProductDetailListDto();
        }

        List<AdProductTotalInfoPo> productsByLibraryIdV2 = adProductRepo.getProductsByLibraryIdV2(libraryIdList);
        List<AdProductTotalInfoPo> adProductTotalInfoPoList = productsByLibraryIdV2.stream()
                .filter(adProductTotalInfoPo -> !Objects.nonNull(adProductId) || Objects.equals(adProductTotalInfoPo.getAdProductId(), adProductId))
                .collect(Collectors.toList());

        for (AdProductTotalInfoPo adProductTotalInfoPo : adProductTotalInfoPoList) {
            if (AdProductLibraryTypeEnum.COURSE.getCode().equals(adProductTotalInfoPo.getLibraryType())) {
                SdpaProductEducationDto sdpaProductEducationDto = convertToSdpaProductEducationDto(adProductTotalInfoPo);
                adProductDetailListDto.getEducationDtoList().add(sdpaProductEducationDto);
            } else if (AdProductLibraryTypeEnum.BORROW_LOAN.getCode().equals(adProductTotalInfoPo.getLibraryType())) {
                SdpaProductLoanDto sdpaProductLoanDto = convertToSdpaProductLoanDto(adProductTotalInfoPo);
                adProductDetailListDto.getLoanDtoList().add(sdpaProductLoanDto);
            } else if (AdProductLibraryTypeEnum.NOVEL.getCode().equals(adProductTotalInfoPo.getLibraryType())) {
                SdpaProductNovelDto sdpaProductNovelDto = convertToSdpaProductNovelDto(adProductTotalInfoPo);
                adProductDetailListDto.getNovelDtoList().add(sdpaProductNovelDto);
            } else if (AdProductLibraryTypeEnum.SHORT_FILM.getCode().equals(adProductTotalInfoPo.getLibraryType())) {
                SdpaShortFilmDto sdpaShortFilmDto = convertToSdpaProductShortFilmDto(adProductTotalInfoPo);
                adProductDetailListDto.getShortFilmDtoList().add(sdpaShortFilmDto);
            } else if (AdProductLibraryTypeEnum.CAR.getCode().equals(adProductTotalInfoPo.getLibraryType())) {
                SdpaProductCarDto sdpaProductCarDto = convertToSdpaProductCarDto(adProductTotalInfoPo);
                List<BdataBrandItem> allCarBrandInfo = bdataService.getAllCarBrandInfo();
                allCarBrandInfo.stream().filter(brandItem -> Objects.equals(String.valueOf(brandItem.getBrandId()), (sdpaProductCarDto.getBrand())))
                        .findFirst().ifPresent(brandItem -> sdpaProductCarDto.setBrandName(brandItem.getBrandName()));
                adProductDetailListDto.getCarDtoList().add(sdpaProductCarDto);
            } else if (AdProductLibraryTypeEnum.E_COMMERCE.getCode().equals(adProductTotalInfoPo.getLibraryType())) {
                SdpaProductECommerceDto sdpaProductECommerceDto = convertToSdpaProductECommerceDto(adProductTotalInfoPo);
                adProductDetailListDto.getECommerceDtoList().add(sdpaProductECommerceDto);
            } else if (AdProductLibraryTypeEnum.MEDICAL.getCode().equals(adProductTotalInfoPo.getLibraryType())) {
                SdpaProductMedicalDto sdpaProductMedicalDto = convertToSdpaProductMedicalDto(adProductTotalInfoPo);
                adProductDetailListDto.getMedicalDtoList().add(sdpaProductMedicalDto);
            } else if (AdProductLibraryTypeEnum.HOUSE.getCode().equals(adProductTotalInfoPo.getLibraryType())) {
                SdpaProductHouseDto sdpaProductHouseDto = convertToSdpaProductHouseDto(adProductTotalInfoPo);
                adProductDetailListDto.getHouseDtoList().add(sdpaProductHouseDto);
            }
        }
        return adProductDetailListDto;

    }

    @Override
    @Transactional(value = AD_TM, rollbackFor = Exception.class)
    public void deleteProductOrLibrary(Long libraryId, Long productId, Operator operator, Boolean isMapiRequest) {
        Assert.isTrue(Utils.isPositive(libraryId) || Utils.isPositive(productId),
                "删除的时候商品库id和商品id最少要传一个");
        Integer accountId = operator.getOperatorId();
        if (Utils.isPositive(libraryId)) {
            var libraryIds = Lists.newArrayList(libraryId);
            var librarys = adProductRepo.getLibraries(accountId, libraryIds);
            Assert.notEmpty(librarys, "您不能删除共享的产品库");

            AdProductLibraryPo adProductLibraryPo = librarys.get(0);
            Set<Integer> libraryTypeSet = Sets.newHashSet(adProductLibraryPo.getType());
            adProductCheckInfoHelper.checkAccountInfo(libraryTypeSet, accountId);

            adProductRepo.deleteAdProductsV2(libraryIds, null);
            adProductRepo.deleteProductLibrary(libraryIds);
            adProductRepo.deleteAdProductInfoShare(new ArrayList<>(), libraryIds);
        } else {
            var old = adProductRepo.getProductBaseByIdV2(productId);
            Assert.notNull(old, "您不能删除共享的产品");
            Assert.isTrue(old.getAccountId().equals(accountId), "您不能删除共享的产品");

            Set<Integer> libraryTypeSet = Sets.newHashSet(old.getLibraryType());
            adProductCheckInfoHelper.checkAccountInfo(libraryTypeSet, accountId);

            adProductRepo.deleteAdProductsV2(null, Lists.newArrayList(productId));
            adProductRepo.deleteAdProductInfoByProductId(Collections.singletonList(productId));
        }
    }

    @Transactional(value = AD_BUSINESS_CPC_TM, rollbackFor = Exception.class)
    public void deleteProductByDark(List<String> uniqueKeys) {
        Assert.isTrue(CollectionUtils.isNotEmpty(uniqueKeys), "删除的时候商品id最少要传一个");

        List<AdProductTotalInfoCPCPo> adProductTotalInfoCPCPoList = adProductRepo.queryAdProductTotalInfoCPCPoList(uniqueKeys);

        List<Long> notDarkAdProductIdList = adProductTotalInfoCPCPoList.stream()
                .filter(adProductTotalInfoCPCPo -> !Objects.equals(adProductTotalInfoCPCPo.getBelongType(), AdProductBelongTypeEnum.DARK.getCode()))
                .map(AdProductTotalInfoCPCPo::getAdProductId)
                .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(notDarkAdProductIdList)) {
            throw new IllegalArgumentException("产品不是暗投产品:" + JSON.toJSONString(notDarkAdProductIdList));
        }

        List<Long> adProductIdList = adProductTotalInfoCPCPoList.stream()
                .filter(adProductTotalInfoCPCPo -> Objects.equals(adProductTotalInfoCPCPo.getBelongType(), AdProductBelongTypeEnum.DARK.getCode()))
                .map(AdProductTotalInfoCPCPo::getAdProductId)
                .collect(Collectors.toList());

        adProductRepo.deleteAdProductDarkV2(adProductIdList);

    }

    @Override
    @Transactional(value = AD_BUSINESS_CPC_TM, rollbackFor = Exception.class)
    public void deleteProductMappingByDark(String uniqueKey, List<Integer> mappingIds, Integer type) {
        List<AdProductTotalInfoCPCPo> adProductTotalInfoCPCPoList = adProductRepo.queryAdProductTotalInfoCPCPoList(Collections.singletonList(uniqueKey));
        if (CollectionUtils.isEmpty(adProductTotalInfoCPCPoList)) {
            return;
        }
        AdProductTotalInfoCPCPo adProductTotalInfoCPCPo = adProductTotalInfoCPCPoList.get(0);
        Long adProductId = adProductTotalInfoCPCPo.getAdProductId();

        adProductRepo.deleteProductMappingByProductIdDark(adProductId, mappingIds, type);
    }


    @Override
    public Long getProductMappingByMappingId(Integer mappingId, Integer type) {
        AdProductMappingPo mappingPo = adProductRepo.getProductMappingByMappingId(mappingId, type);
        return Optional.ofNullable(mappingPo).orElseGet(() -> AdProductMappingPo.builder()
                .adProductId(0L).build()).getAdProductId();
    }


    @Override
    @Transactional(value = AD_TM, rollbackFor = Exception.class)
    public List<ImportAdProductResultDto> importProductCategorySkuDtos(Boolean reImport, AdProductLibraryTypeEnum libraryTypeEnum,
                                                                       List<ImportAdProductCategorySkuDto> importDtos) {
        log.info("importProductCategorySkuDtos start, size={}", importDtos.size());
        if (CollectionUtils.isEmpty(importDtos)) {
            return Collections.EMPTY_LIST;
        }

        validateCategorySkuParams(libraryTypeEnum, importDtos);

        List<AdProductCategoryPo> allCategoryPos = adProductRepo.getAllProductCategoryList();
        List<AdProductSkuPo> allSkuPos = adProductRepo.getAllCategorySkuList();
        Map<Long, String> categoryIdNameMap = allCategoryPos.stream().collect(Collectors.toMap(AdProductCategoryPo::getId, AdProductCategoryPo::getName));
        Map<Long, String> skuIdNameMap = allSkuPos.stream().collect(Collectors.toMap(AdProductSkuPo::getCode, AdProductSkuPo::getName));

        if (reImport) {
//            deleteCategorySkus();
        }

        for (ImportAdProductCategorySkuDto importDto : importDtos) {
            importDto.setSuccess(true);
            importDto.setMsg("");
            importDto.setDataProcessEnum(AdProductDataProcessEnum.CREATE);

            try {
                AdProductCategoryPo firstCategoryPo =
                        adProductRepo.getProductCategoryByCode(importDto.getFirstCategoryCode());
                // 一级
                if (firstCategoryPo == null) {
                    adProductRepo.insertCategory(importDto.getFirstCategoryCode(), importDto.getFirstCategoryName(), 0L, AdProductCategoryLevelEnum.ONE.getCode());
                } else {
                    String oldName = categoryIdNameMap.getOrDefault(importDto.getFirstCategoryCode(), "");
                    if (!importDto.getFirstCategoryName().equals(oldName)) {
                        adProductRepo.updateCategoryNameByCode(importDto.getFirstCategoryCode(),
                                importDto.getFirstCategoryName());
                    }
                }

                // 二级
                AdProductCategoryPo secondCategoryPo =
                        adProductRepo.getProductCategoryByCode(importDto.getSecondCategoryCode(), importDto.getFirstCategoryCode());
                if (secondCategoryPo == null) {
                    adProductRepo.insertCategory(importDto.getSecondCategoryCode(),
                            importDto.getSecondCategoryName(), importDto.getFirstCategoryCode(), AdProductCategoryLevelEnum.TWO.getCode());
                } else {
                    String oldName = categoryIdNameMap.getOrDefault(importDto.getSecondCategoryCode(), "");
                    if (!importDto.getSecondCategoryName().equals(oldName)) {
                        adProductRepo.updateCategoryNameByCode(importDto.getSecondCategoryCode(),
                                importDto.getSecondCategoryName());
                    }
                }

                // 三级
                AdProductCategoryPo thirdCategoryPo =
                        adProductRepo.getProductCategoryByCode(importDto.getThirdCategoryCode(), importDto.getSecondCategoryCode());
                if (thirdCategoryPo == null) {
                    adProductRepo.insertCategory(importDto.getThirdCategoryCode(),
                            importDto.getThirdCategoryName(), importDto.getSecondCategoryCode(), AdProductCategoryLevelEnum.THREE.getCode());
                } else {
                    String oldName = categoryIdNameMap.getOrDefault(importDto.getThirdCategoryCode(), "");
                    if (!importDto.getThirdCategoryName().equals(oldName)) {
                        adProductRepo.updateCategoryNameByCode(importDto.getThirdCategoryCode(),
                                importDto.getThirdCategoryName());
                    }
                }

                // spu
                if (Utils.isPositive(importDto.getSpuCode())) {
                    List<AdProductSpuPo> spuPos = adProductRepo.querySpu(importDto.getSpuCode(), null);
                    if (CollectionUtils.isEmpty(spuPos)) {
                        adProductRepo.insertSpu(importDto.getSpuCode(), importDto.getSpuName(), importDto.getThirdCategoryCode());
                    } else {
                        adProductRepo.updateSpuNameByCode(importDto.getSpuCode(),
                                importDto.getSpuName());
                    }
                }

                // sku
                if (Utils.isPositive(importDto.getSkuCode())) {
                    List<AdProductSkuPo> skuPos = adProductRepo.querySku(importDto.getSkuCode(), null);
                    if (CollectionUtils.isEmpty(skuPos)) {
                        adProductRepo.insertSku(importDto.getSpuCode(), importDto.getSkuCode(),
                                importDto.getSkuName());
                    } else {
                        AdProductSkuPo skuPo = skuPos.get(0);
                        adProductRepo.updateSkuNameByCode(importDto.getSkuCode(),
                                importDto.getSkuName());
                    }
                }
            } catch (Exception e) {
                importDto.setSuccess(false);
                importDto.setMsg(e.getMessage());
                log.error("import category sku error, e={}", e);
            }
        }


        return importDtos.stream().map(t -> ImportAdProductResultDto.builder()
                .name(String.format("skuName=%s,spuName=%s", t.getSkuName(), t.getSpuName()))
                .lineNum(t.getLineNum())
                .success(t.getSuccess())
                .msg(t.getMsg())
                .build()).collect(Collectors.toList());
    }

    @Override
    @Transactional(value = AD_TM, rollbackFor = Exception.class)
    public List<ImportAdProductResultDto> importProductLoanDtos(List<ImportAdProductLoanDto> importDtos, Operator operator) {
        AdProductImportBase adProductBizBase = adProductFactory.getByAdProductLibraryType(AdProductLibraryTypeEnum.BORROW_LOAN);
        return adProductBizBase.importAdProductData(importDtos, operator);
    }

    @Override
    @Transactional(value = AD_TM, rollbackFor = Exception.class)
    public List<ImportAdProductResultDto> importProductEducationDtos(List<ImportAdProductEducationDto> importDtos, Operator operator) {
        AdProductImportBase adProductBizBase = adProductFactory.getByAdProductLibraryType(AdProductLibraryTypeEnum.COURSE);
        return adProductBizBase.importAdProductData(importDtos, operator);
    }

    @Override
    @Transactional(value = AD_TM, rollbackFor = Exception.class)
    public List<ImportAdProductResultDto> importProductNovelDtos(List<ImportAdProductNovelDto> importDtos, Operator operator) {
        AdProductImportBase adProductBizBase = adProductFactory.getByAdProductLibraryType(AdProductLibraryTypeEnum.NOVEL);
        return adProductBizBase.importAdProductData(importDtos, operator);
    }

    @Override
    @Transactional(value = AD_TM, rollbackFor = Exception.class)
    public List<ImportAdProductResultDto> importProductShortFilmDtos(List<ImportAdProductShortFilmDto> importDtos, Operator operator) {
        AdProductImportBase adProductBizBase = adProductFactory.getByAdProductLibraryType(AdProductLibraryTypeEnum.SHORT_FILM);
        return adProductBizBase.importAdProductData(importDtos, operator);
    }

    @Override
    @Transactional(value = AD_TM, rollbackFor = Exception.class)
    public List<ImportAdProductResultDto> importProductMedicalDtos(List<ImportAdProductMedicalDto> importDtos, Operator operator) {
        AdProductImportBase adProductBizBase = adProductFactory.getByAdProductLibraryType(AdProductLibraryTypeEnum.MEDICAL);
        return adProductBizBase.importAdProductData(importDtos, operator);
    }

    @Override
    @Transactional(value = AD_TM, rollbackFor = Exception.class)
    public List<ImportAdProductResultDto> importProductHouseDtos(List<ImportAdProductHouseDto> importDtos, Operator operator) {
        AdProductImportBase adProductBizBase = adProductFactory.getByAdProductLibraryType(AdProductLibraryTypeEnum.HOUSE);
        return adProductBizBase.importAdProductData(importDtos, operator);
    }

    @Override
    @Transactional(value = AD_TM, rollbackFor = Exception.class)
    public List<ImportAdProductResultDto> importProductCarDtos(List<ImportAdProductCarDto> importDtos) {
        AdProductImportBase adProductBizBase = adProductFactory.getByAdProductLibraryType(AdProductLibraryTypeEnum.CAR);
        return adProductBizBase.importAdProductData(importDtos, null);
    }

    private void validateCategorySkuParams(AdProductLibraryTypeEnum libraryTypeEnum,
                                           List<ImportAdProductCategorySkuDto> categorySkuDtos) {

        Set<String> skuSpuNameSet = new HashSet<>();
        for (ImportAdProductCategorySkuDto categorySkuDto : categorySkuDtos) {
            // 存在名称，必须有 id
            if (StringUtils.isNotBlank(categorySkuDto.getFirstCategoryName())) {
                Assert.isTrue(Utils.isPositive(categorySkuDto.getFirstCategoryCode()), "一级code不存在");
            }
            if (StringUtils.isNotBlank(categorySkuDto.getSecondCategoryName())) {
                Assert.isTrue(Utils.isPositive(categorySkuDto.getSecondCategoryCode()), "二级code不存在");
            }
            if (StringUtils.isNotBlank(categorySkuDto.getThirdCategoryName())) {
                Assert.isTrue(Utils.isPositive(categorySkuDto.getThirdCategoryCode()), "三级code不存在");
            }
            if (StringUtils.isNotBlank(categorySkuDto.getSpuName())) {
                Assert.isTrue(Utils.isPositive(categorySkuDto.getSpuCode()), "spu code不存在");
            }
            if (StringUtils.isNotBlank(categorySkuDto.getSkuName())) {
                Assert.isTrue(Utils.isPositive(categorySkuDto.getSkuCode()), "sku code不存在");
            }

            // 一级，二级，三级，spu, sku
            // 存在 sku, spuu,一级二级三级都需要存在
            if (Utils.isPositive(categorySkuDto.getSkuCode())) {
                Assert.isTrue(Utils.isPositive(categorySkuDto.getFirstCategoryCode()), "一级行业code不存在");
                Assert.isTrue(Utils.isPositive(categorySkuDto.getSecondCategoryCode()), "二级行业code不存在");
                Assert.isTrue(Utils.isPositive(categorySkuDto.getThirdCategoryCode()), "三级行业code不存在");
                Assert.isTrue(Utils.isPositive(categorySkuDto.getSpuCode()), "spu code不存在");
            }

            // 存在 spu, 一级二级三级都需要存在
            if (Utils.isPositive(categorySkuDto.getSpuCode())) {
                Assert.isTrue(Utils.isPositive(categorySkuDto.getFirstCategoryCode()), "一级行业code不存在");
                Assert.isTrue(Utils.isPositive(categorySkuDto.getSecondCategoryCode()), "二级行业code不存在");
                Assert.isTrue(Utils.isPositive(categorySkuDto.getThirdCategoryCode()), "三级行业code不存在");
            }

            // 存在 三级, 一级二级都需要存在
            if (Utils.isPositive(categorySkuDto.getThirdCategoryCode())) {
                Assert.isTrue(Utils.isPositive(categorySkuDto.getFirstCategoryCode()), "一级行业code不存在");
                Assert.isTrue(Utils.isPositive(categorySkuDto.getSecondCategoryCode()), "二级行业code不存在");
            }

            // 存在 二级, 一级需要存在
            if (Utils.isPositive(categorySkuDto.getSecondCategoryCode())) {
                Assert.isTrue(Utils.isPositive(categorySkuDto.getFirstCategoryCode()), "一级行业code不存在");
            }

            String key = categorySkuDto.getFirstCategoryName() + "_" + categorySkuDto.getSecondCategoryName() + "_" +
                    categorySkuDto.getThirdCategoryName() + "_" + categorySkuDto.getSpuName() +
                    "_" + categorySkuDto.getSkuName();
            if (skuSpuNameSet.contains(key)) {
                throw new ServiceRuntimeException("存在重复数据，请检查, key=" + key);
            } else {
                skuSpuNameSet.add(key);
            }
        }
    }

    @SneakyThrows
    @Override
    @Transactional(value = AD_TM, rollbackFor = Exception.class)
    public void manualLabelImport(ManualLabelImportDto manualLabelImportDto, byte[] bytes) {
        AdProductLibraryTypeEnum adProductLibraryTypeEnum = manualLabelImportDto.getAdProductLibraryTypeEnum();
        MockMultipartFile multipartFile = new MockMultipartFile(manualLabelImportDto.getFileName(), bytes);
        String objectKey = dir + System.currentTimeMillis() + multipartFile.getName();
        PutObjectResult result = BOSSUtils.putObject(objectKey, multipartFile.getInputStream());
        manualLabelImportDto.setFileKey(result.getETag());
        manualLabelImportDto.setFileUrl(BOSSUtils.getUrlByObjectKey(objectKey).toString());
        Context emptyContext = new Context();
        Integer labelType = manualLabelImportDto.getLabelType();
        switch (adProductLibraryTypeEnum) {
            case NOVEL:
                List<ImportAdProductNovelDto> novelDtos = sdpaParseExcelAndCheckService.parseNovelExcel(multipartFile, emptyContext, 0L, labelType);
                Map<Integer, List<ImportAdProductNovelDto>> novelDtosMap = novelDtos.stream().collect(Collectors.groupingBy(ImportAdProductNovelDto::getAccountId));
                for (Map.Entry<Integer, List<ImportAdProductNovelDto>> entry : novelDtosMap.entrySet()) {
                    Integer accountId = entry.getKey();
                    Long libraryId = adProductRepo.manualLabelInsertLibrary(accountId,
                            adProductLibraryTypeEnum.getName() + "(手工标注)" + System.currentTimeMillis(),
                            adProductLibraryTypeEnum.getCode(), AdProductCreateTypeEnum.BY_FILE.getCode(), AdProductLibraryBelongTypeEnum.MANUAL_LABEL);
                    entry.getValue().forEach(r -> r.setLibraryId(libraryId));
                    entry.getValue().forEach(r -> r.setBelongType(AdProductBelongTypeEnum.MANUAL_IMPORT.getCode()));
                    importProductNovelDtos(entry.getValue(), Operator.SYSTEM);
                }
                break;
            case COURSE:
                List<ImportAdProductEducationDto> educationDtos = sdpaParseExcelAndCheckService.parseEducationExcel(multipartFile, emptyContext, 0L, labelType);
                Map<Integer, List<ImportAdProductEducationDto>> educationDtosMap = educationDtos.stream().collect(Collectors.groupingBy(ImportAdProductEducationDto::getAccountId));
                for (Map.Entry<Integer, List<ImportAdProductEducationDto>> entry : educationDtosMap.entrySet()) {
                    Integer accountId = entry.getKey();
                    Long libraryId = adProductRepo.manualLabelInsertLibrary(accountId,
                            adProductLibraryTypeEnum.getName() + "(手工标注)" + System.currentTimeMillis(),
                            adProductLibraryTypeEnum.getCode(), AdProductCreateTypeEnum.BY_FILE.getCode(), AdProductLibraryBelongTypeEnum.MANUAL_LABEL);
                    entry.getValue().forEach(r -> r.setLibraryId(libraryId));
                    entry.getValue().forEach(r -> r.setBelongType(AdProductBelongTypeEnum.MANUAL_IMPORT.getCode()));

                    importProductEducationDtos(entry.getValue(), Operator.SYSTEM);
                }
                break;
            case SHORT_FILM:
                List<ImportAdProductShortFilmDto> shortFilmDtos = sdpaParseExcelAndCheckService.parseShortFilmExcel(multipartFile, emptyContext, 0L, labelType);
                Map<Integer, List<ImportAdProductShortFilmDto>> shortFilmDtosMap = shortFilmDtos.stream().collect(Collectors.groupingBy(ImportAdProductShortFilmDto::getAccountId));
                for (Map.Entry<Integer, List<ImportAdProductShortFilmDto>> entry : shortFilmDtosMap.entrySet()) {
                    Integer accountId = entry.getKey();
                    Long libraryId = adProductRepo.manualLabelInsertLibrary(accountId,
                            adProductLibraryTypeEnum.getName() + "(手工标注)" + System.currentTimeMillis(),
                            adProductLibraryTypeEnum.getCode(), AdProductCreateTypeEnum.BY_FILE.getCode(), AdProductLibraryBelongTypeEnum.MANUAL_LABEL);
                    entry.getValue().forEach(r -> r.setLibraryId(libraryId));
                    entry.getValue().forEach(r -> r.setBelongType(AdProductBelongTypeEnum.MANUAL_IMPORT.getCode()));

                    importProductShortFilmDtos(entry.getValue(), Operator.SYSTEM);
                }
                break;
            case BORROW_LOAN:
                List<ImportAdProductLoanDto> loanDtos = sdpaParseExcelAndCheckService.parseBorrowLoanExcel(multipartFile, emptyContext, 0L, labelType);
                Map<Integer, List<ImportAdProductLoanDto>> loanDtosMap = loanDtos.stream().collect(Collectors.groupingBy(ImportAdProductLoanDto::getAccountId));
                for (Map.Entry<Integer, List<ImportAdProductLoanDto>> entry : loanDtosMap.entrySet()) {
                    Integer accountId = entry.getKey();
                    Long libraryId = adProductRepo.manualLabelInsertLibrary(accountId,
                            adProductLibraryTypeEnum.getName() + "(手工标注)" + System.currentTimeMillis(),
                            adProductLibraryTypeEnum.getCode(), AdProductCreateTypeEnum.BY_FILE.getCode(), AdProductLibraryBelongTypeEnum.MANUAL_LABEL);
                    entry.getValue().forEach(r -> r.setLibraryId(libraryId));
                    entry.getValue().forEach(r -> r.setBelongType(AdProductBelongTypeEnum.MANUAL_IMPORT.getCode()));

                    importProductLoanDtos(entry.getValue(), Operator.SYSTEM);
                }
                break;
            case MEDICAL:
                List<ImportAdProductMedicalDto> importAdProductMedicalDtos = sdpaParseExcelAndCheckService.parseMedicalExcel(multipartFile, emptyContext, 0L, labelType);
                Map<Integer, List<ImportAdProductMedicalDto>> medicalDtosMap = importAdProductMedicalDtos.stream().collect(Collectors.groupingBy(ImportAdProductMedicalDto::getAccountId));
                for (Map.Entry<Integer, List<ImportAdProductMedicalDto>> entry : medicalDtosMap.entrySet()) {
                    Integer accountId = entry.getKey();
                    Long libraryId = adProductRepo.manualLabelInsertLibrary(accountId,
                            adProductLibraryTypeEnum.getName() + "(手工标注)" + System.currentTimeMillis(),
                            adProductLibraryTypeEnum.getCode(), AdProductCreateTypeEnum.BY_FILE.getCode(), AdProductLibraryBelongTypeEnum.MANUAL_LABEL);
                    entry.getValue().forEach(r -> r.setLibraryId(libraryId));
                    entry.getValue().forEach(r -> r.setBelongType(AdProductBelongTypeEnum.MANUAL_IMPORT.getCode()));

                    importProductMedicalDtos(entry.getValue(), Operator.SYSTEM);
                }
                break;
            case HOUSE:
                List<ImportAdProductHouseDto> importAdProductHouseDtos = sdpaParseExcelAndCheckService.parseHouseExcel(multipartFile, emptyContext, 0L, labelType);
                Map<Integer, List<ImportAdProductHouseDto>> houseDtosMap = importAdProductHouseDtos.stream().collect(Collectors.groupingBy(ImportAdProductHouseDto::getAccountId));
                for (Map.Entry<Integer, List<ImportAdProductHouseDto>> entry : houseDtosMap.entrySet()) {
                    Integer accountId = entry.getKey();
                    Long libraryId = adProductRepo.manualLabelInsertLibrary(accountId,
                            adProductLibraryTypeEnum.getName() + "(手工标注)" + System.currentTimeMillis(),
                            adProductLibraryTypeEnum.getCode(), AdProductCreateTypeEnum.BY_FILE.getCode(), AdProductLibraryBelongTypeEnum.MANUAL_LABEL);
                    entry.getValue().forEach(r -> r.setLibraryId(libraryId));
                    entry.getValue().forEach(r -> r.setBelongType(AdProductBelongTypeEnum.MANUAL_IMPORT.getCode()));

                    importProductHouseDtos(entry.getValue(), Operator.SYSTEM);
                }
                break;
            default:
                return;
        }

        manualLabelImportDto.setCtime(System.currentTimeMillis());
        insertManualLabelLog(manualLabelImportDto);
    }


    private void insertManualLabelLog(ManualLabelImportDto manualLabelImportDto) {
        Operator operator = manualLabelImportDto.getOperator();
        final LogCpcOperationBo bo = LogCpcOperationBo.builder()
                .operatorUsername(operator.getOperatorName())
                .operatorType(operator.getOperatorType().getCode())
                .tableName(DbTable.AD_PRODUCT_LIBRARY_MANUAL_LABEL.getName())
                .type(OperationType.INSERT)
                .value(JSON.toJSONString(manualLabelImportDto))
                .bilibiliUsername(operator.getBilibiliUserName() == null ? "" : operator.getBilibiliUserName())
                .objId(Optional.ofNullable(operator.getOperatorId()).map(Integer::longValue).orElse(0L))
                .accountId(0)
                .build();
        logCpcOperationService.insert(bo);
    }

    @Override
    public PageResult<ManualLabelImportDto> manualLabelImportHistoryList(Operator operator, Page page) {
        final QueryLogCpcOperationBo queryBo = new QueryLogCpcOperationBo();
        queryBo.addTableName(DbTable.AD_PRODUCT_LIBRARY_MANUAL_LABEL.getName());
        queryBo.setPage(page.getPage());
        queryBo.setSize(page.getPageSize());
        final PageResult<LogCpcOperationBo> pagedBos = logCpcOperationService.queryByPage(queryBo);
        PageResult<ManualLabelImportDto> result = new PageResult<>();
        result.setRecords(pagedBos.getRecords()
                .stream()
                .map(r -> JSON.parseObject(r.getValue(), ManualLabelImportDto.class))
                .collect(Collectors.toList()));
        result.setTotal(pagedBos.getTotal());
        return result;
    }

    @Override
    public List<Long> batchGenProductId(Integer size) {
        Assert.isTrue(size <= 50, "size 不能大于50");
        return mainCommonArchSequenceProxy.batchGenId(size);
    }


    private void supplyCategoryName(List<? extends SdpaProductCommonDto> sdpaProductCommonDtoList) {
        Set<Long> categoryCodeSet = new HashSet<>();
        for (SdpaProductCommonDto sdpaProductCommonDto : sdpaProductCommonDtoList) {
            categoryCodeSet.add(sdpaProductCommonDto.getFirstCategoryId());
            categoryCodeSet.add(sdpaProductCommonDto.getSecondCategoryId());
            categoryCodeSet.add(sdpaProductCommonDto.getThirdCategoryId());
        }

        Map<Long, String> categoryCode2NameMap = adProductRepo.getCategoryCode2Name(new ArrayList<>(categoryCodeSet));

        for (SdpaProductCommonDto sdpaProductCommonDto : sdpaProductCommonDtoList) {
            sdpaProductCommonDto.setFirstCategoryName(categoryCode2NameMap.getOrDefault(sdpaProductCommonDto.getFirstCategoryId(), ""));
            sdpaProductCommonDto.setSecondCategoryName(categoryCode2NameMap.getOrDefault(sdpaProductCommonDto.getSecondCategoryId(), ""));
            sdpaProductCommonDto.setThirdCategoryName(categoryCode2NameMap.getOrDefault(sdpaProductCommonDto.getThirdCategoryId(), ""));
        }

    }

    private void validateProductNames(List<? extends SdpaProductCommonDto> sdpaProductCommonDtoList, List<String> productNames) {

        for (SdpaProductCommonDto sdpaProductCommonDto : sdpaProductCommonDtoList) {
            Assert.isTrue(!productNames.contains(sdpaProductCommonDto.getAdProductName()),
                    "[" + sdpaProductCommonDto.getAdProductName() + "]与之前的产品名称一致,请变更名称后重试");
            productNames.add(sdpaProductCommonDto.getAdProductName());
        }
    }

    public SdpaProductEducationDto convertToSdpaProductEducationDto(AdProductTotalInfoPo adProductTotalInfoPo) {
        SdpaProductEducationDto sdpaProductEducationDto = new SdpaProductEducationDto();
        supplyAdProductCommonDto(sdpaProductEducationDto, adProductTotalInfoPo);
        String adAttributes = adProductTotalInfoPo.getAdAttributes();
        SdpaProductEducationExtraDto sdpaProductEducationExtraDto = JSON.parseObject(adAttributes, new TypeReference<SdpaProductEducationExtraDto>() {
        });
        sdpaProductEducationDto.setIosUpUlinkUrl(sdpaProductEducationExtraDto.getIosUpUlinkUrl());
        sdpaProductEducationDto.setBrand(sdpaProductEducationExtraDto.getBrand());
        sdpaProductEducationDto.setSpuCode(sdpaProductEducationExtraDto.getSpuCode());
        sdpaProductEducationDto.setRemark(sdpaProductEducationExtraDto.getRemark());
        sdpaProductEducationDto.setArea(sdpaProductEducationExtraDto.getArea());
        sdpaProductEducationDto.setAge(sdpaProductEducationExtraDto.getAge());
        sdpaProductEducationDto.setTeachChannel(Objects.isNull(sdpaProductEducationExtraDto.getTeachChannel()) ? "" : sdpaProductEducationExtraDto.getTeachChannel());
        sdpaProductEducationDto.setTeachType(Objects.isNull(sdpaProductEducationExtraDto.getTeachType()) ? "" : sdpaProductEducationExtraDto.getTeachType());
        sdpaProductEducationDto.setTrialClass(Objects.isNull(sdpaProductEducationExtraDto.getTrialClass()) ? "" : sdpaProductEducationExtraDto.getTrialClass());
        sdpaProductEducationDto.setBrief(sdpaProductEducationExtraDto.getBrief());
        sdpaProductEducationDto.setSkuCode(sdpaProductEducationExtraDto.getSkuCode());
        sdpaProductEducationDto.setPositivePrice(sdpaProductEducationExtraDto.getPositivePrice());
        sdpaProductEducationDto.setTrialPrice(adProductTotalInfoPo.getAdOriginalPrice());
        return sdpaProductEducationDto;

    }

    private SdpaProductEducationDto convertToSdpaProductEducationDto(AdProductTotalInfoCPCPo adProductTotalInfoPo) {
        SdpaProductEducationDto sdpaProductEducationDto = new SdpaProductEducationDto();
        supplyAdProductCommonDto(sdpaProductEducationDto, adProductTotalInfoPo);
        String adAttributes = adProductTotalInfoPo.getAdAttributes();
        SdpaProductEducationExtraDto sdpaProductEducationExtraDto = JSON.parseObject(adAttributes, new TypeReference<SdpaProductEducationExtraDto>() {
        });
        sdpaProductEducationDto.setIosUpUlinkUrl(sdpaProductEducationExtraDto.getIosUpUlinkUrl());
        sdpaProductEducationDto.setBrand(sdpaProductEducationExtraDto.getBrand());
        sdpaProductEducationDto.setSpuCode(sdpaProductEducationExtraDto.getSpuCode());
        sdpaProductEducationDto.setRemark(sdpaProductEducationExtraDto.getRemark());
        sdpaProductEducationDto.setArea(sdpaProductEducationExtraDto.getArea());
        sdpaProductEducationDto.setAge(sdpaProductEducationExtraDto.getAge());
        sdpaProductEducationDto.setTeachChannel(Objects.isNull(sdpaProductEducationExtraDto.getTeachChannel()) ? "" : sdpaProductEducationExtraDto.getTeachChannel());
        sdpaProductEducationDto.setTeachType(Objects.isNull(sdpaProductEducationExtraDto.getTeachType()) ? "" : sdpaProductEducationExtraDto.getTeachType());
        sdpaProductEducationDto.setTrialClass(Objects.isNull(sdpaProductEducationExtraDto.getTrialClass()) ? "" : sdpaProductEducationExtraDto.getTrialClass());
        sdpaProductEducationDto.setBrief(sdpaProductEducationExtraDto.getBrief());
        sdpaProductEducationDto.setSkuCode(sdpaProductEducationExtraDto.getSkuCode());
        sdpaProductEducationDto.setPositivePrice(sdpaProductEducationExtraDto.getPositivePrice());
        sdpaProductEducationDto.setTrialPrice(adProductTotalInfoPo.getAdOriginalPrice());
        return sdpaProductEducationDto;

    }

    private SdpaProductLoanDto convertToSdpaProductLoanDto(AdProductTotalInfoPo adProductTotalInfoPo) {
        SdpaProductLoanDto sdpaProductLoanDto = new SdpaProductLoanDto();
        supplyAdProductCommonDto(sdpaProductLoanDto, adProductTotalInfoPo);
        String adAttributes = adProductTotalInfoPo.getAdAttributes();
        SdpaProductLoanExtraDto sdpaProductLoanExtraDto = JSON.parseObject(adAttributes, new TypeReference<SdpaProductLoanExtraDto>() {
        });
        sdpaProductLoanDto.setMaxYearRate(sdpaProductLoanExtraDto.getMaxYearRate());
        sdpaProductLoanDto.setSpuCode(sdpaProductLoanExtraDto.getSpuCode());
        sdpaProductLoanDto.setMaxCreditLimit(sdpaProductLoanExtraDto.getMaxCreditLimit());
        sdpaProductLoanDto.setAverageCreditLimit(sdpaProductLoanExtraDto.getAverageCreditLimit());
        sdpaProductLoanDto.setLoanTerm(sdpaProductLoanExtraDto.getLoanTerm());
        sdpaProductLoanDto.setLoanUsage(Objects.isNull(sdpaProductLoanExtraDto.getLoanUsage()) ? "" : sdpaProductLoanExtraDto.getLoanUsage());
        sdpaProductLoanDto.setDailyInterest(sdpaProductLoanExtraDto.getDailyInterest());
        sdpaProductLoanDto.setInterestType(Objects.isNull(sdpaProductLoanExtraDto.getInterestType()) ? "" : sdpaProductLoanExtraDto.getInterestType());
        sdpaProductLoanDto.setRapaymentType(Objects.isNull(sdpaProductLoanExtraDto.getRapaymentType()) ? "" : sdpaProductLoanExtraDto.getRapaymentType());
        sdpaProductLoanDto.setEarlyRepayment(Objects.isNull(sdpaProductLoanExtraDto.getEarlyRepayment()) ? "" : sdpaProductLoanExtraDto.getEarlyRepayment());
        sdpaProductLoanDto.setEarlyRepaymentPenalty(Objects.isNull(sdpaProductLoanExtraDto.getEarlyRepaymentPenalty()) ? "" : sdpaProductLoanExtraDto.getEarlyRepaymentPenalty());
        return sdpaProductLoanDto;
    }

    private SdpaProductLoanDto convertToSdpaProductLoanDto(AdProductTotalInfoCPCPo adProductTotalInfoPo) {
        SdpaProductLoanDto sdpaProductLoanDto = new SdpaProductLoanDto();
        supplyAdProductCommonDto(sdpaProductLoanDto, adProductTotalInfoPo);
        String adAttributes = adProductTotalInfoPo.getAdAttributes();
        SdpaProductLoanExtraDto sdpaProductLoanExtraDto = JSON.parseObject(adAttributes, new TypeReference<SdpaProductLoanExtraDto>() {
        });
        sdpaProductLoanDto.setMaxYearRate(sdpaProductLoanExtraDto.getMaxYearRate());
        sdpaProductLoanDto.setSpuCode(sdpaProductLoanExtraDto.getSpuCode());
        sdpaProductLoanDto.setMaxCreditLimit(sdpaProductLoanExtraDto.getMaxCreditLimit());
        sdpaProductLoanDto.setAverageCreditLimit(sdpaProductLoanExtraDto.getAverageCreditLimit());
        sdpaProductLoanDto.setLoanTerm(sdpaProductLoanExtraDto.getLoanTerm());
        sdpaProductLoanDto.setLoanUsage(Objects.isNull(sdpaProductLoanExtraDto.getLoanUsage()) ? "" : sdpaProductLoanExtraDto.getLoanUsage());
        sdpaProductLoanDto.setDailyInterest(sdpaProductLoanExtraDto.getDailyInterest());
        sdpaProductLoanDto.setInterestType(Objects.isNull(sdpaProductLoanExtraDto.getInterestType()) ? "" : sdpaProductLoanExtraDto.getInterestType());
        sdpaProductLoanDto.setRapaymentType(Objects.isNull(sdpaProductLoanExtraDto.getRapaymentType()) ? "" : sdpaProductLoanExtraDto.getRapaymentType());
        sdpaProductLoanDto.setEarlyRepayment(Objects.isNull(sdpaProductLoanExtraDto.getEarlyRepayment()) ? "" : sdpaProductLoanExtraDto.getEarlyRepayment());
        sdpaProductLoanDto.setEarlyRepaymentPenalty(Objects.isNull(sdpaProductLoanExtraDto.getEarlyRepaymentPenalty()) ? "" : sdpaProductLoanExtraDto.getEarlyRepaymentPenalty());
        return sdpaProductLoanDto;
    }

    private SdpaProductNovelDto convertToSdpaProductNovelDto(AdProductTotalInfoPo adProductTotalInfoPo) {
        SdpaProductNovelDto sdpaProductNovelDto = new SdpaProductNovelDto();
        supplyAdProductCommonDto(sdpaProductNovelDto, adProductTotalInfoPo);
        String adAttributes = adProductTotalInfoPo.getAdAttributes();
        SdpaProductNovelExtraDto sdpaProductNovelExtraDto = JSON.parseObject(adAttributes, new TypeReference<SdpaProductNovelExtraDto>() {
        });
        sdpaProductNovelDto.setChannel(sdpaProductNovelExtraDto.getChannel());
        sdpaProductNovelDto.setBrief(sdpaProductNovelExtraDto.getBrief());
        sdpaProductNovelDto.setTheme(sdpaProductNovelExtraDto.getTheme());
        sdpaProductNovelDto.setChapterNum(sdpaProductNovelExtraDto.getChapterNum());
        sdpaProductNovelDto.setWordsNum(sdpaProductNovelExtraDto.getWordsNum());
        sdpaProductNovelDto.setUpdateStatus(sdpaProductNovelExtraDto.getUpdateStatus());
        sdpaProductNovelDto.setRealizationMethod(sdpaProductNovelExtraDto.getRealizationMethod());
        sdpaProductNovelDto.setFirstPayChapter(sdpaProductNovelExtraDto.getFirstPayChapter());
        sdpaProductNovelDto.setAuthor(sdpaProductNovelExtraDto.getAuthor());
        sdpaProductNovelDto.setChapterPrice(adProductTotalInfoPo.getAdOriginalPrice());
        return sdpaProductNovelDto;
    }

    private SdpaProductNovelDto convertToSdpaProductNovelDto(AdProductTotalInfoCPCPo adProductTotalInfoPo) {
        SdpaProductNovelDto sdpaProductNovelDto = new SdpaProductNovelDto();
        supplyAdProductCommonDto(sdpaProductNovelDto, adProductTotalInfoPo);
        String adAttributes = adProductTotalInfoPo.getAdAttributes();
        SdpaProductNovelExtraDto sdpaProductNovelExtraDto = JSON.parseObject(adAttributes, new TypeReference<SdpaProductNovelExtraDto>() {
        });
        sdpaProductNovelDto.setChannel(sdpaProductNovelExtraDto.getChannel());
        sdpaProductNovelDto.setBrief(sdpaProductNovelExtraDto.getBrief());
        sdpaProductNovelDto.setTheme(sdpaProductNovelExtraDto.getTheme());
        sdpaProductNovelDto.setChapterNum(sdpaProductNovelExtraDto.getChapterNum());
        sdpaProductNovelDto.setWordsNum(sdpaProductNovelExtraDto.getWordsNum());
        sdpaProductNovelDto.setUpdateStatus(sdpaProductNovelExtraDto.getUpdateStatus());
        sdpaProductNovelDto.setRealizationMethod(sdpaProductNovelExtraDto.getRealizationMethod());
        sdpaProductNovelDto.setFirstPayChapter(sdpaProductNovelExtraDto.getFirstPayChapter());
        sdpaProductNovelDto.setAuthor(sdpaProductNovelExtraDto.getAuthor());
        sdpaProductNovelDto.setChapterPrice(adProductTotalInfoPo.getAdOriginalPrice());
        return sdpaProductNovelDto;
    }

    private SdpaShortFilmDto convertToSdpaProductShortFilmDto(AdProductTotalInfoPo adProductTotalInfoPo) {
        SdpaShortFilmDto sdpaShortFilmDto = new SdpaShortFilmDto();
        supplyAdProductCommonDto(sdpaShortFilmDto, adProductTotalInfoPo);
        String adAttributes = adProductTotalInfoPo.getAdAttributes();
        SdpaProductShortFilmExtraDto sdpaShortFilmExtraDto = JSON.parseObject(adAttributes, new TypeReference<SdpaProductShortFilmExtraDto>() {
        });
        sdpaShortFilmDto.setChannel(sdpaShortFilmExtraDto.getChannel());
        sdpaShortFilmDto.setBrief(sdpaShortFilmExtraDto.getBrief());
        sdpaShortFilmDto.setTheme(sdpaShortFilmExtraDto.getTheme());
        sdpaShortFilmDto.setEpisodeNum(sdpaShortFilmExtraDto.getEpisodeNum());
        sdpaShortFilmDto.setEpisodeDuration(sdpaShortFilmExtraDto.getEpisodeDuration());
        sdpaShortFilmDto.setUpdateStatus(sdpaShortFilmExtraDto.getUpdateStatus());
        sdpaShortFilmDto.setIsNew(sdpaShortFilmExtraDto.getIsNew());
        sdpaShortFilmDto.setLink(sdpaShortFilmExtraDto.getLink());
        sdpaShortFilmDto.setLinkArray(org.apache.commons.lang3.StringUtils.isNotBlank(sdpaShortFilmExtraDto.getLinkArray()) ? Arrays.stream(sdpaShortFilmExtraDto.getLinkArray().split(",")).map(Integer::parseInt).collect(Collectors.toList()) : new ArrayList<>());
        sdpaShortFilmDto.setLeaderActor(sdpaShortFilmExtraDto.getLeaderActor());
        sdpaShortFilmDto.setDirector(sdpaShortFilmExtraDto.getDirector());
        sdpaShortFilmDto.setFirstPayEpisode(sdpaShortFilmExtraDto.getFirstPayEpisode());
        sdpaShortFilmDto.setMinTopUp(sdpaShortFilmExtraDto.getMinTopUp());
        sdpaShortFilmDto.setMaxTopUp(sdpaShortFilmExtraDto.getMaxTopUp());
        sdpaShortFilmDto.setMicroLandingPageUrl(sdpaShortFilmExtraDto.getMicroLandingPageUrl());
        sdpaShortFilmDto.setIsMotivationContent(sdpaShortFilmExtraDto.getIsMotivationContent());
        sdpaShortFilmDto.setEpisodePrice(org.apache.commons.lang3.StringUtils.isNotBlank(adProductTotalInfoPo.getAdOriginalPrice()) ? Integer.parseInt(adProductTotalInfoPo.getAdOriginalPrice()) : 0);
        sdpaShortFilmDto.setOnlineTime(sdpaShortFilmExtraDto.getOnlineTime());
        return sdpaShortFilmDto;
    }

    private SdpaShortFilmDto convertToSdpaProductShortFilmDto(AdProductTotalInfoCPCPo adProductTotalInfoPo) {
        SdpaShortFilmDto sdpaShortFilmDto = new SdpaShortFilmDto();
        supplyAdProductCommonDto(sdpaShortFilmDto, adProductTotalInfoPo);
        String adAttributes = adProductTotalInfoPo.getAdAttributes();
        SdpaProductShortFilmExtraDto sdpaShortFilmExtraDto = JSON.parseObject(adAttributes, new TypeReference<SdpaProductShortFilmExtraDto>() {
        });
        sdpaShortFilmDto.setChannel(sdpaShortFilmExtraDto.getChannel());
        sdpaShortFilmDto.setBrief(sdpaShortFilmExtraDto.getBrief());
        sdpaShortFilmDto.setTheme(sdpaShortFilmExtraDto.getTheme());
        sdpaShortFilmDto.setEpisodeNum(sdpaShortFilmExtraDto.getEpisodeNum());
        sdpaShortFilmDto.setEpisodeDuration(sdpaShortFilmExtraDto.getEpisodeDuration());
        sdpaShortFilmDto.setUpdateStatus(sdpaShortFilmExtraDto.getUpdateStatus());
        sdpaShortFilmDto.setIsNew(sdpaShortFilmExtraDto.getIsNew());
        sdpaShortFilmDto.setLink(sdpaShortFilmExtraDto.getLink());
        sdpaShortFilmDto.setLinkArray(org.apache.commons.lang3.StringUtils.isNotBlank(sdpaShortFilmExtraDto.getLinkArray()) ? Arrays.stream(sdpaShortFilmExtraDto.getLinkArray().split(",")).map(Integer::parseInt).collect(Collectors.toList()) : new ArrayList<>());
        sdpaShortFilmDto.setLeaderActor(sdpaShortFilmExtraDto.getLeaderActor());
        sdpaShortFilmDto.setDirector(sdpaShortFilmExtraDto.getDirector());
        sdpaShortFilmDto.setFirstPayEpisode(sdpaShortFilmExtraDto.getFirstPayEpisode());
        sdpaShortFilmDto.setMinTopUp(sdpaShortFilmExtraDto.getMinTopUp());
        sdpaShortFilmDto.setMaxTopUp(sdpaShortFilmExtraDto.getMaxTopUp());
        sdpaShortFilmDto.setMicroLandingPageUrl(sdpaShortFilmExtraDto.getMicroLandingPageUrl());
        sdpaShortFilmDto.setIsMotivationContent(sdpaShortFilmExtraDto.getIsMotivationContent());
        sdpaShortFilmDto.setEpisodePrice(org.apache.commons.lang3.StringUtils.isNotBlank(adProductTotalInfoPo.getAdOriginalPrice()) ? Integer.parseInt(adProductTotalInfoPo.getAdOriginalPrice()) : 0);
        sdpaShortFilmDto.setOnlineTime(sdpaShortFilmExtraDto.getOnlineTime());
        return sdpaShortFilmDto;
    }

    private SdpaProductCarDto convertToSdpaProductCarDto(AdProductTotalInfoPo adProductTotalInfoPo) {
        SdpaProductCarDto sdpaProductCarDto = new SdpaProductCarDto();
        supplyAdProductCommonDto(sdpaProductCarDto, adProductTotalInfoPo);
        String adAttributes = adProductTotalInfoPo.getAdAttributes();
        SdpaProductCarExtraDto sdpaProductCarExtraDto = JSON.parseObject(adAttributes, new TypeReference<SdpaProductCarExtraDto>() {
        });
        sdpaProductCarDto.setBrand(sdpaProductCarExtraDto.getBrand());
        sdpaProductCarDto.setModel(sdpaProductCarExtraDto.getModel());
        sdpaProductCarDto.setSpuCode(sdpaProductCarExtraDto.getSpuCode());
        return sdpaProductCarDto;
    }

    private SdpaProductCarDto convertToSdpaProductCarDto(AdProductTotalInfoCPCPo adProductTotalInfoPo) {
        SdpaProductCarDto sdpaProductCarDto = new SdpaProductCarDto();
        supplyAdProductCommonDto(sdpaProductCarDto, adProductTotalInfoPo);
        String adAttributes = adProductTotalInfoPo.getAdAttributes();
        SdpaProductCarExtraDto sdpaProductCarExtraDto = JSON.parseObject(adAttributes, new TypeReference<SdpaProductCarExtraDto>() {
        });
        sdpaProductCarDto.setBrand(sdpaProductCarExtraDto.getBrand());
        sdpaProductCarDto.setModel(sdpaProductCarExtraDto.getModel());
        sdpaProductCarDto.setSpuCode(sdpaProductCarExtraDto.getSpuCode());
        return sdpaProductCarDto;
    }

    private SdpaProductGoodsDarkDto convertToSdpaProductGoodsDarkDto(AdProductTotalInfoCPCPo adProductTotalInfoPo) {
        SdpaProductGoodsDarkDto sdpaProductGoodsDarkDto = new SdpaProductGoodsDarkDto();
        supplyAdProductCommonDto(sdpaProductGoodsDarkDto, adProductTotalInfoPo);
        String adAttributes = adProductTotalInfoPo.getAdAttributes();
        sdpaProductGoodsDarkDto.setAdAttributes(adAttributes);
        sdpaProductGoodsDarkDto.setAdOriginalPrice(adProductTotalInfoPo.getAdOriginalPrice());

        return sdpaProductGoodsDarkDto;
    }

    private SdpaProductECommerceDto convertToSdpaProductECommerceDto(AdProductTotalInfoPo adProductTotalInfoPo) {
        SdpaProductECommerceDto sdpaProductECommerceDto = new SdpaProductECommerceDto();
        supplyAdProductCommonDto(sdpaProductECommerceDto, adProductTotalInfoPo);
        String adAttributes = adProductTotalInfoPo.getAdAttributes();
        SdpaProductECommerceExtraDto sdpaProductECommerceExtraDto = JSON.parseObject(adAttributes, new TypeReference<SdpaProductECommerceExtraDto>() {
        });
        sdpaProductECommerceDto.setSpuCode(sdpaProductECommerceExtraDto.getSpuCode());
        sdpaProductECommerceDto.setSpuName(sdpaProductECommerceExtraDto.getSpuName());
        sdpaProductECommerceDto.setPlatformName(sdpaProductECommerceExtraDto.getPlatformName());
        sdpaProductECommerceDto.setRemark(sdpaProductECommerceExtraDto.getRemark());
        sdpaProductECommerceDto.setStoreName(sdpaProductECommerceExtraDto.getStoreName());
        sdpaProductECommerceDto.setAge(sdpaProductECommerceExtraDto.getAge());
        sdpaProductECommerceDto.setOriginalPrice(sdpaProductECommerceExtraDto.getOriginalPrice());
        sdpaProductECommerceDto.setDiscount(sdpaProductECommerceExtraDto.getDiscount());
        sdpaProductECommerceDto.setAdOriginalPrice(adProductTotalInfoPo.getAdOriginalPrice());
        sdpaProductECommerceDto.setIosUpUlinkUrl(sdpaProductECommerceExtraDto.getIosUpUlinkUrl());
        sdpaProductECommerceDto.setVideoLink(sdpaProductECommerceExtraDto.getVideoLink());
        return sdpaProductECommerceDto;
    }

    private <T extends SdpaProductCommonDto> void supplyAdProductCommonDto(T adProductCommentDto, AdProductTotalInfoPo adProductTotalInfoPo) {
        adProductCommentDto.setProductId(adProductTotalInfoPo.getAdProductId());
        adProductCommentDto.setFirstCategoryId(adProductTotalInfoPo.getFirstCategoryId());
        adProductCommentDto.setSecondCategoryId(adProductTotalInfoPo.getSecondCategoryId());
        adProductCommentDto.setThirdCategoryId(adProductTotalInfoPo.getThirdCategoryId());
        adProductCommentDto.setFirstCategoryName(adProductTotalInfoPo.getFirstCategoryName());
        adProductCommentDto.setSecondCategoryName(adProductTotalInfoPo.getSecondCategoryName());
        adProductCommentDto.setThirdCategoryName(adProductTotalInfoPo.getThirdCategoryName());
        adProductCommentDto.setAdProductName(adProductTotalInfoPo.getAdProductName());
        adProductCommentDto.setAccountId(adProductTotalInfoPo.getAccountId());
        adProductCommentDto.setLibraryId(adProductTotalInfoPo.getLibraryId());
        adProductCommentDto.setAdMainImgUrl(adProductTotalInfoPo.getAdMainImgUrl());
        adProductCommentDto.setAdSubImgUrl(adProductTotalInfoPo.getAdExtraImgUrl());
        adProductCommentDto.setH5LandingPageUrl(adProductTotalInfoPo.getH5LandingPageUrl());
        adProductCommentDto.setPcLandingPageUrl(adProductTotalInfoPo.getPcLandingPageUrl());
        adProductCommentDto.setIosLandingPageUrl(adProductTotalInfoPo.getIosLandingPageUrl());
        adProductCommentDto.setAndroidLandingPageUrl(adProductTotalInfoPo.getAndroidLandingPageUrl());

    }

    private <T extends SdpaProductCommonDto> void supplyAdProductCommonDto(T adProductCommonDto, AdProductTotalInfoCPCPo adProductTotalInfoPo) {
        adProductCommonDto.setProductId(adProductTotalInfoPo.getAdProductId());
        adProductCommonDto.setFirstCategoryId(adProductTotalInfoPo.getFirstCategoryId());
        adProductCommonDto.setSecondCategoryId(adProductTotalInfoPo.getSecondCategoryId());
        adProductCommonDto.setThirdCategoryId(adProductTotalInfoPo.getThirdCategoryId());
        adProductCommonDto.setFirstCategoryName(adProductTotalInfoPo.getFirstCategoryName());
        adProductCommonDto.setSecondCategoryName(adProductTotalInfoPo.getSecondCategoryName());
        adProductCommonDto.setThirdCategoryName(adProductTotalInfoPo.getThirdCategoryName());
        adProductCommonDto.setAdProductName(adProductTotalInfoPo.getAdProductName());
        adProductCommonDto.setAccountId(adProductTotalInfoPo.getAccountId());
        adProductCommonDto.setLibraryId(adProductTotalInfoPo.getLibraryId());
        adProductCommonDto.setAdMainImgUrl(adProductTotalInfoPo.getAdMainImgUrl());
        adProductCommonDto.setAdSubImgUrl(adProductTotalInfoPo.getAdExtraImgUrl());
        adProductCommonDto.setH5LandingPageUrl(adProductTotalInfoPo.getH5LandingPageUrl());
        adProductCommonDto.setPcLandingPageUrl(adProductTotalInfoPo.getPcLandingPageUrl());
        adProductCommonDto.setIosLandingPageUrl(adProductTotalInfoPo.getIosLandingPageUrl());
        adProductCommonDto.setAndroidLandingPageUrl(adProductTotalInfoPo.getAndroidLandingPageUrl());
        adProductCommonDto.setUniqueKey(adProductTotalInfoPo.getUniqueKey());
    }

    private <T extends SdpaProductCommonDto> Map<String, Long> supplyAdProductIdWithNameIdMap(List<T> adProductCommonDtoList) {
        int productCount = adProductCommonDtoList.size();
        List<Long> idList = batchGenProductId(productCount);
        Map<String, Long> adProductNameIdMap = new HashMap<>();

        for (int i = 0; i < adProductCommonDtoList.size(); i++) {
            T adProductCommonDto = adProductCommonDtoList.get(i);
            adProductCommonDto.setProductId(idList.get(i));
            adProductNameIdMap.put(adProductCommonDto.getAdProductName(), idList.get(i));
        }
        return adProductNameIdMap;
    }

    private <T extends SdpaProductCommonDto> Map<String, Long> supplyAdProductId(List<T> adProductCommonDtoList) {
        int productCount = adProductCommonDtoList.size();
        List<Long> idList = batchGenProductId(productCount);
        Map<String, Long> uniqueAdProductIdMap = new HashMap<>();

        for (int i = 0; i < adProductCommonDtoList.size(); i++) {
            T adProductCommonDto = adProductCommonDtoList.get(i);
            adProductCommonDto.setProductId(idList.get(i));
            uniqueAdProductIdMap.put(adProductCommonDto.getUniqueKey(), idList.get(i));
        }
        return uniqueAdProductIdMap;
    }

    private Map<String, Long> supplyGoodsAdProductId(List<SdpaProductGoodsDarkDto> sdpaProductGoodsDarkDtoList) {
        Map<String, Long> uniqueAdProductIdMap = new HashMap<>();

        for (SdpaProductGoodsDarkDto sdpaProductGoodsDarkDto : sdpaProductGoodsDarkDtoList) {
            if (Objects.isNull(sdpaProductGoodsDarkDto.getProductId())) {
                String uniqueKey = sdpaProductGoodsDarkDto.getUniqueKey();
                String productId = uniqueKey.substring(GOODS_PREFIX.length());
                sdpaProductGoodsDarkDto.setProductId(Long.parseLong(productId));
            }
            uniqueAdProductIdMap.put(sdpaProductGoodsDarkDto.getUniqueKey(), sdpaProductGoodsDarkDto.getProductId());
        }
        return uniqueAdProductIdMap;
    }


    private List<AdProductTotalInfoCPCPo> queryExistDarkCpcPo(AdProductDto adProductDto) {
        List<String> uniqueKeyList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(adProductDto.getEducationDtos())) {
            uniqueKeyList.addAll(adProductDto.getEducationDtos().stream().map(SdpaProductEducationDto::getUniqueKey).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(adProductDto.getLoanDtos())) {
            uniqueKeyList.addAll(adProductDto.getLoanDtos().stream().map(SdpaProductLoanDto::getUniqueKey).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(adProductDto.getNovelDtos())) {
            uniqueKeyList.addAll(adProductDto.getNovelDtos().stream().map(SdpaProductNovelDto::getUniqueKey).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(adProductDto.getShortFilmDtos())) {
            uniqueKeyList.addAll(adProductDto.getShortFilmDtos().stream().map(SdpaShortFilmDto::getUniqueKey).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(adProductDto.getCarDtos())) {
            uniqueKeyList.addAll(adProductDto.getCarDtos().stream().map(SdpaProductCarDto::getUniqueKey).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(adProductDto.getGoodsDtos())) {
            uniqueKeyList.addAll(adProductDto.getGoodsDtos().stream().map(SdpaProductGoodsDarkDto::getUniqueKey).collect(Collectors.toList()));
        }
        List<AdProductTotalInfoCPCPo> existAdProduct = adProductRepo.queryAdProductTotalInfoCPCPoList(uniqueKeyList);
        return existAdProduct;
    }


    @Override
    public void createMiniGameProduct(LauMiniGamePo lauMiniGamePo) {
        try {
            log.info("createMiniGameProduct lauMiniGamePo={}", JSON.toJSONString(lauMiniGamePo));
            Integer accountId = lauMiniGamePo.getAccountId();
            String originId = lauMiniGamePo.getOriginId();
            AccAccountPo accAccountPo = launchAccountV1Service.get(accountId);

            Integer unitedFirstIndustryId = accAccountPo.getUnitedFirstIndustryId();
            if (!Objects.equals(unitedFirstIndustryId, gameUnitedFirstIndustryId)) {
                log.info("createMiniGameProduct unitedFirstIndustryId is not game, unitedFirstIndustryId={} lauMiniGamePo={}", unitedFirstIndustryId, JSON.toJSONString(lauMiniGamePo));
                return;
            }

            AccProductPo accProductPo = newAccAccountRepo.queryAccountProductById(accAccountPo.getProductId());

            String adProductName = accProductPo.getName();
            String miniGameUniqueKey = GAME_UNIQUE_PREFIX + originId + SdpaGameProductTypeEnum.MINI_GAME.getDesc();

            List<AdProductTotalInfoCPCPo> adProductTotalInfoCPCPoList = adProductRepo.queryAdProductTotalInfoCPCPoList(Collections.singletonList(miniGameUniqueKey));

            if (CollectionUtils.isNotEmpty(adProductTotalInfoCPCPoList)) {
                log.info("createMiniGameProduct miniGameUniqueKey is exist, miniGameUniqueKey={}", miniGameUniqueKey);
                return;
            }

            SdpaProductGameDto sdpaProductGameDto = new SdpaProductGameDto();

            MapValue mapValue = getGameExtraInfoFromHive(adProductName);
            if (Objects.nonNull(mapValue)) {
                String thirdCategoryName = mapValue.getValueMap().get("only_tag");
                if (StringUtils.isNotBlank(thirdCategoryName)) {
                    SdpaGameProductCategoryEnum sdpaGameProductCategoryEnum = SdpaGameProductCategoryEnum.getByThirdCategoryName(sdpaGameFirstCategoryId, sdpaMiniGameSecondCategoryId, thirdCategoryName);
                    if (Objects.nonNull(sdpaGameProductCategoryEnum)) {
                        sdpaProductGameDto.setThirdCategoryId(sdpaGameProductCategoryEnum.getThirdCategoryCode());
                    }
                    sdpaProductGameDto.setThirdCategoryName(mapValue.getValueOrDefault("only_tag", ""));
                }
                supplyGameExtraInfo(mapValue, sdpaProductGameDto);
            }
            List<Long> adProductIdList = batchGenProductId(1);

            sdpaProductGameDto.setProductId(adProductIdList.get(0));
            sdpaProductGameDto.setFirstCategoryName("游戏");
            sdpaProductGameDto.setSecondCategoryName("微信小游戏");
            sdpaProductGameDto.setFirstCategoryId(sdpaGameFirstCategoryId);
            sdpaProductGameDto.setSecondCategoryId(sdpaMiniGameSecondCategoryId);
            sdpaProductGameDto.setAdProductName(adProductName);
            sdpaProductGameDto.setUniqueKey(miniGameUniqueKey);
            sdpaProductGameDto.setAccountId(accountId);

            adProductRepo.insertGameDarkV2(Collections.singletonList(sdpaProductGameDto));
        } catch (Exception e) {
            log.error("createMiniGameProduct error, lauMiniGamePo={}", JSON.toJSONString(lauMiniGamePo), e);

        }
    }

    public void createOtherGameProduct(SdpaGameProductDatabusBo sdpaGameProductDatabusBo) {
        try {
            log.info("createOtherGameProduct sdpaGameProductDatabusBo={}", JSON.toJSONString(sdpaGameProductDatabusBo));
            String gameType = sdpaGameProductDatabusBo.getGameType();
            String uniqueKey = GAME_UNIQUE_PREFIX + sdpaGameProductDatabusBo.getGameBaseId() + "_" + gameType;

            List<AdProductTotalInfoCPCPo> adProductTotalInfoCPCPoList = adProductRepo.queryAdProductTotalInfoCPCPoList(Collections.singletonList(uniqueKey));

            if (CollectionUtils.isNotEmpty(adProductTotalInfoCPCPoList)) {
                log.info("createOtherGameProduct miniGameUniqueKey is exist, miniGameUniqueKey={}", uniqueKey);
                return;
            }

            SdpaGameProductCategoryEnum secondCategoryEnum = SdpaGameProductCategoryEnum.getBySecondCategoryName(sdpaGameFirstCategoryId, gameType);
            if (Objects.isNull(secondCategoryEnum)) {
                log.error("createOtherGameProduct secondCategoryName is not exist, secondCategoryName={}", gameType);
                return;
            }


            String adProductName = sdpaGameProductDatabusBo.getGameCnName();
            SdpaProductGameDto sdpaProductGameDto = new SdpaProductGameDto();
            MapValue mapValue = getGameExtraInfoFromHive(adProductName);

            if (Objects.nonNull(mapValue)) {
                String thirdCategoryName = mapValue.getValueMap().get("only_tag");
                if (StringUtils.isNotBlank(thirdCategoryName)) {
                    SdpaGameProductCategoryEnum sdpaGameProductCategoryEnum = SdpaGameProductCategoryEnum.getByThirdCategoryName(sdpaGameFirstCategoryId, secondCategoryEnum.getSecondCategoryCode(), thirdCategoryName);
                    if (Objects.nonNull(sdpaGameProductCategoryEnum)) {
                        sdpaProductGameDto.setThirdCategoryId(sdpaGameProductCategoryEnum.getThirdCategoryCode());
                    }
                    sdpaProductGameDto.setThirdCategoryName(mapValue.getValueOrDefault("only_tag", ""));
                }
                supplyGameExtraInfo(mapValue, sdpaProductGameDto);
            }
            List<Long> adProductIdList = batchGenProductId(1);

            sdpaProductGameDto.setProductId(adProductIdList.get(0));
            sdpaProductGameDto.setFirstCategoryName("游戏");
            sdpaProductGameDto.setSecondCategoryName(gameType);
            sdpaProductGameDto.setFirstCategoryId(sdpaGameFirstCategoryId);
            sdpaProductGameDto.setSecondCategoryId(secondCategoryEnum.getSecondCategoryCode());
            sdpaProductGameDto.setAdProductName(adProductName);
            sdpaProductGameDto.setUniqueKey(uniqueKey);


            adProductRepo.insertGameDarkV2(Collections.singletonList(sdpaProductGameDto));
        } catch (Exception e) {
            log.info("createOtherGameProduct error, sdpaGameProductDatabusBo={}", JSON.toJSONString(sdpaGameProductDatabusBo), e);
        }

    }

    private MapValue getGameExtraInfoFromHive(String adProductName) {
        OsHeader osHeader = OsHeader.newBuilder()
                .setAppKey("254184f13a6b17d99163b58829af4af7")
                .setSecret("xcshZ4A0Wjrtu/kcd7bWCj8u9qLKyNvndqVp8HAKEu8=")
                .setApiId("api_3236")
                .build();

        QueryReq openApiReq = QueryReq.newBuilder()
                .setOsHeader(osHeader)
                .addReqs(OperatorVo.newBuilder().setField("game_name_alias").setOperator("=").addValues(adProductName))
                .setPageReq(PageReq.newBuilder().setPage(1).setPageSize(1))
                .build();

        QueryResp queryResp = oneServiceOpenApiCustomerBlockingStub.query(openApiReq);

        List<MapValue> rowsList = queryResp.getRowsList();
        if (CollectionUtils.isEmpty(rowsList)) {
            log.error("queryResp is empty, adProductName={}", adProductName);
            return null;
        }
        MapValue mapValue = rowsList.get(0);
        return mapValue;
    }

    private Map<String, MapValue> getCarExtraInfoFromHive(List<String> modelList) {
        OsHeader osHeader = OsHeader.newBuilder()
                .setAppKey("254184f13a6b17d99163b58829af4af7")
                .setSecret("xcshZ4A0Wjrtu/kcd7bWCj8u9qLKyNvndqVp8HAKEu8=")
                .setApiId("api_3573")
                .build();

        LocalDate yesterday = LocalDate.now().minusDays(1);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        String yesterdayTime = yesterday.format(formatter);

        QueryReq openApiReq = QueryReq.newBuilder()
                .setOsHeader(osHeader)
                .addReqs(OperatorVo.newBuilder().setField("product_name").setOperator("in").addAllValues(modelList))
                .addReqs(OperatorVo.newBuilder().setField("is_deleted").setOperator("=").addValues("0"))
                .addReqs(OperatorVo.newBuilder().setField("spu_id").setOperator("!=").addValues("0"))
                .addReqs(OperatorVo.newBuilder().setField("data_status").setOperator(">").addValues("0").build())
                .addReqs(OperatorVo.newBuilder().setField("log_date").setOperator("=").addValues(yesterdayTime).build())
                .build();

        QueryResp queryResp = oneServiceOpenApiCustomerBlockingStub.query(openApiReq);

        List<MapValue> rowsList = queryResp.getRowsList();

        Map<String, MapValue> productNameValueMap = rowsList.stream().collect(Collectors.toMap(mapValue -> mapValue.getValueOrDefault("product_name", ""), mapValue -> mapValue));

        return productNameValueMap;
    }

    @Override
    public void createMiniGameMapping(LauCreativeMiniGameMappingDo lauCreativeMiniGameMappingDo) {
        if (lauCreativeMiniGameMappingDo.getMiniGameId() == 0) {
            return;
        }

        try {
            log.info("createMiniGameMapping insert, lauCreativeMiniGameMappingDo={}", JSON.toJSONString(lauCreativeMiniGameMappingDo));

            List<LauUnitCreativePo> lauUnitCreativePos = lauUnitCreativeRepo.queryCreativesByCreativeIds(Collections.singletonList(lauCreativeMiniGameMappingDo.getCreativeId()));
            if (CollectionUtils.isEmpty(lauUnitCreativePos)) {
                log.error("createMiniGameMapping insert, lauUnitCreativePos is empty, lauCreativeMiniGameMappingDo={}", JSON.toJSONString(lauCreativeMiniGameMappingDo));
                return;
            }
            LauUnitCreativePo lauUnitCreativePo = lauUnitCreativePos.get(0);
            Integer unitId = lauUnitCreativePo.getUnitId();

            LauMiniGameDto lauMiniGame = lauMiniGameService.getLauMiniGameById(lauCreativeMiniGameMappingDo.getMiniGameId());
            if (Objects.isNull(lauMiniGame)) {
                log.error("createMiniGameMapping insert, lauMiniGameById is null, lauCreativeMiniGameMappingDo={}", JSON.toJSONString(lauCreativeMiniGameMappingDo));
                return;
            }

            String originId = lauMiniGame.getOriginId();

            String uniqueKey = GAME_UNIQUE_PREFIX + originId + SdpaGameProductTypeEnum.MINI_GAME.getDesc();

            List<AdProductTotalInfoCPCPo> adProductTotalInfoCPCPoList = adProductRepo.queryAdProductTotalInfoCPCPoList(Collections.singletonList(uniqueKey));
            if (CollectionUtils.isEmpty(adProductTotalInfoCPCPoList)) {
                log.error("createMiniGameMapping insert, adProductTotalInfoCPCPoList is empty, lauCreativeMiniGameMappingDo={}, uniqueKey={}", JSON.toJSONString(lauCreativeMiniGameMappingDo), uniqueKey);
                return;
            }

            AdProductTotalInfoCPCPo adProductTotalInfoCPCPo = adProductTotalInfoCPCPoList.get(0);
            Long adProductId = adProductTotalInfoCPCPo.getAdProductId();


            //明投

            //因为可能同时来多个creativeId 需要加个unitId级别的锁
            RLock lock = redissonClient.getLock(SDPA_MINI_GAME_PREFIX + "_" + unitId);
            boolean tryLock = lock.tryLock();

            if (BooleanUtils.isNotTrue(tryLock)) {
                log.error("createMiniGameMapping insert, lock is not tryLock, lauCreativeMiniGameMappingDo={}", JSON.toJSONString(lauCreativeMiniGameMappingDo));
                return;
            }

            try {
                List<AdProductMappingCPCPo> adProductMappingPoList = adProductRepo.queryAdProductMappingCpcPoList(unitId, AdProductBindTypeEnum.UNIT.getCode(), AdProductBelongTypeEnum.USER_TYPE_IN.getCode());
                if (CollectionUtils.isNotEmpty(adProductMappingPoList)) {
                    log.error("createMiniGameMapping insert, adProductMappingPoList is not empty, adProductMappingPoList={}", JSON.toJSONString(adProductMappingPoList));
                    return;
                }

                AdProductMappingCPCPo adProductMappingCPCPo = new AdProductMappingCPCPo();
                adProductMappingCPCPo.setAdProductId(adProductId);
                adProductMappingCPCPo.setMappingId(unitId);
                adProductMappingCPCPo.setType(AdProductBindTypeEnum.UNIT.getCode());
                adProductMappingCPCPo.setBelongType(AdProductBelongTypeEnum.USER_TYPE_IN.getCode());

                adProductRepo.insertAdProductMappingCPCPo(adProductMappingCPCPo);
            } catch (Exception e) {
                throw e;
            } finally {
                if (lock.isLocked()) {
                    lock.unlock();
                }
            }
        } catch (Exception e) {
            log.error("createMiniGameMapping error, lauCreativeMiniGameMappingDo={}", JSON.toJSONString(lauCreativeMiniGameMappingDo), e);
        }
    }

    @Override
    public void createMiniGameMapping(LauUnitMiniGameMappingPo lauUnitMiniGameMappingPo) {
        if (lauUnitMiniGameMappingPo.getMiniGameId() == 0) {
            return;
        }

        try {
            log.info("createMiniGameMapping insert, lauUnitMiniGameMappingPo={}", JSON.toJSONString(lauUnitMiniGameMappingPo));

            Integer unitId = lauUnitMiniGameMappingPo.getUnitId();

            LauMiniGameDto lauMiniGame = lauMiniGameService.getLauMiniGameById(lauUnitMiniGameMappingPo.getMiniGameId());
            if (Objects.isNull(lauMiniGame)) {
                log.error("createMiniGameMapping insert, lauMiniGameById is null, lauUnitMiniGameMappingPo={}", JSON.toJSONString(lauUnitMiniGameMappingPo));
                return;
            }

            String originId = lauMiniGame.getOriginId();

            String uniqueKey = GAME_UNIQUE_PREFIX + originId + SdpaGameProductTypeEnum.MINI_GAME.getDesc();

            List<AdProductTotalInfoCPCPo> adProductTotalInfoCPCPoList = adProductRepo.queryAdProductTotalInfoCPCPoList(Collections.singletonList(uniqueKey));
            if (CollectionUtils.isEmpty(adProductTotalInfoCPCPoList)) {
                log.error("createMiniGameMapping insert, adProductTotalInfoCPCPoList is empty, lauUnitMiniGameMappingPo={}, uniqueKey={}", JSON.toJSONString(lauUnitMiniGameMappingPo), uniqueKey);
                return;
            }

            AdProductTotalInfoCPCPo adProductTotalInfoCPCPo = adProductTotalInfoCPCPoList.get(0);
            Long adProductId = adProductTotalInfoCPCPo.getAdProductId();

            //明投

            //因为可能同时来多个creativeId 需要加个unitId级别的锁
            RLock lock = redissonClient.getLock(SDPA_MINI_GAME_PREFIX + "_" + unitId);
            boolean tryLock = lock.tryLock();

            if (BooleanUtils.isNotTrue(tryLock)) {
                log.error("createMiniGameMapping insert, lock is not tryLock, lauUnitMiniGameMappingPo={}", JSON.toJSONString(lauUnitMiniGameMappingPo));
                return;
            }

            try {
                List<AdProductMappingCPCPo> adProductMappingPoList = adProductRepo.queryAdProductMappingCpcPoList(unitId, AdProductBindTypeEnum.UNIT.getCode(), AdProductBelongTypeEnum.USER_TYPE_IN.getCode());
                if (CollectionUtils.isNotEmpty(adProductMappingPoList)) {
                    log.error("createMiniGameMapping insert, adProductMappingPoList is not empty, adProductMappingPoList={}", JSON.toJSONString(adProductMappingPoList));
                    return;
                }

                AdProductMappingCPCPo adProductMappingCPCPo = new AdProductMappingCPCPo();
                adProductMappingCPCPo.setAdProductId(adProductId);
                adProductMappingCPCPo.setMappingId(unitId);
                adProductMappingCPCPo.setType(AdProductBindTypeEnum.UNIT.getCode());
                adProductMappingCPCPo.setBelongType(AdProductBelongTypeEnum.USER_TYPE_IN.getCode());

                adProductRepo.insertAdProductMappingCPCPo(adProductMappingCPCPo);
            } catch (Exception e) {
                throw e;
            } finally {
                if (lock.isLocked()) {
                    lock.unlock();
                }
            }
        } catch (Exception e) {
            log.error("createMiniGameMapping error, lauUnitMiniGameMappingPo={}", JSON.toJSONString(lauUnitMiniGameMappingPo), e);
        }
    }

    @Override
    public void createBiliGameMapping(LauUnitBiliMiniGamePo lauUnitBiliMiniGamePo) {

        try {
            log.info("createBiliGameMapping insert, lauUnitBiliMiniGamePo={}", JSON.toJSONString(lauUnitBiliMiniGamePo));
            Integer gameBaseId = lauUnitBiliMiniGamePo.getGameBaseId();
            String uniqueKey = GAME_UNIQUE_PREFIX + gameBaseId + SdpaGameProductTypeEnum.BILI_GAME.getDesc();
            List<AdProductTotalInfoCPCPo> adProductTotalInfoCPCPoList =
                    adProductRepo.queryAdProductTotalInfoCPCPoList(Collections.singletonList(uniqueKey));
            if (adProductTotalInfoCPCPoList.isEmpty()) {
                log.error("createBiliGameMapping adProductTotalInfoCPCPoList is empty, uniqueKey={}", uniqueKey);
                return;
            }
            AdProductTotalInfoCPCPo adProductTotalInfoCPCPo = adProductTotalInfoCPCPoList.get(0);
            Long adProductId = adProductTotalInfoCPCPo.getAdProductId();
            Integer unitId = lauUnitBiliMiniGamePo.getUnitId();

            List<AdProductMappingCPCPo> adProductMappingPoList = adProductRepo.queryAdProductMappingCpcPoList(unitId, AdProductBindTypeEnum.UNIT.getCode(), AdProductBelongTypeEnum.USER_TYPE_IN.getCode());
            if (CollectionUtils.isNotEmpty(adProductMappingPoList)) {
                log.error("createBiliGameMapping insert, adProductMappingPoList is not empty, adProductMappingPoList={}", JSON.toJSONString(adProductMappingPoList));
                return;
            }


            AdProductMappingCPCPo adProductMappingCPCPo = new AdProductMappingCPCPo();
            adProductMappingCPCPo.setAdProductId(adProductId);
            adProductMappingCPCPo.setMappingId(unitId);
            adProductMappingCPCPo.setType(AdProductBindTypeEnum.UNIT.getCode());
            adProductMappingCPCPo.setBelongType(AdProductBelongTypeEnum.USER_TYPE_IN.getCode());
            adProductRepo.insertAdProductMappingCPCPo(adProductMappingCPCPo);
        } catch (Exception e) {
            log.info("createBiliGameMapping error, lauUnitBiliMiniGamePo={}", JSON.toJSONString(lauUnitBiliMiniGamePo), e);
        }
    }

    @Override
    public void createMobileGameMapping(com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauUnitGamePo newLauUnitGamePo) {
        try {
            log.info("createMobileGameMapping insert, newLauUnitGamePo={}", JSON.toJSONString(newLauUnitGamePo));
            Integer gameBaseId = newLauUnitGamePo.getGameBaseId();
            String uniqueKey = GAME_UNIQUE_PREFIX + gameBaseId + SdpaGameProductTypeEnum.MOBILE_GAME.getDesc();
            List<AdProductTotalInfoCPCPo> adProductTotalInfoCPCPoList =
                    adProductRepo.queryAdProductTotalInfoCPCPoList(Collections.singletonList(uniqueKey));
            if (adProductTotalInfoCPCPoList.isEmpty()) {
                log.error("createMobileGameMapping adProductTotalInfoCPCPoList is empty, uniqueKey={}", uniqueKey);
                return;
            }
            AdProductTotalInfoCPCPo adProductTotalInfoCPCPo = adProductTotalInfoCPCPoList.get(0);
            Long adProductId = adProductTotalInfoCPCPo.getAdProductId();
            Integer unitId = newLauUnitGamePo.getUnitId();

            List<AdProductMappingCPCPo> adProductMappingPoList = adProductRepo.queryAdProductMappingCpcPoList(unitId, AdProductBindTypeEnum.UNIT.getCode(), AdProductBelongTypeEnum.USER_TYPE_IN.getCode());
            if (CollectionUtils.isNotEmpty(adProductMappingPoList)) {
                log.error("createMobileGameMapping insert, adProductMappingPoList is not empty, adProductMappingPoList={}", JSON.toJSONString(adProductMappingPoList));
                return;
            }

            AdProductMappingCPCPo adProductMappingCPCPo = new AdProductMappingCPCPo();
            adProductMappingCPCPo.setAdProductId(adProductId);
            adProductMappingCPCPo.setMappingId(unitId);
            adProductMappingCPCPo.setType(AdProductBindTypeEnum.UNIT.getCode());
            adProductMappingCPCPo.setBelongType(AdProductBelongTypeEnum.USER_TYPE_IN.getCode());
            adProductRepo.insertAdProductMappingCPCPo(adProductMappingCPCPo);
        } catch (Exception e) {
            log.info("createMobileGameMapping error, newLauUnitGamePo={}", JSON.toJSONString(newLauUnitGamePo), e);
        }
    }


    private void supplyGameExtraInfo(MapValue mapValue, SdpaProductGameDto sdpaProductGameDto) {
        sdpaProductGameDto.setGameName(mapValue.getValueOrDefault("game_name", ""));
        sdpaProductGameDto.setPlatform(mapValue.getValueOrDefault("platform", ""));
        sdpaProductGameDto.setPlay1(mapValue.getValueOrDefault("play1", ""));
        sdpaProductGameDto.setPlay2(mapValue.getValueOrDefault("play2", ""));
        sdpaProductGameDto.setPlay3(mapValue.getValueOrDefault("play3", ""));
        sdpaProductGameDto.setIp(mapValue.getValueOrDefault("ip", ""));
        sdpaProductGameDto.setTheme1(mapValue.getValueOrDefault("theme1", ""));
        sdpaProductGameDto.setTheme2(mapValue.getValueOrDefault("theme2", ""));
        sdpaProductGameDto.setTheme3(mapValue.getValueOrDefault("theme3", ""));
        sdpaProductGameDto.setStyle1(mapValue.getValueOrDefault("style1", ""));
        sdpaProductGameDto.setStyle2(mapValue.getValueOrDefault("style2", ""));
        sdpaProductGameDto.setElement(mapValue.getValueOrDefault("element", ""));
        sdpaProductGameDto.setManufacturer(mapValue.getValueOrDefault("manufacturer", ""));
        sdpaProductGameDto.setRole(mapValue.getValueOrDefault("role", ""));
        sdpaProductGameDto.setPayToPlay(mapValue.getValueOrDefault("pay_to_play", ""));

    }


    public void importCategoryExcel(MultipartFile multipartFile) {
        Set<Long> firstCategoryCodeSet = new HashSet<>();
        Set<Long> secondCategoryCodeSet = new HashSet<>();
        Set<Long> thirdCategoryCodeSet = new HashSet<>();
        Set<Long> spuCodeSet = new HashSet<>();

        Set<Long> existFirstCodeSet = adProductRepo.getAllProductCategoryListByLevel(1).stream().map(AdProductCategoryPo::getCode).collect(Collectors.toSet());
        Set<Long> existSecondCodeSet = adProductRepo.getAllProductCategoryListByLevel(2).stream().map(AdProductCategoryPo::getCode).collect(Collectors.toSet());
        Set<Long> existThirdCodeSet = adProductRepo.getAllProductCategoryListByLevel(3).stream().map(AdProductCategoryPo::getCode).collect(Collectors.toSet());
        Set<Long> existSpuCodeSet = adProductRepo.getAllSpuList().stream().map(AdProductSpuPo::getCode).collect(Collectors.toSet());

        firstCategoryCodeSet.addAll(existFirstCodeSet);
        secondCategoryCodeSet.addAll(existSecondCodeSet);
        thirdCategoryCodeSet.addAll(existThirdCodeSet);
        spuCodeSet.addAll(existSpuCodeSet);


        List<AdProductCategoryPo> adProductCategoryPoList = new ArrayList<>();
        List<AdProductSpuPo> adProductSpuPoList = new ArrayList<>();

        try {
            List<Object> contents = EasyExcel.read(multipartFile.getInputStream())
                    .autoTrim(true)
                    .ignoreEmptyRow(true)
                    .autoCloseStream(true)
                    .sheet().doReadSync();
            int lineNum = 0;
            for (Object obj : contents) {
                Map map = (Map) obj;
                lineNum++;
                String firstCategoryName = EffectiveAdExcelUtils.processCeilString(map.get(0));
                String secondCategoryName = EffectiveAdExcelUtils.processCeilString(map.get(1));
                String thirdCategoryName = EffectiveAdExcelUtils.processCeilString(map.get(2));
                String spuCodeName = EffectiveAdExcelUtils.processCeilString(map.get(3));
                Long firstCategoryCode = EffectiveAdExcelUtils.processCeilStringForLong(map.get(4));
                Long secondCategoryCode = EffectiveAdExcelUtils.processCeilStringForLong(map.get(5));
                Long thirdCategoryCode = EffectiveAdExcelUtils.processCeilStringForLong(map.get(6));
                Long spuCode = EffectiveAdExcelUtils.processCeilStringForLong(map.get(7));

                if (!firstCategoryCodeSet.contains(firstCategoryCode)) {
                    AdProductCategoryPo adProductCategoryPo = new AdProductCategoryPo();
                    adProductCategoryPo.setName(firstCategoryName);
                    adProductCategoryPo.setCode(firstCategoryCode);
                    adProductCategoryPo.setLevel(1);
                    adProductCategoryPo.setPCode(0L);
                    adProductCategoryPo.setRemark("");
                    adProductCategoryPo.setBizStatus(1);
                    adProductCategoryPo.setIsDeleted(0);
                    adProductCategoryPo.setCtime(new Timestamp(System.currentTimeMillis()));
                    adProductCategoryPo.setMtime(new Timestamp(System.currentTimeMillis()));
                    firstCategoryCodeSet.add(firstCategoryCode);
                    adProductCategoryPoList.add(adProductCategoryPo);
                }
                if (!secondCategoryCodeSet.contains(secondCategoryCode)) {
                    AdProductCategoryPo adProductCategoryPo = new AdProductCategoryPo();
                    adProductCategoryPo.setName(secondCategoryName);
                    adProductCategoryPo.setCode(secondCategoryCode);
                    adProductCategoryPo.setLevel(2);
                    adProductCategoryPo.setPCode(firstCategoryCode);
                    adProductCategoryPo.setRemark("");
                    adProductCategoryPo.setBizStatus(1);
                    adProductCategoryPo.setIsDeleted(0);
                    adProductCategoryPo.setCtime(new Timestamp(System.currentTimeMillis()));
                    adProductCategoryPo.setMtime(new Timestamp(System.currentTimeMillis()));
                    secondCategoryCodeSet.add(secondCategoryCode);
                    adProductCategoryPoList.add(adProductCategoryPo);

                }
                if (!thirdCategoryCodeSet.contains(thirdCategoryCode)) {
                    AdProductCategoryPo adProductCategoryPo = new AdProductCategoryPo();
                    adProductCategoryPo.setName(thirdCategoryName);
                    adProductCategoryPo.setCode(thirdCategoryCode);
                    adProductCategoryPo.setLevel(3);
                    adProductCategoryPo.setPCode(secondCategoryCode);
                    adProductCategoryPo.setRemark("");
                    adProductCategoryPo.setBizStatus(1);
                    adProductCategoryPo.setIsDeleted(0);
                    adProductCategoryPo.setCtime(new Timestamp(System.currentTimeMillis()));
                    adProductCategoryPo.setMtime(new Timestamp(System.currentTimeMillis()));
                    thirdCategoryCodeSet.add(thirdCategoryCode);
                    adProductCategoryPoList.add(adProductCategoryPo);
                }
                if (!spuCodeSet.contains(spuCode)) {
                    AdProductSpuPo adProductSpuPo = new AdProductSpuPo();
                    adProductSpuPo.setCode(spuCode);
                    adProductSpuPo.setName(spuCodeName);
                    adProductSpuPo.setThirdCategoryCode(thirdCategoryCode);
                    adProductSpuPo.setRemark("");
                    adProductSpuPo.setBizStatus(1);
                    adProductSpuPo.setIsDeleted(0);
                    adProductSpuPo.setCtime(new Timestamp(System.currentTimeMillis()));
                    adProductSpuPo.setMtime(new Timestamp(System.currentTimeMillis()));
                    spuCodeSet.add(spuCode);
                    adProductSpuPoList.add(adProductSpuPo);
                }


            }

            List<List<AdProductCategoryPo>> adProductCategoryPoPartionList = Lists.partition(adProductCategoryPoList, 100);
            for (List<AdProductCategoryPo> adProductCategoryPos : adProductCategoryPoPartionList) {
                try {
                    adProductRepo.batchInsertCategory(adProductCategoryPos);
                    Thread.sleep(500);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            }

            List<List<AdProductSpuPo>> adProductSpuPoPartionList = Lists.partition(adProductSpuPoList, 100);
            for (List<AdProductSpuPo> adProductSpuPos : adProductSpuPoPartionList) {
                try {
                    adProductRepo.batchInsertSpuCode(adProductSpuPos);
                    Thread.sleep(500);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            }

        } catch (IOException e) {
            throw new IllegalStateException("解析Excel失败：" + e.getMessage());
        }
    }


    @Override
    public void refreshCategoryExcel(MultipartFile multipartFile) {
        try {
            List<Object> contents = EasyExcel.read(multipartFile.getInputStream())
                    .autoTrim(true)
                    .ignoreEmptyRow(true)
                    .autoCloseStream(true)
                    .sheet().doReadSync();
            int lineNum = 0;
            for (Object obj : contents) {
                Map map = (Map) obj;
                lineNum++;
                Long adProductId = EffectiveAdExcelUtils.processCeilStringForLong(map.get(0));
                String firstCategoryName = EffectiveAdExcelUtils.processCeilString(map.get(1));
                String secondCategoryName = EffectiveAdExcelUtils.processCeilString(map.get(2));
                String thirdCategoryName = EffectiveAdExcelUtils.processCeilString(map.get(3));
                String spuCodeName = EffectiveAdExcelUtils.processCeilString(map.get(4));
                Long firstCategoryCode = EffectiveAdExcelUtils.processCeilStringForLong(map.get(5));
                Long secondCategoryCode = EffectiveAdExcelUtils.processCeilStringForLong(map.get(6));
                Long thirdCategoryCode = EffectiveAdExcelUtils.processCeilStringForLong(map.get(7));
                Long spuCode = EffectiveAdExcelUtils.processCeilStringForLong(map.get(8));
                AdProductTotalInfoPo productBaseByIdV2 = adProductRepo.getProductBaseByIdV2(adProductId);
                if (Objects.isNull(productBaseByIdV2)) {
                    log.info("refreshCategoryExcel adProductId:{} not exist", adProductId);
                }
                productBaseByIdV2.setFirstCategoryId(firstCategoryCode);
                productBaseByIdV2.setSecondCategoryId(secondCategoryCode);
                productBaseByIdV2.setThirdCategoryId(thirdCategoryCode);
                productBaseByIdV2.setFirstCategoryName(firstCategoryName);
                productBaseByIdV2.setSecondCategoryName(secondCategoryName);
                productBaseByIdV2.setThirdCategoryName(thirdCategoryName);
                String adAttributes = productBaseByIdV2.getAdAttributes();
                SdpaProductEducationExtraDto sdpaProductEducationExtraDto = JSON.parseObject(adAttributes, new TypeReference<SdpaProductEducationExtraDto>() {
                });
                sdpaProductEducationExtraDto.setSpuCode(spuCode);
                sdpaProductEducationExtraDto.setSpuName(spuCodeName);
                productBaseByIdV2.setAdAttributes(JSON.toJSONString(sdpaProductEducationExtraDto));
                adProductRepo.updateAdProductTotalInfoPo(productBaseByIdV2);

                Thread.sleep(100);
            }
        } catch (IOException | InterruptedException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void updateProductInfoByBinlog(AdProductTotalInfoCPCPo adProductTotalInfoCPCPo) {
        try {
            if (Objects.isNull(adProductTotalInfoCPCPo)) {
                return;
            }
            Long adProductId = adProductTotalInfoCPCPo.getAdProductId();

            List<AdProductTotalInfoCPCPo> adProductTotalInfoCPCPoList = adProductRepo.queryAdProductTotalInfoCPCPoListWithDeleted(Collections.singletonList(adProductId));

            if (CollectionUtils.isEmpty(adProductTotalInfoCPCPoList)) {
                log.info("binlogUpdateProductInfo adProductId:{} not exist", adProductId);
                return;
            }
            adProductRepo.updateProductInfoByBinlog(Collections.singletonList(adProductTotalInfoCPCPo));
        } catch (Exception e) {
            log.info("updateProductInfoByBinlog error adProductTotalInfoCPCPo : {}", JSON.toJSONString(adProductTotalInfoCPCPo), e);
        }
    }

    @Override
    public void insertProductInfoByBinlog(AdProductTotalInfoCPCPo adProductTotalInfoCPCPo) {
        try {
            if (Objects.isNull(adProductTotalInfoCPCPo)) {
                return;
            }
            Long adProductId = adProductTotalInfoCPCPo.getAdProductId();
            List<AdProductTotalInfoCPCPo> adProductTotalInfoCPCPoList = adProductRepo.queryAdProductTotalInfoCPCPoListWithDeleted(Collections.singletonList(adProductId));
            if (CollectionUtils.isNotEmpty(adProductTotalInfoCPCPoList)) {
                log.info("binlogInsertProductInfo adProductId:{} exist", adProductId);
                return;
            }
            adProductRepo.insertProductInfoByBinlog(Collections.singletonList(adProductTotalInfoCPCPo));
        } catch (Exception e) {
            log.error("insertProductInfoByBinlog error adProductTotalInfoCPCPo : {}", JSON.toJSONString(adProductTotalInfoCPCPo), e);
        }
    }

    @Override
    public void updateProductMappingByBinlog(AdProductMappingCPCPo adProductMappingCPCPo) {
        try {
            if (Objects.isNull(adProductMappingCPCPo)) {
                return;
            }
            if (Objects.equals(adProductMappingCPCPo.getType(), AdProductBindTypeEnum.UNIT.getCode())) {
                adProductRepo.updateProductUnitMappingByBinlog(Collections.singletonList(adProductMappingCPCPo));
            } else if (Objects.equals(adProductMappingCPCPo.getType(), AdProductBindTypeEnum.ACCOUNT.getCode())) {
                adProductRepo.updateProductAccountMappingByBinlog(Collections.singletonList(adProductMappingCPCPo));
            }


        } catch (Exception e) {
            log.error("updateProductMappingByBinlog error adProductMappingCPCPo : {}", JSON.toJSONString(adProductMappingCPCPo), e);
        }
    }

    @Override
    public void insertProductMappingByBinlog(AdProductMappingCPCPo adProductMappingCPCPo) {
        try {
            if (Objects.isNull(adProductMappingCPCPo)) {
                return;
            }
            adProductMappingCPCPo.setId(null);
            List<AdProductMappingCPCPo> existAdProductMappingCPCPos = adProductRepo.queryProductInfoByBinlog(adProductMappingCPCPo);
            if(CollectionUtils.isNotEmpty(existAdProductMappingCPCPos)){
                log.info("insertProductMappingByBinlog adProductMappingCPCPo exist, adProductMappingCPCPo={}", JSON.toJSONString(adProductMappingCPCPo));
                return;
            }

            adProductRepo.insertProductMappingByBinlog(Collections.singletonList(adProductMappingCPCPo));
        } catch (Exception e) {
            log.error("insertProductMappingByBinlog error adProductMappingCPCPo : {}", JSON.toJSONString(adProductMappingCPCPo), e);
        }
    }

    @Override
    public void deleteProductMappingByBinlog(AdProductMappingCPCPo adProductMappingCPCPo) {
        try {
            if (Objects.isNull(adProductMappingCPCPo)) {
                return;
            }
            adProductRepo.deleteProductMappingByBinlog(Collections.singletonList(adProductMappingCPCPo));
        } catch (Exception e) {
            log.error("deleteProductMappingByBinlog error adProductMappingCPCPo : {}", JSON.toJSONString(adProductMappingCPCPo), e);
        }
    }

    @Override
    public Boolean checkEnableSecondProduct(Integer accountId) {
        // 先查询用户自己的启用商品
        List<AdProductTotalInfoPo> ownProducts = adProductRepo.queryEnableProduct(accountId);
        if (ownProducts.size() >= 2) {
            // 如果自己的商品已经有2个或以上，不需要提示
            return false;
        }

        // 如果自己的商品少于2个，再查询共享商品
        List<AdProductInfoSharePo> sharedProducts = adProductRepo.queryEnableShareProduct(accountId);

        // 计算总启用商品数量
        int totalEnabledProducts = ownProducts.size() + sharedProducts.size();

        // 如果总数小于2，需要提示
        return totalEnabledProducts == 1;
    }

    private SdpaProductMedicalDto convertToSdpaProductMedicalDto(AdProductTotalInfoPo adProductTotalInfoPo) {
        SdpaProductMedicalDto sdpaProductMedicalDto = new SdpaProductMedicalDto();
        supplyAdProductCommonDto(sdpaProductMedicalDto, adProductTotalInfoPo);
        String adAttributes = adProductTotalInfoPo.getAdAttributes();
        SdpaProductMedicalExtraDto extraDto = JSON.parseObject(adAttributes, new TypeReference<SdpaProductMedicalExtraDto>() {
        });
        sdpaProductMedicalDto.setProductId(adProductTotalInfoPo.getAdProductId());
        sdpaProductMedicalDto.setAdProductName(adProductTotalInfoPo.getAdProductName());
        sdpaProductMedicalDto.setFirstCategoryId(adProductTotalInfoPo.getFirstCategoryId());
        sdpaProductMedicalDto.setSecondCategoryId(adProductTotalInfoPo.getSecondCategoryId());
        sdpaProductMedicalDto.setThirdCategoryId(adProductTotalInfoPo.getThirdCategoryId());
        sdpaProductMedicalDto.setFirstCategoryName(adProductTotalInfoPo.getFirstCategoryName());
        sdpaProductMedicalDto.setSecondCategoryName(adProductTotalInfoPo.getSecondCategoryName());
        sdpaProductMedicalDto.setThirdCategoryName(adProductTotalInfoPo.getThirdCategoryName());
        sdpaProductMedicalDto.setAdMainImgUrl(adProductTotalInfoPo.getAdMainImgUrl());
        sdpaProductMedicalDto.setAdSubImgUrl(adProductTotalInfoPo.getAdExtraImgUrl());
        sdpaProductMedicalDto.setH5LandingPageUrl(adProductTotalInfoPo.getH5LandingPageUrl());
        sdpaProductMedicalDto.setPcLandingPageUrl(adProductTotalInfoPo.getPcLandingPageUrl());
        sdpaProductMedicalDto.setIosLandingPageUrl(adProductTotalInfoPo.getIosLandingPageUrl());
        sdpaProductMedicalDto.setAndroidLandingPageUrl(adProductTotalInfoPo.getAndroidLandingPageUrl());
        sdpaProductMedicalDto.setAdOriginalPrice(adProductTotalInfoPo.getAdOriginalPrice());
        // 设置额外信息
        if (extraDto != null) {
            sdpaProductMedicalDto.setArea(extraDto.getArea());
            sdpaProductMedicalDto.setBrandId(extraDto.getBrandId());
            sdpaProductMedicalDto.setBrandName(extraDto.getBrandName());
            sdpaProductMedicalDto.setProductBrief(extraDto.getProductBrief());
            sdpaProductMedicalDto.setSpuCode(extraDto.getSpuCode());
            sdpaProductMedicalDto.setSkuCode(extraDto.getSkuCode());
        }
        return sdpaProductMedicalDto;
    }

    private SdpaProductMedicalDto convertToSdpaProductMedicalDto(AdProductTotalInfoCPCPo adProductTotalInfoCPCPo) {
        SdpaProductMedicalDto sdpaProductMedicalDto = new SdpaProductMedicalDto();
        String adAttributes = adProductTotalInfoCPCPo.getAdAttributes();
        SdpaProductMedicalExtraDto extraDto = JSON.parseObject(adAttributes, new TypeReference<SdpaProductMedicalExtraDto>() {
        });
        sdpaProductMedicalDto.setProductId(adProductTotalInfoCPCPo.getAdProductId());
        sdpaProductMedicalDto.setAdProductName(adProductTotalInfoCPCPo.getAdProductName());
        sdpaProductMedicalDto.setFirstCategoryId(adProductTotalInfoCPCPo.getFirstCategoryId());
        sdpaProductMedicalDto.setSecondCategoryId(adProductTotalInfoCPCPo.getSecondCategoryId());
        sdpaProductMedicalDto.setThirdCategoryId(adProductTotalInfoCPCPo.getThirdCategoryId());
        sdpaProductMedicalDto.setFirstCategoryName(adProductTotalInfoCPCPo.getFirstCategoryName());
        sdpaProductMedicalDto.setSecondCategoryName(adProductTotalInfoCPCPo.getSecondCategoryName());
        sdpaProductMedicalDto.setThirdCategoryName(adProductTotalInfoCPCPo.getThirdCategoryName());
        sdpaProductMedicalDto.setAdMainImgUrl(adProductTotalInfoCPCPo.getAdMainImgUrl());
        sdpaProductMedicalDto.setAdSubImgUrl(adProductTotalInfoCPCPo.getAdExtraImgUrl());
        sdpaProductMedicalDto.setH5LandingPageUrl(adProductTotalInfoCPCPo.getH5LandingPageUrl());
        sdpaProductMedicalDto.setPcLandingPageUrl(adProductTotalInfoCPCPo.getPcLandingPageUrl());
        sdpaProductMedicalDto.setIosLandingPageUrl(adProductTotalInfoCPCPo.getIosLandingPageUrl());
        sdpaProductMedicalDto.setAndroidLandingPageUrl(adProductTotalInfoCPCPo.getAndroidLandingPageUrl());
        sdpaProductMedicalDto.setAdOriginalPrice(adProductTotalInfoCPCPo.getAdOriginalPrice());
        sdpaProductMedicalDto.setAccountId(adProductTotalInfoCPCPo.getAccountId());
        sdpaProductMedicalDto.setLibraryId(adProductTotalInfoCPCPo.getLibraryId());
        sdpaProductMedicalDto.setUniqueKey(adProductTotalInfoCPCPo.getUniqueKey());
        // 设置额外信息
        if (extraDto != null) {
            sdpaProductMedicalDto.setArea(extraDto.getArea());
            sdpaProductMedicalDto.setBrandId(extraDto.getBrandId());
            sdpaProductMedicalDto.setBrandName(extraDto.getBrandName());
            sdpaProductMedicalDto.setProductBrief(extraDto.getProductBrief());
            sdpaProductMedicalDto.setSpuCode(extraDto.getSpuCode());
            sdpaProductMedicalDto.setSkuCode(extraDto.getSkuCode());
        }
        return sdpaProductMedicalDto;
    }

    private SdpaProductHouseDto convertToSdpaProductHouseDto(AdProductTotalInfoPo adProductTotalInfoPo) {
        SdpaProductHouseDto sdpaProductHouseDto = new SdpaProductHouseDto();
        supplyAdProductCommonDto(sdpaProductHouseDto, adProductTotalInfoPo);
        String adAttributes = adProductTotalInfoPo.getAdAttributes();
        SdpaProductHouseExtraDto extraDto = JSON.parseObject(adAttributes, new TypeReference<SdpaProductHouseExtraDto>() {
        });
        sdpaProductHouseDto.setProductId(adProductTotalInfoPo.getAdProductId());
        sdpaProductHouseDto.setAdProductName(adProductTotalInfoPo.getAdProductName());
        sdpaProductHouseDto.setFirstCategoryId(adProductTotalInfoPo.getFirstCategoryId());
        sdpaProductHouseDto.setSecondCategoryId(adProductTotalInfoPo.getSecondCategoryId());
        sdpaProductHouseDto.setThirdCategoryId(adProductTotalInfoPo.getThirdCategoryId());
        sdpaProductHouseDto.setFirstCategoryName(adProductTotalInfoPo.getFirstCategoryName());
        sdpaProductHouseDto.setSecondCategoryName(adProductTotalInfoPo.getSecondCategoryName());
        sdpaProductHouseDto.setThirdCategoryName(adProductTotalInfoPo.getThirdCategoryName());
        sdpaProductHouseDto.setAdMainImgUrl(adProductTotalInfoPo.getAdMainImgUrl());
        sdpaProductHouseDto.setAdSubImgUrl(adProductTotalInfoPo.getAdExtraImgUrl());
        sdpaProductHouseDto.setH5LandingPageUrl(adProductTotalInfoPo.getH5LandingPageUrl());
        sdpaProductHouseDto.setPcLandingPageUrl(adProductTotalInfoPo.getPcLandingPageUrl());
        sdpaProductHouseDto.setIosLandingPageUrl(adProductTotalInfoPo.getIosLandingPageUrl());
        sdpaProductHouseDto.setAndroidLandingPageUrl(adProductTotalInfoPo.getAndroidLandingPageUrl());
        sdpaProductHouseDto.setAdOriginalPrice(adProductTotalInfoPo.getAdOriginalPrice());
        // 设置额外信息
        if (extraDto != null) {
            sdpaProductHouseDto.setArea(extraDto.getArea());
            sdpaProductHouseDto.setBrandId(extraDto.getBrandId());
            sdpaProductHouseDto.setBrandName(extraDto.getBrandName());
            sdpaProductHouseDto.setSellingPoint(extraDto.getSellingPoint());
            sdpaProductHouseDto.setPromotionDetail(extraDto.getPromotionDetail());
            sdpaProductHouseDto.setMPrice(extraDto.getMPrice());
            sdpaProductHouseDto.setDesignStyle(extraDto.getDesignStyle());
            sdpaProductHouseDto.setIosUpUlinkUrl(extraDto.getIosUpUlinkUrl());
            sdpaProductHouseDto.setSkuCode(extraDto.getSkuCode());
            sdpaProductHouseDto.setSpuCode(extraDto.getSpuCode());
        }
        return sdpaProductHouseDto;
    }

    private SdpaProductHouseDto convertToSdpaProductHouseDto(AdProductTotalInfoCPCPo adProductTotalInfoCPCPo) {
        SdpaProductHouseDto sdpaProductHouseDto = new SdpaProductHouseDto();
        String adAttributes = adProductTotalInfoCPCPo.getAdAttributes();
        SdpaProductHouseExtraDto extraDto = JSON.parseObject(adAttributes, new TypeReference<SdpaProductHouseExtraDto>() {
        });
        sdpaProductHouseDto.setProductId(adProductTotalInfoCPCPo.getAdProductId());
        sdpaProductHouseDto.setAdProductName(adProductTotalInfoCPCPo.getAdProductName());
        sdpaProductHouseDto.setFirstCategoryId(adProductTotalInfoCPCPo.getFirstCategoryId());
        sdpaProductHouseDto.setSecondCategoryId(adProductTotalInfoCPCPo.getSecondCategoryId());
        sdpaProductHouseDto.setThirdCategoryId(adProductTotalInfoCPCPo.getThirdCategoryId());
        sdpaProductHouseDto.setFirstCategoryName(adProductTotalInfoCPCPo.getFirstCategoryName());
        sdpaProductHouseDto.setSecondCategoryName(adProductTotalInfoCPCPo.getSecondCategoryName());
        sdpaProductHouseDto.setThirdCategoryName(adProductTotalInfoCPCPo.getThirdCategoryName());
        sdpaProductHouseDto.setAdMainImgUrl(adProductTotalInfoCPCPo.getAdMainImgUrl());
        sdpaProductHouseDto.setAdSubImgUrl(adProductTotalInfoCPCPo.getAdExtraImgUrl());
        sdpaProductHouseDto.setH5LandingPageUrl(adProductTotalInfoCPCPo.getH5LandingPageUrl());
        sdpaProductHouseDto.setPcLandingPageUrl(adProductTotalInfoCPCPo.getPcLandingPageUrl());
        sdpaProductHouseDto.setIosLandingPageUrl(adProductTotalInfoCPCPo.getIosLandingPageUrl());
        sdpaProductHouseDto.setAndroidLandingPageUrl(adProductTotalInfoCPCPo.getAndroidLandingPageUrl());
        sdpaProductHouseDto.setAdOriginalPrice(adProductTotalInfoCPCPo.getAdOriginalPrice());
        sdpaProductHouseDto.setAccountId(adProductTotalInfoCPCPo.getAccountId());
        sdpaProductHouseDto.setLibraryId(adProductTotalInfoCPCPo.getLibraryId());
        sdpaProductHouseDto.setUniqueKey(adProductTotalInfoCPCPo.getUniqueKey());
        // 设置额外信息
        if (extraDto != null) {
            sdpaProductHouseDto.setArea(extraDto.getArea());
            sdpaProductHouseDto.setBrandId(extraDto.getBrandId());
            sdpaProductHouseDto.setBrandName(extraDto.getBrandName());
            sdpaProductHouseDto.setSellingPoint(extraDto.getSellingPoint());
            sdpaProductHouseDto.setPromotionDetail(extraDto.getPromotionDetail());
            sdpaProductHouseDto.setMPrice(extraDto.getMPrice());
            sdpaProductHouseDto.setDesignStyle(extraDto.getDesignStyle());
            sdpaProductHouseDto.setIosUpUlinkUrl(extraDto.getIosUpUlinkUrl());
            sdpaProductHouseDto.setSkuCode(extraDto.getSkuCode());
            sdpaProductHouseDto.setSpuCode(extraDto.getSpuCode());
        }
        return sdpaProductHouseDto;
    }
}