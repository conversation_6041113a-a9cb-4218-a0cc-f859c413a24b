/**
 * <AUTHOR>
 * @date 2017年6月9日
 */

package com.bilibili.adp.cpc.log.handler;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.account.dto.AccountBaseDto;
import com.bilibili.adp.account.service.IQueryAccountService;
import com.bilibili.adp.common.OcpcTargetEnum;
import com.bilibili.adp.common.enums.LauTagIdsTargetType;
import com.bilibili.adp.common.enums.SalesType;
import com.bilibili.adp.common.util.GsonUtils;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.biz.services.app.api.IAppPackageService;
import com.bilibili.adp.cpc.biz.services.target_package.api.ICrowdPackService;
import com.bilibili.adp.cpc.biz.services.unit.api.ILauUnitService;
import com.bilibili.adp.cpc.core.constants.CommonSwitchEnum;
import com.bilibili.adp.cpc.core.constants.DualBidTwoStageOptimization;
import com.bilibili.adp.cpc.dto.LauUnitBaseDto;
import com.bilibili.adp.cpc.enums.InstalledUserFilterEnum;
import com.bilibili.adp.cpc.enums.InstalledUserTargetContentEnum;
import com.bilibili.adp.launch.api.common.FrequencyUnit;
import com.bilibili.adp.launch.api.service.IHystrixDmpService;
import com.bilibili.adp.launch.api.service.ILauProductGroupService;
import com.bilibili.adp.launch.api.service.IQueryShopGoodsService;
import com.bilibili.adp.launch.api.unit.dto.*;
import com.bilibili.adp.launch.api.unit.param.QueryDpaShopGoodsParam;
import com.bilibili.adp.launch.biz.common.LaunchUtil;
import com.bilibili.adp.launch.biz.handler.log_post_handlers.BasePostHandler;
import com.bilibili.adp.log.bean.DiffItem;
import com.bilibili.adp.log.dto.LogOperateDto;
import com.bilibili.adp.log.handler.LogOperatePostHandler;
import com.bilibili.adp.passport.api.dto.ArchivePartition;
import com.bilibili.adp.passport.api.dto.GameBaseDto;
import com.bilibili.adp.passport.api.dto.UserBasicDto;
import com.bilibili.adp.passport.api.service.IGameCenterService;
import com.bilibili.adp.passport.api.service.IPassportService;
import com.bilibili.adp.passport.biz.manager.ArchiveManager;
import com.bilibili.adp.resource.api.target_lau.IResTargetItemService;
import com.dianping.cat.Cat;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * copy class
 * @see com.bilibili.adp.launch.biz.handler.log_post_handlers.UnitHandler
 */
@Slf4j
@Component(value = "launchUnitHandler")
public class UnitHandler extends BasePostHandler implements LogOperatePostHandler {
    private final Logger LOGGER = LoggerFactory.getLogger(getClass());

    @Autowired
    private ICrowdPackService crowdPackService;
    @Autowired
    private ILauUnitService lauUnitService;
    @Autowired
    private IResTargetItemService resTargetItemService;
    @Resource(name = "cpcAppPackageService")
    private IAppPackageService appPackageService;
    @Autowired
    private IQueryShopGoodsService queryShopGoodsService;
    @Autowired
    private IQueryAccountService queryAccountService;
    @Autowired
    private IGameCenterService gameCenterService;
    @Autowired
    private IPassportService passportService;
    @Autowired
    private ArchiveManager archiveManager;
    @Autowired
    private IHystrixDmpService hystrixDmpService;
    @Autowired
    private ILauProductGroupService lauProductGroupService;

    private <T> List<T> getTargetIdsList(List<LogOperateDto> dtos, List<String> fieldList, Class<T> clazz) {

        Assert.notNull(clazz, "Clazz不可为空");
        Assert.notEmpty(fieldList, "字段名称不可为空");
        return dtos
                .stream()
                .flatMap(dto -> dto.getValue().stream())
                .filter(item -> fieldList.contains(item.getKey()))
                .flatMap(item -> Stream.of(item.getOldValue(), item.getNewValue()))
                .filter(value -> !Strings.isNullOrEmpty(value) && !"0".equals(value))
                .flatMap(value -> JSON.parseArray(value, clazz).stream())
                .distinct()
                .collect(Collectors.toList());
    }

    private Map<Integer, String> getBusinessInterestMap(List<Integer> businessInterestIds) {
        if (CollectionUtils.isEmpty(businessInterestIds)) {
            return Collections.emptyMap();
        }
        Assert.isTrue(!hystrixDmpService.isPeopleGroupCircuitBreakerOpen(), "暂不支持该操作，请稍后重试");
        try {
            return crowdPackService.getCrowdPackNameMapInIdsDirectly(businessInterestIds);
        } catch (Exception e) {
            LOGGER.error("log handler crowdPackService.getCrowdPackNameMapInIds failed", e);
            Cat.logEvent("Exception_SoaBusinessCategoryService", Strings.isNullOrEmpty(e.getMessage()) ? "查询商业兴趣异常信息为空" : e.getMessage());
            return businessInterestIds.stream().collect(Collectors.toMap(Function.identity(), String::valueOf));
        }
    }

    private List<Integer> getTargteIdList(List<LogOperateDto> dtos, List<String> fieldList) {
        return dtos.stream()
                .flatMap(dto -> dto.getValue().stream())
                .filter(item -> fieldList.contains(item.getKey()))
                .flatMap(item -> Stream.of(item.getOldValue(), item.getNewValue()))
                .filter(value -> !Strings.isNullOrEmpty(value) && StringUtils.isNumeric(value))
                .map(value -> Integer.parseInt(value))
                .distinct()
                .collect(Collectors.toList());
    }

    private void addDiffItemOfExtraTarget(List<LogOperateDto> dtos) {
        // 获取所有MID
        List<Long> mids = dtos.stream()
                .flatMap(dto -> dto.getValue().stream())
                .filter(item -> "extraTarget".equals(item.getKey()))
                .flatMap(item -> Stream.of(item.getOldValue(), item.getNewValue()))
                .map(value -> GsonUtils.forceToObject(value, CpcUnitExtraTargetDto.class))
                .filter(Objects::nonNull)
                .flatMap(item -> Stream.of(item.getExcludeTheirsFans(), item.getIncludeTheirsFans()))
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .distinct()
                .collect(Collectors.toList());

        LOGGER.info("UnitHandler.addDiffItemOfExtraTarget:mids.size={}", mids.size());

        // 批量获取用户名称
        Map<Long, UserBasicDto> midToName = CollectionUtils.isEmpty(mids) ?
                Collections.emptyMap() : passportService.getUserBasicInfos(mids);

        // 批量获取两级分区
        Map<Integer, ArchivePartition> partitionMap;
        try {
            partitionMap = archiveManager.getAllVideoSecondPartitions()
                    .stream().collect(Collectors.toMap(ArchivePartition::getId, Function.identity()));
        } catch (Exception e) {
            LOGGER.error("获取视频分区信息失败", e);
            throw new IllegalStateException("获取视频分区信息失败");
        }

        // 遍历差异项
        dtos.forEach(dto -> {
            List<DiffItem> diffItems = new ArrayList<>();
            Iterator<DiffItem> itemIterator = dto.getValue().iterator();
            while (itemIterator.hasNext()) {
                DiffItem item = itemIterator.next();

                // 只处理额外定向中的内容
                if ("extraTarget".equals(item.getKey())) {
                    CpcUnitExtraTargetDto oldTargetDto = GsonUtils.forceToObject(item.getOldValue(), CpcUnitExtraTargetDto.class);
                    CpcUnitExtraTargetDto newTargetDto = GsonUtils.forceToObject(item.getNewValue(), CpcUnitExtraTargetDto.class);
                    LOGGER.info("UnitHandler.addDiffItemOfExtraTarget:oldTargetDto={},newTargetDto={}", oldTargetDto, newTargetDto);

                    List<Long> oldIncludeTheirsFans = oldTargetDto == null ? null : oldTargetDto.getIncludeTheirsFans();
                    List<Long> newIncludeTheirsFans = newTargetDto == null ? null : newTargetDto.getIncludeTheirsFans();
                    List<Long> oldExcludeTheirsFans = oldTargetDto == null ? null : oldTargetDto.getExcludeTheirsFans();
                    List<Long> newExcludeTheirsFans = newTargetDto == null ? null : newTargetDto.getExcludeTheirsFans();
                    List<Integer> oldVideoSecondPartition = oldTargetDto == null ? null : oldTargetDto.getVideoSecondPartition();
                    List<Integer> newVideoSecondPartition = newTargetDto == null ? null : newTargetDto.getVideoSecondPartition();

                    DiffItem diffItem2 = DiffItem.builder().key("extraTarget")
                            .desc("投放UP主粉丝")
                            .oldValue(oldIncludeTheirsFans == null ? "--" : oldIncludeTheirsFans.stream()
                                    .map(mid -> midToName.get(mid).getName()).collect(Collectors.joining(",")))
                            .newValue(newIncludeTheirsFans == null ? "--" : newIncludeTheirsFans.stream()
                                    .map(mid -> midToName.get(mid).getName()).collect(Collectors.joining(",")))
                            .build();
                    if (!Objects.equals(diffItem2.getOldValue(), diffItem2.getNewValue())) {
                        diffItems.add(diffItem2);
                    }
                    DiffItem diffItem1 = DiffItem.builder().key("extraTarget")
                            .desc("排除UP主粉丝")
                            .oldValue(oldExcludeTheirsFans == null ? "--" : oldExcludeTheirsFans.stream()
                                    .map(mid -> midToName.get(mid).getName()).collect(Collectors.joining(",")))
                            .newValue(newExcludeTheirsFans == null ? "--" : newExcludeTheirsFans.stream()
                                    .map(mid -> midToName.get(mid).getName()).collect(Collectors.joining(",")))
                            .build();
                    if (!Objects.equals(diffItem1.getOldValue(), diffItem1.getNewValue())) {
                        diffItems.add(diffItem1);
                    }

                    DiffItem diffItem = DiffItem.builder().key("extraTarget")
                            .desc("感兴趣的类目")
                            .oldValue(oldVideoSecondPartition == null ? "--" : oldVideoSecondPartition.stream()
                                    .map(pid -> partitionMap.get(pid).getName()).collect(Collectors.joining(",")))
                            .newValue(newVideoSecondPartition == null ? "--" : newVideoSecondPartition.stream()
                                    .map(pid -> partitionMap.get(pid).getName()).collect(Collectors.joining(",")))
                            .build();
                    if (!Objects.equals(diffItem.getOldValue(), diffItem.getNewValue())) {
                        diffItems.add(diffItem);
                    }
                    itemIterator.remove();
                }
            }
            diffItems.forEach(item -> dto.getValue().add(item));
        });
    }

    @Override
    public void handle(List<LogOperateDto> dtos) {
        addDiffItemOfExtraTarget(dtos);

        List<Integer> crowdPackIds = this.getTargetIdsList(dtos, Lists.newArrayList("crowdPackIds", "excludeCrowdPackIds"), Integer.class);
        List<Integer> businessInterestIds = this.getTargetIdsList(dtos, Lists.newArrayList("businessInterestIds"), Integer.class);

        List<Integer> otherCpIds = dtos
                .stream()
                .flatMap(dto -> dto.getValue().stream())
                .filter(item -> "otherCrowdPackIdsGroup".equals(item.getKey()))
                .flatMap(item -> Stream.of(item.getOldValue(), item.getNewValue()))
                .filter(value -> !Strings.isNullOrEmpty(value))
                .flatMap(value -> JSON.parseArray(value, String.class).stream())
                .flatMap(value -> JSON.parseArray(value, Integer.class).stream())
                .distinct()
                .collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(otherCpIds)) {
            crowdPackIds.addAll(otherCpIds);
        }

        List<Integer> appIds = this.getTargteIdList(dtos, Lists.newArrayList("appPackageId"));

        Map<Integer, String> cpNameMap = Collections.emptyMap();
        if (!CollectionUtils.isEmpty(crowdPackIds)) {
            Assert.isTrue(!hystrixDmpService.isPeopleGroupCircuitBreakerOpen(), "暂不支持该操作，请稍后重试");
            cpNameMap = crowdPackService.getCrowdPackNameMapInIdsDirectly(crowdPackIds);
        }
        Map<Integer, String> appNameMap = appPackageService.getAppNameMapInPrimaryKeys(appIds);

        List<Integer> unitIds = dtos.stream().map(dto -> dto.getObjId()).collect(Collectors.toList());
        Map<Integer, LauUnitBaseDto> unitMap = lauUnitService.getBaseDtoMapInIds(unitIds);
        Map<Integer, String> itemNameMap = resTargetItemService.getAllItemNameMap();

        Map<Integer, String> businessInterestMap = this.getBusinessInterestMap(businessInterestIds);

        List<Integer> shopGoodsIds = this.getTargteIdList(dtos, Lists.newArrayList("shopGoodsId"));
        Map<Integer, String> shopGoodsMap = getShopGoodsMap(dtos.get(0).getAccountId(), shopGoodsIds);

        List<Integer> gameBaseIds = this.getTargteIdList(dtos, Lists.newArrayList("gameBaseId"));
        Map<Integer, String> gameMap = this.getGameMap(dtos.get(0).getAccountId(), gameBaseIds);
        List<Integer> productGroupIdList = this.getTargteIdList(dtos, Lists.newArrayList("productGroupId"));
        Map<Integer, String> productGroupNameMap = lauProductGroupService.getGroupNameByIdList(productGroupIdList,
                dtos.get(0).getAccountId());

        Iterator<LogOperateDto> dit = dtos.iterator();
        while (dit.hasNext()) {
            LogOperateDto dto = dit.next();
            List<DiffItem> otherDiffs = Lists.newArrayList();
            List<DiffItem> isAllAdSearchUnitDiff = dto.getValue().stream().filter(diffItem -> "isAllAdSearchUnit".equals(diffItem.getKey())).collect(Collectors.toList());
            List<DiffItem> hasKeywords = dto.getValue().stream().filter(diffItem -> "unitKeywordsFieldKey".equals(diffItem.getKey())).collect(Collectors.toList());

            List<DiffItem> diffs = dto.getValue();
            Iterator<DiffItem> it = diffs.iterator();
            while (it.hasNext()) {
                DiffItem d = it.next();

                switch (d.getKey()) {
                    case "defaultGdQuota":
                    case "currentMonthAdjustQuota":
                    case "nextMonthAdjustQuota":

                        if (!StringUtils.isEmpty(d.getOldValue())) {
                            d.setOldValue(Utils.fromFenToYuan(Double.parseDouble(d.getOldValue())).toString());
                        }

                        if (!StringUtils.isEmpty(d.getNewValue())) {
                            d.setNewValue(Utils.fromFenToYuan(Double.parseDouble(d.getNewValue())).toString());
                        }

                        break;
                    case "launchEndDate":
                        String today = Utils.getTimestamp2String(Utils.getToday());

                        if (getDays(d.getOldValue(), today) > 50 * 365) {
                            d.setOldValue("");
                        }

                        if (getDays(d.getNewValue(), today) > 50 * 365) {
                            d.setNewValue("");
                        }

                        break;
                    case "frequencyUnit":
                        if (!StringUtils.isEmpty(d.getOldValue()) && StringUtils.isNumeric(d.getOldValue())) {
                            d.setOldValue(FrequencyUnit.getByCode(Integer.valueOf(d.getOldValue())).getMessage());
                        }
                        if (!StringUtils.isEmpty(d.getNewValue()) && StringUtils.isNumeric(d.getNewValue())) {
                            d.setNewValue(FrequencyUnit.getByCode(Integer.valueOf(d.getNewValue())).getMessage());
                        }
                        d.setDesc("频次限制");
                        break;

                    case "shopGoodsId":
                        d.setOldValue("0".equals(d.getOldValue()) ? "" : getNameById(d.getOldValue(), shopGoodsMap));
                        d.setNewValue(getNameById(d.getNewValue(), shopGoodsMap));
                        break;

                    case "salesType":
                        if (!org.springframework.util.StringUtils.isEmpty(d.getOldValue()) && StringUtils.isNumeric(d.getOldValue())) {
                            d.setOldValue(SalesType.getByCode(Integer.parseInt(d.getOldValue())).getName());
                        }

                        if (!org.springframework.util.StringUtils.isEmpty(d.getNewValue()) && StringUtils.isNumeric(d.getNewValue())) {
                            d.setNewValue(SalesType.getByCode(Integer.parseInt(d.getNewValue())).getName());
                        }
                        break;

                    case "ocpxTargetTwo":
                    case "ocpcTarget":
                        if (!org.springframework.util.StringUtils.isEmpty(d.getOldValue()) && StringUtils.isNumeric(d.getOldValue())) {
                            d.setOldValue("0".equals(d.getOldValue()) ? "" : Optional.ofNullable(OcpcTargetEnum.getByCode(Integer.parseInt(d.getOldValue()))).orElse(OcpcTargetEnum.UNDEFINED).getDesc());
                        }

                        if (!org.springframework.util.StringUtils.isEmpty(d.getNewValue()) && StringUtils.isNumeric(d.getNewValue())) {
                            d.setNewValue("0".equals(d.getNewValue()) ? "" : Optional.ofNullable(OcpcTargetEnum.getByCode(Integer.parseInt(d.getNewValue()))).orElse(OcpcTargetEnum.UNDEFINED).getDesc());
                        }
                        break;

                    case "ocpxTargetTwoBid":
                        if (!org.springframework.util.StringUtils.isEmpty(d.getOldValue()) && StringUtils.isNumeric(d.getOldValue())) {
                            d.setOldValue("0".equals(d.getOldValue()) ? "自动优化" : longStringToBigDecimalString(d.getOldValue()));
                        }

                        if (!org.springframework.util.StringUtils.isEmpty(d.getNewValue()) && StringUtils.isNumeric(d.getNewValue())) {
                            d.setNewValue("0".equals(d.getNewValue()) ? "自动优化" : longStringToBigDecimalString(d.getNewValue()));
                        }
                        break;

                    case "ocpxTargetTwoBid4Log":
                        if (!org.springframework.util.StringUtils.isEmpty(d.getOldValue()) && StringUtils.isNumeric(d.getOldValue())) {
                            d.setOldValue("0".equals(d.getOldValue()) ? "自动优化" : d.getOldValue());
                        }

                        if (!org.springframework.util.StringUtils.isEmpty(d.getNewValue()) && StringUtils.isNumeric(d.getNewValue())) {
                            d.setNewValue("0".equals(d.getNewValue()) ? "自动优化" : d.getNewValue());
                        }
                        break;

                    case "twoStageBid":
                        if (!org.springframework.util.StringUtils.isEmpty(d.getOldValue()) && StringUtils.isNumeric(d.getOldValue())) {
                            d.setOldValue("0".equals(d.getOldValue()) ? "" : longStringToBigDecimalString(d.getOldValue()));
                        }

                        if (!org.springframework.util.StringUtils.isEmpty(d.getNewValue()) && StringUtils.isNumeric(d.getNewValue())) {
                            d.setNewValue("0".equals(d.getNewValue()) ? "" : longStringToBigDecimalString(d.getNewValue()));
                        }
                        break;

                    case "frequencyLimit":
                        String maxIntStr = String.valueOf(Integer.MAX_VALUE);

                        if (maxIntStr.equals(d.getOldValue())) {
                            d.setOldValue("");
                        }

                        if (maxIntStr.equals(d.getNewValue())) {
                            d.setNewValue("");
                        }
                        d.setDesc("展示不超过(次)");
                        break;
                    case "crowdPackIds":
                    case "excludeCrowdPackIds":
                        d.setOldValue(getTargetItemNamesString(d.getOldValue(), cpNameMap));
                        d.setNewValue(getTargetItemNamesString(d.getNewValue(), cpNameMap));
                        break;

                    case "budget":
                    case "nextdayBudget":
                        d.setOldValue(longStringToBigDecimalString(d.getOldValue()));
                        d.setNewValue(longStringToBigDecimalString(d.getNewValue()));
                        break;

                    case "costPrice":
                        d.setOldValue(longStringToBigDecimalString(d.getOldValue()));
                        d.setNewValue(longStringToBigDecimalString(d.getNewValue()));
                        d.setDesc("出价（单位" + Optional.ofNullable(unitMap.get(dto.getObjId())).map(u -> u.getSalesType() == SalesType.CPM.getCode() ? "CPM" : "元").orElse("元") + "）");
                        break;
                    case "gender":
                    case "age":
                    case "os":
                    case "network":
                    case "category":
                    case "keyword":
                    case "area":
                    case "deviceBrand":
                    case "appCategory":
                    case "schedule":

                        d.setOldValue(getTargetItemName(d.getOldValue(), itemNameMap));
                        d.setNewValue(getTargetItemName(d.getNewValue(), itemNameMap));
                        break;
                    case "ageCustomize":
                        d.setOldValue(getAgeCustomizeTarget(d.getOldValue()));
                        d.setNewValue(getAgeCustomizeTarget(d.getNewValue()));
                        break;
                    case "businessInterestIds":
                        d.setOldValue(getTargetItemNamesString(d.getOldValue(), businessInterestMap));
                        d.setNewValue(getTargetItemNamesString(d.getNewValue(), businessInterestMap));
                        break;

                    case "launchTime":
                        d.setOldValue(com.bilibili.adp.cpc.utils.LaunchUtil.beautyUnitLaunchTimeToStr(d.getOldValue()));
                        d.setNewValue(com.bilibili.adp.cpc.utils.LaunchUtil.beautyUnitLaunchTimeToStr((d.getNewValue())));
                        break;

                    case "appPackageId":
                        d.setOldValue("0".equals(d.getOldValue()) ? "" : getAppNameString(d.getOldValue(), appNameMap));
                        d.setNewValue(getAppNameString(d.getNewValue(), appNameMap));
                        break;
                    case "gameBaseId":
                        d.setOldValue("0".equals(d.getOldValue()) ? "" : getGameNameString(d.getOldValue(), gameMap));
                        d.setNewValue(getGameNameString(d.getNewValue(), gameMap));
                        break;

                    case "otherCrowdPackIdsGroup":
                        List<String> oldGroup = getStringArray(d.getOldValue());
                        List<String> newGroup = getStringArray(d.getNewValue());

                        it.remove();

                        otherDiffs.addAll(genOtherCrowdPackGroupDiff(oldGroup, newGroup, cpNameMap));
                        break;

                    case "isFuzzyTags":
                        d.setOldValue(parseEnum(d.getOldValue(), LauTagIdsTargetType::getByCode, LauTagIdsTargetType::getDesc));
                        d.setNewValue(parseEnum(d.getNewValue(), LauTagIdsTargetType::getByCode, LauTagIdsTargetType::getDesc));

                        break;

                    case "videoTag":
                        otherDiffs.addAll(genVideoTagDiff(d.getOldValue(), d.getNewValue()));

                        it.remove();

                        break;
                    case "productGroupId":
                        d.setOldValue(getProductGroupName(d.getOldValue(), productGroupNameMap));
                        d.setNewValue(getProductGroupName(d.getNewValue(), productGroupNameMap));
                        break;

                    case "installedUserFilter":
                        d.setOldValue(getInstalledUserFilter(d.getOldValue()));
                        d.setNewValue(getInstalledUserFilter(d.getNewValue()));
                        break;

                    case "assistPrice":
                        d.setOldValue(longStringToBigDecimalString(d.getOldValue()));
                        d.setNewValue(longStringToBigDecimalString(d.getNewValue()));
                        break;
                    case "dualBidTwoStageOptimization":
                        if (org.apache.commons.lang3.StringUtils.isNumeric(d.getOldValue())) {
                            d.setOldValue(DualBidTwoStageOptimization.getByCode(Integer.parseInt(d.getOldValue())).getDesc());
                        }
                        if (org.apache.commons.lang3.StringUtils.isNumeric(d.getNewValue())) {
                            d.setNewValue(DualBidTwoStageOptimization.getByCode(Integer.parseInt(d.getNewValue())).getDesc());
                        }
                        break;
                    case "targetExpand":
                        if (org.apache.commons.lang3.StringUtils.isNumeric(d.getOldValue())) {
                            d.setOldValue(CommonSwitchEnum.getByCode(Integer.parseInt(d.getOldValue())).getDesc());
                            if (!CollectionUtils.isEmpty(isAllAdSearchUnitDiff)) {
                                String oldValue = isAllAdSearchUnitDiff.get(0).getOldValue();
                                if ("0".equals(oldValue) && CommonSwitchEnum.DISABLED.getDesc().equals(d.getOldValue())) {
                                    d.setOldValue("--");
                                }
                            } else if (!CollectionUtils.isEmpty(hasKeywords)) {
                                String oldValue = hasKeywords.get(0).getOldValue();
                                if (StringUtils.isBlank(oldValue) && CommonSwitchEnum.DISABLED.getDesc().equals(d.getOldValue())) {
                                    d.setOldValue("--");
                                }
                            }
                        }
                        if (org.apache.commons.lang3.StringUtils.isNumeric(d.getNewValue())) {
                            d.setNewValue(CommonSwitchEnum.getByCode(Integer.parseInt(d.getNewValue())).getDesc());
                            if (!CollectionUtils.isEmpty(isAllAdSearchUnitDiff)) {
                                String newValue = isAllAdSearchUnitDiff.get(0).getNewValue();
                                if ("0".equals(newValue) && CommonSwitchEnum.DISABLED.getDesc().equals(d.getNewValue())) {
                                    d.setNewValue("--");
                                }
                            } else if (!CollectionUtils.isEmpty(hasKeywords)) {
                                String newValue = hasKeywords.get(0).getNewValue();
                                if (StringUtils.isBlank(newValue) && CommonSwitchEnum.DISABLED.getDesc().equals(d.getNewValue())) {
                                    d.setNewValue("--");
                                }
                            }
                        }
                        break;
                    case "smartKeyWord":
                        if (org.apache.commons.lang3.StringUtils.isNumeric(d.getOldValue())) {
                            d.setOldValue(CommonSwitchEnum.getByCode(Integer.parseInt(d.getOldValue())).getDesc());
                        }
                        if (org.apache.commons.lang3.StringUtils.isNumeric(d.getNewValue())) {
                            d.setNewValue(CommonSwitchEnum.getByCode(Integer.parseInt(d.getNewValue())).getDesc());
                        }
                        break;
                    case "searchPriceCoefficient":
                        if (org.apache.commons.lang3.StringUtils.isNumeric(d.getOldValue())) {
                            Integer oldValue = Integer.parseInt(d.getOldValue());
                            d.setOldValue(oldValue <= 0 ? "--" : Utils.fromFenToYuan(oldValue).toString());
                        }
                        if (org.apache.commons.lang3.StringUtils.isNumeric(d.getNewValue())) {
                            Integer newValue = Integer.parseInt(d.getNewValue());
                            d.setNewValue(newValue <= 0 ? "--" : Utils.fromFenToYuan(newValue).toString());
                        }
                        break;
                    case "searchFirstPriceCoefficient":
                        if (org.apache.commons.lang3.StringUtils.isNumeric(d.getOldValue())) {
                            Integer oldValue = Integer.parseInt(d.getOldValue());
                            d.setOldValue(oldValue <= 0 ? "--" : Utils.fromFenToYuan(oldValue).toString());
                        }
                        if (org.apache.commons.lang3.StringUtils.isNumeric(d.getNewValue())) {
                            Integer newValue = Integer.parseInt(d.getNewValue());
                            d.setNewValue(newValue <= 0 ? "--" : Utils.fromFenToYuan(newValue).toString());
                        }
                        break;
                }

                if (d.getOldValue().equals(d.getNewValue())) {
                    it.remove();
                }
            }

            if (!CollectionUtils.isEmpty(otherDiffs)) {
                diffs.addAll(otherDiffs);
            }

            if (CollectionUtils.isEmpty(dto.getValue())) {
                dit.remove();
            }
        }
    }

    private Map<Integer, String> getShopGoodsMap(Integer accountId, List<Integer> shopGoodsIds) {
        Map<Integer, String> shopGoodsMap = Collections.emptyMap();
        try {
            if (!CollectionUtils.isEmpty(shopGoodsIds)) {
                AccountBaseDto accountBaseDto = queryAccountService.getAccountBaseDtoById(accountId);
                List<DpaShopGoodsDto> shopGoodsList = queryShopGoodsService.getDpaShopGoodsList(
                        QueryDpaShopGoodsParam.builder().mid(accountBaseDto.getMid()).build());
                shopGoodsMap = shopGoodsList.stream().collect(Collectors.toMap(DpaShopGoodsDto::getGoodsId, DpaShopGoodsDto::getName));
            }
        } catch (Exception e) {
            LOGGER.error("query shop goods failed");
        }

        return shopGoodsMap;
    }

    private Map<Integer, String> getGameMap(Integer accountId, List<Integer> gameBaseIds) {
        Map<Integer, String> gameDtoMap = Collections.emptyMap();
        try {
            if (!CollectionUtils.isEmpty(gameBaseIds)) {
                AccountBaseDto accountBaseDto = queryAccountService.getAccountBaseDtoById(accountId);
                List<GameBaseDto> gameDtos = gameCenterService.getGameBaseDtosByMid(accountBaseDto.getMid());
                gameDtoMap = gameDtos.stream().collect(Collectors.toMap(GameBaseDto::getGameBaseId, GameBaseDto::getGameName));
            }
        } catch (Exception e) {
            LOGGER.error("query game via accountId: [{}] failed", accountId);
        }

        return gameDtoMap;
    }

    private long getDays(String date1, String date2) {
        if (date1 == null || date1.equals(""))
            return 0;
        if (date2 == null || date2.equals(""))
            return 0;
        // 转换为标准时间
        SimpleDateFormat myFormatter = new SimpleDateFormat("yyyy-MM-dd");
        Date date = null;
        Date mydate = null;
        try {
            date = myFormatter.parse(date1);
            mydate = myFormatter.parse(date2);
        } catch (Exception e) {
            return 0;
        }
        long day = (date.getTime() - mydate.getTime()) / (24 * 60 * 60 * 1000);
        return day;
    }

    private String getTargetItemNamesString(String value, Map<Integer, String> cpgMap) {
        String result = "";

        if (Strings.isNullOrEmpty(value)) {
            return result;
        }

        try {
            List<Integer> ids = JSON.parseArray(value, Integer.class);

            if (CollectionUtils.isEmpty(ids)) {
                return result;
            }

            return ids.stream()
                    .map(id -> cpgMap == null || id == null ? String.valueOf(id) : cpgMap.getOrDefault(id, String.valueOf(id)))
                    .collect(Collectors.joining(","));
        } catch (Exception e) {
            return value;
        }
    }

    private String getNameById(String value, Map<Integer, String> idNameMap) {
        String result = "";

        try {
            int id = Integer.parseInt(value);
            result = idNameMap.get(id);
        } catch (Exception e) {
        }

        if (Strings.isNullOrEmpty(result)) {
            result = Strings.isNullOrEmpty(value) ? "" : value;
        }

        return result;
    }

    private String getAppNameString(String value, Map<Integer, String> appNameMap) {
        String result = "";

        try {
            int id = Integer.parseInt(value);
            result = appNameMap.get(id);
        } catch (Exception e) {
        }

        if (Strings.isNullOrEmpty(result)) {
            result = Strings.isNullOrEmpty(value) ? "" : "应用包不存在(id=" + value + ")";
        }

        return result;
    }

    private String getGameNameString(String value, Map<Integer, String> gameNameMap) {
        String result = "";

        try {
            int id = Integer.parseInt(value);
            result = gameNameMap.get(id);
        } catch (Exception e) {
        }

        if (Strings.isNullOrEmpty(result)) {
            result = Strings.isNullOrEmpty(value) ? "" : "游戏(id=" + value + ")不存在";
        }

        return result;
    }

    private List<String> getStringArray(String str) {
        if (Strings.isNullOrEmpty(str)) {
            return Collections.emptyList();
        }

        try {
            return JSON.parseArray(str, String.class);
        } catch (Exception e) {
            LOGGER.error("getStringArray exception:", e);
            return Collections.emptyList();
        }
    }

    private List<DiffItem> genOtherCrowdPackGroupDiff(List<String> oldGroup, List<String> newGroup, Map<Integer, String> cpNameMap) {
        if (CollectionUtils.isEmpty(oldGroup) && CollectionUtils.isEmpty(newGroup)) {
            return Collections.emptyList();
        }

        int len = oldGroup.size() > newGroup.size() ? oldGroup.size() : newGroup.size();
        String oldIds, newIds;
        List<DiffItem> result = Lists.newArrayList();

        for (int i = 0; i < len; i++) {
            oldIds = oldGroup.size() > i ? oldGroup.get(i) : "";
            newIds = newGroup.size() > i ? newGroup.get(i) : "";

            String oldValue = getTargetItemNamesString(oldIds, cpNameMap);
            String newValue = getTargetItemNamesString(newIds, cpNameMap);

            if (!Objects.equals(oldValue, newValue)) {
                result.add(DiffItem
                        .builder()
                        .desc("投放人群包" + (i + 1))
                        .key("otherCrowdPackIdsGroup")
                        .newValue(newValue)
                        .oldValue(oldValue)
                        .build());
            }
        }

        return result;
    }

    private static final Pattern TARGET_TAG_PATTEN = Pattern.compile("tags=(null|(\\[(.*)\\])), isFuzzyTags=(null|(\\d*))");

    private List<DiffItem> genVideoTagDiff(String oldValue, String newValue) {
        List<DiffItem> result = Lists.newArrayList();

        Matcher oldMatcher = TARGET_TAG_PATTEN.matcher(oldValue);
        boolean oldFound = oldMatcher.find();
        String oldVideoTags = Strings.emptyToNull(oldFound ? oldMatcher.group(3) : null);
        String oldIsFuzzyTagsStr = Strings.emptyToNull(oldFound ? oldMatcher.group(5) : null);
        Integer oldIsFuzzyTags = oldIsFuzzyTagsStr == null ? null : Integer.valueOf(oldIsFuzzyTagsStr);

        Matcher newMatcher = TARGET_TAG_PATTEN.matcher(newValue);
        boolean newFound = newMatcher.find();
        String newVideoTags = Strings.emptyToNull(newFound ? newMatcher.group(3) : null);
        String newIsFuzzyTagsStr = Strings.emptyToNull(newFound ? newMatcher.group(5) : null);
        Integer newIsFuzzyTags = newIsFuzzyTagsStr == null ? null : Integer.valueOf(newIsFuzzyTagsStr);

        if (!Objects.equals(oldIsFuzzyTags, newIsFuzzyTags)) {
            result.add(DiffItem
                    .builder()
                    .desc("视频关键词是否是模糊匹配")
                    .key("videoIsFuzzyTags")
                    .oldValue(parseEnum(oldIsFuzzyTags, LauTagIdsTargetType::getByCode, LauTagIdsTargetType::getDesc))
                    .newValue(parseEnum(newIsFuzzyTags, LauTagIdsTargetType::getByCode, LauTagIdsTargetType::getDesc))
                    .build());
        }

        if (!Objects.equals(oldVideoTags, newVideoTags)) {
            result.add(DiffItem
                    .builder()
                    .desc("视频关键词")
                    .key("videoTags")
                    .oldValue(oldVideoTags)
                    .newValue(newVideoTags)
                    .build());
        }

        return result;
    }

    private UnitTargetTagDto genUnitTargetTagDto(String str) {
        if (Strings.isNullOrEmpty(str)) {
            return null;
        }

        String s = str.replace("UnitTargetTagDto", "").replace('(', '{').replace(')', '}').replace('=', ':').replace("tags", "'tags'").replace("isFuzzyTags", "'isFuzzyTags'").replaceAll(", type:[A-Z]*", "");

        try {
            return JSON.parseObject(s, UnitTargetTagDto.class);
        } catch (Exception e) {
            return null;
        }
    }

    private String getProductGroupName(String value, Map<Integer, String> productGroupNameMap) {
        if (NumberUtils.isDigits(value)) {
            try {
                return productGroupNameMap.getOrDefault(Integer.valueOf(value), "");
            } catch (Exception e) {
                LOGGER.error("getProductGroupName error", e);
            }
        }
        return "";
    }

    private String getInstalledUserFilter(String value) {
        if (Strings.isNullOrEmpty(value)) {
            return "";
        }
        try {
            UnitTargetInstalledUserFilterDto dto = JSON.parseObject(value, UnitTargetInstalledUserFilterDto.class);
            StringBuilder sb = new StringBuilder();
            InstalledUserFilterEnum filterEnum = InstalledUserFilterEnum.getByCodeOptional(dto.getFilterType()).orElse(InstalledUserFilterEnum.UNLIMIT);
            sb.append(filterEnum.getName());
            if (!CollectionUtils.isEmpty(dto.getTargetContent())) {
                sb.append(",定向项：");
                dto.getTargetContent().stream().forEach(targetInt -> {
                    InstalledUserTargetContentEnum targetContentEnum = InstalledUserTargetContentEnum.getByCodeOptional(targetInt).orElse(null);
                    sb.append(targetContentEnum == null ? "" : targetContentEnum.getName());
                    sb.append("、");
                });
            }
            String result = sb.toString();
            return result.endsWith("、") ? result.substring(0, result.length() - 1) : result;
        } catch (Exception e) {
            LOGGER.error("getInstalledUserFilter error", e);
        }
        return "";
    }
}
