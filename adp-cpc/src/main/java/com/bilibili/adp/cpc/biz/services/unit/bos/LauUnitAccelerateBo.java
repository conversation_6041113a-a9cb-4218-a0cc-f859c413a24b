package com.bilibili.adp.cpc.biz.services.unit.bos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LauUnitAccelerateBo {

    /**
     * 起量预算
     */
    private Long accelerateBudget;

    /**
     * 起量开启时间
     */
    private java.sql.Timestamp accelerateEndTime;

    /**
     * 起量结束时间
     */
    private java.sql.Timestamp accelerateStartTime;

    /**
     * 起量状态，0未开启，1开启中，2已结束
     */
    private Integer accelerateStatus;

    /**
     * 起量强度，1弱，2强
     */
    private Integer accelerateType;

    private java.sql.Timestamp ctime;

    private java.sql.Timestamp mtime;

    /**
     * 单元id
     */
    private Integer unitId;
}
