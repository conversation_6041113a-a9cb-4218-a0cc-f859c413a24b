package com.bilibili.adp.cpc.config.databus.creative;

import com.alibaba.fastjson.JSONObject;
import com.bilibili.adp.cpc.biz.bos.WarpDatabusProperty;
import com.bilibili.adp.cpc.biz.constants.Constants;
import com.bilibili.adp.util.common.AdpCatUtils;
import com.bilibili.warp.databus.DatabusProperty;
import com.bilibili.warp.databus.DatabusTemplate;
import com.bilibili.warp.databus.Message;
import com.bilibili.warp.databus.PubResult;
import com.site.lookup.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import pleiades.component.databus.model.DatabusProperties;
import pleiades.component.databus.pub.DatabusPub;
import pleiades.venus.naming.client.NamingClient;

import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * @ClassName CmGameCreativeInfoPubDatabusConfig
 * <AUTHOR>
 * @Date 2023/11/29 8:33 下午
 * @Version 1.0
 **/
@Service
@Slf4j
public class CmGameCreativeInfoPubDatabusService {

    public static final String CM_CREATIVE_GAME_INFO = "cm-creative-game-info";
    private final String topic;
    private final String group;

    @Autowired
    private DatabusTemplate databusTemplate;

    public CmGameCreativeInfoPubDatabusService(WarpDatabusProperty warpDatabusProperty) {
        // 注入DatabusProperties，此处的ALIAS常量即配置文件中填写的别名，是topic的上一级配置键
        // pub场景同理，在使用DatabusTemplate的地方注入DatabusProperties来获取
        Map<String, DatabusProperty> properties = warpDatabusProperty.getProperties();
        DatabusProperty property = properties.get(CM_CREATIVE_GAME_INFO);
        log.info("CmArchivePubService, property={}", JSONObject.toJSONString(property));
        this.topic = property.getTopic();
        this.group = property.getPub().getGroup();
    }

    public void pubMsg(String msg) {
        if (StringUtils.isEmpty(msg)) {
            return;
        }

        AdpCatUtils.newTransaction(Constants.CAT_TYPE_DATABUS, CM_CREATIVE_GAME_INFO + ":pub", transaction -> {
            // messageKey和value自定义，value会被配置的serializer序列化
            Message message = Message.Builder.of(UUID.randomUUID().toString(), msg)
                    .build();
            // topic 和 group 按照申请的填写即可. 可以自行注入相关配置，用配置的方式来发送消息
            PubResult result = databusTemplate.pub(topic, group, message);
            if (result.isSuccess()) {
                log.info("CmGameCreativeInfoPubDatabusService pubMsg success, msg={}", msg);
            } else {
                Throwable throwable = result.getThrowable();
                log.error("CmGameCreativeInfoPubDatabusService pub msg error, msg={}, e={}", msg, throwable);
            }
        });

    }

    public void testPub(String msg) {
        // messageKey和value自定义，value会被配置的serializer序列化
        Message message = Message.Builder.of(UUID.randomUUID().toString(), msg)
                .build();
        // topic 和 group 按照申请的填写即可. 可以自行注入相关配置，用配置的方式来发送消息
        PubResult result = databusTemplate.pub(topic, group, message);
        if (result.isSuccess()) {
            log.info("CmGameCreativeInfoPubDatabusService pubMsg success, msg={}", msg);
        }
    }

}
