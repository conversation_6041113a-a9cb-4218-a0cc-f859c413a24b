package com.bilibili.adp.cpc.databus;

import com.bilibili.adp.cpc.databus.bos.LogCpcOperationMessage;
import com.bilibili.adp.log.service.v6.bo.LogCpcOperationBo;
import com.bilibili.warp.databus.DatabusProperty;
import com.bilibili.warp.databus.DatabusTemplate;
import com.bilibili.warp.databus.Message;
import com.bilibili.warp.databus.PubResult;
import com.bilibili.warp.spring.boot.autoconfigure.databus.DatabusProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * 日志写入逻辑推送
 *
 * <AUTHOR>
 * @Description
 * @date 4/10/24
 **/
@Slf4j
@Service
public class LogCpcOperationPub {

    @Resource
    private DatabusTemplate databusTemplate;

    public static final String LOG_CPC_OPERATION = "log-cpc-operation";
    private final String topic;
    private final String group;

    public LogCpcOperationPub(DatabusProperties databusProperties) {
        DatabusProperty databusProperty = databusProperties.getProperties().get(LOG_CPC_OPERATION);
        this.topic = databusProperty.getTopic();
        this.group = databusProperty.getPub().getGroup();
    }

    public void pub(List<LogCpcOperationBo> bos) {


        if (CollectionUtils.isEmpty(bos)) {
            return;
        }
        LogCpcOperationMessage logCpcOperationMessage = new LogCpcOperationMessage();
        logCpcOperationMessage.setLogCpcOperationBos(bos);

        log.info("log-cpc-operation  pub");
        // messageKey和value自定义，value会被配置的serializer序列化
        Message message = Message.Builder.of(bos.get(0).getAccountId() + "", logCpcOperationMessage)
                .build();
        // topic 和 group 按照申请的填写即可. 可以自行注入相关配置，用配置的方式来发送消息
        PubResult result = databusTemplate.pub(topic, group, message);
        if (result.isSuccess()) {
            log.info("log-cpc-operation, msg.size={}", bos.size());
        } else {
            Throwable throwable = result.getThrowable();
            log.error("log-cpc-operation err ", throwable);
            throw new RuntimeException(throwable.getMessage());
        }
    }

}
