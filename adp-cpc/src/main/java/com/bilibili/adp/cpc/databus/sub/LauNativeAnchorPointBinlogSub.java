package com.bilibili.adp.cpc.databus.sub;

import com.alibaba.fastjson.JSONObject;
import com.bapis.ad.scv.anchor.AnchorAuditStatus;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.biz.bos.WarpDatabusProperty;
import com.bilibili.adp.cpc.biz.constants.Constants;
import com.bilibili.adp.cpc.biz.services.creative.business.content.BusinessContentService;
import com.bilibili.adp.cpc.dao.querydsl.pos.LauArchiveAnchorPointPo;
import com.bilibili.adp.cpc.databus.Constant;
import com.bilibili.adp.util.common.AdpCatUtils;
import com.bilibili.warp.databus.AckableMessage;
import com.bilibili.warp.databus.DatabusProperty;
import com.bilibili.warp.databus.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;

/**
 * 原生锚点 binlog 消费
 **/
@Component
@Slf4j
public class LauNativeAnchorPointBinlogSub implements MessageListener {

    @Value("${databus.consume.switch.nativeAnchorPoint.enable:1}")
    private Integer switchEnable;

    private final String topic;
    private final String group;

    @Resource
    private BusinessContentService businessContentService;

    @Override
    public String topic() {
        return this.topic;
    }

    @Override
    public String group() {
        return this.group;
    }

    public LauNativeAnchorPointBinlogSub(WarpDatabusProperty warpDatabusProperty) {
        // 注入DatabusProperties，此处的ALIAS常量即配置文件中填写的别名，是topic的上一级配置键
        // pub场景同理，在使用DatabusTemplate的地方注入DatabusProperties来获取
        Map<String, DatabusProperty> properties = warpDatabusProperty.getProperties();
        DatabusProperty property = properties.get("native-anchor-point");
        log.info("native-anchor-point, property={}", JSONObject.toJSONString(property));
        this.topic = property.getTopic();
        this.group = property.getSub().getGroup();
    }

    @Override
    public void onMessage(AckableMessage ackableMessage) {

        try {
            if (!Utils.isPositive(switchEnable)) {
                return;
            }

            String value = new String(ackableMessage.payload());
            JSONObject msg = JSONObject.parseObject(value);

            String action = msg.getString(Constant.ACTION);
            if (Constant.DELETE.equals(action) || action == null) {
                return;
            }

            JSONObject oldObject = msg.getJSONObject(Constant.OLD);
            LauArchiveAnchorPointPo oldBo = deserializeBinlogDto(oldObject);
            JSONObject newObject = msg.getJSONObject(Constant.NEW);
            LauArchiveAnchorPointPo newBo = deserializeBinlogDto(newObject);

            // 如果是修改，但是审核状态不变，或者不是待审，则不送审
            if (Constant.UPDATE.equals(action) && oldBo != null && newBo != null) {
                if (Objects.equals(oldBo.getAuditStatus(), newBo.getAuditStatus())) {
                    return;
                }
                if (!Objects.equals(newBo.getAuditStatus(), AnchorAuditStatus.AUDITING.getNumber())) {
                    return;
                }
            }

            // 推送给聚合服务
            AdpCatUtils.newTransaction(Constants.CAT_TYPE_DATABUS,  "native-anchor-point-binlog:sub", transaction -> {
                log.info("anchor point push native exempt video, action={}, avid:{},newAuditStatus={}", action, newBo.getAid(), newBo.getAuditStatus());
                businessContentService.pubNativeExemptVideo(newBo.getAid(), newBo.getAccountId(), "锚点");
            });
            ackableMessage.ack();
        } catch (Exception e) {
            log.info("info {}", ackableMessage);
            log.error("LauNativeAnchorPointBinlogSub sub error ", e);
        }
    }


    private LauArchiveAnchorPointPo deserializeBinlogDto(JSONObject jsonObject) {
        if (Objects.isNull(jsonObject)) {
            return null;
        }
        return jsonObject.toJavaObject(LauArchiveAnchorPointPo.class);
    }

}
