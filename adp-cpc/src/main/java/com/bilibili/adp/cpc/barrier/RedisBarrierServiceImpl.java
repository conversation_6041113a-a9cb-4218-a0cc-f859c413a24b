package com.bilibili.adp.cpc.barrier;

import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.Objects;

@Service
@RequiredArgsConstructor
public class RedisBarrierServiceImpl implements IBarrierService {
    @Autowired
    @Qualifier("stringValueRedisTemplate")
    private RedisTemplate<String, String> redisTemplate;

    @Override
    public boolean setBarrierIfAbsent(final String key, Duration duration) {
        final Boolean ok = redisTemplate.opsForValue().setIfAbsent(key, "1", duration);
        if (Objects.isNull(ok)) throw new IllegalStateException("setting barrier failed");

        return ok;
    }
}