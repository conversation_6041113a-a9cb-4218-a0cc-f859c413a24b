package com.bilibili.adp.cpc.databus.sub;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.bilibili.adp.cpc.biz.bos.WarpDatabusProperty;
import com.bilibili.adp.cpc.biz.services.ad_product.IAdProductService;
import com.bilibili.adp.cpc.databus.bos.SdpaGameProductDatabusBo;
import com.bilibili.warp.databus.AckableMessage;
import com.bilibili.warp.databus.DatabusProperty;
import com.bilibili.warp.databus.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;


@Slf4j
public class SdpaGameProductSub implements MessageListener {

    @Autowired
    private IAdProductService adProductService;


    private final String topic;

    private final String group;


    public SdpaGameProductSub(WarpDatabusProperty warpDatabusProperty) {
        // 注入DatabusProperties，此处的ALIAS常量即配置文件中填写的别名，是topic的上一级配置键
        // pub场景同理，在使用DatabusTemplate的地方注入DatabusProperties来获取
        Map<String, DatabusProperty> properties = warpDatabusProperty.getProperties();
        DatabusProperty property = properties.get("sdpa_game_product");
        log.info("sdpa_game_product, property={}", JSONObject.toJSONString(property));
        this.topic = property.getTopic();
        this.group = property.getSub().getGroup();
    }


    @Override
    public String topic() {
        return this.topic;
    }

    @Override
    public String group() {
        return this.group;
    }

    @Override
    public void onMessage(AckableMessage message) {
        String msg = new String(message.payload());
        log.info("BiliGameSdpaProductSub onMessage: {}", msg);
        SdpaGameProductDatabusBo sdpaGameProductDatabusBo = JSON.parseObject(msg, new TypeReference<SdpaGameProductDatabusBo>() {
        });

        adProductService.createOtherGameProduct(sdpaGameProductDatabusBo);

        message.ack();
    }
}
