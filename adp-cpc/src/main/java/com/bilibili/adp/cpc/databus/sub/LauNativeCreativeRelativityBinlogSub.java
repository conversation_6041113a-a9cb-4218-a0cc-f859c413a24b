package com.bilibili.adp.cpc.databus.sub;

import com.alibaba.fastjson.JSONObject;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.biz.bos.WarpDatabusProperty;
import com.bilibili.adp.cpc.biz.constants.Constants;
import com.bilibili.adp.cpc.biz.services.creative.business.content.BusinessContentService;
import com.bilibili.adp.cpc.core.constants.NativeBodyType;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauCreativeNativeRelativityPo;
import com.bilibili.adp.cpc.databus.Constant;
import com.bilibili.adp.util.common.AdpCatUtils;
import com.bilibili.warp.databus.AckableMessage;
import com.bilibili.warp.databus.DatabusProperty;
import com.bilibili.warp.databus.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;

/**
 * 原生稿件 binlog 消费
 **/
@Component
@Slf4j
public class LauNativeCreativeRelativityBinlogSub implements MessageListener {

    @Value("${databus.consume.switch.nativeCreativeRelativity.enable:1}")
    private Integer switchEnable;

    private final String topic;
    private final String group;

    @Resource
    private BusinessContentService businessContentService;

    @Override
    public String topic() {
        return this.topic;
    }

    @Override
    public String group() {
        return this.group;
    }

    public LauNativeCreativeRelativityBinlogSub(WarpDatabusProperty warpDatabusProperty) {
        // 注入DatabusProperties，此处的ALIAS常量即配置文件中填写的别名，是topic的上一级配置键
        // pub场景同理，在使用DatabusTemplate的地方注入DatabusProperties来获取
        Map<String, DatabusProperty> properties = warpDatabusProperty.getProperties();
        DatabusProperty property = properties.get("native-creative-relativity");
        log.info("native-creative-relativity, property={}", JSONObject.toJSONString(property));
        this.topic = property.getTopic();
        this.group = property.getSub().getGroup();
    }

    @Override
    public void onMessage(AckableMessage ackableMessage) {

        try {
            if (!Utils.isPositive(switchEnable)) {
                return;
            }

            String value = new String(ackableMessage.payload());
            JSONObject msg = JSONObject.parseObject(value);

            String action = msg.getString(Constant.ACTION);
            if (!Constant.INSERT.equals(action)) {
                return;
            }

            JSONObject oldObject = msg.getJSONObject(Constant.OLD);
            LauCreativeNativeRelativityPo oldBo = deserializeBinlogDto(oldObject);
            JSONObject newObject = msg.getJSONObject(Constant.NEW);
            LauCreativeNativeRelativityPo newBo = deserializeBinlogDto(newObject);

            if (!Objects.equals(newBo.getType(), NativeBodyType.ARCHIVE)) {
                log.info("pubNativeExemptVideoForCreative fileter, not archive, creativeId={},avid={}", newBo.getCreativeId(), newBo.getAvid());
                return;
            }

            // 推送给聚合服务
            AdpCatUtils.newTransaction(Constants.CAT_TYPE_DATABUS,  "native-creative-relativity-binlog:sub", transaction -> {
                businessContentService.pubNativeExemptVideoForCreative(newBo.getAvid(), newBo.getCreativeId());
            });
            ackableMessage.ack();
        } catch (Exception e) {
            log.info("info {}", ackableMessage);
            log.error("LauNativeCreativeRelativityBinlogSub sub error ", e);
        }
    }


    private LauCreativeNativeRelativityPo deserializeBinlogDto(JSONObject jsonObject) {
        if (Objects.isNull(jsonObject)) {
            return null;
        }
        return jsonObject.toJavaObject(LauCreativeNativeRelativityPo.class);
    }

}
