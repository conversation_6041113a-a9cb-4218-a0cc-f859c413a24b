package com.bilibili.adp.cpc.config.jinghuo;

import com.jd.open.api.sdk.DefaultJdClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2024/10/8
 * @description 京火服客户端配置
 */
@Configuration
public class JhClientConfig {
    @Value("${jinghuo.client.server.url:https://api-dev.jd.com/routerjson}")
    private String serverUrl;

    @Value("${jinghuo.client.app.key:36c72286958a43a79d7aa1747584f6b8}")
    private String appKey;

    @Value("${jinghuo.client.app.secret:27664328e3684aa0a776ecc3d4c51417}")
    private String appSecret;

    @Bean
    public DefaultJdClient jhClient(){
        return new DefaultJdClient(serverUrl,null, appKey, appSecret);
    }

}
