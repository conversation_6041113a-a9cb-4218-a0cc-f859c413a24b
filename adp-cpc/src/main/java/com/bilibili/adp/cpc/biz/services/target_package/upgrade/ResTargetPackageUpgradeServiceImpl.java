package com.bilibili.adp.cpc.biz.services.target_package.upgrade;

import com.bilibili.adp.common.Constants;
import com.bilibili.adp.common.DbTable;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.bean.TargetRule;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.enums.LauTagType;
import com.bilibili.adp.common.enums.LauTargetTagEnum;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.CollectionHelper;
import com.bilibili.adp.common.util.Page;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.biz.services.campaign.ManagedCampaignService;
import com.bilibili.adp.cpc.biz.services.target_package.api.ICrowdPackService;
import com.bilibili.adp.cpc.biz.services.target_package.api.upgrade.*;
import com.bilibili.adp.cpc.biz.services.target_package.api.upgrade.dto.QueryTargetPackageUpgradeDto;
import com.bilibili.adp.cpc.biz.services.target_package.api.upgrade.dto.TargetPackageInstalledUserFilterDto;
import com.bilibili.adp.cpc.biz.services.target_package.api.upgrade.dto.TargetPackageUpgradeDto;
import com.bilibili.adp.cpc.biz.services.target_package.api.upgrade.dto.*;
import com.bilibili.adp.cpc.biz.services.unit.CpcUnitServiceDelegate;
import com.bilibili.adp.cpc.biz.services.unit.api.ICpcUnitService;
import com.bilibili.adp.cpc.biz.services.unit.api.IHystrixDmpService;
import com.bilibili.adp.cpc.biz.services.unit.bos.UnitBaseNameBo;
import com.bilibili.adp.cpc.biz.services.unit.dto.QueryCpcUnitDto;
import com.bilibili.adp.cpc.biz.validator.InstalledUserTargetValidator;
import com.bilibili.adp.cpc.core.constants.IsInterestAuto;
import com.bilibili.adp.cpc.dao.querydsl.pos.ResTargetPackageUpgradePo;
import com.bilibili.adp.cpc.dto.CpcLightUnitDto;
import com.bilibili.adp.cpc.dto.CpcUnitExtraTargetDto;
import com.bilibili.adp.cpc.dto.UnitTargetInstalledUserFilterDto;
import com.bilibili.adp.cpc.enums.AdpVersion;
import com.bilibili.adp.cpc.enums.InstalledUserFilterEnum;
import com.bilibili.adp.cpc.enums.ad.PromotionPurposeType;
import com.bilibili.adp.cpc.po.ad.ResTargetPackageProfessionInterestAutoUpgradePo;
import com.bilibili.adp.launch.api.common.UnitStatus;
import com.bilibili.adp.launch.api.service.ILauTagIdsService;
import com.bilibili.adp.launch.api.service.IUnitTargetRuleService;
import com.bilibili.adp.launch.api.unit.dto.LauTagIdsDto;
import com.bilibili.adp.launch.api.unit.dto.UnitTargetTagDto;
import com.bilibili.adp.launch.biz.ad_core.mybatis.LauTagIdsDao;
import com.bilibili.adp.launch.biz.pojo.LauTagIdsPo;
import com.bilibili.adp.log.service.ILogOperateService;
import com.bilibili.adp.resource.api.target_package.upgrade.dto.*;
import com.bilibili.adp.resource.api.targetmeta.TargetType;
import com.bilibili.adp.resource.biz.config.ResourceDaoConfig;
import com.bilibili.adp.resource.biz.utils.target_package.TargetPackageUpgradeUtil;
import com.bilibili.adp.util.common.DistributedLock;
import com.bilibili.bjcom.querydsl.BaseQueryFactory;
import com.bilibili.bjcom.querydsl.clause.BaseQuery;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import net.logstash.logback.encoder.org.apache.commons.lang.exception.ExceptionUtils;
import org.redisson.api.RLock;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.bilibili.adp.cpc.dao.querydsl.QResTargetPackageUpgrade.resTargetPackageUpgrade;

/**
 * @ClassName ResTargetPackageUpgradeServiceImpl
 * <AUTHOR>
 * @Date 2023/1/16 3:06 下午
 * @Version 1.0
 * <p>
 * copy class
 * @see com.bilibili.adp.resource.biz.service.target_upgrade.ResTargetPackageUpgradeServiceImpl
 **/
@Slf4j
@Service(value = "LaunchResTargetPackageUpgradeService")
@Primary
public class ResTargetPackageUpgradeServiceImpl implements IResTargetPackageUpgradeService {

    @Value("${cpc.fans.target.max:50}")
    private Integer cpcFansTargetMax;

    private final ICpcUnitService cpcUnitService;
    private final ILauTagIdsService lauTagIdsService;
    private final ILogOperateService logOperateService;
    private final IResTargetPackageRuleUpgradeService resTargetPackageRuleService;
    private final IResTargetPackageExtraTargetService resTargetPackageExtraTargetService;
    private final IResTargetPackageBusinessInterestService resTargetPackageBusinessInterestService;
    private final IResTargetPackageCrowdUpgradeService resTargetPackageCrowdUpgradeService;
    private final IResTargetPackageInstalledUserFilterService resTargetPackageInstalledUserFilterService;
    private final IResTargetPackageMinigameService resTargetPackageMinigameService;
    private final ICrowdPackService crowdPackService;
    private final IHystrixDmpService hystrixDmpService;
    private final ManagedCampaignService managedCampaignService;
    private final IResTargetPackageOsVersionUpgradeService resTargetPackageOsVersionUpgradeService;
    private final IResTargetPackageBiliClientUpgradeService resTargetPackageBiliClientUpgradeService;
    private final InstalledUserTargetValidator installedUserTargetValidator;

    private final IResTargetPackageProfessionUpgradeService resTargetPackageProfessionUpgradeService;

    private final IResTargetPackageProfessionAutoUpgradeService resTargetPackageProfessionAutoUpgradeService;

    private final BaseQueryFactory resBqf;
    private final DistributedLock distributedLock;
    private final LauTagIdsDao tagIdsDao;


    @Autowired
    private CpcUnitServiceDelegate cpcUnitServiceDelegate;
    @Autowired
    private IUnitTargetRuleService unitTargetRuleService;

    private final List<Integer> FLY_PROMOTION_PURPOSE_TYPE_LIST = Lists.newArrayList(
            PromotionPurposeType.BRAND_SPREAD.getCode(),
            PromotionPurposeType.ENTERPRISE_PROMOTION.getCode());
    private final List<Integer> INTELLIGENT_MASS_INVALID_PURPOSE_TYPE_LIST = Lists.newArrayList(
            PromotionPurposeType.BRAND_SPREAD.getCode(),
            PromotionPurposeType.ENTERPRISE_PROMOTION.getCode());

    public ResTargetPackageUpgradeServiceImpl(@Qualifier(ResourceDaoConfig.FACTORY_NAME) BaseQueryFactory resBqf,
                                              DistributedLock distributedLock,
                                              ICpcUnitService cpcUnitService,
                                              ILauTagIdsService lauTagIdsService,
                                              ILogOperateService logOperateService,
                                              IResTargetPackageRuleUpgradeService resTargetPackageRuleService,
                                              IResTargetPackageExtraTargetService resTargetPackageExtraTargetService,
                                              IResTargetPackageBusinessInterestService resTargetPackageBusinessInterestService,
                                              IResTargetPackageCrowdUpgradeService resTargetPackageCrowdUpgradeService,
                                              IResTargetPackageInstalledUserFilterService resTargetPackageInstalledUserFilterService,
                                              IResTargetPackageMinigameService resTargetPackageMinigameService,
                                              ICrowdPackService crowdPackService,
                                              IHystrixDmpService hystrixDmpService,
                                              IResTargetPackageOsVersionUpgradeService resTargetPackageOsVersionUpgradeService,
                                              IResTargetPackageBiliClientUpgradeService resTargetPackageBiliClientUpgradeService,
                                              ManagedCampaignService managedCampaignService, InstalledUserTargetValidator installedUserTargetValidator,
                                              IResTargetPackageProfessionUpgradeService resTargetPackageProfessionUpgradeService,
                                              IResTargetPackageProfessionAutoUpgradeService resTargetPackageProfessionAutoUpgradeService,
                                              LauTagIdsDao tagIdsDao) {
        this.resBqf = resBqf;
        this.distributedLock = distributedLock;
        this.cpcUnitService = cpcUnitService;
        this.logOperateService = logOperateService;
        this.resTargetPackageRuleService = resTargetPackageRuleService;
        this.resTargetPackageExtraTargetService = resTargetPackageExtraTargetService;
        this.resTargetPackageBusinessInterestService = resTargetPackageBusinessInterestService;
        this.resTargetPackageCrowdUpgradeService = resTargetPackageCrowdUpgradeService;
        this.resTargetPackageInstalledUserFilterService = resTargetPackageInstalledUserFilterService;
        this.resTargetPackageMinigameService = resTargetPackageMinigameService;
        this.lauTagIdsService = lauTagIdsService;
        this.crowdPackService = crowdPackService;
        this.hystrixDmpService = hystrixDmpService;
        this.resTargetPackageOsVersionUpgradeService = resTargetPackageOsVersionUpgradeService;
        this.resTargetPackageBiliClientUpgradeService = resTargetPackageBiliClientUpgradeService;
        this.managedCampaignService = managedCampaignService;
        this.installedUserTargetValidator = installedUserTargetValidator;
        this.resTargetPackageProfessionUpgradeService = resTargetPackageProfessionUpgradeService;
        this.resTargetPackageProfessionAutoUpgradeService = resTargetPackageProfessionAutoUpgradeService;
        this.tagIdsDao = tagIdsDao;
    }

    private void validateTargetPackageUpgradeBase(TargetPackageUpgradeDto dto, Operator operator, boolean isUpdate) {
        // 各种初始校验条件
        if (isUpdate) {
            Assert.isTrue(Utils.isPositive(dto.getId()), "更新定向包id不可为空");
            Integer existId = getExistByIdAndAccount(dto.getId(), operator.getOperatorId());
            Assert.isTrue(Utils.isPositive(existId), "该定向包不存在,更新失败");
        } else {
            Assert.isTrue(!Utils.isPositive(dto.getId()), "新建时定向包id必须为空");
        }

        Assert.isTrue(!Operator.validateParamIsNull(operator), "操作人不能为空");
        Assert.isTrue(Utils.isPositive(operator.getOperatorId()), "操作人Id不可为空");
        Assert.hasText(dto.getName(), "定向包名称不能为空");
        if (AdpVersion.isMiddle(dto.getAdpVersion())) {
            Assert.isTrue(Utils.isPositive(dto.getPromotionPurposeType()), "旧三连定向包推广类型不能为空");
            PromotionPurposeType ppt = PromotionPurposeType.getByCode(dto.getPromotionPurposeType());
            Assert.isTrue(!PromotionPurposeType.UNKNOWN.equals(ppt),
                    "定向包推广目的类型错误");
            Assert.isTrue(!PromotionPurposeType.TRADE.equals(ppt), "旧三连定向包不允许使用交易经营推广目的");
            if (FLY_PROMOTION_PURPOSE_TYPE_LIST.contains(dto.getPromotionPurposeType())
                    && !CollectionUtils.isEmpty(dto.getTargetRules())) {
                dto.getTargetRules().forEach(targetRule -> {
                    String targetTypeStr = TargetType.getByCode(targetRule.getRuleType()).getName();
                    Assert.isTrue(!TargetType.INVALID_FLY_TARGET_LIST.contains(targetRule.getRuleType())
                                    || CollectionUtils.isEmpty(targetRule.getValueIds()),
                            "当前推广目的不支持定向条件：" + targetTypeStr);
                    Assert.isTrue(!INTELLIGENT_MASS_INVALID_PURPOSE_TYPE_LIST.contains(dto.getPromotionPurposeType())
                                    || TargetType.INTELLIGENT_MASS.getCode() != targetRule.getRuleType()
                                    || CollectionUtils.isEmpty(targetRule.getValueIds()),
                            "当前推广目的不支持智能放量定向");
                });
                Assert.isTrue(!INTELLIGENT_MASS_INVALID_PURPOSE_TYPE_LIST.contains(dto.getPromotionPurposeType())
                        || CollectionUtils.isEmpty(dto.getExtraCrowdPackIds()), "当前推广目的不支持智能放量定向");
                Assert.isTrue(CollectionUtils.isEmpty(dto.getBusinessInterestIds()), "当前推广目的不支持商业兴趣定向");
                Assert.isTrue(CollectionUtils.isEmpty(dto.getExcludeMiniGameIds()), "当前推广目的不支持小游戏定向");
                Assert.isTrue(CollectionUtils.isEmpty(dto.getIncludeMiniGameIds()), "当前推广目的不支持小游戏定向");
            }
            if (Objects.nonNull(dto.getTargetPackageInstalledUserFilterDto())) {
                Integer filterType = dto.getTargetPackageInstalledUserFilterDto()
                        .getFilterType();
                if (InstalledUserFilterEnum.FILTER.getCode().equals(filterType)) {
                    Assert.isTrue(PromotionPurposeType.APP_DOWNLOAD.equals(ppt)
                            || PromotionPurposeType.ON_SHELF_GAME.equals(ppt)
                            || PromotionPurposeType.BRAND_SPREAD.equals(ppt), "当前推广目的不支持已安装用户过滤定向");
                } else if (InstalledUserFilterEnum.TARGET.getCode().equals(filterType)) {
                    Assert.isTrue(PromotionPurposeType.LIVE_ROOM.equals(ppt)
                            || PromotionPurposeType.SALE_GOODS.equals(ppt) || PromotionPurposeType.BRAND_SPREAD.equals(ppt), "当前推广目的不支持已安装用户定向定向");
                }
            }
            validTargetPackageUpgradeByPpt(dto);
            boolean containsLauTags = Objects.nonNull(dto.getVideoTag()) && !CollectionUtils.isEmpty(dto.getVideoTag().getTags());
            Assert.isTrue(!containsLauTags || !FLY_PROMOTION_PURPOSE_TYPE_LIST.contains(ppt.getCode()), "当前推广目的不支持视频关键词定向");
        } else {
            Assert.isTrue(!Utils.isPositive(dto.getPromotionPurposeType()), "新三连定向包不支持推广类型");
        }


        validateTarget(operator.getOperatorId(), dto);
        validateExtraTarget(dto.getExtraTarget());
        validMiniGameConfig(dto.getIncludeMiniGameIds(), dto.getExcludeMiniGameIds());

        List<Integer> existIdsByName = getExistIdsByNameAndAccount(dto.getName(), operator.getOperatorId());
        Assert.isTrue(CollectionUtils.isEmpty(existIdsByName)
                || Utils.isPositive(dto.getId())
                && Lists.newArrayList(dto.getId()).containsAll(existIdsByName), "该定向包名称已存在");

    }

    private void validateExtraTarget(TargetPackageExtraTargetDto extraTarget) {
        if (extraTarget == null) {
            return;
        }
        Assert.isTrue(CollectionHelper.getSize(extraTarget.getIncludeTheirsFans()) <= cpcFansTargetMax, "粉丝定向包含UP主数不能超过" + cpcFansTargetMax);
        Assert.isTrue(CollectionHelper.getSize(extraTarget.getExcludeTheirsFans()) <= cpcFansTargetMax, "粉丝定向排除UP主数不能超过" + cpcFansTargetMax);
        if (!CollectionUtils.isEmpty(extraTarget.getIncludeTheirsFans()) && !CollectionUtils.isEmpty(extraTarget.getExcludeTheirsFans())) {
            Assert.isTrue(extraTarget.getIncludeTheirsFans().stream().noneMatch(extraTarget.getExcludeTheirsFans()::contains), "存在重复粉丝定向");
        }
    }

    /**
     * 检查小游戏定向配置是否错存在问题
     */
    private void validMiniGameConfig(List<String> includeMiniGameIds, List<String> excludeMiniGameIds) {
        if (!CollectionUtils.isEmpty(includeMiniGameIds) && !CollectionUtils.isEmpty(excludeMiniGameIds)) {
            Set<String> includeIdSet = new HashSet<>(includeMiniGameIds);
            Set<String> excludeIdSet = new HashSet<>(excludeMiniGameIds);
            includeIdSet.retainAll(excludeIdSet);

            if (!includeIdSet.isEmpty()) {
                throw new IllegalArgumentException("小游戏定向配置错误,定向和排除不能包含相同游戏");
            }
        }
    }

    // targetRule对应校验在ResTargetPackageRuleUpgradeServiceImpl
    public void validateTarget(Integer accountId, TargetPackageUpgradeDto targetPackageUpgradeDto) {
        Assert.notNull(accountId, "账号ID不可为空");
        Assert.notNull(targetPackageUpgradeDto, "定向信息不可为空");
        // tag定向(视频关键词兴趣)
        this.validateTags(targetPackageUpgradeDto.getTags());

        this.validateTags(targetPackageUpgradeDto.getVideoTag() == null ? Collections.emptyList() : targetPackageUpgradeDto.getVideoTag().getTags());

        this.validateCrowdPack(targetPackageUpgradeDto.getCrowdPackIds(), targetPackageUpgradeDto.getExcludeCrowdPackIds(), accountId, targetPackageUpgradeDto.getOtherCrowdPackIdsGroup());

        this.validateBusinessInterest(targetPackageUpgradeDto.getBusinessInterestIds());
        validateInstalledUserFilterTarget(targetPackageUpgradeDto.getPromotionPurposeType(), targetPackageUpgradeDto.getTargetPackageInstalledUserFilterDto());
    }

    @SneakyThrows
    private void validateCrowdPack(List<Integer> crowdPackIds, List<Integer> excludeCrowdPackIds,
                                   Integer accountId, List<List<Integer>> otherCrowdPackIdsGroup) {
        Set<Integer> cpIdSet = Sets.newHashSet();

        if (!CollectionUtils.isEmpty(crowdPackIds)) {
            cpIdSet.addAll(crowdPackIds);
        }

        if (!CollectionUtils.isEmpty(excludeCrowdPackIds)) {
            cpIdSet.addAll(excludeCrowdPackIds);
        }

        if (!CollectionUtils.isEmpty(otherCrowdPackIdsGroup)) {
            cpIdSet.addAll(otherCrowdPackIdsGroup
                    .stream()
                    .filter(list -> !CollectionUtils.isEmpty(list))
                    .flatMap(Collection::stream)
                    .distinct()
                    .collect(Collectors.toList()));
        }

        crowdPackService.validateCrowdPack(cpIdSet, accountId);
    }

    @SneakyThrows
    private void validateTags(List<String> tags) {
        if (CollectionUtils.isEmpty(tags)) {
            return;
        }

        lauTagIdsService.validateTags(tags);
    }

    private void validateInstalledUserFilterTarget(Integer promotionPurposeType, TargetPackageInstalledUserFilterDto targetPackageInstalledUserFilterDto) {
        if (targetPackageInstalledUserFilterDto == null) {
            return;
        }

        installedUserTargetValidator.validateInstalledUserFilterTarget(promotionPurposeType,
                targetPackageInstalledUserFilterDto.getFilterType());
    }

    private void validateBusinessInterest(List<Integer> businessInterestIds) {
        if (!CollectionUtils.isEmpty(businessInterestIds)) {
            Map<Integer, String> crowdPackNameMap;
            Assert.isTrue(!hystrixDmpService.isPeopleGroupCircuitBreakerOpen(), "暂不支持该操作，请稍后重试");
            try {
                crowdPackNameMap = crowdPackService.getCrowdPackNameMapInIdsDirectly(businessInterestIds);
            } catch (Exception e) {
                log.error("getCrowdPackNameMapInIdsError", e);
                businessInterestIds.forEach(id -> Assert.notNull(id, "商业兴趣分类不能包含空的ID"));
                return;
            }
            Assert.isTrue(crowdPackNameMap.keySet().containsAll(businessInterestIds), "存在无效的商业兴趣分类");
        }
    }

    /**
     * 根据账户校验定向包
     */
    private void validTargetPackageUpgradeByPpt(TargetPackageUpgradeDto dto) {
        PromotionPurposeType ppt = PromotionPurposeType.getByCode(dto.getPromotionPurposeType());
        switch (ppt) {
            case GAME_CARD:
                throw new IllegalArgumentException("目前游戏卡暂不支持新版定向包");
            case GAME_ACTIVITY_CARD:
                throw new IllegalArgumentException("目前游戏活动卡暂不支持新版定向包");

        }
    }

    private List<Integer> getExistIdsByNameAndAccount(String name, Integer accountId) {
        return resBqf.from(resTargetPackageUpgrade)
                .select(resTargetPackageUpgrade.id)
                .where(resTargetPackageUpgrade.accountId.eq(accountId)
                        .and(resTargetPackageUpgrade.packageName.eq(name))
                        .and(resTargetPackageUpgrade.isDeleted.eq(IsDeleted.VALID.getCode())))
                .fetch().stream()
                .map(Long::intValue)
                .collect(Collectors.toList());
    }


    private Integer getExistByIdAndAccount(Integer id, Integer accountId) {
        Long existId = resBqf.from(resTargetPackageUpgrade)
                .select(resTargetPackageUpgrade.id)
                .where(resTargetPackageUpgrade.id.eq(id.longValue())
                        .and(resTargetPackageUpgrade.accountId.eq(accountId))
                        .and(resTargetPackageUpgrade.isDeleted.eq(IsDeleted.VALID.getCode())))
                .fetchFirst();
        return Utils.isPositive(existId) ? existId.intValue() : null;
    }

    @Override
    @Transactional(value = "resourceTransactionManager", rollbackFor = Exception.class)
    public int create(TargetPackageUpgradeDto dto, Operator operator) {
        // from sunjicheng:如果代码逻辑有变动,还需要关注定向包方法copy()
        log.info("create targetPackageUpgradeDto:{}, operator:{}", dto, operator);
        int targetPackageId = createBaseTargetPackageUpgrade(dto, operator);

        resTargetPackageRuleService.create(operator, dto.getPromotionPurposeType(), targetPackageId, dto.getTargetRules(), true);
        resTargetPackageExtraTargetService.saveExtraTarget(targetPackageId, dto.getExtraTarget());
        // todo 下线商业兴趣
        //resTargetPackageBusinessInterestService.saveBusinessInterestTarget(targetPackageId, dto.getBusinessInterestIds());
        // todo sjc 直接为空
        resTargetPackageCrowdUpgradeService.insertCrowdPackIds(targetPackageId, dto.getCrowdPackIds(), dto.getExcludeCrowdPackIds(), dto.getOtherCrowdPackIdsGroup(), dto.getExtraCrowdPackIds());
        resTargetPackageInstalledUserFilterService.saveInstalledUserFilterTarget(targetPackageId, dto.getTargetPackageInstalledUserFilterDto(), true);


        //todo 下线小游戏定向
        //resTargetPackageMinigameService.saveTargetPackageMinigameTarget(targetPackageId, dto.getIncludeMiniGameIds(), dto.getExcludeMiniGameIds());

        // 定向包-操作系统版本
        resTargetPackageOsVersionUpgradeService.save(operator, targetPackageId, dto);
        // 定向包-bili客户端版本
        resTargetPackageBiliClientUpgradeService.save(operator, targetPackageId, dto);

        //行业兴趣人群定向
        resTargetPackageProfessionUpgradeService.save(targetPackageId, dto);

        Integer interestAuto = dto.getProfessionInterestAuto();
        if (IsInterestAuto.IS_AUTO == interestAuto) {
            resTargetPackageProfessionAutoUpgradeService.save(targetPackageId, operator.getOperatorId());
        }

        try {
            lauTagIdsService.addContentTagIds(targetPackageId, LauTargetTagEnum.PACKAGE.getCode(), dto.getTags(), dto.getIsFuzzyTags());

            lauTagIdsService.addTags(
                    targetPackageId,
                    LauTargetTagEnum.PACKAGE.getCode(),
                    UnitTargetTagDto
                            .builder()
                            .tags(dto.getVideoTag().getTags())
                            .isFuzzyTags(dto.getVideoTag().getIsFuzzyTags())
                            .type(LauTagType.VIDEO)
                            .build());
        } catch (Exception e) {
            log.error("create: add tags encounter an exception: " + ExceptionUtils.getStackTrace(e));
        }

        TargetPackageRuleUpgradeDto ruleUpgradeDto = TargetPackageUpgradeUtil.getRuleUpgradeDto(targetPackageId, dto.getTargetRules());
        logOperateService.addInsertLog(DbTable.RES_TARGET_PACKAGE_UPGRADE, operator, Arrays.asList(dto, ruleUpgradeDto), targetPackageId);

        return targetPackageId;
    }


    @Override
    @SneakyThrows
    public List<TargetPackageCopyFaillDto> copy(int targetID, Operator operator, List<Integer> accountIDs) {
        long now = Utils.getNow().getTime();
        log.info("List<TargetPackageCopyFaillDto> copy targetIDs:{}, operator:{}", targetID, operator);
        List<TargetPackageUpgradeDto> originTpuDTOs = query(QueryTargetPackageUpgradeDto.builder().id(targetID).build());
        Assert.isTrue(!CollectionUtils.isEmpty(originTpuDTOs), "找不到对应的新版定向包");
        TargetPackageUpgradeDto originTpuDTO = originTpuDTOs.get(0);
        Assert.isTrue(originTpuDTO.getAccountId().equals(operator.getOperatorId()), "您不能查看不属于自己的定向包");

        List<TargetPackageCopyFaillDto> faillResult = new ArrayList<>();

        String copyName = wrapperTargetPackageShareName(originTpuDTO.getName());
        List<TargetPackageUpgradeDto> checkNameList = query(QueryTargetPackageUpgradeDto.builder().name(copyName).accountIds(accountIDs).build());
        if (!CollectionUtils.isEmpty(checkNameList)) {
            List<Integer> faillaccountList = checkNameList.stream().map(TargetPackageUpgradeDto::getAccountId).distinct().collect(Collectors.toList());
            List<TargetPackageCopyFaillDto> faillList = faillaccountList.stream().map(TargetPackageCopyFaillDto::repeatTargetPackageCopyFaillDto).collect(Collectors.toList());
            faillResult.addAll(faillList);
            accountIDs = accountIDs.stream().filter(t -> !faillaccountList.contains(t)).collect(Collectors.toList());
        }


        Assert.isTrue(!CollectionUtils.isEmpty(accountIDs), "近期已经推送过该定向包了，过5分钟后试试！！");

        TargetPackageUpgradeDto tpuDto = buildTargetPackageUpgradeDto4Copy(targetID, originTpuDTO, copyName);
        TargetPackageUpgradeCopyDto copyDTO = TargetPackageUpgradeCopyDto.builder().originTargetId(tpuDto.getOriginTargetId()).originTargetName(originTpuDTO.getName()).build();


        long now2 = Utils.getNow().getTime();
        for (Integer accountID : accountIDs) {
            try {
                int targetPackageId = ((IResTargetPackageUpgradeService) AopContext.currentProxy()).insertTargetPackCopy(tpuDto, copyDTO, operator, accountID);
                //日志错误可以忽略
                TargetPackageRuleUpgradeDto ruleUpgradeDto = TargetPackageUpgradeUtil.getRuleUpgradeDto(targetPackageId, tpuDto.getTargetRules());
                logOperateService.addInsertLog(DbTable.RES_TARGET_PACKAGE_UPGRADE, operator, Arrays.asList(tpuDto, ruleUpgradeDto, copyDTO), targetPackageId);
            } catch (Exception e) {
                log.warn("TargetPackageCopyFaillDto err targetID={}", targetID, e);
                faillResult.add(TargetPackageCopyFaillDto.dbErrrTargetPackageCopyFaillDto(accountID));
            }
        }
        if (Utils.getNow().getTime() - now > 3000) {
            log.warn("List<TargetPackageCopyFaillDto> time is over, time={},writeTime={},targetID ={},  accountIDs.size()={} ",
                    Utils.getNow().getTime() - now2, Utils.getNow().getTime() - now, targetID, accountIDs.size());
        }
        return faillResult;
    }

    @Override
    @Transactional(value = "resourceTransactionManager", rollbackFor = Exception.class)
    public int insertTargetPackCopy(TargetPackageUpgradeDto tpuDto, TargetPackageUpgradeCopyDto copyDTO, Operator operator, Integer accountID) {
        if (tpuDto == null) {
            return 0;
        }
        int targetPackageId = copyBaseTargetPackageUpgrade(tpuDto, accountID);
        if (Objects.nonNull(tpuDto.getExtraTarget())) {
            resTargetPackageExtraTargetService.saveExtraTargetOrigin(targetPackageId, tpuDto.getExtraTarget());
        }
        if (!CollectionUtils.isEmpty(tpuDto.getTargetRules())) {
            resTargetPackageRuleService.create(operator, 0, targetPackageId, tpuDto.getTargetRules(), false);
        }
        if (Objects.nonNull(tpuDto.getTargetPackageInstalledUserFilterDto())) {
            resTargetPackageInstalledUserFilterService.saveInstalledUserFilterTarget(targetPackageId, tpuDto.getTargetPackageInstalledUserFilterDto(), false);
        }
        if (Objects.nonNull(tpuDto.getBiliClientVersion())) {
            resTargetPackageBiliClientUpgradeService.saveOrigin(targetPackageId, tpuDto.getBiliClientVersion());
        }
        if (Objects.nonNull(tpuDto.getOsVersion())) {
            resTargetPackageOsVersionUpgradeService.saveOrigin(targetPackageId, tpuDto.getOsVersion());
        }
        if (IsInterestAuto.IS_AUTO == tpuDto.getProfessionInterestAuto()) {
            resTargetPackageProfessionAutoUpgradeService.save(targetPackageId, accountID);
        }
        if (Objects.nonNull(tpuDto.getProfessionInterest())) {
            resTargetPackageProfessionUpgradeService.save(targetPackageId, tpuDto);
        }

        if (Objects.nonNull(tpuDto.getVideoTagDto())) {
            tagIdsDao.insertSelective(dtoToPo(tpuDto.getVideoTagDto(), targetPackageId));
        }

        return targetPackageId;
    }

    // 保存基础定向包名称描述信息
    private int createBaseTargetPackageUpgrade(TargetPackageUpgradeDto dto, Operator operator) {
        // 前置校验
        validateTargetPackageUpgradeBase(dto, operator, false);
        ResTargetPackageUpgradePo insertPo = buildResTargetPackageUpgradePo(dto, operator.getOperatorId());
        final Long targetPackageId;
        targetPackageId = resBqf.insert(resTargetPackageUpgrade).insertGetKey(insertPo);
        Assert.isTrue(Utils.isPositive(targetPackageId), "定向包基本信息保存失败");
        // 历史原因 只能是int 后续优化
        Assert.isTrue(targetPackageId <= Integer.MAX_VALUE, "定向包数量超限");
        return targetPackageId.intValue();
    }

    // 推送基础定向包名称描述信息
    private int copyBaseTargetPackageUpgrade(TargetPackageUpgradeDto dto, int accountId) {
        ResTargetPackageUpgradePo insertPo = buildResTargetPackageUpgradePo(dto, accountId);
        final Long targetPackageId;
        targetPackageId = resBqf.insert(resTargetPackageUpgrade).insertGetKey(insertPo);
        Assert.isTrue(Utils.isPositive(targetPackageId), "定向包基本信息保存失败");
        // 历史原因 只能是int 后续优化
        Assert.isTrue(targetPackageId <= Integer.MAX_VALUE, "定向包数量超限");
        return targetPackageId.intValue();
    }

    private ResTargetPackageUpgradePo buildResTargetPackageUpgradePo(TargetPackageUpgradeDto dto, int accountId) {
        ResTargetPackageUpgradePo insertPo = new ResTargetPackageUpgradePo();
        insertPo.setAccountId(accountId);
        insertPo.setPackageName(dto.getName());
        insertPo.setAdpVersion(dto.getAdpVersion());
        insertPo.setDescription(dto.getDescription());
        insertPo.setPromotionPurposeType(dto.getPromotionPurposeType());
        insertPo.setIsDeleted(IsDeleted.VALID.getCode());
        insertPo.setOriginTargetId(dto.getOriginTargetId());
        return insertPo;
    }

    @Override
    @Transactional(value = "resourceTransactionManager", rollbackFor = Exception.class)
    public void delete(Integer id, Operator operator) {
        log.info("delete targetPackageUpgrade id:{}, operator:{}", id, operator);
        RLock lock = distributedLock.getLock(id, Constants.TARGET_PACKAGE_UPGRADE_LOCK_SUFFIX, () -> "您有其它定向包正在生成，请稍后重试");
        try {
            PageResult<CpcLightUnitDto> unitBasePageResult = cpcUnitService.queryLightUnitByPage(QueryCpcUnitDto.builder()
                    .unitStatusList(UnitStatus.VALID_UNIT_STATUS_LIST)
                    .accountIds(Lists.newArrayList(operator.getOperatorId()))
                    .targetPackageId(id)
                    .page(Page.valueOf(1, 10))
                    .build());
            Assert.isTrue(CollectionUtils.isEmpty(unitBasePageResult.getRecords()), "当前定向包已关联可投放单元，删除失败");
            TargetPackageUpgradeDto newEntity = TargetPackageUpgradeDto.builder()
                    .id(id)
                    .isDeleted(IsDeleted.DELETED.getCode()).build();
            TargetPackageUpgradeDto oldDto = load(id, true);
            Assert.isTrue(oldDto.getAccountId().equals(operator.getOperatorId()), "您不能删除不属于自己的定向包");
            TargetPackageUpgradeDto oldEntity = TargetPackageUpgradeDto.builder()
                    .id(id)
                    .isDeleted(oldDto.getIsDeleted()).build();
            deleteBaseTargetPackage(id, operator);
            logOperateService.addDeleteLog(DbTable.RES_TARGET_PACKAGE_UPGRADE, operator, oldEntity, newEntity, id);
        } finally {
            log.info(System.currentTimeMillis() + "---delete target package upgrade success----");
            lock.unlock();
        }
    }

    private BaseQuery<Long> getIdQuery(QueryTargetPackageUpgradeDto queryDto) {
        Long id = Utils.isPositive(queryDto.getId()) ? queryDto.getId().longValue() : null;
        List<Long> ids = CollectionUtils.isEmpty(queryDto.getIds()) ? Collections.emptyList() :
                queryDto.getIds().stream()
                        .map(Integer::longValue)
                        .collect(Collectors.toList());
        return resBqf.select(resTargetPackageUpgrade.id)
                .from(resTargetPackageUpgrade)
                .whereIfNotNull(id, resTargetPackageUpgrade.id::eq)
                .whereIfNotEmpty(ids, resTargetPackageUpgrade.id::in)
                .whereIfNotNull(queryDto.getAccountId(), resTargetPackageUpgrade.accountId::eq)
                .whereIfNotNull(queryDto.getName(), resTargetPackageUpgrade.packageName::eq)
                .whereIfNotNull(queryDto.getPromotionPurposeType(), resTargetPackageUpgrade.promotionPurposeType::eq)
                .whereIfNotNull(queryDto.getIsDeleted(), resTargetPackageUpgrade.isDeleted::eq)
                .whereIfNotEmpty(queryDto.getAccountIds(), resTargetPackageUpgrade.accountId::in)
                .whereIfHasText(queryDto.getLikeName(), resTargetPackageUpgrade.packageName::contains)
                .orderByIfHasText(queryDto.getOrderBy())
                .offsetIfNotNull(queryDto.getPage() == null ? null : queryDto.getPage().getOffset())
                .limitIfNotNull(queryDto.getPage() == null ? null : queryDto.getPage().getLimit());
    }

    private BaseQuery<ResTargetPackageUpgradePo> getBaseQuery(QueryTargetPackageUpgradeDto queryDto) {
        Long id = Utils.isPositive(queryDto.getId()) ? queryDto.getId().longValue() : null;
        List<Long> ids = CollectionUtils.isEmpty(queryDto.getIds()) ? Collections.emptyList() :
                queryDto.getIds().stream()
                        .map(Integer::longValue)
                        .collect(Collectors.toList());

        List<Long> excludeIds = new ArrayList<>();
        if (Utils.isPositive(queryDto.getAccountId()) && (queryDto.isExcludeInstallUserFilter()
                || queryDto.isExcludeTransUserFilter())) {
            List<ResTargetPackageUpgradePo> pos = resBqf.selectFrom(resTargetPackageUpgrade)
                    .from(resTargetPackageUpgrade)
                    .where(resTargetPackageUpgrade.accountId.eq(queryDto.getAccountId()))
                    .where(resTargetPackageUpgrade.isDeleted.eq(IsDeleted.VALID.getCode()))
                    .fetch();
            List<Integer> intAllIds = pos.stream().map(o -> o.getId().intValue()).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(intAllIds)) {
                // 如果要排除"已安装定向过滤"，先查出来"已安装定向过滤"的包id
                if (queryDto.isExcludeInstallUserFilter()) {
                    excludeIds.addAll(resTargetPackageInstalledUserFilterService.queryFilterInstallUserPackageIds(intAllIds));
                }
                // 如果要排除"已转化用户过滤"，先查出来"已转化用户过滤"的包id
                if (queryDto.isExcludeTransUserFilter()) {
                    excludeIds.addAll(resTargetPackageRuleService.queryConvertedUserFilterPackageIds(intAllIds));
                }
            }
        }
        return resBqf.selectFrom(resTargetPackageUpgrade)
                .whereIfNotNull(id, resTargetPackageUpgrade.id::eq)
                .whereIfNotEmpty(ids, resTargetPackageUpgrade.id::in)
                .whereIfNotNull(queryDto.getAdpVersion(), resTargetPackageUpgrade.adpVersion::eq)
                .whereIfNotNull(queryDto.getAccountId(), resTargetPackageUpgrade.accountId::eq)
                .whereIfNotNull(queryDto.getName(), resTargetPackageUpgrade.packageName::eq)
                .whereIfNotNull(queryDto.getPromotionPurposeType(), resTargetPackageUpgrade.promotionPurposeType::eq)
                .whereIfNotNull(queryDto.getIsDeleted(), resTargetPackageUpgrade.isDeleted::eq)
                .whereIfNotEmpty(queryDto.getAccountIds(), resTargetPackageUpgrade.accountId::in)
                .whereIfHasText(queryDto.getLikeName(), resTargetPackageUpgrade.packageName::contains)
                .whereIfNotEmpty(excludeIds, resTargetPackageUpgrade.id::notIn)
                .orderByIfHasText(queryDto.getOrderBy())
                .offsetIfNotNull(queryDto.getPage() == null ? null : queryDto.getPage().getOffset())
                .limitIfNotNull(queryDto.getPage() == null ? null : queryDto.getPage().getLimit());
    }

    private TargetPackageUpgradeDto convertPo2Dto(ResTargetPackageUpgradePo packageUpgradePo) {
        return TargetPackageUpgradeDto.builder()
                .id(packageUpgradePo.getId().intValue())
                .name(packageUpgradePo.getPackageName())
                .accountId(packageUpgradePo.getAccountId())
                .description(packageUpgradePo.getDescription())
                .adpVersion(packageUpgradePo.getAdpVersion())
                .promotionPurposeType(packageUpgradePo.getPromotionPurposeType())
                .ctime(packageUpgradePo.getCtime())
                .mtime(packageUpgradePo.getMtime())
                .originTargetId(packageUpgradePo.getOriginTargetId())
                .build();
    }

    private int deleteBaseTargetPackage(Integer id, Operator operator) {
        Integer existId = getExistByIdAndAccount(id, operator.getOperatorId());
        Assert.isTrue(Utils.isPositive(existId), "该定向包不存在,删除失败");

        // 仅仅删除了主表 resTargetPackageUpgrade，未删除关联表
        Long result = resBqf.update(resTargetPackageUpgrade)
                .set(resTargetPackageUpgrade.isDeleted, IsDeleted.DELETED.getCode())
                .where(resTargetPackageUpgrade.id.eq(id.longValue())
                        .and(resTargetPackageUpgrade.isDeleted.eq(IsDeleted.VALID.getCode()))).execute();
        Assert.isTrue(result.equals(1L), "定向包删除失败");
        return result.intValue();
    }

    @Override
    public TargetPackageUpgradeDto load(Integer id, boolean isBase) {
        QueryTargetPackageUpgradeDto queryBaseDto = QueryTargetPackageUpgradeDto.builder()
                .id(id)
                .isDeleted(IsDeleted.VALID.getCode())
                .build();
        BaseQuery<ResTargetPackageUpgradePo> baseQuery = getBaseQuery(queryBaseDto);
        ResTargetPackageUpgradePo resultPo = baseQuery.fetchFirst();
        Assert.notNull(resultPo, "该定向包不存在");
        PageResult<CpcLightUnitDto> unitBasePageResult = cpcUnitService.queryLightUnitByPage(QueryCpcUnitDto.builder()
                .unitStatusList(UnitStatus.NON_DELETED_NON_HISTORY_UNIT_STATUS_LIST)
                .accountIds(Lists.newArrayList(resultPo.getAccountId()))
                .targetPackageId(id)
                .page(Page.valueOf(1, 100))
                .build());
        TargetPackageUpgradeDto baseDto = convertPo2Dto(resultPo);
        List<UnitBaseNameBo> unitNameBoList = unitBasePageResult.getRecords()
                .stream().map(unit -> {
                    return UnitBaseNameBo.builder()
                            .unitId(unit.getUnitId())
                            .unitName(unit.getUnitName())
                            .build();
                }).collect(Collectors.toList());
        baseDto.setUnitList(unitNameBoList);
        baseDto.setTotalUnitCount(unitBasePageResult.getTotal());
        if (isBase) {
            return baseDto;
        }

        // 查询完整定向信息
        List<TargetRule> targetRules = resTargetPackageRuleService.getTargetRules(id);
        baseDto.setTargetRules(targetRules);

        TargetPackageExtraTargetDto extraTargetDto = resTargetPackageExtraTargetService.getTargetPackageExtraTargetDosByTargetPackageId(id);
        baseDto.setExtraTarget(extraTargetDto);

        List<Integer> businessInterestIds = resTargetPackageBusinessInterestService.getBusinessInterestIds(id);
        baseDto.setBusinessInterestIds(businessInterestIds);

        List<Integer> professionInterestIds = resTargetPackageProfessionUpgradeService.getProfessionInterestIds(id);
        baseDto.setProfessionInterest(professionInterestIds);

        ResTargetPackageProfessionInterestAutoUpgradePo po = resTargetPackageProfessionAutoUpgradeService.selectInterestAuto(id);
        Integer interestAuto = IsInterestAuto.NO_AUTO;
        if (Objects.nonNull(po)) {
            interestAuto = po.getInterestAuto();
        }
        baseDto.setProfessionInterestAuto(interestAuto);

        TargetPackageCrowdPackDto crowdPackDto = resTargetPackageCrowdUpgradeService.queryCrowdPackByTargetPackageId(id);
        baseDto.setCrowdPackIds(crowdPackDto.getCrowdPackIds());
        baseDto.setExcludeCrowdPackIds(crowdPackDto.getExcludeCrowdPackIds());
        baseDto.setOtherCrowdPackIdsGroup(crowdPackDto.getOtherCrowdPackIdsGroup());
        baseDto.setExtraCrowdPackIds(crowdPackDto.getExtraCrowdPackIds());

        TargetPackageInstalledUserFilterDto installedUserFilterTargetDto = resTargetPackageInstalledUserFilterService.getInstalledUserFilterTarget(id);
        baseDto.setTargetPackageInstalledUserFilterDto(installedUserFilterTargetDto);

        // 操作系统和bili客户端版本
        TargetUpgradeBiliClientVersionDto biliClientVersionDto = resTargetPackageBiliClientUpgradeService.queryOsVersion(id);
        baseDto.setBiliClientVersion(biliClientVersionDto);
        TargetUpgradeOsVersionDto osVersionDto = resTargetPackageOsVersionUpgradeService.queryOsVersion(id);
        baseDto.setOsVersion(osVersionDto);

        TargetPackageMinigameTargetDto minigameTargetDto = resTargetPackageMinigameService.getTargetPackageMinigameTarget(id);
        baseDto.setIncludeMiniGameIds(Objects.isNull(minigameTargetDto) ? Collections.emptyList() : minigameTargetDto.getIncludeMiniGameIds());
        baseDto.setExcludeMiniGameIds(Objects.isNull(minigameTargetDto) ? Collections.emptyList() : minigameTargetDto.getExcludeMiniGameIds());

        LauTagIdsDto userTagDto = lauTagIdsService.getByLauId(id, LauTargetTagEnum.PACKAGE.getCode(), LauTagType.USER.getCode());
        baseDto.setIsFuzzyTags(userTagDto == null ? 0 : userTagDto.getTagTargetType());
        baseDto.setTags(userTagDto == null ? Collections.emptyList() : userTagDto.getTags());

        LauTagIdsDto videoTagDto = lauTagIdsService.getByLauId(id, LauTargetTagEnum.PACKAGE.getCode(), LauTagType.VIDEO.getCode());
        baseDto.setVideoTag(videoTagDto == null ? TargetPackageTargetTagDto.getEmpty() : TargetPackageTargetTagDto.builder()
                .tags(videoTagDto.getTags())
                .isFuzzyTags(videoTagDto.getTagTargetType())
                .type(LauTagType.getByCode(videoTagDto.getTagType()))
                .build());

        return baseDto;
    }

    @Override
    public PageResult<TargetPackageUpgradeDto> queryByPage(QueryTargetPackageUpgradeDto queryDto) {
        queryDto.setIsDeleted(IsDeleted.VALID.getCode());
        BaseQuery<ResTargetPackageUpgradePo> baseQuery = getBaseQuery(queryDto);
        List<ResTargetPackageUpgradePo> resultPoList = baseQuery.fetch();
        List<TargetPackageUpgradeDto> resultDtoList = resultPoList.stream()
                .map(this::convertPo2Dto)
                .collect(Collectors.toList());
        BaseQuery<ResTargetPackageUpgradePo> countQuery = getBaseQuery(queryDto);
        Long total = countQuery.fetchCount();
        List<Integer> targetPackageIds = resultDtoList.stream()
                .map(TargetPackageUpgradeDto::getId)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(targetPackageIds)) {
            return PageResult.<TargetPackageUpgradeDto>builder()
                    .records(resultDtoList)
                    .total(total.intValue())
                    .build();
        }

        // 查询完整定向信息
        Map<Integer, List<TargetRule>> targetRulesMap =
                resTargetPackageRuleService.getTargetRulesMapInResTargetPackageIds(targetPackageIds);
        Map<Integer, TargetPackageExtraTargetDto> extraTargetMap =
                resTargetPackageExtraTargetService.getTargetPackageExtraTargetDoMapByTargetPackageIds(targetPackageIds);
        Map<Integer, List<Integer>> businessInterestIdMap =
                resTargetPackageBusinessInterestService.getBusinessInterestIdMap(targetPackageIds);
        Map<Integer, List<Integer>> professionInterestIdMap
                = resTargetPackageProfessionUpgradeService.getProfessionInterestIdMap(targetPackageIds);
        Map<Integer, ResTargetPackageProfessionInterestAutoUpgradePo> mapByTargetPackage =
                resTargetPackageProfessionAutoUpgradeService.getMapByLauId(targetPackageIds);
        Map<Integer, TargetPackageCrowdPackDto> crowdPackDtoMap =
                resTargetPackageCrowdUpgradeService.queryCrowdPackMapByTargetPackageIds(targetPackageIds);
        Map<Integer, TargetPackageInstalledUserFilterDto> installedUserFilterTargetMap =
                resTargetPackageInstalledUserFilterService.getInstalledUserFilterTargetMap(targetPackageIds);
        Map<Integer, TargetPackageMinigameTargetDto> minigameTargetMap =
                resTargetPackageMinigameService.getTargetPackageMinigameTargetMap(targetPackageIds);
        Map<Integer, LauTagIdsDto> userTagDtoMap =
                lauTagIdsService.getMapByLauId(targetPackageIds, LauTargetTagEnum.PACKAGE.getCode(), LauTagType.USER.getCode());
        Map<Integer, LauTagIdsDto> videoTagDtoMap =
                lauTagIdsService.getMapByLauId(targetPackageIds, LauTargetTagEnum.PACKAGE.getCode(), LauTagType.VIDEO.getCode());
        Map<Integer, TargetUpgradeOsVersionDto> osVersionDtoMap = resTargetPackageOsVersionUpgradeService.queryOsVersionMap(targetPackageIds);
        Map<Integer, TargetUpgradeBiliClientVersionDto> biliClientVersionDtoMap = resTargetPackageBiliClientUpgradeService.queryOsVersionMap(targetPackageIds);

        resultDtoList.forEach(resultDto -> {
            Integer targetPackageId = resultDto.getId();

            List<TargetRule> targetRules = targetRulesMap.getOrDefault(targetPackageId, Collections.emptyList());
            resultDto.setTargetRules(targetRules);
            TargetPackageExtraTargetDto extraTargetDto = extraTargetMap.getOrDefault(targetPackageId, TargetPackageExtraTargetDto.getEmpty());
            resultDto.setExtraTarget(extraTargetDto);
            List<Integer> businessInterestIds = businessInterestIdMap.getOrDefault(targetPackageId, Collections.emptyList());
            resultDto.setBusinessInterestIds(businessInterestIds);
            List<Integer> professionInterestIds = professionInterestIdMap.getOrDefault(targetPackageId, Collections.emptyList());
            resultDto.setProfessionInterest(professionInterestIds);
            ResTargetPackageProfessionInterestAutoUpgradePo isAuto = mapByTargetPackage.getOrDefault(targetPackageId, null);
            resultDto.setProfessionInterestAuto(Objects.isNull(isAuto) ? IsInterestAuto.NO_AUTO : isAuto.getInterestAuto());
            TargetPackageCrowdPackDto crowdPackDto = crowdPackDtoMap.getOrDefault(targetPackageId, TargetPackageCrowdPackDto.getEmpty());
            resultDto.setCrowdPackIds(crowdPackDto.getCrowdPackIds());
            resultDto.setExcludeCrowdPackIds(crowdPackDto.getExcludeCrowdPackIds());
            resultDto.setOtherCrowdPackIdsGroup(crowdPackDto.getOtherCrowdPackIdsGroup());
            resultDto.setExtraCrowdPackIds(crowdPackDto.getExtraCrowdPackIds());
            TargetPackageInstalledUserFilterDto installedUserFilterDto = installedUserFilterTargetMap.getOrDefault(targetPackageId, TargetPackageInstalledUserFilterDto.getEmpty());
            resultDto.setTargetPackageInstalledUserFilterDto(installedUserFilterDto);
            TargetPackageMinigameTargetDto minigameTargetDto = minigameTargetMap.getOrDefault(targetPackageId, TargetPackageMinigameTargetDto.getEmpty());
            resultDto.setIncludeMiniGameIds(minigameTargetDto.getIncludeMiniGameIds());
            resultDto.setExcludeMiniGameIds(minigameTargetDto.getExcludeMiniGameIds());
            LauTagIdsDto userTagDto = userTagDtoMap.get(targetPackageId);
            resultDto.setIsFuzzyTags(userTagDto == null ? 0 : userTagDto.getTagTargetType());
            resultDto.setTags(userTagDto == null ? Collections.emptyList() : userTagDto.getTags());
            LauTagIdsDto videoTagDto = videoTagDtoMap.get(targetPackageId);
            resultDto.setVideoTag(videoTagDto == null ? TargetPackageTargetTagDto.getEmpty() : TargetPackageTargetTagDto.builder()
                    .tags(videoTagDto.getTags())
                    .isFuzzyTags(videoTagDto.getTagTargetType())
                    .type(LauTagType.getByCode(videoTagDto.getTagType()))
                    .build());
            resultDto.setOsVersion(osVersionDtoMap.get(targetPackageId));
            resultDto.setBiliClientVersion(biliClientVersionDtoMap.get(targetPackageId));
        });
        return PageResult.<TargetPackageUpgradeDto>builder()
                .records(resultDtoList)
                .total(total.intValue())
                .build();
    }

    @Override
    public List<TargetPackageUpgradeDto> query(QueryTargetPackageUpgradeDto queryDto) {
        queryDto.setIsDeleted(IsDeleted.VALID.getCode());
        BaseQuery<ResTargetPackageUpgradePo> baseQuery = getBaseQuery(queryDto);
        List<ResTargetPackageUpgradePo> resultPoList = baseQuery.fetch();
        return resultPoList.stream()
                .map(this::convertPo2Dto)
                .collect(Collectors.toList());
    }

    @Override
    public List<Integer> queryIds(QueryTargetPackageUpgradeDto queryDto) {
        queryDto.setIsDeleted(IsDeleted.VALID.getCode());
        BaseQuery<Long> baseQuery = getIdQuery(queryDto);
        return baseQuery.fetch()
                .stream()
                .map(Long::intValue)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(value = "resourceTransactionManager", rollbackFor = Exception.class)
    public int update(TargetPackageUpgradeDto dto, Operator operator) {
        log.info("update targetPackageUpgradeDto:{}, operator:{}", dto, operator);
        RLock lock = distributedLock.getLock(dto.getId(), Constants.TARGET_PACKAGE_UPGRADE_LOCK_SUFFIX, () -> "您有其它定向包正在生成，请稍后重试");
        try {
            List<UnitBaseNameBo> unitBaseList = dto.getUnitList();
            Integer targetPackageId = dto.getId();
            TargetPackageUpgradeDto oldDto = load(dto.getId(), false);
            Assert.isTrue(oldDto.getPromotionPurposeType().equals(dto.getPromotionPurposeType()), "您不能修改新版定向包的推广目的类型");
            Assert.isTrue(oldDto.getAccountId().equals(operator.getOperatorId()), "您不能编辑不属于自己的定向包");
            TargetPackageRuleUpgradeDto oldRuleUpgradeDto = TargetPackageUpgradeUtil.getRuleUpgradeDto(targetPackageId, oldDto.getTargetRules());
            int result = updateBaseTargetPackageUpgrade(dto, operator);
            resTargetPackageRuleService.update(operator, dto.getPromotionPurposeType(), targetPackageId, dto.getTargetRules());
            resTargetPackageExtraTargetService.saveExtraTarget(targetPackageId, dto.getExtraTarget());

            // todo 下线商业兴趣
            //resTargetPackageBusinessInterestService.saveBusinessInterestTarget(targetPackageId, dto.getBusinessInterestIds());
            resTargetPackageCrowdUpgradeService.updateCrowdPackIds(targetPackageId, dto.getCrowdPackIds(), dto.getExcludeCrowdPackIds(), dto.getOtherCrowdPackIdsGroup(), dto.getExtraCrowdPackIds());
            resTargetPackageInstalledUserFilterService.saveInstalledUserFilterTarget(targetPackageId, dto.getTargetPackageInstalledUserFilterDto(), true);

            // todo 下线小游戏定向
            //resTargetPackageMinigameService.saveTargetPackageMinigameTarget(targetPackageId, dto.getIncludeMiniGameIds(), dto.getExcludeMiniGameIds());

            // 定向包-操作系统版本
            resTargetPackageOsVersionUpgradeService.save(operator, targetPackageId, dto);
            // 定向包-bili客户端版本
            resTargetPackageBiliClientUpgradeService.save(operator, targetPackageId, dto);

            //行业兴趣人群
            resTargetPackageProfessionUpgradeService.save(targetPackageId, dto);
            Integer auto = dto.getProfessionInterestAuto();

            if (IsInterestAuto.IS_AUTO == auto) {
                resTargetPackageProfessionAutoUpgradeService.update(targetPackageId, unitBaseList);
            } else {
                resTargetPackageProfessionAutoUpgradeService.deleteProfessionInterestAutoByTargetPackageId(targetPackageId);
            }


            try {
                lauTagIdsService.updateContentTagIds(dto.getId(), LauTargetTagEnum.PACKAGE.getCode(), oldDto.getTags(), dto.getTags(), dto.getIsFuzzyTags(), oldDto.getIsFuzzyTags());

                lauTagIdsService.updateTags(
                        dto.getId(),
                        LauTargetTagEnum.PACKAGE.getCode(),
                        UnitTargetTagDto
                                .builder()
                                .tags(dto.getVideoTag().getTags())
                                .isFuzzyTags(dto.getVideoTag().getIsFuzzyTags())
                                .type(LauTagType.VIDEO)
                                .build());
                // 更新定向包后处理
                this.postProcessUpdateTargetPackage(targetPackageId, oldDto.getPromotionPurposeType(),
                        oldDto.getTargetPackageInstalledUserFilterDto(), dto.getTargetPackageInstalledUserFilterDto());
            } catch (Exception e) {
                log.error("update update tags encounter an exception: " + ExceptionUtils.getStackTrace(e));
            }

            TargetPackageRuleUpgradeDto ruleUpgradeDto = TargetPackageUpgradeUtil.getRuleUpgradeDto(targetPackageId, dto.getTargetRules());

            logOperateService.addUpdateLog(DbTable.RES_TARGET_PACKAGE_UPGRADE, operator, Arrays.asList(oldDto, oldRuleUpgradeDto), Arrays.asList(dto, ruleUpgradeDto), targetPackageId);
            return result;
        } finally {
            log.info(System.currentTimeMillis() + "---update target package upgrade success----");
            lock.unlock();
        }
    }

    @Override
    public List<Integer> getTargetPackageIdsInCrowdPackIds(List<Integer> crowdPackIds) {
        if (CollectionUtils.isEmpty(crowdPackIds)) {
            return Collections.emptyList();
        }
        List<Integer> crowdRelatedTargetPackageIds =
                resTargetPackageCrowdUpgradeService.queryTargetPackageIdListInCrowdPackIds(crowdPackIds);
        List<Integer> bussInterestRelatedTargetPackageIds =
                resTargetPackageBusinessInterestService.queryTargetPackageIdsInBusInterestIds(crowdPackIds);
        List<Integer> proInterestRelatedTargetPackageIds =
                resTargetPackageBusinessInterestService.queryTargetPackageIdsInBusInterestIds(crowdPackIds);

        List<Integer> queryTargetPackageIds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(crowdPackIds)) {
            queryTargetPackageIds.addAll(crowdRelatedTargetPackageIds);
        }
        if (!CollectionUtils.isEmpty(bussInterestRelatedTargetPackageIds)) {
            queryTargetPackageIds.addAll(bussInterestRelatedTargetPackageIds);
        }
        if (!CollectionUtils.isEmpty(proInterestRelatedTargetPackageIds)) {
            queryTargetPackageIds.addAll(proInterestRelatedTargetPackageIds);
        }
        if (CollectionUtils.isEmpty(queryTargetPackageIds)) {
            return Collections.emptyList();
        }
        QueryTargetPackageUpgradeDto queryDto = QueryTargetPackageUpgradeDto.builder()
                .ids(queryTargetPackageIds)
                .page(Page.valueOf(1, 10))
                .build();
        return queryIds(queryDto);
    }

    @Override
    public boolean isBrand(Long packageId) {
        if (packageId == null) {
            return false;
        }
        List<Long> ids = resBqf.select(resTargetPackageUpgrade.id)
                .from(resTargetPackageUpgrade)
                .where(resTargetPackageUpgrade.id.eq(packageId))
                .where(resTargetPackageUpgrade.promotionPurposeType.eq(PromotionPurposeType.BRAND_SPREAD.getCode()))
                .fetch();
        return !CollectionUtils.isEmpty(ids);
    }

    @Override
    public boolean isBrandOrAccountPromotion(Long packageId) {
        if (packageId == null) {
            return false;
        }
        List<Long> ids = resBqf.select(resTargetPackageUpgrade.id)
                .from(resTargetPackageUpgrade)
                .where(resTargetPackageUpgrade.id.eq(packageId))
                .where(resTargetPackageUpgrade.promotionPurposeType.in(Arrays.asList(
                        PromotionPurposeType.BRAND_SPREAD.getCode(), PromotionPurposeType.ENTERPRISE_PROMOTION.getCode())))
                .fetch();
        return !CollectionUtils.isEmpty(ids);
    }

    // 保存基础定向包名称描述信息
    private int updateBaseTargetPackageUpgrade(TargetPackageUpgradeDto dto, Operator operator) {

        // 前置校验
        validateTargetPackageUpgradeBase(dto, operator, true);

        // 不允许修改adp版本
        ResTargetPackageUpgradePo updatePo = new ResTargetPackageUpgradePo();
        updatePo.setId(dto.getId().longValue());
        updatePo.setAccountId(operator.getOperatorId());
        updatePo.setPackageName(dto.getName());
        updatePo.setDescription(dto.getDescription());
        updatePo.setPromotionPurposeType(dto.getPromotionPurposeType());

        long result = resBqf.update(resTargetPackageUpgrade)
                .where(resTargetPackageUpgrade.id.eq(dto.getId().longValue())
                        .and(resTargetPackageUpgrade.isDeleted.eq(IsDeleted.VALID.getCode()))).updateBean(updatePo);
        Assert.isTrue(result == 1, "定向包基本信息更新失败");
        return updatePo.getId().intValue();
    }

    public void postProcessUpdateTargetPackage(Integer targetPackageId, Integer ppt,
                                               TargetPackageInstalledUserFilterDto oldInstall,
                                               TargetPackageInstalledUserFilterDto newInstall) {
        // 针对品牌传播
        if (Objects.equals(PromotionPurposeType.BRAND_SPREAD.getCode(), ppt)) {
            // 如果已安装用户由不限 -> 过滤
            if (this.installedUserFilterUnLimit2Filter(oldInstall, newInstall)) {
                // 根据定向包id找到所有关联单元【无应用包】,删除关联关系
                cpcUnitService.batchUnBindNotHasAppPackageUnit(targetPackageId);
                // 根据定向包id找到所有关联托管计划【无应用包】,删除关联关系
                managedCampaignService.batchUnBindNotHasAppPackageManagedCampaign(targetPackageId);
            }
        }
    }

    private boolean installedUserFilterUnLimit2Filter(TargetPackageInstalledUserFilterDto oldInstall,
                                                      TargetPackageInstalledUserFilterDto newInstall) {
        // 如果已安装用户由不限 -> 过滤
        if (oldInstall != null && oldInstall.getFilterType() != null &&
                oldInstall.getFilterType().equals(InstalledUserFilterEnum.UNLIMIT.getCode().intValue())) {
            if (newInstall != null && newInstall.getFilterType() != null &&
                    newInstall.getFilterType().equals(InstalledUserFilterEnum.FILTER.getCode().intValue())) {
                return true;
            }
        }
        return false;
    }

    private boolean convertedUserFilterClose2Open(TargetRule oldConvertedUserFilter,
                                                  TargetRule newConvertedUserFilter) {
        // 如果已转化用户过滤由关闭 -> 开启
        if (oldConvertedUserFilter == null || oldConvertedUserFilter.getValueIds() == null ||
                (oldConvertedUserFilter.getValueIds().size() == 1 && oldConvertedUserFilter.getValueIds().contains(-1))) {
            if (newConvertedUserFilter != null && newConvertedUserFilter.getValueIds() != null &&
                    !newConvertedUserFilter.getValueIds().contains(-1)) {
                return true;
            }
        }
        return false;
    }

    private void copyFromPackage2UnitId(TargetPackageUpgradeDto dto, Operator operator, Integer unitId) throws ServiceException {
        unitTargetRuleService.update(operator, unitId, dto.getTargetRules());
        if (dto.getExtraTarget() != null) {
            // uppt字段无所谓,定向包不会有"投放粉丝来源推荐包"定向
            cpcUnitServiceDelegate.saveExtraTarget(unitId, CpcUnitExtraTargetDto.builder()
                    .fansRelation(dto.getExtraTarget().getFansRelation())
                    .includeTheirsFans(dto.getExtraTarget().getIncludeTheirsFans())
                    .excludeTheirsFans(dto.getExtraTarget().getExcludeTheirsFans())
                    .interaction(dto.getExtraTarget().getInteraction())
                    .browse(dto.getExtraTarget().getBrowse())
                    .videoSecondPartition(dto.getExtraTarget().getVideoSecondPartition())
                    .recommendType(dto.getExtraTarget().getRecommendType())
                    .build(), null, operator);
        } else {
            cpcUnitServiceDelegate.saveExtraTarget(unitId, CpcUnitExtraTargetDto.getEmpty(), null, operator);
        }
        // 起飞没有商业兴趣定向
        cpcUnitServiceDelegate.updateCrowdPackIds(unitId, dto.getCrowdPackIds(), dto.getExcludeCrowdPackIds(), dto.getOtherCrowdPackIdsGroup(), dto.getExtraCrowdPackIds());
        if (dto.getTargetPackageInstalledUserFilterDto() != null) {
            cpcUnitServiceDelegate.saveInstalledUserFilterTarget(unitId,
                    UnitTargetInstalledUserFilterDto.builder().filterType(dto.getTargetPackageInstalledUserFilterDto().getFilterType())
                            .targetContent(dto.getTargetPackageInstalledUserFilterDto().getTargetContent())
                            .build());
        } else {
            cpcUnitServiceDelegate.saveInstalledUserFilterTarget(unitId, UnitTargetInstalledUserFilterDto.getEmpty());
        }
        // 起飞没有小游戏
        lauTagIdsService.updateContentTagIds(dto.getId(), LauTargetTagEnum.UNIT.getCode(), null, dto.getTags(),
                dto.getIsFuzzyTags(), null);
        lauTagIdsService.updateTags(
                dto.getId(),
                LauTargetTagEnum.UNIT.getCode(),
                UnitTargetTagDto
                        .builder()
                        .tags(dto.getVideoTag().getTags())
                        .isFuzzyTags(dto.getVideoTag().getIsFuzzyTags())
                        .type(LauTagType.VIDEO)
                        .build());
    }


    private TargetPackageUpgradeDto buildTargetPackageUpgradeDto4Copy(int targetID, TargetPackageUpgradeDto originTpuDTO, String name) {
        ResTargetPackageProfessionInterestAutoUpgradePo professionInterestAutoPo = resTargetPackageProfessionAutoUpgradeService.selectInterestAuto(targetID);
        LauTagIdsDto videoTagDto = lauTagIdsService.getByLauId(targetID, LauTargetTagEnum.PACKAGE.getCode(), LauTagType.VIDEO.getCode());
        TargetPackageTargetTagDto videoTag = videoTagDto == null ? TargetPackageTargetTagDto.getEmpty() : TargetPackageTargetTagDto.builder()
                .tags(videoTagDto.getTags())
                .isFuzzyTags(videoTagDto.getTagTargetType())
                .type(LauTagType.getByCode(videoTagDto.getTagType()))
                .build();


        return TargetPackageUpgradeDto.builder()
                .promotionPurposeType(originTpuDTO.getPromotionPurposeType())
                .adpVersion(originTpuDTO.getAdpVersion())
                .name(name)
                .description(originTpuDTO.getDescription())
                .hasStartUpCrowd(null)
                .targetRules(resTargetPackageRuleService.getTargetRules(targetID))
                .tags(Collections.emptyList())
                .isFuzzyTags(0)
                .videoTag(videoTag)
                .extraTarget(resTargetPackageExtraTargetService.getTargetPackageExtraTargetDosByTargetPackageId(targetID))
                .targetPackageInstalledUserFilterDto(resTargetPackageInstalledUserFilterService.getInstalledUserFilterTarget(targetID))
                .businessInterestIds(null)
                .includeMiniGameIds(null)
                .excludeMiniGameIds(null)
                .crowdPackIds(null)
                .excludeCrowdPackIds(null)
                .otherCrowdPackIdsGroup(null)
                .extraCrowdPackIds(null)
                .osVersion(resTargetPackageOsVersionUpgradeService.queryOsVersion(targetID))
                .biliClientVersion(resTargetPackageBiliClientUpgradeService.queryOsVersion(targetID))
                .professionInterest(resTargetPackageProfessionUpgradeService.getProfessionInterestIds(targetID))
                .professionInterestAuto(professionInterestAutoPo != null ? professionInterestAutoPo.getInterestAuto() : IsInterestAuto.NO_AUTO)
                .originTargetId(targetID)
                .videoTagDto(videoTagDto)
                .originTargetId(targetID)
                .build();
    }

    private LauTagIdsPo dtoToPo(LauTagIdsDto dto, int lauID) {
        LauTagIdsPo entity = new LauTagIdsPo();
        entity.setLauId(lauID);
        entity.setLauType(dto.getLauType());
        entity.setrTagId(dto.getRTagId());
        entity.setsTagId(dto.getSTagId());
        entity.setTagTargetType(dto.getTagTargetType());
        entity.setTagType(dto.getTagType());
        entity.setTags(Joiner.on(",").skipNulls().join(dto.getTags()));
        return entity;
    }

    private static final int PackageNameLen = 16;
    private static final String PackageNameShareSuffix = "_推送";

    private String wrapperTargetPackageShareName(String name) {
        // 获取当前时间并格式化为"年月日小时分钟"
        String suffix = PackageNameShareSuffix + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyMMddHHmm"));

        // 检查是否已经包含后缀
        int suffixIndex = name.indexOf(PackageNameShareSuffix);
        if (suffixIndex != -1) {
            name = name.substring(0, suffixIndex); // 移除旧的后缀
        }

        // 检查长度限制
        if (name.length() + suffix.length() > PackageNameLen) {
            name = name.substring(0, PackageNameLen - suffix.length()); // 截短标题
        }
        // 添加新的后缀
        return name + suffix;
    }
}
