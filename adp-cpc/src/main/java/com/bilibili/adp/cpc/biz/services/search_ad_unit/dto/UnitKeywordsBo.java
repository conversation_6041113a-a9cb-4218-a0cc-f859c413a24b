package com.bilibili.adp.cpc.biz.services.search_ad_unit.dto;

import com.bilibili.adp.cpc.biz.services.recommend.bos.AdStatSearchWordBo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UnitKeywordsBo {
    private List<String> keywords;
    private List<String> negPreciseKeywords;
    private List<String> negTermKeywords;

    // 买词list，包含了搜索词、来源
    private List<AdStatSearchWordBo> keywordsTag;

    private Integer accountId;

    // 0 新增，2更新
    private Integer operateType;
    private Integer adType;
    private Integer oldTargetExpand;
    private Integer newTargetExpand;
}
