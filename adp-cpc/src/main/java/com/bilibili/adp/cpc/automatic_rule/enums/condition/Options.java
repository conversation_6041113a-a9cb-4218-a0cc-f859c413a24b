package com.bilibili.adp.cpc.automatic_rule.enums.condition;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum Options {
    DEFAULT(0, ""),
    ACCOUNT_BUDGET(1, "账户预算"),
    UNIT_BUDGET(2, "单元预算"),
    BID_PRICE(3, "单元出价"),
    ;

    private final int code;
    private final String desc;

    public static Options getByCode(int code) {
        return Arrays.stream(values()).
                filter(options -> options.getCode() == code)
                .findFirst()
                .orElseThrow(IllegalArgumentException::new);
    }
}
