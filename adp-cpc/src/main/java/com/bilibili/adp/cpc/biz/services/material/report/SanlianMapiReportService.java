package com.bilibili.adp.cpc.biz.services.material.report;

import com.bapis.ad.archive.ArchiveInfo;
import com.bapis.ad.jupiter.archive.*;
import com.bapis.ad.jupiter.img.PicCreativeAnalysisReportReply;
import com.bapis.ad.jupiter.img.PicCreativeAnalysisReq;
import com.bapis.ad.mgk.material.MaterialIdRegistry;
import com.bapis.archive.service.*;
import com.bapis.datacenter.service.oneservice.*;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.biz.constants.Constants;
import com.bilibili.adp.cpc.biz.services.material.MaterialCenterGrpcService;
import com.bilibili.adp.cpc.biz.services.material.constants.ImageCostTableConstants;
import com.bilibili.adp.cpc.biz.services.material.enums.MaterialIdTypeEnum;
import com.bilibili.adp.cpc.biz.services.material.enums.MaterialReportTypeEnum;
import com.bilibili.adp.cpc.biz.services.material.report.bos.SanlianMapiReportArchiveDetailBo;
import com.bilibili.adp.cpc.biz.services.material.report.bos.SanlianMapiReportArchiveStatBo;
import com.bilibili.adp.cpc.biz.services.material.report.bos.SanlianMapiReportImageStatBo;
import com.bilibili.adp.cpc.biz.services.pic.bos.PicReportDataAnalysisBo;
import com.bilibili.adp.cpc.proxy.ArchiveServiceProxy;
import com.bilibili.adp.cpc.proxy.CpmJupiterProxy;
import com.bilibili.adp.cpc.proxy.CpmScvProxy;
import com.bilibili.adp.cpc.utils.ValidateUtil;
import com.bilibili.adp.resource.api.account.label.IAccountLabelService;
import com.bilibili.mgk.platform.api.archive.soa.ISoaMgkCmSpaceService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import pleiades.venus.starter.rpc.client.RPCClient;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Date;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.bilibili.adp.cpc.biz.services.material.constants.ArchiveCostTableConstants.*;
import static com.bilibili.adp.cpc.biz.services.material.constants.ConvTableConstants.AVID;
import static com.bilibili.adp.cpc.biz.services.material.constants.ConvTableConstants.LOG_DATE;
import static com.bilibili.adp.cpc.biz.services.material.constants.ConvTableConstants.*;
import static com.bilibili.adp.cpc.biz.services.material.constants.ImageCostTableConstants.IMAGE_MD5;


/**
 * @ClassName SanlianMapiReportService
 * <AUTHOR> @Date 2025/1/8 7:52 下午
 * @Version 1.0
 * 这个类不知道塞哪里 先放这里
 **/
@Component
@Slf4j
public class SanlianMapiReportService {

    @RPCClient("datacenter.oneservice.akuya-dispatch-service")
    private OneServiceOpenApiManagerGrpc.OneServiceOpenApiManagerBlockingStub oneServiceOpenApiManagerBlockingStub;

    @Resource
    private CpmScvProxy cpmScvProxy;
    @Autowired
    private MaterialCenterGrpcService materialCenterGrpcService;

    @Resource
    private ArchiveServiceProxy archiveServiceProxy;

    @Resource
    private CpmJupiterProxy cpmJupiterProxy;

    @Autowired
    private ISoaMgkCmSpaceService soaMgkCmSpaceService;

    @Autowired
    private IAccountLabelService accountLabelService;

    @Value("${sanlian.mapi.report.appkey:254184f13a6b17d99163b58829af4af7}")
    private String appkey;

    @Value("${sanlian.mapi.report.secret:xcshZ4A0Wjrtu/kcd7bWCj8u9qLKyNvndqVp8HAKEu8=}")
    private String secret;

    @Value("${sanlian.mapi.report.archive.cost.apiId:api_3463}")
    private String archiveCostApiId;

    @Value("${sanlian.mapi.report.pic.cost.apiId:api_3477}")
    private String picCostApiId;

    @Value("${sanlian.mapi.report.conv.apiId:api_3478}")
    private String convApiId;

    @Value("${sanlian.mapi.report.cost.table.creativeType.customize:0}")
    private String costTableCreativeTypeCustomize;

    @Value("${sanlian.mapi.report.cost.table.creativeType.programmatic:1}")
    private String costTableCreativeTypeProgrammatic;

    @Value("${report.order.fill.zero.label.id:732}")
    private Integer reportOrderFillZeroLabelId;

    private final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");

    private final static DateTimeFormatter formatterWithHyphen = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    private final BigDecimal thousand = new BigDecimal(1000);

    private final String tenThousand = "100000";

    private final BigDecimal hundred = new BigDecimal(100);

    private final Integer defaultScale = 4;


    public PageResult<SanlianMapiReportArchiveDetailBo> queryArchiveAnalysisReportCompleteData(List<Integer> campaignIds,
                                                                                               List<Integer> unitIds,
                                                                                               List<Integer> creativeIds,
                                                                                               List<Long> aids,
                                                                                               Integer accountId,
                                                                                               Integer sceneType,
                                                                                               Integer timeType,
                                                                                               Integer creativeType,
                                                                                               Long fromTime,
                                                                                               Long toTime,
                                                                                               String materialCenterId,
                                                                                               Integer page,
                                                                                               Integer size) {


        ValidateUtil.checkLongAdIdSize(aids, 100);

        if (StringUtils.isNotBlank(materialCenterId)) {
            List<Long> avidByMaterialCenterId = getAvidByMaterialCenterId(materialCenterId);
            aids.addAll(avidByMaterialCenterId);
        }

        Assert.notNull(accountId, "账户id不可为空");
        ValidateUtil.checkIntAdIdSize(campaignIds, 100);
        ValidateUtil.checkIntAdIdSize(unitIds, 100);
        ValidateUtil.checkIntAdIdSize(creativeIds, 100);

        Assert.isTrue(Utils.isPositive(fromTime), "查询稿件元素报表起始时间不可为空");
        Assert.isTrue(Utils.isPositive(toTime), "查询稿件元素报表结束时间不可为空");
        Assert.isTrue(toTime >= fromTime, "查询稿件素材报表结束时间不可早于开始时间");
        java.sql.Date fromDate = new java.sql.Date(fromTime),
                toDate = new java.sql.Date(toTime);

        Assert.isTrue(size <= 100, "分页大小最大为100");

        // 产品要求 间隔30天
        boolean isTimeRangeValid = toDate.toLocalDate().minusDays(31L).isBefore(fromDate.toLocalDate());
        Assert.isTrue(isTimeRangeValid, "查询稿件元素报表时间间隔最大为30天");

        PageResult<SanlianMapiReportArchiveStatBo> sanlianMapiReportStatBoPageResult = queryArchiveAnalysisReportFromOneService(campaignIds, unitIds, creativeIds, aids,
                accountId, sceneType, timeType, creativeType, fromTime, toTime, page, size);

        List<SanlianMapiReportArchiveStatBo> sanlianMapiReportArchiveStatBoList = sanlianMapiReportStatBoPageResult.getRecords();
        // 当前分页为空 直接返回
        if (CollectionUtils.isEmpty(sanlianMapiReportArchiveStatBoList)) {
            return new PageResult<>(sanlianMapiReportStatBoPageResult.getTotal(), Collections.emptyList());
        }

        List<Long> avidList = sanlianMapiReportArchiveStatBoList.stream().map(SanlianMapiReportArchiveStatBo::getAid).distinct().collect(Collectors.toList());
        ArcsRequest arcsRequest = ArcsRequest.newBuilder()
                .addAllAids(avidList)
                .build();

        ArcsReply arcs = archiveServiceProxy.arcs(arcsRequest);
        Map<Long, Arc> arcsMap = arcs.getArcsMap();

        List<Long> arcMidList = arcsMap.values().stream().map(Arc::getAuthor).map(Author::getMid).distinct().collect(Collectors.toList());

        List<Long> cmMidList = soaMgkCmSpaceService.getArcMidsInMgkCmSpace(arcMidList);

        Map<Long, String> materialCenterIdByAvid = getMaterialCenterIdByAvid(avidList);

        List<SanlianMapiReportArchiveDetailBo> result = new ArrayList<>();

        for (SanlianMapiReportArchiveStatBo sanlianMapiReportArchiveStatBo : sanlianMapiReportArchiveStatBoList) {
            SanlianMapiReportArchiveDetailBo sanlianMapiReportArchiveDetailBo = generateArchiveAnalysisDetailBo(fromTime, toTime, sanlianMapiReportArchiveStatBo, arcsMap, cmMidList, materialCenterIdByAvid);

            result.add(sanlianMapiReportArchiveDetailBo);
        }

        Boolean accountIdInLabel = accountLabelService.isAccountIdInLabel(accountId, reportOrderFillZeroLabelId);
        if (BooleanUtils.isTrue(accountIdInLabel)) {
            for (SanlianMapiReportArchiveDetailBo sanlianMapiReportArchiveDetailBo : result) {
                sanlianMapiReportArchiveDetailBo.setOrderPlaceAmount(BigDecimal.ZERO);
                sanlianMapiReportArchiveDetailBo.setOrderPlaceCount(0L);
                sanlianMapiReportArchiveDetailBo.setCostPerOrderPlace(BigDecimal.ZERO);
                sanlianMapiReportArchiveDetailBo.setOrderPlaceRate(BigDecimal.ZERO);
                sanlianMapiReportArchiveDetailBo.setOrderRoi(BigDecimal.ZERO);
            }
        }


        return new PageResult<>(sanlianMapiReportStatBoPageResult.getTotal(), result);


    }


    public PageResult<PicReportDataAnalysisBo> queryPicAnalysisReportCompleteData(List<Integer> campaignIds,
                                                                                  List<Integer> unitIds,
                                                                                  List<Integer> creativeIds,
                                                                                  Integer accountId,
                                                                                  String imageMd5,
                                                                                  Integer picType,
                                                                                  Integer creativeType,
                                                                                  Integer timeType,
                                                                                  String materialCenterId,
                                                                                  Long fromTime,
                                                                                  Long toTime,
                                                                                  Integer page,
                                                                                  Integer size) {

        Assert.isTrue(Utils.isPositive(fromTime), "查询图片元素报表起始时间不可为空");
        Assert.isTrue(Utils.isPositive(toTime), "查询图片元素报表结束时间不可为空");
        Assert.isTrue(Utils.isPositive(accountId), "账户id不可为空");
        Assert.isTrue(toTime >= fromTime, "查询图片素材报表结束时间不可早于开始时间");
        java.sql.Date fromDate = new java.sql.Date(fromTime),
                toDate = new java.sql.Date(toTime),
                nowDate = new Date(System.currentTimeMillis());
        // 产品要求 间隔31天
        boolean isTimeRangeValid = toDate.toLocalDate().minusDays(31L).isBefore(fromDate.toLocalDate());
        boolean isFromDateRangeValid = nowDate.toLocalDate().minusDays(31L).isBefore(fromDate.toLocalDate());
        Assert.isTrue(isTimeRangeValid, "查询图片元素报表时间间隔最大为30天");
        Assert.isTrue(isFromDateRangeValid, "查询图片元素报表起始时间最早为30天前");
        ValidateUtil.checkIntAdIdSize(campaignIds, 100);
        ValidateUtil.checkIntAdIdSize(unitIds, 100);
        ValidateUtil.checkIntAdIdSize(creativeIds, 100);

        Assert.isTrue(size <= 100, "分页大小最大为100");


        List<String> imageMd5List = getPicMd5List(imageMd5);
        if (StringUtils.isNotBlank(materialCenterId)) {
            List<String> md5ByMaterialCenterId = getMd5ByMaterialCenterId(materialCenterId);
            imageMd5List.addAll(md5ByMaterialCenterId);
        }

        PageResult<SanlianMapiReportImageStatBo> sanlianMapiReportImageStatBoPageResult = queryImageAnalysisReportFromOneService(campaignIds, unitIds, creativeIds, imageMd5List, accountId, picType, creativeType, timeType, fromTime, toTime, page, size);
        List<SanlianMapiReportImageStatBo> sanlianMapiReportImageStatBoList = sanlianMapiReportImageStatBoPageResult.getRecords();

        List<String> imageMd5AllList = sanlianMapiReportImageStatBoList.stream().map(SanlianMapiReportImageStatBo::getImageMd5).distinct().collect(Collectors.toList());

        Map<String, MaterialIdRegistry> byMd5sMap = materialCenterGrpcService.findByMd5s(imageMd5AllList, MaterialIdTypeEnum.IMG.getName());


        List<PicReportDataAnalysisBo> resultList = new ArrayList<>();
        for (SanlianMapiReportImageStatBo sanlianMapiReportImageStatBo : sanlianMapiReportImageStatBoList) {
            PicReportDataAnalysisBo picReportDataAnalysisBo = generatePicReportDataAnalysisBo(sanlianMapiReportImageStatBo, byMd5sMap, fromTime, toTime);
            resultList.add(picReportDataAnalysisBo);
        }


        return new PageResult<>(sanlianMapiReportImageStatBoPageResult.getTotal(), resultList);
    }

    private PicReportDataAnalysisBo generatePicReportDataAnalysisBo(SanlianMapiReportImageStatBo sanlianMapiReportImageStatBo, Map<String, MaterialIdRegistry> byMd5sMap, Long fromTime, Long toTime) {
        PicReportDataAnalysisBo picReportDataAnalysisBo = new PicReportDataAnalysisBo();
        picReportDataAnalysisBo.setDate(sanlianMapiReportImageStatBo.getDate());
        picReportDataAnalysisBo.setPicMd5(sanlianMapiReportImageStatBo.getImageMd5());
        picReportDataAnalysisBo.setPicUrl(sanlianMapiReportImageStatBo.getImageUrl());
        picReportDataAnalysisBo.setCost(BigDecimal.valueOf(sanlianMapiReportImageStatBo.getCost()));
        picReportDataAnalysisBo.setShowCount(sanlianMapiReportImageStatBo.getShowCount());
        picReportDataAnalysisBo.setClickCount(sanlianMapiReportImageStatBo.getClickCount());
        picReportDataAnalysisBo.setClickRate(BigDecimal.valueOf(sanlianMapiReportImageStatBo.getClickRate()).multiply(hundred).setScale(2, RoundingMode.HALF_UP));
        picReportDataAnalysisBo.setCostPerClick(BigDecimal.valueOf(sanlianMapiReportImageStatBo.getCostPerClick()));
        picReportDataAnalysisBo.setAverageCostPerThousand(BigDecimal.valueOf(sanlianMapiReportImageStatBo.getAverageCostPerThousand()).setScale(2, RoundingMode.HALF_UP));
        picReportDataAnalysisBo.setFormSubmitCount(sanlianMapiReportImageStatBo.getFormSubmitCount());
        picReportDataAnalysisBo.setFormSubmitAverageCost(BigDecimal.valueOf(sanlianMapiReportImageStatBo.getFormSubmitAverageCost()).setScale(2, RoundingMode.HALF_UP));
        picReportDataAnalysisBo.setFormSubmitRate(BigDecimal.valueOf(sanlianMapiReportImageStatBo.getFormSubmitRate()).multiply(hundred).setScale(2, RoundingMode.HALF_UP));
        picReportDataAnalysisBo.setFormPaidCount(sanlianMapiReportImageStatBo.getFormPaidCount());
        picReportDataAnalysisBo.setFormPaidCost(BigDecimal.valueOf(sanlianMapiReportImageStatBo.getFormPaidCost()).setScale(2, RoundingMode.HALF_UP));
        picReportDataAnalysisBo.setFormPaidRate(BigDecimal.valueOf(sanlianMapiReportImageStatBo.getFormPaidRate()).multiply(hundred).setScale(2, RoundingMode.HALF_UP));
        picReportDataAnalysisBo.setValidClueCount(sanlianMapiReportImageStatBo.getValidClueCount());
        picReportDataAnalysisBo.setValidClueAverageCost(BigDecimal.valueOf(sanlianMapiReportImageStatBo.getValidClueAverageCost()).setScale(2, RoundingMode.HALF_UP));
        picReportDataAnalysisBo.setValidClueRate(BigDecimal.valueOf(sanlianMapiReportImageStatBo.getValidClueRate()).multiply(hundred).setScale(2, RoundingMode.HALF_UP));
        picReportDataAnalysisBo.setApplyCount(sanlianMapiReportImageStatBo.getApplyCount());
        picReportDataAnalysisBo.setApplyCost(BigDecimal.valueOf(sanlianMapiReportImageStatBo.getApplyCost()).setScale(2, RoundingMode.HALF_UP));
        picReportDataAnalysisBo.setApplyRate(BigDecimal.valueOf(sanlianMapiReportImageStatBo.getApplyRate()).multiply(hundred).setScale(2, RoundingMode.HALF_UP));
        picReportDataAnalysisBo.setCreditCount(sanlianMapiReportImageStatBo.getCreditCount());
        picReportDataAnalysisBo.setCreditCost(BigDecimal.valueOf(sanlianMapiReportImageStatBo.getCreditCost()).setScale(2, RoundingMode.HALF_UP));
        picReportDataAnalysisBo.setCreditRate(BigDecimal.valueOf(sanlianMapiReportImageStatBo.getCreditRate()).multiply(hundred).setScale(2, RoundingMode.HALF_UP));
        picReportDataAnalysisBo.setWithdrawDepositsCount(sanlianMapiReportImageStatBo.getWithdrawDepositsCount());
        picReportDataAnalysisBo.setWithdrawDepositsRate(BigDecimal.valueOf(sanlianMapiReportImageStatBo.getWithdrawDepositsRate()).multiply(hundred).setScale(2, RoundingMode.HALF_UP));
        picReportDataAnalysisBo.setWithdrawDepositsCost(BigDecimal.valueOf(sanlianMapiReportImageStatBo.getWithdrawDepositsCost()).setScale(2, RoundingMode.HALF_UP));
//        picReportDataAnalysisBo.setFirstWithdrawCount(sanlianMapiReportImageStatBo.getFirstWithdrawCount());
//        picReportDataAnalysisBo.setFirstWithdrawCost(BigDecimal.valueOf(sanlianMapiReportImageStatBo.getCostPerFirstWithdraw()).setScale(2, RoundingMode.HALF_UP));
//        picReportDataAnalysisBo.setFirstWithdrawRate(BigDecimal.valueOf(sanlianMapiReportImageStatBo.getFirstWithdrawRate()).multiply(hundred).setScale(2, RoundingMode.HALF_UP));
        picReportDataAnalysisBo.setGameReserveCount(sanlianMapiReportImageStatBo.getGameReserveCount());
        picReportDataAnalysisBo.setGameReserveAverageCost(BigDecimal.valueOf(sanlianMapiReportImageStatBo.getGameReserveAverageCost()).setScale(2, RoundingMode.HALF_UP));
        picReportDataAnalysisBo.setGameReserveRate(BigDecimal.valueOf(sanlianMapiReportImageStatBo.getGameReserveRate()).multiply(hundred).setScale(2, RoundingMode.HALF_UP));
        picReportDataAnalysisBo.setAndroidDownloadCount(sanlianMapiReportImageStatBo.getAndroidDownloadCount());
        picReportDataAnalysisBo.setAndroidDownloadAverageCost(BigDecimal.valueOf(sanlianMapiReportImageStatBo.getAndroidDownloadAverageCost()).setScale(2, RoundingMode.HALF_UP));
        picReportDataAnalysisBo.setAndroidDownloadRate(BigDecimal.valueOf(sanlianMapiReportImageStatBo.getAndroidDownloadRate()).multiply(hundred).setScale(2, RoundingMode.HALF_UP));
        picReportDataAnalysisBo.setAndroidInstallCount(sanlianMapiReportImageStatBo.getAndroidInstallCount());
        picReportDataAnalysisBo.setAndroidInstallAverageCost(BigDecimal.valueOf(sanlianMapiReportImageStatBo.getAndroidInstallAverageCost()).setScale(2, RoundingMode.HALF_UP));
        picReportDataAnalysisBo.setAndroidInstallRate(BigDecimal.valueOf(sanlianMapiReportImageStatBo.getAndroidInstallRate()).multiply(hundred).setScale(2, RoundingMode.HALF_UP));
        picReportDataAnalysisBo.setIosActivateCount(sanlianMapiReportImageStatBo.getIosActivateCount());
        picReportDataAnalysisBo.setAndroidActivateCount(sanlianMapiReportImageStatBo.getAndroidActivateCount());
        picReportDataAnalysisBo.setActivateCount(sanlianMapiReportImageStatBo.getActivateCount());
        picReportDataAnalysisBo.setAppActivateAverageCost(BigDecimal.valueOf(sanlianMapiReportImageStatBo.getAppActivateAverageCost()).setScale(2, RoundingMode.HALF_UP));
        picReportDataAnalysisBo.setAppActivateRate(BigDecimal.valueOf(sanlianMapiReportImageStatBo.getAppActivateRate()).multiply(hundred).setScale(2, RoundingMode.HALF_UP));
        picReportDataAnalysisBo.setAndroidGameCenterActivationCount(sanlianMapiReportImageStatBo.getAndroidGameCenterActivationCount());
        picReportDataAnalysisBo.setAndroidGameCenterActivationCost(BigDecimal.valueOf(sanlianMapiReportImageStatBo.getAndroidGameCenterActivationCost()).setScale(2, RoundingMode.HALF_UP));
        picReportDataAnalysisBo.setAndroidGameCenterActivationRate(BigDecimal.valueOf(sanlianMapiReportImageStatBo.getAndroidGameCenterActivationRate()).multiply(hundred).setScale(2, RoundingMode.HALF_UP));
        picReportDataAnalysisBo.setRegisterCount(sanlianMapiReportImageStatBo.getRegisterCount());
        picReportDataAnalysisBo.setRegisterAverageCost(BigDecimal.valueOf(sanlianMapiReportImageStatBo.getRegisterAverageCost()).setScale(2, RoundingMode.HALF_UP));
        picReportDataAnalysisBo.setRegisterRate(BigDecimal.valueOf(sanlianMapiReportImageStatBo.getRegisterRate()).multiply(hundred).setScale(2, RoundingMode.HALF_UP));
        picReportDataAnalysisBo.setOrderPayCount(sanlianMapiReportImageStatBo.getOrderPayCount());
        picReportDataAnalysisBo.setOrderPayAmount(BigDecimal.valueOf(sanlianMapiReportImageStatBo.getOrderPayAmount()).setScale(2, RoundingMode.HALF_UP));
        picReportDataAnalysisBo.setOrderFirstPayCount(sanlianMapiReportImageStatBo.getOrderFirstPayCount());
        picReportDataAnalysisBo.setOrderFirstPayAmount(BigDecimal.valueOf(sanlianMapiReportImageStatBo.getOrderFirstPayAmount()).setScale(2, RoundingMode.HALF_UP));
        picReportDataAnalysisBo.setOrderFirstPayCost(BigDecimal.valueOf(sanlianMapiReportImageStatBo.getOrderFirstPayCost()).setScale(2, RoundingMode.HALF_UP));
        picReportDataAnalysisBo.setOrderFirstPayRate(BigDecimal.valueOf(sanlianMapiReportImageStatBo.getOrderFirstPayRate()).multiply(hundred).setScale(2, RoundingMode.HALF_UP));
        picReportDataAnalysisBo.setAndroidGameCenterPaymentInAppCount(sanlianMapiReportImageStatBo.getAndroidGameCenterPaymentInAppCount());
        picReportDataAnalysisBo.setAndroidGameCenterFirstPaymentInAppCount(sanlianMapiReportImageStatBo.getAndroidGameCenterFirstPaymentInAppCount());
        picReportDataAnalysisBo.setAndroidGameCenterFirstPaymentInAppCost(BigDecimal.valueOf(sanlianMapiReportImageStatBo.getAndroidGameCenterFirstPaymentInAppCost()).setScale(2, RoundingMode.HALF_UP));
        picReportDataAnalysisBo.setAndroidGameCenterFirstPaymentInAppRate(BigDecimal.valueOf(sanlianMapiReportImageStatBo.getAndroidGameCenterFirstPaymentInAppRate()).multiply(hundred).setScale(2, RoundingMode.HALF_UP));
        picReportDataAnalysisBo.setRetentionCount(sanlianMapiReportImageStatBo.getRetentionCount());
        picReportDataAnalysisBo.setRetentionRate(BigDecimal.valueOf(sanlianMapiReportImageStatBo.getRetentionRate()).multiply(hundred).setScale(2, RoundingMode.HALF_UP));
        picReportDataAnalysisBo.setRetentionCost(BigDecimal.valueOf(sanlianMapiReportImageStatBo.getRetentionCost()).setScale(2, RoundingMode.HALF_UP));
        picReportDataAnalysisBo.setAppCallupCount(sanlianMapiReportImageStatBo.getAppCallupCount());
        picReportDataAnalysisBo.setAppCallupCost(BigDecimal.valueOf(sanlianMapiReportImageStatBo.getAppCallupCost()).setScale(2, RoundingMode.HALF_UP));
        picReportDataAnalysisBo.setAppCallupRate(BigDecimal.valueOf(sanlianMapiReportImageStatBo.getAppCallupRate()).multiply(hundred).setScale(2, RoundingMode.HALF_UP));
        picReportDataAnalysisBo.setLpCallUpSuccessStayCount(sanlianMapiReportImageStatBo.getLpCallUpSuccessStayCount());
        picReportDataAnalysisBo.setLpCallUpSuccessStayRate(BigDecimal.valueOf(sanlianMapiReportImageStatBo.getLpCallUpSuccessStayRate()).multiply(hundred).setScale(2, RoundingMode.HALF_UP));
        picReportDataAnalysisBo.setLpCallUpSuccessStayCost(BigDecimal.valueOf(sanlianMapiReportImageStatBo.getLpCallUpSuccessStayCost()).setScale(2, RoundingMode.HALF_UP));
        picReportDataAnalysisBo.setOrderSubmitCount(sanlianMapiReportImageStatBo.getOrderSubmitCount());
        picReportDataAnalysisBo.setOrderSubmitAmount(BigDecimal.valueOf(sanlianMapiReportImageStatBo.getOrderSubmitAmount()).setScale(2, RoundingMode.HALF_UP));
        picReportDataAnalysisBo.setOrderSubmitCost(BigDecimal.valueOf(sanlianMapiReportImageStatBo.getOrderSubmitCost()).setScale(2, RoundingMode.HALF_UP));
        picReportDataAnalysisBo.setOrderSubmitRate(BigDecimal.valueOf(sanlianMapiReportImageStatBo.getOrderSubmitRate()).multiply(hundred).setScale(2, RoundingMode.HALF_UP));
        picReportDataAnalysisBo.setGoodsRoi(BigDecimal.valueOf(sanlianMapiReportImageStatBo.getGoodsRoi()).setScale(2, RoundingMode.HALF_UP));
        picReportDataAnalysisBo.setFirstOrderPlaceCount(sanlianMapiReportImageStatBo.getFirstOrderPlaceCount());
        picReportDataAnalysisBo.setFirstOrderPlaceAmount(BigDecimal.valueOf(sanlianMapiReportImageStatBo.getFirstOrderPlaceAmount()).setScale(2, RoundingMode.HALF_UP));
        picReportDataAnalysisBo.setFirstOrderPlaceCost(BigDecimal.valueOf(sanlianMapiReportImageStatBo.getFirstOrderPlaceCost()).setScale(2, RoundingMode.HALF_UP));
        picReportDataAnalysisBo.setFirstOrderPlaceRate(BigDecimal.valueOf(sanlianMapiReportImageStatBo.getFirstOrderPlaceRate()).multiply(hundred).setScale(2, RoundingMode.HALF_UP));
        picReportDataAnalysisBo.setKeyBehaviorCount(sanlianMapiReportImageStatBo.getKeyBehaviorCount());
        picReportDataAnalysisBo.setKeyBehaviorRate(BigDecimal.valueOf(sanlianMapiReportImageStatBo.getKeyBehaviorRate()).multiply(hundred).setScale(2, RoundingMode.HALF_UP));
        picReportDataAnalysisBo.setKeyBehaviorCost(BigDecimal.valueOf(sanlianMapiReportImageStatBo.getKeyBehaviorCost()).setScale(2, RoundingMode.HALF_UP));
        picReportDataAnalysisBo.setMaterialCenterId(Optional.ofNullable(byMd5sMap.get(sanlianMapiReportImageStatBo.getImageMd5())).map(MaterialIdRegistry::getMaterialId).orElse(""));
        picReportDataAnalysisBo.setMaterialUrl(sanlianMapiReportImageStatBo.getImageUrl());

        // 游戏充值相关字段处理
        // 激活后24小时变现相关字段
        picReportDataAnalysisBo.setGameChargeIn24hCount(sanlianMapiReportImageStatBo.getGameChargeIn24HCount());
        picReportDataAnalysisBo.setCostPerGameChargeIn24h(BigDecimal.valueOf(sanlianMapiReportImageStatBo.getCostPerGameChargeIn24H()).setScale(2, RoundingMode.HALF_UP));
        picReportDataAnalysisBo.setGameChargeIn24hAmount(BigDecimal.valueOf(sanlianMapiReportImageStatBo.getGameChargeIn24HAmount()).divide(hundred, 2, RoundingMode.HALF_UP));
        picReportDataAnalysisBo.setGameChargeIn24hRoi(BigDecimal.valueOf(sanlianMapiReportImageStatBo.getGameChargeIn24HRoi()).setScale(2, RoundingMode.HALF_UP));

        // 激活后首自然日变现相关字段
        picReportDataAnalysisBo.setGameChargeIn1dCount(sanlianMapiReportImageStatBo.getGameChargeIn1DCount());
        picReportDataAnalysisBo.setCostPerGameChargeIn1d(BigDecimal.valueOf(sanlianMapiReportImageStatBo.getCostPerGameChargeIn1D()).setScale(2, RoundingMode.HALF_UP));
        picReportDataAnalysisBo.setGameChargeIn1dAmount(BigDecimal.valueOf(sanlianMapiReportImageStatBo.getGameChargeIn1DAmount()).divide(hundred, 2, RoundingMode.HALF_UP));
        picReportDataAnalysisBo.setGameChargeIn1dRoi(BigDecimal.valueOf(sanlianMapiReportImageStatBo.getGameChargeIn1DRoi()).setScale(2, RoundingMode.HALF_UP));

        // 激活后7日变现相关字段
        picReportDataAnalysisBo.setGameChargeIn7dCount(sanlianMapiReportImageStatBo.getGameChargeIn7DCount());
        picReportDataAnalysisBo.setCostPerGameChargeIn7d(BigDecimal.valueOf(sanlianMapiReportImageStatBo.getCostPerGameChargeIn7D()).setScale(2, RoundingMode.HALF_UP));
        picReportDataAnalysisBo.setGameChargeIn7dAmount(BigDecimal.valueOf(sanlianMapiReportImageStatBo.getGameChargeIn7DAmount()).divide(hundred, 2, RoundingMode.HALF_UP));
        picReportDataAnalysisBo.setGameChargeIn7dRoi(BigDecimal.valueOf(sanlianMapiReportImageStatBo.getGameChargeIn7DRoi()).setScale(2, RoundingMode.HALF_UP));

        // 激活后14日变现相关字段
        picReportDataAnalysisBo.setGameChargeIn14dCount(sanlianMapiReportImageStatBo.getGameChargeIn14DCount());
        picReportDataAnalysisBo.setCostPerGameChargeIn14d(BigDecimal.valueOf(sanlianMapiReportImageStatBo.getCostPerGameChargeIn14D()).setScale(2, RoundingMode.HALF_UP));
        picReportDataAnalysisBo.setGameChargeIn14dAmount(BigDecimal.valueOf(sanlianMapiReportImageStatBo.getGameChargeIn14DAmount()).divide(hundred, 2, RoundingMode.HALF_UP));
        picReportDataAnalysisBo.setGameChargeIn14dRoi(BigDecimal.valueOf(sanlianMapiReportImageStatBo.getGameChargeIn14DRoi()).setScale(2, RoundingMode.HALF_UP));

        // 混合变现相关字段
        picReportDataAnalysisBo.setGameChargeIn24hMixAmount(BigDecimal.valueOf(sanlianMapiReportImageStatBo.getGameChargeIn24HMixAmount()).divide(hundred, 2, RoundingMode.HALF_UP));
        picReportDataAnalysisBo.setGameChargeIn24hMixRoi(BigDecimal.valueOf(sanlianMapiReportImageStatBo.getGameChargeIn24HMixRoi()).setScale(2, RoundingMode.HALF_UP));
        picReportDataAnalysisBo.setGameChargeIn1dMixAmount(BigDecimal.valueOf(sanlianMapiReportImageStatBo.getGameChargeIn1DMixAmount()).divide(hundred, 2, RoundingMode.HALF_UP));
        picReportDataAnalysisBo.setGameChargeIn1dMixRoi(BigDecimal.valueOf(sanlianMapiReportImageStatBo.getGameChargeIn1DMixRoi()).setScale(2, RoundingMode.HALF_UP));
        picReportDataAnalysisBo.setGameChargeIn7dMixAmount(BigDecimal.valueOf(sanlianMapiReportImageStatBo.getGameChargeIn7DMixAmount()).divide(hundred, 2, RoundingMode.HALF_UP));
        picReportDataAnalysisBo.setGameChargeIn7dMixRoi(BigDecimal.valueOf(sanlianMapiReportImageStatBo.getGameChargeIn7DMixRoi()).setScale(2, RoundingMode.HALF_UP));
        picReportDataAnalysisBo.setGameChargeIn14dMixAmount(BigDecimal.valueOf(sanlianMapiReportImageStatBo.getGameChargeIn14DMixAmount()).divide(hundred, 2, RoundingMode.HALF_UP));
        picReportDataAnalysisBo.setGameChargeIn14dMixRoi(BigDecimal.valueOf(sanlianMapiReportImageStatBo.getGameChargeIn14DMixRoi()).setScale(2, RoundingMode.HALF_UP));

        if (StringUtils.isEmpty(picReportDataAnalysisBo.getDate())) {
            picReportDataAnalysisBo.setDate(time2String(fromTime) + "~" + time2String(toTime));
        } else {
            picReportDataAnalysisBo.setDate(picReportDataAnalysisBo.getDate());
        }

        return picReportDataAnalysisBo;
    }

    public PageResult<SanlianMapiReportArchiveStatBo> queryArchiveAnalysisReportFromOneService(List<Integer> campaignIds,
                                                                                               List<Integer> unitIds,
                                                                                               List<Integer> creativeIds,
                                                                                               List<Long> aids,
                                                                                               Integer accountId,
                                                                                               Integer sceneType,
                                                                                               Integer timeType,
                                                                                               Integer creativeType,
                                                                                               Long fromTime,
                                                                                               Long toTime,
                                                                                               Integer page,
                                                                                               Integer size) {

        StatReq.Builder builder = StatReq.newBuilder()
                .setType(Type.ARC)
                .setSortField("cost")
                .setSortOrder(SortOrder.DESC);

        Optional.ofNullable(timeType).ifPresent(builder::setTimeTypeValue);
        Optional.ofNullable(fromTime).ifPresent(builder::setStartTime);
        Optional.ofNullable(toTime).ifPresent(builder::setEndTime);
        Optional.ofNullable(aids).filter(CollectionUtils::isNotEmpty).ifPresent(builder::addAllAids);
        Optional.ofNullable(accountId).ifPresent(builder::addAccountIds);
        Optional.ofNullable(campaignIds).filter(CollectionUtils::isNotEmpty)
                .ifPresent(ids -> builder.addAllCampaignIds(ids.stream().map(Integer::longValue).collect(Collectors.toList())));
        Optional.ofNullable(unitIds).filter(CollectionUtils::isNotEmpty)
                .ifPresent(ids -> builder.addAllUnitIds(ids.stream().map(Integer::longValue).collect(Collectors.toList())));
        Optional.ofNullable(creativeIds).filter(CollectionUtils::isNotEmpty)
                .ifPresent(ids -> builder.addAllCreativeIds(ids.stream().map(Integer::longValue).collect(Collectors.toList())));
        Optional.ofNullable(sceneType).ifPresent(builder::setSceneTypeValue);
        Optional.ofNullable(creativeType).ifPresent(builder::setCreativeTypeValue);
        Optional.ofNullable(page).ifPresent(builder::setPn);
        Optional.ofNullable(size).ifPresent(builder::setPs);
        builder.setIsManaged(Constants.OPTION_ALL);
        builder.setSupportAuto(Constants.OPTION_ALL);
        StatResp stats = cpmJupiterProxy.stats(builder.build());

        List<SanlianMapiReportArchiveStatBo> result = stats.getStatsList().stream()
                .map(SanlianMapiReportMapper.INSTANCE::toArchiveBo).collect(Collectors.toList());

        return new PageResult<>(Math.toIntExact(stats.getTotal()), result);
    }


    public PageResult<SanlianMapiReportImageStatBo> queryImageAnalysisReportFromOneService(List<Integer> campaignIds,
                                                                                           List<Integer> unitIds,
                                                                                           List<Integer> creativeIds,
                                                                                           List<String> imageMd5List,
                                                                                           Integer accountId,
                                                                                           Integer picType,
                                                                                           Integer creativeType,
                                                                                           Integer timeType,
                                                                                           Long fromTime,
                                                                                           Long toTime,
                                                                                           Integer page,
                                                                                           Integer size) {


        PicCreativeAnalysisReq.Builder builder = PicCreativeAnalysisReq.newBuilder();

        Optional.ofNullable(accountId).ifPresent(builder::addAccountId);
        Optional.ofNullable(imageMd5List).filter(CollectionUtils::isNotEmpty).ifPresent(builder::addAllImageMd5);
        Optional.ofNullable(campaignIds).filter(CollectionUtils::isNotEmpty).ifPresent(builder::addAllCampaignId);
        Optional.ofNullable(unitIds).filter(CollectionUtils::isNotEmpty).ifPresent(builder::addAllUnitId);
        Optional.ofNullable(creativeIds).filter(CollectionUtils::isNotEmpty).ifPresent(builder::addAllCreativeId);
        Optional.ofNullable(picType).ifPresent(builder::setPicTypeValue);
        Optional.ofNullable(fromTime).ifPresent(builder::setFromTime);
        Optional.ofNullable(toTime).ifPresent(builder::setToTime);
        Optional.ofNullable(creativeType).ifPresent(builder::setCreateTypeValue);
        Optional.ofNullable(timeType).ifPresent(builder::setTimeTypeValue);
        Optional.ofNullable(page).ifPresent(builder::setPage);
        Optional.ofNullable(size).ifPresent(builder::setPageSize);
        builder.setIsManaged(Constants.OPTION_ALL);
        builder.setSupportAuto(Constants.OPTION_ALL);

        PicCreativeAnalysisReportReply reply = cpmJupiterProxy.picCreativeAnalysisList(builder.build());
        List<SanlianMapiReportImageStatBo> result = reply.getDataList().stream()
                .map(SanlianMapiReportMapper.INSTANCE::toImageBo)
                .collect(Collectors.toList());

        return new PageResult<>(reply.getTotal(), result);
    }


    private QueryResp queryConvQueryResp(List<Integer> campaignIds, List<Integer> unitIds, List<Integer> creativeIds,
                                         List<Long> aids, List<String> imageMd5List, Integer accountId, Integer timeType,
                                         Integer creativeType, Integer picType, Long fromTime, Long toTime, Integer reportType) {
        QueryReq.Builder convReqBuild = QueryReq.newBuilder();
        convReqBuild.setOsHeader(getConvHeader());

        List<OperatorVo> convReqList = generateConvReq(campaignIds, unitIds, creativeIds, aids, imageMd5List, accountId, picType, creativeType, fromTime, toTime, reportType);
        convReqBuild.addAllReqs(convReqList);

        ArrayList<String> convRespList = Lists.newArrayList(CONV_VALUE_SUM, CONV_VALUE_COUNT, CONV_TYPE, DEVICE_TYPE);
        if (Objects.equals(reportType, MaterialReportTypeEnum.VIDEO.getCode())) {
            convRespList.addAll(Lists.newArrayList(AVID));
        } else if (Objects.equals(reportType, MaterialReportTypeEnum.IMAGE.getCode())) {
            convRespList.addAll(Lists.newArrayList(IMAGE_MD5));
        }

        if (Objects.equals(timeType, TimeType.DAY_VALUE)) {
            convRespList.add(LOG_DATE);
        }
        convReqBuild.addAllResps(convRespList);
        QueryResp convQueryResp = oneServiceOpenApiManagerBlockingStub.query(convReqBuild.build());
        return convQueryResp;
    }

    public QueryResp queryImageCostQueryResp(Integer accountId, List<Integer> campaignIds, List<Integer> unitIds,
                                             List<Integer> creativeIds, List<String> imageMd5List, Integer picType,
                                             Integer creativeType, Integer timeType, Boolean needFilter, Long fromTime,
                                             Long toTime, Integer page, Integer pageSize) {
        QueryReq.Builder imageCostReqBuild = QueryReq.newBuilder();
        imageCostReqBuild.setOsHeader(getImageCostHeader());

        List<OperatorVo> reqList = generateImageCostReq(accountId, campaignIds, unitIds, creativeIds, imageMd5List, picType, creativeType, needFilter, fromTime, toTime, page, pageSize);
        imageCostReqBuild.addAllReqs(reqList);

        ArrayList<String> imageCostRespList = Lists.newArrayList(IMAGE_MD5, ImageCostTableConstants.IMAGE_URL, ImageCostTableConstants.PV_SUM, ImageCostTableConstants.CLICK_SUM, ImageCostTableConstants.COST_MILLI_SUM);
        if (Objects.equals(timeType, TimeType.DAY_VALUE)) {
            imageCostRespList.add(ImageCostTableConstants.LOG_DATE);
        }

        imageCostReqBuild.addAllResps(imageCostRespList);
        imageCostReqBuild.setPageReq(PageReq.newBuilder().setPage(page).setPageSize(pageSize).build());
        imageCostReqBuild.addAllOrders(Lists.newArrayList("cost_milli_sum desc"));
        QueryResp imageCostQueryResp = oneServiceOpenApiManagerBlockingStub.query(imageCostReqBuild.build());
        return imageCostQueryResp;

    }

    private QueryResp queryArchiveCostQueryResp(List<Integer> campaignIds, List<Integer> unitIds, List<Integer> creativeIds,
                                                List<Long> aids, Integer accountId, Integer sceneType, Integer timeType,
                                                Integer creativeType, Long fromTime, Long toTime, Integer page, Integer size) {
        QueryReq.Builder archiveCostReqBuild = QueryReq.newBuilder();
        archiveCostReqBuild.setOsHeader(getArchiveCostHeader());

        List<OperatorVo> reqList = generateArchiveCostReq(campaignIds, unitIds, creativeIds, aids, accountId, sceneType, creativeType, fromTime, toTime);
        archiveCostReqBuild.addAllReqs(reqList);

        ArrayList<String> archiveCostRespList = Lists.newArrayList(ACCOUNT_ID, AVID, PV_SUM, CLICK_SUM, COST_MILLI_SUM,
                PLAY_3S_CNT_SUM, PLAY_5S_CNT_SUM, PLAY_10S_CNT_SUM, PLAY_CNT_SUM, PLAY_DAILY_MAX, REPLY_DAILY_SUM,
                FAV_DAILY_SUM, COIN_DAILY_SUM, DANMU_DAILY_SUM, SHARE_DAILY_SUM, LIKES_DAILY_SUM, VIDEO_PLAY_SUM,
                VIDEO_LIKE_SUM, FIRST_COMMENT_COPY_SUM, COMMENT_URL_SHOW_SUM);
        if (Objects.equals(timeType, TimeType.DAY_VALUE)) {
            archiveCostRespList.add(LOG_DATE);
        }
        archiveCostReqBuild.addAllResps(archiveCostRespList);
        archiveCostReqBuild.setPageReq(PageReq.newBuilder().setPage(page).setPageSize(size).build());
        archiveCostReqBuild.addAllOrders(Lists.newArrayList("cost_milli_sum desc"));
        QueryResp archiveCostQueryResp = oneServiceOpenApiManagerBlockingStub.query(archiveCostReqBuild.build());
        return archiveCostQueryResp;
    }

    private Map<String, Long> generateConvTypeValueMap(List<MapValue> convMapValueList) {
        Map<String, Long> convTypeValueMap = new HashMap<>();

        for (MapValue convMapValue : convMapValueList) {
            Map<String, String> convValueMap = convMapValue.getValueMap();
            String convType = convValueMap.get(CONV_TYPE);
            String deviceType = convValueMap.get(DEVICE_TYPE);
            Long convValueCount = Long.parseLong(convValueMap.getOrDefault(CONV_VALUE_COUNT, "0"));
            Long convValueSum = Long.parseLong(convValueMap.getOrDefault(CONV_VALUE_SUM, "0"));

            if (Objects.equals(convType, FAN_INCREASE)) {
                convTypeValueMap.put(FAN_INCREASE_COUNT, convTypeValueMap.getOrDefault(FAN_INCREASE_COUNT, 0L) + convValueCount);
            }
            if (Objects.equals(convType, UNDER_BOX_LINK)) {
                convTypeValueMap.put(UNDER_BOX_LINK_COUNT, convTypeValueMap.getOrDefault(UNDER_BOX_LINK_COUNT, 0L) + convValueCount);
            }
            if (Objects.equals(convType, COMMENT_URL_CLICK)) {
                convTypeValueMap.put(COMMENT_URL_CLICK_COUNT, convTypeValueMap.getOrDefault(COMMENT_URL_CLICK_COUNT, 0L) + convValueCount);
            }
            if (Objects.equals(convType, FORM_SUBMIT)) {
                convTypeValueMap.put(FORM_SUBMIT_COUNT, convTypeValueMap.getOrDefault(FORM_SUBMIT_COUNT, 0L) + convValueCount);
            }
            if (Objects.equals(convType, FORM_USER_COST)) {
                convTypeValueMap.put(FORM_USER_COST_COUNT, convTypeValueMap.getOrDefault(FORM_USER_COST_COUNT, 0L) + convValueCount);
            }
            if (Objects.equals(convType, CLUE_VALID)) {
                convTypeValueMap.put(CLUE_VALID_COUNT, convTypeValueMap.getOrDefault(CLUE_VALID_COUNT, 0L) + convValueCount);
            }
            if (Objects.equals(convType, APPLY)) {
                convTypeValueMap.put(APPLY_COUNT, convTypeValueMap.getOrDefault(APPLY_COUNT, 0L) + convValueCount);
            }
            if (Objects.equals(convType, CREDIT)) {
                convTypeValueMap.put(CREDIT_COUNT, convTypeValueMap.getOrDefault(CREDIT_COUNT, 0L) + convValueCount);
            }
            if (Objects.equals(convType, WITHDRAW_DEPOSITS)) {
                convTypeValueMap.put(WITHDRAW_DEPOSITS_COUNT, convTypeValueMap.getOrDefault(WITHDRAW_DEPOSITS_COUNT, 0L) + convValueCount);
            }
            if (Objects.equals(convType, FIRST_WITHDRAW)) {
                convTypeValueMap.put(FIRST_WITHDRAW_COUNT, convTypeValueMap.getOrDefault(FIRST_WITHDRAW_COUNT, 0L) + convValueCount);
            }
            if (Objects.equals(convType, GAME_SUBSCRIBE_API)) {
                convTypeValueMap.put(GAME_SUBSCRIBE_API_COUNT, convTypeValueMap.getOrDefault(GAME_SUBSCRIBE_API_COUNT, 0L) + convValueCount);
            }
            if (Objects.equals(convType, DOWNLOAD_SUCCESS)) {
                convTypeValueMap.put(DOWNLOAD_SUCCESS_COUNT, convTypeValueMap.getOrDefault(DOWNLOAD_SUCCESS_COUNT, 0L) + convValueCount);
            }
            if (Objects.equals(convType, INSTALL_SUCCESS)) {
                convTypeValueMap.put(INSTALL_SUCCESS_COUNT, convTypeValueMap.getOrDefault(INSTALL_SUCCESS_COUNT, 0L) + convValueCount);
            }
            if (Objects.equals(convType, USER_REGISTER)) {
                convTypeValueMap.put(USER_REGISTER_COUNT, convTypeValueMap.getOrDefault(USER_REGISTER_COUNT, 0L) + convValueCount);
            }
            if (Objects.equals(convType, APP_ACTIVE)) {
                convTypeValueMap.put(APP_ACTIVE_COUNT, convTypeValueMap.getOrDefault(APP_ACTIVE_COUNT, 0L) + convValueCount);
                if (Objects.equals(deviceType, "1")) {
                    convTypeValueMap.put(IOS_APP_ACTIVE_COUNT, convTypeValueMap.getOrDefault(IOS_APP_ACTIVE_COUNT, 0L) + convValueCount);
                }
                if (Objects.equals(deviceType, "2")) {
                    convTypeValueMap.put(ANDROID_APP_ACTIVE_COUNT, convTypeValueMap.getOrDefault(ANDROID_APP_ACTIVE_COUNT, 0L) + convValueCount);
                }
            }
            if (Objects.equals(convType, APP_FIRST_ACTIVE)) {
                convTypeValueMap.put(APP_ACTIVE_COUNT, convTypeValueMap.getOrDefault(APP_ACTIVE_COUNT, 0L) + convValueCount);
                if (Objects.equals(deviceType, "1")) {
                    convTypeValueMap.put(IOS_APP_ACTIVE_COUNT, convTypeValueMap.getOrDefault(IOS_APP_ACTIVE_COUNT, 0L) + convValueCount);
                }
                if (Objects.equals(deviceType, "2")) {
                    convTypeValueMap.put(ANDROID_APP_ACTIVE_COUNT, convTypeValueMap.getOrDefault(ANDROID_APP_ACTIVE_COUNT, 0L) + convValueCount);
                }
            }
            if (Objects.equals(convType, GAME_ACTIVE_API)) {
                convTypeValueMap.put(GAME_ACTIVE_API_COUNT, convTypeValueMap.getOrDefault(GAME_ACTIVE_API_COUNT, 0L) + convValueCount);
            }
            if (Objects.equals(convType, USER_COST)) {
                convTypeValueMap.put(USER_COST_COUNT, convTypeValueMap.getOrDefault(USER_COST_COUNT, 0L) + convValueCount);
                convTypeValueMap.put(USER_COST_SUM, convTypeValueMap.getOrDefault(USER_COST_SUM, 0L) + convValueSum);
            }
            if (Objects.equals(convType, USER_FIRST_COST)) {
                convTypeValueMap.put(USER_FIRST_COST_COUNT, convTypeValueMap.getOrDefault(USER_FIRST_COST_COUNT, 0L) + convValueCount);
                convTypeValueMap.put(USER_FIRST_COST_SUM, convTypeValueMap.getOrDefault(USER_FIRST_COST_SUM, 0L) + convValueSum);
            }
            if (Objects.equals(convType, GAME_USER_COST)) {
                convTypeValueMap.put(GAME_USER_COST_COUNT, convTypeValueMap.getOrDefault(GAME_USER_COST_COUNT, 0L) + convValueCount);
                convTypeValueMap.put(GAME_USER_COST_SUM, convTypeValueMap.getOrDefault(GAME_USER_COST_SUM, 0L) + convValueSum);
            }
            if (Objects.equals(convType, GAME_USER_FIRST_COST)) {
                convTypeValueMap.put(GAME_USER_FIRST_COST_COUNT, convTypeValueMap.getOrDefault(GAME_USER_FIRST_COST_COUNT, 0L) + convValueCount);
                convTypeValueMap.put(GAME_USER_FIRST_COST_SUM, convTypeValueMap.getOrDefault(GAME_USER_FIRST_COST_SUM, 0L) + convValueSum);
            }
            if (Objects.equals(convType, RETENTION)) {
                convTypeValueMap.put(RETENTION_COUNT, convTypeValueMap.getOrDefault(RETENTION_COUNT, 0L) + convValueCount);
            }
            if (Objects.equals(convType, APP_CALLUP)) {
                convTypeValueMap.put(APP_CALLUP_COUNT, convTypeValueMap.getOrDefault(APP_CALLUP_COUNT, 0L) + convValueCount);
            }
            if (Objects.equals(convType, LP_CALLUP_SUCC)) {
                convTypeValueMap.put(LP_CALLUP_SUCC_COUNT, convTypeValueMap.getOrDefault(LP_CALLUP_SUCC_COUNT, 0L) + convValueCount);
            }
            if (Objects.equals(convType, LP_CALLUP_SUCC_STAY)) {
                convTypeValueMap.put(LP_CALLUP_SUCC_STAY_COUNT, convTypeValueMap.getOrDefault(LP_CALLUP_SUCC_STAY_COUNT, 0L) + convValueCount);
            }
            if (Objects.equals(convType, ORDER_PLACE)) {
                convTypeValueMap.put(ORDER_PLACE_COUNT, convTypeValueMap.getOrDefault(ORDER_PLACE_COUNT, 0L) + convValueCount);
                convTypeValueMap.put(ORDER_PLACE_SUM, convTypeValueMap.getOrDefault(ORDER_PLACE_SUM, 0L) + convValueSum);
            }
            if (Objects.equals(convType, FIRST_ORDER_PLACE)) {
                convTypeValueMap.put(FIRST_ORDER_PLACE_COUNT, convTypeValueMap.getOrDefault(FIRST_ORDER_PLACE_COUNT, 0L) + convValueCount);
                convTypeValueMap.put(FIRST_ORDER_PLACE_SUM, convTypeValueMap.getOrDefault(FIRST_ORDER_PLACE_SUM, 0L) + convValueSum);
            }
            if (Objects.equals(convType, ACTION_VALID)) {
                convTypeValueMap.put(ACTION_VALID_COUNT, convTypeValueMap.getOrDefault(ACTION_VALID_COUNT, 0L) + convValueCount);
            }


        }
        return convTypeValueMap;
    }

    private SanlianMapiReportArchiveDetailBo generateArchiveAnalysisDetailBo(Long fromTime, Long toTime, SanlianMapiReportArchiveStatBo sanlianMapiReportArchiveStatBo, Map<Long, Arc> arcsMap, List<Long> cmMidList, Map<Long, String> materialCenterIdByAvid) {
        Long aid = sanlianMapiReportArchiveStatBo.getAid();
        Arc arc = arcsMap.get(aid);
        long mid = arc.getAuthor().getMid();

        SanlianMapiReportArchiveDetailBo sanlianMapiReportArchiveDetailBo = new SanlianMapiReportArchiveDetailBo();
        sanlianMapiReportArchiveDetailBo.setMid(String.valueOf(arc.getAuthor().getMid()));
        sanlianMapiReportArchiveDetailBo.setPic(arc.getPic());
        sanlianMapiReportArchiveDetailBo.setDuration(arc.getDuration());
        sanlianMapiReportArchiveDetailBo.setTitle(arc.getTitle());
        sanlianMapiReportArchiveDetailBo.setName(arc.getAuthor().getName());
        sanlianMapiReportArchiveDetailBo.setHeight(arc.getDimension().getHeight());
        sanlianMapiReportArchiveDetailBo.setWidth(arc.getDimension().getWidth());

        //还是决定把rate的处理放到这里了 无力orz
        sanlianMapiReportArchiveDetailBo.setLogDate(sanlianMapiReportArchiveStatBo.getLogDate());
        sanlianMapiReportArchiveDetailBo.setAid(sanlianMapiReportArchiveStatBo.getAid());
        sanlianMapiReportArchiveDetailBo.setAccountId(sanlianMapiReportArchiveStatBo.getAccountId());
        sanlianMapiReportArchiveDetailBo.setCost(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getCost()));
        sanlianMapiReportArchiveDetailBo.setPv(sanlianMapiReportArchiveStatBo.getPv());
        sanlianMapiReportArchiveDetailBo.setClick(sanlianMapiReportArchiveStatBo.getClick());
        sanlianMapiReportArchiveDetailBo.setClickThroughRate(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getClickThroughRate()).multiply(hundred).setScale(2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setCostPerClick(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getCostPerClick()).setScale(2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setCostPerMille(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getCostPerMille()).setScale(2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setPlay3SRate(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getPlay3sRate()).multiply(hundred).setScale(2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setPlay5SRate(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getPlay5sRate()).multiply(hundred).setScale(2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setPlay10SRate(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getPlay10sRate()).multiply(hundred).setScale(2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setPlayIncrement(sanlianMapiReportArchiveStatBo.getPlayIncrement());
        sanlianMapiReportArchiveDetailBo.setReplyIncrement(sanlianMapiReportArchiveStatBo.getReplyIncrement());
        sanlianMapiReportArchiveDetailBo.setFavIncrement(sanlianMapiReportArchiveStatBo.getFavIncrement());
        sanlianMapiReportArchiveDetailBo.setCoinIncrement(sanlianMapiReportArchiveStatBo.getCoinIncrement());
        sanlianMapiReportArchiveDetailBo.setDanmakuIncrement(sanlianMapiReportArchiveStatBo.getDanmakuIncrement());
        sanlianMapiReportArchiveDetailBo.setShareIncrement(sanlianMapiReportArchiveStatBo.getShareIncrement());
        sanlianMapiReportArchiveDetailBo.setLikesIncrement(sanlianMapiReportArchiveStatBo.getLikesIncrement());
        sanlianMapiReportArchiveDetailBo.setVideoPlayCount(sanlianMapiReportArchiveStatBo.getVideoPlayCount());
        sanlianMapiReportArchiveDetailBo.setCostPerVideoPlay(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getCostPerVideoPlay()));
        sanlianMapiReportArchiveDetailBo.setVideoPlayRate(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getVideoPlayRate()).multiply(hundred).setScale(2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setVideoLikeCount(sanlianMapiReportArchiveStatBo.getVideoLikeCount());
        sanlianMapiReportArchiveDetailBo.setCostPerVideoLike(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getCostPerVideoLike()));
        sanlianMapiReportArchiveDetailBo.setVideoLikeRate(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getVideoLikeRate()).multiply(hundred).setScale(2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setFanIncreaseCount(sanlianMapiReportArchiveStatBo.getFanIncreaseCount());
        sanlianMapiReportArchiveDetailBo.setCostPerFanIncrease(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getCostPerFanIncrease()));
        sanlianMapiReportArchiveDetailBo.setFanIncreaseRate(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getFanIncreaseRate()).multiply(hundred).setScale(2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setFirstCommentCopyCount(sanlianMapiReportArchiveStatBo.getFirstCommentCopyCount());
        sanlianMapiReportArchiveDetailBo.setCostPerFirstCommentCopy(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getCostPerFirstCommentCopy()));
        sanlianMapiReportArchiveDetailBo.setFirstCommentCopyRate(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getFirstCommentCopyRate()).multiply(hundred).setScale(2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setUnderBoxLinkCount(sanlianMapiReportArchiveStatBo.getUnderBoxLinkCount());
        sanlianMapiReportArchiveDetailBo.setCostPerUnderBoxLink(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getCostPerUnderBoxLink()));
        sanlianMapiReportArchiveDetailBo.setUnderBoxLinkRate(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getUnderBoxLinkRate()).multiply(hundred).setScale(2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setCommentUrlShowCount(sanlianMapiReportArchiveStatBo.getCommentUrlShowCount());
        sanlianMapiReportArchiveDetailBo.setCommentUrlShowRate(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getCommentUrlShowRate()).multiply(hundred).setScale(2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setCommentUrlShowClickRate(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getCommentUrlShowClickRate()).multiply(hundred).setScale(2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setCommentUrlClickCount(sanlianMapiReportArchiveStatBo.getCommentUrlClickCount());
        sanlianMapiReportArchiveDetailBo.setCommentUrlClickRate(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getCommentUrlClickRate()).multiply(hundred).setScale(2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setCostPerCommentUrlClick(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getCostPerCommentUrlClick()).setScale(2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setFormSubmitCount(sanlianMapiReportArchiveStatBo.getFormSubmitCount());
        sanlianMapiReportArchiveDetailBo.setCostPerFormSubmit(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getCostPerFormSubmit()).setScale(2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setFormSubmitRate(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getFormSubmitRate()).multiply(hundred).setScale(2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setFormUserCostCount(sanlianMapiReportArchiveStatBo.getFormUserCostCount());
        sanlianMapiReportArchiveDetailBo.setCostPerFormUserCost(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getCostPerFormUserCost()).setScale(2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setFormUserCostRate(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getFormUserCostRate()).multiply(hundred).setScale(2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setClueValidCount(sanlianMapiReportArchiveStatBo.getClueValidCount());
        sanlianMapiReportArchiveDetailBo.setCostPerClueValid(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getCostPerClueValid()).setScale(2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setClueValidCountRate(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getClueValidCountRate()).multiply(hundred).setScale(2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setApplyCount(sanlianMapiReportArchiveStatBo.getApplyCount());
        sanlianMapiReportArchiveDetailBo.setCostPerApply(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getCostPerApply()).setScale(2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setApplyRate(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getApplyRate()).multiply(hundred).setScale(2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setCreditCount(sanlianMapiReportArchiveStatBo.getCreditCount());
        sanlianMapiReportArchiveDetailBo.setCostPerCredit(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getCostPerCredit()).setScale(2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setCreditRate(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getCreditRate()).multiply(hundred).setScale(2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setWithdrawDepositsCount(sanlianMapiReportArchiveStatBo.getWithdrawDepositsCount());
        sanlianMapiReportArchiveDetailBo.setCostPerWithdrawDeposits(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getCostPerWithdrawDeposits()).setScale(2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setWithdrawDepositsRate(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getWithdrawDepositsRate()).multiply(hundred).setScale(2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setGameSubscribeApiCount(sanlianMapiReportArchiveStatBo.getGameSubscribeApiCount());
        sanlianMapiReportArchiveDetailBo.setCostPerGameSubscribeApi(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getCostPerGameSubscribeApi()).setScale(2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setGameSubscribeApiRate(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getGameSubscribeApiRate()).multiply(hundred).setScale(2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setDownloadSuccessCount(sanlianMapiReportArchiveStatBo.getDownloadSuccessCount());
        sanlianMapiReportArchiveDetailBo.setCostPerDownloadSuccess(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getCostPerDownloadSuccess()).setScale(2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setDownloadSuccessRate(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getDownloadSuccessRate()).multiply(hundred).setScale(2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setInstallSuccessCount(sanlianMapiReportArchiveStatBo.getInstallSuccessCount());
        sanlianMapiReportArchiveDetailBo.setCostPerInstallSuccess(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getCostPerInstallSuccess()).setScale(2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setInstallSuccessRate(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getInstallSuccessRate()).multiply(hundred).setScale(2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setIosAppActiveCount(sanlianMapiReportArchiveStatBo.getIosAppActiveCount());
        sanlianMapiReportArchiveDetailBo.setAndroidAppActiveCount(sanlianMapiReportArchiveStatBo.getAndroidAppActiveCount());
        sanlianMapiReportArchiveDetailBo.setAppActiveCount(sanlianMapiReportArchiveStatBo.getAppActiveCount());
        sanlianMapiReportArchiveDetailBo.setCostPerAppActive(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getCostPerAppActive()).setScale(2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setAppActiveRate(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getAppActiveRate()).multiply(hundred).setScale(2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setGameActiveApiCount(sanlianMapiReportArchiveStatBo.getGameActiveApiCount());
        sanlianMapiReportArchiveDetailBo.setCostPerGameActiveApi(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getCostPerGameActiveApi()).setScale(2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setGameActiveApiRate(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getGameActiveApiRate()).multiply(hundred).setScale(2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setUserRegisterCount(sanlianMapiReportArchiveStatBo.getUserRegisterCount());
        sanlianMapiReportArchiveDetailBo.setCostPerUserRegister(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getCostPerUserRegister()).setScale(2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setUserRegisterRate(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getUserRegisterRate()).multiply(hundred).setScale(2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setUserCostCount(sanlianMapiReportArchiveStatBo.getUserCostCount());
        sanlianMapiReportArchiveDetailBo.setUserCostAmount(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getUserCostAmount()).divide(hundred, 2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setUserFirstCostCount(sanlianMapiReportArchiveStatBo.getUserFirstCostCount());
        sanlianMapiReportArchiveDetailBo.setUserFirstCostAmount(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getUserFirstCostAmount()).divide(hundred, 2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setCostPerUserFirstCost(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getCostPerUserFirstCost()).setScale(2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setUserFirstCostRate(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getUserFirstCostRate()).multiply(hundred).setScale(2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setGameUserCostCount(sanlianMapiReportArchiveStatBo.getGameUserCostCount());
        sanlianMapiReportArchiveDetailBo.setGameUserFirstCostCount(sanlianMapiReportArchiveStatBo.getGameUserFirstCostCount());
        sanlianMapiReportArchiveDetailBo.setCostPerGameUserFirstCost(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getCostPerGameUserFirstCost()).setScale(2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setGameUserFirstCostRate(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getGameUserFirstCostRate()).multiply(hundred).setScale(2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setRetentionCount(sanlianMapiReportArchiveStatBo.getRetentionCount());
        sanlianMapiReportArchiveDetailBo.setCostPerRetention(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getCostPerRetention()).setScale(2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setRetentionRate(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getRetentionRate()).multiply(hundred).setScale(2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setAppCallUpCount(sanlianMapiReportArchiveStatBo.getAppCallUpCount());
        sanlianMapiReportArchiveDetailBo.setCostPerAppCallUp(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getCostPerAppCallUp()).setScale(2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setAppCallUpRate(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getAppCallUpRate()).multiply(hundred).setScale(2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setLpCallUpSuccessCount(sanlianMapiReportArchiveStatBo.getLpCallUpSuccessCount());
        sanlianMapiReportArchiveDetailBo.setCostPerLpCallUpSuccess(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getCostPerLpCallUpSuccess()).setScale(2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setLpCallUpSuccessRate(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getLpCallUpSuccessRate()).multiply(hundred).setScale(2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setLpCallUpSuccessStayCount(sanlianMapiReportArchiveStatBo.getLpCallUpSuccessStayCount());
        sanlianMapiReportArchiveDetailBo.setCostPerLpCallUpSuccessStay(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getCostPerLpCallUpSuccessStay()).setScale(2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setLpCallUpSuccessStayRate(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getLpCallUpSuccessStayRate()).multiply(hundred).setScale(2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setOrderPlaceCount(sanlianMapiReportArchiveStatBo.getOrderPlaceCount());
        sanlianMapiReportArchiveDetailBo.setOrderPlaceAmount(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getOrderPlaceAmount()).divide(hundred, 2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setCostPerOrderPlace(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getCostPerOrderPlace()).setScale(2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setOrderPlaceRate(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getOrderPlaceRate()).multiply(hundred).setScale(2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setOrderRoi(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getOrderRoi()).setScale(2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setOrderConversionRate(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getOrderConversionRate()).multiply(hundred).setScale(2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setFirstOrderPlaceCount(sanlianMapiReportArchiveStatBo.getFirstOrderPlaceCount());
        sanlianMapiReportArchiveDetailBo.setFirstOrderPlaceAmount(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getFirstOrderPlaceAmount()).divide(hundred, 2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setCostPerFirstOrderPlace(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getCostPerFirstOrderPlace()).setScale(2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setFirstOrderPlaceRate(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getFirstOrderPlaceRate()).multiply(hundred).setScale(2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setActionValidCount(sanlianMapiReportArchiveStatBo.getActionValidCount());
        sanlianMapiReportArchiveDetailBo.setCostPerActionValid(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getCostPerActionValid()).setScale(2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setActionValidRate(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getActionValidRate()).multiply(hundred).setScale(2, RoundingMode.HALF_UP));

        // 游戏充值相关字段处理
        // 激活后24小时变现相关字段
        sanlianMapiReportArchiveDetailBo.setGameChargeIn24hCount(sanlianMapiReportArchiveStatBo.getGameChargeIn24HCount());
        sanlianMapiReportArchiveDetailBo.setCostPerGameChargeIn24h(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getCostPerGameChargeIn24H()).setScale(2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setGameChargeIn24hAmount(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getGameChargeIn24HAmount()).divide(hundred, 2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setGameChargeIn24hRoi(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getGameChargeIn24HRoi()).setScale(2, RoundingMode.HALF_UP));

        // 激活后首自然日变现相关字段
        sanlianMapiReportArchiveDetailBo.setGameChargeIn1dCount(sanlianMapiReportArchiveStatBo.getGameChargeIn1DCount());
        sanlianMapiReportArchiveDetailBo.setCostPerGameChargeIn1d(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getCostPerGameChargeIn1D()).setScale(2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setGameChargeIn1dAmount(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getGameChargeIn1DAmount()).divide(hundred, 2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setGameChargeIn1dRoi(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getGameChargeIn1DRoi()).setScale(2, RoundingMode.HALF_UP));

        // 激活后7日变现相关字段
        sanlianMapiReportArchiveDetailBo.setGameChargeIn7dCount(sanlianMapiReportArchiveStatBo.getGameChargeIn7DCount());
        sanlianMapiReportArchiveDetailBo.setCostPerGameChargeIn7d(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getCostPerGameChargeIn7D()).setScale(2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setGameChargeIn7dAmount(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getGameChargeIn7DAmount()).divide(hundred, 2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setGameChargeIn7dRoi(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getGameChargeIn7DRoi()).setScale(2, RoundingMode.HALF_UP));

        // 激活后14日变现相关字段
        sanlianMapiReportArchiveDetailBo.setGameChargeIn14dCount(sanlianMapiReportArchiveStatBo.getGameChargeIn14DCount());
        sanlianMapiReportArchiveDetailBo.setCostPerGameChargeIn14d(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getCostPerGameChargeIn14D()).setScale(2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setGameChargeIn14dAmount(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getGameChargeIn14DAmount()).divide(hundred, 2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setGameChargeIn14dRoi(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getGameChargeIn14DRoi()).setScale(2, RoundingMode.HALF_UP));

        // 混合变现相关字段
        sanlianMapiReportArchiveDetailBo.setGameChargeIn24hMixAmount(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getGameChargeIn24HMixAmount()).divide(hundred, 2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setGameChargeIn24hMixRoi(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getGameChargeIn24HMixRoi()).setScale(2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setGameChargeIn1dMixAmount(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getGameChargeIn1DMixAmount()).divide(hundred, 2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setGameChargeIn1dMixRoi(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getGameChargeIn1DMixRoi()).setScale(2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setGameChargeIn7dMixAmount(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getGameChargeIn7DMixAmount()).divide(hundred, 2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setGameChargeIn7dMixRoi(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getGameChargeIn7DMixRoi()).setScale(2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setGameChargeIn14dMixAmount(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getGameChargeIn14DMixAmount()).divide(hundred, 2, RoundingMode.HALF_UP));
        sanlianMapiReportArchiveDetailBo.setGameChargeIn14dMixRoi(BigDecimal.valueOf(sanlianMapiReportArchiveStatBo.getGameChargeIn14DMixRoi()).setScale(2, RoundingMode.HALF_UP));

        if (cmMidList.contains(mid)) {
            sanlianMapiReportArchiveDetailBo.setMid(StringUtils.EMPTY);
            sanlianMapiReportArchiveDetailBo.setName(StringUtils.EMPTY);
        }
        if (StringUtils.isEmpty(sanlianMapiReportArchiveDetailBo.getLogDate())) {
            sanlianMapiReportArchiveDetailBo.setLogDate(time2String(fromTime) + "~" + time2String(toTime));
        } else {
            sanlianMapiReportArchiveDetailBo.setLogDate(sanlianMapiReportArchiveDetailBo.getLogDate());
        }

        sanlianMapiReportArchiveDetailBo.setMaterialCenterId(materialCenterIdByAvid.getOrDefault(aid, StringUtils.EMPTY));
        sanlianMapiReportArchiveDetailBo.setMaterialUrl(Constants.BILIBILI_ARC_PC_PREFIX + aid);
        return sanlianMapiReportArchiveDetailBo;
    }


    private SanlianMapiReportArchiveStatBo generateSanlianMapiReportArchiveStatBo(Map<String, String> archiveCostValueMap, Map<String, Long> convTypeValueMap) {
        SanlianMapiReportArchiveStatBo sanlianMapiReportArchiveStatBo = new SanlianMapiReportArchiveStatBo();
        String costSum = divideTenThousand(archiveCostValueMap.get(COST_MILLI_SUM)).toString();
        String clickSum = archiveCostValueMap.get(CLICK_SUM);
        sanlianMapiReportArchiveStatBo.setLogDate(archiveCostValueMap.get(LOG_DATE));
        sanlianMapiReportArchiveStatBo.setAid(Long.parseLong(archiveCostValueMap.get(AVID)));
        sanlianMapiReportArchiveStatBo.setAccountId(Long.parseLong(archiveCostValueMap.get(ACCOUNT_ID)));
        sanlianMapiReportArchiveStatBo.setCost(divideThousand(archiveCostValueMap.get(COST_MILLI_SUM)));
        sanlianMapiReportArchiveStatBo.setPv(Long.parseLong(archiveCostValueMap.get(PV_SUM)));
        sanlianMapiReportArchiveStatBo.setClick(Long.parseLong(clickSum));
        sanlianMapiReportArchiveStatBo.setClickThroughRate(divide(clickSum, archiveCostValueMap.get(PV_SUM)));
        sanlianMapiReportArchiveStatBo.setCostPerClick(divide(costSum, clickSum));
        sanlianMapiReportArchiveStatBo.setCostPerMille(divide(divideHundred(archiveCostValueMap.get(COST_MILLI_SUM)).toString(), archiveCostValueMap.get(PV_SUM)));
        sanlianMapiReportArchiveStatBo.setPlay3sRate(divide(archiveCostValueMap.get(PLAY_3S_CNT_SUM), archiveCostValueMap.get(PLAY_CNT_SUM), defaultScale));
        sanlianMapiReportArchiveStatBo.setPlay5sRate(divide(archiveCostValueMap.get(PLAY_5S_CNT_SUM), archiveCostValueMap.get(PLAY_CNT_SUM), defaultScale));
        sanlianMapiReportArchiveStatBo.setPlay10sRate(divide(archiveCostValueMap.get(PLAY_10S_CNT_SUM), archiveCostValueMap.get(PLAY_CNT_SUM), defaultScale));
        sanlianMapiReportArchiveStatBo.setPlayIncrement(Long.parseLong(archiveCostValueMap.get(PLAY_DAILY_MAX)));
        sanlianMapiReportArchiveStatBo.setReplyIncrement(Long.parseLong(archiveCostValueMap.get(REPLY_DAILY_SUM)));
        sanlianMapiReportArchiveStatBo.setFavIncrement(Long.parseLong(archiveCostValueMap.get(FAV_DAILY_SUM)));
        sanlianMapiReportArchiveStatBo.setCoinIncrement(Long.parseLong(archiveCostValueMap.get(COIN_DAILY_SUM)));
        sanlianMapiReportArchiveStatBo.setDanmakuIncrement(Long.parseLong(archiveCostValueMap.get(DANMU_DAILY_SUM)));
        sanlianMapiReportArchiveStatBo.setShareIncrement(Long.parseLong(archiveCostValueMap.get(SHARE_DAILY_SUM)));
        sanlianMapiReportArchiveStatBo.setLikesIncrement(Long.parseLong(archiveCostValueMap.get(LIKES_DAILY_SUM)));

        sanlianMapiReportArchiveStatBo.setVideoPlayCount(Long.parseLong(archiveCostValueMap.get(VIDEO_PLAY_SUM)));
        sanlianMapiReportArchiveStatBo.setCostPerVideoPlay(divide(costSum, archiveCostValueMap.get(VIDEO_PLAY_SUM)));
        sanlianMapiReportArchiveStatBo.setVideoPlayRate(divide(archiveCostValueMap.get(VIDEO_PLAY_SUM), archiveCostValueMap.get(PV_SUM)));

        sanlianMapiReportArchiveStatBo.setVideoLikeCount(Long.parseLong(archiveCostValueMap.get(VIDEO_LIKE_SUM)));
        sanlianMapiReportArchiveStatBo.setCostPerVideoLike(divide(costSum, archiveCostValueMap.get(VIDEO_LIKE_SUM)));
        sanlianMapiReportArchiveStatBo.setVideoLikeRate(divide(archiveCostValueMap.get(VIDEO_LIKE_SUM), archiveCostValueMap.get(PV_SUM)));

        Long fanIncreaseCount = convTypeValueMap.getOrDefault(FAN_INCREASE_COUNT, 0L);
        sanlianMapiReportArchiveStatBo.setFanIncreaseCount(fanIncreaseCount);
        sanlianMapiReportArchiveStatBo.setCostPerFanIncrease(divide(costSum, fanIncreaseCount.toString()));
        sanlianMapiReportArchiveStatBo.setFanIncreaseRate(divide(fanIncreaseCount.toString(), clickSum));

        String firstCommentCopySum = archiveCostValueMap.get(FIRST_COMMENT_COPY_SUM);
        sanlianMapiReportArchiveStatBo.setFirstCommentCopyCount(Long.parseLong(firstCommentCopySum));
        sanlianMapiReportArchiveStatBo.setCostPerFirstCommentCopy(divide(costSum, firstCommentCopySum));
        sanlianMapiReportArchiveStatBo.setFirstCommentCopyRate(divide(firstCommentCopySum, archiveCostValueMap.get(VIDEO_PLAY_SUM)));

        Long underBoxLinkCount = convTypeValueMap.getOrDefault(UNDER_BOX_LINK_COUNT, 0L);
        sanlianMapiReportArchiveStatBo.setUnderBoxLinkCount(underBoxLinkCount);
        sanlianMapiReportArchiveStatBo.setCostPerUnderBoxLink(divide(costSum, underBoxLinkCount.toString()));
        sanlianMapiReportArchiveStatBo.setUnderBoxLinkRate(divide(underBoxLinkCount.toString(), archiveCostValueMap.get(VIDEO_PLAY_SUM)));

        String commentUrlShowSum = archiveCostValueMap.get(COMMENT_URL_SHOW_SUM);
        sanlianMapiReportArchiveStatBo.setCommentUrlShowCount(Long.parseLong(commentUrlShowSum));
        sanlianMapiReportArchiveStatBo.setCommentUrlShowRate(divide(archiveCostValueMap.get(COMMENT_URL_SHOW_SUM), archiveCostValueMap.get(VIDEO_PLAY_SUM)));
        sanlianMapiReportArchiveStatBo.setCommentUrlShowClickRate(divide(convTypeValueMap.getOrDefault(COMMENT_URL_CLICK_COUNT, 0L).toString(), commentUrlShowSum));

        Long commentUrLClickCount = convTypeValueMap.getOrDefault(COMMENT_URL_CLICK_COUNT, 0L);
        sanlianMapiReportArchiveStatBo.setCommentUrlClickCount(commentUrLClickCount);
        sanlianMapiReportArchiveStatBo.setCommentUrlClickRate(divide(commentUrLClickCount.toString(), archiveCostValueMap.get(VIDEO_PLAY_SUM)));
        sanlianMapiReportArchiveStatBo.setCostPerCommentUrlClick(divide(costSum, commentUrLClickCount.toString()));


        sanlianMapiReportArchiveStatBo.setCommentCallUpSuccessCount(convTypeValueMap.getOrDefault(COMMENT_CALLUP_SUCC_COUNT, 0L));

        Long formSubmitCount = convTypeValueMap.getOrDefault(FORM_SUBMIT_COUNT, 0L);
        sanlianMapiReportArchiveStatBo.setFormSubmitCount(formSubmitCount);
        sanlianMapiReportArchiveStatBo.setCostPerFormSubmit(divide(costSum, formSubmitCount.toString()));
        sanlianMapiReportArchiveStatBo.setFormSubmitRate(divide(formSubmitCount.toString(), clickSum));

        Long formUserCostCount = convTypeValueMap.getOrDefault(FORM_USER_COST_COUNT, 0L);
        sanlianMapiReportArchiveStatBo.setFormUserCostCount(formUserCostCount);
        sanlianMapiReportArchiveStatBo.setCostPerFormUserCost(divide(costSum, formUserCostCount.toString()));
        sanlianMapiReportArchiveStatBo.setFormUserCostRate(divide(formUserCostCount.toString(), clickSum));

        Long clueValidCount = convTypeValueMap.getOrDefault(CLUE_VALID_COUNT, 0L);
        sanlianMapiReportArchiveStatBo.setClueValidCount(clueValidCount);
        sanlianMapiReportArchiveStatBo.setCostPerClueValid(divide(costSum, clueValidCount.toString()));
        sanlianMapiReportArchiveStatBo.setClueValidCountRate(divide(clueValidCount.toString(), formSubmitCount.toString()));

        Long applyCount = convTypeValueMap.getOrDefault(APPLY_COUNT, 0L);
        sanlianMapiReportArchiveStatBo.setApplyCount(applyCount);
        sanlianMapiReportArchiveStatBo.setCostPerApply(divide(costSum, applyCount.toString()));
        sanlianMapiReportArchiveStatBo.setApplyRate(divide(applyCount.toString(), clickSum));

        Long creditCount = convTypeValueMap.getOrDefault(CREDIT_COUNT, 0L);
        sanlianMapiReportArchiveStatBo.setCreditCount(creditCount);
        sanlianMapiReportArchiveStatBo.setCostPerCredit(divide(costSum, creditCount.toString()));
        sanlianMapiReportArchiveStatBo.setCreditRate(divide(creditCount.toString(), clickSum));

        Long withdrawDepositsCount = convTypeValueMap.getOrDefault(WITHDRAW_DEPOSITS_COUNT, 0L);
        sanlianMapiReportArchiveStatBo.setWithdrawDepositsCount(withdrawDepositsCount);
        sanlianMapiReportArchiveStatBo.setCostPerWithdrawDeposits(divide(costSum, withdrawDepositsCount.toString()));
        sanlianMapiReportArchiveStatBo.setWithdrawDepositsRate(divide(withdrawDepositsCount.toString(), clickSum));

        Long gameSubscribeApiCount = convTypeValueMap.getOrDefault(GAME_SUBSCRIBE_API_COUNT, 0L);
        sanlianMapiReportArchiveStatBo.setGameSubscribeApiCount(gameSubscribeApiCount);
        sanlianMapiReportArchiveStatBo.setCostPerGameSubscribeApi(divide(costSum, gameSubscribeApiCount.toString()));
        sanlianMapiReportArchiveStatBo.setGameSubscribeApiRate(divide(gameSubscribeApiCount.toString(), clickSum));

        Long downloadSuccessCount = convTypeValueMap.getOrDefault(DOWNLOAD_SUCCESS_COUNT, 0L);
        sanlianMapiReportArchiveStatBo.setDownloadSuccessCount(downloadSuccessCount);
        sanlianMapiReportArchiveStatBo.setCostPerDownloadSuccess(divide(costSum, downloadSuccessCount.toString()));
        sanlianMapiReportArchiveStatBo.setDownloadSuccessRate(divide(downloadSuccessCount.toString(), clickSum));

        Long installSuccessCount = convTypeValueMap.getOrDefault(INSTALL_SUCCESS_COUNT, 0L);
        sanlianMapiReportArchiveStatBo.setInstallSuccessCount(installSuccessCount);
        sanlianMapiReportArchiveStatBo.setCostPerInstallSuccess(divide(costSum, installSuccessCount.toString()));
        sanlianMapiReportArchiveStatBo.setInstallSuccessRate(divide(installSuccessCount.toString(), clickSum));

        sanlianMapiReportArchiveStatBo.setIosAppActiveCount(convTypeValueMap.getOrDefault(IOS_APP_ACTIVE_COUNT, 0L));
        sanlianMapiReportArchiveStatBo.setAndroidAppActiveCount(convTypeValueMap.getOrDefault(ANDROID_APP_ACTIVE_COUNT, 0L));

        Long appActiveCount = convTypeValueMap.getOrDefault(APP_ACTIVE_COUNT, 0L);
        sanlianMapiReportArchiveStatBo.setAppActiveCount(appActiveCount);
        sanlianMapiReportArchiveStatBo.setCostPerAppActive(divide(costSum, appActiveCount.toString()));
        sanlianMapiReportArchiveStatBo.setAppActiveRate(divide(appActiveCount.toString(), clickSum));

        Long gameActiveApiCount = convTypeValueMap.getOrDefault(GAME_ACTIVE_API_COUNT, 0L);
        sanlianMapiReportArchiveStatBo.setGameActiveApiCount(gameActiveApiCount);
        sanlianMapiReportArchiveStatBo.setCostPerGameActiveApi(divide(costSum, gameActiveApiCount.toString()));
        sanlianMapiReportArchiveStatBo.setGameActiveApiRate(divide(gameActiveApiCount.toString(), clickSum));

        Long userRegisterCount = convTypeValueMap.getOrDefault(USER_REGISTER_COUNT, 0L);
        sanlianMapiReportArchiveStatBo.setUserRegisterCount(userRegisterCount);
        sanlianMapiReportArchiveStatBo.setCostPerUserRegister(divide(costSum, userRegisterCount.toString()));
        sanlianMapiReportArchiveStatBo.setUserRegisterRate(divide(userRegisterCount.toString(), clickSum));

        sanlianMapiReportArchiveStatBo.setUserCostCount(convTypeValueMap.getOrDefault(USER_COST_COUNT, 0L));
        sanlianMapiReportArchiveStatBo.setUserCostAmount(convTypeValueMap.getOrDefault(USER_COST_SUM, 0L));
        Long userFirstCostCount = convTypeValueMap.getOrDefault(USER_FIRST_COST_COUNT, 0L);
        sanlianMapiReportArchiveStatBo.setUserFirstCostCount(userFirstCostCount);
        sanlianMapiReportArchiveStatBo.setUserFirstCostAmount(convTypeValueMap.getOrDefault(USER_FIRST_COST_SUM, 0L));

        sanlianMapiReportArchiveStatBo.setCostPerUserFirstCost(divide(costSum, userFirstCostCount.toString()));
        sanlianMapiReportArchiveStatBo.setUserFirstCostRate(divide(userFirstCostCount.toString(), clickSum));

        sanlianMapiReportArchiveStatBo.setGameUserCostCount(convTypeValueMap.getOrDefault(GAME_USER_COST_COUNT, 0L));
        Long gameUserFirstCostSum = convTypeValueMap.getOrDefault(GAME_USER_FIRST_COST_COUNT, 0L);
        sanlianMapiReportArchiveStatBo.setGameUserFirstCostCount(gameUserFirstCostSum);
        sanlianMapiReportArchiveStatBo.setCostPerGameUserFirstCost(divide(costSum, gameUserFirstCostSum.toString()));
        sanlianMapiReportArchiveStatBo.setGameUserFirstCostRate(divide(gameUserFirstCostSum.toString(), convTypeValueMap.getOrDefault(ANDROID_APP_ACTIVE_COUNT, 0L).toString()));

        Long retentionCount = convTypeValueMap.getOrDefault(RETENTION_COUNT, 0L);
        sanlianMapiReportArchiveStatBo.setRetentionCount(retentionCount);
        sanlianMapiReportArchiveStatBo.setCostPerRetention(divide(costSum, retentionCount.toString()));
        sanlianMapiReportArchiveStatBo.setRetentionRate(divide(convTypeValueMap.getOrDefault(APP_CALLUP_COUNT, 0L).toString(), convTypeValueMap.getOrDefault(APP_ACTIVE_COUNT, 0L).toString()));

        Long appCallupCount = convTypeValueMap.getOrDefault(APP_CALLUP_COUNT, 0L);
        sanlianMapiReportArchiveStatBo.setAppCallUpCount(appCallupCount);
        sanlianMapiReportArchiveStatBo.setCostPerAppCallUp(divide(costSum, appCallupCount.toString()));
        sanlianMapiReportArchiveStatBo.setAppCallUpRate(divide(appCallupCount.toString(), clickSum));

        Long lpCallupSuccCount = convTypeValueMap.getOrDefault(LP_CALLUP_SUCC_COUNT, 0L);
        sanlianMapiReportArchiveStatBo.setLpCallUpSuccessCount(lpCallupSuccCount);
        sanlianMapiReportArchiveStatBo.setCostPerLpCallUpSuccess(divide(costSum, lpCallupSuccCount.toString()));
        sanlianMapiReportArchiveStatBo.setLpCallUpSuccessRate(divide(lpCallupSuccCount.toString(), clickSum));

        Long lpCallupSuccStayCount = convTypeValueMap.getOrDefault(LP_CALLUP_SUCC_STAY_COUNT, 0L);
        sanlianMapiReportArchiveStatBo.setLpCallUpSuccessStayCount(lpCallupSuccStayCount);
        sanlianMapiReportArchiveStatBo.setCostPerLpCallUpSuccessStay(divide(costSum, lpCallupSuccStayCount.toString()));
        sanlianMapiReportArchiveStatBo.setLpCallUpSuccessStayRate(divide(lpCallupSuccStayCount.toString(), lpCallupSuccCount.toString()));

        Long orderPlaceCount = convTypeValueMap.getOrDefault(ORDER_PLACE_COUNT, 0L);
        Long orderPlaceSum = convTypeValueMap.getOrDefault(ORDER_PLACE_SUM, 0L);
        sanlianMapiReportArchiveStatBo.setOrderPlaceCount(orderPlaceCount);
        sanlianMapiReportArchiveStatBo.setOrderPlaceAmount(orderPlaceSum);
        sanlianMapiReportArchiveStatBo.setCostPerOrderPlace(divide(costSum, orderPlaceCount.toString()));
        sanlianMapiReportArchiveStatBo.setOrderPlaceRate(divide(orderPlaceCount.toString(), clickSum));
        sanlianMapiReportArchiveStatBo.setOrderRoi(divide(costSum, orderPlaceSum.toString()));
        sanlianMapiReportArchiveStatBo.setOrderConversionRate(divide(multiplyHundred(orderPlaceCount.toString()).toString(), clickSum));

        Long firstOrderPlaceCount = convTypeValueMap.getOrDefault(FIRST_ORDER_PLACE_COUNT, 0L);
        sanlianMapiReportArchiveStatBo.setFirstOrderPlaceAmount(convTypeValueMap.getOrDefault(FIRST_ORDER_PLACE_SUM, 0L));
        sanlianMapiReportArchiveStatBo.setFirstOrderPlaceCount(firstOrderPlaceCount);
        sanlianMapiReportArchiveStatBo.setCostPerFirstOrderPlace(divide(costSum, firstOrderPlaceCount.toString()));
        sanlianMapiReportArchiveStatBo.setFirstOrderPlaceRate(divide(firstOrderPlaceCount.toString(), clickSum));

        Long actionValidCount = convTypeValueMap.getOrDefault(ACTION_VALID_COUNT, 0L);
        sanlianMapiReportArchiveStatBo.setActionValidCount(actionValidCount);
        sanlianMapiReportArchiveStatBo.setCostPerActionValid(divide(costSum, actionValidCount.toString()));
        sanlianMapiReportArchiveStatBo.setActionValidRate(divide(actionValidCount.toString(), clickSum));
        return sanlianMapiReportArchiveStatBo;
    }

    private SanlianMapiReportImageStatBo generateSanlianMapiReportImageStatBo(Map<String, String> archiveCostValueMap, Map<String, Long> convTypeValueMap) {
        SanlianMapiReportImageStatBo sanlianMapiReportImageStatBo = new SanlianMapiReportImageStatBo();
        String costSum = divideTenThousand(archiveCostValueMap.get(COST_MILLI_SUM)).toString();
        String clickSum = archiveCostValueMap.get(CLICK_SUM);
        sanlianMapiReportImageStatBo.setImageMd5(archiveCostValueMap.get(IMAGE_MD5));
        sanlianMapiReportImageStatBo.setDate(archiveCostValueMap.get(LOG_DATE));
        sanlianMapiReportImageStatBo.setShowCount(Long.parseLong(archiveCostValueMap.get(PV_SUM)));
        sanlianMapiReportImageStatBo.setClickCount(Long.parseLong(clickSum));
        sanlianMapiReportImageStatBo.setCost(divideTenThousand(archiveCostValueMap.get(COST_MILLI_SUM)));
        sanlianMapiReportImageStatBo.setCostPerClick(divide(costSum, clickSum));
        sanlianMapiReportImageStatBo.setClickRate(divide(clickSum, archiveCostValueMap.get(PV_SUM)));
        sanlianMapiReportImageStatBo.setAverageCostPerThousand(divide(multiplyThousand(costSum).toString(), archiveCostValueMap.get(PV_SUM)));

        Long formSubmitCount = convTypeValueMap.getOrDefault(FORM_SUBMIT_COUNT, 0L);
        sanlianMapiReportImageStatBo.setFormSubmitCount(formSubmitCount);
        sanlianMapiReportImageStatBo.setFormSubmitAverageCost(divide(costSum, formSubmitCount.toString()));
        sanlianMapiReportImageStatBo.setFormSubmitRate(divide(formSubmitCount.toString(), clickSum));

        Long formUserCostCount = convTypeValueMap.getOrDefault(FORM_USER_COST_COUNT, 0L);
        sanlianMapiReportImageStatBo.setFormPaidCount(formUserCostCount);
        sanlianMapiReportImageStatBo.setFormPaidCost(divide(costSum, formUserCostCount.toString()));
        sanlianMapiReportImageStatBo.setFormPaidRate(divide(formUserCostCount.toString(), clickSum));

        Long clueValidCount = convTypeValueMap.getOrDefault(CLUE_VALID_COUNT, 0L);
        sanlianMapiReportImageStatBo.setValidClueCount(clueValidCount);
        sanlianMapiReportImageStatBo.setValidClueAverageCost(divide(costSum, clueValidCount.toString()));
        sanlianMapiReportImageStatBo.setValidClueRate(divide(clueValidCount.toString(), clickSum));

        Long applyCount = convTypeValueMap.getOrDefault(APPLY_COUNT, 0L);
        sanlianMapiReportImageStatBo.setApplyCount(applyCount);
        sanlianMapiReportImageStatBo.setApplyCost(divide(costSum, applyCount.toString()));
        sanlianMapiReportImageStatBo.setApplyRate(divide(applyCount.toString(), clickSum));

        Long creditCount = convTypeValueMap.getOrDefault(CREDIT_COUNT, 0L);
        sanlianMapiReportImageStatBo.setCreditCount(creditCount);
        sanlianMapiReportImageStatBo.setCreditCost(divide(costSum, creditCount.toString()));
        sanlianMapiReportImageStatBo.setCreditRate(divide(creditCount.toString(), clickSum));

        Long withdrawDepositsCount = convTypeValueMap.getOrDefault(WITHDRAW_DEPOSITS_COUNT, 0L);
        sanlianMapiReportImageStatBo.setWithdrawDepositsCount(withdrawDepositsCount);
        sanlianMapiReportImageStatBo.setWithdrawDepositsCost(divide(costSum, withdrawDepositsCount.toString()));
        sanlianMapiReportImageStatBo.setWithdrawDepositsRate(divide(withdrawDepositsCount.toString(), clickSum));

        Long firstWithdrawCount = convTypeValueMap.getOrDefault(FIRST_WITHDRAW_COUNT, 0L);
        sanlianMapiReportImageStatBo.setFirstWithdrawCount(firstWithdrawCount);
        sanlianMapiReportImageStatBo.setCostPerFirstWithdraw(divide(costSum, firstWithdrawCount.toString()));
        sanlianMapiReportImageStatBo.setFirstWithdrawRate(divide(firstWithdrawCount.toString(), clickSum));

        Long gameSubscribeApiCount = convTypeValueMap.getOrDefault(GAME_SUBSCRIBE_API_COUNT, 0L);
        sanlianMapiReportImageStatBo.setGameReserveCount(gameSubscribeApiCount);
        sanlianMapiReportImageStatBo.setGameReserveAverageCost(divide(costSum, gameSubscribeApiCount.toString()));
        sanlianMapiReportImageStatBo.setGameReserveRate(divide(gameSubscribeApiCount.toString(), clickSum));

        Long downloadSuccessCount = convTypeValueMap.getOrDefault(DOWNLOAD_SUCCESS_COUNT, 0L);
        sanlianMapiReportImageStatBo.setAndroidDownloadCount(downloadSuccessCount);
        sanlianMapiReportImageStatBo.setAndroidDownloadAverageCost(divide(costSum, downloadSuccessCount.toString()));
        sanlianMapiReportImageStatBo.setAndroidDownloadRate(divide(downloadSuccessCount.toString(), clickSum));

        Long installSuccessCount = convTypeValueMap.getOrDefault(INSTALL_SUCCESS_COUNT, 0L);
        sanlianMapiReportImageStatBo.setAndroidInstallCount(installSuccessCount);
        sanlianMapiReportImageStatBo.setAndroidInstallAverageCost(divide(costSum, installSuccessCount.toString()));
        sanlianMapiReportImageStatBo.setAndroidInstallRate(divide(installSuccessCount.toString(), clickSum));

        sanlianMapiReportImageStatBo.setIosActivateCount(convTypeValueMap.getOrDefault(IOS_APP_ACTIVE_COUNT, 0L));
        sanlianMapiReportImageStatBo.setAndroidActivateCount(convTypeValueMap.getOrDefault(ANDROID_APP_ACTIVE_COUNT, 0L));
        sanlianMapiReportImageStatBo.setActivateCount(convTypeValueMap.getOrDefault(APP_ACTIVE_COUNT, 0L));
        sanlianMapiReportImageStatBo.setAppActivateAverageCost(divide(costSum, sanlianMapiReportImageStatBo.getActivateCount().toString()));
        sanlianMapiReportImageStatBo.setAppActivateRate(divide(sanlianMapiReportImageStatBo.getActivateCount().toString(), clickSum));

        Long gameActiveApiCount = convTypeValueMap.getOrDefault(GAME_ACTIVE_API_COUNT, 0L);
        sanlianMapiReportImageStatBo.setAndroidGameCenterActivationCount(gameActiveApiCount);
        sanlianMapiReportImageStatBo.setAndroidGameCenterActivationCost(divide(costSum, gameActiveApiCount.toString()));
        sanlianMapiReportImageStatBo.setAndroidGameCenterActivationRate(divide(gameActiveApiCount.toString(), clickSum));

        Long userRegisterCount = convTypeValueMap.getOrDefault(USER_REGISTER_COUNT, 0L);
        sanlianMapiReportImageStatBo.setRegisterCount(userRegisterCount);
        sanlianMapiReportImageStatBo.setRegisterAverageCost(divide(costSum, userRegisterCount.toString()));
        sanlianMapiReportImageStatBo.setRegisterRate(divide(userRegisterCount.toString(), clickSum));

        Long orderPayCount = convTypeValueMap.getOrDefault(USER_COST_COUNT, 0L);
        Long orderPayAmount = convTypeValueMap.getOrDefault(USER_COST_SUM, 0L);
        sanlianMapiReportImageStatBo.setOrderPayCount(orderPayCount);
        sanlianMapiReportImageStatBo.setOrderPayAmount(orderPayAmount / 1D);

        Long orderFirstPayCount = convTypeValueMap.getOrDefault(USER_FIRST_COST_COUNT, 0L);
        Long orderFirstPayAmount = convTypeValueMap.getOrDefault(USER_FIRST_COST_SUM, 0L);
        sanlianMapiReportImageStatBo.setOrderFirstPayCount(orderFirstPayCount);
        sanlianMapiReportImageStatBo.setOrderFirstPayAmount(orderFirstPayAmount / 1D);
        sanlianMapiReportImageStatBo.setOrderFirstPayCost(divide(costSum, orderFirstPayCount.toString()));
        sanlianMapiReportImageStatBo.setOrderFirstPayRate(divide(orderFirstPayCount.toString(), clickSum));

        Long gameCenterFirstPaymentInApp = convTypeValueMap.getOrDefault(GAME_USER_FIRST_COST_COUNT, 0L);
        sanlianMapiReportImageStatBo.setAndroidGameCenterPaymentInAppCount(convTypeValueMap.getOrDefault(GAME_USER_COST_COUNT, 0L));
        sanlianMapiReportImageStatBo.setAndroidGameCenterFirstPaymentInAppCount(gameCenterFirstPaymentInApp);
        sanlianMapiReportImageStatBo.setAndroidGameCenterFirstPaymentInAppCost(divide(costSum, gameCenterFirstPaymentInApp.toString()));
        sanlianMapiReportImageStatBo.setAndroidGameCenterFirstPaymentInAppRate(divide(gameCenterFirstPaymentInApp.toString(), clickSum));

        Long retentionCount = convTypeValueMap.getOrDefault(RETENTION_COUNT, 0L);
        sanlianMapiReportImageStatBo.setRetentionCount(retentionCount);
        sanlianMapiReportImageStatBo.setRetentionCost(divide(costSum, retentionCount.toString()));
        sanlianMapiReportImageStatBo.setRetentionRate(divide(retentionCount.toString(), clickSum));

        Long appCallUpCount = convTypeValueMap.getOrDefault(APP_CALLUP_COUNT, 0L);
        sanlianMapiReportImageStatBo.setAppCallupCount(appCallUpCount);
        sanlianMapiReportImageStatBo.setAppCallupCost(divide(costSum, appCallUpCount.toString()));
        sanlianMapiReportImageStatBo.setAppCallupRate(divide(appCallUpCount.toString(), clickSum));

        Long lpCallupSuccCount = convTypeValueMap.getOrDefault(LP_CALLUP_SUCC_COUNT, 0L);
        sanlianMapiReportImageStatBo.setLpCallUpSuccessCount(lpCallupSuccCount);
        sanlianMapiReportImageStatBo.setLpCallUpSuccessRate(divide(lpCallupSuccCount.toString(), clickSum));
        sanlianMapiReportImageStatBo.setLpCallUpSuccessCost(divide(costSum, lpCallupSuccCount.toString()));

        Long lpCallupSuccStayCount = convTypeValueMap.getOrDefault(LP_CALLUP_SUCC_STAY_COUNT, 0L);
        sanlianMapiReportImageStatBo.setLpCallUpSuccessStayCount(lpCallupSuccStayCount);
        sanlianMapiReportImageStatBo.setLpCallUpSuccessStayRate(divide(lpCallupSuccStayCount.toString(), lpCallupSuccCount.toString()));
        sanlianMapiReportImageStatBo.setLpCallUpSuccessStayCost(divide(costSum, lpCallupSuccStayCount.toString()));

        Long orderPlaceCount = convTypeValueMap.getOrDefault(ORDER_PLACE_COUNT, 0L);
        Long orderPlaceSum = convTypeValueMap.getOrDefault(ORDER_PLACE_SUM, 0L);
        sanlianMapiReportImageStatBo.setOrderSubmitCount(orderPlaceCount);
        sanlianMapiReportImageStatBo.setOrderSubmitAmount(orderPlaceSum / 1d);
        sanlianMapiReportImageStatBo.setOrderSubmitRate(divide(orderPlaceCount.toString(), clickSum));
        sanlianMapiReportImageStatBo.setOrderSubmitCost(divide(costSum, orderPlaceCount.toString()));
        sanlianMapiReportImageStatBo.setGoodsRoi(divide(costSum, orderPlaceSum.toString()));

        Long firstOrderPlaceCount = convTypeValueMap.getOrDefault(FIRST_ORDER_PLACE_COUNT, 0L);
        sanlianMapiReportImageStatBo.setFirstOrderPlaceCount(firstOrderPlaceCount);
        sanlianMapiReportImageStatBo.setFirstOrderPlaceAmount(convTypeValueMap.getOrDefault(FIRST_ORDER_PLACE_SUM, 0L) / 1d);
        sanlianMapiReportImageStatBo.setFirstOrderPlaceRate(divide(firstOrderPlaceCount.toString(), clickSum));
        sanlianMapiReportImageStatBo.setFirstOrderPlaceCost(divide(costSum, firstOrderPlaceCount.toString()));

        Long actionValidCount = convTypeValueMap.getOrDefault(ACTION_VALID_COUNT, 0L);
        sanlianMapiReportImageStatBo.setKeyBehaviorCount(actionValidCount);
        sanlianMapiReportImageStatBo.setKeyBehaviorRate(divide(actionValidCount.toString(), clickSum));
        sanlianMapiReportImageStatBo.setKeyBehaviorCost(divide(costSum, actionValidCount.toString()));

        sanlianMapiReportImageStatBo.setImageUrl(archiveCostValueMap.get(ImageCostTableConstants.IMAGE_URL));

        return sanlianMapiReportImageStatBo;
    }

    private List<OperatorVo> generateConvReq(List<Integer> campaignIds, List<Integer> unitIds, List<Integer> creativeIds,
                                             List<Long> aids, List<String> imageMd5List, Integer accountId, Integer picType,
                                             Integer creativeType, Long fromTime, Long toTime, Integer reportType) {
        List<OperatorVo> convReqList = new ArrayList<>();

        convReqList.add(OperatorVo.newBuilder().setField(AVID).setOperator("!=").addAllValues(Lists.newArrayList("0")).build());
        if (Objects.nonNull(fromTime)) {
            LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(fromTime), ZoneId.systemDefault());
            String formattedFromTime = localDateTime.format(formatter);
            convReqList.add(OperatorVo.newBuilder().setField(LOG_DATE).setOperator(">=").addAllValues(Lists.newArrayList(formattedFromTime)).build());
        }
        if (Objects.nonNull(toTime)) {
            LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(toTime), ZoneId.systemDefault());
            String formattedToTime = localDateTime.format(formatter);
            convReqList.add(OperatorVo.newBuilder().setField(LOG_DATE).setOperator("<=").addAllValues(Lists.newArrayList(formattedToTime)).build());
        }

        if (Objects.equals(creativeType, StatReq.CreativeType.CUSTOMIZED_CREATIVE_VALUE)) {
            convReqList.add(OperatorVo.newBuilder().setField(PROGRAMMATIC).setOperator("=").addAllValues(Lists.newArrayList(costTableCreativeTypeCustomize)).build());
        }
        if (Objects.equals(creativeType, StatReq.CreativeType.PROGRAMMATIC_CREATIVE_VALUE)) {
            convReqList.add(OperatorVo.newBuilder().setField(PROGRAMMATIC).setOperator("=").addAllValues(Lists.newArrayList(costTableCreativeTypeProgrammatic)).build());
        }
        if (CollectionUtils.isNotEmpty(aids)) {
            convReqList.add(OperatorVo.newBuilder().setField(AVID).setOperator("in").addAllValues(aids.stream().map(String::valueOf).collect(Collectors.toList())).build());
        }
        if (CollectionUtils.isNotEmpty(imageMd5List)) {
            convReqList.add(OperatorVo.newBuilder().setField(IMAGE_MD5).setOperator("in").addAllValues(imageMd5List).build());
        }
        if (Objects.nonNull(accountId)) {
            convReqList.add(OperatorVo.newBuilder().setField(ACCOUNT_ID).setOperator("=").addAllValues(Lists.newArrayList(accountId.toString())).build());
        }
        if (CollectionUtils.isNotEmpty(campaignIds)) {
            convReqList.add(OperatorVo.newBuilder().setField(CAMPAIGN_ID).setOperator("in").addAllValues(campaignIds.stream().map(String::valueOf).collect(Collectors.toList())).build());
        }
        if (CollectionUtils.isNotEmpty(unitIds)) {
            convReqList.add(OperatorVo.newBuilder().setField(UNIT_ID).setOperator("in").addAllValues(unitIds.stream().map(String::valueOf).collect(Collectors.toList())).build());
        }
        if (CollectionUtils.isNotEmpty(creativeIds)) {
            convReqList.add(OperatorVo.newBuilder().setField(CREATIVE_ID).setOperator("in").addAllValues(creativeIds.stream().map(String::valueOf).collect(Collectors.toList())).build());
        }

        if (Objects.equals(reportType, MaterialReportTypeEnum.IMAGE.getCode())) {
            if (Objects.isNull(picType) || Objects.equals(picType, ImageCostTableConstants.PIC_TYPE_ALL)) {
                convReqList.add(OperatorVo.newBuilder().setField(ImageCostTableConstants.STYLE_ABILITY).setOperator("in").addAllValues(Lists.newArrayList(ImageCostTableConstants.PIC_TYPE_STATIC.toString(), ImageCostTableConstants.PIC_TYPE_GIF.toString())).build());
            } else {
                convReqList.add(OperatorVo.newBuilder().setField(ImageCostTableConstants.STYLE_ABILITY).setOperator("in").addAllValues(Lists.newArrayList(picType.toString())).build());
            }
        }

        return convReqList;
    }

    private List<OperatorVo> generateImageCostReq(Integer accountId, List<Integer> campaignIds, List<Integer> unitIds, List<Integer> creativeIds, List<String> imageMd5List, Integer picType, Integer creativeType, Boolean needFilter, Long fromTime, Long toTime, Integer page, Integer pageSize) {
        List<OperatorVo> reqList = new ArrayList<>();
        if (Objects.nonNull(accountId)) {
            reqList.add(OperatorVo.newBuilder().setField(ACCOUNT_ID).setOperator("in").addAllValues(Lists.newArrayList(accountId.toString())).build());
        }
        if (CollectionUtils.isNotEmpty(imageMd5List)) {
            reqList.add(OperatorVo.newBuilder().setField(IMAGE_MD5).setOperator("in").addAllValues(imageMd5List).build());
        }
        if (CollectionUtils.isNotEmpty(campaignIds)) {
            reqList.add(OperatorVo.newBuilder().setField(PLAN_ID).setOperator("in").addAllValues(campaignIds.stream().map(String::valueOf).collect(Collectors.toList())).build());
        }
        if (CollectionUtils.isNotEmpty(unitIds)) {
            reqList.add(OperatorVo.newBuilder().setField(UNIT_ID).setOperator("in").addAllValues(unitIds.stream().map(String::valueOf).collect(Collectors.toList())).build());
        }
        if (CollectionUtils.isNotEmpty(creativeIds)) {
            reqList.add(OperatorVo.newBuilder().setField(CREATIVE_ID).setOperator("in").addAllValues(creativeIds.stream().map(String::valueOf).collect(Collectors.toList())).build());
        }

        if (Objects.isNull(picType) || Objects.equals(picType, ImageCostTableConstants.PIC_TYPE_ALL)) {
            reqList.add(OperatorVo.newBuilder().setField(ImageCostTableConstants.STYLE_ABILITY).setOperator("in").addAllValues(Lists.newArrayList(ImageCostTableConstants.PIC_TYPE_STATIC.toString(), ImageCostTableConstants.PIC_TYPE_GIF.toString())).build());
        } else {
            reqList.add(OperatorVo.newBuilder().setField(ImageCostTableConstants.STYLE_ABILITY).setOperator("in").addAllValues(Lists.newArrayList(picType.toString())).build());
        }

        if (Objects.nonNull(fromTime)) {
            LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(fromTime), ZoneId.systemDefault());
            String formattedFromTime = localDateTime.format(formatter);
            reqList.add(OperatorVo.newBuilder().setField(LOG_DATE).setOperator(">=").addAllValues(Lists.newArrayList(formattedFromTime)).build());
        }
        if (Objects.nonNull(toTime)) {
            LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(toTime), ZoneId.systemDefault());
            String formattedToTime = localDateTime.format(formatter);
            reqList.add(OperatorVo.newBuilder().setField(LOG_DATE).setOperator("<=").addAllValues(Lists.newArrayList(formattedToTime)).build());
        }

        if (Objects.equals(creativeType, StatReq.CreativeType.CUSTOMIZED_CREATIVE_VALUE)) {
            reqList.add(OperatorVo.newBuilder().setField(PROGRAMMATIC).setOperator("=").addAllValues(Lists.newArrayList(costTableCreativeTypeCustomize)).build());
        }
        if (Objects.equals(creativeType, StatReq.CreativeType.PROGRAMMATIC_CREATIVE_VALUE)) {
            reqList.add(OperatorVo.newBuilder().setField(PROGRAMMATIC).setOperator("=").addAllValues(Lists.newArrayList(costTableCreativeTypeProgrammatic)).build());
        }

        reqList.add(OperatorVo.newBuilder().setField(IMAGE_MD5).setOperator("!=").addAllValues(Lists.newArrayList(StringUtils.EMPTY)).build());

        if (BooleanUtils.isTrue(needFilter)) {
            reqList.add(OperatorVo.newBuilder().setField(ImageCostTableConstants.PV).setOperator("!=").addAllValues(Lists.newArrayList("0")).build());
            reqList.add(OperatorVo.newBuilder().setField(ImageCostTableConstants.CLICK).setOperator("!=").addAllValues(Lists.newArrayList("0")).build());
            reqList.add(OperatorVo.newBuilder().setField(ImageCostTableConstants.COST_MILLI).setOperator("!=").addAllValues(Lists.newArrayList("0")).build());
        }


        return reqList;
    }

    private List<OperatorVo> generateArchiveCostReq(List<Integer> campaignIds, List<Integer> unitIds, List<Integer> creativeIds,
                                                    List<Long> aids, Integer accountId, Integer sceneType,
                                                    Integer creativeType, Long fromTime, Long toTime) {
        List<OperatorVo> reqList = new ArrayList<>();
        reqList.add(OperatorVo.newBuilder().setField(AVID).setOperator("!=").addAllValues(Lists.newArrayList("0")).build());

        if (Objects.nonNull(fromTime)) {
            LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(fromTime), ZoneId.systemDefault());
            String formattedFromTime = localDateTime.format(formatter);
            reqList.add(OperatorVo.newBuilder().setField(LOG_DATE).setOperator(">=").addAllValues(Lists.newArrayList(formattedFromTime)).build());
        }
        if (Objects.nonNull(toTime)) {
            LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(toTime), ZoneId.systemDefault());
            String formattedToTime = localDateTime.format(formatter);
            reqList.add(OperatorVo.newBuilder().setField(LOG_DATE).setOperator("<=").addAllValues(Lists.newArrayList(formattedToTime)).build());
        }
        if (Objects.equals(sceneType, StatReq.SceneType.VERTICAL_SCENE_VALUE) || Objects.equals(sceneType, StatReq.SceneType.HORIZONTAL_SCENE_VALUE) || Objects.equals(sceneType, StatReq.SceneType.GRAPHIC_AND_TEXT_SCENE_VIDEO_LANDING_PAGE_VALUE)) {
            reqList.add(OperatorVo.newBuilder().setField(AVID_SCENSE).setOperator("=").addAllValues(Lists.newArrayList(sceneType.toString())).build());
        }
        if (Objects.equals(creativeType, StatReq.CreativeType.CUSTOMIZED_CREATIVE_VALUE)) {
            reqList.add(OperatorVo.newBuilder().setField(PROGRAMMATIC).setOperator("=").addAllValues(Lists.newArrayList(costTableCreativeTypeCustomize)).build());
        }
        if (Objects.equals(creativeType, StatReq.CreativeType.PROGRAMMATIC_CREATIVE_VALUE)) {
            reqList.add(OperatorVo.newBuilder().setField(PROGRAMMATIC).setOperator("=").addAllValues(Lists.newArrayList(costTableCreativeTypeProgrammatic)).build());
        }
        if (CollectionUtils.isNotEmpty(aids)) {
            reqList.add(OperatorVo.newBuilder().setField(AVID).setOperator("in").addAllValues(aids.stream().map(String::valueOf).collect(Collectors.toList())).build());
        }
        if (Objects.nonNull(accountId)) {
            reqList.add(OperatorVo.newBuilder().setField(ACCOUNT_ID).setOperator("=").addAllValues(Lists.newArrayList(accountId.toString())).build());
        }
        if (CollectionUtils.isNotEmpty(campaignIds)) {
            reqList.add(OperatorVo.newBuilder().setField(PLAN_ID).setOperator("in").addAllValues(campaignIds.stream().map(String::valueOf).collect(Collectors.toList())).build());
        }
        if (CollectionUtils.isNotEmpty(unitIds)) {
            reqList.add(OperatorVo.newBuilder().setField(UNIT_ID).setOperator("in").addAllValues(unitIds.stream().map(String::valueOf).collect(Collectors.toList())).build());
        }
        if (CollectionUtils.isNotEmpty(creativeIds)) {
            reqList.add(OperatorVo.newBuilder().setField(CREATIVE_ID).setOperator("in").addAllValues(creativeIds.stream().map(String::valueOf).collect(Collectors.toList())).build());
        }
        return reqList;
    }

    private List<String> getPicMd5List(String picId) {
        List<String> picMd5List = new ArrayList<>();
        if (StringUtils.isNotBlank(picId)) {
            String[] split = StringUtils.split(picId, ",");
            picMd5List = Arrays.stream(split).collect(Collectors.toList());
        }
        return picMd5List;
    }


    private List<String> getMd5ByMaterialCenterId(String materialCenterId) {
        if (StringUtils.isBlank(materialCenterId)) {
            return Collections.emptyList();
        }

        String[] split = StringUtils.split(materialCenterId, ",");
        List<String> materialCenterIdList = Arrays.stream(split).collect(Collectors.toList());

        Map<String, MaterialIdRegistry> materialCenterIdsMap = materialCenterGrpcService.findByMaterialIds(materialCenterIdList);
        return materialCenterIdsMap.values().stream().map(MaterialIdRegistry::getMaterialUk).collect(Collectors.toList());
    }

    private List<Long> getAvidByMaterialCenterId(String materialCenterId) {

        if (StringUtils.isBlank(materialCenterId)) {
            return Collections.emptyList();
        }

        String[] split = StringUtils.split(materialCenterId, ",");
        List<String> materialCenterIdList = Arrays.stream(split).collect(Collectors.toList());

        Map<String, MaterialIdRegistry> materialCenterIdsMap = materialCenterGrpcService.findByMaterialIds(materialCenterIdList);
        List<String> md5List = materialCenterIdsMap.values().stream().map(MaterialIdRegistry::getMaterialUk).collect(Collectors.toList());
        List<ArchiveInfo> cmArchiveInfosList = cpmScvProxy.queryCmArchiveByConditions(md5List);
        return cmArchiveInfosList.stream().map(ArchiveInfo::getAvid).collect(Collectors.toList());

    }

    private Map<Long, String> getMaterialCenterIdByAvid(List<Long> avids) {
        Map<Long, String> avidAndMd5 = cpmScvProxy.queryCmArchiveByConditionByAvid(avids).stream().collect(Collectors.toMap(ArchiveInfo::getAvid, ArchiveInfo::getMd5, (c1, c2) -> c2));
        ArrayList<String> videoMd5s = new ArrayList<>(avidAndMd5.values());
        Map<String, MaterialIdRegistry> byMd5s = CollectionUtils.isEmpty(videoMd5s) ? new HashMap<>()
                : materialCenterGrpcService.findByMd5s(videoMd5s, MaterialIdTypeEnum.VIDEO.getName());


        HashMap<Long, String> avidAndMaterialCenterId = new HashMap<>();
        avidAndMd5.keySet().forEach(avid -> {
            MaterialIdRegistry materialIdRegistry = byMd5s.getOrDefault(avidAndMd5.getOrDefault(avid, ""), null);
            String materialCenterId = Objects.nonNull(materialIdRegistry) ? materialIdRegistry.getMaterialId() : "";
            avidAndMaterialCenterId.put(avid, materialCenterId);
        });
        return avidAndMaterialCenterId;
    }

    private String time2String(long time) {
        LocalDateTime ldt = LocalDateTime.ofInstant(Instant.ofEpochMilli(time), ZoneId.systemDefault());
        return ldt.toLocalDate().format(formatterWithHyphen);
    }

    public String convertToHyphenFormat(String logDate) {

        LocalDate date = LocalDate.parse(logDate, formatter);
        return date.format(formatterWithHyphen);
    }

    private Double multiplyThousand(String multiplier) {
        return new BigDecimal(multiplier).multiply(thousand).doubleValue();
    }

    private Double multiplyHundred(String multiplier) {
        return new BigDecimal(multiplier).multiply(hundred).doubleValue();
    }

    private Double divideHundred(String dividend) {
        return new BigDecimal(dividend).divide(hundred).doubleValue();
    }

    private Double divideThousand(String dividend) {
        return new BigDecimal(dividend).divide(thousand).doubleValue();
    }

    private Double divideTenThousand(String dividend) {
        return new BigDecimal(dividend).divide(new BigDecimal(tenThousand)).doubleValue();
    }

    private Double divide(String dividend, String divisor) {
        return divide(dividend, divisor, defaultScale);
    }

    private Double divide(String dividend, String divisor, Integer scale) {

        BigDecimal bigDecimalDividend = new BigDecimal(dividend);
        BigDecimal bigDecimalDivisor = new BigDecimal(divisor);

        if (bigDecimalDivisor.equals(BigDecimal.ZERO)) {
            return BigDecimal.ZERO.doubleValue();
        }

        if (Objects.isNull(scale)) {
            return bigDecimalDividend.divide(bigDecimalDivisor).doubleValue();
        }

        return bigDecimalDividend.divide(bigDecimalDivisor, scale, RoundingMode.HALF_UP).doubleValue();

    }


    private OsHeader getArchiveCostHeader() {
        return OsHeader.newBuilder()
                .setAppKey(appkey)
                .setSecret(secret)
                .setApiId(archiveCostApiId)
                .build();
    }

    private OsHeader getImageCostHeader() {
        return OsHeader.newBuilder()
                .setAppKey(appkey)
                .setSecret(secret)
                .setApiId(picCostApiId)
                .build();
    }

    private OsHeader getConvHeader() {
        return OsHeader.newBuilder()
                .setAppKey(appkey)
                .setSecret(secret)
                .setApiId(convApiId)
                .build();
    }

}
