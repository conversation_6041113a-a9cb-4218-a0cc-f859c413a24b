package com.bilibili.adp.cpc.dao.ad_core.querydsl.pos;

import javax.annotation.Generated;

/**
 * LauShadowCreativePo is a Querydsl bean type
 */
@Generated("com.querydsl.codegen.BeanSerializer")
public class LauShadowCreativePo {

    private Integer accountId;

    private Integer auditStatus;

    private Integer campaignId;

    private Integer creativeId;

    private Integer creativeStatus;

    private java.sql.Timestamp ctime;

    private Long id;

    private Long mgkPageId;

    private java.sql.Timestamp mtime;

    private Long pageGroupId;

    private String reason;

    private String shadowCreative;

    private Integer unitId;

    public Integer getAccountId() {
        return accountId;
    }

    public void setAccountId(Integer accountId) {
        this.accountId = accountId;
    }

    public Integer getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(Integer auditStatus) {
        this.auditStatus = auditStatus;
    }

    public Integer getCampaignId() {
        return campaignId;
    }

    public void setCampaignId(Integer campaignId) {
        this.campaignId = campaignId;
    }

    public Integer getCreativeId() {
        return creativeId;
    }

    public void setCreativeId(Integer creativeId) {
        this.creativeId = creativeId;
    }

    public Integer getCreativeStatus() {
        return creativeStatus;
    }

    public void setCreativeStatus(Integer creativeStatus) {
        this.creativeStatus = creativeStatus;
    }

    public java.sql.Timestamp getCtime() {
        return ctime;
    }

    public void setCtime(java.sql.Timestamp ctime) {
        this.ctime = ctime;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getMgkPageId() {
        return mgkPageId;
    }

    public void setMgkPageId(Long mgkPageId) {
        this.mgkPageId = mgkPageId;
    }

    public java.sql.Timestamp getMtime() {
        return mtime;
    }

    public void setMtime(java.sql.Timestamp mtime) {
        this.mtime = mtime;
    }

    public Long getPageGroupId() {
        return pageGroupId;
    }

    public void setPageGroupId(Long pageGroupId) {
        this.pageGroupId = pageGroupId;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getShadowCreative() {
        return shadowCreative;
    }

    public void setShadowCreative(String shadowCreative) {
        this.shadowCreative = shadowCreative;
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    @Override
    public String toString() {
         return "accountId = " + accountId + ", auditStatus = " + auditStatus + ", campaignId = " + campaignId + ", creativeId = " + creativeId + ", creativeStatus = " + creativeStatus + ", ctime = " + ctime + ", id = " + id + ", mgkPageId = " + mgkPageId + ", mtime = " + mtime + ", pageGroupId = " + pageGroupId + ", reason = " + reason + ", shadowCreative = " + shadowCreative + ", unitId = " + unitId;
    }

}

