package com.bilibili.adp.cpc.databus;

import com.alibaba.fastjson.JSONObject;
import com.bilibili.adp.cpc.biz.services.creative.bos.CreativeEsBo;
import com.bilibili.warp.databus.DatabusProperty;
import com.bilibili.warp.databus.DatabusTemplate;
import com.bilibili.warp.databus.Message;
import com.bilibili.warp.databus.PubResult;
import com.bilibili.warp.spring.boot.autoconfigure.databus.DatabusProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * 生成创意 es 发布器
 **/
@Slf4j
@Service
public class GenCreativeEs1Pub {

    @Resource
    private DatabusTemplate databusTemplate;

    public static final String DATABUS_KEY = "adp-creative-es1";
    private final String topic;
    private final String group;

    @Value("${debug:1}")
    private Integer isDebug;

    public GenCreativeEs1Pub(DatabusProperties databusProperties) {
        DatabusProperty databusProperty = databusProperties.getProperties().get(DATABUS_KEY);
        log.info("GenCreativeEs1Pub databusProperty={}", databusProperty);

        this.topic = databusProperty.getTopic();
        this.group = databusProperty.getPub().getGroup();
        log.info("GenCreativeEs1Pub topic={},group={}", topic, group);
    }

    public void pub(JSONObject bo, Integer creativeId) {
        log.info("GenCreativeEs1Pub pub, msg={}", bo);

        // messageKey和value自定义，value会被配置的serializer序列化
        Message message = Message.Builder
                .of(creativeId.toString(), bo)
                .build();
        // topic 和 group 按照申请的填写即可. 可以自行注入相关配置，用配置的方式来发送消息
        PubResult result = databusTemplate.pub(topic, group, message);
        if (result.isSuccess()) {
            log.info("pub gen creative es1 msg success, msg={}", bo);
        } else {
            Throwable throwable = result.getThrowable();
            log.error("pub gen creative es1 msg error ", throwable);
            throw new RuntimeException(throwable.getMessage());
        }
    }

    public void batchPub(List<CreativeEsBo> esBos) {
        if (CollectionUtils.isEmpty(esBos)){
            return;
        }
        log.info("GenCreativeEs1Pub batchPub, esBos={}", esBos.size());

        // messageKey和value自定义，value会被配置的serializer序列化
        List<Message> messages = new ArrayList<>(esBos.size());
        esBos.forEach(x -> {
            JSONObject newJson = new JSONObject();
            newJson.put("new", x);
            newJson.put("action", "update");
            Message message = Message.Builder
                    .of(x.getCreativeId().toString(), newJson)
                    .build();
            if (isDebug > 0) {
                log.info("batchPub, newJson={}", newJson);
            }
            messages.add(message);
        });

        // topic 和 group 按照申请的填写即可. 可以自行注入相关配置，用配置的方式来发送消息
        PubResult result = databusTemplate.batchPub(topic, group, messages);
        if (result.isSuccess()) {
            log.info("pub gen creative es1 msg success, msg={}", messages.size());
        } else {
            Throwable throwable = result.getThrowable();
            log.error("pub gen creative es1 msg error ", throwable);
            throw new RuntimeException(throwable.getMessage());
        }
    }

}
