package com.bilibili.adp.cpc.biz.converter.game;

import com.bapis.ad.adp.mini_game.*;
import com.bilibili.adp.common.util.Page;
import com.bilibili.adp.cpc.biz.services.game.bos.QueryLauMiniGameDto;
import com.bilibili.adp.launch.api.minigame.dto.CreateLauMiniGameDto;
import com.bilibili.adp.cpc.biz.services.game.bos.LauMiniGameDto;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/2/1 15:14
 */
@Component
public class GameConverter {

    public QueryLauMiniGameDto convertDropListRequest2Dto(MiniGameDropListRequest request) {
        if (request == null) {
            return null;
        }

        QueryLauMiniGameDto.QueryLauMiniGameDtoBuilder queryLauMiniGameDto = QueryLauMiniGameDto.builder();

        queryLauMiniGameDto.accountId(request.getAccountId());

        return queryLauMiniGameDto.build();
    }

    public List<MiniGameDropListItem> convertDtos2ReplyList(List<LauMiniGameDto> list) {
        if (list == null) {
            return null;
        }

        List<MiniGameDropListItem> list1 = new ArrayList<MiniGameDropListItem>(list.size());
        for (LauMiniGameDto lauMiniGameDto : list) {
            list1.add(convertDto2Reply(lauMiniGameDto));
        }

        return list1;
    }

    public MiniGameDropListItem convertDto2Reply(LauMiniGameDto game) {
        if (game == null) {
            return null;
        }

        MiniGameDropListItem.Builder miniGameDropListItem = MiniGameDropListItem.newBuilder();

        miniGameDropListItem.setGameUrl(game.getGameUrl());
        miniGameDropListItem.setOriginId(game.getOriginId());
        if (game.getId() != null) {
            miniGameDropListItem.setId(game.getId());
        }
        miniGameDropListItem.setName(game.getName());

        miniGameDropListItem.setState(MiniGameState.valueOf(game.getState()));

        return miniGameDropListItem.build();
    }

    public CreateLauMiniGameDto convertAddRequest2Dto(AddMiniGameRequest request) {
        if (request == null) {
            return null;
        }

        CreateLauMiniGameDto.CreateLauMiniGameDtoBuilder createLauMiniGameDto = CreateLauMiniGameDto.builder();

        createLauMiniGameDto.name(request.getName());
        createLauMiniGameDto.originId(request.getOriginId());
        createLauMiniGameDto.gameUrl(request.getGameUrl());

        return createLauMiniGameDto.build();
    }

    public QueryLauMiniGameDto convertPageRequest2Dto(MiniGamePageListRequest request, Page page) {
        if (request == null && page == null) {
            return null;
        }

        QueryLauMiniGameDto.QueryLauMiniGameDtoBuilder queryLauMiniGameDto = QueryLauMiniGameDto.builder();

        if (request != null) {
            queryLauMiniGameDto.nameLike(request.getNameLike());
            List<Integer> list = request.getIdsList();
            if (list != null) {
                queryLauMiniGameDto.ids(list);
            }
            queryLauMiniGameDto.accountId(request.getAccountId());
        }
        if (page != null) {
            queryLauMiniGameDto.page(page);
        }

        return queryLauMiniGameDto.build();
    }

    public List<MiniGamePageListItem> convertGameDtos2Reply(List<LauMiniGameDto> records) {
        if (records == null) {
            return null;
        }

        List<MiniGamePageListItem> list = new ArrayList<MiniGamePageListItem>(records.size());
        for (LauMiniGameDto lauMiniGameDto : records) {
            list.add(convertGameDto2Reply(lauMiniGameDto));
        }

        return list;
    }

    public MiniGamePageListItem convertGameDto2Reply(LauMiniGameDto game) {
        if (game == null) {
            return null;
        }

        com.bapis.ad.adp.mini_game.MiniGamePageListItem.Builder miniGamePageListItem = MiniGamePageListItem.newBuilder();

        if (game.getId() != null) {
            miniGamePageListItem.setId(game.getId());
        }
        miniGamePageListItem.setOriginId(game.getOriginId());
        miniGamePageListItem.setName(game.getName());
        miniGamePageListItem.setGameUrl(game.getGameUrl());

        miniGamePageListItem.setCtime(game.getCtime().getTime());
        miniGamePageListItem.setState(MiniGameState.valueOf(game.getState()));
        miniGamePageListItem.setMtime(game.getMtime().getTime());

        return miniGamePageListItem.build();
    }
}
