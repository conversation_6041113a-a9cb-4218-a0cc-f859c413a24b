package com.bilibili.adp.cpc.converter;

import com.bilibili.adp.cpc.biz.services.target_package.api.upgrade.dto.SingleBiliClientVersionDto;
import com.bilibili.adp.cpc.biz.services.target_package.api.upgrade.dto.TargetOsCommonDto;
import com.bilibili.adp.cpc.po.ad.*;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/24 15:42
 */
@Mapper
public interface TargetPoMapper {

    TargetPoMapper INSTANCE = Mappers.getMapper(TargetPoMapper.class);

    List<SingleBiliClientVersionDto> convertPackageBiliClientUpdatePos2Dtos(List<ResTargetPackageBiliClientUpgradePo> packageBiliClientUpgradePos);
    SingleBiliClientVersionDto convertPackageBiliClientUpdatePo2Dto(ResTargetPackageBiliClientUpgradePo packageBiliClientUpgradePo);
    List<SingleBiliClientVersionDto> convertUnitBiliClientUpdatePos2Dtos(List<LauUnitTargetBiliClientUpgradePo> unitTargetBiliClientUpgradePos);


    SingleBiliClientVersionDto convertJobTargetBiliClientUpdatePo2Dto(LauManagedCampaignJobTargetBiliClientPo jobTargetBiliClientPo);


    List<TargetOsCommonDto> convertPackageOsVersionPos2CommonBos(List<ResTargetPackageOsVersionUpgradePo> pos);

    List<TargetOsCommonDto> convertUnitOsVersionPos2CommonBos(List<LauUnitTargetOsVersionUpgradePo> pos);


}
