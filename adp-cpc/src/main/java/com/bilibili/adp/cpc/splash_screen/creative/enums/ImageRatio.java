package com.bilibili.adp.cpc.splash_screen.creative.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum ImageRatio {
    RATIO_10_16(1, "9比16", 1600, 2560),

    RATIO_1_2(2, "1比2", 1280, 2560),
    ;

    private final int code;
    private final String desc;
    private final int width;
    private final int height;

    public static ImageRatio getByCode(int ratio) {
        return Arrays.stream(values())
                .filter(imageRatio -> imageRatio.code == ratio)
                .findFirst()
                .orElseThrow(IllegalArgumentException::new);
    }

}
