package com.bilibili.adp.cpc.splash_screen.creative.service;

import com.bapis.ad.mgk.LandingPageServiceGrpc;
import com.bapis.ad.mgk.ValidateAndGetPageReply;
import com.bapis.ad.mgk.ValidateAndGetPageReq;
import com.bilibili.adp.common.util.biz.MgkLandingPageParser;
import com.bilibili.adp.cpc.core.constants.JumpType;
import com.bilibili.adp.cpc.splash_screen.creative.strategy.bos.CreativeContext;
import com.bilibili.adp.cpc.splash_screen.creative.strategy.jump_url_macro.IJumpUrlMacroStrategy;
import com.bilibili.adp.cpc.splash_screen.creative.strategy.jump_url_macro.JumpUrlMacroStrategy;
import com.bilibili.adp.cpc.utils.MgkLandingPageParserUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Slf4j
@Service
@RequiredArgsConstructor
public class SplashScreenCreativeJumpUrlService {
    private final LandingPageServiceGrpc.LandingPageServiceBlockingStub landingPageServiceBlockingStub;
    private final List<IJumpUrlMacroStrategy> strategies;

     private IJumpUrlMacroStrategy macroStrategy(Integer promotionPurposeType) {
        return strategies
                .stream()
                .filter(strategy -> Objects.equals(promotionPurposeType, strategy.getPromotionPurposeType()))
                .findFirst()
                .orElse(new JumpUrlMacroStrategy() {
                    @Override
                    public Integer getPromotionPurposeType() {
                        return promotionPurposeType;
                    }
                });
    }

    public String addMacro(Integer promotionPurposeType, CreativeContext context, String jumpUrl) {
        final IJumpUrlMacroStrategy jumpUrlMacroStrategy = macroStrategy(promotionPurposeType);
        return jumpUrlMacroStrategy.addMacro(context, jumpUrl);
    }

    public String removeMacro(Integer promotionPurposeType, String jumpUrl) {
        final IJumpUrlMacroStrategy jumpUrlMacroStrategy = macroStrategy(promotionPurposeType);
        return jumpUrlMacroStrategy.removeMacro(jumpUrl);
    }


    public ValidateAndGetPageReply getPageReply(Integer jumpType, String jumpUrl) {
        if (Objects.equals(JumpType.MGK_PAGE_ID, jumpType)) {
            final ValidateAndGetPageReq request = ValidateAndGetPageReq.newBuilder()
                    .setPageId(Long.parseLong(jumpUrl))
                    .build();
            return landingPageServiceBlockingStub.validatePageIdAndGetPage(request);
        } else {
            return ValidateAndGetPageReply.newBuilder()
                    .setPageId(0L)
                    .setAdVersionControlId(0)
                    .setJumpUrl(jumpUrl)
                    .build();
        }
    }

    public String parse(Integer jumpType, String jumpUrl) {
        if (Objects.equals(JumpType.MGK_PAGE_ID, jumpType)) {
            return MgkLandingPageParserUtils.parse(jumpUrl).getPageId().toString();
        }
        return jumpUrl;
    }
}
