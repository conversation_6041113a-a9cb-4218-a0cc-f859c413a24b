package com.bilibili.adp.cpc.converter;

import com.bapis.ad.jupiter.img.PicCreativeAnalysisReport;
import com.bilibili.adp.cpc.biz.services.pic.bos.ExportPicReportBo;
import com.bilibili.adp.cpc.biz.services.pic.bos.PicAnalysisReportExportDto;
import com.bilibili.adp.cpc.biz.services.pic.bos.PicReportDataAnalysisBo;
import com.bilibili.adp.cpc.biz.services.pic.bos.QueryPicReportBo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/2/1
 **/

@Mapper(imports = {RoundingMode.class})
public interface PicAnalysisReportConverter {

    PicAnalysisReportConverter MAPPER = Mappers.getMapper(PicAnalysisReportConverter.class);

    BigDecimal HUNDRED = new BigDecimal(100);

    @Mapping(source = "imageMd5", target = "picMd5")
    @Mapping(source = "imageUrl", target = "picUrl")
    // 激活后24小时变现相关映射
    @Mapping(source = "gameChargeIn24HCount", target = "gameChargeIn24hCount")
    @Mapping(source = "costPerGameChargeIn24H", target = "costPerGameChargeIn24h")
    @Mapping(target = "gameChargeIn24hAmount", expression = "java(BigDecimal.valueOf(dataBo.getGameChargeIn24HAmount()).divide(HUNDRED, 2, RoundingMode.HALF_UP))")
    @Mapping(target = "gameChargeIn24hRoi", expression = "java(BigDecimal.valueOf(dataBo.getGameChargeIn24HRoi()).setScale(2, RoundingMode.HALF_UP ))")
    // 激活后首自然日变现相关映射
    @Mapping(source = "gameChargeIn1DCount", target = "gameChargeIn1dCount")
    @Mapping(source = "costPerGameChargeIn1D", target = "costPerGameChargeIn1d")
    @Mapping(target = "gameChargeIn1dAmount", expression = "java(BigDecimal.valueOf(dataBo.getGameChargeIn1DAmount()).divide(HUNDRED, 2, RoundingMode.HALF_UP))")
    @Mapping(target = "gameChargeIn1dRoi", expression = "java(BigDecimal.valueOf(dataBo.getGameChargeIn1DRoi()).setScale(2, RoundingMode.HALF_UP ))")
    // 激活后7日变现相关映射
    @Mapping(source = "gameChargeIn7DCount", target = "gameChargeIn7dCount")
    @Mapping(source = "costPerGameChargeIn7D", target = "costPerGameChargeIn7d")
    @Mapping(target = "gameChargeIn7dAmount", expression = "java(BigDecimal.valueOf(dataBo.getGameChargeIn7DAmount()).divide(HUNDRED, 2, RoundingMode.HALF_UP))")
    @Mapping(target = "gameChargeIn7dRoi", expression = "java(BigDecimal.valueOf(dataBo.getGameChargeIn7DRoi()).setScale(2, RoundingMode.HALF_UP ))")
    // 激活后14日变现相关映射
    @Mapping(source = "gameChargeIn14DCount", target = "gameChargeIn14dCount")
    @Mapping(source = "costPerGameChargeIn14D", target = "costPerGameChargeIn14d")
    @Mapping(target = "gameChargeIn14dAmount", expression = "java(BigDecimal.valueOf(dataBo.getGameChargeIn14DAmount()).divide(HUNDRED, 2, RoundingMode.HALF_UP))")
    @Mapping(target = "gameChargeIn14dRoi", expression = "java(BigDecimal.valueOf(dataBo.getGameChargeIn14DRoi()).setScale(2, RoundingMode.HALF_UP ))")
    // 激活后混合变现金额和ROI相关映射
    @Mapping(target = "gameChargeIn24hMixAmount", expression = "java(BigDecimal.valueOf(dataBo.getGameChargeIn24HMixAmount()).divide(HUNDRED, 2, RoundingMode.HALF_UP))")
    @Mapping(target = "gameChargeIn24hMixRoi", expression = "java(BigDecimal.valueOf(dataBo.getGameChargeIn24HMixRoi()).setScale(2, RoundingMode.HALF_UP ))")
    @Mapping(target = "gameChargeIn1dMixAmount", expression = "java(BigDecimal.valueOf(dataBo.getGameChargeIn1DMixAmount()).divide(HUNDRED, 2, RoundingMode.HALF_UP))")
    @Mapping(target = "gameChargeIn1dMixRoi", expression = "java(BigDecimal.valueOf(dataBo.getGameChargeIn1DMixRoi()).setScale(2, RoundingMode.HALF_UP ))")
    @Mapping(target = "gameChargeIn7dMixAmount", expression = "java(BigDecimal.valueOf(dataBo.getGameChargeIn7DMixAmount()).divide(HUNDRED, 2, RoundingMode.HALF_UP))")
    @Mapping(target = "gameChargeIn7dMixRoi", expression = "java(BigDecimal.valueOf(dataBo.getGameChargeIn7DMixRoi()).setScale(2, RoundingMode.HALF_UP ))")
    @Mapping(target = "gameChargeIn14dMixAmount", expression = "java(BigDecimal.valueOf(dataBo.getGameChargeIn14DMixAmount()).divide(HUNDRED, 2, RoundingMode.HALF_UP))")
    @Mapping(target = "gameChargeIn14dMixRoi", expression = "java(BigDecimal.valueOf(dataBo.getGameChargeIn14DMixRoi()).setScale(2, RoundingMode.HALF_UP ))")
    PicReportDataAnalysisBo grpcToBo(PicCreativeAnalysisReport dataBo);


    @Mapping(target = "pageSize", expression = "java(10000)")
    @Mapping(target = "page", expression = "java(1)")
    @Mapping(target = "picMd5List", ignore = true)
    @Mapping(target = "picMd5", ignore = true)
    @Mapping(target = "isChart", ignore = true)
    @Mapping(target = "isForExport", constant = "true")
    QueryPicReportBo toBo(ExportPicReportBo query);


    PicAnalysisReportExportDto toDto(PicReportDataAnalysisBo bo);

}
