package com.bilibili.adp.cpc.config;

import com.google.common.collect.Lists;
import io.grpc.Channel;
import io.grpc.ClientInterceptors;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import pleiades.component.rpc.client.BiliRpcClientCallInterceptor;
import pleiades.component.rpc.client.ChannelBuilder;
import pleiades.component.rpc.client.naming.RPCNamingClientNameResolverFactory;
import pleiades.venus.breaker.BiliBreakerProperties;
import pleiades.venus.naming.client.NamingClient;
import pleiades.venus.naming.client.Namings;
import pleiades.venus.naming.client.resolve.NamingResolver;

import java.util.concurrent.TimeUnit;

/**
 * @ClassName CpmAdpGrpcChannelConfig
 * <AUTHOR>
 * @Date 2022/12/13 5:12 下午
 * @Version 1.0
 **/
@Configuration
public class CpmAdpGrpcChannelConfig {

    private static final String mgkServiceDiscoveryId = "sycpb.cpm.mgk-portal";
    private static final String commentReplyServiceDiscoveryId = "main.community.reply";

    @Value("${mgk.grpc.zone}")
    private String mgkGrpcZone;

    @Value("${comment.reply.update.grpc.zone:sh001}")
    private String commentReplyUpdateZone;

    @Bean("mgkChannel")
    public Channel mgkChannel(NamingClient namingClient) {
        // BiliRpcClientCallInterceptor 自带熔断器和监控，如不需要可以不添加
        return ClientInterceptors.intercept(
                buildDefaultChannelWithZone(namingClient, mgkServiceDiscoveryId, mgkGrpcZone),
                Lists.newArrayList(new BiliRpcClientCallInterceptor(new BiliBreakerProperties())));
    }

    @Bean("commentReplyUpdateChannel")
    public Channel commentReplyUpdateChannel(NamingClient namingClient) {
        // BiliRpcClientCallInterceptor 自带熔断器和监控，如不需要可以不添加
        return ClientInterceptors.intercept(
                buildDefaultChannelWithZone(namingClient, commentReplyServiceDiscoveryId, commentReplyUpdateZone),
                Lists.newArrayList(new BiliRpcClientCallInterceptor(new BiliBreakerProperties())));
    }

    private Channel buildDefaultChannelWithZone(NamingClient namingClient,
                                                String serviceName,
                                                String zone) {
        NamingResolver resolver = namingClient.resolveForReady(serviceName, Namings.Scheme.GRPC);

        return ChannelBuilder.forTarget(serviceName)
                .directExecutor()
                .disableRetry()
                .defaultLoadBalancingPolicy("round_robin")
                .usePlaintext()
                .keepAliveTime(1, TimeUnit.SECONDS)
                .keepAliveTimeout(100, TimeUnit.MILLISECONDS)
                .idleTimeout(1, TimeUnit.MINUTES)
                .nameResolverFactory(new RPCNamingClientNameResolverFactory(zone, resolver))
                .build();
    }



}
