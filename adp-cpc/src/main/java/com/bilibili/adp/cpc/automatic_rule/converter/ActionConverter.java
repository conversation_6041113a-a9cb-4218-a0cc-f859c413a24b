package com.bilibili.adp.cpc.automatic_rule.converter;

import com.bilibili.adp.cpc.automatic_rule.bos.ActionBo;
import com.bilibili.adp.cpc.dao.querydsl.pos.LauAutomaticRuleActionPo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper
public interface ActionConverter {
    ActionConverter MAPPER = Mappers.getMapper(ActionConverter.class);

    @Mapping(target = "isDeleted", ignore = true)
    @Mapping(target = "ctime", ignore = true)
    @Mapping(target = "mtime", ignore = true)
    LauAutomaticRuleActionPo bo2Po(Integer accountId, Long ruleId, ActionBo bo);

    ActionBo po2Bo(LauAutomaticRuleActionPo po);

}
