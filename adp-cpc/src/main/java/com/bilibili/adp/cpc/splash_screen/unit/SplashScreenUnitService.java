package com.bilibili.adp.cpc.splash_screen.unit;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.enums.OperatorType;
import com.bilibili.adp.common.enums.SalesType;
import com.bilibili.adp.common.enums.SystemType;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.biz.bos.unit.QueryUnitBo;
import com.bilibili.adp.cpc.biz.bos.unit.targets.LauStartUpCrowdsBo;
import com.bilibili.adp.cpc.biz.services.AdpCpcLauStartUpCrowdsService;
import com.bilibili.adp.cpc.biz.services.account.LaunchAccountV1Service;
import com.bilibili.adp.cpc.biz.services.account.config.AccountConfig;
import com.bilibili.adp.cpc.biz.services.target_package.ResTargetItemService;
import com.bilibili.adp.cpc.biz.services.target_package.api.ICrowdPackService;
import com.bilibili.adp.cpc.biz.services.unit.CpcUnitServiceDelegate;
import com.bilibili.adp.cpc.biz.services.unit.LauSubjectService;
import com.bilibili.adp.cpc.biz.services.unit.dto.CpcUnitDto;
import com.bilibili.adp.cpc.core.LaunchCampaignService;
import com.bilibili.adp.cpc.core.LaunchUnitV1Service;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauCampaignPo;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauSplashScreenCreativePo;
import com.bilibili.adp.cpc.enums.ad.CampaignAdType;
import com.bilibili.adp.cpc.enums.ad.PromotionPurposeType;
import com.bilibili.adp.cpc.splash_screen.config.SplashScreenUnitConfig;
import com.bilibili.adp.cpc.splash_screen.unit.bos.LaunchTarget;
import com.bilibili.adp.cpc.splash_screen.unit.bos.SalesTypeOption;
import com.bilibili.adp.cpc.splash_screen.unit.bos.Target;
import com.bilibili.adp.cpc.splash_screen.unit.converter.TargetConverter;
import com.bilibili.adp.launch.api.common.LauSubjectType;
import com.bilibili.adp.passport.biz.manager.LiveBroadcastHttpService;
import com.bilibili.adp.passport.biz.manager.bean.LiveBroadcastRoomInfo;
import com.bilibili.adp.resource.api.crowd_pack.CrowdPackDto;
import com.bilibili.adp.resource.api.target_lau.dto.TargetTreeDto;
import com.bilibili.adp.resource.api.targetmeta.TargetType;
import com.bilibili.bjcom.querydsl.BaseQueryFactory;
import com.bilibili.brand.api.common.enums.UnitStatus;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.bilibili.adp.cpc.dao.ad_core.querydsl.QLauSplashScreenCreative.lauSplashScreenCreative;
import static com.bilibili.adp.cpc.dao.config.PlatformMySqlConfig.AD_CORE_BQF;

@Slf4j
@Service
@RequiredArgsConstructor
public class SplashScreenUnitService {
    private final LaunchAccountV1Service launchAccountV1Service;
    private final AccountConfig accountConfig;
    final Function<Integer, BigDecimal> FEN_2_YUAN_FUNCTION = fen -> BigDecimal.valueOf(fen).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
    public static final List<TargetType> SPLASH_SCREEN_SUPPORT_TARGETS = Arrays.asList(
            TargetType.GENDER,
            TargetType.AGE,
            TargetType.AREA,
            TargetType.OS,
            TargetType.CROW_PACK,
            TargetType.DEVICE_BRAND
    );
    /**
     * 人群包定向
     */
    public static final String UNIT_TARGET_CROWD_PACK_KEY = "crowd_pack";

    private final SplashScreenUnitConfig splashScreenUnitConfig;
    private static final SalesTypeOption CPM = SalesTypeOption.builder()
            .salesType(SalesType.CPM.getCode())
            .name(SalesType.CPM.getName())
            .build();
    private static final SalesTypeOption CPC = SalesTypeOption.builder()
            .salesType(SalesType.CPC.getCode())
            .name(SalesType.CPC.getName())
            .build();
    private final LaunchCampaignService launchCampaignService;
    private final ResTargetItemService resTargetItemService;
    private final ICrowdPackService crowdPackService;
    private final AdpCpcLauStartUpCrowdsService adpCpcLauStartUpCrowdsService;
    private final LaunchUnitV1Service launchUnitV1Service;

    private final LauSubjectService lauSubjectService;
    private final LiveBroadcastHttpService liveBroadcastHttpService;
    private final CpcUnitServiceDelegate LaunchCpcUnitServiceDelegate;

    @Resource(name = AD_CORE_BQF)
    private BaseQueryFactory adCoreBqf;

    public List<LaunchTarget> launchTarget(Integer campaignId, List<Integer> accountLabelIds) {
        final LauCampaignPo campaignPo = launchCampaignService.get(campaignId);
        List<LaunchTarget> launchTargetList = new ArrayList<>();
        if (Objects.equals(PromotionPurposeType.BRAND_SPREAD.getCode(), campaignPo.getPromotionPurposeType())) {
            final LaunchTarget goods = LaunchTarget.builder()
                    .promotionPurposeType(PromotionPurposeType.GOODS.getCode())
                    .promotionPurposeTypeDesc(PromotionPurposeType.GOODS.getDesc())
                    .build();
            launchTargetList.add(goods);

            if (accountLabelIds.contains(accountConfig.getSupportUnitSsLive())) {
                launchTargetList.add(LaunchTarget.builder()
                        .promotionPurposeType(PromotionPurposeType.GOODS_LIVE.getCode())
                        .promotionPurposeTypeDesc("直播间")
                        .build());
            }
        }
        return launchTargetList;
    }


    public static List<SalesTypeOption> SalesTypeOptions(Integer unitPromotionPurposeTypeCode) {
        final List<SalesTypeOption> salesTypeOptions = new LinkedList<>();
        final PromotionPurposeType unitPromotionPurposeType = PromotionPurposeType.getByCode(unitPromotionPurposeTypeCode);
        if (unitPromotionPurposeType == PromotionPurposeType.GOODS) {
            salesTypeOptions.add(CPM);
        }
        if (unitPromotionPurposeType == PromotionPurposeType.APP_DOWNLOAD) {
            salesTypeOptions.add(CPC);
            salesTypeOptions.add(CPM);
        }
        if (unitPromotionPurposeType == PromotionPurposeType.ON_SHELF_GAME) {
            salesTypeOptions.add(CPC);
            salesTypeOptions.add(CPM);
        }
        if (unitPromotionPurposeType == PromotionPurposeType.SALE_GOODS) {
            salesTypeOptions.add(CPC);
            salesTypeOptions.add(CPM);
        }
        if (unitPromotionPurposeType == PromotionPurposeType.GOODS_LIVE) {
            salesTypeOptions.add(CPM);
        }

        return salesTypeOptions;
    }

    public BigDecimal lowestBid(Integer accountId, Integer unitPromotionPurposeTypeCode, Integer salesTypeCode) {
       return FEN_2_YUAN_FUNCTION.apply(lowestBidFen(accountId, unitPromotionPurposeTypeCode, salesTypeCode));
    }

    public int lowestBidFen(Integer accountId, Integer unitPromotionPurposeTypeCode, Integer salesTypeCode) {
        final Optional<SplashScreenUnitConfig.LowestBid> any = splashScreenUnitConfig.getLowestBid()
                .stream()
                .filter(lb -> Objects.equals(lb.getSalesType(), salesTypeCode) && Objects.equals(lb.getPromotionPurposeType(), unitPromotionPurposeTypeCode))
                .findFirst();
        final int[] lowestBid = new int[1];
        any.ifPresent(lb -> {
            Integer labelId = lb.getLabelId();
            if (Utils.isPositive(labelId) && launchAccountV1Service.accountWithLabel(accountId, labelId)) {
                lowestBid[0] = lb.getConfiguredLowestBid();
            } else {
                lowestBid[0] = lb.getDefaultLowestBid();
            }
        });
        if (any.isPresent()) {
            return lowestBid[0];
        }
        return 0;
    }

    @SneakyThrows
    public Map<String, List<Target>> targets(Integer accountId) {
        final Map<TargetType, List<TargetTreeDto>> target2ItemMap = resTargetItemService.getAllValidTarget2ItemTreeMap();
        final Map<TargetType, List<TargetTreeDto>> filterdTarget2ItemMap = target2ItemMap
                .entrySet()
                .stream()
                .filter(entry -> SPLASH_SCREEN_SUPPORT_TARGETS.contains(entry.getKey()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        final Map<String, List<Target>> splashScreenTargetMap = filterdTarget2ItemMap
                .entrySet()
                .stream()
                .collect(Collectors.toMap(entry -> entry.getKey().getByName(), entry -> entry
                        .getValue()
                        .stream()
                        .map(TargetConverter.MAPPER::targetTree2Target)
                        .collect(Collectors.toList()))
                );
        final List<CrowdPackDto> crowdPackDtos = crowdPackService.getCrowdPackDtosByAccountId(accountId);
        final Set<Integer> startUpCrowdsIdSet = adpCpcLauStartUpCrowdsService.list()
                .stream()
                .map(LauStartUpCrowdsBo::getCrowdId)
                .collect(Collectors.toSet());
        final List<Target> crowdPackTarget = crowdPackDtos
                .stream()
                .filter(x -> !startUpCrowdsIdSet.contains(x.getId()))
                .map(TargetConverter.MAPPER::crowdPack2Target)
                .collect(Collectors.toList());
        splashScreenTargetMap.put(UNIT_TARGET_CROWD_PACK_KEY, crowdPackTarget);
        return splashScreenTargetMap;
    }

    public void refreshUnitStatus() {
        long id = 0;
        List<LauSplashScreenCreativePo> lauSplashScreenCreativePos = getLauSplashScreenCreativeById(id);

        while (!CollectionUtils.isEmpty(lauSplashScreenCreativePos)) {
            doRefreshUnitStatus(lauSplashScreenCreativePos);
            lauSplashScreenCreativePos = getLauSplashScreenCreativeById(lauSplashScreenCreativePos.get(lauSplashScreenCreativePos.size() - 1).getId());
        }
    }

    private void doRefreshUnitStatus(List<LauSplashScreenCreativePo> lauSplashScreenCreativePos) {

        List<Integer> campaignIds = lauSplashScreenCreativePos.stream().map(LauSplashScreenCreativePo::getCampaignId).collect(Collectors.toList());
        List<LauCampaignPo> lauCampaignPos = launchCampaignService.listCampaigns(campaignIds);
        Map<Integer, LauCampaignPo> campaignId2PoMap = CollectionUtils.isEmpty(lauCampaignPos) ? new HashMap<>() : lauCampaignPos.stream()
                .filter(po -> PromotionPurposeType.BRAND_SPREAD.getCode() == po.getPromotionPurposeType() && CampaignAdType.SPLASH_SCREEN.getCode().equals(po.getAdType()) && IsDeleted.VALID.getCode() == po.getIsDeleted())
                .collect(Collectors.toMap(LauCampaignPo::getCampaignId, Function.identity()));

        List<Integer> unitIds = lauSplashScreenCreativePos.stream().map(LauSplashScreenCreativePo::getUnitId).collect(Collectors.toList());
        QueryUnitBo queryUnitBo = QueryUnitBo.builder()
                .unitIds(unitIds)
                .promotionPurposeTypes(Lists.newArrayList(PromotionPurposeType.GOODS_LIVE.getCode()))
                .build();
        Map<Integer, CpcUnitDto> unitId2DtoMap = launchUnitV1Service.listUnits(queryUnitBo).stream()
                .filter(dto -> Utils.isPositive(dto.getSubjectId()))
                .collect(Collectors.toMap(CpcUnitDto::getUnitId, Function.identity()));

        List<CpcUnitDto> validCpcUnitDto = new ArrayList<>();
        for (LauSplashScreenCreativePo lauSplashScreenCreativePo : lauSplashScreenCreativePos) {
            LauCampaignPo lauCampaignPo = campaignId2PoMap.get(lauSplashScreenCreativePo.getCampaignId());
            if (lauCampaignPo == null) {
                continue;
            }

            CpcUnitDto cpcUnitDto = unitId2DtoMap.get(lauSplashScreenCreativePo.getUnitId());
            if (cpcUnitDto == null) {
                continue;
            }

            if (UnitStatus.VALID.getCode() == cpcUnitDto.getUnitStatus()) {
                validCpcUnitDto.add(cpcUnitDto);
            }
        }

        try {
            finishUnit(validCpcUnitDto);
        } catch (Exception e) {
            log.warn("带货闪屏直播间关播结束单元异常，unitIds:{}",validCpcUnitDto.stream().map(CpcUnitDto::getUnitId).collect(Collectors.toList()));
        }
    }


    private void finishUnit(List<CpcUnitDto> validCpcUnitDto) throws ServiceException {
        if (CollectionUtils.isEmpty(validCpcUnitDto)) {
            return;
        }

        List<Integer> subjectIds = validCpcUnitDto.stream().map(CpcUnitDto::getSubjectId).collect(Collectors.toList());
        Map<Integer, String> subjectId2MaterialIdMap = lauSubjectService.querySubjectMaterialRel(subjectIds, LauSubjectType.LIVE_ROOM);
        if (MapUtils.isEmpty(subjectId2MaterialIdMap)) {
            return;
        }

        List<Integer> roomIds = subjectId2MaterialIdMap.values().stream().map(Integer::valueOf).collect(Collectors.toList());
        Map<Integer, LiveBroadcastRoomInfo> roomId2RoomInfoMap = liveBroadcastHttpService.queryLiveBroadcastRoomInfoByIds(roomIds);

        Operator system = Operator.builder()
                .operatorName("System")
                .operatorType(OperatorType.SYSTEM)
                .systemType(SystemType.CPM)
                .build();
        for (CpcUnitDto cpcUnitDto : validCpcUnitDto) {
            if (isRoomNotLive(cpcUnitDto.getSubjectId(), subjectId2MaterialIdMap, roomId2RoomInfoMap)) {
                system.setOperatorId(cpcUnitDto.getAccountId());
                LaunchCpcUnitServiceDelegate.batchEnd(system, Lists.newArrayList(cpcUnitDto.getUnitId()));
            }
        }
    }

    private boolean isRoomNotLive(Integer subjectId, Map<Integer, String> subjectId2MaterialIdMap, Map<Integer, LiveBroadcastRoomInfo> roomId2RoomInfoMap) {
        if (!Utils.isPositive(subjectId)) {
            return false;
        }

        String materialId = subjectId2MaterialIdMap.get(subjectId);
        if (!StringUtils.hasText(materialId)) {
            return false;
        }

        Integer roomId = Integer.valueOf(materialId);
        LiveBroadcastRoomInfo roomInfo = roomId2RoomInfoMap.get(roomId);
        return roomInfo == null || roomInfo.getIsShow() == null || roomInfo.getIsShow() == 0;
    }

    private List<LauSplashScreenCreativePo> getLauSplashScreenCreativeById(Long id) {
        return adCoreBqf.selectFrom(lauSplashScreenCreative)
                .where(lauSplashScreenCreative.id.gt(id)
                        .and(lauSplashScreenCreative.isDeleted.eq(IsDeleted.VALID.getCode())))
                .orderBy("id asc")
                .limit(20)
                .fetch(LauSplashScreenCreativePo.class);
    }
}
