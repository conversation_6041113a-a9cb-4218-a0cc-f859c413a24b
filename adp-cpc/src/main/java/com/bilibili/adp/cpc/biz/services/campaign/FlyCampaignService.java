package com.bilibili.adp.cpc.biz.services.campaign;

import com.bilibili.adp.account.dto.AccountAllInfoDto;
import com.bilibili.adp.account.service.IQueryAccountService;
import com.bilibili.adp.common.bean.IntIdNameDto;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.enums.JumpTypeEnum;
import com.bilibili.adp.common.enums.fly.FlyIsSetInviteLinkEnum;
import com.bilibili.adp.common.enums.professional_fly.GdPlusUnitLaunchingStatusEnum;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.ObjectUtils;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.biz.bos.unit.QueryUnitBo;
import com.bilibili.adp.cpc.biz.services.campaign.api.ICpcCampaignService;
import com.bilibili.adp.cpc.biz.services.campaign.bos.ManagedCampaignDetailDto;
import com.bilibili.adp.cpc.biz.services.creative.FlyCreativeService;
import com.bilibili.adp.cpc.biz.services.dynamic.FlyDynamicService;
import com.bilibili.adp.cpc.biz.services.unit.CpcUnitServiceDelegate;
import com.bilibili.adp.cpc.biz.services.unit.FlyUnitService;
import com.bilibili.adp.cpc.biz.services.unit.dto.CpcUnitDto;
import com.bilibili.adp.cpc.biz.services.unit.dto.QueryCpcUnitDto;
import com.bilibili.adp.cpc.core.LaunchUnitV1Service;
import com.bilibili.adp.cpc.enums.ad.LaunchTargetEnum;
import com.bilibili.adp.launch.api.campaign.dto.*;
import com.bilibili.adp.cpc.biz.services.campaign.dto.QueryCpcCampaignDto;
import com.bilibili.adp.cpc.biz.services.campaign.dto.CpcCampaignDto;
import com.bilibili.adp.launch.api.common.*;
import com.bilibili.adp.launch.api.flyPro.dto.enums.BusinessLiveRoomType;
import com.bilibili.adp.launch.api.flyPro.dto.enums.DynamicTypeEnum;
import com.bilibili.adp.launch.api.flyPro.dto.enums.RoomPromoteScenesEnum;
import com.bilibili.adp.launch.api.flyPro.dto.v2.*;
import com.bilibili.adp.launch.api.unit.dto.FlyProCampaignUpdateDto;
import com.bilibili.adp.launch.biz.converter.flyPro.CampaignConverter;
import com.bilibili.adp.launch.biz.flyPro.v2.FlyGdPlusDelegate;
import com.bilibili.adp.launch.biz.ad_core.mybatis.LauCampaignDao;
import com.bilibili.adp.launch.biz.lau_dao.LauFlyArchiveCampaignNewDao;
import com.bilibili.adp.launch.biz.pojo.*;
import com.bilibili.adp.launch.biz.service.LiveInfoService;
import com.bilibili.adp.passport.api.dto.ArchiveDetail;
import com.bilibili.adp.passport.api.dto.UserInfoDto;
import com.bilibili.adp.passport.biz.manager.ArchiveManager;
import com.bilibili.adp.passport.biz.manager.LiveBroadcastHttpService;
import com.bilibili.adp.passport.biz.manager.bean.LiveBroadcastRoomInfo;
import com.bilibili.adp.passport.biz.manager.bean.LiveRoomDetailResponse;
import com.bilibili.adp.passport.biz.manager.bean.dynamic.DynamicDetailInfo;
import com.bilibili.adp.passport.biz.service.PassportService;
import com.bilibili.adp.resource.api.account.label.IAccountLabelService;
import com.bilibili.crm.platform.common.IsValid;
import com.bilibili.mas.api.soa.ISoaVideoMappingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Primary;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.MultiValueMap;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;

import java.sql.Timestamp;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

;import static com.bilibili.adp.cpc.dao.config.PlatformMySqlConfig.AD_CORE_TM;

/**
 * copy class
 * @see com.bilibili.adp.launch.biz.flyPro.v2.FlyCampaignService
 */
@Primary
@Slf4j
@RequiredArgsConstructor
@Service(value = "LaunchFlyCampaignService")
public class FlyCampaignService {

    private static final Logger LOGGER = LoggerFactory.getLogger(FlyCampaignService.class);
    @Autowired
    private ArchiveManager archiveManager;
    @Autowired
    private ICpcCampaignService cpcCampaignService;
    @Autowired
    private IQueryAccountService queryAccountService;
    @Autowired
    private LauFlyArchiveCampaignNewDao lauFlyArchiveCampaignNewDao;
    @Autowired
    private LauCampaignDao lauCampaignDao;
    @Autowired
    private IAccountLabelService accountLabelService;
    @Autowired
    private LiveBroadcastHttpService liveBroadcastHttpService;
    @Autowired
    private PassportService passportService;
    @Autowired
    private FlyDynamicService flyDynamicService;
    @Autowired
    private ManagedCampaignService managedCampaignService;
    @Autowired
    private ISoaVideoMappingService soaVideoMappingService;
    @Autowired
    private CpcUnitServiceDelegate cpcUnitServiceDelegate;
    @Autowired
    private FlyGdPlusDelegate flyGdPlusDelegate;
    @Autowired
    private FlyUnitService flyUnitService;
    @Autowired
    private FlyCreativeService flyCreativeService;
    @Autowired
    private LiveInfoService liveInfoService;

    private final LaunchUnitV1Service launchUnitV1Service;

    /**
     * 活动标签id
     */
    @Value("${label.activity:225}")
    private Integer activityLabel;

    public GdPlusUnitLaunchingStatusEnum queryGdPlusLaunchingStatusByCampaignId(Integer campaignId) {
        Assert.notNull(campaignId, "计划id不可为空");
        List<Integer> unitIds = flyUnitService.getFlyUnitIds(QueryCpcUnitDto.builder()
                .campaignId(campaignId)
                .unitStatusList(UnitStatus.NON_DELETED_UNIT_STATUS_LIST)
                .build());
        Map<Integer, List<Timestamp>> unitScheduleDayMap = flyGdPlusDelegate.queryValidUnitsSchedules(unitIds);
        Set<Timestamp> unitDaySet = unitScheduleDayMap.values().stream()
                .flatMap(list -> list.stream())
                .collect(Collectors.toSet());
        return flyGdPlusDelegate.getGdLaunchingStatusBySchedules(unitDaySet.stream()
                .collect(Collectors.toList()));
    }

    private void checkBindAvid(Integer accountId, Long avid) {
        LauFlyArchiveCampaignNewPoExample example = new LauFlyArchiveCampaignNewPoExample();
        ////校验稿件绑定
        example.or().andAccountIdEqualTo(accountId)
                .andAvIdEqualTo(avid)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<LauFlyArchiveCampaignNewPo> list = lauFlyArchiveCampaignNewDao.selectByExample(example);
        if (!CollectionUtils.isEmpty(list)) {
            LauCampaignPoExample example2 = queryToExample(QueryCpcCampaignDto.builder()
                    .campaignIds(list.stream().map(LauFlyArchiveCampaignNewPo::getCampaignId).collect(Collectors.toList()))
                    .isGdPlus(IsValid.FALSE.getCode())
                    .build());
            List<LauCampaignPo> pos = lauCampaignDao.selectByExample(example2);
            Assert.isTrue(CollectionUtils.isEmpty(pos), "账号已存在绑定该视频的普通计划，无法创建计划");
        }
    }

    private FlyReserveDto beforeCreateCheck(Integer accountId, FlyCampaignDto flyCampaignDto, Integer isGdPlus) throws ServiceException {
        FlyReserveDto flyReserveDto = new FlyReserveDto();
        LauFlyArchiveCampaignNewPoExample example = new LauFlyArchiveCampaignNewPoExample();
        LaunchTargetEnum launchTarget = LaunchTargetEnum.getByCode(flyCampaignDto.getLaunchTarget());
        Assert.isTrue(launchTarget != null, "计划投放目标不正确");
        if (launchTarget == LaunchTargetEnum.ACCOUNT_GROWTH || launchTarget == LaunchTargetEnum.TRAFFIC_BOOST) {
            //GD+，不校验稿件是否使用过
            if (IsValid.TRUE.getCode().equals(isGdPlus)) {
                return null;
            }
            //校验稿件绑定
            this.checkBindAvid(accountId, flyCampaignDto.getAvId());
        }
        if (launchTarget == LaunchTargetEnum.DYNAMIC) {
            if (DynamicTypeEnum.CHOOSE_DYNAMIC.getCode().equals(flyCampaignDto.getDynamicType())) {
                example.or().andAccountIdEqualTo(accountId)
                        .andDynamicTypeEqualTo(DynamicTypeEnum.CHOOSE_DYNAMIC.getCode())
                        .andDynamicIdEqualTo(flyCampaignDto.getDynamicId())
                        .andFlyPurposeEqualTo(flyCampaignDto.getLaunchTarget())
                        .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
                List<LauFlyArchiveCampaignNewPo> list = lauFlyArchiveCampaignNewDao.selectByExample(example);
                Assert.isTrue(CollectionUtils.isEmpty(list), "账号已存在绑定该动态的计划，无法创建计划");
            }
            if (DynamicTypeEnum.MANUAL_DYNAMIC.getCode().equals(flyCampaignDto.getDynamicType())) {
                Assert.isTrue(JumpTypeEnum.LINK.validateUrlIsValid(flyCampaignDto.getDynamicLink()), "跳转类型与链接值不匹配");
            }
        }
        if (LaunchTargetEnum.LIVE_PROMOTION == LaunchTargetEnum.getByCode(flyCampaignDto.getLaunchTarget())) {
            //是直播预约
            if (RoomPromoteScenesEnum.RESERVE.getCode().equals(flyCampaignDto.getRoomPromoteScenes())) {
                flyReserveDto = this.doCheckLiveReserve(flyCampaignDto.getReserveId());
                //校验稿件绑定
                this.checkBindAvid(accountId, flyCampaignDto.getAvId());
            }
        }
        if (LaunchTargetEnum.BUSINESS_LIVE_PROMOTION == LaunchTargetEnum.getByCode(flyCampaignDto.getLaunchTarget())) {
            //是直播预约
            if (RoomPromoteScenesEnum.RESERVE.getCode().equals(flyCampaignDto.getRoomPromoteScenes())) {
                flyReserveDto = this.doCheckLiveReserve(flyCampaignDto.getReserveId());
                //校验稿件绑定
                this.checkBindAvid(accountId, flyCampaignDto.getAvId());
            } else {
                BusinessLiveRoomType liveRoomType = BusinessLiveRoomType.getByCode(flyCampaignDto.getLiveRoomType());
                Assert.isTrue(liveRoomType == BusinessLiveRoomType.CHOOSE_LIVE_ROOM || liveRoomType == BusinessLiveRoomType.MANUAL_LIVE_ROOM,
                        "直播间助推下直播间类型不能为空或者未非直播间");
                if (BusinessLiveRoomType.MANUAL_LIVE_ROOM == BusinessLiveRoomType.getByCode(flyCampaignDto.getLiveRoomType())) {
                    Long manuallyRoomId = flyCampaignDto.getManuallyRoomId();
                    Assert.isTrue(manuallyRoomId != null && manuallyRoomId != 0, "指定直播间不能为空");
                    Map<Integer, LiveBroadcastRoomInfo> roomInfoMapMap =
                            liveBroadcastHttpService.queryLiveBroadcastRoomInfoByIds(Collections.singletonList(manuallyRoomId.intValue()));
                    Assert.isTrue(!CollectionUtils.isEmpty(roomInfoMapMap) && roomInfoMapMap.containsKey(manuallyRoomId.intValue()), "填写的直播间号对应的直播间不存在");
                }
            }
        }
        if (launchTarget == LaunchTargetEnum.ACTIVITY) {
            String activityLink = flyCampaignDto.getActivityLink();
            String dynamicId = getDynamicIdFromActivityLink(activityLink);
            if (StringUtils.isNotBlank(dynamicId)) {
                DynamicDetailInfo dynamicDetail = null;
                try {
                    dynamicDetail = flyDynamicService.getDynamicById(Long.valueOf(dynamicId));
                } catch (Exception e) {
                    log.warn("method：FlyCampaignService.beforeCreateCheck,dynamicId:{}不存在", dynamicId);
                }
                Assert.isTrue(dynamicDetail != null
                        && dynamicDetail.getData() != null
                        && dynamicDetail.getData().getCard() != null, "未查询到动态");
            }
        }
        return flyReserveDto;
    }

    private FlyReserveDto doCheckLiveReserve(Long reserveId) throws ServiceException {
        Assert.isTrue(reserveId != null && reserveId > 0, "预约id必须大于0");
        Map<Long, UpActReserveRelationInfoBo> map = liveInfoService.queryUpActReserve(Arrays.asList(reserveId));
        Assert.notEmpty(map, "没有查到预约id相关信息");
        UpActReserveRelationInfoBo bo = map.get(reserveId);
        Assert.notNull(bo, "没有查到预约id相关信息");
        Assert.isTrue(Integer.valueOf(100).equals(bo.getState()), "预约id状态无效");
        Long reserveUpMid = Long.valueOf(bo.getUpmid());
        //兼容int，long上线后尽快改
        UserInfoDto userInfoDto = passportService.getUserByMid(reserveUpMid);
        return FlyReserveDto.builder()
                .reserveId(reserveId)
                .reserveStautus(bo.getState())
                .reserveUpMid(reserveUpMid)
                .reserveUpNickname(userInfoDto.getName())
                .build();
    }

    private String getDynamicIdFromActivityLink(String activityLink) {
        Assert.isTrue(StringUtils.isNotBlank(activityLink), "活动链接不能为空");
        UriComponents uriComponents = UriComponentsBuilder.fromUriString(activityLink).build();
        MultiValueMap<String, String> params = uriComponents.getQueryParams();
        List<String> param = params.get("dynamic_id");
        return CollectionUtils.isEmpty(param) ? "" : param.get(0);
    }

    public void batchUpdateBudget(List<BatchUpdateNoDpaCampaignDto> dtoList, Operator operator) throws ServiceException {
        boolean isAllCampaignDailyBudget = dtoList.stream()
                .allMatch(campaignUpdateDto -> {
                    if (!CollectionUtils.isEmpty(campaignUpdateDto.getCampaignIds())) {
                        return campaignUpdateDto.getCampaignIds().stream()
                                .allMatch(campaignId -> {
                                    CpcCampaignDto campaign = cpcCampaignService.loadCpcCampaignDto(campaignId);
                                    return campaign.getBudget() == BudgetType.DAILY.getCode();
                                });
                    }
                    return true;
                });
        Assert.isTrue(isAllCampaignDailyBudget, "非日预算不可批量修改预算");
        cpcCampaignService.batchUpdateNoDpaCampaign(dtoList, operator,new HashMap<>());
    }

    private void insertLauFlyArchiveCampaignNewTable(Integer accountId,
                                                     Integer campaignId,
                                                     FlyCampaignDto flyCampaignDto,
                                                     FlyReserveDto flyReserveDto) throws ServiceException {
        LaunchTargetEnum launchTarget = LaunchTargetEnum.getByCode(flyCampaignDto.getLaunchTarget());
        //粉丝成长,宣发推广(绑定稿件)
        if (LaunchTargetEnum.ACCOUNT_GROWTH.getCode().equals(flyCampaignDto.getLaunchTarget()) ||
                LaunchTargetEnum.TRAFFIC_BOOST.getCode().equals(flyCampaignDto.getLaunchTarget())) {
            LauFlyArchiveCampaignNewPo po = LauFlyArchiveCampaignNewPo.builder()
                    .accountId(accountId)
                    .mid(flyCampaignDto.getMid())
                    .avId(flyCampaignDto.getAvId())
                    .campaignId(campaignId)
                    .flyPurpose(flyCampaignDto.getLaunchTarget())
                    .isDeleted(IsDeleted.VALID.getCode())
                    .videoType(flyCampaignDto.getVideoType())
                    .proxyMid(flyCampaignDto.getProxyMid())
                    .build();
            lauFlyArchiveCampaignNewDao.insertSelective(po);
            //直播间助推（绑定直播间）
        } else
            //直播间助推（绑定直播间）
            if (LaunchTargetEnum.LIVE_PROMOTION.getCode().equals(flyCampaignDto.getLaunchTarget())
                    || LaunchTargetEnum.BUSINESS_LIVE_PROMOTION.getCode().equals(flyCampaignDto.getLaunchTarget())) {
                BusinessLiveRoomType roomType = BusinessLiveRoomType.getByCode(flyCampaignDto.getLiveRoomType());
                LauFlyArchiveCampaignNewPo po = LauFlyArchiveCampaignNewPo.builder()
                        .accountId(accountId)
                        .mid(flyCampaignDto.getMid())
                        //存一个无意义的唯一值
                        .avId(0L)
                        .campaignId(campaignId)
                        .flyPurpose(flyCampaignDto.getLaunchTarget())
                        .isDeleted(IsDeleted.VALID.getCode())
                        .videoType(0)
                        .ctime(Utils.getNow())
                        .mtime(Utils.getNow())
                        .liveRoomId(roomType == BusinessLiveRoomType.CHOOSE_LIVE_ROOM ? flyCampaignDto.getLiveRoomId() : null)
                        .proxyMid(flyCampaignDto.getProxyMid())
                        .manuallyRoomId(roomType == BusinessLiveRoomType.MANUAL_LIVE_ROOM ?
                                flyCampaignDto.getManuallyRoomId() : 0)
                        .liveRoomType(flyCampaignDto.getLiveRoomType())
                        .roomPromoteScenes(flyCampaignDto.getRoomPromoteScenes())
                        .reserveId(0L)
                        .reserveStautus(0)
                        .reserveUpMid(0L)
                        .reserveUpNickname("")
                        .build();
                //直播预约,填充一些预约相关额外信息
                if (RoomPromoteScenesEnum.RESERVE.getCode().equals(flyCampaignDto.getRoomPromoteScenes())) {
                    BeanUtils.copyProperties(flyReserveDto, po);
                    po.setAvId(flyCampaignDto.getAvId());
                    po.setVideoType(flyCampaignDto.getVideoType());
                }
                lauFlyArchiveCampaignNewDao.insertSelective(po);
                //动态
            } else if (launchTarget == LaunchTargetEnum.DYNAMIC) {
                LauFlyArchiveCampaignNewPo po = LauFlyArchiveCampaignNewPo.builder()
                        .accountId(accountId)
                        .mid(flyCampaignDto.getMid())
                        //存一个无意义的唯一值
                        .avId(-1L * campaignId)
                        .campaignId(campaignId)
                        .flyPurpose(flyCampaignDto.getLaunchTarget())
                        .isDeleted(IsDeleted.VALID.getCode())
                        .videoType(0)
                        .ctime(Utils.getNow())
                        .mtime(Utils.getNow())
                        .dynamicType(flyCampaignDto.getDynamicType())
                        .dynamicId(DynamicTypeEnum.CHOOSE_DYNAMIC.getCode().equals(flyCampaignDto.getDynamicType()) ?
                                flyCampaignDto.getDynamicId() : 0L)
                        .dynamicLink(DynamicTypeEnum.MANUAL_DYNAMIC.getCode().equals(flyCampaignDto.getDynamicType()) ?
                                flyCampaignDto.getDynamicLink() : "")
                        .build();
                lauFlyArchiveCampaignNewDao.insertSelective(po);
            } else if (launchTarget == LaunchTargetEnum.ACTIVITY) {
                LauFlyArchiveCampaignNewPo po = LauFlyArchiveCampaignNewPo.builder()
                        .accountId(accountId)
                        .mid(flyCampaignDto.getMid())
                        //存一个无意义的唯一值
                        .avId(-1L * campaignId)
                        .campaignId(campaignId)
                        .flyPurpose(flyCampaignDto.getLaunchTarget())
                        .isDeleted(IsDeleted.VALID.getCode())
                        .videoType(0)
                        .ctime(Utils.getNow())
                        .mtime(Utils.getNow())
                        .activityLink(flyCampaignDto.getActivityLink())
                        .build();
                String dynamicId = getDynamicIdFromActivityLink(flyCampaignDto.getActivityLink());
                if (StringUtils.isNotBlank(dynamicId)) {
                    po.setDynamicId(Long.valueOf(dynamicId));
                }
                lauFlyArchiveCampaignNewDao.insertSelective(po);
            }
    }


    @Transactional(value = AD_CORE_TM, rollbackFor = Exception.class)
    public int createFlyCampaign(NewCpcCampaignDto dto, Operator operator, FlyCampaignDto flyCampaignDto) throws ServiceException {
        FlyReserveDto flyReserveDto = beforeCreateCheck(dto.getAccountId(), flyCampaignDto, dto.getIsGdPlus());
        int campaignId = cpcCampaignService.createCpcCampaign(dto, operator);
        insertLauFlyArchiveCampaignNewTable(dto.getAccountId(), campaignId, flyCampaignDto, flyReserveDto);
        return campaignId;
    }

    public FlyCampaignDetailDto getFlyCampaignById(Integer id) throws ServiceException {
        CpcCampaignDto campaign = cpcCampaignService.loadCpcCampaignDto(id);
        //托管
        if (campaign.getIsManaged() == 1) {
            ManagedCampaignDetailDto detail = managedCampaignService.getManagedCampaignById(campaign.getCampaignId(), false);
            return FlyCampaignDetailDto.builder()
                    .accountId(campaign.getAccountId())
                    .campaignId(detail.getCampaignId())
                    .campaignName(detail.getCampaignName())
                    .campaignStatus(campaign.getCampaignStatus())
                    .budget(Utils.fromFenToYuan(detail.getBudget()))
                    .speedMode(SpeedMode.SMOOTH.getCode())
                    .speedModeDesc(SpeedMode.SMOOTH.getName())
                    .launchTarget(LaunchTargetEnum.ACCOUNT_GROWTH.getCode())
                    .launchTargetDesc(LaunchTargetEnum.ACCOUNT_GROWTH.getName())
                    .managed(true)
                    .managedVersion(detail.getManagedVersion())
                    .gdPlus(false)
                    .isMiddleAd(campaign.getIsMiddleAd())
                    .isNewFly(campaign.getIsNewFly())
                    .build();
        }

        LauFlyArchiveCampaignNewPoExample example = new LauFlyArchiveCampaignNewPoExample();
        example.or().andCampaignIdEqualTo(id)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<LauFlyArchiveCampaignNewPo> list = lauFlyArchiveCampaignNewDao.selectByExample(example);
        if (CollectionUtils.isEmpty(list)) {
            return FlyCampaignDetailDto.builder().build();
        }
        LauFlyArchiveCampaignNewPo po = list.get(0);
        String avId = "";
        String title = "";
        String roomId = "";
        String roomTitle = "";
        String dynamicId = "";
        String dynamicContent = "";
        String dynamicLink = "";
        String manuallyRoomId = "";
        Integer hasInviteLink = FlyIsSetInviteLinkEnum.FALSE.getCode();
        //稿件
        if (LaunchTargetEnum.ACCOUNT_GROWTH.getCode().equals(po.getFlyPurpose()) ||
                LaunchTargetEnum.TRAFFIC_BOOST.getCode().equals(po.getFlyPurpose())) {
            ArchiveDetail archiveDetail = archiveManager.queryArchivesByAid(po.getAvId());
            avId = String.valueOf(po.getAvId());
            title = Optional.ofNullable(archiveDetail).map(o -> o.getArchive()).map(o -> o.getTitle()).orElse("");
            hasInviteLink = Boolean.TRUE.equals(soaVideoMappingService.videoHasLaunchingInviteTask(po.getAvId())) ?
                    FlyIsSetInviteLinkEnum.TRUE.getCode() : FlyIsSetInviteLinkEnum.FALSE.getCode();
        } else
            //直播间
            if (LaunchTargetEnum.LIVE_PROMOTION.getCode().equals(po.getFlyPurpose())) {
                if (RoomPromoteScenesEnum.RESERVE.getCode().equals(po.getRoomPromoteScenes())) {
                    //预约的直播间信息用ReserveUpMid查
                    LiveRoomDetailResponse liveRoomDetailResponse = liveBroadcastHttpService.queryLiveRoomByUid(po.getReserveUpMid());
                    roomId = String.valueOf(po.getLiveRoomId());
                    roomTitle = Optional.ofNullable(liveRoomDetailResponse).map(LiveRoomDetailResponse::getTitle).orElse("");
                    //预约要展示稿件aid，标题
                    avId = this.getReserveArchiveAid(po);
                    title = this.getReserveArchiveTitle(po);
                } else {
                    Long mid = po.getProxyMid() != null && po.getProxyMid() > 0 ? po.getProxyMid() : po.getMid();
                    LiveRoomDetailResponse liveRoomDetailResponse = liveBroadcastHttpService.queryLiveRoomByUid(mid.longValue());
                    roomId = String.valueOf(po.getLiveRoomId());
                    roomTitle = Optional.ofNullable(liveRoomDetailResponse).map(LiveRoomDetailResponse::getTitle).orElse("");
                }
            } else if (LaunchTargetEnum.BUSINESS_LIVE_PROMOTION.getCode().equals(po.getFlyPurpose())) {
                if (RoomPromoteScenesEnum.RESERVE.getCode().equals(po.getRoomPromoteScenes())) {
                    //预约的直播间信息用ReserveUpMid查
                    LiveRoomDetailResponse liveRoomDetailResponse = liveBroadcastHttpService.queryLiveRoomByUid(po.getReserveUpMid());
                    roomId = String.valueOf(po.getLiveRoomId());
                    roomTitle = Optional.ofNullable(liveRoomDetailResponse).map(LiveRoomDetailResponse::getTitle).orElse("");
                    //预约要展示稿件aid，标题
                    avId = this.getReserveArchiveAid(po);
                    title = this.getReserveArchiveTitle(po);
                } else {
                    Integer queryRoomId;
                    if (po.getLiveRoomType().equals(BusinessLiveRoomType.MANUAL_LIVE_ROOM.getCode())) {
                        manuallyRoomId = String.valueOf(po.getManuallyRoomId());
                        queryRoomId = Math.toIntExact(po.getManuallyRoomId());
                    } else if (po.getLiveRoomType().equals(BusinessLiveRoomType.CHOOSE_LIVE_ROOM.getCode())) {
                        roomId = String.valueOf(po.getLiveRoomId());
                        queryRoomId = Math.toIntExact(po.getLiveRoomId());
                    } else {
                        queryRoomId = null;
                    }
                    if (queryRoomId != null) {
                        Map<Integer, LiveBroadcastRoomInfo> res =
                                liveBroadcastHttpService.queryLiveBroadcastRoomInfoByIds(Collections.singletonList(queryRoomId));
                        roomTitle = Optional.ofNullable(res).map(info -> info.get(queryRoomId).getTitle()).orElse("");
                    }
                }
            } else
                //动态
                if (LaunchTargetEnum.DYNAMIC.getCode().equals(po.getFlyPurpose())) {
                    if (DynamicTypeEnum.CHOOSE_DYNAMIC.getCode().equals(po.getDynamicType())) {
                        dynamicId = String.valueOf(po.getDynamicId());
                        try {
                            dynamicContent = flyDynamicService.getDynamicContentById(po.getDynamicId());
                        } catch (Exception e) {
                            LOGGER.info("method：FlyCampaignService.getFlyCampaignById,dynamicId:{}不存在", po.getDynamicId());
                        }
                    }
                    if (DynamicTypeEnum.MANUAL_DYNAMIC.getCode().equals(po.getDynamicType())) {
                        dynamicLink = po.getDynamicLink();
                    }
                } else if (LaunchTargetEnum.ACTIVITY.getCode().equals(po.getFlyPurpose())) {
                    dynamicId = String.valueOf(po.getDynamicId());
                }
        return FlyCampaignDetailDto.builder()
                .campaignId(id)
                .campaignName(campaign.getCampaignName())
                .promotionPurposeType(campaign.getPromotionPurposeType())
                .launchTarget(po.getFlyPurpose())
                .launchTargetDesc(LaunchTargetEnum.getByCode(po.getFlyPurpose()).getName())
                .avId(avId)
                .title(title)
                .roomId(roomId)
                .roomTitle(roomTitle)
                .dynamicType(po.getDynamicType())
                .dynamicId(dynamicId)
                .dynamicContent(dynamicContent)
                .dynamicLink(dynamicLink)
                .budget(Utils.fromFenToYuan(campaign.getBudget()))
                .budgetType(CampaignConverter.transferBudgetType.apply(campaign.getBudgetLimitType(), campaign.getBudgetType()))
                .speedMode(campaign.getSpeedMode())
                .speedModeDesc(FlySpeedMode.getByCode(campaign.getSpeedMode()).getName())
                .accountId(po.getAccountId())
                .adpVersion(campaign.getAdpVersion())
                .videoType(po.getVideoType())
                .campaignStatus(campaign.getCampaignStatus())
                .proxyMid(po.getProxyMid())
                .proxyNickname(Optional.ofNullable(passportService.getUserByMid(po.getProxyMid())).map(UserInfoDto::getName).orElse(""))
                .liveRoomType(po.getLiveRoomType())
                .manuallyRoomId(manuallyRoomId)
                .hasInviteLink(hasInviteLink)
                .activityLink(po.getActivityLink())
                .managed(false)
                .gdPlus(IsValid.TRUE.getCode().equals(campaign.getIsGdPlus()))
                .crmOrderId(campaign.getCrmOrderId())
                .roomPromoteScenes(po.getRoomPromoteScenes())
                .reserveId(po.getReserveId())
                .isMiddleAd(campaign.getIsMiddleAd())
                .isNewFly(campaign.getIsNewFly())
                .build();
    }

    private String getReserveArchiveAid(LauFlyArchiveCampaignNewPo po) throws ServiceException {
        return String.valueOf(po.getAvId());
    }

    private String getReserveArchiveTitle(LauFlyArchiveCampaignNewPo po) throws ServiceException {
        ArchiveDetail archiveDetail = archiveManager.queryArchivesByAid(po.getAvId());
        return Optional.ofNullable(archiveDetail).map(o -> o.getArchive()).map(o -> o.getTitle()).orElse("");
    }

    public List<AccountLaunchTargetDto> getFlyAccountLaunchTarget(Integer accountId) throws ServiceException {
        List<AccountLaunchTargetDto> ans = new ArrayList<>();
        AccountAllInfoDto accountAllInfoDto = queryAccountService.getAccountAllInfoFromCache(accountId);
        //支持内容起飞
        if (Integer.valueOf(1).equals(accountAllInfoDto.getAccountDto().getIsSupportContent())) {
            ans.add(AccountLaunchTargetDto.builder()
                    .id(LaunchTargetEnum.ACCOUNT_GROWTH.getCode())
                    .desc(LaunchTargetEnum.ACCOUNT_GROWTH.getName())
                    .build());
            ans.add(AccountLaunchTargetDto.builder()
                    .id(LaunchTargetEnum.LIVE_PROMOTION.getCode())
                    .desc(LaunchTargetEnum.LIVE_PROMOTION.getName())
                    .build());
        } else if (Integer.valueOf(1).equals(accountAllInfoDto.getAccountDto().getIsSupportFly())) {
            //支持商业起飞
            ans.add(AccountLaunchTargetDto.builder()
                    .id(LaunchTargetEnum.TRAFFIC_BOOST.getCode())
                    .desc(LaunchTargetEnum.TRAFFIC_BOOST.getName())
                    .build());
            ans.add(AccountLaunchTargetDto.builder()
                    .id(LaunchTargetEnum.BUSINESS_LIVE_PROMOTION.getCode())
                    .desc(LaunchTargetEnum.BUSINESS_LIVE_PROMOTION.getName())
                    .build());
        }

        ans.add(AccountLaunchTargetDto.builder()
                .id(LaunchTargetEnum.DYNAMIC.getCode())
                .desc(LaunchTargetEnum.DYNAMIC.getName())
                .build());
        //在"起飞活动"标签下
        if (accountLabelService.isAccountIdsInLabels(Collections.singletonList(accountId),
                Collections.singletonList(activityLabel))) {
            ans.add(AccountLaunchTargetDto.builder()
                    .id(LaunchTargetEnum.ACTIVITY.getCode())
                    .desc(LaunchTargetEnum.ACTIVITY.getName())
                    .build());
        }
        return ans;
    }


    public PageResult<CpcCampaignDto> getFlyCampaignByPage(QueryCpcCampaignDto query) throws ServiceException {
        LOGGER.info("queryFlyCampaignByPage query:{}", query);
        LauCampaignPoExample example = queryToExample(query);
        int total = (int) lauCampaignDao.countByExample(example);
        if (total < 1) {
            return PageResult.EMPTY_PAGE_RESULT;
        }
        List<LauCampaignPo> pos = lauCampaignDao.selectByExample(example);
        return PageResult.<CpcCampaignDto>builder().total(total).records(flyCampaignPos2Dtos(pos)).build();
    }

    public List<IntIdNameDto> getCampaignDropBox(Integer accountId, Integer flyPurpose, boolean filterManaged) throws ServiceException {
        QueryCpcCampaignDto query = QueryCpcCampaignDto.builder()
                .accountId(accountId)
                .orderBy("campaign_id desc")
                .campaignStatusList(CampaignStatus.NON_DELETED_CAMPAIGN_STATUS_LIST)
                .build();
        if (filterManaged) {
            query.setIsManaged(0);
        }
        // 复制单元的时候拉计划列表要传flyPurpose，要过滤掉GD+单元
        if (flyPurpose != null) {
            query.setIsGdPlus(0);
        }
        LauCampaignPoExample example = queryToExample(query);
        List<LauCampaignPo> pos = lauCampaignDao.selectByExample(example);
        List<Integer> campaignIds = pos.stream().map(o -> o.getCampaignId()).collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(campaignIds) && flyPurpose != null) {
            LauFlyArchiveCampaignNewPoExample lauFlyArchiveCampaignNewPoExample = new LauFlyArchiveCampaignNewPoExample();
            LauFlyArchiveCampaignNewPoExample.Criteria criteria = lauFlyArchiveCampaignNewPoExample.createCriteria();
            criteria.andFlyPurposeEqualTo(flyPurpose)
                    .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                    .andCampaignIdIn(campaignIds);
            List<LauFlyArchiveCampaignNewPo> list = lauFlyArchiveCampaignNewDao.selectByExample(lauFlyArchiveCampaignNewPoExample);
            List<Integer> validateCampaignIds = list.stream().map(o -> o.getCampaignId()).collect(Collectors.toList());
            List<LauCampaignPo> filterPos = pos.stream().filter(o -> validateCampaignIds.contains(o.getCampaignId())).collect(Collectors.toList());
            return flyCampaignPos2IntIdNameDtos(filterPos);
        }
        return flyCampaignPos2IntIdNameDtos(pos);
    }

    public List<Integer> getFlyCampaignIds(QueryCpcCampaignDto query) throws ServiceException {
        LauCampaignPoExample example = queryToExample(query);
        List<LauCampaignPo> pos = lauCampaignDao.selectByExample(example);
        return pos.stream().map(LauCampaignPo::getCampaignId).collect(Collectors.toList());
    }

    public Map<Long, Integer> getAccountIdBindAvIdsAndCampaignsWithOutFlyPurpose(Integer accountId)
            throws ServiceException {
        LauFlyArchiveCampaignNewPoExample example = new LauFlyArchiveCampaignNewPoExample();
        example.or().andAccountIdEqualTo(accountId)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andAvIdGreaterThan(0L);
        List<LauFlyArchiveCampaignNewPo> list = lauFlyArchiveCampaignNewDao.selectByExample(example);
        Map<Long, List<LauFlyArchiveCampaignNewPo>> avIdMap = list.stream().collect(Collectors.groupingBy(LauFlyArchiveCampaignNewPo::getAvId));
        if (CollectionUtils.isEmpty(avIdMap)) {
            return new HashMap<>();
        }
        LauCampaignPoExample example2 = queryToExample(QueryCpcCampaignDto.builder()
                .campaignIds(list.stream().map(LauFlyArchiveCampaignNewPo::getCampaignId).collect(Collectors.toList()))
                .build());
        List<LauCampaignPo> pos = lauCampaignDao.selectByExample(example2);
        Map<Integer, Integer> campaignIdMap = pos.stream().collect(Collectors.toMap(LauCampaignPo::getCampaignId, LauCampaignPo::getIsGdPlus));

        Map<Long, Integer> ans = new HashMap<>();

        avIdMap.forEach((k, v) -> {
            Integer bindNormalCampaignId = 0;
            for (LauFlyArchiveCampaignNewPo po : v) {
                Integer isGdPlus = campaignIdMap.getOrDefault(po.getCampaignId(), 0);
                if (isGdPlus == 0) {
                    //一个账号非GD的稿件只会绑定一次
                    bindNormalCampaignId = po.getCampaignId();
                }
            }
            ans.put(k, bindNormalCampaignId);
        });
        return ans;
    }

    public Map<Long, Integer> getAccountIdBindAvIdsAndCampaigns(Integer accountId, Integer flyPurpose) throws ServiceException {
        LauFlyArchiveCampaignNewPoExample example = new LauFlyArchiveCampaignNewPoExample();
        example.or().andAccountIdEqualTo(accountId)
                .andFlyPurposeEqualTo(flyPurpose)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<LauFlyArchiveCampaignNewPo> list = lauFlyArchiveCampaignNewDao.selectByExample(example);
        Map<Long, List<LauFlyArchiveCampaignNewPo>> avIdMap = list.stream().collect(Collectors.groupingBy(LauFlyArchiveCampaignNewPo::getAvId));
        if (CollectionUtils.isEmpty(avIdMap)) {
            return new HashMap<>();
        }
        LauCampaignPoExample example2 = queryToExample(QueryCpcCampaignDto.builder()
                .campaignIds(list.stream().map(LauFlyArchiveCampaignNewPo::getCampaignId).collect(Collectors.toList()))
                .build());
        List<LauCampaignPo> pos = lauCampaignDao.selectByExample(example2);
        Map<Integer, Integer> campaignIdMap = pos.stream().collect(Collectors.toMap(LauCampaignPo::getCampaignId, LauCampaignPo::getIsGdPlus));

        Map<Long, Integer> ans = new HashMap<>();

        avIdMap.forEach((k, v) -> {
            Integer bindNormalCampaignId = 0;
            for (LauFlyArchiveCampaignNewPo po : v) {
                Integer isGdPlus = campaignIdMap.get(po.getCampaignId());
                if (isGdPlus == 0) {
                    //一个账号非GD的稿件只会绑定一次
                    bindNormalCampaignId = po.getCampaignId();
                }
            }
            ans.put(k, bindNormalCampaignId);
        });
        return ans;
    }

    public Map<Long, Integer> isAvIdsBindGdPlus(List<Long> avIds) {
        Map<Long, Integer> ans = new HashMap<>();
        avIds.forEach(o -> {
            ans.put(o, 0);
        });

        LauFlyArchiveCampaignNewPoExample example = new LauFlyArchiveCampaignNewPoExample();
        example.or().andAvIdIn(avIds)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<LauFlyArchiveCampaignNewPo> list = lauFlyArchiveCampaignNewDao.selectByExample(example);
        Map<Long, List<LauFlyArchiveCampaignNewPo>> avIdMap = list.stream().collect(Collectors.groupingBy(LauFlyArchiveCampaignNewPo::getAvId));
        if (CollectionUtils.isEmpty(avIdMap)) {
            return ans;
        }

        LauCampaignPoExample example2 = queryToExample(QueryCpcCampaignDto.builder()
                .campaignIds(list.stream().map(LauFlyArchiveCampaignNewPo::getCampaignId).collect(Collectors.toList()))
                .build());
        List<LauCampaignPo> pos = lauCampaignDao.selectByExample(example2);
        Map<Integer, Integer> campaignIdGdPlusMap = pos.stream().collect(Collectors.toMap(LauCampaignPo::getCampaignId, LauCampaignPo::getIsGdPlus));

        avIds.forEach(o -> {
            List<LauFlyArchiveCampaignNewPo> bindCampaigns = avIdMap.get(o);
            //这个avId存在绑定的计划
            if (!CollectionUtils.isEmpty(bindCampaigns)) {
                List<Integer> bindCampaignIds = bindCampaigns.stream().map(LauFlyArchiveCampaignNewPo::getCampaignId).collect(Collectors.toList());
                for (Integer bindCampaignId : bindCampaignIds) {
                    //存在1个绑定的GD+计划
                    if (Integer.valueOf(1).equals(campaignIdGdPlusMap.get(bindCampaignId))) {
                        ans.put(o, 1);
                        break;
                    }
                }
            }
        });

        return ans;
    }

    public Integer judgeAccountIdHasBindAvId(Integer accountId, Long avId) throws ServiceException {
        List<LauFlyArchiveCampaignNewPo> list = this.queryDosByAccountIdAndAvId(accountId, avId);
        return CollectionUtils.isEmpty(list) ? IsValid.FALSE.getCode() : IsValid.TRUE.getCode();
    }

    private List<LauFlyArchiveCampaignNewPo> queryDosByAccountIdAndAvId(Integer accountId, Long avId) throws ServiceException {
        LauFlyArchiveCampaignNewPoExample example = new LauFlyArchiveCampaignNewPoExample();
        example.or().andAccountIdEqualTo(accountId)
                .andAvIdEqualTo(avId)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        return lauFlyArchiveCampaignNewDao.selectByExample(example);
    }

    public Map<Long, LauFlyArchiveCampaignNewPo> getAccountIdBindDynamicIdsAndCampaigns(Integer accountId) throws ServiceException {
        LauFlyArchiveCampaignNewPoExample example = new LauFlyArchiveCampaignNewPoExample();
        example.or().andAccountIdEqualTo(accountId)
                .andFlyPurposeEqualTo(LaunchTargetEnum.DYNAMIC.getCode())
                .andDynamicTypeEqualTo(DynamicTypeEnum.CHOOSE_DYNAMIC.getCode())
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<LauFlyArchiveCampaignNewPo> list = lauFlyArchiveCampaignNewDao.selectByExample(example);
        return list.stream().collect(Collectors.toMap(LauFlyArchiveCampaignNewPo::getDynamicId, a -> a, (k1, k2) -> k1));
    }

    private LauCampaignPoExample queryToExample(QueryCpcCampaignDto query) {
        LauCampaignPoExample example = new LauCampaignPoExample();
        LauCampaignPoExample.Criteria c = buildCampaignCriteria(query, example);
        //单独查询托管相关字段
        buildManagedCriteria(query, example, c);
        return example;
    }

    private void buildManagedCriteria(QueryCpcCampaignDto query, LauCampaignPoExample example, LauCampaignPoExample.Criteria c) {

        int managedCampaignNotStarted = 6;
        int managedCampaignAlreadyDone = 7;
        String today = LocalDate.now().toString();
        if (query.getIsManaged() != null) {
            //  只有isManaged==1时，managedStatus才有用
            if (query.getIsManaged() == 1) {
                c.andIsManagedEqualTo(1);
                if (!CollectionUtils.isEmpty(query.getManagedStatus())) {
                    if (query.getManagedStatus().size() == 2
                            && query.getManagedStatus().containsAll(Arrays.asList(managedCampaignNotStarted, managedCampaignAlreadyDone))) {
                        c.andManagedBeginTimeGreaterThan(today);
                        buildCampaignCriteria(query, example)
                                .andIsManagedEqualTo(1)
                                .andManagedEndTimeLessThan(today)
                                .andManagedEndTimeNotEqualTo("");
                    } else {
                        for (Integer managedStatus : query.getManagedStatus()) {
                            if (managedStatus == managedCampaignNotStarted) {
                                c.andManagedBeginTimeGreaterThan(today);
                            }
                            if (managedStatus == managedCampaignAlreadyDone) {
                                c.andManagedEndTimeLessThan(today)
                                        .andManagedEndTimeNotEqualTo("");
                            }
                        }
                    }
                }
            }
            if (query.getIsManaged() == 0) {
                c.andIsManagedEqualTo(0);
            }
        }
    }


    private LauCampaignPoExample.Criteria buildCampaignCriteria(QueryCpcCampaignDto query, LauCampaignPoExample example) {
        LauCampaignPoExample.Criteria c = example.or()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andIsNewFlyEqualTo(IsValid.TRUE.getCode());

        ObjectUtils.setObject(query::getAccountId, c::andAccountIdEqualTo);
        ObjectUtils.setList(query::getCampaignIds, c::andCampaignIdIn);
        ObjectUtils.setString(query::getLikeCampaignName, c::andCampaignNameLike);
        ObjectUtils.setList(query::getCampaignStatusList, c::andCampaignStatusIn);

        ObjectUtils.setString(query::getOrderBy, example::setOrderByClause);
        ObjectUtils.setPage(query::getPage, example::setLimit, example::setOffset);
        ObjectUtils.setObject(query::getIsGdPlus, c::andIsGdPlusEqualTo);
        ObjectUtils.setObject(query::getCampaignAdType, c::andAdTypeEqualTo);
        return c;
    }

    private List<CpcCampaignDto> flyCampaignPos2Dtos(List<LauCampaignPo> pos) {
        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyList();
        }

        return pos.stream().map(po -> flyCampaignPo2Dto(po)).collect(Collectors.toList());
    }

    private CpcCampaignDto flyCampaignPo2Dto(LauCampaignPo po) {
        CpcCampaignDto dto = new CpcCampaignDto();
        BeanUtils.copyProperties(po, dto);
        return dto;
    }

    private List<IntIdNameDto> flyCampaignPos2IntIdNameDtos(List<LauCampaignPo> pos) {
        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyList();
        }

        return pos.stream().map(po -> flyCampaignPo2IntIdNameDto(po)).collect(Collectors.toList());
    }

    private IntIdNameDto flyCampaignPo2IntIdNameDto(LauCampaignPo po) {
        IntIdNameDto dto = new IntIdNameDto();
        dto.setId(po.getCampaignId());
        dto.setName(po.getCampaignName());
        dto.setIsGdPlus(po.getIsGdPlus());
        return dto;

    }

    @Transactional(value = AD_CORE_TM, rollbackFor = Exception.class)
    public Pair<Integer, Integer> updateFlyCampaign(FlyProCampaignUpdateDto dto, Operator operator) throws ServiceException {
        FlyCampaignDetailDto flyCampaignDetailDto = this.getFlyCampaignById(dto.getCampaignId());
        UpdateCpcCampaignDto updateCpcCampaignDto = null;
        //起飞gd+计划
        if (flyCampaignDetailDto.isGdPlus()) {
            //更新名称
            updateCpcCampaignDto = UpdateCpcCampaignDto
                    .builder()
                    .campaignId(dto.getCampaignId())
                    .campaignName(dto.getCampaignName())
                    .build();
            Pair<Boolean, List<CpcUnitDto>> pair = this.campaignScheduleInFutureOrHasNoSchedule(dto.getCampaignId());
            //计划有在今天或过去的排期，不允许编辑你稿件
            if (!pair.getFirst()) {
                Assert.isTrue(ObjectUtils.nullSafeEquals(flyCampaignDetailDto.getAvId(), dto.getAvId()), "投放中的保量计划，无法编辑稿件");
            } else {
                //计划排期都在未来或没有排期，还支持编辑稿件

                //确实改变了avId
                if (!ObjectUtils.nullSafeEquals(flyCampaignDetailDto.getAvId(), dto.getAvId())) {
                    LauFlyArchiveCampaignNewPoExample example = new LauFlyArchiveCampaignNewPoExample();
                    example.or().andAccountIdEqualTo(operator.getOperatorId())
                            .andCampaignIdEqualTo(dto.getCampaignId())
                            .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
                    List<LauFlyArchiveCampaignNewPo> pos = lauFlyArchiveCampaignNewDao.selectByExample(example);
                    Assert.isTrue(!CollectionUtils.isEmpty(pos), "计划绑定的稿件为空");
                    LauFlyArchiveCampaignNewPo po = pos.get(0);
                    po.setAvId(dto.getAvId());
                    po.setVideoType(dto.getVideoType());
                    po.setMtime(null);
                    //更新计划稿件表
                    lauFlyArchiveCampaignNewDao.updateByPrimaryKeySelective(po);
                    //有单元，处理单元创意
                    if (!CollectionUtils.isEmpty(pair.getSecond())) {
                        Boolean anyUnitSelectIncreaseFans = pair.getSecond().stream().anyMatch(o -> Integer.valueOf(0).equals(o.getGdPlusOptimizeModel())
                                && Integer.valueOf(1).equals(o.getGdPlusOptimizeTarget()));
                        //如果有单元选了系统优化+涨粉成本
                        if (anyUnitSelectIncreaseFans) {
                            //校验是内容起飞或者商业起飞蓝V稿件
                            Assert.isTrue(flyUnitService.supportIncreaseFans(flyCampaignDetailDto.getAccountId(),
                                    Long.valueOf(dto.getAvId())), "不能更换高级设置为系统优化+涨粉成本");
                        }
                        //更新单元稿件表
                        cpcUnitServiceDelegate.saveArchiveContentByUnitIds(
                                pair.getSecond().stream().map(o -> o.getUnitId()).collect(Collectors.toList()),
                                dto.getAvId(), dto.getVideoType());
                        //更新后的计划额外表
                        FlyCampaignDetailDto newFlyCampaignDetailDto = this.getFlyCampaignById(dto.getCampaignId());
                        flyCreativeService.deleteAndAddCreatives(newFlyCampaignDetailDto, pair.getSecond(), operator);
                    }
                }
            }
        } else {
            //正常计划
            updateCpcCampaignDto = UpdateCpcCampaignDto
                    .builder()
                    .campaignId(dto.getCampaignId())
                    .campaignName(dto.getCampaignName())
                    .budget(Utils.fromYuanToFen(dto.getBudget()))
                    //起飞不修改预算类型 用原有预算类型填充
                    .budgetType(flyCampaignDetailDto.getBudgetType())
                    .speedMode(dto.getSpeedMode())
                    .build();
            //预约的直播间计划
            if (RoomPromoteScenesEnum.RESERVE.getCode().equals(flyCampaignDetailDto.getRoomPromoteScenes())) {
                // 新的预约id+
                if (!dto.getReserveId().equals(flyCampaignDetailDto.getReserveId())) {
                    // 不可预约，报错
                    FlyReserveDto flyReserveDto = doCheckLiveReserve(dto.getReserveId());
                    // 可预约，更新计划
                    LauFlyArchiveCampaignNewPo newPo = new LauFlyArchiveCampaignNewPo();
                    BeanUtils.copyProperties(flyReserveDto, newPo);
                    LauFlyArchiveCampaignNewPoExample example = new LauFlyArchiveCampaignNewPoExample();
                    example.or().andCampaignIdEqualTo(flyCampaignDetailDto.getCampaignId());
                    lauFlyArchiveCampaignNewDao.updateByExampleSelective(newPo, example);
                    // 推审创意
                    flyCreativeService.reAuditCampaignCreatives(flyCampaignDetailDto.getCampaignId());
                }
            }
        }
        //修改计划主表
        cpcCampaignService.updateCpcCampaign(updateCpcCampaignDto, operator);
        return Pair.of(updateCpcCampaignDto.getCampaignId(), flyCampaignDetailDto.isGdPlus() ? IsValid.TRUE.getCode() : IsValid.FALSE.getCode());
    }

    public Pair<Boolean, List<CpcUnitDto>> campaignScheduleInFutureOrHasNoSchedule(Integer campaignId) {
//        List<CpcUnitDto> cpcUnitDtos = cpcUnitServiceDelegate.queryCpcUnit(QueryCpcUnitDto.builder()
//                .campaignId(campaignId)
//                .unitStatusList(UnitStatus.NON_DELETED_UNIT_STATUS_LIST)
//                .build());
        QueryUnitBo query = QueryUnitBo.builder()
                .campaignIds(Collections.singletonList(campaignId))
                .unitStatusList(UnitStatus.NON_DELETED_UNIT_STATUS_LIST)
                .build();
        List<CpcUnitDto> cpcUnitDtos = launchUnitV1Service.listUnits(query);
        //计划下没单元，即计划没排期
        if (CollectionUtils.isEmpty(cpcUnitDtos)) {
            return Pair.of(true, Collections.EMPTY_LIST);
        }
        List<Integer> unitIds = cpcUnitDtos.stream().map(CpcUnitDto::getUnitId).collect(Collectors.toList());
        //查出每个单元的排期
        Map<Integer, List<Timestamp>> unitSchedules = flyGdPlusDelegate.queryValidUnitsSchedules(unitIds);
        //是否每个单元,都是未投放状态
        Boolean unitSchedulesAllInFuture = flyGdPlusDelegate.allUnitNotLaunch(unitSchedules);
        return Pair.of(unitSchedulesAllInFuture, cpcUnitDtos);
    }
}
