package com.bilibili.adp.cpc.biz.services.material;

import com.bapis.ad.mgk.material.*;
import com.bapis.ad.mgk.media.MediaServiceGrpc;
import com.bapis.ad.mgk.media.OriginImageInfo;
import com.bapis.ad.mgk.media.QueryOriginByCropMd5Req;
import com.bilibili.adp.cpc.biz.services.material.enums.MaterialIdTypeEnum;
import com.bilibili.adp.cpc.proxy.CpmMgkPortalProxy;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import pleiades.venus.starter.rpc.client.RPCClient;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/5/22
 * @description 素材唯一化处理
 */
@Slf4j
@Service
public class MaterialCenterGrpcService {

    @Resource
    private CpmMgkPortalProxy cpmMgkPortalProxy;


    /**
     *
     * @param materialIdType //素材id类型 图片img， 视频video，
     * @param materialUk     // 素材唯一键，类型下唯一，通常为md5
     * @param name     // 素材名称, 通常为素材标题而非上传文件名，可用于搜索
     * @param referenceUk    //同一素材下可能会有多个素材引用，如果同一个素材引用需要去重，使用该字段，进行去重，
     *     // 如果字段在上传时不提供，那么会使用snowflake自动生成id，也就是说每次注册都是全新的引用，不会进行去重
     * @param content    // 素材明细内容，该字段在注册事件中会进行透传，图片为上传图片url，视频为视频url，
     *     // "特殊的如gif，可以为上传前后两个url的json,
     *     //  e.g.:{\"img_url_before\":\"https://xxxx.jpg\",\"img_url_after\":\"https://xxxx.jpg\"}"
     * @param accountId    // 账户id"
     * @param source    // 素材来源,直接填写服务名， 例如 sycpb.cpm.mgk-portal
     * @param forceUpdate     // 是否强制更新，如果为true，则强制更新，更新名称和\t"
     *     //  "如果为false，则如果在referenceUk已经存在是，会选择不做更新，默认forceUpdate=true
     */
    public void produceImageSnowId(
            String materialUk,
            String name,
            String referenceUk,
            String content,
            String accountId
    ){
        MaterialIdRegisterReq req = MaterialIdRegisterReq.newBuilder()
                .setName(name)
                .setMaterialIdType(MaterialIdTypeEnum.IMG.getName())
                .setMaterialUk(materialUk)
                .setReferenceUk(referenceUk)
                .setContent(content)
                .setAccountId(accountId)
                .setSource("sycpb.cpm.adp")
                .setForceUpdate(false).build();
        cpmMgkPortalProxy.register(req);
    }


    /**
     * 根据素材id查询素材信息
     * @param mgkMaterialIds md5 list
     * @return
     */
    public Map<String, MaterialIdRegistry> findByMaterialIds(List<String> mgkMaterialIds) {
        return cpmMgkPortalProxy.findByMaterialIds(mgkMaterialIds);

    }

    public Map<String, MaterialIdRegistry> findByMd5s(List<String> md5s, String type) {
        if (md5s == null || md5s.isEmpty()) {
            return Collections.emptyMap();
        }
        return Try.of(() -> {
            return cpmMgkPortalProxy.findByTypeAndUks(md5s,  type);
        }).onFailure(t->{
            log.error("Fail to query material by md5s: {}, type={}", md5s, type, t);
        }).getOrElse(Collections.emptyMap());
    }


    public Map<String, OriginImageInfo> findOriginImageByCropMd5(List<String> cropImgMd5s) {
        return cpmMgkPortalProxy.findOriginImageByCropMd5(cropImgMd5s);
    }

}
