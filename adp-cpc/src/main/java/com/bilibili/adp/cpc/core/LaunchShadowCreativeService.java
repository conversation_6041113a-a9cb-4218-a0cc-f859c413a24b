package com.bilibili.adp.cpc.core;

import com.bapis.ad.mgk.*;
import com.bapis.ad.mgk.page.group.PageGroupStatus;
import com.bapis.archive.service.ArcsReply;
import com.bapis.archive.service.ArcsRequest;
import com.bilibili.adp.common.enums.AdvertisingMode;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.biz.bos.audit.AuditInfoBo;
import com.bilibili.adp.cpc.biz.bos.creative.CreativeLandingPageBo;
import com.bilibili.adp.cpc.biz.bos.creative.ShadowCreativeBo;
import com.bilibili.adp.cpc.biz.bos.unit.QueryUnitBo;
import com.bilibili.adp.cpc.biz.converter.creative.*;
import com.bilibili.adp.cpc.biz.services.creative.AdpCpcCreativeService;
import com.bilibili.adp.cpc.biz.services.page_group.api.ILaunchMgkPageGroupService;
import com.bilibili.adp.cpc.biz.services.page_group.bind.api.ILaunchMgkPageGroupBindService;
import com.bilibili.adp.cpc.biz.services.page_group.bind.bos.LaunchPageGroupCreativeBindBo;
import com.bilibili.adp.cpc.biz.services.page_group.bos.LaunchPageGroupBo;
import com.bilibili.adp.cpc.biz.services.page_group.bos.QueryLaunchPageGroupBo;
import com.bilibili.adp.cpc.biz.services.unit.dto.CpcUnitDto;
import com.bilibili.adp.cpc.core.bos.CreativeButtonBo;
import com.bilibili.adp.cpc.core.bos.CreativeImageBo;
import com.bilibili.adp.cpc.core.constants.AuditStatus;
import com.bilibili.adp.cpc.core.constants.BusinessDomain;
import com.bilibili.adp.cpc.core.constants.CreativeStatus;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.*;
import com.bilibili.adp.cpc.enums.AdpVersion;
import com.bilibili.adp.cpc.enums.ad.PromotionPurposeType;
import com.bilibili.adp.cpc.proxy.ArchiveServiceProxy;
import com.bilibili.adp.cpc.utils.CommonFuncs;
import com.bilibili.adp.cpc.utils.RecDiffResult;
import com.bilibili.bjcom.querydsl.BaseQueryFactory;
import com.bilibili.mgk.platform.api.landing_page.dto.TemplatePageDto;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.bilibili.adp.cpc.dao.ad_core.querydsl.QLauShadowCreative.lauShadowCreative;
import static com.bilibili.adp.cpc.dao.ad_core.querydsl.QLauUnitCreative.lauUnitCreative;
import static com.bilibili.adp.cpc.dao.config.PlatformMySqlConfig.AD_CORE_BQF;
import static com.bilibili.adp.cpc.dao.config.PlatformMySqlConfig.AD_CORE_TM;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LaunchShadowCreativeService {
    public static final List<Integer> DEFAULT_AUDIT_STATUS_LIST = Arrays.asList(
            AuditStatus.AUDITING,
            AuditStatus.PASSED,
            AuditStatus.REJECTED
    );

    public static final List<Integer> DEFAULT_CREATIVE_STATUS_LIST = Arrays.asList(
            CreativeStatus.AUDITING,
            CreativeStatus.VALID,
            CreativeStatus.REJECTED,
            CreativeStatus.LANDING_PAGE_AUDITING
    );

    public static final List<Integer> MATERIAL_SAVE_EXCLUDE_UNIT_PPT_LIST = Arrays.asList(
            PromotionPurposeType.ARCHIVE_CONTENT.getKey(),
            PromotionPurposeType.DYNAMIC.getKey(),
            PromotionPurposeType.GOODS_CATALOG.getKey()
    );

    @Resource(name = AD_CORE_BQF)
    private BaseQueryFactory adCoreBqf;

    @Autowired
    private ObjectMapper objectMapper;
    @Resource
    private ArchiveServiceProxy archiveServiceProxy;
    @Autowired
    private LandingPageServiceGrpc.LandingPageServiceBlockingStub landingPageServiceBlockingStub;
    @Autowired
    private LaunchUnitV1Service launchUnitV1Service;
    @Autowired
    private LaunchCreativeButtonService launchCreativeButtonService;
    @Autowired
    private LaunchCreativeImageService launchCreativeImageService;
    @Autowired
    private LaunchCreativeComponentService launchCreativeComponentService;
    @Autowired
    private LaunchCreativeFlyExtInfoService launchCreativeFlyExtInfoService;
    @Autowired
    private LaunchCreativeTabService launchCreativeTabService;
    @Autowired
    private AdpCpcCreativeService adpCpcCreativeService;
    @Autowired
    private LaunchCreativeLandingPageService launchCreativeLandingPageService;
    @Autowired
    private ILaunchMgkPageGroupBindService launchMgkPageGroupBindService;
    @Autowired
    private ILaunchMgkPageGroupService launchMgkPageGroupService;
    @Autowired
    private LaunchProgrammaticCreativeDetailService launchProgrammaticCreativeDetailService;

    public List<LauShadowCreativePo> list() {
        return adCoreBqf.selectFrom(lauShadowCreative)
                .fetch();
    }

    @SneakyThrows
    public ShadowCreativeBo shadowCreative(int creativeId) {
        return shadowCreative(creativeId, DEFAULT_AUDIT_STATUS_LIST, DEFAULT_CREATIVE_STATUS_LIST);
    }

    @SneakyThrows
    public ShadowCreativeBo shadowCreative(int creativeId, List<Integer> auditStatus, List<Integer> creativeStatus) {
        LauShadowCreativePo lauShadowCreativePo = shadowCreativePosByCreativeIds(Collections.singletonList(creativeId), auditStatus, creativeStatus).get(creativeId);
        if (Objects.isNull(lauShadowCreativePo)) {
            return null;
        }
        return objectMapper.readValue(lauShadowCreativePo.getShadowCreative(), ShadowCreativeBo.class);
    }

    @SneakyThrows
    public Map<Integer, ShadowCreativeBo> shadowCreatives(Collection<Integer> creativeIds) {
        return shadowCreatives(creativeIds, DEFAULT_AUDIT_STATUS_LIST, DEFAULT_CREATIVE_STATUS_LIST);
    }

    public Map<Integer, Boolean> hasShadowCreatives(Collection<Integer> creativeIds, List<Integer> auditStatus, List<Integer> creativeStatus) {
        List<Integer> shadowCreativeIds = adCoreBqf.select(lauShadowCreative.creativeId)
                .from(lauShadowCreative)
                .where(lauShadowCreative.creativeId.in(creativeIds))
                .whereIfNotEmpty(auditStatus, lauShadowCreative.auditStatus::in)
                .whereIfNotEmpty(creativeStatus, lauShadowCreative.creativeStatus::in)
                .fetch();
        return creativeIds
                .stream()
                .collect(Collectors.toMap(Function.identity(), shadowCreativeIds::contains));
    }


    @SneakyThrows
    public Map<Integer, ShadowCreativeBo> shadowCreatives(Collection<Integer> creativeIds, List<Integer> auditStatus, List<Integer> creativeStatus) {
        Assert.notEmpty(creativeIds, "creative ids should not be empty");
        //Assert.isTrue(creativeIds.size() >= 1 && creativeIds.size() <= 100, "creative id size should goe 1 and loe 100");
        Map<Integer, LauShadowCreativePo> map = shadowCreativePosByCreativeIds(creativeIds, auditStatus, creativeStatus);
        if (CollectionUtils.isEmpty(map.values())) {
            return Collections.emptyMap();
        }
        return po2BoMap(map);
    }

    @SneakyThrows
    public Map<Integer, ShadowCreativeBo> shadowCreatives(Integer unitId) {
        return shadowCreatives(unitId, DEFAULT_AUDIT_STATUS_LIST, DEFAULT_CREATIVE_STATUS_LIST);
    }

    public Map<Integer, LauShadowCreativePo> shadowCreativePosByCreativeIds(Collection<Integer> creativeIds, List<Integer> auditStatus, List<Integer> creativeStatus) {
        return adCoreBqf.selectFrom(lauShadowCreative)
                .where(lauShadowCreative.creativeId.in(creativeIds))
                .whereIfNotEmpty(auditStatus, lauShadowCreative.auditStatus::in)
                .whereIfNotEmpty(creativeStatus, lauShadowCreative.creativeStatus::in)
                .fetch()
                .stream()
                .filter(po -> StringUtils.isNotBlank(po.getShadowCreative()))
                .collect(Collectors.toMap(LauShadowCreativePo::getCreativeId, Function.identity(), (sc1, sc2) -> sc1));
    }

    public Map<Integer, LauShadowCreativePo> shadowCreativePosByPageIds(Collection<Long> mgkPageIds) {
        return shadowCreativePosByPageIds(mgkPageIds, DEFAULT_AUDIT_STATUS_LIST, DEFAULT_CREATIVE_STATUS_LIST);
    }

    public Map<Integer, LauShadowCreativePo> shadowCreativePosByPageIds(Collection<Long> mgkPageIds, List<Integer> auditStatus, List<Integer> creativeStatus) {
        return adCoreBqf.selectFrom(lauShadowCreative)
                .where(lauShadowCreative.mgkPageId.in(mgkPageIds))
                .whereIfNotEmpty(auditStatus, lauShadowCreative.auditStatus::in)
                .whereIfNotEmpty(creativeStatus, lauShadowCreative.creativeStatus::in)
                .fetch()
                .stream()
                .collect(Collectors.toMap(LauShadowCreativePo::getCreativeId, Function.identity(), (sc1, sc2) -> sc1));
    }

    public Map<Integer, LauShadowCreativePo> shadowCreativePosByPageGroupIds(Collection<Long> mgkPageGroupIds) {
        return shadowCreativePosByPageGroupIds(mgkPageGroupIds, DEFAULT_AUDIT_STATUS_LIST, DEFAULT_CREATIVE_STATUS_LIST);
    }

    public Map<Integer, LauShadowCreativePo> shadowCreativePosByPageGroupIds(Collection<Long> mgkPageGroupIds,
                                                                             List<Integer> auditStatus,
                                                                             List<Integer> creativeStatus) {
        return adCoreBqf.selectFrom(lauShadowCreative)
                .where(lauShadowCreative.pageGroupId.in(mgkPageGroupIds))
                .whereIfNotEmpty(auditStatus, lauShadowCreative.auditStatus::in)
                .whereIfNotEmpty(creativeStatus, lauShadowCreative.creativeStatus::in)
                .fetch()
                .stream()
                .collect(Collectors.toMap(LauShadowCreativePo::getCreativeId, Function.identity(), (sc1, sc2) -> sc1));
    }

    @SneakyThrows
    public Map<Integer, ShadowCreativeBo> shadowCreatives(Integer unitId, List<Integer> auditStatus, List<Integer> creativeStatus) {
        Assert.notNull(unitId, "unit id should not be null");
        Map<Integer, LauShadowCreativePo> map = adCoreBqf
                .selectFrom(lauShadowCreative)
                .where(lauShadowCreative.unitId.eq(unitId))
                .where(lauShadowCreative.auditStatus.in(auditStatus))
                .where(lauShadowCreative.creativeStatus.in(creativeStatus))
                .fetch()
                .stream()
                .filter(po -> StringUtils.isNotBlank(po.getShadowCreative()))
                .collect(Collectors.toMap(LauShadowCreativePo::getCreativeId, Function.identity(), (sc1, sc2) -> sc1));
        return po2BoMap(map);
    }
    @SneakyThrows
    private Map<Integer, ShadowCreativeBo> po2BoMap(Map<Integer, LauShadowCreativePo> map) {
        Map<Integer, ShadowCreativeBo> result = new HashMap<>(map.size());
        for (Map.Entry<Integer, LauShadowCreativePo> entry : map.entrySet()) {
            Integer creativeId = entry.getKey();
            LauShadowCreativePo po = entry.getValue();
            ShadowCreativeBo shadowCreativeBo = objectMapper.readValue(entry.getValue().getShadowCreative(), ShadowCreativeBo.class);
            shadowCreativeBo.setAdvertisingMode(Optional.ofNullable(shadowCreativeBo.getAdvertisingMode()).orElse(0));
            shadowCreativeBo.setJumpUrlSecondary(Optional.ofNullable(shadowCreativeBo.getJumpUrlSecondary()).orElse(StringUtils.EMPTY));
            shadowCreativeBo.setMgkPageId(Optional.ofNullable(shadowCreativeBo.getMgkPageId()).orElse(0L));
            shadowCreativeBo.setCmMark(Optional.ofNullable(shadowCreativeBo.getCmMark()).orElse(0));
            shadowCreativeBo.setBusMark(Optional.ofNullable(shadowCreativeBo.getBusMark()).orElse(0));
            shadowCreativeBo.setButton(shadowCreativeBo.getButton());
            shadowCreativeBo.setImages(Optional.ofNullable(shadowCreativeBo.getImages()).orElseGet(Collections::emptyList));
            shadowCreativeBo.setComponents(Optional.ofNullable(shadowCreativeBo.getComponents()).orElseGet(Collections::emptyList));
            shadowCreativeBo.setTab(shadowCreativeBo.getTab());
            shadowCreativeBo.setLandingPage(shadowCreativeBo.getLandingPage());
            shadowCreativeBo.setFlyExtInfo(shadowCreativeBo.getFlyExtInfo());
            shadowCreativeBo.setAuditStatus(po.getAuditStatus());
            shadowCreativeBo.setCreativeStatus(po.getCreativeStatus());
            shadowCreativeBo.setReason(po.getReason());
            shadowCreativeBo.setIsPageGroup(Optional.ofNullable(shadowCreativeBo.getIsPageGroup()).orElse(0));
            shadowCreativeBo.setExtDescription(Optional.ofNullable(shadowCreativeBo.getExtDescription()).orElse(StringUtils.EMPTY));
            shadowCreativeBo.setVideoId(Optional.ofNullable(shadowCreativeBo.getVideoId()).orElse(0L));
            shadowCreativeBo.setProgMiscElemAuditStatus(Optional.ofNullable(shadowCreativeBo.getProgMiscElemAuditStatus()).orElse(0));
            shadowCreativeBo.setProgAuditStatus(Optional.ofNullable(shadowCreativeBo.getProgAuditStatus()).orElse(0));
            result.put(creativeId, shadowCreativeBo);
        }
        return result;
    }

    public List<LauShadowCreativePo> listByCreativeId(int creativeId) {
        return adCoreBqf.from(lauShadowCreative)
                .where(lauShadowCreative.creativeId.eq(creativeId))
                .select(lauShadowCreative.id,
                        lauShadowCreative.creativeId
                )
                .fetch(LauShadowCreativePo.class);
    }

    public List<LauShadowCreativePo> list(int unitId) {
        return adCoreBqf.from(lauShadowCreative)
                .where(lauShadowCreative.unitId.eq(unitId))
                .select(lauShadowCreative.id,
                        lauShadowCreative.creativeId
                )
                .fetch(LauShadowCreativePo.class);
    }

    public void saveByCreativeId(int creativeId, List<LauShadowCreativePo> pos) {
        final List<LauShadowCreativePo> existingPos = listByCreativeId(creativeId);
        final RecDiffResult<LauShadowCreativePo, Long> result = CommonFuncs.recDiff(existingPos, pos, this::uk, LauShadowCreativePo::getId, LauShadowCreativePo::setId);
        CommonFuncs.handleRecDiff(result, adCoreBqf, lauShadowCreative, lauShadowCreative.id::in);
    }

    public void save(int unitId, List<LauShadowCreativePo> pos) {
        final RecDiffResult<LauShadowCreativePo, Long> result = CommonFuncs.recDiff(list(unitId), pos, this::uk, LauShadowCreativePo::getId, LauShadowCreativePo::setId);
        CommonFuncs.handleRecDiff(result, adCoreBqf, lauShadowCreative, lauShadowCreative.id::in);
    }

    private String uk(LauShadowCreativePo po) {
        return po.getCreativeId().toString();
    }

    @SneakyThrows
    @Transactional(transactionManager = AD_CORE_TM, rollbackFor = Exception.class)
    public void save(boolean existShadowCreative, ShadowCreativeBo shadowCreativeBo, Integer creativeStatus) {
        String shadowCreative = objectMapper.writeValueAsString(shadowCreativeBo);
        if (existShadowCreative) {
            adCoreBqf.update(lauShadowCreative)
                    .setIfNotNull(lauShadowCreative.mgkPageId, shadowCreativeBo.getMgkPageId())
                    .setIfNotNull(lauShadowCreative.shadowCreative, shadowCreative)
                    .setIfNotNull(lauShadowCreative.auditStatus, com.bilibili.adp.cpc.core.constants.AuditStatus.AUDITING)
                    .setIfNotNull(lauShadowCreative.creativeStatus, creativeStatus)
                    .where(lauShadowCreative.campaignId.eq(shadowCreativeBo.getCampaignId()))
                    .where(lauShadowCreative.unitId.eq(shadowCreativeBo.getUnitId()))
                    .where(lauShadowCreative.creativeId.eq(shadowCreativeBo.getCreativeId()))
                    .execute();
        } else {
            adCoreBqf.insert(lauShadowCreative)
                    .setIfNotNull(lauShadowCreative.accountId, shadowCreativeBo.getAccountId())
                    .setIfNotNull(lauShadowCreative.campaignId, shadowCreativeBo.getCampaignId())
                    .setIfNotNull(lauShadowCreative.unitId, shadowCreativeBo.getUnitId())
                    .setIfNotNull(lauShadowCreative.creativeId, shadowCreativeBo.getCreativeId())
                    .setIfNotNull(lauShadowCreative.mgkPageId, shadowCreativeBo.getMgkPageId())
                    .setIfNotNull(lauShadowCreative.shadowCreative, shadowCreative)
                    .setIfNotNull(lauShadowCreative.auditStatus, com.bilibili.adp.cpc.core.constants.AuditStatus.AUDITING)
                    .setIfNotNull(lauShadowCreative.creativeStatus, creativeStatus)
                    .execute();
        }
    }

    @Transactional(transactionManager = AD_CORE_TM, rollbackFor = Exception.class)
    public void auditShadowCreatives(AuditInfoBo auditInfo, Integer auditStatus) {
        auditShadowCreativesAndWriteBack(Collections.singletonList(auditInfo), auditStatus);
    }

    /**
     * 运营后台审核创意使用，目前跟在审核拒绝和审核通过之后的逻辑
     * 审核通过:
     * 1.原创意是新建的(不存在影子创意),原创意的状态已经翻转过了,无需任何操作直接返回
     * 2.原创意是更新的(存在影子创意), 翻转影子创意的状态为【审核通过】,再根据影子创意的内容填充原创意并更新原创意以及创意额外信息(例如 图片、弹幕、按钮等)
     * 审核拒绝:
     * 1.原创意是新建的(不存在影子创意),原创意的状态已经翻转过了,无需任何操作直接返回
     * 2.原创意是更新的(存在影子创意), 翻转影子创意的状态为【审核拒绝】,再根据影子创意的内容填充原创意并更新原创意以及创意额外信息(例如 图片、弹幕、按钮等)
     *
     * @param auditInfos  创意 列表
     * @param auditStatus 审核状态
     */
    @Transactional(transactionManager = AD_CORE_TM, rollbackFor = Exception.class)
    public void auditShadowCreativesAndWriteBack(Collection<AuditInfoBo> auditInfos, Integer auditStatus) {
        Assert.isTrue(!auditInfos.isEmpty() && auditInfos.size() <= 50, "creative size should gt 0 and loe 50");
        List<Integer> creativeIds = auditInfos
                .stream()
                .map(AuditInfoBo::getCreativeId)
                .collect(Collectors.toList());
        Map<Integer, LauShadowCreativePo> shadowCreativePoMap = shadowCreativePosByCreativeIds(creativeIds, DEFAULT_AUDIT_STATUS_LIST, DEFAULT_CREATIVE_STATUS_LIST);
        //如果没有影子创意直接返回，证明不需要根据影子创意来回写创意
        if (CollectionUtils.isEmpty(shadowCreativePoMap.values())) {
            return;
        }
        log.info("start write back shadow creatives, creative ids :{}", shadowCreativePoMap.keySet());

        // 用影子创意回填创意信息
        writeBackShadowCreatives(shadowCreativePoMap, auditStatus);

        // 删除影子
        adCoreBqf.delete(lauShadowCreative).where(lauShadowCreative.creativeId.in(creativeIds)).execute();
    }

    public void auditShadowCreatives(Collection<Integer> creativeIds,
                                     Integer creativeStatus,
                                     Integer auditStatus,
                                     String reason) {
        adCoreBqf.update(lauShadowCreative)
                .where(lauShadowCreative.creativeId.in(creativeIds))
                .set(lauShadowCreative.creativeStatus, creativeStatus)
                .set(lauShadowCreative.auditStatus, auditStatus)
                .set(lauShadowCreative.reason, reason)
                .execute();
    }

    @SneakyThrows
    public void writeBackShadowCreatives(Map<Integer, LauShadowCreativePo> shadowCreativePoMap, Integer auditStatus) {
        if (CollectionUtils.isEmpty(shadowCreativePoMap)) {
            return;
        }
        final Map<Integer, LauUnitCreativePo> creativePos = adCoreBqf
                .selectFrom(lauUnitCreative)
                .where(lauUnitCreative.creativeId.in(shadowCreativePoMap.keySet()))
                .fetch()
                .stream()
                // 过滤adpVersion=6创意
                .filter(po -> !AdpVersion.isCpcFlyMerge(po.getAdpVersion()))
                .collect(Collectors.toMap(LauUnitCreativePo::getCreativeId, Function.identity()));
        final Map<Integer, ShadowCreativeBo> shadowCreativeBos = po2BoMap(shadowCreativePoMap);

        // 审核通过
        if (auditStatus == AuditStatus.PASSED) {
            final List<Long> mgkPageIds = shadowCreativeBos
                    .values()
                    .stream()
                    .map(ShadowCreativeBo::getMgkPageId)
                    .filter(Utils::isPositive)
                    .distinct()
                    .collect(Collectors.toList());
            validateMgkPages(mgkPageIds);
            final List<Long> aids = shadowCreativeBos
                    .values()
                    .stream()
                    .map(ShadowCreativeBo::getVideoId)
                    .filter(Utils::isPositive)
                    .distinct()
                    .collect(Collectors.toList());
            validateArcs(aids);
            final List<Long> pageGroupIds = shadowCreativeBos.values()
                    .stream()
                    .map(ShadowCreativeBo::getPageGroupId)
                    .filter(Utils::isPositive)
                    .distinct()
                    .collect(Collectors.toList());
            validatePageGroupIds(pageGroupIds);
        }
        //此处需要查一遍的原因：之前保存的联投落地页可能有雨落地页变更，导致联投落地页下线，所以需要实时查一下
        //不然可能会出现，之前保存的联投落地页id对应的落地页已经下线，线上出现空窗情况(xuhaoyu说的)
        final List<Long> extraLandingPageIds = shadowCreativeBos
                .values()
                .stream()
                .map(ShadowCreativeBo::getLandingPage)
                .filter(Objects::nonNull)
                .map(CreativeLandingPageBo::getContainerPageId)
                .filter(Utils::isPositive)
                .collect(Collectors.toList());
        final TemplatePagesReply templatePagesReply = landingPageServiceBlockingStub.templatePages(TemplatePagesReq.newBuilder().addAllMgkPageId(extraLandingPageIds).build());
        final Map<Long, TemplatePageDto> extraLandingPages = templatePagesReply.getTemplatePagesMap()
                .entrySet()
                .stream()
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> TemplatePageDto.builder()
                        .mgkPageId(entry.getValue().getMgkPageId())
                        .launchUrl(entry.getValue().getJumpUrl())
                        .launchUrlSecondary(entry.getValue().getJumpUrlSecondary())
                        .build()));

        // 创意存在影子，则用影子回填
        creativePos.forEach((creativeId, creative) -> Optional.ofNullable(shadowCreativeBos.get(creativeId)).ifPresent(shadowCreative -> {
            // 影子回填
            writeBackCreative(creative, shadowCreative);
            //创意联投落地页 page id
            Long extraLandingPageId = Optional.ofNullable(shadowCreative.getLandingPage()).map(CreativeLandingPageBo::getContainerPageId).orElse(0L);
            writeBackCreativeExtraInfo(creative, shadowCreative, extraLandingPages.get(extraLandingPageId));
            creative.setMtime(Utils.getNow());
        }));
        log.info("start to update creatives, creative ids :{}", creativePos.keySet());
        adCoreBqf.update(lauUnitCreative).updateBeans(creativePos.values());

        // 更新物料
        updateCreativeMaterials(creativePos.values());
    }

    /**
     * 用影子创意回写创意(方法外写库)
     * @param creative 创意
     * @param shadowCreative 影子创意
     */
    private void writeBackCreative(LauUnitCreativePo creative, ShadowCreativeBo shadowCreative) {
        creative.setTitle(shadowCreative.getTitle());
        creative.setCmMark(shadowCreative.getCmMark());
        creative.setBusMarkId(shadowCreative.getBusMark());
        creative.setDescription(shadowCreative.getDescription());
        creative.setExtDescription(shadowCreative.getExtDescription());
        creative.setMgkPageId(shadowCreative.getMgkPageId());
        creative.setJumpType(shadowCreative.getJumpType());
        creative.setPromotionPurposeContent(shadowCreative.getJumpUrl());
        creative.setPromotionPurposeContentSecondary(shadowCreative.getJumpUrlSecondary());
        creative.setTrackadf(shadowCreative.getTrackadf());
        List<CreativeImageBo> images = shadowCreative.getImages();
        CreativeImageBo image = !CollectionUtils.isEmpty(images) ? images.get(0) : CreativeImageBo.builder().imageUrl(StringUtils.EMPTY).imageMd5(StringUtils.EMPTY).build();
        creative.setImageUrl(image.getImageUrl());
        creative.setImageMd5(image.getImageMd5());
        creative.setIsPageGroup(shadowCreative.getIsPageGroup());
        creative.setButtonCopy(Optional.ofNullable(shadowCreative.getButton()).map(CreativeButtonBo::getButtonContent).orElse(StringUtils.EMPTY));
    }

    /**
     * 用影子创意回写创意其他信息(方法内写库)
     *
     * @param creative         创意信息
     * @param shadowCreative   影子创意
     * @param extraLandingPage 创意联投落地页
     */
    private void writeBackCreativeExtraInfo(LauUnitCreativePo creative, ShadowCreativeBo shadowCreative, TemplatePageDto extraLandingPage) {
        //保存创意图片
        List<LauCreativeImagePo> images = shadowCreative.getImages()
                .stream()
                .filter(Objects::nonNull)
                .map(image -> CreativeImageConverter.MAPPER.bo2Po(creative.getUnitId(), creative.getCreativeId(), image))
                .collect(Collectors.toList());
        launchCreativeImageService.saveByCreativeId(creative.getCreativeId(), images);
        //保存创意按钮
        List<LauCreativeButtonCopyPo> buttons = Stream.of(shadowCreative.getButton())
                .filter(Objects::nonNull)
                .map(button -> CreativeButtonConverter.MAPPER.bo2Po(creative.getUnitId(), creative.getCreativeId(), button))
                .collect(Collectors.toList());
        launchCreativeButtonService.saveByCreativeId(creative.getCreativeId(), buttons);
        //保存创意组件
        List<LauCreativeComponentPo> components = shadowCreative.getComponents()
                .stream()
                .filter(Objects::nonNull)
                .map(component -> CreativeComponentConverter.MAPPER.bo2Po(creative.getAccountId(), creative.getCampaignId(), creative.getUnitId(), creative.getCreativeId(), component))
                .collect(Collectors.toList());
        launchCreativeComponentService.saveByCreativeId(creative.getCreativeId(), components);
        //保存原生落地页
        if (AdvertisingMode.nativeContentMode(shadowCreative.getAdvertisingMode())) {
            List<LauCreativeTabPo> tabs = Stream.of(shadowCreative.getTab())
                    .filter(Objects::nonNull)
                    .map(tab -> CreativeTabConverter.MAPPER.bo2Po(shadowCreative.getAccountId(), shadowCreative.getUnitId(), (long) shadowCreative.getCreativeId(), tab))
                    .collect(Collectors.toList());
            launchCreativeTabService.saveByCreativeId(creative.getCreativeId(), tabs);
        }
        //保存创意联投
        final boolean needSaveExtraLandingPage = Objects.equals(PromotionPurposeType.LANDING_PAGE.getCode(), shadowCreative.getPromotionPurposeType())
                || Objects.equals(PromotionPurposeType.ON_SHELF_GAME.getCode(), shadowCreative.getPromotionPurposeType())
                || Objects.equals(PromotionPurposeType.APP_DOWNLOAD.getCode(), shadowCreative.getPromotionPurposeType())
                || Objects.equals(PromotionPurposeType.SALE_GOODS.getCode(), shadowCreative.getPromotionPurposeType());
        if (needSaveExtraLandingPage) {
            launchCreativeLandingPageService.saveLandingPageExtraInfo(creative.getAccountId(), creative.getCampaignId(), creative.getUnitId(), creative.getCreativeId(), extraLandingPage);
        }
        // 保存黄车信息(起飞独有)
        launchCreativeFlyExtInfoService.saveCreativeFlyYellowCarInfo(creative.getCreativeId(), shadowCreative.getFlyExtInfo());
        // 保存落地页组绑定信息
        LaunchPageGroupCreativeBindBo bindBo = LaunchPageGroupCreativeBindBo.builder()
                .creativeId(creative.getCreativeId())
                .groupId(shadowCreative.getPageGroupId())
                .canJointLaunch(shadowCreative.getCanJointLaunchPageGroup())
                .accountId(creative.getAccountId())
                .build();
        launchMgkPageGroupBindService.saveCreativePageGroup(bindBo);

        // creative details
        if (!CollectionUtils.isEmpty(shadowCreative.getCreativeDetailBos())) {
            List<LauProgrammaticCreativeDetailPo> creativeDetailPos = shadowCreative.getCreativeDetailBos()
                    .stream()
                    .filter(Objects::nonNull)
                    .map(detailBo -> ProgrammaticCreativeDetailConverter.MAPPER.bo2po(detailBo)).collect(Collectors.toList());
            if (Utils.isPositive(creative.getIsProgrammatic()) && !CollectionUtils.isEmpty(creativeDetailPos)) {
                launchProgrammaticCreativeDetailService.saveCreativeDetails(creative.getCreativeId(), creativeDetailPos);
            }
        }

    }


    private void updateCreativeMaterials(Collection<LauUnitCreativePo> creativePoList) {
        if (CollectionUtils.isEmpty(creativePoList)) {
            return;
        }
        // 更新物料信息
        List<Integer> unitIds = creativePoList.stream()
                .map(LauUnitCreativePo::getUnitId)
                .distinct()
                .collect(Collectors.toList());
        QueryUnitBo queryUnitBo = QueryUnitBo.builder()
                .unitIds(unitIds)
                .adpVersions(Lists.newArrayList(AdpVersion.MIDDLE.getKey()))
                .build();
        List<CpcUnitDto> unitDtoList = launchUnitV1Service.listUnits(queryUnitBo);
        List<Integer> needUpdateMaterialUnitIdList = unitDtoList.stream().filter(unitDto ->
                BusinessDomain.CPC == unitDto.getBusinessDomain()
                        && !MATERIAL_SAVE_EXCLUDE_UNIT_PPT_LIST.contains(unitDto.getPromotionPurposeType()))
                .map(CpcUnitDto::getUnitId)
                .collect(Collectors.toList());
        List<Integer> needUpdateMaterialCreativeIdList = creativePoList.stream()
                .filter(creativePo -> needUpdateMaterialUnitIdList.contains(creativePo.getUnitId()))
                .map(LauUnitCreativePo::getCreativeId)
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(needUpdateMaterialCreativeIdList)) {
            adpCpcCreativeService.updateMaterialIdsForNonProgrammaticCreatives(null ,needUpdateMaterialCreativeIdList);
        }
    }

    private void validateMgkPages(final List<Long> mgkPageIds) {
        log.info("start to validate mgk page, page ids, :{}", mgkPageIds);
        if (CollectionUtils.isEmpty(mgkPageIds)) {
            return;
        }
        MgkPagesStatusReply mgkPagesStatusReply = landingPageServiceBlockingStub.mgkPageStatus(MgkPagesStatusReq.newBuilder().addAllMgkPageId(mgkPageIds).build());
        long count = mgkPagesStatusReply.getPagesStatusMap().values().stream().filter(status -> status == MgkPageStatus.PUBLISHED).count();
        Assert.isTrue(count == mgkPageIds.size(), "include invalid mgk page ids");
    }

    private void validateArcs(final List<Long> aids) {
        log.info("start to validate arcs, aids, :{}", aids);
        if (CollectionUtils.isEmpty(aids)) {
            return;
        }
        ArcsReply arcs = archiveServiceProxy.arcs(ArcsRequest.newBuilder().addAllAids(aids).build());
        //稿件状态是 >= 才是合法状态
        long count = arcs.getArcsMap().values().stream().filter(arc -> arc.getState() >= 0).count();
        Assert.isTrue(count == aids.size(), "include invalid aids");
    }

    private void validatePageGroupIds(final List<Long> pageGroupIds) {
        if (CollectionUtils.isEmpty(pageGroupIds)) {
            return;
        }
        log.info("start to validate pageGroupIds, pageGroupIds, :{}", pageGroupIds);
        QueryLaunchPageGroupBo queryBo = QueryLaunchPageGroupBo.builder()
                .pageGroupIdList(pageGroupIds)
                .build();
        List<LaunchPageGroupBo> launchPageGroupBoList = launchMgkPageGroupService.queryPageGroup(queryBo);
        Assert.isTrue(launchPageGroupBoList.size() == pageGroupIds.size(), "部分落地页组不存在或者已被删除");
        Assert.isTrue(launchPageGroupBoList.stream()
                        .allMatch(pageGroupBo -> Lists.newArrayList(
                                PageGroupStatus.NO_CREATIVE.getNumber(),
                                PageGroupStatus.VALID.getNumber(),
                                PageGroupStatus.WAIT_AUDIT.getNumber()).contains(pageGroupBo.getGroupStatus())),
                "部分落地页组不可用");
    }

    @Transactional(transactionManager = AD_CORE_TM, rollbackFor = Exception.class)
    public void deleteByUnitIds(Collection<Integer> unitIds) {
        adCoreBqf.delete(lauShadowCreative)
                .where(lauShadowCreative.unitId.in(unitIds))
                .execute();
    }

    @Transactional(transactionManager = AD_CORE_TM, rollbackFor = Exception.class)
    public Long deleteByCreativeIds(Collection<Integer> creativeIds) {
        long count = adCoreBqf.delete(lauShadowCreative)
                .where(lauShadowCreative.creativeId.in(creativeIds))
                .execute();
        return count;
    }
}
