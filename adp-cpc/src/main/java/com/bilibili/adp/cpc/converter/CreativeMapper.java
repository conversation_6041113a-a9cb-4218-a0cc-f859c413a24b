package com.bilibili.adp.cpc.converter;

import com.bapis.ad.creative.SingleQueryCreativeListRep;
import com.bapis.ad.creative.SingleQueryProgramCreativeDetailListRep;
import com.bilibili.adp.cpc.biz.services.creative.bos.LauUnitCreativeBo;
import com.bilibili.adp.cpc.biz.services.creative.bos.programmatic.LauProgrammaticCreativeDetailBo;
import com.bilibili.adp.cpc.biz.services.creative.dto.CpcCreativeDto;
import com.bilibili.adp.cpc.config.databus.SimpleLauUnitCreativeBo;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauUnitCreativePo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface CreativeMapper {
    CreativeMapper MAPPER = Mappers.getMapper(CreativeMapper.class);
    LauUnitCreativePo toPo(com.bilibili.adp.launch.biz.pojo.LauUnitCreativePo po);

    List<SimpleLauUnitCreativeBo> toBo(List<LauUnitCreativePo> pos);

    List<SimpleLauUnitCreativeBo> toBos(List<com.bilibili.adp.launch.biz.pojo.LauUnitCreativePo> po);


    SingleQueryCreativeListRep bos2grpc(LauUnitCreativeBo creativeBos);
    List<SingleQueryCreativeListRep> bos2grpcs(List<LauUnitCreativeBo> creativeBos);

    List<SingleQueryProgramCreativeDetailListRep> detailBos2grpc(List<LauProgrammaticCreativeDetailBo> creativeDetailBos);

}