package com.bilibili.adp.cpc.biz.services.archive;


import com.alibaba.fastjson.JSON;
import com.bapis.account.service.v2.UserInfo;
import com.bapis.account.service.v2.UsersInfoReply;
import com.bapis.ad.adp.component.*;
import com.bapis.ad.component.BizCodeEnum;
import com.bapis.ad.component.CommentContainerTypeEnum;
import com.bapis.ad.component.ComponentType;
import com.bapis.ad.component.SingleCreateReply;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.enums.OperatorType;
import com.bilibili.adp.common.util.EmojiUtils;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.biz.bos.archive.ArchiveCommentConversionComponentBo;
import com.bilibili.adp.cpc.biz.constants.Constants;
import com.bilibili.adp.cpc.biz.converter.archive.ArchiveCommentComponentConverter;
import com.bilibili.adp.cpc.biz.converter.components.ComponentComponentConverter;
import com.bilibili.adp.cpc.biz.services.account.AdpCpcAccountMidBindingService;
import com.bilibili.adp.cpc.biz.services.account.BusinessToolAccountQuerier;
import com.bilibili.adp.cpc.biz.services.archive.bos.CommentComponentUpdateLogBo;
import com.bilibili.adp.cpc.biz.services.archive.bos.CreateAdThreeElementsBo;
import com.bilibili.adp.cpc.biz.services.business_message.BusinessMessageService;
import com.bilibili.adp.cpc.biz.services.business_message.dto.CreateMessageComponentDto;
import com.bilibili.adp.cpc.biz.services.business_message.dto.MessageComponentDto;
import com.bilibili.adp.cpc.biz.services.business_message.dto.MessageComponentInquiryQuery;
import com.bilibili.adp.cpc.biz.services.log.DbTable;
import com.bilibili.adp.cpc.biz.services.log.ILogOperateForLongService;
import com.bilibili.adp.cpc.enums.ClueTypeEnum;
import com.bilibili.adp.util.common.AdpCatUtils;
import com.bilibili.bvid.BVIDUtils;
import com.bilibili.mgk.platform.api.landing_page.dto.MgkLandingPageDto;
import com.bilibili.mgk.platform.api.landing_page.dto.QueryLandingPageParamDto;
import com.bilibili.mgk.platform.api.landing_page.soa.ISoaLandingPageService;
import com.google.common.collect.Lists;
import com.google.protobuf.Empty;
import edu.emory.mathcs.backport.java.util.Collections;
import io.grpc.stub.StreamObserver;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import pleiades.venus.starter.rpc.server.RPCService;

import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

/**
 * 稿件评论组件 rpc 服务(提供给经营平台)
 *
 * <AUTHOR>
 * @date 2024/1/19 17:20
 */
@Slf4j
@Service
@RPCService
@AllArgsConstructor
public class ArchiveCommentComponentRpcService extends CommentComponentServiceGrpc.CommentComponentServiceImplBase {

    public static final String ID = "ArchiveCommentComponentRpcService";

    private final Set<Integer> blueVRoleSet = new HashSet<>(Arrays.asList(3, 4, 5, 6));

    private final ArchiveConversionComponentService archiveConversionComponentService;
    private final BusinessToolAccountQuerier businessToolAccountQuerier;
    private final BusinessToolArchiveConversionComponentService businessToolArchiveConversionComponentService;
    private final ThreadPoolTaskExecutor businessToolThreadPoolExecutor;
    private final ILogOperateForLongService logOperateForLongService;
    private final BusinessMessageService businessMessageService;
    private final ISoaLandingPageService iSoaLandingPageService;
    private final AdpCpcAccountMidBindingService adpCpcAccountMidBindingService;

    @Override
    public void createAdThreeElements(SingleCreateComponentThreeElementsReq request, StreamObserver<SingleCreateComponentThreeElementsReply> responseObserver) {
        try {
            CreateAdThreeElementsBo adThreeElements = businessToolArchiveConversionComponentService.createAdThreeElements(request.getAccountId());

            responseObserver.onNext(SingleCreateComponentThreeElementsReply.newBuilder()
                    .setCampaignId(adThreeElements.getCampaignId())
                    .setUnitId(adThreeElements.getUnitId())
                    .setCreativeId(adThreeElements.getCreativeId())
                    .build());
            responseObserver.onCompleted();
        } catch (Throwable t) {
            responseObserver.onError(t);
            log.error("{}: 创建广告三要素[{}]，触发失败 -> {}", ID, request.getAccountId(),
                    t.getMessage());
        }
    }

    @Override
    public void addCommentComponents(AddCommentComponentsReq request, StreamObserver<AddCommentComponentsReply> responseObserver) {

        try {
            List<AddCommentComponentReply> componentReplyList = AdpCatUtils.newTransactionAndReturn(Constants.CAT_TYPE_OTHER, "经营号批量创建稿件评论组件",
                    t -> doAddCommentComponentsByParallel(request));

            responseObserver.onNext(AddCommentComponentsReply.newBuilder().addAllList(componentReplyList).build());
            responseObserver.onCompleted();
        } catch (Throwable t) {
            responseObserver.onError(t);
            log.error("{}: 创建稿件组件[{}]，触发失败 -> {}", ID, request.getAvidsList(), t.getMessage());
        }
    }


    private List<AddCommentComponentReply> doAddCommentComponentsByParallel(AddCommentComponentsReq request) {
        List<Long> avids = request.getAvidsList();
        List<AddCommentComponentReply> componentReplyList = new ArrayList<>();
        Operator operator = businessToolAccountQuerier.getBusinessToolOperator(request.getAccountId());

        // 校验运营工具(多个稿件公用的经营组件，只需要转一次)
        ArchiveCommentConversionComponentBo commentConversionComponentBo =
                businessToolArchiveConversionComponentService.validateBusinessToolThenGenConvUrl(operator, request);
        if (CollectionUtils.isEmpty(avids)) {
            return Collections.emptyList();
        }
        log.info("commentConversionComponentBo={}", JSON.toJSONString(commentConversionComponentBo));

        // 并行处理
        List<CompletableFuture<AddCommentComponentReply>> completableFutureList = new ArrayList<>();
        for (Long avid : avids) {
            try {
                CompletableFuture<AddCommentComponentReply> completableFuture = CompletableFuture.supplyAsync(() -> {
                    SingleCreateReply createReply = AdpCatUtils.newTransactionAndReturn(Constants.CAT_TYPE_OTHER,
                            "创建single经营平台稿件评论组件", t -> addSingleCommentComponent(request, operator, avid, commentConversionComponentBo));


                    AddCommentComponentReply.Builder builder;
                    if (createReply.getCode() == 0) {
                        builder = AddCommentComponentReply.newBuilder()
                                .setAvid(avid).setSuccess(true).setMsg("ok")
                                .setCreativeId(createReply.getCreativeId())
                                .setId(createReply.getId());
                    } else {
                        builder = AddCommentComponentReply.newBuilder()
                                .setAvid(avid).setSuccess(false).setMsg(createReply.getMsg())
                                .setCreativeId(createReply.getCreativeId())
                                .setId(createReply.getId());
                    }

                    return builder.build();
                }, businessToolThreadPoolExecutor);

                completableFutureList.add(completableFuture);
            } catch (Exception e) {
                log.error("{}: 创建稿件组件 saveCommentConversionComponent[{}]，触发失败 -> {}", ID, request.getAvidsList(), e.getMessage());
                AddCommentComponentReply commentComponentReply =
                        AddCommentComponentReply.newBuilder().setAvid(avid).setSuccess(false).setMsg(e.getMessage()).build();
                componentReplyList.add(commentComponentReply);
            }
        }
        // 阻塞，等待所有任务执行完成
        CompletableFuture<Void> all = CompletableFuture.allOf(completableFutureList.toArray(new CompletableFuture[0]));

        CompletableFuture<List<AddCommentComponentReply>> alllistCompletableFuture = all.thenApply(t -> completableFutureList.stream().map(a -> {
            try {
                return a.get();
            } catch (InterruptedException | ExecutionException e) {
                log.error("doAddCommentComponentsByParallel error, e={}", e);
                AddCommentComponentReply commentComponentReply =
                        AddCommentComponentReply.newBuilder().setSuccess(false).setMsg(e.getMessage()).build();
                return commentComponentReply;
            }
        }).collect(Collectors.toList()));

        try {
            return alllistCompletableFuture.get();
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        } catch (ExecutionException e) {
            throw new RuntimeException(e);
        }
    }


    @Override
    public void updateCommentComponents(AddCommentComponentsReq request, StreamObserver<AddCommentComponentsReply> responseObserver) {

        try {
            List<AddCommentComponentReply> componentReplyList = AdpCatUtils.newTransactionAndReturn(Constants.CAT_TYPE_OTHER, "businessTool-updateCommentComponents",
                    t -> doUpdateCommentComponentsByParallel(request));

            responseObserver.onNext(AddCommentComponentsReply.newBuilder().addAllList(componentReplyList).build());
            responseObserver.onCompleted();
        } catch (Throwable t) {
            responseObserver.onError(t);
            log.error("{}: 更新稿件组件[{}]，触发失败 -> {}", ID, request.getAvidsList(), t.getMessage());
        }
    }


    private List<AddCommentComponentReply> doUpdateCommentComponentsByParallel(AddCommentComponentsReq request) {
        List<Long> avids = request.getAvidsList();
        List<AddCommentComponentReply> componentReplyList = new ArrayList<>();
        Operator operator = businessToolAccountQuerier.getBusinessToolOperator(request.getAccountId());

        // 校验运营工具(多个稿件公用的经营组件，只需要转一次)
        ArchiveCommentConversionComponentBo commentConversionComponentBo =
                businessToolArchiveConversionComponentService.validateBusinessToolThenGenConvUrlForUpdate(operator, request);
        if (CollectionUtils.isEmpty(avids)) {
            return Collections.emptyList();
        }
        log.info("commentConversionComponentBo={}", JSON.toJSONString(commentConversionComponentBo));

        // 并行处理
        List<CompletableFuture<AddCommentComponentReply>> completableFutureList = new ArrayList<>();
        for (Long avid : avids) {
            try {
                CompletableFuture<AddCommentComponentReply> completableFuture = CompletableFuture.supplyAsync(() -> {
                    SingleCreateReply createReply = AdpCatUtils.newTransactionAndReturn(Constants.CAT_TYPE_OTHER,
                            "doUpdateCommentComponentsByParallel", t -> updateSingleCommentComponent(request, operator, avid, commentConversionComponentBo));


                    AddCommentComponentReply.Builder builder;
                    if (createReply.getCode() == 0) {
                        builder = AddCommentComponentReply.newBuilder()
                                .setAvid(avid).setSuccess(true).setMsg("ok")
                                .setCreativeId(createReply.getCreativeId())
                                .setId(createReply.getId());
                    } else {
                        builder = AddCommentComponentReply.newBuilder()
                                .setAvid(avid).setSuccess(false).setMsg(createReply.getMsg())
                                .setCreativeId(createReply.getCreativeId())
                                .setId(createReply.getId());
                    }

                    return builder.build();
                }, businessToolThreadPoolExecutor);

                completableFutureList.add(completableFuture);
            } catch (Exception e) {
                log.error("{}: 更新稿件组件 saveCommentConversionComponent[{}]，触发失败 -> {}", ID, request.getAvidsList(), e.getMessage());
                AddCommentComponentReply commentComponentReply =
                        AddCommentComponentReply.newBuilder().setAvid(avid).setSuccess(false).setMsg(e.getMessage()).build();
                componentReplyList.add(commentComponentReply);
            }
        }
        // 阻塞，等待所有任务执行完成
        CompletableFuture<Void> all = CompletableFuture.allOf(completableFutureList.toArray(new CompletableFuture[0]));

        CompletableFuture<List<AddCommentComponentReply>> alllistCompletableFuture = all.thenApply(t -> completableFutureList.stream().map(a -> {
            try {
                return a.get();
            } catch (InterruptedException | ExecutionException e) {
                log.error("doUpdateCommentComponentsByParallel error, e={}", e);
                AddCommentComponentReply commentComponentReply =
                        AddCommentComponentReply.newBuilder().setSuccess(false).setMsg(e.getMessage()).build();
                return commentComponentReply;
            }
        }).collect(Collectors.toList()));

        try {
            return alllistCompletableFuture.get();
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        } catch (ExecutionException e) {
            log.error("error ", e);
            throw new RuntimeException(e);
        }
    }


    private SingleCreateReply addSingleCommentComponent(AddCommentComponentsReq request,
                                                        Operator businessToolOperator, Long avid,
                                                        ArchiveCommentConversionComponentBo componentBo) {
        String conversionUrl = componentBo.getConversionUrl();
        String iosUrl = componentBo.getIosUrl();
        String androidUrl = componentBo.getAndroidUrl();
        // 校验私信经营工具
        if (ObjectUtils.equals(ClueTypeEnum.MESSAGE.getCode(), componentBo.getClueType())) {
            checkBusinessTool(request, businessToolOperator, avid);
            String sourceBvid = "&source_bvid=" + BVIDUtils.avToBv(avid);
            conversionUrl = conversionUrl + sourceBvid;
            iosUrl = iosUrl + sourceBvid;
            androidUrl = androidUrl + sourceBvid;
        }

        // 线索类型
        ArchiveCommentConversionComponentBo commentConversionComponentBo = ArchiveCommentConversionComponentBo.builder()
                .bizCodeEnum(BizCodeEnum.BUSINESS_TOOL)
                .aid(avid)
                .creativeAccountId(businessToolOperator.getOperatorId())
                .generalCommentText(request.getGeneralCommentText())
                .conversionUrlText(request.getConversionUrlText())
                .componentType(componentBo.getComponentType())
                .textLocation(request.getTextLocationValue())
                .qualificationIds(request.getQualificationIdList())
                .clueData(request.getBusinessToolId())
                .conversionUrlPageId(componentBo.getConversionUrlPageId())
                // 用已经获取的 componentBo 覆盖
                .clueType(componentBo.getClueType())
                .androidAppPackageId(componentBo.getAndroidAppPackageId())
                .iosAppPackageId(componentBo.getIosAppPackageId())
                .androidSchemaUrl(componentBo.getAndroidSchemaUrl())
                .iosSchemaUrl(componentBo.getIosSchemaUrl())
                .iosUrlPageId(componentBo.getIosUrlPageId())
                .androidUrlPageId(componentBo.getAndroidUrlPageId())
                .iosUrlType(componentBo.getIosUrlType())
                .androidUrlType(componentBo.getAndroidUrlType())
                .customizedClickUrl(componentBo.getCustomizedClickUrl())
                .conversionUrl(conversionUrl)
                .androidUrl(androidUrl)
                .iosUrl(iosUrl)
                .shopId(request.getShopId())
                .isAndroidAppDirect(componentBo.getIsAndroidAppDirect())
                .build();
        return businessToolArchiveConversionComponentService.saveCommentConversionComponentForBusinessTool(businessToolOperator,
                commentConversionComponentBo);
    }


    private SingleCreateReply updateSingleCommentComponent(AddCommentComponentsReq request,
                                                           Operator businessToolOperator, Long avid,
                                                           ArchiveCommentConversionComponentBo componentBo) {
        String conversionUrl = componentBo.getConversionUrl();
        String iosUrl = componentBo.getIosUrl();
        String androidUrl = componentBo.getAndroidUrl();
        // 校验私信经营工具
        if (ObjectUtils.equals(ClueTypeEnum.MESSAGE.getCode(), componentBo.getClueType())) {
            checkBusinessTool(request, businessToolOperator, avid);
            String sourceBvid = "&source_bvid=" + BVIDUtils.avToBv(avid);
            conversionUrl = conversionUrl + sourceBvid;
            iosUrl = iosUrl + sourceBvid;
            androidUrl = androidUrl + sourceBvid;
        }

        // 线索类型
        ArchiveCommentConversionComponentBo commentConversionComponentBo = ArchiveCommentConversionComponentBo.builder()
                .bizCodeEnum(BizCodeEnum.BUSINESS_TOOL)
                .aid(avid)
                .creativeAccountId(businessToolOperator.getOperatorId())
                .generalCommentText(request.getGeneralCommentText())
                .conversionUrlText(request.getConversionUrlText())
                .componentType(componentBo.getComponentType())
                .textLocation(request.getTextLocationValue())
                .qualificationIds(request.getQualificationIdList())
                .clueData(request.getBusinessToolId())
                .conversionUrlPageId(componentBo.getConversionUrlPageId())
                // 用已经获取的 componentBo 覆盖
                .clueType(componentBo.getClueType())
                .androidAppPackageId(componentBo.getAndroidAppPackageId())
                .iosAppPackageId(componentBo.getIosAppPackageId())
                .androidSchemaUrl(componentBo.getAndroidSchemaUrl())
                .iosSchemaUrl(componentBo.getIosSchemaUrl())
                .iosUrlPageId(componentBo.getIosUrlPageId())
                .androidUrlPageId(componentBo.getAndroidUrlPageId())
                .iosUrlType(componentBo.getIosUrlType())
                .androidUrlType(componentBo.getAndroidUrlType())
                .customizedClickUrl(componentBo.getCustomizedClickUrl())
                .conversionUrl(conversionUrl)
                .androidUrl(androidUrl)
                .iosUrl(iosUrl)
                .shopId(request.getShopId())
                .isAndroidAppDirect(componentBo.getIsAndroidAppDirect())
                .build();
        return businessToolArchiveConversionComponentService.updateCommentConversionComponentForBusinessTool(businessToolOperator,
                commentConversionComponentBo);
    }

    private void checkBusinessTool(AddCommentComponentsReq request, Operator businessToolOperator, Long avid) {
        log.info("request = {},avid={}", JSON.toJSONString(request), avid);
        //校验是否已有蓝链
        List<Long> avids = new ArrayList<>();
        avids.add(avid);
        QueryCommentComponentsReq queryReq = QueryCommentComponentsReq.newBuilder()
                .setAccountId(request.getAccountId()).setPage(1).setPageSize(10).addAllAvids(avids).build();
        log.info("queryReq={}", JSON.toJSONString(queryReq));
        CommentComponentsReply commentComponentsReply = businessToolArchiveConversionComponentService.listCommentConversionComponentOfAccount(queryReq);
        Assert.isTrue(commentComponentsReply.getTotal() <= 0, "该视频评论区已有商业蓝链");
        Assert.isTrue(Utils.isPositive(request.getShopId()), "该mid未配置客服，请更换mid重试");
    }


    @Override
    public void addDynCommentComponents(AddCommentComponentsReq request, StreamObserver<AddCommentComponentsReply> responseObserver) {

        try {
            List<AddCommentComponentReply> componentReplyList = AdpCatUtils.newTransactionAndReturn(Constants.CAT_TYPE_OTHER, "经营号批量创建稿件评论组件",
                    t -> doAddDynCommentComponentsByParallel(request));

            responseObserver.onNext(AddCommentComponentsReply.newBuilder().addAllList(componentReplyList).build());
            responseObserver.onCompleted();
        } catch (Throwable t) {
            responseObserver.onError(t);
            log.error("{}: 创建图文组件[{}]，触发失败 -> {}", ID, request.getDynamicIdsList(), t.getMessage());
        }
    }


    private List<AddCommentComponentReply> doAddDynCommentComponentsByParallel(AddCommentComponentsReq request) {
        List<Long> dynamicIdsList = request.getDynamicIdsList();
        List<AddCommentComponentReply> componentReplyList = new ArrayList<>();
        Operator operator = businessToolAccountQuerier.getBusinessToolOperator(request.getAccountId());

        // 校验运营工具(多个稿件公用的经营组件，只需要转一次)
        ArchiveCommentConversionComponentBo commentConversionComponentBo =
                businessToolArchiveConversionComponentService.validateBusinessToolThenGenConvUrl(operator, request);
        if (CollectionUtils.isEmpty(dynamicIdsList)) {
            return Collections.emptyList();
        }
        log.info("commentConversionComponentBo={}", JSON.toJSONString(commentConversionComponentBo));

        // 并行处理
        List<CompletableFuture<AddCommentComponentReply>> completableFutureList = new ArrayList<>();
        for (Long dynId : dynamicIdsList) {
            try {
                CompletableFuture<AddCommentComponentReply> completableFuture = CompletableFuture.supplyAsync(() -> {
                    SingleCreateReply createReply = AdpCatUtils.newTransactionAndReturn(Constants.CAT_TYPE_OTHER,
                            "创建single经营平台动态评论组件", t -> addSingleDynCommentComponent(request, operator, dynId, commentConversionComponentBo));

                    AddCommentComponentReply.Builder builder = AddCommentComponentReply.newBuilder()
                            .setDynamicId(dynId).setSuccess(true).setMsg("ok")
                            .setCreativeId(createReply.getCreativeId())
                            .setId(createReply.getId());

                    if (createReply.getCode() > 0) {
                        builder.setSuccess(false);
                        builder.setErrorCode(createReply.getCode());
                        builder.setMsg(createReply.getMsg());
                    }
                    return builder.build();
                }, businessToolThreadPoolExecutor);

                completableFutureList.add(completableFuture);
            } catch (Exception e) {
                log.error("{}: 创建稿件组件 saveCommentConversionComponent[{}]，触发失败 -> {}", ID, request.getDynamicIdsList(), e.getMessage());
                AddCommentComponentReply commentComponentReply =
                        AddCommentComponentReply.newBuilder().setDynamicId(dynId).setSuccess(false).setMsg(e.getMessage()).build();
                componentReplyList.add(commentComponentReply);
            }
        }
        // 阻塞，等待所有任务执行完成
        CompletableFuture<Void> all = CompletableFuture.allOf(completableFutureList.toArray(new CompletableFuture[0]));

        CompletableFuture<List<AddCommentComponentReply>> alllistCompletableFuture = all.thenApply(t -> completableFutureList.stream().map(a -> {
            try {
                return a.get();
            } catch (InterruptedException | ExecutionException e) {
                log.error("doAddCommentComponentsByParallel error, e={}", e);
                AddCommentComponentReply commentComponentReply =
                        AddCommentComponentReply.newBuilder().setSuccess(false).setMsg(e.getMessage()).build();
                return commentComponentReply;
            }
        }).collect(Collectors.toList()));

        try {
            return alllistCompletableFuture.get();
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        } catch (ExecutionException e) {
            throw new RuntimeException(e);
        }
    }

    private SingleCreateReply addSingleDynCommentComponent(AddCommentComponentsReq request,
                                                           Operator businessToolOperator, Long dynId,
                                                           ArchiveCommentConversionComponentBo componentBo) {
        // 校验私信经营工具
        if (ObjectUtils.equals(ClueTypeEnum.MESSAGE.getCode(), componentBo.getClueType())) {
            checkBusinessTool(request, businessToolOperator, dynId);
        }

        // 线索类型
        ArchiveCommentConversionComponentBo commentConversionComponentBo = ArchiveCommentConversionComponentBo.builder()
                .bizCodeEnum(BizCodeEnum.BUSINESS_TOOL)
                .dynamicId(dynId)
                .creativeAccountId(businessToolOperator.getOperatorId())
                .generalCommentText(request.getGeneralCommentText())
                .conversionUrlText(request.getConversionUrlText())
                .componentType(ComponentType.CLUE_VALUE)
                .textLocation(request.getTextLocationValue())
                .qualificationIds(request.getQualificationIdList())
                .clueData(request.getBusinessToolId())
                .conversionUrlPageId(componentBo.getConversionUrlPageId())
                // 用已经获取的 componentBo 覆盖
                .clueType(componentBo.getClueType())
                .conversionUrl(componentBo.getConversionUrl())
                .androidUrl(componentBo.getAndroidUrl())
                .iosUrl(componentBo.getIosUrl())
                .shopId(request.getShopId())
                .build();
        return businessToolArchiveConversionComponentService.saveDynamicCommentConversionComponentForBusinessTool(businessToolOperator,
                commentConversionComponentBo);
    }


    @Override
    public void queryCommentComponents(QueryCommentComponentsReq request, StreamObserver<CommentComponentsReply> responseObserver) {
        try {
            CommentComponentsReply commentComponentsReply = businessToolArchiveConversionComponentService.listCommentConversionComponentOfAccount(request);
            responseObserver.onNext(commentComponentsReply);
            responseObserver.onCompleted();
        } catch (Throwable t) {
            responseObserver.onError(t);
            log.error("{}: 查询稿件组件[{}]，触发失败 -> {}", ID, request.getAccountId(), t.getMessage());
        }
    }

    @Override
    public void queryCommentComponentInfo(QueryCommentComponentInfoReq request, StreamObserver<QueryCommentComponentInfoReply> responseObserver) {
        try {

            QueryCommentComponentInfoReply componentInfoReply = businessToolArchiveConversionComponentService.queryCommentComponentInfo(request);
            responseObserver.onNext(componentInfoReply);
            responseObserver.onCompleted();
        } catch (Throwable t) {
            responseObserver.onError(t);
            log.error("{}: queryCommentComponentInfo[{}]，触发失败 -> {}", ID, request.getAvid(), t.getMessage(), t);
        }
    }

    @Override
    public void deleteComponent(DeleteCompponentRequest request, StreamObserver<UpdateCountReply> responseObserver) {
        try {
            Operator businessToolOperator = businessToolAccountQuerier.getBusinessToolOperator(request.getAccountId());
            Integer count = archiveConversionComponentService.deleteCommentConversionComponent(request.getAvid(),
                    CommentContainerTypeEnum.ARCHIVE_VALUE, businessToolOperator);

            responseObserver.onNext(UpdateCountReply.newBuilder().setCount(count).build());
            responseObserver.onCompleted();
        } catch (Throwable t) {
            responseObserver.onError(t);
            log.error("{}: 删除稿件组件[{}]，触发失败 -> {}", ID, request.getAvid(), t.getMessage());
        }
    }

    @Override
    public void deleteDynComponent(DeleteCompponentRequest request, StreamObserver<UpdateCountReply> responseObserver) {
        try {
            Operator businessToolOperator = businessToolAccountQuerier.getBusinessToolOperator(request.getAccountId());
            Integer count = archiveConversionComponentService.deleteCommentConversionComponent(request.getDynamicId(),
                    CommentContainerTypeEnum.DYNAMIC_VALUE, businessToolOperator);

            responseObserver.onNext(UpdateCountReply.newBuilder().setCount(count).build());
            responseObserver.onCompleted();
        } catch (Throwable t) {
            responseObserver.onError(t);
            log.error("{}: 删除动态组件[{}]，触发失败 -> {}", ID, request.getAvid(), t.getMessage());
        }
    }

    @Override
    @Deprecated
    public void addComponentUpdateLog(AddComponentUpdateLogRequest request, StreamObserver<Empty> responseObserver) {
        try {
            AdpCatUtils.newTransaction(Constants.CAT_TYPE_OTHER, "评论组件修改日志", (transaction -> {
                Operator operator = Operator.builder()
                        .operatorId(request.getOperator().getOperatorId())
                        .operatorName(request.getOperator().getOperatorName())
                        .operatorType(OperatorType.getByCode(request.getOperator().getOperatorTypeValue()))
                        .build();
                CommentComponentUpdateLogBo oldLogBo = ArchiveCommentComponentConverter.MAPPER.convertUpdateLogRpc2Bo(request.getOldLog());
                CommentComponentUpdateLogBo newLogBo = ArchiveCommentComponentConverter.MAPPER.convertUpdateLogRpc2Bo(request.getNewLog());

                if (oldLogBo != null) {
                    oldLogBo.setConversionUrlText(EmojiUtils.filterEmoji(oldLogBo.getConversionUrlText(), "?"));
                    oldLogBo.setGeneralCommentText(EmojiUtils.filterEmoji(oldLogBo.getGeneralCommentText(), "?"));
                    oldLogBo.setGeneralCommentTextExt(EmojiUtils.filterEmoji(oldLogBo.getGeneralCommentTextExt(), "?"));
                }

                if (newLogBo != null) {
                    newLogBo.setConversionUrlText(EmojiUtils.filterEmoji(newLogBo.getConversionUrlText(), "?"));
                    newLogBo.setGeneralCommentText(EmojiUtils.filterEmoji(newLogBo.getGeneralCommentText(), "?"));
                    newLogBo.setGeneralCommentTextExt(EmojiUtils.filterEmoji(newLogBo.getGeneralCommentTextExt(), "?"));
                }
                logOperateForLongService.addUpdateLog(DbTable.LAU_ARCHIVE_COMMENT_CONVERSION_COMPONENT, operator, oldLogBo, newLogBo,
                        request.getCreativeId());
                responseObserver.onNext(Empty.newBuilder().build());
                responseObserver.onCompleted();
            }));
        } catch (Throwable t) {
            responseObserver.onError(t);
            log.error("{}: 添加组件修改日志[{}]，触发失败 -> {}", ID, request.getCreativeId(), t.getMessage());
        }
    }

    @Override
    public void createMessageComponent(CreateMessageComponentReq request, StreamObserver<CreateMessageComponentRes> responseObserver) {
        try {
            CreateMessageComponentDto createMessageComponentDto = new CreateMessageComponentDto();
            createMessageComponentDto.setAccountId(request.getAccountId());
            createMessageComponentDto.setMessageType(Byte.valueOf(String.valueOf(request.getMessageType())));
            createMessageComponentDto.setContent(request.getContent());
            createMessageComponentDto.setImageUrl(request.getImageUrl());
            createMessageComponentDto.setJumpUrl(request.getJumpUrl());
            createMessageComponentDto.setIconUrl(request.getIconUrl());
            createMessageComponentDto.setName(request.getName());
            createMessageComponentDto.setRelatePageId(request.getRelatePageId());
            log.info("createMessageComponentDto = {}", createMessageComponentDto);
            MessageComponentDto messageComponentDto = businessMessageService.createMessageComponent(createMessageComponentDto);
            log.info("messageComponentDto = {}", messageComponentDto);
            CreateMessageComponentRes createMessageComponentRes = CreateMessageComponentRes.newBuilder()
                    .setAccountId(messageComponentDto.getAccountId())
                    .setAdCreativeId(messageComponentDto.getAdCreativeId())
                    .setContent(messageComponentDto.getContent())
                    .setImageUrl(messageComponentDto.getImageUrl())
                    .setJumpUrl(messageComponentDto.getJumpUrl()).build();
            responseObserver.onNext(createMessageComponentRes);
            responseObserver.onCompleted();
        } catch (Throwable t) {
            responseObserver.onError(t);
            log.error("{}: 创建私信组件[{}]，触发失败 -> {}", ID, request.getAccountId(), t.getMessage());
        }
    }

    @Override
    public void queryMessageComponents(QueryMessageComponentsReq request, StreamObserver<QueryMessageComponentsRes> responseObserver) {
        try {
            MessageComponentInquiryQuery messageComponentInquiryQuery = new MessageComponentInquiryQuery();
            messageComponentInquiryQuery.setAccountId(request.getAccountId());
            messageComponentInquiryQuery.setMessageType(Byte.valueOf(String.valueOf(request.getMessageType())));
            messageComponentInquiryQuery.setPageIndex(request.getPageIndex());
            messageComponentInquiryQuery.setPageSize(request.getPageSize());
            messageComponentInquiryQuery.setIncludeDeleted(Byte.valueOf("1").equals(request.getIncludeDeleted()));
            messageComponentInquiryQuery.setRelatePageId(request.getRelatePageId() <= 0L ? null : request.getRelatePageId());
            PageResult<MessageComponentDto> pageRes = businessMessageService.queryMessageComponents(messageComponentInquiryQuery);
            QueryMessageComponentsRes queryMessageComponentsRes = QueryMessageComponentsRes.newBuilder()
                    .setPage(request.getPageIndex())
                    .setTotal(pageRes.getTotal())
                    .addAllCreateMessageComponentRes(convert(pageRes.getRecords())).build();
            responseObserver.onNext(queryMessageComponentsRes);
            responseObserver.onCompleted();
        } catch (Throwable t) {
            responseObserver.onError(t);
            log.error("{}: 查询私信组件[{}]，触发失败 -> {}", ID, request.getAccountId(), t.getMessage());
        }
    }

    private List<CreateMessageComponentRes> convert(List<MessageComponentDto> records) {
        List<CreateMessageComponentRes> createMessageComponentRes = new ArrayList<>();
        records.stream().forEach(record -> {
            CreateMessageComponentRes res = CreateMessageComponentRes.newBuilder()
                    .setId(record.getId())
                    .setAccountId(record.getAccountId())
                    .setAdCreativeId(record.getAdCreativeId())
                    .setContent(record.getContent())
                    .setImageUrl(record.getImageUrl())
                    .setJumpUrl(record.getJumpUrl())
                    .setCtime(Utils.getTimestamp2StringBySecond(new Timestamp(record.getCtime().getTime())))
                    .setMtime(Utils.getTimestamp2StringBySecond(new Timestamp(record.getMtime().getTime())))
                    .setIsDeleted(record.getIsDeleted().intValue())
                    .setName(record.getName())
                    .build();
            createMessageComponentRes.add(res);
        });
        return createMessageComponentRes;
    }

    @Override
    public void batchDeleteComponent(BatchDeleteComponentReq request, StreamObserver<BatchDeleteComponentRes> responseObserver) {
        try {
            Operator businessToolOperator = businessToolAccountQuerier.getBusinessToolOperator(request.getDeleteComponentRequest(0).getAccountId());
            List<Long> avidList = request.getDeleteComponentRequestList().stream().map(DeleteCompponentRequest::getAvid).collect(Collectors.toList());
            List<Long> dynamicIdList = request.getDeleteComponentRequestList().stream().map(DeleteCompponentRequest::getDynamicId).collect(Collectors.toList());
            Map<Long, Integer> resMap = archiveConversionComponentService.batchDeleteCommentConversionComponent(avidList, dynamicIdList, businessToolOperator);
            List<Long> successList = new ArrayList<>();
            List<Long> failedList = new ArrayList<>();
            for (Long avid : avidList) {
                if (!resMap.containsKey(avid)) {
                    failedList.add(avid);
                } else if (Integer.valueOf(1).equals(resMap.get(avid))) {
                    successList.add(avid);
                } else {
                    failedList.add(avid);
                }
            }

            for (Long dynamicId : dynamicIdList) {
                if (!resMap.containsKey(dynamicId)) {
                    failedList.add(dynamicId);
                } else if (Integer.valueOf(1).equals(resMap.get(dynamicId))) {
                    successList.add(dynamicId);
                } else {
                    failedList.add(dynamicId);
                }
            }

            BatchDeleteComponentRes batchDeleteComponentRes = BatchDeleteComponentRes.newBuilder()
                    .addAllFailedAvids(failedList).addAllSuccessAvids(successList).build();
            responseObserver.onNext(batchDeleteComponentRes);
            responseObserver.onCompleted();
        } catch (Throwable t) {
            responseObserver.onError(t);
            log.error("{}: 删除稿件组件[{}]，触发失败 -> {}", ID, request.getDeleteComponentRequestList(), t.getMessage());
        }
    }

    @Override
    public void deleteMessageComponent(DeleteMessageComponentReq request, StreamObserver<DeleteMessageComponentRes> responseObserver) {
        try {
            responseObserver.onNext(DeleteMessageComponentRes.newBuilder().setCount(businessMessageService.deleteMessageComponent(request.getAccountId(), request.getCreativeId()).intValue()).build());
            responseObserver.onCompleted();
        } catch (Throwable t) {
            responseObserver.onError(t);
            log.error("{}: 删除私信组件[{}]，触发失败 -> {}", ID, request.getCreativeId(), t.getMessage());
        }
    }

    @Override
    public void getMessageComponentByCreativeId(GetMessageComponentsReq request, StreamObserver<CreateMessageComponentRes> responseObserver) {
        try {
            MessageComponentDto messageComponentDto = businessMessageService.getMessageComponentByCreativeId(request.getAccountId(), request.getCreativeId());
            log.info("messageComponentDto:{}", JSON.toJSONString(messageComponentDto));
            CreateMessageComponentRes createMessageComponentRes = CreateMessageComponentRes.newBuilder()
                    .setId(messageComponentDto.getId())
                    .setAccountId(messageComponentDto.getAccountId())
                    .setAdCreativeId(messageComponentDto.getAdCreativeId())
                    .setJumpUrl(messageComponentDto.getJumpUrl())
                    .setContent(messageComponentDto.getContent())
                    .setContent(messageComponentDto.getContent())
                    .setIconUrl(messageComponentDto.getIconUrl())
                    .setIsDeleted(messageComponentDto.getIsDeleted())
                    .setName(messageComponentDto.getName())
                    .setCtime(Utils.getTimestamp2StringBySecond(new Timestamp(messageComponentDto.getCtime().getTime())))
                    .setMtime(Utils.getTimestamp2StringBySecond(new Timestamp(messageComponentDto.getMtime().getTime())))
                    .build();
            responseObserver.onNext(createMessageComponentRes);
            responseObserver.onCompleted();
        } catch (Throwable t) {
            responseObserver.onError(t);
            log.error("{}: 查询私信组件[{}]，触发失败 -> {}", ID, request.getCreativeId(), t.getMessage());
        }
    }

    @Override
    public void updateMessageComponentTitle(UpdateMessageComponentReq updateMessageComponentReq, StreamObserver<UpdateMessageComponentRes> responseObserver) {
        try {
            Long res = businessMessageService.updateMessageComponentTitle(updateMessageComponentReq.getPageId(), updateMessageComponentReq.getTitle());
            UpdateMessageComponentRes updateMessageComponentRes = UpdateMessageComponentRes.newBuilder().setCount(res.intValue()).build();
            responseObserver.onNext(updateMessageComponentRes);
            responseObserver.onCompleted();
        } catch (Throwable t) {
            responseObserver.onError(t);
            log.error("{}: 升级私信组件[{}]，触发失败 -> {}", ID, updateMessageComponentReq.getPageId(), t.getMessage());
        }
    }

    @Override
    public void update4NewMessageComponent(Update4NewMessageComponentReq request, StreamObserver<CreateMessageComponentRes> responseObserver) {
        try {
            CreateMessageComponentDto createMessageComponentDto = new CreateMessageComponentDto();
            createMessageComponentDto.setAccountId(request.getCreateMessageComponentReq().getAccountId());
            createMessageComponentDto.setMessageType(Byte.valueOf(String.valueOf(request.getCreateMessageComponentReq().getMessageType())));
            createMessageComponentDto.setJumpUrl(request.getCreateMessageComponentReq().getJumpUrl());
            createMessageComponentDto.setIconUrl(request.getCreateMessageComponentReq().getIconUrl());
            createMessageComponentDto.setName(request.getCreateMessageComponentReq().getName());
            createMessageComponentDto.setRelatePageId(request.getCreateMessageComponentReq().getRelatePageId());
            QueryLandingPageParamDto queryLandingPageParamDto = new QueryLandingPageParamDto();
            queryLandingPageParamDto.setPageIdList(Lists.newArrayList(createMessageComponentDto.getRelatePageId()));
            List<MgkLandingPageDto> mgkLandingPageDtoList = iSoaLandingPageService.getLandingPageDtos(queryLandingPageParamDto);
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(mgkLandingPageDtoList)) {
                MgkLandingPageDto mgkLandingPageDto = mgkLandingPageDtoList.get(0);
                createMessageComponentDto.setContent(mgkLandingPageDto.getTitle());
                createMessageComponentDto.setImageUrl(mgkLandingPageDto.getPageCover());
            }
            MessageComponentDto messageComponentDto = businessMessageService.update4NewMessageComponent(request.getDeleteMessageComponentReq().getAccountId(), request.getDeleteMessageComponentReq().getCreativeId(), createMessageComponentDto);
            CreateMessageComponentRes createMessageComponentRes = CreateMessageComponentRes.newBuilder()
                    .setAccountId(messageComponentDto.getAccountId())
                    .setAdCreativeId(messageComponentDto.getAdCreativeId())
                    .setContent(messageComponentDto.getContent())
                    .setImageUrl(messageComponentDto.getImageUrl())
                    .setJumpUrl(messageComponentDto.getJumpUrl())
                    .build();
            responseObserver.onNext(createMessageComponentRes);
            responseObserver.onCompleted();
        } catch (Throwable t) {
            responseObserver.onError(t);
            log.error("{}: 删除升级私信组件[{}]，触发失败 -> {}", ID, request.getDeleteMessageComponentReq().getCreativeId(), t.getMessage());
        }
    }

    @Override
    public void bizExemptionVerify(BizExemptionVerifyReq request, StreamObserver<BizExemptionVerifyReply> responseObserver) {

        UsersInfoReply reply = adpCpcAccountMidBindingService.getMidUserInfo(Lists.newArrayList(request.getMid()));

        UserInfo userInfo = reply.getInfosMap().getOrDefault(request.getMid(), UserInfo.getDefaultInstance());
        // 豁免类型：1：不豁免，2：豁免
        BizExemptionVerifyReply result = BizExemptionVerifyReply.newBuilder()
                .setExemptionType(blueVRoleSet.contains(userInfo.getOfficialInfo().getRole()) ? 2 : 1)
                .build();
        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    @Override
    public void batchUpdateCommentComponents(ArchiveCommentConversionComponentBatch request, StreamObserver<AddCommentComponentsReply> responseObserver) {
        ArchiveCommentConversionComponentBo archiveCommentConversionComponentBo = ComponentComponentConverter.MAPPER.fromRpcBatchBo(request.getComponentBaseInfo());
        Operator operator = ComponentComponentConverter.MAPPER.fromRpcBatchBo(request.getOperator());
        AddCommentComponentsReply.Builder builder = AddCommentComponentsReply.newBuilder();
        AddCommentComponentReply.Builder newBuilder = AddCommentComponentReply.newBuilder()
                .setAvid(Optional.ofNullable(archiveCommentConversionComponentBo.getAid()).orElse(0L))
                .setDynamicId(Optional.ofNullable(archiveCommentConversionComponentBo.getDynamicId()).orElse(0L));
        try {
            if (ComponentType.GOODS.getNumber() == archiveCommentConversionComponentBo.getComponentType()) {
                archiveConversionComponentService.batchUpdateCommentConversionComponentsAsync(operator, archiveCommentConversionComponentBo, java.util.Collections.singletonList(archiveCommentConversionComponentBo.getAid()), java.util.Collections.singletonList(archiveCommentConversionComponentBo.getDynamicId()));
            } else {
                archiveConversionComponentService.updateCommentConversionComponent(operator, archiveCommentConversionComponentBo);
            }
            newBuilder.setSuccess(true);
        } catch (Exception e) {
            log.error("batchUpdateCommentComponents, e", e);
            newBuilder.setSuccess(false)
                    .setMsg(e.getMessage());
        }
        responseObserver.onNext(builder.addAllList(java.util.Collections.singletonList(newBuilder.build())).build());
        responseObserver.onCompleted();
    }

    @Override
    public void batchCreateCommentComponents(ArchiveCommentConversionComponentBatch request, StreamObserver<AddCommentComponentsReply> responseObserver) {
        ArchiveCommentConversionComponentBo archiveCommentConversionComponentBo = ComponentComponentConverter.MAPPER.fromRpcBatchBo(request.getComponentBaseInfo());
        Operator operator = ComponentComponentConverter.MAPPER.fromRpcBatchBo(request.getOperator());
        AddCommentComponentsReply.Builder builder = AddCommentComponentsReply.newBuilder();
        AddCommentComponentReply.Builder newBuilder = AddCommentComponentReply.newBuilder()
                .setAvid(Optional.ofNullable(archiveCommentConversionComponentBo.getAid()).orElse(0L))
                .setDynamicId(Optional.ofNullable(archiveCommentConversionComponentBo.getDynamicId()).orElse(0L));
        try {
            if (ComponentType.GOODS.getNumber() == archiveCommentConversionComponentBo.getComponentType()) {
                List<Long> aids = new ArrayList<>();
                List<Long> dynamicIds = new ArrayList<>();
                if (archiveCommentConversionComponentBo.getAid() != 0) {
                    aids.add(archiveCommentConversionComponentBo.getAid());
                }
                if (archiveCommentConversionComponentBo.getDynamicId() != 0) {
                    dynamicIds.add(archiveCommentConversionComponentBo.getDynamicId());
                }
                archiveConversionComponentService.batchCreateCommentConversionComponentsAsync(operator, archiveCommentConversionComponentBo, aids, dynamicIds);
            } else {
                archiveConversionComponentService.saveCommentConversionComponent(operator, archiveCommentConversionComponentBo);
            }
            newBuilder.setSuccess(true);
        } catch (Exception e) {
            log.error("batchUpdateCommentComponents, e", e);
            newBuilder.setSuccess(false)
                    .setMsg(e.getMessage());
        }
        responseObserver.onNext(builder.addAllList(java.util.Collections.singletonList(newBuilder.build())).build());
        responseObserver.onCompleted();
    }

    @Override
    public void batchDeleteCommentComponents(ArchiveCommentConversionComponentBatch request, StreamObserver<AddCommentComponentsReply> responseObserver) {
        ArchiveCommentConversionComponentBo archiveCommentConversionComponentBo = ComponentComponentConverter.MAPPER.fromRpcBatchBo(request.getComponentBaseInfo());
        Operator operator = ComponentComponentConverter.MAPPER.fromRpcBatchBo(request.getOperator());
        AddCommentComponentsReply.Builder builder = AddCommentComponentsReply.newBuilder();
        AddCommentComponentReply.Builder newBuilder = AddCommentComponentReply.newBuilder()
                .setAvid(Optional.ofNullable(archiveCommentConversionComponentBo.getAid()).orElse(0L))
                .setDynamicId(Optional.ofNullable(archiveCommentConversionComponentBo.getDynamicId()).orElse(0L));
        try {
            Long containerId = archiveCommentConversionComponentBo.getAid();
            int containerType = CommentContainerTypeEnum.ARCHIVE.getNumber();
            if (Objects.isNull(containerId) || containerId == 0L) {
                containerId = archiveCommentConversionComponentBo.getDynamicId();
                containerType = CommentContainerTypeEnum.DYNAMIC.getNumber();
            }
            archiveConversionComponentService.deleteCommentConversionComponent(containerId, containerType, operator);
            newBuilder.setSuccess(true);
        } catch (Exception e) {
            log.error("batchUpdateCommentComponents, e", e);
            newBuilder.setSuccess(false)
                    .setMsg(e.getMessage());
        }
        responseObserver.onNext(builder.addAllList(java.util.Collections.singletonList(newBuilder.build())).build());
        responseObserver.onCompleted();
    }
}
