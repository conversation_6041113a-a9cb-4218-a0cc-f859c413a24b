package com.bilibili.adp.cpc.splash_screen.creative.bos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SplashScreenCreativeDetail {

    //账户ID
    private Integer accountId;

    //创意ID
    private Integer creativeId;

    //创意名称
    private String creativeName;

    //单元ID
    private Integer unitId;

    //单元名称
    private String unitName;

    //计划ID
    private Integer campaignId;

    //计划名称
    private String  campaignName;

    //广告标志
    private String  busMarkName;

    //投放开始时间
    private String beginTime;

    //投放结束时间
    private String  endTime;

    //素材类型 0-图片 1-视频
    private Integer  materialType;

    //展示时长,单位秒
    private Integer  duration;

    //是否可跳过: 0-否 1-是
    private Integer  isSkip;

    //创意信息
    //按钮类型: 6-全屏滑动 7-扭一扭
    private Integer  interactStyle;

    //按钮类型: 6-全屏滑动 7-扭一扭
    private String  interactStyleDesc;

    //跳转动态图片URL
    private String jumpImageUrl;

    //跳转动态图片Md5
    private String jumpImageMd5;

    //唤起动态图片URL
    private String schemaImageUrl;

    //唤起动态图片Md5
    private String schemaImageMd5;

    //跳转引导文案
    private String jumpGuideContent;

    //唤起引导文案
    private String schemaGuideContent;

    //跳转类型
    private Integer jumpType;

    //跳转类型名称
    private String jumpTypeDesc;

    //跳转链接
    private String promotionPurposeContent;

    //唤起链接
    private String schemeUrl;

    //监控
    //点击监控链接
    private String customizedClickUrl;

    //展示监控链接
    private String customizedImpUrl;

    //定向信息
    private SplashScreenTarget target;
}
