package com.bilibili.adp.cpc.biz.bos.material;

import com.bilibili.adp.cpc.core.constants.MaterialType;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class RejectMaterialBo {
    // lau_material表的id
    private Integer materialId;
    private String type;
    private String rejectReason;
    // 基本等于md5
    private String materialUk;


    public static String AIGC_MATERIAL_TYPE_IMAGE = "img";
    public static String AIGC_MATERIAL_TYPE_TITLE = "title";



    // 目前只有两种，如果后续增加了务必要改这个函数
    public static String LauMaterialType2RejectMaterialType(int materialType) {
        if (materialType == MaterialType.TITLE) {
            return AIGC_MATERIAL_TYPE_TITLE;
        }
        if (materialType == MaterialType.IMAGE) {
            return AIGC_MATERIAL_TYPE_IMAGE;
        }
        throw new IllegalArgumentException("不支持的类型: " + materialType);
    }

    public static int RejectMaterialType2LauMaterialType(String type) {
        if (type.equals(AIGC_MATERIAL_TYPE_TITLE)) {
            return MaterialType.TITLE;
        }
        if (type.equals(AIGC_MATERIAL_TYPE_IMAGE)) {
            return MaterialType.IMAGE;
        }
        throw new IllegalArgumentException("不支持的类型: " + type);
    }
}
