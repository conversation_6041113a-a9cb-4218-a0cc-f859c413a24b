package com.bilibili.adp.cpc.pluto.converter;

import com.bapis.ad.pluto.service.*;
import com.bilibili.adp.cpc.biz.bos.report.StatBo;
import com.bilibili.adp.cpc.pluto.service.LaunchReportV1Service;
import lombok.RequiredArgsConstructor;

import java.math.BigDecimal;
import java.math.RoundingMode;

@RequiredArgsConstructor
public abstract class StatConverterDecorator implements StatConverter {
    private final StatConverter delegate;

    /**
     * 这段代码存在的意义
     * 1.率相关的指标 ✖ 100 保留两位小数返回前端
     * 2.成本相关的指标 保留两位小数返回给前端
     */
    @Override
    public StatBo toBo(Stat stat) {
        StatBo statBo = delegate.toBo(stat);
        //todo 临时处理 废弃字段设置为0
        statBo.setIosAppActiveCount(0);
        statBo.setAndroidAppActiveCount(0);
        statBo.setDynamicDetailPageBrowseCount(0);
        statBo.setActivityPagePullUpCount(0);
        statBo.setActivateRate(LaunchReportV1Service.genRate(BigDecimal.valueOf(stat.getAppActiveRate())));
        statBo.setCostPerActivate(BigDecimal.valueOf(stat.getCostPerAppActive()).setScale(2, RoundingMode.HALF_UP));
        statBo.setFormPaidRate(LaunchReportV1Service.genRate(BigDecimal.valueOf(stat.getFormUserCostRate())));
        statBo.setCostPerFormPaid(BigDecimal.valueOf(stat.getCostPerFormUserCost()).setScale(2, RoundingMode.HALF_UP));
        statBo.setPlayRate(LaunchReportV1Service.genRate(BigDecimal.valueOf(stat.getVideoPlayRate())));
        statBo.setCostPerPlay(BigDecimal.valueOf(stat.getCostPerVideoPlay()).setScale(2, RoundingMode.HALF_UP));
        statBo.setFirstCommentRate(LaunchReportV1Service.genRate(BigDecimal.valueOf(stat.getFirstCommentCopyRate())));
        statBo.setCostPerFirstComment(BigDecimal.valueOf(stat.getCostPerFirstCommentCopy()).setScale(2, RoundingMode.HALF_UP).setScale(2, RoundingMode.HALF_UP));
        statBo.setCommentShowRate(LaunchReportV1Service.genRate(BigDecimal.valueOf(stat.getCommentUrlShowRate())));
        statBo.setCommentShowClickRate(LaunchReportV1Service.genRate(BigDecimal.valueOf(stat.getCommentUrlShowClickRate())));
        statBo.setAndroidInstallSuccessRate(LaunchReportV1Service.genRate(BigDecimal.valueOf(stat.getInstallSuccessRate())));
        statBo.setCostPerAndroidInstallSuccess(BigDecimal.valueOf(stat.getCostPerInstallSuccess()).setScale(2, RoundingMode.HALF_UP));
        statBo.setAndroidDownloadSuccessRate(LaunchReportV1Service.genRate(BigDecimal.valueOf(stat.getDownloadSuccessRate())));
        statBo.setCostPerAndroidDownloadSuccess(BigDecimal.valueOf(stat.getCostPerDownloadSuccess()).setScale(2, RoundingMode.HALF_UP));
        statBo.setCallUpSuccessOrderRate(LaunchReportV1Service.genRate(BigDecimal.valueOf(stat.getLpCallUpSuccessOrderPlaceRate())));
        statBo.setPlay2FansRate(LaunchReportV1Service.genRate(BigDecimal.valueOf(stat.getVideoPlayFanIncreaseRate())));
        statBo.setPlayCallUpRate(LaunchReportV1Service.genRate(BigDecimal.valueOf(stat.getVideoPlayLpCallUpSuccessRate())));
        statBo.setComponentClickCost(BigDecimal.valueOf(stat.getCostPerComponentClick()).setScale(2, RoundingMode.HALF_UP));
        statBo.setAndroidGameCenterActivationRate(LaunchReportV1Service.genRate(BigDecimal.valueOf(stat.getGameActiveApiRate())));
        statBo.setCostPerAndroidGameCenterActivation(BigDecimal.valueOf(stat.getCostPerGameActiveApi()).setScale(2, RoundingMode.HALF_UP));
        statBo.setNewFirstDayPayAmount(BigDecimal.valueOf(stat.getFirstDayRoiAmount()).setScale(2, RoundingMode.HALF_UP));
        statBo.setNewFirstDayPayRate(LaunchReportV1Service.genRate(BigDecimal.valueOf(stat.getFirstDayRoiRate())));
        statBo.setNewFirstDayPayCost(BigDecimal.valueOf(stat.getCostPerFirstDayRoi()).setScale(2, RoundingMode.HALF_UP));
        statBo.setNewFirstDayPayRoi(BigDecimal.valueOf(stat.getFirstDayRoiRoi()));
        statBo.setPaidIn24HourAmount(BigDecimal.valueOf(stat.getPaidIn24HAmount()).setScale(2, RoundingMode.HALF_UP));
        statBo.setCostPerPaidIn24Hour(BigDecimal.valueOf(stat.getCostPerPaidIn24H()).setScale(2, RoundingMode.HALF_UP));
        statBo.setPaidIn24HourRoi(BigDecimal.valueOf(stat.getPaidIn24HRoi()));
        statBo.setPaidIn7DayAmount(BigDecimal.valueOf(stat.getPaidIn7DAmount()).setScale(2, RoundingMode.HALF_UP));
        statBo.setCostPerPaidIn7Day(BigDecimal.valueOf(stat.getCostPerPaidIn7D()).setScale(2, RoundingMode.HALF_UP));
        statBo.setPaidIn7DayRoi(BigDecimal.valueOf(stat.getPaidIn7DRoi()));
        statBo.setAndroidGameActivePaidIn24hAmount(BigDecimal.valueOf(stat.getGamePaidIn24HAmount()).setScale(2, RoundingMode.HALF_UP));
        statBo.setAndroidGameActivePaidIn24hCost(BigDecimal.valueOf(stat.getCostPerGamePaidIn24H()).setScale(2, RoundingMode.HALF_UP));
        statBo.setAndroidGameActivePaidIn24hRoi(BigDecimal.valueOf(stat.getGamePaidIn24HRoi()));
        statBo.setAndroidGameActivePaidIn7dAmount(BigDecimal.valueOf(stat.getGamePaidIn7DAmount()).setScale(2, RoundingMode.HALF_UP));
        statBo.setAndroidGameActivePaidIn7dCost(BigDecimal.valueOf(stat.getCostPerGamePaidIn7D()).setScale(2, RoundingMode.HALF_UP));
        statBo.setAndroidGameActivePaidIn7dRoi(BigDecimal.valueOf(stat.getGamePaidIn7DRoi()));
        statBo.setAndroidGameActivePaidIn1dAmount(BigDecimal.valueOf(stat.getGameFirstDayRoiAmount()).setScale(2, RoundingMode.HALF_UP));
        statBo.setAndroidGameActivePaidIn1dRate(LaunchReportV1Service.genRate(BigDecimal.valueOf(stat.getGameFirstDayRoiRate())));
        statBo.setAndroidGameActivePaidIn1dCost(BigDecimal.valueOf(stat.getCostPerGameFirstDayRoi()).setScale(2, RoundingMode.HALF_UP));
        statBo.setAndroidGameActivePaidIn1dRoi(BigDecimal.valueOf(stat.getGameFirstDayRoiRoi()));
        statBo.setLiveCallUpRate(LaunchReportV1Service.genRate(BigDecimal.valueOf(stat.getLiveClickRate())));
        statBo.setCostPerLiveCallUp(BigDecimal.valueOf(stat.getCostPerLiveClick()).setScale(2, RoundingMode.HALF_UP));
        statBo.setLiveGameCardClickCost(BigDecimal.valueOf(stat.getCostPerLiveGameCardClick()).setScale(2, RoundingMode.HALF_UP));
        statBo.setFirstWithdrawCost(BigDecimal.valueOf(stat.getCostPerFirstWithdraw()).setScale(2, RoundingMode.HALF_UP));
        statBo.setChargedCostMilli(stat.getChargedCostMilli());
        statBo.setCost(BigDecimal.valueOf(stat.getCost()).setScale(2, RoundingMode.HALF_UP));
        statBo.setIntelligentCost(BigDecimal.valueOf(stat.getIntelligentCost()).setScale(2, RoundingMode.HALF_UP));
        statBo.setIntelligentCostRate(LaunchReportV1Service.genRate(BigDecimal.valueOf(stat.getIntelligentCostRate())));
        statBo.setOrderPlaceAmount(BigDecimal.valueOf(stat.getOrderPlaceAmount()).setScale(2, RoundingMode.HALF_UP));
        statBo.setFirstOrderPlaceAmount(BigDecimal.valueOf(stat.getFirstOrderPlaceAmount()).setScale(2, RoundingMode.HALF_UP));
        statBo.setFirstDayPayAmount(BigDecimal.valueOf(stat.getFirstDayPayAmount()).setScale(2, RoundingMode.HALF_UP));
        statBo.setUserCostAmount(BigDecimal.valueOf(stat.getUserCostAmount()).setScale(2, RoundingMode.HALF_UP));
        statBo.setUserFirstCostAmount(BigDecimal.valueOf(stat.getUserFirstCostAmount()).setScale(2, RoundingMode.HALF_UP));
        statBo.setGameUserCostAmount(BigDecimal.valueOf(stat.getGameUserCostAmount()).setScale(2, RoundingMode.HALF_UP));
        statBo.setGameUserFirstCostAmount(BigDecimal.valueOf(stat.getGameUserFirstCostAmount()).setScale(2, RoundingMode.HALF_UP));
        statBo.setClickThroughRate(LaunchReportV1Service.genRate(BigDecimal.valueOf(stat.getClickThroughRate())));
        statBo.setCostPerMille(BigDecimal.valueOf(stat.getCostPerMille()).setScale(2, RoundingMode.HALF_UP));
        statBo.setCostPerClick(BigDecimal.valueOf(stat.getCostPerClick()).setScale(2, RoundingMode.HALF_UP));
        statBo.setGoodsConversionRate(LaunchReportV1Service.genRate(BigDecimal.valueOf(stat.getGoodsConversionRate())));
        statBo.setGoodsRoi(BigDecimal.valueOf(stat.getGoodsRoi()));
        statBo.setOrderPlaceRate(LaunchReportV1Service.genRate(BigDecimal.valueOf(stat.getOrderPlaceRate())));
        statBo.setCostPerOrderPlace(BigDecimal.valueOf(stat.getCostPerOrderPlace()).setScale(2, RoundingMode.HALF_UP));
        statBo.setFirstOrderPlaceRate(LaunchReportV1Service.genRate(BigDecimal.valueOf(stat.getFirstOrderPlaceRate())));
        statBo.setCostPerFirstOrderPlace(BigDecimal.valueOf(stat.getCostPerFirstOrderPlace()).setScale(2, RoundingMode.HALF_UP));
        statBo.setFirstDayPayRate(LaunchReportV1Service.genRate(BigDecimal.valueOf(stat.getFirstDayPayRate())));
        statBo.setCostPerFirstDayPay(BigDecimal.valueOf(stat.getCostPerFirstDayPay()).setScale(2, RoundingMode.HALF_UP));
        statBo.setFirstDayPayRoi(BigDecimal.valueOf(stat.getFirstDayPayRoi()));
        statBo.setUserFirstCostRate(LaunchReportV1Service.genRate(BigDecimal.valueOf(stat.getUserFirstCostRate())));
        statBo.setCostPerUserFirstCost(BigDecimal.valueOf(stat.getCostPerUserFirstCost()).setScale(2, RoundingMode.HALF_UP));
        statBo.setGameUserFirstCostRate(LaunchReportV1Service.genRate(BigDecimal.valueOf(stat.getGameUserFirstCostRate())));
        statBo.setCostPerGameUserFirstCost(BigDecimal.valueOf(stat.getCostPerGameUserFirstCost()).setScale(2, RoundingMode.HALF_UP));
        statBo.setActionValidRate(LaunchReportV1Service.genRate(BigDecimal.valueOf(stat.getActionValidRate())));
        statBo.setCostPerActionValid(BigDecimal.valueOf(stat.getCostPerActionValid()).setScale(2, RoundingMode.HALF_UP));
        statBo.setGameSubscribeApiRate(LaunchReportV1Service.genRate(BigDecimal.valueOf(stat.getGameSubscribeApiRate())));
        statBo.setCostPerGameSubscribeApi(BigDecimal.valueOf(stat.getCostPerGameSubscribeApi()).setScale(2, RoundingMode.HALF_UP));
        statBo.setFanIncreaseRate(LaunchReportV1Service.genRate(BigDecimal.valueOf(stat.getFanIncreaseRate())));
        statBo.setCostPerFanIncrease(BigDecimal.valueOf(stat.getCostPerFanIncrease()).setScale(2, RoundingMode.HALF_UP));
        statBo.setAppActiveRate(LaunchReportV1Service.genRate(BigDecimal.valueOf(stat.getAppActiveRate())));
        statBo.setCostPerAppActive(BigDecimal.valueOf(stat.getCostPerAppActive()).setScale(2, RoundingMode.HALF_UP));
        statBo.setUserRegisterRate(LaunchReportV1Service.genRate(BigDecimal.valueOf(stat.getUserRegisterRate())));
        statBo.setCostPerUserRegister(BigDecimal.valueOf(stat.getCostPerUserRegister()).setScale(2, RoundingMode.HALF_UP));
        statBo.setFormSubmitRate(LaunchReportV1Service.genRate(BigDecimal.valueOf(stat.getFormSubmitRate())));
        statBo.setCostPerFormSubmit(BigDecimal.valueOf(stat.getCostPerFormSubmit()).setScale(2, RoundingMode.HALF_UP));
        statBo.setClueValidRate(LaunchReportV1Service.genRate(BigDecimal.valueOf(stat.getClueValidRate())));
        statBo.setCostPerClueValid(BigDecimal.valueOf(stat.getCostPerClueValid()).setScale(2, RoundingMode.HALF_UP));
        statBo.setRetentionRate(LaunchReportV1Service.genRate(BigDecimal.valueOf(stat.getRetentionRate())));
        statBo.setCostPerRetention(BigDecimal.valueOf(stat.getCostPerRetention()).setScale(2, RoundingMode.HALF_UP));
        statBo.setAppCallUpRate(LaunchReportV1Service.genRate(BigDecimal.valueOf(stat.getAppCallUpRate())));
        statBo.setCostPerAppCallUp(BigDecimal.valueOf(stat.getCostPerAppCallUp()).setScale(2, RoundingMode.HALF_UP));
        statBo.setLpCallUpRate(LaunchReportV1Service.genRate(BigDecimal.valueOf(stat.getLpCallUpRate())));
        statBo.setCostPerLpCallUp(BigDecimal.valueOf(stat.getCostPerLpCallUp()).setScale(2, RoundingMode.HALF_UP));
        statBo.setLpCallUpSuccessRate(LaunchReportV1Service.genRate(BigDecimal.valueOf(stat.getLpCallUpSuccessRate())));
        statBo.setCostPerLpCallUpSuccess(BigDecimal.valueOf(stat.getCostPerLpCallUpSuccess()).setScale(2, RoundingMode.HALF_UP));
        statBo.setLpCallUpSuccessStayRate(LaunchReportV1Service.genRate(BigDecimal.valueOf(stat.getLpCallUpSuccessStayRate())));
        statBo.setCostPerLpCallUpSuccessStay(BigDecimal.valueOf(stat.getCostPerLpCallUpSuccessStay()).setScale(2, RoundingMode.HALF_UP));
        statBo.setAccountSubscribeRate(LaunchReportV1Service.genRate(BigDecimal.valueOf(stat.getAccountSubscribeRate())));
        statBo.setCostPerAccountSubscribe(BigDecimal.valueOf(stat.getCostPerAccountSubscribe()).setScale(2, RoundingMode.HALF_UP));
        statBo.setUnderBoxLinkRate(LaunchReportV1Service.genRate(BigDecimal.valueOf(stat.getUnderBoxLinkRate())));
        statBo.setCostPerUnderBoxLink(BigDecimal.valueOf(stat.getCostPerUnderBoxLink()).setScale(2, RoundingMode.HALF_UP));
        statBo.setCommentUrlClickRate(LaunchReportV1Service.genRate(BigDecimal.valueOf(stat.getCommentUrlClickRate())));
        statBo.setCostPerCommentUrlClick(BigDecimal.valueOf(stat.getCostPerCommentUrlClick()).setScale(2, RoundingMode.HALF_UP));
        statBo.setWxCopyRate(LaunchReportV1Service.genRate(BigDecimal.valueOf(stat.getWxCopyRate())));
        statBo.setCostPerWxCopy(BigDecimal.valueOf(stat.getCostPerWxCopy()).setScale(2, RoundingMode.HALF_UP));
        statBo.setApplyRate(LaunchReportV1Service.genRate(BigDecimal.valueOf(stat.getApplyRate())));
        statBo.setCostPerApply(BigDecimal.valueOf(stat.getCostPerApply()).setScale(2, RoundingMode.HALF_UP));
        statBo.setCreditRate(LaunchReportV1Service.genRate(BigDecimal.valueOf(stat.getCreditRate())));
        statBo.setCostPerCredit(BigDecimal.valueOf(stat.getCostPerCredit()).setScale(2, RoundingMode.HALF_UP));
        statBo.setWithdrawDepositsRate(LaunchReportV1Service.genRate(BigDecimal.valueOf(stat.getWithdrawDepositsRate())));
        statBo.setCostPerWithdrawDeposits(BigDecimal.valueOf(stat.getCostPerWithdrawDeposits()).setScale(2, RoundingMode.HALF_UP));
        statBo.setLiveEntryRate(LaunchReportV1Service.genRate(BigDecimal.valueOf(stat.getLiveEntryRate())));
        statBo.setLiveReserveRate(LaunchReportV1Service.genRate(BigDecimal.valueOf(stat.getLiveReserveRate())));
        statBo.setCostPerLiveEntry(BigDecimal.valueOf(stat.getCostPerLiveEntry()).setScale(2, RoundingMode.HALF_UP));
        statBo.setCostPerLiveReserve(BigDecimal.valueOf(stat.getCostPerLiveReserve()).setScale(2, RoundingMode.HALF_UP));
        statBo.setWxAddFansRate(LaunchReportV1Service.genRate(BigDecimal.valueOf(stat.getWxAddFansRate())));
        statBo.setCostPerWxAddFans(BigDecimal.valueOf(stat.getCostPerWxAddFans()).setScale(2, RoundingMode.HALF_UP));
        statBo.setCommentCallUpSuccessRate(LaunchReportV1Service.genRate(BigDecimal.valueOf(stat.getCommentCallUpSuccessRate())));
        statBo.setCostPerCommentCallUpSuccess(BigDecimal.valueOf(stat.getCostPerCommentCallUpSuccess()).setScale(2, RoundingMode.HALF_UP));
        statBo.setFirstWithdrawRate(LaunchReportV1Service.genRate(BigDecimal.valueOf(stat.getFirstWithdrawRate())));
        statBo.setComponentClickRate(LaunchReportV1Service.genRate(BigDecimal.valueOf(stat.getComponentClickRate())));
        statBo.setLiveGameCardClickRate(LaunchReportV1Service.genRate(BigDecimal.valueOf(stat.getLiveGameCardClickRate())));
        statBo.setCardOpenRate(LaunchReportV1Service.genRate(BigDecimal.valueOf(stat.getCardOpenRate())));
        statBo.setCostPerCardOpen(BigDecimal.valueOf(stat.getCostPerCardOpen()).setScale(2, RoundingMode.HALF_UP));
        statBo.setVideoLikeRate(LaunchReportV1Service.genRate(BigDecimal.valueOf(stat.getVideoLikeRate())));
        statBo.setCostPerVideoLike(BigDecimal.valueOf(stat.getCostPerVideoLike()).setScale(2, RoundingMode.HALF_UP));
        statBo.setVideoFavRate(LaunchReportV1Service.genRate(BigDecimal.valueOf(stat.getVideoFavRate())));
        statBo.setCostPerVideoFav(BigDecimal.valueOf(stat.getCostPerVideoFav()).setScale(2, RoundingMode.HALF_UP));
        statBo.setVideoShareRate(LaunchReportV1Service.genRate(BigDecimal.valueOf(stat.getVideoShareRate())));
        statBo.setCostPerVideoShare(BigDecimal.valueOf(stat.getCostPerVideoShare()).setScale(2, RoundingMode.HALF_UP));
        statBo.setVideoBulletRate(LaunchReportV1Service.genRate(BigDecimal.valueOf(stat.getVideoBulletRate())));
        statBo.setCostPerVideoBullet(BigDecimal.valueOf(stat.getCostPerVideoBullet()).setScale(2, RoundingMode.HALF_UP));
        statBo.setVideoReplyRate(LaunchReportV1Service.genRate(BigDecimal.valueOf(stat.getVideoReplyRate())));
        statBo.setCostPerVideoReply(BigDecimal.valueOf(stat.getCostPerVideoReply()).setScale(2, RoundingMode.HALF_UP));
        statBo.setVideoCoinRate(LaunchReportV1Service.genRate(BigDecimal.valueOf(stat.getVideoCoinRate())));
        statBo.setCostPerVideoCoin(BigDecimal.valueOf(stat.getCostPerVideoCoin()).setScale(2, RoundingMode.HALF_UP));
        statBo.setVideoInteractRate(LaunchReportV1Service.genRate(BigDecimal.valueOf(stat.getVideoInteractRate())));
        statBo.setCostPerVideoInteract(BigDecimal.valueOf(stat.getCostPerVideoInteract()).setScale(2, RoundingMode.HALF_UP));
        statBo.setNotAutoPlayClickThroughRate(LaunchReportV1Service.genRate(BigDecimal.valueOf(stat.getNotAutoPlayClickThroughRate())));

        statBo.setGameChargeIn24HAmount(BigDecimal.valueOf(stat.getGameChargeIn24HAmount()).setScale(2, RoundingMode.HALF_UP));
        statBo.setCostPerGameChargeIn24H(BigDecimal.valueOf(stat.getCostPerGameChargeIn24H()).setScale(2, RoundingMode.HALF_UP));

        statBo.setGameChargeIn1DAmount(BigDecimal.valueOf(stat.getGameChargeIn1DAmount()).setScale(2, RoundingMode.HALF_UP));
        statBo.setCostPerGameChargeIn1D(BigDecimal.valueOf(stat.getCostPerGameChargeIn1D()).setScale(2, RoundingMode.HALF_UP));

        statBo.setGameChargeIn7DAmount(BigDecimal.valueOf(stat.getGameChargeIn7DAmount()).setScale(2, RoundingMode.HALF_UP));
        statBo.setCostPerGameChargeIn7D(BigDecimal.valueOf(stat.getCostPerGameChargeIn7D()).setScale(2, RoundingMode.HALF_UP));

        statBo.setOrderGmv(BigDecimal.valueOf(stat.getOrderGmv()).setScale(2, RoundingMode.HALF_UP));
        statBo.setDealGmv(BigDecimal.valueOf(stat.getDealGmv()).setScale(2, RoundingMode.HALF_UP));

        statBo.setMessageChatRate(LaunchReportV1Service.genRate(BigDecimal.valueOf(stat.getMessageChatRate())));
        statBo.setCostPerMessageChat(BigDecimal.valueOf(stat.getCostPerMessageChat()).setScale(2, RoundingMode.HALF_UP));

        statBo.setMessageLeadRate(LaunchReportV1Service.genRate(BigDecimal.valueOf(stat.getMessageLeadRate())));

        statBo.setGameChargeIn14dCount(stat.getGameChargeIn14DCount());
        statBo.setGameChargeIn14dAmount(BigDecimal.valueOf(stat.getGameChargeIn14DAmount()).setScale(2, RoundingMode.HALF_UP));
        statBo.setCostPerGameChargeIn14d(BigDecimal.valueOf(stat.getCostPerGameChargeIn14D()).setScale(2, RoundingMode.HALF_UP));
        statBo.setGameChargeIn14dRoi(BigDecimal.valueOf(stat.getGameChargeIn14DRoi()));

        statBo.setGameChargeIn30dCount(stat.getGameChargeIn30DCount());
        statBo.setGameChargeIn30dAmount(BigDecimal.valueOf(stat.getGameChargeIn30DAmount()).setScale(2, RoundingMode.HALF_UP));
        statBo.setCostPerGameChargeIn30d(BigDecimal.valueOf(stat.getCostPerGameChargeIn30D()).setScale(2, RoundingMode.HALF_UP));
        statBo.setGameChargeIn30dRoi(BigDecimal.valueOf(stat.getGameChargeIn30DRoi()));

        statBo.setPaidIn14dCount(stat.getPaidIn14DCount());
        statBo.setPaidIn14dAmount(BigDecimal.valueOf(stat.getPaidIn14DAmount()).setScale(2, RoundingMode.HALF_UP));
        statBo.setCostPerPaidIn14d(BigDecimal.valueOf(stat.getCostPerPaidIn14D()).setScale(2, RoundingMode.HALF_UP));
        statBo.setPaidIn14dRoi(BigDecimal.valueOf(stat.getPaidIn14DRoi()));

        statBo.setPaidIn30dCount(stat.getPaidIn30DCount());
        statBo.setPaidIn30dAmount(BigDecimal.valueOf(stat.getPaidIn30DAmount()).setScale(2, RoundingMode.HALF_UP));
        statBo.setCostPerPaidIn30d(BigDecimal.valueOf(stat.getCostPerPaidIn30D()).setScale(2, RoundingMode.HALF_UP));
        statBo.setPaidIn30dRoi(BigDecimal.valueOf(stat.getPaidIn30DRoi()));

        statBo.setGameChargeIn24hMixAmount(BigDecimal.valueOf(stat.getGameChargeIn24HMixAmount()).setScale(2, RoundingMode.HALF_UP));
        statBo.setGameChargeIn24hMixRoi(BigDecimal.valueOf(stat.getGameChargeIn24HMixRoi()));
        statBo.setGameChargeIn1dMixAmount(BigDecimal.valueOf(stat.getGameChargeIn1DMixAmount()).setScale(2, RoundingMode.HALF_UP));
        statBo.setGameChargeIn1dMixRoi(BigDecimal.valueOf(stat.getGameChargeIn1DMixRoi()));
        statBo.setGameChargeIn7dMixAmount(BigDecimal.valueOf(stat.getGameChargeIn7DMixAmount()).setScale(2, RoundingMode.HALF_UP));
        statBo.setGameChargeIn7dMixRoi(BigDecimal.valueOf(stat.getGameChargeIn7DMixRoi()));

        statBo.setGameChargeIn14dMixAmount(BigDecimal.valueOf(stat.getGameChargeIn14DMixAmount()).setScale(2, RoundingMode.HALF_UP));
        statBo.setGameChargeIn14dMixRoi(BigDecimal.valueOf(stat.getGameChargeIn14DMixRoi()));
        statBo.setGameChargeIn30dMixAmount(BigDecimal.valueOf(stat.getGameChargeIn30DMixAmount()).setScale(2, RoundingMode.HALF_UP));
        statBo.setGameChargeIn30dMixRoi(BigDecimal.valueOf(stat.getGameChargeIn30DMixRoi()));

        statBo.setRetention3dCount(stat.getRetention3DCount());
        statBo.setRetention3dRate(LaunchReportV1Service.genRate(BigDecimal.valueOf(stat.getRetention3DRate())));
        statBo.setCostPerRetention3d(BigDecimal.valueOf(stat.getCostPerRetention3D()).setScale(2, RoundingMode.HALF_UP));

        statBo.setRetention5dCount(stat.getRetention5DCount());
        statBo.setRetention5dRate(LaunchReportV1Service.genRate(BigDecimal.valueOf(stat.getRetention5DRate())));
        statBo.setCostPerRetention5d(BigDecimal.valueOf(stat.getCostPerRetention5D()).setScale(2, RoundingMode.HALF_UP));

        statBo.setRetention7dCount(stat.getRetention7DCount());
        statBo.setRetention7dRate(LaunchReportV1Service.genRate(BigDecimal.valueOf(stat.getRetention7DRate())));
        statBo.setCostPerRetention7d(BigDecimal.valueOf(stat.getCostPerRetention7D()).setScale(2, RoundingMode.HALF_UP));

        statBo.setRetentionDaysCount(stat.getRetentionDaysCount());
        statBo.setRetentionDaysRate(LaunchReportV1Service.genRate(BigDecimal.valueOf(stat.getRetentionDaysRate())));
        statBo.setCostPerRetentionDays(BigDecimal.valueOf(stat.getCostPerRetentionDays()).setScale(2, RoundingMode.HALF_UP));

        statBo.setOpenActionValidCount(stat.getOpenActionValidCount());
        statBo.setCostPerOpenActionValid(BigDecimal.valueOf(stat.getCostPerOpenActionValid()).setScale(2, RoundingMode.HALF_UP));

        return statBo;
    }

    @Override
    public StatBo toBo(AccountStat accountStat) {
        StatBo accountStatBo = delegate.toBo(accountStat);
        StatBo statBo = this.toBo(accountStat.getStat());
        delegate.updateField(accountStatBo, statBo);
        return accountStatBo;
    }

    @Override
    public StatBo toBo(CampaignStat campaignStat) {
        StatBo campaignStatBo = delegate.toBo(campaignStat);
        StatBo statBo = this.toBo(campaignStat.getStat());
        delegate.updateField(campaignStatBo, statBo);
        return campaignStatBo;
    }

    @Override
    public StatBo toBo(UnitStat unitStat) {
        StatBo unitStatBo = delegate.toBo(unitStat);
        StatBo statBo = this.toBo(unitStat.getStat());
        delegate.updateField(unitStatBo, statBo);
        return unitStatBo;
    }

    @Override
    public StatBo toBo(CreativeStat creativeStat) {
        StatBo creativeStatBo = delegate.toBo(creativeStat);
        StatBo statBo = this.toBo(creativeStat.getStat());
        delegate.updateField(creativeStatBo, statBo);
        return creativeStatBo;
    }
}
