package com.bilibili.adp.cpc.config;

import com.bilibili.adp.cpc.utils.DummyTrustManager;
import com.bilibili.adp.cpc.utils.HttpUtils;
import com.bilibili.adp.launch.api.common.LaunchConstant;
import com.bilibili.bjcom.querydsl.BaseQueryFactory;
import com.bilibili.bjcom.querydsl.support.CatSQLLogger;
import com.bilibili.bjcom.querydsl.support.ConnectionProvider;
import com.bilibili.bjcom.querydsl.support.SafeSQLListener;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.querydsl.sql.MySQLTemplates;
import com.querydsl.sql.SQLTemplates;
import com.querydsl.sql.spring.SpringExceptionTranslator;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import okhttp3.OkHttpClient;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.javers.core.Javers;
import org.javers.core.JaversBuilder;
import org.javers.core.diff.ListCompareAlgorithm;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import javax.annotation.PostConstruct;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import javax.sql.DataSource;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.time.Duration;
import java.util.concurrent.ThreadPoolExecutor;

@Configuration
public class AppConfig {
    public static final String BUSINESS_AD_LOG_MYBATIS = "logTransactionManager";
    public static final String CPC_CLICKHOUSE_SYCPB2 = "clickhouseDataSource";
    public static final String CPC_CLICKHOUSE_ADP = "clickhouseAdpDataSource";
    public static final String CPC_CLICKHOUSE_BQF = "cpcClickhouseBqf";
    public static final String HEADER_KEY = "open-ad-account-id";
    public static final String SR_SESSION_KEY = "sr_session";
    public static final String OBJECT_MAPPER = "om";
    public static final String CPC_CLICKHOUSE_SYCPB_OFFLINE = "clickhouseOfflineDataSource";
    public static final String CPC_CLICKHOUSE_SYCPB_OFFLINE_BQF = "cpcClickhouseOfflineBqf";
    public static final String CPC_CLICKHOUSE_SYCPB_ADP_BQF = "cpcClickhouseAdpBqf";

    public static final String AD_BUSINESS_CPC_DATA_SOURCE = "adBusinessCpcDataSource";
    public static final String AD_BUSINESS_CPC_BQF = "adBusinessCpcBQF";
    public static final String AD_BUSINESS_CPC_TM = "adBusinessCpcTransactionManager";

    public static final String CRM_BQF = "crmBQF";
    public static final String CRM_CP = "crmDataSource";

    @Bean(AD_BUSINESS_CPC_BQF)
    public BaseQueryFactory getBusinessCpcBqf(@Qualifier(AD_BUSINESS_CPC_DATA_SOURCE) DataSource ds) {
        return getBaseQueryFactory(ds);
    }
    @Bean(CRM_BQF)
    public BaseQueryFactory getCrmBqf(@Qualifier(CRM_CP) DataSource ds) {
        return getBaseQueryFactory(ds);
    }

    @Bean(CRM_CP)
    public DataSource getCrmDataSource() {
        final HikariConfig config = new HikariConfig();
        config.setDriverClassName(crmDriver);
        config.setJdbcUrl(crmUrl);
        config.setUsername(crmUsername);
        config.setPassword(crmPassword);
        config.setMaximumPoolSize(10);
        return new HikariDataSource(config);
    }

    private BaseQueryFactory getBaseQueryFactory(DataSource ds) {
        final SQLTemplates templates = MySQLTemplates.builder().quote().build();
        final com.querydsl.sql.Configuration configuration = new com.querydsl.sql.Configuration(templates);
        configuration.setExceptionTranslator(new SpringExceptionTranslator());
        configuration.addListener(new SafeSQLListener());
        configuration.addListener(new CatSQLLogger());
        return new BaseQueryFactory(configuration, new ConnectionProvider(ds, configuration));
    }

    private static final String AD_DATA_CP = "adDataReadDataSource";
    public static final String AD_DATA_BQF = "adDataBQF";

    @Bean(AD_DATA_BQF)
    public BaseQueryFactory getAdDataBqf(@Qualifier(AD_DATA_CP) DataSource ds) {
        return getBaseQueryFactory(ds);
    }

    private static final String AD_AIGC_CP = "adAigcDataSource";
    public static final String AD_AIGC = "adAigcBQF";

    @Bean(AD_AIGC)
    public BaseQueryFactory getAdAigcBqf(@Qualifier(AD_AIGC_CP) DataSource ds) {
        return getBaseQueryFactory(ds);
    }

    @Bean
    Javers javers() {
        return JaversBuilder.javers()
                .withListCompareAlgorithm(ListCompareAlgorithm.SIMPLE)
                .build();
    }

    @Bean(OBJECT_MAPPER)
    ObjectMapper getObjectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        return objectMapper;
    }

    @Value("${clickhouse.url}")
    private String url;

    @Value("${clickhouse.sycpb2}")
    private String sycpb2;

    @Value("${clickhouse.username}")
    private String userName;

    @Value("${clickhouse.password}")
    private String password;

    @Value("${clickhouse.pool.size:5}")
    private Integer poolSize;

    @Value("${clickhouse.offline.url}")
    private String clickhouseOfflineUrl;

    @Value("${clickhouse.offline.name}")
    private String clickhouseOfflineName;

    @Value("${clickhouse.offline.username}")
    private String clickhouseOfflineUserName;

    @Value("${clickhouse.offline.password}")
    private String clickhouseOfflinePassword;

    @Value("${clickhouse.offline.pool.size:5}")
    private Integer clickhouseOfflinePoolSize;

    @Value("${clickhouse.adp.url}")
    private String clickhouseAdpUrl;

    @Value("${clickhouse.adp.name}")
    private String clickhouseAdpName;

    @Value("${clickhouse.adp.username}")
    private String clickhouseAdpUserName;

    @Value("${clickhouse.adp.password}")
    private String clickhouseAdpPassword;

    @Value("${clickhouse.adp.pool.size:5}")
    private Integer clickhouseAdpPoolSize;

    @Bean(CPC_CLICKHOUSE_BQF)
    public BaseQueryFactory getClickHouseBaseQueryFactory(@Qualifier(CPC_CLICKHOUSE_SYCPB2) DataSource ds) {
        return getBaseQueryFactory(ds);
    }

    @Bean(CPC_CLICKHOUSE_SYCPB2)
    public DataSource getClickHouseSycpb2DataSource() {
        final HikariConfig config = new HikariConfig();
        config.setDriverClassName("ru.yandex.clickhouse.ClickHouseDriver");
        config.setJdbcUrl(url + "/" + sycpb2);
        config.setUsername(userName);
        config.setPassword(password);
        config.setMaximumPoolSize(poolSize);
        config.setConnectionTestQuery("select 1");
        config.addDataSourceProperty("cachePrepStmts", "true");
        config.addDataSourceProperty("prepStmtCacheSize", "250");
        config.addDataSourceProperty("prepStmtCacheSqlLimit", "2048");
        config.addDataSourceProperty("useServerPrepStmts", "true");
        config.addDataSourceProperty("useLocalSessionState", "true");
        config.addDataSourceProperty("rewriteBatchedStatements", "true");
        config.addDataSourceProperty("cacheResultSetMetadata", "true");
        config.addDataSourceProperty("cacheServerConfiguration", "true");
        config.addDataSourceProperty("elideSetAutoCommits", "true");
        config.addDataSourceProperty("maintainTimeStats", "false");
        return new HikariDataSource(config);
    }

    @Bean(name = "okHttpClient")
    public OkHttpClient getOkHttpClient() {
        return HttpUtils.client;
    }
    @Bean(name = "gameOkHttpClient")
    public OkHttpClient getGameOkHttpClient() throws NoSuchAlgorithmException, KeyManagementException {
        final X509TrustManager trustManager = new DummyTrustManager();
        final SSLContext sslContext = SSLContext.getInstance("TLS");
        sslContext.init(null, new TrustManager[]{trustManager}, new SecureRandom());
        final SSLSocketFactory sslSocketFactory = sslContext.getSocketFactory();
        return new OkHttpClient.Builder()
                .sslSocketFactory(sslSocketFactory, trustManager)
                .hostnameVerifier(new NoopHostnameVerifier())
                .connectTimeout(Duration.ofSeconds(1))
                .readTimeout(Duration.ofSeconds(1))
                .build();
    }


    @Bean(CPC_CLICKHOUSE_SYCPB_OFFLINE_BQF)
    public BaseQueryFactory getClickHouseOfflineBaseQueryFactory(@Qualifier(CPC_CLICKHOUSE_SYCPB_OFFLINE) DataSource ds) {
        return getBaseQueryFactory(ds);
    }

    @Bean(CPC_CLICKHOUSE_SYCPB_OFFLINE)
    public DataSource getClickHouseOfflineDataSource() {
        final HikariConfig config = new HikariConfig();
        config.setDriverClassName("ru.yandex.clickhouse.ClickHouseDriver");
        config.setJdbcUrl(clickhouseOfflineUrl + "/" + clickhouseOfflineName);
        config.setUsername(clickhouseOfflineUserName);
        config.setPassword(clickhouseOfflinePassword);
        config.setMaximumPoolSize(clickhouseOfflinePoolSize);
        config.setConnectionTestQuery("select 1");
        config.addDataSourceProperty("cachePrepStmts", "true");
        config.addDataSourceProperty("prepStmtCacheSize", "250");
        config.addDataSourceProperty("prepStmtCacheSqlLimit", "2048");
        config.addDataSourceProperty("useServerPrepStmts", "true");
        config.addDataSourceProperty("useLocalSessionState", "true");
        config.addDataSourceProperty("rewriteBatchedStatements", "true");
        config.addDataSourceProperty("cacheResultSetMetadata", "true");
        config.addDataSourceProperty("cacheServerConfiguration", "true");
        config.addDataSourceProperty("elideSetAutoCommits", "true");
        config.addDataSourceProperty("maintainTimeStats", "false");
        return new HikariDataSource(config);
    }

    @Bean(CPC_CLICKHOUSE_SYCPB_ADP_BQF)
    public BaseQueryFactory getClickHouseAdpBaseQueryFactory(@Qualifier(CPC_CLICKHOUSE_ADP) DataSource ds) {
        return getBaseQueryFactory(ds);
    }

    @Bean(CPC_CLICKHOUSE_ADP)
    public DataSource getClickHouseAdpDataSource() {
        final HikariConfig config = new HikariConfig();
        config.setDriverClassName("ru.yandex.clickhouse.ClickHouseDriver");
        config.setJdbcUrl(clickhouseAdpUrl + "/" + clickhouseAdpName);
        config.setUsername(clickhouseAdpUserName);
        config.setPassword(clickhouseAdpPassword);
        config.setMaximumPoolSize(clickhouseAdpPoolSize);
        config.setConnectionTestQuery("select 1");
        config.addDataSourceProperty("cachePrepStmts", "true");
        config.addDataSourceProperty("prepStmtCacheSize", "250");
        config.addDataSourceProperty("prepStmtCacheSqlLimit", "2048");
        config.addDataSourceProperty("useServerPrepStmts", "true");
        config.addDataSourceProperty("useLocalSessionState", "true");
        config.addDataSourceProperty("rewriteBatchedStatements", "true");
        config.addDataSourceProperty("cacheResultSetMetadata", "true");
        config.addDataSourceProperty("cacheServerConfiguration", "true");
        config.addDataSourceProperty("elideSetAutoCommits", "true");
        config.addDataSourceProperty("maintainTimeStats", "false");
        return new HikariDataSource(config);
    }

    @Bean
    public ThreadPoolTaskExecutor taskExecutor() {
        ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();
        taskExecutor.setCorePoolSize(5);
        taskExecutor.setKeepAliveSeconds(300);
        taskExecutor.setMaxPoolSize(20);
        taskExecutor.setQueueCapacity(128);
        taskExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        return taskExecutor;
    }

    @Value("${ad.campaign.max.num:1000}")
    private int MAX_CAMPAIGN_NUM;
    @Value("${ad.unit.max.num:1000}")
    private int MAX_UNIT_NUM;
    @Value("${ad.creative.max.num:10000}")
    private int MAX_CREATIVE_NUM;
    @Value("${ad.min.cpc.bid:100}")
    private int MIN_CPC_BID;
    @Value("${ad.min.cpm.bid:100}")
    private int MIN_CPM_BID;
    @Value("${ad.max.cpc.target.count:2}")
    private int MAX_CPC_TARGET_COUNT;
    @Value("${ad.is.ping.url:false}")
    private boolean IS_PING_URL;
    @Value("${ad.is.validate.video:false}")
    private boolean IS_VALIDATE_VIDEO;
    @Value("${ad.budget.percentage:20}")
    private int BUDGET_PERCENTAGE;
    @Value("${ad.campaign.budget.max.update.limit:3}")
    private int CAMPAIGN_BUDGET_UPDATE_LIMIT;
    @Value("${ad.unit.budget.max.update.limit:3}")
    private int UNIT_BUDGET_UPDATE_LIMIT;
    @Value("${ad.creative.preview.max.count:5}")
    private int CREATIVE_PREVIEW_MAX_COUNT;
    @Value("${ad.creative.preview.max.minute:10}")
    private int CREATIVE_PREVIEW_MAX_MINUTE;


    @Value("${crm.account.read.only.jdbc.driver}")
    private String crmDriver;

    @Value("${crm.account.read.only.jdbc.url}")
    private String crmUrl;

    @Value("${crm.account.read.only.jdbc.username}")
    private String crmUsername;

    @Value("${crm.account.read.only.jdbc.password}")
    private String crmPassword;

    @PostConstruct
    public void init() {
        LaunchConstant.MAX_CAMPAIGN_NUM = MAX_CAMPAIGN_NUM;
        LaunchConstant.MAX_UNIT_NUM = MAX_UNIT_NUM;
        LaunchConstant.MAX_CREATIVE_NUM = MAX_CREATIVE_NUM;
        LaunchConstant.MIN_CPC_BID = MIN_CPC_BID;
        LaunchConstant.MIN_CPM_BID = MIN_CPM_BID;
        LaunchConstant.MAX_CPC_TARGET_COUNT = MAX_CPC_TARGET_COUNT;
        LaunchConstant.IS_PING_URL = IS_PING_URL;
        LaunchConstant.IS_VALIDATE_VIDEO = IS_VALIDATE_VIDEO;
        LaunchConstant.BUDGET_PERCENTAGE = BUDGET_PERCENTAGE;
        LaunchConstant.CAMPAIGN_BUDGET_UPDATE_LIMIT = CAMPAIGN_BUDGET_UPDATE_LIMIT;
        LaunchConstant.UNIT_BUDGET_UPDATE_LIMIT = UNIT_BUDGET_UPDATE_LIMIT;
        LaunchConstant.CREATIVE_PREVIEW_MAX_COUNT = CREATIVE_PREVIEW_MAX_COUNT;
        LaunchConstant.CREATIVE_PREVIEW_MAX_MIUNTE = CREATIVE_PREVIEW_MAX_MINUTE;
    }
}
