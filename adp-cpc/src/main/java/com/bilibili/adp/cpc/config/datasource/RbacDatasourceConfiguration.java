package com.bilibili.adp.cpc.config.datasource;

import com.mchange.v2.c3p0.ComboPooledDataSource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;
import java.beans.PropertyVetoException;

/**
 * @see rbac-biz.xml
 */
@Configuration
@MapperScan(sqlSessionFactoryRef = "rbacSqlSessionFactory", basePackages = "com.bilibili.rbac.biz.dao")
public class RbacDatasourceConfiguration {

    @Value("${rbac.jdbc.driver}")
    private String rbacJdbcDriver;
    @Value("${rbac.jdbc.url}")
    private String rbacJdbcUrl;
    @Value("${rbac.jdbc.username}")
    private String rbacJdbcUsername;
    @Value("${rbac.jdbc.password}")
    private String rbacJdbcPassword;

    @Bean
    public ComboPooledDataSource rbacDataSource() throws PropertyVetoException {
        ComboPooledDataSource rbacDataSource = new ComboPooledDataSource();
        rbacDataSource.setDriverClass(rbacJdbcDriver);
        rbacDataSource.setJdbcUrl(rbacJdbcUrl);
        rbacDataSource.setUser(rbacJdbcUsername);
        rbacDataSource.setPassword(rbacJdbcPassword);
        rbacDataSource.setMaxPoolSize(10);
        rbacDataSource.setMaxIdleTime(7200);
        rbacDataSource.setTestConnectionOnCheckout(true);
        rbacDataSource.setIdleConnectionTestPeriod(5);
        rbacDataSource.setPreferredTestQuery("SELECT 1");
        rbacDataSource.setCheckoutTimeout(1800000);
        return rbacDataSource;
    }

    @Bean
    public SqlSessionFactory rbacSqlSessionFactory(DataSource rbacDataSource) throws Exception {
        SqlSessionFactoryBean sqlSessionFactoryBean = new SqlSessionFactoryBean();
        sqlSessionFactoryBean.setDataSource(rbacDataSource);
        ResourcePatternResolver resourcePatternResolver = new PathMatchingResourcePatternResolver();
        Resource[] resources = resourcePatternResolver.getResources("classpath:mapper/rbac/*.xml");
        sqlSessionFactoryBean.setMapperLocations(resources);
        return sqlSessionFactoryBean.getObject();
    }

    @Bean
    public DataSourceTransactionManager rbacTransactionManager(DataSource rbacDataSource) {
        DataSourceTransactionManager transactionManager = new DataSourceTransactionManager();
        transactionManager.setDataSource(rbacDataSource);
        return transactionManager;
    }
}
