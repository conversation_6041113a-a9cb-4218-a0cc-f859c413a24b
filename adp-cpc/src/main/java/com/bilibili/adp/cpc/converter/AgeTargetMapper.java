package com.bilibili.adp.cpc.converter;

import com.bilibili.adp.cpc.core.bos.AgeTargetBo;
import com.bilibili.adp.cpc.wrapper.bos.AgeTargetWrapperBo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface AgeTargetMapper {
    AgeTargetMapper INSTANCE = Mappers.getMapper(AgeTargetMapper.class);

    AgeTargetBo fromWrapper(AgeTargetWrapperBo bo);
    AgeTargetWrapperBo toWrapper(AgeTargetBo bo);
}
