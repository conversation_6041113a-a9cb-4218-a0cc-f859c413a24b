package com.bilibili.adp.cpc.databus.sub.jinghuo;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.cpc.biz.bos.WarpDatabusProperty;
import com.bilibili.adp.cpc.biz.services.jinghuo.JhService;
import com.bilibili.adp.cpc.biz.services.jinghuo.bos.JhCreativeInfoBo;
import com.bilibili.warp.databus.AckableMessage;
import com.bilibili.warp.databus.DatabusProperty;
import com.bilibili.warp.databus.MessageListener;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024/9/30
 * @description 京火有消耗创意数据消费者
 */
@Slf4j
@Component
public class JhConsCreativeSub implements MessageListener {
    @Resource
    private ObjectMapper objectMapper;
    @Resource
    private JhService jhService;
    private final String topic;
    private final String group;
    @Override
    public String topic() {
        return this.topic;
    }

    @Override
    public String group() {
        return this.group;
    }
    public JhConsCreativeSub(WarpDatabusProperty databusProperties){
        DatabusProperty databusProperty = databusProperties.getProperties().get("jh-creative-cons");
        this.topic = databusProperty.getTopic();
        this.group = databusProperty.getSub().getGroup();
    }



    @Override
    public void onMessage(AckableMessage message) {
        try {
            JhCreativeInfoBo creativeInfoBo = objectMapper.readValue(message.payload(), JhCreativeInfoBo.class);
            log.info("jh JhCreativeSub receive message: {}", objectMapper.writeValueAsString(creativeInfoBo));
//            JhCreativeInfoBo creativeInfoBo = new JhCreativeInfoBo();
//            creativeInfoBo.setCreativeId("*********");
//            creativeInfoBo.setCreativeName("2024-10-09 07:24:48_个人起飞订单_***************");
//            creativeInfoBo.setAccountId("10350");
//            creativeInfoBo.setAccountName("伊冉宝宝");
//            creativeInfoBo.setTaskId("123");
//            creativeInfoBo.setCampaignId("8333033");
//            creativeInfoBo.setCampaignName("2024-10-09 07:24:48_个人起飞订单");
//            creativeInfoBo.setPromotionPurposeType("15");
//            creativeInfoBo.setUnitId("********");
//            creativeInfoBo.setUnitName("2037-01-01 00:00:00_个人起飞订单_***************");
//            creativeInfoBo.setAvId("***************");
//            creativeInfoBo.setAuthorId("0");
//            creativeInfoBo.setAuthorName("");
//            creativeInfoBo.setAuthorFansNum("");
//            creativeInfoBo.setEventTime("*************");

            jhService.pushJdCreativeInfo(creativeInfoBo);
        } catch (Exception e) {
            log.error("jh JhCreativeSub error", e);
        }
    }

    @Override
    public boolean autoCommit() {
        return true;
    }

}
