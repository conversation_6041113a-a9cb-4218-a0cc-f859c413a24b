package com.bilibili.adp.cpc.biz.services.anchor;

import com.bapis.ad.adp.anchor.AnchorSaveBatch;
import com.bapis.ad.component.BizCodeEnum;
import com.bapis.ad.component.GamePlatformType;
import com.bapis.ad.component.UrlType;
import com.bapis.ad.pandora.core.batch.BatchOperationType;
import com.bapis.ad.pandora.core.batch.RegisterAnchorOperationReq;
import com.bapis.ad.pandora.core.batch.RegisterOperationResp;
import com.bapis.ad.scv.anchor.*;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.exception.ServiceRuntimeException;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.biz.bos.anchor.*;
import com.bilibili.adp.cpc.biz.bos.app.AppPackageBo;
import com.bilibili.adp.cpc.biz.bos.archive.ArchiveBo;
import com.bilibili.adp.cpc.biz.converter.anchor.AnchorConverter;
import com.bilibili.adp.cpc.biz.converter.anchor.AnchorConvertorImpl;
import com.bilibili.adp.cpc.biz.services.app.EffectAdAppService;
import com.bilibili.adp.cpc.biz.services.archive.ArchiveService;
import com.bilibili.adp.cpc.biz.services.archive.bos.BatchCreateAdThreeElementsBo;
import com.bilibili.adp.cpc.biz.services.archive.bos.CreateAdThreeElementsBo;
import com.bilibili.adp.cpc.biz.services.bili_game.BilibiliGameService;
import com.bilibili.adp.cpc.biz.services.creative.CpcSaveCreativeService;
import com.bilibili.adp.cpc.biz.services.creative.config.AccLabelConfig;
import com.bilibili.adp.cpc.biz.services.creative.config.CreativePositionConfig;
import com.bilibili.adp.cpc.biz.services.game.AdpCpcGameService;
import com.bilibili.adp.cpc.biz.services.game.LaunchBiliMiniGameService;
import com.bilibili.adp.cpc.biz.services.game.bos.BiliMiniGameBo;
import com.bilibili.adp.cpc.biz.services.log.DbTable;
import com.bilibili.adp.cpc.biz.services.log.ILogOperateForLongService;
import com.bilibili.adp.cpc.biz.services.pandora.OperatorConverter;
import com.bilibili.adp.cpc.biz.services.unit.AdpCpcLauUnitGameService;
import com.bilibili.adp.cpc.core.LaunchCreativeMonitoringService;
import com.bilibili.adp.cpc.core.constants.GamePackage;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.*;
import com.bilibili.adp.cpc.dao.querydsl.QLauArchiveAnchorPoint;
import com.bilibili.adp.cpc.dao.querydsl.pos.LauArchiveAnchorPointPo;
import com.bilibili.adp.cpc.dao.querydsl.pos.LauCreativeMonitoringPo;
import com.bilibili.adp.cpc.enums.ClueTypeEnum;
import com.bilibili.adp.cpc.enums.CreativeMonitorType;
import com.bilibili.adp.cpc.enums.anchor.AnchorSceneEnum;
import com.bilibili.adp.cpc.enums.sanlian.SanlianGamePlatformTypeEnum;
import com.bilibili.adp.cpc.proxy.CpmPandoraProxy;
import com.bilibili.adp.cpc.proxy.CpmScvProxy;
import com.bilibili.adp.cpc.repo.CreativeAnchorMappingRepo;
import com.bilibili.adp.cpc.repo.LauUnitCreativeRepo;
import com.bilibili.adp.cpc.utils.metrics.CustomMetrics;
import com.bilibili.adp.cpc.utils.metrics.MetricsKeyConstant;
import com.bilibili.adp.launch.api.common.LaunchConstant;
import com.bilibili.adp.launch.api.minigame.dto.LauMiniGameDto;
import com.bilibili.adp.launch.api.service.ILauMiniGameService;
import com.bilibili.adp.resource.api.account.label.IAccountLabelService;
import com.bilibili.adp.web.framework.core.Pagination;
import com.bilibili.bjcom.querydsl.BaseQueryFactory;
import com.bilibili.crm.platform.api.account.dto.AccountBaseDto;
import com.bilibili.crm.platform.soa.ISoaQueryAccountService;
import com.bilibili.mgk.platform.api.landing_page.dto.MgkLandingPageDto;
import com.bilibili.mgk.platform.api.landing_page.dto.QueryLandingPageParamDto;
import com.bilibili.mgk.platform.api.landing_page.soa.ISoaLandingPageService;
import com.bilibili.mgk.platform.common.page_bean.MgkLandingPageBean;
import com.bilibili.sycpb.acc.api.dict.common.YesNoEnum;
import com.google.common.collect.Lists;
import io.grpc.Status;
import io.grpc.StatusRuntimeException;
import io.opentelemetry.api.common.AttributeKey;
import io.opentelemetry.api.common.Attributes;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.util.UriComponentsBuilder;
import pleiades.venus.starter.rpc.client.RPCClient;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.bilibili.adp.common.Constants.__TRACKID__;
import static com.bilibili.adp.common.Constants.track_id;
import static com.bilibili.adp.cpc.biz.constants.Constants.INT_TRUE;
import static com.bilibili.adp.cpc.dao.ad_core.querydsl.QLauCampaign.lauCampaign;
import static com.bilibili.adp.cpc.dao.ad_core.querydsl.QLauCreativeTemplate.lauCreativeTemplate;
import static com.bilibili.adp.cpc.dao.ad_core.querydsl.QLauUnit.lauUnit;
import static com.bilibili.adp.cpc.dao.ad_core.querydsl.QLauUnitCreative.lauUnitCreative;
import static com.bilibili.adp.cpc.dao.config.PlatformMySqlConfig.*;
import static com.bilibili.adp.cpc.dao.querydsl.QLauPreviewRequest.lauPreviewRequest;
import static com.bilibili.adp.cpc.utils.metrics.MetricsKeyConstant.*;


/**
 * <AUTHOR>
 * @date 2024/4/4 17:13
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AnchorServiceImpl implements IAnchorService {

    public final static String MGK_PAGE_MACRO_PARAM_ANCHOR = "buvid=__BUVID__&mid=__MID__&imei=__IMEI__" +
            "&duid=__DUID__&idfa=__IDFA__&android_id=__ANDROIDID__&os=__OS__&request_id=__REQUESTID__" +
            "&source_id=__SOURCEID__&track_id=__TRACKID__&creative_id=__CREATIVEID__&adtype=__ADTYPE__&oaid=__OAID__" +
            "&oaid_md5=__OAIDMD5__&caid=__CAID__&oaid_new=__OAIDNEW__&oaid_md5_new=__OAIDMD5NEW__";
    public static final List<Integer> APP_ANDROID_TYPES = Arrays.asList(AnchorType.APP_VALUE, AnchorType.GAME_DOWNLOAD_VALUE);

    public static final String BUTTON_TEXT_MINI_GAME = "去小程序";

    @Value("${archive.comment.conversion.component.default.account_id:987258}")
    private Integer defaultAccountId;
    @Value("${anchor.batchCreateSizeLimit:50}")
    private Integer batchCreateSizeLimit;
    // 微信小程序 H5 兜底链接
    @Getter
    @Value("${component.miniGame.h5BottomLink:http://www.bilibili.com}")
    private String miniGameH5BottomLink;

    @Value("${anchor.android.opt.validate.switch:0}")
    private Integer anchorAndroidOptValidateSwitch;

    private final ILogOperateForLongService logOperateForLongService;
    private final BilibiliGameService bilibiliGameService;
    private final ISoaQueryAccountService soaQueryAccountService;
    private final ArchiveService archiveService;
    private final ISoaLandingPageService soaLandingPageService;

    private final EffectAdAppService effectAdAppService;
    private final AdpCpcGameService adpCpcGameService;
    private final ILauMiniGameService lauMiniGameService;
    private final CreativePositionConfig creativePositionConfig;
    private final ArchiveAnchorQuerier anchorQuerier;
    private final CreativeAnchorMappingRepo creativeAnchorMappingRepo;
    private final ArchiveAnchorQuerier archiveAnchorQuerier;
    private final LaunchCreativeMonitoringService launchCreativeMonitoringService;
    private final AdpCpcLauUnitGameService adpCpcLauUnitGameService;
    private final LauUnitCreativeRepo lauUnitCreativeRepo;
    private final AnchorConvertorImpl anchorConvertorImpl;
    private final CustomMetrics customMetrics;
    private final IAccountLabelService accountLabelService;
    private final AccLabelConfig accLabelConfig;
    private final LaunchBiliMiniGameService launchBiliMiniGameService;

    @Resource(name = AD_CORE_BQF)
    private BaseQueryFactory adCoreBqf;

    @Resource(name = AD_BQF)
    private BaseQueryFactory adBqf;

    @Resource
    private CpmScvProxy cpmScvProxy;

    @Resource
    private CpmPandoraProxy cpmPandoraProxy;


    @Value("${component.batch.max.size:50}")
    private Integer componentBatchMaxSize;

    @Override
    public AnchorInfoBo fetchAnchorDetail(Long id) {

        SingleQueryAnchorRep singleQueryAnchorRep = cpmScvProxy.fetchAnchorInfo(id);
        AnchorInfoBo anchorInfoBo = AnchorConverter.MAPPER.convertAnchInfoGrpc2Bo(singleQueryAnchorRep);

        ArchiveBo archiveBo = archiveService.getArchiveByAid(anchorInfoBo.getAid());
        anchorInfoBo.setArchiveBo(archiveBo);

        Map<Long, String> pageMap = this.landingPage4ComponentList(Arrays.asList(anchorInfoBo));
        // 获取iOS，Android应用包信息
        Map<Integer, AppPackageBo> appPackageBoMap = this.appPackage4ComponentList(Arrays.asList(anchorInfoBo));
        anchorInfoBo.setAndroidUrlPageName(pageMap.getOrDefault(anchorInfoBo.getAndroidUrlPageId(), ""));
        anchorInfoBo.setConversionUrlPageName(pageMap.getOrDefault(anchorInfoBo.getConversionUrlPageId(), ""));
        anchorInfoBo.setIosUrlPageName(pageMap.getOrDefault(anchorInfoBo.getIosUrlPageId(), ""));

        AppPackageBo androidPkgBo = appPackageBoMap.get(anchorInfoBo.getAndroidAppPackageId());
        if (androidPkgBo != null) {
            if (androidPkgBo.getVersion() != null) {
                anchorInfoBo.setAndroidAppPackageVersion(androidPkgBo.getVersion());
            }
            if (androidPkgBo.getSize() != null) {
                anchorInfoBo.setAndroidAppPackageSize(androidPkgBo.getSize());
            }
            if (androidPkgBo.getPackageName() != null) {
                anchorInfoBo.setAndroidAppPackageName(androidPkgBo.getPackageName());
            }
            if (androidPkgBo.getUrl() != null) {
                anchorInfoBo.setAndroidAppPackageUrl(androidPkgBo.getUrl());
            }
        }

        AppPackageBo iosPkgBo = appPackageBoMap.get(anchorInfoBo.getIosAppPackageId());
        if (iosPkgBo != null) {
            if (iosPkgBo.getUrl() != null) {
                anchorInfoBo.setIosAppPackageUrl(iosPkgBo.getUrl());
            }
            if (iosPkgBo.getPackageName() != null) {
                anchorInfoBo.setIosAppPackageName(iosPkgBo.getPackageName());
            }
            if (iosPkgBo.getSize() != null) {
                anchorInfoBo.setIosAppPackageSize(iosPkgBo.getSize());
            }
            if (iosPkgBo.getVersion() != null) {
                anchorInfoBo.setIosAppPackageVersion(iosPkgBo.getVersion());
            }
        }
        anchorInfoBo.setQualificationIds(singleQueryAnchorRep.getQualificationIdsList());
        return anchorInfoBo;
    }

    @Override
    public Pagination<List<AnchorInfoBo>> queryAnchorPageList(AnchorListQueryBo queryBo) {

        QueryAnchorPageListReq queryAnchorReq = convertQueryBo2Req(queryBo);

        Attributes attributes = Attributes.of(AttributeKey.stringKey(count_type), MetricsKeyConstant.count_type_anchor,
                AttributeKey.stringKey(a_operator), a_operator_page_list,
                AttributeKey.stringKey(response_code), response_code_request
        );
        customMetrics.count(1, attributes);

        QueryAnchorRep queryAnchorRep = cpmScvProxy.queryAnchorPageListReq(queryAnchorReq);

        List<AnchorInfoBo> anchorInfoBos = AnchorConverter.MAPPER.convertAnchInfosGrpc2Bos(queryAnchorRep.getDataList());

        List<Long> aids = anchorInfoBos.stream().map(t -> t.getAid()).distinct().collect(Collectors.toList());
        Map<Long, ArchiveBo> archiveBoMap = archiveService.getArcsByAids(aids);

        // 其他信息
        // 获取建站落地页名称
        Map<Long, String> pageMap = this.landingPage4ComponentList(anchorInfoBos);
        // 获取iOS，Android应用包信息
        Map<Integer, AppPackageBo> appPackageBoMap = this.appPackage4ComponentList(anchorInfoBos);

        for (AnchorInfoBo anchorInfoBo : anchorInfoBos) {
            anchorInfoBo.setArchiveBo(archiveBoMap.get(anchorInfoBo.getAid()));
            // 关联创意数
            anchorInfoBo.setCreativeNum(0L);

            anchorInfoBo.setAndroidUrlPageName(pageMap.getOrDefault(anchorInfoBo.getAndroidUrlPageId(), ""));
            anchorInfoBo.setConversionUrlPageName(pageMap.getOrDefault(anchorInfoBo.getConversionUrlPageId(), ""));
            anchorInfoBo.setIosUrlPageName(pageMap.getOrDefault(anchorInfoBo.getIosUrlPageId(), ""));

            AppPackageBo androidPkgBo = appPackageBoMap.get(anchorInfoBo.getAndroidAppPackageId());
            if (androidPkgBo != null) {
                if (androidPkgBo.getVersion() != null) {
                    anchorInfoBo.setAndroidAppPackageVersion(androidPkgBo.getVersion());
                }
                if (androidPkgBo.getSize() != null) {
                    anchorInfoBo.setAndroidAppPackageSize(androidPkgBo.getSize());
                }
                if (androidPkgBo.getPackageName() != null) {
                    anchorInfoBo.setAndroidAppPackageName(androidPkgBo.getPackageName());
                }
                if (androidPkgBo.getUrl() != null) {
                    anchorInfoBo.setAndroidAppPackageUrl(androidPkgBo.getUrl());
                }
            }

            AppPackageBo iosPkgBo = appPackageBoMap.get(anchorInfoBo.getIosAppPackageId());
            if (iosPkgBo != null) {
                if (iosPkgBo.getUrl() != null) {
                    anchorInfoBo.setIosAppPackageUrl(iosPkgBo.getUrl());
                }
                if (iosPkgBo.getPackageName() != null) {
                    anchorInfoBo.setIosAppPackageName(iosPkgBo.getPackageName());
                }
                if (iosPkgBo.getSize() != null) {
                    anchorInfoBo.setIosAppPackageSize(iosPkgBo.getSize());
                }
                if (iosPkgBo.getVersion() != null) {
                    anchorInfoBo.setIosAppPackageVersion(iosPkgBo.getVersion());
                }
            }
        }

        return new Pagination<>(queryBo.getPage(), queryAnchorRep.getTotal(), anchorInfoBos);
    }

    private static QueryAnchorPageListReq convertQueryBo2Req(AnchorListQueryBo queryBo) {
        QueryAnchorPageListReq.Builder queryAnchorPageListReq = QueryAnchorPageListReq.newBuilder();

        if (queryBo.getAccountIds() != null) {
            for (Integer accountId : queryBo.getAccountIds()) {
                queryAnchorPageListReq.addAccountId(accountId);
            }
        }
        if (queryBo.getAgentIds() != null) {
            for (Integer agentId : queryBo.getAgentIds()) {
                queryAnchorPageListReq.addAgentId(agentId);
            }
        }
        if (queryBo.getMids() != null) {
            for (Long mid : queryBo.getMids()) {
                queryAnchorPageListReq.addMid(mid);
            }
        }
        if (queryBo.getIds() != null) {
            for (Long id : queryBo.getIds()) {
                queryAnchorPageListReq.addId(id);
            }
        }
        if (queryBo.getAids() != null) {
            for (Long aid : queryBo.getAids()) {
                queryAnchorPageListReq.addAid(aid);
            }
        }
        if (queryBo.getPage() != null) {
            queryAnchorPageListReq.setPage(queryBo.getPage());
        }
        if (queryBo.getSize() != null) {
            queryAnchorPageListReq.setSize(queryBo.getSize());
        }
        if (queryBo.getFromTime() != null) {
            queryAnchorPageListReq.setFromTime(queryBo.getFromTime());
        }
        if (queryBo.getToTime() != null) {
            queryAnchorPageListReq.setToTime(queryBo.getToTime());
        }
        queryAnchorPageListReq.setAnchorName(queryBo.getAnchorName());
        if (queryBo.getIsDeleted() != null) {
            for (Integer isDeleted : queryBo.getIsDeleted()) {
                queryAnchorPageListReq.addIsDeleted(isDeleted);
            }
        }

        if (!CollectionUtils.isEmpty(queryBo.getStatusList())) {
            queryAnchorPageListReq.addAllStatusValue(queryBo.getStatusList());
        }
        if (!CollectionUtils.isEmpty(queryBo.getAuditStatusList())) {
            queryAnchorPageListReq.addAllAuditStatusValue(queryBo.getAuditStatusList());
        }
        if (!(Objects.isNull(queryBo.getGroupId()) || queryBo.getGroupId() == 0)) {
            queryAnchorPageListReq.setGroupId(queryBo.getGroupId());
        }
        if (!StringUtils.isEmpty(queryBo.getArchiveTitle())) {
            queryAnchorPageListReq.setArchiveTitle(queryBo.getArchiveTitle());
        }
        queryAnchorPageListReq.addAllBizCodeList(Collections.singletonList(BizCodeEnum.BIZ_CODE_ENUM_ADP_VALUE));

        QueryAnchorPageListReq queryAnchorReq = queryAnchorPageListReq.build();
        return queryAnchorReq;
    }

    public Map<Integer, AppPackageBo> appPackage4ComponentList(List<AnchorInfoBo> anchorInfoBos) {
        List<Integer> androidAppPackageIds = anchorInfoBos.stream()
                .map(AnchorInfoBo::getAndroidAppPackageId)
                .filter(androidAppPackageId -> androidAppPackageId > 0)
                .collect(Collectors.toList());
        List<Integer> iosAppPackageIds = anchorInfoBos.stream()
                .map(AnchorInfoBo::getIosAppPackageId)
                .filter(iosAppPackageId -> iosAppPackageId > 0)
                .collect(Collectors.toList());
        List<Integer> packageIds = new ArrayList<>();
        packageIds.addAll(androidAppPackageIds);
        packageIds.addAll(iosAppPackageIds);
        List<AppPackageBo> list = effectAdAppService.queryByIds(packageIds);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }
        return list.stream().collect(Collectors.toMap(AppPackageBo::getId, Function.identity()));
    }

    public Map<Long, String> landingPage4ComponentList(List<AnchorInfoBo> anchorInfoBos) {
        List<Long> conversionPageIds = anchorInfoBos.stream()
                .map(AnchorInfoBo::getConversionUrlPageId)
                .filter(conversionUrlPageId -> conversionUrlPageId > 0)
                .collect(Collectors.toList());
        List<Long> androidPageIds = anchorInfoBos.stream()
                .map(AnchorInfoBo::getAndroidUrlPageId)
                .filter(androidUrlPageId -> androidUrlPageId > 0)
                .collect(Collectors.toList());
        List<Long> iOSPageIds = anchorInfoBos.stream()
                .map(AnchorInfoBo::getIosUrlPageId)
                .filter(iosUrlPageId -> iosUrlPageId > 0)
                .collect(Collectors.toList());

        List<Long> pageIds = new ArrayList<>();
        pageIds.addAll(conversionPageIds);
        pageIds.addAll(androidPageIds);
        pageIds.addAll(iOSPageIds);
        if (CollectionUtils.isEmpty(pageIds)) {
            return Collections.emptyMap();
        }
        final QueryLandingPageParamDto queryParam = QueryLandingPageParamDto.builder()
                .pageIdList(pageIds)
                .build();
        List<MgkLandingPageDto> list = soaLandingPageService.getLandingPageDtos(queryParam);
        return list.stream().collect(Collectors.toMap(MgkLandingPageDto::getPageId, MgkLandingPageDto::getName));
    }

    /**
     * 批量创建锚点，一个失败，则全部回滚
     *
     * @param bo
     * @param operator
     * @return
     */
    @Override
    @Transactional(transactionManager = AD_CORE_TM, rollbackFor = Exception.class)
    public List<AnchorCreateResultBo> createAnchors(AnchorSaveBo bo, Operator operator) {
        log.info("createAnchors bo = {}", bo);
        AnchorSaveContext anchorSaveContext = AnchorSaveContext.init(bo, operator, false);
        preProcess(anchorSaveContext);

        AccountBaseDto accountBaseDto = anchorSaveContext.getAccountBaseDto();
        // 构建空计划单元创意
        BatchCreateAdThreeElementsBo batchCreateAdThreeElementsBo = anchorSaveContext.getBatchCreateAdThreeElementsBo();

        // 安卓游戏
        // 保存游戏信息，目前只会有安卓游戏
        boolean isSubPkg = false;
        if (Objects.equals(AnchorType.GAME_DOWNLOAD_VALUE, bo.getType())) {
            Integer gameBaseId = bo.getGameBaseId();
            bilibiliGameService.saveGameInfo(GamePlatformType.ANDROID_VALUE, gameBaseId);

            isSubPkg = GamePackage.isSubPkg(bo.getSubPkg());
            if (isSubPkg) {
                final Set<Integer> gameBaseIds =
                        adpCpcGameService.fetchSubPkgAvailableGameBaseIds(Collections.singletonList(gameBaseId));
                Assert.isTrue(!CollectionUtils.isEmpty(gameBaseIds), MessageFormat.format("游戏id={0}对应的游戏包失效", gameBaseId));
            }

            // 保存内广 android 游戏 creative monitor url
            List<Long> creativeIds = batchCreateAdThreeElementsBo.getCreativeIds().stream().map(t -> Long.valueOf(t)).distinct().collect(Collectors.toList());
            List<LauCreativeMonitoringPo> existMonitoringPos = launchCreativeMonitoringService.listByCreativeIds(creativeIds);
            createCreativesMonitors(bo.getGameBaseId(), batchCreateAdThreeElementsBo, isSubPkg, Utils.isPositive(accountBaseDto.getIsInner()), existMonitoringPos);
        }

        Map<Long, ArchiveBo> archiveBoMap = archiveService.getArcsByAids(bo.getAids());
        bo.setConversionUrl(getUrlByType(bo.getType(), bo.getConversionUrlType(), bo.getConversionUrlPageId(),
                bo.getConversionUrl(), isSubPkg));

        // 微信小游戏
        if (Objects.equals(bo.getType(), AnchorType.WECHAT_MINI_GAME_VALUE) && Utils.isPositive(bo.getIsOpenMiniGame())) {
            LauMiniGameDto miniGameDto = lauMiniGameService.getLauMiniGameById(bo.getMiniGameId());
            Assert.isTrue(miniGameDto != null, "微信小游戏不存在");
            Assert.isTrue(YesNoEnum.YES.getCode().equals(miniGameDto.getState()), "微信小游戏已经下线，请检查");
            Assert.isTrue(!StringUtils.isEmpty(miniGameDto.getGameUrl()), "微信小游戏url为空，请检查！");
        }

        buildUrl(bo, isSubPkg);

        List<String> topImgUrls = bo.getTopImgUrls().stream().map(t -> t.getUrl()).collect(Collectors.toList());
        List<String> appImgUrls = bo.getAppImgUrls().stream().map(t -> t.getUrl()).collect(Collectors.toList());

        // 应用
        if (Objects.equals(AnchorType.APP.getNumber(), bo.getType())) {
            if (StringUtils.isEmpty(bo.getAndroidSchemaUrl()) && !StringUtils.isEmpty(bo.getIosSchemaUrl())) {
                // 应用推广选择了 IOS
                bo.setIosButtonSchemaUrl(bo.getIosSchemaUrl());
            } else if (!StringUtils.isEmpty(bo.getAndroidSchemaUrl())) {
                if (!Objects.isNull(bo.getIosAppPackageId()) && bo.getIosAppPackageId() != 0) {
                    // 应用推广，选择了 IOS 安卓，此时 卡片、按钮，四个跳转连接是同一个
                    String schemaUrl = Optional.ofNullable(bo.getAndroidSchemaUrl()).orElse("");
                    bo.setAndroidSchemaUrl(schemaUrl);
                    bo.setIosSchemaUrl(schemaUrl);
                    bo.setAndroidButtonSchemaUrl(schemaUrl);
                    bo.setIosButtonSchemaUrl(schemaUrl);
                } else {
                    // IOS信息为空，说明选择了安卓
                    bo.setAndroidButtonSchemaUrl(bo.getAndroidSchemaUrl());
                }
            }

        }

        CreateAnchorsReq createAnchorsReq = anchorConvertorImpl.convertCreatesBo2Grpc(bo, accountBaseDto,
                archiveBoMap, batchCreateAdThreeElementsBo, topImgUrls, appImgUrls);

        log.info("createAnchorReq ={}", createAnchorsReq.toString());

        // 打点
        Attributes attributes = Attributes.of(AttributeKey.stringKey(count_type), MetricsKeyConstant.count_type_anchor,
                AttributeKey.stringKey(a_operator), a_operator_create,
                AttributeKey.stringKey(response_code), response_code_request
        );
        customMetrics.count(1, attributes);
        CreatesReply createReply = cpmScvProxy.createAnchors(createAnchorsReq);

        // 打点
        Attributes attributesSuccess = Attributes.of(AttributeKey.stringKey(count_type), MetricsKeyConstant.count_type_anchor,
                AttributeKey.stringKey(a_operator), a_operator_create,
                AttributeKey.stringKey(response_code), response_code_success
        );
        customMetrics.count(1, attributesSuccess);

        List<Long> anchorIds = createReply.getCreateRepListList().stream().map(t -> t.getId()).collect(Collectors.toList());
        List<AnchorCreateResultBo> anchorCreateResultBoList = createReply.getCreateRepListList().stream()
                .map(createReplySingle -> AnchorCreateResultBo.builder()
                        .anchorId(createReplySingle.getId())
                        .avid(createReplySingle.getAvid())
                        .creativeId(createReplySingle.getCreativeId())
                        .build())
                .collect(Collectors.toList());

        List<AnchorSaveBo> saveBoLogs = new ArrayList<>();
        for (Long aid : bo.getAids()) {
            AnchorSaveBo saveBo = new AnchorSaveBo();
            BeanUtils.copyProperties(bo, saveBo);
            saveBo.setAid(aid);
            saveBoLogs.add(saveBo);
        }

        // 操作日志
        logOperateForLongService.addBatchInsertLog(DbTable.LAU_ARCHIVE_ANCHOR_POINT, operator, saveBoLogs, anchorIds);
        return anchorCreateResultBoList;
    }

    private void buildUrl(AnchorSaveBo bo, boolean isSubPkg) {
        //如果是线索类型组件 用兜底链接填充双端链接(引擎老板们的要求）
        if (Objects.equals(bo.getType(), AnchorType.CLUE_VALUE) && Objects.equals(bo.getClueType(), ClueTypeEnum.MESSAGE.getCode())) {
            bo.setIosUrl(bo.getIosUrl());
            bo.setAndroidUrl(bo.getAndroidUrl());
        } else if (Objects.equals(bo.getType(), AnchorType.CLUE_VALUE) || Objects.equals(bo.getType(), AnchorType.WECHAT_MINI_GAME_VALUE)) {
            bo.setIosUrl(bo.getConversionUrl());
            bo.setAndroidUrl(bo.getConversionUrl());
        } else {
            bo.setIosUrl(getUrlByType(bo.getType(), bo.getIosUrlType(), bo.getIosUrlPageId(), bo.getIosUrl(), isSubPkg));
            bo.setAndroidUrl(getUrlByType(bo.getType(), bo.getAndroidUrlType(), bo.getAndroidUrlPageId(), bo.getAndroidUrl(), isSubPkg));
        }
        // b站小游戏
        if (Objects.equals(AnchorType.BILI_MINI_GAME.getNumber(), bo.getType())) {
            // 查询小游戏校验，并填充
            BiliMiniGameBo biliMiniGameBo = launchBiliMiniGameService.get(bo.getBiliMiniGameMid(), bo.getGameBaseId());
            Assert.isTrue(biliMiniGameBo != null, "b站小游戏不存在");
            Assert.isTrue(!StringUtils.isEmpty(biliMiniGameBo.getGameLink()), "b站小游戏链接不存在");
            // 三端地址
            bo.setConversionUrl(miniGameH5BottomLink);
            // 获取 game link 格式: https://miniapp.bilibili.com/game/biligamea0e63a01fa11c978?sourcefrom=**********&track_id=__TRACKID__&account_id=__ACCOUNTID__&campaign_id=__CAMPAIGNID__&unit_id=__UNITID__&creative_id=__CREATIVEID__ 
            String url = UriComponentsBuilder.fromUriString(biliMiniGameBo.getGameLink())
                    .queryParam(CpcSaveCreativeService.SOURCE_FROM, CpcSaveCreativeService.SOURCE_FROM_BILI_GAME_ANCHOR)
                    .queryParam(track_id, __TRACKID__)
                    .queryParam(CpcSaveCreativeService.ACCOUNT_ID, CpcSaveCreativeService.__ACCOUNT_ID__)
                    .queryParam(CpcSaveCreativeService.CAMPAIGN_ID, CpcSaveCreativeService.__CAMPAIGN_ID__)
                    .queryParam(CpcSaveCreativeService.UNIT_ID, CpcSaveCreativeService.__UNIT_ID__)
                    .queryParam(CpcSaveCreativeService.CREATIVE_ID, CpcSaveCreativeService.__CREATIVE_ID__)
                    .queryParam(CpcSaveCreativeService.BV_ID, CpcSaveCreativeService.__BV_ID__)
                    .build(false).toUriString();
            bo.setIosUrl(url);
            bo.setAndroidUrl(url);
        }

        // b站小程序
        buildAppletUrl(bo);
    }

    @Override
    public Long createAnchorsBatch(AnchorSaveBo anchorSaveBo, Operator operator) {
        if (!CollectionUtils.isEmpty(anchorSaveBo.getAids())) {
            Assert.isTrue(anchorSaveBo.getAids().size() <= componentBatchMaxSize, "单次最多支持" + componentBatchMaxSize + "个稿件");
        }

        RegisterAnchorOperationReq req = RegisterAnchorOperationReq.newBuilder()
                .setOperator(AnchorConverter.MAPPER.toRpcBo(operator))
                .addAllOperation(Collections.singletonList(AnchorConverter.MAPPER.toRpcBo(anchorSaveBo, operator)))
                .setOperationType(BatchOperationType.BATCH_COMPONENT_CREATE).build();
        RegisterOperationResp registerOperationResp = cpmPandoraProxy.registerAnchorOperation(req);

        return registerOperationResp.getOperationId();
    }

    public void preHandler(AnchorSaveContext anchorSaveContext) {

        Operator operator = anchorSaveContext.getOperator();
        AnchorSaveBo newVersionBo = anchorSaveContext.getAnchorSaveBo();
        AccountBaseDto accountBaseDto = soaQueryAccountService.getAccountBaseDtoById(anchorSaveContext.getOperator().getOperatorId());
        anchorSaveContext.setAccountBaseDto(accountBaseDto);

        // 预处理
        newVersionBo.setSceneNames(newVersionBo.getScenes().stream().map(scene -> AnchorSceneEnum.getByCode(scene).getName()).collect(Collectors.joining(",")));

        if (anchorSaveContext.isUpdate()) {
            AnchorInfoBo anchorInfoBo = this.fetchAnchorDetail(newVersionBo.getId());
            anchorSaveContext.setOldAnchorSaveBo(AnchorConverter.MAPPER.convertAnchorInfoBo2SaveBo(anchorInfoBo));
//            newVersionBo.setAid(anchorInfoBo.getAid());

            CreateAdThreeElementsBo adThreeElements = CreateAdThreeElementsBo.builder()
                    .accountId(defaultAccountId)
                    .creativeId(anchorInfoBo.getCreativeId())
                    .unitId(anchorInfoBo.getUnitId())
                    .build();
            anchorSaveContext.setCreateAdThreeElementsBo(adThreeElements);

            Assert.isTrue(anchorInfoBo.getAccountId().equals(operator.getOperatorId()), "不能操作其他账户数据");
            Assert.isTrue(anchorInfoBo.getType().equals(newVersionBo.getType()), "type不能修改");
            Assert.isTrue(anchorInfoBo.getAid().equals(newVersionBo.getAid()), "aid不能修改");
        } else {
            BatchCreateAdThreeElementsBo batchCreateAdThreeElementsBo = this.batchCreateAdThreeElements(defaultAccountId, newVersionBo.getAids(), newVersionBo.getScenes());
            anchorSaveContext.setBatchCreateAdThreeElementsBo(batchCreateAdThreeElementsBo);
        }


    }

    private void preHandleScenes(AnchorSaveContext anchorSaveContext) {
        AnchorSaveBo anchorSaveBo = anchorSaveContext.getAnchorSaveBo();
        Operator operator = anchorSaveContext.getOperator();

        List<Integer> accountLabelIds = accountLabelService.getLabelIdsByAccountId(operator.getOperatorId());
        anchorSaveContext.setAccountLabelIds(accountLabelIds);
        if (!Objects.equals(anchorSaveBo.getBizCode(), BizCodeEnum.BIZ_CODE_ENUM_ADP_VALUE)) {
            return;
        }

        // 设置场景默认值
        boolean isSupportAnchorCustomScenes = accountLabelIds.contains(accLabelConfig.getAnchorCustomScenesLabelId());
        // 有白名单支持自主选择场景，否则后端写死两个场景
        if (isSupportAnchorCustomScenes) {
            if (CollectionUtils.isEmpty(anchorSaveBo.getScenes())) {
                anchorSaveBo.setScenes(AnchorSceneEnum.SCENES_LIST);
            }
        } else {
            anchorSaveBo.setScenes(AnchorSceneEnum.SCENES_LIST);
        }
    }

    public static void setDefaultValue(AnchorSaveContext ctx) {
        AnchorSaveBo bo = ctx.getAnchorSaveBo();
        // 默认值处理
        if (bo.getIsMapiRequest() == null) {
            bo.setIsMapiRequest(false);
        }
        if (bo.getTopImgUrls() == null) {
            bo.setTopImgUrls(new ArrayList<>());
        }
        if (bo.getAppImgUrls() == null) {
            bo.setAppImgUrls(new ArrayList<>());
        }
        if (bo.getAppLabels() == null) {
            bo.setAppLabels(new ArrayList<>());
        }

        // 兼容选择单个锚点创建的 aid 字段
        if (bo.getAid() != null && CollectionUtils.isEmpty(bo.getAids())) {
            bo.setAids(Collections.singletonList(bo.getAid()));
        }
        if (bo.getIsOpenMiniGame() == null) {
            bo.setIsOpenMiniGame(0);
        }
        if (Objects.equals(AnchorType.GAME_DOWNLOAD_VALUE, bo.getType())) {
            bo.setGamePlatformType(GamePlatformType.ANDROID_VALUE);
        }
        // 微信小程序，按钮文案写死
        if (Objects.equals(AnchorType.WECHAT_MINI_GAME_VALUE, bo.getType())) {
            bo.setButtonText(BUTTON_TEXT_MINI_GAME);
        }
        if (bo.getScenes() == null) {
            bo.setScenes(new ArrayList<>());
        }
        // 预处理
        bo.setSceneNames(bo.getScenes().stream().map(scene -> AnchorSceneEnum.getByCode(scene).getName()).collect(Collectors.joining(",")));
        //mapi都是三连的 塞个默认值
        if (bo.getBizCode() == null) {
            bo.setBizCode(BizCodeEnum.BIZ_CODE_ENUM_ADP_VALUE);
        }
    }

    public void validate(AnchorSaveContext ctx) {
        AnchorSaveBo bo = ctx.getAnchorSaveBo();

        if (Objects.equals(bo.getBizCode(), BizCodeEnum.BUSINESS_TOOL_VALUE)) {
            return;
        }

        boolean isUpdate = ctx.isUpdate();
        if (isUpdate) {
            Assert.isTrue(Utils.isPositive(bo.getId()), "id不能为空");
//            Assert.isTrue(Utils.isPositive(bo.getAid()), "aid不能为空");


        } else {
            Assert.isTrue(!CollectionUtils.isEmpty(bo.getAids()), "aids不能为空");
            Assert.isTrue(bo.getAids().size() <= batchCreateSizeLimit, "稿件数量不允许超过" + batchCreateSizeLimit);
        }

//        if (!bo.getIsMapiRequest()) {
//            return;
//        }

        Assert.isTrue(!StringUtils.isEmpty(bo.getName()), "锚点名称不能为空");
        Assert.isTrue(!StringUtils.isEmpty(bo.getMainTitle()), "主标题不能为空");
        Assert.isTrue(bo.getMainTitle().length() <= 12, "主标题长度不能超过12个字符");
        Assert.isTrue(!StringUtils.isEmpty(bo.getSubTitle()), "副标题不能为空");
        Assert.isTrue(bo.getSubTitle().length() <= 14, "副标题长度不能超过14个字符");
        Assert.isTrue(!StringUtils.isEmpty(bo.getButtonText()), "按钮文案不能为空");
        Assert.isTrue(bo.getButtonText().length() <= 4, "按钮文案长度不能超过4个字符");

        // 应用
        if (Objects.equals(bo.getType(), AnchorType.APP_VALUE)) {
            Assert.isTrue(Utils.isPositive(bo.getAndroidAppPackageId()) || Utils.isPositive(bo.getIosAppPackageId()), "安卓应用包和ios应用包不能为同时空");

            if (Utils.isPositive(bo.getAndroidAppPackageId())) {
                if (Objects.equals(bo.getAndroidUrlType(), UrlType.GAONENG_VALUE)) {
                    Assert.isTrue(Utils.isPositive(bo.getAndroidUrlPageId()), "android落地页id不能为空");
                } else if (Objects.equals(bo.getAndroidUrlType(), UrlType.THIRD_PARTY_VALUE)) {
                    Assert.isTrue(!StringUtils.isEmpty(bo.getAndroidUrl()), "android三方落地页链接不能为空");
//                Assert.isTrue(bo.getAndroidUrl().startsWith("https"), "android三方落地页链接必须以https开头");
                } else {
                    throw new ServiceRuntimeException("ios链接类型不能为空");
                }
            }

            if (Utils.isPositive(bo.getIosAppPackageId())) {
                if (Objects.equals(bo.getIosUrlType(), UrlType.GAONENG_VALUE)) {
                    Assert.isTrue(Utils.isPositive(bo.getIosUrlPageId()), "ios落地页id不能为空");
                } else if (Objects.equals(bo.getIosUrlType(), UrlType.THIRD_PARTY_VALUE)) {
                    Assert.isTrue(!StringUtils.isEmpty(bo.getIosUrl()), "ios三方落地页链接不能为空");
//                Assert.isTrue(bo.getIosUrl().startsWith("https"), "ios三方落地页链接必须以https开头");
                } else {
                    throw new ServiceRuntimeException("ios链接类型不能为空");
                }
            }
        }
        // 安卓游戏
        else if (Objects.equals(bo.getType(), AnchorType.GAME_DOWNLOAD_VALUE)) {
            Assert.isTrue(Utils.isPositive(bo.getGameBaseId()) || Utils.isPositive(bo.getIosAppPackageId()), "安卓游戏id和ios应用包不能为同时空");

            if (Utils.isPositive(bo.getGameBaseId())) {
                if (Objects.equals(bo.getAndroidUrlType(), UrlType.GAONENG_VALUE)) {
                    Assert.isTrue(Utils.isPositive(bo.getAndroidUrlPageId()), "android落地页id不能为空");
                } else if (Objects.equals(bo.getAndroidUrlType(), UrlType.THIRD_PARTY_VALUE)) {
                    Assert.isTrue(!StringUtils.isEmpty(bo.getAndroidUrl()), "android三方落地页链接不能为空");
//                Assert.isTrue(bo.getAndroidUrl().startsWith("https"), "android三方落地页链接必须以https开头");
                } else {
                    throw new ServiceRuntimeException("ios链接类型不能为空");
                }
            }
            if (Utils.isPositive(bo.getIosAppPackageId())) {
                if (Objects.equals(bo.getIosUrlType(), UrlType.GAONENG_VALUE)) {
                    Assert.isTrue(Utils.isPositive(bo.getIosUrlPageId()), "ios落地页id不能为空");
                } else if (Objects.equals(bo.getIosUrlType(), UrlType.THIRD_PARTY_VALUE)) {
                    Assert.isTrue(!StringUtils.isEmpty(bo.getIosUrl()), "ios三方落地页链接不能为空");
//                Assert.isTrue(bo.getIosUrl().startsWith("https"), "ios三方落地页链接必须以https开头");
                } else {
                    throw new ServiceRuntimeException("ios链接类型不能为空");
                }
            }

            bo.setAndroidSchemaUrl("");
            bo.setIosSchemaUrl("");
        }
        // 线索
        else if (Objects.equals(bo.getType(), AnchorType.CLUE_VALUE)) {
            if (Objects.equals(bo.getConversionUrlType(), UrlType.GAONENG_VALUE)) {
                Assert.isTrue(Utils.isPositive(bo.getConversionUrlPageId()), "落地页id不能为空");
            } else if (Objects.equals(bo.getConversionUrlType(), UrlType.THIRD_PARTY_VALUE)) {
                Assert.isTrue(!StringUtils.isEmpty(bo.getConversionUrl()), "三方落地页链接不能为空");
//                Assert.isTrue(bo.getConversionUrl().startsWith("https"), "三方落地页链接必须以https开头");
            } else {
                throw new ServiceRuntimeException("链接类型不能为空");
            }
            bo.setAndroidSchemaUrl("");
            bo.setIosSchemaUrl("");
        }
        // 微信小游戏
        else if (Objects.equals(bo.getType(), AnchorType.WECHAT_MINI_GAME_VALUE)) {
            if (Objects.equals(bo.getConversionUrlType(), UrlType.GAONENG_VALUE)) {
                Assert.isTrue(Utils.isPositive(bo.getConversionUrlPageId()), "落地页id不能为空");
            } else if (Objects.equals(bo.getConversionUrlType(), UrlType.THIRD_PARTY_VALUE)) {
                Assert.isTrue(!StringUtils.isEmpty(bo.getConversionUrl()), "三方落地页链接不能为空");
//                Assert.isTrue(bo.getConversionUrl().startsWith("https"), "三方落地页链接必须以https开头");
            } else {
                throw new ServiceRuntimeException("链接类型不能为空");
            }

            Assert.isTrue(bo.getIsOpenMiniGame() != null, "是否开启小程序一跳不能为空");
            if (Objects.equals(bo.getIsOpenMiniGame(), 1)) {
                Assert.isTrue(Utils.isPositive(bo.getMiniGameId()), "微信小游戏id不能为空");
            }
            bo.setAndroidSchemaUrl("");
            bo.setIosSchemaUrl("");
        }
        // b站小游戏
        else if (Objects.equals(bo.getType(), AnchorType.BILI_MINI_GAME_VALUE)) {
            Assert.isTrue(Utils.isPositive(bo.getGameBaseId()), "游戏id不能为空");
            Assert.isTrue(Utils.isPositive(bo.getBiliMiniGameMid()), "b站小游戏up mid不能为空");
        } else if (Objects.equals(bo.getType(), AnchorType.BILI_APPLET_VALUE)) {
            Assert.isTrue(!StringUtils.isEmpty(bo.getBiliAppletUrl()), "投放目标为b站小程序时，小程序链接不能为空");
            Assert.isTrue(bo.getBiliAppletUrl().startsWith("https://miniapp.bilibili.com"), "您填写的链接不符合B站小程序规范；B站小程序链接形式可参考 https://miniapp.bilibili.com.xxx");
        } else {
            throw new ServiceRuntimeException("锚点类型错误");
        }
    }

    private void validateAndroidNewFields(AnchorSaveContext anchorSaveContext) {
        AnchorSaveBo anchorSaveBo = anchorSaveContext.getAnchorSaveBo();
        // 经营号跳过
        if (Objects.equals(anchorSaveBo.getBizCode(), BizCodeEnum.BUSINESS_TOOL_VALUE)) {
            return;
        }
        // mapi 跳过先
        if (anchorSaveBo.getIsMapiRequest() != null && anchorSaveBo.getIsMapiRequest()) {
            return;
        }
        boolean isSupportAnchorAndroidNew = anchorSaveContext.getAccountLabelIds().contains(accLabelConfig.getAnchorAndroidNewPanelLabelId());
        if (!isSupportAnchorAndroidNew) {
            return;
        }

        if (!Utils.isPositive(anchorAndroidOptValidateSwitch)) {
            return;
        }

        if (APP_ANDROID_TYPES.contains(anchorSaveBo.getType())) {
            if (CollectionUtils.isEmpty(anchorSaveBo.getAppLabels())) {
                throw new StatusRuntimeException(Status.INVALID_ARGUMENT.withDescription("请填写app标签"));
            } else {
                if (anchorSaveBo.getAppLabels().size() > 3) {
                    throw new StatusRuntimeException(Status.INVALID_ARGUMENT.withDescription("标签最多3个"));
                }
                for (String appLabel : anchorSaveBo.getAppLabels()) {
                    if (!org.apache.commons.lang3.StringUtils.isEmpty(appLabel) && appLabel.length() > 4) {
                        throw new StatusRuntimeException(Status.INVALID_ARGUMENT.withDescription("单个标签最多4个字符"));
                    }
                }
            }

            if (CollectionUtils.isEmpty(anchorSaveBo.getTopImgUrls())) {
                throw new StatusRuntimeException(Status.INVALID_ARGUMENT.withDescription("请选择顶图"));
            } else {
                if (anchorSaveBo.getTopImgUrls().size() != 1) {
                    throw new StatusRuntimeException(Status.INVALID_ARGUMENT.withDescription("顶图最多一个"));
                }
            }
            if (CollectionUtils.isEmpty(anchorSaveBo.getAppImgUrls())) {
                throw new StatusRuntimeException(Status.INVALID_ARGUMENT.withDescription("请选择app图片"));
            } else {
                if (anchorSaveBo.getAppImgUrls().size() < 3 || anchorSaveBo.getAppImgUrls().size() > 8) {
                    throw new StatusRuntimeException(Status.INVALID_ARGUMENT.withDescription("app图片最少3张，最多8张"));
                }
            }
            if (org.apache.commons.lang3.StringUtils.isEmpty(anchorSaveBo.getGuideText())) {
                throw new StatusRuntimeException(Status.INVALID_ARGUMENT.withDescription("请填写引导文案"));
            } else {
                if (anchorSaveBo.getGuideText().length() > 15) {
                    throw new StatusRuntimeException(Status.INVALID_ARGUMENT.withDescription("引导文案最多15个字符"));
                }
            }
            if (org.apache.commons.lang3.StringUtils.isEmpty(anchorSaveBo.getAppDetailText())) {
                throw new StatusRuntimeException(Status.INVALID_ARGUMENT.withDescription("请填写app详情"));
            } else {
                if (anchorSaveBo.getAppDetailText().length() > 200) {
                    throw new StatusRuntimeException(Status.INVALID_ARGUMENT.withDescription("app详情最多200个字符"));
                }
            }
        }
    }

    public String getUrlByType(Integer type, Integer conversionUrlType, Long conversionUrlPageId, String url,
                               boolean isSubPkg) {
        String urlWithMacro = url;
        AnchorType componentTypeEnum = AnchorType.forNumber(type);

        // 游戏下载
        if (componentTypeEnum == AnchorType.GAME_DOWNLOAD) {
            UriComponentsBuilder builder = UriComponentsBuilder.fromUriString(urlWithMacro);
            builder.replaceQueryParam(LaunchConstant.LANDING_PAGE_QUERY_GAME_KEY, LaunchConstant.LANDING_PAGE_QUERY_GAME_VALUE);
            if (isSubPkg) {
                builder.replaceQueryParam("channelId", "1")
                        .replaceQueryParam("channelExtra", "");
            } else {
                builder.replaceQueryParam("channelId")
                        .replaceQueryParam("channelExtra");
            }
            builder.replaceQueryParam(LaunchConstant.GAME_HIDDEN_PARAM_KEY, "1000100041");
            urlWithMacro = builder
                    .build(false)
                    .toUriString();
        }

        // 建站落地页
        if (Objects.equals(conversionUrlType, UrlType.GAONENG_VALUE)) {
            MgkLandingPageBean mgkLandingPageBean = soaLandingPageService.validatePageIdAndGetLandingPage(String.valueOf(conversionUrlPageId));
            return UriComponentsBuilder.fromUriString(mgkLandingPageBean.getLaunchUrlSecondary())
                    .query(MGK_PAGE_MACRO_PARAM_ANCHOR)
                    .build(false)
                    .toUriString();
        }
        // 建站落地页组，三方落地页组
//        if (Objects.equals(conversionUrlType, UrlType.THIRD_PARTY_PAGE_GROUP_VALUE) || Objects.equals(conversionUrlType, UrlType.MGK_PAGE_GROUP_VALUE)) {
//            LaunchPageGroupBo launchPageGroupBo = launchMgkPageGroupService.loadPageGroup(conversionUrlPageId);
//
//        }

        return urlWithMacro;
    }


    @Transactional(transactionManager = AD_CORE_TM, rollbackFor = Exception.class)
    public CreateAdThreeElementsBo createAdThreeElements(Integer accountId, List<Integer> scenes) {
        Assert.isTrue(Utils.isPositive(accountId), "账户id不能为空");

        //构造空计划
        LauCampaignPo campaign = new LauCampaignPo();
        campaign.setAccountId(accountId);
        // 底表是 tinyint(-128~127)
//        campaign.setSalesType(SalesType.NATIVE_ANCHOR.getCode());
        Integer campaignId = adCoreBqf.insert(lauCampaign).insertGetKey(campaign);
        //构造空单元
        LauUnitPo unit = new LauUnitPo();
        unit.setAccountId(accountId);
        unit.setCampaignId(campaignId);
//        unit.setSalesType(SalesType.NATIVE_ANCHOR.getCode());
        Integer unitId = adCoreBqf.insert(lauUnit).insertGetKey(unit);
        //构造空创意
        LauUnitCreativePo creative = new LauUnitCreativePo();
        creative.setAccountId(accountId);
        creative.setCampaignId(campaignId);
        creative.setUnitId(unitId);
        creative.setCreativeType(0);
        creative.setTemplateId(0);
        creative.setAuditStatus(0);
        creative.setStatus(0);
//        creative.setSalesType(SalesType.NATIVE_ANCHOR.getCode());

        Integer creativeId = adCoreBqf.insert(lauUnitCreative).insertGetKey(creative);

        CreateAdThreeElementsBo createAdThreeElementsBo = CreateAdThreeElementsBo.builder()
                .accountId(accountId)
                .campaignId(campaignId)
                .unitId(unitId)
                .creativeId(creativeId)
                .build();

        // 创意广告位组模板关系
        reSaveCreativeTemplateMappings(createAdThreeElementsBo, scenes);

        return createAdThreeElementsBo;
    }

    @Transactional(transactionManager = AD_CORE_TM, rollbackFor = Exception.class)
    public BatchCreateAdThreeElementsBo batchCreateAdThreeElements(Integer accountId, List<Long> aids, List<Integer> scenes) {
        Assert.isTrue(Utils.isPositive(accountId), "账户id不能为空");

        //构造空计划
        LauCampaignPo campaign = new LauCampaignPo();
        campaign.setAccountId(accountId);
        // 底表是 tinyint(-128~127)
//        campaign.setSalesType(SalesType.NATIVE_ANCHOR.getCode());
        Integer campaignId = adCoreBqf.insert(lauCampaign).insertGetKey(campaign);
        //构造空单元
        LauUnitPo unit = new LauUnitPo();
        unit.setAccountId(accountId);
        unit.setCampaignId(campaignId);
//        unit.setSalesType(SalesType.NATIVE_ANCHOR.getCode());
        Integer unitId = adCoreBqf.insert(lauUnit).insertGetKey(unit);
        //构造空创意

        List<LauUnitCreativePo> creativePos = new ArrayList<>();
        for (Long aid : aids) {
            LauUnitCreativePo creative = new LauUnitCreativePo();
            creative.setAccountId(accountId);
            creative.setCampaignId(campaignId);
            creative.setUnitId(unitId);
            creative.setCreativeType(0);
            creative.setTemplateId(0);
            creative.setAuditStatus(0);
            creative.setStatus(0);
//        creative.setSalesType(SalesType.NATIVE_ANCHOR.getCode());
            creativePos.add(creative);
        }


        List<Integer> creativeIds = adCoreBqf.insert(lauUnitCreative).insertGetKeys(creativePos);

        BatchCreateAdThreeElementsBo createAdThreeElementsBo = BatchCreateAdThreeElementsBo.builder()
                .accountId(accountId)
                .campaignId(campaignId)
                .unitId(unitId)
                .creativeIds(creativeIds)
                .build();

        // 创意广告位组模板关系
        reSaveCreativesTemplateMappings(createAdThreeElementsBo, scenes);
        return createAdThreeElementsBo;
    }

    @Transactional(transactionManager = AD_CORE_TM, rollbackFor = Exception.class)
    public void updateCreateTemplatesByScenes(CreateAdThreeElementsBo createAdThreeElementsBo, List<Integer> scenes) {
        Integer creativeId = createAdThreeElementsBo.getCreativeId();
        Assert.isTrue(Utils.isPositive(creativeId), "creativeId 不能为空");

        // 删除旧关系
        List<LauCreativeTemplatePo> existCreTemPos = adCoreBqf.selectFrom(lauCreativeTemplate)
                .where(lauCreativeTemplate.creativeId.eq(Long.valueOf(creativeId)))
                .where(lauCreativeTemplate.isDeleted.eq(0))
                .fetch();
        if (!CollectionUtils.isEmpty(existCreTemPos)) {
            adCoreBqf.delete(lauCreativeTemplate)
                    .where(lauCreativeTemplate.creativeId.eq(Long.valueOf(creativeId)))
                    .where(lauCreativeTemplate.isDeleted.eq(0))
                    .execute();
        }

        // 创意广告位组模板关系
        reSaveCreativeTemplateMappings(createAdThreeElementsBo, scenes);
    }

    private void reSaveCreativeTemplateMappings(CreateAdThreeElementsBo createAdThreeElementsBo, List<Integer> scenes) {

        // 修改先删除旧数据
        Long creativeId = Long.valueOf(createAdThreeElementsBo.getCreativeId());

        List<LauCreativeTemplatePo> creativeTemplatePos = new ArrayList<>();
        for (Integer scene : scenes) {
            LauCreativeTemplatePo creativeTemplatePo = new LauCreativeTemplatePo();
            creativeTemplatePo.setCreativeId(creativeId);
            creativeTemplatePo.setUnitId(createAdThreeElementsBo.getUnitId());
            creativeTemplatePo.setAccountId(createAdThreeElementsBo.getAccountId());

            if (Objects.equals(AnchorSceneType.UNDER_BOX_VALUE, scene)) {
                creativeTemplatePo.setTemplateId(creativePositionConfig.getTemplateIdAnchorUnderFrame());
                creativeTemplatePo.setSlotGroupId(creativePositionConfig.getSlotGroupIdAnchorUnderFrame());
                creativeTemplatePos.add(creativeTemplatePo);
            } else if (Objects.equals(AnchorSceneType.STORY_VALUE, scene)) {
                creativeTemplatePo.setTemplateId(creativePositionConfig.getTemplateIdAnchorStory());
                creativeTemplatePo.setSlotGroupId(creativePositionConfig.getSlotGroupIdAnchorStory());
                creativeTemplatePos.add(creativeTemplatePo);
            }
        }

        if (!CollectionUtils.isEmpty(creativeTemplatePos)) {
            adCoreBqf.insert(lauCreativeTemplate).insertBeans(creativeTemplatePos);
        }
    }

    private void reSaveCreativesTemplateMappings(BatchCreateAdThreeElementsBo createAdThreeElementsBo, List<Integer> scenes) {
        // 修改先删除旧数据

        List<LauCreativeTemplatePo> creativeTemplatePos = new ArrayList<>();
        for (Integer creativeId : createAdThreeElementsBo.getCreativeIds()) {
            Long creativeIdLong = Long.valueOf(creativeId);

            for (Integer scene : scenes) {
                LauCreativeTemplatePo creativeTemplatePo = new LauCreativeTemplatePo();
                creativeTemplatePo.setCreativeId(creativeIdLong);
                creativeTemplatePo.setUnitId(createAdThreeElementsBo.getUnitId());
                creativeTemplatePo.setAccountId(createAdThreeElementsBo.getAccountId());

                if (Objects.equals(AnchorSceneType.UNDER_BOX_VALUE, scene)) {
                    creativeTemplatePo.setTemplateId(creativePositionConfig.getTemplateIdAnchorUnderFrame());
                    creativeTemplatePo.setSlotGroupId(creativePositionConfig.getSlotGroupIdAnchorUnderFrame());
                    creativeTemplatePos.add(creativeTemplatePo);
                } else if (Objects.equals(AnchorSceneType.STORY_VALUE, scene)) {
                    creativeTemplatePo.setTemplateId(creativePositionConfig.getTemplateIdAnchorStory());
                    creativeTemplatePo.setSlotGroupId(creativePositionConfig.getSlotGroupIdAnchorStory());
                    creativeTemplatePos.add(creativeTemplatePo);
                }
            }
        }

        if (!CollectionUtils.isEmpty(creativeTemplatePos)) {
            adCoreBqf.insert(lauCreativeTemplate).insertBeans(creativeTemplatePos);
        }

    }

    /**
     * 单个修改锚点
     *
     * @param bo
     * @param operator
     * @return
     */
    @Override
    @Transactional(transactionManager = AD_CORE_TM, rollbackFor = Exception.class)
    public Integer updateAnchor(AnchorSaveBo bo, Operator operator) {
        log.info("updateAnchor bo = {}", bo);
        AnchorSaveContext anchorSaveContext = AnchorSaveContext.init(bo, operator, true);

        // 改为异步处理后，前端不再传 id
        if (bo.getId() == null || bo.getId() == 0) {
            if (bo.getAid() == null || bo.getAid() == 0) {
                throw new ServiceRuntimeException("id和aid不能同时为空");
            }

            QueryAnchorPageListReq listReq = QueryAnchorPageListReq.newBuilder()
                    .setPage(1)
                    .setSize(2)
                    .addAllAid(Collections.singletonList(bo.getAid()))
                    .addIsDeleted(0)
                    .build();
            List<SingleQueryAnchorRep> dataList = cpmScvProxy.queryAnchorPageList(listReq);
            bo.setId(dataList.get(0).getId());
        }

        preProcess(anchorSaveContext);

        AnchorSaveBo oldAnchorBo = anchorSaveContext.getOldAnchorSaveBo();
        AccountBaseDto accountBaseDto = anchorSaveContext.getAccountBaseDto();
        CreateAdThreeElementsBo adThreeElements = anchorSaveContext.getCreateAdThreeElementsBo();
        Assert.isTrue(oldAnchorBo.getAccountId().equals(operator.getOperatorId()), "不能操作其他账户数据");

        // 保存游戏信息，目前只会有安卓游戏
        boolean isSubPkg = false;
        // 游戏下载
        if (Objects.equals(AnchorType.GAME_DOWNLOAD_VALUE, bo.getType())) {
            Integer gameBaseId = bo.getGameBaseId();
            bilibiliGameService.saveGameInfo(GamePlatformType.ANDROID_VALUE, gameBaseId);

            isSubPkg = GamePackage.isSubPkg(bo.getSubPkg());
            if (isSubPkg) {
                final Set<Integer> gameBaseIds =
                        adpCpcGameService.fetchSubPkgAvailableGameBaseIds(Collections.singletonList(gameBaseId));
                Assert.isTrue(!CollectionUtils.isEmpty(gameBaseIds), MessageFormat.format("游戏id={0}对应的游戏包失效", gameBaseId));
            }

            // 保存内广 android 游戏 creative monitor url
            List<LauCreativeMonitoringPo> creativeMonitoringPos = launchCreativeMonitoringService.listByCreativeId(adThreeElements.getCreativeId().longValue());
            if (CollectionUtils.isEmpty(creativeMonitoringPos)) {
                createCreativeMonitor(bo, adThreeElements, isSubPkg, Utils.isPositive(accountBaseDto.getIsInner()));
            } else {
                launchCreativeMonitoringService.deleteByCreativeId(adThreeElements.getCreativeId().longValue());
                createCreativeMonitor(bo, adThreeElements, isSubPkg, Utils.isPositive(accountBaseDto.getIsInner()));
            }
        }

        ArchiveBo archiveBo = archiveService.getArchiveByAid(bo.getAid());

        bo.setConversionUrl(getUrlByType(bo.getType(), bo.getConversionUrlType(), bo.getConversionUrlPageId(),
                bo.getConversionUrl(), isSubPkg));

        if (Objects.equals(bo.getType(), AnchorType.WECHAT_MINI_GAME_VALUE) && Utils.isPositive(bo.getIsOpenMiniGame())) {
            LauMiniGameDto miniGameDto = lauMiniGameService.getLauMiniGameById(bo.getMiniGameId());
            Assert.isTrue(miniGameDto != null, "微信小游戏不存在");
            Assert.isTrue(YesNoEnum.YES.getCode().equals(miniGameDto.getState()), "微信小游戏已经下线，请检查");
            Assert.isTrue(!StringUtils.isEmpty(miniGameDto.getGameUrl()), "微信小游戏url为空，请检查！");
        }
        //如果是线索类型组件 用兜底链接填充双端链接(引擎老板们的要求）
        if (Objects.equals(bo.getType(), AnchorType.CLUE_VALUE) || Objects.equals(bo.getType(), AnchorType.WECHAT_MINI_GAME_VALUE)) {
            bo.setIosUrl(bo.getConversionUrl());
            bo.setAndroidUrl(bo.getConversionUrl());
        } else {
            bo.setIosUrl(getUrlByType(bo.getType(), bo.getIosUrlType(), bo.getIosUrlPageId(), bo.getIosUrl(), isSubPkg));
            bo.setAndroidUrl(getUrlByType(bo.getType(), bo.getAndroidUrlType(), bo.getAndroidUrlPageId(), bo.getAndroidUrl(), isSubPkg));
        }

        // b站小游戏
        if (Objects.equals(AnchorType.BILI_MINI_GAME.getNumber(), bo.getType())) {
            // 查询小游戏校验，并填充
            BiliMiniGameBo biliMiniGameBo = launchBiliMiniGameService.get(bo.getBiliMiniGameMid(), bo.getGameBaseId());
            Assert.isTrue(biliMiniGameBo != null, "b站小游戏不存在");
            Assert.isTrue(!StringUtils.isEmpty(biliMiniGameBo.getGameLink()), "b站小游戏链接不存在");
            // 三端地址
            bo.setConversionUrl(miniGameH5BottomLink);
            // 获取 game link 格式: https://miniapp.bilibili.com/game/biligamea0e63a01fa11c978?sourcefrom=**********&track_id=__TRACKID__&account_id=__ACCOUNTID__&campaign_id=__CAMPAIGNID__&unit_id=__UNITID__&creative_id=__CREATIVEID__ 
            String url = UriComponentsBuilder.fromUriString(biliMiniGameBo.getGameLink())
                    .queryParam(CpcSaveCreativeService.SOURCE_FROM, CpcSaveCreativeService.SOURCE_FROM_BILI_GAME_ANCHOR)
                    .queryParam(track_id, __TRACKID__)
                    .queryParam(CpcSaveCreativeService.ACCOUNT_ID, CpcSaveCreativeService.__ACCOUNT_ID__)
                    .queryParam(CpcSaveCreativeService.CAMPAIGN_ID, CpcSaveCreativeService.__CAMPAIGN_ID__)
                    .queryParam(CpcSaveCreativeService.UNIT_ID, CpcSaveCreativeService.__UNIT_ID__)
                    .queryParam(CpcSaveCreativeService.CREATIVE_ID, CpcSaveCreativeService.__CREATIVE_ID__)
                    .queryParam(CpcSaveCreativeService.BV_ID, CpcSaveCreativeService.__BV_ID__)
                    .build(false).toUriString();
            bo.setIosUrl(url);
            bo.setAndroidUrl(url);
        }

        // b站小程序
        buildAppletUrl(bo);

        if (Objects.equals(AnchorType.APP.getNumber(), bo.getType())) {
            if (StringUtils.isEmpty(bo.getAndroidSchemaUrl()) && !StringUtils.isEmpty(bo.getIosSchemaUrl())) {
                // 应用推广选择了 IOS
                bo.setIosButtonSchemaUrl(bo.getIosSchemaUrl());
            } else if (!StringUtils.isEmpty(bo.getAndroidSchemaUrl())) {
                if (!Objects.isNull(bo.getIosAppPackageId()) && bo.getIosAppPackageId() != 0) {
                    // 应用推广，选择了 IOS 安卓，此时 卡片、按钮，四个跳转连接是同一个
                    String schemaUrl = Optional.ofNullable(bo.getAndroidSchemaUrl()).orElse("");
                    bo.setAndroidSchemaUrl(schemaUrl);
                    bo.setIosSchemaUrl(schemaUrl);
                    bo.setAndroidButtonSchemaUrl(schemaUrl);
                    bo.setIosButtonSchemaUrl(schemaUrl);
                } else {
                    // IOS信息为空，说明选择了安卓
                    bo.setAndroidButtonSchemaUrl(bo.getAndroidSchemaUrl());
                }
            }

        }

        // 重新保存创意模板关系
        updateCreateTemplatesByScenes(adThreeElements, bo.getScenes());

        com.bapis.ad.audit.Operator grpcOoperator = OperatorConverter.MAPPER.operator2GrpcOperator(operator);
        List<String> topImgUrls = bo.getTopImgUrls().stream().map(t -> t.getUrl()).collect(Collectors.toList());
        List<String> appImgUrls = bo.getAppImgUrls().stream().map(t -> t.getUrl()).collect(Collectors.toList());

        UpdateAnchorReq createAnchorReq = anchorConvertorImpl.convertUpdateBo2Grpc(bo, accountBaseDto,
                archiveBo.getAuthor(), grpcOoperator, topImgUrls, appImgUrls);

        // 打点
        Attributes attributes = Attributes.of(AttributeKey.stringKey(count_type), MetricsKeyConstant.count_type_anchor,
                AttributeKey.stringKey(a_operator), a_operator_update,
                AttributeKey.stringKey(response_code), response_code_request
        );
        customMetrics.count(1, attributes);

        // 先写日志，后RPC，RPC失败，则回滚
        logOperateForLongService.addUpdateLog(DbTable.LAU_ARCHIVE_ANCHOR_POINT, operator, oldAnchorBo, bo,
                bo.getId());

        UpdateCountReply updateCountReply = cpmScvProxy.updateAnchor(createAnchorReq);

        Attributes attributesResponse = Attributes.of(AttributeKey.stringKey(count_type), MetricsKeyConstant.count_type_anchor,
                AttributeKey.stringKey(a_operator), a_operator_update,
                AttributeKey.stringKey(response_code), response_code_success
        );
        customMetrics.count(1, attributesResponse);
        return updateCountReply.getCount();
    }

    public void buildAppletUrl(AnchorSaveBo bo) {
        if (Objects.equals(AnchorType.BILI_APPLET.getNumber(), bo.getType())) {
            Assert.isTrue(!StringUtils.isEmpty(bo.getBiliAppletUrl()), "b站小程序链接不存在");
            // 兜底链接不需要
            bo.setConversionUrl("");
            // 获取 game link 格式: https://miniapp.bilibili.com/game/biligamea0e63a01fa11c978?sourcefrom=**********&track_id=__TRACKID__&account_id=__ACCOUNTID__&campaign_id=__CAMPAIGNID__&unit_id=__UNITID__&creative_id=__CREATIVEID__ 
            String url = UriComponentsBuilder.fromUriString(bo.getBiliAppletUrl())
                    .queryParam(CpcSaveCreativeService.SOURCE_FROM, CpcSaveCreativeService.SOURCE_FROM_BILI_APPLET_ANCHOR)
                    .queryParam(track_id, __TRACKID__)
                    .queryParam(CpcSaveCreativeService.ACCOUNT_ID, CpcSaveCreativeService.__ACCOUNT_ID__)
                    .queryParam(CpcSaveCreativeService.CAMPAIGN_ID, CpcSaveCreativeService.__CAMPAIGN_ID__)
                    .queryParam(CpcSaveCreativeService.UNIT_ID, CpcSaveCreativeService.__UNIT_ID__)
                    .queryParam(CpcSaveCreativeService.CREATIVE_ID, CpcSaveCreativeService.__CREATIVE_ID__)
                    .queryParam(CpcSaveCreativeService.BV_ID, CpcSaveCreativeService.__BV_ID__)
                    .build(false).toUriString();
            bo.setIosUrl(url);
            bo.setAndroidUrl(url);
        }
    }

    /**
     * 将http的入参，转换成 bo后，直接透传给 pandora任务中心
     *
     * @param anchorSaveBo
     * @param operator
     * @return
     */
    @Override
    public Long updateAnchorBatch(AnchorSaveBo anchorSaveBo, Operator operator) {
        if (!CollectionUtils.isEmpty(anchorSaveBo.getAids())) {
            Assert.isTrue(anchorSaveBo.getAids().size() <= componentBatchMaxSize, "单次最多支持" + componentBatchMaxSize + "个稿件");
        }

        List<AnchorSaveBatch> anchorSaveBatches = Collections.singletonList(AnchorConverter.MAPPER.toRpcBo(anchorSaveBo, operator));
        RegisterAnchorOperationReq req = RegisterAnchorOperationReq.newBuilder()
                .setOperator(AnchorConverter.MAPPER.toRpcBo(operator))
                .addAllOperation(anchorSaveBatches)
                .setOperationType(BatchOperationType.BATCH_COMPONENT_UPDATE).build();
        RegisterOperationResp registerOperationResp = cpmPandoraProxy.registerAnchorOperation(req);
        return registerOperationResp.getOperationId();
    }

    @Override
    public Integer anchorGroup(AnchorSaveBo anchorSaveBo, Operator operator) {
        AnchorGroupReq anchorGroupReq = AnchorGroupReq.newBuilder()
                .setGroupId(anchorSaveBo.getGroupId())
                .addAllIds(anchorSaveBo.getIds()).build();
        UpdateCountReply group = cpmScvProxy.anchorGroup(anchorGroupReq);
        return group.getCount();
    }

    private void preProcess(AnchorSaveContext anchorSaveContext) {
        setDefaultValue(anchorSaveContext);
        validate(anchorSaveContext);
        preHandleScenes(anchorSaveContext);
        preHandler(anchorSaveContext);

//        validateAndroidNewFields(anchorSaveContext);
    }

    @Deprecated
    private void createCreativeMonitor(AnchorSaveBo bo, CreateAdThreeElementsBo adThreeElements, boolean isSubPkg, boolean isInner) {
        LauCreativeMonitoringPo creativeMonitoringPo = new LauCreativeMonitoringPo();
        creativeMonitoringPo.setUnitId(adThreeElements.getUnitId());
        creativeMonitoringPo.setCreativeId(adThreeElements.getCreativeId().longValue());
        creativeMonitoringPo.setType(CreativeMonitorType.GAME_CLICK_MONITOR.getCode());
        creativeMonitoringPo.setUrl(adpCpcLauUnitGameService.getAutoGameMonitorUrl(bo.getGameBaseId(),
                SanlianGamePlatformTypeEnum.ANDROID.getCode(), isSubPkg ? 1 : 0, isInner));
        launchCreativeMonitoringService.save(creativeMonitoringPo);
    }

    public void createCreativesMonitors(Integer gameBaseId, BatchCreateAdThreeElementsBo adThreeElements, boolean isSubPkg, boolean isInner, List<LauCreativeMonitoringPo> existMonitoringPos) {

        List<LauCreativeMonitoringPo> monitoringPos = new ArrayList<>();
        for (Integer creativeId : adThreeElements.getCreativeIds()) {
            LauCreativeMonitoringPo creativeMonitoringPo = new LauCreativeMonitoringPo();
            creativeMonitoringPo.setUnitId(adThreeElements.getUnitId());
            creativeMonitoringPo.setCreativeId(creativeId.longValue());
            creativeMonitoringPo.setType(CreativeMonitorType.GAME_CLICK_MONITOR.getCode());
            creativeMonitoringPo.setUrl(adpCpcLauUnitGameService.getAutoGameMonitorUrl(gameBaseId,
                    SanlianGamePlatformTypeEnum.ANDROID.getCode(), isSubPkg ? 1 : 0, isInner));
            monitoringPos.add(creativeMonitoringPo);
        }

        launchCreativeMonitoringService.save(monitoringPos, existMonitoringPos);
    }

    @Override
    @Transactional(transactionManager = AD_CORE_TM, rollbackFor = Exception.class)
    public Integer deleteAnchor(Long id, Operator operator) {
        AnchorInfoBo anchorInfoBo = this.fetchAnchorDetail(id);
        Assert.isTrue(anchorInfoBo.getAccountId().equals(operator.getOperatorId()), "不能操作其他账户数据");

        UpdateCountReply updateCountReply = cpmScvProxy.deleteAnchor(id);

        // 操作日志
        logOperateForLongService.addDeleteLog(DbTable.LAU_ARCHIVE_ANCHOR_POINT, operator, anchorInfoBo, id);

        Attributes attributes = Attributes.of(AttributeKey.stringKey(count_type), MetricsKeyConstant.count_type_anchor,
                AttributeKey.stringKey(a_operator), a_operator_delete,
                AttributeKey.stringKey(response_code), response_code_success
        );
        customMetrics.count(1, attributes);
        return updateCountReply.getCount();
    }

    @Override
    public Integer deleteAnchorBatchForBusinessTool(AnchorSaveBo saveBo, Operator operator) {

        List<Long> ids = saveBo.getIds();

        AnchorInfoBo anchorInfoBo = this.fetchAnchorDetail(id);
        Assert.isTrue(anchorInfoBo.getAccountId().equals(operator.getOperatorId()), "不能操作其他账户数据");

        UpdateCountReply updateCountReply = cpmScvProxy.batchDeleteAnchor(ids);

        // 操作日志
        logOperateForLongService.addBatchDeleteLog(DbTable.LAU_ARCHIVE_ANCHOR_POINT, operator, anchorInfoBo, id);

        Attributes attributes = Attributes.of(AttributeKey.stringKey(count_type), MetricsKeyConstant.count_type_anchor,
                AttributeKey.stringKey(a_operator), a_operator_delete,
                AttributeKey.stringKey(response_code), response_code_success
        );
        customMetrics.count(1, attributes);
        return updateCountReply.getCount();
    }

    /**
     * 批量删除，入参仅为 anchorId
     *
     * @param saveBo
     * @param operator
     * @return
     */
    @Override
    public Long deleteAnchorBatch(AnchorSaveBo saveBo, Operator operator) {
        RegisterAnchorOperationReq req = RegisterAnchorOperationReq.newBuilder()
                .setOperator(AnchorConverter.MAPPER.toRpcBo(operator))
                .addAllOperation(Collections.singletonList(AnchorConverter.MAPPER.toRpcBo(saveBo, operator)))
                .setOperationType(BatchOperationType.BATCH_COMPONENT_DELETE).build();
        RegisterOperationResp registerOperationResp = cpmPandoraProxy.registerAnchorOperation(req);
        return registerOperationResp.getOperationId();
    }

    @Override
    public AnchorPreviewBo queryAnchorByPriority(Long avid) {

        return anchorQuerier.queryBizAnchorByPriority(avid);
    }

    @Override
    public List<Integer> queryAnchorRelatedCreativeIds(Long anchorId) {
        if (!Utils.isPositive(anchorId)) {
            return Collections.EMPTY_LIST;
        }

        List<LauCreativeAnchorMappingPo> anchorMappingPos = creativeAnchorMappingRepo.queryCreativeAnchorMappingList(Arrays.asList(anchorId));
        List<Integer> creativeIds = anchorMappingPos.stream().map(t -> t.getCreativeId()).distinct().collect(Collectors.toList());

        Set<Integer> auditPassedCreativeIdSet = lauUnitCreativeRepo.queryAuditPassedCreativeIds(creativeIds);
        // 过滤出审核通过的的创意
        anchorMappingPos = anchorMappingPos.stream().filter(anchorMappingPo -> auditPassedCreativeIdSet.contains(anchorMappingPo.getCreativeId())).collect(Collectors.toList());
        return anchorMappingPos.stream().map(t -> t.getCreativeId()).collect(Collectors.toList());
    }

    @Override
    public List<AnchorPreviewBo> queryBizAnchorsByPriority(List<Long> avids) {

        return archiveAnchorQuerier.queryBizAnchorsByPriority(avids);
    }

    @Override
    public Map<Long, AnchorPreviewBo> queryBizAnchorsMapByPriority(List<Long> avids) {
        return archiveAnchorQuerier.queryBizAnchorsMapByPriority(avids);
    }

    @Override
    public List<AnchorBizSimpleBo> querySimpleBizAnchorByPriority(List<Long> avids) {
        return archiveAnchorQuerier.querySimpleBizAnchorByPriority(avids);
    }

    @Override
    public Map<Long, AnchorBizSimpleBo> querySimpleBizAnchorMapByPriority(List<Long> avids) {

        List<AnchorBizSimpleBo> anchorBizSimpleBos = archiveAnchorQuerier.querySimpleBizAnchorByPriority(avids);
        return anchorBizSimpleBos.stream().collect(Collectors.toMap(t -> t.getAid(), t -> t));
    }

    public String getConfigMiniGameH5BottomLink() {
        return miniGameH5BottomLink;
    }

    public void fixAnchorWithoutTemplate(Integer accountId) {
        List<LauArchiveAnchorPointPo> res = adBqf.selectFrom(QLauArchiveAnchorPoint.lauArchiveAnchorPoint)
                .where(QLauArchiveAnchorPoint.lauArchiveAnchorPoint.accountId.eq(accountId)
                        .and(QLauArchiveAnchorPoint.lauArchiveAnchorPoint.scenes.isEmpty()))
                .fetch();
        if (CollectionUtils.isEmpty(res)) {
            return;
        }
        res.forEach(t -> {

            adBqf.update(QLauArchiveAnchorPoint.lauArchiveAnchorPoint)
                    .set(QLauArchiveAnchorPoint.lauArchiveAnchorPoint.scenes, "1,3")
                    .where(QLauArchiveAnchorPoint.lauArchiveAnchorPoint.id.eq(t.getId())).execute();

            updateCreateTemplatesByScenes(CreateAdThreeElementsBo.builder()
                    .accountId(defaultAccountId)
                    .campaignId(t.getCampaignId())
                    .unitId(t.getUnitId())
                    .creativeId(t.getCreativeId())
                    .build(), AnchorSceneEnum.SCENES_LIST);
        });
    }
}
