package com.bilibili.adp.cpc.automatic_rule.converter;

import com.bilibili.adp.cpc.dao.querydsl.pos.LauAutomaticRuleObjectPo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper
public interface ObjectConverter {
    ObjectConverter MAPPER = Mappers.getMapper(ObjectConverter.class);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "isDeleted", ignore = true)
    @Mapping(target = "ctime", ignore = true)
    @Mapping(target = "mtime", ignore = true)
    LauAutomaticRuleObjectPo bo2Po(Integer accountId, Long ruleId, Integer objectType, Integer objectId);
}
