package com.bilibili.adp.cpc.po.ad;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public class AdProductNovelPoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public AdProductNovelPoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAccountIdIsNull() {
            addCriterion("account_id is null");
            return (Criteria) this;
        }

        public Criteria andAccountIdIsNotNull() {
            addCriterion("account_id is not null");
            return (Criteria) this;
        }

        public Criteria andAccountIdEqualTo(Integer value) {
            addCriterion("account_id =", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdNotEqualTo(Integer value) {
            addCriterion("account_id <>", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdGreaterThan(Integer value) {
            addCriterion("account_id >", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("account_id >=", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdLessThan(Integer value) {
            addCriterion("account_id <", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdLessThanOrEqualTo(Integer value) {
            addCriterion("account_id <=", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdIn(List<Integer> values) {
            addCriterion("account_id in", values, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdNotIn(List<Integer> values) {
            addCriterion("account_id not in", values, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdBetween(Integer value1, Integer value2) {
            addCriterion("account_id between", value1, value2, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdNotBetween(Integer value1, Integer value2) {
            addCriterion("account_id not between", value1, value2, "accountId");
            return (Criteria) this;
        }

        public Criteria andLibraryIdIsNull() {
            addCriterion("library_id is null");
            return (Criteria) this;
        }

        public Criteria andLibraryIdIsNotNull() {
            addCriterion("library_id is not null");
            return (Criteria) this;
        }

        public Criteria andLibraryIdEqualTo(Long value) {
            addCriterion("library_id =", value, "libraryId");
            return (Criteria) this;
        }

        public Criteria andLibraryIdNotEqualTo(Long value) {
            addCriterion("library_id <>", value, "libraryId");
            return (Criteria) this;
        }

        public Criteria andLibraryIdGreaterThan(Long value) {
            addCriterion("library_id >", value, "libraryId");
            return (Criteria) this;
        }

        public Criteria andLibraryIdGreaterThanOrEqualTo(Long value) {
            addCriterion("library_id >=", value, "libraryId");
            return (Criteria) this;
        }

        public Criteria andLibraryIdLessThan(Long value) {
            addCriterion("library_id <", value, "libraryId");
            return (Criteria) this;
        }

        public Criteria andLibraryIdLessThanOrEqualTo(Long value) {
            addCriterion("library_id <=", value, "libraryId");
            return (Criteria) this;
        }

        public Criteria andLibraryIdIn(List<Long> values) {
            addCriterion("library_id in", values, "libraryId");
            return (Criteria) this;
        }

        public Criteria andLibraryIdNotIn(List<Long> values) {
            addCriterion("library_id not in", values, "libraryId");
            return (Criteria) this;
        }

        public Criteria andLibraryIdBetween(Long value1, Long value2) {
            addCriterion("library_id between", value1, value2, "libraryId");
            return (Criteria) this;
        }

        public Criteria andLibraryIdNotBetween(Long value1, Long value2) {
            addCriterion("library_id not between", value1, value2, "libraryId");
            return (Criteria) this;
        }

        public Criteria andAdProductIdIsNull() {
            addCriterion("ad_product_id is null");
            return (Criteria) this;
        }

        public Criteria andAdProductIdIsNotNull() {
            addCriterion("ad_product_id is not null");
            return (Criteria) this;
        }

        public Criteria andAdProductIdEqualTo(Long value) {
            addCriterion("ad_product_id =", value, "adProductId");
            return (Criteria) this;
        }

        public Criteria andAdProductIdNotEqualTo(Long value) {
            addCriterion("ad_product_id <>", value, "adProductId");
            return (Criteria) this;
        }

        public Criteria andAdProductIdGreaterThan(Long value) {
            addCriterion("ad_product_id >", value, "adProductId");
            return (Criteria) this;
        }

        public Criteria andAdProductIdGreaterThanOrEqualTo(Long value) {
            addCriterion("ad_product_id >=", value, "adProductId");
            return (Criteria) this;
        }

        public Criteria andAdProductIdLessThan(Long value) {
            addCriterion("ad_product_id <", value, "adProductId");
            return (Criteria) this;
        }

        public Criteria andAdProductIdLessThanOrEqualTo(Long value) {
            addCriterion("ad_product_id <=", value, "adProductId");
            return (Criteria) this;
        }

        public Criteria andAdProductIdIn(List<Long> values) {
            addCriterion("ad_product_id in", values, "adProductId");
            return (Criteria) this;
        }

        public Criteria andAdProductIdNotIn(List<Long> values) {
            addCriterion("ad_product_id not in", values, "adProductId");
            return (Criteria) this;
        }

        public Criteria andAdProductIdBetween(Long value1, Long value2) {
            addCriterion("ad_product_id between", value1, value2, "adProductId");
            return (Criteria) this;
        }

        public Criteria andAdProductIdNotBetween(Long value1, Long value2) {
            addCriterion("ad_product_id not between", value1, value2, "adProductId");
            return (Criteria) this;
        }

        public Criteria andBizStatusIsNull() {
            addCriterion("biz_status is null");
            return (Criteria) this;
        }

        public Criteria andBizStatusIsNotNull() {
            addCriterion("biz_status is not null");
            return (Criteria) this;
        }

        public Criteria andBizStatusEqualTo(Integer value) {
            addCriterion("biz_status =", value, "bizStatus");
            return (Criteria) this;
        }

        public Criteria andBizStatusNotEqualTo(Integer value) {
            addCriterion("biz_status <>", value, "bizStatus");
            return (Criteria) this;
        }

        public Criteria andBizStatusGreaterThan(Integer value) {
            addCriterion("biz_status >", value, "bizStatus");
            return (Criteria) this;
        }

        public Criteria andBizStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("biz_status >=", value, "bizStatus");
            return (Criteria) this;
        }

        public Criteria andBizStatusLessThan(Integer value) {
            addCriterion("biz_status <", value, "bizStatus");
            return (Criteria) this;
        }

        public Criteria andBizStatusLessThanOrEqualTo(Integer value) {
            addCriterion("biz_status <=", value, "bizStatus");
            return (Criteria) this;
        }

        public Criteria andBizStatusIn(List<Integer> values) {
            addCriterion("biz_status in", values, "bizStatus");
            return (Criteria) this;
        }

        public Criteria andBizStatusNotIn(List<Integer> values) {
            addCriterion("biz_status not in", values, "bizStatus");
            return (Criteria) this;
        }

        public Criteria andBizStatusBetween(Integer value1, Integer value2) {
            addCriterion("biz_status between", value1, value2, "bizStatus");
            return (Criteria) this;
        }

        public Criteria andBizStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("biz_status not between", value1, value2, "bizStatus");
            return (Criteria) this;
        }

        public Criteria andNovelNameIsNull() {
            addCriterion("novel_name is null");
            return (Criteria) this;
        }

        public Criteria andNovelNameIsNotNull() {
            addCriterion("novel_name is not null");
            return (Criteria) this;
        }

        public Criteria andNovelNameEqualTo(String value) {
            addCriterion("novel_name =", value, "novelName");
            return (Criteria) this;
        }

        public Criteria andNovelNameNotEqualTo(String value) {
            addCriterion("novel_name <>", value, "novelName");
            return (Criteria) this;
        }

        public Criteria andNovelNameGreaterThan(String value) {
            addCriterion("novel_name >", value, "novelName");
            return (Criteria) this;
        }

        public Criteria andNovelNameGreaterThanOrEqualTo(String value) {
            addCriterion("novel_name >=", value, "novelName");
            return (Criteria) this;
        }

        public Criteria andNovelNameLessThan(String value) {
            addCriterion("novel_name <", value, "novelName");
            return (Criteria) this;
        }

        public Criteria andNovelNameLessThanOrEqualTo(String value) {
            addCriterion("novel_name <=", value, "novelName");
            return (Criteria) this;
        }

        public Criteria andNovelNameLike(String value) {
            addCriterion("novel_name like", value, "novelName");
            return (Criteria) this;
        }

        public Criteria andNovelNameNotLike(String value) {
            addCriterion("novel_name not like", value, "novelName");
            return (Criteria) this;
        }

        public Criteria andNovelNameIn(List<String> values) {
            addCriterion("novel_name in", values, "novelName");
            return (Criteria) this;
        }

        public Criteria andNovelNameNotIn(List<String> values) {
            addCriterion("novel_name not in", values, "novelName");
            return (Criteria) this;
        }

        public Criteria andNovelNameBetween(String value1, String value2) {
            addCriterion("novel_name between", value1, value2, "novelName");
            return (Criteria) this;
        }

        public Criteria andNovelNameNotBetween(String value1, String value2) {
            addCriterion("novel_name not between", value1, value2, "novelName");
            return (Criteria) this;
        }

        public Criteria andChannelIsNull() {
            addCriterion("channel is null");
            return (Criteria) this;
        }

        public Criteria andChannelIsNotNull() {
            addCriterion("channel is not null");
            return (Criteria) this;
        }

        public Criteria andChannelEqualTo(String value) {
            addCriterion("channel =", value, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelNotEqualTo(String value) {
            addCriterion("channel <>", value, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelGreaterThan(String value) {
            addCriterion("channel >", value, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelGreaterThanOrEqualTo(String value) {
            addCriterion("channel >=", value, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelLessThan(String value) {
            addCriterion("channel <", value, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelLessThanOrEqualTo(String value) {
            addCriterion("channel <=", value, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelLike(String value) {
            addCriterion("channel like", value, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelNotLike(String value) {
            addCriterion("channel not like", value, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelIn(List<String> values) {
            addCriterion("channel in", values, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelNotIn(List<String> values) {
            addCriterion("channel not in", values, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelBetween(String value1, String value2) {
            addCriterion("channel between", value1, value2, "channel");
            return (Criteria) this;
        }

        public Criteria andChannelNotBetween(String value1, String value2) {
            addCriterion("channel not between", value1, value2, "channel");
            return (Criteria) this;
        }

        public Criteria andThemeIsNull() {
            addCriterion("theme is null");
            return (Criteria) this;
        }

        public Criteria andThemeIsNotNull() {
            addCriterion("theme is not null");
            return (Criteria) this;
        }

        public Criteria andThemeEqualTo(String value) {
            addCriterion("theme =", value, "theme");
            return (Criteria) this;
        }

        public Criteria andThemeNotEqualTo(String value) {
            addCriterion("theme <>", value, "theme");
            return (Criteria) this;
        }

        public Criteria andThemeGreaterThan(String value) {
            addCriterion("theme >", value, "theme");
            return (Criteria) this;
        }

        public Criteria andThemeGreaterThanOrEqualTo(String value) {
            addCriterion("theme >=", value, "theme");
            return (Criteria) this;
        }

        public Criteria andThemeLessThan(String value) {
            addCriterion("theme <", value, "theme");
            return (Criteria) this;
        }

        public Criteria andThemeLessThanOrEqualTo(String value) {
            addCriterion("theme <=", value, "theme");
            return (Criteria) this;
        }

        public Criteria andThemeLike(String value) {
            addCriterion("theme like", value, "theme");
            return (Criteria) this;
        }

        public Criteria andThemeNotLike(String value) {
            addCriterion("theme not like", value, "theme");
            return (Criteria) this;
        }

        public Criteria andThemeIn(List<String> values) {
            addCriterion("theme in", values, "theme");
            return (Criteria) this;
        }

        public Criteria andThemeNotIn(List<String> values) {
            addCriterion("theme not in", values, "theme");
            return (Criteria) this;
        }

        public Criteria andThemeBetween(String value1, String value2) {
            addCriterion("theme between", value1, value2, "theme");
            return (Criteria) this;
        }

        public Criteria andThemeNotBetween(String value1, String value2) {
            addCriterion("theme not between", value1, value2, "theme");
            return (Criteria) this;
        }

        public Criteria andChapterNumIsNull() {
            addCriterion("chapter_num is null");
            return (Criteria) this;
        }

        public Criteria andChapterNumIsNotNull() {
            addCriterion("chapter_num is not null");
            return (Criteria) this;
        }

        public Criteria andChapterNumEqualTo(Integer value) {
            addCriterion("chapter_num =", value, "chapterNum");
            return (Criteria) this;
        }

        public Criteria andChapterNumNotEqualTo(Integer value) {
            addCriterion("chapter_num <>", value, "chapterNum");
            return (Criteria) this;
        }

        public Criteria andChapterNumGreaterThan(Integer value) {
            addCriterion("chapter_num >", value, "chapterNum");
            return (Criteria) this;
        }

        public Criteria andChapterNumGreaterThanOrEqualTo(Integer value) {
            addCriterion("chapter_num >=", value, "chapterNum");
            return (Criteria) this;
        }

        public Criteria andChapterNumLessThan(Integer value) {
            addCriterion("chapter_num <", value, "chapterNum");
            return (Criteria) this;
        }

        public Criteria andChapterNumLessThanOrEqualTo(Integer value) {
            addCriterion("chapter_num <=", value, "chapterNum");
            return (Criteria) this;
        }

        public Criteria andChapterNumIn(List<Integer> values) {
            addCriterion("chapter_num in", values, "chapterNum");
            return (Criteria) this;
        }

        public Criteria andChapterNumNotIn(List<Integer> values) {
            addCriterion("chapter_num not in", values, "chapterNum");
            return (Criteria) this;
        }

        public Criteria andChapterNumBetween(Integer value1, Integer value2) {
            addCriterion("chapter_num between", value1, value2, "chapterNum");
            return (Criteria) this;
        }

        public Criteria andChapterNumNotBetween(Integer value1, Integer value2) {
            addCriterion("chapter_num not between", value1, value2, "chapterNum");
            return (Criteria) this;
        }

        public Criteria andWordsNumIsNull() {
            addCriterion("words_num is null");
            return (Criteria) this;
        }

        public Criteria andWordsNumIsNotNull() {
            addCriterion("words_num is not null");
            return (Criteria) this;
        }

        public Criteria andWordsNumEqualTo(String value) {
            addCriterion("words_num =", value, "wordsNum");
            return (Criteria) this;
        }

        public Criteria andWordsNumNotEqualTo(String value) {
            addCriterion("words_num <>", value, "wordsNum");
            return (Criteria) this;
        }

        public Criteria andWordsNumGreaterThan(String value) {
            addCriterion("words_num >", value, "wordsNum");
            return (Criteria) this;
        }

        public Criteria andWordsNumGreaterThanOrEqualTo(String value) {
            addCriterion("words_num >=", value, "wordsNum");
            return (Criteria) this;
        }

        public Criteria andWordsNumLessThan(String value) {
            addCriterion("words_num <", value, "wordsNum");
            return (Criteria) this;
        }

        public Criteria andWordsNumLessThanOrEqualTo(String value) {
            addCriterion("words_num <=", value, "wordsNum");
            return (Criteria) this;
        }

        public Criteria andWordsNumLike(String value) {
            addCriterion("words_num like", value, "wordsNum");
            return (Criteria) this;
        }

        public Criteria andWordsNumNotLike(String value) {
            addCriterion("words_num not like", value, "wordsNum");
            return (Criteria) this;
        }

        public Criteria andWordsNumIn(List<String> values) {
            addCriterion("words_num in", values, "wordsNum");
            return (Criteria) this;
        }

        public Criteria andWordsNumNotIn(List<String> values) {
            addCriterion("words_num not in", values, "wordsNum");
            return (Criteria) this;
        }

        public Criteria andWordsNumBetween(String value1, String value2) {
            addCriterion("words_num between", value1, value2, "wordsNum");
            return (Criteria) this;
        }

        public Criteria andWordsNumNotBetween(String value1, String value2) {
            addCriterion("words_num not between", value1, value2, "wordsNum");
            return (Criteria) this;
        }

        public Criteria andUpdateStatusIsNull() {
            addCriterion("update_status is null");
            return (Criteria) this;
        }

        public Criteria andUpdateStatusIsNotNull() {
            addCriterion("update_status is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateStatusEqualTo(String value) {
            addCriterion("update_status =", value, "updateStatus");
            return (Criteria) this;
        }

        public Criteria andUpdateStatusNotEqualTo(String value) {
            addCriterion("update_status <>", value, "updateStatus");
            return (Criteria) this;
        }

        public Criteria andUpdateStatusGreaterThan(String value) {
            addCriterion("update_status >", value, "updateStatus");
            return (Criteria) this;
        }

        public Criteria andUpdateStatusGreaterThanOrEqualTo(String value) {
            addCriterion("update_status >=", value, "updateStatus");
            return (Criteria) this;
        }

        public Criteria andUpdateStatusLessThan(String value) {
            addCriterion("update_status <", value, "updateStatus");
            return (Criteria) this;
        }

        public Criteria andUpdateStatusLessThanOrEqualTo(String value) {
            addCriterion("update_status <=", value, "updateStatus");
            return (Criteria) this;
        }

        public Criteria andUpdateStatusLike(String value) {
            addCriterion("update_status like", value, "updateStatus");
            return (Criteria) this;
        }

        public Criteria andUpdateStatusNotLike(String value) {
            addCriterion("update_status not like", value, "updateStatus");
            return (Criteria) this;
        }

        public Criteria andUpdateStatusIn(List<String> values) {
            addCriterion("update_status in", values, "updateStatus");
            return (Criteria) this;
        }

        public Criteria andUpdateStatusNotIn(List<String> values) {
            addCriterion("update_status not in", values, "updateStatus");
            return (Criteria) this;
        }

        public Criteria andUpdateStatusBetween(String value1, String value2) {
            addCriterion("update_status between", value1, value2, "updateStatus");
            return (Criteria) this;
        }

        public Criteria andUpdateStatusNotBetween(String value1, String value2) {
            addCriterion("update_status not between", value1, value2, "updateStatus");
            return (Criteria) this;
        }

        public Criteria andAuthorIsNull() {
            addCriterion("author is null");
            return (Criteria) this;
        }

        public Criteria andAuthorIsNotNull() {
            addCriterion("author is not null");
            return (Criteria) this;
        }

        public Criteria andAuthorEqualTo(String value) {
            addCriterion("author =", value, "author");
            return (Criteria) this;
        }

        public Criteria andAuthorNotEqualTo(String value) {
            addCriterion("author <>", value, "author");
            return (Criteria) this;
        }

        public Criteria andAuthorGreaterThan(String value) {
            addCriterion("author >", value, "author");
            return (Criteria) this;
        }

        public Criteria andAuthorGreaterThanOrEqualTo(String value) {
            addCriterion("author >=", value, "author");
            return (Criteria) this;
        }

        public Criteria andAuthorLessThan(String value) {
            addCriterion("author <", value, "author");
            return (Criteria) this;
        }

        public Criteria andAuthorLessThanOrEqualTo(String value) {
            addCriterion("author <=", value, "author");
            return (Criteria) this;
        }

        public Criteria andAuthorLike(String value) {
            addCriterion("author like", value, "author");
            return (Criteria) this;
        }

        public Criteria andAuthorNotLike(String value) {
            addCriterion("author not like", value, "author");
            return (Criteria) this;
        }

        public Criteria andAuthorIn(List<String> values) {
            addCriterion("author in", values, "author");
            return (Criteria) this;
        }

        public Criteria andAuthorNotIn(List<String> values) {
            addCriterion("author not in", values, "author");
            return (Criteria) this;
        }

        public Criteria andAuthorBetween(String value1, String value2) {
            addCriterion("author between", value1, value2, "author");
            return (Criteria) this;
        }

        public Criteria andAuthorNotBetween(String value1, String value2) {
            addCriterion("author not between", value1, value2, "author");
            return (Criteria) this;
        }

        public Criteria andBriefIsNull() {
            addCriterion("brief is null");
            return (Criteria) this;
        }

        public Criteria andBriefIsNotNull() {
            addCriterion("brief is not null");
            return (Criteria) this;
        }

        public Criteria andBriefEqualTo(String value) {
            addCriterion("brief =", value, "brief");
            return (Criteria) this;
        }

        public Criteria andBriefNotEqualTo(String value) {
            addCriterion("brief <>", value, "brief");
            return (Criteria) this;
        }

        public Criteria andBriefGreaterThan(String value) {
            addCriterion("brief >", value, "brief");
            return (Criteria) this;
        }

        public Criteria andBriefGreaterThanOrEqualTo(String value) {
            addCriterion("brief >=", value, "brief");
            return (Criteria) this;
        }

        public Criteria andBriefLessThan(String value) {
            addCriterion("brief <", value, "brief");
            return (Criteria) this;
        }

        public Criteria andBriefLessThanOrEqualTo(String value) {
            addCriterion("brief <=", value, "brief");
            return (Criteria) this;
        }

        public Criteria andBriefLike(String value) {
            addCriterion("brief like", value, "brief");
            return (Criteria) this;
        }

        public Criteria andBriefNotLike(String value) {
            addCriterion("brief not like", value, "brief");
            return (Criteria) this;
        }

        public Criteria andBriefIn(List<String> values) {
            addCriterion("brief in", values, "brief");
            return (Criteria) this;
        }

        public Criteria andBriefNotIn(List<String> values) {
            addCriterion("brief not in", values, "brief");
            return (Criteria) this;
        }

        public Criteria andBriefBetween(String value1, String value2) {
            addCriterion("brief between", value1, value2, "brief");
            return (Criteria) this;
        }

        public Criteria andBriefNotBetween(String value1, String value2) {
            addCriterion("brief not between", value1, value2, "brief");
            return (Criteria) this;
        }

        public Criteria andChapterPriceIsNull() {
            addCriterion("chapter_price is null");
            return (Criteria) this;
        }

        public Criteria andChapterPriceIsNotNull() {
            addCriterion("chapter_price is not null");
            return (Criteria) this;
        }

        public Criteria andChapterPriceEqualTo(String value) {
            addCriterion("chapter_price =", value, "chapterPrice");
            return (Criteria) this;
        }

        public Criteria andChapterPriceNotEqualTo(String value) {
            addCriterion("chapter_price <>", value, "chapterPrice");
            return (Criteria) this;
        }

        public Criteria andChapterPriceGreaterThan(String value) {
            addCriterion("chapter_price >", value, "chapterPrice");
            return (Criteria) this;
        }

        public Criteria andChapterPriceGreaterThanOrEqualTo(String value) {
            addCriterion("chapter_price >=", value, "chapterPrice");
            return (Criteria) this;
        }

        public Criteria andChapterPriceLessThan(String value) {
            addCriterion("chapter_price <", value, "chapterPrice");
            return (Criteria) this;
        }

        public Criteria andChapterPriceLessThanOrEqualTo(String value) {
            addCriterion("chapter_price <=", value, "chapterPrice");
            return (Criteria) this;
        }

        public Criteria andChapterPriceLike(String value) {
            addCriterion("chapter_price like", value, "chapterPrice");
            return (Criteria) this;
        }

        public Criteria andChapterPriceNotLike(String value) {
            addCriterion("chapter_price not like", value, "chapterPrice");
            return (Criteria) this;
        }

        public Criteria andChapterPriceIn(List<String> values) {
            addCriterion("chapter_price in", values, "chapterPrice");
            return (Criteria) this;
        }

        public Criteria andChapterPriceNotIn(List<String> values) {
            addCriterion("chapter_price not in", values, "chapterPrice");
            return (Criteria) this;
        }

        public Criteria andChapterPriceBetween(String value1, String value2) {
            addCriterion("chapter_price between", value1, value2, "chapterPrice");
            return (Criteria) this;
        }

        public Criteria andChapterPriceNotBetween(String value1, String value2) {
            addCriterion("chapter_price not between", value1, value2, "chapterPrice");
            return (Criteria) this;
        }

        public Criteria andFirstPayChapterIsNull() {
            addCriterion("first_pay_chapter is null");
            return (Criteria) this;
        }

        public Criteria andFirstPayChapterIsNotNull() {
            addCriterion("first_pay_chapter is not null");
            return (Criteria) this;
        }

        public Criteria andFirstPayChapterEqualTo(String value) {
            addCriterion("first_pay_chapter =", value, "firstPayChapter");
            return (Criteria) this;
        }

        public Criteria andFirstPayChapterNotEqualTo(String value) {
            addCriterion("first_pay_chapter <>", value, "firstPayChapter");
            return (Criteria) this;
        }

        public Criteria andFirstPayChapterGreaterThan(String value) {
            addCriterion("first_pay_chapter >", value, "firstPayChapter");
            return (Criteria) this;
        }

        public Criteria andFirstPayChapterGreaterThanOrEqualTo(String value) {
            addCriterion("first_pay_chapter >=", value, "firstPayChapter");
            return (Criteria) this;
        }

        public Criteria andFirstPayChapterLessThan(String value) {
            addCriterion("first_pay_chapter <", value, "firstPayChapter");
            return (Criteria) this;
        }

        public Criteria andFirstPayChapterLessThanOrEqualTo(String value) {
            addCriterion("first_pay_chapter <=", value, "firstPayChapter");
            return (Criteria) this;
        }

        public Criteria andFirstPayChapterLike(String value) {
            addCriterion("first_pay_chapter like", value, "firstPayChapter");
            return (Criteria) this;
        }

        public Criteria andFirstPayChapterNotLike(String value) {
            addCriterion("first_pay_chapter not like", value, "firstPayChapter");
            return (Criteria) this;
        }

        public Criteria andFirstPayChapterIn(List<String> values) {
            addCriterion("first_pay_chapter in", values, "firstPayChapter");
            return (Criteria) this;
        }

        public Criteria andFirstPayChapterNotIn(List<String> values) {
            addCriterion("first_pay_chapter not in", values, "firstPayChapter");
            return (Criteria) this;
        }

        public Criteria andFirstPayChapterBetween(String value1, String value2) {
            addCriterion("first_pay_chapter between", value1, value2, "firstPayChapter");
            return (Criteria) this;
        }

        public Criteria andFirstPayChapterNotBetween(String value1, String value2) {
            addCriterion("first_pay_chapter not between", value1, value2, "firstPayChapter");
            return (Criteria) this;
        }

        public Criteria andRealizationMethodIsNull() {
            addCriterion("realization_method is null");
            return (Criteria) this;
        }

        public Criteria andRealizationMethodIsNotNull() {
            addCriterion("realization_method is not null");
            return (Criteria) this;
        }

        public Criteria andRealizationMethodEqualTo(String value) {
            addCriterion("realization_method =", value, "realizationMethod");
            return (Criteria) this;
        }

        public Criteria andRealizationMethodNotEqualTo(String value) {
            addCriterion("realization_method <>", value, "realizationMethod");
            return (Criteria) this;
        }

        public Criteria andRealizationMethodGreaterThan(String value) {
            addCriterion("realization_method >", value, "realizationMethod");
            return (Criteria) this;
        }

        public Criteria andRealizationMethodGreaterThanOrEqualTo(String value) {
            addCriterion("realization_method >=", value, "realizationMethod");
            return (Criteria) this;
        }

        public Criteria andRealizationMethodLessThan(String value) {
            addCriterion("realization_method <", value, "realizationMethod");
            return (Criteria) this;
        }

        public Criteria andRealizationMethodLessThanOrEqualTo(String value) {
            addCriterion("realization_method <=", value, "realizationMethod");
            return (Criteria) this;
        }

        public Criteria andRealizationMethodLike(String value) {
            addCriterion("realization_method like", value, "realizationMethod");
            return (Criteria) this;
        }

        public Criteria andRealizationMethodNotLike(String value) {
            addCriterion("realization_method not like", value, "realizationMethod");
            return (Criteria) this;
        }

        public Criteria andRealizationMethodIn(List<String> values) {
            addCriterion("realization_method in", values, "realizationMethod");
            return (Criteria) this;
        }

        public Criteria andRealizationMethodNotIn(List<String> values) {
            addCriterion("realization_method not in", values, "realizationMethod");
            return (Criteria) this;
        }

        public Criteria andRealizationMethodBetween(String value1, String value2) {
            addCriterion("realization_method between", value1, value2, "realizationMethod");
            return (Criteria) this;
        }

        public Criteria andRealizationMethodNotBetween(String value1, String value2) {
            addCriterion("realization_method not between", value1, value2, "realizationMethod");
            return (Criteria) this;
        }

        public Criteria andFirstCategoryCodeIsNull() {
            addCriterion("first_category_code is null");
            return (Criteria) this;
        }

        public Criteria andFirstCategoryCodeIsNotNull() {
            addCriterion("first_category_code is not null");
            return (Criteria) this;
        }

        public Criteria andFirstCategoryCodeEqualTo(Long value) {
            addCriterion("first_category_code =", value, "firstCategoryCode");
            return (Criteria) this;
        }

        public Criteria andFirstCategoryCodeNotEqualTo(Long value) {
            addCriterion("first_category_code <>", value, "firstCategoryCode");
            return (Criteria) this;
        }

        public Criteria andFirstCategoryCodeGreaterThan(Long value) {
            addCriterion("first_category_code >", value, "firstCategoryCode");
            return (Criteria) this;
        }

        public Criteria andFirstCategoryCodeGreaterThanOrEqualTo(Long value) {
            addCriterion("first_category_code >=", value, "firstCategoryCode");
            return (Criteria) this;
        }

        public Criteria andFirstCategoryCodeLessThan(Long value) {
            addCriterion("first_category_code <", value, "firstCategoryCode");
            return (Criteria) this;
        }

        public Criteria andFirstCategoryCodeLessThanOrEqualTo(Long value) {
            addCriterion("first_category_code <=", value, "firstCategoryCode");
            return (Criteria) this;
        }

        public Criteria andFirstCategoryCodeIn(List<Long> values) {
            addCriterion("first_category_code in", values, "firstCategoryCode");
            return (Criteria) this;
        }

        public Criteria andFirstCategoryCodeNotIn(List<Long> values) {
            addCriterion("first_category_code not in", values, "firstCategoryCode");
            return (Criteria) this;
        }

        public Criteria andFirstCategoryCodeBetween(Long value1, Long value2) {
            addCriterion("first_category_code between", value1, value2, "firstCategoryCode");
            return (Criteria) this;
        }

        public Criteria andFirstCategoryCodeNotBetween(Long value1, Long value2) {
            addCriterion("first_category_code not between", value1, value2, "firstCategoryCode");
            return (Criteria) this;
        }

        public Criteria andSecondCategoryCodeIsNull() {
            addCriterion("second_category_code is null");
            return (Criteria) this;
        }

        public Criteria andSecondCategoryCodeIsNotNull() {
            addCriterion("second_category_code is not null");
            return (Criteria) this;
        }

        public Criteria andSecondCategoryCodeEqualTo(Long value) {
            addCriterion("second_category_code =", value, "secondCategoryCode");
            return (Criteria) this;
        }

        public Criteria andSecondCategoryCodeNotEqualTo(Long value) {
            addCriterion("second_category_code <>", value, "secondCategoryCode");
            return (Criteria) this;
        }

        public Criteria andSecondCategoryCodeGreaterThan(Long value) {
            addCriterion("second_category_code >", value, "secondCategoryCode");
            return (Criteria) this;
        }

        public Criteria andSecondCategoryCodeGreaterThanOrEqualTo(Long value) {
            addCriterion("second_category_code >=", value, "secondCategoryCode");
            return (Criteria) this;
        }

        public Criteria andSecondCategoryCodeLessThan(Long value) {
            addCriterion("second_category_code <", value, "secondCategoryCode");
            return (Criteria) this;
        }

        public Criteria andSecondCategoryCodeLessThanOrEqualTo(Long value) {
            addCriterion("second_category_code <=", value, "secondCategoryCode");
            return (Criteria) this;
        }

        public Criteria andSecondCategoryCodeIn(List<Long> values) {
            addCriterion("second_category_code in", values, "secondCategoryCode");
            return (Criteria) this;
        }

        public Criteria andSecondCategoryCodeNotIn(List<Long> values) {
            addCriterion("second_category_code not in", values, "secondCategoryCode");
            return (Criteria) this;
        }

        public Criteria andSecondCategoryCodeBetween(Long value1, Long value2) {
            addCriterion("second_category_code between", value1, value2, "secondCategoryCode");
            return (Criteria) this;
        }

        public Criteria andSecondCategoryCodeNotBetween(Long value1, Long value2) {
            addCriterion("second_category_code not between", value1, value2, "secondCategoryCode");
            return (Criteria) this;
        }

        public Criteria andThirdCategoryCodeIsNull() {
            addCriterion("third_category_code is null");
            return (Criteria) this;
        }

        public Criteria andThirdCategoryCodeIsNotNull() {
            addCriterion("third_category_code is not null");
            return (Criteria) this;
        }

        public Criteria andThirdCategoryCodeEqualTo(Long value) {
            addCriterion("third_category_code =", value, "thirdCategoryCode");
            return (Criteria) this;
        }

        public Criteria andThirdCategoryCodeNotEqualTo(Long value) {
            addCriterion("third_category_code <>", value, "thirdCategoryCode");
            return (Criteria) this;
        }

        public Criteria andThirdCategoryCodeGreaterThan(Long value) {
            addCriterion("third_category_code >", value, "thirdCategoryCode");
            return (Criteria) this;
        }

        public Criteria andThirdCategoryCodeGreaterThanOrEqualTo(Long value) {
            addCriterion("third_category_code >=", value, "thirdCategoryCode");
            return (Criteria) this;
        }

        public Criteria andThirdCategoryCodeLessThan(Long value) {
            addCriterion("third_category_code <", value, "thirdCategoryCode");
            return (Criteria) this;
        }

        public Criteria andThirdCategoryCodeLessThanOrEqualTo(Long value) {
            addCriterion("third_category_code <=", value, "thirdCategoryCode");
            return (Criteria) this;
        }

        public Criteria andThirdCategoryCodeIn(List<Long> values) {
            addCriterion("third_category_code in", values, "thirdCategoryCode");
            return (Criteria) this;
        }

        public Criteria andThirdCategoryCodeNotIn(List<Long> values) {
            addCriterion("third_category_code not in", values, "thirdCategoryCode");
            return (Criteria) this;
        }

        public Criteria andThirdCategoryCodeBetween(Long value1, Long value2) {
            addCriterion("third_category_code between", value1, value2, "thirdCategoryCode");
            return (Criteria) this;
        }

        public Criteria andThirdCategoryCodeNotBetween(Long value1, Long value2) {
            addCriterion("third_category_code not between", value1, value2, "thirdCategoryCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeIsNull() {
            addCriterion("spu_code is null");
            return (Criteria) this;
        }

        public Criteria andSpuCodeIsNotNull() {
            addCriterion("spu_code is not null");
            return (Criteria) this;
        }

        public Criteria andSpuCodeEqualTo(Long value) {
            addCriterion("spu_code =", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeNotEqualTo(Long value) {
            addCriterion("spu_code <>", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeGreaterThan(Long value) {
            addCriterion("spu_code >", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeGreaterThanOrEqualTo(Long value) {
            addCriterion("spu_code >=", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeLessThan(Long value) {
            addCriterion("spu_code <", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeLessThanOrEqualTo(Long value) {
            addCriterion("spu_code <=", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeIn(List<Long> values) {
            addCriterion("spu_code in", values, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeNotIn(List<Long> values) {
            addCriterion("spu_code not in", values, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeBetween(Long value1, Long value2) {
            addCriterion("spu_code between", value1, value2, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeNotBetween(Long value1, Long value2) {
            addCriterion("spu_code not between", value1, value2, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeIsNull() {
            addCriterion("sku_code is null");
            return (Criteria) this;
        }

        public Criteria andSkuCodeIsNotNull() {
            addCriterion("sku_code is not null");
            return (Criteria) this;
        }

        public Criteria andSkuCodeEqualTo(Long value) {
            addCriterion("sku_code =", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeNotEqualTo(Long value) {
            addCriterion("sku_code <>", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeGreaterThan(Long value) {
            addCriterion("sku_code >", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeGreaterThanOrEqualTo(Long value) {
            addCriterion("sku_code >=", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeLessThan(Long value) {
            addCriterion("sku_code <", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeLessThanOrEqualTo(Long value) {
            addCriterion("sku_code <=", value, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeIn(List<Long> values) {
            addCriterion("sku_code in", values, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeNotIn(List<Long> values) {
            addCriterion("sku_code not in", values, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeBetween(Long value1, Long value2) {
            addCriterion("sku_code between", value1, value2, "skuCode");
            return (Criteria) this;
        }

        public Criteria andSkuCodeNotBetween(Long value1, Long value2) {
            addCriterion("sku_code not between", value1, value2, "skuCode");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Integer value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Integer value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Integer value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Integer value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Integer value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Integer> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Integer> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNull() {
            addCriterion("ctime is null");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNotNull() {
            addCriterion("ctime is not null");
            return (Criteria) this;
        }

        public Criteria andCtimeEqualTo(Timestamp value) {
            addCriterion("ctime =", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotEqualTo(Timestamp value) {
            addCriterion("ctime <>", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThan(Timestamp value) {
            addCriterion("ctime >", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("ctime >=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThan(Timestamp value) {
            addCriterion("ctime <", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("ctime <=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeIn(List<Timestamp> values) {
            addCriterion("ctime in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotIn(List<Timestamp> values) {
            addCriterion("ctime not in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime not between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNull() {
            addCriterion("mtime is null");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNotNull() {
            addCriterion("mtime is not null");
            return (Criteria) this;
        }

        public Criteria andMtimeEqualTo(Timestamp value) {
            addCriterion("mtime =", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotEqualTo(Timestamp value) {
            addCriterion("mtime <>", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThan(Timestamp value) {
            addCriterion("mtime >", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("mtime >=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThan(Timestamp value) {
            addCriterion("mtime <", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("mtime <=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeIn(List<Timestamp> values) {
            addCriterion("mtime in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotIn(List<Timestamp> values) {
            addCriterion("mtime not in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime not between", value1, value2, "mtime");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}