package com.bilibili.adp.cpc.dao.querydsl.pos;

import javax.annotation.Generated;

/**
 * LauGeneralAvidOperateRecordPo is a Querydsl bean type
 */
@Generated("com.querydsl.codegen.BeanSerializer")
public class LauGeneralAvidOperateRecordPo {

    private Integer accountId;

    private java.sql.Timestamp ctime;

    private Long id;

    private Integer isDeleted;

    private Integer mappingId;

    private java.sql.Timestamp mtime;

    private Integer operatorType;

    public Integer getAccountId() {
        return accountId;
    }

    public void setAccountId(Integer accountId) {
        this.accountId = accountId;
    }

    public java.sql.Timestamp getCtime() {
        return ctime;
    }

    public void setCtime(java.sql.Timestamp ctime) {
        this.ctime = ctime;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Integer getMappingId() {
        return mappingId;
    }

    public void setMappingId(Integer mappingId) {
        this.mappingId = mappingId;
    }

    public java.sql.Timestamp getMtime() {
        return mtime;
    }

    public void setMtime(java.sql.Timestamp mtime) {
        this.mtime = mtime;
    }

    public Integer getOperatorType() {
        return operatorType;
    }

    public void setOperatorType(Integer operatorType) {
        this.operatorType = operatorType;
    }

    @Override
    public String toString() {
         return "accountId = " + accountId + ", ctime = " + ctime + ", id = " + id + ", isDeleted = " + isDeleted + ", mappingId = " + mappingId + ", mtime = " + mtime + ", operatorType = " + operatorType;
    }

}

