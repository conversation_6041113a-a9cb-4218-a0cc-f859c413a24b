package com.bilibili.adp.cpc.compare;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Map;

@Getter
@RequiredArgsConstructor
public class  CompareMeta<T> {
    private final String key;
    private final String desc;
    private final boolean reAudit;
    private final int metaType;

    public CompareMeta<T> appendInto(Map<String, CompareMeta> map) {
        map.put(this.key, this);
        return this;
    }
}
