package com.bilibili.adp.cpc.automatic_rule.enums.condition;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum Subject {
    COST(1, "消耗"),
    PV(2, "展示数"),
    CLICK(3, "点击数"),
    CTR(4, "点击率"),
    CONVERSION(5, "转化数"),
    ORDER_ROI(6, "订单ROI"),
    PAID_IN_24H_AFTER_ACTIVATION_ROI(7, "激活后24h付费ROI"),
    CPA(8, "转化成本"),
    ;

    private final int code;
    private final String desc;

    public static Subject getByCode(int code) {
        return Arrays.stream(values())
                .filter(subject -> subject.getCode() == code)
                .findFirst()
                .orElseThrow(IllegalArgumentException::new);
    }
}
