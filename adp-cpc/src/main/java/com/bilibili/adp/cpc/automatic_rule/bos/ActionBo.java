package com.bilibili.adp.cpc.automatic_rule.bos;

import com.bilibili.adp.cpc.automatic_rule.enums.action.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ActionBo {
    private Long actionId;
    /**
     * 操作对象
     *
     * @see Subject
     */
    private Integer subject;
    /**
     * 调整方向/内容
     *
     * @see ActionType
     */
    private Integer actionType;

    /**
     * 值类型
     *
     * @see ValueType
     */
    private Integer valueType;
    /**
     * 值
     */
    private String value;
    /**
     * 上限类型
     *
     * @see LimitType
     */
    private Integer limitType;
    /**
     * 上限
     */
    private String limitValue;
    /**
     * 调整频次
     *
     * @see FrequencyScope
     */
    private Integer frequencyScope;
    /**
     * 调整次数
     *
     * @see FrequencyCount
     */
    private Integer frequencyCount;
}
