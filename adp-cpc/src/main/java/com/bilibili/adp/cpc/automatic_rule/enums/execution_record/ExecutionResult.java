package com.bilibili.adp.cpc.automatic_rule.enums.execution_record;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum ExecutionResult {
    FAIL(0, "失败"),
    SUCCESS(1, "成功"),
    ;

    private final int code;
    private final String desc;

    public static ExecutionResult getByCode(int code) {
        return Arrays.stream(values())
                .filter(executionResult -> executionResult.getCode() == code)
                .findFirst()
                .orElseThrow(IllegalArgumentException::new);
    }
}
