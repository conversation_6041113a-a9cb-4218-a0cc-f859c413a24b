package com.bilibili.adp.cpc.wrapper.bos;

import com.bilibili.adp.cpc.core.bos.UnitMonitorBo;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class UnitMonitorWrapperBo {
    @ApiModelProperty("展示监控链接")
    private String impUrl;
    @ApiModelProperty("点击监控链接")
    private String clickUrl;
    @ApiModelProperty("评论点击监控链接")
    private String commentClickUrl;
    @ApiModelProperty("视频0秒播放监控链接")
    private String videoPlayZeroSecUrl;
    @ApiModelProperty("视频3秒播放监控链接")
    private String videoPlayThreeSecUrl;
    @ApiModelProperty("视频5秒播放监控链接")
    private String videoPlayFiveSecUrl;

    public UnitMonitorBo toCoreBo() {
        return UnitMonitorBo.builder()
                .impUrl(impUrl)
                .clickUrl(clickUrl)
                .commentClickUrl(commentClickUrl)
                .videoPlayZeroSecUrl(videoPlayZeroSecUrl)
                .videoPlayThreeSecUrl(videoPlayThreeSecUrl)
                .videoPlayFiveSecUrl(videoPlayFiveSecUrl)
                .build();
    }

    public static UnitMonitorWrapperBo fromCoreBo(UnitMonitorBo bo) {
        if (Objects.isNull(bo)) return null;

        return UnitMonitorWrapperBo.builder()
                .impUrl(bo.getImpUrl())
                .clickUrl(bo.getClickUrl())
                .commentClickUrl(bo.getCommentClickUrl())
                .videoPlayZeroSecUrl(bo.getVideoPlayZeroSecUrl())
                .videoPlayThreeSecUrl(bo.getVideoPlayThreeSecUrl())
                .videoPlayFiveSecUrl(bo.getVideoPlayFiveSecUrl())
                .build();
    }
}
