package com.bilibili.adp.cpc.databus.event;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.cpc.biz.services.creative.UnderframeComponentService;
import com.bilibili.adp.cpc.databus.AdpCpcDatabusConfig;
import com.bilibili.adp.cpc.databus.DatabusClient;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Base64Utils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

@Slf4j
@Service
@RequiredArgsConstructor
public class PandoraEventService {
    private final ObjectMapper om;
    private final UnderframeComponentService underframeComponentService;

    @Resource(name = AdpCpcDatabusConfig.PANDORA_EVENT_SUB)
    private DatabusClient client;

    @PostConstruct
    public void sub() {
        if (client.isEnabled()) {
            log.info("PandoraEvent开始消费");
            client.getExecutorService().execute(() -> client.getSub().sub(this::exec));
        }
    }


    @SneakyThrows
    private void exec(JSON json) {
        final PandoraEventMsg msg = json.toJavaObject(PandoraEventMsg.class);
        final byte[] bytes = Base64Utils.decodeFromString(msg.getEncoded());
        switch (msg.getKey()) {
            case PandoraEvents.UNDERFRAME_COMPONENT_CHANGE:
                final JsonNode jsonNode = om.readTree(bytes);
                final long componentId = jsonNode.get("id").asLong();
                log.info("PandoraEvent: 框下组件{}变更", componentId);
                underframeComponentService.rejectAndReAudit(componentId);
                break;
            default:
                // 不认识的key忽略就好
        }
    }
}
