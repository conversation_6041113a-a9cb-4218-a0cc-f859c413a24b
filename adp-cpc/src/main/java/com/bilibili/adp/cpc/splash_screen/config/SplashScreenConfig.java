package com.bilibili.adp.cpc.splash_screen.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import pleiades.venus.config.WatchedProperties;
import pleiades.venus.starter.paladin.PaladinPropertySourceFactory;
@Slf4j
@Configuration
@PropertySource(value = "classpath:splash_screen.yaml", factory = PaladinPropertySourceFactory.class)
public class SplashScreenConfig {
    @Bean
    @WatchedProperties
    @ConfigurationProperties(prefix = "splash-screen-unit")
    public SplashScreenUnitConfig splashScreenUnitConfig() {
        return new SplashScreenUnitConfig();
    }
    @Bean
    @WatchedProperties
    @ConfigurationProperties(prefix = "splash-screen-creative")
    public SplashScreenCreativeConfig splashScreenCreativeConfig() {
        return new SplashScreenCreativeConfig();
    }
}
