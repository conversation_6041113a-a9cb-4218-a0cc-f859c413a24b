package com.bilibili.adp.cpc.biz.services.creative.native_ad;

import com.alibaba.fastjson.JSON;
import com.bapis.ad.audit.NativeBizType;
import com.bapis.ad.audit.NativeBodyType;
import com.bapis.archive.service.Arc;
import com.bapis.archive.service.Author;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.enums.AuditStatus;
import com.bilibili.adp.common.enums.OperatorType;
import com.bilibili.adp.common.exception.ServiceRuntimeException;
import com.bilibili.adp.common.util.EmojiUtils;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.biz.services.archive.ArchiveService;
import com.bilibili.adp.cpc.biz.services.creative.dto.CpcCreativeDto;
import com.bilibili.adp.cpc.biz.services.creative.dto.FlyProgNativeJudgeDto;
import com.bilibili.adp.cpc.biz.services.creative.mapper.NativeAdMapper;
import com.bilibili.adp.cpc.biz.services.creative.native_ad.dto.*;
import com.bilibili.adp.cpc.biz.services.log.DbTable;
import com.bilibili.adp.cpc.biz.services.log.ILogOperateForLongService;
import com.bilibili.adp.cpc.biz.services.log.ILogOperateNewService;
import com.bilibili.adp.cpc.biz.services.unit.LauSubjectService;
import com.bilibili.adp.cpc.biz.services.unit.bos.DynamicBo;
import com.bilibili.adp.cpc.biz.services.unit.dto.CpcUnitDto;
import com.bilibili.adp.cpc.core.bos.*;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauCreativeNativeArchiveRelativityPo;
import com.bilibili.adp.cpc.dao.querydsl.pos.LauNativeArchivePo;
import com.bilibili.adp.cpc.repo.LauCreativeNativeArchiveRelativityRepo;
import com.bilibili.adp.cpc.repo.LauNativeArchiveRepo;
import com.bilibili.adp.cpc.repo.LauUnitCreativeRepo;
import com.bilibili.adp.launch.api.common.LauSubjectType;
import com.bilibili.adp.launch.api.common.LaunchConstant;
import com.bilibili.adp.launch.api.creative.dto.LauSubjectDto;
import com.bilibili.adp.launch.biz.pojo.LauUnitCreativePo;
import com.bilibili.adp.passport.biz.manager.LiveBroadcastHttpService;
import com.bilibili.adp.passport.biz.manager.bean.LiveBroadcastRoomInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.bilibili.adp.cpc.dao.config.PlatformMySqlConfig.AD_CORE_TM;

/**
 * <AUTHOR>
 * @date 2024/3/15 15:56
 */
@Component
@Slf4j
public class NativeService {

    @Value("${biz.native.dynamic.switch:1}")
    private Integer dynamicNativeSwitch;

    @Autowired
    private LauNativeArchiveRepo lauNativeArchiveRepo;
    @Autowired
    private LauCreativeNativeArchiveRelativityRepo lauCreativeNativeArchiveRelativityRepo;
    @Autowired
    private ILogOperateForLongService logOperateForLongService;
    @Autowired
    private ILogOperateNewService logOperateService;
    @Autowired
    private ArchiveService archiveService;
    @Autowired
    private LauSubjectService lauSubjectService;
    @Autowired
    private LauUnitCreativeRepo lauUnitCreativeRepo;
    @Autowired
    private NativeAdJudger nativeAdJudger;
    @Autowired
    private LiveBroadcastHttpService liveBroadcastHttpService;


    /**
     * 保存原生数据(起飞用)
     *
     * @param creativeNativeArchiveRelativityPoMap
     * @param creative
     * @param isNativeCreative
     * @param creativeId
     * @param needReAudit
     * @param operator
     * @param arc
     */
    @Transactional(value = AD_CORE_TM, rollbackFor = Exception.class)
    public void saveNativeDataIfNecessary(Map<Integer, LauCreativeNativeArchiveRelativityPo> creativeNativeArchiveRelativityPoMap, CpcCreativeDto creative,
                                          boolean isNativeCreative, int creativeId, Boolean needReAudit,
                                          Operator operator, Arc arc, CpcUnitDto unit) {
        if (!isNativeCreative) {
            return;
        }
        log.info("saveNativeDataIfNecessary, isNativeCreative={}, creativeId={}, videoId={}", isNativeCreative,
                creativeId, creative.getVideoId());

        NativeBodyInfoDto nativeBodyInfoDto = new NativeBodyInfoDto();
        DynamicBo dynamic = unit.getDynamic();

        Integer auditStatus = AuditStatus.INIT.getCode();
        Integer shadowAuditStatus = AuditStatus.INIT.getCode();
        // 直播间默认审核通过，其他的默认待审
        Integer nativeBodyType = NativeBodyType.ARCHIVE_VALUE;
        Long objId = 0L;

        // 直播
        if (Utils.isPositive(unit.getSubjectId())) {
            // 动态直播推审开关
            if (!Utils.isPositive(dynamicNativeSwitch)) {
                return;
            }

            auditStatus = AuditStatus.ACCEPT.getCode();
            shadowAuditStatus = AuditStatus.ACCEPT.getCode();
            nativeBodyType = NativeBodyType.LIVE_ROOM_VALUE;
            LauSubjectDto subjectDto = lauSubjectService.getLauSubjectById(unit.getSubjectId(), LauSubjectType.LIVE_ROOM);
            if (subjectDto == null) {
                log.error("saveNativeDataIfNecessary subject not exist, isNativeCreative={}, creativeId={}, subjectId={}", isNativeCreative,
                        creativeId, unit.getSubjectId());
                return;
            }
            objId = Long.parseLong(subjectDto.getMaterialId());

            Map<Integer, LiveBroadcastRoomInfo> roomInfoMap = liveBroadcastHttpService.queryLiveBroadcastRoomInfoByIds(Arrays.asList(objId.intValue()));
            LiveBroadcastRoomInfo liveBroadcastRoomInfo = roomInfoMap.get(objId.intValue());
            if (liveBroadcastRoomInfo != null) {
                nativeBodyInfoDto.setUpMid(liveBroadcastRoomInfo.getUid());
                nativeBodyInfoDto.setTitle(EmojiUtils.filterEmoji(liveBroadcastRoomInfo.getTitle(), "?"));
                nativeBodyInfoDto.setCover(liveBroadcastRoomInfo.getCover());
            }
        }
        // 动态
        else if (dynamic != null) {
            // 动态直播推审开关
            if (!Utils.isPositive(dynamicNativeSwitch)) {
                return;
            }

            nativeBodyInfoDto.setUpMid(dynamic.getDynamicUpMid());
            String dynamicContent = dynamic.getDynamicContent();
            dynamicContent = StringUtils.left(dynamicContent, LaunchConstant.MAX_CREATIVE_NAME_LENGTH);
            nativeBodyInfoDto.setTitle(EmojiUtils.filterEmoji(dynamicContent, "?"));
            nativeBodyInfoDto.setUpNickname(dynamic.getDynamicUpNickname());
            nativeBodyType = NativeBodyType.DYNAMIC_VALUE;
            objId = dynamic.getDynamicId();
        }
        // 原生稿件
        else if (arc != null) {
            String title = EmojiUtils.filterEmoji(arc.getTitle(), "?");
            nativeBodyInfoDto.setTitle(title);
            nativeBodyInfoDto.setCover(arc.getPic());
            nativeBodyInfoDto.setUpMid(arc.getAuthor().getMid());
            nativeBodyInfoDto.setUpNickname(arc.getAuthor().getName());
            nativeBodyInfoDto.setTagIds(arc.getTag());
            objId = creative.getVideoId();
        }
        log.info("saveNativeDataIfNecessary, isNativeCreative={}, creativeId={}, objId={},nativeBodyType={}", isNativeCreative,
                creativeId, objId, nativeBodyType);

        // 骚操作需要存 biz type，没有其他字段了
        operator.setBilibiliUserName(NativeBizType.SANLIAN_VALUE + "");
        LauNativeArchivePo oldNativeArchivePo = lauNativeArchiveRepo.queryNativeArchiveByAvidAndBizType(objId, NativeBizType.SANLIAN_VALUE, nativeBodyType);
        // 原生稿件
        if (oldNativeArchivePo == null) {
            LauNativeArchivePo lauNativeArchviePo = new LauNativeArchivePo();
            lauNativeArchviePo.setAvid(objId);
            lauNativeArchviePo.setAuditStatus(auditStatus);
            lauNativeArchviePo.setShallowAuditStatus(shadowAuditStatus);
            lauNativeArchviePo.setBizType(NativeBizType.SANLIAN_VALUE);
            lauNativeArchviePo.setTitle(nativeBodyInfoDto.getTitle());
            lauNativeArchviePo.setCover(nativeBodyInfoDto.getCover());
            lauNativeArchviePo.setUpMid(nativeBodyInfoDto.getUpMid());
            lauNativeArchviePo.setUpNickname(nativeBodyInfoDto.getUpNickname());
            lauNativeArchviePo.setTagIds(nativeBodyInfoDto.getTagIds());
            lauNativeArchviePo.setType(nativeBodyType);
            Long id = lauNativeArchiveRepo.addLauNativeArchive(lauNativeArchviePo);
            oldNativeArchivePo = lauNativeArchviePo;

            operator.setBilibiliUserName(NativeBizType.SANLIAN_VALUE + "");
            NativeArchiveLogBo nativeArchiveLogBo = NativeArchiveLogBo.builder()
                    .auditStatus(auditStatus)
//                    .bizType(NativeBizType.SANLIAN_VALUE)
                    .build();
            logOperateForLongService.addInsertLog(DbTable.LAU_NATIVE_ARCHIVE, operator, nativeArchiveLogBo, id);
        } else {
            // 已经通过/驳回，原生稿件审核状态就不变了
//            if (Objects.equals(oldNativeArchivePo.getAuditStatus(), AuditStatus.REJECT.getCode())) {
//                oldNativeArchivePo.setAuditStatus(AuditStatus.INIT.getCode());
//            }
            oldNativeArchivePo.setTitle(nativeBodyInfoDto.getTitle());
            oldNativeArchivePo.setCover(nativeBodyInfoDto.getCover());
            oldNativeArchivePo.setUpMid(nativeBodyInfoDto.getUpMid());
            oldNativeArchivePo.setUpNickname(nativeBodyInfoDto.getUpNickname());
            oldNativeArchivePo.setTagIds(nativeBodyInfoDto.getTagIds());
            lauNativeArchiveRepo.updateNativeArchive(oldNativeArchivePo);

            operator.setBilibiliUserName(NativeBizType.SANLIAN_VALUE + "");
            NativeArchiveLogBo nativeArchiveLogBo = NativeArchiveLogBo.builder()
                    .auditStatus(oldNativeArchivePo.getAuditStatus())
//                    .bizType(NativeBizType.SANLIAN_VALUE)
                    .build();
            logOperateForLongService.addUpdateStatusLog(DbTable.LAU_NATIVE_ARCHIVE, operator, nativeArchiveLogBo, oldNativeArchivePo.getId());
        }

        operator.setBilibiliUserName("");
        // 原生创意相关性
        LauCreativeNativeArchiveRelativityPo oldRelativityPo = creativeNativeArchiveRelativityPoMap.get(creativeId);
        if (oldRelativityPo == null) {
            LauCreativeNativeArchiveRelativityPo relativityPo = new LauCreativeNativeArchiveRelativityPo();
            relativityPo.setCreativeId(creativeId);
            relativityPo.setAvid(objId);
            relativityPo.setType(nativeBodyType);
            relativityPo.setAuditStatus(auditStatus);

            // 直播的，跟着主体状态
            if (Objects.equals(nativeBodyType, NativeBodyType.LIVE_ROOM_VALUE)) {
                Integer archiveAuditStatus = oldNativeArchivePo.getAuditStatus();
                log.info("saveNativeDataIfNecessary[直播间], creativeId={}, objId={},archiveAuditStatus={}", creativeId, objId, archiveAuditStatus);
                relativityPo.setAuditStatus(archiveAuditStatus);
                if (Objects.equals(archiveAuditStatus, AuditStatus.REJECT)) {
                    relativityPo.setReason("原生稿件驳回");
                } else {
                    relativityPo.setReason("");
                }
            }

            Long executeCount = lauCreativeNativeArchiveRelativityRepo.addLauCreativeNativeRelativity(relativityPo);

            logOperateService.addInsertLog(DbTable.LAU_CREATIVE_NATIVE_ARCHIVE_RELATIVITY, operator, relativityPo, creativeId);

        } else {
            LauCreativeNativeArchiveRelativityPo newRelativityPo = new LauCreativeNativeArchiveRelativityPo();
            CreativeNativeArchiveRelativityLogBo oldLogBo = new CreativeNativeArchiveRelativityLogBo();
            BeanUtils.copyProperties(oldRelativityPo, oldLogBo);
            BeanUtils.copyProperties(oldRelativityPo, newRelativityPo);

            if (needReAudit) {
                newRelativityPo.setAuditStatus(AuditStatus.INIT.getCode());
            }

            // 直播的，跟着主体状态
            if (Objects.equals(nativeBodyType, NativeBodyType.LIVE_ROOM_VALUE)) {
                Integer archiveAuditStatus = oldNativeArchivePo.getAuditStatus();
                log.info("saveNativeDataIfNecessary[直播间], creativeId={}, objId={},archiveAuditStatus={}", creativeId, objId, archiveAuditStatus);
                newRelativityPo.setAuditStatus(archiveAuditStatus);
                if (Objects.equals(archiveAuditStatus, AuditStatus.REJECT)) {
                    newRelativityPo.setReason("原生稿件驳回");
                } else {
                    newRelativityPo.setReason("");
                }
            }

            newRelativityPo.setAvid(objId);
            newRelativityPo.setVersion(oldRelativityPo.getVersion() + 1);

            CreativeNativeArchiveRelativityLogBo newLogBo = new CreativeNativeArchiveRelativityLogBo();
            BeanUtils.copyProperties(newRelativityPo, newLogBo);

            lauCreativeNativeArchiveRelativityRepo.updateNativeArchiveRelativity(newRelativityPo,
                    oldRelativityPo.getVersion());
            logOperateService.addUpdateLog(DbTable.LAU_CREATIVE_NATIVE_ARCHIVE_RELATIVITY, operator, oldLogBo, newLogBo, creativeId);
        }
    }

    @Transactional(value = AD_CORE_TM, rollbackFor = Exception.class)
    public PushNativeAuditContext saveNativeData(PushNativeAuditDto pushNativeAuditDto) {
        log.info("pushNativeAudit, pushNativeAuditDto={}", JSON.toJSONString(pushNativeAuditDto));
        PushNativeAuditContext context = PushNativeAuditContext.builder().pushNativeAuditDto(pushNativeAuditDto).build();
        Assert.isTrue(Utils.isPositive(pushNativeAuditDto.getAvid()), "avid 参数错误");
        Assert.isTrue(Utils.isPositive(pushNativeAuditDto.getBizType()), "biz type 参数错误");

        Operator operator = new Operator("SYSTEM", OperatorType.SYSTEM);
        operator.setOperatorName(getOperatorName(pushNativeAuditDto.getBizType()));

        if (Objects.equals(NativeBizType.BIHUO_VALUE, pushNativeAuditDto.getBizType())) {
            Assert.isTrue(!CollectionUtils.isEmpty(pushNativeAuditDto.getCreativeIds()), "必火创意id不能为空");
        }

        if (!CollectionUtils.isEmpty(pushNativeAuditDto.getCreativeIds())) {
            Map<Integer, LauUnitCreativePo> unitCreativePoMap = lauUnitCreativeRepo.queryCreativeMap(pushNativeAuditDto.getCreativeIds());
            for (Integer creativeId : pushNativeAuditDto.getCreativeIds()) {
                if (!unitCreativePoMap.containsKey(creativeId)) {
                    throw new ServiceRuntimeException("创意不存在, creativeId=" + creativeId);
                }
            }
        }

        /**
         * 处理原生数据
         */
        // 查询原生数据
        Long archiveUpdateCount = 0L;
        LauNativeArchivePo oldNativeArchivePo = lauNativeArchiveRepo.queryNativeArchiveByAvidAndBizType(pushNativeAuditDto.getAvid(),
                pushNativeAuditDto.getBizType(), NativeBodyType.ARCHIVE_VALUE);
        // 原生稿件
        if (oldNativeArchivePo == null) {

            // 查询稿件
            Arc arc = archiveService.arc(pushNativeAuditDto.getAvid());
            Assert.notNull(arc, "稿件不存在");

            String title = EmojiUtils.filterEmoji(arc.getTitle(), "?");

            LauNativeArchivePo lauNativeArchviePo = new LauNativeArchivePo();
            lauNativeArchviePo.setAvid(pushNativeAuditDto.getAvid());
            lauNativeArchviePo.setAuditStatus(AuditStatus.INIT.getCode());
            lauNativeArchviePo.setBizType(pushNativeAuditDto.getBizType());
            lauNativeArchviePo.setTitle(title);
            lauNativeArchviePo.setCover(arc.getPic());
            lauNativeArchviePo.setUpMid(arc.getAuthor().getMid());
            lauNativeArchviePo.setUpNickname(arc.getAuthor().getName());
            lauNativeArchviePo.setTagIds(arc.getTag());
            Long id = lauNativeArchiveRepo.addLauNativeArchiveGetCount(lauNativeArchviePo);
            archiveUpdateCount = 1L;

            operator.setBilibiliUserName(pushNativeAuditDto.getBizType() + "");
            NativeArchiveLogBo nativeArchiveLogBo = NativeArchiveLogBo.builder()
                    .auditStatus(AuditStatus.INIT.getCode())
//                    .bizType(pushNativeAuditDto.getBizType())
                    .build();
            logOperateForLongService.addInsertLog(DbTable.LAU_NATIVE_ARCHIVE, operator, nativeArchiveLogBo, id);
        }
        context.setCount(archiveUpdateCount);

        if (CollectionUtils.isEmpty(pushNativeAuditDto.getCreativeIds())) {
            return context;
        }

        // 创意相关性
        boolean needReAudit = true;
        Map<Integer, LauCreativeNativeArchiveRelativityPo> relativityPoMap = lauCreativeNativeArchiveRelativityRepo.queryNativeArchiveRelativityPoMap(pushNativeAuditDto.getCreativeIds());
        Long creativeUpdateCount = 0L;
        operator.setBilibiliUserName("");
        for (Integer creativeId : pushNativeAuditDto.getCreativeIds()) {
            LauCreativeNativeArchiveRelativityPo oldRelativityPo = relativityPoMap.get(creativeId);
            // 原生创意相关性
            if (oldRelativityPo == null) {
                LauCreativeNativeArchiveRelativityPo relativityPo = new LauCreativeNativeArchiveRelativityPo();
                relativityPo.setCreativeId(creativeId);
                relativityPo.setAvid(pushNativeAuditDto.getAvid());
                relativityPo.setAuditStatus(AuditStatus.INIT.getCode());
                creativeUpdateCount += lauCreativeNativeArchiveRelativityRepo.addLauCreativeNativeRelativity(relativityPo);
                logOperateService.addInsertLog(DbTable.LAU_CREATIVE_NATIVE_ARCHIVE_RELATIVITY, operator, relativityPo, creativeId);
            } else {
                LauCreativeNativeArchiveRelativityPo newRelativityPo = new LauCreativeNativeArchiveRelativityPo();
                BeanUtils.copyProperties(oldRelativityPo, newRelativityPo);
                if (needReAudit) {
                    newRelativityPo.setAuditStatus(AuditStatus.INIT.getCode());
                }
                newRelativityPo.setAvid(pushNativeAuditDto.getAvid());
                newRelativityPo.setVersion(oldRelativityPo.getVersion() + 1);
                creativeUpdateCount += lauCreativeNativeArchiveRelativityRepo.updateNativeArchiveRelativity(newRelativityPo, oldRelativityPo.getVersion());
                logOperateService.addUpdateLog(DbTable.LAU_CREATIVE_NATIVE_ARCHIVE_RELATIVITY, operator, oldRelativityPo, creativeId);
            }
        }
        context.setCount(creativeUpdateCount);
        return context;
    }

    private static String getOperatorName(Integer bizType) {
        if (Objects.equals(NativeBizType.BIHUO_VALUE, bizType)) {
            return "必火";
        } else if (Objects.equals(NativeBizType.BRAND_VALUE, bizType)) {
            return "品牌";
        }
        return "其他";
    }

    @Transactional(value = AD_CORE_TM, rollbackFor = Exception.class)
    public int updateNativeArchiveShallowStatus(long avid, int bizType, int auditStatusValue) {
        LauNativeArchivePo archivePo = lauNativeArchiveRepo.queryNativeArchiveByAvidAndBizType(avid, bizType, NativeBodyType.ARCHIVE_VALUE);
        if (archivePo == null) {
            return 0;
        }
        int count = lauNativeArchiveRepo.updateNativeArchiveShallowStatus(avid, bizType, auditStatusValue);

        Operator operator = new Operator("SYSTEM", OperatorType.SYSTEM);
        // 操作人要求必火
        operator.setOperatorName(getOperatorName(bizType));
        operator.setBilibiliUserName(bizType + "");

        NativeArchiveLogBo logBo = NativeArchiveLogBo.builder()
                .shallowAuditStatus(auditStatusValue)
                .build();
        logOperateForLongService.addUpdateLog(DbTable.LAU_NATIVE_ARCHIVE, operator, logBo, archivePo.getId());
        return count;
    }

    public boolean saveCreativeRelativity(UnitCreativeBo unitCreativeBo, SaveUnitCreativeContextBo ctx) {

        List<Integer> creativeIds = unitCreativeBo.getCreatives().stream().map(t -> t.getCreativeId()).collect(Collectors.toList());
        boolean isNativeCreative = false;
        Operator operator = ctx.getOperator();
        List<LauCreativeNativeArchiveRelativityPo> existingPos = queryCreativeRelativityList(creativeIds);
        Map<Integer, LauCreativeNativeArchiveRelativityPo> existRelativityPoMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(existingPos)) {
            existRelativityPoMap = existingPos.stream().collect(Collectors.toMap(t -> t.getCreativeId(), t -> t));
        }

        // 是否原生
        FlyProgNativeJudgeDto flyProgNativeJudgeDto = FlyProgNativeJudgeDto.builder()
                .campaign(ctx.getCampaign())
                .unit(ctx.getUnit())
                .unitCreativeBo(unitCreativeBo)
                .creativeBos(unitCreativeBo.getCreatives())
                // 暂未用到
                .archiveExistsMap(new HashMap<>())
                .arc(ctx.getFlyArc())
                .build();
        Map<Integer, Boolean> nativeCreativeMap = nativeAdJudger.judgeIsNativeCreative(flyProgNativeJudgeDto);

        // 生成原生需要信息
        for (CreativeBo creative : unitCreativeBo.getCreatives()) {
            Boolean isNativeThisCreative = nativeCreativeMap.getOrDefault(creative.getCreativeId(), false);
            log.info("fly program genCreativeRelativityPos, creativeId={}, isNative={}, avid={}", creative.getCreativeId(), isNativeCreative, unitCreativeBo.getVideoId());

            LauCreativeNativeArchiveRelativityPo existRelativityPo = existRelativityPoMap.get(creative.getCreativeId());
            if (!isNativeThisCreative) {
                continue;
            }

            isNativeCreative = true;
            if (existRelativityPo == null) {
                LauCreativeNativeArchiveRelativityPo relativityPo = new LauCreativeNativeArchiveRelativityPo();
                relativityPo.setCreativeId(creative.getCreativeId());
                relativityPo.setAvid(unitCreativeBo.getVideoId());
                relativityPo.setAuditStatus(AuditStatus.INIT.getCode());
                relativityPo.setType(NativeBodyType.ARCHIVE_VALUE);
                Long executeCount = lauCreativeNativeArchiveRelativityRepo.addLauCreativeNativeRelativity(relativityPo);

                logOperateService.addInsertLog(DbTable.LAU_CREATIVE_NATIVE_ARCHIVE_RELATIVITY, operator, relativityPo, creative.getCreativeId());

            } else {
                LauCreativeNativeArchiveRelativityPo newRelativityPo = new LauCreativeNativeArchiveRelativityPo();
                BeanUtils.copyProperties(existRelativityPo, newRelativityPo);
                if (ctx.isTriggerAudit()) {
                    newRelativityPo.setAuditStatus(AuditStatus.INIT.getCode());
                }
                newRelativityPo.setAvid(unitCreativeBo.getVideoId());
                newRelativityPo.setVersion(existRelativityPo.getVersion() + 1);
                lauCreativeNativeArchiveRelativityRepo.updateNativeArchiveRelativity(newRelativityPo, existRelativityPo.getVersion());
                logOperateService.addUpdateLog(DbTable.LAU_CREATIVE_NATIVE_ARCHIVE_RELATIVITY, operator, existRelativityPo, creative.getCreativeId());
            }
        }
        return isNativeCreative;
    }

    public List<LauCreativeNativeArchiveRelativityPo> queryCreativeRelativityList(List<Integer> creativeIds) {
        return lauCreativeNativeArchiveRelativityRepo.queryNativeArchiveRelativityByCreativeIds(creativeIds);
    }

    public LauNativeArchivePo queryNativeArchviePo(Long videoId) {
        if (videoId == null) {
            return null;
        }

        // 动态和直播没有程序化，这次写死稿件
        return lauNativeArchiveRepo.queryNativeArchiveByAvidAndBizType(videoId, NativeBizType.SANLIAN_VALUE, NativeBodyType.ARCHIVE_VALUE);
    }

    /**
     * 目前程序化用
     * 目前必选稿件有程序化，起飞的动态和直播间都没有程序化
     *
     * @param unitCreative
     * @param ctx
     */
    public void saveNativeArchive(UnitCreativeBo unitCreative, SaveUnitCreativeContextBo ctx) {

        Long videoId = unitCreative.getVideoId();
        LauNativeArchivePo existPo = queryNativeArchviePo(videoId);

        // 是原生，relativityPos 才会有值
        Operator operator = new Operator();
        BeanUtils.copyProperties(ctx.getOperator(), operator);
        Arc flyArc = ctx.getFlyArc();
        if (existPo == null) {
            LauNativeArchivePo nativeArchivePo = new LauNativeArchivePo();
            nativeArchivePo.setAvid(videoId);
            nativeArchivePo.setBizType(NativeBizType.SANLIAN_VALUE);
            nativeArchivePo.setAuditStatus(com.bilibili.adp.cpc.core.constants.AuditStatus.AUDITING);
            nativeArchivePo.setShallowAuditStatus(com.bilibili.adp.cpc.core.constants.AuditStatus.AUDITING);
            if (flyArc != null) {
                nativeArchivePo.setCover(flyArc.getPic());
                nativeArchivePo.setTitle(flyArc.getTitle());
                nativeArchivePo.setTagIds(flyArc.getTag());
                Author author = flyArc.getAuthor();
                if (author != null) {
                    nativeArchivePo.setUpMid(author.getMid());
                    nativeArchivePo.setUpNickname(author.getName());
                }
            }
            Long id = lauNativeArchiveRepo.addLauNativeArchive(nativeArchivePo);

            operator.setBilibiliUserName(NativeBizType.SANLIAN_VALUE + "");
            NativeArchiveLogBo nativeArchiveLogBo = NativeArchiveLogBo.builder()
                    .auditStatus(AuditStatus.INIT.getCode())
                    .build();
            logOperateForLongService.addInsertLog(DbTable.LAU_NATIVE_ARCHIVE, operator, nativeArchiveLogBo, id);
        } else {
            if (flyArc != null) {
                existPo.setCover(flyArc.getPic());
                existPo.setTitle(flyArc.getTitle());
                existPo.setTagIds(flyArc.getTag());
                Author author = flyArc.getAuthor();
                if (author != null) {
                    existPo.setUpMid(author.getMid());
                    existPo.setUpNickname(author.getName());
                }
            }
            lauNativeArchiveRepo.updateNativeArchive(existPo);
        }
    }

    @Transactional(value = AD_CORE_TM, rollbackFor = Exception.class)
    public Long nativeArchiveToAudit(List<Long> avids) {
        if (CollectionUtils.isEmpty(avids)) {
            return 0L;
        }

        List<LauNativeArchivePo> nativeArchivePos = lauNativeArchiveRepo.queryNativeArchive(avids, null, NativeBodyType.ARCHIVE_VALUE);
        if (CollectionUtils.isEmpty(nativeArchivePos)) {
            return 0L;
        }

        // 待审日志
        int count = lauNativeArchiveRepo.updateNativeArchiveToAudit(avids);

        Operator operator = new Operator("SYSTEM", OperatorType.SYSTEM);
        for (LauNativeArchivePo nativeArchivePo : nativeArchivePos) {
            Integer bizType = nativeArchivePo.getBizType();
            operator.setBilibiliUserName(bizType + "");

            NativeArchiveLogBo logBo = NativeArchiveLogBo.builder()
                    .auditStatus(AuditStatus.INIT.getCode())
                    .build();
            logOperateForLongService.addUpdateLog(DbTable.LAU_NATIVE_ARCHIVE, operator, logBo, nativeArchivePo.getId());
        }
        return (long) count;
    }

    @Transactional(value = AD_CORE_TM, rollbackFor = Exception.class)
    public Long nativeCreativeToAudit(List<Integer> creativeIds) {

        if (CollectionUtils.isEmpty(creativeIds)) {
            return 0L;
        }

        // 待审日志
        int count = lauCreativeNativeArchiveRelativityRepo.updateNativeCreativeToAudit(creativeIds);

        Operator operator = new Operator("SYSTEM", OperatorType.SYSTEM);
        for (Integer creativeId : creativeIds) {
            LauCreativeNativeArchiveRelativityPo relativityPo = new LauCreativeNativeArchiveRelativityPo();
            relativityPo.setCreativeId(creativeId);
            relativityPo.setAuditStatus(AuditStatus.INIT.getCode());
            logOperateService.addUpdateLog(DbTable.LAU_CREATIVE_NATIVE_ARCHIVE_RELATIVITY, operator, relativityPo, creativeId);
        }
        return (long) count;
    }


    public List<LauCreativeNativeArchiveRelativityDto> queryNativeRelativityInfoByCreativeIds(List<Integer> creativeIds) {
        if (CollectionUtils.isEmpty(creativeIds)) {
            return Collections.emptyList();
        }

        return lauCreativeNativeArchiveRelativityRepo.queryNativeArchiveRelativityByCreativeIds(creativeIds)
                .stream()
                .map(NativeAdMapper.MAPPER::fromPo)
                .collect(Collectors.toList());
    }

    public List<LauNativeArchiveDto> queryNativeInfoByIds(List<Long> avids, List<Integer> typeList) {
        if (CollectionUtils.isEmpty(avids)) {
            return Collections.emptyList();
        }

        List<LauNativeArchivePo> poList =
                lauNativeArchiveRepo.queryNativeArchiveByAvidAndTypeList(avids, NativeBizType.SANLIAN_VALUE, typeList);
        return poList.stream()
            .map(NativeAdMapper.MAPPER::fromPo)
            .collect(Collectors.toList());
    }
}
