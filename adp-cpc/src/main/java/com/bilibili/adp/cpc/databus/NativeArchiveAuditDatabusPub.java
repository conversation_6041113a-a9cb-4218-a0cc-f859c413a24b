package com.bilibili.adp.cpc.databus;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bilibili.adp.common.exception.ServiceRuntimeException;
import com.bilibili.adp.cpc.biz.bos.WarpDatabusProperty;
import com.bilibili.adp.cpc.biz.constants.Constants;
import com.bilibili.adp.cpc.databus.bos.NativeArchivePushAuditMsg;
import com.bilibili.adp.util.common.AdpCatUtils;
import com.bilibili.warp.databus.DatabusProperty;
import com.bilibili.warp.databus.DatabusTemplate;
import com.bilibili.warp.databus.Message;
import com.bilibili.warp.databus.PubResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.Map;

/**
 * 原生稿件审核 databus 消息发布
 * <AUTHOR>
 * @date 2024/3/13 14:23
 */
@Slf4j
@Component
public class NativeArchiveAuditDatabusPub {

    public static final String CREATIVE_AUDIT = "native-archive-audit";
    private final String topic;
    private final String group;

    @Autowired
    private DatabusTemplate databusTemplate;

    public NativeArchiveAuditDatabusPub(WarpDatabusProperty warpDatabusProperty) {
        // 注入DatabusProperties，此处的ALIAS常量即配置文件中填写的别名，是topic的上一级配置键
        // pub场景同理，在使用DatabusTemplate的地方注入DatabusProperties来获取
        Map<String, DatabusProperty> properties = warpDatabusProperty.getProperties();
        DatabusProperty property = properties.get(CREATIVE_AUDIT);
        log.info("NativeArchiveAuditDatabusPub, property={}", JSONObject.toJSONString(property));
        this.topic = property.getTopic();
        this.group = property.getPub().getGroup();
    }

    /**
     * 发布推审消息
     *
     * @param auditMessage
     */
    public void pubMsg(NativeArchivePushAuditMsg auditMessage) {
        String msgStr = JSON.toJSONString(auditMessage);
        log.info("wrap native archive audit pub msg, msg={}", msgStr);
        Assert.notNull(auditMessage, "auditMessage参数不能为空");

        AdpCatUtils.newTransaction(Constants.CAT_TYPE_DATABUS, CREATIVE_AUDIT + ":pub", transaction -> {
            // messageKey和value自定义，value会被配置的serializer序列化
            Message message = Message.Builder.of(msgStr, auditMessage).build();
            // topic 和 group 按照申请的填写即可. 可以自行注入相关配置，用配置的方式来发送消息
            PubResult result = databusTemplate.pub(topic, group, message);
            if (result.isSuccess()) {
                log.info("native archive pub msg success, msg={}", JSON.toJSONString(auditMessage));
            } else {
                Throwable throwable = result.getThrowable();
                log.info("native archive pub msg error, e={}", throwable);
                throw new ServiceRuntimeException(throwable.getMessage());
            }
        });
    }
}
