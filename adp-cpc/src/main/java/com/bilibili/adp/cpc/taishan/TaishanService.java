package com.bilibili.adp.cpc.taishan;

import com.bapis.infra.service.taishan.*;
import com.bilibili.adp.cpc.config.taishan.TaishanConfig;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.protobuf.ByteString;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.common.TriFunction;
import org.springframework.stereotype.Service;
import pleiades.venus.context.concurrent.ContextFutures;
import pleiades.venus.starter.rpc.client.RPCClient;

import javax.annotation.Resource;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.function.BiFunction;

@Slf4j
@Service //springboot自动构造
public class TaishanService {

    //异步调用的stub,通过@RPCClient自动生成,输入taishan proxy的服务发现ID
    @RPCClient(value = "inf.taishan.proxy", cluster = "common")
    private TaishanProxyGrpc.TaishanProxyFutureStub futureStub;

    //同步调用的stub
    @RPCClient(value = "inf.taishan.proxy", cluster = "common")
    private TaishanProxyGrpc.TaishanProxyBlockingStub blockingStub;

    @Resource(name = TaishanConfig.TAISHAN_MAP)
    private Map<String, String> tokenMap;

    //record 代表一个kv pair,taishan proto中会用到
    public static final BiFunction<String, String, Record> recordBuilder = (key, value) -> {
        Record.Builder builder = Record.newBuilder();
        builder.setKey(ByteString.copyFromUtf8(key));
        if (value != null) {
            builder.addColumns(Column.newBuilder()
                    .setValue(ByteString.copyFromUtf8(value))
                    .build());
        }
        return builder.build();
    };
    // record 代表一个kv pair, taishan proto中会用到
    public static final TriFunction<String, String, Integer, Record> recordBuilderWithTTL = (key, value, ttl) -> {
        Record.Builder builder = Record.newBuilder();
        builder.setKey(ByteString.copyFromUtf8(key));
        if (value != null) {
            builder.addColumns(Column.newBuilder()
                    .setValue(ByteString.copyFromUtf8(value))
                    .build());
        }
        if (ttl != null) {
            builder.setTtl(ttl);
        }
        return builder.build();
    };

    //同步get获取
    public String get(String table, String key) throws Exception {
        GetReq getReq = GetReq.newBuilder()
                .setAuth(Auth.newBuilder().setToken(tokenMap.get(table)).build())
                .setTable(table)
                .setRecord(recordBuilder.apply(key, null))
                .build();
        ByteString byteString = blockingStub.withDeadlineAfter(200, TimeUnit.MILLISECONDS).get(getReq).getRecord().getColumns(0).getValue();
        if (byteString == null) {
            return null;
        } else {
            return byteString.toStringUtf8();
        }
    }

    //同步set方法
    public void set(String table, String key, String value) {
        PutReq putReq = PutReq.newBuilder()
                .setAuth(Auth.newBuilder().setToken(tokenMap.get(table)).build())
                .setTable(table)
                .setRecord(recordBuilder.apply(key, value))
                .build();
        PutResp resp = blockingStub.put(putReq);
        if (resp.getStatus().getErrNo() != 0) {
            log.warn("set key error, key: {}, resp: {}", key, resp);
            throw new RuntimeException("set taishan key error");
        }
    }

    //同步del
    public void del(String table, String key) {
        DelReq delReq = DelReq.newBuilder()
                .setAuth(Auth.newBuilder().setToken(tokenMap.get(table)).build())
                .setTable(table)
                .setRecord(recordBuilder.apply(key, null))
                .build();

        DelResp resp = blockingStub.del(delReq);
        if (resp.getStatus().getErrNo() != 0) {
            log.warn("taishan删除操作失败，key:{}, resp:{}", key, resp);
            throw new RuntimeException("del taishan key error");
        }
    }

    //异步put
    public void putAsync(String table, String key, String value) {
        PutReq putReq = PutReq.newBuilder()
                .setAuth(Auth.newBuilder().setToken(tokenMap.get(table)).build())
                .setTable(table)
                .setRecord(recordBuilder.apply(key, value))
                .build();
        ContextFutures.asCompletableFuture(futureStub.put(putReq)).exceptionally(e -> {
            log.warn("taishan插入操作失败，key:{},value:{}", key, value, e);
            return null;
        });
    }

    //异步del
    public void delAsync(String table, String key) {
        DelReq delReq = DelReq.newBuilder()
                .setAuth(Auth.newBuilder().setToken(tokenMap.get(table)).build())
                .setTable(table)
                .setRecord(recordBuilder.apply(key, null))
                .build();

        ContextFutures.asCompletableFuture(futureStub.del(delReq)).exceptionally(e -> {
            log.warn("taishan删除操作失败，key:{}", key, e);
            return null;
        });
    }

    //同步batch get
    public BatchGetResp batchGet(BatchGetReq batchGetReq) {
        try {
            BatchGetResp reply = blockingStub
                    .withDeadlineAfter(500, TimeUnit.MILLISECONDS)
                    .withWaitForReady()
                    .batchGet(batchGetReq);
            if (reply != null && reply.getAllSucceed()) {
                return reply;
            } else {
                log.warn("tai shan batchGet error! batchPutReq:{}, reply:{}", batchGetReq, reply);
                return reply;
            }
        } catch (Exception e) {
            log.warn("tai shan batch_get error! batchPutReq:{}", batchGetReq, e);
            return null;
        }
    }

    public BatchPutResp batchPut(BatchPutReq batchPutReq) {
        try {
            BatchPutResp reply = blockingStub
                    .withDeadlineAfter(500, TimeUnit.MILLISECONDS)
                    .withWaitForReady()
                    .batchPut(batchPutReq);
            if (reply != null && reply.getAllSucceed()) {
                return reply;
            } else {
                log.warn("tai shan batchPut error! batchPutReq:{}, reply:{}", batchPutReq, reply);
                return reply;
            }
        } catch (Exception e) {
            log.warn("tai shan batchPut error! batchPutReq:{}", batchPutReq, e);
            return null;
        }
    }


    public void batchPutAsync(BatchPutReq batchPutReq) {
        ListenableFuture<BatchPutResp> future = futureStub
                .withDeadlineAfter(500, TimeUnit.MILLISECONDS)
                .withWaitForReady()
                .batchPut(batchPutReq);
        ContextFutures.asCompletableFuture(future).exceptionally(e -> {
            return null;
        });
    }
}
