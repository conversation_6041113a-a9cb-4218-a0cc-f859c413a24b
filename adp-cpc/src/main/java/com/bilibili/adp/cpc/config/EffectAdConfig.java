package com.bilibili.adp.cpc.config;

import com.bilibili.adp.cpc.config.unit.UnitConfig;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import pleiades.venus.config.WatchedProperties;
import pleiades.venus.starter.paladin.PaladinPropertySourceFactory;
@WatchedProperties
@Configuration
@PropertySource(value = "classpath:effect_ad.yaml", factory = PaladinPropertySourceFactory.class)
public class EffectAdConfig {

    @Bean
    @ConfigurationProperties(prefix = "unit")
    public UnitConfig unitConfig() {
        return new UnitConfig();
    }
}
