package com.bilibili.adp.cpc.biz.services.danmaku;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.util.SnowflakeIdWorker;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.biz.converter.danmaku.SelfDanmakuConverter;
import com.bilibili.adp.cpc.biz.services.danmaku.api.ISelfDanmakuMaterialService;
import com.bilibili.adp.cpc.biz.services.danmaku.bos.*;
import com.bilibili.adp.cpc.biz.services.danmaku.bos.audit.DanmakuGroupSendAuditBo;
import com.bilibili.adp.cpc.biz.services.danmaku.bos.query.QuerySelfDanmakuBo;
import com.bilibili.adp.cpc.biz.services.danmaku.bos.query.QuerySelfDanmakuGroupBo;
import com.bilibili.adp.cpc.biz.services.log.DbTable;
import com.bilibili.adp.cpc.biz.services.log.ILogOperateNewService;
import com.bilibili.adp.cpc.core.constants.IsDeleted;
import com.bilibili.adp.cpc.dao.querydsl.pos.LauSelfDanmakuGroupPo;
import com.bilibili.adp.cpc.enums.danmaku.SelfDanmakuAuditStatusEnum;
import com.bilibili.adp.cpc.enums.danmaku.SelfDanmakuGroupTypeEnum;
import com.bilibili.adp.cpc.enums.danmaku.SelfDanmakuStatusEnum;
import com.bilibili.adp.cpc.enums.danmaku.SelfDanmakuTypeEnum;
import com.bilibili.adp.cpc.utils.SelfDanmakuValidateUtil;
import com.bilibili.bjcom.querydsl.BaseQueryFactory;
import com.bilibili.bjcom.querydsl.clause.BaseQuery;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.bilibili.adp.cpc.dao.config.PlatformMySqlConfig.AD_BQF;
import static com.bilibili.adp.cpc.dao.config.PlatformMySqlConfig.AD_TM;
import static com.bilibili.adp.cpc.dao.querydsl.QLauSelfDanmakuGroup.lauSelfDanmakuGroup;

/**
 * @ClassName SelfDanmakuGroupService
 * <AUTHOR>
 * @Date 2023/12/29 3:43 下午
 * @Version 1.0
 **/
@Service
@Slf4j
public class SelfDanmakuGroupServiceDelegate {

    @Resource(name = AD_BQF)
    private BaseQueryFactory adBqf;

    @Resource
    private SnowflakeIdWorker snowflakeIdWorker;

    @Resource
    private SelfDanmakuService selfDanmakuService;

    @Resource
    private AdpCpcSelfDanmakuGroupMappingService adpCpcSelfDanmakuGroupMappingService;

    @Resource
    private SelfDanmakuGroupShadowService selfDanmakuGroupShadowService;

    @Resource
    private ISelfDanmakuMaterialService selfDanmakuMaterialService;

    @Resource
    private SelfDanmakuGroupAuditService selfDanmakuGroupAuditService;

    @Autowired
    private ILogOperateNewService logOperateService;

    private void validateSaveBo(SelfDanmakuGroupSaveBo saveBo) {
        Assert.notNull(saveBo, "保存商业弹幕组对象不可为空");
        Assert.isTrue(Utils.isPositive(saveBo.getAccountId()), "商业弹幕组账户id不可为空");
        Assert.hasText(saveBo.getGroupName(), "商业弹幕组名称不可为空");
        checkGroupNameDuplicate(saveBo.getGroupName(), saveBo.getAccountId(), saveBo.getGroupId());
        List<SelfDanmakuSaveEntityBo> entityBoList = saveBo.getEntityBoList();
        Assert.notEmpty(entityBoList, "弹幕组绑定弹幕不可为空");
        SelfDanmakuGroupTypeEnum groupTypeEnum = SelfDanmakuGroupTypeEnum.getByCode(saveBo.getGroupType());
        Map<Integer, List<SelfDanmakuSaveEntityBo>> danmakuEntityTypeMap = entityBoList.stream()
                .collect(Collectors.groupingBy(SelfDanmakuSaveEntityBo::getDanmakuType));
        List<SelfDanmakuSaveEntityBo> baseEntityBoList =
                danmakuEntityTypeMap.getOrDefault(SelfDanmakuTypeEnum.BASIC.getCode(), Collections.emptyList());
        List<SelfDanmakuSaveEntityBo> colorEntityBoList =
                danmakuEntityTypeMap.getOrDefault(SelfDanmakuTypeEnum.COLOR.getCode(), Collections.emptyList());
        List<SelfDanmakuSaveEntityBo> picEntityBoList =
                danmakuEntityTypeMap.getOrDefault(SelfDanmakuTypeEnum.PIC.getCode(), Collections.emptyList());
        switch (groupTypeEnum) {
            case PIC:
                Assert.isTrue(CollectionUtils.isEmpty(baseEntityBoList), "纯图弹幕组不支持基础弹幕");
                Assert.isTrue(CollectionUtils.isEmpty(colorEntityBoList), "纯图弹幕组不支持彩色弹幕");
                Assert.isTrue(picEntityBoList.size() >= 1 && picEntityBoList.size() <= 3, "纯图弹幕组支持1-3个图片弹幕");
                break;
            case MIX:
                Assert.isTrue(entityBoList.size() >= 20 && entityBoList.size() <= 50,
                        "混合弹幕组弹幕组件为20-50个");
                break;
            default:
                throw new IllegalArgumentException("未知的弹幕组类型：" + saveBo.getGroupType());
        }
        entityBoList.forEach(this::validateSaveEntityBoAndFillMd5);
        List<String> entityMd5List = entityBoList.stream()
                .map(SelfDanmakuSaveEntityBo::getDanmakuMd5)
                .distinct().collect(Collectors.toList());
        Assert.isTrue(entityBoList.size() == entityMd5List.size(), "当前弹幕组内弹幕存在重复内容,请修改");
    }

    private void validateSaveEntityBoAndFillMd5(SelfDanmakuSaveEntityBo saveBo) {
        Assert.notNull(saveBo, "保存商业弹幕具体对象不可为空");
        SelfDanmakuValidateUtil.validateSaveEntityBoAndFillMd5(saveBo);
    }

    public List<SelfDanmakuGroupInfoBo> queryForList(QuerySelfDanmakuGroupBo queryBo) {
        validateQueryBo(queryBo);
        return generateBaseQuery(queryBo).fetch(SelfDanmakuGroupInfoBo.class);
    }

    public PageResult<SelfDanmakuGroupInfoBo> queryForPage(QuerySelfDanmakuGroupBo queryBo) {
        validateQueryBo(queryBo);
        long total = generateBaseQuery(queryBo).fetchCount();
        List<SelfDanmakuGroupInfoBo> recordList = generateBaseQuery(queryBo).fetch(SelfDanmakuGroupInfoBo.class);
        queryPostHandle(recordList);
        return PageResult.<SelfDanmakuGroupInfoBo>builder()
                .records(recordList)
                .total((int) total)
                .build();
    }

    private void queryPostHandle(List<SelfDanmakuGroupInfoBo> boList) {
        if (CollectionUtils.isEmpty(boList)) {
            return;
        }

        List<Long> groupIdList = boList.stream()
                .map(SelfDanmakuGroupInfoBo::getGroupId)
                .collect(Collectors.toList());
        List<Long> overLimitGroupIds =
                adpCpcSelfDanmakuGroupMappingService.queryBindingOverLimitGroupIds(groupIdList);
        Map<Long, List<SelfDanmakuBo>> danmakuGroupIdMap = selfDanmakuService.queryMapByGroupIdList(groupIdList);
        List<SelfDanmakuGroupShadowBo> shadowBoList = selfDanmakuGroupShadowService.getShadowBoListByGroupIdList(groupIdList);
        Map<Long, List<SelfDanmakuSaveEntityBo>> shadowEntityGroupIdMap = shadowBoList.stream()
                .collect(Collectors.toMap(SelfDanmakuGroupShadowBo::getGroupId,
                        SelfDanmakuGroupShadowBo::getEntityBoList));

        boList.forEach(bo -> {
            Long groupId = bo.getGroupId();
            bo.setHasShadow(shadowEntityGroupIdMap.containsKey(bo.getGroupId()) ? 1 : 0);
            bo.setCanBindCreative(overLimitGroupIds.contains(bo.getGroupId()) ? 0 : 1);
            if (shadowEntityGroupIdMap.containsKey(groupId)) {
                bo.setDanmakuInfoList(SelfDanmakuConverter.MAPPER.convertDanmakuSaveEntityList2InfoBoList(
                        shadowEntityGroupIdMap.get(groupId)));
            } else {
                bo.setDanmakuInfoList(SelfDanmakuConverter.MAPPER.convertDanmakuBoList2InfoBoList(
                        danmakuGroupIdMap.getOrDefault(groupId, Collections.emptyList())));
            }
        });
    }

    public SelfDanmakuGroupInfoBo getBase(Long groupId) {
        Assert.isTrue(Utils.isPositive(groupId), "查询商业弹幕组弹幕组id不可为空");

        QuerySelfDanmakuGroupBo queryBo = QuerySelfDanmakuGroupBo.builder()
                .danmakuGroupIdList(Lists.newArrayList(groupId))
                .danmakuGroupStatusList(SelfDanmakuStatusEnum.NON_DELETED_SELF_DANMAKU_STATUS_LIST)
                .build();

        List<SelfDanmakuGroupInfoBo> groupBaseBoList = queryForList(queryBo);
        if (CollectionUtils.isEmpty(groupBaseBoList)) {
            return null;
        }

        return groupBaseBoList.get(0);
    }

    public SelfDanmakuGroupBo getDetail(int accountId, Long groupId) {
        Assert.isTrue(Utils.isPositive(accountId), "查询商业弹幕组账户id不可为空");
        Assert.isTrue(Utils.isPositive(groupId), "查询商业弹幕组弹幕组id不可为空");

        SelfDanmakuGroupInfoBo groupBaseBo = getBase(groupId);
        if (Objects.isNull(groupBaseBo)) {
            return null;
        }

        Assert.isTrue(groupBaseBo.getAccountId().equals(accountId), "您不能查看不属于当前账户的弹幕组");
        QuerySelfDanmakuBo queryDanmakuBo = QuerySelfDanmakuBo.builder()
                .danmakuStatusList(SelfDanmakuStatusEnum.NON_DELETED_SELF_DANMAKU_STATUS_LIST)
                .groupId(groupBaseBo.getGroupId())
                .build();

        List<SelfDanmakuBo> danmakuBoList = selfDanmakuService.queryList(queryDanmakuBo);
        SelfDanmakuGroupShadowBo shadowBo = selfDanmakuGroupShadowService.getShadowBoByDanmakuGroupId(groupId);
        if (Objects.nonNull(shadowBo)) {
            danmakuBoList = shadowBo.getEntityBoList().stream()
                    .map(this::getDanmakuBoWithSaveEntityBo)
                    .collect(Collectors.toList());
        }
        SelfDanmakuGroupBo groupBo = SelfDanmakuConverter.MAPPER.convertGroupBaseBo2Bo(groupBaseBo);
        groupBo.setDanmakuBoList(danmakuBoList);
        groupBo.setHasShadow(Objects.nonNull(shadowBo) ? 1 : 0);
        return groupBo;
    }

    private SelfDanmakuBo getDanmakuBoWithSaveEntityBo(SelfDanmakuSaveEntityBo saveEntityBo) {
        if (Objects.isNull(saveEntityBo)) {
            return null;
        }

        return SelfDanmakuBo.builder()
                .accountId(saveEntityBo.getAccountId())
                .groupId(saveEntityBo.getGroupId())
                .danmakuId(saveEntityBo.getDanmakuId())
                .danmakuType(saveEntityBo.getDanmakuType())
                .danmakuText(saveEntityBo.getDanmakuText())
                .danmakuColor(saveEntityBo.getDanmakuColor())
                .danmakuIconId(saveEntityBo.getDanmakuIconId())
                .danmakuPicUrl(saveEntityBo.getDanmakuPicUrl())
                .danmakuMd5(saveEntityBo.getDanmakuMd5())
                .build();
    }

    private BaseQuery<LauSelfDanmakuGroupPo> generateBaseQuery(QuerySelfDanmakuGroupBo queryBo) {
        return adBqf.selectFrom(lauSelfDanmakuGroup)
                .whereIfNotEmpty(queryBo.getDanmakuGroupIdList(), lauSelfDanmakuGroup.groupId::in)
                .whereIfNotEmpty(queryBo.getDanmakuGroupStatusList(), lauSelfDanmakuGroup.groupStatus::in)

                .whereIfNotNull(queryBo.getAccountId(), lauSelfDanmakuGroup.accountId::eq)
                .whereIfNotNull(queryBo.getDanmakuGroupType(), lauSelfDanmakuGroup.groupType::eq)
                .whereIfNotNull(queryBo.getDanmakuGroupStatus(), lauSelfDanmakuGroup.groupStatus::eq)
                .whereIfNotNull(queryBo.getStartMtime(), startMtime -> lauSelfDanmakuGroup.mtime.goe(new Timestamp(startMtime)))
                .whereIfNotNull(queryBo.getEndMtime(), endMtime -> lauSelfDanmakuGroup.mtime.loe(new Timestamp(endMtime)))

                .whereIfHasText(queryBo.getGroupName(), lauSelfDanmakuGroup.groupName::eq)
                .whereIfHasText(queryBo.getLikeGroupName(),
                        likeGroupName -> lauSelfDanmakuGroup.groupName.like("%" + likeGroupName + "%"))

                .where(lauSelfDanmakuGroup.isDeleted.eq(IsDeleted.VALID))

                .orderByIfHasText(queryBo.getOrderBy())

                .limitIfNotNull(queryBo.getPageSize())
                .offsetIfNotNull(Objects.isNull(queryBo.getPage()) || Objects.isNull(queryBo.getPageSize()) ?
                        null : (long) (queryBo.getPage() - 1) * queryBo.getPageSize());
    }

    private void validateQueryBo(QuerySelfDanmakuGroupBo queryBo) {
        Assert.notNull(queryBo, "查询商业弹幕组条件不可为空");
        boolean hasAccountId = Utils.isPositive(queryBo.getAccountId());
        boolean hasDanmakuGroupIdList = !CollectionUtils.isEmpty(queryBo.getDanmakuGroupIdList());
        Assert.isTrue(hasAccountId || hasDanmakuGroupIdList, "查询商业自提弹幕组条件不足");
    }

    @Transactional(value = AD_TM, rollbackFor = Exception.class)
    public Long createSelfDanmakuGroup(SelfDanmakuGroupSaveBo insertBo, Operator operator) {
        Assert.isTrue(!Utils.isPositive(insertBo.getGroupId()), "新建弹幕组不允许指定弹幕组id");
        validateSaveBo(insertBo);
        Long groupId = snowflakeIdWorker.nextId();
        insertBo.setGroupId(groupId);
        insertBo.setGroupStatus(SelfDanmakuStatusEnum.WAIT_AUDIT.getCode());
        insertBo.setGroupAuditStatus(SelfDanmakuAuditStatusEnum.WAIT_AUDIT.getCode());

        LauSelfDanmakuGroupPo insertPo = SelfDanmakuConverter.MAPPER.convertGroupSaveBo2Po(insertBo);
        Long id = adBqf.insert(lauSelfDanmakuGroup).insertGetKey(insertPo);

        insertBo.getEntityBoList().forEach(entityBo -> {
            entityBo.setGroupId(groupId);
            entityBo.setDanmakuStatus(SelfDanmakuStatusEnum.WAIT_AUDIT.getCode());
            entityBo.setDanmakuAuditStatus(SelfDanmakuAuditStatusEnum.WAIT_AUDIT.getCode());
        });
        selfDanmakuService.saveSelfDanmakuList(insertBo, Collections.emptyList(), true);

        DanmakuGroupSendAuditBo sendAuditBo = DanmakuGroupSendAuditBo.builder()
                .groupId(groupId)
                .groupName(insertBo.getGroupName())
                .accountId(insertBo.getAccountId())
                .groupType(insertBo.getGroupType())
                .logId(id)
                .build();
        selfDanmakuGroupAuditService.sendAudit(sendAuditBo);

        SelfDanmakuGroupLogBo insertLogBo = SelfDanmakuConverter.MAPPER.convertDanmakuGroupSaveBo2LogBo(insertBo);

        logOperateService.addInsertLog(DbTable.LAU_SELF_DANMAKU_GROUP, operator, insertLogBo, id.intValue());
        return groupId;
    }

    @Transactional(value = AD_TM, rollbackFor = Exception.class)
    public Long updateSelfDanmakuGroup(SelfDanmakuGroupSaveBo updateBo, Operator operator) {
        Long groupId = updateBo.getGroupId();
        int accountId = operator.getOperatorId();
        Assert.isTrue(Utils.isPositive(groupId), "更新弹幕组id不可为空");
        validateSaveBo(updateBo);

        SelfDanmakuGroupBo current = getDetail(accountId, groupId);
        Assert.notNull(current, "当前弹幕组不存在或已被删除");
        Assert.isTrue(!current.getGroupStatus().equals(SelfDanmakuStatusEnum.DELETED.getCode()),
                "不允许编辑已经删除的弹幕组");
        Assert.isTrue(current.getVersion().equals(updateBo.getVersion()), "当前弹幕组已被编辑,请刷新页面");

        updateBo.setVersion(updateBo.getVersion() + 1L);

        List<SelfDanmakuBo> existDanmakuBoList = current.getDanmakuBoList();

        Map<Long, SelfDanmakuBo> originDanmakuBoIdMap = existDanmakuBoList.stream()
                //此时影子的部分新增弹幕可能还没有弹幕id
                .filter(existDanmakuBo -> Utils.isPositive(existDanmakuBo.getDanmakuId()))
                .collect(Collectors.toMap(SelfDanmakuBo::getDanmakuId, Function.identity()));

        // 判断是否需要送审 或者说是否改动/新增了弹幕组内的弹幕内容
        Set<Boolean> needAuditList = updateBo.getEntityBoList().stream().map(saveEntityBo -> {
            if (!Utils.isPositive(saveEntityBo.getDanmakuId())) {
                return true;
            }
            SelfDanmakuBo originBo = originDanmakuBoIdMap.get(saveEntityBo.getDanmakuId());
            Assert.notNull(originBo, "当前更新弹幕" + saveEntityBo.getDanmakuId() + "不存在,更新失败");
            return !saveEntityBo.getDanmakuMd5().equals(originBo.getDanmakuMd5());
        }).collect(Collectors.toSet());
        // 更新/新建弹幕 更换弹幕组状态 需要送审
        boolean needAudit = needAuditList.contains(Boolean.TRUE)
                || !updateBo.getGroupType().equals(current.getGroupType());

        // 填充更新前的状态 为日志做准备
        updateBo.setGroupStatus(current.getGroupStatus());
        updateBo.setGroupAuditStatus(current.getGroupAuditStatus());

        updateBo.getEntityBoList().forEach(entityBo -> {
            entityBo.setGroupId(groupId);
        });

        SelfDanmakuGroupShadowBo shadowBo = selfDanmakuGroupShadowService.getShadowBoByDanmakuGroupId(groupId);
        boolean hasShadow = Objects.nonNull(shadowBo);

        // 非审核通过 或者审核通过且不需要推审 直接改动弹幕组本体内容 这个时候不需要影子
        if (!current.getGroupStatus().equals(SelfDanmakuStatusEnum.AUDIT_PASS.getCode())
                || !needAudit && !hasShadow) {
            // 如果需要推审 所有弹幕 &弹幕组状态翻转
            if (needAudit) {
                updateBo.getEntityBoList().forEach(entityBo -> {
                    entityBo.setDanmakuStatus(SelfDanmakuStatusEnum.WAIT_AUDIT.getCode());
                    entityBo.setDanmakuAuditStatus(SelfDanmakuAuditStatusEnum.WAIT_AUDIT.getCode());
                });
                updateBo.setGroupStatus(SelfDanmakuStatusEnum.WAIT_AUDIT.getCode());
                updateBo.setGroupAuditStatus(SelfDanmakuAuditStatusEnum.WAIT_AUDIT.getCode());
            }
            selfDanmakuService.saveSelfDanmakuList(updateBo, existDanmakuBoList, true);
        } else {
            // 影子逻辑 直接保存影子
            List<SelfDanmakuSaveEntityBo> entityBoList = updateBo.getEntityBoList();
            String shadowStr = JSON.toJSONString(entityBoList);
            SelfDanmakuGroupShadowSaveBo shadowSaveBo = SelfDanmakuGroupShadowSaveBo.builder()
                    .groupId(groupId)
                    .accountId(accountId)
                    .shadowDanmaku(shadowStr)
                    .build();
            selfDanmakuGroupShadowService.saveShadowWithLock(shadowSaveBo);
            // 同步素材库
            List<SelfDanmakuMaterialSaveBo> materialSaveBoList =
                    SelfDanmakuConverter.MAPPER.convertSave2MaterialSaveBoList(entityBoList);
            materialSaveBoList.forEach(materialSaveBo -> {
                Long materialId = snowflakeIdWorker.nextId();
                String danmakuTypeDesc =
                        SelfDanmakuTypeEnum.getByCode(materialSaveBo.getDanmakuType())
                                .getDesc();
                String materialName = updateBo.getGroupName() + "-" + danmakuTypeDesc + "-" + materialId;
                materialSaveBo.setDanmakuName(materialName);
                materialSaveBo.setDanmakuMaterialId(materialId);
            });
            selfDanmakuMaterialService.batchInsertMaterialListFromGroup(materialSaveBoList, accountId);

        }

        if (needAudit || hasShadow) {
            DanmakuGroupSendAuditBo sendAuditBo = DanmakuGroupSendAuditBo.builder()
                    .groupId(groupId)
                    .groupName(updateBo.getGroupName())
                    .accountId(accountId)
                    .groupType(updateBo.getGroupType())
                    .logId(current.getId())
                    .build();
            selfDanmakuGroupAuditService.sendAudit(sendAuditBo);
        }

        // 弹幕组本身改动不需要触审 不需要影子逻辑
        LauSelfDanmakuGroupPo updatePo = SelfDanmakuConverter.MAPPER.convertGroupSaveBo2Po(updateBo);
        adBqf.update(lauSelfDanmakuGroup)
                .where(lauSelfDanmakuGroup.groupId.eq(groupId))
                .where(lauSelfDanmakuGroup.isDeleted.eq(IsDeleted.VALID))
                .populate(updatePo)
                .execute();

        SelfDanmakuGroupLogBo originLogBo = SelfDanmakuConverter.MAPPER.convertDanmakuGroupBo2LogBo(current);
        SelfDanmakuGroupLogBo updateLogBo = SelfDanmakuConverter.MAPPER.convertDanmakuGroupSaveBo2LogBo(updateBo);

        logOperateService.addUpdateLog(DbTable.LAU_SELF_DANMAKU_GROUP, operator, originLogBo, updateLogBo, current.getId().intValue());

        return updateBo.getGroupId();
    }

    @Transactional(value = AD_TM, rollbackFor = Exception.class)
    public void auditPass(SelfDanmakuGroupAuditBo auditBo) {
        Long groupId = auditBo.getGroupId();
        Operator operator = auditBo.getOperator();
        Long version = auditBo.getVersion();

        SelfDanmakuGroupBo detail = getDetail(operator.getOperatorId(), groupId);
        // 不能过审已经删除或者不存在的弹幕组
        if (Objects.isNull(detail)
                || detail.getGroupStatus().equals(SelfDanmakuStatusEnum.DELETED.getCode())) {
            return;
        }

        Assert.isTrue(detail.getVersion().equals(version), "弹幕组" + groupId + "已变更,请刷新页面");
        SelfDanmakuGroupSaveBo saveBo = SelfDanmakuGroupSaveBo.builder()
                .accountId(detail.getAccountId())
                .groupId(groupId)
                .groupStatus(SelfDanmakuStatusEnum.AUDIT_PASS.getCode())
                .groupAuditStatus(SelfDanmakuAuditStatusEnum.AUDIT_PASS.getCode())
                .version(version + 1L)
                .build();
        List<SelfDanmakuBo> existDanmakuBoList = selfDanmakuService.queryByGroupId(groupId);
        SelfDanmakuGroupShadowBo shadowBo = selfDanmakuGroupShadowService.getShadowBoByDanmakuGroupId(groupId);
        List<SelfDanmakuSaveEntityBo> entityBoList;
        // 影子回写
        if (Objects.nonNull(shadowBo)) {
            entityBoList = shadowBo.getEntityBoList();
            entityBoList.forEach(entityBo -> {
                entityBo.setDanmakuStatus(SelfDanmakuStatusEnum.AUDIT_PASS.getCode());
                entityBo.setDanmakuAuditStatus(SelfDanmakuAuditStatusEnum.AUDIT_PASS.getCode());
            });
            saveBo.setEntityBoList(entityBoList);
            selfDanmakuService.saveSelfDanmakuList(saveBo, existDanmakuBoList, false);
            selfDanmakuGroupShadowService.deleteShadowByGroupIdList(Lists.newArrayList(groupId));
        } else {
            List<Long> danmakuIdList = existDanmakuBoList.stream()
                    .map(SelfDanmakuBo::getDanmakuId)
                    .collect(Collectors.toList());
            // 不存在影子 直接更新状态
            selfDanmakuService.auditPassByGroupIdAndDanmakuIdList(groupId, danmakuIdList);
        }

        LauSelfDanmakuGroupPo updatePo = SelfDanmakuConverter.MAPPER.convertGroupSaveBo2Po(saveBo);
        updatePo.setReason("");
        adBqf.update(lauSelfDanmakuGroup)
                .where(lauSelfDanmakuGroup.groupId.eq(groupId))
                .where(lauSelfDanmakuGroup.isDeleted.eq(IsDeleted.VALID))
                .populate(updatePo)
                .execute();

        long danmakuNum = selfDanmakuService.countByGroupId(groupId);
        selfDanmakuGroupAuditService.auditPass(auditBo, danmakuNum);

        SelfDanmakuGroupLogBo originLogBo = SelfDanmakuGroupLogBo.builder()
                .groupId(groupId)
                .groupStatusDesc(SelfDanmakuStatusEnum.getByCode(detail.getGroupStatus()).getDesc())
                .groupAuditStatusDesc(SelfDanmakuAuditStatusEnum.getByCode(detail.getGroupAuditStatus()).getDesc())
                .build();

        SelfDanmakuGroupLogBo updateLogBo = SelfDanmakuGroupLogBo.builder()
                .groupId(groupId)
                .groupStatusDesc(SelfDanmakuStatusEnum.AUDIT_PASS.getDesc())
                .groupAuditStatusDesc(SelfDanmakuAuditStatusEnum.AUDIT_PASS.getDesc())
                .build();

        logOperateService.addUpdateLog(DbTable.LAU_SELF_DANMAKU_GROUP, operator, originLogBo, updateLogBo, detail.getId().intValue());
    }

    @Transactional(value = AD_TM, rollbackFor = Exception.class)
    public void auditReject(SelfDanmakuGroupAuditBo auditBo) {
        Long groupId = auditBo.getGroupId();
        Operator operator = auditBo.getOperator();
        Long version = auditBo.getVersion();

        SelfDanmakuGroupBo detail = getDetail(operator.getOperatorId(), groupId);
        // 不能拒审已经删除或者不存在的弹幕组
        if (Objects.isNull(detail)
                || detail.getGroupStatus().equals(SelfDanmakuStatusEnum.DELETED.getCode())) {
            return;
        }

        Assert.isTrue(detail.getVersion().equals(version), "弹幕组" + groupId + "已变更,请刷新页面");
        SelfDanmakuGroupSaveBo saveBo = SelfDanmakuGroupSaveBo.builder()
                .accountId(detail.getAccountId())
                .groupId(groupId)
                .groupStatus(SelfDanmakuStatusEnum.AUDIT_REJECT.getCode())
                .groupAuditStatus(SelfDanmakuAuditStatusEnum.AUDIT_REJECT.getCode())
                .version(version + 1L)
                .build();
        List<SelfDanmakuBo> existDanmakuBoList = selfDanmakuService.queryByGroupId(groupId);
        SelfDanmakuGroupShadowBo shadowBo = selfDanmakuGroupShadowService.getShadowBoByDanmakuGroupId(groupId);
        List<SelfDanmakuSaveEntityBo> entityBoList;
        // 影子回写
        if (Objects.nonNull(shadowBo)) {
            entityBoList = shadowBo.getEntityBoList();
            entityBoList.forEach(entityBo -> {
                entityBo.setDanmakuStatus(SelfDanmakuStatusEnum.AUDIT_REJECT.getCode());
                entityBo.setDanmakuAuditStatus(SelfDanmakuAuditStatusEnum.AUDIT_REJECT.getCode());
            });
            saveBo.setEntityBoList(entityBoList);
            selfDanmakuService.saveSelfDanmakuList(saveBo, existDanmakuBoList, false);
            selfDanmakuGroupShadowService.deleteShadowByGroupIdList(Lists.newArrayList(groupId));
        } else {
            List<Long> danmakuIdList = existDanmakuBoList.stream()
                    .map(SelfDanmakuBo::getDanmakuId)
                    .collect(Collectors.toList());
            // 不存在影子 直接更新状态
            selfDanmakuService.auditRejectByGroupIdAndDanmakuIdList(groupId, danmakuIdList);
        }

        LauSelfDanmakuGroupPo updatePo = SelfDanmakuConverter.MAPPER.convertGroupSaveBo2Po(saveBo);
        updatePo.setReason(auditBo.getReason());
        adBqf.update(lauSelfDanmakuGroup)
                .where(lauSelfDanmakuGroup.groupId.eq(groupId))
                .where(lauSelfDanmakuGroup.isDeleted.eq(IsDeleted.VALID))
                .populate(updatePo)
                .execute();

        long danmakuNum = selfDanmakuService.countByGroupId(groupId);
        selfDanmakuGroupAuditService.auditReject(auditBo, danmakuNum);

        SelfDanmakuGroupLogBo originLogBo = SelfDanmakuGroupLogBo.builder()
                .groupId(groupId)
                .groupStatusDesc(SelfDanmakuStatusEnum.getByCode(detail.getGroupStatus()).getDesc())
                .groupAuditStatusDesc(SelfDanmakuAuditStatusEnum.getByCode(detail.getGroupAuditStatus()).getDesc())
                .build();

        SelfDanmakuGroupLogBo updateLogBo = SelfDanmakuGroupLogBo.builder()
                .groupId(groupId)
                .groupStatusDesc(SelfDanmakuStatusEnum.AUDIT_REJECT.getDesc())
                .groupAuditStatusDesc(SelfDanmakuAuditStatusEnum.AUDIT_REJECT.getDesc())
                .build();

        logOperateService.addUpdateLog(DbTable.LAU_SELF_DANMAKU_GROUP, operator, originLogBo, updateLogBo, detail.getId().intValue());
    }


    private void checkGroupNameDuplicate(String groupName, int accountId, Long groupId) {
        QuerySelfDanmakuGroupBo queryBo = QuerySelfDanmakuGroupBo.builder()
                .accountId(accountId)
                .groupName(groupName)
                .danmakuGroupStatusList(SelfDanmakuStatusEnum.NON_DELETED_SELF_DANMAKU_STATUS_LIST)
                .build();

        List<SelfDanmakuGroupInfoBo> existNameBoList = queryForList(queryBo);
        Assert.isTrue(CollectionUtils.isEmpty(existNameBoList)
                        || existNameBoList.stream()
                        .allMatch(existBo -> existBo.getGroupId().equals(groupId)),
                "当前账户已有同名弹幕组,请重新命名");
    }

    public void batchDelete(List<Long> groupIdList, Operator operator) {
        Assert.notEmpty(groupIdList, "删除弹幕组id不可为空");
        Assert.isTrue(groupIdList.size() <= 20, "单次最多删除20个弹幕组");
        Assert.notNull(operator, "操作人不可为空");

        QuerySelfDanmakuGroupBo queryBo = QuerySelfDanmakuGroupBo.builder()
                .accountId(operator.getOperatorId())
                .danmakuGroupIdList(groupIdList)
                .build();

        List<SelfDanmakuGroupInfoBo> deleteBoList = queryForList(queryBo);
        Assert.isTrue(deleteBoList.size() == groupIdList.size(), "您不能删除不存在或不属于当前账户的弹幕组");

        // 删除弹幕组
        adBqf.update(lauSelfDanmakuGroup)
                .where(lauSelfDanmakuGroup.groupId.in(groupIdList))
                .set(lauSelfDanmakuGroup.groupStatus, SelfDanmakuStatusEnum.DELETED.getCode())
                .set(lauSelfDanmakuGroup.groupIsUsing, 0)
                .execute();

        // 删除弹幕
        selfDanmakuService.deleteByGroupIdList(groupIdList);

        // 删除绑定关系
        adpCpcSelfDanmakuGroupMappingService.clearAllDanmakuGroupMappingFromDeleteGroupIds(groupIdList);

        // 删除影子
        selfDanmakuGroupShadowService.deleteShadowByGroupIdList(groupIdList);

        // 取消推审
        selfDanmakuGroupAuditService.cancelAudit(groupIdList);
    }

    public void updateGroupIdIsUsingInfo(Long groupId, Integer isUsing) {
        adBqf.update(lauSelfDanmakuGroup)
                .where(lauSelfDanmakuGroup.groupId.eq(groupId))
                .set(lauSelfDanmakuGroup.groupIsUsing, isUsing)
                .execute();
    }
}
