package com.bilibili.adp.cpc.splash_screen.creative.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum InteractType {
    TWIST(0, "扭一扭"),
    SLIDE(1, "全屏滑动"),
    CLICK(2,"点击"),
    ;
    private final int code;
    private final String desc;

    public static InteractType getByCode(int code) {
        return Arrays.stream(values())
                .filter(interactType -> interactType.getCode() == code)
                .findFirst()
                .orElseThrow(IllegalArgumentException::new);
    }
}
