package com.bilibili.adp.cpc.dao.querydsl.pos;

import javax.annotation.Generated;

/**
 * LauUnitArchiveVideoPo is a Querydsl bean type
 */
@Generated("com.querydsl.codegen.BeanSerializer")
public class LauUnitArchiveVideoPo {

    private Long cid;

    private java.sql.Timestamp ctime;

    private String headLink;

    private Integer id;

    private Integer isDeleted;

    private Integer launchVideoType;

    private Long mid;

    private java.sql.Timestamp mtime;

    private Long realMid;

    private String realNickname;

    private Integer subTid;

    private Integer unitId;

    private Integer videoCategory;

    private String videoFrom;

    private Long videoId;

    private Long videoPage;

    private Long seasonId;

    public Long getCid() {
        return cid;
    }

    public void setCid(Long cid) {
        this.cid = cid;
    }

    public java.sql.Timestamp getCtime() {
        return ctime;
    }

    public void setCtime(java.sql.Timestamp ctime) {
        this.ctime = ctime;
    }

    public String getHeadLink() {
        return headLink;
    }

    public void setHeadLink(String headLink) {
        this.headLink = headLink;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Integer getLaunchVideoType() {
        return launchVideoType;
    }

    public void setLaunchVideoType(Integer launchVideoType) {
        this.launchVideoType = launchVideoType;
    }

    public Long getMid() {
        return mid;
    }

    public void setMid(Long mid) {
        this.mid = mid;
    }

    public java.sql.Timestamp getMtime() {
        return mtime;
    }

    public void setMtime(java.sql.Timestamp mtime) {
        this.mtime = mtime;
    }

    public Long getRealMid() {
        return realMid;
    }

    public void setRealMid(Long realMid) {
        this.realMid = realMid;
    }

    public String getRealNickname() {
        return realNickname;
    }

    public void setRealNickname(String realNickname) {
        this.realNickname = realNickname;
    }

    public Integer getSubTid() {
        return subTid;
    }

    public void setSubTid(Integer subTid) {
        this.subTid = subTid;
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public Integer getVideoCategory() {
        return videoCategory;
    }

    public void setVideoCategory(Integer videoCategory) {
        this.videoCategory = videoCategory;
    }

    public String getVideoFrom() {
        return videoFrom;
    }

    public void setVideoFrom(String videoFrom) {
        this.videoFrom = videoFrom;
    }

    public Long getVideoId() {
        return videoId;
    }

    public void setVideoId(Long videoId) {
        this.videoId = videoId;
    }

    public Long getVideoPage() {
        return videoPage;
    }

    public void setVideoPage(Long videoPage) {
        this.videoPage = videoPage;
    }

    public Long getSeasonId() {
        return seasonId;
    }

    public void setSeasonId(Long seasonId) {
        this.seasonId = seasonId;
    }

    @Override
    public String toString() {
         return "cid = " + cid + ", ctime = " + ctime + ", headLink = " + headLink + ", id = " + id + ", isDeleted = " + isDeleted + ", launchVideoType = " + launchVideoType + ", mid = " + mid + ", mtime = " + mtime + ", realMid = " + realMid + ", realNickname = " + realNickname + ", subTid = " + subTid + ", unitId = " + unitId + ", videoCategory = " + videoCategory + ", videoFrom = " + videoFrom + ", videoId = " + videoId + ", videoPage = " + videoPage;
    }

}

