package com.bilibili.adp.cpc.biz.services;

import com.bilibili.adp.common.exception.ServiceRuntimeException;
import com.bilibili.bjcom.querydsl.BaseQueryFactory;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;

import static com.bilibili.adp.cpc.dao.config.PlatformMySqlConfig.AD_BQF;
import static com.bilibili.adp.cpc.dao.querydsl.QLauUserBehavior.lauUserBehavior;

@Slf4j
@Service
public class AdpCpcLauUserBehaviorService {
    @Resource(name = AD_BQF)
    private BaseQueryFactory adBqf;
    @Resource
    private ObjectMapper om;

    /**
     * 获取账户的某个类型的自定义列字段
     *
     * @param aid accountId
     * @param bid 自定义类型，如计划/单元/创意。。。
     * @param system 系统 0-必选 1-新中台
     * @return
     */
    public List<Integer> getSubscribedColumns(int aid, int bid,int system) {
        try {
            final String behavior = adBqf.select(lauUserBehavior.behavior)
                    .from(lauUserBehavior)
                    .where(lauUserBehavior.accountId.eq(aid))
                    .where(lauUserBehavior.behaviorId.eq(bid))
                    .where(lauUserBehavior.system.eq(system))
                    .fetchFirst();
            if (behavior == null) return null;
            return om.readValue(behavior, new TypeReference<List<Integer>>(){});
        } catch (IOException e) {
            throw new ServiceRuntimeException(e.getMessage());
        }
    }

    public void saveUserReportBehavior(int aid, int bid, List<Integer> subscribeList,int system) {
        try {
            final long count = adBqf.select(lauUserBehavior.count())
                    .from(lauUserBehavior)
                    .where(lauUserBehavior.accountId.eq(aid))
                    .where(lauUserBehavior.behaviorId.eq(bid))
                    .where(lauUserBehavior.system.eq(system))
                    .fetchFirst();
            final String behavior = om.writeValueAsString(subscribeList);
            if (count == 0) {
                adBqf.insert(lauUserBehavior)
                        .set(lauUserBehavior.accountId, aid)
                        .set(lauUserBehavior.behaviorId, bid)
                        .set(lauUserBehavior.behavior, behavior)
                        .set(lauUserBehavior.system, system)
                        .execute();
            } else {
                adBqf.update(lauUserBehavior)
                        .where(lauUserBehavior.accountId.eq(aid))
                        .where(lauUserBehavior.behaviorId.eq(bid))
                        .where(lauUserBehavior.system.eq(system))
                        .set(lauUserBehavior.behavior, behavior)
                        .execute();
            }
            autoCleanUp();
        } catch (Exception e) {
            log.error("LauUserBehavior write error: aid = {}, list = {}", aid, subscribeList.toString());
        }
    }


    /**
     * 获取账户的某个类型的通用配置
     *
     * @param aid
     * @param bid
     * @param system
     * @return
     */
    public String getGeneralConfig(int aid, int bid, int system) {
        return adBqf.select(lauUserBehavior.generalConfig)
                .from(lauUserBehavior)
                .where(lauUserBehavior.accountId.eq(aid))
                .where(lauUserBehavior.behaviorId.eq(bid))
                .where(lauUserBehavior.system.eq(system))
                .fetchFirst();
    }

    public void saveUserGeneralConfig(int aid, int bid, int system, String generalConfig) {
        try {
            final long count = adBqf.select(lauUserBehavior.count())
                    .from(lauUserBehavior)
                    .where(lauUserBehavior.accountId.eq(aid))
                    .where(lauUserBehavior.behaviorId.eq(bid))
                    .where(lauUserBehavior.system.eq(system))
                    .fetchFirst();
            if (count == 0) {
                adBqf.insert(lauUserBehavior)
                        .set(lauUserBehavior.accountId, aid)
                        .set(lauUserBehavior.behaviorId, bid)
                        .set(lauUserBehavior.system, system)
                        .set(lauUserBehavior.generalConfig, generalConfig)
                        .execute();
            } else {
                adBqf.update(lauUserBehavior)
                        .where(lauUserBehavior.accountId.eq(aid))
                        .where(lauUserBehavior.behaviorId.eq(bid))
                        .where(lauUserBehavior.system.eq(system))
                        .set(lauUserBehavior.generalConfig, generalConfig)
                        .execute();
            }
            autoCleanUp();
        } catch (Exception e) {
            log.error("LauUserBehavior write error: aid = {}, generalConfig = {}", aid, generalConfig);
        }
    }

    private void autoCleanUp() {
        adBqf.delete(lauUserBehavior)
                .where(lauUserBehavior.mtime.before(Timestamp.valueOf(LocalDateTime.now().minusDays(180))))
                .execute();
    }
}
