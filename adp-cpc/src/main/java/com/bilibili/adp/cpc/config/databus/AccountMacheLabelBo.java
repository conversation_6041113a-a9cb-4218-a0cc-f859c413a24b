package com.bilibili.adp.cpc.config.databus;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AccountMacheLabelBo implements Serializable {
    private static final long serialVersionUID = 1L;

    private Integer accountId;

    private Boolean machineFlag;

}