package com.bilibili.adp.cpc.wrapper.bos;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ChannelAndSceneBo {
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class ChannelBo {
        private Integer channelId;
        private String channelName;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class SceneBo {
        private Integer sceneId;
        private String sceneName;
    }

    @ApiModelProperty("流量类型")
    private ChannelBo channel;
    @ApiModelProperty("场景")
    private List<SceneBo> scenes;
    @ApiModelProperty("流量类型")
    private Integer isValid;
}
