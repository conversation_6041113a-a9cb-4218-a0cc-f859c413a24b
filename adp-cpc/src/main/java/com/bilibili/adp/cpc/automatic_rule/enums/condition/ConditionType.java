package com.bilibili.adp.cpc.automatic_rule.enums.condition;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum ConditionType {
    GREATER_OR_EQUAL(1, "大于等于"),
    LESS_OR_EQUAL(2, "小于等于"),
    ;

    private final int code;
    private final String desc;

    public static ConditionType getByCode(int code) {
        return Arrays.stream(values())
                .filter(conditionType -> conditionType.getCode() == code)
                .findFirst()
                .orElseThrow(IllegalArgumentException::new);
    }
}
