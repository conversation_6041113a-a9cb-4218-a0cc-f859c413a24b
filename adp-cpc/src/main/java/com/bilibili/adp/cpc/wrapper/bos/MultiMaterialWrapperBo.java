package com.bilibili.adp.cpc.wrapper.bos;

import com.bilibili.adp.cpc.core.bos.MultiMaterialBo;
import com.bilibili.adp.cpc.core.bos.SmartTitleBo;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class MultiMaterialWrapperBo {
    private List<SmartTitleBo> titles;
    private List<ImageGroupWrapperBo> imageGroups;
    private List<ArchiveGroupWrapperBo> archiveGroups;

    public MultiMaterialBo toCoreBo() {
        return MultiMaterialBo.builder()
                .titles(titles)
                .imageGroups(Optional.ofNullable(imageGroups)
                        .orElse(Collections.emptyList())
                        .stream()
                        .map(ImageGroupWrapperBo::toCoreBo)
                        .collect(Collectors.toList()))
                .archiveGroups(Optional.ofNullable(archiveGroups)
                        .orElse(Collections.emptyList())
                        .stream()
                        .map(ArchiveGroupWrapperBo::toCoreBo)
                        .collect(Collectors.toList()))
                .build();
    }

    public static MultiMaterialWrapperBo fromCoreBo(MultiMaterialBo bo) {
        return MultiMaterialWrapperBo.builder()
                .titles(bo.getTitles())
                .imageGroups(Optional.ofNullable(bo.getImageGroups())
                        .orElse(Collections.emptyList())
                        .stream()
                        .map(ImageGroupWrapperBo::fromCoreBo)
                        .collect(Collectors.toList()))
                .archiveGroups(Optional.ofNullable(bo.getArchiveGroups())
                        .orElse(Collections.emptyList())
                        .stream()
                        .map(ArchiveGroupWrapperBo::fromCoreBo)
                        .collect(Collectors.toList()))
                .build();
    }
}
