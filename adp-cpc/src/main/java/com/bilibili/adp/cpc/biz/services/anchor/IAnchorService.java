package com.bilibili.adp.cpc.biz.services.anchor;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.cpc.biz.bos.anchor.*;
import com.bilibili.adp.cpc.biz.services.archive.bos.BatchCreateAdThreeElementsBo;
import com.bilibili.adp.web.framework.core.Pagination;

import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 */
public interface IAnchorService {

    /**
     * 锚点详情
     *
     * @param id
     * @return
     */
    AnchorInfoBo fetchAnchorDetail(Long id);

    /**
     * 锚点详情
     *
     * @param id
     * @return
     */
    AnchorInfoBo fetchAnchorDetail(Long id);

    /**
     * 锚点分页列表
     *
     * @param build
     * @return
     */
    Pagination<List<AnchorInfoBo>> queryAnchorPageList(AnchorListQueryBo build);

    /**
     * 批量新建锚点
     *
     * @param anchorSaveBo
     * @param operator
     * @return
     */
    List<AnchorCreateResultBo> createAnchors(AnchorSaveBo anchorSaveBo, Operator operator);

    Long createAnchorsBatch(AnchorSaveBo anchorSaveBo, Operator operator);


    BatchCreateAdThreeElementsBo batchCreateAdThreeElements(Integer accountId, List<Long> aids, List<Integer> scenes);

    /**
     * 修改锚点
     *
     * @param anchorSaveBo
     * @param operator
     * @return
     */

    Integer updateAnchor(AnchorSaveBo anchorSaveBo, Operator operator);

    Long updateAnchorBatch(AnchorSaveBo anchorSaveBo, Operator operator);

    Integer anchorGroup(AnchorSaveBo anchorSaveBo, Operator operator);

    /**
     * 删除锚点
     *
     * @param id
     * @param operator
     * @return
     */
    Integer deleteAnchor(Long id, Operator operator);

    Integer deleteAnchorBatchForBusinessTool(AnchorSaveBo saveBo, Operator operator);

    Long deleteAnchorBatch(AnchorSaveBo saveBo, Operator operator);

    /**
     * 根据稿件优先级获取锚点
     * 花火 > 带货 > 三连
     *
     * @param avid
     * @return
     */

    AnchorPreviewBo queryAnchorByPriority(Long avid);

    /**
     * 获取锚点关联创意数
     *
     * @param anchorId
     * @return
     */
    List<Integer> queryAnchorRelatedCreativeIds(Long anchorId);

    /**
     * 根据优先级查询稿件的业务锚点预览信息
     *
     * @param avids
     * @return
     */
    List<AnchorPreviewBo> queryBizAnchorsByPriority(List<Long> avids);

    /**
     * 根据优先级查询稿件的业务锚点预览信息
     *
     * @param avids
     * @return
     */
    Map<Long, AnchorPreviewBo> queryBizAnchorsMapByPriority(List<Long> avids);

    List<AnchorBizSimpleBo> querySimpleBizAnchorByPriority(List<Long> avids);

    Map<Long, AnchorBizSimpleBo> querySimpleBizAnchorMapByPriority(List<Long> avids);
}
