package com.bilibili.adp.cpc.config;

import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import pleiades.venus.starter.paladin.PaladinPropertySourceFactory;

@Configuration
@PropertySource(value = "classpath:boggart.yaml", factory = PaladinPropertySourceFactory.class)
@ComponentScan(basePackages = "sycpb.platform.cpm.boggart")
public class BoggartConfig {
}
