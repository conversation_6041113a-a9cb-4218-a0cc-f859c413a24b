package com.bilibili.adp.cpc.databus;

import com.alibaba.fastjson.JSONObject;
import com.bilibili.adp.cpc.biz.services.creative.bos.CreativeEsBo;
import com.bilibili.adp.cpc.config.CreativeEsConfig;
import com.bilibili.warp.databus.DatabusProperty;
import com.bilibili.warp.databus.DatabusTemplate;
import com.bilibili.warp.databus.Message;
import com.bilibili.warp.databus.PubResult;
import com.bilibili.warp.spring.boot.autoconfigure.databus.DatabusProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 生成创意 es 发布器
 **/
@Slf4j
@Service
public class GenCreativeEsPub {

    @Autowired
    private GenCreativeEs1Pub es1Pub;
    @Autowired
    private GenCreativeEs2Pub es2Pub;
    @Autowired
    private CreativeEsConfig creativeEsConfig;

    public void pub(JSONObject bo, Integer creativeId) {
        if (Objects.equals(creativeEsConfig.getCreativeEsWriteDataType(), CreativeEsConfig.CREATIVE_ES_WRITE_DATA_TYPE_ES1)) {
            // topic1
            es1Pub.pub(bo, creativeId);
        } else if (Objects.equals(creativeEsConfig.getCreativeEsWriteDataType(), CreativeEsConfig.CREATIVE_ES_WRITE_DATA_TYPE_ES2)) {
            // topic2
            es2Pub.pub(bo, creativeId);
        } else if (Objects.equals(creativeEsConfig.getCreativeEsWriteDataType(), CreativeEsConfig.CREATIVE_ES_WRITE_DATA_TYPE_ES_BOTH)) {
            // topic1、topic2
            es1Pub.pub(bo, creativeId);
            es2Pub.pub(bo, creativeId);
        }
    }

    public void batchPub(List<CreativeEsBo> esBos) {
        if (Objects.equals(creativeEsConfig.getCreativeEsWriteDataType(), CreativeEsConfig.CREATIVE_ES_WRITE_DATA_TYPE_ES1)) {
            // topic1
            es1Pub.batchPub(esBos);
        } else if (Objects.equals(creativeEsConfig.getCreativeEsWriteDataType(), CreativeEsConfig.CREATIVE_ES_WRITE_DATA_TYPE_ES2)) {
            // topic2
            es2Pub.batchPub(esBos);
        } else if (Objects.equals(creativeEsConfig.getCreativeEsWriteDataType(), CreativeEsConfig.CREATIVE_ES_WRITE_DATA_TYPE_ES_BOTH)) {
            // topic1、topic2
            es1Pub.batchPub(esBos);
            es2Pub.batchPub(esBos);
        }
    }

}
