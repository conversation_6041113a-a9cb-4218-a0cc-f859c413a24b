package com.bilibili.adp.cpc.config.databus;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SimpleLauUnitCreativeBo implements Serializable {
    /**
     * 创意ID
     */
    private Integer creativeId;

    /**
     * 账户id
     */
    private Integer accountId;

    /**
     * 审核状态（1-待审核，2-审核通过，3-审核不通过）
     */
    private Integer auditStatus;

    /**
     * 添加时间
     */
    private Timestamp ctime;

    /**
     * 创意状态:1-有效,2-已暂停,3-已结束,4-已删除,5-审核中,6-审核拒绝, 7-修改待下线, 8-已下线,9-落地页待审核创意待送审
     */
    private Integer creativeStatus;

    /**
     * 创意来源 0-未知，1-个人起飞 2-现金版个人起飞 3-签约金订单 4-签约金托管个人起飞  5-通用托管个人起飞
     */
    private Integer bilibiliUserId;

    /**
     * 母创意id
     */
    private Integer parentCreativeId;

    private static final long serialVersionUID = 1L;
}