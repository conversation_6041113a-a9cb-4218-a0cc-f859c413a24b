package com.bilibili.adp.cpc.automatic_rule.enums.action;

import com.bilibili.adp.cpc.automatic_rule.enums.object.ObjectType;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum Subject {
    ACCOUNT_BUDGET(1, "账户预算", ObjectType.ACCOUNT),
    UNIT_BUDGET(2, "单元预算", ObjectType.UNIT),
    UNIT_OPCX_BID_PRICE(3, "单元ocpx出价", ObjectType.UNIT),
    PAUSE_UNIT(4, "暂停单元", ObjectType.UNIT),
    ;

    private final int code;
    private final String desc;
    private final ObjectType objectType;

    public static Subject getByCode(int code) {
        return Arrays.stream(values())
                .filter(subject -> subject.getCode() == code)
                .findFirst()
                .orElseThrow(IllegalArgumentException::new);
    }
}
