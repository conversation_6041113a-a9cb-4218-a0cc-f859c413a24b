package com.bilibili.adp.cpc.splash_screen.creative.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum NavigationType {
    NAVIGATE_TO_ACTIVITY_PAGE(1, "跳转活动页"),
    LAUNCH_THIRD_PARTY_APP(2, "唤起第三方应用"),
    LIVE(3,"跳转直播间")
    ;
    private final int code;
    private final String desc;

    public static NavigationType getByCode(int code) {
        return Arrays.stream(values())
                .filter(navigationType -> navigationType.code == code)
                .findFirst()
                .orElseThrow(IllegalArgumentException::new);
    }
}
