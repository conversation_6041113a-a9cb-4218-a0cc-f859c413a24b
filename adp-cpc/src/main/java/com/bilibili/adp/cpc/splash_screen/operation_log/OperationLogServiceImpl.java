package com.bilibili.adp.cpc.splash_screen.operation_log;

import com.bilibili.adp.cpc.compare.ChangeBo;
import com.bilibili.adp.cpc.compare.CompareMeta;
import com.bilibili.adp.cpc.compare.CompareService;
import com.bilibili.adp.cpc.splash_screen.operation_log.converter.OperationLogConverter;
import com.bilibili.adp.cpc.splash_screen.operation_log.enums.OperationType;
import com.bilibili.adp.log.service.v6.ILogCpcOperationService;
import com.bilibili.adp.log.service.v6.bo.LogCpcOperationBo;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.javers.core.diff.changetype.PropertyChange;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@RequiredArgsConstructor
public class OperationLogServiceImpl implements IOperationLogService {
    private final CompareService compareService;
    private final ObjectMapper objectMapper;
    private final ILogCpcOperationService logCpcOperationService;

    @SneakyThrows
    @Override
    public <T> List<ChangeBo> changes(T oldVersion, T newVersion, Map<String, CompareMeta> metaMap, Integer metaType, boolean ignoreIfUnknown) {
        final List<PropertyChange> propertyChanges = compareService.propertyChanges(oldVersion, newVersion);
        return propertyChanges
                .stream()
                .flatMap(propertyChange -> {
                    Optional<CompareMeta> compareMetaOptional = Optional.ofNullable(metaMap).map(meta -> meta.get(propertyChange.getPropertyName()));
                    if (ignoreIfUnknown && !compareMetaOptional.isPresent()) {
                        return Stream.empty();
                    }
                    if (compareMetaOptional.isPresent() && !Objects.equals(metaType, compareMetaOptional.get().getMetaType())) {
                        return Stream.empty();
                    }
                    return Stream.of(ChangeBo.builder()
                            .key(propertyChange.getPropertyName())
                            .desc(compareMetaOptional.map(CompareMeta::getDesc).orElse("未知属性"))
                            .reAudit(compareMetaOptional.map(CompareMeta::isReAudit).orElse(false))
                            .oldValue(propertyChange.getLeft())
                            .newValue(propertyChange.getRight())
                            .build());
                }).collect(Collectors.toList());
    }

    @SneakyThrows
    public void save(Collection<OperationContextBo> contextBos) {
        Assert.notEmpty(contextBos, "操作上下文不能为空");
        final List<LogCpcOperationBo> bos = new ArrayList<>();
        for (OperationContextBo ctx : contextBos) {
            final String changesStr;
            if (OperationType.isDelete(ctx.getOperationType())) {
                changesStr = "[]";
            } else if (CollectionUtils.isEmpty(ctx.getChanges())) {
                continue;
            } else {
                changesStr = objectMapper.writeValueAsString(ctx.getChanges());
            }
            final LogCpcOperationBo po = OperationLogConverter.MAPPER.toBo(ctx, changesStr);
            bos.add(po);
        }
        logCpcOperationService.batchInsert(bos);
    }
}
