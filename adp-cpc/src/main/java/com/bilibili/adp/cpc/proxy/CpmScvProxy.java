package com.bilibili.adp.cpc.proxy;

import com.bapis.ad.archive.*;
import com.bapis.ad.component.*;
import com.bapis.ad.ott.AdOttServiceGrpc;
import com.bapis.ad.ott.VideoOttAuditReq;
import com.bapis.ad.scv.anchor.*;
import com.bapis.ad.scv.component_group.ComponentGroupServiceGrpc;
import com.bapis.ad.scv.component_group.QueryComponentGroupPageListReq;
import com.bapis.ad.scv.component_group.QueryComponentGroupRep;
import com.bapis.ad.scv.component_group.SaveComponentGroupReq;
import com.bapis.ad.unit.BatchDeleteReq;
import com.bilibili.adp.cpc.biz.services.creative.StoryComponentService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import pleiades.venus.starter.rpc.client.RPCClient;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @ClassName AdAccountGrpcService
 * <AUTHOR>
 * @Date 2023/8/21 3:34 下午
 * @Version 1.0
 **/
@Service
@Slf4j
public class CpmScvProxy {


    private static final Long Deadline2S = 2000L;

    private static final Long Deadline3S = 3000L;

    @RPCClient("sycpb.cpm.scv")
    private NativeAnchorServiceGrpc.NativeAnchorServiceBlockingStub nativeAnchorServiceBlockingStub;

    @RPCClient("sycpb.cpm.scv")
    private CmArchiveServiceGrpc.CmArchiveServiceBlockingStub cmArchiveServiceBlockingStub;

    @RPCClient("sycpb.cpm.scv")
    private ComponentGroupServiceGrpc.ComponentGroupServiceBlockingStub componentGroupServiceBlockingStub;

    @RPCClient("sycpb.cpm.scv")
    private StoryComponentServiceGrpc.StoryComponentServiceBlockingStub storyComponentServiceBlockingStub;

    @Resource
    private  AdOttServiceGrpc.AdOttServiceBlockingStub adOttServiceBlockingStub;

    @Resource
    private CommentComponentServiceGrpc.CommentComponentServiceBlockingStub commentComponentServiceBlockingStub;

    public List<SingleQueryAnchorRep> queryAnchorPageList(QueryAnchorPageListReq req) {
        QueryAnchorRep queryAnchorRep = nativeAnchorServiceBlockingStub
                .withDeadlineAfter(Deadline3S, TimeUnit.MILLISECONDS)
                .queryAnchorPageList(req);
        return queryAnchorRep.getDataList();
    }

    public QueryAnchorRep queryAnchorPageListReq(QueryAnchorPageListReq req) {
        return nativeAnchorServiceBlockingStub
                .withDeadlineAfter(Deadline3S, TimeUnit.MILLISECONDS)
                .queryAnchorPageList(req);
    }

    public List<SingleQueryAnchorRep> queryAnchorList(List<Long> avids) {
        if (CollectionUtils.isEmpty(avids)) {
            return Collections.EMPTY_LIST;
        }
        QueryAnchorReq queryAnchorReq = QueryAnchorReq.newBuilder().addAllAid(avids).build();
        QueryAnchorRep queryAnchorRep = nativeAnchorServiceBlockingStub
                .withDeadlineAfter(Deadline3S, TimeUnit.MILLISECONDS)
                .queryAnchorList(queryAnchorReq);
        return queryAnchorRep.getDataList();
    }


    public CreatesReply createAnchors(CreateAnchorsReq createAnchorsReq) {
        return nativeAnchorServiceBlockingStub
                .withDeadlineAfter(Deadline2S, TimeUnit.MILLISECONDS)
                .createAnchors(createAnchorsReq);
    }

    public UpdateAvidByAnchorIdReply updateAvidByAnchorId(UpdateAvidByAnchorIdReq request) {
        return   nativeAnchorServiceBlockingStub
                .withDeadlineAfter(Deadline2S, TimeUnit.MILLISECONDS)
                .updateAvidByAnchorId(request);
    }


    public UpdateCountReply updateAnchor(UpdateAnchorReq request) {
      return   nativeAnchorServiceBlockingStub
              .withDeadlineAfter(Deadline2S, TimeUnit.MILLISECONDS)
              .updateAnchor(request);
    }

    public UpdateCountReply deleteAnchor(Long id) {
        DeleteAnchorReq deleteAnchorReq = DeleteAnchorReq.newBuilder().setId(id).build();
        return nativeAnchorServiceBlockingStub
                .withDeadlineAfter(Deadline2S, TimeUnit.MILLISECONDS)
                .deleteAnchor(deleteAnchorReq);
    }

    public UpdateCountReply batchDeleteAnchor(List<Long> idList){
        BatchDeleteAnchorReq batchDeleteAnchorReq = BatchDeleteAnchorReq.newBuilder().addAllId(idList).build();
        return nativeAnchorServiceBlockingStub
                .withDeadlineAfter(Deadline2S, TimeUnit.MILLISECONDS)
                .batchDeleteAnchor(batchDeleteAnchorReq);
    }

    public List<ArchiveInfo> getCmArchiveInfos(List<String> md5s, List<Long> avids){
        Conditions.Builder builder = Conditions.newBuilder();
        if (!org.springframework.util.CollectionUtils.isEmpty(md5s)){
            builder.addAllMd5S(md5s);
        }
        if (!org.springframework.util.CollectionUtils.isEmpty(avids)){
            builder.addAllAvids(avids);
        }
        return cmArchiveServiceBlockingStub
                .withDeadlineAfter(Deadline2S, TimeUnit.MILLISECONDS)
                .queryCmArchiveByCondition(builder.build()).getArchiveInfoList();

    }



    public List<ArchiveInfo> queryCmArchiveByCondition(String md5) {
        CmArchiveInfoResp cmArchiveInfoResp = cmArchiveServiceBlockingStub.queryCmArchiveByCondition(Conditions.newBuilder().addMd5S(md5).build());
        return cmArchiveInfoResp.getArchiveInfoList();
    }


    public List<ArchiveInfo> queryCmArchiveByConditions(List<String> md5List) {
        CmArchiveInfoResp cmArchiveInfoResp = cmArchiveServiceBlockingStub.queryCmArchiveByCondition(Conditions.newBuilder().addAllMd5S(md5List).build());
        return cmArchiveInfoResp.getArchiveInfoList();
    }


    public List<ArchiveInfo> queryCmArchiveByConditionByAvid(List<Long> avids) {
        CmArchiveInfoResp cmArchiveInfoResp = cmArchiveServiceBlockingStub.queryCmArchiveByCondition(Conditions.newBuilder().addAllAvids(avids).build());
        return cmArchiveInfoResp.getArchiveInfoList();
    }

    public ListCmArchiveResp listCmArchive(ListCmArchiveReq req) {
        return cmArchiveServiceBlockingStub.listCmArchive(req);
    }

    public QueryComponentGroupRep queryComponentGroupTotalList(QueryComponentGroupPageListReq req) {
       return componentGroupServiceBlockingStub.queryComponentGroupTotalList(req);
    }

    public void deleteComponentGroup(SaveComponentGroupReq req) {
        componentGroupServiceBlockingStub.deleteComponentGroup(req);
    }


    public void createComponentGroup(SaveComponentGroupReq req) {
        componentGroupServiceBlockingStub.createComponentGroup(req);
    }

    public void updateComponentGroup(SaveComponentGroupReq req) {
         componentGroupServiceBlockingStub.updateComponentGroup(req);
    }


    public QueryArchiveExistsReply queryArchiveExists(List<Long> avids) {
        QueryArchiveExistsReq.Builder reqBuilder = QueryArchiveExistsReq.newBuilder()
                .addAllAvids(avids);
        return cmArchiveServiceBlockingStub.queryArchiveExists(reqBuilder.build());
    }

    public Boolean archiveExists(Long avid) {
        QueryArchiveExistsReq req = QueryArchiveExistsReq.newBuilder().addAvids(avid).build();
        QueryArchiveExistsReply archiveExistsReply = cmArchiveServiceBlockingStub.withDeadlineAfter(5, TimeUnit.SECONDS)
                .queryArchiveExists(req);
        return archiveExistsReply.getListList().stream().map(SingleQueryArchiveExistsReply::getExist).collect(Collectors.toList()).contains(true);
    }

    public Map<Integer, List<Long>> batchQueryVideoMaterialAvids(List<Integer> accountIds, Timestamp startTime, Timestamp endTime) {
        BatchQueryArchiveCountReq queryArchiveCountReq = BatchQueryArchiveCountReq.newBuilder()
                .setBeginTime(startTime.getTime())
                .setEndTime(endTime.getTime())
                .addAllAccountIds(accountIds)
                .build();
        BatchQueryArchiveAvidsRep queryArchiveAvidsRep = cmArchiveServiceBlockingStub.withDeadlineAfter(5, TimeUnit.SECONDS)
                .batchQueryArchiveAvids(queryArchiveCountReq);
        return queryArchiveAvidsRep.getArchiveAvidsSingeleInfosList().stream().collect(Collectors.toMap(QueryArchiveAvidsSingleInfo::getAccountId, QueryArchiveAvidsSingleInfo::getAvidsList));
    }


    public List<Long> queryVideoMaterialAvids(Integer accountId, Timestamp startTime, Timestamp endTime) {

        QueryArchiveCountReq queryArchiveCountReq = QueryArchiveCountReq.newBuilder().setAccountId(accountId).setBeginTime(startTime.getTime()).setEndTime(endTime.getTime()).build();
        QueryArchiveAvidsRep queryArchiveAvidsRep = cmArchiveServiceBlockingStub.withDeadlineAfter(5, TimeUnit.SECONDS)
                .queryArchiveAvids(queryArchiveCountReq);
        return queryArchiveAvidsRep.getAvidsList();
    }


    public Long batchQueryVideoMaterialCount(List<Integer> accountIds, Timestamp startTime, Timestamp endTime) {
        BatchQueryArchiveCountReq queryArchiveCountReq = BatchQueryArchiveCountReq.newBuilder()
                .setBeginTime(startTime.getTime())
                .setEndTime(endTime.getTime())
                .addAllAccountIds(accountIds)
                .build();
        QueryArchiveCountReply queryArchiveCountReply = cmArchiveServiceBlockingStub.withDeadlineAfter(5, TimeUnit.SECONDS)
                .batchQueryArchiveCount(queryArchiveCountReq);
        return (long) queryArchiveCountReply.getCount();
    }

    public Long queryVideoMaterialCount(Integer accountId, Timestamp startTime, Timestamp endTime) {
        QueryArchiveCountReq queryArchiveCountReq = QueryArchiveCountReq.newBuilder().setAccountId(accountId).setBeginTime(startTime.getTime()).setEndTime(endTime.getTime()).build();
        QueryArchiveCountReply queryArchiveCountReply = cmArchiveServiceBlockingStub.withDeadlineAfter(5, TimeUnit.SECONDS)
                .queryArchiveCount(queryArchiveCountReq);
        return (long) queryArchiveCountReply.getCount();
    }


    public StoryComponents getImageStoryComponents(long itemId) {
        StoryComponentIdAndType imageStoryComponentReq = StoryComponentIdAndType.newBuilder().setType(StoryComponentService.STORY_IMAGE).setItemId(itemId).build();
        return storyComponentServiceBlockingStub.listByItemId(imageStoryComponentReq);
    }

    public StoryComponents getPriceDifferenceStoryComponents(long itemId) {
        StoryComponentIdAndType priceDifferenceStoryComponentReq = StoryComponentIdAndType.newBuilder().setType(StoryComponentService.STORY_PRICE_DIFFERENCE).setItemId(itemId).build();
        return storyComponentServiceBlockingStub.listByItemId(priceDifferenceStoryComponentReq);
    }


    public SingleQueryAnchorRep fetchAnchorInfo(Long id) {
        FetchAnchorInfoReq fetchAnchorInfoReq = FetchAnchorInfoReq.newBuilder().setId(id).build();
        return nativeAnchorServiceBlockingStub.fetchAnchorInfo(fetchAnchorInfoReq);
    }

    public FetchAnchorInfoBatchRep fetchAnchorInfoBatch(List<Long>ids){
        FetchAnchorInfoBatchReq fetchAnchorInfoBatchReq = FetchAnchorInfoBatchReq.newBuilder().addAllId(ids).build();
        return nativeAnchorServiceBlockingStub.fetchAnchorInfoBatch(fetchAnchorInfoBatchReq);
    }


    public UpdateCountReply anchorGroup(AnchorGroupReq anchorGroupReq) {
        return nativeAnchorServiceBlockingStub.group(anchorGroupReq);
    }



    public StoryComponentIdAndType saveStoryComponent(StoryComponent storyComponent) {
        log.info("StoryComponentService save: {}", storyComponent);
        return storyComponentServiceBlockingStub.save(storyComponent);
    }

    public StoryComponent getStoryComponent(StoryComponentIdAndType storyComponent) {
        return storyComponentServiceBlockingStub.get(storyComponent);
    }


    public StoryComponents listStoryComponent(StoryComponentListParams storyComponent) {
        return storyComponentServiceBlockingStub.list(storyComponent);
    }

    public void deleteStoryComponent(StoryComponentIdAndType storyComponent) {
        storyComponentServiceBlockingStub.delete(storyComponent);
    }


    public ComponentReply getCommentConversionComponent(Long aid) {
        ComponentReply reply = ComponentReply.getDefaultInstance();
        try {
            reply = commentComponentServiceBlockingStub.component(ComponentReq
                    .newBuilder()
                    .setAid(aid)
                    .build());
        } catch (Exception e) {
            log.error("get archive comment conversion component error", e);
        }
        return reply;
    }

    public ComponentReply getComponent(ComponentReq req) {
        return commentComponentServiceBlockingStub.component(req);
    }


    public DeleteReply deleteV2Component(DeleteReq req) {
        return commentComponentServiceBlockingStub.deleteV2(req);
    }


    public ComponentsReply getCommentConversionComponents(List<Long> aids) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(aids)) {
            return null;
        }

        ComponentsReply reply = ComponentsReply.getDefaultInstance();
        try {
            reply = commentComponentServiceBlockingStub.components(ComponentsReq
                    .newBuilder()
                    .addAllAid(aids)
                    .build());
        } catch (Exception e) {
            log.error("get archive comment conversion component error", e);
        }
        return reply;
    }


    public ComponentsReply getComponents(ComponentsReq req) {
        return commentComponentServiceBlockingStub.withWaitForReady()
                .withDeadlineAfter(4000, TimeUnit.MILLISECONDS).components(req);
    }


    public void batchCreateCommentComponentsAsync(BatchCreateCommentComponentReq req) {
        commentComponentServiceBlockingStub.batchCreateCommentComponentsAsync(req);
    }

    public void batchUpdateCommentComponentsAsync(BatchUpdateCommentComponentReq req) {
        commentComponentServiceBlockingStub.batchUpdateCommentComponentsAsync(req);
    }


    public void createV2tComponent(CreateReq req) {
        commentComponentServiceBlockingStub.createV2(req);
    }


    public void groupComponent(CommentGroupReq req) {
        commentComponentServiceBlockingStub.group(req);
    }


    public void updateV2Component(UpdateReq req) {
        commentComponentServiceBlockingStub.updateV2(req);
    }

    public void deleteCommentIfNecessary(GoodsMngReq goodsMngReq) {
        commentComponentServiceBlockingStub.withDeadlineAfter(5, TimeUnit.SECONDS)
                .deleteCommentIfNecessary(goodsMngReq);
    }


    public List<CommentComponent> queryRelatedComponents(RelatedTargetReq relatedTargetReq) {
        ComponentsReply componentsReply = commentComponentServiceBlockingStub.withDeadlineAfter(5, TimeUnit.SECONDS)
                .queryRelatedComponents(relatedTargetReq);
        return componentsReply.getComponentsList();
    }


    public SingleCreateReply createForBusinessTool(CreateReq createReq) {
        return commentComponentServiceBlockingStub.createForBusinessTool(createReq);
    }


    public void videoOttAudit(VideoOttAuditReq request) {
        adOttServiceBlockingStub.videoOttAudit(request);
    }

}

