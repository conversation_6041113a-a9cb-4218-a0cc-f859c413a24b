package com.bilibili.adp.cpc.splash_screen.creative.service;

import com.bilibili.adp.cpc.core.constants.IsDeleted;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauSplashScreenCreativeImagePo;
import com.bilibili.adp.cpc.splash_screen.creative.bos.SplashScreenImage;
import com.bilibili.adp.cpc.splash_screen.creative.converter.ImageConverter;
import com.bilibili.adp.cpc.utils.CommonFuncs;
import com.bilibili.adp.cpc.utils.RecDiffResult;
import com.bilibili.bjcom.querydsl.BaseQueryFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.bilibili.adp.cpc.dao.ad_core.querydsl.QLauSplashScreenCreativeImage.lauSplashScreenCreativeImage;
import static com.bilibili.adp.cpc.dao.config.PlatformMySqlConfig.AD_CORE_BQF;

@Slf4j
@Service
public class SplashScreenCreativeImageService {

    private static final int WIDTH_1600 = 1600;
    private static final int WIDTH_1280 = 1280;

    @Resource(name = AD_CORE_BQF)
    private BaseQueryFactory adCoreBqf;

    public static List<LauSplashScreenCreativeImagePo> generatePos(Integer creativeId, List<SplashScreenImage> images) {
        return images
                .stream()
                .map(image -> ImageConverter.MAPPER.bo2Po(creativeId, image))
                .collect(Collectors.toList());
    }

    public void save(Integer creativeId, List<LauSplashScreenCreativeImagePo> images) {
        final List<LauSplashScreenCreativeImagePo> existingPos = list(creativeId);
        final RecDiffResult<LauSplashScreenCreativeImagePo, Long> result = CommonFuncs.recDiff(existingPos, images, this::uk, LauSplashScreenCreativeImagePo::getId, CommonFuncs.getDefaultBiFunction(LauSplashScreenCreativeImagePo::getId, LauSplashScreenCreativeImagePo::setId));
        CommonFuncs.handleRecDiff(result, adCoreBqf, lauSplashScreenCreativeImage, lauSplashScreenCreativeImage.id::in);
    }

    public List<LauSplashScreenCreativeImagePo> list(Integer creativeId) {
        return adCoreBqf.selectFrom(lauSplashScreenCreativeImage)
                .where(lauSplashScreenCreativeImage.creativeId.eq(creativeId))
                .where(lauSplashScreenCreativeImage.isDeleted.eq(0))
                .fetch();
    }
    public List<LauSplashScreenCreativeImagePo> list(Collection<Integer> creativeIds) {
        return adCoreBqf.selectFrom(lauSplashScreenCreativeImage)
                .where(lauSplashScreenCreativeImage.creativeId.in(creativeIds))
                .where(lauSplashScreenCreativeImage.isDeleted.eq(0))
                .fetch();
    }

    private String uk(LauSplashScreenCreativeImagePo creativeImage) {
        return creativeImage.getCreativeId() + '-' + creativeImage.getMd5();
    }

    public static boolean imagesNeedReAudit(List<SplashScreenImage> cur, List<SplashScreenImage> prev) {
        // 删除图片不触发审核
        if (CollectionUtils.isEmpty(cur)) {
            return false;
        }

        // 新增图片触发审核
        if (CollectionUtils.isEmpty(prev)) {
            return true;
        }
        final String prevKeys = prev
                .stream()
                .map(x -> x.getUrl() + "-" + x.getMd5())
                .sorted()
                .collect(Collectors.joining(","));
        final String curKeys = cur
                .stream()
                .map(x -> x.getUrl() + "-" + x.getMd5())
                .sorted()
                .collect(Collectors.joining(","));
        // 图片变更需要重新审核
        return !Objects.equals(prevKeys, curKeys);
    }


    public Map<Integer, LauSplashScreenCreativeImagePo> get1to2Image(List<Integer> creativeIds) {
        List<LauSplashScreenCreativeImagePo> imagePos = adCoreBqf.selectFrom(lauSplashScreenCreativeImage)
                .where(lauSplashScreenCreativeImage.creativeId.in(creativeIds))
                .where(lauSplashScreenCreativeImage.isDeleted.eq(IsDeleted.VALID))
                .where(lauSplashScreenCreativeImage.width.eq(WIDTH_1280))
                .fetch();
        if(CollectionUtils.isEmpty(imagePos)){
            return new HashMap<>();
        }
        return imagePos.stream().collect(Collectors.toMap(LauSplashScreenCreativeImagePo::getCreativeId,
                Function.identity(), (x, y) -> x));
    }
}
