package com.bilibili.adp.cpc.biz.services.creative.dto;

import com.bilibili.adp.cpc.biz.services.unit.dto.CpcUnitDto;
import com.bilibili.adp.cpc.biz.services.campaign.dto.CpcCampaignDto;
import com.bilibili.adp.cpc.biz.services.unit.dto.CpcUnitExtraDto;
import com.bilibili.adp.launch.api.creative.dto.*;
import com.bilibili.location.api.cardtype.dto.CreativeStyle;
import com.bilibili.location.api.template.dto.TemplateGroupBo;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

/**
 * Created by walker on 16/8/29.
 * copy class
 * @see com.bilibili.adp.launch.api.creative.dto.CpcCreativeDto
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CpcCreativeDto implements Serializable {

    private static final long serialVersionUID = -6232796296818490937L;

    private Integer unitId;

    private Integer accountId;

    private Integer campaignId;

    private Integer creativeId;

    private Integer orderId;

    private Integer salesType;

    private Integer creativeType;

    private Integer cmMark;

    private String cmMarkDesc;

    private Integer busMarkId;

    private String creativeName;

    private String promotionPurposeContent;

    private String promotionPurposeContentSecondary;

    private String customizedImpUrl;

    private String customizedClickUrl;

    private String title;

    private String description;

    private String extDescription;

    /**
     * 创意图片
     */
    private List<ImageDto> imageDtos;

    private List<String> titleList;

    private long videoId;

    private int mgkVideoId;

    private String videoUrl;

    private String mgkVideoUrl;

    private String extImageUrl;

    private String extImageMd5;

    private Integer auditStatus;

    private Integer status;

    private String reason;

    private Integer templateId;

    private Integer firstCategoryId;

    private Integer secondCategoryId;

    private List<String> tags;

    private Integer creativeStatus;

    private Timestamp modifyTime;

    private Date beginTime;

    private Date endTime;

    private Integer attachType;

    private Integer buttonCopyId;

    private Integer buttonCopyType;

    private String buttonCopy;

    private String buttonCopyUrl;

    private String appDownloadUrl;

    private Integer promotionPurposeType;

    private CpcCampaignDto campaign;

    private CpcUnitDto unit;

    private CpcUnitExtraDto unitExtra;

    /**
     * 创意形态 1-静态图文 2-动态图文 3-静态视频 4-广告位播放视频
     * @see CreativeStyle
     */
    private Integer styleAbility;

    // 模板
    private TemplateGroupBo template;

    private String imageUrl;

    private String imageMd5;

    // 新的稿件封面url
    private String arcCoverUrl;

    private Integer isHistory;

    private Integer version;

    /**
     * 是否自动填写 0-手动填写 1-自动填写
     */
    private Integer isAutoFill;

    private String schemeUrl;

    private List<String> danmakus;

    private Integer jumpType;

    private Long mgkPageId;

    private Integer mgkPageStatus;

    /**
     * 微信小游戏id
     */
    private Integer miniGameId;
    /**
     * 微信小游戏链接
     */
    private String miniGameUrl;

    /**
     * 是否有微信小游戏绑定
     */
    private Integer hasLauMiniGame;

    private Integer adVersionControllId;

    private CpcCreativeWildcard wildcardDesc;

    private int buFirstCategoryId;
    private int buSecondCategoryId;
    private int buThirdCategoryId;

    private String adMark;

    private Integer flowWeightState;

    private Timestamp ctime;

    /**
     * 创意更新时间
     */
    private Timestamp mtime;

    /**
     * 创意动态
     */
    private CpcCreativeDynamicDto dynamic;

    private Integer shareState;
    private String shareImageUrl;
    private String shareImageMd5;
    private String shareTitle;
    private String shareSubtitle;

    private Integer creativeStyle;

    private CpcCreativeAutoDto autoDto;
    private List<CpcCreativeMonitoringDto> creativeMonitoring;
    /**
     * 创意来源 0-未知，1-个人起飞 2-现金版个人起飞
     */
    private Integer bilibiliUserId;

    private Integer adpVersion;

    private Integer templateGroupId;

    private Integer preferScene;

    private List<Integer> scenes;

    private List<CreativeSlotGroupTemplateDto> slotGroupTemplates;

    private Integer isProgrammatic;

    private Integer isNewFly;

    private Integer brandInfoId;

    private Integer flag;

    // 稿件视频信息
    private BilibiliVideoBo bilibiliVideoBo;

    private Long materialVideoId;

    /**
     * 框下位置是否允许投放 0不允许 1允许
     */
    private Integer underFrameAuditFlag;
    
    private Integer flyScenesType;
    private List<Integer> flySpecificScenesType;
    private String flySpecificScenes;
    private Integer coverType;
    private Integer dynamicType;
    private Long dynamicId;
    private Long dynamicUpMid;
    private String likeCount;
    private String nickName;
    private boolean flyCopy;
    private Integer sourceCreativeIdAuditStatus;
    private Integer sourceCreativeIdCreativeStatus;

    private List<Integer> sceneIds;
    private Integer channelId;

    private AdpCpcMgkLandingPageBo mgkLandingPage;

    private Integer isManaged;

    private Integer isSetInviteLink;
    private FlyProUnderBoxSaveDto flyProUnderBox;

    private Integer advertisingMode;

    private Integer industryId;

    private Integer tabId;

    private Integer isGdPlus;


    /**
     * tab名称
     */
    private String tabName;

    /**
     * 程序化创意额外元素审核状态: 0-审核通过 1-待审 2-审核拒绝
     */
    private Integer progMiscElemAuditStatus;

    /**
     * 程序化创意审核状态: 0-审核完成 1-待审
     */
    private Integer progAuditStatus;

    /**
     * 起飞版位收敛4.0创意尺寸类型: 1-16:10 2-16:9
     */
    private Integer flyBannerCoverType;

    /**
     * 起飞版位收敛4.0单元场景类型
     */
    private Integer flyBannerUnitScenesType;

    /**
     * 起飞版位收敛4.0单元指定场景
     */
    private List<Integer> flyBannerUnitSpecificScenesType;


    private Boolean preferDirectCallUp;
    private Integer isYellowCar;
    private String yellowCarTitle;
    private Boolean lastHasYellowCarNowNotHas;

    /**
     * 是新中台广告：0-否 1-是
     */
    private Integer isMiddleAd;
    //
    private List<CreativeComponentBo> components;

    /**
     * 创意资质包 id(非必填)
     */
    private Integer qualificationPackageId;

    private List<Integer> qualificationIds;

    // 邀约组件信息
    private List<FlyInvitationDto> flyInvitationDtos;

    // 托管计划标题 id
    private Long managedJobTitleId;

    private Long pageGroupId;
    private Integer isPageGroup;

    private Integer imageMacroType;
    private String imageMacroPlaceHolder;

    private Long danmakuGroupId;

    private Integer parentCreativeId;

    // 新三连
    private Long flyDynamicId;

    private Long materialCenterVideoId;

    private boolean hasAigcReplaceHistory;
}
