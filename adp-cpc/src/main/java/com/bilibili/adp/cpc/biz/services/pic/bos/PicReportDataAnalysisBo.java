package com.bilibili.adp.cpc.biz.services.pic.bos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PicReportDataAnalysisBo {

    private String date;
    private String picMd5;
    private String croppedPicMd5;
    private String picUrl;
    private Long consumedImageCount;

    @Builder.Default
    private BigDecimal cost = BigDecimal.ZERO;
    @Builder.Default
    private Long showCount = 0L;
    @Builder.Default
    private Long clickCount = 0L;
    @Builder.Default
    private BigDecimal clickRate = BigDecimal.ZERO;
    @Builder.Default
    private BigDecimal costPerClick = BigDecimal.ZERO;
    @Builder.Default
    private BigDecimal averageCostPerThousand = BigDecimal.ZERO;

    @Builder.Default
    private Long formSubmitCount = 0L;
    @Builder.Default
    private BigDecimal formSubmitAverageCost = BigDecimal.ZERO;
    @Builder.Default
    private BigDecimal formSubmitRate = BigDecimal.ZERO;

    @Builder.Default
    private Long formPaidCount = 0L;
    @Builder.Default
    private BigDecimal formPaidCost = BigDecimal.ZERO;
    @Builder.Default
    private BigDecimal formPaidRate = BigDecimal.ZERO;

    @Builder.Default
    private Long validClueCount = 0L;
    @Builder.Default
    private BigDecimal validClueAverageCost = BigDecimal.ZERO;
    @Builder.Default
    private BigDecimal validClueRate = BigDecimal.ZERO;

    @Builder.Default
    private Long applyCount = 0L;
    @Builder.Default
    private BigDecimal applyCost = BigDecimal.ZERO;
    @Builder.Default
    private BigDecimal applyRate = BigDecimal.ZERO;

    @Builder.Default
    private Long creditCount = 0L;
    @Builder.Default
    private BigDecimal creditCost = BigDecimal.ZERO;
    @Builder.Default
    private BigDecimal creditRate = BigDecimal.ZERO;

    @Builder.Default
    private Long withdrawDepositsCount = 0L;
    @Builder.Default
    private BigDecimal withdrawDepositsCost = BigDecimal.ZERO;
    @Builder.Default
    private BigDecimal withdrawDepositsRate = BigDecimal.ZERO;

    @Builder.Default
    private Long firstWithdrawCount = 0L;
    @Builder.Default
    private BigDecimal firstWithdrawCost = BigDecimal.ZERO;
    @Builder.Default
    private BigDecimal firstWithdrawRate = BigDecimal.ZERO;

    @Builder.Default
    private Long gameReserveCount = 0L;
    @Builder.Default
    private BigDecimal gameReserveAverageCost = BigDecimal.ZERO;
    @Builder.Default
    private BigDecimal gameReserveRate = BigDecimal.ZERO;

    @Builder.Default
    private Long androidDownloadCount = 0L;
    @Builder.Default
    private BigDecimal androidDownloadAverageCost = BigDecimal.ZERO;
    @Builder.Default
    private BigDecimal androidDownloadRate = BigDecimal.ZERO;

    @Builder.Default
    private Long androidInstallCount = 0L;
    @Builder.Default
    private BigDecimal androidInstallAverageCost = BigDecimal.ZERO;
    @Builder.Default
    private BigDecimal androidInstallRate = BigDecimal.ZERO;

    @Builder.Default
    private Long iosActivateCount = 0L;
    @Builder.Default
    private Long androidActivateCount = 0L;
    @Builder.Default
    private Long activateCount = 0L;
    @Builder.Default
    private BigDecimal appActivateAverageCost = BigDecimal.ZERO;
    @Builder.Default
    private BigDecimal appActivateRate = BigDecimal.ZERO;

    @Builder.Default
    private Long androidGameCenterActivationCount = 0L;
    @Builder.Default
    private BigDecimal androidGameCenterActivationCost = BigDecimal.ZERO;
    @Builder.Default
    private BigDecimal androidGameCenterActivationRate = BigDecimal.ZERO;

    @Builder.Default
    private Long registerCount = 0L;
    @Builder.Default
    private BigDecimal registerAverageCost = BigDecimal.ZERO;
    @Builder.Default
    private BigDecimal registerRate = BigDecimal.ZERO;

    @Builder.Default
    private Long orderPayCount = 0L;
    @Builder.Default
    private BigDecimal orderPayAmount = BigDecimal.ZERO;
    @Builder.Default
    private Long orderFirstPayCount = 0L;
    @Builder.Default
    private BigDecimal orderFirstPayAmount = BigDecimal.ZERO;
    @Builder.Default
    private BigDecimal orderFirstPayCost = BigDecimal.ZERO;
    @Builder.Default
    private BigDecimal orderFirstPayRate = BigDecimal.ZERO;

    @Builder.Default
    private Long androidGameCenterPaymentInAppCount = 0L;
    @Builder.Default
    private Long androidGameCenterFirstPaymentInAppCount = 0L;
    @Builder.Default
    private BigDecimal androidGameCenterFirstPaymentInAppCost = BigDecimal.ZERO;
    @Builder.Default
    private BigDecimal androidGameCenterFirstPaymentInAppRate = BigDecimal.ZERO;

    @Builder.Default
    private Long retentionCount = 0L;
    @Builder.Default
    private BigDecimal retentionCost = BigDecimal.ZERO;
    @Builder.Default
    private BigDecimal retentionRate = BigDecimal.ZERO;

    @Builder.Default
    private Long appCallupCount = 0L;
    @Builder.Default
    private BigDecimal appCallupCost = BigDecimal.ZERO;
    @Builder.Default
    private BigDecimal appCallupRate = BigDecimal.ZERO;

    @Builder.Default
    private Long lpCallUpSuccessStayCount = 0L;
    @Builder.Default
    private BigDecimal lpCallUpSuccessStayCost = BigDecimal.ZERO;
    @Builder.Default
    private BigDecimal lpCallUpSuccessStayRate = BigDecimal.ZERO;

    @Builder.Default
    private Long orderSubmitCount = 0L;
    @Builder.Default
    private BigDecimal orderSubmitAmount = BigDecimal.ZERO;
    @Builder.Default
    private BigDecimal orderSubmitCost = BigDecimal.ZERO;
    @Builder.Default
    private BigDecimal orderSubmitRate = BigDecimal.ZERO;
    @Builder.Default
    private BigDecimal goodsRoi = BigDecimal.ZERO;

    @Builder.Default
    private Long firstOrderPlaceCount = 0L;
    @Builder.Default
    private BigDecimal firstOrderPlaceAmount = BigDecimal.ZERO;
    @Builder.Default
    private BigDecimal firstOrderPlaceCost = BigDecimal.ZERO;
    @Builder.Default
    private BigDecimal firstOrderPlaceRate = BigDecimal.ZERO;

    @Builder.Default
    private Long keyBehaviorCount = 0L;
    @Builder.Default
    private BigDecimal keyBehaviorCost = BigDecimal.ZERO;
    @Builder.Default
    private BigDecimal keyBehaviorRate = BigDecimal.ZERO;

    private String materialCenterId;
    private String materialScore;
    private String materialUrl;

    @Builder.Default
    private Long gameChargeIn24hCount = 0L;
    @Builder.Default
    private BigDecimal costPerGameChargeIn24h = BigDecimal.ZERO;
    @Builder.Default
    private BigDecimal gameChargeIn24hAmount = BigDecimal.ZERO;
    @Builder.Default
    private BigDecimal gameChargeIn24hRoi = BigDecimal.ZERO;

    @Builder.Default
    private Long gameChargeIn1dCount = 0L;
    @Builder.Default
    private BigDecimal costPerGameChargeIn1d = BigDecimal.ZERO;
    @Builder.Default
    private BigDecimal gameChargeIn1dAmount = BigDecimal.ZERO;
    @Builder.Default
    private BigDecimal gameChargeIn1dRoi = BigDecimal.ZERO;

    @Builder.Default
    private Long gameChargeIn7dCount = 0L;
    @Builder.Default
    private BigDecimal costPerGameChargeIn7d = BigDecimal.ZERO;
    @Builder.Default
    private BigDecimal gameChargeIn7dAmount = BigDecimal.ZERO;
    @Builder.Default
    private BigDecimal gameChargeIn7dRoi = BigDecimal.ZERO;

    @Builder.Default
    private Long gameChargeIn14dCount = 0L;
    @Builder.Default
    private BigDecimal costPerGameChargeIn14d = BigDecimal.ZERO;
    @Builder.Default
    private BigDecimal gameChargeIn14dAmount = BigDecimal.ZERO;
    @Builder.Default
    private BigDecimal gameChargeIn14dRoi = BigDecimal.ZERO;

    @Builder.Default
    private BigDecimal gameChargeIn24hMixAmount = BigDecimal.ZERO;
    @Builder.Default
    private BigDecimal gameChargeIn24hMixRoi = BigDecimal.ZERO;

    @Builder.Default
    private BigDecimal gameChargeIn1dMixAmount = BigDecimal.ZERO;
    @Builder.Default
    private BigDecimal gameChargeIn1dMixRoi = BigDecimal.ZERO;

    @Builder.Default
    private BigDecimal gameChargeIn7dMixAmount = BigDecimal.ZERO;
    @Builder.Default
    private BigDecimal gameChargeIn7dMixRoi = BigDecimal.ZERO;

    @Builder.Default
    private BigDecimal gameChargeIn14dMixAmount = BigDecimal.ZERO;
    @Builder.Default
    private BigDecimal gameChargeIn14dMixRoi = BigDecimal.ZERO;

    public static PicReportDataAnalysisBo emptyInstance(String date) {
        return PicReportDataAnalysisBo.builder()
                .date(date)
                .build();
    }
}