package com.bilibili.adp.cpc.biz.services.ad_product.dto;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.cpc.enums.ad_product.AdProductLibraryTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @date 2024/6/4
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ManualLabelImportDto implements Serializable {

    private static final long serialVersionUID = -3014692037127218091L;

    private Operator operator;

    private String fileName;

    private String fileKey;

    private String fileUrl;

    private List<Integer> accountIds;

    private List<Integer> unitIds;

    private AdProductLibraryTypeEnum adProductLibraryTypeEnum;

    private Integer labelType;

    private Long ctime;

}
