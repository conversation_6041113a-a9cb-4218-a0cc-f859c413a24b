package com.bilibili.adp.cpc.converter;

import com.bilibili.adp.cpc.biz.services.dynamic.DynamicLinkProc;
import com.bilibili.adp.cpc.dto.DynamicResponseDto;
import com.bilibili.adp.cpc.dto.NewDynamicDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/4/10 14:57
 */
@Component
public class DynamicDtoConvertor {

    @Autowired
    private DynamicLinkProc dynamicLinkProc;

    public List<NewDynamicDto> convertNewDynamicDtoList(List<DynamicResponseDto> dynInfoList) {
        if (CollectionUtils.isEmpty(dynInfoList)) {
            return Collections.EMPTY_LIST;
        }

        List<NewDynamicDto> dynamicDtos = dynInfoList.stream().map((dynamicResponseDto) -> {
            NewDynamicDto dynamicDto = convertDymamicDto(dynamicResponseDto);
            return dynamicDto;
        }).collect(Collectors.toList());
        return dynamicDtos;
    }

    public NewDynamicDto convertDymamicDto(DynamicResponseDto midDynamicResponseDto) {
        if (midDynamicResponseDto == null) {
            return null;
        }

        NewDynamicDto dynamicDto = new NewDynamicDto();
        dynamicDto.setDynamicId(midDynamicResponseDto.getDyn_id() + "");
        if (midDynamicResponseDto.getOwner() != null) {
            dynamicDto.setMid(midDynamicResponseDto.getOwner().getUid() + "");
            dynamicDto.setFace(midDynamicResponseDto.getOwner().getFace());
            dynamicDto.setNickname(midDynamicResponseDto.getOwner().getName());
        }
        dynamicDto.setDynamicContent(midDynamicResponseDto.getContent());
        if (!CollectionUtils.isEmpty(midDynamicResponseDto.getPictures())) {
            dynamicDto.setDynamicImages(midDynamicResponseDto.getPictures().stream().map(p -> p.getImg_src()).collect(Collectors.toList()));
        }
        if (midDynamicResponseDto.getPub_time() != null) {
            dynamicDto.setPub_time(new Timestamp(midDynamicResponseDto.getPub_time() * 1000));
        }
        dynamicDto.setDynamicLink(dynamicLinkProc.spliceDynamicInnerLink(midDynamicResponseDto.getDyn_id()));
        dynamicDto.setDynamicSkipLink(dynamicLinkProc.spliceDynamicSkipLinkByEnv(midDynamicResponseDto.getDyn_id()));
        dynamicDto.setSid(midDynamicResponseDto.getReserve_id());
        dynamicDto.setSneaking_visiable(midDynamicResponseDto.getSneaking_visiable());
        dynamicDto.setType(midDynamicResponseDto.getType());
        dynamicDto.setRid(midDynamicResponseDto.getRid());
        return dynamicDto;
    }


}
