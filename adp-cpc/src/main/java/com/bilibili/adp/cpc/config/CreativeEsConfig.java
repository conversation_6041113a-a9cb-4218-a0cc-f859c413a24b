package com.bilibili.adp.cpc.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
@Data
public class CreativeEsConfig {

    @Value("${system.environment:prod}")
    private String env;

    public static final Integer CREATIVE_ES_WRITE_DATA_TYPE_ES1 = 1;
    public static final Integer CREATIVE_ES_WRITE_DATA_TYPE_ES2 = 2;
    public static final Integer CREATIVE_ES_WRITE_DATA_TYPE_ES_BOTH = 3;

    /**
     * 写入数据方式 1:es1 2:es2 3:es1+es2
     */
    @Value("${creative.es.writeDataType:1}")
    private Integer creativeEsWriteDataType;
}
