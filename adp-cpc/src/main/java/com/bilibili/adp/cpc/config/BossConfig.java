package com.bilibili.adp.cpc.config;

import com.alibaba.excel.EasyExcel;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.Assert;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.S3Configuration;
import software.amazon.awssdk.services.s3.model.*;
import software.amazon.awssdk.services.s3.presigner.S3Presigner;
import software.amazon.awssdk.services.s3.presigner.model.GetObjectPresignRequest;
import software.amazon.awssdk.services.s3.presigner.model.PresignedGetObjectRequest;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.URI;
import java.time.Duration;
import java.util.ArrayList;

/**
 * <AUTHOR>
 * @date 2024/9/9
 */

@Configuration
public class BossConfig {

    @Value("${download.center.endpoint:http://uat-boss.bilibili.co}")
    private String endpoint;

    @Value("${download.center.accessKey:31o9yrjvXCB6L1B4}")
    private String downloadAccessKey;

    @Value("${download.center.secretKey:ECD08ZySo2IuNFM4BxHsOWvlnh3jqrxj}")
    private String downloadSecretKey;

    @Value("${download.center.endpoint.sycpb-cm-apk:http://uat-boss.bilibili.co}")
    private String apkUpLoadEndpoint;

    @Value("${download.center.accessKey.sycpb-cm-apk:pbp5JTUGIr3eTnDM}")
    private String apkUpLoadAccessKey;

    @Value("${download.center.secretKey.sycpb-cm-apk:GXSa0wzXdEZxdrTlTZ3KBvM41lVFb1Od}")
    private String apkUpLoadSecretKey;

    @Bean("downloadCenterS3")
    public S3Client downloadCenter(){
        return buildClient(endpoint, downloadAccessKey, downloadSecretKey);
    }

    @Bean("sycpb-cm-apk-client")
    public S3Client client() {
        return buildClient(apkUpLoadEndpoint, apkUpLoadAccessKey, apkUpLoadSecretKey);
    }

    @Bean("sycpb-cm-apk-presigner")
    public S3Presigner s3Presigner(){
        return buildS3Presigner(apkUpLoadEndpoint, apkUpLoadAccessKey, apkUpLoadSecretKey);
    }

    private static S3Client buildClient(String endpoint, String accessKey, String secretKey) {
        Assert.isTrue(StringUtils.isNotBlank(endpoint), "endpoint不能为空");
        Region region = Region.of("boss"); // 目前暂未开启 Region 校验
        AwsBasicCredentials credentials = AwsBasicCredentials.create(accessKey, secretKey);
        StaticCredentialsProvider provider = StaticCredentialsProvider.create(credentials);
        S3Configuration config = S3Configuration.builder()
                .pathStyleAccessEnabled(true)
                .checksumValidationEnabled(false)
                .build();

        return S3Client.builder()
                .endpointOverride(URI.create(endpoint))
                .region(region)
                .credentialsProvider(provider)
                .serviceConfiguration(config)
                .build();
    }

    public static S3Presigner buildS3Presigner(String endpoint, String accessKey, String secretKey) {
        Assert.isTrue(StringUtils.isNotBlank(endpoint), "endpoint不能为空");
        Region region = Region.of("boss"); // 目前暂未开启 Region 校验
        AwsBasicCredentials credentials = AwsBasicCredentials.create(accessKey, secretKey);
        StaticCredentialsProvider provider = StaticCredentialsProvider.create(credentials);
        S3Configuration config = S3Configuration.builder()
                .pathStyleAccessEnabled(true)
                .checksumValidationEnabled(false)
                .build();

        return S3Presigner.builder()
                .endpointOverride(URI.create(endpoint))
                .region(region)
                .credentialsProvider(provider)
                .serviceConfiguration(config)
                .build();
    }

    public static void main(String[] args) {

        String endpoint = "http://jssz-inner-boss.bilibili.co";
        String accessKey = "10d4f5a2be99eccf";
        String secretKey = "87795b8f5087a340f290b26b1097fcfb";

        S3Client client = BossConfig.buildClient(endpoint, accessKey, secretKey);

        PutObjectRequest req = PutObjectRequest
                .builder()
                .bucket("cm")
                .contentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
                .key("test/测试中文.excel")
                .build();


        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            EasyExcel.write(outputStream).sheet().doWrite(new ArrayList<>());
            client.putObject(req, RequestBody.fromBytes(outputStream.toByteArray())); // 上传字符串
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        listObjects(client,"cm","test/测试中文.excel");

        //http://uat-boss.bilibili.co/cm/example.excel

        S3Presigner presigner = buildS3Presigner(endpoint, accessKey, secretKey);;

        System.out.println(createPresignedGetUrl(presigner, "cm", "test/测试中文.excel"));
        System.out.println("结束");
    }

    public static void listObjects(S3Client client, String bucket, String prefix) {
        // 默认最多返回 1000 个，需要返回更多，请使用 listObjectsV2Paginator 方法
        ListObjectsV2Request request = ListObjectsV2Request.builder().bucket(bucket).prefix(prefix).build();
        ListObjectsV2Response response = client.listObjectsV2(request);
        for (S3Object content : response.contents()) {
            System.out.println(content.key());
        }
    }

    public static String createPresignGetUrl(S3Presigner signer, String bucket, String key, Duration duration) {
        return signer.presignGetObject(r -> r.getObjectRequest(b -> b.bucket(bucket).key(key)).signatureDuration(duration)).url().toString();
    }
    public static String createPresignedGetUrl(S3Presigner signer,String bucketName, String keyName) {

            GetObjectRequest objectRequest = GetObjectRequest.builder()
                    .bucket(bucketName)
                    .key(keyName)
                    .build();

            GetObjectPresignRequest presignRequest = GetObjectPresignRequest.builder()
                    .signatureDuration(Duration.ofSeconds(1))  // The URL will expire in 10 minutes.
                    .getObjectRequest(objectRequest)
                    .build();

            PresignedGetObjectRequest presignedRequest = signer.presignGetObject(presignRequest);

            return presignedRequest.url().toExternalForm();
    }

}
