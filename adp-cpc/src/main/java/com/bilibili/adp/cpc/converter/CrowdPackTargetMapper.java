package com.bilibili.adp.cpc.converter;

import com.bilibili.adp.cpc.core.bos.CrowdPackTargetBo;
import com.bilibili.adp.cpc.wrapper.bos.CrowdPackTargetWrapperBo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface CrowdPackTargetMapper {
    CrowdPackTargetMapper INSTANCE = Mappers.getMapper(CrowdPackTargetMapper.class);

    CrowdPackTargetBo fromWrapper(CrowdPackTargetWrapperBo bo);
    CrowdPackTargetWrapperBo toWrapper(CrowdPackTargetBo bo);
}
