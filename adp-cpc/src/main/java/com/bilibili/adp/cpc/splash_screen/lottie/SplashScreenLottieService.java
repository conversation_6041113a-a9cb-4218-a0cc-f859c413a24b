package com.bilibili.adp.cpc.splash_screen.lottie;

import com.bilibili.adp.cpc.dao.querydsl.pos.LauSplashScreenLottiePo;
import com.bilibili.bjcom.querydsl.BaseQueryFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

import static com.bilibili.adp.cpc.dao.config.PlatformMySqlConfig.AD_BQF;
import static com.bilibili.adp.cpc.dao.querydsl.QLauSplashScreenLottie.lauSplashScreenLottie;

@Slf4j
@Service
public class SplashScreenLottieService {
    @Resource(name = AD_BQF)
    private BaseQueryFactory adBqf;

    public List<LauSplashScreenLottiePo> list() {
        return adBqf.selectFrom(lauSplashScreenLottie)
                .fetch();
    }
}
