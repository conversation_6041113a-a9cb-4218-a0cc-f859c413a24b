package com.bilibili.adp.cpc.automatic_rule.bos;

import com.bilibili.adp.cpc.automatic_rule.enums.object.ObjectType;
import com.bilibili.adp.cpc.automatic_rule.enums.rule.ExecuteLogic;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RuleBo {
    private Long ruleId;
    private Integer ruleType;
    private String ruleName;
    /**
     * @see ExecuteLogic
     */
    private Integer executeLogic;
    private List<String> conditions;
    private List<String> actions;
    /**
     * @see ObjectType
     */
    private Integer objectType;

    private List<Integer> objectIds;
    private Integer status;
}
