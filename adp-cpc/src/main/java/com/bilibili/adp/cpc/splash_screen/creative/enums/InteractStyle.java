package com.bilibili.adp.cpc.splash_screen.creative.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum InteractStyle {
    CLICK(0, "点击"),
    WORD(3, "文字"),
    SLIDE(6, "全屏滑动"),
    TWIST(7, "扭一扭"),
    ;
    private final int code;
    private final String desc;

    public static InteractStyle getByCode(int code) {
        return Arrays.stream(values())
                .filter(interactStyle -> interactStyle.getCode() == code)
                .findFirst()
                .orElseThrow(IllegalArgumentException::new);
    }
}
