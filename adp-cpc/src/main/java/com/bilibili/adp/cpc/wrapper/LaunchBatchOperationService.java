package com.bilibili.adp.cpc.wrapper;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.biz.bos.unit.LauUnitBo;
import com.bilibili.adp.cpc.biz.bos.unit.UnitUpdateDualBidTwoStageOptimizationBo;
import com.bilibili.adp.cpc.biz.services.target_package.CrowdPackService;
import com.bilibili.adp.cpc.compare.ChangeBo;
import com.bilibili.adp.cpc.compare.constants.MetaType;
import com.bilibili.adp.cpc.compare.constants.UnitCompareMeta;
import com.bilibili.adp.cpc.converter.AgeTargetMapper;
import com.bilibili.adp.cpc.converter.CrowdPackTargetMapper;
import com.bilibili.adp.cpc.converter.UnitNoBidMapper;
import com.bilibili.adp.cpc.converter.UnitPricingMapper;
import com.bilibili.adp.cpc.core.LaunchCampaignService;
import com.bilibili.adp.cpc.core.LaunchUnitTargetService;
import com.bilibili.adp.cpc.core.LaunchUnitV1Service;
import com.bilibili.adp.cpc.core.bos.AgeTargetBo;
import com.bilibili.adp.cpc.core.bos.CrowdPackTargetBo;
import com.bilibili.adp.cpc.core.constants.AdType;
import com.bilibili.adp.cpc.core.constants.BatchUnitDimension;
import com.bilibili.adp.cpc.core.constants.CrowdPackType;
import com.bilibili.adp.cpc.core.logs.LaunchOperationService;
import com.bilibili.adp.cpc.core.logs.LaunchUnitOperationService;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauUnitPo;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauUnitTargetCrowdPackPo;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauUnitTargetRulePo;
import com.bilibili.adp.cpc.splash_screen.operation_log.IOperationLogService;
import com.bilibili.adp.cpc.splash_screen.operation_log.OperationContextBo;
import com.bilibili.adp.cpc.splash_screen.operation_log.OperatorBo;
import com.bilibili.adp.cpc.splash_screen.operation_log.enums.OperationType;
import com.bilibili.adp.cpc.splash_screen.operator.OperatorConverter;
import com.bilibili.adp.cpc.wrapper.bos.AgeTargetWrapperBo;
import com.bilibili.adp.cpc.wrapper.bos.BatchUnitWrapperBo;
import com.bilibili.adp.cpc.wrapper.bos.CrowdPackTargetWrapperBo;
import com.bilibili.adp.cpc.wrapper.bos.UnitPricingWrapperBo;
import com.bilibili.adp.log.service.v6.ILogCpcOperationService;
import com.bilibili.adp.log.service.v6.bo.LogCpcOperationBo;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.bilibili.adp.cpc.dao.config.PlatformMySqlConfig.AD_CORE_TM;

@Service
@RequiredArgsConstructor
public class LaunchBatchOperationService {
    private final LaunchUnitTargetService launchUnitTargetService;
    private final LaunchUnitV1Service launchUnitV1Service;
    private final LaunchUnitOperationService launchUnitOperationService;
    private final LaunchOperationService launchOperationService;
    private final CrowdPackService crowdPackService;
    private final LaunchCampaignService launchCampaignService;
    private final IOperationLogService operationLogService;
    private final ILogCpcOperationService logCpcOperationService;

    public List<BatchUnitWrapperBo> listUnitTargetV6(Collection<Integer> unitIds, String dim, Integer accountId) {
        if (CollectionUtils.isEmpty(unitIds)) return Collections.emptyList();

        Assert.isTrue(unitIds.size() <= 100, "单次最大查询数量为100");
        final Long count = launchUnitV1Service.getUnitAccount(unitIds, accountId);
        Assert.isTrue((long) unitIds.size() == count, "您不能查看不属于您的单元");
        final Map<Integer, Integer> unitCampaignMap = launchUnitV1Service.getUnitCampaignMap(unitIds);
        final Map<Integer, Integer> campaignSearchMap = launchCampaignService.getCampaignSearchMap(unitCampaignMap.values());
        final Map<Integer, Integer> unitTargetPackageMap = launchUnitV1Service.getUnitTargetPackageMap(unitIds);

        final boolean isArea = BatchUnitDimension.isArea(dim);
        final boolean isGender = BatchUnitDimension.isGender(dim);
        final boolean isAge = BatchUnitDimension.isAge(dim);
        final boolean isCrowdPack = BatchUnitDimension.isCrowdPack(dim);

        final List<BatchUnitWrapperBo> batchUnitWrapperBos;
        if (isArea || isGender || isAge) {
            batchUnitWrapperBos = fromUnitTargetRule(unitIds, isArea, isGender, isAge);
        } else if (isCrowdPack) {
            batchUnitWrapperBos = fromCrowdPack(unitIds);
        } else {
            throw new IllegalArgumentException("无法识别dim参数" + dim);
        }
        for (BatchUnitWrapperBo batchUnitWrapperBo : batchUnitWrapperBos) {
            final Integer unitId = batchUnitWrapperBo.getUnitIds().get(0);
            final Integer campaignId = unitCampaignMap.get(unitId);
            if (Objects.isNull(campaignId)) continue;

            final Integer adType = campaignSearchMap.get(campaignId);
            batchUnitWrapperBo.setSearchAd(Objects.equals(adType, AdType.SEARCH));
            final Integer targetPackageId = unitTargetPackageMap.get(unitId);
            batchUnitWrapperBo.setHasTargetPackage(Utils.isPositive(targetPackageId));
        }
        return batchUnitWrapperBos;
    }

    public List<BatchUnitWrapperBo> listUnitTarget(Collection<Integer> unitIds, String dim, Integer accountId) {
        if (CollectionUtils.isEmpty(unitIds)) return Collections.emptyList();

        Assert.isTrue(unitIds.size() <= 100, "单次最大查询数量为100");
        final Long count = launchUnitV1Service.getUnitAccount(unitIds, accountId);
        Assert.isTrue((long) unitIds.size() == count, "您不能查看不属于您的单元");
        final Map<Integer, Integer> unitCampaignMap = launchUnitV1Service.getUnitCampaignMap(unitIds);
        final Map<Integer, Integer> campaignSearchMap = launchCampaignService.getCampaignSearchMap(unitCampaignMap.values());
        final Map<Integer, Integer> unitTargetPackageMap = launchUnitV1Service.getUnitTargetPackageMap(unitIds);
        final boolean hasSearchAd = campaignSearchMap.values().stream().anyMatch(x -> Objects.equals(x, AdType.SEARCH));
        final boolean hasTargetPackage = unitTargetPackageMap.values().stream().anyMatch(Utils::isPositive);
        if (hasTargetPackage) {
            return unitIds.stream()
                    .map(x -> {
                        final Integer targetPackageId = unitTargetPackageMap.get(x);
                        final BatchUnitWrapperBo bo = new BatchUnitWrapperBo();
                        bo.setUnitIds(Collections.singletonList(x));
                        bo.setHasTargetPackage(Utils.isPositive(targetPackageId));
                        return bo;
                    }).collect(Collectors.toList());
        }

        final boolean isArea = BatchUnitDimension.isArea(dim);
        final boolean isGender = !isArea && BatchUnitDimension.isGender(dim);
        final boolean isAge = !isArea && !isGender && BatchUnitDimension.isAge(dim);
        final boolean isCrowdPack = !isArea && !isGender && !isAge && BatchUnitDimension.isCrowdPack(dim);

        if (isArea || isGender || isAge) {
            return fromUnitTargetRule(unitIds, isArea, isGender, isAge);
        } else if (isCrowdPack) {
            if (hasSearchAd) {
                return unitIds.stream()
                        .map(x -> {
                            final BatchUnitWrapperBo bo = new BatchUnitWrapperBo();
                            bo.setUnitIds(Collections.singletonList(x));
                            final Integer campaignId = unitCampaignMap.get(x);
                            final Integer adType = campaignSearchMap.get(campaignId);
                            bo.setSearchAd(Objects.equals(AdType.SEARCH, adType));
                            return bo;
                        }).collect(Collectors.toList());
            }
            return fromCrowdPack(unitIds);
        }
        throw new IllegalArgumentException("无法识别dim参数" + dim);
    }

    @Transactional(AD_CORE_TM)
    public void batchUpdateUnitBid(Operator operator, List<BatchUnitWrapperBo> bos, List<LauUnitPo> existingUnitPos, Integer updateType) {
        if (CollectionUtils.isEmpty(bos)) return;

        final Map<Integer, BatchUnitWrapperBo> batchUnitMap = fromPricing(existingUnitPos, updateType).stream().collect(Collectors.toMap(x -> x.getUnitIds().get(0), Function.identity(), (x, y) -> y));
        // 分组更新, 降低io次数
        for (Map.Entry<Integer, List<BatchUnitWrapperBo>> entry : bos.stream().collect(Collectors.groupingBy(x -> x.getPricing().getOcpxTargetTwoBid())).entrySet()) {
            final List<Integer> unitIds = entry.getValue()
                    .stream()
                    .map(x -> x.getUnitIds().get(0))
                    .collect(Collectors.toList());
            launchUnitV1Service.batchUpdatePricing(unitIds, UnitPricingMapper.INSTANCE.fromWrapper(entry.getValue().get(0).getPricing()));
        }
        final List<LogCpcOperationBo> logCpcOperationBos = new ArrayList<>();
        for (BatchUnitWrapperBo bo : bos) {
            final BatchUnitWrapperBo existingBo = batchUnitMap.get(bo.getUnitIds().get(0));
            final LogCpcOperationBo logBo = launchUnitOperationService.genPricingBatchOperationLog(existingBo, bo.getPricing(), operator);
            if (Objects.nonNull(logBo)) {
                logCpcOperationBos.add(logBo);
            }
        }
        logCpcOperationService.batchInsert(logCpcOperationBos);
    }

    @SneakyThrows
    @Transactional(AD_CORE_TM)
    public void batchUpdateUnitTarget(Operator operator, List<BatchUnitWrapperBo> bos) {
        if (CollectionUtils.isEmpty(bos)) return;

        final List<Integer> allUnitIds = bos.stream()
                .flatMap(x -> x.getUnitIds().stream())
                .collect(Collectors.toList());
        final BatchUnitWrapperBo sample0 = bos.get(0);
        final List<LogCpcOperationBo> logCpcOperationBos = new ArrayList<>();
        if (Objects.nonNull(sample0.getAge())) {
            // 分组可以减少io次数
            final List<BatchUnitWrapperBo> batchUnitWrapperBos = fromUnitTargetRule(allUnitIds, false, false, true);
            final Map<Integer, BatchUnitWrapperBo> batchUnitMap = genBatchUnitMap(batchUnitWrapperBos);
            for (List<BatchUnitWrapperBo> group : bos.stream().collect(Collectors.groupingBy(x -> x.getAge().toString())).values()) {
                final BatchUnitWrapperBo sample1 = group.get(0);
                final List<Integer> unitIds = group.stream()
                        .flatMap(x -> x.getUnitIds().stream())
                        .collect(Collectors.toList());
                final AgeTargetWrapperBo ageWrapper = sample1.getAge();
                ageWrapper.setAges(sort(ageWrapper.getAges()));
                ageWrapper.setCustomizedAges(sort(ageWrapper.getCustomizedAges()));
                final AgeTargetBo age = AgeTargetMapper.INSTANCE.fromWrapper(ageWrapper);
                launchUnitTargetService.batchUpdateAge(unitIds, age);
                final List<BatchUnitWrapperBo> existingBos = unitIds.stream()
                        .map(batchUnitMap::get)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
                logCpcOperationBos.addAll(launchUnitOperationService.genAgeBatchOperationLogs(existingBos, age, operator));
            }
        } else if (Objects.nonNull(sample0.getArea())) {
            // 更新地域，地域和地域类型一起更新
            final List<BatchUnitWrapperBo> batchUnitWrapperBos = fromUnitTargetRule(allUnitIds, true, false, false);
            final Map<Integer, BatchUnitWrapperBo> batchUnitMap = genBatchUnitMap(batchUnitWrapperBos);
            for (List<BatchUnitWrapperBo> group : bos.stream().collect(Collectors.groupingBy(x -> x.getArea().toString())).values()) {
                final BatchUnitWrapperBo sample1 = group.get(0);
                final List<Integer> unitIds = group.stream()
                        .flatMap(x -> x.getUnitIds().stream())
                        .collect(Collectors.toList());
                final List<Integer> area = sort(sample1.getArea());
                // 地域类型单选，不用排序
                final List<Integer> areaType = sample1.getAreaType();
                launchUnitTargetService.batchUpdateArea(unitIds, area,areaType);
                final List<BatchUnitWrapperBo> existingBos = unitIds.stream()
                        .map(batchUnitMap::get)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
                logCpcOperationBos.addAll(launchUnitOperationService.genAreaBatchOperationLogs(existingBos, area, operator));
            }
        } else if (Objects.nonNull(sample0.getGender())) {
            final List<BatchUnitWrapperBo> batchUnitWrapperBos = fromUnitTargetRule(allUnitIds, false, true, false);
            final Map<Integer, BatchUnitWrapperBo> batchUnitMap = genBatchUnitMap(batchUnitWrapperBos);
            for (List<BatchUnitWrapperBo> group : bos.stream().collect(Collectors.groupingBy(BatchUnitWrapperBo::getGender)).values()) {
                final BatchUnitWrapperBo sample1 = group.get(0);
                final List<Integer> unitIds = group.stream()
                        .flatMap(x -> x.getUnitIds().stream())
                        .collect(Collectors.toList());
                final List<Integer> gender = sort(sample1.getGender());
                launchUnitTargetService.batchUpdateGender(unitIds, gender);
                final List<BatchUnitWrapperBo> existingBos = unitIds.stream()
                        .map(batchUnitMap::get)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
                logCpcOperationBos.addAll(launchUnitOperationService.genGenderBatchOperationLogs(existingBos, gender, operator));
            }
        } else if (Objects.nonNull(sample0.getCrowdPack())) {
            final List<BatchUnitWrapperBo> batchUnitWrapperBos = fromCrowdPack(allUnitIds);
            final Map<Integer, BatchUnitWrapperBo> batchUnitMap = genBatchUnitMap(batchUnitWrapperBos);
            for (List<BatchUnitWrapperBo> group : bos.stream().collect(Collectors.groupingBy(x -> x.getCrowdPack().toString())).values()) {
                final BatchUnitWrapperBo sample1 = group.get(0);
                final List<Integer> unitIds = group.stream()
                        .flatMap(x -> x.getUnitIds().stream())
                        .collect(Collectors.toList());
                final CrowdPackTargetWrapperBo crowdPackWrapper = sample1.getCrowdPack();
                final List<Integer> includedCrowdPackIds = crowdPackWrapper.getIncludedCrowdPackIds()
                        .stream()
                        .distinct()
                        .collect(Collectors.toList());
                final List<Integer> excludedCrowdPackIds = crowdPackWrapper.getExcludedCrowdPackIds()
                        .stream()
                        .distinct()
                        .collect(Collectors.toList());
                final List<Integer> crowdPackIds = new ArrayList<>(includedCrowdPackIds);
                crowdPackIds.addAll(excludedCrowdPackIds);
                crowdPackService.validateCrowdPack(crowdPackIds, operator.getOperatorId());
                crowdPackWrapper.setIncludedCrowdPackIds(sort(includedCrowdPackIds));
                crowdPackWrapper.setExcludedCrowdPackIds(sort(excludedCrowdPackIds));
                final CrowdPackTargetBo crowdPack = CrowdPackTargetMapper.INSTANCE.fromWrapper(crowdPackWrapper);
                launchUnitTargetService.batchUpdateCrowdPack(unitIds, crowdPack);
                final List<BatchUnitWrapperBo> existingBos = unitIds.stream()
                        .map(batchUnitMap::get)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
                logCpcOperationBos.addAll(launchUnitOperationService.genCrowdPackBatchOperationLogs(existingBos, crowdPack, operator));
            }
        }
        launchOperationService.insertLogOperations(logCpcOperationBos);
    }

    private List<BatchUnitWrapperBo> fromPricing(Collection<LauUnitPo> unitPos, Integer upateType) {
        return unitPos.stream()
                .map(x -> {
                    return BatchUnitWrapperBo.builder()
                            .unitIds(Collections.singletonList(x.getUnitId()))
                            .pricing(UnitPricingWrapperBo.fromPo(x, upateType))
                            .build();
                }).collect(Collectors.toList());
    }

    private List<BatchUnitWrapperBo> fromCrowdPack(Collection<Integer> unitIds) {
        final Map<Integer, Map<Integer, List<Integer>>> map0 = launchUnitTargetService.listCrowdPackTargetWithoutStartUpCrowds(unitIds, true)
                .stream()
                .collect(Collectors.groupingBy(LauUnitTargetCrowdPackPo::getUnitId,
                        Collectors.groupingBy(LauUnitTargetCrowdPackPo::getType,
                                Collectors.mapping(LauUnitTargetCrowdPackPo::getCrowdPackId, Collectors.toList()))));
        final List<BatchUnitWrapperBo> list = new ArrayList<>();
        for (Map.Entry<Integer, Map<Integer, List<Integer>>> entry0 : map0.entrySet()) {
            final BatchUnitWrapperBo bo = new BatchUnitWrapperBo();
            bo.setUnitIds(Collections.singletonList(entry0.getKey()));
            final Map<Integer, List<Integer>> map1 = entry0.getValue();
            final CrowdPackTargetBo crowdPack = CrowdPackTargetBo.builder()
                    .includedCrowdPackIds(sort(map1.getOrDefault(CrowdPackType.INCLUDED, Collections.emptyList())))
                    .excludedCrowdPackIds(sort(map1.getOrDefault(CrowdPackType.EXCLUDED, Collections.emptyList())))
                    .build();
            bo.setCrowdPack(CrowdPackTargetMapper.INSTANCE.toWrapper(crowdPack));
            list.add(bo);
        }
        return list;
    }

    private List<BatchUnitWrapperBo> fromUnitTargetRule(Collection<Integer> unitIds, boolean isArea, boolean isGender, boolean isAge) {
        final List<LauUnitTargetRulePo> pos = launchUnitTargetService.listBaseTarget(unitIds);
        if (isArea) {
            return pos.stream()
                    .map(x -> {
                        final BatchUnitWrapperBo bo = new BatchUnitWrapperBo();
                        bo.setUnitIds(Collections.singletonList(x.getUnitId()));
                        bo.setArea(LaunchUnitTargetService.parseIntoIntegerArray(x.getArea()));
                        bo.setAreaLevel(LaunchUnitTargetService.parseIntoIntegerArray(x.getAreaLevel()));
                        return bo;
                    }).collect(Collectors.toList());
        }
        if (isGender) {
            return pos.stream()
                    .map(x -> {
                        final BatchUnitWrapperBo bo = new BatchUnitWrapperBo();
                        bo.setUnitIds(Collections.singletonList(x.getUnitId()));
                        bo.setGender(LaunchUnitTargetService.parseIntoIntegerArray(x.getGender()));
                        return bo;
                    }).collect(Collectors.toList());
        }
        if (isAge) {
            return pos.stream()
                    .map(x -> {
                        final BatchUnitWrapperBo bo = new BatchUnitWrapperBo();
                        bo.setUnitIds(Collections.singletonList(x.getUnitId()));
                        final AgeTargetBo age = AgeTargetBo.builder()
                                .ages(LaunchUnitTargetService.parseIntoIntegerArray(x.getAge()))
                                .customizedAges(LaunchUnitTargetService.parseIntoIntegerArray(x.getAgeCustomize()))
                                .build();
                        bo.setAge(AgeTargetMapper.INSTANCE.toWrapper(age));
                        return bo;
                    }).collect(Collectors.toList());
        }
        throw new IllegalArgumentException("必须指定区域, 性别, 年龄三个参数之一");
    }

    private Map<Integer, BatchUnitWrapperBo> genBatchUnitMap(List<BatchUnitWrapperBo> bos) {
        final Map<Integer, BatchUnitWrapperBo> map = new HashMap<>();
        for (BatchUnitWrapperBo bo : bos) {
            for (Integer unitId : bo.getUnitIds()) {
                map.put(unitId, bo);
            }
        }
        return map;
    }

    private List<Integer> sort(List<Integer> source) {
        return source.stream()
                .sorted()
                .collect(Collectors.toList());
    }

    @Transactional(transactionManager = AD_CORE_TM, rollbackFor = Exception.class)
    public void batchUpdateDualBidTwoStageOptimization(OperatorBo operator, List<UnitUpdateDualBidTwoStageOptimizationBo> list) {
        final Map<Integer, Integer> unitIdMap = list
                .stream()
                .collect(Collectors.toMap(UnitUpdateDualBidTwoStageOptimizationBo::getUnitId, UnitUpdateDualBidTwoStageOptimizationBo::getDualBidTwoStageOptimization));
        final List<LauUnitPo> units = launchUnitV1Service.listUnits(unitIdMap.keySet());
        final Map<Integer, Boolean> supportMap = launchUnitV1Service.supportDualBidTwoStageOptimization(operator.getOperatorId(), units);
        final List<Integer> invalidUnitIds = supportMap
                .entrySet()
                .stream()
                .filter(entry -> Objects.equals(entry.getValue(), Boolean.FALSE))
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
        Assert.isTrue(invalidUnitIds.isEmpty(), "单元需同时满足「oCPC/自定义双出价/特定双目标组合」方可开启两阶段优化，不满足条件单元" + invalidUnitIds);
        launchUnitV1Service.updateUnitDualBidTwoStageOptimization(list);
        final List<OperationContextBo> contextBos = new ArrayList<>();
        units.forEach(unit -> {
            final LauUnitBo oldVersion = LauUnitBo.builder()
                    .unitId(unit.getUnitId())
                    .dualBidTwoStageOptimization(unit.getDualBidTwoStageOptimization())
                    .build();
            final LauUnitBo newVersion = LauUnitBo.builder()
                    .unitId(unit.getUnitId())
                    .dualBidTwoStageOptimization(unitIdMap.get(unit.getUnitId()))
                    .build();
            final List<ChangeBo> changes = operationLogService.changes(oldVersion, newVersion, UnitCompareMeta.META_MAP, MetaType.BASE.getCode(), true);
            final OperationContextBo contextBo = OperatorConverter.MAPPER.toContext(UnitCompareMeta.TABLE_NAME, unit.getUnitId(), OperationType.UPDATE, operator, changes);
            contextBos.add(contextBo);
        });
        operationLogService.save(contextBos);
    }

    /**
     *  这是单元已有字段, 加了字段但是一直没用, 也不知道要校验什么
     *  个人起飞需求使用了(个人起飞用于成本约束)
     */
    public void batchUpdateUnitNoBid(Operator operator, List<BatchUnitWrapperBo> bos) {
        // 分组更新, 降低io次数
        for (Map.Entry<Long, List<BatchUnitWrapperBo>> entry : bos.stream().collect(Collectors.groupingBy(x -> x.getNoBid().getNoBidMax())).entrySet()) {
            final List<Integer> unitIds = entry.getValue()
                    .stream()
                    .map(x -> x.getUnitIds().get(0))
                    .collect(Collectors.toList());
            launchUnitV1Service.batchUpdateNoBid(unitIds, UnitNoBidMapper.INSTANCE.fromWrapper(entry.getValue().get(0).getNoBid()));
        }
    }
}
