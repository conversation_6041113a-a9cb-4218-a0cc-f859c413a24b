package com.bilibili.adp.cpc.wrapper.converters;

import com.bilibili.adp.cpc.core.bos.ArchiveWithCoverBo;
import com.bilibili.adp.cpc.wrapper.bos.ArchiveWithCoverWrapperBo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface ArchiveWithCoverConverter {
    ArchiveWithCoverConverter MAPPER = Mappers.getMapper(ArchiveWithCoverConverter.class);

    ArchiveWithCoverWrapperBo toWrapper(ArchiveWithCoverBo bo);
    List<ArchiveWithCoverWrapperBo> toWrapperList(List<ArchiveWithCoverBo> list);
    ArchiveWithCoverBo toCore(ArchiveWithCoverWrapperBo bo);
    List<ArchiveWithCoverBo> toCoreList(List<ArchiveWithCoverWrapperBo> list);
}
