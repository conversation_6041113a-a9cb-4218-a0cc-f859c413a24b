package com.bilibili.adp.cpc.automatic_rule.converter;

import com.bilibili.adp.cpc.automatic_rule.bos.ActionBo;
import com.bilibili.adp.cpc.automatic_rule.bos.ConditionBo;
import com.bilibili.adp.cpc.automatic_rule.bos.RuleBo;
import com.bilibili.adp.cpc.automatic_rule.bos.RuleDetailBo;
import com.bilibili.adp.cpc.dao.querydsl.pos.LauAutomaticRulePo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface RuleConverter {
    RuleConverter MAPPER = Mappers.getMapper(RuleConverter.class);

    RuleBo po2Bo(LauAutomaticRulePo po, List<String> conditions, List<String> actions, Integer objectType, List<Integer> objectIds);

    @Mapping(target = "dayOfWeek", source = "dayOfWeek")
    @Mapping(target = "fixedTime", source = "fixedTime")
    @Mapping(target = "periodTime", source = "periodTime")
    RuleDetailBo po2DetailBo(LauAutomaticRulePo po,  List<ConditionBo> conditions, List<ActionBo> actions, Integer objectType, List<Integer> objectIds, List<Integer> dayOfWeek, List<String> fixedTime, List<String> periodTime);

    @Mapping(target = "isDeleted", ignore = true)
    @Mapping(target = "ctime", ignore = true)
    @Mapping(target = "mtime", ignore = true)
    @Mapping(target = "dayOfWeek", source = "dayOfWeek")
    @Mapping(target = "fixedTime", source = "fixedTime")
    @Mapping(target = "periodTime", source = "periodTime")
    LauAutomaticRulePo bo2Po(Integer accountId, String dayOfWeek, String fixedTime, String periodTime, RuleDetailBo bo);
}
