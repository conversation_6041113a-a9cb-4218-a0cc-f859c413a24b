package com.bilibili.adp.cpc.splash_screen.operator;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.cpc.compare.ChangeBo;
import com.bilibili.adp.cpc.splash_screen.operation_log.OperationContextBo;
import com.bilibili.adp.cpc.splash_screen.operation_log.OperatorBo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface OperatorConverter {
    OperatorConverter MAPPER = Mappers.getMapper(OperatorConverter.class);
    @Mapping(target = "operatorType", source = "operatorType.code")
    @Mapping(target = "systemType", source = "systemType.code")
    OperatorBo toOperator(Operator operator);

    @Mapping(target = "reAudit", ignore = true)
    OperationContextBo toContext(OperatorBo operator);

    OperationContextBo toContext(String tableName, Integer objId, Integer operationType, OperatorBo operator, List<ChangeBo> changes);
}
