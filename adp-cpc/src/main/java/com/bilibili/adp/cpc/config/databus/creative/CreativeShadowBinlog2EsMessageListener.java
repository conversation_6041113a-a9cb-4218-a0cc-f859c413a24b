package com.bilibili.adp.cpc.config.databus.creative;

import com.alibaba.fastjson.JSONObject;
import com.bilibili.adp.cpc.biz.bos.WarpDatabusProperty;
import com.bilibili.adp.cpc.biz.constants.Constants;
import com.bilibili.adp.cpc.biz.services.creative.CpcCreativeServiceDelegate;
import com.bilibili.adp.cpc.biz.services.creative.bos.CreativeEsBo;
import com.bilibili.adp.cpc.core.LaunchCreativeService;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauUnitCreativePo;
import com.bilibili.adp.cpc.databus.GenCreativeEsPub;
import com.bilibili.adp.cpc.repo.LauUnitCreativeRepo;
import com.bilibili.adp.util.common.AdpCatUtils;
import com.bilibili.warp.databus.AckableMessage;
import com.bilibili.warp.databus.AckableMessages;
import com.bilibili.warp.databus.DatabusProperty;
import com.bilibili.warp.databus.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
@Slf4j
public class CreativeShadowBinlog2EsMessageListener implements MessageListener {

    public static final String CREATIVE_SHADOW_BINLOG = "creative-shadow-binlog";

    private final String topic;
    private final String group;

    @Autowired
    private Creative2EsService creative2EsService;

    @Autowired
    private LaunchCreativeService launchCreativeService;

    @Autowired
    private GenCreativeEsPub esPub;

    public CreativeShadowBinlog2EsMessageListener(WarpDatabusProperty warpDatabusProperty) {
        // 注入DatabusProperties，此处的ALIAS常量即配置文件中填写的别名，是topic的上一级配置键
        // pub场景同理，在使用DatabusTemplate的地方注入DatabusProperties来获取
        Map<String, DatabusProperty> properties = warpDatabusProperty.getProperties();
        DatabusProperty property = properties.get(CREATIVE_SHADOW_BINLOG);
        log.info("CreativeShadowBinlog2EsMessageListener, property={}", JSONObject.toJSONString(property));
        this.topic = Objects.isNull(property) ? "" : property.getTopic();
        this.group = Objects.isNull(property) ? "" : property.getSub().getGroup();
    }

    @Override
    public String topic() {
        return topic;
    }

    @Override
    public String group() {
        return group;
    }

    @Override
    public boolean autoCommit() {
        return false;
    }

    @Override
    public void onMessages(AckableMessages messages) {
        List<AckableMessage> messagess = messages.getMessages();
        try {
            AdpCatUtils.newTransaction(Constants.CAT_TYPE_DATABUS, CREATIVE_SHADOW_BINLOG + ":batchSub", transaction -> {
                List<String> values = messagess.stream().map(x -> new String(x.payload())).collect(Collectors.toList());
                handleMsgs(values);
                messages.getMessages().forEach(AckableMessage::ack);
            });
        } catch (Exception e) {
            log.error("CreativeBinlogSubTask error", e);
            messages.getMessages().forEach(AckableMessage::nack);
        }

        messages.ack();
    }

    private void handleMsgs(List<String> values) {
        List<Integer> creativeIds = values.stream()
                .map(x -> {
                    JSONObject jsonObject = JSONObject.parseObject(x);
                    String action = jsonObject.getString("action");
                    if (StringUtils.isEmpty(action)) {
                        return null;
                    }

                    JSONObject newObject = jsonObject.getJSONObject("new");
                    return null == newObject ? null : newObject.getInteger("creative_id");
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(creativeIds)) {

            List<LauUnitCreativePo> creativePos = launchCreativeService.listCreatives(creativeIds);
            pub2Es(creativePos);
        }
    }

    private List<CreativeEsBo> pub2Es(List<LauUnitCreativePo> creativePos) {
        if (CollectionUtils.isEmpty(creativePos)) {
            return Collections.emptyList();
        }
        // 推送生成创意 es，index别名: adp-creative
        List<CreativeEsBo> creativeEsBos = creative2EsService.decorateEsPo(creativePos);
        if (CollectionUtils.isEmpty(creativeEsBos)) {
            return Collections.emptyList();
        }

        esPub.batchPub(creativeEsBos);
        return creativeEsBos;
    }
}
