package com.bilibili.adp.cpc.splash_screen.creative.converter;

import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauCreativeExtraPo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;
@Mapper
public interface ExtraConverter {
    ExtraConverter MAPPER = Mappers.getMapper(ExtraConverter.class);
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "imageMacro", ignore = true)
    @Mapping(target = "imageMacroType", ignore = true)
    @Mapping(target = "isDeleted", ignore = true)
    @Mapping(target = "ctime", ignore = true)
    @Mapping(target = "mtime", ignore = true)
    LauCreativeExtraPo po(Integer accountId, Integer campaignId, Integer unitId, Integer creativeId, Integer qualificationPackageId);
}
