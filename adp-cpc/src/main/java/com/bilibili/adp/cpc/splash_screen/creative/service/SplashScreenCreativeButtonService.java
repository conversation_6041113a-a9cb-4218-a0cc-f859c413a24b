package com.bilibili.adp.cpc.splash_screen.creative.service;

import com.bilibili.adp.cpc.core.constants.AdType;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauSplashScreenCreativeButtonPo;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauSplashScreenCreativeButtonSlideInfoPo;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauSplashScreenCreativeButtonTwistInfoPo;
import com.bilibili.adp.cpc.splash_screen.config.SplashScreenCreativeConfig;
import com.bilibili.adp.cpc.splash_screen.creative.bos.SplashScreenButton;
import com.bilibili.adp.cpc.splash_screen.creative.bos.SplashScreenCreative;
import com.bilibili.adp.cpc.splash_screen.creative.converter.ButtonConverter;
import com.bilibili.adp.cpc.splash_screen.creative.enums.ButtonStyle;
import com.bilibili.adp.cpc.splash_screen.creative.enums.InteractType;
import com.bilibili.bjcom.querydsl.BaseQueryFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

import static com.bilibili.adp.cpc.dao.ad_core.querydsl.QLauCampaign.lauCampaign;
import static com.bilibili.adp.cpc.dao.config.PlatformMySqlConfig.AD_CORE_BQF;
import static com.bilibili.adp.cpc.dao.config.PlatformMySqlConfig.AD_CORE_TM;
import static com.bilibili.adp.cpc.dao.ad_core.querydsl.QLauSplashScreenCreativeButton.lauSplashScreenCreativeButton;
import static com.bilibili.adp.cpc.dao.ad_core.querydsl.QLauSplashScreenCreativeButtonSlideInfo.lauSplashScreenCreativeButtonSlideInfo;
import static com.bilibili.adp.cpc.dao.ad_core.querydsl.QLauSplashScreenCreativeButtonTwistInfo.lauSplashScreenCreativeButtonTwistInfo;
import static com.bilibili.adp.cpc.dao.ad_core.querydsl.QLauUnitCreative.lauUnitCreative;

@Slf4j
@Service
@RequiredArgsConstructor
public class SplashScreenCreativeButtonService {
    @Resource(name = AD_CORE_BQF)
    private BaseQueryFactory adCoreBqf;

    private final SplashScreenCreativeConfig splashScreenCreativeConfig;

    public List<LauSplashScreenCreativeButtonPo> list(Integer creativeId) {
        return adCoreBqf.selectFrom(lauSplashScreenCreativeButton)
                .where(lauSplashScreenCreativeButton.creativeId.eq(creativeId))
                .where(lauSplashScreenCreativeButton.isDeleted.eq(0))
                .fetch();
    }

    public List<LauSplashScreenCreativeButtonPo> list(Collection<Integer> creativeIds) {
        return adCoreBqf.selectFrom(lauSplashScreenCreativeButton)
                .where(lauSplashScreenCreativeButton.creativeId.in(creativeIds))
                .where(lauSplashScreenCreativeButton.isDeleted.eq(0))
                .fetch();
    }

    @Transactional(transactionManager = AD_CORE_TM, rollbackFor = Exception.class)
    public void save(SplashScreenCreative splashScreenCreative) {
        final InteractType interactTypeEnum = InteractType.getByCode(splashScreenCreative.getInteractType());
        switch (interactTypeEnum) {
            case TWIST:
                saveTwistButtons(splashScreenCreative);
                break;
            case SLIDE:
                saveSlideButtons(splashScreenCreative);
                break;
            case CLICK:
                saveClickButtons(splashScreenCreative);
            default:
                break;
        }
    }

    /**
     * 扭一扭 具体实现为3个按钮
     * InteractStyle.TWIST + InteractStyle.WORD + InteractStyle.WORD
     * 参考 select * from ssa_splash_screen_button where splash_screen_id  = 19990 and platform = 1 and is_deleted = 0
     */
    private void saveTwistButtons(SplashScreenCreative splashScreenCreative) {
        final Integer creativeId = splashScreenCreative.getCreativeId();
        final List<LauSplashScreenCreativeButtonPo> buttons = list(creativeId);
        if (!CollectionUtils.isEmpty(buttons)) {
            adCoreBqf.delete(lauSplashScreenCreativeButton).where(lauSplashScreenCreativeButton.creativeId.eq(creativeId)).execute();
            adCoreBqf.delete(lauSplashScreenCreativeButtonTwistInfo).where(lauSplashScreenCreativeButtonTwistInfo.creativeId.eq(creativeId)).execute();
        }
        final SplashScreenButton firstButton = SplashScreenButton.builder()
                .jumpGuideContent("点击进入活动页")
                .schemaGuideContent("点击进入活动页")
                .jumpImageUrl(splashScreenCreative.getJumpImageUrl())
                .jumpImageMd5(splashScreenCreative.getJumpImageMd5())
                .schemaImageUrl(splashScreenCreative.getSchemaImageUrl())
                .schemaImageMd5(splashScreenCreative.getSchemaImageMd5())
                .build();
        final LauSplashScreenCreativeButtonPo firstTwistButton = ButtonConverter.MAPPER.composeButton(creativeId, ButtonStyle.TWIST.get(0), firstButton, "");
        final long firstTwistButtonId = adCoreBqf.insert(lauSplashScreenCreativeButton).insertGetKey(firstTwistButton);
        final SplashScreenButton secondButton = SplashScreenButton.builder()
                .jumpGuideContent("扭一扭")
                .schemaGuideContent("扭一扭")
                .build();
        final LauSplashScreenCreativeButtonPo secondTwistButton = ButtonConverter.MAPPER.composeButton(creativeId, ButtonStyle.TWIST.get(1), secondButton, String.valueOf(firstTwistButtonId));
        final SplashScreenButton thirdButton = SplashScreenButton.builder()
                .jumpGuideContent(splashScreenCreative.getJumpGuideContent())
                .schemaGuideContent(splashScreenCreative.getSchemaGuideContent())
                .build();
        final LauSplashScreenCreativeButtonPo thirdTwistButton = ButtonConverter.MAPPER.composeButton(creativeId, ButtonStyle.TWIST.get(2), thirdButton, String.valueOf(firstTwistButtonId));
        adCoreBqf.insert(lauSplashScreenCreativeButton).insertBeans(Arrays.asList(secondTwistButton, thirdTwistButton));
        final LauSplashScreenCreativeButtonTwistInfoPo twistInfo = generateTwistInfo(Math.toIntExact(creativeId), firstTwistButtonId);
        adCoreBqf.insert(lauSplashScreenCreativeButtonTwistInfo).insertBean(twistInfo);
    }

    private LauSplashScreenCreativeButtonTwistInfoPo generateTwistInfo(Integer creativeId, long buttonId) {
        LauSplashScreenCreativeButtonTwistInfoPo twistInfo = new LauSplashScreenCreativeButtonTwistInfoPo();
        twistInfo.setCreativeId(creativeId);
        twistInfo.setButtonId(buttonId);
        twistInfo.setTwistAngle(90F);
        twistInfo.setTwistSpeed(-1F);
        return twistInfo;
    }

    /**
     * 滑动 具体实现为2个按钮
     * InteractStyle.SLIDE + InteractStyle.WORD
     */
    private void saveSlideButtons(SplashScreenCreative splashScreenCreative) {
        final Integer creativeId = splashScreenCreative.getCreativeId();
        final List<LauSplashScreenCreativeButtonPo> buttons = list(creativeId);
        if (!CollectionUtils.isEmpty(buttons)) {
            adCoreBqf.delete(lauSplashScreenCreativeButton).where(lauSplashScreenCreativeButton.creativeId.eq(creativeId)).execute();
            adCoreBqf.delete(lauSplashScreenCreativeButtonSlideInfo).where(lauSplashScreenCreativeButtonSlideInfo.creativeId.eq(creativeId)).execute();
        }
        final SplashScreenButton firstButton = SplashScreenButton.builder()
                .jumpGuideContent("点击进入活动页")
                .schemaGuideContent("点击进入活动页")
                .jumpImageUrl(splashScreenCreative.getJumpImageUrl())
                .jumpImageMd5(splashScreenCreative.getJumpImageMd5())
                .schemaImageUrl(splashScreenCreative.getSchemaImageUrl())
                .schemaImageMd5(splashScreenCreative.getSchemaImageMd5())
                .build();
        final LauSplashScreenCreativeButtonPo firstSlideButton = ButtonConverter.MAPPER.composeButton(creativeId, ButtonStyle.SLIDE.get(0), firstButton,"");
        final long firstSlideButtonId = adCoreBqf.insert(lauSplashScreenCreativeButton).insertGetKey(firstSlideButton);
        final SplashScreenButton secondButton = SplashScreenButton.builder()
                .jumpGuideContent(splashScreenCreative.getJumpGuideContent())
                .schemaGuideContent(splashScreenCreative.getSchemaGuideContent())
                .build();
        final LauSplashScreenCreativeButtonPo secondSlideButton = ButtonConverter.MAPPER.composeButton(creativeId, ButtonStyle.SLIDE.get(1), secondButton, String.valueOf(firstSlideButtonId));
        adCoreBqf.insert(lauSplashScreenCreativeButton).insertBean(secondSlideButton);
        final LauSplashScreenCreativeButtonSlideInfoPo slideInfo = generateSlideInfo(creativeId, firstSlideButtonId);
        adCoreBqf.insert(lauSplashScreenCreativeButtonSlideInfo).insertBean(slideInfo);
    }

    private void saveClickButtons(SplashScreenCreative splashScreenCreative) {
        final Integer creativeId = splashScreenCreative.getCreativeId();
        final List<LauSplashScreenCreativeButtonPo> buttons = list(creativeId);
        if (!CollectionUtils.isEmpty(buttons)) {
            adCoreBqf.delete(lauSplashScreenCreativeButton).where(lauSplashScreenCreativeButton.creativeId.eq(creativeId)).execute();
        }

        final SplashScreenButton firstButton = SplashScreenButton.builder()
                .jumpGuideContent(StringUtils.hasText(splashScreenCreative.getJumpGuideContent()) ? splashScreenCreative.getJumpGuideContent() : "点击进入直播间")
                .schemaGuideContent(StringUtils.hasText(splashScreenCreative.getJumpGuideContent()) ? splashScreenCreative.getJumpGuideContent() : "点击进入直播间")
                .jumpImageUrl(splashScreenCreative.getJumpImageUrl())
                .jumpImageMd5(splashScreenCreative.getJumpImageMd5())
                .build();

        final LauSplashScreenCreativeButtonPo firstClickButton = ButtonConverter.MAPPER.composeButton(creativeId, ButtonStyle.CLICK.get(0), firstButton,"");
        adCoreBqf.insert(lauSplashScreenCreativeButton).insertBean(firstClickButton);
    }

    private LauSplashScreenCreativeButtonSlideInfoPo generateSlideInfo(Integer creativeId, long buttonId) {
        LauSplashScreenCreativeButtonSlideInfoPo slideInfo = new LauSplashScreenCreativeButtonSlideInfoPo();
        slideInfo.setCreativeId(creativeId);
        slideInfo.setButtonId(buttonId);
        //brand portal的配置
        //@Value("${ssa.slide.angel:60}")
        slideInfo.setSlideAngle(90);
        //com.bilibili.bce.index.biz.service.impl.BceSplashServiceImpl#chooseInteractDistance
        //bce 抄来的 都是90
        slideInfo.setSlideDistance(90);
        return slideInfo;
    }

    /**
     * for backdoor
     * @param accountIds 账号列表
     * @param twistAngle 扭一扭角度
     * @return 更新行数
     */
    @Transactional(value = AD_CORE_TM, rollbackFor = Exception.class)
    public long modifyTwistAngle(String operatorName, List<Integer> accountIds, Integer twistAngle) {
        final boolean validOperator = splashScreenCreativeConfig
                .getModifyTwistAngleOperators()
                .stream()
                .anyMatch(operator -> Objects.equals(operator.getOperatorName(), operatorName));
        Assert.isTrue(validOperator, "无操作权限");
        Assert.isTrue(!accountIds.isEmpty() && accountIds.size() <= 10, "账户ID大于10个，请分批输入");
        Assert.isTrue(twistAngle >= 42 && twistAngle <= 120, "配置角度输入有误，请输入42-120之间的整数");
        final List<Integer> campaignIds = adCoreBqf.select(lauCampaign.campaignId)
                .from(lauCampaign)
                .where(lauCampaign.accountId.in(accountIds))
                .where(lauCampaign.adType.eq(AdType.SPLASH_SCREEN))
                .fetch();
        if (CollectionUtils.isEmpty(campaignIds)) {
            return 0L;
        }
        final List<Integer> creativeIds = adCoreBqf.select(lauUnitCreative.creativeId)
                .from(lauUnitCreative)
                .where(lauUnitCreative.accountId.in(accountIds))
                .where(lauUnitCreative.campaignId.in(campaignIds))
                .fetch();
        if (CollectionUtils.isEmpty(creativeIds)) {
            return 0L;
        }
        return adCoreBqf.update(lauSplashScreenCreativeButtonTwistInfo)
                .set(lauSplashScreenCreativeButtonTwistInfo.twistAngle, Float.valueOf(twistAngle))
                .where(lauSplashScreenCreativeButtonTwistInfo.creativeId.in(creativeIds))
                .execute();
    }
}
