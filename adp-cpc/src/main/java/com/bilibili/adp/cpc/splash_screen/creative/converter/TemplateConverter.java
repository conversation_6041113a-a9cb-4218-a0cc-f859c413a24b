package com.bilibili.adp.cpc.splash_screen.creative.converter;

import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauCreativeTemplatePo;
import com.bilibili.adp.cpc.splash_screen.creative.bos.SplashScreenTemplate;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper
public interface TemplateConverter {
    TemplateConverter MAPPER = Mappers.getMapper(TemplateConverter.class);

    SplashScreenTemplate creative2Template(Integer accountId, Integer campaignId, Integer unitId, Integer creativeId, Integer templateId);

    @Mapping(target = "slotGroupId", ignore = true)
    @Mapping(target = "busMarkId", ignore = true)
    @Mapping(target = "reservedPrice", ignore = true)
    @Mapping(target = "reserveRuleId", ignore = true)
    @Mapping(target = "mtime", ignore = true)
    @Mapping(target = "isDeleted", ignore = true)
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "ctime", ignore = true)
    @Mapping(target = "creativeStyle", ignore = true)
    @Mapping(target = "bizStatus", ignore = true)
    LauCreativeTemplatePo bo2Po(SplashScreenTemplate creativeTemplate);
}
