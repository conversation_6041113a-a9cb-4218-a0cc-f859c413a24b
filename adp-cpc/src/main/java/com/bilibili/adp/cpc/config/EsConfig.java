package com.bilibili.adp.cpc.config;

import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;

/**
 * <AUTHOR>
 * @date 2023/6/14 23:16
 */
@Configuration
public class EsConfig {

    /**
     * 这是应为data-elasticsearch中与Netty之间产生冲突
     */
    @PostConstruct
    void init() {
        System.setProperty("es.set.netty.runtime.available.processors", "false");
    }
}
