package com.bilibili.adp.cpc.compare;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChangeBo {
    private String key;
    private String desc;
    private Object oldValue;
    private Object newValue;
    @JsonIgnore
    private boolean reAudit;
}
