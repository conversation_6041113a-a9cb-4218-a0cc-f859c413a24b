package com.bilibili.adp.cpc.biz.services.creative;

import com.alibaba.fastjson.JSON;
import com.bapis.ad.mgk.ValidateAndGetPageReply;
import com.bapis.archive.service.Arc;
import com.bilibili.adp.account.dto.AccountBaseDto;
import com.bilibili.adp.account.service.IQueryAccountService;
import com.bilibili.adp.common.Constants;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.enums.AuditStatus;
import com.bilibili.adp.common.enums.*;
import com.bilibili.adp.common.enums.fly.FlyIsSetInviteLinkEnum;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.AssertCheckUtils;
import com.bilibili.adp.common.util.CollectionHelper;
import com.bilibili.adp.common.util.ObjectUtils;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.common.util.func.Functions;
import com.bilibili.adp.cpc.biz.bos.creative.CreativeFlyExtInfoBo;
import com.bilibili.adp.cpc.biz.bos.creative.ShadowCreativeBo;
import com.bilibili.adp.cpc.biz.converter.creative.CreativeImageConverter;
import com.bilibili.adp.cpc.biz.converter.creative.ShadowCreativeConverter;
import com.bilibili.adp.cpc.biz.services.campaign.FlyCampaignService;
import com.bilibili.adp.cpc.biz.services.creative.bos.JumpInfoBo;
import com.bilibili.adp.cpc.biz.services.creative.config.CreativePositionConfig;
import com.bilibili.adp.cpc.biz.services.creative.config.FlyBusMarkConfig;
import com.bilibili.adp.cpc.biz.services.creative.dto.CpcCreativeDto;
import com.bilibili.adp.cpc.biz.services.creative.dynamic.AbstractFlyDynamicDarkLaunchProc;
import com.bilibili.adp.cpc.biz.services.creative.dynamic.FlyDynamicDarkLaunchProcFactory;
import com.bilibili.adp.cpc.biz.services.creative.native_ad.NativeAdJudger;
import com.bilibili.adp.cpc.biz.services.creative.native_ad.NativeService;
import com.bilibili.adp.cpc.biz.services.dynamic.LauDynamicService;
import com.bilibili.adp.cpc.biz.services.hidden.HiddenTemplateConverter;
import com.bilibili.adp.cpc.biz.services.hidden.HiddenTemplateService;
import com.bilibili.adp.cpc.biz.services.material.AdpCpcLauMaterialBiliVideoService;
import com.bilibili.adp.cpc.biz.services.style.AdpCpcBusMarkService;
import com.bilibili.adp.cpc.biz.services.unit.CpcUnitServiceDelegate;
import com.bilibili.adp.cpc.biz.services.unit.FlyAccountUnitJudger;
import com.bilibili.adp.cpc.biz.services.unit.FlyUnitService;
import com.bilibili.adp.cpc.biz.services.unit.MiddleFlyUnitService;
import com.bilibili.adp.cpc.biz.services.unit.api.ICpcUnitService;
import com.bilibili.adp.cpc.biz.services.unit.dto.CpcUnitDto;
import com.bilibili.adp.cpc.biz.validator.CpcCreativeValidator;
import com.bilibili.adp.cpc.config.flypro.SearchPositionConfig;
import com.bilibili.adp.cpc.core.*;
import com.bilibili.adp.cpc.core.bos.CreativeImageBo;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauCampaignPo;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauCreativeNativeArchiveRelativityPo;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauShadowCreativePo;
import com.bilibili.adp.cpc.dto.CpcCreativeDetailDto;
import com.bilibili.adp.cpc.enums.AdpVersion;
import com.bilibili.adp.cpc.enums.UserType;
import com.bilibili.adp.cpc.enums.*;
import com.bilibili.adp.cpc.enums.ad.LaunchTargetEnum;
import com.bilibili.adp.cpc.enums.ad.OcpcTargetEnum;
import com.bilibili.adp.cpc.enums.ad.PromotionPurposeType;
import com.bilibili.adp.cpc.po.ad.LauUnitFlyMiddleInfoPo;
import com.bilibili.adp.cpc.proxy.ArchiveServiceProxy;
import com.bilibili.adp.cpc.repo.*;
import com.bilibili.adp.cpc.utils.HttpUtils;
import com.bilibili.adp.launch.api.common.LaunchStatus;
import com.bilibili.adp.launch.api.common.*;
import com.bilibili.adp.launch.api.creative.dto.*;
import com.bilibili.adp.launch.api.flyPro.dto.enums.CoverTypeEnum;
import com.bilibili.adp.launch.api.flyPro.dto.enums.DynamicTypeEnum;
import com.bilibili.adp.launch.api.flyPro.dto.enums.RoomPromoteScenesEnum;
import com.bilibili.adp.launch.api.flyPro.dto.enums.ScenesEnum;
import com.bilibili.adp.launch.api.flyPro.dto.v2.*;
import com.bilibili.adp.launch.api.launch.dto.GetBidCostParam;
import com.bilibili.adp.launch.api.service.ICreativeCommonService;
import com.bilibili.adp.launch.api.service.ILauQualificationService;
import com.bilibili.adp.launch.biz.ad_core.mybatis.LauCreativeFlyExtInfoDao;
import com.bilibili.adp.launch.biz.ad_core.mybatis.LauUnitCreativeDao;
import com.bilibili.adp.launch.biz.ad_core.querydsl.dos.LauCreativeTemplateDo;
import com.bilibili.adp.launch.biz.common.LaunchUtil;
import com.bilibili.adp.launch.biz.config.CreativeTemplateConfig;
import com.bilibili.adp.launch.biz.exception.LaunchExceptionCode;
import com.bilibili.adp.launch.biz.fly.FlyArchiveService;
import com.bilibili.adp.launch.biz.lau_dao.LauUnitFlyExtInfoDao;
import com.bilibili.adp.launch.biz.pojo.*;
import com.bilibili.adp.launch.biz.service.CreativeTrackadfProc;
import com.bilibili.adp.launch.biz.service.component.LaunchComponentService;
import com.bilibili.adp.passport.api.dto.ArchiveDetail;
import com.bilibili.adp.passport.api.enums.ReplySubjectMonitorEnum;
import com.bilibili.adp.passport.api.enums.ReplySubjectStateEnum;
import com.bilibili.adp.passport.biz.manager.ArchiveManager;
import com.bilibili.adp.resource.api.account.label.IAccountLabelService;
import com.bilibili.adp.resource.api.bus_mark_rule.IBusMarkRuleService;
import com.bilibili.adp.resource.api.cm.mark.CmMarkDto;
import com.bilibili.adp.resource.api.cm.mark.ICmMarkService;
import com.bilibili.adp.resource.api.common.BusMarkRuleAdSysEnum;
import com.bilibili.adp.resource.api.slot_group.IResSlotGroupService;
import com.bilibili.adp.resource.api.slot_group.QueryTemplateLaunchTypeMappingDto;
import com.bilibili.adp.resource.api.slot_group.ResSlotGroupBaseDto;
import com.bilibili.adp.resource.api.slot_group.ResSlotGroupTemplateMappingDto;
import com.bilibili.bjcom.querydsl.BaseQueryFactory;
import com.bilibili.crm.platform.common.IsValid;
import com.bilibili.location.api.bus_mark.dto.BusMarkDto;
import com.bilibili.location.api.cardtype.dto.CreativeStyle;
import com.bilibili.location.api.service.ITemplateService;
import com.bilibili.location.api.service.query.IQueryTemplateService;
import com.bilibili.location.api.template.dto.ButtonCopyDto;
import com.bilibili.location.api.template.dto.TemplateDto;
import com.bilibili.location.common.CmMarkEnum;
import com.bilibili.mgk.platform.common.MgkConstants;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.querydsl.core.types.Projections;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.bilibili.adp.common.Constants.BIZ_STATUS_OK;
import static com.bilibili.adp.common.Constants.BIZ_STATUS_UNDER_RESERVED_PRICE;
import static com.bilibili.adp.cpc.dao.ad_core.querydsl.QLauCreativeTemplate.lauCreativeTemplate;
import static com.bilibili.adp.cpc.dao.ad_core.querydsl.QLauUnitCreative.lauUnitCreative;
import static com.bilibili.adp.cpc.dao.config.PlatformMySqlConfig.AD_CORE_BQF;
import static com.bilibili.adp.cpc.dao.config.PlatformMySqlConfig.AD_CORE_TM;

/**
 * copy class
 *
 * @see com.bilibili.adp.launch.biz.service.DoFlyCreativeService
 */
@Primary
@Service(value = "LaunchDoFlyCreativeService")
@Slf4j
@RequiredArgsConstructor
public class DoFlyCreativeService {
    private static final Logger LOGGER = LoggerFactory.getLogger(DoFlyCreativeService.class);
    @Autowired
    private ICmMarkService cmMarkService;
    @Autowired
    private IBusMarkRuleService busMarkRuleService;
    @Autowired
    private IQueryTemplateService queryTemplateService;

    @Value("${up.cm.mark:2}")
    private int ARCHIVE_CM_MARK = 2;
    @Value("${up.ad.tag:UP主自荐}")
    private String ARCHIVE_AD_MARK;
    private static final int ARCHIVE_FIRST_CATEGORY = 10;
    private static final int ARCHIVE_SECOND_CATEGORY = 95;
    private static final int ARCHIVE_THIRD_CATEGORY = 0;

    @Autowired
    private IResSlotGroupService resSlotGroupService;

    @Value("${lau.creative.landing.page.style:www.bilibili.com/video/av}")
    public String landingPageStyle;
    @Autowired
    private CpcCreativeValidator validator;
    @Autowired
    private CpcUnitServiceDelegate unitDelegate;
    @Autowired
    private FlyCampaignService flyCampaignService;
    @Autowired
    private IQueryAccountService queryAccountService;
    @Autowired
    private CpcSaveCreativeService cpcSaveCreativeService;
    @Autowired
    private ManagedCampaignJobRepo managedCampaignJobRepo;
    @Autowired
    private LauCreativeExtraProc lauCreativeExtraProc;
    @Autowired
    private LauCreativeExtraService lauCreativeExtraService;
    @Autowired
    protected ICreativeCommonService creativeCommonService;
    @Autowired
    private LauUnitCreativeDao lauUnitCreativeDao;
    @Autowired
    private ICpcUnitService cpcUnitService;
    @Resource
    private AdpCpcLauMaterialBiliVideoService biliVideoService;

    @Resource(name = AD_CORE_BQF)
    private BaseQueryFactory adCoreBqf;
    @Autowired
    private LauCreativeFlyExtInfoDao lauCreativeFlyExtInfoDao;
    @Autowired
    private CpcCreativeValidator cpcCreativeValidator;
    @Autowired
    private ArchiveManager archiveManager;
    @Autowired
    private IAccountLabelService accountLabelService;
    @Autowired
    private CpcCreativeServiceDelegate creativeServiceDelegate;
    @Autowired
    private DoFlyCreativePositionService doFlyCreativePositionService;
    @Autowired
    private NativeAdJudger nativeAdJudger;
    @Autowired
    private LauCreativeNativeArchiveRelativityRepo lauCreativeNativeArchiveRelativityRepo;

    @Value("${fly.under.box.msg.info.slot.group.id:349}")
    private Integer flyUnderBoxMsgInfoSlotGroupId;
    @Value("${fly.under.box.play.page.slot.group.id:350}")
    private Integer flyUnderBoxPlayPageSlotGroupId;
    @Value("${fly.under.box.msg.info.gif.slot.group.id:351}")
    private Integer flyUnderBoxMsgInfoGifSlotGroupId;
    @Autowired
    private CreativePositionConfig creativePositionConfig;
    @Autowired
    private FlyUnitService flyUnitService;
    @Autowired
    private ArchiveServiceProxy archiveServiceProxy;
    @Autowired
    private FlyArchiveService flyArchiveService;
    @Autowired
    private ITemplateService templateService;
    @Autowired
    private MiddleFlyUnitService middleFlyUnitService;
    @Autowired
    private LauUnitFlyExtInfoDao lauUnitFlyExtInfoDao;
    @Autowired
    private CreativeTrackadfProc creativeTrackadfProc;
    @Autowired
    private NewAccAccountRepo newAccAccountRepo;
    @Autowired
    private LaunchComponentService launchComponentService;
    @Autowired
    private CreativeTemplateConfig creativeTemplateConfig;
    @Autowired
    private LaunchCreativeService launchCreativeService;
    @Autowired
    private LaunchCreativeImageService launchCreativeImageService;
    @Autowired
    private LaunchJumpUrlService launchJumpUrlService;
    @Autowired
    private LaunchCreativeFlyExtInfoService launchCreativeFlyExtInfoService;
    @Autowired
    private LaunchShadowCreativeService launchShadowCreativeService;
    @Autowired
    private FlyCreativeService flyCreativeService;
    @Autowired
    private NativeService nativeService;
    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private FlyBusMarkConfig flyBusMarkConfig;
    @Autowired
    private OuterLauUnitRepo outerLauUnitRepo;
    @Autowired
    private OuterLauCampaignRepo outerLauCampaignRepo;
    @Autowired
    private FlyAccountUnitJudger flyUnitJudger;
    @Resource
    private ILauQualificationService lauQualificationService;
    @Autowired
    private AdpCpcBusMarkService adpCpcBusMarkService;
    private final FlyDynamicDarkLaunchProcFactory flyDynamicDarkLaunchProcFactory;
    @Autowired
    private LaunchUnitArchiveService launchUnitArchiveService;
    @Autowired
    private LauDynamicService lauDynamicService;

    @Value("#{'${no.bus.mark.account.ids:6907,7226,6969}'.split(',')}")
    private List<Integer> noBusMarkAccountIds = Collections.emptyList();
    @Value("${no.bus.mark.id:9}")
    //不展示标，生产环境，默认是9（不展示标）
    private Integer noBusMarkId;
    @Value("${game.bus.mark.id:4}")
    //游戏业务标，生产环境，默认是4（游戏标）
    private Integer gameBusMarkId;
    @Value("${fly.bus.mark.id:54}")
    //起飞推广标，生产环境，默认是54（起飞-内容推广标-新）
    private Integer flyBusMarkId;
    @Value("${fly.live.bus.mark.id:36}")
    //起飞直播间推广标，生产环境，默认是36（直播标-feeds）
    private Integer flyLiveBusMarkId;
    /**
     * 创作推广标
     */
    @Value("${fly.live.creationPromotion.id:54}")
    private Integer flyCreationPromotionMarkId;

    /**
     * story资源位商业标
     */
    @Value("${fly.story.mark.id:94}")
    private Integer storyBusMarkId;

    /**
     * 起飞火箭标
     */
    @Value("${fly.rocket.mark.id:123}")
    private Integer rocketBusMarkId;

    /**
     * 课堂商业标
     */
    @Value("${fly.season.mark.id:87}")
    private Integer seasonBusMarkId;

    /**
     * up主推荐广告商业标
     */
    @Value("${up.recommend.mark.id:79}")
    private Integer upRecommendBusMarkId;


    /**
     * pc首页信息栏标签
     */
    @Value("${fly.pc.index.mark.id:102}")
    private Integer pcIndexBusMarkId;

    /**
     * pc播放页标签
     */
    @Value("${fly.pc.play.mark.id:102}")
    private Integer pcPlayBusMarkId;

    @Value("${fly.pc.app.index.mark.id:102}")
    private Integer pcAppIndexBusMarkId;

    /**
     * pc播放页标签
     */
    @Value("${fly.pc.app.play.mark.id:102}")
    private Integer pcAppPlayBusMarkId;

    @Value("${fly.tv.mark.id:105}")
    private Integer tvBusMarkId;

    /**
     * 活动标
     */
    @Value("${fly.activity.mark.id:167}")
    private Integer activityBusMarkId;

    /**
     * 小卡模版
     */
    @Value("#{'${new.fly.small.card.template.ids:334,523}'.split(',')}")
    private List<Integer> smallCardTemplateIds;

    /**
     * 播放页模版
     */
    @Value("#{'${fly.play.template.ids:335}'.split(',')}")
    private List<Integer> playTemplateIds;

    /**
     * 大卡inline模版
     */
    @Value("#{'${fly.big.card.template.ids:272}'.split(',')}")
    private List<Integer> bigCardTemplateIds;

    /**
     * iPAD模版
     */
    @Value("#{'${fly.ipad.card.template.ids:274}'.split(',')}")
    private List<Integer> iPADCardTemplateIds;

    /**
     * 动态inline模版
     */
    @Value("#{'${fly.dynamic.inline.card.template.ids:270}'.split(',')}")
    private List<Integer> dynamicInlineCardTemplateIds;

    /**
     * story资源位商业标
     */
    @Value("#{'${fly.story.template.ids:365}'.split(',')}")
    private List<Integer> storyTemplateIds;

    /**
     * pc首页推广栏商业标
     */
    @Value("#{'${fly.pc.index.template.ids:381}'.split(',')}")
    private List<Integer> pcIndexTemplateIds;

    /**
     * pc播放详情页商业标
     */
    @Value("#{'${fly.pc.play.template.ids:382}'.split(',')}")
    private List<Integer> pcPlayTemplateIds;

    @Value("#{'${fly.pc.app.index.template.ids:637}'.split(',')}")
    private List<Integer> pcAppIndexTemplateIds;

    /**
     * pc播放详情页商业标
     */
    @Value("#{'${fly.pc.app.play.template.ids:638}'.split(',')}")
    private List<Integer> pcAppPlayTemplateIds;

    @Value("#{'${fly.tv.template.ids:402,496}'.split(',')}")
    private List<Integer> tvTemplateIds;

    @Value("#{'${fly.search.template.ids:415,427}'.split(',')}")
    private List<Integer> searchTemplateIds;

    @Value("#{'${fly.pc.search.template.ids:457,459}'.split(',')}")
    private List<Integer> pcSearchTemplateIds;

    @Value("#{'${fly.choose.search.template.ids:475}'.split(',')}")
    private List<Integer> chooseSearchTemplateIds;

    @Value("#{'${fly.ipad.search.template.ids:579}'.split(',')}")
    private List<Integer> iPadSearchTemplateIds;

    @Value("${fly.reserve.button.copy.id:56}")
    private Integer flyReserveButtonCopyId;

    /**
     * 框下模板id
     */
    @Value("#{'${fly.under.box.template.ids:354}'.split(',')}")
    private List<Integer> underBoxTemplateIds;
    @Value("${effect.ad.shadow.creative.label.id:504}")
    private Integer effectAdShadowCreativeLabelId;
    @Value("${personal.fly.story.template.id:587}")
    private Integer personalFlyStoryTemplateId;

    /**
     * 内容起飞搜索配置项
     */
    @Autowired
    SearchPositionConfig searchPositionConfig;
    @Autowired
    private HiddenTemplateService hiddenTemplateService;

    @Deprecated
    @Transactional(value = AD_CORE_TM, rollbackFor = Exception.class)
    public List<Integer> saveFlyBanner4(Operator operator, List<CpcCreativeDetailDto> saveCreativeDtos, boolean deleteOtherInUnit,
                                        AccountBaseDto account, FlyCampaignDetailDto flyCampaignDetailDto, CpcUnitDto unit) throws ServiceException {

        FlyProUnitPositionsDto flyProUnitPositionsDto = null;
        // 托管2有 4:3，专业起飞不管他
        if (Utils.isPositive(unit.getIsManaged()) && ManagedVersion.NEW_2.getKey().equals(flyCampaignDetailDto.getManagedVersion())) {
            // 单元下三种类型创意规格(16_10, 16_9, 4_3) 的 Map<广告位组id, 模板>
            flyProUnitPositionsDto = doFlyCreativePositionService.middleCreativeDetails2slotGroupIdAndTemplateDto(
                    unit, saveCreativeDtos, operator, unit.getPromotionPurposeType(),
                    flyCampaignDetailDto.getRoomPromoteScenes(), false, account);
        } else {
            // 单元下两种类型创意(16_10, 16_9)投的具体位置信息 Map<广告位组id, 模板>
            flyProUnitPositionsDto = doFlyCreativePositionService.creativeDetails2slotGroupIdAndTemplateDto(
                    unit, saveCreativeDtos, operator, flyCampaignDetailDto);
        }

        // 转换成创意列表(包括模板，商业标...)
        List<CpcCreativeDto> creativeList = this.saveCreativeDto2List(saveCreativeDtos, unit, flyCampaignDetailDto, operator, flyProUnitPositionsDto);

        this.validatorBanner4CreateCreative(saveCreativeDtos, unit, creativeList, operator, flyProUnitPositionsDto);

        // 修改的情况: creativeIds
        List<Integer> reserveIds = creativeList.stream()
                .map(CpcCreativeDto::getCreativeId)
                .filter(Utils::isPositive)
                .collect(Collectors.toList());

        // 获取修改的创意 map
        Map<Integer, CpcCreativeDto> reserveCreativeMap = cpcSaveCreativeService.getMapInIds(reserveIds);

        if (deleteOtherInUnit) {
            cpcSaveCreativeService.deleteCreativeByUnitId(unit.getUnitId(), reserveIds);
        }

        // 保存选中的投放位置（lau_unit_fly_ext_info）
        flyUnitService.saveFlyBanner4UnitLaunchScenes(unit.getAccountId(), unit.getUnitId(),
                creativeList.get(0).getFlyBannerUnitScenesType(),
                creativeList.get(0).getFlyBannerUnitSpecificScenesType());
        // 保存创意列表
        List<Integer> savedCreativeIds = this.saveCreative(unit, creativeList, reserveCreativeMap, account, null,
                operator.getOperatorName(), flyCampaignDetailDto, operator, flyProUnitPositionsDto);

        // for promotionPurposeContent compare
        for (CpcCreativeDto cpcCreativeDto : creativeList) {
            cpcCreativeDto.setPromotionPurposeContent(JumpTypeEnum.getByCode(cpcCreativeDto.getJumpType())
                    .parseLaunchUrl(cpcCreativeDto.getPromotionPurposeContent()));
        }

        cpcSaveCreativeService.addCreativeInsertLog(operator, creativeList, reserveCreativeMap, savedCreativeIds, Collections.emptyMap(), Collections.emptyMap());

        return savedCreativeIds;
    }

    @Deprecated
    @Transactional(value = AD_CORE_TM, rollbackFor = Exception.class)
    public List<Integer> saveFlyBanner(Operator operator, List<CpcCreativeDetailDto> saveCreativeDtos, boolean deleteOtherInUnit,
                                       Map<Integer, List<ResSlotGroupTemplateMappingDto>> map) throws ServiceException {
        LOGGER.info("SaveCreative:operator={}, saveCreativeDtos={}", operator, JSON.toJSONString(saveCreativeDtos));
        Assert.isTrue(!Operator.validateParamIsNull(operator), "操作人不能为空");
        Assert.notEmpty(saveCreativeDtos, "创意列表不能为空");

        // 创意单元信息
        List<Integer> unitIds = saveCreativeDtos.stream().map(CpcCreativeDetailDto::getUnitId).distinct().collect(Collectors.toList());
        Assert.isTrue(unitIds.size() == 1, "只能保存同一个单元下的创意");
        Integer unitId = unitIds.get(0);
        CpcUnitDto unit = unitDelegate.loadCpcUnit(unitId);
        Assert.notNull(unit, "创意所选单元不存在");
        Assert.isTrue(operator.getOperatorId().equals(unit.getAccountId()), "您只能在自己的单元下新建创意");
        LOGGER.info("SaveCreative:unit:{}", JSON.toJSONString(unit));

        // 单元计划信息
        FlyCampaignDetailDto flyCampaignDetailDto = flyCampaignService.getFlyCampaignById(unit.getCampaignId());

        // 账号信息
        AccountBaseDto account = queryAccountService.getAccountBaseDtoById(unit.getAccountId());

        // 转换成创意列表
        List<CpcCreativeDto> creativeList = this.saveCreativeDto2List(saveCreativeDtos, unit, flyCampaignDetailDto, operator, null);

        this.validatorCreateCreative(saveCreativeDtos, unit, creativeList, map, flyCampaignDetailDto, operator);

        List<Integer> reserveIds = creativeList.stream()
                .map(CpcCreativeDto::getCreativeId)
                .filter(creativeId -> Utils.isPositive(creativeId))
                .collect(Collectors.toList());

        Map<Integer, CpcCreativeDto> reserveCreativeMap = cpcSaveCreativeService.getMapInIds(reserveIds);

        if (deleteOtherInUnit) {
            cpcSaveCreativeService.deleteCreativeByUnitId(unit.getUnitId(), reserveIds);
        }

        // 保存创意列表
        List<Integer> savedCreativeIds = this.saveCreative(unit, creativeList, reserveCreativeMap, account, map, operator.getOperatorName(), flyCampaignDetailDto, operator, null);

        // for promotionPurposeContent compare
        for (CpcCreativeDto cpcCreativeDto : creativeList) {
            cpcCreativeDto.setPromotionPurposeContent(JumpTypeEnum.getByCode(cpcCreativeDto.getJumpType())
                    .parseLaunchUrl(cpcCreativeDto.getPromotionPurposeContent()));
        }

        cpcSaveCreativeService.addCreativeInsertLog(operator, creativeList, reserveCreativeMap, savedCreativeIds, Collections.emptyMap(), Collections.emptyMap());

        return savedCreativeIds;
    }

    /**
     * 保存创意列表
     *
     * @param unit
     * @param creativeList
     * @param reserveCreatives
     * @param account
     * @param map
     * @param operatorName
     * @param flyCampaignDetailDto
     * @param operator
     * @param flyProUnitPositionsDto
     * @return
     * @throws ServiceException
     */
    @Deprecated
    private List<Integer> saveCreative(
            CpcUnitDto unit, List<CpcCreativeDto> creativeList, Map<Integer, CpcCreativeDto> reserveCreatives,
            AccountBaseDto account, Map<Integer, List<ResSlotGroupTemplateMappingDto>> map, String operatorName,
            FlyCampaignDetailDto flyCampaignDetailDto, Operator operator, FlyProUnitPositionsDto flyProUnitPositionsDto) throws ServiceException {

        List<Integer> savedCreativeIds = Lists.newArrayList();
        Set<Long> cmVideoSet = biliVideoService.getCmVideoSet(creativeList.stream()
                .filter(x -> !Objects.isNull(x.getBilibiliVideoBo()))
                .map(x -> Optional.ofNullable(x.getBilibiliVideoBo().getAvId()).orElse(0L))
                .filter(x -> x > 0L).collect(Collectors.toList()));

        for (CpcCreativeDto creative : creativeList) {

            CpcCreativeDto oldCreative = reserveCreatives.get(creative.getCreativeId());

            // 转成创意 po
            LauUnitCreativePo creativePo = cpcSaveCreativeService.convertNewCreativeToPo(creative, oldCreative, unit, account);

            // 处理仅投story创意的描述(忽略托管)
            boolean onlyStoryOrBigCard = this.handleOnlyStoryOrBigCardDescription(unit, flyProUnitPositionsDto, creative, creativePo);

            LOGGER.info("Save creative LauUnitCreativePo {}, CpcUnitDto {}, oldCreative {}", JSON.toJSONString(creativePo), unit, oldCreative);

            final boolean isBilibiliVideo = (Objects.equals(creative.getCreativeStyle(), CreativeStyle.BVID.getCode()) || Objects.equals(creative.getCreativeStyle(), CreativeStyle.AVID.getCode()))
                    && Objects.nonNull(creative.getBilibiliVideoBo());

            if (isBilibiliVideo) {
                // 保存avid
                creativePo.setVideoId(creative.getBilibiliVideoBo().getAvId());
            }

            int oldId = creativePo.getCreativeId() != null && creativePo.getCreativeId() > 0 ? creativePo.getCreativeId() : 0;

            if (oldCreative != null) {
                Assert.isTrue(oldCreative.getAccountId().equals(unit.getAccountId()), "你不能操作不属于您的创意");
            }

            //是否插入
            Boolean isInsert = creativePo.getCreativeId() == null;

            // 新增/修改创意
            cpcSaveCreativeService.saveOrUpdatePo(creativePo, unit.getAccountId(), unit.getUnitId());

            // 添加待审记录
            if (Objects.equals(AuditStatus.INIT.getCode(), creativePo.getAuditStatus())) {
                creativeServiceDelegate.addCreativeAuditStat(creativePo.getCreativeId(), CreativeAuditEvent.AUDIT, operatorName);
            }
            int newCreativeId = oldId > 0 ? oldId : creativePo.getCreativeId();
            LOGGER.info("get creative after save newCreativeId {}, creativePo {}", newCreativeId, JSON.toJSONString(creativePo));

            LauUnitCreativePo updatedPo = lauUnitCreativeDao.selectByPrimaryKey(newCreativeId);
            Assert.isTrue(updatedPo != null &&
                            updatedPo.getAccountId() != null && updatedPo.getAccountId().equals(unit.getAccountId()) &&
                            updatedPo.getTitle().equals(creative.getTitle()) &&
                            (onlyStoryOrBigCard || updatedPo.getDescription().equals(creative.getDescription())),
                    "保存创意失败，请稍后重试");

            // 创意落地页分享
            cpcSaveCreativeService.saveCreativeShare(creative, newCreativeId);

            // 保存创意自动更新表
            cpcSaveCreativeService.saveCreativeAuto(creative, newCreativeId);

            // 删除老的创意图片
            cpcSaveCreativeService.deleteOldICreativeImage(oldCreative != null ? oldCreative.getImageDtos() : Collections.emptyList(),
                    creative.getImageDtos());
            // 批量保存 创意 images
            if (!CollectionUtils.isEmpty(creative.getImageDtos())) {
                cpcSaveCreativeService.batchSaveCreativeImage(creativePo.getUnitId(), newCreativeId, creative.getImageDtos());
            }
            boolean isFLyLiveReserse = RoomPromoteScenesEnum.RESERVE.getCode().equals(flyCampaignDetailDto.getRoomPromoteScenes());
            //计划是直播预约，才出现起飞大卡模板的按钮
            if (isFLyLiveReserse) {
                Map<Integer, TemplateDto> templateDtoMap = templateService.getTemplateMapByTemplateIds(
                        Arrays.asList(creativePositionConfig.getFlyTemplateLiveReserve()));
                TemplateDto templateDto = templateDtoMap.get(creativePositionConfig.getFlyTemplateLiveReserve());
                if (templateDto != null) {
                    List<ButtonCopyDto> validateButtonCopyDtos = templateDto.getButtonCopyDtos();
                    if (!CollectionUtils.isEmpty(validateButtonCopyDtos) &&
                            LaunchButtonTypeEnum.LIVE_RESERVE.getCode() == validateButtonCopyDtos.get(0).getType()) {
                        creative.setAttachType(CreativeAttachTypeEnum.BUTTON_COPY.getCode());
                        creative.setButtonCopyId(flyReserveButtonCopyId);
                        cpcSaveCreativeService.saveCreativeButtonCopy(unit, creative, true,
                                newCreativeId, LaunchButtonTypeEnum.LIVE_RESERVE.getCode());
                    }
                }
            }


            cpcSaveCreativeService.saveDanmaku(newCreativeId, creative.getDanmakus());
            cpcSaveCreativeService.saveCreativeMonitoring(newCreativeId, creative);

            //起飞链接
            String formatedJumpUrl = this.saveFormatedJumpUrl(newCreativeId, unit, creative.getPromotionPurposeContent(), flyCampaignDetailDto.getRoomPromoteScenes(), Objects.equals(account.getIsInner(), 1));

            //起飞不支持动态布局
            cpcSaveCreativeService.saveCreativLayout(creative, null, newCreativeId, formatedJumpUrl);

            cpcSaveCreativeService.saveCreativeBusinessCategory(newCreativeId, creative.getBuFirstCategoryId(), creative.getBuSecondCategoryId(), creative.getBuThirdCategoryId());

            savedCreativeIds.add(newCreativeId);

            // 处理加急审核情况
            cpcSaveCreativeService.processUrgentAudit(unit.getAccountId(), creative, oldCreative, creativePo.getAuditStatus(), newCreativeId);

            // 保存广告位组模板 Mapping
            this.saveSlotGroupTemplateMapping(unit, creative, map, newCreativeId, flyCampaignDetailDto.getLaunchTarget(), operator, flyProUnitPositionsDto, flyCampaignDetailDto.getRoomPromoteScenes());

            //新建创意投动态位置注册一个动态
            this.registerDynamic(isInsert, creative, newCreativeId, unit, operator);

            //起飞创意额外信息表
            this.saveFlyCreativeScenes(unit, creative, newCreativeId);

            // mapi可能数据不含mid 需要手动填充
            List<BilibiliVideoBo> bilibiliVideoBoList = creativeList.stream()
                    .map(CpcCreativeDto::getBilibiliVideoBo)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            biliVideoService.fillMid(bilibiliVideoBoList);

            //专业起飞复制，自动审核(通过&驳回)
            if (isInsert && unit.getIsNewFly() == 1 && creative.isFlyCopy()) {
                creativeCommonService.autoAuditCopyCreative(unit.getUnitId(), creativePo.getCreativeId(), creative.getSourceCreativeIdAuditStatus());
                //暂停创意
                if (ObjectUtils.nullSafeEquals(creative.getSourceCreativeIdCreativeStatus(), CreativeStatus.PAUSED.getCode())) {
                    LauUnitCreativePoExample example = new LauUnitCreativePoExample();
                    example.or()
                            .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                            .andAccountIdEqualTo(account.getAccountId())
                            .andCreativeIdEqualTo(creativePo.getCreativeId());
                    LauUnitCreativePo po = new LauUnitCreativePo();
                    po.setStatus(LaunchStatus.STOP.getCode());
                    po.setCreativeStatus(CreativeStatus.PAUSED.getCode());
                    lauUnitCreativeDao.updateByExampleSelective(po, example);
                }
                //再查出来,写日志
                LauUnitCreativePo po = lauUnitCreativeDao.selectByPrimaryKey(creativePo.getCreativeId());
                creative.setAuditStatus(po.getAuditStatus());
                creative.setCreativeStatus(po.getCreativeStatus());
                creative.setStatus(po.getStatus());
            } else {
                //专业起飞，新建直播间创意，不是直播预约，自动审核
                if (isInsert && unit.getIsNewFly() == 1 && unit.getPromotionPurposeType() == PromotionPurposeType.LIVE_ROOM.getCode()
                        && !isFLyLiveReserse) {
                    creativeCommonService.autoAuditLiveRoomCreative(unit.getUnitId(), creativePo.getCreativeId());
                }
            }

            if (Objects.equals(creative.getCreativeStyle(), CreativeStyle.BVID.getCode()) || Objects.equals(creative.getCreativeStyle(), CreativeStyle.AVID.getCode())) {
                // 保存封面
                biliVideoService.save(BilibiliVideoBo.builder().avId(creative.getVideoId()).build(), cmVideoSet.contains(creative.getVideoId()));
            }

            //动态和活动保存lau_creative_fly_dynamic_info
            if (ObjectUtils.nullSafeEquals(DynamicTypeEnum.CHOOSE_DYNAMIC.getCode(), creative.getDynamicType())
                    || flyCampaignDetailDto.getLaunchTarget().equals(LaunchTargetEnum.ACTIVITY.getCode())) {

                cpcSaveCreativeService.saveDynamicInfo(isInsert, account.getAccountId(), unit.getUnitId(), newCreativeId,
                        creative.getDynamicId(), creative.getLikeCount(), creative.getNickName());
            }

            //邀约链接相关
            if (ObjectUtils.nullSafeEquals(FlyIsSetInviteLinkEnum.FALSE.getCode(), creative.getIsSetInviteLink())) {
                cpcSaveCreativeService.saveInviteLink(isInsert, unit.getVideoId(), unit.getRealMid().longValue(), account.getAccountId(),
                        unit.getCampaignId(), unit.getUnitId(), newCreativeId, creative.getFlyProUnderBox());
            }
            // 保存托管创意的 titleId 与 creativeId 关系
            if (Utils.isPositive(creative.getManagedJobTitleId())) {
                managedCampaignJobRepo.updateJobTitleCreativeId(creative.getManagedJobTitleId(),
                        creativePo.getCreativeId(), creativePo.getUnitId());
            }
            // 保存创意 extra 信息
            LauCreativeExtraPo creativeExtraPo = LauCreativeExtraPo.builder()
                    .creativeId(newCreativeId).accountId(creative.getAccountId())
                    .campaignId(creative.getCampaignId()).unitId(creative.getUnitId())
                    .qualificationPackageId(creative.getQualificationPackageId())
                    .build();
            lauCreativeExtraProc.saveCreativeExtra(creativeExtraPo, newCreativeId);

            // 保存资质
            lauQualificationService.saveCreativeQualification(newCreativeId,
                    creative.getQualificationIds(), creative.getQualificationPackageId());
        }
        List<Long> dynamicIds = creativeList.stream().map(t -> t.getDynamicId()).filter(Objects::nonNull).filter(Utils::isPositive).distinct().collect(Collectors.toList());
        lauDynamicService.processDynamics(dynamicIds);

        cpcSaveCreativeService.deleteUnUsedCreativeTemplate(unit.getUnitId(), savedCreativeIds);
        LOGGER.info("Return creativeIds {}", savedCreativeIds);
        return savedCreativeIds;
    }

    private void registerDynamic(Boolean isInsert, CpcCreativeDto creative, Integer newCreativeId,
                                 CpcUnitDto unit, Operator operator) {
        if (isInsert) {
            if (AdpVersion.isFlyBanner2(creative.getAdpVersion())) {
                if (creative.getFlySpecificScenesType().contains(SpecificScenesEnum.DYNAMIC_FLOW.getCode())) {
                    cpcSaveCreativeService.flyRegisterOrUpdate(newCreativeId, unit.getRealMid().longValue(), unit.getAccountId(), creative.getTitle(),
                            ForwardState.ALLOWED.getKey(), ReplySubjectStateEnum.OPEN.getCode(), ReplySubjectMonitorEnum.SHOW_FIRST.getCode());
                }
            } else {
                if (AdpVersion.isFlyBanner4(creative.getAdpVersion())) {
                    if (FlyBanner4CoverEnum.SIZE_16_9.getCode().equals(creative.getFlyBannerCoverType()) && doFlyCreativePositionService.dynamicLaunchSmallCard(unit, creative, operator)) {
                        cpcSaveCreativeService.flyRegisterOrUpdate(newCreativeId, unit.getRealMid().longValue(), unit.getAccountId(), creative.getTitle(),
                                ForwardState.ALLOWED.getKey(), ReplySubjectStateEnum.OPEN.getCode(), ReplySubjectMonitorEnum.SHOW_FIRST.getCode());
                    }
                    //带货动态tab暗投 https://www.tapd.cn/********/prong/stories/view/11********004158849
                    if (FlyBanner4CoverEnum.SCENE_MERGE.getCode().equals(creative.getFlyBannerCoverType()) && doFlyCreativePositionService.dynamicLaunchSmallCard(unit, creative, operator)) {
                        cpcSaveCreativeService.flyRegisterOrUpdate(newCreativeId, unit.getRealMid(), unit.getAccountId(), creative.getTitle(),
                                ForwardState.ALLOWED.getKey(), ReplySubjectStateEnum.OPEN.getCode(), ReplySubjectMonitorEnum.SHOW_FIRST.getCode());
                    }
                }
            }
        }
    }

    public void saveFlyCreativeScenes(CpcUnitDto unit, CpcCreativeDto creative, Integer newCreativeId) {
        //存起飞创意额外信息表（请求字段直接存）
        if (AdpVersion.isFlyBanner2(creative.getAdpVersion())) {
            String specificScenes = String.join(",", creative.getFlySpecificScenesType().stream().map(o -> String.valueOf(o)).collect(Collectors.toList()));

            LauCreativeFlyExtInfoPo po = LauCreativeFlyExtInfoPo.builder()
                    .accountId(creative.getAccountId())
                    .unitId(creative.getUnitId())
                    .creativeId(newCreativeId)
                    .isDeleted(IsDeleted.VALID.getCode())
                    .creativeStyle(creative.getCoverType())
                    .scenesType(creative.getFlyScenesType())
                    .specificScenes(specificScenes)
                    .build();
            lauCreativeFlyExtInfoDao.insertUpdateSelective(po);
        } else {
            if (AdpVersion.isFlyBanner4(creative.getAdpVersion())) {
                //存起飞创意额外信息表(根据flyBanner打头的三个字段来存）
                String specificScenes = String.join(",", creative.getFlyBannerUnitSpecificScenesType().stream()
                        .filter(o -> FlyBanner4CoverEnum.SIZE_16_10.getCode().equals(creative.getFlyBannerCoverType()) ?
                                SpecificScenesEnum.getClickSceneInt().contains(o) :
                                SpecificScenesEnum.getAutoSceneInt().contains(o))
                        .map(o -> String.valueOf(o)).collect(Collectors.toList()));

                // 托管不用过滤，上面的老逻辑不动
                if (Utils.isPositive(unit.getIsManaged())) {
                    specificScenes = String.join(",", creative.getFlySpecificScenesType().stream().map(o -> String.valueOf(o)).collect(Collectors.toList()));
                }

                LauCreativeFlyExtInfoPo po = LauCreativeFlyExtInfoPo.builder()
                        .accountId(creative.getAccountId())
                        .unitId(creative.getUnitId())
                        .creativeId(newCreativeId)
                        .isDeleted(IsDeleted.VALID.getCode())
                        .creativeStyle(creative.getCoverType())
                        .scenesType(creative.getFlyBannerUnitScenesType())
                        .specificScenes(specificScenes)
                        .banner4CoverType(creative.getFlyBannerCoverType())
                        .storyUrl(this.generateStoryUrl(unit, creative))
                        .preferDirectCallUp(Functions.boolean2Integer(creative.getPreferDirectCallUp()))
                        .isYellowCar(creative.getIsYellowCar())
                        .yellowCarIcon(Utils.isPositive(creative.getIsYellowCar()) ? 1 : 0)
                        .yellowCarTitle(Utils.isPositive(creative.getIsYellowCar()) ? creative.getYellowCarTitle() : "")
                        // 目前不存 tvurl 问【彬彬黄】
                        //.tvUrl(this.generateTvUrl(unit, creative))
                        .build();
                lauCreativeFlyExtInfoDao.insertUpdateSelective(po);
            }
        }

    }


    public String generateStoryUrl(CpcUnitDto unit, CpcCreativeDto creative) {
        // story_url: 要求稿件, auto play, 竖屏
        Long avid = creative.getVideoId();
        if (PromotionPurposeType.ARCHIVE_CONTENT.getCode() == creative.getPromotionPurposeType() && Utils.isPositive(avid)) {
            Arc arc = archiveServiceProxy.arc(avid).getArc();
            Boolean autoPlay = flyArchiveService.isAutoPlay(arc);
            Boolean verticalScreen = flyArchiveService.isVerticalScreen(arc);
            if (autoPlay && verticalScreen) {
                return com.bilibili.adp.cpc.utils.LaunchUtil.doGenerateStoryUrl(avid, unit.getCid());
            }
        }
        return "";
    }

    public List<CpcCreativeDto> saveCreativeDto2List(List<CpcCreativeDetailDto> saveCreativeDtos, CpcUnitDto unit,
                                                     FlyCampaignDetailDto flyCampaignDetailDto, Operator operator,
                                                     FlyProUnitPositionsDto flyProUnitPositionsDto) throws ServiceException {
        // 获取账户角标信息(老的商业标)
        Map<Integer, CmMarkDto> cmMarkMap = cmMarkService.getCmMarkMapByAccountId(unit.getAccountId());
        LOGGER.info("saveCreativeDto2List:cmMarkMap={}", JSON.toJSONString(cmMarkMap));

        return saveCreativeDtos.stream()
                .map(saveCreativeDto -> buildCreativeDto(unit, saveCreativeDto, cmMarkMap, flyCampaignDetailDto, operator, flyProUnitPositionsDto))
                .collect(Collectors.toList());
    }

    /**
     * 构建创意其他信息
     * 如: 模板，商业标
     *
     * @param unit
     * @param saveCreativeDto
     * @param cmMarkMap
     * @param flyCampaignDetailDto
     * @param operator
     * @param flyProUnitPositionsDto
     * @return
     */
    private CpcCreativeDto buildCreativeDto(CpcUnitDto unit, CpcCreativeDetailDto saveCreativeDto,
                                            Map<Integer, CmMarkDto> cmMarkMap, FlyCampaignDetailDto flyCampaignDetailDto,
                                            Operator operator, FlyProUnitPositionsDto flyProUnitPositionsDto) {
        // 所有广告位组的模板 ids
        List<Integer> templateIds = new ArrayList<>();

        // 根据不同的 adp version 获取模板 ids
        if (AdpVersion.isFlyBanner2(saveCreativeDto.getAdpVersion())) {
            // 根据投放场景获取模板 ids
            templateIds.addAll(doFlyCreativePositionService.getFlyTemplateIds(saveCreativeDto.getLaunchScenes(),
                    saveCreativeDto.getSpecificScenesList(), flyCampaignDetailDto.getLaunchTarget(),
                    operator));
        } else {
            // 中台走这个case，不会用到flyCampaignDetailDto
            if (AdpVersion.isFlyBanner4(saveCreativeDto.getAdpVersion())) {
                // 根据 convType 获取两种规格的 Map<slotGroupId, 模板>
                Map<Integer, TemplateDto> slotGroupId2TemplateDto = this.getMapByFlyBanner4CoverType(saveCreativeDto.getFlyBannerCoverType(), flyProUnitPositionsDto);
                templateIds.addAll(slotGroupId2TemplateDto.values().stream().map(o -> o.getTemplateId()).collect(Collectors.toList()));
            }
        }

        // 获取模板信息
        Map<Integer, TemplateDto> templateMap = queryTemplateService.getTemplateMapInIds(templateIds);

        // isFlyBanner(4和5) && 有播放器框下场景, 写死商业标: up主推荐广告商业标
        if (AdpVersion.isFlyBanner4(unit.getAdpVersion()) && saveCreativeDto.getFlyBannerUnitSpecificScenesType().contains(SpecificScenesEnum.UNDER_BOX.getCode())) {
            saveCreativeDto.setBusMarkId(upRecommendBusMarkId);
        } else {
            // 这个字段实际上没啥用了, 只有预览还会用到, 暂时先写创作推广标, 有需求再改
            saveCreativeDto.setBusMarkId(flyCreationPromotionMarkId);
        }
        // 投稿内容 || OGV || 起飞原生落地页
        boolean supportVideo = Objects.equals(PromotionPurposeType.ARCHIVE_CONTENT.getCode(), unit.getPromotionPurposeType()) || Objects.equals(PromotionPurposeType.OGV.getCode(), unit.getPromotionPurposeType());
        // 稿件模板支持videoId，其它模板不支持videoId
        String landingPageUrl = cpcSaveCreativeService.getActualLandingPageUrl(saveCreativeDto.getPromotionPurposeContent(), supportVideo, unit.getChannelId(), saveCreativeDto.getVideoId());

        saveCreativeDto.setPromotionPurposeContent(landingPageUrl);

        // 起飞暂时不支持小程序, 如果开了白名单, 使用h5
        ValidateAndGetPageReply landingPage = cpcSaveCreativeService.validateJumpTypeAndGetPromotionPurposeContent(saveCreativeDto.getJumpType(), landingPageUrl);

        // 从模板获取创意类型
        Integer creativeType = 4;
        if (templateMap != null) {
            ArrayList<TemplateDto> templateDtos = new ArrayList<>(templateMap.values());
            if (!CollectionUtils.isEmpty(templateDtos)) {
                TemplateDto templateDto = templateDtos.get(0);
                if (templateDto != null) {
                    creativeType = templateDto.getCreativeType();
                }
            }
        }

        CpcCreativeDto.CpcCreativeDtoBuilder creativeBuilder = CpcCreativeDto.builder()
                .creativeId(saveCreativeDto.getCreativeId())
                .creativeType(creativeType)
                .orderId(0)
                .adpVersion(saveCreativeDto.getAdpVersion())
                .preferScene(saveCreativeDto.getPreferScene())
                .scenes(saveCreativeDto.getScenes())
                .jumpType(saveCreativeDto.getJumpType())
                .campaignId(unit.getCampaignId())
                .accountId(unit.getAccountId())
                .unitId(unit.getUnitId())
                //模板对象为空
                .template(null)
                .salesType(unit.getSalesType())
                .cmMark(saveCreativeDto.getCmMark())
                .cmMarkDesc(cmMarkMap.getOrDefault(saveCreativeDto.getCmMark(), CmMarkDto.builder().name("广告").build()).getName())
                .creativeName(saveCreativeDto.getCreativeName())
                .promotionPurposeContent(landingPage.getJumpUrl())
                .customizedClickUrl(StringUtils.trimToEmpty(saveCreativeDto.getCustomizedClickUrl()))
                .customizedImpUrl(StringUtils.trimToEmpty(saveCreativeDto.getCustomizedImpUrl()))
                .title(cpcSaveCreativeService.genWildcard(StringUtils.trimToEmpty(saveCreativeDto.getTitle()), saveCreativeDto.getWildcardDesc()))
                .managedJobTitleId(saveCreativeDto.getManagedJobTitleId())
                .templateId(saveCreativeDto.getTemplateId())
                .templateGroupId(saveCreativeDto.getTemplateGroupId())
                .imageDtos(cpcSaveCreativeService.buildImageWithMd5(saveCreativeDto.getImageDtos()))
                .extImageUrl(saveCreativeDto.getExtImageUrl())
                .extImageMd5(Strings.isNullOrEmpty(saveCreativeDto.getExtImageHash()) ? "" : cpcSaveCreativeService.getMd5FromHash(saveCreativeDto.getExtImageHash()))
                .videoUrl(saveCreativeDto.getVideoUrl())
                .description(StringUtils.trimToEmpty(saveCreativeDto.getDescription()))
                .extDescription(StringUtils.trimToEmpty(saveCreativeDto.getExtDescription()))
                .videoId(saveCreativeDto.getVideoId())
                .mgkVideoId(saveCreativeDto.getMgkVideoId())
                .buttonCopy(saveCreativeDto.getButtonCopy())
                .firstCategoryId(saveCreativeDto.getFirstCategoryId())
                .secondCategoryId(saveCreativeDto.getSecondCategoryId())
                .tags(Lists.newArrayList(saveCreativeDto.getTags()))
                .beginTime(LaunchUtil.getString2Date(unit.getLaunchBeginDate()))
                .endTime(LaunchUtil.getString2Date(unit.getLaunchEndDate()))
                .attachType(saveCreativeDto.getAttachType())
                .buttonCopyId(saveCreativeDto.getButtonCopyId())
                .buttonCopyUrl(saveCreativeDto.getButtonCopyUrl())
                .promotionPurposeType(unit.getPromotionPurposeType())
                .imageUrl(CollectionUtils.isEmpty(saveCreativeDto.getImageDtos()) ? "" : saveCreativeDto.getImageDtos().get(0).getUrl())
                .imageMd5(CollectionUtils.isEmpty(saveCreativeDto.getImageDtos()) ? "" : cpcSaveCreativeService.getMd5FromImageDto(saveCreativeDto.getImageDtos().get(0)))
                .schemeUrl(saveCreativeDto.getSchemeUrl())
                .danmakus(saveCreativeDto.getDanmakus())
                .mgkPageId(landingPage.getPageId())
                .mgkPageStatus(landingPage.getPageStatusValue())
                .adVersionControllId(landingPage.getAdVersionControlId())
                .buFirstCategoryId(saveCreativeDto.getBuFirstCategoryId())
                .buSecondCategoryId(saveCreativeDto.getBuSecondCategoryId())
                .buThirdCategoryId(saveCreativeDto.getBuThirdCategoryId())
                .adMark(saveCreativeDto.getAdMark())
                .busMarkId(saveCreativeDto.getBusMarkId())
                .shareState(saveCreativeDto.getShareState())
                .shareTitle(saveCreativeDto.getShareTitle())
                .shareSubtitle(saveCreativeDto.getShareSubtitle())
                .shareImageUrl(saveCreativeDto.getShareImageUrl())
                .shareImageMd5(Strings.isNullOrEmpty(saveCreativeDto.getShareImageHash()) ? "" : cpcSaveCreativeService.getMd5FromHash(saveCreativeDto.getShareImageHash()))
                .creativeStyle(saveCreativeDto.getCreativeStyle())
                .creativeMonitoring(cpcSaveCreativeService.trimCreativeMonitoring(saveCreativeDto.getCreativeMonitoring()))
                .bilibiliUserId(saveCreativeDto.getBilibiliUserId())
                .isNewFly(saveCreativeDto.getIsNewFly())
                .isMiddleAd(saveCreativeDto.getIsMiddleAd())
                .brandInfoId(saveCreativeDto.getBrandInfoId())
                .bilibiliVideoBo(saveCreativeDto.getBilibiliVideo())
                .flyScenesType(saveCreativeDto.getLaunchScenes())
                .flySpecificScenesType(saveCreativeDto.getSpecificScenesList())
                .coverType(saveCreativeDto.getCoverType())
                .dynamicId(saveCreativeDto.getDynamicId())
                .dynamicType(saveCreativeDto.getDynamicType())
                .likeCount(saveCreativeDto.getLikeCount())
                .nickName(saveCreativeDto.getNickName())
                .flyCopy(saveCreativeDto.isFlyCopy())
                .sourceCreativeIdAuditStatus(saveCreativeDto.getSourceCreativeIdAuditStatus())
                .sourceCreativeIdCreativeStatus(saveCreativeDto.getSourceCreativeIdCreativeStatus())
                .isManaged(saveCreativeDto.getIsManaged())
                .isSetInviteLink(saveCreativeDto.getIsSetInviteLink())
                .flyProUnderBox(saveCreativeDto.getFlyProUnderBox())
                .flyBannerCoverType(saveCreativeDto.getFlyBannerCoverType())
                .flyBannerUnitScenesType(saveCreativeDto.getFlyBannerUnitScenesType())
                .flyBannerUnitSpecificScenesType(saveCreativeDto.getFlyBannerUnitSpecificScenesType())
                .preferDirectCallUp(saveCreativeDto.getPreferDirectCallUp())
                .isYellowCar(saveCreativeDto.getIsYellowCar())
                .yellowCarTitle(saveCreativeDto.getYellowCarTitle())
                .components(saveCreativeDto.getCreativeComponents())
                .flyInvitationDtos(saveCreativeDto.getFlyInvitationDtos())
                .qualificationPackageId(saveCreativeDto.getQualificationPackageId())
                .qualificationIds(saveCreativeDto.getQualificationIds())
                .flag(saveCreativeDto.getFlag());
        if (saveCreativeDto.isFlyNativeLandingPage()) {
            creativeBuilder.advertisingMode(AdvertisingMode.NATIVE_CONTET.getKey());
        }
        CpcCreativeDto creative = creativeBuilder.build();

        // 稿件内容的默认字段
        if (unit.getPromotionPurposeType().equals(PromotionPurposeType.ARCHIVE_CONTENT.getCode())) {
            if (creative.getBuFirstCategoryId() == 0) {
                creative.setBuFirstCategoryId(ARCHIVE_FIRST_CATEGORY);
            }
            if (creative.getBuSecondCategoryId() == 0) {
                creative.setBuSecondCategoryId(ARCHIVE_SECOND_CATEGORY);
            }
            if (creative.getBuThirdCategoryId() == 0) {
                creative.setBuThirdCategoryId(ARCHIVE_THIRD_CATEGORY);
            }
            if (ARCHIVE_CM_MARK == CmMarkEnum.UP_CAMPAIGN.getCode()) {
                creative.setCmMark(ARCHIVE_CM_MARK);
                creative.setAdMark(ARCHIVE_AD_MARK);
            } else {
                if (Utils.isPositive(creative.getCmMark())) {
                    creative.setCmMark(ARCHIVE_CM_MARK);
                }
                if (Strings.isNullOrEmpty(creative.getAdMark())) {
                    creative.setAdMark(ARCHIVE_AD_MARK);
                }
            }
        }
        final boolean liveUnit = Objects.equals(unit.getPromotionPurposeType(), PromotionPurposeType.LIVE_ROOM.getCode())
                || Objects.equals(unit.getPromotionPurposeType(), PromotionPurposeType.GOODS_LIVE.getCode());
        if (Objects.nonNull(saveCreativeDto.getRefreshType()) && liveUnit) {
            CreativeRefreshType.getByCode(saveCreativeDto.getRefreshType());
            CpcCreativeAutoDto autoDto = new CpcCreativeAutoDto();
            autoDto.setRefreshType(saveCreativeDto.getRefreshType());
            creative.setAutoDto(autoDto);
        }
        final boolean isGoods = Objects.equals(unit.getPromotionPurposeType(), PromotionPurposeType.GOODS.getCode());
        if (isGoods) {
            Assert.notNull(unit.getGoods(), "商品信息不能为空");
            creative.setSchemeUrl(unit.getGoods().getSchemaUrl());
            creative.setPromotionPurposeContent(unit.getGoods().getJumpLink());
            //这2行代码 本不应该set的 但是后续有com.bilibili.adp.cpc.biz.services.creative.DoFlyCreativeService.validatorBanner4CreateCreative
            saveCreativeDto.setSchemeUrl(unit.getGoods().getSchemaUrl());
            saveCreativeDto.setPromotionPurposeContent(unit.getGoods().getJumpLink());
        }
        final boolean isDynamic = Objects.equals(unit.getPromotionPurposeType(), PromotionPurposeType.DYNAMIC.getCode());
        if (isDynamic) {
            Assert.notNull(unit.getDynamic(), "动态信息不能为空");
            Assert.isTrue(Utils.isPositive(unit.getDynamic().getDynamicId()), "动态id错误");
            final JumpInfoBo jumpInfo = launchJumpUrlService.dynamicJumpInfo(unit.getDynamic().isNewVersion(), unit.getDynamic().getDynamicId());
            creative.setPromotionPurposeContent(jumpInfo.getJumpUrl());
            creative.setPromotionPurposeContentSecondary(jumpInfo.getDegradedJumpUrl());
            creative.setDynamicUpMid(unit.getDynamic().getDynamicUpMid());
            //这1行代码 本不应该set的 但是后续有com.bilibili.adp.cpc.biz.services.creative.DoFlyCreativeService.validatorBanner4CreateCreative
            saveCreativeDto.setPromotionPurposeContent(jumpInfo.getJumpUrl());
        }
        return creative;
    }


    public void validatorCreateCreative(List<CpcCreativeDetailDto> saveCreativeDtos, CpcUnitDto unit,
                                        List<CpcCreativeDto> creativeList, Map<Integer, List<ResSlotGroupTemplateMappingDto>> map,
                                        FlyCampaignDetailDto flyCampaignDetailDto, Operator operator) throws ServiceException {

        validator.validateUnitInfo(unit);

        for (CpcCreativeDetailDto saveCreativeDto : saveCreativeDtos) {
            Assert.notNull(saveCreativeDto, "创意信息不可为空");
            Assert.notNull(saveCreativeDto.getCmMark(), "广告标识不可为空");
            validator.validateUnitCreativeCommonInfo(saveCreativeDto, true);
        }

        validator.validateCreativeNumber(creativeList, unit.getUnitId());

        this.validateMaterialInfo(unit, saveCreativeDtos);

        this.validateCreativeTemplate(creativeList, map, flyCampaignDetailDto.getLaunchTarget(), operator, flyCampaignDetailDto.getRoomPromoteScenes());

    }

    private void validateMaterialInfo(CpcUnitDto unitDto, List<CpcCreativeDetailDto> list)
            throws ServiceException {
        if (CollectionUtils.isEmpty(list)) {
            throw new ServiceException(LaunchExceptionCode.CREATIVE_NO_UPLOAD_MATERIALINFO);
        }

        for (CpcCreativeDetailDto saveCreativeDto : list) {

            cpcCreativeValidator.validateShareImage(saveCreativeDto);

            if (saveCreativeDto.getVideoId() > 0 && LaunchConstant.IS_VALIDATE_VIDEO) {
                ArchiveDetail detail = archiveManager.queryArchivesByAid(saveCreativeDto.getVideoId());
                LOGGER.info("createCreative validateMaterialInfo videoId is {}, detail is {}", saveCreativeDto.getVideoId(), JSON.toJSONString(detail));

                Assert.isTrue(detail != null && detail.getArchive() != null && !CollectionUtils.isEmpty(detail.getVideos()), "视频不存在或未审核");
            }

            String creativeName = saveCreativeDto.getCreativeName();
            if (Strings.isNullOrEmpty(creativeName)) {
                throw new ServiceException(LaunchExceptionCode.CREATIVE_NAME_NULL);
            } else if (creativeName.length() > LaunchConstant.MAX_CREATIVE_NAME_LENGTH) {
                throw new ServiceException(LaunchExceptionCode.CREATIVE_NAME_EXCEED_MAX_LENGTH);
            }

            //起飞没有页卡唤起
            saveCreativeDto.setSchemeUrl("");
            Assert.isTrue(Strings.isNullOrEmpty(saveCreativeDto.getSchemeUrl()), "您无权使用页卡唤起功能");

        }
    }

    /**
     * 保存广告位组模板 Mapping
     * <p>
     * 优选：
     * 内容起飞+静态：内容起飞信息流、内容起飞播放页
     * 商业起飞+静态：商业起飞信息流、商业起飞播放页
     * 内容起飞+GIF：内容起飞信息流gif
     * 商业起飞+GIF：商业起飞信息流gif
     * <p>
     * 信息流：（直播间只能选信息流）
     * 内容起飞+静态：内容起飞信息流
     * 商业起飞+静态：商业起飞信息流
     * 内容起飞+GIF：内容起飞信息流gif
     * 商业起飞+GIF：商业起飞信息流gif
     * 直播间+静态：直播间信息流
     * <p>
     * 播放页：
     * 内容起飞+静态：内容起飞播放页
     * 商业起飞+静态：商业起飞播放页
     * <p>
     * 信息流大卡：
     * <p>
     * 动态流：
     * <p>
     * 信息流+播放页：
     * 内容起飞+静态：内容起飞信息流、内容起飞播放页
     * 商业起飞+静态：商业起飞信息流、商业起飞播放页
     */
    public void saveSlotGroupTemplateMapping(
            CpcUnitDto unit,
            CpcCreativeDto creative,
            Map<Integer, List<ResSlotGroupTemplateMappingDto>> map,
            Integer newCreativeId,
            Integer launchTarget,
            Operator operator,
            FlyProUnitPositionsDto flyProUnitPositionsDto,
            Integer roomPromoteScenes) {
        if (AdpVersion.isFlyBanner2(creative.getAdpVersion())) {
            this.saveSlotGroupTemplateMapping4Banner2(unit, creative, map, newCreativeId, launchTarget, operator, roomPromoteScenes);
        } else {
            if (AdpVersion.isFlyBanner4(creative.getAdpVersion())) {
                this.saveSlotGroupTemplateMapping4Banner4(unit, creative, newCreativeId, launchTarget, operator, flyProUnitPositionsDto);
            }
        }
    }

    /**
     * 保存广告位组和模板信息
     *
     * @param unit
     * @param creative
     * @param map
     * @param newCreativeId
     * @param launchTarget
     * @param operator
     * @param roomPromoteScenes
     */
    private void saveSlotGroupTemplateMapping4Banner2(CpcUnitDto unit,
                                                      CpcCreativeDto creative,
                                                      Map<Integer, List<ResSlotGroupTemplateMappingDto>> map,
                                                      Integer newCreativeId,
                                                      Integer launchTarget,
                                                      Operator operator,
                                                      Integer roomPromoteScenes) {
        List<ResSlotGroupTemplateMappingDto> resSlotGroupTemplateMappingDtos = null;
        //todo ocpm为框下链接的单元 一个月后请删除

        // 根据业务规则(优化目标，场景类型)写死广告位组
        if (OcpcTargetEnum.UNDER_BOX_LINK_CLICK.getCode().equals(unit.getOcpcTarget())) {
            List<Integer> slotGroupIds = new ArrayList<>();
            // 优选场景
            if (ScenesEnum.PREFER_SCENES.getCode().equals(creative.getFlyScenesType())) {
                slotGroupIds.add(flyUnderBoxMsgInfoSlotGroupId);
                slotGroupIds.add(flyUnderBoxPlayPageSlotGroupId);
            } else {
                List<Integer> flySpecificScenesType = creative.getFlySpecificScenesType();
                if (CoverTypeEnum.STATIC.getCode().equals(creative.getCoverType())) {
                    if (flySpecificScenesType.contains(SpecificScenesEnum.MSG_FLOW.getCode())) {
                        slotGroupIds.add(flyUnderBoxMsgInfoSlotGroupId);
                    }
                    if (flySpecificScenesType.contains(SpecificScenesEnum.PLAY_PAGE.getCode())) {
                        slotGroupIds.add(flyUnderBoxPlayPageSlotGroupId);
                    }
                } else {
                    if (CoverTypeEnum.GIF.getCode().equals(creative.getCoverType())) {
                        if (flySpecificScenesType.contains(SpecificScenesEnum.MSG_FLOW.getCode())) {
                            slotGroupIds.add(flyUnderBoxMsgInfoGifSlotGroupId);
                        }
                    }
                }
            }
            Assert.notEmpty(slotGroupIds, "数据错误!");
            resSlotGroupTemplateMappingDtos = resSlotGroupService
                    .querySlotGroupTemplateMappingInSlotGroupIds(QueryTemplateLaunchTypeMappingDto
                            .builder()
                            .slotGroupIds(slotGroupIds)
                            .promotionPurposeType(PromotionPurposeType.ARCHIVE_CONTENT.getCode())
                            .build());
        } else {
            //正常逻辑（根据页面的优选场景或指定场景，返回对应的广告位组，并考虑稿件autoplay=0的情况）
            resSlotGroupTemplateMappingDtos = doFlyCreativePositionService.getResSlotGroupTemplateMappingDtoListAndThenFilterWithAutoPlay
                    (map, creative, launchTarget, operator, roomPromoteScenes);
            if (CollectionUtils.isEmpty(resSlotGroupTemplateMappingDtos)) {
                return;
            }
            if (unit.getIsManaged() == 1
                    && unit.getPromotionPurposeType() == PromotionPurposeType.ARCHIVE_CONTENT.getCode()
                    && this.accountIdInLabel(creative.getAccountId(), searchPositionConfig.getFlyContentSearchWhiteLabel())
            ) {
                // 判断场景中的16-9和16-10
                boolean has1609 = resSlotGroupTemplateMappingDtos.stream().anyMatch(o -> searchPositionConfig.getFlyConrentTrust1609SlotGroupIds().contains(o.getSlotGroupId()));
                boolean has1610 = !has1609 || !resSlotGroupTemplateMappingDtos.stream().allMatch(o -> searchPositionConfig.getFlyConrentTrust1609SlotGroupIds().contains(o.getSlotGroupId()));
                List<Integer> searchTemplatesIds = new ArrayList<>();
                List<Integer> searchGroupIds = new ArrayList<>();
                if (has1610) {
                    searchTemplatesIds.add(searchPositionConfig.getFlyContentSearchTemplateId());
                    searchGroupIds.add(searchPositionConfig.getFlyContentSearchSlotGroupId());
                }
                if (has1609) {
                    searchTemplatesIds.add(searchPositionConfig.getFlyContentSearchAutoTemplateId());
                    searchGroupIds.add(searchPositionConfig.getFlyContentSearchAutoSlotGroupId());
                }
                try {
                    // 托管 && 稿件视频内容(稿件标的) && 白名单 暗投搜索
                    Map<Integer, TemplateDto> searchTemplateDtoMap = queryTemplateService.getTemplateMapInIds(searchTemplatesIds);
                    if (searchTemplateDtoMap == null || searchTemplateDtoMap.isEmpty()) {
                        throw new Exception("saveSlotGroupTemplateMapping queryTemplateService.getTemplateMapInIds empty");
                    }
                    Map<Integer, ResSlotGroupBaseDto> searchSlotGroupMap = this.getSlotGroupMapBtIds(searchGroupIds);
                    if (searchSlotGroupMap.isEmpty()) {
                        throw new Exception("saveSlotGroupTemplateMapping resSlotGroupService.getGroup2SlotIdsMap empty");
                    }
                    if (has1610) {
                        LOGGER.warn("saveSlotGroupTemplateMapping has1610 process");
                        ResSlotGroupBaseDto searchSlotGroup = searchSlotGroupMap.get(searchPositionConfig.getFlyContentSearchSlotGroupId());
                        TemplateDto searchTemplateDto = searchTemplateDtoMap.get(searchPositionConfig.getFlyContentSearchTemplateId());
                        if (searchSlotGroup == null || searchTemplateDto == null) {
                            throw new Exception("saveSlotGroupTemplateMapping searchSlotGroup searchTemplateDto empty");
                        }
                        resSlotGroupTemplateMappingDtos.add(ResSlotGroupTemplateMappingDto.builder()
                                .launchType(PromotionPurposeType.ARCHIVE_CONTENT.getCode())
                                .slotGroup(searchSlotGroup)
                                .slotGroupId(searchPositionConfig.getFlyContentSearchSlotGroupId())
                                .templates(Collections.singletonList(searchTemplateDto))
                                .build()
                        );
                    }
                    if (has1609) {
                        LOGGER.warn("saveSlotGroupTemplateMapping has1609 process");
                        // 存在点击播放场景
                        ResSlotGroupBaseDto searchAutoSlotGroup = searchSlotGroupMap.get(searchPositionConfig.getFlyContentSearchAutoSlotGroupId());
                        TemplateDto searchAutoTemplateDto = searchTemplateDtoMap.get(searchPositionConfig.getFlyContentSearchAutoTemplateId());
                        if (searchAutoSlotGroup == null || searchAutoTemplateDto == null) {
                            throw new Exception("saveSlotGroupTemplateMapping searchAutoSlotGroup searchAutoTemplateDto empty");
                        }
                        resSlotGroupTemplateMappingDtos.add(ResSlotGroupTemplateMappingDto.builder()
                                .launchType(PromotionPurposeType.ARCHIVE_CONTENT.getCode())
                                .slotGroup(searchAutoSlotGroup)
                                .slotGroupId(searchPositionConfig.getFlyContentSearchSlotGroupId())
                                .templates(Collections.singletonList(searchAutoTemplateDto))
                                .build()
                        );
                    }
                    LOGGER.info("saveSlotGroupTemplateMapping add search SlotGroupTemplate");
                } catch (Exception e) {
                    LOGGER.error("saveSlotGroupTemplateMapping get search resource fail e=", e);
                }
            }
        }
        LOGGER.info("saveSlotGroupTemplateMapping:resSlotGroupTemplateMappingDtos.size={}", CollectionHelper.getSize(resSlotGroupTemplateMappingDtos));
        if (CollectionUtils.isEmpty(resSlotGroupTemplateMappingDtos)) {
            return;
        }

        final int aid = unit.getAccountId();
        final int ppt = unit.getPromotionPurposeType();
        final int st = unit.getSalesType();
        final int bidPrice = unit.getCostPrice();
        final int ocpcTarget = unit.getOcpcTarget();

        List<LauCreativeTemplateDo> dos = new ArrayList<>();
        resSlotGroupTemplateMappingDtos.stream().forEach(dto -> {
            final int sg = dto.getSlotGroupId();
            final int reservedPrice = cpcUnitService.getFlyLowestCost(GetBidCostParam.builder()
                    .accountId(aid)
                    .slotGroupId(sg)
                    .launchType(ppt)
                    .salesType(st)
                    .build(), ocpcTarget);
            final int cardType = dto.getTemplates().get(0).getCardType();
            final boolean isStory = storyTemplateIds.contains(dto.getTemplates().get(0).getTemplateId());
            if (isStory) {
                //没有story权限
                if (!hasStoryPermission(launchTarget, unit, aid)) {
                    return;
                }
            }
            final boolean isPcIndex = pcIndexTemplateIds.contains(dto.getTemplates().get(0).getTemplateId());
            final boolean isPcPlay = pcPlayTemplateIds.contains(dto.getTemplates().get(0).getTemplateId());
            final boolean isUnderBox = underBoxTemplateIds.contains(dto.getTemplates().get(0));
            int busMarkId = this.generateBusMarkId(unit, operator, Arrays.asList(cardType),
                    launchTarget, isStory, isPcIndex, isPcPlay, isUnderBox, dto.getTemplates().get(0).getTemplateId());
            if (this.isRocketBusMark(dto.getTemplates().get(0).getTemplateId())) {
                // 内容起飞搜索暗投-火箭标
                busMarkId = rocketBusMarkId;
            }
            LauCreativeTemplateDo lauCreativeTemplateDo = new LauCreativeTemplateDo();
            lauCreativeTemplateDo.setCreativeId(newCreativeId.longValue());
            lauCreativeTemplateDo.setAccountId(creative.getAccountId());
            lauCreativeTemplateDo.setUnitId(creative.getUnitId());
            lauCreativeTemplateDo.setSlotGroupId(dto.getSlotGroup().getId());
            lauCreativeTemplateDo.setTemplateId(dto.getTemplates().get(0).getTemplateId());
            lauCreativeTemplateDo.setIsDeleted(IsDeleted.VALID.getCode());
            lauCreativeTemplateDo.setReservedPrice(reservedPrice);

            if (unit.getIsNoBid() != null && Utils.isPositive(unit.getIsNoBid())) {
                lauCreativeTemplateDo.setBizStatus(BIZ_STATUS_OK);
            } else {
                lauCreativeTemplateDo.setBizStatus(bidPrice < reservedPrice ? BIZ_STATUS_UNDER_RESERVED_PRICE : BIZ_STATUS_OK);
            }

            lauCreativeTemplateDo.setBusMarkId(busMarkId);
            lauCreativeTemplateDo.setCreativeStyle(this.slotGroupId2CreativeStyle(dto.getSlotGroup().getId(), creative.getPromotionPurposeType()));
            dos.add(lauCreativeTemplateDo);
        });
        adCoreBqf.insertMysql(lauCreativeTemplate).insertDuplicateUpdates(dos);
    }

    private Map<Integer, ResSlotGroupBaseDto> getSlotGroupMapBtIds(List<Integer> ids) {
        Map<Integer, ResSlotGroupBaseDto> mp = new HashMap<>();
        ids.forEach(id -> {
            ResSlotGroupBaseDto res = resSlotGroupService.getGroupById(id);
            if (res != null) {
                mp.put(id, res);
            }
        });
        return mp;
    }

    /**
     * 根据广告位组模板 map 保存创意模板信息
     *
     * @param unit
     * @param creative
     * @param newCreativeId
     * @param launchTarget
     * @param operator
     * @param flyProUnitPositionsDto
     */
    private void saveSlotGroupTemplateMapping4Banner4(CpcUnitDto unit,
                                                      CpcCreativeDto creative,
                                                      Integer newCreativeId,
                                                      Integer launchTarget,
                                                      Operator operator,
                                                      FlyProUnitPositionsDto flyProUnitPositionsDto) {
        //获取要投放的具体位置
        Map<Integer, TemplateDto> slotGroupId2TemplateDto = this.getMapByFlyBanner4CoverType(creative.getFlyBannerCoverType(), flyProUnitPositionsDto);

        final int aid = unit.getAccountId();
        final int ppt = unit.getPromotionPurposeType();
        final int st = unit.getSalesType();
        final int bidPrice = unit.getCostPrice();
        final int ocpcTarget = unit.getOcpcTarget();
        boolean isMiddleAd = AdpVersion.isMiddle(unit.getAdpVersion());

        List<LauCreativeTemplateDo> dos = new ArrayList<>();
        // 遍历 slotGroup，准备创意模板 po list
        slotGroupId2TemplateDto.forEach((k, v) -> {
            // 根据规则获取广告位组的底价
            final int reservedPrice = cpcUnitService.getFlyBanner4LowestCost4Creative(GetBidCostParam.builder()
                    .accountId(aid)
                    .slotGroupId(k)
                    .launchType(ppt)
                    .salesType(st)
                    .build(), ocpcTarget);

            final int cardType = v.getCardType();
            final boolean isStory = storyTemplateIds.contains(v.getTemplateId())
                    || creativeTemplateConfig.getDarkFlyTemplateStory().equals(v.getTemplateId());
            final boolean isPcIndex = pcIndexTemplateIds.contains(v.getTemplateId())
                    || creativeTemplateConfig.getDarkFlyTemplatePcIndex().equals(v.getTemplateId());
            final boolean isPcPlay = pcPlayTemplateIds.contains(v.getTemplateId())
                    || creativeTemplateConfig.getDarkFlyTemplatePcPlay().equals(v.getTemplateId());
            final boolean isUnderBox = underBoxTemplateIds.contains(v.getTemplateId())
                    || creativeTemplateConfig.getDarkFlyTemplateUnderBox().equals(v.getTemplateId());

            // 根据推广目的等条件获取配置的商业标
            int busMarkId = this.generateBusMarkId(unit, operator, Arrays.asList(cardType),
                    launchTarget, isStory, isPcIndex, isPcPlay, isUnderBox, v.getTemplateId());
            if (isMiddleAd && searchTemplateIds.contains(v.getTemplateId())) {
                // 三连搜索设置火箭标（三连只有托管会进入这个方法）
                busMarkId = rocketBusMarkId;
            }
            if (!isMiddleAd && this.isRocketBusMark(v.getTemplateId())) {
                // 内容起飞搜索设置火箭表
                busMarkId = rocketBusMarkId;
            }

            LauCreativeTemplateDo lauCreativeTemplateDo = new LauCreativeTemplateDo();
            lauCreativeTemplateDo.setCreativeId(newCreativeId.longValue());
            lauCreativeTemplateDo.setAccountId(creative.getAccountId());
            lauCreativeTemplateDo.setUnitId(creative.getUnitId());
            lauCreativeTemplateDo.setSlotGroupId(k);
            lauCreativeTemplateDo.setTemplateId(v.getTemplateId());
            lauCreativeTemplateDo.setIsDeleted(IsDeleted.VALID.getCode());
            lauCreativeTemplateDo.setReservedPrice(reservedPrice);

            int bizStatus = BIZ_STATUS_OK;
            if (unit.getIsNoBid() != null && Utils.isPositive(unit.getIsNoBid())) {
                bizStatus = BIZ_STATUS_OK;
            } else {
                bizStatus = bidPrice < reservedPrice ? BIZ_STATUS_UNDER_RESERVED_PRICE : BIZ_STATUS_OK;
            }
            lauCreativeTemplateDo.setBizStatus(bizStatus);

            // 商业标
            lauCreativeTemplateDo.setBusMarkId(busMarkId);
            // 创意样式
            lauCreativeTemplateDo.setCreativeStyle(this.banner4SlotGroupId2CreativeStyle(k, creative.getPromotionPurposeType(), creative.getPromotionPurposeContent()));
            dos.add(lauCreativeTemplateDo);
        });
        //如果是编辑创意，获取应该删除的id
        if (creative.getCreativeId() != null && creative.getCreativeId() > 0) {
            List<Integer> doSlotGroupIds = dos.stream().map(LauCreativeTemplateDo::getSlotGroupId).collect(Collectors.toList());
            List<LauCreativeTemplateDo> lauCreativeTemplateDos = adCoreBqf.select(
                            Projections.bean(LauCreativeTemplateDo.class,
                                    lauCreativeTemplate.id,
                                    lauCreativeTemplate.slotGroupId))
                    .from(lauCreativeTemplate)
                    .where(lauCreativeTemplate.creativeId.eq(creative.getCreativeId().longValue()))
                    .where(lauCreativeTemplate.isDeleted.eq(IsDeleted.VALID.getCode()))
                    .fetch();
            List<Long> needDeleteIds = new ArrayList<>();
            lauCreativeTemplateDos.stream().forEach(o -> {
                if (!doSlotGroupIds.contains(o.getSlotGroupId())) {
                    needDeleteIds.add(o.getId());
                }
            });
            //删除
            if (!CollectionUtils.isEmpty(needDeleteIds)) {
                adCoreBqf.update(lauCreativeTemplate)
                        .set(lauCreativeTemplate.isDeleted, IsDeleted.DELETED.getCode())
                        .where(lauCreativeTemplate.creativeId.in(creative.getCreativeId().longValue()))
                        .where(lauCreativeTemplate.id.in(needDeleteIds))
                        .execute();
            }
        }
        //插入&更新 creative_id,slot_group_id 为key的一条数据
        adCoreBqf.insertMysql(lauCreativeTemplate).insertDuplicateUpdates(dos);
    }

    private boolean hasStoryPermission(Integer launchTarget, CpcUnitDto unit, Integer accountId) {
        if (LaunchTargetEnum.ACCOUNT_GROWTH.getCode().equals(launchTarget) ||
                LaunchTargetEnum.TRAFFIC_BOOST.getCode().equals(launchTarget)) {
            // 稿件 CPM
            if (SalesType.CPM.getCode() == unit.getSalesType()) {
                //CPM
                if (unit.getOcpcTarget() <= 0) {
                    if (!this.accountLabelService.isAccountIdsInLabels(Collections.singletonList(accountId),
                            Collections.singletonList(creativePositionConfig.getStoryBlackListLabelId()))) {
                        return true;
                    }
                } else {
                    //OCPM
                    if (!this.accountLabelService.isAccountIdsInLabels(Collections.singletonList(accountId),
                            Collections.singletonList(creativePositionConfig.getStoryBlackListLabelId()))) {
                        if (OcpcTargetEnum.USER_FOLLOW.getCode().equals(unit.getOcpcTarget()) ||
                                OcpcTargetEnum.VIDEO_PLAY.getCode().equals(unit.getOcpcTarget())) {
                            return true;
                        }
                    }
                }
            }
        }
        return false;
    }

    public Boolean isRocketBusMark(Integer templateId) {
        return searchPositionConfig.getRocketMarkTemplateIds().contains(templateId);
    }

    public Boolean accountIdInLabel(Integer accountId, Integer labelId) {
        return labelId == 0 || this.accountLabelService.isAccountIdsInLabels(Collections.singletonList(accountId),
                Collections.singletonList(labelId));
    }

    /**
     * 根据推广目的等条件获取配置的商业标
     *
     * @param unit
     * @param operator
     * @param cardTypes
     * @param launchTarget
     * @param isStory
     * @param isPcIndex
     * @param isPcPlay
     * @param isUnderBox
     * @return
     */
    public Integer generateBusMarkId(CpcUnitDto unit, Operator operator, List<Integer> cardTypes, int launchTarget,
                                     boolean isStory, boolean isPcIndex, boolean isPcPlay, boolean isUnderBox, Integer templateId) {

        //起飞gd+固定火箭标
        if (IsValid.TRUE.getCode().equals(unit.getIsGdPlus())) {
            return rocketBusMarkId;
        }
        //商业直播间固定 创作推广
        if (LaunchTargetEnum.BUSINESS_LIVE_PROMOTION == LaunchTargetEnum.getByCode(launchTarget)) {
            return flyCreationPromotionMarkId;
        }
        //内容直播间固定 直播间
        if (LaunchTargetEnum.LIVE_PROMOTION == LaunchTargetEnum.getByCode(launchTarget)) {
            return flyLiveBusMarkId;
        }
        // 活动推广商业标
        if (LaunchTargetEnum.ACTIVITY == LaunchTargetEnum.getByCode(launchTarget)) {
            return activityBusMarkId;
        }

        // 根据帐号和 cardType 获取有效的商业标列表
        List<BusMarkDto> markList = busMarkRuleService.getValidBusMarkList(operator.getOperatorId(),
                BusMarkRuleAdSysEnum.CPC, cardTypes);
        Integer busMarkId = 0;
        final boolean isTv = tvTemplateIds.contains(templateId);

        //投稿内容
        if (PromotionPurposeType.ARCHIVE_CONTENT.getCode() == unit.getPromotionPurposeType()) {
            if (!CollectionUtils.isEmpty(markList)) {
                List<Integer> markIds = markList.stream().map(BusMarkDto::getId).collect(Collectors.toList());
                //特殊账号不展示标
                if (noBusMarkAccountIds.contains(unit.getAccountId()) && markIds.contains(noBusMarkId)) {
                    busMarkId = noBusMarkId;
                } else {
                    if (isStory) {
                        return storyBusMarkId;
                    }
                    if (isPcIndex) {
                        return pcIndexBusMarkId;
                    }
                    if (isPcPlay) {
                        return pcPlayBusMarkId;
                    }
                    if (isUnderBox) {
                        return upRecommendBusMarkId;
                    }
                    // tv 商业标
                    if (isTv) {
                        return tvBusMarkId;
                    }
                    //优先拿"游戏标"
                    if (markIds.contains(gameBusMarkId)) {
                        busMarkId = gameBusMarkId;
                    } else {
                        //其次拿"起飞-内容推广标-新"
                        if (markIds.contains(flyBusMarkId)) {
                            busMarkId = flyBusMarkId;
                        }
                    }
                }
            }
        }
        //动态拿"起飞-内容推广标-新"
        if (PromotionPurposeType.DYNAMIC.getCode() == unit.getPromotionPurposeType()) {
            if (!CollectionUtils.isEmpty(markList)) {
                List<Integer> markIds = markList.stream().map(o -> o.getId()).collect(Collectors.toList());
                if (markIds.contains(flyBusMarkId)) {
                    busMarkId = flyBusMarkId;
                }
            }
        }
        return busMarkId;
    }

    private Integer slotGroupId2CreativeStyle(Integer slotGroupId, Integer promotionPurpose) {
        //稿件
        if (ObjectUtils.nullSafeEquals(PromotionPurposeType.ARCHIVE_CONTENT.getCode(), promotionPurpose)) {
            if (creativePositionConfig.getStaticSlotGroupIds().contains(slotGroupId) ||
                    creativePositionConfig.getStaticDynamicFlowSlotGroupIds().contains(slotGroupId) ||
                    creativePositionConfig.getIPadRecommendSlotGroupIds().contains(slotGroupId) ||
                    creativePositionConfig.getPcIndexSlotGroupIds().contains(slotGroupId) ||
                    creativePositionConfig.getPcPlaySlotGroupIds().contains(slotGroupId)
            ) {
                return CreativeStyle.AVID.getCode();
            }

            // 内容起飞搜索，点击播放
            if (searchPositionConfig.getFlyContentSearchSlotGroupId().equals(slotGroupId) ||
                    searchPositionConfig.getFlyContentSearchAutoSlotGroupId().equals(slotGroupId)) {
                return CreativeStyle.IMAGE.getCode();
            }
            if (creativePositionConfig.getGifSlotGroupIds().contains(slotGroupId)) {
                return CreativeStyle.GIF.getCode();
            }
            if (creativePositionConfig.getStaticInlineSlotGroupIds().contains(slotGroupId)
            ) {
                return CreativeStyle.VIDEO.getCode();
            }
            if (creativePositionConfig.getStorySlotGroupIds().contains(slotGroupId)) {
                return CreativeStyle.BVID.getCode();
            }
        }
        //直播间
        if (ObjectUtils.nullSafeEquals(PromotionPurposeType.LIVE_ROOM.getCode(), promotionPurpose)) {
            if (creativePositionConfig.getStaticSlotGroupIds().contains(slotGroupId)) {
                return CreativeStyle.IMAGE.getCode();
            }
            if (creativePositionConfig.getGifSlotGroupIds().contains(slotGroupId)) {
                return CreativeStyle.GIF.getCode();
            }
        }
        //动态
        if (ObjectUtils.nullSafeEquals(PromotionPurposeType.DYNAMIC.getCode(), promotionPurpose)) {
            return CreativeStyle.IMAGE.getCode();
        }
        return null;
    }

    private Integer banner4SlotGroupId2CreativeStyle(Integer slotGroupId, Integer promotionPurpose, String url) {
        //稿件
        if (ObjectUtils.nullSafeEquals(PromotionPurposeType.ARCHIVE_CONTENT.getCode(), promotionPurpose)) {
            // 小卡
            // +暗投的1个
            if (creativePositionConfig.getFlySlotArchiveSmall().equals(slotGroupId) ||
                    creativePositionConfig.getFlySlotArchiveSmallUnderBox().equals(slotGroupId) ||
                    creativePositionConfig.getDarkFlySlotSmall().equals(slotGroupId) ||
                    creativePositionConfig.getMsgFlowAndroidPadSlotGroupId().equals(slotGroupId) ||
                    Objects.equals(creativePositionConfig.getSmallCard43SlotGroupId(), slotGroupId)) {
                if (StringUtils.endsWith(url, "gif")) {
                    return CreativeStyle.GIF.getCode();
                } else {
                    return CreativeStyle.AVID.getCode();
                }
            }
            // 播放页、动态位、iPAD、PC首栏推荐、PC播放页、框下
            // +暗投的5个
            if (creativePositionConfig.getFlySlotArchivePlay().equals(slotGroupId) ||
                    creativePositionConfig.getFlySlotArchivePlayUnderBox().equals(slotGroupId) ||
                    creativePositionConfig.getFlySlotArchiveDynamic().equals(slotGroupId) ||
                    creativePositionConfig.getFlySlotArchiveIPad().equals(slotGroupId) ||
                    creativePositionConfig.getFlySlotArchivePcIndex().equals(slotGroupId) ||
                    creativePositionConfig.getFlySlotArchivePcPlay().equals(slotGroupId) ||
                    creativePositionConfig.getFlySlotArchiveUnderBox().equals(slotGroupId) ||
                    creativePositionConfig.getFlySlotArchiveTvInline().equals(slotGroupId) ||
                    creativePositionConfig.getDarkFlySlotPlay().equals(slotGroupId) ||
                    creativePositionConfig.getDarkFlySlotIPad().equals(slotGroupId) ||
                    creativePositionConfig.getDarkFlySlotPcIndex().equals(slotGroupId) ||
                    creativePositionConfig.getDarkFlySlotPcPlay().equals(slotGroupId) ||
                    creativePositionConfig.getDarkFlySlotUnderBox().equals(slotGroupId) ||
                    creativePositionConfig.getPlayPageAndroidPadSlotGroupId().equals(slotGroupId) ||
                    Objects.equals(doFlyCreativePositionService.daihuoHiddenOttSlotGroupId, slotGroupId) ||
                    Objects.equals(creativePositionConfig.getDynamicFeedsHiddenSlotGroupId(), slotGroupId)
//                    Objects.equals(creativePositionConfig.getUnderFrameHiddenSlotGroupId(), slotGroupId)
            ) {
                return CreativeStyle.AVID.getCode();
            }
            // 大卡
            // +暗投的1个
            if (creativePositionConfig.getFlySlotArchiveBig().equals(slotGroupId) ||
                    creativePositionConfig.getDarkFlySlotBig().equals(slotGroupId)) {
                return CreativeStyle.VIDEO.getCode();
            }
            // story
            // +暗投的1个
            if (creativePositionConfig.getFlySlotArchiveStory().equals(slotGroupId) ||
                    creativePositionConfig.getDarkFlySlotStory().equals(slotGroupId)) {
                return CreativeStyle.BVID.getCode();
            }
            // 搜索
            if (creativePositionConfig.getFlySlotSearch().equals(slotGroupId)) {
                return CreativeStyle.IMAGE.getCode();
            }
            // 搜索
            if (creativePositionConfig.getFlySlotSearchAuto().equals(slotGroupId)) {
                return CreativeStyle.IMAGE.getCode();
            }
            // 搜索明投
            if (creativePositionConfig.getFlySlotChooseSearch().equals(slotGroupId)) {
                return CreativeStyle.IMAGE.getCode();
            }
            // PC搜索
            if (creativePositionConfig.getFlyPcSlotSearch().equals(slotGroupId)) {
                return CreativeStyle.AVID.getCode();
            }
            // iPAD搜索
            if (creativePositionConfig.getFlyIPadSearchSlotGroupId().equals(slotGroupId)) {
                return CreativeStyle.IMAGE.getCode();
            }
            // 暗投稿件动态
            if (creativePositionConfig.getSlotGroupIdDynamicDarkFlyOfArchive().equals(slotGroupId)) {
                return CreativeStyle.VIDEO.getCode();
            }
        }
        //直播间
        if (ObjectUtils.nullSafeEquals(PromotionPurposeType.LIVE_ROOM.getCode(), promotionPurpose)) {
            if (StringUtils.endsWith(url, "gif")) {
                return CreativeStyle.GIF.getCode();
            } else {
                return CreativeStyle.IMAGE.getCode();
            }
        }
        //动态
        if (ObjectUtils.nullSafeEquals(PromotionPurposeType.DYNAMIC.getCode(), promotionPurpose)) {
            // 小卡是搜索明投都是IMAGE
            return CreativeStyle.IMAGE.getCode();
        }
        if (ObjectUtils.nullSafeEquals(PromotionPurposeType.OGV.getCode(), promotionPurpose)) {
            //小卡
            if (creativePositionConfig.getFlySlotOgvSmall().equals(slotGroupId)) {
                if (StringUtils.endsWith(url, "gif")) {
                    return CreativeStyle.GIF.getCode();
                } else {
                    return CreativeStyle.AVID.getCode();
                }
            }
            //大卡
            if (creativePositionConfig.getFlySlotOgvBig().equals(slotGroupId)) {
                return CreativeStyle.VIDEO.getCode();
            }
            // 搜索明投
            if (creativePositionConfig.getFlySlotChooseSearch().equals(slotGroupId)) {
                return CreativeStyle.IMAGE.getCode();
            }
        }
        if (Objects.equals(slotGroupId, creativePositionConfig.getStoryHiddenExpSlotGroupId())) {
            return CreativeStyle.VIDEO.getCode();
        }
        return null;
    }

    public String saveFormatedJumpUrl(Integer creativeId, CpcUnitDto unit,
                                      String landingPageUrl, Integer roomPromoteScenes, Boolean isInner) {
        String formatedUrl = landingPageUrl;

        if (unit.getPromotionPurposeType().equals(PromotionPurposeType.ARCHIVE_CONTENT.getCode())) {
            formatedUrl = LaunchUtil.flyProArchiveLinkAddSuffixWithCreativeId(landingPageUrl, unit.getOcpcTarget(), creativeId);
        }

        if (unit.getPromotionPurposeType().equals(PromotionPurposeType.LIVE_ROOM.getCode())) {
            if (RoomPromoteScenesEnum.RESERVE.getCode().equals(roomPromoteScenes)) {
                formatedUrl = LaunchUtil.flyProArchiveLinkAddSuffixWithCreativeId(landingPageUrl, unit.getOcpcTarget(), creativeId);
            } else {
                formatedUrl = cpcSaveCreativeService.decorateLiveRoomUrl(landingPageUrl);
            }
        }

        if (landingPageUrl.contains(MgkConstants.H5_LANDING_PAGE.replace(MgkConstants.HTTP_SCHEME, ""))) {
            formatedUrl = UriComponentsBuilder.fromUriString(landingPageUrl)
                    .query(Constants.MGK_PAGE_MACRO_PARAM)
                    .build(false).toUriString();
        }

        if (Utils.isPositive(unit.getGameBaseId())) {
            cpcSaveCreativeService.decorateGameJumpUrl(landingPageUrl, LaunchConstant.GAME_HIDDEN_PARAM_VALUE_9788, isInner, false);
        }

        if (Strings.isNullOrEmpty(formatedUrl) || formatedUrl.equals(landingPageUrl)) {
            return landingPageUrl;
        }

        LauUnitCreativePo po = new LauUnitCreativePo();
        po.setCreativeId(creativeId);
        po.setPromotionPurposeContent(formatedUrl);

        lauUnitCreativeDao.updateByPrimaryKeySelective(po);

        return formatedUrl;
    }

    private void checkSlotGroupWithAutoPlay(VirtualTemplateGroupDto virtualTemplateGroupDto) {
        AssertCheckUtils.notEmpty(virtualTemplateGroupDto.getResSlotGroupTemplateMappingDtos(), LaunchExceptionCode.NO_VALID_SLOT_GROUP_FOUND);//稿件auto_play=0,可能会报这个错
    }

    public void validateCreativeTemplate(List<CpcCreativeDto> creativeList, Map<Integer, List<ResSlotGroupTemplateMappingDto>> map,
                                         Integer launchTarget, Operator operator, Integer roomPromoteScenes) throws ServiceException {
        Assert.notEmpty(creativeList, "创意信息不可为空");
        for (CpcCreativeDto creative : creativeList) {
            VirtualTemplateGroupDto virtualTemplateGroupDto = doFlyCreativePositionService.generateVirtualTemplateGroup(map, creative, launchTarget, operator, roomPromoteScenes);
            checkSlotGroupWithAutoPlay(virtualTemplateGroupDto);
            this.validateByTemplate(creative, virtualTemplateGroupDto, false, false);
        }
    }

    public void validateByTemplate(CpcCreativeDto creative, VirtualTemplateGroupDto virtualTemplateGroupDto,
                                   boolean skipCheckDescription, boolean isSceneMerge) throws ServiceException {
        LOGGER.info("CreativeService.create.validateCreative param[creative-{}, virtualTemplateGroupDto-{}]", creative, virtualTemplateGroupDto);
        if (creative == null || virtualTemplateGroupDto == null) {
            LOGGER.info("CreativeService.create.validateCreative param is null, perhaps templateGroup not exist.");
            throw new ServiceException(LaunchExceptionCode.REQUIRED_PARAM);
        }

        cpcCreativeValidator.validateWildcard(creative.getTitle());

        String realTitle = cpcCreativeValidator.replaceWildcard(creative.getTitle());

        if (!CpcCreativeValidator.validateText(virtualTemplateGroupDto.getIsFillTitle()
                , virtualTemplateGroupDto.getTitleMinLength()
                , virtualTemplateGroupDto.getTitleMaxLength()
                , realTitle)) {
            throw new ServiceException(LaunchExceptionCode.CREATIVE_TITLE_VALID);
        }
        if (!skipCheckDescription) {
            if (virtualTemplateGroupDto.getIsFillDesc() != null && virtualTemplateGroupDto.getIsFillDesc()) {
                if (!CpcCreativeValidator.textWithinRange(creative.getDescription(),
                        virtualTemplateGroupDto.getDescMinLength(), virtualTemplateGroupDto.getDescMaxLength())) {
                    throw new ServiceException(LaunchExceptionCode.CREATIVE_DESCRIPTION_VALID);
                }
            } else if (!isSceneMerge) {
                // 不支持描述的情况, 不验证直接清空数据, 因为不支持描述的情况前端没有文字框编辑描述
                // 场景合并的不作处理
                creative.setDescription("");
            }
        }

        if (IsValid.TRUE.getCode().equals(virtualTemplateGroupDto.getIsFillExtDesc())) {
            if (!CpcCreativeValidator.textWithinRange(creative.getExtDescription(), virtualTemplateGroupDto.getExtDescMinLength(), virtualTemplateGroupDto.getExtDescMaxLength())) {
                throw new ServiceException(LaunchExceptionCode.CREATIVE_EXT_DESCRIPTION_VALID);
            }
        } else {
            creative.setExtDescription("");
        }

        //起飞不支持按钮
        //validateButtonCopy(creative, virtualTemplateGroupDto);

        if (!CollectionUtils.isEmpty(creative.getImageDtos())) {
            for (ImageDto imageDto : creative.getImageDtos()) {
                if (!cpcCreativeValidator.flyValidateUrl(
                        virtualTemplateGroupDto.getIsSupportImage() != null &&
                                virtualTemplateGroupDto.getIsSupportImage()
                        , imageDto.getUrl())) {

                    throw new ServiceException(LaunchExceptionCode.CREATIVE_IMAGE_INVALID);
                }

                if (!virtualTemplateGroupDto.getIsSupportGif() && imageDto.getUrl() != null
                        && (imageDto.getUrl().endsWith("webp") || imageDto.getUrl().endsWith("gif"))) {
                    throw new ServiceException("创意投放场景不支持gif素材");
                }

                if (Strings.isNullOrEmpty(imageDto.getUrl())
                        && !Strings.isNullOrEmpty(imageDto.getMd5())) {
                    throw new ServiceException(LaunchExceptionCode.CREATIVE_IMAGE_MD5_NO_INPUT);
                } else if (!Strings.isNullOrEmpty(imageDto.getUrl()) && Strings.isNullOrEmpty(imageDto.getMd5())) {
                    throw new ServiceException(LaunchExceptionCode.CREATIVE_IMAGE_MD5_INVALID);
                } else if (!Strings.isNullOrEmpty(imageDto.getMd5()) && imageDto.getMd5().length() != Constants.MD5_LENGTH) {
                    throw new ServiceException(LaunchExceptionCode.CREATIVE_IMAGE_MD5_INVALID);
                }

                cpcCreativeValidator.validateJumpUrl(imageDto.getJumpUrl(), "图片跳转");
                cpcCreativeValidator.validateScheme(creative.getAccountId(), imageDto.getCallUpUrl(), null, null);
                if (!CollectionUtils.isEmpty(imageDto.getClickReportUrls())) {
                    imageDto.getClickReportUrls().forEach(reportUrl -> cpcCreativeValidator.validateJumpUrl(reportUrl, "图片点击监控"));
                }
            }
        }

        if (!cpcCreativeValidator.validateUrl(IsValid.TRUE.getCode().equals(virtualTemplateGroupDto.getIsSupportExtImage())
                , creative.getExtImageUrl())) {
            throw new ServiceException(LaunchExceptionCode.CREATIVE_EXT_IMAGE_INVALID);
        }

        if (Strings.isNullOrEmpty(creative.getExtImageUrl())
                && !Strings.isNullOrEmpty(creative.getExtImageMd5())) {
            throw new ServiceException(LaunchExceptionCode.CREATIVE_EXT_IMAGE_MD5_NO_INPUT);
        } else if (!Strings.isNullOrEmpty(creative.getExtImageUrl()) && Strings.isNullOrEmpty(creative.getExtImageMd5())) {
            throw new ServiceException(LaunchExceptionCode.CREATIVE_EXT_IMAGE_MD5_INVALID);
        } else if (!Strings.isNullOrEmpty(creative.getExtImageMd5()) && creative.getExtImageMd5().length() != 32) {
            throw new ServiceException(LaunchExceptionCode.CREATIVE_EXT_IMAGE_MD5_INVALID);
        }
    }

    public void validatorBanner4CreateCreative(List<CpcCreativeDetailDto> saveCreativeDtos, CpcUnitDto unit,
                                               List<CpcCreativeDto> creativeList, Operator operator,
                                               FlyProUnitPositionsDto flyProUnitPositionsDto) throws ServiceException {

        validator.validateUnitInfo(unit);

        for (CpcCreativeDetailDto saveCreativeDto : saveCreativeDtos) {
            Assert.notNull(saveCreativeDto, "创意信息不可为空");
            Assert.notNull(saveCreativeDto.getCmMark(), "广告标识不可为空");
            validator.validateUnitCreativeCommonInfo(saveCreativeDto, true);
        }

        validator.validateCreativeNumber(creativeList, unit.getUnitId());

        // 校验素材
        // todo creative上的videoId查询后续是否可复用
        this.validateMaterialInfo(unit, saveCreativeDtos);

        FlyProSelectScenesDto flyProSelectScenesDto = doFlyCreativePositionService.creativeDto2FlyProSelectScenesDto(creativeList.get(0));
        // 这边用不特殊处理story的方法
        // todo 逻辑可复用待确认
        List<FlyProAttributeDto> flyProAttributeDtos = doFlyCreativePositionService.getAllFlyProCreativeStyle(flyProSelectScenesDto, unit.getUnitId(), operator.getOperatorId());
        Map<Integer, FlyProAttributeDto> flyProAttributeDtosMap = flyProAttributeDtos.stream().collect(Collectors.toMap(o -> o.getKey(), o -> o));
        log.info("validatorBanner4CreateCreative, flyProAttributeDtosMap.keys:{}", JSON.toJSONString(flyProAttributeDtosMap.keySet()));

        //每个创意进行校验
        for (CpcCreativeDto creative : creativeList) {
            log.info("validatorBanner4CreateCreative, cid:{}, camId:{}, creative.getFlyBannerCoverType:{}", creative.getCreativeId(), creative.getCampaignId(), creative.getFlyBannerCoverType());
            if (FlyBanner4CoverEnum.isSceneMerge(creative.getFlyBannerCoverType())) {
                Assert.isTrue(!CollectionUtils.isEmpty(flyProAttributeDtosMap), "未找到起飞场景:" + FlyBanner4CoverEnum.desc(creative.getFlyBannerCoverType()));
                for (FlyProAttributeDto flyProAttributeDto : flyProAttributeDtosMap.values()) {
                    validateBanner4Creative(flyProAttributeDto, unit, flyProUnitPositionsDto, creative, true);
                }
            } else {
                FlyProAttributeDto flyProAttributeDto = flyProAttributeDtosMap.get(creative.getFlyBannerCoverType());
                Assert.notNull(flyProAttributeDto, "未找到起飞场景:" + FlyBanner4CoverEnum.desc(creative.getFlyBannerCoverType()));
                validateBanner4Creative(flyProAttributeDto, unit, flyProUnitPositionsDto, creative, false);
            }
        }
    }

    @SneakyThrows
    private void validateBanner4Creative(FlyProAttributeDto flyProAttributeDto, CpcUnitDto unit,
                                         FlyProUnitPositionsDto flyProUnitPositionsDto, CpcCreativeDto creative, boolean isSceneMerge) {
        VirtualTemplateGroupDto virtualTemplateGroupDto = doFlyCreativePositionService.flyProAttributeDto2virtualTemplateGroupDto(flyProAttributeDto);
        boolean onlyStoryOrBigCard = this.onlyStoryOrBigCard(unit, flyProUnitPositionsDto, creative);
        // 创意仅story大卡（非托管），跳过描述校验（描述会被暗投赋值位稿件up昵称）
        Assert.notNull(virtualTemplateGroupDto, "未找到模板组");
        this.validateByTemplate(creative, virtualTemplateGroupDto, onlyStoryOrBigCard, isSceneMerge);
    }

    /**
     * 根据 convType 获取两种规格的 Map<slotGroupId, 模板>
     *
     * @param coverType              16比10 还是 16比9
     * @param flyProUnitPositionsDto
     * @return
     */
    public Map<Integer, TemplateDto> getMapByFlyBanner4CoverType(Integer coverType, FlyProUnitPositionsDto flyProUnitPositionsDto) {
        // 根据 conv type 获取 模板
        Map<Integer, TemplateDto> slotGroupId2TemplateDto = new HashMap<>();
        if (FlyBanner4CoverEnum.SIZE_16_10.getCode().equals(coverType)) {
            slotGroupId2TemplateDto = flyProUnitPositionsDto.getPosition_map_16_10();
        }
        if (FlyBanner4CoverEnum.SIZE_16_9.getCode().equals(coverType)) {
            slotGroupId2TemplateDto = flyProUnitPositionsDto.getPosition_map_16_9();
        }
        if (FlyBanner4CoverEnum.SIZE_4_3.getCode().equals(coverType)) {
            slotGroupId2TemplateDto = flyProUnitPositionsDto.getPosition_map_4_3();
        }
        if (FlyBanner4CoverEnum.isSceneMerge(coverType)) {
            slotGroupId2TemplateDto.putAll(flyProUnitPositionsDto.getPosition_map_16_10());
            slotGroupId2TemplateDto.putAll(flyProUnitPositionsDto.getPosition_map_16_9());
        }
        return slotGroupId2TemplateDto;
    }

    /**
     * 保存创意列表
     *
     * @param operator
     * @param saveCreativeDtos
     * @param deleteOtherInUnit
     * @param account
     * @param unit
     * @return
     * @throws ServiceException
     */
    @Transactional(value = AD_CORE_TM, rollbackFor = Exception.class)
    public List<Integer> saveMiddleFlyBanner4(Operator operator, List<CpcCreativeDetailDto> saveCreativeDtos,
                                              boolean deleteOtherInUnit, AccountBaseDto account, CpcUnitDto unit,
                                              boolean isPersonalFly) throws ServiceException {

        FlyCampaignDetailDto flyCampaignDetailDto = flyCampaignService.getFlyCampaignById(unit.getCampaignId());
        // 单元下三种类型创意规格(16_10, 16_9, 4_3) 的 Map<广告位组id, 模板>
        // todo 耗时 暂无优化
        FlyProUnitPositionsDto flyProUnitPositionsDto = doFlyCreativePositionService.middleCreativeDetails2slotGroupIdAndTemplateDto(
                unit, saveCreativeDtos, operator, unit.getPromotionPurposeType(),
                flyCampaignDetailDto.getRoomPromoteScenes(), true, account);

        // 转换成创意列表
        List<CpcCreativeDto> creativeList = this.saveCreativeDto2List(saveCreativeDtos, unit, null, operator, flyProUnitPositionsDto);

        LauCampaignPo lauCampaignPo = outerLauCampaignRepo.queryCampaignById(unit.getCampaignId());
        Arc arc = null;
        LauUnitArchiveVideoPo unitArchiveVideoPo = outerLauUnitRepo.queryUnitArchive(unit.getUnitId());
        if (unitArchiveVideoPo != null) {
            arc = archiveServiceProxy.arc(unitArchiveVideoPo.getVideoId()).getArc();
        }
        specificStorySceneAndNoCoversProcess(lauCampaignPo, unit, creativeList, arc);

        this.validatorBanner4CreateCreative(saveCreativeDtos, unit, creativeList, operator, flyProUnitPositionsDto);

        // 获取已存在的创意 map
        List<Integer> reserveIds = creativeList.stream()
                .map(CpcCreativeDto::getCreativeId)
                .filter(Utils::isPositive)
                .collect(Collectors.toList());
        Map<Integer, CpcCreativeDto> reserveCreativeMap = cpcSaveCreativeService.getMapInIds(reserveIds);

        if (deleteOtherInUnit) {
            cpcSaveCreativeService.deleteCreativeByUnitId(unit.getUnitId(), reserveIds);
        }

        // 保存选中的投放位置（lau_unit_fly_ext_info）
        flyUnitService.saveFlyBanner4UnitLaunchScenes(unit.getAccountId(), unit.getUnitId(),
                creativeList.get(0).getFlyBannerUnitScenesType(),
                creativeList.get(0).getFlyBannerUnitSpecificScenesType());


        LauUnitFlyMiddleInfoPo lauUnitFlyMiddleInfoPo = middleFlyUnitService.queryLauUnitFlyMiddleInfo(unit.getUnitId());
        Integer gameBaseId = null;
        Integer gamePlatformType = GamePlatformTypeEnum.ANDROID.getCode();
        ;
        int subPkg = 0;
        final boolean isInner = Objects.equals(account.getIsInner(), 1);
        if (Utils.isPositive(unit.getGameBaseId())) {
            gameBaseId = unit.getGameBaseId();
            gamePlatformType = unit.getGamePlatformType();
            subPkg = unit.getSubPkg();
        }
        // 添加游戏隐藏监控
        if (Utils.isPositive(gameBaseId)) {
            final CpcCreativeMonitoringDto cpcCreativeMonitoringDto = cpcSaveCreativeService.genGameHiddenMonitoring(gameBaseId, gamePlatformType, subPkg, isInner);
            for (CpcCreativeDto cpcCreativeDto : creativeList) {
                if (CollectionUtils.isEmpty(cpcCreativeDto.getCreativeMonitoring())) {
                    cpcCreativeDto.setCreativeMonitoring(Collections.singletonList(cpcCreativeMonitoringDto));
                } else {
                    cpcCreativeDto.getCreativeMonitoring().add(cpcCreativeMonitoringDto);
                }
            }
        }

        List<Integer> savedCreativeIds = this.saveFlyMiddleCreativeNew(unit, creativeList, reserveCreativeMap, account, operator,
                operator.getOperatorName(), flyProUnitPositionsDto, lauUnitFlyMiddleInfoPo, isPersonalFly,
                lauCampaignPo, arc);

        // for promotionPurposeContent compare
        for (CpcCreativeDto cpcCreativeDto : creativeList) {
            cpcCreativeDto.setPromotionPurposeContent(JumpTypeEnum.getByCode(cpcCreativeDto.getJumpType())
                    .parseLaunchUrl(cpcCreativeDto.getPromotionPurposeContent()));
        }

        cpcSaveCreativeService.addCreativeInsertLog(operator, creativeList, reserveCreativeMap, savedCreativeIds, Collections.emptyMap(), Collections.emptyMap());

        return savedCreativeIds;
    }

    /**
     * 存在指定场景 story，没有封面的处理
     *
     * @param unit
     * @param creativeList
     */
    private void specificStorySceneAndNoCoversProcess(LauCampaignPo lauCampaignPo, CpcUnitDto unit,
                                                      List<CpcCreativeDto> creativeList, Arc arc) {

        // 没有标签不处理
//        if (!this.accountIdInLabel(unit.getAccountId(), creativePositionConfig.getLabelIdStoryDarkFlySlotSearchClick())) {
//            return;
//        }
        if (!flyUnitJudger.isFlyUnit(lauCampaignPo.getPromotionPurposeType(), unit.getPromotionPurposeType())) {
            return;
        }

        // 存在指定场景 story，没有封面的情况
        boolean existPointStorySceneAndNoCover =
                creativeList.stream().anyMatch(flyCreativeDto -> isSpecificStoryAndNoImages(flyCreativeDto));
        String videoCover = "";
        log.info("specificStorySceneAndNoCoversProcess,existPointStorySceneAndNoCover:{}", existPointStorySceneAndNoCover);
        if (existPointStorySceneAndNoCover && arc != null) {
            // 存在指定场景 story，没有封面的情况(上面先处理了创意模板，然后添加暗投图片)
            // 获取单元稿件封面
            videoCover = arc.getPic();
        }
        for (CpcCreativeDto cpcCreativeDto : creativeList) {
            // 指定场景 story, 没有封面，后端设置稿件的封面
            if (isSpecificStoryAndNoImages(cpcCreativeDto)) {
                if (!StringUtils.isEmpty(videoCover)) {
                    LOGGER.info("specificStorySceneAndNoCoversProcess, 指定场景story&没有图片,获取稿件封面:{}", videoCover);
                    List<ImageDto> imageDtos = new ArrayList<>();
                    String md5 = DigestUtils.md5Hex(HttpUtils.download(videoCover));
                    imageDtos.add(ImageDto.builder().url(videoCover).md5(md5).build());
                    cpcCreativeDto.setImageDtos(imageDtos);
                    cpcCreativeDto.setImageUrl(videoCover);
                    cpcCreativeDto.setImageMd5(md5);
                }
            }
        }
    }

    /**
     * 是否指定场景 & 没有封面
     *
     * @param flyCreativeDto
     * @return
     */
    private static boolean isSpecificStoryAndNoImages(CpcCreativeDto flyCreativeDto) {
        return ScenesEnum.MOBILE_SCENES.getCode().equals(flyCreativeDto.getFlyBannerUnitScenesType()) &&
                !CollectionUtils.isEmpty(flyCreativeDto.getFlyBannerUnitSpecificScenesType()) &&
                flyCreativeDto.getFlyBannerUnitSpecificScenesType().size() == 1 &&
                flyCreativeDto.getFlyBannerUnitSpecificScenesType().contains(SpecificScenesEnum.STORY.getCode()) &&
                CollectionUtils.isEmpty(flyCreativeDto.getImageDtos());
    }

    private boolean onlyStoryOrBigCard(CpcUnitDto unit, FlyProUnitPositionsDto flyProUnitPositionsDto, CpcCreativeDto creative) {
        // 非托管，16:9，稿件，仅story，给描述存稿件up主昵称
        if (Integer.valueOf(0).equals(creative.getIsManaged())) {
            if (Objects.equals(FlyBanner4CoverEnum.SIZE_16_9.getCode(), creative.getFlyBannerCoverType())) {
                if (Objects.equals(PromotionPurposeType.ARCHIVE_CONTENT.getCode(), unit.getPromotionPurposeType())) {
                    Set<Integer> slotGroupIds = flyProUnitPositionsDto.getPosition_map_16_9().keySet();
                    if ((slotGroupIds.contains(creativePositionConfig.getFlySlotArchiveStory()) ||
                            slotGroupIds.contains(creativePositionConfig.getFlySlotArchiveBig())) &&
                            !slotGroupIds.contains(creativePositionConfig.getFlySlotArchiveDynamic())) {
                        return Objects.equals(FlyBanner4CoverEnum.SIZE_16_9.getCode(), creative.getFlyBannerCoverType());
                    }
                }
            }
        }
        return false;
    }

    private boolean handleOnlyStoryOrBigCardDescription(CpcUnitDto unit, FlyProUnitPositionsDto flyProUnitPositionsDto,
                                                        CpcCreativeDto creative, LauUnitCreativePo creativePo) {
        if (this.onlyStoryOrBigCard(unit, flyProUnitPositionsDto, creative)) {
            // 满足条件，flyProUnitPositionsDto的authorName才有值
            if (StringUtils.isNotBlank(flyProUnitPositionsDto.getAuthorName())) {
                creativePo.setDescription(flyProUnitPositionsDto.getAuthorName());
                return true;
            }
        }
        return false;
    }

    @SneakyThrows
    private List<Integer> saveFlyMiddleCreativeNew(
            CpcUnitDto unit, List<CpcCreativeDto> creativeList, Map<Integer, CpcCreativeDto> reserveCreatives,
            AccountBaseDto account, Operator operator, String operatorName, FlyProUnitPositionsDto flyProUnitPositionsDto,
            LauUnitFlyMiddleInfoPo lauUnitFlyMiddleInfoPo, boolean isPersonalFly, LauCampaignPo campaignPo, Arc arc) {
        final int accountId = account.getAccountId();
        final int campaignId = unit.getCampaignId();
        final int unitId = unit.getUnitId();
        final int promotionPurposeType = unit.getPromotionPurposeType();
        final int salesType = unit.getSalesType();
        final int ocpcTarget = unit.getOcpcTarget();
        final boolean canSaveShadowCreative = accountLabelService.isAccountIdInLabel(accountId, effectAdShadowCreativeLabelId);
        final Map<Integer, ShadowCreativeBo> shadowCreativeMap = launchShadowCreativeService.shadowCreatives(unitId);

        List<Integer> bannerCoverTypeList = creativeList.stream().map(CpcCreativeDto::getFlyBannerCoverType)
                .distinct()
                .collect(Collectors.toList());
        // 获取要投放的具体位置 Map<bannerCoverType, Map<slotGroupId, 模板>>
        Map<Integer, Map<Integer, TemplateDto>> bannerCoverType2SlotGroupId2TemplateMap = bannerCoverTypeList.stream()
                .collect(Collectors.toMap(Function.identity(), bannerCoverType -> this.getMapByFlyBanner4CoverType(bannerCoverType, flyProUnitPositionsDto)));
        Map<Integer, TemplateDto> distinctSlotGroupId2TemplateMap = bannerCoverType2SlotGroupId2TemplateMap.values().stream()
                .flatMap(slotGroupId2TemplateMap -> slotGroupId2TemplateMap.entrySet().stream())
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (v1, v2) -> v1));
        List<Integer> cardTypeIdList = distinctSlotGroupId2TemplateMap.values().stream().map(TemplateDto::getCardType)
                .distinct()
                .collect(Collectors.toList());
        Map<Integer, List<Integer>> cardType2BusMarkMap = adpCpcBusMarkService.getCardTypeBusMarkMap(accountId, cardTypeIdList);
        // 保留价
        Map<Integer, Integer> slotGroupId2ReservePriceMap = new HashMap<>(distinctSlotGroupId2TemplateMap.size());
        // 商业标
        Map<Integer, Integer> slotGroupId2BusMarkIdMap = new HashMap<>(distinctSlotGroupId2TemplateMap.size());
        // 账号在无标白名单
        boolean accountNoBusMark = accountLabelService.isAccountIdInLabel(unit.getAccountId(), creativePositionConfig.getNoBusMarkLabelId());
        // 个人起飞课堂
        com.bilibili.adp.cpc.dao.querydsl.pos.LauUnitArchiveVideoPo lauUnitArchiveVideoPo = launchUnitArchiveService.get(unitId);
        long seasonId = lauUnitArchiveVideoPo == null ? 0 : lauUnitArchiveVideoPo.getSeasonId();
        distinctSlotGroupId2TemplateMap.forEach((slotGroupId, v) -> {
            // 获取广告位组保留价
            final int reservedPrice = cpcUnitService.getFlyBanner4LowestCost4Creative(GetBidCostParam.builder()
                    .accountId(accountId)
                    .slotGroupId(slotGroupId)
                    .launchType(promotionPurposeType)
                    .salesType(salesType)
                    .build(), ocpcTarget);
            slotGroupId2ReservePriceMap.put(slotGroupId, reservedPrice);
            // 根据广告位组模板卡片获取对应的商业标
            final int busMarkId = middleFlyGenerateBusMarkId(unit, v.getTemplateId(), slotGroupId, isPersonalFly,
                    cardType2BusMarkMap.getOrDefault(v.getCardType(), Collections.emptyList()), accountNoBusMark, seasonId);
            slotGroupId2BusMarkIdMap.put(slotGroupId, busMarkId);
        });

        // mapi可能数据不含mid 需要手动填充
        List<BilibiliVideoBo> bilibiliVideoBoList = creativeList.stream()
                .map(CpcCreativeDto::getBilibiliVideoBo)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        biliVideoService.fillMid(bilibiliVideoBoList);

        // 获取原生
        List<Long> videoIds = creativeList.stream().map(t -> t.getVideoId()).distinct().collect(Collectors.toList());
        List<Integer> creativeIds =
                creativeList.stream().map(t -> t.getCreativeId()).filter(t -> t != null).distinct().collect(Collectors.toList());
        Map<Integer, LauCreativeNativeArchiveRelativityPo> creativeNativeArchiveRelativityPoMap = lauCreativeNativeArchiveRelativityRepo.queryNativeArchiveRelativityPoMap(creativeIds);

        // scv 提供是否小号稿件
//        QueryArchiveExistsReq queryArchiveExistsReq = QueryArchiveExistsReq.newBuilder()
//                .addAllAvids(videoIds)
//                .build();
//        QueryArchiveExistsReply queryArchiveExistsReply = cmArchiveServiceBlockingStub.queryArchiveExists(queryArchiveExistsReq);
//        Map<Long, Boolean> archiveExistsMap = queryArchiveExistsReply.getListList().stream().collect(Collectors.toMap(SingleQueryArchiveExistsReply::getAvid, SingleQueryArchiveExistsReply::getExist));
        Map<Long, Boolean> archiveExistsMap = new HashMap<>();

        final Boolean allowSearchHidden = !isPersonalFly
                && Objects.nonNull(arc);
        List<Integer> savedCreativeIds = Lists.newArrayList();

        Set<Long> cmVideoSet = biliVideoService.getCmVideoSet(creativeList.stream()
                .filter(x -> !Objects.isNull(x.getBilibiliVideoBo()))
                .map(x -> Optional.ofNullable(x.getBilibiliVideoBo().getAvId()).orElse(0L))
                .filter(x -> x > 0L).collect(Collectors.toList()));

        for (CpcCreativeDto creative : creativeList) {
            boolean isNativeCreative = false;
            CpcCreativeDto prevCreative = reserveCreatives.get(creative.getCreativeId());
            Integer originalAuditStatus = Optional.ofNullable(prevCreative)
                    .map(CpcCreativeDto::getAuditStatus)
                    .orElse(AuditStatus.UNKNOWN.getCode());
            final Integer bilibiliUserId = Optional.ofNullable(creative.getBilibiliUserId()).orElseGet(() -> UserType.getByCode(account.getUserType()) == UserType.PERSONAL_FLY ? CreativeSourceType.PERSONAL_FLY.getCode() : CreativeSourceType.UNKNOWN.getCode());
            final boolean onlyStoryOrBigCard = this.onlyStoryOrBigCard(unit, flyProUnitPositionsDto, creative) && StringUtils.isNotBlank(flyProUnitPositionsDto.getAuthorName());
            String description = creative.getDescription();
            if (onlyStoryOrBigCard) {
                description = flyProUnitPositionsDto.getAuthorName();
            }
            final boolean isBilibiliVideo = (Objects.equals(creative.getCreativeStyle(), CreativeStyle.BVID.getCode()) || Objects.equals(creative.getCreativeStyle(), CreativeStyle.AVID.getCode()));
            Long videoId = creative.getVideoId();
            if (isBilibiliVideo) {
                biliVideoService.save(BilibiliVideoBo.builder().avId(videoId).build(), cmVideoSet.contains(videoId));
            }
            final List<CreativeImageBo> imageBos = Optional.ofNullable(creative.getImageDtos())
                    .orElseGet(Collections::emptyList)
                    .stream()
                    .map(CreativeImageConverter.MAPPER::imageDto2ImageBo)
                    .collect(Collectors.toList());
            final List<CreativeComponentBo> components = Optional.ofNullable(creative.getComponents()).orElseGet(Collections::emptyList);
            final String specificScenes = fetchSpecificScenes(creative.getFlyBannerUnitSpecificScenesType(), creative.getFlyBannerCoverType());
            final String storyUrl = generateStoryUrl(unit, creative);
            //注意 yellowCarIcon 目前只有一个值 1-购物
            // 所以如果是黄车则使用1，引擎通过1来识别使用【购物图标】
            // 如果后续逻辑变更修改此处逻辑
            final CreativeFlyExtInfoBo flyExtInfo = CreativeFlyExtInfoBo.builder()
                    .scenesType(creative.getFlyBannerUnitScenesType())
                    .specificScenes(specificScenes)
                    .creativeStyle(creative.getCoverType())
                    .banner4CoverType(creative.getFlyBannerCoverType())
                    .storyUrl(storyUrl)
                    .preferDirectCallUp(Functions.boolean2Integer(creative.getPreferDirectCallUp()))
                    .isYellowCar(creative.getIsYellowCar())
                    .yellowCarTitle(creative.getYellowCarTitle())
                    .yellowCarIcon(creative.getIsYellowCar())
                    .build();

            final boolean needAddBizExtra = !CollectionUtils.isEmpty(components) && creative.getComponents().stream().anyMatch(x -> LaunchComponentService.isUnderframe(x.getType()));
            creative.setAdpVersion(unit.getAdpVersion());
            creative.setIsMiddleAd(1);
            creative.setBilibiliUserId(bilibiliUserId);
            creative.setIsHistory(0);
            creative.setDescription(description);
            creative.setVideoId(videoId);
            final boolean isNewCreative = Objects.isNull(prevCreative);
            //没有旧创意，获取不到影子创意，自然不可能存在影子创意
            final ShadowCreativeBo prevShadowCreative = shadowCreativeMap.get(Optional.ofNullable(prevCreative).map(CpcCreativeDto::getCreativeId).orElse(0));
            final boolean existedShadowCreative = Objects.nonNull(prevShadowCreative);
            //判断是否需要重新审核
            //没有影子创意的权限时 || 或者不存在影子创意时, 判断是否需要推审 使用新创意和旧创意对比 old creative 来源 lau_unit_creative
            //存在影子创意时 判断是否需要推审，使用新创意和原有影子创意对比 shadow creative 来源 lau_shadow_creative
            final boolean needReAudit;
            if (!canSaveShadowCreative || !existedShadowCreative) {
                needReAudit = launchCreativeService.creativeNeedReAudit(creative, prevCreative);
            } else {
                needReAudit = launchCreativeService.creativeNeedReAudit(creative, prevShadowCreative);
            }
            //是否需要保存影子创意 = (有保存影子创意的权限 && 更新创意 && 原创意审核通过 && 需要重新审核) || (存在影子创意 & 影子创意待审核)
            final boolean needSaveShadowCreative = (canSaveShadowCreative && !isNewCreative && originalAuditStatus == com.bilibili.adp.cpc.core.constants.AuditStatus.PASSED && needReAudit)
                    || (Objects.nonNull(prevShadowCreative) && Objects.equals(prevShadowCreative.getAuditStatus(), com.bilibili.adp.cpc.core.constants.AuditStatus.AUDITING));
            com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauUnitCreativePo creativePo = LaunchCreativeService.generatePoAndModifyStatus(creative, prevCreative, needSaveShadowCreative, needReAudit, false);
            ShadowCreativeBo.ShadowCreativeBoBuilder shadowCreativeBuilder = ShadowCreativeBo.builder();
            final int creativeId;
            if (isNewCreative) {
                creativeId = adCoreBqf.insert(lauUnitCreative).insertGetKey(creativePo);
            } else {
                creativeId = prevCreative.getCreativeId();
                adCoreBqf.update(lauUnitCreative).updateBean(creativePo);
            }

            // 要求自定义创意
            isNativeCreative = nativeAdJudger.judgeIsNativeCreative(unit, campaignPo, arc, accountId, archiveExistsMap, creative, creativePo);
            nativeService.saveNativeDataIfNecessary(creativeNativeArchiveRelativityPoMap, creative, isNativeCreative, creativeId, needReAudit, operator, arc, unit);

            String trackadf = creativeTrackadfProc.spliceCreateTrackadfIfNecessary(creative.getCreativeId(), account, creative.getPromotionPurposeContent());
            String jumpUrl = launchJumpUrlService.generateFlyNativeJumpUrl(creative.getPromotionPurposeContent(), creativeId, needAddBizExtra, isPersonalFly, seasonId);
            String degradedJumpUrl = Optional.ofNullable(creative.getPromotionPurposeContentSecondary()).orElse(StringUtils.EMPTY);
            // lau_creative_fly_ext_info 的信息分两步保存，因为部分信息是判断是否需要审
            launchCreativeFlyExtInfoService.saveCreativeFlyExtInfoWithoutYellowCar(accountId, unitId, creativeId, flyExtInfo);

            //不需要保存影子创意时 直接保存创意相关信息
            //需要保存影子创意时 则保存至lau_shadow_creative表 json形式
            if (!needSaveShadowCreative) {
                launchJumpUrlService.saveCreativePromotionPurposeContentAndTrackadf(creativeId, jumpUrl, degradedJumpUrl, trackadf);
                final List<com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauCreativeImagePo> images = imageBos.stream().filter(Objects::nonNull).map(image -> CreativeImageConverter.MAPPER.bo2Po(unitId, creativeId, image)).collect(Collectors.toList());
                launchCreativeImageService.saveByCreativeId(creativeId, images);
                launchCreativeFlyExtInfoService.saveCreativeFlyYellowCarInfo(creativeId, flyExtInfo);
                launchComponentService.saveCreativeComponent(accountId, campaignId, unitId, creativeId, components);
            } else {
                shadowCreativeBuilder.accountId(accountId);
                shadowCreativeBuilder.campaignId(campaignId);
                shadowCreativeBuilder.unitId(unitId);
                shadowCreativeBuilder.creativeId(creativeId);
                shadowCreativeBuilder.promotionPurposeType(promotionPurposeType);
                shadowCreativeBuilder.advertisingMode(creative.getAdvertisingMode());
                shadowCreativeBuilder.jumpType(JumpTypeEnum.LINK.getCode());
                shadowCreativeBuilder.jumpUrl(jumpUrl);
                shadowCreativeBuilder.jumpUrlSecondary(degradedJumpUrl);
                shadowCreativeBuilder.title(creative.getTitle());
                shadowCreativeBuilder.description(creative.getDescription());
                shadowCreativeBuilder.extDescription(creative.getExtDescription());
                shadowCreativeBuilder.mgkPageId(0L);
                shadowCreativeBuilder.videoId(videoId);
                shadowCreativeBuilder.cmMark(creative.getCmMark());
                shadowCreativeBuilder.busMark(creative.getBusMarkId());
                shadowCreativeBuilder.button(null);
                shadowCreativeBuilder.images(imageBos);
                shadowCreativeBuilder.components(components);
                shadowCreativeBuilder.tab(null);
                shadowCreativeBuilder.flyExtInfo(flyExtInfo);
                shadowCreativeBuilder.trackadf(trackadf);
                shadowCreativeBuilder.auditStatus(com.bilibili.adp.cpc.core.constants.AuditStatus.AUDITING);
                shadowCreativeBuilder.creativeStatus(com.bilibili.adp.cpc.core.constants.CreativeStatus.AUDITING);
                ShadowCreativeBo shadowCreativeBo = shadowCreativeBuilder.build();
                String shadowCreative = objectMapper.writeValueAsString(shadowCreativeBo);
                //保存影子创意
                final List<LauShadowCreativePo> shadowCreatives = Stream.of(shadowCreativeBo).filter(Objects::nonNull).map(bo -> ShadowCreativeConverter.MAPPER.bo2Po(bo, shadowCreative)).collect(Collectors.toList());
                launchShadowCreativeService.saveByCreativeId(creativeId, shadowCreatives);
            }

            if (Objects.equals(AuditStatus.INIT.getCode(), creativePo.getAuditStatus())) {
                creativeServiceDelegate.addCreativeAuditStat(creativePo.getCreativeId(), CreativeAuditEvent.AUDIT, operatorName);
            }

            // 创意落地页分享
            cpcSaveCreativeService.saveCreativeShare(creative, creativeId);

            // 保存创意自动更新表
            cpcSaveCreativeService.saveCreativeAuto(creative, creativeId);

            // 保存创意 extra 信息
            Integer ppcType = lauCreativeExtraService.generatePpcType(jumpUrl, unit.getPromotionPurposeType(), unit.getBusinessDomain(), creative.getVideoId(), creative.getAdvertisingMode());
            LauCreativeExtraPo creativeExtraPo = LauCreativeExtraPo.builder()
                    .creativeId(creativeId).accountId(creative.getAccountId())
                    .campaignId(creative.getCampaignId()).unitId(creative.getUnitId())
                    .qualificationPackageId(creative.getQualificationPackageId())
                    .ppcType(ppcType)
                    .build();
            lauCreativeExtraProc.saveCreativeExtra(creativeExtraPo, creativeId);
            cpcSaveCreativeService.saveCreativeMonitoring(creativeId, creative);
            cpcSaveCreativeService.saveCreativeBusinessCategory(creativeId, creative.getBuFirstCategoryId(), creative.getBuSecondCategoryId(), creative.getBuThirdCategoryId());

            savedCreativeIds.add(creativeId);

            // 处理加急审核情况
            cpcSaveCreativeService.processUrgentAudit(unit.getAccountId(), creative, prevCreative, creativePo.getAuditStatus(), creativeId);

            // 版位通投1期: https://www.tapd.cn/********/prong/stories/view/11********004352994
            boolean allowMsgFlow2Story = false;
            boolean allowStory2MsgFlow = false;
            if (!isPersonalFly
                    && Objects.nonNull(arc)
                    && Objects.equals(creative.getFlyBannerCoverType(), 3)
                    && !CollectionUtils.isEmpty(creative.getFlyBannerUnitSpecificScenesType())) {
                final Set<Integer> set = new HashSet<>(creative.getFlyBannerUnitSpecificScenesType());
                if (set.contains(SpecificScenesEnum.MSG_FLOW.getCode()) && !set.contains(SpecificScenesEnum.STORY.getCode())) {
                    allowMsgFlow2Story = true;
                } else if (set.contains(SpecificScenesEnum.STORY.getCode()) && !set.contains(SpecificScenesEnum.MSG_FLOW.getCode())) {
                    allowStory2MsgFlow = true;
                }
            }

            // 保存广告位组模板 mapping
            this.middleFlySaveSlotGroupTemplateMapping(unit, creative, creativeId, bannerCoverType2SlotGroupId2TemplateMap,
                    slotGroupId2ReservePriceMap, slotGroupId2BusMarkIdMap, allowSearchHidden, allowMsgFlow2Story, allowStory2MsgFlow);

            //新建创意投动态位置注册一个动态
            this.registerDynamic(isNewCreative, creative, creativeId, unit, operator);

            //保存邀约组件点击监测链接
            flyCreativeService.saveFlyCreativeInvitations(unit.getUnitId(), unit.getPromotionPurposeType(),
                    unit.getVideoId(), creativeId, creative.getFlyInvitationDtos());


            //专业起飞复制，自动审核(通过&驳回)
            if (isNewCreative && unit.getIsNewFly() == 1 && creative.isFlyCopy()) {
                creativeCommonService.autoAuditCopyCreative(unit.getUnitId(), creativePo.getCreativeId(), creative.getSourceCreativeIdAuditStatus());
                //暂停创意
                if (ObjectUtils.nullSafeEquals(creative.getSourceCreativeIdCreativeStatus(), CreativeStatus.PAUSED.getCode())) {
                    LauUnitCreativePoExample example = new LauUnitCreativePoExample();
                    example.or()
                            .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                            .andAccountIdEqualTo(account.getAccountId())
                            .andCreativeIdEqualTo(creativePo.getCreativeId());
                    LauUnitCreativePo po = new LauUnitCreativePo();
                    po.setStatus(LaunchStatus.STOP.getCode());
                    po.setCreativeStatus(CreativeStatus.PAUSED.getCode());
                    lauUnitCreativeDao.updateByExampleSelective(po, example);
                }
                //再查出来,写日志
                LauUnitCreativePo po = lauUnitCreativeDao.selectByPrimaryKey(creativePo.getCreativeId());
                creative.setAuditStatus(po.getAuditStatus());
                creative.setCreativeStatus(po.getCreativeStatus());
                creative.setStatus(po.getStatus());
            } else {
                //新建直播间创意，自动审核
                // 目前直播间创意包括 LIVE_ROOM 和 GOODS_LIVE
                final boolean autoAudit = Objects.equals(unit.getPromotionPurposeType(), PromotionPurposeType.LIVE_ROOM.getCode())
                        || Objects.equals(unit.getPromotionPurposeType(), PromotionPurposeType.GOODS_LIVE.getCode());
                if (autoAudit) {
                    creativeCommonService.autoAuditLiveRoomCreative(unit.getUnitId(), creativePo.getCreativeId());
                }
            }

            //选择动态和活动保存lau_creative_fly_dynamic_info
            if (lauUnitFlyMiddleInfoPo != null) {
                // 手动选择动态
                if (PromotionPurposeType.DYNAMIC.getCode() == lauUnitFlyMiddleInfoPo.getPromotionPurposeType() &&
                        Objects.equals(DynamicTypeEnum.CHOOSE_DYNAMIC.getCode(), lauUnitFlyMiddleInfoPo.getDynamicType())) {
                    cpcSaveCreativeService.saveDynamicInfo(isNewCreative, account.getAccountId(), unit.getUnitId(), creativeId, creative.getDynamicId(), creative.getDynamicUpMid(), creative.getLikeCount(), creative.getNickName());
                }
                if (PromotionPurposeType.ACTIVITY.getCode() == lauUnitFlyMiddleInfoPo.getPromotionPurposeType()) {
                    cpcSaveCreativeService.saveDynamicInfo(isNewCreative, account.getAccountId(), unit.getUnitId(), creativeId,
                            creative.getDynamicId(), creative.getLikeCount(), creative.getNickName());
                }
            }

            // 稿件需要暗投动态
//            if (!Objects.equals(UserType.PERSONAL_FLY.getCode(), account.getUserType())) {
                AbstractFlyDynamicDarkLaunchProc dynamicDarkLaunchProc = flyDynamicDarkLaunchProcFactory.getDynamicDarkLaunchProc(unit.getPromotionPurposeType());
                creative.setCreativeId(creativeId);
                Optional.ofNullable(dynamicDarkLaunchProc).ifPresent(t -> t.saveDaynamicDarkLaunchDataIfNecessary(isNewCreative, creative, unit,
                        creative.getDynamicId(), flyProUnitPositionsDto));
//            }

            //邀约链接相关
            if (ObjectUtils.nullSafeEquals(FlyIsSetInviteLinkEnum.FALSE.getCode(), creative.getIsSetInviteLink())) {
                cpcSaveCreativeService.saveInviteLink(isNewCreative, unit.getVideoId(), unit.getRealMid().longValue(), account.getAccountId(),
                        unit.getCampaignId(), unit.getUnitId(), creativeId, creative.getFlyProUnderBox());
            }
        }

        List<Long> dynamicIds = creativeList.stream().map(t -> t.getDynamicId()).filter(Objects::nonNull).filter(Utils::isPositive).distinct().collect(Collectors.toList());
        lauDynamicService.processDynamics(dynamicIds);
        cpcSaveCreativeService.deleteUnUsedCreativeTemplate(unit.getUnitId(), savedCreativeIds);

        LOGGER.info("Return creativeIds {}", savedCreativeIds);
        return savedCreativeIds;
    }


    /**
     * 保存广告位组模板 mapping
     *
     * @param unit
     * @param creative
     * @param newCreativeId
     */
    public void middleFlySaveSlotGroupTemplateMapping(
            CpcUnitDto unit,
            CpcCreativeDto creative,
            Integer newCreativeId,
            Map<Integer, Map<Integer, TemplateDto>> bannerCoverType2SlotGroupId2TemplateMap,
            Map<Integer, Integer> slotGroupId2ReservePriceMap,
            Map<Integer, Integer> slotGroupId2BusMarkIdMap,
            Boolean allowSearchHidden,
            Boolean allowMsgFlow2Story,
            Boolean allowStory2MsgFlow) {
        // 保存广告位组模板 mapping
        this.middleFlySaveSlotGroupTemplateMapping4Banner4(unit, creative, newCreativeId, bannerCoverType2SlotGroupId2TemplateMap,
                slotGroupId2ReservePriceMap, slotGroupId2BusMarkIdMap, allowSearchHidden, allowMsgFlow2Story, allowStory2MsgFlow);
    }

    /**
     * 保存广告位组模板 mapping
     *
     * @param unit
     * @param creative
     * @param newCreativeId
     */
    private void middleFlySaveSlotGroupTemplateMapping4Banner4(
            CpcUnitDto unit,
            CpcCreativeDto creative,
            Integer newCreativeId,
            Map<Integer, Map<Integer, TemplateDto>> bannerCoverType2SlotGroupId2TemplateMap,
            Map<Integer, Integer> slotGroupId2ReservePriceMap,
            Map<Integer, Integer> slotGroupId2BusMarkIdMap,
            Boolean allowSearchHidden,
            Boolean allowMsgFlow2Story,
            Boolean allowStory2MsgFlow) {
        // 获取要投放的具体位置 Map<slotGroupId, 模板>
        Map<Integer, TemplateDto> slotGroupId2TemplateDto = bannerCoverType2SlotGroupId2TemplateMap.getOrDefault(creative.getFlyBannerCoverType(), Collections.emptyMap());

        // 暗投逻辑
        final int ppt = unit.getPromotionPurposeType();
        final int bidPrice = unit.getCostPrice();
        final boolean isNoBid = unit.getIsNoBid() != null && Utils.isPositive(unit.getIsNoBid());
        List<LauCreativeTemplateDo> dos = new ArrayList<>(slotGroupId2TemplateDto.size());

        slotGroupId2TemplateDto.forEach((slotGroupId, v) -> {
            // 获取广告位组保留价
            final int reservedPrice = slotGroupId2ReservePriceMap.getOrDefault(slotGroupId, 0);
            // 根据广告位组模板卡片获取对应的商业标
            final int busMarkId = slotGroupId2BusMarkIdMap.getOrDefault(slotGroupId, 0);

            LauCreativeTemplateDo lauCreativeTemplateDo = new LauCreativeTemplateDo();
            lauCreativeTemplateDo.setCreativeId(newCreativeId.longValue());
            lauCreativeTemplateDo.setAccountId(creative.getAccountId());
            lauCreativeTemplateDo.setUnitId(creative.getUnitId());
            lauCreativeTemplateDo.setSlotGroupId(slotGroupId);
            lauCreativeTemplateDo.setTemplateId(v.getTemplateId());
            lauCreativeTemplateDo.setIsDeleted(IsDeleted.VALID.getCode());
            lauCreativeTemplateDo.setReservedPrice(reservedPrice);

            int bizStatus = BIZ_STATUS_OK;
            if (!isNoBid) {
                bizStatus = bidPrice < reservedPrice ? BIZ_STATUS_UNDER_RESERVED_PRICE : BIZ_STATUS_OK;
            }
            lauCreativeTemplateDo.setBizStatus(bizStatus);

            lauCreativeTemplateDo.setBusMarkId(busMarkId);
            // 创意样式
            lauCreativeTemplateDo.setCreativeStyle(this.banner4SlotGroupId2CreativeStyle(slotGroupId, ppt, creative.getPromotionPurposeContent()));
            dos.add(lauCreativeTemplateDo);
        });

        if (allowSearchHidden) {
            hiddenTemplateService.addSearchHiddenTemplate(dos, LauCreativeTemplateDo::getTemplateId, HiddenTemplateConverter.MAPPER::copy);
        }
        Assert.isTrue(!CollectionUtils.isEmpty(dos), "创意模板不能为空");
        final LauCreativeTemplateDo sample = dos.get(0);
        if (allowMsgFlow2Story) {
            final LauCreativeTemplateDo po = HiddenTemplateConverter.MAPPER.copy(sample, 680, 565, 123, 4);
            dos.add(po);
        }
        if (allowStory2MsgFlow) {
            final LauCreativeTemplateDo po = HiddenTemplateConverter.MAPPER.copy(sample, 682, 566, 123, 3);
            dos.add(po);
        }

        //如果是编辑创意，获取应该删除的id
        if (creative.getCreativeId() != null && creative.getCreativeId() > 0) {
            List<Integer> doSlotGroupIds = dos.stream().map(LauCreativeTemplateDo::getSlotGroupId).collect(Collectors.toList());
            List<LauCreativeTemplateDo> lauCreativeTemplateDos = adCoreBqf.select(
                            Projections.bean(LauCreativeTemplateDo.class,
                                    lauCreativeTemplate.id,
                                    lauCreativeTemplate.slotGroupId))
                    .from(lauCreativeTemplate)
                    .where(lauCreativeTemplate.creativeId.eq(creative.getCreativeId().longValue()))
                    .where(lauCreativeTemplate.isDeleted.eq(IsDeleted.VALID.getCode()))
                    .fetch();
            List<Long> needDeleteIds = new ArrayList<>();
            lauCreativeTemplateDos.stream().forEach(o -> {
                if (!doSlotGroupIds.contains(o.getSlotGroupId())) {
                    needDeleteIds.add(o.getId());
                }
            });
            //删除
            if (!CollectionUtils.isEmpty(needDeleteIds)) {
                adCoreBqf.update(lauCreativeTemplate)
                        .set(lauCreativeTemplate.isDeleted, IsDeleted.DELETED.getCode())
                        .where(lauCreativeTemplate.creativeId.in(creative.getCreativeId().longValue()))
                        .where(lauCreativeTemplate.id.in(needDeleteIds))
                        .execute();
            }
        }
        //插入&更新 creative_id,slot_group_id 为key的一条数据
        adCoreBqf.insertMysql(lauCreativeTemplate).insertDuplicateUpdates(dos);
    }

    private String fetchSpecificScenes(List<Integer> specificScenes, Integer coverType) {
        if (FlyBanner4CoverEnum.SIZE_16_10.getCode().equals(coverType)) {
            return specificScenes
                    .stream()
                    .filter(SpecificScenesEnum.getClickSceneInt()::contains)
                    .map(String::valueOf)
                    .collect(Collectors.joining(","));
        }
        if (FlyBanner4CoverEnum.SIZE_16_9.getCode().equals(coverType)) {
            return specificScenes
                    .stream()
                    .filter(SpecificScenesEnum.getAutoSceneInt()::contains)
                    .map(String::valueOf)
                    .collect(Collectors.joining(","));
        }
        if (FlyBanner4CoverEnum.SIZE_4_3.getCode().equals(coverType)) {
            return specificScenes
                    .stream()
                    .filter(SpecificScenesEnum.get4_3SceneInt()::contains)
                    .map(String::valueOf)
                    .collect(Collectors.joining(","));
        }
        if (FlyBanner4CoverEnum.SCENE_MERGE.getCode().equals(coverType)) {
            return specificScenes
                    .stream()
                    .map(String::valueOf)
                    .collect(Collectors.joining(","));
        }
        return StringUtils.EMPTY;
    }


    /**
     * 中台起飞商业标处理
     * 根据广告位组模板卡片获取对应的商业标
     *
     * @param unit
     * @return
     */
    public Integer middleFlyGenerateBusMarkId(
            CpcUnitDto unit, Integer templateId, Integer slotGroupId, boolean isPersonalFly, List<Integer> markIds,
            boolean accountNoBusMark, Long seasonId) {
        final boolean isStory = storyTemplateIds.contains(templateId)
                || creativeTemplateConfig.getDarkFlyTemplateStory().equals(templateId);
        if (isPersonalFly) {
            if (accountNoBusMark) {
                if (Objects.equals(personalFlyStoryTemplateId, templateId)) return flyBusMarkConfig.getStoryNoBusMarkId();
                return flyBusMarkConfig.getNonStoryNoBusMarkId();
            }
            if (Utils.isPositive(seasonId)) {
                return seasonBusMarkId;
            }
            return rocketBusMarkId;
        }
        // 无标配置最优先
        if (accountNoBusMark) {
            if (isStory) return flyBusMarkConfig.getStoryNoBusMarkId();

            return flyBusMarkConfig.getNonStoryNoBusMarkId();
        }
        if (Objects.equals(slotGroupId, creativePositionConfig.getStoryHiddenExpSlotGroupId())) {
            return creativePositionConfig.getStoryHiddenExpFlyBusMarkId();
        }
        if (Objects.equals(slotGroupId, 407)
                || Objects.equals(slotGroupId, creativePositionConfig.getDarkFlySlotUnderBox())) {
//                || Objects.equals(slotGroupId, creativePositionConfig.getUnderFrameHiddenSlotGroupId())) {
            return 79;
        }
        if (Objects.equals(slotGroupId, creativePositionConfig.getDynamicFeedsHiddenSlotGroupId())) {
            return 54;
        }
        final boolean isPcAppIndex = pcAppIndexTemplateIds.contains(templateId);
        final boolean isPcAppPlay = pcAppPlayTemplateIds.contains(templateId);
        if (isPcAppIndex) {
            return pcAppIndexBusMarkId;
        }
        if (isPcAppPlay) {
            return pcAppPlayBusMarkId;
        }

        //商业直播间固定 创作推广
        if (PromotionPurposeType.LIVE_ROOM == PromotionPurposeType.getByCode(unit.getPromotionPurposeType())) {
            return flyCreationPromotionMarkId;
        }
        //活动拿活动标
        if (PromotionPurposeType.ACTIVITY == PromotionPurposeType.getByCode(unit.getPromotionPurposeType())) {
            return activityBusMarkId;
        }
        //动态拿"起飞-内容推广标-新"
        if (PromotionPurposeType.DYNAMIC.getCode() == unit.getPromotionPurposeType()) {
            return rocketBusMarkId;
        }
        // 目前ogv 创作推广标
        if (PromotionPurposeType.OGV.getCode() == unit.getPromotionPurposeType()) {
            return flyBusMarkId;
        }
        // 直播带货 创作推广标(此处存54，引擎会有替换逻辑)。********，带货直播的广告标全部改为123
        if (PromotionPurposeType.GOODS_LIVE.getCode() == unit.getPromotionPurposeType()) {
            return rocketBusMarkId;
        }
        Integer busMarkId = 0;
        //投稿内容
        if (PromotionPurposeType.ARCHIVE_CONTENT.getCode() == unit.getPromotionPurposeType()) {
            if (!CollectionUtils.isEmpty(markIds)) {
                //特殊账号不展示标
                if (noBusMarkAccountIds.contains(unit.getAccountId()) && markIds.contains(noBusMarkId)) {
                    busMarkId = noBusMarkId;
                } else {
                    final boolean isPcIndex = pcIndexTemplateIds.contains(templateId)
                            || creativeTemplateConfig.getDarkFlyTemplatePcIndex().equals(templateId);
                    final boolean isPcPlay = pcPlayTemplateIds.contains(templateId)
                            || creativeTemplateConfig.getDarkFlyTemplatePcPlay().equals(templateId);
                    // 是否 tv-inline 模板
                    final boolean isTv = tvTemplateIds.contains(templateId);
                    final boolean isSearch = searchTemplateIds.contains(templateId);
                    final boolean isPcSearch = pcSearchTemplateIds.contains(templateId);
                    final boolean isChooseSearch = chooseSearchTemplateIds.contains(templateId);
                    final boolean isIPadSearch = iPadSearchTemplateIds.contains(templateId);
                    if (isPcIndex) {
                        return pcIndexBusMarkId;
                    }
                    if (isPcPlay) {
                        return pcPlayBusMarkId;
                    }
                    // tv 商业标
                    if (isTv) {
                        return tvBusMarkId;
                    }
                    if (isSearch) {
                        return rocketBusMarkId;
                    }
                    if (isPcSearch) {
                        return rocketBusMarkId;
                    }
                    if (isChooseSearch) {
                        return rocketBusMarkId;
                    }
                    if (isStory) {
                        return rocketBusMarkId;
                    }
                    if (isIPadSearch) {
                        return rocketBusMarkId;
                    }
                    //优先拿"游戏标"
                    if (markIds.contains(gameBusMarkId)) {
                        busMarkId = gameBusMarkId;
                    } else {
                        // 小卡、播放页、大卡、iPAD拿火箭标
                        final boolean isSmallCard = smallCardTemplateIds.contains(templateId)
                                || creativeTemplateConfig.getDarkFlyTemplateSmall().equals(templateId);
                        final boolean isPlay = playTemplateIds.contains(templateId)
                                || creativeTemplateConfig.getDarkFlyTemplatePlay().equals(templateId);
                        final boolean isBigCard = bigCardTemplateIds.contains(templateId)
                                || creativeTemplateConfig.getDarkFlyTemplateBig().equals(templateId);
                        final boolean isIPad = iPADCardTemplateIds.contains(templateId)
                                || creativeTemplateConfig.getDarkFlyTemplateIPad().equals(templateId);
                        if (isSmallCard || isPlay || isBigCard || isIPad) {
                            return rocketBusMarkId;
                        }
                        //其次拿"起飞-内容推广标-新"
                        if (markIds.contains(flyBusMarkId)) {
                            busMarkId = flyBusMarkId;
                        }
                    }
                }
            }
        }
        return busMarkId;
    }
}
