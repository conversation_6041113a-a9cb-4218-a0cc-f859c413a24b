package com.bilibili.adp.cpc.automatic_rule.enums.action;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum ActionType {
    DEFAULT(0, ""),
    INCREASE(1, "提高"),
    DECREASE(2, "降低"),
    ADJUST_TO(3, "调整至"),
    ;

    private final int code;
    private final String desc;


    public static ActionType getByCode(int code) {
        return Arrays.stream(values())
                .filter(actionType -> actionType.getCode() == code)
                .findFirst()
                .orElseThrow(IllegalArgumentException::new);
    }
}
