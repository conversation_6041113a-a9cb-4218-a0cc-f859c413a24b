package com.bilibili.adp.cpc.splash_screen.creative.service;

import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauCreativeTemplatePo;
import com.bilibili.adp.cpc.splash_screen.creative.bos.SplashScreenTemplate;
import com.bilibili.adp.cpc.splash_screen.creative.converter.TemplateConverter;
import com.bilibili.adp.cpc.splash_screen.creative.enums.InteractType;
import com.bilibili.adp.cpc.utils.CommonFuncs;
import com.bilibili.adp.cpc.utils.RecDiffResult;
import com.bilibili.bjcom.querydsl.BaseQueryFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static com.bilibili.adp.cpc.dao.ad_core.querydsl.QLauCreativeTemplate.lauCreativeTemplate;
import static com.bilibili.adp.cpc.dao.config.PlatformMySqlConfig.AD_CORE_BQF;

@Slf4j
@Service
public class SplashScreenCreativeTemplateService {
    /**
     * 扭一扭模板id
     */
    private static final int TWIST_TEMPLATE_ID = 592;
    /**
     * 全屏滑动模板id
     */
    private static final int SLIDE_TEMPLATE_Id = 593;
    /**
     * 点击按钮模板id
     */
    private static final int CLICK_TEMPLATE_Id = 593;
    public static List<Integer> TEMPLATE_IDS = Arrays.asList(TWIST_TEMPLATE_ID, SLIDE_TEMPLATE_Id);

    @Resource(name = AD_CORE_BQF)
    private BaseQueryFactory adCoreBqf;

    public void save(Integer interactTypeCode, Integer accountId, Integer campaignId, Integer unitId, Integer creativeId) {
        final InteractType interactType = InteractType.getByCode(interactTypeCode);
        final int templateId;
        switch (interactType) {
            case TWIST:
                templateId = TWIST_TEMPLATE_ID;
                break;
            case SLIDE:
                templateId = SLIDE_TEMPLATE_Id;
                break;
            case CLICK:
                templateId = CLICK_TEMPLATE_Id;
                break;
            default:
                throw new IllegalArgumentException("错误的交互类型");
        }
        final SplashScreenTemplate splashScreenTemplate = TemplateConverter.MAPPER.creative2Template(accountId, campaignId, unitId, creativeId, templateId);
        final LauCreativeTemplatePo template = TemplateConverter.MAPPER.bo2Po(splashScreenTemplate);
        final List<LauCreativeTemplatePo> templates = Collections.singletonList(template);
        final List<LauCreativeTemplatePo> existingPos = list(creativeId);
        final RecDiffResult<LauCreativeTemplatePo, Long> result = CommonFuncs.recDiff(existingPos, templates, this::uk, LauCreativeTemplatePo::getId, CommonFuncs.getDefaultBiFunction(LauCreativeTemplatePo::getId, LauCreativeTemplatePo::setId));
        CommonFuncs.handleRecDiff(result, adCoreBqf, lauCreativeTemplate, lauCreativeTemplate.id::in);
    }

    public List<LauCreativeTemplatePo> list(long creativeId) {
        return adCoreBqf.selectFrom(lauCreativeTemplate)
                .where(lauCreativeTemplate.creativeId.eq(creativeId))
                .where(lauCreativeTemplate.isDeleted.eq(0))
                .fetch();
    }

    public String uk(LauCreativeTemplatePo creativeTemplate) {
        return creativeTemplate.getCreativeId() + "-" + creativeTemplate.getTemplateId();
    }
}
