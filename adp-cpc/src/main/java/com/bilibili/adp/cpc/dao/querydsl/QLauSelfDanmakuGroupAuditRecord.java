package com.bilibili.adp.cpc.dao.querydsl;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.bilibili.adp.cpc.dao.querydsl.pos.LauSelfDanmakuGroupAuditRecordPo;


import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;

import com.querydsl.sql.ColumnMetadata;
import java.sql.Types;




/**
 * QLauSelfDanmakuGroupAuditRecord is a Querydsl query type for LauSelfDanmakuGroupAuditRecordPo
 */
@Generated("com.querydsl.sql.codegen.MetaDataSerializer")
public class QLauSelfDanmakuGroupAuditRecord extends com.querydsl.sql.RelationalPathBase<LauSelfDanmakuGroupAuditRecordPo> {

    private static final long serialVersionUID = -********;

    public static final QLauSelfDanmakuGroupAuditRecord lauSelfDanmakuGroupAuditRecord = new QLauSelfDanmakuGroupAuditRecord("lau_self_danmaku_group_audit_record");

    public final NumberPath<Integer> accountId = createNumber("accountId", Integer.class);

    public final StringPath auditorName = createString("auditorName");

    public final DateTimePath<java.sql.Timestamp> auditTime = createDateTime("auditTime", java.sql.Timestamp.class);

    public final DateTimePath<java.sql.Timestamp> ctime = createDateTime("ctime", java.sql.Timestamp.class);

    public final NumberPath<Long> danmakuNum = createNumber("danmakuNum", Long.class);

    public final NumberPath<Long> groupId = createNumber("groupId", Long.class);

    public final StringPath groupName = createString("groupName");

    public final NumberPath<Integer> groupType = createNumber("groupType", Integer.class);

    public final NumberPath<Long> id = createNumber("id", Long.class);

    public final NumberPath<Integer> isDeleted = createNumber("isDeleted", Integer.class);

    public final DateTimePath<java.sql.Timestamp> mtime = createDateTime("mtime", java.sql.Timestamp.class);

    public final NumberPath<Integer> operateType = createNumber("operateType", Integer.class);

    public final StringPath reason = createString("reason");

    public final DateTimePath<java.sql.Timestamp> sendAuditTime = createDateTime("sendAuditTime", java.sql.Timestamp.class);

    public final NumberPath<Long> workOrderId = createNumber("workOrderId", Long.class);

    public final com.querydsl.sql.PrimaryKey<LauSelfDanmakuGroupAuditRecordPo> primary = createPrimaryKey(id);

    public QLauSelfDanmakuGroupAuditRecord(String variable) {
        super(LauSelfDanmakuGroupAuditRecordPo.class, forVariable(variable), "null", "lau_self_danmaku_group_audit_record");
        addMetadata();
    }

    public QLauSelfDanmakuGroupAuditRecord(String variable, String schema, String table) {
        super(LauSelfDanmakuGroupAuditRecordPo.class, forVariable(variable), schema, table);
        addMetadata();
    }

    public QLauSelfDanmakuGroupAuditRecord(String variable, String schema) {
        super(LauSelfDanmakuGroupAuditRecordPo.class, forVariable(variable), schema, "lau_self_danmaku_group_audit_record");
        addMetadata();
    }

    public QLauSelfDanmakuGroupAuditRecord(Path<? extends LauSelfDanmakuGroupAuditRecordPo> path) {
        super(path.getType(), path.getMetadata(), "null", "lau_self_danmaku_group_audit_record");
        addMetadata();
    }

    public QLauSelfDanmakuGroupAuditRecord(PathMetadata metadata) {
        super(LauSelfDanmakuGroupAuditRecordPo.class, metadata, "null", "lau_self_danmaku_group_audit_record");
        addMetadata();
    }

    public void addMetadata() {
        addMetadata(accountId, ColumnMetadata.named("account_id").withIndex(3).ofType(Types.INTEGER).withSize(10).notNull());
        addMetadata(auditorName, ColumnMetadata.named("auditor_name").withIndex(5).ofType(Types.VARCHAR).withSize(128).notNull());
        addMetadata(auditTime, ColumnMetadata.named("audit_time").withIndex(7).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(ctime, ColumnMetadata.named("ctime").withIndex(8).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(danmakuNum, ColumnMetadata.named("danmaku_num").withIndex(13).ofType(Types.BIGINT).withSize(19).notNull());
        addMetadata(groupId, ColumnMetadata.named("group_id").withIndex(2).ofType(Types.BIGINT).withSize(20).notNull());
        addMetadata(groupName, ColumnMetadata.named("group_name").withIndex(15).ofType(Types.VARCHAR).withSize(128).notNull());
        addMetadata(groupType, ColumnMetadata.named("group_type").withIndex(14).ofType(Types.INTEGER).withSize(10).notNull());
        addMetadata(id, ColumnMetadata.named("id").withIndex(1).ofType(Types.BIGINT).withSize(20).notNull());
        addMetadata(isDeleted, ColumnMetadata.named("is_deleted").withIndex(10).ofType(Types.TINYINT).withSize(3).notNull());
        addMetadata(mtime, ColumnMetadata.named("mtime").withIndex(9).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(operateType, ColumnMetadata.named("operate_type").withIndex(4).ofType(Types.TINYINT).withSize(3).notNull());
        addMetadata(reason, ColumnMetadata.named("reason").withIndex(11).ofType(Types.VARCHAR).withSize(255).notNull());
        addMetadata(sendAuditTime, ColumnMetadata.named("send_audit_time").withIndex(6).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(workOrderId, ColumnMetadata.named("work_order_id").withIndex(12).ofType(Types.BIGINT).withSize(20).notNull());
    }

}

