package com.bilibili.adp.cpc.databus.sub;

import com.alibaba.fastjson.JSONObject;
import com.bilibili.adp.cpc.biz.bos.WarpDatabusProperty;
import com.bilibili.adp.cpc.biz.services.creative.bos.CreativeBinlogBo;
import com.bilibili.adp.cpc.biz.services.creative.business.content.BusinessContentService;
import com.bilibili.adp.cpc.po.ad.LauLiveConversionComponentPo;
import com.bilibili.warp.databus.AckableMessage;
import com.bilibili.warp.databus.DatabusProperty;
import com.bilibili.warp.databus.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import com.bilibili.adp.cpc.databus.Constant;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description
 * @date 2024/7/10
 **/
@Component
@Slf4j
public class LauLiveConversionComponentBinlogSub implements MessageListener {

    private final String topic;
    private final String group;

    @Resource
    private BusinessContentService businessContentService;

    @Override
    public String topic() {
        return this.topic;
    }

    @Override
    public String group() {
        return this.group;
    }

    public LauLiveConversionComponentBinlogSub(WarpDatabusProperty warpDatabusProperty) {
        // 注入DatabusProperties，此处的ALIAS常量即配置文件中填写的别名，是topic的上一级配置键
        // pub场景同理，在使用DatabusTemplate的地方注入DatabusProperties来获取
        Map<String, DatabusProperty> properties = warpDatabusProperty.getProperties();
        DatabusProperty property = properties.get("live-conversion-component");
        log.info("live-conversion-component, property={}", JSONObject.toJSONString(property));
        this.topic = property.getTopic();
        this.group = property.getSub().getGroup();
    }

    @Override
    public void onMessage(AckableMessage ackableMessage) {
        try {
            String value = new String(ackableMessage.payload());
            JSONObject msg = JSONObject.parseObject(value);

            String action = msg.getString(Constant.ACTION);
            if (Constant.DELETE.equals(action)) {
                return;
            }

            JSONObject oldObject = msg.getJSONObject(Constant.OLD);
            LauLiveConversionComponentPo oldBo = deserializeBinlogDto(oldObject);
            JSONObject newObject = msg.getJSONObject(Constant.NEW);
            LauLiveConversionComponentPo newBo = deserializeBinlogDto(newObject);
            businessContentService.pubManagerLive(newBo);
            ackableMessage.ack();
        } catch (Exception e) {
            log.info("info {}", ackableMessage);
            log.error("LauLiveConversionComponentBinlogSub sub error ", e);
        }
    }

    private LauLiveConversionComponentPo deserializeBinlogDto(JSONObject jsonObject) {
        if (Objects.isNull(jsonObject)) {
            return null;
        }
        return jsonObject.toJavaObject(LauLiveConversionComponentPo.class);
    }
}
