package com.bilibili.adp.cpc.biz.services.creative;

import com.bapis.ad.component.*;
import com.bilibili.adp.common.DbTable;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.enums.AuditStatus;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.biz.bos.component.*;
import com.bilibili.adp.cpc.biz.services.account.AdpCpcAccountService;
import com.bilibili.adp.cpc.biz.services.creative.bos.ButtonBo;
import com.bilibili.adp.cpc.biz.services.creative.dto.CpcCreativeDto;
import com.bilibili.adp.cpc.biz.services.misc.AdpCpcOperationLogService;
import com.bilibili.adp.cpc.core.LaunchUnitCreativeService;
import com.bilibili.adp.cpc.dao.querydsl.pos.AccAccountPo;
import com.bilibili.adp.cpc.databus.bos.StoryComponentGoodsMessageBo;
import com.bilibili.adp.cpc.enums.ad.PromotionPurposeType;
import com.bilibili.adp.cpc.grpc.AdAuditRpcService;
import com.bilibili.adp.cpc.proxy.CpmScvProxy;
import com.bilibili.adp.launch.api.common.CreativeStatus;
import com.bilibili.adp.launch.api.common.LaunchStatus;
import com.bilibili.adp.launch.api.creative.dto.QueryCpcCreativeDto;
import com.bilibili.adp.launch.biz.service.component.LaunchComponentService;
import com.bilibili.adp.log.service.LogOperateService;
import com.bilibili.crm.platform.common.IsValid;
import com.bilibili.location.api.service.query.IQueryButtonCopyService;
import com.bilibili.location.api.template.dto.ButtonCopyDto;
import com.bilibili.location.common.ButtonCopyTypeEnum;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.bilibili.adp.cpc.dao.config.PlatformMySqlConfig.AD_TM;

@Slf4j
@Service
public class StoryComponentService {
    private final AdpCpcAccountService adpCpcAccountService;
    private final AdpCpcOperationLogService adpCpcOperationLogService;
    private final IQueryButtonCopyService queryButtonCopyService;
    private final LaunchComponentService launchComponentService;
    private final AdAuditRpcService adAuditRpcService;
    private final CpcCreativeService cpcCreativeService;
    private final CpcCreativeServiceDelegate creativeServiceDelegate;
    private final LogOperateService logOperateService;

    @Autowired
    private LaunchUnitCreativeService launchUnitCreativeService;

    @Resource
    private CpmScvProxy cpmScvProxy;

    // 目前仅图片翻转卡不需要绑按钮
    public static final int STORY_COMMON = 2;
    public static final int STORY_COUPON = 3;
    public static final int STORY_IMAGE = 4;
    public static final int STORY_PRICE_DIFFERENCE = 6; // 5被评论组件占用了

    private static final Set<String> STORY_BUTTON_SET = new HashSet<>(Arrays.asList("了解更多", "立即报名", "预约体验", "抢先报名", "领取课程", "免费领取课程", "免费试听", "试听课程", "免费领取", "免费申领", "测一测", "开始探索", "开始测试", "领取门票", "立即报名", "下载查看", "立即下载", "下载", "免费下载", "去下载", "下载体验", "立即预约", "预约", "去预约", "查看详情", "去逛逛", "去看看", "领取优惠", "查看优惠", "买它！", "立即购买", "更多精彩", "更多爆款", "立即抢购", "买同款", "马上种草", "免费体验", "点我玩玩", "抢先体验", "抢先挑战", "新手礼包", "领取福利", "查看额度", "申请额度", "测测额度"));

    public StoryComponentService(
            AdpCpcAccountService adpCpcAccountService,
            AdpCpcOperationLogService adpCpcOperationLogService,
            IQueryButtonCopyService queryButtonCopyService,
            LaunchComponentService launchComponentService,
            AdAuditRpcService adAuditRpcService,
            CpcCreativeService cpcCreativeService,
            CpcCreativeServiceDelegate creativeServiceDelegate,
            LogOperateService logOperateService) {
        this.adpCpcAccountService = adpCpcAccountService;
        this.adpCpcOperationLogService = adpCpcOperationLogService;
        this.queryButtonCopyService = queryButtonCopyService;
        this.launchComponentService = launchComponentService;
        this.adAuditRpcService = adAuditRpcService;
        this.cpcCreativeService = cpcCreativeService;
        this.creativeServiceDelegate = creativeServiceDelegate;
        this.logOperateService = logOperateService;
    }

    public void validateCoupon(List<ComponentInfoBo> components, Integer accountId,
                               String launchEndDate, String launchBeginDate) {
        if (CollectionUtils.isEmpty(components)) return;

        final Map<String, ComponentInfoBo> map = components.stream()
                .collect(Collectors.toMap(x -> x.getId() + "-" + x.getType(), Function.identity(), (x, y) -> y));
        if (!CollectionUtils.isEmpty(map)) {
            for (ComponentInfoBo componentInfoBo : map.values()) {
                final LauStoryComponentBo component = get(componentInfoBo.getId(), componentInfoBo.getType(), accountId);
                if (Objects.isNull(component)) continue;
                if (LaunchComponentService.isStoryCoupon(component.getComponentType())) {
                    // 优惠券获取end时间 <= 优惠券有效end时间
                    Assert.isTrue(component.getCoupon().getObtainPeriodEnd().compareTo(
                            component.getCoupon().getUsePeriodEnd()) <= 0, "优惠券的获取时间不能晚于优惠券的使用时间");
                    // 优惠券获取end时间 >= 单元投放结束时间
                    Assert.isTrue(component.getCoupon().getObtainPeriodEnd().compareTo(Timestamp.valueOf(
                                    LocalDate.parse(launchEndDate).atStartOfDay())) >= 0,
                            "必须满足单元投放结束时间 <= 优惠券投放结束时间");
                    // 优惠券获取begin时间 <= 单元投放开始时间
                    Assert.isTrue(component.getCoupon().getObtainPeriodStart().compareTo(Timestamp.valueOf(
                                    LocalDate.parse(launchBeginDate).atStartOfDay())) <= 0,
                            "必须满足单元投放开始时间 >= 优惠券投放开始时间");
                }
            }
        }
    }


    @Transactional(AD_TM)
    public Long save(LauStoryComponentBo bo, Operator operator, List<Integer> needPushCommonCreatives, List<Integer> needPushProgrammaticCreatives) {
        final AccAccountPo accountPo = adpCpcAccountService.get(bo.getAccountId());
        Assert.notNull(accountPo, "账号不存在");
        validateSaveBo(bo);

        final LauStoryComponentBo prevBo = get(bo.getComponentId(), bo.getComponentType(), bo.getAccountId());
        // 新建or更新
        StoryComponent storyComponent = toRpcBo(bo);
        final StoryComponentIdAndType storyComponentIdAndType = cpmScvProxy.saveStoryComponent(storyComponent);
        bo.setComponentId(storyComponentIdAndType.getId());

        adpCpcOperationLogService.saveOperationLog(bo.meta(), prevBo, bo, operator);

        boolean isChanged = LauStoryComponentBo.isChanged(prevBo, bo);
        if (isChanged) {
            getAffectedCreatives(bo, operator, needPushCommonCreatives, needPushProgrammaticCreatives);
        }
        log.info("StoryComponentService needPushCommonCreatives: {}, needPushProgrammaticCreatives: {}", needPushCommonCreatives, needPushProgrammaticCreatives);
        return storyComponentIdAndType.getId();
    }

    public void getAffectedCreatives(LauStoryComponentBo bo, Operator operator, List<Integer> needPushCommonCreatives, List<Integer> needPushProgrammaticCreatives) {
        // 查询组件关联的创意
        final List<Integer> boundCreativeIds = launchComponentService.getBoundCreativeIds(bo.getComponentId(), bo.getComponentType());
        if (!CollectionUtils.isEmpty(boundCreativeIds)) {
            // 区分已暂停的创意、非暂停的创意
            List<CpcCreativeDto> cpcCreativeDtos = cpcCreativeService.queryCpcCreativeDto(QueryCpcCreativeDto.builder()
                    .creativeIds(boundCreativeIds)
                    .build());
            List<Integer> pauseCreatives = cpcCreativeDtos.stream()
                    .filter(o -> Objects.equals(CreativeStatus.PAUSED.getCode(), o.getCreativeStatus()))
                    .map(o -> o.getCreativeId())
                    .collect(Collectors.toList());
            List<Integer> notPauseCreatives = cpcCreativeDtos.stream()
                    .filter(o -> !Objects.equals(CreativeStatus.PAUSED.getCode(), o.getCreativeStatus()))
                    .map(o -> o.getCreativeId())
                    .collect(Collectors.toList());
            Map<Integer, CpcCreativeDto> map = cpcCreativeDtos.stream().collect(Collectors.toMap(CpcCreativeDto::getCreativeId, Function.identity()));
            // 已暂停的创意id
            if (!CollectionUtils.isEmpty(pauseCreatives)) {
                for (Integer creativeId : pauseCreatives) {
                    // 维持驳回
                    log.info("StoryComponentService reject, componentId:{}, pauseCreatives:{}", bo.getComponentId(), pauseCreatives);
                    adAuditRpcService.reject(creativeId, "绑定的story组件已变更, 需要重新审核", false);
                }
            }
            // 非暂停的创意id，状态改为"审核中"
            if (!CollectionUtils.isEmpty(notPauseCreatives)) {
                for (Integer creativeId : notPauseCreatives) {
                    // 需要重新审核
                    CpcCreativeDto oldCreative = map.get(creativeId);
                    CpcCreativeDto logDto = new CpcCreativeDto();
                    logDto.setAuditStatus(AuditStatus.INIT.getCode());
                    logDto.setReason("Story组件ID " + bo.getComponentId() + "，内容变更，关联创意已送审");
                    if (oldCreative.getStatus() == LaunchStatus.START.getCode()) {
                        logDto.setCreativeStatus(CreativeStatus.AUDITING.getCode());
                        // 日志
                        logOperateService.addBatchUpdateStatusLog(DbTable.LAU_UNIT_CREATIVE, operator,
                                Lists.newArrayList(creativeServiceDelegate.creativeDtoToOperationDto(logDto, null)), Lists.newArrayList(creativeId), Lists.newArrayList(oldCreative.getAccountId()));
                        if (IsValid.TRUE.getCode().equals(oldCreative.getIsProgrammatic())) {
                            // 不变更程序化创意元素状态，follow保存创意逻辑
                            // creativeStatus = 5
                            launchUnitCreativeService.updateProgrammaticAuditStatus(creativeId, AuditStatus.INIT.getCode(), CreativeStatus.AUDITING.getCode(),
                                    AuditStatus.INIT.getCode(), AuditStatus.INIT.getCode(), "创意组件变更");
                            needPushProgrammaticCreatives.add(creativeId);
                        } else {
                            // creativeStatus = 5
                            cpcCreativeService.updateAuditStatusAndCreativeStatus(creativeId, AuditStatus.INIT.getCode(), CreativeStatus.AUDITING.getCode());
                            needPushCommonCreatives.add(creativeId);
                        }
                    } else {
                        // 日志
                        logOperateService.addBatchUpdateStatusLog(DbTable.LAU_UNIT_CREATIVE, operator,
                                Lists.newArrayList(creativeServiceDelegate.creativeDtoToOperationDto(logDto, null)), Lists.newArrayList(creativeId), Lists.newArrayList(oldCreative.getAccountId()));
                        if (IsValid.TRUE.getCode().equals(oldCreative.getIsProgrammatic())) {
                            // 不变更程序化创意元素状态，follow保存创意逻辑
                            // creativeStatus = null
                            launchUnitCreativeService.updateProgrammaticAuditStatus(creativeId, AuditStatus.INIT.getCode(), null,
                                    AuditStatus.INIT.getCode(), AuditStatus.INIT.getCode(), "创意组件变更");
                            needPushProgrammaticCreatives.add(creativeId);
                        } else {
                            // creativeStatus = null
                            cpcCreativeService.updateAuditStatusAndCreativeStatus(creativeId, AuditStatus.INIT.getCode(), null);
                            needPushCommonCreatives.add(creativeId);
                        }
                    }
                }
            }
        }
    }

    private void validateSaveBo(LauStoryComponentBo bo) {
        Assert.notNull(bo, "保存Story创意组件对象不可为空");
        switch (bo.getComponentType()) {
            case STORY_COMMON:
                validateCommonComponentBo(bo);
                break;
            case STORY_COUPON:
                validateCouponComponentBo(bo);
                break;
            case STORY_IMAGE:
                validateImageComponentBo(bo);
                break;
            case STORY_PRICE_DIFFERENCE:
                validatePriceDifferenceComponentBo(bo);
                break;
            default:
                throw new IllegalArgumentException("未知的story创意组件类型:" + bo.getComponentType());
        }
    }

    private void validateCommonComponentBo(LauStoryComponentBo saveBo) {
        Assert.isTrue(Utils.isPositive(saveBo.getAccountId()), "保存Story创意组件账户id不可为空");
        Assert.hasText(saveBo.getComponentName(), "保存Story创意组件组件名称不可为空");
        LauStoryCommonComponentBo common = saveBo.getCommon();
        Assert.notNull(common, "保存Story创意组件普通组件对象不可为空");
        Assert.hasText(common.getTitle(), "保存Story创意组件普通组件标题不可为空");
        Assert.hasText(common.getDescription(), "保存Story创意组件普通组件描述不可为空");
        Assert.hasText(common.getImageUrl(), "保存Story创意组件普通组件图片url不可为空");
        Assert.hasText(common.getImageMd5(), "保存Story创意组件普通组件图片md5不可为空");
        Assert.isTrue(Utils.isPositive(common.getButtonId()), "保存Story创意组件普通组件按钮id不可为空");
        Assert.isTrue(Utils.isPositive(common.getButtonType()), "保存Story创意组件普通组件按钮类型不可为空");
        Assert.hasText(common.getButtonText(), "保存Story创意组件普通组件按钮文案不可为空");
        Assert.notEmpty(common.getKeywords(), "保存Story创意组件普通组件推广卖点不可为空");
        validateButton(saveBo.getComponentType(), common.getButtonId(), common.getButtonType(), common.getButtonText());

        Assert.isNull(saveBo.getCoupon(), "保存Story创意普通组件不允许保存优惠券组件");
        Assert.isNull(saveBo.getImage(), "保存Story创意普通组件不允许保存图片翻转组件");
        Assert.isNull(saveBo.getPriceDifference(), "保存Story创意普通组件不允许保存价差卡组件");
    }

    private void validateCouponComponentBo(LauStoryComponentBo saveBo) {
        Assert.isTrue(Utils.isPositive(saveBo.getAccountId()), "保存Story创意组件账户id不可为空");
        Assert.hasText(saveBo.getComponentName(), "保存Story创意组件组件名称不可为空");
        LauStoryCouponComponentBo coupon = saveBo.getCoupon();
        Assert.notNull(coupon, "保存Story创意组件普通组件对象不可为空");
        Assert.isTrue(Utils.isPositive(coupon.getCostFen()), "保存Story创意组件优惠券组件金额不可为空");
        Assert.hasText(coupon.getDescription(), "保存Story创意组件优惠券组件描述不可为空");
        Assert.hasText(coupon.getComment(), "保存Story创意组件优惠券组件备注信息不可为空");
        Assert.notNull(coupon.getObtainPeriodStart(), "保存Story创意组件优惠券组件投放开始日期不可为空");
        Assert.notNull(coupon.getObtainPeriodEnd(), "保存Story创意组件优惠券组件投放结束日期不可为空");
        Assert.notNull(coupon.getUsePeriodStart(), "保存Story创意组件优惠券组件有效期开始日期不可为空");
        Assert.notNull(coupon.getUsePeriodEnd(), "保存Story创意组件优惠券组件有效期结束日期不可为空");
        Assert.isTrue(coupon.getObtainPeriodEnd().getTime() >= coupon.getObtainPeriodStart().getTime(),
                "保存Story创意组件优惠券组件投放结束日期不可早于投放开始日期");
        Assert.isTrue(coupon.getUsePeriodEnd().getTime() >= coupon.getUsePeriodStart().getTime(),
                "保存Story创意组件优惠券组件有效期结束日期不可早于有效期开始日期");
        Assert.isTrue(Utils.isPositive(coupon.getButtonId()), "保存Story创意组件优惠券组件按钮id不可为空");
        Assert.isTrue(Utils.isPositive(coupon.getButtonType()), "保存Story创意组件优惠券组件按钮类型不可为空");
        Assert.hasText(coupon.getButtonText(), "保存Story创意组件优惠券组件按钮文案不可为空");
        validateButton(saveBo.getComponentType(), coupon.getButtonId(), coupon.getButtonType(), coupon.getButtonText());

        Assert.isNull(saveBo.getCommon(), "保存Story创意优惠券组件不允许保存普通组件");
        Assert.isNull(saveBo.getImage(), "保存Story创意优惠券组件不允许保存图片翻转组件");
        Assert.isNull(saveBo.getPriceDifference(), "保存Story创意优惠券组件不允许保存价差卡组件");
    }

    private void validateImageComponentBo(LauStoryComponentBo saveBo) {
        Assert.isTrue(Utils.isPositive(saveBo.getAccountId()), "保存Story创意组件账户id不可为空");
        Assert.hasText(saveBo.getComponentName(), "保存Story创意组件组件名称不可为空");
        LauStoryImageComponentBo image = saveBo.getImage();
        Assert.notNull(image, "保存Story创意组件图片翻转组件不可为空");
        Assert.hasText(image.getImageUrl(), "保存Story创意组件图片翻转组件图片url不可为空");
        Assert.hasText(image.getImageMd5(), "保存Story创意组件图片翻转组件图片md5不可为空");

        Assert.isNull(saveBo.getCommon(), "保存Story创意图片翻转组件不允许保存普通组件");
        Assert.isNull(saveBo.getCoupon(), "保存Story创意图片翻转组件不允许保存优惠券组件");
        Assert.isNull(saveBo.getPriceDifference(), "保存Story创意图片翻转组件不允许保存价差卡组件");
    }

    private void validatePriceDifferenceComponentBo(LauStoryComponentBo saveBo) {
        Assert.isTrue(Utils.isPositive(saveBo.getAccountId()), "保存Story创意组件账户id不可为空");
        Assert.hasText(saveBo.getComponentName(), "保存Story创意组件组件名称不可为空");
        LauStoryPriceDifferenceComponentBo priceDifference = saveBo.getPriceDifference();
        Assert.notNull(priceDifference, "保存Story创意组件价差卡组件不可为空");
        Assert.hasText(priceDifference.getImageUrl(), "保存Story创意组件价差卡组件图片url不可为空");
        Assert.hasText(priceDifference.getImageMd5(), "保存Story创意组件价差卡组件图片md5不可为空");
        Assert.hasText(priceDifference.getTitle(), "保存Story创意组件价差卡组件图片卡片标题不可为空");
        Assert.hasText(priceDifference.getItemId(), "保存Story创意组件价差卡组件商品id不可为空");
        Assert.hasText(priceDifference.getJumpUrl(), "保存Story创意组件价差卡组件商品跳转链接不可为空");
        Assert.isTrue(Utils.isPositive(priceDifference.getOriginalPrice()), "保存Story创意组件价差卡组件原价不可为空");
        Assert.isTrue(Utils.isPositive(priceDifference.getPrice()), "保存Story创意组件价差卡组件优惠价不可为空");

        Assert.isNull(saveBo.getCommon(), "保存Story创意图片翻转组件不允许保存普通组件");
        Assert.isNull(saveBo.getCoupon(), "保存Story创意图片翻转组件不允许保存优惠券组件");
        Assert.isNull(saveBo.getImage(), "保存Story创意图片翻转组件不允许保存图片翻转组件");
    }

    private void validateButton(Integer componentType, Integer buttonId, Integer buttonType, String buttonText) {
        List<ButtonBo> buttonBos = listButtons(0, componentType);
        ButtonBo resultButtonBo = buttonBos.stream()
                .filter(buttonBo -> buttonBo.getButtonId().equals(buttonId))
                .findFirst().orElseThrow(() -> new IllegalArgumentException("当前story创意组件类型不支持按钮" + buttonId));
        Assert.isTrue(resultButtonBo.getButtonType().equals(buttonType), "保存Story创意组件按钮类型错误");
        Assert.isTrue(resultButtonBo.getButtonText().equals(buttonText), "保存Story创意组件按钮文案错误");
    }

    private static BaseComponent toRpcBaseComponent(Long id, String name, Integer type, Integer accountId) {
        Assert.isTrue(StringUtils.hasText(name), "组件名称不能为空");
        Assert.isTrue(Utils.isPositive(type), "组件类型不能为空");

        return BaseComponent.newBuilder()
                .setId(Optional.ofNullable(id).orElse(0L))
                .setName(name)
                .setType(type)
                .setAccountId(accountId)
                .setCtimeMilli(0L)
                .build();
    }

    private StoryComponent toRpcBo(LauStoryComponentBo bo) {
        final StoryComponent.Builder builder = StoryComponent.newBuilder();
        switch (bo.getComponentType()) {
            case LaunchComponentService.STORY_COMMON:
                builder.setBaseComponent(toRpcBaseComponent(bo.getComponentId(), bo.getComponentName(), bo.getComponentType(), bo.getAccountId()));
                builder.setCommon(LauStoryCommonComponentBo.toRpcBo(bo.getCommon()));
                break;
            case LaunchComponentService.STORY_COUPON:
                builder.setBaseComponent(toRpcBaseComponent(bo.getComponentId(), bo.getComponentName(), bo.getComponentType(), bo.getAccountId()));
                builder.setCoupon(LauStoryCouponComponentBo.toRpcBo(bo.getCoupon()));
                break;
            case LaunchComponentService.STORY_IMAGE:
                builder.setBaseComponent(toRpcBaseComponent(bo.getComponentId(), bo.getComponentName(), bo.getComponentType(), bo.getAccountId()));
                builder.setImage(LauStoryImageComponentBo.toRpcBo(bo.getImage()));
                break;
            case STORY_PRICE_DIFFERENCE:
                builder.setBaseComponent(toRpcBaseComponent(bo.getComponentId(), bo.getComponentName(), bo.getComponentType(), bo.getAccountId()));
                builder.setPriceDifference(LauStoryPriceDifferenceComponentBo.toRpcBo(bo.getPriceDifference()));
                break;
            default:
                throw new IllegalArgumentException("无法识别story组件类型");
        }
        return builder.build();
    }

    public LauStoryComponentBo get(Long id, Integer type, Integer accountId) {
        if (!Utils.isPositive(id)) return null;

        final StoryComponent component = cpmScvProxy.getStoryComponent(StoryComponentIdAndType.newBuilder()
                .setId(id)
                .setType(type)
                .build());
        final LauStoryComponentBo bo = LauStoryComponentBo.fromRpcBo(component);
        Assert.isTrue(Objects.equals(accountId, bo.getAccountId()), "只能获取自己账号下的组件");

        return bo;
    }

    public LauStoryComponentBo getBo4Creative(Long id, Integer type) {
        if (!Utils.isPositive(id)) return null;

        final StoryComponent component = cpmScvProxy.getStoryComponent(StoryComponentIdAndType.newBuilder()
                .setId(id)
                .setType(type)
                .build());
        final LauStoryComponentBo bo = LauStoryComponentBo.fromRpcBo(component);

        return bo;
    }

    public List<LauStoryComponentBo> list(Integer accountId, Long componentId, Integer componentType, String componentName, Long startCtime, Long endCtime, Integer scope) {
        final StoryComponents components = cpmScvProxy.listStoryComponent(StoryComponentListParams.newBuilder()
                .setAccountId(accountId)
                .setCtimeMilliStart(startCtime)
                .setCtimeMilliEnd(endCtime)
                .setScope(scope)
                .build());
        // 根据条件进行过滤
        Stream<StoryComponent> stream = components.getComponentsList().stream();
        if (Utils.isPositive(componentId)) {
            stream = stream.filter(x -> Objects.equals(x.getBaseComponent().getId(), componentId));
        }
        if (StringUtils.hasText(componentName)) {
            stream = stream.filter(x -> x.getBaseComponent().getName().contains(componentName));
        }
        if (Utils.isPositive(componentType)) {
            stream = stream.filter(x -> Objects.equals(x.getBaseComponent().getType(), componentType));
        }

        return stream.map(LauStoryComponentBo::fromRpcBo).collect(Collectors.toList());
    }

    public void delete(Long id, Integer type) {
        cpmScvProxy.deleteStoryComponent(StoryComponentIdAndType.newBuilder()
                .setId(id)
                .setType(type)
                .build());
    }

    public List<ButtonBo> listButtons(Integer ppt, Integer componentType) {
        final List<ButtonCopyDto> dtos = queryButtonCopyService.list();
        final boolean supportDownload = Objects.equals(ppt, 0) || Objects.equals(ppt, PromotionPurposeType.APP_DOWNLOAD.getCode()) || Objects.equals(ppt, PromotionPurposeType.ON_SHELF_GAME.getCode());
        Stream<ButtonCopyDto> stream = dtos.stream()
                .filter(x -> STORY_BUTTON_SET.contains(x.getContent()))
                .filter(x -> supportDownload || Objects.equals(x.getType(), ButtonCopyTypeEnum.JUMP_LINK.getCode()));
        if (Utils.isPositive(componentType) && LaunchComponentService.isStoryCoupon(componentType)) {
            stream = stream.filter(x -> x.getContent().length() <= 3);
        }
        return stream.collect(Collectors.toMap(ButtonCopyDto::getContent, Function.identity(), (x, y) -> y))
                .values()
                .stream()
                .map(x -> ButtonBo.builder()
                        .buttonId(x.getId())
                        .buttonText(x.getContent())
                        .buttonType(x.getContent().contains("下载") ? ButtonCopyTypeEnum.APP_DOWN_AWAKEN.getCode() : x.getType())
                        .build())
                .collect(Collectors.toList());
    }

    // 更新组件中商品相关信息
    @Transactional(AD_TM)
    public void updateComponentGoodsInfo(StoryComponents imageStoryComponents, StoryComponents priceDifferenceStoryComponents, StoryComponentGoodsMessageBo newBo, StoryComponentGoodsMessageBo oldBo, Operator operator) {
        for (StoryComponent storyComponent : imageStoryComponents.getComponentsList()) {
            StoryComponent saveStoryComponent = toImageRpc(storyComponent, newBo);
            log.info("StoryComponentGoodsBinlogSub save image storyComponent: {}", saveStoryComponent);
            saveStoryComponent(saveStoryComponent, toImageRpc(storyComponent, oldBo), operator);
        }
        for (StoryComponent storyComponent : priceDifferenceStoryComponents.getComponentsList()) {
            StoryComponent saveStoryComponent = toPriceDifferenceRpc(storyComponent, newBo);
            log.info("StoryComponentGoodsBinlogSub save priceDifference storyComponent: {}", saveStoryComponent);
            saveStoryComponent(saveStoryComponent, toPriceDifferenceRpc(storyComponent, oldBo), operator);
        }
    }

    private Long saveStoryComponent(StoryComponent storyComponent, StoryComponent prev, Operator operator) {
        StoryComponentIdAndType rsp = cpmScvProxy.saveStoryComponent(storyComponent);
        LauStoryComponentBo bo = LauStoryComponentBo.fromRpcBo(storyComponent);
        LauStoryComponentBo prevBo = LauStoryComponentBo.fromRpcBo(prev);
        adpCpcOperationLogService.saveOperationLog(bo.meta(), prevBo, bo, operator);
        return rsp.getId();
    }

    private StoryComponent toImageRpc(StoryComponent component, StoryComponentGoodsMessageBo newBo) {
        StoryComponent.Builder builder = StoryComponent.newBuilder();
        BaseComponent.Builder baseComponent = BaseComponent.newBuilder();
        baseComponent.setId(component.getBaseComponent().getId())
                .setType(component.getBaseComponent().getType())
                .setName(component.getBaseComponent().getName())
                .setAccountId(component.getBaseComponent().getAccountId());
        builder.setBaseComponent(baseComponent.build());
        Image.Builder image = Image.newBuilder();
        image.setImageUrl(component.getImage().getImageUrl());
        image.setImageMd5(component.getImage().getImageMd5());
        GoodsInfo.Builder goodsBuilder = GoodsInfo.newBuilder();
        if (component.getImage().hasGoodsInfo()) {
            goodsBuilder.setItemId(newBo.getItemId())
                    .setItemSource(newBo.getSourceType())
                    .setJumpType(newBo.getUrlType())
                    .setJumpUrl(newBo.getJumpUrl());
            if (newBo.getSchemaUrl() != null) {
                goodsBuilder.setSchemaUrl(newBo.getSchemaUrl());
            }
            if (newBo.getMiniAppOriginId() != null) {
                goodsBuilder.setMiniProgramId(newBo.getMiniAppOriginId());
            }
            if (newBo.getMiniAppName() != null) {
                goodsBuilder.setMiniProgramName(newBo.getMiniAppName());
            }
            if (newBo.getMiniAppPath() != null) {
                goodsBuilder.setMiniProgramPath(newBo.getMiniAppPath());
            }
            image.setGoodsInfo(goodsBuilder.build());
        }
        builder.setImage(image.build());
        return builder.build();
    }

    private StoryComponent toPriceDifferenceRpc(StoryComponent component, StoryComponentGoodsMessageBo newBo) {
        StoryComponent.Builder builder = StoryComponent.newBuilder();
        BaseComponent.Builder baseComponent = BaseComponent.newBuilder();
        baseComponent.setId(component.getBaseComponent().getId())
                .setType(component.getBaseComponent().getType())
                .setName(component.getBaseComponent().getName())
                .setAccountId(component.getBaseComponent().getAccountId());
        builder.setBaseComponent(baseComponent.build());
        PriceDifference.Builder priceDifference = PriceDifference.newBuilder();
        priceDifference.setImageUrl(component.getPriceDifference().getImageUrl());
        priceDifference.setImageMd5(component.getPriceDifference().getImageMd5());
        priceDifference.setTitle(component.getPriceDifference().getTitle());
        priceDifference.setPrice(component.getPriceDifference().getPrice());
        priceDifference.setOriginalPrice(component.getPriceDifference().getOriginalPrice());
        GoodsInfo.Builder goodsBuilder = GoodsInfo.newBuilder();
        if (component.getPriceDifference().hasGoodsInfo()) {
            goodsBuilder.setItemId(newBo.getItemId())
                    .setItemSource(newBo.getSourceType())
                    .setJumpType(newBo.getUrlType())
                    .setJumpUrl(newBo.getJumpUrl());
            if (newBo.getSchemaUrl() != null) {
                goodsBuilder.setSchemaUrl(newBo.getSchemaUrl());
            }
            if (newBo.getMiniAppOriginId() != null) {
                goodsBuilder.setMiniProgramId(newBo.getMiniAppOriginId());
            }
            if (newBo.getMiniAppName() != null) {
                goodsBuilder.setMiniProgramName(newBo.getMiniAppName());
            }
            if (newBo.getMiniAppPath() != null) {
                goodsBuilder.setMiniProgramPath(newBo.getMiniAppPath());
            }
            priceDifference.setGoodsInfo(goodsBuilder.build());
        }
        builder.setPriceDifference(priceDifference.build());
        return builder.build();
    }
}