package com.bilibili.adp.cpc.config.databus.creative;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bilibili.adp.cpc.biz.bos.WarpDatabusProperty;
import com.bilibili.adp.cpc.biz.constants.Constants;
import com.bilibili.adp.cpc.biz.services.creative.bos.CreativeAuditESNotifyBo;
import com.bilibili.adp.cpc.biz.services.creative.bos.CreativeEsBo;
import com.bilibili.adp.cpc.config.CreativeEsConfig;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauCreativeFlyDynamicInfoPo;
import com.bilibili.adp.cpc.databus.GenCreativeEsPub;
import com.bilibili.adp.util.common.AdpCatUtils;
import com.bilibili.warp.databus.AckableMessage;
import com.bilibili.warp.databus.DatabusProperty;
import com.bilibili.warp.databus.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Date 2023/6/16 11:38 上午
 * @Version 1.0
 **/
@Component
@Slf4j
public class CreativeFlyDynamicInfoBinlogListener implements MessageListener {

    public static final String CREATIVE_BINLOG = "creative-fly-dynamic-info-binlog";
    private final String topic;
    private final String group;

    @Value("${databus.creative.audit.es.enabled:true}")
    private Boolean enabled;

    @Autowired
    private GenCreativeEsPub esPub;
    @Autowired
    private CreativeEsConfig creativeEsConfig;
    @Autowired
    private Creative2EsService creative2EsService;

    private static final String ACTION = "action";
    private static final String INSERT = "insert";
    private static final String UPDATE = "update";
    private static final String DELETE = "delete";
    private static final String NEW = "new";
    private static final String OLD = "old";

    public CreativeFlyDynamicInfoBinlogListener(WarpDatabusProperty warpDatabusProperty) {
        // 注入DatabusProperties，此处的ALIAS常量即配置文件中填写的别名，是topic的上一级配置键
        // pub场景同理，在使用DatabusTemplate的地方注入DatabusProperties来获取
        Map<String, DatabusProperty> properties = warpDatabusProperty.getProperties();
        DatabusProperty property = properties.get(CREATIVE_BINLOG);
        log.info("CreativeFlyDynamicInfoBinlogListener, property={}", JSONObject.toJSONString(property));
        this.topic = Objects.isNull(property) ? "" : property.getTopic();
        this.group = Objects.isNull(property) ? "" : property.getSub().getGroup();
    }

    private void handleMsg (String value) {
        JSONObject jsonObject = JSONObject.parseObject(value);
        log.info("CreativeFlyDynamicInfoBinlogListener, json={}", value);

        LauCreativeFlyDynamicInfoPo newBo = getPo(jsonObject);
        if (Objects.isNull(newBo)) {
            // 创建 || 动态id未修改时，不更新es
            return;
        }
        List<CreativeEsBo> creativeEsBos = creative2EsService.generateEsPo(CreativeAuditESNotifyBo.builder()
                .creativeIds(Collections.singletonList(newBo.getCreativeId()))
                .isComplex(0).build());
        if (CollectionUtils.isEmpty(creativeEsBos)) {
            return;
        }
        JSONObject newJson = new JSONObject();
        newJson.put("new", creativeEsBos.get(0));
        newJson.put("action", jsonObject.getString(ACTION));

        esPub.pub(newJson, newBo.getCreativeId());
    }

    private LauCreativeFlyDynamicInfoPo deserialize(JSONObject jsonObject) {
        if (Objects.isNull(jsonObject)) {
            return null;
        }
        LauCreativeFlyDynamicInfoPo LauCreativeFlyDynamicInfoPo = null;
        try {
            LauCreativeFlyDynamicInfoPo = jsonObject.toJavaObject(LauCreativeFlyDynamicInfoPo.class);
        } catch (Exception e) {
            log.error("CreativeFlyDynamicInfoBinlogListener deserializeCreativeBinlogDto error, insertObject:{}", jsonObject, e);
        }
        return LauCreativeFlyDynamicInfoPo;
    }

    public LauCreativeFlyDynamicInfoPo getPo(JSONObject jsonObject) {
        if (Objects.isNull(jsonObject)) {
            return null;
        }
        JSONObject old = jsonObject.getJSONObject(OLD);
        LauCreativeFlyDynamicInfoPo oldDynamicPo = deserialize(old);
        JSONObject neww = jsonObject.getJSONObject(NEW);
        LauCreativeFlyDynamicInfoPo newDynamicPo = deserialize(neww);

        if (Objects.isNull(old) || newDynamicPo.getDynamicId().equals(oldDynamicPo.getDynamicId())) {
            // 创建 || 动态id未修改时，不更新es
            return null;
        }

        return newDynamicPo;
    }

    @Override
    public String topic() {
        return this.topic;
    }

    @Override
    public String group() {
        return this.group;
    }

    @Override
    public void onMessage(AckableMessage ackableMessage) {
        log.info("wrap onMessage, CreativeFlyDynamicInfoBinlogListener ackableMessage={}", JSON.toJSONString(ackableMessage));
        if (!Boolean.TRUE.equals(enabled)) {
            return;
        }

        try {
            AdpCatUtils.newTransaction(Constants.CAT_TYPE_DATABUS, CREATIVE_BINLOG + ":sub", transaction -> {
                String value = new String(ackableMessage.payload());

                handleMsg(value);
            });
        } catch (Exception e) {
            log.error("CreativeFlyDynamicInfoBinlogListener error", e);
        }

    }

    @Override
    public boolean autoCommit() {
        return true;
    }
}
