package com.bilibili.adp.cpc.automatic_rule.service;

import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.enums.AdpVersion;
import com.bilibili.adp.cpc.automatic_rule.bos.ApplicableObjectBo;
import com.bilibili.adp.cpc.automatic_rule.constants.AutomaticRuleConstant;
import com.bilibili.adp.cpc.automatic_rule.constants.AutomaticRuleErrorMessage;
import com.bilibili.adp.cpc.automatic_rule.converter.ObjectConverter;
import com.bilibili.adp.cpc.automatic_rule.enums.object.ObjectType;
import com.bilibili.adp.cpc.core.LaunchUnitV1Service;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauCampaignPo;
import com.bilibili.adp.cpc.dao.querydsl.pos.LauAutomaticRuleObjectPo;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauUnitPo;
import com.bilibili.adp.cpc.utils.CommonFuncs;
import com.bilibili.adp.cpc.utils.RecDiffResult;
import com.bilibili.adp.launch.api.common.CampaignStatus;
import com.bilibili.adp.launch.api.common.UnitStatus;
import com.bilibili.bjcom.querydsl.BaseQueryFactory;
import com.bilibili.bjcom.querydsl.paging.Page;
import com.bilibili.bjcom.querydsl.paging.Pager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.bilibili.adp.cpc.dao.ad_core.querydsl.QLauCampaign.lauCampaign;
import static com.bilibili.adp.cpc.dao.config.PlatformMySqlConfig.*;
import static com.bilibili.adp.cpc.dao.querydsl.QLauAutomaticRuleObject.lauAutomaticRuleObject;
import static com.bilibili.adp.cpc.dao.ad_core.querydsl.QLauUnit.lauUnit;

@Slf4j
@Service
@RequiredArgsConstructor
public class ObjectService {
    @Resource(name = AD_BQF)
    private BaseQueryFactory adBqf;

    @Resource(name = AD_CORE_BQF)
    private BaseQueryFactory adCore;

    private final LaunchUnitV1Service launchUnitV1Service;

    public PageResult<ApplicableObjectBo> applicableObjects(Integer accountId, Integer campaignId, Integer unitId, Integer promotionPurposeType, List<Integer> ocpxTarget, Integer pn, Integer ps) {
        final List<ApplicableObjectBo> accountObjects = Collections.singletonList(po2AutomaticRuleApplicableObjectBo(ObjectType.ACCOUNT.getCode(), 0, accountId, ""));
        final List<LauUnitPo> units = adCore
                .selectFrom(lauUnit)
                .where(lauUnit.accountId.eq(accountId))
                .where(lauUnit.isManaged.eq(0))
                .where(lauUnit.unitStatus.eq(UnitStatus.VALID.getCode()))
                .where(lauUnit.adpVersion.goe(AdpVersion.MIDDLE.getKey()))
                .whereIfNotNull(campaignId, lauUnit.campaignId::eq)
                .whereIfNotNull(unitId, lauUnit.unitId::eq)
                .whereIfNotEmpty(ocpxTarget, lauUnit.ocpcTarget::in)
                .orderBy(lauUnit.mtime.desc())
                .fetch();
        final List<Integer> campaignIds = units
                .stream()
                .map(LauUnitPo::getCampaignId)
                .distinct()
                .collect(Collectors.toList());
        final Page<LauCampaignPo> lauCampaignPoPage = adCore
                .selectFrom(lauCampaign)
                .where(lauCampaign.accountId.eq(accountId))
                .where(lauCampaign.isManaged.eq(0))
                .where(lauCampaign.campaignStatus.eq(CampaignStatus.VALID.getCode()))
                .where(lauCampaign.campaignId.in(campaignIds))
                .whereIfNotNull(campaignId, lauCampaign.campaignId::eq)
                .whereIfNotNull(promotionPurposeType, lauCampaign.promotionPurposeType::eq)
                .orderBy(lauCampaign.mtime.desc())
                .fetchPage(Pager.of(pn, ps));
        final List<LauCampaignPo> campaigns = lauCampaignPoPage.getRows();
        final List<ApplicableObjectBo> campaignObjects = campaigns
                .stream()
                .map(campaign -> po2AutomaticRuleApplicableObjectBo(ObjectType.CAMPAIGN.getCode(), campaign.getAccountId(), campaign.getCampaignId(), campaign.getCampaignName()))
                .collect(Collectors.toList());
        final List<ApplicableObjectBo> unitObjects = units
                .stream()
                .map(unit -> po2AutomaticRuleApplicableObjectBo(ObjectType.UNIT.getCode(), unit.getCampaignId(), unit.getUnitId(), unit.getUnitName()))
                .collect(Collectors.toList());
        final List<ApplicableObjectBo> creativeObjects = Collections.emptyList();
        final List<ApplicableObjectBo> applicableObjects = Stream.of(accountObjects, campaignObjects, unitObjects, creativeObjects)
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
        final List<ApplicableObjectBo> applicableObjectBos = buildApplicableObjects(applicableObjects, 0);
        return PageResult
                .<ApplicableObjectBo>builder()
                .total((int) lauCampaignPoPage.getTotal())
                .records(applicableObjectBos)
                .build();
    }

    private List<ApplicableObjectBo> buildApplicableObjects(List<ApplicableObjectBo> applicableObjects, int parentObjectId) {
        List<ApplicableObjectBo> result = applicableObjects
                .stream()
                .filter(object -> Objects.equals(parentObjectId, object.getParentObjectId()))
                .collect(Collectors.toList());
        result.forEach(object -> {
            List<ApplicableObjectBo> children = buildApplicableObjects(applicableObjects, object.getObjectId());
            object.setObjects(children);
        });
        return result;
    }

    private ApplicableObjectBo po2AutomaticRuleApplicableObjectBo(Integer objectType, Integer parentObjectId, Integer objectId, String objectName) {
        return ApplicableObjectBo
                .builder()
                .objectType(objectType)
                .parentObjectId(parentObjectId)
                .objectId(objectId)
                .objectName(objectName)
                .build();
    }

    public List<LauAutomaticRuleObjectPo> listObjectsByAccountId(Integer accountId) {
        return adBqf
                .selectFrom(lauAutomaticRuleObject)
                .where(lauAutomaticRuleObject.accountId.eq(accountId))
                .where(lauAutomaticRuleObject.isDeleted.eq(0))
                .fetch();
    }

    public List<LauAutomaticRuleObjectPo> listObjectsByRuleId(Long ruleId) {
        return adBqf
                .selectFrom(lauAutomaticRuleObject)
                .where(lauAutomaticRuleObject.ruleId.eq(ruleId))
                .where(lauAutomaticRuleObject.isDeleted.eq(0))
                .fetch();
    }

    public List<LauAutomaticRuleObjectPo> listObjectsByRuleIds(Collection<Long> ruleIds) {
        return adBqf
                .selectFrom(lauAutomaticRuleObject)
                .where(lauAutomaticRuleObject.ruleId.in(ruleIds))
                .where(lauAutomaticRuleObject.isDeleted.eq(0))
                .fetch();
    }

    public List<LauAutomaticRuleObjectPo> listObjectsByRuleIdAndObjectIds(Long ruleId, Integer objectType, List<Integer> objectIds) {
        return adBqf
                .selectFrom(lauAutomaticRuleObject)
                .where(lauAutomaticRuleObject.ruleId.eq(ruleId))
                .where(lauAutomaticRuleObject.objectType.eq(objectType))
                .where(lauAutomaticRuleObject.objectId.in(objectIds))
                .where(lauAutomaticRuleObject.isDeleted.eq(0))
                .fetch();
    }

    @Transactional(transactionManager = AD_TM, rollbackFor = Exception.class)
    public void saveObjects(Integer accountId, Long ruleId, Integer objectType, List<Integer> objectIds) {
        this.verifyUnit(accountId, ruleId, objectType, objectIds);
        final List<LauAutomaticRuleObjectPo> existObjects = listObjectsByRuleId(ruleId);
        final List<LauAutomaticRuleObjectPo> newPos = objectIds
                .stream()
                .map(objectId -> ObjectConverter.MAPPER.bo2Po(accountId, ruleId, objectType, objectId))
                .collect(Collectors.toList());
        saveObjects(existObjects, newPos);
    }

    @Transactional(transactionManager = AD_TM, rollbackFor = Exception.class)
    public void saveObjectsWithObjectIds(Integer accountId, Long ruleId, Integer objectType, List<Integer> objectIds) {
        this.verifyUnit(accountId, ruleId, objectType, objectIds);
        final List<LauAutomaticRuleObjectPo> existObjects = listObjectsByRuleIdAndObjectIds(ruleId, objectType, objectIds);
        final List<LauAutomaticRuleObjectPo> newPos = objectIds
                .stream()
                .map(objectId -> ObjectConverter.MAPPER.bo2Po(accountId, ruleId, objectType, objectId))
                .collect(Collectors.toList());
        saveObjects(existObjects, newPos);
    }

    private void saveObjects(List<LauAutomaticRuleObjectPo> existObjects, List<LauAutomaticRuleObjectPo> newPos) {
        final RecDiffResult<LauAutomaticRuleObjectPo, Long> result = CommonFuncs.recDiff(existObjects, newPos, this::uk, LauAutomaticRuleObjectPo::getId, LauAutomaticRuleObjectPo::setId);
        CommonFuncs.handleRecDiff(result, adBqf, lauAutomaticRuleObject, lauAutomaticRuleObject.id::in);
    }

    @Transactional(transactionManager = AD_TM, rollbackFor = Exception.class)
    public void deleteObjects(Integer accountId, Long ruleId) {
        adBqf.update(lauAutomaticRuleObject)
                .set(lauAutomaticRuleObject.isDeleted, AutomaticRuleConstant.RULE_IS_DELETED)
                .where(lauAutomaticRuleObject.accountId.eq(accountId))
                .where(lauAutomaticRuleObject.ruleId.eq(ruleId))
                .execute();
    }
    private void verifyUnit(Integer accountId, Long ruleId, Integer objectType, List<Integer> objectIds) {
        if (!Objects.equals(ObjectType.UNIT.getCode(), objectType)) {
            return;
        }
        final List<LauAutomaticRuleObjectPo> objects = listObjectsByAccountId(accountId);
        final Map<Long, List<LauAutomaticRuleObjectPo>> ruleIdObjectsMap = objects
                .stream()
                .collect(Collectors.groupingBy(LauAutomaticRuleObjectPo::getRuleId));
        //先处理 0 的情况
        final List<Long> unlimitedObjectsRuleIds = ruleIdObjectsMap
                .entrySet()
                .stream()
                .filter(entry -> !Objects.equals(ruleId, entry.getKey()))
                .filter(entry -> entry
                        .getValue()
                        .stream()
                        .anyMatch(object -> Objects.equals(objectType, object.getObjectType())
                                && Objects.equals(AutomaticRuleConstant.UNLIMITED_OBJECTS, object.getObjectId())
                        )
                )
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
        Assert.isTrue(unlimitedObjectsRuleIds.isEmpty(), MessageFormat.format(AutomaticRuleErrorMessage.INVALID_UNIT_CONTAINS_UNLIMITED_RULE, unlimitedObjectsRuleIds.size(), unlimitedObjectsRuleIds));
        // 处理不是 0 的情况
        final Map<Integer, List<LauAutomaticRuleObjectPo>> objectIdRulesMap = objects
                .stream()
                .collect(Collectors.groupingBy(LauAutomaticRuleObjectPo::getObjectId));
        final List<Integer> ruleBoundUnitIds = objectIdRulesMap
                .entrySet()
                .stream()
                .filter(entry -> objectIds.contains(entry.getKey()))
                .filter(entry -> entry
                        .getValue()
                        .stream()
                        .noneMatch(object -> Objects.equals(ruleId, object.getRuleId()))
                )
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
        Assert.isTrue(ruleBoundUnitIds.isEmpty(), MessageFormat.format(AutomaticRuleErrorMessage.INVALID_UNIT_CONTAINS_RULE_BOUND_UNIT, ruleBoundUnitIds.size(), ruleBoundUnitIds));
        final List<LauUnitPo> unitPos = launchUnitV1Service.listUnits(objectIds);
        final List<Integer> noBidUnitIds = unitPos.stream()
                .filter(unit -> unit.getIsNoBid() == 1)
                .map(LauUnitPo::getUnitId)
                .collect(Collectors.toList());
        Assert.isTrue(noBidUnitIds.isEmpty(), MessageFormat.format(AutomaticRuleErrorMessage.INVALID_UNIT_CONTAINS_NO_BID_UNIT, noBidUnitIds.size(), noBidUnitIds));
    }

    private String uk(LauAutomaticRuleObjectPo po) {
        return po.getRuleId() + "-" + po.getObjectType() + "-" + po.getObjectId();
    }
}
