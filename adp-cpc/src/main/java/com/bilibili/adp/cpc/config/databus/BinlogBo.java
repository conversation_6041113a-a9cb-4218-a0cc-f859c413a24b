package com.bilibili.adp.cpc.config.databus;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 *
 * // 改
 * {
 *     "action": "update",
 *     "schema": "schema1",
 *     "table": "table1",
 *     "pk_names": "id",
 *     "old": {
 *         "ctime": "2021-09-26 11:05:48",
 *         "field1": "6",
 *         "field2": "6",
 *         "field3": "",
 *         "field4": "",
 *         "field5": "",
 *         "id": 20,
 *         "is_delete": 0,
 *         "mtime": "2021-09-26 11:12:52"
 *     },
 *     "new": {
 *         "ctime": "2021-09-26 11:05:48",
 *         "field1": "20",
 *         "field2": "20",
 *         "field3": "",
 *         "field4": "",
 *         "field5": "",
 *         "id": 20,
 *         "is_delete": 0,
 *         "mtime": "2021-10-11 11:10:26"
 *     },
 *     "msec": 1633950626630,
 *     "seq": 6853285649076716000,
 *     "canal_pos_str": "0-1-8505"
 * }
 *
 * <AUTHOR>
 * @date 2024/8/6
 */
@Data
public class BinlogBo {

    private String action;
    @JSONField(name = "old")
    private String oldDataJson;
    @JSONField(name = "new")
    private String newDataJson;

    public boolean isInsert(){
        return "insert".equals(action);
    }

    public boolean isUpdate(){
        return "update".equals(action);
    }

    public boolean isDelete(){return "delete".equals(action);}

    public <T> T parseOldDataJson(Class<T> clazz){
        return JSONObject.parseObject(oldDataJson, clazz);
    }

    public <T> T parseNewDataJson(Class<T> clazz){
        return JSONObject.parseObject(newDataJson, clazz);
    }
}
