package com.bilibili.adp.cpc.biz.services.account;


import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.biz.constants.Constants;
import com.bilibili.adp.cpc.biz.services.account.dto.LauGeneralAvidAuthDto;
import com.bilibili.adp.cpc.biz.services.account.dto.LauGeneralAvidMappingDto;
import com.bilibili.adp.cpc.biz.services.account.dto.LauGeneralAvidOperateRecordDto;
import com.bilibili.adp.cpc.biz.services.account.dto.LauGeneralAvidRequestDto;
import com.bilibili.adp.cpc.biz.services.account.vo.GeneralMidInfoVo;
import com.bilibili.adp.cpc.dao.querydsl.pos.LauGeneralAvidAuthPo;
import com.bilibili.adp.cpc.dao.querydsl.pos.LauGeneralAvidMappingPo;
import com.bilibili.adp.cpc.dao.querydsl.pos.LauGeneralAvidOperateRecordPo;
import com.bilibili.adp.cpc.dao.querydsl.pos.LauGeneralAvidRequestPo;
import com.bilibili.adp.cpc.enums.GeneralVideoAuthStatusEnum;
import com.bilibili.adp.cpc.enums.GeneralVideoEnum;
import com.bilibili.bjcom.querydsl.BaseQueryFactory;
import com.bilibili.bjcom.querydsl.clause.BaseQuery;
import com.bilibili.bjcom.querydsl.paging.Page;
import com.bilibili.bjcom.querydsl.paging.Pager;
import com.bilibili.crm.platform.api.account.dto.AccountBaseDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.bilibili.adp.cpc.dao.config.PlatformMySqlConfig.*;
import static com.bilibili.adp.cpc.dao.querydsl.QLauCustomerAgentCreativeMapping.lauCustomerAgentCreativeMapping;
import static com.bilibili.adp.cpc.dao.querydsl.QLauGeneralAvidAuth.lauGeneralAvidAuth;
import static com.bilibili.adp.cpc.dao.querydsl.QLauGeneralAvidMapping.lauGeneralAvidMapping;
import static com.bilibili.adp.cpc.dao.querydsl.QLauGeneralAvidOperateRecord.lauGeneralAvidOperateRecord;
import static com.bilibili.adp.cpc.dao.querydsl.QLauGeneralAvidRequest.lauGeneralAvidRequest;
import static com.bilibili.adp.cpc.dao.ad_core.querydsl.QLauUnitCreative.lauUnitCreative;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class GeneralVideoAuthDaoService {

    @Resource(name = AD_BQF)
    private BaseQueryFactory adBqf;

    @Resource(name = AD_CORE_BQF)
    private BaseQueryFactory adCoreBqf;

    @Autowired
    GeneralVideoAuthCommonService generalVideoAuthCommonService;


    @Transactional(value = AD_TM, rollbackFor = Exception.class) //todo
    public Integer authBind(LauGeneralAvidMappingDto avidMappingDto, LauGeneralAvidRequestDto avidRequestDto, GeneralVideoAuthService.VideoValid videoValid) throws Exception {

        Integer mappingId = avidMappingDto.getId();

        // 存在 mapping
        if (Utils.isPositive(mappingId)) {
            avidMappingUpdate(avidMappingDto);
        }
        // 不存在 mapping
        else {
            //插入(授权是代理维度，不是 accountId 维度)
            mappingId = avidMappingInsert(avidMappingDto);
        }

        //插入请求表 request
        avidRequestDto.setMappingId(mappingId);
        Integer requestId = avidRequestInsert(avidRequestDto).intValue();
        // 更新 mapping 表的 lastRequestId
        avidMappingUpdate(LauGeneralAvidMappingDto.builder().id(mappingId).lastRequestId(requestId).build());

        //插入记录表 record
        Integer avidOperateRecordId = avidOperateRecordInsert(mappingId, videoValid.getOperatorType(), videoValid.getAccountId()).intValue();

        //插入授权表 auth
        List<Integer> authIds = new ArrayList<>();

        // 该稿件的所有的作者发送站内信(一般只有一个，联合通过有多个)
        for (GeneralMidInfoVo midInfo : videoValid.getGeneralVideoInfoVo().getMidInfoVoList()) {
            // 查询每个 mid 与 mappingId 的关系(广告主的)
            Integer authId = avidAuthInsert(LauGeneralAvidAuthDto.builder().mappingId(mappingId).mid(midInfo.getMid()).face(midInfo.getFace())
                                .name(midInfo.getName()).midAuthStatus(GeneralVideoEnum.AuthEnum.WAIT_ACCEPT.getCode()).requestId(requestId).build()).intValue();
            generalVideoAuthCommonService.sendAuthMessage(videoValid.getAccountBaseDto(), authId, midInfo.getMid());
            authIds.add(authId);
        }

        log.info("authBind mappingId={},avidRequestId={},avidOperateRecordId={},authIds={}", mappingId, requestId, avidOperateRecordId, authIds);


        return mappingId;
    }

    @Transactional(value = AD_TM, rollbackFor = Exception.class) //todo
    public Integer cancelBind(Integer accountId, Integer mappingId) {


        //更新关系表 auth status=6 授权已撤回
        avidMappingUpdateAuthStatus(mappingId, GeneralVideoAuthStatusEnum.CANCEL.getCode());


        //授权表撤回-撤回之前所有申请(is_cacel = 0 的全部修改为 1)
        AvidAuthCancel(mappingId);


        //插入记录表
        Integer avidOperateRecordId = avidOperateRecordInsert(mappingId, GeneralVideoEnum.OperatorEnum.CANCEL.getCode(), accountId).intValue();

        // 不需要对 request 进行操作

        log.info("cancelBind mappingId={},avidOperateRecordId={}", mappingId, avidOperateRecordId);


        return mappingId;
    }

    @Transactional(value = AD_TM, rollbackFor = Exception.class) //todo
    public Integer renewalBind(LauGeneralAvidMappingDto avidMappingDto,LauGeneralAvidRequestDto avidRequestDto, GeneralVideoAuthService.VideoValid videoValid) throws Exception {


        Integer mappingId = avidMappingDto.getId();

        if(Utils.isPositive(mappingId)){


            //更新
            avidMappingUpdate(avidMappingDto);


            //插入请求表
            Integer requestId = avidRequestInsert(avidRequestDto).intValue();
            //更新lastRequestId
            avidMappingUpdate(LauGeneralAvidMappingDto.builder().id(mappingId).renewalStatus(avidMappingDto.getRenewalStatus()).lastRequestId(requestId).build());


            //插入记录表
            Integer avidOperateRecordId = avidOperateRecordInsert(mappingId,videoValid.getOperatorType(),videoValid.getAccountId()).intValue();

            //插入授权表
            List<Integer> authIds = new ArrayList<>();

            for (GeneralMidInfoVo midInfo : videoValid.getGeneralVideoInfoVo().getMidInfoVoList()) {

                Integer authId = avidAuthInsert(LauGeneralAvidAuthDto.builder().mappingId(mappingId).mid(midInfo.getMid()).face(midInfo.getFace())
                        .name(midInfo.getName()).midAuthStatus(GeneralVideoEnum.AuthEnum.WAIT_ACCEPT.getCode()).requestId(requestId).build()).intValue();

                generalVideoAuthCommonService.sendRenewalMessage(videoValid.getAccountBaseDto(), authId, midInfo.getMid());
                authIds.add(authId);

            }

            log.info("renewalBind mappingId={},avidRequestId={},avidOperateRecordId={},authIds={}", mappingId, requestId, avidOperateRecordId, authIds);


        }



        return mappingId;
    }

    @Transactional(value = AD_TM, rollbackFor = Exception.class)
    public void acceptUpdate(Integer authId,Integer mappingId,Integer currentAuthStatus,Timestamp acceptExpireTime, Integer authTimeType,Integer authStatus){

        // 授权表-接受
        AvidAuthUpdateById(authId, GeneralVideoEnum.AuthEnum.ACCEPTED.getCode(),Utils.getNow());

        //关系状态表
        if(Objects.nonNull(acceptExpireTime)){
            if(GeneralVideoAuthStatusEnum.RENEWAL_CODE_SET.contains(currentAuthStatus)){

                //更新状态为续约成功
                avidMappingUpdate(LauGeneralAvidMappingDto.builder().id(mappingId)
                        .authTimeType(authTimeType)
                        .authExpireTime(acceptExpireTime)
                        .renewalStatus(GeneralVideoEnum.RenewalEnum.CONFIRM.getCode()).build());

            }else{

                //更新状态为授权中或授权待生效
                avidMappingUpdate(LauGeneralAvidMappingDto.builder().id(mappingId)
                        .authTimeType(authTimeType)
                        .authExpireTime(acceptExpireTime)
                        .authStatus(authStatus).build());

            }
        }



    }

    @Transactional(value = AD_TM, rollbackFor = Exception.class)
    public void rejectUpdate(Integer authId,Integer mappingId,Integer currentAuthStatus){

        // 更新 auth 为拒绝
        AvidAuthUpdateById(authId, GeneralVideoEnum.AuthEnum.REJECTED.getCode(),Utils.getNow());

        if(GeneralVideoAuthStatusEnum.RENEWAL_CODE_SET.contains(currentAuthStatus)){

            //更新状态为续约拒绝
            avidMappingUpdate(LauGeneralAvidMappingDto.builder().id(mappingId).renewalStatus(GeneralVideoEnum.RenewalEnum.REJECTED.getCode()).build());

        }else{

            //更新状态为已拒绝
            avidMappingUpdate(LauGeneralAvidMappingDto.builder().id(mappingId).authStatus(GeneralVideoAuthStatusEnum.REJECT.getCode()).build());

        }
    }

    @Transactional(value = AD_TM, rollbackFor = Exception.class)
    public void rejectUpdateForRefresh(Integer authId,Integer mappingId,Integer currentAuthStatus, LauGeneralAvidRequestPo avidRequestInfo){

        // 更新 auth 为拒绝
        AvidAuthUpdateById(authId, GeneralVideoEnum.AuthEnum.REJECTED.getCode(),Utils.getNow());

        if (GeneralVideoEnum.RequestEnum.RENEWAL.getCode().equals(avidRequestInfo.getRequestType())) {
            //更新状态为续约拒绝
            avidMappingUpdate(LauGeneralAvidMappingDto.builder().id(mappingId).authStatus(GeneralVideoAuthStatusEnum.REJECT.getCode()).renewalStatus(GeneralVideoEnum.RenewalEnum.REJECTED.getCode()).build());
        }else{

            //更新状态为已拒绝
            avidMappingUpdate(LauGeneralAvidMappingDto.builder().id(mappingId).authStatus(GeneralVideoAuthStatusEnum.REJECT.getCode()).build());

        }
    }

    //最新一条申请为主
    public LauGeneralAvidRequestPo getAvidRequestById(long requestId) {
        return adBqf.selectFrom(lauGeneralAvidRequest)
                .where(lauGeneralAvidRequest.id.eq(requestId))
                .fetchFirst();
    }


    public List<LauGeneralAvidMappingDto> getAvidMappingListByStatus(List<Integer> authStatus,Integer lastMaxId,Integer batchSize){
         return adBqf.selectFrom(lauGeneralAvidMapping)
                .where(lauGeneralAvidMapping.authStatus.in(authStatus))
                .where(lauGeneralAvidMapping.isDeleted.eq(Constants.INT_FALSE))
                .where(lauGeneralAvidMapping.id.goe(lastMaxId))
                .limit(batchSize)
                .orderBy(lauGeneralAvidMapping.id.asc())
                .fetch(LauGeneralAvidMappingDto.class);
    }

    public void updateAvidMappingById(Integer mappingId,Integer authStatus){
        avidMappingUpdate(LauGeneralAvidMappingDto.builder().id(mappingId).authStatus(authStatus).build());
    }



    public List<Integer> getCreativeIds(Integer customerId, Integer agentId, Long avid) {

       return adBqf.from(lauCustomerAgentCreativeMapping)
                .where(lauCustomerAgentCreativeMapping.customerId.eq(customerId))
                .where(lauCustomerAgentCreativeMapping.agentId.eq(agentId))
                .where(lauCustomerAgentCreativeMapping.avid.eq(avid))
                .where(lauCustomerAgentCreativeMapping.creativeAvidSource.eq(Constants.GENERAL_VIDEO_BIND))
                .where(lauCustomerAgentCreativeMapping.isDeleted.eq(Constants.INT_FALSE))
                .select(lauCustomerAgentCreativeMapping.creativeId)
                .fetch();

    }

    public Integer getCreativeStatus(Integer creativeId){
        return adCoreBqf.from(lauUnitCreative)
                .where(lauUnitCreative.creativeId.eq(creativeId))
                .where(lauUnitCreative.isDeleted.eq(Constants.INT_FALSE))
                .select(lauUnitCreative.creativeStatus)
                .fetchFirst();

    }
    @Transactional(value = AD_CORE_TM, rollbackFor = Exception.class)
    public void updateCreativeStatus(Integer creativeId,Integer creativeStatus,String rejectReason) {
        adCoreBqf.update(lauUnitCreative)
                .where(lauUnitCreative.creativeId.eq(creativeId))
                .setIfNotNull(lauUnitCreative.creativeStatus,creativeStatus)
//                .set(lauUnitCreative.creativeStatus, CreativeStatus.AUDIT_REJECT.getCode())
                .set(lauUnitCreative.reason, rejectReason)
                .execute();
    }

    public Page<LauGeneralAvidMappingDto> getAvidMappingList(AccountBaseDto accountBaseDto,
                                                             Long avid,
                                                             Integer authStatus,
                                                             Integer authMode,
                                                             Integer pageNo,
                                                             Integer pageSize){
        return avidMappingPoBaseQuery(accountBaseDto.getCustomerId(), accountBaseDto.getDependencyAgentId(), avid, authStatus, authMode,null)
                .orderBy(lauGeneralAvidMapping.mtime.desc())
                .fetchPage(Pager.of(pageNo, pageSize), LauGeneralAvidMappingDto.class);
    }

    public Page<LauGeneralAvidMappingDto> getAvidMappingList(AccountBaseDto accountBaseDto, Integer authStatus, Integer authMode,Integer pageNo, Integer pageSize){
        return avidMappingPoBaseQuery(accountBaseDto.getCustomerId(), accountBaseDto.getDependencyAgentId(), null, authStatus, authMode,null)
                .orderBy(lauGeneralAvidMapping.mtime.desc())
                .fetchPage(Pager.of(pageNo, pageSize), LauGeneralAvidMappingDto.class);
    }

    public List<LauGeneralAvidMappingDto> getAvidMappingList(AccountBaseDto accountBaseDto, Long avid, Integer authStatus, Integer authMode){
        return avidMappingPoBaseQuery(accountBaseDto.getCustomerId(), accountBaseDto.getDependencyAgentId(), avid, authStatus, authMode,null)
                .orderBy(lauGeneralAvidMapping.mtime.desc())
                .fetch(LauGeneralAvidMappingDto.class);
    }

    public List<LauGeneralAvidMappingDto> getAvidMappingList(AccountBaseDto accountBaseDto, Integer authStatus, Integer authMode){
        return avidMappingPoBaseQuery(accountBaseDto.getCustomerId(), accountBaseDto.getDependencyAgentId(), null, authStatus, authMode,null)
                .orderBy(lauGeneralAvidMapping.mtime.desc())
                .fetch(LauGeneralAvidMappingDto.class);
    }

    public List<LauGeneralAvidMappingDto> getAvidMappingListOrderBy(AccountBaseDto accountBaseDto, Integer authStatus, Integer authMode, String orderBy){
        return avidMappingPoBaseQuery(accountBaseDto.getCustomerId(), accountBaseDto.getDependencyAgentId(), null, authStatus, authMode,null)
                .orderBy(orderBy)
                .fetch(LauGeneralAvidMappingDto.class);
    }

    public LauGeneralAvidAuthDto getAuthInfoById(Long authId, Long mid){
        return avidAuthPoBaseQuery(authId, null, mid, null).fetchFirst(LauGeneralAvidAuthDto.class);
    }


    public List<LauGeneralAvidMappingDto> getAvidMappingByStatus(AccountBaseDto accountBaseDto,List<Integer> authStatus,Integer authTimeType){
        return getAvidMappingByStatus(accountBaseDto.getCustomerId(), accountBaseDto.getDependencyAgentId(), authStatus, authTimeType,null);

    }

    public List<LauGeneralAvidMappingDto> getAvidMappingByAuthModel(AccountBaseDto accountBaseDto,List<Integer> authStatus,Integer authModel){
        return getAvidMappingByStatus(accountBaseDto.getCustomerId(), accountBaseDto.getDependencyAgentId(), authStatus, null,authModel);

    }

    public long getAvidRecordCountByMapping(List<Integer> mappingIds, Integer operatorType,Timestamp today){
        return adBqf.selectFrom(lauGeneralAvidOperateRecord)
                .where(lauGeneralAvidOperateRecord.mappingId.in(mappingIds))
                .whereIfNotNull(operatorType,t->lauGeneralAvidOperateRecord.operatorType.eq(operatorType))
                .whereIfNotNull(today,t->lauGeneralAvidOperateRecord.ctime.goe(today))
                .where(lauGeneralAvidOperateRecord.isDeleted.eq(Constants.INT_FALSE))
                .fetchCount();

    }

    public Integer insertAvidRecordByMapping(List<Integer> mappingIds, Integer operatorType,Integer accountId){
        Integer avidOperateRecordId = null;
        //插入记录表
        for(Integer mappingId: mappingIds){
            avidOperateRecordId = avidOperateRecordInsert(mappingId,operatorType,accountId).intValue();
            if(Objects.nonNull(avidOperateRecordId)){
                break;
            }
        }

        return avidOperateRecordId;
    }

    public List<LauGeneralAvidMappingDto> getAvidMappingByStatus(Integer customerId,Integer agentId, List<Integer> authStatus,Integer authTimeType,Integer authModel){
       return adBqf.selectFrom(lauGeneralAvidMapping)
                .where(lauGeneralAvidMapping.customerId.eq(customerId))
                .where(lauGeneralAvidMapping.agentId.eq(agentId))
                .where(lauGeneralAvidMapping.authStatus.in(authStatus))
                .whereIfNotNull(authTimeType, t -> lauGeneralAvidMapping.authTimeType.eq(authTimeType))
                .whereIfNotNull(authModel, t -> lauGeneralAvidMapping.authMode.eq(authModel))
                .where(lauGeneralAvidMapping.isDeleted.eq(Constants.INT_FALSE))
        .fetch(LauGeneralAvidMappingDto.class);
    }

    public List<LauGeneralAvidMappingPo> getAvidMappings(Integer gtId){
        return adBqf.selectFrom(lauGeneralAvidMapping)
                .where(lauGeneralAvidMapping.id.gt(gtId))
                .where(lauGeneralAvidMapping.pubTime.isNull())
                .where(lauGeneralAvidMapping.isDeleted.eq(0))
                .limit(100)
                .fetch(LauGeneralAvidMappingPo.class);
    }


    public void updatePubTime(Integer id, Timestamp pubTime){
        adBqf.update(lauGeneralAvidMapping)
                .where(lauGeneralAvidMapping.id.eq(id))
                .set(lauGeneralAvidMapping.pubTime, pubTime)
                .execute();
    }


    public void AvidAuthCancel(Integer mappingId){
        adBqf.update(lauGeneralAvidAuth)
                .where(lauGeneralAvidAuth.requestId.in(mappingId))
                .where(lauGeneralAvidAuth.isDeleted.eq(IsDeleted.VALID.getCode()))
                .where(lauGeneralAvidAuth.isCancel.eq(0))
                .set(lauGeneralAvidAuth.isCancel,1)
                .execute();
    }

    public void AvidAuthUpdateById(Integer authId, Integer midAuthStatus, Timestamp midAuthTime){
        adBqf.update(lauGeneralAvidAuth)
                .where(lauGeneralAvidAuth.id.in(authId))
                .where(lauGeneralAvidAuth.isDeleted.eq(IsDeleted.VALID.getCode()))
                .where(lauGeneralAvidAuth.isCancel.eq(0))
                .set(lauGeneralAvidAuth.midAuthStatus,midAuthStatus)
                .set(lauGeneralAvidAuth.midAuthTime,midAuthTime)
                .execute();
    }

    public List<LauGeneralAvidAuthPo> AvidAuthList(List<Integer> mappingIdList){
        return adBqf.selectFrom(lauGeneralAvidAuth)
                .where(lauGeneralAvidAuth.requestId.in(mappingIdList))
                .where(lauGeneralAvidAuth.isDeleted.eq(Constants.INT_FALSE))
                .fetch();
    }

    public LauGeneralAvidMappingDto getBindInfo(AccountBaseDto accountBaseDto, Long avid){
        return avidMappingPoBaseQuery(accountBaseDto.getCustomerId(), accountBaseDto.getDependencyAgentId(), avid, null, null,null)
                .orderBy(lauGeneralAvidMapping.mtime.desc())
                .fetchFirst(LauGeneralAvidMappingDto.class);
    }

    public LauGeneralAvidMappingDto getBindInfoById(Integer id){
        return adBqf.selectFrom(lauGeneralAvidMapping)
                .whereEqKey(id)
                .where(lauGeneralAvidMapping.isDeleted.eq(IsDeleted.VALID.getCode()))
                .fetchFirst(LauGeneralAvidMappingDto.class);
    }


    public void avidMappingUpdate(LauGeneralAvidMappingDto dto) {
        adBqf.update(lauGeneralAvidMapping)
                .where(lauGeneralAvidMapping.id.eq(dto.getId()))
                .where(lauGeneralAvidMapping.isDeleted.eq(IsDeleted.VALID.getCode()))
                .setIfNotNull(lauGeneralAvidMapping.avid, dto.getAvid())
                .setIfNotNull(lauGeneralAvidMapping.customerId, dto.getCustomerId())
                .setIfNotNull(lauGeneralAvidMapping.agentId, dto.getAgentId())
                .setIfNotNull(lauGeneralAvidMapping.authEffectiveTime, dto.getAuthEffectiveTime())
                .setIfNotNull(lauGeneralAvidMapping.authExpireTime, dto.getAuthExpireTime())
                .setIfNotNull(lauGeneralAvidMapping.authStatus, dto.getAuthStatus())
                .setIfNotNull(lauGeneralAvidMapping.authMode, dto.getAuthMode())
                .setIfNotNull(lauGeneralAvidMapping.authTimeType, dto.getAuthTimeType())
                .setIfNotNull(lauGeneralAvidMapping.isCooperation, dto.getIsCooperation())
                .setIfNotNull(lauGeneralAvidMapping.renewalStatus,dto.getRenewalStatus())
                .setIfNotNull(lauGeneralAvidMapping.lastRequestId,dto.getLastRequestId())
                // todo 版本号更新
                .execute();

    }

    private void avidMappingUpdateAuthStatus(Integer id, Integer authStatus) {
        adBqf.update(lauGeneralAvidMapping)
                .where(lauGeneralAvidMapping.id.eq(id))
                .where(lauGeneralAvidMapping.isDeleted.eq(IsDeleted.VALID.getCode()))
                .set(lauGeneralAvidMapping.authStatus, authStatus)
                .execute();

    }

    public Integer avidMappingInsert(LauGeneralAvidMappingDto dto) {
        return adBqf.insert(lauGeneralAvidMapping).insertGetKey(dto);
    }

    public Long avidRequestInsert(LauGeneralAvidRequestDto dto) {
        return adBqf.insert(lauGeneralAvidRequest).insertGetKey(dto);
    }

    public Long avidOperateRecordInsert(Integer mappingId, Integer operatorType, Integer accountId) {
        return adBqf.insert(lauGeneralAvidOperateRecord).insertGetKey(LauGeneralAvidOperateRecordDto.builder()
                .mappingId(mappingId)
                .operatorType(operatorType)
                .accountId(accountId).build());
    }

    public Long avidAuthInsert(LauGeneralAvidAuthDto avidAuthDto) {
        return adBqf.insert(lauGeneralAvidAuth).insertGetKey(avidAuthDto);
    }



    private BaseQuery<LauGeneralAvidMappingPo> avidMappingPoBaseQuery(Integer customerId, Integer agentId, Long avid, Integer authStatus,Integer authMode, Timestamp expireTime) {
        return adBqf.selectFrom(lauGeneralAvidMapping)
                .whereIfNotNull(customerId, t -> lauGeneralAvidMapping.customerId.eq(customerId))
                .whereIfNotNull(agentId, t -> lauGeneralAvidMapping.agentId.eq(agentId))
                .whereIfNotNull(avid, t -> lauGeneralAvidMapping.avid.eq(avid))
                .whereIfNotNull(authStatus, t -> lauGeneralAvidMapping.authStatus.eq(authStatus))
                .whereIfNotNull(authMode,t->lauGeneralAvidMapping.authMode.eq(authMode))
                .whereIfNotNull(expireTime, t -> lauGeneralAvidMapping.authExpireTime.goe(expireTime))
                .where(lauGeneralAvidMapping.isDeleted.eq(Constants.INT_FALSE));
    }

    private BaseQuery<LauGeneralAvidRequestPo> avidRequestPoBaseQuery(Long id, Integer mappingId, Long avid, Integer requestType) {
        return adBqf.selectFrom(lauGeneralAvidRequest)
                .whereIfNotNull(id, t -> lauGeneralAvidRequest.id.eq(id))
                .whereIfNotNull(mappingId, t -> lauGeneralAvidRequest.mappingId.eq(mappingId))
                .whereIfNotNull(requestType, t -> lauGeneralAvidRequest.requestType.eq(requestType))
                .where(lauGeneralAvidRequest.isDeleted.eq(Constants.INT_FALSE));
    }

    public BaseQuery<LauGeneralAvidAuthPo> avidAuthPoBaseQuery(Long id, Integer mappingId, Long mid, Integer midAuthStatus) {
        return adBqf.selectFrom(lauGeneralAvidAuth)
                .whereIfNotNull(id, t -> lauGeneralAvidAuth.id.eq(id))
                .whereIfNotNull(mappingId, t -> lauGeneralAvidAuth.mappingId.eq(mappingId))
                .whereIfNotNull(mid, t -> lauGeneralAvidAuth.mid.eq(mid))
                .whereIfNotNull(midAuthStatus, t -> lauGeneralAvidAuth.midAuthStatus.eq(midAuthStatus))
                .where(lauGeneralAvidAuth.isDeleted.eq(Constants.INT_FALSE));
    }

    private BaseQuery<LauGeneralAvidOperateRecordPo> avidOperateRecordPoBaseQuery(Integer accountId, Long id, Integer mappingId, Integer operatorType) {
        return adBqf.selectFrom(lauGeneralAvidOperateRecord)
                .whereIfNotNull(accountId, t -> lauGeneralAvidOperateRecord.accountId.eq(accountId))
                .whereIfNotNull(id, t -> lauGeneralAvidOperateRecord.id.eq(id))
                .whereIfNotNull(mappingId, t -> lauGeneralAvidOperateRecord.mappingId.eq(mappingId))
                .whereIfNotNull(operatorType, t -> lauGeneralAvidOperateRecord.operatorType.eq(operatorType))
                .where(lauGeneralAvidOperateRecord.isDeleted.eq(Constants.INT_FALSE));
    }


    public long getRequestTimesByWeek(Integer mappingId, Integer requestType) {
        Timestamp beginTime = Utils.getSomeDayBeforeToday(7);
        return adBqf.selectFrom(lauGeneralAvidRequest)
                .where(lauGeneralAvidRequest.mappingId.eq(mappingId))
                .where(lauGeneralAvidRequest.ctime.goe(beginTime))
                .where(lauGeneralAvidRequest.requestType.eq(requestType))
                .where(lauGeneralAvidRequest.isDeleted.eq(Constants.INT_FALSE))
                .fetchCount();
    }
}