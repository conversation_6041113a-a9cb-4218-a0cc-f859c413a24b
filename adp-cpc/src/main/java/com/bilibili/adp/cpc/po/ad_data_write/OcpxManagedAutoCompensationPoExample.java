package com.bilibili.adp.cpc.po.ad_data_write;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public class OcpxManagedAutoCompensationPoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public OcpxManagedAutoCompensationPoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAccountIdIsNull() {
            addCriterion("account_id is null");
            return (Criteria) this;
        }

        public Criteria andAccountIdIsNotNull() {
            addCriterion("account_id is not null");
            return (Criteria) this;
        }

        public Criteria andAccountIdEqualTo(Integer value) {
            addCriterion("account_id =", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdNotEqualTo(Integer value) {
            addCriterion("account_id <>", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdGreaterThan(Integer value) {
            addCriterion("account_id >", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("account_id >=", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdLessThan(Integer value) {
            addCriterion("account_id <", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdLessThanOrEqualTo(Integer value) {
            addCriterion("account_id <=", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdIn(List<Integer> values) {
            addCriterion("account_id in", values, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdNotIn(List<Integer> values) {
            addCriterion("account_id not in", values, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdBetween(Integer value1, Integer value2) {
            addCriterion("account_id between", value1, value2, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdNotBetween(Integer value1, Integer value2) {
            addCriterion("account_id not between", value1, value2, "accountId");
            return (Criteria) this;
        }

        public Criteria andCampaignIdIsNull() {
            addCriterion("campaign_id is null");
            return (Criteria) this;
        }

        public Criteria andCampaignIdIsNotNull() {
            addCriterion("campaign_id is not null");
            return (Criteria) this;
        }

        public Criteria andCampaignIdEqualTo(Integer value) {
            addCriterion("campaign_id =", value, "campaignId");
            return (Criteria) this;
        }

        public Criteria andCampaignIdNotEqualTo(Integer value) {
            addCriterion("campaign_id <>", value, "campaignId");
            return (Criteria) this;
        }

        public Criteria andCampaignIdGreaterThan(Integer value) {
            addCriterion("campaign_id >", value, "campaignId");
            return (Criteria) this;
        }

        public Criteria andCampaignIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("campaign_id >=", value, "campaignId");
            return (Criteria) this;
        }

        public Criteria andCampaignIdLessThan(Integer value) {
            addCriterion("campaign_id <", value, "campaignId");
            return (Criteria) this;
        }

        public Criteria andCampaignIdLessThanOrEqualTo(Integer value) {
            addCriterion("campaign_id <=", value, "campaignId");
            return (Criteria) this;
        }

        public Criteria andCampaignIdIn(List<Integer> values) {
            addCriterion("campaign_id in", values, "campaignId");
            return (Criteria) this;
        }

        public Criteria andCampaignIdNotIn(List<Integer> values) {
            addCriterion("campaign_id not in", values, "campaignId");
            return (Criteria) this;
        }

        public Criteria andCampaignIdBetween(Integer value1, Integer value2) {
            addCriterion("campaign_id between", value1, value2, "campaignId");
            return (Criteria) this;
        }

        public Criteria andCampaignIdNotBetween(Integer value1, Integer value2) {
            addCriterion("campaign_id not between", value1, value2, "campaignId");
            return (Criteria) this;
        }

        public Criteria andPeriodIsNull() {
            addCriterion("period is null");
            return (Criteria) this;
        }

        public Criteria andPeriodIsNotNull() {
            addCriterion("period is not null");
            return (Criteria) this;
        }

        public Criteria andPeriodEqualTo(Integer value) {
            addCriterion("period =", value, "period");
            return (Criteria) this;
        }

        public Criteria andPeriodNotEqualTo(Integer value) {
            addCriterion("period <>", value, "period");
            return (Criteria) this;
        }

        public Criteria andPeriodGreaterThan(Integer value) {
            addCriterion("period >", value, "period");
            return (Criteria) this;
        }

        public Criteria andPeriodGreaterThanOrEqualTo(Integer value) {
            addCriterion("period >=", value, "period");
            return (Criteria) this;
        }

        public Criteria andPeriodLessThan(Integer value) {
            addCriterion("period <", value, "period");
            return (Criteria) this;
        }

        public Criteria andPeriodLessThanOrEqualTo(Integer value) {
            addCriterion("period <=", value, "period");
            return (Criteria) this;
        }

        public Criteria andPeriodIn(List<Integer> values) {
            addCriterion("period in", values, "period");
            return (Criteria) this;
        }

        public Criteria andPeriodNotIn(List<Integer> values) {
            addCriterion("period not in", values, "period");
            return (Criteria) this;
        }

        public Criteria andPeriodBetween(Integer value1, Integer value2) {
            addCriterion("period between", value1, value2, "period");
            return (Criteria) this;
        }

        public Criteria andPeriodNotBetween(Integer value1, Integer value2) {
            addCriterion("period not between", value1, value2, "period");
            return (Criteria) this;
        }

        public Criteria andConvNumIsNull() {
            addCriterion("conv_num is null");
            return (Criteria) this;
        }

        public Criteria andConvNumIsNotNull() {
            addCriterion("conv_num is not null");
            return (Criteria) this;
        }

        public Criteria andConvNumEqualTo(Integer value) {
            addCriterion("conv_num =", value, "convNum");
            return (Criteria) this;
        }

        public Criteria andConvNumNotEqualTo(Integer value) {
            addCriterion("conv_num <>", value, "convNum");
            return (Criteria) this;
        }

        public Criteria andConvNumGreaterThan(Integer value) {
            addCriterion("conv_num >", value, "convNum");
            return (Criteria) this;
        }

        public Criteria andConvNumGreaterThanOrEqualTo(Integer value) {
            addCriterion("conv_num >=", value, "convNum");
            return (Criteria) this;
        }

        public Criteria andConvNumLessThan(Integer value) {
            addCriterion("conv_num <", value, "convNum");
            return (Criteria) this;
        }

        public Criteria andConvNumLessThanOrEqualTo(Integer value) {
            addCriterion("conv_num <=", value, "convNum");
            return (Criteria) this;
        }

        public Criteria andConvNumIn(List<Integer> values) {
            addCriterion("conv_num in", values, "convNum");
            return (Criteria) this;
        }

        public Criteria andConvNumNotIn(List<Integer> values) {
            addCriterion("conv_num not in", values, "convNum");
            return (Criteria) this;
        }

        public Criteria andConvNumBetween(Integer value1, Integer value2) {
            addCriterion("conv_num between", value1, value2, "convNum");
            return (Criteria) this;
        }

        public Criteria andConvNumNotBetween(Integer value1, Integer value2) {
            addCriterion("conv_num not between", value1, value2, "convNum");
            return (Criteria) this;
        }

        public Criteria andIsConfidenceIsNull() {
            addCriterion("is_confidence is null");
            return (Criteria) this;
        }

        public Criteria andIsConfidenceIsNotNull() {
            addCriterion("is_confidence is not null");
            return (Criteria) this;
        }

        public Criteria andIsConfidenceEqualTo(Integer value) {
            addCriterion("is_confidence =", value, "isConfidence");
            return (Criteria) this;
        }

        public Criteria andIsConfidenceNotEqualTo(Integer value) {
            addCriterion("is_confidence <>", value, "isConfidence");
            return (Criteria) this;
        }

        public Criteria andIsConfidenceGreaterThan(Integer value) {
            addCriterion("is_confidence >", value, "isConfidence");
            return (Criteria) this;
        }

        public Criteria andIsConfidenceGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_confidence >=", value, "isConfidence");
            return (Criteria) this;
        }

        public Criteria andIsConfidenceLessThan(Integer value) {
            addCriterion("is_confidence <", value, "isConfidence");
            return (Criteria) this;
        }

        public Criteria andIsConfidenceLessThanOrEqualTo(Integer value) {
            addCriterion("is_confidence <=", value, "isConfidence");
            return (Criteria) this;
        }

        public Criteria andIsConfidenceIn(List<Integer> values) {
            addCriterion("is_confidence in", values, "isConfidence");
            return (Criteria) this;
        }

        public Criteria andIsConfidenceNotIn(List<Integer> values) {
            addCriterion("is_confidence not in", values, "isConfidence");
            return (Criteria) this;
        }

        public Criteria andIsConfidenceBetween(Integer value1, Integer value2) {
            addCriterion("is_confidence between", value1, value2, "isConfidence");
            return (Criteria) this;
        }

        public Criteria andIsConfidenceNotBetween(Integer value1, Integer value2) {
            addCriterion("is_confidence not between", value1, value2, "isConfidence");
            return (Criteria) this;
        }

        public Criteria andIsViolatedIsNull() {
            addCriterion("is_violated is null");
            return (Criteria) this;
        }

        public Criteria andIsViolatedIsNotNull() {
            addCriterion("is_violated is not null");
            return (Criteria) this;
        }

        public Criteria andIsViolatedEqualTo(Integer value) {
            addCriterion("is_violated =", value, "isViolated");
            return (Criteria) this;
        }

        public Criteria andIsViolatedNotEqualTo(Integer value) {
            addCriterion("is_violated <>", value, "isViolated");
            return (Criteria) this;
        }

        public Criteria andIsViolatedGreaterThan(Integer value) {
            addCriterion("is_violated >", value, "isViolated");
            return (Criteria) this;
        }

        public Criteria andIsViolatedGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_violated >=", value, "isViolated");
            return (Criteria) this;
        }

        public Criteria andIsViolatedLessThan(Integer value) {
            addCriterion("is_violated <", value, "isViolated");
            return (Criteria) this;
        }

        public Criteria andIsViolatedLessThanOrEqualTo(Integer value) {
            addCriterion("is_violated <=", value, "isViolated");
            return (Criteria) this;
        }

        public Criteria andIsViolatedIn(List<Integer> values) {
            addCriterion("is_violated in", values, "isViolated");
            return (Criteria) this;
        }

        public Criteria andIsViolatedNotIn(List<Integer> values) {
            addCriterion("is_violated not in", values, "isViolated");
            return (Criteria) this;
        }

        public Criteria andIsViolatedBetween(Integer value1, Integer value2) {
            addCriterion("is_violated between", value1, value2, "isViolated");
            return (Criteria) this;
        }

        public Criteria andIsViolatedNotBetween(Integer value1, Integer value2) {
            addCriterion("is_violated not between", value1, value2, "isViolated");
            return (Criteria) this;
        }

        public Criteria andIsSentIsNull() {
            addCriterion("is_sent is null");
            return (Criteria) this;
        }

        public Criteria andIsSentIsNotNull() {
            addCriterion("is_sent is not null");
            return (Criteria) this;
        }

        public Criteria andIsSentEqualTo(Integer value) {
            addCriterion("is_sent =", value, "isSent");
            return (Criteria) this;
        }

        public Criteria andIsSentNotEqualTo(Integer value) {
            addCriterion("is_sent <>", value, "isSent");
            return (Criteria) this;
        }

        public Criteria andIsSentGreaterThan(Integer value) {
            addCriterion("is_sent >", value, "isSent");
            return (Criteria) this;
        }

        public Criteria andIsSentGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_sent >=", value, "isSent");
            return (Criteria) this;
        }

        public Criteria andIsSentLessThan(Integer value) {
            addCriterion("is_sent <", value, "isSent");
            return (Criteria) this;
        }

        public Criteria andIsSentLessThanOrEqualTo(Integer value) {
            addCriterion("is_sent <=", value, "isSent");
            return (Criteria) this;
        }

        public Criteria andIsSentIn(List<Integer> values) {
            addCriterion("is_sent in", values, "isSent");
            return (Criteria) this;
        }

        public Criteria andIsSentNotIn(List<Integer> values) {
            addCriterion("is_sent not in", values, "isSent");
            return (Criteria) this;
        }

        public Criteria andIsSentBetween(Integer value1, Integer value2) {
            addCriterion("is_sent between", value1, value2, "isSent");
            return (Criteria) this;
        }

        public Criteria andIsSentNotBetween(Integer value1, Integer value2) {
            addCriterion("is_sent not between", value1, value2, "isSent");
            return (Criteria) this;
        }

        public Criteria andCompensationIsNull() {
            addCriterion("compensation is null");
            return (Criteria) this;
        }

        public Criteria andCompensationIsNotNull() {
            addCriterion("compensation is not null");
            return (Criteria) this;
        }

        public Criteria andCompensationEqualTo(Integer value) {
            addCriterion("compensation =", value, "compensation");
            return (Criteria) this;
        }

        public Criteria andCompensationNotEqualTo(Integer value) {
            addCriterion("compensation <>", value, "compensation");
            return (Criteria) this;
        }

        public Criteria andCompensationGreaterThan(Integer value) {
            addCriterion("compensation >", value, "compensation");
            return (Criteria) this;
        }

        public Criteria andCompensationGreaterThanOrEqualTo(Integer value) {
            addCriterion("compensation >=", value, "compensation");
            return (Criteria) this;
        }

        public Criteria andCompensationLessThan(Integer value) {
            addCriterion("compensation <", value, "compensation");
            return (Criteria) this;
        }

        public Criteria andCompensationLessThanOrEqualTo(Integer value) {
            addCriterion("compensation <=", value, "compensation");
            return (Criteria) this;
        }

        public Criteria andCompensationIn(List<Integer> values) {
            addCriterion("compensation in", values, "compensation");
            return (Criteria) this;
        }

        public Criteria andCompensationNotIn(List<Integer> values) {
            addCriterion("compensation not in", values, "compensation");
            return (Criteria) this;
        }

        public Criteria andCompensationBetween(Integer value1, Integer value2) {
            addCriterion("compensation between", value1, value2, "compensation");
            return (Criteria) this;
        }

        public Criteria andCompensationNotBetween(Integer value1, Integer value2) {
            addCriterion("compensation not between", value1, value2, "compensation");
            return (Criteria) this;
        }

        public Criteria andLogDateIsNull() {
            addCriterion("log_date is null");
            return (Criteria) this;
        }

        public Criteria andLogDateIsNotNull() {
            addCriterion("log_date is not null");
            return (Criteria) this;
        }

        public Criteria andLogDateEqualTo(Timestamp value) {
            addCriterion("log_date =", value, "logDate");
            return (Criteria) this;
        }

        public Criteria andLogDateNotEqualTo(Timestamp value) {
            addCriterion("log_date <>", value, "logDate");
            return (Criteria) this;
        }

        public Criteria andLogDateGreaterThan(Timestamp value) {
            addCriterion("log_date >", value, "logDate");
            return (Criteria) this;
        }

        public Criteria andLogDateGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("log_date >=", value, "logDate");
            return (Criteria) this;
        }

        public Criteria andLogDateLessThan(Timestamp value) {
            addCriterion("log_date <", value, "logDate");
            return (Criteria) this;
        }

        public Criteria andLogDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("log_date <=", value, "logDate");
            return (Criteria) this;
        }

        public Criteria andLogDateIn(List<Timestamp> values) {
            addCriterion("log_date in", values, "logDate");
            return (Criteria) this;
        }

        public Criteria andLogDateNotIn(List<Timestamp> values) {
            addCriterion("log_date not in", values, "logDate");
            return (Criteria) this;
        }

        public Criteria andLogDateBetween(Timestamp value1, Timestamp value2) {
            addCriterion("log_date between", value1, value2, "logDate");
            return (Criteria) this;
        }

        public Criteria andLogDateNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("log_date not between", value1, value2, "logDate");
            return (Criteria) this;
        }

        public Criteria andVersionIsNull() {
            addCriterion("version is null");
            return (Criteria) this;
        }

        public Criteria andVersionIsNotNull() {
            addCriterion("version is not null");
            return (Criteria) this;
        }

        public Criteria andVersionEqualTo(Integer value) {
            addCriterion("version =", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotEqualTo(Integer value) {
            addCriterion("version <>", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThan(Integer value) {
            addCriterion("version >", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThanOrEqualTo(Integer value) {
            addCriterion("version >=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThan(Integer value) {
            addCriterion("version <", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThanOrEqualTo(Integer value) {
            addCriterion("version <=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionIn(List<Integer> values) {
            addCriterion("version in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotIn(List<Integer> values) {
            addCriterion("version not in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionBetween(Integer value1, Integer value2) {
            addCriterion("version between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotBetween(Integer value1, Integer value2) {
            addCriterion("version not between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Integer value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Integer value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Integer value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Integer value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Integer value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Integer> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Integer> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNull() {
            addCriterion("ctime is null");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNotNull() {
            addCriterion("ctime is not null");
            return (Criteria) this;
        }

        public Criteria andCtimeEqualTo(Timestamp value) {
            addCriterion("ctime =", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotEqualTo(Timestamp value) {
            addCriterion("ctime <>", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThan(Timestamp value) {
            addCriterion("ctime >", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("ctime >=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThan(Timestamp value) {
            addCriterion("ctime <", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("ctime <=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeIn(List<Timestamp> values) {
            addCriterion("ctime in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotIn(List<Timestamp> values) {
            addCriterion("ctime not in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime not between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNull() {
            addCriterion("mtime is null");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNotNull() {
            addCriterion("mtime is not null");
            return (Criteria) this;
        }

        public Criteria andMtimeEqualTo(Timestamp value) {
            addCriterion("mtime =", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotEqualTo(Timestamp value) {
            addCriterion("mtime <>", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThan(Timestamp value) {
            addCriterion("mtime >", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("mtime >=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThan(Timestamp value) {
            addCriterion("mtime <", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("mtime <=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeIn(List<Timestamp> values) {
            addCriterion("mtime in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotIn(List<Timestamp> values) {
            addCriterion("mtime not in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime not between", value1, value2, "mtime");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}