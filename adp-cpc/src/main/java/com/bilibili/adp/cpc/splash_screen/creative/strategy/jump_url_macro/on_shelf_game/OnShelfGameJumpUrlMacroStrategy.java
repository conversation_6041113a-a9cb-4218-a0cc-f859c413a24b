package com.bilibili.adp.cpc.splash_screen.creative.strategy.jump_url_macro.on_shelf_game;

import com.bilibili.adp.cpc.dao.querydsl.pos.AccAccountPo;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauUnitGamePo;
import com.bilibili.adp.cpc.enums.ad.PromotionPurposeType;
import com.bilibili.adp.cpc.splash_screen.creative.strategy.bos.CreativeContext;
import com.bilibili.adp.cpc.splash_screen.creative.strategy.jump_url_macro.JumpUrlMacroStrategy;
import com.bilibili.adp.launch.api.common.LaunchConstant;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.Objects;
@Service
public class OnShelfGameJumpUrlMacroStrategy extends JumpUrlMacroStrategy {
    private static final String ON_SHELF_GAME_MACRO_KEY_CHANNEL_ID = "channelId";
    private static final String ON_SHELF_GAME_MACRO_VALUE_CHANNEL_ID = "1";
    private static final String ON_SHELF_GAME_MACRO_KEY_CHANNEL_EXTRA = "channelExtra";
    private static final String ON_SHELF_GAME_MACRO_VALUE_CHANNEL_EXTRA = StringUtils.EMPTY;

    @Override
    public Integer getPromotionPurposeType() {
        return PromotionPurposeType.ON_SHELF_GAME.getCode();
    }

    @Override
    public String addMacro(CreativeContext context, String jumpUrl) {
        final String formattedJumpUrl = super.addMacro(context, jumpUrl);
        final AccAccountPo accAccountPo = context.getAccAccountPo();
        final boolean isSupportGame = Objects.equals(1, accAccountPo.getIsSupportGame());
        final boolean notInner = !Objects.equals(1, accAccountPo.getIsInner());
        //支持游戏并且是外广账号才继续添加宏参数，否则直接返回
        if (!(isSupportGame && notInner)) {
            return formattedJumpUrl;
        }
        final UriComponentsBuilder builder = UriComponentsBuilder.fromUriString(formattedJumpUrl);
        final LauUnitGamePo lauUnitGamePo = context.getLauUnitGamePo();
        final boolean isSubPkg = Objects.equals(1, lauUnitGamePo.getSubPkg());
        builder.replaceQueryParam(LaunchConstant.LANDING_PAGE_QUERY_GAME_KEY, LaunchConstant.LANDING_PAGE_QUERY_GAME_VALUE);
        builder.replaceQueryParam(LaunchConstant.GAME_HIDDEN_PARAM_KEY, LaunchConstant.GAME_HIDDEN_PARAM_VALUE_9782);
        if (isSubPkg) {
            builder.replaceQueryParam(ON_SHELF_GAME_MACRO_KEY_CHANNEL_ID, ON_SHELF_GAME_MACRO_VALUE_CHANNEL_ID);
            builder.replaceQueryParam(ON_SHELF_GAME_MACRO_KEY_CHANNEL_EXTRA, ON_SHELF_GAME_MACRO_VALUE_CHANNEL_EXTRA);
        }
        return builder.build().toUriString();
    }

    @Override
    public String removeMacro(String jumpUrl) {
        final String jumpUrlWithoutBaseMacro = super.removeMacro(jumpUrl);
        final UriComponentsBuilder builder = UriComponentsBuilder.fromUriString(jumpUrlWithoutBaseMacro);
        builder.replaceQueryParam(LaunchConstant.LANDING_PAGE_QUERY_GAME_KEY);
        builder.replaceQueryParam(LaunchConstant.GAME_HIDDEN_PARAM_KEY);
        builder.replaceQueryParam(ON_SHELF_GAME_MACRO_KEY_CHANNEL_ID);
        builder.replaceQueryParam(ON_SHELF_GAME_MACRO_KEY_CHANNEL_EXTRA);
        return builder.build().toUriString();
    }
}
