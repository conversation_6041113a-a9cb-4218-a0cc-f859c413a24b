package com.bilibili.adp.cpc.automatic_rule.constants;

public class AutomaticRuleErrorMessage {
    public static final String INVALID_RULE_ID = "规则不存在";
    public static final String REACH_THE_FREQUENCY_LIMIT = "到达调整频次上限";
    public static final String REACH_THE_VALUE_LIMIT = "到达调整值上限";
    public static final String INVALID_UNIT_ID = "无效单元";
    public static final String INVALID_UNIT_CONTAINS_UNLIMITED_RULE = "有{0}个规则已绑定不限单元，规则id：{1}";
    public static final String INVALID_UNIT_CONTAINS_RULE_BOUND_UNIT = "目标单元中有{0}个当前已绑定其他规则，单元id：{1}";
    public static final String INVALID_UNIT_CONTAINS_NO_BID_UNIT = "目标单元中有{0}个单元为选择“最大转化投放”的单元，该类单元不支持出价调整, 单元id：{1}";
}
