package com.bilibili.adp.cpc.wrapper.bos;

import com.bilibili.adp.cpc.core.bos.ImageBo;
import com.bilibili.adp.cpc.core.bos.ImageGroup;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ImageGroupWrapperBo {
    private Integer templateGroupId;
    private List<ImageBo> images;

    public ImageGroup toCoreBo() {
        return ImageGroup.builder()
                .templateGroupId(templateGroupId)
                .images(images)
                .build();
    }

    public static ImageGroupWrapperBo fromCoreBo(ImageGroup bo) {
        return ImageGroupWrapperBo.builder()
                .templateGroupId(bo.getTemplateGroupId())
                .images(bo.getImages())
                .build();
    }
}
