package com.bilibili.adp.cpc.databus.sub;

import com.alibaba.fastjson.JSONObject;
import com.bilibili.adp.cpc.biz.bos.WarpDatabusProperty;
import com.bilibili.adp.cpc.biz.services.ad_product.IAdProductService;
import com.bilibili.adp.cpc.dao.querydsl_cpc.pos.AdProductTotalInfoCPCPo;
import com.bilibili.adp.cpc.databus.Constant;
import com.bilibili.adp.cpc.repo.AdProductRepo;
import com.bilibili.warp.databus.AckableMessage;
import com.bilibili.warp.databus.DatabusProperty;
import com.bilibili.warp.databus.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Base64;
import java.util.Map;
import java.util.Objects;


@Slf4j
@Component
public class AdProductTotalInfoBinlogSub implements MessageListener {

    @Autowired
    private IAdProductService adProductService;

    private final String topic;

    private final String group;

    private static final String DEFAULT_TIME_STAMP = "0000-00-00 00:00:00";


    @Override
    public String topic() {
        return this.topic;
    }

    @Override
    public String group() {
        return this.group;
    }

    public AdProductTotalInfoBinlogSub(WarpDatabusProperty warpDatabusProperty) {
        // 注入DatabusProperties，此处的ALIAS常量即配置文件中填写的别名，是topic的上一级配置键
        // pub场景同理，在使用DatabusTemplate的地方注入DatabusProperties来获取
        Map<String, DatabusProperty> properties = warpDatabusProperty.getProperties();
        DatabusProperty property = properties.get("ad_product_total_info");
        log.info("ad_product_info, property={}", JSONObject.toJSONString(property));
        this.topic = property.getTopic();
        this.group = property.getSub().getGroup();
    }

    @Override
    public void onMessage(AckableMessage message) {
        try {
            String value = new String(message.payload());
            JSONObject msg = JSONObject.parseObject(value);

            String action = msg.getString(Constant.ACTION);

            JSONObject oldObject = msg.getJSONObject(Constant.OLD);
            AdProductTotalInfoCPCPo oldAdProductTotalInfoCPCPo = deserializeBinlogDto(oldObject);
            JSONObject newObject = msg.getJSONObject(Constant.NEW);
            AdProductTotalInfoCPCPo newAdProductTotalInfoCPCPo = deserializeBinlogDto(newObject);

            if (Objects.equals(action, Constant.UPDATE)) {
                adProductService.updateProductInfoByBinlog(newAdProductTotalInfoCPCPo);
            } else if (Objects.equals(action, Constant.INSERT)) {
                adProductService.insertProductInfoByBinlog(newAdProductTotalInfoCPCPo);
            }

        } catch (Exception e) {
            String msg = new String(message.payload());
            log.info("AdProductTotalInfoBinlogSub onMessage error message : {}", msg, e);
        }

        message.ack();
    }

    private AdProductTotalInfoCPCPo deserializeBinlogDto(JSONObject jsonObject) {
        if (Objects.isNull(jsonObject)) {
            return null;
        }
        AdProductTotalInfoCPCPo adProductTotalInfoCPCPo = jsonObject.toJavaObject(AdProductTotalInfoCPCPo.class);


        String deleteTime = jsonObject.getString("delete_time");

        if (Objects.equals(deleteTime, DEFAULT_TIME_STAMP)) {
            adProductTotalInfoCPCPo.setDeleteTime(null);
        }

        if (StringUtils.isBlank(adProductTotalInfoCPCPo.getUniqueKey())) {
            adProductTotalInfoCPCPo.setUniqueKey(String.valueOf(adProductTotalInfoCPCPo.getAdProductId()));
        }

        byte[] decodeAdAttribute = Base64.getDecoder().decode(adProductTotalInfoCPCPo.getAdAttributes());
        adProductTotalInfoCPCPo.setAdAttributes(new String(decodeAdAttribute));

        return adProductTotalInfoCPCPo;
    }
}
