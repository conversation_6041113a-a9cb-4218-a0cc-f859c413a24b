package com.bilibili.adp.cpc.compare.constants;

import com.bilibili.adp.cpc.compare.CompareMeta;

import java.util.HashMap;
import java.util.Map;

public class UnitCompareMeta {
    public static final String TABLE_NAME = "lau_unit";
    public static final Map<String, CompareMeta> META_MAP = new HashMap<>();
    public static final CompareMeta DUAL_BID_TWO_STAGE_OPTIMIZATION = new CompareMeta<>("dualBidTwoStageOptimization", "双出价两阶段优化", true, MetaType.BASE.getCode()).appendInto(META_MAP);
}
