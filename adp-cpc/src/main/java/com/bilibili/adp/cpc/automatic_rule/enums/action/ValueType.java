package com.bilibili.adp.cpc.automatic_rule.enums.action;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum ValueType {
    DEFAULT(0, "", ""),
    NUMERIC(1, "数值", ""),
    PERCENTAGE(2, "百分比", "%"),
    ;
    private final int code;
    private final String desc;
    private final String mark;

    public static ValueType getByCode(int code) {
        return Arrays.stream(values())
                .filter(valueType -> valueType.getCode() == code)
                .findFirst()
                .orElseThrow(IllegalArgumentException::new);
    }
}
