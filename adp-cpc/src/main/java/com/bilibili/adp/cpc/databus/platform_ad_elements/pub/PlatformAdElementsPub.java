package com.bilibili.adp.cpc.databus.platform_ad_elements.pub;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.databus.platform_ad_elements.bos.PlatformAdElementsMessage;
import com.bilibili.warp.databus.DatabusProperty;
import com.bilibili.warp.databus.DatabusTemplate;
import com.bilibili.warp.databus.Message;
import com.bilibili.warp.databus.PubResult;
import com.bilibili.warp.spring.boot.autoconfigure.databus.DatabusProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
public class PlatformAdElementsPub {
    public static final String DIMENSION_CAMPAIGN = "campaign";
    public static final String DIMENSION_UNIT = "unit";
    public static final String DIMENSION_CREATIVE = "creative";

    private static final String ALIAS = "platform-ad-elements";
    private final DatabusTemplate template;
    private final String topic;
    private final String group;

    public PlatformAdElementsPub(DatabusProperties databusProperties, DatabusTemplate template) {
        DatabusProperty databusProperty = databusProperties.getProperties().get(ALIAS);
        this.topic = databusProperty.getTopic();
        this.group = databusProperty.getPub().getGroup();
        this.template = template;
    }

    public void pub(Integer accountId, String dimension, Collection<Integer> ids) {
        List<Message> messages = ids
                .stream()
                .map(id -> {
                    PlatformAdElementsMessage adElementsMessage = PlatformAdElementsMessage.builder()
                            .dimension(dimension)
                            .id(id)
                            .build();
                    return Message.Builder.of(Utils.isPositive(accountId) ? String.valueOf(accountId) : String.valueOf(id), adElementsMessage).build();
                })
                .collect(Collectors.toList());
        PubResult result = template.batchPub(topic, group, messages);
        if (result.isSuccess()) {
            log.info("pub messages success, messages={}", messages);
        } else {
            log.error("pub messages error ", result.getThrowable());
        }
    }

    // 勿删, for刷数
    public void pub(String dimension, Collection<Integer> ids) {
        List<Message> messages = ids
                .stream()
                .map(id -> {
                    PlatformAdElementsMessage adElementsMessage = PlatformAdElementsMessage.builder()
                            .dimension(dimension)
                            .id(id)
                            .build();
                    return Message.Builder.of(String.valueOf(id), adElementsMessage).build();
                })
                .collect(Collectors.toList());
        PubResult result = template.batchPub(topic, group, messages);
        if (result.isSuccess()) {
            log.info("pub messages success, messages={}", messages);
        } else {
            log.error("pub messages error ", result.getThrowable());
        }
    }
}
