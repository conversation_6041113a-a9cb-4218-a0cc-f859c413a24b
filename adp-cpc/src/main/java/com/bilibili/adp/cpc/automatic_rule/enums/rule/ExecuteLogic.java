package com.bilibili.adp.cpc.automatic_rule.enums.rule;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum ExecuteLogic {
    ALL(1, "所有条件"),
    ANY(2, "任一条件"),
    ;
    private final int code;
    private final String desc;

    public static ExecuteLogic getByCode(int code) {
        return Arrays.stream(values())
                .filter(executeLogic -> executeLogic.getCode() == code)
                .findFirst()
                .orElseThrow(IllegalArgumentException::new);
    }
}
