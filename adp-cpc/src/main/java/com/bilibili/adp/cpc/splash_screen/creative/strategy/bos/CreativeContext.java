package com.bilibili.adp.cpc.splash_screen.creative.strategy.bos;

import com.bilibili.adp.cpc.dao.querydsl.pos.AccAccountPo;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauUnitGamePo;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauUnitPo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreativeContext {
    private AccAccountPo accAccountPo;
    private LauUnitPo lauUnitPo;
    private LauUnitGamePo lauUnitGamePo;
}
