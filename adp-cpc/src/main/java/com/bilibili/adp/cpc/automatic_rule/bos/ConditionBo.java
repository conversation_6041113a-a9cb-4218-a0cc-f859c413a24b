package com.bilibili.adp.cpc.automatic_rule.bos;

import com.bilibili.adp.cpc.automatic_rule.enums.condition.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConditionBo {
    private Long conditionId;
    /**
     * 操作对象
     *
     * @see Subject
     */
    private Integer subject;
    /**
     * 时间范围
     *
     * @see TimeScope
     */
    private Integer timeScope;
    /**
     * 条件类型
     *
     * @see ConditionType
     */
    private Integer conditionType;
    /**
     * 判断对象
     *
     * @see Options
     */
    private Integer options;
    /**
     * 值类型
     *
     * @see ValueType
     */
    private Integer valueType;
    /**
     * 值
     */
    private String value;
}
