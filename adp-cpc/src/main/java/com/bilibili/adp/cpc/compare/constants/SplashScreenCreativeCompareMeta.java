package com.bilibili.adp.cpc.compare.constants;

import com.bilibili.adp.cpc.compare.CompareMeta;

import java.util.HashMap;
import java.util.Map;

public class SplashScreenCreativeCompareMeta {
    public static final Map<String, CompareMeta> META_MAP = new HashMap<>();
    public static final CompareMeta IMAGE_URL = new CompareMeta<>("url", "图片url", true, MetaType.BASE.getCode()).appendInto(META_MAP);
    public static final CompareMeta IMAGE_MD5 = new CompareMeta<>("md5", "图片md5", true, MetaType.BASE.getCode()).appendInto(META_MAP);
    public static final CompareMeta CREATIVE_NAME = new CompareMeta<>("creativeName", "创意名称", false, MetaType.BASE.getCode()).appendInto(META_MAP);
    public static final CompareMeta INTERACT_TYPE = new CompareMeta<>("interactType", "按钮类型", false, MetaType.BASE.getCode()).appendInto(META_MAP);
    public static final CompareMeta QUALIFICATION = new CompareMeta<>("qualificationIds", "资质id列表", false, MetaType.BASE.getCode()).appendInto(META_MAP);
    public static final CompareMeta QUALIFICATION_PACKAGE = new CompareMeta<>("qualificationPackageId", "资质包", false, MetaType.BASE.getCode()).appendInto(META_MAP);
    public static final CompareMeta NAVIGATION_TYPE = new CompareMeta<>("navigationType","跳转类型", true, MetaType.BASE.getCode()).appendInto(META_MAP);
    public static final CompareMeta JUMP_GUIDE_CONTENT = new CompareMeta<>("jumpGuideContent","跳转引导文案", true, MetaType.BASE.getCode()).appendInto(META_MAP);
    public static final CompareMeta SCHEMA_GUIDE_CONTENT = new CompareMeta<>("schemaGuideContent","唤起引导文案", true, MetaType.BASE.getCode()).appendInto(META_MAP);
    public static final CompareMeta JUMP_URL = new CompareMeta<>("jumpUrl","跳转链接", true, MetaType.BASE.getCode()).appendInto(META_MAP);
    public static final CompareMeta SCHEMA_URL = new CompareMeta<>("schemaUrl","唤起链接", true, MetaType.BASE.getCode()).appendInto(META_MAP);
    public static final CompareMeta APP_PACKAGE_NAME = new CompareMeta<>("appPackageName","应用包名", false, MetaType.BASE.getCode()).appendInto(META_MAP);
    public static final CompareMeta CUSTOMIZED_IMP_URL = new CompareMeta<>("customizedImpUrl","展示监控链接", false, MetaType.BASE.getCode()).appendInto(META_MAP);
    public static final CompareMeta CUSTOMIZED_CLICK_URL = new CompareMeta<>("customizedClickUrl","点击监控链接", false, MetaType.BASE.getCode()).appendInto(META_MAP);
    public static final CompareMeta AUDIT_STATUS = new CompareMeta<>("auditStatus","审核状态", false, MetaType.STATUS.getCode()).appendInto(META_MAP);
    public static final CompareMeta CREATIVE_STATUS = new CompareMeta<>("creativeStatus","创意状态", false, MetaType.STATUS.getCode()).appendInto(META_MAP);

}
