package com.bilibili.adp.cpc.splash_screen.creative.enums;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

@Getter
@Builder
@AllArgsConstructor
public class ButtonStyle {

    /**
     * 横坐标
     */
    private int x;
    /**
     * 纵坐标
     */
    private int y;
    /**
     * 宽
     */
    private int width;
    /**
     * 高
     */
    private int height;
    /**
     * 按钮类型
     */
    private int buttonType;
    /**
     * 交互类型
     */
    private int interactStyle;
    /**
     * 字体占按钮的比例
     */
    private int fontRatio;
    /**
     * 点击区域扩张比例
     */
    private int clickExpandRatio;
    /**
     * 字体颜色
     */
    private String textColor;
    /**
     * 背景颜色
     */
    private String bgColor;
    /**
     * 降级策略
     */
    private int degradeType;
    /**
     * 顺序
     */
    private int seq;

    public static final int FIRST_BUTTON_SEQ = 0;

    private static final ButtonStyle TWIST_FIRST = ButtonStyle.builder()
            .x(50)
            .y(76)
            .width(70)
            .height(6)
            .buttonType(ButtonType.LOTTIE_BUTTON.getCode())
            .interactStyle(InteractStyle.TWIST.getCode())
            .fontRatio(22)
            .clickExpandRatio(100)
            .textColor("#FFFFFFFF")
            .bgColor("#80000000")
            .degradeType(0)
            .seq(FIRST_BUTTON_SEQ)
            .build();

    private static final ButtonStyle TWIST_SECOND = ButtonStyle.builder()
            .x(50)
            .y(81)
            .width(70)
            .height(7)
            .buttonType(ButtonType.FIXED_CONTENT_BUTTON.getCode())
            .interactStyle(InteractStyle.WORD.getCode())
            .fontRatio(30)
            .clickExpandRatio(100)
            .textColor("#FFFFFFFF")
            .bgColor("#80000000")
            .degradeType(0)
            .seq(1)
            .build();
    private static final ButtonStyle TWIST_THIRD = ButtonStyle.builder()
            .x(50)
            .y(84)
            .width(70)
            .height(7)
            .buttonType(ButtonType.CUSTOMIZED_CONTENT_BUTTON.getCode())
            .interactStyle(InteractStyle.WORD.getCode())
            .fontRatio(23)
            .clickExpandRatio(100)
            .textColor("#FFFFFFFF")
            .bgColor("#80000000")
            .degradeType(0)
            .seq(2)
            .build();

    private static final ButtonStyle SLIDE_FIRST = ButtonStyle.builder()
            .x(50)
            .y(78)
            .width(70)
            .height(7)
            .buttonType(ButtonType.LOTTIE_BUTTON.getCode())
            .interactStyle(InteractStyle.SLIDE.getCode())
            .fontRatio(23)
            .clickExpandRatio(100)
            .textColor("#FFFFFFFF")
            .bgColor("#80000000")
            .degradeType(1)
            .seq(FIRST_BUTTON_SEQ)
            .build();

    private static final ButtonStyle SLIDE_SECOND = ButtonStyle.builder()
            .x(50)
            .y(83)
            .width(64)
            .height(7)
            .buttonType(ButtonType.CUSTOMIZED_CONTENT_BUTTON.getCode())
            .interactStyle(InteractStyle.WORD.getCode())
            .fontRatio(23)
            .clickExpandRatio(100)
            .textColor("#FFFFFFFF")
            .bgColor("#80000000")
            .degradeType(0)
            .seq(1)
            .build();

    private static final ButtonStyle CLICK_FIRST = ButtonStyle.builder()
            .x(50)
            .y(76)
            .width(70)
            .height(7)
            .buttonType(ButtonType.CUSTOMIZED_CONTENT_BUTTON.getCode())
            .interactStyle(InteractStyle.CLICK.getCode())
            .fontRatio(22)
            .clickExpandRatio(100)
            .textColor("#FFFFFFFF")
            .bgColor("#80000000")
            .degradeType(0)
            .seq(FIRST_BUTTON_SEQ)
            .build();

    public static final List<ButtonStyle> TWIST = Arrays.asList(TWIST_FIRST, TWIST_SECOND, TWIST_THIRD);
    public static final List<ButtonStyle> SLIDE = Arrays.asList(SLIDE_FIRST, SLIDE_SECOND);
    public static final List<ButtonStyle> CLICK = Arrays.asList(CLICK_FIRST);
}
