package com.bilibili.adp.cpc.automatic_rule.service;

import com.bapis.ad.automatic.rule.service.Object;
import com.bapis.ad.automatic.rule.service.TriggerRequest;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.enums.OperatorType;
import com.bilibili.adp.cpc.automatic_rule.bos.ExecutionRecordBo;
import com.bilibili.adp.cpc.automatic_rule.constants.AutomaticRuleConstant;
import com.bilibili.adp.cpc.automatic_rule.constants.AutomaticRuleErrorMessage;
import com.bilibili.adp.cpc.automatic_rule.enums.action.*;
import com.bilibili.adp.cpc.automatic_rule.enums.execution_record.ExecutionResult;
import com.bilibili.adp.cpc.automatic_rule.enums.object.ObjectType;
import com.bilibili.adp.cpc.biz.services.account.api.ILauAccountService;
import com.bilibili.adp.cpc.biz.services.unit.CpcUnitService;
import com.bilibili.adp.cpc.biz.services.unit.UnitService;
import com.bilibili.adp.cpc.biz.services.unit.dto.UnitPriceDto;
import com.bilibili.adp.cpc.biz.services.unit.dto.UnitUpdateBidPriceDto;
import com.bilibili.adp.cpc.core.LaunchAccountBudgetService;
import com.bilibili.adp.cpc.core.LaunchUnitV1Service;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauUnitPo;
import com.bilibili.adp.cpc.dao.querydsl.pos.*;
import com.bilibili.adp.cpc.dto.NewBatchUpdateCpcUnitBudgetDto;
import com.bilibili.adp.launch.api.account.dto.AccountBudgetDto;
import com.bilibili.adp.launch.api.common.*;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

import static com.bilibili.adp.cpc.dao.config.PlatformMySqlConfig.AD_TM;

@Slf4j
@Service
@RequiredArgsConstructor
public class ActionTriggerService {
    private static final BigDecimal HUNDRED = BigDecimal.valueOf(100);

    private final RuleService ruleService;
    private final ConditionService conditionService;
    private final ActionService actionService;
    private final LaunchUnitV1Service launchUnitV1Service;
    private final LaunchAccountBudgetService launchAccountBudgetService;
    private final UnitService unitService;
    private final ILauAccountService lauAccountService;
    private final CpcUnitService cpcUnitService;
    private final ExecutionRecordsService executionRecordsService;
    @Transactional(transactionManager = AD_TM, rollbackFor = Throwable.class)
    public void trigger(TriggerRequest request) {
        final LauAutomaticRulePo rule = ruleService.getRuleByRuleId(request.getRule().getRuleId());
        Assert.notNull(rule, AutomaticRuleErrorMessage.INVALID_RULE_ID);
        if (Objects.equals(AutomaticRuleConstant.RULE_IS_DELETED, rule.getIsDeleted())) {
            log.warn("rule has been deleted, rule id :{}", rule.getRuleId());
            return;
        }
        if (Objects.equals(AutomaticRuleConstant.RULE_STATUS_INVALID, rule.getStatus())) {
            log.warn("invalid rule status, rule id :{}", rule.getRuleId());
            return;
        }
        final List<LauAutomaticRuleConditionPo> conditions = conditionService.listConditionsByRuleId(request.getRule().getRuleId());
        final Integer accountId = rule.getAccountId();
        final long actionId = request.getAction().getActionId();
        final Object object = request.getObject();
        checkObject(object.getObjectTypeValue(), object.getObjectId());
        final LauAutomaticRuleActionPo action = actionService.getAction(actionId);
        checkFrequencyLimit(action, object.getObjectTypeValue(), object.getObjectId());
        final BigDecimal originValue = getOriginValue(action.getSubject(), object.getObjectId());
        final BigDecimal adjustValue = getAdjustValue(action.getActionType(), action.getValueType(), action.getValue(), action.getSubject(), originValue);
        checkValueLimit(action.getActionType(), action.getLimitType(), action.getLimitValue(), adjustValue);
        //事先构造执行记录
        final ExecutionRecordBo record = executionRecordsService.buildRecord(request, rule, conditions, action);
        try {
            doTrigger(accountId, action.getSubject(), object.getObjectId(), adjustValue);
            final String executeResultDetail = ActionService.action(action, originValue, adjustValue);
            record.setExecuteResultDetail(executeResultDetail);
            record.setExecuteResult(ExecutionResult.SUCCESS.getCode());
        } catch (Throwable throwable) {
            final String executeResultDetail = throwable.getMessage();
            record.setExecuteResult(ExecutionResult.FAIL.getCode());
            record.setExecuteResultDetail(executeResultDetail);
        }
        executionRecordsService.save(record);
    }

    private void checkObject(Integer objectType, Integer objectId) {
        if (Objects.equals(ObjectType.UNIT.getCode(), objectType)) {
            final LauUnitPo unitPo = launchUnitV1Service.get(objectId);
            Assert.isTrue(Objects.equals(unitPo.getUnitStatus(), UnitStatus.VALID.getCode()), AutomaticRuleErrorMessage.INVALID_UNIT_ID);
        }
    }

    private void checkFrequencyLimit(LauAutomaticRuleActionPo action, Integer objectType, Integer objectId) {
        final Integer frequencyCount = action.getFrequencyCount();
        final Integer frequencyScope = action.getFrequencyScope();
        final FrequencyCount frequencyCountEnum = FrequencyCount.getByCode(frequencyCount);
        //如果为不限次数，则不需要走后续校验
        if (frequencyCountEnum == FrequencyCount.UNLIMITED) {
            return;
        }
        final int count = frequencyCountEnum.getCount();
        final long days = FrequencyScope.getDaysByCode(frequencyScope);
        final LocalDateTime end = LocalDateTime.now();
        final LocalDateTime start = end.toLocalDate().atStartOfDay().minusDays(days);
        final List<Integer> executeResults = Collections.singletonList(ExecutionResult.SUCCESS.getCode());
        final List<LauAutomaticRuleExecutionRecordPo> records = executionRecordsService.listRecords(action.getActionId(), objectType, objectId, executeResults, start, end);
        Assert.isTrue(records.size() < count, AutomaticRuleErrorMessage.REACH_THE_FREQUENCY_LIMIT);
    }

    private void checkValueLimit(Integer actionType, Integer limitType, String limitValue, BigDecimal adjustValue) {
        final LimitType limitTypeEnum = LimitType.getByCode(limitType);
        if (limitTypeEnum == LimitType.UNLIMITED) {
            return;
        }
        final ActionType actionTypeEnum = ActionType.getByCode(actionType);
        switch (actionTypeEnum) {
            case INCREASE:
                Assert.isTrue(adjustValue.compareTo(new BigDecimal(limitValue)) <= 0, AutomaticRuleErrorMessage.REACH_THE_VALUE_LIMIT);
                break;
            case DECREASE:
                Assert.isTrue(adjustValue.compareTo(new BigDecimal(limitValue)) >= 0, AutomaticRuleErrorMessage.REACH_THE_VALUE_LIMIT);
                break;
        }
    }

    private void doTrigger(Integer accountId, Integer actionSubject, Integer objectId, BigDecimal adjustValue) {
        Operator operator = getOperator(accountId);
        final Subject subject = Subject.getByCode(actionSubject);
        switch (subject) {
            case ACCOUNT_BUDGET:
                adjustAccountBudget(operator, adjustValue);
                break;
            case UNIT_BUDGET:
                adjustUnitBudget(operator, objectId, adjustValue);
                break;
            case UNIT_OPCX_BID_PRICE:
                adjustUnitOcpxBidPrice(operator, objectId, adjustValue);
                break;
            case PAUSE_UNIT:
                pauseUnit(operator, objectId);
                break;
            default:
                break;
        }
    }

    private BigDecimal getOriginValue(Integer subject, Integer objectId) {
        final Subject subjectEnum = Subject.getByCode(subject);
        final LauAccountBudgetPo accountBudgetPo;
        final LauUnitPo unitPo;
        final BigDecimal originValue;
        switch (subjectEnum) {
            case ACCOUNT_BUDGET:
                accountBudgetPo = launchAccountBudgetService.get(objectId);
                //账号预算（元）
                originValue = BigDecimal.valueOf(accountBudgetPo.getBudget()).divide(HUNDRED, 2, RoundingMode.HALF_UP);
                break;
            case UNIT_BUDGET:
                unitPo = launchUnitV1Service.get(objectId);
                //单元预算（元）
                originValue = BigDecimal.valueOf(unitPo.getBudget()).divide(HUNDRED, 2, RoundingMode.HALF_UP);
                break;
            case UNIT_OPCX_BID_PRICE:
                unitPo = launchUnitV1Service.get(objectId);
                //单元OCPX出价（元）
                originValue = BigDecimal.valueOf(unitPo.getTwoStageBid()).divide(HUNDRED, 2, RoundingMode.HALF_UP);
                break;
            case PAUSE_UNIT:
                //do nothing
                originValue = BigDecimal.ZERO;
                break;
            default:
                throw new IllegalArgumentException();
        }
        return originValue;
    }

    private BigDecimal getAdjustValue(Integer actionType, Integer valueType, String value, Integer subject, BigDecimal originValue) {
        final ActionType actionTypeEnum = ActionType.getByCode(actionType);
        final ValueType valueTypeEnum = ValueType.getByCode(valueType);
        final Subject subjectEnum = Subject.getByCode(subject);
        final BigDecimal adjustValue;
        switch (subjectEnum) {
            case ACCOUNT_BUDGET:
            case UNIT_BUDGET:
            case UNIT_OPCX_BID_PRICE:
                //单元OCPX出价（元）
                //单元预算（元）
                //账号预算（元）
                adjustValue = calculate(actionTypeEnum, valueTypeEnum, originValue, new BigDecimal(value));
                break;
            case PAUSE_UNIT:
                //暂停单元他就不需要看上限
                //do nothing
                adjustValue = BigDecimal.ZERO;
                break;
            default:
                throw new IllegalArgumentException();
        }
        return adjustValue;
    }

    private static BigDecimal calculate(ActionType actionTypeEnum, ValueType valueTypeEnum, BigDecimal originValue, BigDecimal value) {
        final BigDecimal adjustValue;
        switch (valueTypeEnum) {
            case NUMERIC:
                adjustValue = value;
                break;
            case PERCENTAGE:
                adjustValue = originValue.multiply(value).divide(HUNDRED, 2, RoundingMode.HALF_UP);
                break;
            default:
                throw new IllegalArgumentException();
        }
        final BigDecimal result;
        switch (actionTypeEnum) {
            case DECREASE:
                result = originValue.subtract(adjustValue);
                break;
            case INCREASE:
                result = originValue.add(adjustValue);
                break;
            case ADJUST_TO:
                result = adjustValue;
                break;
            default:
                throw new IllegalArgumentException();
        }
        return result;
    }

    public void adjustAccountBudget(Operator operator, BigDecimal accountBudget) {
        //千万注意：账户预算的入参是分
        final BigDecimal accountBudgetFen = accountBudget.multiply(HUNDRED);
        final AccountBudgetDto accountBudgetDto = AccountBudgetDto
                .builder()
                .budget(accountBudgetFen.intValue())
                .budgetLimitType(BudgetLimitType.DESIGNATE.getCode())
                .type(BudgetType.DAILY)
                .build();
        lauAccountService.saveBudget(operator, accountBudgetDto);
    }

    @SneakyThrows
    private void adjustUnitBudget(Operator operator, Integer unitId, BigDecimal unitBudget) {
        //千万注意：单元预算的入参是元
        final NewBatchUpdateCpcUnitBudgetDto unitBudgetDto = NewBatchUpdateCpcUnitBudgetDto
                .builder()
                .unitIds(Collections.singletonList(unitId))
                .budget(unitBudget)
                .dailyBudgetType(DailyBudgetType.MANUAL.getCode())
                .effectType(BudgetEffectType.CURRENTDAY.getCode())
                .build();
        cpcUnitService.batchUpdateCpcUnitBudget(unitBudgetDto, operator);
    }

    @SneakyThrows
    private void adjustUnitOcpxBidPrice(Operator operator, Integer unitId, BigDecimal unitOcpxBidPrice) {
        //实在是对不起，没时间改com.bilibili.adp.cpc.biz.services.unit.CpcUnitService.batchUpdateNoDpaBidPrice
        //又查了遍sales type
        final LauUnitPo unitPo = launchUnitV1Service.get(unitId);
        //千万注意：单元出价的入参是分
        final BigDecimal unitOcpxBidPriceFen = unitOcpxBidPrice.multiply(HUNDRED);
        final UnitPriceDto unitBidPriceDto = UnitPriceDto
                .builder()
                .unitId(unitId)
                .value(unitOcpxBidPriceFen)
                .build();
        final UnitUpdateBidPriceDto batchUpdateUnitBidPriceDto = UnitUpdateBidPriceDto
                .builder()
                .unitPriceDtos(Collections.singletonList(unitBidPriceDto))
                .updateType(UnitUpdateBidPriceTypeEnum.OCPX_OPTIMIZE.getCode())
                .salesType(unitPo.getSalesType())
                .build();
        cpcUnitService.batchUpdateNoDpaBidPrice(operator, batchUpdateUnitBidPriceDto);
    }

    @SneakyThrows
    private void pauseUnit(Operator operator, Integer unitId) {
        unitService.pauseUnit(operator, Collections.singletonList(unitId));
    }

    private Operator getOperator(Integer accountId) {
        final Operator operator = new Operator();
        operator.setOperatorId(accountId);
        operator.setOperatorName("自动化规则");
        operator.setOperatorType(OperatorType.SYSTEM);
        return operator;
    }
}
