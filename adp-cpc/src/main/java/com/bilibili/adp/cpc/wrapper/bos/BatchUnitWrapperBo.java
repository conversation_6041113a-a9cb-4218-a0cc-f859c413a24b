package com.bilibili.adp.cpc.wrapper.bos;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class BatchUnitWrapperBo {
    private List<Integer> unitIds;
    private AgeTargetWrapperBo age;
    private List<Integer> gender;
    private List<Integer> area;
    private List<Integer> areaLevel;
    private List<Integer> areaType;
    private CrowdPackTargetWrapperBo crowdPack;
    private boolean hasTargetPackage;
    private boolean isSearchAd;
    private UnitPricingWrapperBo pricing;
    private UnitNoBidWrapperBo noBid;
}
