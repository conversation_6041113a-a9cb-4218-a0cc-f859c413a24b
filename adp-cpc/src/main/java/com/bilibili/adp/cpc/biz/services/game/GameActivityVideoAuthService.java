package com.bilibili.adp.cpc.biz.services.game;

import com.bapis.ad.account.crm.acc.AccountBase;
import com.bapis.ad.account.crm.acc.AccountBaseReply;
import com.bapis.archive.service.*;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.exception.SystemException;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.biz.bos.archive.ArchiveBo;
import com.bilibili.adp.cpc.biz.bos.game.GameActivityVideoBo;
import com.bilibili.adp.cpc.biz.bos.game.GameActivityVideoRequest;
import com.bilibili.adp.cpc.biz.services.account.GeneralVideoAuthCommonService;
import com.bilibili.adp.cpc.biz.services.account.GeneralVideoAuthDaoService;
import com.bilibili.adp.cpc.biz.services.account.GeneralVideoAuthService;
import com.bilibili.adp.cpc.biz.services.account.ProfileInfoService;
import com.bilibili.adp.cpc.biz.services.account.bos.ProfileDto;
import com.bilibili.adp.cpc.biz.services.account.vo.GameActivityAuthInfoVo;
import com.bilibili.adp.cpc.biz.services.account.vo.GameActivityVideoAuthInfoVo;
import com.bilibili.adp.cpc.biz.services.account.vo.GeneralMidInfoVo;
import com.bilibili.adp.cpc.biz.services.account.vo.GeneralVideoInfoVo;
import com.bilibili.adp.cpc.biz.services.archive.ArchiveService;
import com.bilibili.adp.cpc.biz.services.game.bos.GameApiRequest;
import com.bilibili.adp.cpc.biz.services.game.bos.GameBaseBo;
import com.bilibili.adp.cpc.biz.services.game.bos.LauAccountGameAvidMappingBo;
import com.bilibili.adp.cpc.biz.services.game.bos.LauAccountGameMappingBo;
import com.bilibili.adp.cpc.dao.querydsl.QLauAccountGameAvidMapping;
import com.bilibili.adp.cpc.dao.querydsl.QLauAccountGameMapping;
import com.bilibili.adp.cpc.dao.querydsl.QLauGeneralAvidRequest;
import com.bilibili.adp.cpc.dao.querydsl.pos.LauAccountGameAvidMappingPo;
import com.bilibili.adp.cpc.dao.querydsl.pos.LauAccountGameMappingPo;
import com.bilibili.adp.cpc.dao.querydsl.pos.LauAgentGameMappingConfigPo;
import com.bilibili.adp.cpc.dao.querydsl.pos.LauGeneralAvidRequestPo;
import com.bilibili.adp.cpc.enums.GeneralVideoAuthStatusEnum;
import com.bilibili.adp.cpc.enums.GeneralVideoEnum;
import com.bilibili.adp.cpc.enums.archive.ArchiveShareScopeEnum;
import com.bilibili.adp.cpc.proxy.ArchiveServiceProxy;
import com.bilibili.adp.cpc.proxy.CpmAdAccountProxy;
import com.bilibili.adp.cpc.proxy.CrmPlatformProxy;
import com.bilibili.adp.passport.api.dto.UserInfoDto;
import com.bilibili.adp.passport.api.service.IPassportService;
import com.bilibili.adp.passport.biz.common.ArchiveState;
import com.bilibili.adp.passport.biz.manager.MessageManager;
import com.bilibili.bjcom.querydsl.BaseQueryFactory;
import com.bilibili.bjcom.querydsl.clause.BaseQuery;
import com.bilibili.bvid.BVIDUtils;
import com.bilibili.crm.platform.api.agent.dto.AgentDto;
import com.bilibili.crm.platform.api.customer.dto.CustomerInfoDto;
import com.google.common.collect.Lists;
import com.google.gson.reflect.TypeToken;
import com.querydsl.core.types.dsl.BooleanExpression;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

import static com.bilibili.adp.cpc.dao.config.PlatformMySqlConfig.AD_BQF;
import static com.bilibili.adp.cpc.dao.config.PlatformMySqlConfig.AD_TM;
import static com.bilibili.adp.cpc.dao.querydsl.QLauAgentGameMappingConfig.lauAgentGameMappingConfig;
import static com.bilibili.adp.cpc.dao.querydsl.QLauGeneralAvidRequest.lauGeneralAvidRequest;

/**
 * <AUTHOR>
 * @Description
 * @date 3/15/24
 **/
@Component
@Slf4j
public class GameActivityVideoAuthService {

    private final static List<Integer> VALID_VIDEO_STATES = Arrays.asList(ArchiveState.OPEN_BROWSE.getCode(),
            ArchiveState.ORANGE_THROUGH.getCode(), ArchiveState.REPAIR_PENDING.getCode(),
            ArchiveState.VIP_ACCESS.getCode());
    @Value("${game.cp.bind.send.message.link:https://cm.bilibili.com/art/activities/game-auth}")
    private String GAME_CP_BIND_SEND_MESSAGE_LINK;
    @Value("${game.video.bind.send.message.link:https://cm.bilibili.com/art/activities/game-video-auth}")
    private String GAME_VIDEO_BIND_SEND_MESSAGE_LINK;
    @Resource(name = AD_BQF)
    private BaseQueryFactory adBqf;
    @Resource
    private GameInfoService gameInfoService;
    @Resource
    private CrmPlatformProxy crmPlatformProxy;
    @Resource
    private ArchiveServiceProxy archiveServiceProxy;
    @Resource
    private GeneralVideoAuthDaoService generalVideoAuthDaoService;
    @Resource
    private GeneralVideoAuthService generalVideoAuthService;
    @Resource
    private MessageManager messageManager;
    @Resource
    private ProfileInfoService profileInfoService;
    @Resource
    private GeneralVideoAuthCommonService arcSupportAdvertisingService;
    @Resource
    private ArchiveService archiveService;
    @Resource
    private IPassportService passportService;

    @Resource
    private CpmAdAccountProxy cpmAdAccountProxy;


    public PageResult<LauAccountGameMappingPo> cpAuthList(Integer accountId, List<Integer> authStatus, Integer page,
                                                          Integer size) {
        AccountBaseReply accountBase = cpmAdAccountProxy.getAccountBase(accountId);
        int agentId = accountBase.getData().getDependencyAgentId();
        BooleanExpression expr = QLauAccountGameMapping.lauAccountGameMapping.accountId.eq(accountId);
        if (agentId != 0) {
            expr = expr.or(QLauAccountGameMapping.lauAccountGameMapping.agentId.eq(agentId));
        }
        long count = adBqf.selectFrom(QLauAccountGameMapping.lauAccountGameMapping)
                .where(expr)
                .whereIfNotEmpty(authStatus, QLauAccountGameMapping.lauAccountGameMapping.authStatus::in)
                .fetchCount();

        List<LauAccountGameMappingPo> data = adBqf.selectFrom(QLauAccountGameMapping.lauAccountGameMapping)
                .where(expr)
                .whereIfNotEmpty(authStatus, QLauAccountGameMapping.lauAccountGameMapping.authStatus::in)
                .orderBy("mtime desc").offset((long) (page - 1) * size).limit(size).fetch();
        return new PageResult<>((int) count, data);
    }

    public List<GameBaseBo> canAuthGameList(Integer accountId) {

        AccountBaseReply accountBase = cpmAdAccountProxy.getAccountBase(accountId);
        AgentDto agentDto = crmPlatformProxy.iSoaAgentService().getAgentById(accountBase.getData().getDependencyAgentId());
        Integer sysAgentId = agentDto.getSysAgentId();
        List<LauAgentGameMappingConfigPo> configPos = adBqf.selectFrom(lauAgentGameMappingConfig)
                .where(lauAgentGameMappingConfig.agentId.eq(sysAgentId)
                        .and(lauAgentGameMappingConfig.isDeleted.eq(IsDeleted.VALID.getCode()))).fetch();
        List<Long> gameBaseIds = configPos.stream().map(LauAgentGameMappingConfigPo::getGameBaseId)
                .collect(Collectors.toList());
        return gameInfoService.queryGames(GameApiRequest.builder().game_base_ids(StringUtils.join(gameBaseIds, ","))
                .sdk_type(GameInfoService.SdkTypeEnum.ANDROID.getCode()).build());
    }

    @SneakyThrows
    @Transactional(value = AD_TM, rollbackFor = Throwable.class)
    public void accountGameAuth(LauAccountGameMappingBo bo) throws ServiceException, SystemException {
        LauAccountGameMappingPo lauAccountGameMappingPo = adBqf.selectFrom(QLauAccountGameMapping.lauAccountGameMapping)
                .where(QLauAccountGameMapping.lauAccountGameMapping.accountId.eq(bo.getAccountId())
                        .and(QLauAccountGameMapping.lauAccountGameMapping.gameBaseId.eq(bo.getGameBaseId())))
                .fetchOne(LauAccountGameMappingPo.class);

        // game_base_id account 校验
        AccountBaseReply accountBase = cpmAdAccountProxy.getAccountBase(bo.getAccountId());
        AgentDto agent = crmPlatformProxy.iSoaAgentService().getAgentById(accountBase.getData().getDependencyAgentId());
        bo.setAgentId(agent.getSysAgentId());
        List<Long> gameBaseIds = queryAuthAgentGameBaseIds(bo);

        Assert.isTrue(gameBaseIds.contains(bo.getGameBaseId()), "你不能操作未授权的游戏CP");

        bo.setAuthStatus(GeneralVideoEnum.AuthEnum.WAIT_ACCEPT.getCode());
        //game_base_id对应的mid
        Long mid = gameInfoService.queryMid(bo.getGameBaseId());
        bo.setAuthMid(mid);

        if (GeneralVideoEnum.AuthTimeTypeEnum.NO_LIMIT.getCode().equals(bo.getAuthTimeType())) {
            bo.setAuthEffectiveTime(Utils.getToday());
            bo.setAuthExpireTime(Utils.getSomeYearAfter(Utils.getToday(), 10));
        }

        Assert.isTrue(getCPRequestTimesByWeek(bo,
                        GeneralVideoEnum.RequestEnum.GAME_CP_AUTH.getCode()) < generalVideoAuthService.getGENERAL_VIDEO_BIND_RENEWAL_TIMES(),
                "7天内只能对同一账号发起" + generalVideoAuthService.getGENERAL_VIDEO_BIND_RENEWAL_TIMES() + "次授权");

        if (bo.getSharingScope() != null && bo.getSharingScope().equals(ArchiveShareScopeEnum.THIS_ACCOUNT_ONLY.getCode())) {
            bo.setAgentId(0);
        }
        if (null == lauAccountGameMappingPo) {
            //send
            Long key = adBqf.insert(QLauAccountGameMapping.lauAccountGameMapping).insertGetKey(bo);
            insertRequestLog(key.intValue(), GeneralVideoEnum.RequestEnum.GAME_CP_AUTH.getCode(), bo.getAuthTimeType(),
                    bo.getAuthEffectiveTime(), bo.getAuthExpireTime());

            //send message
            sendMessage(key.intValue(), bo.getAuthMid(), "游戏CP授权申请", "您有一条游戏CP授权申请",
                    GeneralVideoEnum.RequestEnum.GAME_CP_AUTH, GAME_CP_BIND_SEND_MESSAGE_LINK);

        } else {
            adBqf.update(QLauAccountGameMapping.lauAccountGameMapping)
                    .set(QLauAccountGameMapping.lauAccountGameMapping.authEffectiveTime, bo.getAuthEffectiveTime())
                    .set(QLauAccountGameMapping.lauAccountGameMapping.authExpireTime, bo.getAuthExpireTime())
                    .set(QLauAccountGameMapping.lauAccountGameMapping.isDeleted, IsDeleted.VALID.getCode())
                    .set(QLauAccountGameMapping.lauAccountGameMapping.authMid, bo.getAuthMid())
                    .set(QLauAccountGameMapping.lauAccountGameMapping.authStatus, bo.getAuthStatus())
                    .set(QLauAccountGameMapping.lauAccountGameMapping.agentId, bo.getAgentId())
                    .setIfNotNull(QLauAccountGameMapping.lauAccountGameMapping.sharingScope, bo.getSharingScope())
                    .where(QLauAccountGameMapping.lauAccountGameMapping.accountId.eq(bo.getAccountId())
                            .and(QLauAccountGameMapping.lauAccountGameMapping.gameBaseId.eq(bo.getGameBaseId())))
                    .execute();
            insertRequestLog(lauAccountGameMappingPo.getId().intValue(),
                    GeneralVideoEnum.RequestEnum.GAME_CP_AUTH.getCode(), bo.getAuthTimeType(),
                    bo.getAuthEffectiveTime(), bo.getAuthExpireTime());

            //send message
            sendMessage(lauAccountGameMappingPo.getId().intValue(), lauAccountGameMappingPo.getAuthMid(),
                    "游戏CP授权申请", "您有一条游戏CP授权申请", GeneralVideoEnum.RequestEnum.GAME_CP_AUTH,
                    GAME_CP_BIND_SEND_MESSAGE_LINK);
        }

        //存在 未删除的 提示报错
//        if (!lauAccountGameMappingPo.getIsDeleted().equals(IsDeleted.DELETED.getCode())) {
//            throw new RuntimeException("该游戏已经存在授权，不支持二次授权");
//        }
    }

    private List<Long> queryAuthAgentGameBaseIds(LauAccountGameMappingBo bo) {
        List<LauAgentGameMappingConfigPo> agentGameMappingConfigPos = adBqf.selectFrom(lauAgentGameMappingConfig)
                .where(lauAgentGameMappingConfig.agentId.eq(bo.getAgentId())
                        .and(lauAgentGameMappingConfig.isDeleted.eq(IsDeleted.VALID.getCode()))).fetch();
        return agentGameMappingConfigPos.stream().map(LauAgentGameMappingConfigPo::getGameBaseId).distinct()
                .collect(Collectors.toList());
    }

    //同代理下7天内只允许申请3次
    private Long getCPRequestTimesByWeek(LauAccountGameMappingBo bo, Integer requestType) {
        List<Long> ids = adBqf.select(QLauAccountGameMapping.lauAccountGameMapping.id)
                .from(QLauAccountGameMapping.lauAccountGameMapping)
                .where(QLauAccountGameMapping.lauAccountGameMapping.agentId.eq(bo.getAgentId())
                        .and(QLauAccountGameMapping.lauAccountGameMapping.gameBaseId.eq(bo.getGameBaseId()))).fetch();

        if (CollectionUtils.isEmpty(ids)) {
            return 0L;
        }

        Timestamp beginTime = Utils.getSomeDayBeforeToday(7);
        List<Integer> mappingIds = ids.stream().map(Long::intValue).collect(Collectors.toList());
        return adBqf.selectFrom(lauGeneralAvidRequest).whereIfNotEmpty(mappingIds, lauGeneralAvidRequest.mappingId::in)
                .where(lauGeneralAvidRequest.ctime.goe(beginTime))
                .where(lauGeneralAvidRequest.requestType.eq(requestType)).fetchCount();
    }

    public LauGeneralAvidRequestPo insertRequestLog(Integer mappingId, Integer requestType, Integer authTimeType,
                                                    Timestamp authEffectiveTime, Timestamp authExpireTime) {
        LauGeneralAvidRequestPo lauGeneralAvidRequestPo = new LauGeneralAvidRequestPo();
        lauGeneralAvidRequestPo.setMappingId(mappingId);
        lauGeneralAvidRequestPo.setRequestType(requestType);

        lauGeneralAvidRequestPo.setAuthTimeType(authTimeType);
        if (GeneralVideoEnum.AuthTimeTypeEnum.LIMIT.getCode().equals(authTimeType)) {
            Assert.isTrue(authEffectiveTime != null && authExpireTime != null, "续约开始和结束时间都不能为空");
            lauGeneralAvidRequestPo.setAuthEffectiveTime(authEffectiveTime);
            lauGeneralAvidRequestPo.setAuthExpireTime(authExpireTime);
        } else {
            lauGeneralAvidRequestPo.setAuthEffectiveTime(Utils.getToday());
            lauGeneralAvidRequestPo.setAuthExpireTime(
                    new Timestamp(DateUtils.addYears(new Date(authEffectiveTime.getTime()), 100).getTime()));
        }

        adBqf.insert(QLauGeneralAvidRequest.lauGeneralAvidRequest).insertGetKey(lauGeneralAvidRequestPo);
        return lauGeneralAvidRequestPo;
    }

    public void sendMessage(Integer authId, Long mid, String title, String content,
                            GeneralVideoEnum.RequestEnum requestEnum,
                            String urlPreFix) throws ServiceException, SystemException {
        //发送站内信给mid
        String linkUrl = UriComponentsBuilder.fromUriString(urlPreFix).queryParam("auth_id", authId)
                .queryParam("type", requestEnum.getCode()).build(false).toUriString();
        String linkStr = String.format("#{查看详情}{\"%s\"}", linkUrl);
        messageManager.sendMessageToMids(title, String.format("%s，%s", content, linkStr),
                Collections.singletonList(mid));
    }

    private void insertRequestLog(Integer mappingId, GeneralVideoEnum.RequestEnum requestEnum) {
        LauGeneralAvidRequestPo lauGeneralAvidRequestPo = new LauGeneralAvidRequestPo();
        lauGeneralAvidRequestPo.setMappingId(mappingId);
        lauGeneralAvidRequestPo.setRequestType(requestEnum.getCode());
        adBqf.insert(QLauGeneralAvidRequest.lauGeneralAvidRequest).insertGetKey(lauGeneralAvidRequestPo);
    }

    public GameActivityAuthInfoVo gameActivityCpInfo(Long authId, Long mid, Integer type) {
        LauAccountGameMappingPo lauAccountGameMappingPo = adBqf.selectFrom(QLauAccountGameMapping.lauAccountGameMapping)
                .where(QLauAccountGameMapping.lauAccountGameMapping.id.eq(authId)
                                .and(QLauAccountGameMapping.lauAccountGameMapping.authMid.eq(mid))
//                        .and(QLauAccountGameMapping.lauAccountGameMapping.authStatus.eq(GeneralVideoEnum.AuthEnum.WAIT_ACCEPT.getCode()))
                ).fetchOne(LauAccountGameMappingPo.class);

        Assert.isTrue(null != lauAccountGameMappingPo, "审核记录不存在");
        //获取最新审核记录
        LauGeneralAvidRequestPo lauGeneralAvidRequestPo = adBqf.selectFrom(lauGeneralAvidRequest)
                .where(lauGeneralAvidRequest.mappingId.eq(authId.intValue())
                        .and(lauGeneralAvidRequest.requestType.eq(type))).orderBy(" mtime desc ").limit(1).fetchOne();

        Integer authTimeType = lauGeneralAvidRequestPo.getAuthTimeType();
        Timestamp authExpireTime = lauGeneralAvidRequestPo.getAuthExpireTime();
        if (GeneralVideoEnum.AuthTimeTypeEnum.NO_LIMIT.getCode().equals(authTimeType)) {
            authExpireTime = null;
        }

        List<GameBaseBo> gameBaseBos = gameInfoService.queryGames(
                GameApiRequest.builder().game_base_ids(String.valueOf(lauAccountGameMappingPo.getGameBaseId()))
                        .sdk_type(GameInfoService.SdkTypeEnum.ANDROID.getCode()).build());
        Assert.isTrue(CollectionUtils.isNotEmpty(gameBaseBos), "游戏不存在");

        GameBaseBo gameBaseBo = gameBaseBos.get(0);

        AccountBaseReply accountBase = cpmAdAccountProxy.getAccountBase(lauAccountGameMappingPo.getAccountId());
        AccountBase account = accountBase.getData();

        CustomerInfoDto customerInfoDto = CustomerInfoDto.builder().build();
        AgentDto agent = AgentDto.builder().build();
        UserInfoDto userInfoDto = UserInfoDto.builder().build();
        try {
            agent = crmPlatformProxy.iSoaAgentService().getAgentById(account.getDependencyAgentId());
            customerInfoDto = crmPlatformProxy.iSoaCustomerService().getCustomerDto(account.getCustomerId());
            userInfoDto = passportService.getUserByMid(lauAccountGameMappingPo.getAuthMid());
        } catch (Exception e) {
            log.error("error ", e);
        }

        return GameActivityAuthInfoVo.builder().gameBaseId(lauAccountGameMappingPo.getGameBaseId())
                .agentName(agent.getName()).customerName(customerInfoDto.getUsername()).mid(userInfoDto.getMid())
                .avatar(userInfoDto.getFace()).name(userInfoDto.getName())
                .authTimeType(lauAccountGameMappingPo.getAuthTimeType())
                .effectiveTime(lauGeneralAvidRequestPo.getAuthEffectiveTime()).expireTime(authExpireTime)
                .cover(gameBaseBo.getGameIcon()).gameIcon(gameBaseBo.getGameIcon()).gameName(gameBaseBo.getGameName())
                .build();
    }

    public void gameActivityCpAccept(Long authId, Long mid) {
        LauAccountGameMappingPo lauAccountGameMappingPo = adBqf.selectFrom(QLauAccountGameMapping.lauAccountGameMapping)
                .where(QLauAccountGameMapping.lauAccountGameMapping.id.eq(authId)
                        .and(QLauAccountGameMapping.lauAccountGameMapping.authMid.eq(mid))
                        .and(QLauAccountGameMapping.lauAccountGameMapping.authStatus.eq(
                                GeneralVideoEnum.AuthEnum.WAIT_ACCEPT.getCode())))
                .fetchOne(LauAccountGameMappingPo.class);

        LauAccountGameMappingPo renewLauAccountGameMappingPo = adBqf.selectFrom(
                        QLauAccountGameMapping.lauAccountGameMapping)
                .where(QLauAccountGameMapping.lauAccountGameMapping.id.eq(authId)
                        .and(QLauAccountGameMapping.lauAccountGameMapping.authMid.eq(mid))
                        .and(QLauAccountGameMapping.lauAccountGameMapping.renewalStatus.eq(
                                GeneralVideoEnum.RenewalEnum.WAIT_ACCEPT.getCode())))
                .fetchOne(LauAccountGameMappingPo.class);

        Assert.isTrue(null != lauAccountGameMappingPo || null != renewLauAccountGameMappingPo,
                "请确审核记录是否存在，或者审核状态是已审核或者已决绝");

        if (null != lauAccountGameMappingPo) {
            GeneralVideoAuthStatusEnum authStatusEnum = GeneralVideoAuthStatusEnum.CONFIRM_WAIT_ACTIVE;
            if (GeneralVideoEnum.AuthTimeTypeEnum.NO_LIMIT.getCode()
                    .equals(lauAccountGameMappingPo.getAuthTimeType()) || lauAccountGameMappingPo.getAuthEffectiveTime()
                    .compareTo(Utils.getNow()) <= 0) {
                authStatusEnum = GeneralVideoAuthStatusEnum.CONFIRM;
            }

            adBqf.update(QLauAccountGameMapping.lauAccountGameMapping)
                    .set(QLauAccountGameMapping.lauAccountGameMapping.authStatus, authStatusEnum.getCode())
                    .where(QLauAccountGameMapping.lauAccountGameMapping.id.eq(authId)
                            .and(QLauAccountGameMapping.lauAccountGameMapping.authMid.eq(mid))).execute();
        }

        if (null != renewLauAccountGameMappingPo) {
            LauGeneralAvidRequestPo requestPo = adBqf.selectFrom(lauGeneralAvidRequest)
                    .where(lauGeneralAvidRequest.mappingId.eq(renewLauAccountGameMappingPo.getId().intValue())
                            .and(lauGeneralAvidRequest.isDeleted.eq(IsDeleted.VALID.getCode()))
                            .and(lauGeneralAvidRequest.requestType.eq(
                                    GeneralVideoEnum.RequestEnum.GAME_CP_RENEWAL.getCode()))).orderBy(" mtime desc ")
                    .limit(1).fetchOne();

            adBqf.update(QLauAccountGameMapping.lauAccountGameMapping)
                    .set(QLauAccountGameMapping.lauAccountGameMapping.renewalStatus,
                            GeneralVideoEnum.RenewalEnum.CONFIRM.getCode())
                    .set(QLauAccountGameMapping.lauAccountGameMapping.authEffectiveTime,
                            requestPo.getAuthEffectiveTime())
                    .set(QLauAccountGameMapping.lauAccountGameMapping.authExpireTime, requestPo.getAuthExpireTime())
                    .where(QLauAccountGameMapping.lauAccountGameMapping.id.eq(authId)
                            .and(QLauAccountGameMapping.lauAccountGameMapping.authMid.eq(mid))).execute();
        }
    }

    public void gameActivityCpReject(Long authId, Long mid) {
        LauAccountGameMappingPo lauAccountGameMappingPo = adBqf.selectFrom(QLauAccountGameMapping.lauAccountGameMapping)
                .where(QLauAccountGameMapping.lauAccountGameMapping.id.eq(authId)
                        .and(QLauAccountGameMapping.lauAccountGameMapping.authMid.eq(mid))
                        .and(QLauAccountGameMapping.lauAccountGameMapping.authStatus.eq(
                                GeneralVideoEnum.AuthEnum.WAIT_ACCEPT.getCode())))
                .fetchOne(LauAccountGameMappingPo.class);

        LauAccountGameMappingPo renewLauAccountGameMappingPo = adBqf.selectFrom(
                        QLauAccountGameMapping.lauAccountGameMapping)
                .where(QLauAccountGameMapping.lauAccountGameMapping.id.eq(authId)
                        .and(QLauAccountGameMapping.lauAccountGameMapping.authMid.eq(mid))
                        .and(QLauAccountGameMapping.lauAccountGameMapping.renewalStatus.eq(
                                GeneralVideoEnum.RenewalEnum.WAIT_ACCEPT.getCode())))
                .fetchOne(LauAccountGameMappingPo.class);

        Assert.isTrue(null != lauAccountGameMappingPo || null != renewLauAccountGameMappingPo,
                "请确审核记录是否存在，或者审核状态是已审核或者已决绝");


        if (null != lauAccountGameMappingPo) {
            adBqf.update(QLauAccountGameMapping.lauAccountGameMapping)
                    .set(QLauAccountGameMapping.lauAccountGameMapping.authStatus,
                            GeneralVideoAuthStatusEnum.REJECT.getCode())
                    .where(QLauAccountGameMapping.lauAccountGameMapping.id.eq(authId)
                            .and(QLauAccountGameMapping.lauAccountGameMapping.authMid.eq(mid))).execute();
        }

        if (null != renewLauAccountGameMappingPo) {
            adBqf.update(QLauAccountGameMapping.lauAccountGameMapping)
                    .set(QLauAccountGameMapping.lauAccountGameMapping.renewalStatus,
                            GeneralVideoEnum.RenewalEnum.REJECTED.getCode())
                    .where(QLauAccountGameMapping.lauAccountGameMapping.id.eq(authId)
                            .and(QLauAccountGameMapping.lauAccountGameMapping.authMid.eq(mid))).execute();
        }
    }

    public GameActivityVideoAuthInfoVo gameActivityVideoInfo(Long authId, Long mid, Integer type) {
        LauAccountGameAvidMappingPo lauAccountGameAvidMappingPo = adBqf.selectFrom(
                        QLauAccountGameAvidMapping.lauAccountGameAvidMapping)
                .where(QLauAccountGameAvidMapping.lauAccountGameAvidMapping.id.eq(authId)
                        .and(QLauAccountGameAvidMapping.lauAccountGameAvidMapping.authMid.eq(mid)))
                .fetchOne(LauAccountGameAvidMappingPo.class);

        Assert.isTrue(null != lauAccountGameAvidMappingPo, "审核记录不存在");

        //获取最新审核记录
        LauGeneralAvidRequestPo lauGeneralAvidRequestPo = adBqf.selectFrom(lauGeneralAvidRequest)
                .where(lauGeneralAvidRequest.mappingId.eq(authId.intValue())
                        .and(lauGeneralAvidRequest.requestType.eq(type))).orderBy(" mtime desc ").limit(1).fetchOne();

        AccountBaseReply accountBase = cpmAdAccountProxy.getAccountBase(lauAccountGameAvidMappingPo.getAccountId());
        AccountBase account = accountBase.getData();

        CustomerInfoDto customerInfoDto = CustomerInfoDto.builder().build();
        AgentDto agent = AgentDto.builder().build();

        try {
            agent = crmPlatformProxy.iSoaAgentService().getAgentById(account.getDependencyAgentId());
            customerInfoDto = crmPlatformProxy.iSoaCustomerService().getCustomerDto(account.getCustomerId());
        } catch (Exception e) {
            log.error("error ", e);
        }

        Long avid = lauAccountGameAvidMappingPo.getAvid();

        Map<Long, Arc> arcMap = new HashMap<>();
        try {
            ArcsReply arcsReply = archiveServiceProxy.arcs(
                    ArcsRequest.newBuilder().addAids(lauAccountGameAvidMappingPo.getAvid()).build());
            arcMap = ((arcsReply == null) || (arcsReply.getArcsCount() == 0)) ? Collections.emptyMap() : arcsReply.getArcsMap();
        } catch (Exception e) {
            log.error("gameActivityVideoInfo get avid error " + lauAccountGameAvidMappingPo.getAvid(), e);
            throw new RuntimeException("获取稿件信息失败");
        }

        Arc arc = arcMap.getOrDefault(avid, Arc.newBuilder().build());
        String cover = StringUtils.isNotEmpty(arc.getPic()) ? arc.getPic().replace("http", "https") : StringUtils.EMPTY;


        Integer authTimeType = lauGeneralAvidRequestPo.getAuthTimeType();
        Timestamp authExpireTime = lauGeneralAvidRequestPo.getAuthExpireTime();
        if (GeneralVideoEnum.AuthTimeTypeEnum.NO_LIMIT.getCode().equals(authTimeType)) {
            authExpireTime = null;
        }

        Author author = null == arc.getAuthor() ? Author.newBuilder().build() : arc.getAuthor();
        return GameActivityVideoAuthInfoVo.builder().requestType(GeneralVideoEnum.RequestEnum.GAME_VIDEO_AUTH.getCode())
                .agentName(
                        Optional.ofNullable(agent).orElse(AgentDto.builder().name(StringUtils.EMPTY).build()).getName())
                .customerName(customerInfoDto.getUsername())
                .authEffectiveTime(lauGeneralAvidRequestPo.getAuthEffectiveTime()).authTimeType(authTimeType)
                .authExpireTime(authExpireTime).avid(avid).bvid(BVIDUtils.avToBv(avid)).cover(cover)
                .title(arc.getTitle()).mid(author.getMid()).name(author.getName()).avatar(author.getFace()).build();
    }

    public void gameActivityVideoAccept(Long authId, Long mid) {
        LauAccountGameMappingPo lauAccountGameMappingPo = adBqf.selectFrom(
                        QLauAccountGameAvidMapping.lauAccountGameAvidMapping)
                .where(QLauAccountGameAvidMapping.lauAccountGameAvidMapping.id.eq(authId)
                        .and(QLauAccountGameAvidMapping.lauAccountGameAvidMapping.authMid.eq(mid))
                        .and(QLauAccountGameAvidMapping.lauAccountGameAvidMapping.authStatus.eq(
                                GeneralVideoEnum.AuthEnum.WAIT_ACCEPT.getCode())))
                .fetchOne(LauAccountGameMappingPo.class);

        LauAccountGameMappingPo renewLauAccountGameMappingPo = adBqf.selectFrom(
                        QLauAccountGameAvidMapping.lauAccountGameAvidMapping)
                .where(QLauAccountGameAvidMapping.lauAccountGameAvidMapping.id.eq(authId)
                        .and(QLauAccountGameAvidMapping.lauAccountGameAvidMapping.authMid.eq(mid))
                        .and(QLauAccountGameAvidMapping.lauAccountGameAvidMapping.renewalStatus.eq(
                                GeneralVideoEnum.RenewalEnum.WAIT_ACCEPT.getCode())))
                .fetchOne(LauAccountGameMappingPo.class);

        Assert.isTrue(null != lauAccountGameMappingPo || null != renewLauAccountGameMappingPo,
                "请确审核记录是否存在，或者审核状态是已审核或者已决绝");

        if (null != lauAccountGameMappingPo) {
            GeneralVideoAuthStatusEnum authStatusEnum = GeneralVideoAuthStatusEnum.CONFIRM_WAIT_ACTIVE;
            if (GeneralVideoEnum.AuthTimeTypeEnum.NO_LIMIT.getCode()
                    .equals(lauAccountGameMappingPo.getAuthTimeType()) || lauAccountGameMappingPo.getAuthEffectiveTime()
                    .compareTo(Utils.getNow()) <= 0) {
                authStatusEnum = GeneralVideoAuthStatusEnum.CONFIRM;
            }

            adBqf.update(QLauAccountGameAvidMapping.lauAccountGameAvidMapping)
                    .set(QLauAccountGameAvidMapping.lauAccountGameAvidMapping.authStatus, authStatusEnum.getCode())
                    .where(QLauAccountGameAvidMapping.lauAccountGameAvidMapping.id.eq(authId)
                            .and(QLauAccountGameAvidMapping.lauAccountGameAvidMapping.authMid.eq(mid))).execute();
        }

        if (null != renewLauAccountGameMappingPo) {

            LauGeneralAvidRequestPo requestPo = adBqf.selectFrom(lauGeneralAvidRequest)
                    .where(lauGeneralAvidRequest.mappingId.eq(renewLauAccountGameMappingPo.getId().intValue())
                            .and(lauGeneralAvidRequest.isDeleted.eq(IsDeleted.VALID.getCode()))
                            .and(lauGeneralAvidRequest.requestType.eq(
                                    GeneralVideoEnum.RequestEnum.GAME_VIDEO_RENEWAL.getCode()))).orderBy(" mtime desc ")
                    .limit(1).fetchOne();

            adBqf.update(QLauAccountGameAvidMapping.lauAccountGameAvidMapping)
                    .set(QLauAccountGameAvidMapping.lauAccountGameAvidMapping.renewalStatus,
                            GeneralVideoEnum.RenewalEnum.CONFIRM.getCode())
                    .set(QLauAccountGameAvidMapping.lauAccountGameAvidMapping.authEffectiveTime,
                            requestPo.getAuthEffectiveTime())
                    .set(QLauAccountGameAvidMapping.lauAccountGameAvidMapping.authExpireTime,
                            requestPo.getAuthExpireTime())
                    .where(QLauAccountGameAvidMapping.lauAccountGameAvidMapping.id.eq(authId)
                            .and(QLauAccountGameAvidMapping.lauAccountGameAvidMapping.authMid.eq(mid))).execute();
        }
    }

    public void gameActivityVideoReject(Long authId, Long mid) {
        LauAccountGameMappingPo lauAccountGameMappingPo = adBqf.selectFrom(
                        QLauAccountGameAvidMapping.lauAccountGameAvidMapping)
                .where(QLauAccountGameAvidMapping.lauAccountGameAvidMapping.id.eq(authId)
                        .and(QLauAccountGameAvidMapping.lauAccountGameAvidMapping.authMid.eq(mid))
                        .and(QLauAccountGameAvidMapping.lauAccountGameAvidMapping.authStatus.eq(
                                GeneralVideoEnum.AuthEnum.WAIT_ACCEPT.getCode())))
                .fetchOne(LauAccountGameMappingPo.class);

        LauAccountGameMappingPo renewLauAccountGameMappingPo = adBqf.selectFrom(
                        QLauAccountGameAvidMapping.lauAccountGameAvidMapping)
                .where(QLauAccountGameAvidMapping.lauAccountGameAvidMapping.id.eq(authId)
                        .and(QLauAccountGameAvidMapping.lauAccountGameAvidMapping.authMid.eq(mid))
                        .and(QLauAccountGameAvidMapping.lauAccountGameAvidMapping.renewalStatus.eq(
                                GeneralVideoEnum.RenewalEnum.WAIT_ACCEPT.getCode())))
                .fetchOne(LauAccountGameMappingPo.class);

        Assert.isTrue(null != lauAccountGameMappingPo || null != renewLauAccountGameMappingPo,
                "请确审核记录是否存在，或者审核状态是已审核或者已决绝");


        if (null != lauAccountGameMappingPo) {
            adBqf.update(QLauAccountGameAvidMapping.lauAccountGameAvidMapping)
                    .set(QLauAccountGameAvidMapping.lauAccountGameAvidMapping.authStatus,
                            GeneralVideoAuthStatusEnum.REJECT.getCode())
                    .where(QLauAccountGameAvidMapping.lauAccountGameAvidMapping.id.eq(authId)
                            .and(QLauAccountGameAvidMapping.lauAccountGameAvidMapping.authMid.eq(mid))).execute();
        }

        if (null != renewLauAccountGameMappingPo) {
            adBqf.update(QLauAccountGameAvidMapping.lauAccountGameAvidMapping)
                    .set(QLauAccountGameAvidMapping.lauAccountGameAvidMapping.renewalStatus,
                            GeneralVideoEnum.RenewalEnum.REJECTED.getCode())
                    .where(QLauAccountGameAvidMapping.lauAccountGameAvidMapping.id.eq(authId)
                            .and(QLauAccountGameAvidMapping.lauAccountGameAvidMapping.authMid.eq(mid))).execute();
        }
    }

    @Transactional(value = AD_TM, rollbackFor = Throwable.class)
    public void cancelGameCp(Integer accountId, Long id) {
        LauAccountGameMappingPo lauAccountGameMappingPo = adBqf.selectFrom(QLauAccountGameMapping.lauAccountGameMapping)
                .where(QLauAccountGameMapping.lauAccountGameMapping.id.eq(id)
                        .and(QLauAccountGameMapping.lauAccountGameMapping.accountId.eq(accountId)))
                .fetchOne(LauAccountGameMappingPo.class);

        if (lauAccountGameMappingPo != null) {
            if (!GeneralVideoAuthStatusEnum.CONFIRM_CODE_SET.contains(lauAccountGameMappingPo.getAuthStatus())) {
                throw new RuntimeException("只能对已授权的绑定关系进行撤回操作");
            }

        } else {
            throw new RuntimeException("绑定关系不存在");
        }

        //更新关系表
        adBqf.update(QLauAccountGameMapping.lauAccountGameMapping)
                .set(QLauAccountGameMapping.lauAccountGameMapping.authStatus,
                        GeneralVideoAuthStatusEnum.CANCEL.getCode())
                .where(QLauAccountGameMapping.lauAccountGameMapping.id.eq(id)
                        .and(QLauAccountGameMapping.lauAccountGameMapping.accountId.eq(accountId))).execute();

        //授权表撤回-撤回之前所有申请
        adBqf.update(QLauGeneralAvidRequest.lauGeneralAvidRequest)
                .where(QLauGeneralAvidRequest.lauGeneralAvidRequest.mappingId.in(id.intValue()))
                .where(QLauGeneralAvidRequest.lauGeneralAvidRequest.isDeleted.eq(IsDeleted.VALID.getCode()))
                .set(QLauGeneralAvidRequest.lauGeneralAvidRequest.isDeleted, IsDeleted.DELETED.getCode()).execute();

    }

    @Transactional(value = AD_TM, rollbackFor = Throwable.class)
    public void renewalGameCp(Integer accountId, LauAccountGameMappingBo bo,
                              Integer requestType) throws ServiceException, SystemException {
        Long id = bo.getId();
        LauAccountGameMappingPo lauAccountGameMappingPo = adBqf.selectFrom(QLauAccountGameMapping.lauAccountGameMapping)
                .where(QLauAccountGameMapping.lauAccountGameMapping.id.eq(id)
                        .and(QLauAccountGameMapping.lauAccountGameMapping.accountId.eq(accountId)))
                .fetchOne(LauAccountGameMappingPo.class);

        if (GeneralVideoEnum.AuthTimeTypeEnum.NO_LIMIT.getCode().equals(bo.getAuthTimeType())) {
            bo.setAuthEffectiveTime(Utils.getToday());
            bo.setAuthExpireTime(Utils.getSomeYearAfter(Utils.getToday(), 10));
        }

        if (lauAccountGameMappingPo != null) {
            if (!GeneralVideoAuthStatusEnum.RENEWAL_CODE_SET.contains(lauAccountGameMappingPo.getAuthStatus())) {
                throw new RuntimeException("只能对授权待生效或授权中的绑定关系进行续期操作");
            }
            if (GeneralVideoEnum.AuthTimeTypeEnum.NO_LIMIT.getCode()
                    .equals(lauAccountGameMappingPo.getAuthTimeType())) {
                throw new RuntimeException("只能对非不限的授权进行续期操作");
            }

            Assert.isTrue(getCPRequestTimesByWeek(lauAccountGameMappingPo,
                            requestType) < generalVideoAuthService.getGENERAL_VIDEO_BIND_RENEWAL_TIMES(),
                    "7天内只能对同一账号发起" + generalVideoAuthService.getGENERAL_VIDEO_BIND_RENEWAL_TIMES() + "次续期");
            LauGeneralAvidRequestPo avidRequestPo = insertRequestLog(bo.getId().intValue(), requestType,
                    bo.getAuthTimeType(), bo.getAuthEffectiveTime(), bo.getAuthExpireTime());
            //
//            lauAccountGameMappingPo.setAuthTimeType(bo.getAuthTimeType());
//            lauAccountGameMappingPo.setAuthEffectiveTime(bo.getAuthEffectiveTime());
//            lauAccountGameMappingPo.setAuthExpireTime(bo.getAuthExpireTime());
            lauAccountGameMappingPo.setRenewalStatus(GeneralVideoEnum.RenewalEnum.WAIT_ACCEPT.getCode());

            adBqf.update(QLauAccountGameMapping.lauAccountGameMapping).updateWithNull(lauAccountGameMappingPo);
            //send message
            sendMessage(lauAccountGameMappingPo.getId().intValue(), lauAccountGameMappingPo.getAuthMid(),
                    "游戏CP授权续期申请", "您有一条游戏CP授权续期申请", GeneralVideoEnum.RequestEnum.GAME_CP_RENEWAL,
                    GAME_CP_BIND_SEND_MESSAGE_LINK);
        } else {
            throw new RuntimeException("绑定关系不存在");
        }
    }

    //同代理下7天内只允许申请3次
    private Long getCPRequestTimesByWeek(LauAccountGameMappingPo gameMappingPo, Integer requestType) {
        List<Long> ids = adBqf.select(QLauAccountGameMapping.lauAccountGameMapping.id)
                .from(QLauAccountGameMapping.lauAccountGameMapping)
                .where(QLauAccountGameMapping.lauAccountGameMapping.agentId.eq(gameMappingPo.getAgentId())
                        .and(QLauAccountGameMapping.lauAccountGameMapping.gameBaseId.eq(gameMappingPo.getGameBaseId())))
                .fetch();

        if (CollectionUtils.isEmpty(ids)) {
            return 0L;
        }

        Timestamp beginTime = Utils.getSomeDayBeforeToday(7);
        List<Integer> mappingIds = ids.stream().map(Long::intValue).collect(Collectors.toList());
        return adBqf.selectFrom(lauGeneralAvidRequest).whereIfNotEmpty(mappingIds, lauGeneralAvidRequest.mappingId::in)
                .where(lauGeneralAvidRequest.ctime.goe(beginTime))
                .where(lauGeneralAvidRequest.requestType.eq(requestType)).fetchCount();
    }

    public PageResult<GameActivityVideoAuthInfoVo> gameActiveVideoAuthList(Integer accountId, Long gameBaseId,
                                                                           Integer authStatus, Integer page,
                                                                           Integer size) {

        Long count = adBqf.selectFrom(QLauAccountGameAvidMapping.lauAccountGameAvidMapping)
                .where(QLauAccountGameAvidMapping.lauAccountGameAvidMapping.accountId.eq(accountId))
                .whereIfNotNull(authStatus, QLauAccountGameAvidMapping.lauAccountGameAvidMapping.authStatus::in)
                .whereIfNotNull(gameBaseId, QLauAccountGameAvidMapping.lauAccountGameAvidMapping.gameBaseId::eq)
                .fetchCount();

        List<LauAccountGameAvidMappingPo> accountGameAvidMappingPos = adBqf.selectFrom(
                        QLauAccountGameAvidMapping.lauAccountGameAvidMapping)
                .where(QLauAccountGameAvidMapping.lauAccountGameAvidMapping.accountId.eq(accountId))
                .whereIfNotNull(authStatus, QLauAccountGameAvidMapping.lauAccountGameAvidMapping.authStatus::in)
                .whereIfNotNull(gameBaseId, QLauAccountGameAvidMapping.lauAccountGameAvidMapping.gameBaseId::eq)
                .orderBy("mtime desc").offset((long) (page - 1) * size).limit(size).fetch();

        Map<Long, Arc> arcReplyControlledMap = new HashMap<>();
        List<Long> avidList = accountGameAvidMappingPos.stream().map(LauAccountGameAvidMappingPo::getAvid).distinct()
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(avidList)) {
            ArcsReply arcsReply = archiveServiceProxy.arcs(ArcsRequest.newBuilder().addAllAids(avidList).build());
            arcReplyControlledMap = ((arcsReply == null) || (arcsReply.getArcsCount() == 0)) ? Collections.emptyMap() : arcsReply.getArcsMap();
        }

        Map<Long, Arc> finalArcReplyControlledMap = arcReplyControlledMap;
        List<GameActivityVideoAuthInfoVo> gameActivityVideoAuthInfoVos = accountGameAvidMappingPos.stream().map(r -> {
            Arc arc = finalArcReplyControlledMap.getOrDefault(r.getAvid(), Arc.newBuilder().build());
            String cover = StringUtils.isNotEmpty(arc.getPic()) ? arc.getPic()
                    .replace("http", "https") : StringUtils.EMPTY;
            //稿件状态正常 && 非限流状态 = 支持投放
            boolean isSupportAdvertising = VALID_VIDEO_STATES.contains(arc.getState());

            GameActivityVideoAuthInfoVo vo = GameActivityVideoAuthInfoVo.builder().id(r.getId()).avid(r.getAvid())
                    .authTimeType(r.getAuthTimeType()).bvid(BVIDUtils.avToBv(r.getAvid())).cover(cover)
                    .title(arc.getTitle()).duration(arc.getDuration()).state(arc.getState()).desc(arc.getDesc())
                    .authStatus(r.getAuthStatus())
                    .authStateDesc(GeneralVideoAuthStatusEnum.getByCode(r.getAuthStatus()).getName())
                    .renewalState(r.getRenewalStatus()).renewalStateDesc(
                            Utils.isPositive(r.getRenewalStatus()) ? GeneralVideoEnum.RenewalEnum.getByCode(
                                    r.getRenewalStatus()).getName() : null).isSupportAdvertising(isSupportAdvertising)
                    .authEffectiveTime(r.getAuthEffectiveTime()).authExpireTime(r.getAuthExpireTime()).build();
            if (null != arc.getDimension()) {
                vo.setWidth(arc.getDimension().getWidth());
                vo.setHeight(arc.getDimension().getHeight());
            }

            if (null != arc.getAuthor()) {
                vo.setMid(arc.getAuthor().getMid());
                vo.setName(arc.getAuthor().getName());
            }
            return vo;
        }).collect(Collectors.toList());

        return new PageResult(count.intValue(), gameActivityVideoAuthInfoVos);
    }

    @Transactional(value = AD_TM, rollbackFor = Throwable.class)
    public void bindGameVideoBatch(LauAccountGameAvidMappingBo bo) throws ServiceException, SystemException {

        AccountBaseReply accountBase = cpmAdAccountProxy.getAccountBase(bo.getAccountId());
        AgentDto agent = crmPlatformProxy.iSoaAgentService().getAgentById(accountBase.getData().getDependencyAgentId());
        bo.setAgentId(agent.getSysAgentId());
        bo.setAuthMid(gameInfoService.queryMid(bo.getGameBaseId()));

        for (Long avid : bo.getAvids()) {
            bindGameVideo(bo, avid);
        }
    }

    @Transactional(value = AD_TM, rollbackFor = Throwable.class)
    public void bindGameVideo(LauAccountGameAvidMappingBo bo, Long avid) throws ServiceException, SystemException {

        if (GeneralVideoEnum.AuthTimeTypeEnum.NO_LIMIT.getCode().equals(bo.getAuthTimeType())) {
            bo.setAuthEffectiveTime(Utils.getToday());
            bo.setAuthExpireTime(Utils.getSomeYearAfter(Utils.getToday(), 10));
        }

        LauAccountGameAvidMappingPo po = new LauAccountGameAvidMappingPo();
        BeanUtils.copyProperties(bo, po);
        po.setAvid(avid);
        LauAccountGameAvidMappingPo LauAccountGameAvidMappingPo = adBqf.selectFrom(
                        QLauAccountGameAvidMapping.lauAccountGameAvidMapping)
                .where(QLauAccountGameAvidMapping.lauAccountGameAvidMapping.accountId.eq(po.getAccountId())
                        .and(QLauAccountGameAvidMapping.lauAccountGameAvidMapping.avid.eq(po.getAvid())))
                .fetchOne(LauAccountGameAvidMappingPo.class);

        po.setAuthStatus(GeneralVideoEnum.AuthEnum.WAIT_ACCEPT.getCode());
        if (null == LauAccountGameAvidMappingPo) {
            Long key = adBqf.insert(QLauAccountGameAvidMapping.lauAccountGameAvidMapping).insertGetKey(po);
            po.setId(key);
            insertRequestLog(key.intValue(), GeneralVideoEnum.RequestEnum.GAME_VIDEO_AUTH.getCode(),
                    bo.getAuthTimeType(), bo.getAuthEffectiveTime(), bo.getAuthExpireTime());
        } else {
            adBqf.update(QLauAccountGameAvidMapping.lauAccountGameAvidMapping)
                    .set(QLauAccountGameAvidMapping.lauAccountGameAvidMapping.authEffectiveTime,
                            po.getAuthEffectiveTime())
                    .set(QLauAccountGameAvidMapping.lauAccountGameAvidMapping.authExpireTime, po.getAuthExpireTime())
                    .set(QLauAccountGameAvidMapping.lauAccountGameAvidMapping.isDeleted, IsDeleted.VALID.getCode())
                    .set(QLauAccountGameAvidMapping.lauAccountGameAvidMapping.authMid, po.getAuthMid())
                    .set(QLauAccountGameAvidMapping.lauAccountGameAvidMapping.agentId, po.getAgentId())
                    .set(QLauAccountGameAvidMapping.lauAccountGameAvidMapping.authStatus, po.getAuthStatus())
                    .set(QLauAccountGameAvidMapping.lauAccountGameAvidMapping.avid, po.getAvid())
                    .set(QLauAccountGameAvidMapping.lauAccountGameAvidMapping.gameBaseId, po.getGameBaseId())
                    .where(QLauAccountGameAvidMapping.lauAccountGameAvidMapping.accountId.eq(po.getAccountId())
                            .and(QLauAccountGameAvidMapping.lauAccountGameAvidMapping.avid.eq(po.getAvid()))).execute();
            insertRequestLog(po.getId().intValue(), GeneralVideoEnum.RequestEnum.GAME_VIDEO_AUTH.getCode(),
                    bo.getAuthTimeType(), bo.getAuthEffectiveTime(), bo.getAuthExpireTime());
        }

        AccountBaseReply accountBase = cpmAdAccountProxy.getAccountBase(bo.getAccountId());
        AccountBase account = accountBase.getData();

        CustomerInfoDto customerInfoDto = CustomerInfoDto.builder().build();
        AgentDto agent = AgentDto.builder().build();
        try {
            agent = crmPlatformProxy.iSoaAgentService().getAgentById(account.getDependencyAgentId());
            customerInfoDto = crmPlatformProxy.iSoaCustomerService().getCustomerDto(account.getCustomerId());
        } catch (Exception e) {
            log.error("error ", e);
        }

        //sendmessgae
        sendMessage(po.getId().intValue(), po.getAuthMid(), "游戏视频授权申请",
                "您有一条来自" + agent.getName() + "-" + customerInfoDto.getUsername() + "的视频授权申请",
                GeneralVideoEnum.RequestEnum.GAME_VIDEO_AUTH, GAME_VIDEO_BIND_SEND_MESSAGE_LINK);

    }

    @Transactional(value = AD_TM, rollbackFor = Throwable.class)
    public void cancelGameVideo(Integer accountId, Long id) {
        LauAccountGameAvidMappingPo lauAccountGameAvidMappingPo = adBqf.selectFrom(
                        QLauAccountGameAvidMapping.lauAccountGameAvidMapping)
                .where(QLauAccountGameAvidMapping.lauAccountGameAvidMapping.id.eq(id)
                        .and(QLauAccountGameAvidMapping.lauAccountGameAvidMapping.accountId.eq(accountId)))
                .fetchOne(LauAccountGameAvidMappingPo.class);

        if (lauAccountGameAvidMappingPo != null) {
            if (!GeneralVideoAuthStatusEnum.CONFIRM_CODE_SET.contains(lauAccountGameAvidMappingPo.getAuthStatus())) {
                throw new RuntimeException("只能对已授权的绑定关系进行撤回操作");
            }

        } else {
            throw new RuntimeException("绑定关系不存在");
        }

        //更新关系表
        adBqf.update(QLauAccountGameAvidMapping.lauAccountGameAvidMapping)
                .set(QLauAccountGameAvidMapping.lauAccountGameAvidMapping.authStatus,
                        GeneralVideoAuthStatusEnum.CANCEL.getCode())
                .where(QLauAccountGameAvidMapping.lauAccountGameAvidMapping.id.eq(id)
                        .and(QLauAccountGameAvidMapping.lauAccountGameAvidMapping.accountId.eq(accountId))).execute();

        //授权表撤回-撤回之前所有申请
        adBqf.update(QLauGeneralAvidRequest.lauGeneralAvidRequest)
                .where(QLauGeneralAvidRequest.lauGeneralAvidRequest.mappingId.in(id.intValue()))
                .where(QLauGeneralAvidRequest.lauGeneralAvidRequest.isDeleted.eq(IsDeleted.VALID.getCode()))
                .set(QLauGeneralAvidRequest.lauGeneralAvidRequest.isDeleted, IsDeleted.DELETED.getCode()).execute();
    }

    public void renewalGameVideo(Integer accountId, LauAccountGameAvidMappingBo bo,
                                 Integer requestType) throws ServiceException, SystemException {
        Long id = bo.getId();
        LauAccountGameAvidMappingPo lauAccountGameAvidMappingPo = adBqf.selectFrom(
                        QLauAccountGameAvidMapping.lauAccountGameAvidMapping)
                .where(QLauAccountGameAvidMapping.lauAccountGameAvidMapping.id.eq(id)
                        .and(QLauAccountGameAvidMapping.lauAccountGameAvidMapping.accountId.eq(accountId)))
                .fetchOne(LauAccountGameAvidMappingPo.class);

        if (GeneralVideoEnum.AuthTimeTypeEnum.NO_LIMIT.getCode().equals(bo.getAuthTimeType())) {
            bo.setAuthEffectiveTime(Utils.getToday());
            bo.setAuthExpireTime(Utils.getSomeYearAfter(Utils.getToday(), 10));
        }

        if (lauAccountGameAvidMappingPo != null) {
            if (!GeneralVideoAuthStatusEnum.RENEWAL_CODE_SET.contains(lauAccountGameAvidMappingPo.getAuthStatus())) {
                throw new RuntimeException("只能对授权待生效或授权中的绑定关系进行续期操作");
            }
            if (GeneralVideoEnum.AuthTimeTypeEnum.NO_LIMIT.getCode()
                    .equals(lauAccountGameAvidMappingPo.getAuthTimeType())) {
                throw new RuntimeException("只能对非不限的授权进行续期操作");
            }

            LauGeneralAvidRequestPo avidRequestPo = insertRequestLog(bo.getId().intValue(), requestType,
                    bo.getAuthTimeType(), bo.getAuthEffectiveTime(), bo.getAuthExpireTime());
            //
            lauAccountGameAvidMappingPo.setRenewalStatus(GeneralVideoEnum.RenewalEnum.WAIT_ACCEPT.getCode());

            adBqf.update(QLauAccountGameAvidMapping.lauAccountGameAvidMapping)
                    .updateWithNull(lauAccountGameAvidMappingPo);

            AccountBaseReply accountBase = cpmAdAccountProxy.getAccountBase(bo.getAccountId());
            AccountBase account = accountBase.getData();

            CustomerInfoDto customerInfoDto = CustomerInfoDto.builder().build();
            AgentDto agent = AgentDto.builder().build();
            UserInfoDto userInfoDto = UserInfoDto.builder().build();
            try {
                agent = crmPlatformProxy.iSoaAgentService().getAgentById(account.getDependencyAgentId());
                customerInfoDto = crmPlatformProxy.iSoaCustomerService().getCustomerDto(account.getCustomerId());
            } catch (Exception e) {
                log.error("error ", e);
            }

            //send message
            sendMessage(lauAccountGameAvidMappingPo.getId().intValue(), lauAccountGameAvidMappingPo.getAuthMid(),
                    "游戏视频授权续期申请",
                    "您有一条来自" + agent.getName() + "-" + customerInfoDto.getUsername() + "的视频授权续期申请",
                    GeneralVideoEnum.RequestEnum.GAME_VIDEO_RENEWAL, GAME_VIDEO_BIND_SEND_MESSAGE_LINK);
        } else {
            throw new RuntimeException("绑定关系不存在");
        }
    }

    public PageResult<GameActivityVideoBo> gameActiveVideoList(GameActivityVideoRequest request) {
        AccountBaseReply accountBase = cpmAdAccountProxy.getAccountBase(request.getAccountId());
        //game base id 权限校验
        BaseQuery<LauAccountGameMappingPo> bq = adBqf.selectFrom(QLauAccountGameMapping.lauAccountGameMapping)
                .where(QLauAccountGameMapping.lauAccountGameMapping.gameBaseId.eq(request.getGameBaseId())
                        .and(QLauAccountGameMapping.lauAccountGameMapping.authStatus.in(
                                Lists.newArrayList(GeneralVideoAuthStatusEnum.CONFIRM.getCode())))
                        .and(QLauAccountGameMapping.lauAccountGameMapping.isDeleted.eq(IsDeleted.VALID.getCode()))
                        .and(QLauAccountGameMapping.lauAccountGameMapping.authExpireTime.after(
                                new Timestamp(System.currentTimeMillis())))
                        .and(QLauAccountGameMapping.lauAccountGameMapping.authEffectiveTime.before(
                                new Timestamp(System.currentTimeMillis()))));


        if (accountBase.getData().getDependencyAgentId() == 0) {
            bq.where(QLauAccountGameMapping.lauAccountGameMapping.accountId.eq(request.getAccountId()));
        } else {
            bq.where(QLauAccountGameMapping.lauAccountGameMapping.accountId.eq(request.getAccountId())
                    .or(QLauAccountGameMapping.lauAccountGameMapping.agentId.eq(
                            accountBase.getData().getDependencyAgentId())));
        }
        LauAccountGameMappingPo lauAccountGameMappingPo = bq.fetchOne(LauAccountGameMappingPo.class);
        Assert.isTrue(null != lauAccountGameMappingPo, "你无权操作该游戏，请先通过CP授权");

        //拉取视屏   https://info.bilibili.co/pages/viewpage.action?pageId=*********#id-1%E3%80%81%E6%B8%B8%E6%88%8F%E4%BF%A1%E6%81%AF%E6%8E%A5%E5%8F%A3-14.%E8%8E%B7%E5%8F%96%E6%B4%BB%E5%8A%A8%E7%A8%BF%E4%BB%B6
        Map<String, Object> param = new HashMap<>();
        param.put("game_base_id", request.getGameBaseId());
        param.put("avid", request.getAvid());
        param.put("vv_order", request.getVvOrderType());
        param.put("pub_start_time", request.getStartTime());
        param.put("pub_end_time", request.getEndTime());
        param.put("external_act_status", request.getExternalActStatus());
        param.put("page_num", request.getPage());
        param.put("page_size", request.getPageSize());
        GameInfoService.GameApiReplyWithPageV2<GameActivityVideoBo> gameApiReplyWithPage = gameInfoService.queryGamesByParam(
                param,
                new TypeToken<GameInfoService.GameApiCommonReplay<GameInfoService.GameApiReplyWithPageV2<GameActivityVideoBo>>>() {
                }, gameInfoService.getGameOpenApiUrl() + "/material_center/get_act_materials");

        buildVideoInfo(gameApiReplyWithPage);
        return new PageResult<>(gameApiReplyWithPage.getTotal_count(), gameApiReplyWithPage.getList());
    }

    private void buildVideoInfo(GameInfoService.GameApiReplyWithPageV2<GameActivityVideoBo> gameApiReplyWithPage) {
        List<Long> avids = gameApiReplyWithPage.getList().stream().map(GameActivityVideoBo::getAvid)
                .collect(Collectors.toList());
        Map<Long, ArchiveBo> archiveBoMap = archiveService.getArcsByAids(avids);
        for (GameActivityVideoBo videoBo : gameApiReplyWithPage.getList()) {
            ArchiveBo archiveBo = archiveBoMap.getOrDefault(videoBo.getAvid(), ArchiveBo.builder().build());
            videoBo.setCover(archiveBo.getPic());
            videoBo.setCopyright(archiveBo.getCopyright());
            videoBo.setDesc(archiveBo.getDesc());
            videoBo.setTitle(archiveBo.getTitle());
            if (null != archiveBo.getAuthor()) {
                videoBo.setMid(archiveBo.getAuthor().getMid());
                videoBo.setName(archiveBo.getAuthor().getName());
            }
            if (null != archiveBo.getDimension()) {
                videoBo.setWidth(archiveBo.getDimension().getWidth().intValue());
                videoBo.setHeight(archiveBo.getDimension().getHeight().intValue());
            }
            videoBo.setState(archiveBo.getState());
            videoBo.setStateDesc(archiveBo.getStateDesc());
            videoBo.setDuration(archiveBo.getDuration());
        }
    }

    public void refreshAuthStatus() {
        refreshCPAuthStatus();
        refreshVideoAuthStatus();
    }

    private void refreshCPAuthStatus() {

        Timestamp currentDay = Utils.getNow();
        Long lastMaxId = 0L;
        int batchSize = 1000;

        while (true) {
            List<LauAccountGameMappingPo> accountGameMappingPos = adBqf.selectFrom(
                            QLauAccountGameMapping.lauAccountGameMapping)
                    .where(QLauAccountGameMapping.lauAccountGameMapping.id.gt(lastMaxId)
                            .and(QLauAccountGameMapping.lauAccountGameMapping.authStatus.in(
                                    GeneralVideoAuthStatusEnum.REFRESH_CODE_LIST))
                            .and(QLauAccountGameMapping.lauAccountGameMapping.isDeleted.eq(IsDeleted.VALID.getCode())))
                    .orderBy(QLauAccountGameMapping.lauAccountGameMapping.id.asc()).limit(batchSize).fetch();

            if (CollectionUtils.isEmpty(accountGameMappingPos)) {
                break;
            }
            lastMaxId = accountGameMappingPos.get(accountGameMappingPos.size() - 1).getId();

            for (LauAccountGameMappingPo po : accountGameMappingPos) {

                try {
                    Integer authStatus = po.getAuthStatus();

                    //视频待生效刷新
                    if (GeneralVideoAuthStatusEnum.CONFIRM_WAIT_ACTIVE.getCode().equals(authStatus)) {
                        if (currentDay.after(po.getAuthEffectiveTime())) {
                            adBqf.update(QLauAccountGameMapping.lauAccountGameMapping)
                                    .set(QLauAccountGameMapping.lauAccountGameMapping.authStatus,
                                            GeneralVideoAuthStatusEnum.CONFIRM.getCode())
                                    .where(QLauAccountGameMapping.lauAccountGameMapping.id.eq(po.getId())).execute();
                        }
                    }

                    //视频过期下线
                    if (GeneralVideoAuthStatusEnum.CONFIRM.getCode().equals(authStatus)) {
                        if (currentDay.after(po.getAuthExpireTime())) {
                            adBqf.update(QLauAccountGameMapping.lauAccountGameMapping)
                                    .set(QLauAccountGameMapping.lauAccountGameMapping.authStatus,
                                            GeneralVideoAuthStatusEnum.INVALID.getCode())
                                    .where(QLauAccountGameMapping.lauAccountGameMapping.id.eq(po.getId())).execute();
                        }

                    }
                } catch (Exception e) {
                    log.info("error", e);
                }
            }
        }

    }

    private void refreshVideoAuthStatus() {
        Timestamp currentDay = Utils.getNow();
        Long lastMaxId = 0L;
        int batchSize = 1000;

        while (true) {
            List<LauAccountGameAvidMappingPo> accountGameAvidMappingPos = adBqf.selectFrom(
                            QLauAccountGameAvidMapping.lauAccountGameAvidMapping)
                    .where(QLauAccountGameAvidMapping.lauAccountGameAvidMapping.id.gt(lastMaxId)
                            .and(QLauAccountGameAvidMapping.lauAccountGameAvidMapping.authStatus.in(
                                    GeneralVideoAuthStatusEnum.REFRESH_CODE_LIST))
                            .and(QLauAccountGameAvidMapping.lauAccountGameAvidMapping.isDeleted.eq(
                                    IsDeleted.VALID.getCode())))
                    .orderBy(QLauAccountGameAvidMapping.lauAccountGameAvidMapping.id.asc()).limit(batchSize).fetch();

            if (CollectionUtils.isEmpty(accountGameAvidMappingPos)) {
                break;
            }
            lastMaxId = accountGameAvidMappingPos.get(accountGameAvidMappingPos.size() - 1).getId();

            for (LauAccountGameAvidMappingPo po : accountGameAvidMappingPos) {

                try {
                    Integer authStatus = po.getAuthStatus();

                    //视频待生效刷新
                    if (GeneralVideoAuthStatusEnum.CONFIRM_WAIT_ACTIVE.getCode().equals(authStatus)) {
                        if (currentDay.after(po.getAuthEffectiveTime())) {
                            adBqf.update(QLauAccountGameAvidMapping.lauAccountGameAvidMapping)
                                    .set(QLauAccountGameAvidMapping.lauAccountGameAvidMapping.authStatus,
                                            GeneralVideoAuthStatusEnum.CONFIRM.getCode())
                                    .where(QLauAccountGameAvidMapping.lauAccountGameAvidMapping.id.eq(po.getId()))
                                    .execute();
                        }
                    }

                    //视频过期下线
                    if (GeneralVideoAuthStatusEnum.CONFIRM.getCode().equals(authStatus)) {
                        if (currentDay.after(po.getAuthExpireTime())) {
                            adBqf.update(QLauAccountGameAvidMapping.lauAccountGameAvidMapping)
                                    .set(QLauAccountGameAvidMapping.lauAccountGameAvidMapping.authStatus,
                                            GeneralVideoAuthStatusEnum.INVALID.getCode())
                                    .where(QLauAccountGameAvidMapping.lauAccountGameAvidMapping.id.eq(po.getId()))
                                    .execute();
                        }

                    }
                } catch (Exception e) {
                    log.info("error", e);
                }
            }
        }

    }

    public GeneralVideoInfoVo gameCPActiveBindState(Integer accountId, Long gameId,
                                                    Integer requestType) throws ServiceException {

        GeneralVideoInfoVo generalVideoInfoVo = new GeneralVideoInfoVo();

        LauAccountGameMappingPo gameMappingPo = adBqf.selectFrom(QLauAccountGameMapping.lauAccountGameMapping)
                .where(QLauAccountGameMapping.lauAccountGameMapping.accountId.eq(accountId)
                        .and(QLauAccountGameMapping.lauAccountGameMapping.gameBaseId.eq(gameId))).fetchOne();

        if (gameMappingPo != null) {

            //授权校验
            if (GeneralVideoEnum.RequestEnum.AUTH.getCode()
                    .equals(requestType) && GeneralVideoAuthStatusEnum.CONFIRM_CODE_SET.contains(
                    gameMappingPo.getAuthStatus())) {

                throw new ServiceException(400003, "该视频已经授权，不支持二次授权");
            }

            generalVideoInfoVo.setAuthRequestTimes(getCPRequestTimesByWeek(gameMappingPo, requestType));
        }
        return generalVideoInfoVo;
    }

    public GeneralVideoInfoVo gameActiveVideoBindState(Integer accountId, long avid, Integer requestType,
                                                       String bvid) throws ServiceException {
        GeneralVideoInfoVo generalVideoInfoVo = new GeneralVideoInfoVo();

        buildGameVideoInfo(accountId, avid, bvid, generalVideoInfoVo);

        LauAccountGameAvidMappingPo gameAvidMappingPo = adBqf.selectFrom(
                        QLauAccountGameAvidMapping.lauAccountGameAvidMapping)
                .where(QLauAccountGameAvidMapping.lauAccountGameAvidMapping.accountId.eq(accountId)
                        .and(QLauAccountGameAvidMapping.lauAccountGameAvidMapping.avid.eq(avid))).fetchOne();

        if (gameAvidMappingPo != null) {

            //授权校验
            if (GeneralVideoEnum.RequestEnum.AUTH.getCode()
                    .equals(requestType) && GeneralVideoAuthStatusEnum.CONFIRM_CODE_SET.contains(
                    gameAvidMappingPo.getAuthStatus())) {

                throw new ServiceException(400003, "该视频已经授权，不支持二次授权");
            }

//            generalVideoInfoVo.setAuthRequestTimes(generalVideoAuthDaoService.getRequestTimesByWeek(gameAvidMappingPo.getId().intValue(), requestType));
        }
        return generalVideoInfoVo;
    }

    private void buildGameVideoInfo(Integer accountId, long avid, String bvid,
                                    GeneralVideoInfoVo generalVideoInfoVo) throws ServiceException {
        generalVideoInfoVo.setAvid(avid);
        generalVideoInfoVo.setBvid(bvid);
        ArcReply arcReply = null;
        try {
            //arcReply = grpcManager.getArcReply(ArcRequest.newBuilder().setAid(avid).build());
            arcReply = archiveServiceProxy.arc(avid);
        } catch (Exception e) {
            throw new ServiceException("获取稿件信息失败");
        }
        Arc arc = arcReply.getArc();


        //稿件状态异常 || 限流状态 不支持绑定
        generalVideoInfoVo.setIsSupportAdvertising(true);
        if (!VALID_VIDEO_STATES.contains(arc.getState())) {
//                throw new ServiceException(400002,"该内容暂不支持投放");
            generalVideoInfoVo.setIsSupportAdvertising(false);
        }
        //商单限制
        if (arcSupportAdvertisingService.isOrderVideo(accountId, avid)) {
            throw new ServiceException(400001, "普通视频授权仅支持非花火商单的普通视频");
        }

        String cover = StringUtils.isNotEmpty(arc.getPic()) ? arc.getPic().replace("http", "https") : StringUtils.EMPTY;
        generalVideoInfoVo.setCover(cover);
        generalVideoInfoVo.setTitle(arc.getTitle());

        Rights rights = arc.getRights();
        //联合投稿 0=否 1=是
        if (rights != null && 1 == rights.getIsCooperation()) {
            generalVideoInfoVo.setIsCooperation(true);
            List<GeneralMidInfoVo> midInfoVoList = new ArrayList<>();
            midInfoVoList.add(GeneralMidInfoVo.builder().mid(arc.getAuthor().getMid()).face(arc.getAuthor().getFace())
                    .name(arc.getAuthor().getName()).build());
            //mid封装
            int staffInfoCount = arc.getStaffInfoCount();
            for (int i = 0; i < staffInfoCount; i++) {
                StaffInfo staffInfo = arc.getStaffInfo(i);
                long mid = staffInfo.getMid();
                ProfileDto profile = profileInfoService.getProfileByMid(mid);
                Assert.isTrue(profile.isValid(), "视频对应的MID账户不合法");
                midInfoVoList.add(
                        GeneralMidInfoVo.builder().mid(mid).face(profile.getFace()).name(profile.getName()).build());
            }
            generalVideoInfoVo.setMidInfoVoList(midInfoVoList);
        } else {
            generalVideoInfoVo.setIsCooperation(false);
            generalVideoInfoVo.setMidInfoVoList(Collections.singletonList(
                    GeneralMidInfoVo.builder().mid(arc.getAuthor().getMid()).face(arc.getAuthor().getFace())
                            .name(arc.getAuthor().getName()).build()));
        }
    }


}
