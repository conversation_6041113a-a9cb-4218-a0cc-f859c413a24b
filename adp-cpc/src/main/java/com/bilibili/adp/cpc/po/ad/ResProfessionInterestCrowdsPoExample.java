package com.bilibili.adp.cpc.po.ad;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public class ResProfessionInterestCrowdsPoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public ResProfessionInterestCrowdsPoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andCrowdIdIsNull() {
            addCriterion("crowd_id is null");
            return (Criteria) this;
        }

        public Criteria andCrowdIdIsNotNull() {
            addCriterion("crowd_id is not null");
            return (Criteria) this;
        }

        public Criteria andCrowdIdEqualTo(Integer value) {
            addCriterion("crowd_id =", value, "crowdId");
            return (Criteria) this;
        }

        public Criteria andCrowdIdNotEqualTo(Integer value) {
            addCriterion("crowd_id <>", value, "crowdId");
            return (Criteria) this;
        }

        public Criteria andCrowdIdGreaterThan(Integer value) {
            addCriterion("crowd_id >", value, "crowdId");
            return (Criteria) this;
        }

        public Criteria andCrowdIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("crowd_id >=", value, "crowdId");
            return (Criteria) this;
        }

        public Criteria andCrowdIdLessThan(Integer value) {
            addCriterion("crowd_id <", value, "crowdId");
            return (Criteria) this;
        }

        public Criteria andCrowdIdLessThanOrEqualTo(Integer value) {
            addCriterion("crowd_id <=", value, "crowdId");
            return (Criteria) this;
        }

        public Criteria andCrowdIdIn(List<Integer> values) {
            addCriterion("crowd_id in", values, "crowdId");
            return (Criteria) this;
        }

        public Criteria andCrowdIdNotIn(List<Integer> values) {
            addCriterion("crowd_id not in", values, "crowdId");
            return (Criteria) this;
        }

        public Criteria andCrowdIdBetween(Integer value1, Integer value2) {
            addCriterion("crowd_id between", value1, value2, "crowdId");
            return (Criteria) this;
        }

        public Criteria andCrowdIdNotBetween(Integer value1, Integer value2) {
            addCriterion("crowd_id not between", value1, value2, "crowdId");
            return (Criteria) this;
        }

        public Criteria andLevelOneIdIsNull() {
            addCriterion("level_one_id is null");
            return (Criteria) this;
        }

        public Criteria andLevelOneIdIsNotNull() {
            addCriterion("level_one_id is not null");
            return (Criteria) this;
        }

        public Criteria andLevelOneIdEqualTo(Integer value) {
            addCriterion("level_one_id =", value, "levelOneId");
            return (Criteria) this;
        }

        public Criteria andLevelOneIdNotEqualTo(Integer value) {
            addCriterion("level_one_id <>", value, "levelOneId");
            return (Criteria) this;
        }

        public Criteria andLevelOneIdGreaterThan(Integer value) {
            addCriterion("level_one_id >", value, "levelOneId");
            return (Criteria) this;
        }

        public Criteria andLevelOneIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("level_one_id >=", value, "levelOneId");
            return (Criteria) this;
        }

        public Criteria andLevelOneIdLessThan(Integer value) {
            addCriterion("level_one_id <", value, "levelOneId");
            return (Criteria) this;
        }

        public Criteria andLevelOneIdLessThanOrEqualTo(Integer value) {
            addCriterion("level_one_id <=", value, "levelOneId");
            return (Criteria) this;
        }

        public Criteria andLevelOneIdIn(List<Integer> values) {
            addCriterion("level_one_id in", values, "levelOneId");
            return (Criteria) this;
        }

        public Criteria andLevelOneIdNotIn(List<Integer> values) {
            addCriterion("level_one_id not in", values, "levelOneId");
            return (Criteria) this;
        }

        public Criteria andLevelOneIdBetween(Integer value1, Integer value2) {
            addCriterion("level_one_id between", value1, value2, "levelOneId");
            return (Criteria) this;
        }

        public Criteria andLevelOneIdNotBetween(Integer value1, Integer value2) {
            addCriterion("level_one_id not between", value1, value2, "levelOneId");
            return (Criteria) this;
        }

        public Criteria andLevelOneNameIsNull() {
            addCriterion("level_one_name is null");
            return (Criteria) this;
        }

        public Criteria andLevelOneNameIsNotNull() {
            addCriterion("level_one_name is not null");
            return (Criteria) this;
        }

        public Criteria andLevelOneNameEqualTo(String value) {
            addCriterion("level_one_name =", value, "levelOneName");
            return (Criteria) this;
        }

        public Criteria andLevelOneNameNotEqualTo(String value) {
            addCriterion("level_one_name <>", value, "levelOneName");
            return (Criteria) this;
        }

        public Criteria andLevelOneNameGreaterThan(String value) {
            addCriterion("level_one_name >", value, "levelOneName");
            return (Criteria) this;
        }

        public Criteria andLevelOneNameGreaterThanOrEqualTo(String value) {
            addCriterion("level_one_name >=", value, "levelOneName");
            return (Criteria) this;
        }

        public Criteria andLevelOneNameLessThan(String value) {
            addCriterion("level_one_name <", value, "levelOneName");
            return (Criteria) this;
        }

        public Criteria andLevelOneNameLessThanOrEqualTo(String value) {
            addCriterion("level_one_name <=", value, "levelOneName");
            return (Criteria) this;
        }

        public Criteria andLevelOneNameLike(String value) {
            addCriterion("level_one_name like", value, "levelOneName");
            return (Criteria) this;
        }

        public Criteria andLevelOneNameNotLike(String value) {
            addCriterion("level_one_name not like", value, "levelOneName");
            return (Criteria) this;
        }

        public Criteria andLevelOneNameIn(List<String> values) {
            addCriterion("level_one_name in", values, "levelOneName");
            return (Criteria) this;
        }

        public Criteria andLevelOneNameNotIn(List<String> values) {
            addCriterion("level_one_name not in", values, "levelOneName");
            return (Criteria) this;
        }

        public Criteria andLevelOneNameBetween(String value1, String value2) {
            addCriterion("level_one_name between", value1, value2, "levelOneName");
            return (Criteria) this;
        }

        public Criteria andLevelOneNameNotBetween(String value1, String value2) {
            addCriterion("level_one_name not between", value1, value2, "levelOneName");
            return (Criteria) this;
        }

        public Criteria andLevelTwoIdIsNull() {
            addCriterion("level_two_id is null");
            return (Criteria) this;
        }

        public Criteria andLevelTwoIdIsNotNull() {
            addCriterion("level_two_id is not null");
            return (Criteria) this;
        }

        public Criteria andLevelTwoIdEqualTo(Integer value) {
            addCriterion("level_two_id =", value, "levelTwoId");
            return (Criteria) this;
        }

        public Criteria andLevelTwoIdNotEqualTo(Integer value) {
            addCriterion("level_two_id <>", value, "levelTwoId");
            return (Criteria) this;
        }

        public Criteria andLevelTwoIdGreaterThan(Integer value) {
            addCriterion("level_two_id >", value, "levelTwoId");
            return (Criteria) this;
        }

        public Criteria andLevelTwoIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("level_two_id >=", value, "levelTwoId");
            return (Criteria) this;
        }

        public Criteria andLevelTwoIdLessThan(Integer value) {
            addCriterion("level_two_id <", value, "levelTwoId");
            return (Criteria) this;
        }

        public Criteria andLevelTwoIdLessThanOrEqualTo(Integer value) {
            addCriterion("level_two_id <=", value, "levelTwoId");
            return (Criteria) this;
        }

        public Criteria andLevelTwoIdIn(List<Integer> values) {
            addCriterion("level_two_id in", values, "levelTwoId");
            return (Criteria) this;
        }

        public Criteria andLevelTwoIdNotIn(List<Integer> values) {
            addCriterion("level_two_id not in", values, "levelTwoId");
            return (Criteria) this;
        }

        public Criteria andLevelTwoIdBetween(Integer value1, Integer value2) {
            addCriterion("level_two_id between", value1, value2, "levelTwoId");
            return (Criteria) this;
        }

        public Criteria andLevelTwoIdNotBetween(Integer value1, Integer value2) {
            addCriterion("level_two_id not between", value1, value2, "levelTwoId");
            return (Criteria) this;
        }

        public Criteria andLevelTwoNameIsNull() {
            addCriterion("level_two_name is null");
            return (Criteria) this;
        }

        public Criteria andLevelTwoNameIsNotNull() {
            addCriterion("level_two_name is not null");
            return (Criteria) this;
        }

        public Criteria andLevelTwoNameEqualTo(String value) {
            addCriterion("level_two_name =", value, "levelTwoName");
            return (Criteria) this;
        }

        public Criteria andLevelTwoNameNotEqualTo(String value) {
            addCriterion("level_two_name <>", value, "levelTwoName");
            return (Criteria) this;
        }

        public Criteria andLevelTwoNameGreaterThan(String value) {
            addCriterion("level_two_name >", value, "levelTwoName");
            return (Criteria) this;
        }

        public Criteria andLevelTwoNameGreaterThanOrEqualTo(String value) {
            addCriterion("level_two_name >=", value, "levelTwoName");
            return (Criteria) this;
        }

        public Criteria andLevelTwoNameLessThan(String value) {
            addCriterion("level_two_name <", value, "levelTwoName");
            return (Criteria) this;
        }

        public Criteria andLevelTwoNameLessThanOrEqualTo(String value) {
            addCriterion("level_two_name <=", value, "levelTwoName");
            return (Criteria) this;
        }

        public Criteria andLevelTwoNameLike(String value) {
            addCriterion("level_two_name like", value, "levelTwoName");
            return (Criteria) this;
        }

        public Criteria andLevelTwoNameNotLike(String value) {
            addCriterion("level_two_name not like", value, "levelTwoName");
            return (Criteria) this;
        }

        public Criteria andLevelTwoNameIn(List<String> values) {
            addCriterion("level_two_name in", values, "levelTwoName");
            return (Criteria) this;
        }

        public Criteria andLevelTwoNameNotIn(List<String> values) {
            addCriterion("level_two_name not in", values, "levelTwoName");
            return (Criteria) this;
        }

        public Criteria andLevelTwoNameBetween(String value1, String value2) {
            addCriterion("level_two_name between", value1, value2, "levelTwoName");
            return (Criteria) this;
        }

        public Criteria andLevelTwoNameNotBetween(String value1, String value2) {
            addCriterion("level_two_name not between", value1, value2, "levelTwoName");
            return (Criteria) this;
        }

        public Criteria andLevelThreeIdIsNull() {
            addCriterion("level_three_id is null");
            return (Criteria) this;
        }

        public Criteria andLevelThreeIdIsNotNull() {
            addCriterion("level_three_id is not null");
            return (Criteria) this;
        }

        public Criteria andLevelThreeIdEqualTo(Integer value) {
            addCriterion("level_three_id =", value, "levelThreeId");
            return (Criteria) this;
        }

        public Criteria andLevelThreeIdNotEqualTo(Integer value) {
            addCriterion("level_three_id <>", value, "levelThreeId");
            return (Criteria) this;
        }

        public Criteria andLevelThreeIdGreaterThan(Integer value) {
            addCriterion("level_three_id >", value, "levelThreeId");
            return (Criteria) this;
        }

        public Criteria andLevelThreeIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("level_three_id >=", value, "levelThreeId");
            return (Criteria) this;
        }

        public Criteria andLevelThreeIdLessThan(Integer value) {
            addCriterion("level_three_id <", value, "levelThreeId");
            return (Criteria) this;
        }

        public Criteria andLevelThreeIdLessThanOrEqualTo(Integer value) {
            addCriterion("level_three_id <=", value, "levelThreeId");
            return (Criteria) this;
        }

        public Criteria andLevelThreeIdIn(List<Integer> values) {
            addCriterion("level_three_id in", values, "levelThreeId");
            return (Criteria) this;
        }

        public Criteria andLevelThreeIdNotIn(List<Integer> values) {
            addCriterion("level_three_id not in", values, "levelThreeId");
            return (Criteria) this;
        }

        public Criteria andLevelThreeIdBetween(Integer value1, Integer value2) {
            addCriterion("level_three_id between", value1, value2, "levelThreeId");
            return (Criteria) this;
        }

        public Criteria andLevelThreeIdNotBetween(Integer value1, Integer value2) {
            addCriterion("level_three_id not between", value1, value2, "levelThreeId");
            return (Criteria) this;
        }

        public Criteria andLevelThreeNameIsNull() {
            addCriterion("level_three_name is null");
            return (Criteria) this;
        }

        public Criteria andLevelThreeNameIsNotNull() {
            addCriterion("level_three_name is not null");
            return (Criteria) this;
        }

        public Criteria andLevelThreeNameEqualTo(String value) {
            addCriterion("level_three_name =", value, "levelThreeName");
            return (Criteria) this;
        }

        public Criteria andLevelThreeNameNotEqualTo(String value) {
            addCriterion("level_three_name <>", value, "levelThreeName");
            return (Criteria) this;
        }

        public Criteria andLevelThreeNameGreaterThan(String value) {
            addCriterion("level_three_name >", value, "levelThreeName");
            return (Criteria) this;
        }

        public Criteria andLevelThreeNameGreaterThanOrEqualTo(String value) {
            addCriterion("level_three_name >=", value, "levelThreeName");
            return (Criteria) this;
        }

        public Criteria andLevelThreeNameLessThan(String value) {
            addCriterion("level_three_name <", value, "levelThreeName");
            return (Criteria) this;
        }

        public Criteria andLevelThreeNameLessThanOrEqualTo(String value) {
            addCriterion("level_three_name <=", value, "levelThreeName");
            return (Criteria) this;
        }

        public Criteria andLevelThreeNameLike(String value) {
            addCriterion("level_three_name like", value, "levelThreeName");
            return (Criteria) this;
        }

        public Criteria andLevelThreeNameNotLike(String value) {
            addCriterion("level_three_name not like", value, "levelThreeName");
            return (Criteria) this;
        }

        public Criteria andLevelThreeNameIn(List<String> values) {
            addCriterion("level_three_name in", values, "levelThreeName");
            return (Criteria) this;
        }

        public Criteria andLevelThreeNameNotIn(List<String> values) {
            addCriterion("level_three_name not in", values, "levelThreeName");
            return (Criteria) this;
        }

        public Criteria andLevelThreeNameBetween(String value1, String value2) {
            addCriterion("level_three_name between", value1, value2, "levelThreeName");
            return (Criteria) this;
        }

        public Criteria andLevelThreeNameNotBetween(String value1, String value2) {
            addCriterion("level_three_name not between", value1, value2, "levelThreeName");
            return (Criteria) this;
        }

        public Criteria andLevelFourIdIsNull() {
            addCriterion("level_four_id is null");
            return (Criteria) this;
        }

        public Criteria andLevelFourIdIsNotNull() {
            addCriterion("level_four_id is not null");
            return (Criteria) this;
        }

        public Criteria andLevelFourIdEqualTo(Integer value) {
            addCriterion("level_four_id =", value, "levelFourId");
            return (Criteria) this;
        }

        public Criteria andLevelFourIdNotEqualTo(Integer value) {
            addCriterion("level_four_id <>", value, "levelFourId");
            return (Criteria) this;
        }

        public Criteria andLevelFourIdGreaterThan(Integer value) {
            addCriterion("level_four_id >", value, "levelFourId");
            return (Criteria) this;
        }

        public Criteria andLevelFourIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("level_four_id >=", value, "levelFourId");
            return (Criteria) this;
        }

        public Criteria andLevelFourIdLessThan(Integer value) {
            addCriterion("level_four_id <", value, "levelFourId");
            return (Criteria) this;
        }

        public Criteria andLevelFourIdLessThanOrEqualTo(Integer value) {
            addCriterion("level_four_id <=", value, "levelFourId");
            return (Criteria) this;
        }

        public Criteria andLevelFourIdIn(List<Integer> values) {
            addCriterion("level_four_id in", values, "levelFourId");
            return (Criteria) this;
        }

        public Criteria andLevelFourIdNotIn(List<Integer> values) {
            addCriterion("level_four_id not in", values, "levelFourId");
            return (Criteria) this;
        }

        public Criteria andLevelFourIdBetween(Integer value1, Integer value2) {
            addCriterion("level_four_id between", value1, value2, "levelFourId");
            return (Criteria) this;
        }

        public Criteria andLevelFourIdNotBetween(Integer value1, Integer value2) {
            addCriterion("level_four_id not between", value1, value2, "levelFourId");
            return (Criteria) this;
        }

        public Criteria andLevelFourNameIsNull() {
            addCriterion("level_four_name is null");
            return (Criteria) this;
        }

        public Criteria andLevelFourNameIsNotNull() {
            addCriterion("level_four_name is not null");
            return (Criteria) this;
        }

        public Criteria andLevelFourNameEqualTo(String value) {
            addCriterion("level_four_name =", value, "levelFourName");
            return (Criteria) this;
        }

        public Criteria andLevelFourNameNotEqualTo(String value) {
            addCriterion("level_four_name <>", value, "levelFourName");
            return (Criteria) this;
        }

        public Criteria andLevelFourNameGreaterThan(String value) {
            addCriterion("level_four_name >", value, "levelFourName");
            return (Criteria) this;
        }

        public Criteria andLevelFourNameGreaterThanOrEqualTo(String value) {
            addCriterion("level_four_name >=", value, "levelFourName");
            return (Criteria) this;
        }

        public Criteria andLevelFourNameLessThan(String value) {
            addCriterion("level_four_name <", value, "levelFourName");
            return (Criteria) this;
        }

        public Criteria andLevelFourNameLessThanOrEqualTo(String value) {
            addCriterion("level_four_name <=", value, "levelFourName");
            return (Criteria) this;
        }

        public Criteria andLevelFourNameLike(String value) {
            addCriterion("level_four_name like", value, "levelFourName");
            return (Criteria) this;
        }

        public Criteria andLevelFourNameNotLike(String value) {
            addCriterion("level_four_name not like", value, "levelFourName");
            return (Criteria) this;
        }

        public Criteria andLevelFourNameIn(List<String> values) {
            addCriterion("level_four_name in", values, "levelFourName");
            return (Criteria) this;
        }

        public Criteria andLevelFourNameNotIn(List<String> values) {
            addCriterion("level_four_name not in", values, "levelFourName");
            return (Criteria) this;
        }

        public Criteria andLevelFourNameBetween(String value1, String value2) {
            addCriterion("level_four_name between", value1, value2, "levelFourName");
            return (Criteria) this;
        }

        public Criteria andLevelFourNameNotBetween(String value1, String value2) {
            addCriterion("level_four_name not between", value1, value2, "levelFourName");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNull() {
            addCriterion("ctime is null");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNotNull() {
            addCriterion("ctime is not null");
            return (Criteria) this;
        }

        public Criteria andCtimeEqualTo(Timestamp value) {
            addCriterion("ctime =", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotEqualTo(Timestamp value) {
            addCriterion("ctime <>", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThan(Timestamp value) {
            addCriterion("ctime >", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("ctime >=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThan(Timestamp value) {
            addCriterion("ctime <", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("ctime <=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeIn(List<Timestamp> values) {
            addCriterion("ctime in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotIn(List<Timestamp> values) {
            addCriterion("ctime not in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime not between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNull() {
            addCriterion("mtime is null");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNotNull() {
            addCriterion("mtime is not null");
            return (Criteria) this;
        }

        public Criteria andMtimeEqualTo(Timestamp value) {
            addCriterion("mtime =", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotEqualTo(Timestamp value) {
            addCriterion("mtime <>", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThan(Timestamp value) {
            addCriterion("mtime >", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("mtime >=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThan(Timestamp value) {
            addCriterion("mtime <", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("mtime <=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeIn(List<Timestamp> values) {
            addCriterion("mtime in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotIn(List<Timestamp> values) {
            addCriterion("mtime not in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime not between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andIsDeleteIsNull() {
            addCriterion("is_delete is null");
            return (Criteria) this;
        }

        public Criteria andIsDeleteIsNotNull() {
            addCriterion("is_delete is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeleteEqualTo(Integer value) {
            addCriterion("is_delete =", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteNotEqualTo(Integer value) {
            addCriterion("is_delete <>", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteGreaterThan(Integer value) {
            addCriterion("is_delete >", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_delete >=", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteLessThan(Integer value) {
            addCriterion("is_delete <", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteLessThanOrEqualTo(Integer value) {
            addCriterion("is_delete <=", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteIn(List<Integer> values) {
            addCriterion("is_delete in", values, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteNotIn(List<Integer> values) {
            addCriterion("is_delete not in", values, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteBetween(Integer value1, Integer value2) {
            addCriterion("is_delete between", value1, value2, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteNotBetween(Integer value1, Integer value2) {
            addCriterion("is_delete not between", value1, value2, "isDelete");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}