package com.bilibili.adp.cpc.biz.services.anchor;

import com.bapis.ad.adp.anchor.*;
import com.bapis.ad.component.ClueType;
import com.bapis.ad.component.UrlType;
import com.bapis.ad.scv.anchor.*;
import com.bilibili.adp.common.Constants;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.enums.OperatorType;
import com.bilibili.adp.common.enums.SystemType;
import com.bilibili.adp.cpc.biz.bos.anchor.*;
import com.bilibili.adp.cpc.biz.converter.anchor.AnchorConverter;
import com.bilibili.adp.cpc.biz.converter.components.ComponentComponentConverter;
import com.bilibili.adp.cpc.biz.services.account.BusinessToolAccountQuerier;
import com.bilibili.adp.cpc.biz.services.anchor.commercial.AnchorCommercialService;
import com.bilibili.adp.cpc.biz.services.enterprise_wechat.EnterpriseWechatQuerier;
import com.bilibili.adp.cpc.biz.services.enterprise_wechat.dto.EnterpriseWechatDto;
import com.bilibili.adp.cpc.enums.BusinessToolTypeEnum;
import com.bilibili.adp.cpc.enums.anchor.AnchorBizCodeEnum;
import com.bilibili.adp.cpc.proxy.CpmScvProxy;
import com.bilibili.adp.cpc.repo.CreativeAnchorMappingRepo;
import com.bilibili.adp.cpc.utils.metrics.MetricsKeyConstant;
import com.bilibili.adp.launch.api.minigame.dto.LauMiniGameDto;
import com.bilibili.adp.launch.api.service.ILauMiniGameService;
import com.bilibili.adp.web.framework.core.Pagination;
import com.bilibili.mgk.platform.api.landing_page.soa.ISoaLandingPageService;
import com.bilibili.mgk.platform.common.page_bean.MgkLandingPageBean;
import com.bilibili.sycpb.acc.api.dict.common.YesNoEnum;
import io.grpc.Status;
import io.grpc.StatusRuntimeException;
import io.grpc.stub.StreamObserver;
import io.opentelemetry.api.common.AttributeKey;
import io.opentelemetry.api.common.Attributes;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.util.UriComponentsBuilder;
import pleiades.venus.starter.rpc.client.RPCClient;
import pleiades.venus.starter.rpc.server.RPCService;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.bilibili.adp.cpc.utils.metrics.MetricsKeyConstant.*;

/**
 * <AUTHOR>
 * @date 2024/4/7 14:46
 */
@Slf4j
@Service
@RPCService
@RequiredArgsConstructor
public class AnchorRpcService extends AnchorServiceGrpc.AnchorServiceImplBase {

    public static final String ID = "AnchorRpcService";

    private final CreativeAnchorMappingRepo creativeAnchorMappingRepo;
    private final ArchiveAnchorQuerier archiveAnchorQuerier;
    private final IAnchorService archiveAnchorService;
    private final ILauMiniGameService lauMiniGameService;
    private final ISoaLandingPageService soaLandingPageService;
    private final BusinessToolAccountQuerier businessToolAccountQuerier;
    private final EnterpriseWechatQuerier enterpriseWechatQuerier;
    private final AnchorCommercialService anchorCommercialService;

    @Resource
    private CpmScvProxy cpmScvProxy;

    @Getter
    @Value("${component.miniGame.h5BottomLink:http://www.bilibili.com}")
    private String miniGameH5BottomLink;

    private static final Long MESSAGE_DEFAULT_PAGE_ID = 630454194903265280L;

    private static final String MESSAGE_PAGE_MACRO_PARAM = "buvid=__BUVID__&mid=__MID__&imei=__IMEI__&duid=__DUID__&idfa=__IDFA__&android_id=__ANDROIDID__&os=__OS__&request_id=__REQUESTID__&source_id=__SOURCEID__&track_id=__TRACKID__&creative_id=__CREATIVEID__&adtype=__ADTYPE__&cm_from_track_id=__CMFROMTRACKID__&gaoneng_play_style=__GAONENGPLAYSTYLE__&gaoneng_half_style=__GAONENGHALFSTYLE__&assembly_track_id=__ASSEMBLYTRACKID__&from_spmid=__FROMSPMID__&situation=12";
    public static final String BUSINESS_TOOL_MGK_PAGE_MACRO_PARAM = Constants.MGK_PAGE_MACRO_PARAM + "&cm_from_track_id=__CMFROMTRACKID__&gaoneng_play_style=__GAONENGPLAYSTYLE__&gaoneng_half_style=__GAONENGHALFSTYLE__&assembly_track_id=__ASSEMBLYTRACKID__";

    @Override
    public void queryBindCreativeNum(QueryBindCreativeNumReq request, StreamObserver<BindCreativeNumReply> responseObserver) {
        try {
            Map<Long, Long> creativeNumMap = creativeAnchorMappingRepo.queryRelatedCreativeNum(request.getIdsList());
            List<SingleBindCreativeNumReply> bindCreativeNumReplies = creativeNumMap.entrySet().stream().map(entey -> SingleBindCreativeNumReply.newBuilder()
                    .setId(entey.getKey())
                    .setBindCreativeNum(Math.toIntExact(entey.getValue()))
                    .build()).collect(Collectors.toList());

            BindCreativeNumReply reply = BindCreativeNumReply.newBuilder().addAllData(bindCreativeNumReplies).build();
            responseObserver.onNext(reply);
            responseObserver.onCompleted();
        } catch (Throwable t) {
            responseObserver.onError(new StatusRuntimeException(Status.fromCode(Status.Code.INTERNAL)
                    .withDescription(t.getMessage())
                    .withCause(t)));
            log.error("{}: queryBindCreativeNum,触发失败 -> {}", ID, t.getMessage());
        }
    }

    @Override
    public void queryArchiveBizAnchors(QueryArchiveBizAnchorReq request, StreamObserver<QueryArchiveBizAnchorRes> responseObserver) {
        try {

            List<AnchorPreviewBo> anchorPreviewBos = archiveAnchorQuerier.queryBizAnchorsByPriority(request.getAvidsList());
            List<SingleQueryArchiveBizAnchorRes> anchorRes = anchorPreviewBos.stream().map(anchorPreviewBo -> SingleQueryArchiveBizAnchorRes.newBuilder()
                    .setAnchorId(anchorPreviewBo.getId())
                    .setAvid(anchorPreviewBo.getAid())
                    .setAnchorSystemType(anchorPreviewBo.getAnchorSystemType())
                    .build()).collect(Collectors.toList());

            responseObserver.onNext(QueryArchiveBizAnchorRes.newBuilder().addAllData(anchorRes).build());
            responseObserver.onCompleted();
        } catch (Throwable t) {
            responseObserver.onError(new StatusRuntimeException(Status.fromCode(Status.Code.INTERNAL)
                    .withDescription(t.getMessage())
                    .withCause(t)));
            log.error("{}: queryArchiveBizAnchors,触发失败 -> {}", ID, t.getMessage());
        }
    }

    @Override
    public void addBusinessNativeAnchors(AddBusinessNativeAnchorsReq request, StreamObserver<AddBusinessNativeAnchorsRes> responseObserver) {
        try {
            Operator operator = businessToolAccountQuerier.getBusinessToolOperator(request.getAccountId());

            AnchorSaveBo anchorSaveBo = new AnchorSaveBo();

            List<Long> avidsList = request.getAvidsList();
            anchorSaveBo.setAids(avidsList);
            //经营号只有story
            anchorSaveBo.setScenes(Collections.singletonList(AnchorSceneType.STORY_VALUE));
            anchorSaveBo.setBizCode(AnchorBizCodeEnum.BUSINESS.getCode());

            Integer businessComponentType = request.getBusinessToolType();
            String businessComponentId = request.getBusinessToolId();

            if (Objects.equals(businessComponentType, BusinessToolTypeEnum.MINI_APP.getCode())) {
                anchorSaveBo.setType(AnchorType.WECHAT_MINI_GAME_VALUE);
                anchorSaveBo.setClueType(ClueType.LANDING_PAGE_VALUE);
                anchorSaveBo.setMiniGameId(Integer.parseInt(businessComponentId));
                LauMiniGameDto miniGameDto = lauMiniGameService.getLauMiniGameById(Integer.parseInt(businessComponentId));
                Assert.isTrue(miniGameDto != null, "微信小游戏不存在");
                Assert.isTrue(YesNoEnum.YES.getCode().equals(miniGameDto.getState()), "微信小游戏已经下线，请检查");
                Assert.isTrue(!StringUtils.isEmpty(miniGameDto.getGameUrl()), "微信小游戏url为空，请检查！");
                anchorSaveBo.setConversionUrl(miniGameH5BottomLink);
                anchorSaveBo.setConversionUrlType(UrlType.UNKNOWN_URL_TYPE_VALUE);
                anchorSaveBo.setAndroidUrl(anchorSaveBo.getConversionUrl());
                anchorSaveBo.setIosUrl(anchorSaveBo.getConversionUrl());
                anchorSaveBo.setAndroidUrlType(UrlType.UNKNOWN_URL_TYPE_VALUE);
                anchorSaveBo.setIosUrlType(UrlType.UNKNOWN_URL_TYPE_VALUE);
                anchorSaveBo.setBusinessToolType(BusinessToolTypeEnum.MINI_APP.getCode());
            } else if (Objects.equals(businessComponentType, BusinessToolTypeEnum.RESERVE.getCode())) {
                anchorSaveBo.setType(AnchorType.CLUE_VALUE);
                anchorSaveBo.setClueType(ClueType.LANDING_PAGE_VALUE);
                anchorSaveBo.setClueData(businessComponentId);
                anchorSaveBo.setConversionUrlPageId(Long.parseLong(businessComponentId));
                MgkLandingPageBean mgkLandingPageBean = soaLandingPageService.validatePageIdAndGetLandingPage(String.valueOf(businessComponentId));
                String conversionUrl = UriComponentsBuilder.fromUriString(mgkLandingPageBean.getLaunchUrlSecondary())
                        // 宏替换
                        .query(BUSINESS_TOOL_MGK_PAGE_MACRO_PARAM)
                        .build(false)
                        .toUriString();
                anchorSaveBo.setConversionUrl(conversionUrl);
                anchorSaveBo.setConversionUrlType(UrlType.GAONENG_VALUE);
                anchorSaveBo.setAndroidUrlType(UrlType.GAONENG_VALUE);
                anchorSaveBo.setIosUrlType(UrlType.GAONENG_VALUE);
                anchorSaveBo.setAndroidUrl(anchorSaveBo.getConversionUrl());
                anchorSaveBo.setIosUrl(anchorSaveBo.getConversionUrl());
                anchorSaveBo.setBusinessToolType(BusinessToolTypeEnum.RESERVE.getCode());
            } else if (Objects.equals(businessComponentType, BusinessToolTypeEnum.THIRD_PARTY_LANDING_PAGE_URL.getCode())) {
                anchorSaveBo.setType(AnchorType.CLUE_VALUE);
                anchorSaveBo.setClueType(ClueType.LANDING_PAGE_VALUE);
                anchorSaveBo.setClueData(businessComponentId);
                anchorSaveBo.setConversionUrlPageId(Long.parseLong(businessComponentId));
                anchorSaveBo.setConversionUrl(request.getThirdPartyLandingPageUrl());
                anchorSaveBo.setConversionUrlType(UrlType.THIRD_PARTY_VALUE);
                anchorSaveBo.setAndroidUrlType(UrlType.THIRD_PARTY_VALUE);
                anchorSaveBo.setIosUrlType(UrlType.THIRD_PARTY_VALUE);
                anchorSaveBo.setAndroidUrl(anchorSaveBo.getConversionUrl());
                anchorSaveBo.setIosUrl(anchorSaveBo.getConversionUrl());
                anchorSaveBo.setBusinessToolType(BusinessToolTypeEnum.THIRD_PARTY_LANDING_PAGE_URL.getCode());
            } else if (Objects.equals(businessComponentType, BusinessToolTypeEnum.CONTACT_QW.getCode())) {
                anchorSaveBo.setType(AnchorType.CLUE_VALUE);
                anchorSaveBo.setClueType(ClueType.ENTERPRISE_WECHAT_VALUE);
                anchorSaveBo.setClueData(businessComponentId);
                EnterpriseWechatDto enterpriseWechatDto =
                        enterpriseWechatQuerier.queryEnterpriseWechatInfo(operator.getOperatorId(), Long.parseLong(businessComponentId));
                Assert.isTrue(enterpriseWechatDto != null, "企业微信不存在");
                Assert.isTrue(!StringUtils.isEmpty(enterpriseWechatDto.getUrl()), "企业微信url为空，请检查！");
                anchorSaveBo.setConversionUrl(enterpriseWechatDto.getUrl());
                anchorSaveBo.setConversionUrlType(UrlType.UNKNOWN_URL_TYPE_VALUE);
                anchorSaveBo.setAndroidUrl(anchorSaveBo.getConversionUrl());
                anchorSaveBo.setIosUrl(anchorSaveBo.getConversionUrl());
                anchorSaveBo.setAndroidUrlType(UrlType.UNKNOWN_URL_TYPE_VALUE);
                anchorSaveBo.setIosUrlType(UrlType.UNKNOWN_URL_TYPE_VALUE);
                anchorSaveBo.setBusinessToolType(BusinessToolTypeEnum.CONTACT_QW.getCode());
            } else if (Objects.equals(businessComponentType, BusinessToolTypeEnum.ONLINE_CONSULT.getCode())) {
                anchorSaveBo.setType(AnchorType.CLUE_VALUE);
                anchorSaveBo.setClueType(ClueType.MESSAGE_VALUE);
                anchorSaveBo.setClueData(businessComponentId);

                int shopId = request.getShopId();
                String chatUrl = "https://gaoneng.bilibili.com/tetris/page/?pageId=" + request.getBusinessToolId() + "&shop_father_id=3&&shop_id=" + shopId;

                String conversionUrl = UriComponentsBuilder.fromUriString(chatUrl)
                        // 宏替换
                        .query(MESSAGE_PAGE_MACRO_PARAM)
                        .build(false)
                        .toUriString();
                anchorSaveBo.setConversionUrl(conversionUrl);
                anchorSaveBo.setConversionUrlPageId(MESSAGE_DEFAULT_PAGE_ID);
                anchorSaveBo.setConversionUrlType(UrlType.UNKNOWN_URL_TYPE_VALUE);
                String jumpUrl = "bilibili://link/customer/chat?shop_father_id=3" + "&shop_id=" + shopId;
                jumpUrl = UriComponentsBuilder.fromUriString(jumpUrl)
                        // 宏替换
                        .query(MESSAGE_PAGE_MACRO_PARAM)
                        .build(false)
                        .toUriString();

                anchorSaveBo.setIosUrl(jumpUrl);
                anchorSaveBo.setAndroidUrl(jumpUrl);
                anchorSaveBo.setAndroidUrlType(UrlType.UNKNOWN_URL_TYPE_VALUE);
                anchorSaveBo.setIosUrlType(UrlType.UNKNOWN_URL_TYPE_VALUE);
                anchorSaveBo.setBusinessToolType(BusinessToolTypeEnum.ONLINE_CONSULT.getCode());

            }

            anchorSaveBo.setMainTitle(request.getMainTitle());
            anchorSaveBo.setSubTitle(request.getSubTitle());
            anchorSaveBo.setButtonText(request.getButtonText());

            anchorSaveBo.setQualificationIds(request.getQualificationIdsList().stream().map(Long::intValue).collect(Collectors.toList()));
            anchorSaveBo.setName(request.getBusinessToolName());
            anchorSaveBo.setBizCode(AnchorBizCodeEnum.BUSINESS.getCode());

            List<AnchorCreateResultBo> anchorCreateResultBoList = archiveAnchorService.createAnchors(anchorSaveBo, operator);
            List<AddNativeAnchorSingleInfo> singleAddNativeAnchorResList = anchorCreateResultBoList.stream().map(anchorCreateResultBo -> AddNativeAnchorSingleInfo.newBuilder()
                    .setAvid(anchorCreateResultBo.getAvid())
                    .setAnchorId(anchorCreateResultBo.getAnchorId())
                    .setCreativeId(anchorCreateResultBo.getCreativeId())
                    .build()).collect(Collectors.toList());
            AddBusinessNativeAnchorsRes addNativeAnchorsRes = AddBusinessNativeAnchorsRes.newBuilder()
                    .addAllSingleInfos(singleAddNativeAnchorResList)
                    .setSuccess(Boolean.TRUE)
                    .setMsg("")
                    .build();
            responseObserver.onNext(addNativeAnchorsRes);
            responseObserver.onCompleted();
            log.info("{}: addNativeAnchors,触发成功 -> {}", ID, anchorCreateResultBoList);
        } catch (Exception e) {
            responseObserver.onError(new StatusRuntimeException(Status.fromCode(Status.Code.INTERNAL)
                    .withDescription(e.getMessage())
                    .withCause(e)));
            log.error("{}: addNativeAnchors,触发失败 -> {}", ID, e.getMessage(), e);
        }


    }

    @Override
    public void queryNativeAnchorDetailInfo(QueryNativeAnchorReq request, StreamObserver<QueryNativeAnchorRes> responseObserver) {
        try {
            AnchorInfoBo anchorInfoBo = archiveAnchorService.fetchAnchorDetail(request.getAnchorId());

            Integer type = anchorInfoBo.getType();
            Integer clueType = anchorInfoBo.getClueType();
            Integer conversionUrlType = anchorInfoBo.getConversionUrlType();
            Integer businessToolType = convertToBusinessToolType(type, clueType, conversionUrlType);

            QueryNativeAnchorRes queryNativeAnchorRes = QueryNativeAnchorRes.newBuilder()
                    .setAvid(anchorInfoBo.getAid())
                    .setBusinessToolType(businessToolType)
                    .setBusinessToolId(anchorInfoBo.getClueData())
                    .setBusinessToolName(anchorInfoBo.getName())

                    .setMainTitle(anchorInfoBo.getMainTitle())
                    .setSubTitle(anchorInfoBo.getSubTitle())
                    .setButtonText(anchorInfoBo.getButtonText())
                    .addAllQualificationIds(anchorInfoBo.getQualificationIds().stream().map(Long::valueOf).collect(Collectors.toList()))
                    .setThirdPartyLandingPageUrl(anchorInfoBo.getConversionUrl())
                    .setAuditStatus(anchorInfoBo.getStatus())
                    .setReason(anchorInfoBo.getReason())
                    .setBizCode(anchorInfoBo.getBizCode())
                    .build();
            responseObserver.onNext(queryNativeAnchorRes);
            responseObserver.onCompleted();
            log.info("{}: queryNativeAnchorDetailInfo,触发成功 -> {}", ID, anchorInfoBo);
        } catch (RuntimeException e) {
            responseObserver.onError(new StatusRuntimeException(Status.fromCode(Status.Code.INTERNAL)
                    .withDescription(e.getMessage())
                    .withCause(e)));
            log.error("{}: queryNativeAnchorDetailInfo,触发失败 -> {}", ID, e.getMessage(), e);
        }
    }

    @Override
    public void deleteNativeAnchor(DeleteNativeAnchorReq request, StreamObserver<DeleteNativeAnchorRes> responseObserver) {
        try {
            Operator operator = businessToolAccountQuerier.getBusinessToolOperator(request.getAccountId());
            archiveAnchorService.deleteAnchor(request.getAnchorId(), operator);
            responseObserver.onNext(DeleteNativeAnchorRes.newBuilder().setSuccess(Boolean.TRUE).build());
            responseObserver.onCompleted();
            log.info("{}: deleteNativeAnchor,触发成功 -> {}", ID, request.getAnchorId());
        } catch (Exception e) {
            responseObserver.onError(new StatusRuntimeException(Status.fromCode(Status.Code.INTERNAL)
                    .withDescription(e.getMessage())
                    .withCause(e)));
            log.error("{}: deleteNativeAnchor,触发失败 -> {}", ID, e.getMessage(), e);
        }
    }

    @Override
    public void batchDeleteAnchorForBusinessTool(AnchorSaveBatch request, StreamObserver<UpdateCountReply> responseObserver) {
        try {
            Operator operator = businessToolAccountQuerier.getBusinessToolOperator(request.getOperator().getOperatorId());


            archiveAnchorService.deleteAnchorBatchForBusinessTool(AnchorSaveBo.builder().ids(request.getAnchorBaseInfo().getids).build(),operator );
            responseObserver.onNext(DeleteNativeAnchorRes.newBuilder().setSuccess(Boolean.TRUE).build());
            responseObserver.onCompleted();
            log.info("{}: batchDeleteAnchorForBusinessTool,触发成功 -> {}", ID, request.getAnchorId());
        } catch (Exception e) {
            responseObserver.onError(new StatusRuntimeException(Status.fromCode(Status.Code.INTERNAL)
                    .withDescription(e.getMessage())
                    .withCause(e)));
            log.error("{}: batchDeleteAnchorForBusinessTool,触发失败 -> {}", ID, e.getMessage(), e);
        }
    }

    @Override
    public void queryNativeAnchorDetailInfoByAvids(QueryNativeAnchorDetailInfoByAvidsReq request, StreamObserver<QueryNativeAnchorDetailInfoByAvidsRes> responseObserver) {
        try {
            List<Long> avidsList = request.getAvidsList();
            List<AnchorInfoBo> anchorInfoBos = archiveAnchorQuerier.queryAnchorsByAvids(avidsList);


            List<QueryNativeAnchorRes> queryNativeAnchorResList = new ArrayList<>();
            for (AnchorInfoBo anchorInfoBo : anchorInfoBos) {
                Integer type = anchorInfoBo.getType();
                Integer clueType = anchorInfoBo.getClueType();
                Integer conversionUrlType = anchorInfoBo.getConversionUrlType();
                Integer businessToolType = convertToBusinessToolType(type, clueType, conversionUrlType);
                QueryNativeAnchorRes queryNativeAnchorRes = QueryNativeAnchorRes.newBuilder()
                        .setAvid(anchorInfoBo.getAid())
                        .setBusinessToolType(businessToolType)
                        .setBusinessToolId(anchorInfoBo.getClueData())
                        .setBusinessToolName(anchorInfoBo.getName())
                        .setMainTitle(anchorInfoBo.getMainTitle())
                        .setSubTitle(anchorInfoBo.getSubTitle())
                        .setButtonText(anchorInfoBo.getButtonText())
                        .addAllQualificationIds(Optional.ofNullable(anchorInfoBo.getQualificationIds()).map(qualificationIds -> qualificationIds.stream().map(Long::valueOf).collect(Collectors.toList())).orElse(new ArrayList<>()))
                        .setThirdPartyLandingPageUrl(anchorInfoBo.getConversionUrl())
                        .setAuditStatus(anchorInfoBo.getStatus())
                        .setReason(anchorInfoBo.getReason())
                        .setAnchorId(anchorInfoBo.getId())
                        .setBizCode(anchorInfoBo.getBizCode())
                        .build();
                queryNativeAnchorResList.add(queryNativeAnchorRes);
            }
            QueryNativeAnchorDetailInfoByAvidsRes queryNativeAnchorDetailInfoByAvidsRes = QueryNativeAnchorDetailInfoByAvidsRes.newBuilder()
                    .addAllData(queryNativeAnchorResList)
                    .build();
            responseObserver.onNext(queryNativeAnchorDetailInfoByAvidsRes);
            responseObserver.onCompleted();
            log.info("{}: queryNativeAnchorDetailInfoByAvids,触发成功 -> {}", ID, queryNativeAnchorDetailInfoByAvidsRes);
        } catch (Exception e) {
            responseObserver.onError(new StatusRuntimeException(Status.fromCode(Status.Code.INTERNAL)
                    .withDescription(e.getMessage())
                    .withCause(e)));
            log.error("{}: queryNativeAnchorDetailInfoByAvids,触发失败 -> {}", ID, e.getMessage(), e);
        }

    }

    private Integer convertToBusinessToolType(Integer type, Integer clueType, Integer conversionUrlType) {
        if (Objects.equals(type, AnchorType.WECHAT_MINI_GAME_VALUE)) {
            return BusinessToolTypeEnum.MINI_APP.getCode();
        } else if (Objects.equals(type, AnchorType.CLUE_VALUE)) {
            if (Objects.equals(clueType, ClueType.LANDING_PAGE_VALUE)) {
                if (Objects.equals(conversionUrlType, UrlType.THIRD_PARTY_VALUE)) {
                    return BusinessToolTypeEnum.THIRD_PARTY_LANDING_PAGE_URL.getCode();
                } else if (Objects.equals(conversionUrlType, UrlType.GAONENG_VALUE)) {
                    return BusinessToolTypeEnum.RESERVE.getCode();
                }
            } else if (Objects.equals(clueType, ClueType.ENTERPRISE_WECHAT_VALUE)) {
                return BusinessToolTypeEnum.CONTACT_QW.getCode();
            } else if (Objects.equals(clueType, ClueType.MESSAGE_VALUE)) {
                return BusinessToolTypeEnum.ONLINE_CONSULT.getCode();
            }
        }
        return -1;
    }

    @Override
    public void batchCreateAnchor(AnchorSaveBatch request, StreamObserver<CreatesReply> responseObserver) {

        AnchorSaveBo anchorSaveBo = AnchorConverter.MAPPER.fromRpcBatchBo(request.getAnchorBaseInfo());
        log.info("batchCreateAnchor request={}   anchorSaveBo={}", request, anchorSaveBo);
        Operator operator = ComponentComponentConverter.MAPPER.fromRpcBatchBo(request.getOperator());
        List<AnchorCreateResultBo> anchorCreateResultBoList = archiveAnchorService.createAnchors(anchorSaveBo, operator);
        List<CreateReply> createReplyList = anchorCreateResultBoList.stream().map(x -> CreateReply.newBuilder()
                .setId(Optional.ofNullable(x.getAnchorId()).orElse(0L))
                .setAvid(x.getAvid())
                .setCreativeId(Optional.ofNullable(x.getCreativeId()).orElse(0))
                .build()).collect(Collectors.toList());
        CreatesReply reply = CreatesReply.newBuilder().addAllCreateRepList(createReplyList).build();

        responseObserver.onNext(reply);
        responseObserver.onCompleted();
    }

    @Override
    public void batchUpdateAnchor(AnchorSaveBatch request, StreamObserver<UpdateCountReply> responseObserver) {
        AnchorSaveBo anchorSaveBo = AnchorConverter.MAPPER.fromRpcBatchBo(request.getAnchorBaseInfo());
        log.info("batchUpdateAnchor request={}   anchorSaveBo={}", request, anchorSaveBo);

        Operator operator = ComponentComponentConverter.MAPPER.fromRpcBatchBo(request.getOperator());
        QueryAnchorPageListReq listReq = QueryAnchorPageListReq.newBuilder()
                .setPage(1)
                .setSize(2)
                .addAllAid(Collections.singletonList(anchorSaveBo.getAid()))
                .addIsDeleted(0)
                .build();

        List<SingleQueryAnchorRep> dataList = cpmScvProxy.queryAnchorPageList(listReq);

        UpdateCountReply.Builder builder = UpdateCountReply.newBuilder();
        if (CollectionUtils.isEmpty(dataList)) {
            builder.setCount(0)
                    .setMsg("avid=" + anchorSaveBo.getAid() + "锚点不存在");
        } else if (dataList.size() > 1) {
            builder.setCount(0)
                    .setMsg("avid=" + anchorSaveBo.getAid() + "锚点存在多个，不可批量操作，请单个删除");
        } else {
            anchorSaveBo.setId(dataList.get(0).getId());
            Integer count = archiveAnchorService.updateAnchor(anchorSaveBo, operator);
            builder.setCount(count);
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void batchDeleteAnchor(AnchorSaveBatch request, StreamObserver<UpdateCountReply> responseObserver) {
        AnchorSaveBo anchorSaveBo = AnchorConverter.MAPPER.fromRpcBatchBo(request.getAnchorBaseInfo());
        log.info("batchDeleteAnchor request={}   anchorSaveBo={}", request, anchorSaveBo);

        Operator operator = ComponentComponentConverter.MAPPER.fromRpcBatchBo(request.getOperator());
        QueryAnchorPageListReq listReq = QueryAnchorPageListReq.newBuilder()
                .setPage(1)
                .setSize(2)
                .addAllAid(Collections.singletonList(anchorSaveBo.getAid()))
                .addIsDeleted(0)
                .build();

        List<SingleQueryAnchorRep> dataList = cpmScvProxy.queryAnchorPageList(listReq);

        UpdateCountReply.Builder builder = UpdateCountReply.newBuilder();
        if (CollectionUtils.isEmpty(dataList)) {
            builder.setCount(0)
                    .setMsg("avid=" + anchorSaveBo.getAid() + "锚点不存在");
        } else if (dataList.size() > 1) {
            builder.setCount(0)
                    .setMsg("avid=" + anchorSaveBo.getAid() + "锚点存在多个，不可批量操作，请单个删除");
        } else {
            Integer count = archiveAnchorService.deleteAnchor(dataList.get(0).getId(), operator);
            builder.setCount(count);
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void addCommercialAnchor(AnchorSaveBatch request, StreamObserver<AddAnchorRes> responseObserver) {
        AddAnchorRes.Builder replybuilder = AddAnchorRes.newBuilder();
        try {
            List<AnchorCreateResultBo> commercialAnchors = anchorCommercialService.createCommercialAnchors(request, AnchorBizCodeEnum.COMMERCIAL);
            replybuilder.addAllSingleInfos(commercialAnchors.stream().map(x -> AddNativeAnchorSingleInfo.newBuilder()
                    .setAvid(x.getAvid())
                    .setAnchorId(x.getAnchorId())
                    .setCreativeId(x.getCreativeId())
                    .build()).collect(Collectors.toList()));
        } catch (Exception e) {
            replybuilder.setMsg(null == e.getMessage() ? "添加商业锚点失败" : e.getMessage());
            replybuilder.setSuccess(false);
            log.error("addCommercialAnchor error", e);
        }
        responseObserver.onNext(replybuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void addPartnerAnchor(AnchorSaveBatch request, StreamObserver<AddAnchorRes> responseObserver) {
        AddAnchorRes.Builder replybuilder = AddAnchorRes.newBuilder();
        try {
            List<AnchorCreateResultBo> commercialAnchors = anchorCommercialService.createCommercialAnchors(request, AnchorBizCodeEnum.PARTNER);
            replybuilder.addAllSingleInfos(commercialAnchors.stream().map(x -> AddNativeAnchorSingleInfo.newBuilder()
                    .setAvid(x.getAvid())
                    .setAnchorId(x.getAnchorId())
                    .setCreativeId(x.getCreativeId())
                    .build()).collect(Collectors.toList()));
        } catch (Exception e) {
            replybuilder.setMsg(null == e.getMessage() ? "添加商业锚点失败" : e.getMessage());
            replybuilder.setSuccess(false);
            log.error("addPartnerAnchor error", e);
        }
        responseObserver.onNext(replybuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void updateAvidByAnchorId(AnchorSaveBatch request, StreamObserver<AddAnchorRes> responseObserver) {
        AddAnchorRes.Builder replyBuilder = AddAnchorRes.newBuilder();
        try {
            anchorCommercialService.updateAvidByAnchorId(UpdateAvidByAnchorIdReq.newBuilder()
                    .setAnchorId(request.getAnchorBaseInfo().getId())
                    .setAvid(request.getAnchorBaseInfo().getAid())
                    .build());
            replyBuilder.addSingleInfos(AddNativeAnchorSingleInfo.newBuilder()
                    .setAnchorId(request.getAnchorBaseInfo().getId())
                    .setAvid(request.getAnchorBaseInfo().getAid())
                    .build());
            replyBuilder.setSuccess(true);
        } catch (Exception e) {
            replyBuilder.setMsg(e.getMessage());
            replyBuilder.setSuccess(false);
            log.error("updateAvidByAnchorId error", e);
        }
        responseObserver.onNext(replyBuilder.build());
        responseObserver.onCompleted();
    }

}
