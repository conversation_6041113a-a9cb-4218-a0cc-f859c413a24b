package com.bilibili.adp.cpc.databus.bos;

import com.bilibili.adp.cpc.dao.querydsl.pos.LauMaterialBilibiliVideoPo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LauMaterialBilibiliVideoChange {
    private String eventName ;

    private List<LauMaterialBilibiliVideoPo> LauMaterialBilibiliVideo;

}
