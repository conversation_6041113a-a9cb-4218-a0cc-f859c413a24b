package com.bilibili.adp.cpc.config;

import com.bapis.account.service.AccountGrpc;
import com.bapis.account.service.account_control_plane.AccountControlPlaneGrpc;
import com.bapis.activity.service.ActivityGrpc;
import com.bapis.ad.component.CommentComponentServiceGrpc;
import com.bapis.ad.component.StoryComponentServiceGrpc;
import com.bapis.ad.external.ExternalServiceGrpc;
import com.bapis.ad.jupiter.img.CreativeImageGrpc;
import com.bapis.ad.location.ResourceServiceGrpc;
import com.bapis.ad.mgk.LandingPageServiceGrpc;
import com.bapis.ad.mgk.audit.DynamicBlueLinkAuditServiceGrpc;
import com.bapis.ad.mgk.business_tool.MgkBusinessToolServiceGrpc;
import com.bapis.ad.mgk.page.group.LandingPageGroupServiceGrpc;
import com.bapis.ad.ott.AdOttServiceGrpc;
import com.bapis.archive.service.ArchiveGrpc;
import com.bapis.community.interfaces.reply.ReplyInterfaceGrpc;
import com.bapis.community.interfaces.tag.TagRPCGrpc;
import com.bapis.live.xroom.RoomGrpc;
import com.bapis.ott.service.OTTServiceGrpc;
import com.bapis.pgc.service.season.episode.EpisodeGrpc;
import com.bapis.up.archive.service.UpArchiveGrpc;
import com.bapis.videoup.open.service.VideoUpOpenGrpc;
import io.grpc.Channel;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import pleiades.component.env.v1.Env;
import pleiades.component.rpc.client.ChannelBuilder;
import pleiades.component.rpc.client.naming.RPCNamingClientNameResolverFactory;
import pleiades.venus.naming.client.NamingClient;
import pleiades.venus.naming.client.Namings;
import pleiades.venus.naming.client.resolve.NamingResolver;

import java.util.concurrent.TimeUnit;

@Configuration
public class GrpcConfig {

    @Bean
    public ArchiveGrpc.ArchiveBlockingStub archiveBlockingStub(Channel archiveChannel) {
        return ArchiveGrpc.newBlockingStub(archiveChannel);
    }

    @Bean
    public UpArchiveGrpc.UpArchiveBlockingStub upArchiveBlockingStub(Channel upArchiveChannel) {
        return UpArchiveGrpc.newBlockingStub(upArchiveChannel);
    }

    @Bean
    public VideoUpOpenGrpc.VideoUpOpenBlockingStub videoUpOpenBlockingStub(Channel videoUpChannel) {
        return VideoUpOpenGrpc.newBlockingStub(videoUpChannel);
    }

    @Bean
    public CommentComponentServiceGrpc.CommentComponentServiceBlockingStub commentComponentServiceBlockingStub(Channel scvChannel) {
        return CommentComponentServiceGrpc.newBlockingStub(scvChannel);
    }

    @Bean
    public StoryComponentServiceGrpc.StoryComponentServiceBlockingStub storyComponentServiceBlockingStub(Channel scvChannel) {
        return StoryComponentServiceGrpc.newBlockingStub(scvChannel);
    }

    @Bean
    AdOttServiceGrpc.AdOttServiceBlockingStub adOttServiceBlockingStub(Channel scvChannel) {
        return AdOttServiceGrpc.newBlockingStub(scvChannel);
    }

    @Bean
    public AccountGrpc.AccountBlockingStub accountBlockingStub(Channel accountServiceChannel) {
        return AccountGrpc.newBlockingStub(accountServiceChannel);
    }

    @Bean
    public AccountControlPlaneGrpc.AccountControlPlaneBlockingStub accountControlPlaneBlockingStub(Channel mainAccountAccountControlPlaneChannel) {
        return AccountControlPlaneGrpc.newBlockingStub(mainAccountAccountControlPlaneChannel);
    }

    @Bean
    public TagRPCGrpc.TagRPCBlockingStub tagRPCBlockingStub(Channel tagChannel) {
        return TagRPCGrpc.newBlockingStub(tagChannel);
    }

    @Bean
    @Primary
    public OTTServiceGrpc.OTTServiceBlockingStub ottServiceBlockingStub(Channel ottServiceChannel) {
        return OTTServiceGrpc.newBlockingStub(ottServiceChannel);
    }

    @Bean
    public LandingPageServiceGrpc.LandingPageServiceBlockingStub landingPageServiceBlockingStub(NamingClient namingClient) {
        String serviceName = "sycpb.cpm.mgk-portal";
        return LandingPageServiceGrpc.newBlockingStub(buildDefaultChannel(namingClient, serviceName));
    }

    private Channel buildDefaultChannel(NamingClient namingClient, String serviceName) {
        NamingResolver resolver = namingClient.resolveForReady(serviceName, Namings.Scheme.GRPC);

        return ChannelBuilder.forTarget(serviceName)
                .directExecutor()
                .disableRetry()
                .defaultLoadBalancingPolicy("round_robin")
                .usePlaintext()
                .keepAliveTime(1, TimeUnit.SECONDS)
                .keepAliveTimeout(100, TimeUnit.MILLISECONDS)
                .idleTimeout(1, TimeUnit.MINUTES)
                .nameResolverFactory(new RPCNamingClientNameResolverFactory(Env.getZone(), resolver))
                .build();
    }

    @Bean("locationChannel")
    public Channel locationChannel(NamingClient namingClient) {
        return buildDefaultChannel(namingClient, "sycpb.cpm.cpm-location");
    }

    @Bean
    public ResourceServiceGrpc.ResourceServiceBlockingStub resourceServiceBlockingStub(Channel locationChannel) {
        return ResourceServiceGrpc.newBlockingStub(locationChannel);
    }

    @Bean
    public ActivityGrpc.ActivityBlockingStub activityBlockingStub(Channel activityChannel) {
        return ActivityGrpc.newBlockingStub(activityChannel);
    }

    @Bean
    public RoomGrpc.RoomBlockingStub roomBlockingStub(Channel liveChannel) {
        return RoomGrpc.newBlockingStub(liveChannel);
    }

    @Bean
    public ReplyInterfaceGrpc.ReplyInterfaceBlockingStub replyInterfaceBlockingStub(Channel commentReplyUpdateChannel) {

        return ReplyInterfaceGrpc.newBlockingStub(commentReplyUpdateChannel);
    }

    @Bean
    public EpisodeGrpc.EpisodeBlockingStub episodeBlockingStub(NamingClient namingClient) {
        String name = "season.service";
        return EpisodeGrpc.newBlockingStub(buildDefaultChannel(namingClient, name));
    }

    @Bean
    public LandingPageServiceGrpc.LandingPageServiceBlockingStub mgkPageBlockingStub(@Qualifier("mgkChannel") Channel mgkChannel) {
        return LandingPageServiceGrpc.newBlockingStub(mgkChannel);
    }

    @Bean
    LandingPageGroupServiceGrpc.LandingPageGroupServiceBlockingStub mgkPageGroupBlockingStub(@Qualifier("mgkChannel") Channel mgkChannel) {
        return LandingPageGroupServiceGrpc.newBlockingStub(mgkChannel);
    }

    @Bean
    CreativeImageGrpc.CreativeImageBlockingStub creativeImageBlockingStub(@Qualifier("mgkChannel") Channel archive) {
        return CreativeImageGrpc.newBlockingStub(archive);
    }

    @Bean
    ExternalServiceGrpc.ExternalServiceFutureStub scvExternalServiceFutureStub(Channel scvChannel) {
        return ExternalServiceGrpc.newFutureStub(scvChannel);
    }

    @Bean
    MgkBusinessToolServiceGrpc.MgkBusinessToolServiceBlockingStub mgkBusinessToolServiceBlockingStub(@Qualifier("mgkChannel") Channel mgkChannel) {
        return MgkBusinessToolServiceGrpc.newBlockingStub(mgkChannel);
    }



    @Bean
    DynamicBlueLinkAuditServiceGrpc.DynamicBlueLinkAuditServiceBlockingStub blueLinkAuditServiceBlockingStub(@Qualifier("mgkChannel") Channel mgkChannel) {
        return DynamicBlueLinkAuditServiceGrpc.newBlockingStub(mgkChannel);
    }
}
