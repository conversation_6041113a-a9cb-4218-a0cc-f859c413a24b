package com.bilibili.adp.cpc.splash_screen.creative.service;

import com.bilibili.adp.cpc.splash_screen.creative.strategy.bos.CreativeContext;
import com.bilibili.adp.cpc.splash_screen.creative.strategy.context.ContextStrategy;
import com.bilibili.adp.cpc.splash_screen.creative.strategy.context.IContextStrategy;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Service
@RequiredArgsConstructor
public class SplashScreenContextService {
    private final List<IContextStrategy> strategies;

     private IContextStrategy contextStrategy(Integer promotionPurposeType) {
        return strategies
                .stream()
                .filter(strategy -> Objects.equals(promotionPurposeType, strategy.getPromotionPurposeType()))
                .findFirst()
                .orElse(new ContextStrategy() {
                    @Override
                    public Integer getPromotionPurposeType() {
                        return promotionPurposeType;
                    }
                });
    }

    public void buildContext(Integer promotionPurposeType, CreativeContext context) {
        final IContextStrategy contextStrategy = contextStrategy(promotionPurposeType);
        contextStrategy.buildContext(context);
    }
}
