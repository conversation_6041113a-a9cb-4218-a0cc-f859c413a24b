package com.bilibili.adp.cpc.splash_screen.operation_log;

import com.bilibili.adp.cpc.compare.ChangeBo;
import com.bilibili.adp.cpc.compare.CompareMeta;

import java.util.Collection;
import java.util.List;
import java.util.Map;

public interface IOperationLogService {
    <T> List<ChangeBo> changes(T oldVersion, T newVersion, Map<String, CompareMeta> metaMap, Integer metaType, boolean ignoreUnknown);
    void save(Collection<OperationContextBo> contextBos);
}
