package com.bilibili.adp.cpc.databus.sub;

import com.alibaba.fastjson.JSONObject;
import com.bilibili.adp.cpc.biz.bos.WarpDatabusProperty;
import com.bilibili.adp.cpc.biz.services.ad_product.IAdProductService;
import com.bilibili.adp.cpc.biz.services.game.api.ILauMiniGameService;
import com.bilibili.adp.cpc.databus.Constant;
import com.bilibili.adp.cpc.repo.AdProductRepo;
import com.bilibili.adp.cpc.repo.LauUnitCreativeRepo;
import com.bilibili.adp.launch.biz.ad_core.querydsl.dos.LauCreativeMiniGameMappingDo;
import com.bilibili.warp.databus.AckableMessage;
import com.bilibili.warp.databus.DatabusProperty;
import com.bilibili.warp.databus.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

@Component
@Slf4j
public class LauCreativeMiniGameBinlogSub implements MessageListener {


    @Autowired
    private AdProductRepo adProductRepo;

    @Autowired
    private IAdProductService adProductService;

    @Autowired
    private ILauMiniGameService lauMiniGameService;

    @Autowired
    private LauUnitCreativeRepo lauUnitCreativeRepo;

    private final String topic;

    private final String group;

    public LauCreativeMiniGameBinlogSub(WarpDatabusProperty warpDatabusProperty) {
        // 注入DatabusProperties，此处的ALIAS常量即配置文件中填写的别名，是topic的上一级配置键
        // pub场景同理，在使用DatabusTemplate的地方注入DatabusProperties来获取
        Map<String, DatabusProperty> properties = warpDatabusProperty.getProperties();
        DatabusProperty property = properties.get("lau_creative_mini_game");
        log.info("lau_creative_mini_game, property={}", JSONObject.toJSONString(property));
        this.topic = property.getTopic();
        this.group = property.getSub().getGroup();
    }

    @Override
    public String topic() {
        return this.topic;
    }

    @Override
    public String group() {
        return this.group;
    }


    @Override
    public void onMessage(AckableMessage message) {
        String value = new String(message.payload());
        JSONObject msg = JSONObject.parseObject(value);

        String action = msg.getString(Constant.ACTION);

        JSONObject newObject = msg.getJSONObject(Constant.NEW);
        LauCreativeMiniGameMappingDo lauCreativeMiniGameMappingDo = deserializeBinlogDto(newObject);

        if (Objects.equals(action, Constant.INSERT)) {
            adProductService.createMiniGameMapping(lauCreativeMiniGameMappingDo);
        }
        message.ack();
    }

    @Override
    public boolean autoCommit() {
        return Boolean.TRUE;
    }

    private LauCreativeMiniGameMappingDo deserializeBinlogDto(JSONObject jsonObject) {
        if (Objects.isNull(jsonObject)) {
            return null;
        }
        return jsonObject.toJavaObject(LauCreativeMiniGameMappingDo.class);

    }
}
