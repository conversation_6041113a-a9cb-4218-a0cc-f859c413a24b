package com.bilibili.adp.cpc.databus;

import com.bilibili.warp.databus.DatabusProperty;
import com.bilibili.warp.spring.boot.autoconfigure.databus.DatabusProperties;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import java.text.MessageFormat;

public class DatabusUtils {
    public static DatabusProperty getDatabusProperty(DatabusProperties databusProperties, String key) {
        Assert.isTrue(StringUtils.hasText(key), "databus配置的key不能为空");
        final DatabusProperty databusProperty = databusProperties.getProperties().get(key);
        Assert.notNull(databusProperty, MessageFormat.format("找不到key={0}对应的databus配置", key));
        Assert.isTrue(StringUtils.hasText(databusProperty.getTopic()), MessageFormat.format("key={0}对应的topic不能为空", key));
        return databusProperty;
    }
}
