package com.bilibili.adp.cpc.splash_screen.creative.service;

import com.bilibili.adp.cpc.dao.querydsl.pos.LauCreativeMonitoringPo;
import com.bilibili.adp.cpc.splash_screen.creative.bos.SplashScreenMonitoring;
import com.bilibili.adp.cpc.splash_screen.creative.converter.MonitoringConverter;
import com.bilibili.adp.cpc.utils.CommonFuncs;
import com.bilibili.adp.cpc.utils.RecDiffResult;
import com.bilibili.bjcom.querydsl.BaseQueryFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

import static com.bilibili.adp.cpc.dao.config.PlatformMySqlConfig.AD_BQF;
import static com.bilibili.adp.cpc.dao.querydsl.QLauCreativeMonitoring.lauCreativeMonitoring;

@Service
public class SplashScreenCreativeMonitoringService {
    @Resource(name = AD_BQF)
    private BaseQueryFactory adBqf;

    public static List<LauCreativeMonitoringPo> generatePos(Integer unitId, Integer creativeId, List<SplashScreenMonitoring> monitoringBos) {
        return monitoringBos
                .stream()
                .map(monitoring -> monitoring
                        .getUrls()
                        .stream()
                        .map(url -> MonitoringConverter.MAPPER.po(unitId, creativeId, monitoring.getType(), url))
                        .collect(Collectors.toList())
                )
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
    }

    public void save(Integer creativeId, List<LauCreativeMonitoringPo> pos) {
        final List<LauCreativeMonitoringPo> existingPos = list(creativeId);
        final RecDiffResult<LauCreativeMonitoringPo, Integer> result = CommonFuncs.recDiff(existingPos, pos, this::uk, LauCreativeMonitoringPo::getId, CommonFuncs.getDefaultBiFunction(LauCreativeMonitoringPo::getId, LauCreativeMonitoringPo::setId));
        CommonFuncs.handleRecDiff(result, adBqf, lauCreativeMonitoring, lauCreativeMonitoring.id::in);
    }

    public List<LauCreativeMonitoringPo> list(Integer creativeId) {
        return adBqf.selectFrom(lauCreativeMonitoring)
                .where(lauCreativeMonitoring.creativeId.eq(Long.valueOf(creativeId)))
                .fetch();
    }


    public String uk(LauCreativeMonitoringPo po) {
        return po.getCreativeId() + "-" + po.getType() + "-" + po.getUrl();
    }
}
