package com.bilibili.adp.cpc.automatic_rule.bos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ApplicableObjectBo {
    /**
     * 对象类型：1-账户,2-计划，3-单元，4-创意
     */
    private Integer objectType;
    /**
     * 父对象id
     */
    private Integer parentObjectId;
    /**
     * 对象id
     */
    private Integer objectId;
    /**
     * 对象名
     */
    private String objectName;
    /**
     * 子对象列表
     */
    private List<ApplicableObjectBo> objects;
}
