package com.bilibili.adp.cpc.splash_screen.config;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
public class SplashScreenUnitConfig {
    private List<LowestBid> lowestBid;
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LowestBid {
        private Integer salesType;
        private Integer promotionPurposeType;
        private Integer defaultLowestBid;
        private Integer labelId;
        private Integer configuredLowestBid;
    }
}
