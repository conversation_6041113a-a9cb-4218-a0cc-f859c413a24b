package com.bilibili.adp.cpc.biz.services.game.bos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @date 3/19/24
 **/
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LauAccountGameAvidMappingBo {

    private Integer accountId;

    private Integer agentId;

    private java.sql.Timestamp authEffectiveTime;

    private java.sql.Timestamp authExpireTime;

    private Long authMid;

    private Integer authMode;

    private Integer authStatus;

    private Integer authTimeType;

    private List<Long> avids;

    private java.sql.Timestamp ctime;

    private Integer customerId;

    private Long gameBaseId;

    private Long id;

    private Integer isDeleted;

    private java.sql.Timestamp mtime;

    private Integer renewalStatus;


}
