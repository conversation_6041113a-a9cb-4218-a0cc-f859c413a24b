package com.bilibili.adp.cpc.databus;

import com.bilibili.warp.databus.DatabusProperty;
import com.bilibili.warp.databus.DatabusTemplate;
import com.bilibili.warp.databus.Message;
import com.bilibili.warp.spring.boot.autoconfigure.databus.DatabusProperties;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

public class DatabusPrototypePub {
    private final String topic;
    private final String group;

    private final DatabusTemplate databusTemplate;

    public DatabusPrototypePub(String key, DatabusProperties databusProperties, DatabusTemplate databusTemplate) {
        final DatabusProperty databusProperty = DatabusUtils.getDatabusProperty(databusProperties, key);
        this.databusTemplate = databusTemplate;
        Assert.notNull(databusProperty.getPub(), "未找到生产者配置");
        topic = databusProperty.getTopic();
        group = databusProperty.getPub().getGroup();
        Assert.isTrue(StringUtils.hasText(group), "生产者分组不能为空");
    }

    public void pub(Message msg) {
        databusTemplate.pub(topic, group, msg);
    }
}
