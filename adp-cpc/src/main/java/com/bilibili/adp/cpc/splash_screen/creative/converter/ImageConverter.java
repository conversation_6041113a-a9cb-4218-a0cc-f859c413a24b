package com.bilibili.adp.cpc.splash_screen.creative.converter;

import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauSplashScreenCreativeImagePo;
import com.bilibili.adp.cpc.splash_screen.creative.bos.SplashScreenImage;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper
public interface ImageConverter {
    ImageConverter MAPPER = Mappers.getMapper(ImageConverter.class);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "isDeleted", ignore = true)
    @Mapping(target = "ctime", ignore = true)
    @Mapping(target = "mtime", ignore = true)
    LauSplashScreenCreativeImagePo bo2Po(Integer creativeId, SplashScreenImage bo);

    SplashScreenImage po2Bo(LauSplashScreenCreativeImagePo po);
}
