package com.bilibili.adp.cpc.wrapper;

import com.alibaba.fastjson.JSON;
import com.bapis.ad.audit.NativeBizType;
import com.bapis.ad.component.GamePlatformType;
import com.bilibili.adp.account.dto.AccountBaseDto;
import com.bilibili.adp.account.service.IQueryAccountService;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.enums.SalesType;
import com.bilibili.adp.common.exception.ServiceRuntimeException;
import com.bilibili.adp.common.util.EmojiUtils;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.biz.services.creative.FlyCreativeService;
import com.bilibili.adp.cpc.biz.services.creative.config.CreativePositionConfig;
import com.bilibili.adp.cpc.biz.services.creative.config.CreativeProgramConfig;
import com.bilibili.adp.cpc.biz.services.creative.native_ad.NativeAdPushAuditService;
import com.bilibili.adp.cpc.biz.services.creative.native_ad.dto.PushNativeAuditDto;
import com.bilibili.adp.cpc.biz.services.resource.AdpCpcResourceService;
import com.bilibili.adp.cpc.converter.FlyInvitationConverter;
import com.bilibili.adp.cpc.core.*;
import com.bilibili.adp.cpc.core.bos.*;
import com.bilibili.adp.cpc.core.constants.BusinessDomain;
import com.bilibili.adp.cpc.dao.querydsl.pos.LauUnitArchiveVideoPo;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauUnitCreativePo;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauUnitGamePo;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauUnitPo;
import com.bilibili.adp.cpc.databus.CreativeAuditDatabusPub;
import com.bilibili.adp.cpc.enums.AdpVersion;
import com.bilibili.adp.cpc.enums.ad.OcpcTargetEnum;
import com.bilibili.adp.cpc.enums.ad.PromotionPurposeType;
import com.bilibili.adp.cpc.utils.CommonFuncs;
import com.bilibili.adp.cpc.vo.fly.MiddleFlyInvitationInfoVo;
import com.bilibili.adp.cpc.wrapper.bos.*;
import com.bilibili.adp.launch.api.common.UnitStatus;
import com.bilibili.adp.launch.api.creative.dto.FlyInvitationDto;
import com.bilibili.adp.launch.biz.bean.audit.AuditCreativeMessage;
import com.bilibili.adp.launch.biz.service.account.LaunchAccountGroupService;
import com.bilibili.adp.resource.api.account.label.IAccountLabelService;
import jodd.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
@RequiredArgsConstructor
public class LaunchProgrammaticService {
    private final LaunchCoreCreativeService coreCreativeService;
    private final LaunchUnitV1Service launchUnitService;
    private final LaunchUnitGameV1Service launchUnitGameV1Service;
    private final LaunchArchiveService launchArchiveService;
    private final LaunchUnitArchiveService launchUnitArchiveService;
    private final LaunchResourceV1Service launchResourceV1Service;
    private final CreativeAuditDatabusPub creativeAuditDatabusPub;
    private final NativeAdPushAuditService nativeAdPushAuditService;
    private final LaunchAccountGroupService launchAccountGroupService;
    private final LaunchUnitCreativeService launchUnitCreativeService;
    private final FlyCreativeService flyCreativeService;
    private final IAccountLabelService accountLabelService;
    private final CreativePositionConfig creativePositionConfig;
    private final CreativeProgramConfig creativeProgramConfig;
    private final AdpCpcResourceService adpCpcResourceService;
    private final IQueryAccountService queryAccountService;

    @Value("${fly.prog.account.label:511}")
    private Integer flyProgAccountLabel;

    private static Set<Integer> supportedTargets = Stream.of(
            OcpcTargetEnum.VIDEO_PLAY,
            OcpcTargetEnum.COMMENT_CLICK,
            OcpcTargetEnum.FIRST_COMMENT_COPY,
            OcpcTargetEnum.GOODS_TRANSACTION,
            OcpcTargetEnum.WX_COPY,
            OcpcTargetEnum.FORM_SUBMIT,
            OcpcTargetEnum.APP_ACTIVE,
            OcpcTargetEnum.GAME_RESERVE,
            OcpcTargetEnum.LP_CALL_UP_SUCCESS,
            OcpcTargetEnum.USER_FOLLOW,
            OcpcTargetEnum.PAID_IN_7D_COST,
            OcpcTargetEnum.PAID_IN_24H_ROI,
            OcpcTargetEnum.LIVE_RESERVE)
            .map(OcpcTargetEnum::getKey)
            .collect(Collectors.toSet());

    public SupportProgrammaticResponseBo supportProgrammatic(Integer accountId, Integer unitId) {
        Assert.isTrue(Utils.isPositive(accountId), "账号id不能为空");

        final AccountBaseDto accountBaseDto = queryAccountService.getAccountBaseDtoByIdFromCache(accountId);
        if (Utils.isPositive(accountBaseDto.getIsSupportContent())) {
            return SupportProgrammaticResponseBo.builder()
                    .supportProgrammatic(false)
                    .rejectedReason("内容起飞不支持程序化创意")
                    .build();
        }
        final boolean ok = launchAccountGroupService.isAccountWithLabel(accountId, flyProgAccountLabel);
        if (!ok) {
            return reject("账号没有程序化创意的权限");
        }
        Assert.isTrue(Utils.isPositive(unitId), "单元id不能为空");
        final LauUnitPo lauUnitPo = launchUnitService.get(unitId);
        return supportProgrammatic(accountId, lauUnitPo);
    }

    private SupportProgrammaticResponseBo supportProgrammatic(Integer accountId, final LauUnitPo lauUnitPo) {
        Assert.notNull(lauUnitPo, "单元id对应的数据不存在");
        Integer unitId = lauUnitPo.getUnitId();
        Assert.isTrue(Objects.equals(accountId, lauUnitPo.getAccountId()), "不能查看不属于自己的单元");
        if (!Objects.equals(PromotionPurposeType.ARCHIVE_CONTENT.getCode(), lauUnitPo.getPromotionPurposeType())) {
            return reject("单元标的必须为稿件");
        }

        if (!Objects.equals(lauUnitPo.getSalesType(), SalesType.CPM.getCode()) || !Utils.isPositive(lauUnitPo.getOcpcTarget())) {
            return reject("出价方式必须为oCPM");
        }

        if (!supportedTargets.contains(lauUnitPo.getOcpcTarget())) {
            return reject("优化目标暂不支持");
        }
        final List<Integer> programmaticCreativeStatus = launchUnitCreativeService.programmaticCreativeStatus(unitId);
        if (programmaticCreativeStatus.contains(0)) {
            return reject("当前单元下已经存在非程序化创意");
        }
        return new SupportProgrammaticResponseBo(true, "");
    }

    private SupportProgrammaticResponseBo reject(String reason) {
        return new SupportProgrammaticResponseBo(false, reason);
    }

    public GetProgrammaticCreativeResponseBo get(Integer accountId, Integer unitId) {
        Assert.isTrue(Utils.isPositive(unitId), "单元id不能为空");
        final LauUnitPo lauUnitPo = launchUnitService.get(unitId);
        Assert.notNull(lauUnitPo, "单元id对应的数据不存在");
        Assert.isTrue(Objects.equals(accountId, lauUnitPo.getAccountId()), "不能查看不属于自己的单元");
        final GetUnitCreativeContextBo ctx = GetUnitCreativeContextBo.builder()
                .lauUnitPo(lauUnitPo)
                .build();
        Assert.isTrue(UnitStatus.NON_DELETED_UNIT_STATUS_LIST.contains(lauUnitPo.getUnitStatus()),
                "当前单元已删除,不支持查看起飞程序化创意");
        Assert.isTrue(PromotionPurposeType.ARCHIVE_CONTENT.getCode() == lauUnitPo.getPromotionPurposeType(),
                "当前单元非投稿单元,不支持起飞程序化创意");
        Assert.isTrue(SalesType.CPM.getCode() == lauUnitPo.getSalesType(), "当前单元非CPM/OCPM单元,不支持查看起飞程序化创意");
        final UnitCreativeBo unitCreative = coreCreativeService.getUnitCreatives(unitId, ctx);
        final GetProgrammaticCreativeResponseBo response = new GetProgrammaticCreativeResponseBo();
        if (CollectionUtils.isEmpty(unitCreative.getCreatives())) return response;
        Assert.isTrue(unitCreative.getIsProgrammatic(), "当前单元下非程序化创意,不支持查看起飞程序化创意");

        BeanUtils.copyProperties(unitCreative, response);
        response.setVideoId(CommonFuncs.long2Str(unitCreative.getVideoId()));
        final CreativeWrapperBo creative = new CreativeWrapperBo();
        response.setBusinessCategory(UnitBusinessCategoryWrapperBo.fromCoreBo(unitCreative.getBusinessCategory()));
        response.setMonitoring(UnitMonitorWrapperBo.fromCoreBo(unitCreative.getMonitoring()));
        if (Utils.isPositive(unitCreative.getVideoId())) {
            response.setNativeLandingPageArchiveInfo(launchArchiveService.getArchiveWrapper(unitCreative.getVideoId()));
        }
        response.setCreative(creative);
        BeanUtils.copyProperties(unitCreative.getCreatives().get(0), creative);
        final MultiMaterialWrapperBo multiMaterialWrapper = MultiMaterialWrapperBo.fromCoreBo(unitCreative.getCreatives().get(0).getMultiMaterial());
        creative.setMultiMaterial(multiMaterialWrapper);
        //邀约组件信息
        Map<Integer, List<FlyInvitationDto>> flyInvitationDtos = flyCreativeService.processFlyInvitationInfo(unitId, Arrays.asList(creative.getCreativeId()));
        //只有一个创意
        if (!flyInvitationDtos.isEmpty()) {
            List<MiddleFlyInvitationInfoVo> vos = FlyInvitationConverter.flyInvitationDtos2Vos(flyInvitationDtos.get(creative.getCreativeId()));
            response.setFly_invitation_infos(vos);
        }
        // 起飞创意化创意特殊处理
        this.processFly43TemplateGroupId(response,accountId);
        return response;
    }

    private void processFly43TemplateGroupId(GetProgrammaticCreativeResponseBo bo,Integer accountId){
        boolean supportSmallCard43 = accountLabelService.isAccountIdInLabel(accountId,creativePositionConfig.getFlySmallCard43LabelId());
        // 处理86模版组的指定场景，仅选小卡(8)创意, 在白名单
        if(bo!=null && !bo.getIsPreferScene() && !CollectionUtils.isEmpty(bo.getSceneIds()) &&
                bo.getSceneIds().size() == 1 && bo.getSceneIds().contains(8) && supportSmallCard43){
            if(bo.getCreative()!=null && bo.getCreative().getMultiMaterial()!=null &&
                    !CollectionUtils.isEmpty(bo.getCreative().getMultiMaterial().getImageGroups())){
                List<ImageGroupWrapperBo> imageGroupWrapperBos = bo.getCreative().getMultiMaterial().getImageGroups();
                imageGroupWrapperBos.forEach(o->{
                    if(creativeProgramConfig.getFlyProgramCommonTemplateGroupId().equals(o.getTemplateGroupId())){
                        o.setTemplateGroupId(creativeProgramConfig.getFlyProgram43TemplateGroupId());
                        o.getImages().forEach(p->{
                            p.setMd5("");
                            p.setUrl("");
                        });
                    }
                });
            }
        }
        // 不在白名单
        if(!supportSmallCard43){
            if(bo!=null && bo.getCreative()!=null && bo.getCreative().getMultiMaterial()!=null &&
                    !CollectionUtils.isEmpty(bo.getCreative().getMultiMaterial().getImageGroups())){
                List<ImageGroupWrapperBo> imageGroupWrapperBos = bo.getCreative().getMultiMaterial().getImageGroups();
                imageGroupWrapperBos.forEach(o->{
                    if(creativeProgramConfig.getFlyProgram43TemplateGroupId().equals(o.getTemplateGroupId())){
                        o.setTemplateGroupId(creativeProgramConfig.getFlyProgramCommonTemplateGroupId());
                        o.getImages().forEach(p->{
                            p.setMd5("");
                            p.setUrl("");
                        });
                    }
                });
            }
        }
    }

    public SaveProgrammaticCreativeResponseBo save(SaveProgrammaticCreativeRequestBo bo, Operator operator) {
        Assert.isTrue(Utils.isPositive(bo.getUnitId()), "单元id不能为空");
        final LauUnitPo lauUnitPo = launchUnitService.get(bo.getUnitId());
        Assert.notNull(lauUnitPo, "单元id对应的数据不存在");
        Assert.isTrue(Objects.equals(operator.getOperatorId(), lauUnitPo.getAccountId()), "不能编辑不属于自己的单元");
        Assert.isTrue(lauUnitPo.getAdpVersion().equals(AdpVersion.MIDDLE.getKey()), "目前仅支持在三连推广平台单元下保存起飞程序化创意");
        List<LauUnitCreativePo> creativePoList = launchUnitCreativeService.list(bo.getUnitId());
        Assert.isTrue(creativePoList.size() <= 1, "一个单元下只能有一个起飞程序化创意");
        if (!CollectionUtils.isEmpty(creativePoList)) {
            LauUnitCreativePo creativePo = creativePoList.get(0);
            Assert.isTrue(creativePo.getCreativeId().equals(bo.getCreative().getCreativeId()),
                    "当前单元下已经存在一个起飞程序化创意");
            Assert.isTrue(Utils.isPositive(creativePo.getIsProgrammatic()), "当前单元下已经存在一个非起飞程序化创意");
        }


        SupportProgrammaticResponseBo supportProgrammaticResponseBo = supportProgrammatic(operator.getOperatorId(), lauUnitPo);
        Assert.isTrue(supportProgrammaticResponseBo.getSupportProgrammatic(), supportProgrammaticResponseBo.getRejectedReason());

        final boolean isFly = BusinessDomain.isFly(lauUnitPo.getBusinessDomain());
        final SaveUnitCreativeContextBo ctx = SaveUnitCreativeContextBo.builder()
                .operator(operator)
                .unit(lauUnitPo)
                .build();

        final UnitCreativeBo unitCreative = UnitCreativeBo.builder()
                .unitId(bo.getUnitId())
                .channelId(bo.getChannelId())
                .isPreferScene(bo.getIsPreferScene())
                .sceneIds(bo.getSceneIds())
                .isProgrammatic(true)
                .cmMarkId(Optional.ofNullable(bo.getCmMark()).orElse(1))
                .brandInfoId(bo.getBrandInfoId())
                .spaceMid(bo.getSpaceMid())
                .tags(bo.getTags())
                .isSmartMaterial(bo.getIsSmartMaterial())
                .build();
        Optional.ofNullable(bo.getMonitoring()).map(UnitMonitorWrapperBo::toCoreBo).ifPresent(unitCreative::setMonitoring);

        final AccountBaseDto accountBaseDto = queryAccountService.getAccountBaseDtoByIdFromCache(operator.getOperatorId());
        boolean isInner = Objects.equals(accountBaseDto.getIsInner(), 1);
        // 安卓游戏，生成监测链接
        String gameMonitoringUrl = this.getGameMonitoringUrl(bo.getUnitId(), isInner);
        if (StringUtil.isNotEmpty(gameMonitoringUrl)) {
            unitCreative.getMonitoring().setGameHiddenUrl(gameMonitoringUrl);
        }
        Optional.ofNullable(bo.getBusinessCategory()).map(UnitBusinessCategoryWrapperBo::toCoreBo).ifPresent(unitCreative::setBusinessCategory);
        final CreativeBo creative = new CreativeBo();
        BeanUtils.copyProperties(bo.getCreative(), creative);
        if (bo.getCreative() != null) {
            // emoji 表情处理
            creative.setCreativeName(EmojiUtils.filterEmoji(bo.getCreative().getCreativeName(), "?"));
            creative.setDescription(EmojiUtils.filterEmoji(bo.getCreative().getDescription(), "?"));
            creative.setExtDescription(EmojiUtils.filterEmoji(bo.getCreative().getExtDescription(), "?"));
        }

        creative.setMultiMaterial(bo.getCreative().getMultiMaterial().toCoreBo());
        if (isFly) {
            if (!CollectionUtils.isEmpty(bo.getCreative().getComponents())) {
                // 起飞原生落地页需要有创意组件填充
                ctx.setNativeLandingPageStyle(true);
            }
            // 先写死创作推广, 后面考虑改成可配
            creative.setBusMarkId(launchResourceV1Service.getContentFallbackBusMarkId());
            final LauUnitArchiveVideoPo lauUnitArchiveVideoPo = launchUnitArchiveService.get(bo.getUnitId());
            if (Objects.nonNull(lauUnitArchiveVideoPo)) {
                // 如果单元上绑定了稿件(起飞), 原生稿件使用单元上绑定的稿件
                unitCreative.setVideoId(lauUnitArchiveVideoPo.getVideoId());
            } else if (Utils.isPositive(bo.getVideoId())) {
                unitCreative.setVideoId(bo.getVideoId());
                // 回写单元绑定的稿件
                launchUnitArchiveService.insert(bo.getUnitId(), bo.getVideoId());
            } else {
                throw new IllegalArgumentException("单元上缺少绑定的稿件");
            }
        } else {
            unitCreative.setVideoId(bo.getVideoId());
        }
        //邀约组件
        unitCreative.setMiddleFlyInvitationInfoVos(bo.getFly_invitation_infos());

        unitCreative.setCreatives(Collections.singletonList(creative));

        // 保存创意
        ProgrammaticSaveResultBo saveResultBo = coreCreativeService.saveUnitCreatives(unitCreative, ctx);
        final List<Integer> creativeIds = saveResultBo.getCreativeIds();
        if (isFly) {
            adpCpcResourceService.updateUnitOcpxInfoOnCreativeChange(bo.getUnitId());
        }

        // 是否触发推审
        if (ctx.isTriggerAudit()) {
            try {
//                final Map<String, List<Integer>> map = new HashMap<>();
//                map.put("creativeId", creativeIds);
                AuditCreativeMessage auditCreativeMessage = AuditCreativeMessage.builder().creativeId(creativeIds).build();
                creativeAuditDatabusPub.pubMsg(auditCreativeMessage);
                log.info("起飞程序化创意推审: {}", creativeIds);

                // 原生推审
                if (Utils.isPositive(saveResultBo.getFlyAvid())) {
                    PushNativeAuditDto pushNativeAuditDto = PushNativeAuditDto.builder()
                            .avid(saveResultBo.getFlyAvid())
                            .creativeIds(saveResultBo.getCreativeIds())
                            .bizType(NativeBizType.SANLIAN_VALUE)
                            .build();
                    nativeAdPushAuditService.nativeArchivePushAudit(pushNativeAuditDto);
                    nativeAdPushAuditService.nativeCreativePushAudit(pushNativeAuditDto);
                }
            } catch (Exception e) {
                throw new ServiceRuntimeException("序列化推审消息失败");
            } catch (Throwable t) {
                log.error("生成审核消息失败", t);
                throw new ServiceRuntimeException(MessageFormat.format("生成审核消息失败: {0}", t.getMessage()));
            }
        }
        return SaveProgrammaticCreativeResponseBo.builder()
                .creativeId(creativeIds.get(0))
                .build();
    }

    private String getGameMonitoringUrl(Integer unitId, boolean isInner) {
        String gameMonitoringUrl = "";
        LauUnitGamePo lauUnitGame = launchUnitGameV1Service.get(unitId);
        if (lauUnitGame != null) {
            if (Utils.isPositive(lauUnitGame.getGameBaseId()) && lauUnitGame.getPlatformType().equals(GamePlatformType.ANDROID_VALUE)) {
                gameMonitoringUrl = launchUnitGameV1Service.genGameHiddenMonitoringUrl(lauUnitGame.getGameBaseId(), lauUnitGame.getPlatformType(), lauUnitGame.getSubPkg(), isInner);
            }
        }
        return gameMonitoringUrl;
    }
}
