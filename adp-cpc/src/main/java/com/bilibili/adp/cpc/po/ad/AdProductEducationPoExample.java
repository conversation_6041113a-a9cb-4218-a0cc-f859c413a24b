package com.bilibili.adp.cpc.po.ad;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public class AdProductEducationPoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public AdProductEducationPoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAccountIdIsNull() {
            addCriterion("account_id is null");
            return (Criteria) this;
        }

        public Criteria andAccountIdIsNotNull() {
            addCriterion("account_id is not null");
            return (Criteria) this;
        }

        public Criteria andAccountIdEqualTo(Integer value) {
            addCriterion("account_id =", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdNotEqualTo(Integer value) {
            addCriterion("account_id <>", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdGreaterThan(Integer value) {
            addCriterion("account_id >", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("account_id >=", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdLessThan(Integer value) {
            addCriterion("account_id <", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdLessThanOrEqualTo(Integer value) {
            addCriterion("account_id <=", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdIn(List<Integer> values) {
            addCriterion("account_id in", values, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdNotIn(List<Integer> values) {
            addCriterion("account_id not in", values, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdBetween(Integer value1, Integer value2) {
            addCriterion("account_id between", value1, value2, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdNotBetween(Integer value1, Integer value2) {
            addCriterion("account_id not between", value1, value2, "accountId");
            return (Criteria) this;
        }

        public Criteria andLibraryIdIsNull() {
            addCriterion("library_id is null");
            return (Criteria) this;
        }

        public Criteria andLibraryIdIsNotNull() {
            addCriterion("library_id is not null");
            return (Criteria) this;
        }

        public Criteria andLibraryIdEqualTo(Long value) {
            addCriterion("library_id =", value, "libraryId");
            return (Criteria) this;
        }

        public Criteria andLibraryIdNotEqualTo(Long value) {
            addCriterion("library_id <>", value, "libraryId");
            return (Criteria) this;
        }

        public Criteria andLibraryIdGreaterThan(Long value) {
            addCriterion("library_id >", value, "libraryId");
            return (Criteria) this;
        }

        public Criteria andLibraryIdGreaterThanOrEqualTo(Long value) {
            addCriterion("library_id >=", value, "libraryId");
            return (Criteria) this;
        }

        public Criteria andLibraryIdLessThan(Long value) {
            addCriterion("library_id <", value, "libraryId");
            return (Criteria) this;
        }

        public Criteria andLibraryIdLessThanOrEqualTo(Long value) {
            addCriterion("library_id <=", value, "libraryId");
            return (Criteria) this;
        }

        public Criteria andLibraryIdIn(List<Long> values) {
            addCriterion("library_id in", values, "libraryId");
            return (Criteria) this;
        }

        public Criteria andLibraryIdNotIn(List<Long> values) {
            addCriterion("library_id not in", values, "libraryId");
            return (Criteria) this;
        }

        public Criteria andLibraryIdBetween(Long value1, Long value2) {
            addCriterion("library_id between", value1, value2, "libraryId");
            return (Criteria) this;
        }

        public Criteria andLibraryIdNotBetween(Long value1, Long value2) {
            addCriterion("library_id not between", value1, value2, "libraryId");
            return (Criteria) this;
        }

        public Criteria andAdProductIdIsNull() {
            addCriterion("ad_product_id is null");
            return (Criteria) this;
        }

        public Criteria andAdProductIdIsNotNull() {
            addCriterion("ad_product_id is not null");
            return (Criteria) this;
        }

        public Criteria andAdProductIdEqualTo(Long value) {
            addCriterion("ad_product_id =", value, "adProductId");
            return (Criteria) this;
        }

        public Criteria andAdProductIdNotEqualTo(Long value) {
            addCriterion("ad_product_id <>", value, "adProductId");
            return (Criteria) this;
        }

        public Criteria andAdProductIdGreaterThan(Long value) {
            addCriterion("ad_product_id >", value, "adProductId");
            return (Criteria) this;
        }

        public Criteria andAdProductIdGreaterThanOrEqualTo(Long value) {
            addCriterion("ad_product_id >=", value, "adProductId");
            return (Criteria) this;
        }

        public Criteria andAdProductIdLessThan(Long value) {
            addCriterion("ad_product_id <", value, "adProductId");
            return (Criteria) this;
        }

        public Criteria andAdProductIdLessThanOrEqualTo(Long value) {
            addCriterion("ad_product_id <=", value, "adProductId");
            return (Criteria) this;
        }

        public Criteria andAdProductIdIn(List<Long> values) {
            addCriterion("ad_product_id in", values, "adProductId");
            return (Criteria) this;
        }

        public Criteria andAdProductIdNotIn(List<Long> values) {
            addCriterion("ad_product_id not in", values, "adProductId");
            return (Criteria) this;
        }

        public Criteria andAdProductIdBetween(Long value1, Long value2) {
            addCriterion("ad_product_id between", value1, value2, "adProductId");
            return (Criteria) this;
        }

        public Criteria andAdProductIdNotBetween(Long value1, Long value2) {
            addCriterion("ad_product_id not between", value1, value2, "adProductId");
            return (Criteria) this;
        }

        public Criteria andBizStatusIsNull() {
            addCriterion("biz_status is null");
            return (Criteria) this;
        }

        public Criteria andBizStatusIsNotNull() {
            addCriterion("biz_status is not null");
            return (Criteria) this;
        }

        public Criteria andBizStatusEqualTo(Integer value) {
            addCriterion("biz_status =", value, "bizStatus");
            return (Criteria) this;
        }

        public Criteria andBizStatusNotEqualTo(Integer value) {
            addCriterion("biz_status <>", value, "bizStatus");
            return (Criteria) this;
        }

        public Criteria andBizStatusGreaterThan(Integer value) {
            addCriterion("biz_status >", value, "bizStatus");
            return (Criteria) this;
        }

        public Criteria andBizStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("biz_status >=", value, "bizStatus");
            return (Criteria) this;
        }

        public Criteria andBizStatusLessThan(Integer value) {
            addCriterion("biz_status <", value, "bizStatus");
            return (Criteria) this;
        }

        public Criteria andBizStatusLessThanOrEqualTo(Integer value) {
            addCriterion("biz_status <=", value, "bizStatus");
            return (Criteria) this;
        }

        public Criteria andBizStatusIn(List<Integer> values) {
            addCriterion("biz_status in", values, "bizStatus");
            return (Criteria) this;
        }

        public Criteria andBizStatusNotIn(List<Integer> values) {
            addCriterion("biz_status not in", values, "bizStatus");
            return (Criteria) this;
        }

        public Criteria andBizStatusBetween(Integer value1, Integer value2) {
            addCriterion("biz_status between", value1, value2, "bizStatus");
            return (Criteria) this;
        }

        public Criteria andBizStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("biz_status not between", value1, value2, "bizStatus");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andTrialPriceIsNull() {
            addCriterion("trial_price is null");
            return (Criteria) this;
        }

        public Criteria andTrialPriceIsNotNull() {
            addCriterion("trial_price is not null");
            return (Criteria) this;
        }

        public Criteria andTrialPriceEqualTo(String value) {
            addCriterion("trial_price =", value, "trialPrice");
            return (Criteria) this;
        }

        public Criteria andTrialPriceNotEqualTo(String value) {
            addCriterion("trial_price <>", value, "trialPrice");
            return (Criteria) this;
        }

        public Criteria andTrialPriceGreaterThan(String value) {
            addCriterion("trial_price >", value, "trialPrice");
            return (Criteria) this;
        }

        public Criteria andTrialPriceGreaterThanOrEqualTo(String value) {
            addCriterion("trial_price >=", value, "trialPrice");
            return (Criteria) this;
        }

        public Criteria andTrialPriceLessThan(String value) {
            addCriterion("trial_price <", value, "trialPrice");
            return (Criteria) this;
        }

        public Criteria andTrialPriceLessThanOrEqualTo(String value) {
            addCriterion("trial_price <=", value, "trialPrice");
            return (Criteria) this;
        }

        public Criteria andTrialPriceLike(String value) {
            addCriterion("trial_price like", value, "trialPrice");
            return (Criteria) this;
        }

        public Criteria andTrialPriceNotLike(String value) {
            addCriterion("trial_price not like", value, "trialPrice");
            return (Criteria) this;
        }

        public Criteria andTrialPriceIn(List<String> values) {
            addCriterion("trial_price in", values, "trialPrice");
            return (Criteria) this;
        }

        public Criteria andTrialPriceNotIn(List<String> values) {
            addCriterion("trial_price not in", values, "trialPrice");
            return (Criteria) this;
        }

        public Criteria andTrialPriceBetween(String value1, String value2) {
            addCriterion("trial_price between", value1, value2, "trialPrice");
            return (Criteria) this;
        }

        public Criteria andTrialPriceNotBetween(String value1, String value2) {
            addCriterion("trial_price not between", value1, value2, "trialPrice");
            return (Criteria) this;
        }

        public Criteria andPositivePriceIsNull() {
            addCriterion("positive_price is null");
            return (Criteria) this;
        }

        public Criteria andPositivePriceIsNotNull() {
            addCriterion("positive_price is not null");
            return (Criteria) this;
        }

        public Criteria andPositivePriceEqualTo(String value) {
            addCriterion("positive_price =", value, "positivePrice");
            return (Criteria) this;
        }

        public Criteria andPositivePriceNotEqualTo(String value) {
            addCriterion("positive_price <>", value, "positivePrice");
            return (Criteria) this;
        }

        public Criteria andPositivePriceGreaterThan(String value) {
            addCriterion("positive_price >", value, "positivePrice");
            return (Criteria) this;
        }

        public Criteria andPositivePriceGreaterThanOrEqualTo(String value) {
            addCriterion("positive_price >=", value, "positivePrice");
            return (Criteria) this;
        }

        public Criteria andPositivePriceLessThan(String value) {
            addCriterion("positive_price <", value, "positivePrice");
            return (Criteria) this;
        }

        public Criteria andPositivePriceLessThanOrEqualTo(String value) {
            addCriterion("positive_price <=", value, "positivePrice");
            return (Criteria) this;
        }

        public Criteria andPositivePriceLike(String value) {
            addCriterion("positive_price like", value, "positivePrice");
            return (Criteria) this;
        }

        public Criteria andPositivePriceNotLike(String value) {
            addCriterion("positive_price not like", value, "positivePrice");
            return (Criteria) this;
        }

        public Criteria andPositivePriceIn(List<String> values) {
            addCriterion("positive_price in", values, "positivePrice");
            return (Criteria) this;
        }

        public Criteria andPositivePriceNotIn(List<String> values) {
            addCriterion("positive_price not in", values, "positivePrice");
            return (Criteria) this;
        }

        public Criteria andPositivePriceBetween(String value1, String value2) {
            addCriterion("positive_price between", value1, value2, "positivePrice");
            return (Criteria) this;
        }

        public Criteria andPositivePriceNotBetween(String value1, String value2) {
            addCriterion("positive_price not between", value1, value2, "positivePrice");
            return (Criteria) this;
        }

        public Criteria andAreaIsNull() {
            addCriterion("area is null");
            return (Criteria) this;
        }

        public Criteria andAreaIsNotNull() {
            addCriterion("area is not null");
            return (Criteria) this;
        }

        public Criteria andAreaEqualTo(String value) {
            addCriterion("area =", value, "area");
            return (Criteria) this;
        }

        public Criteria andAreaNotEqualTo(String value) {
            addCriterion("area <>", value, "area");
            return (Criteria) this;
        }

        public Criteria andAreaGreaterThan(String value) {
            addCriterion("area >", value, "area");
            return (Criteria) this;
        }

        public Criteria andAreaGreaterThanOrEqualTo(String value) {
            addCriterion("area >=", value, "area");
            return (Criteria) this;
        }

        public Criteria andAreaLessThan(String value) {
            addCriterion("area <", value, "area");
            return (Criteria) this;
        }

        public Criteria andAreaLessThanOrEqualTo(String value) {
            addCriterion("area <=", value, "area");
            return (Criteria) this;
        }

        public Criteria andAreaLike(String value) {
            addCriterion("area like", value, "area");
            return (Criteria) this;
        }

        public Criteria andAreaNotLike(String value) {
            addCriterion("area not like", value, "area");
            return (Criteria) this;
        }

        public Criteria andAreaIn(List<String> values) {
            addCriterion("area in", values, "area");
            return (Criteria) this;
        }

        public Criteria andAreaNotIn(List<String> values) {
            addCriterion("area not in", values, "area");
            return (Criteria) this;
        }

        public Criteria andAreaBetween(String value1, String value2) {
            addCriterion("area between", value1, value2, "area");
            return (Criteria) this;
        }

        public Criteria andAreaNotBetween(String value1, String value2) {
            addCriterion("area not between", value1, value2, "area");
            return (Criteria) this;
        }

        public Criteria andBrandIsNull() {
            addCriterion("brand is null");
            return (Criteria) this;
        }

        public Criteria andBrandIsNotNull() {
            addCriterion("brand is not null");
            return (Criteria) this;
        }

        public Criteria andBrandEqualTo(String value) {
            addCriterion("brand =", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotEqualTo(String value) {
            addCriterion("brand <>", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandGreaterThan(String value) {
            addCriterion("brand >", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandGreaterThanOrEqualTo(String value) {
            addCriterion("brand >=", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandLessThan(String value) {
            addCriterion("brand <", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandLessThanOrEqualTo(String value) {
            addCriterion("brand <=", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandLike(String value) {
            addCriterion("brand like", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotLike(String value) {
            addCriterion("brand not like", value, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandIn(List<String> values) {
            addCriterion("brand in", values, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotIn(List<String> values) {
            addCriterion("brand not in", values, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandBetween(String value1, String value2) {
            addCriterion("brand between", value1, value2, "brand");
            return (Criteria) this;
        }

        public Criteria andBrandNotBetween(String value1, String value2) {
            addCriterion("brand not between", value1, value2, "brand");
            return (Criteria) this;
        }

        public Criteria andSellPointIsNull() {
            addCriterion("sell_point is null");
            return (Criteria) this;
        }

        public Criteria andSellPointIsNotNull() {
            addCriterion("sell_point is not null");
            return (Criteria) this;
        }

        public Criteria andSellPointEqualTo(String value) {
            addCriterion("sell_point =", value, "sellPoint");
            return (Criteria) this;
        }

        public Criteria andSellPointNotEqualTo(String value) {
            addCriterion("sell_point <>", value, "sellPoint");
            return (Criteria) this;
        }

        public Criteria andSellPointGreaterThan(String value) {
            addCriterion("sell_point >", value, "sellPoint");
            return (Criteria) this;
        }

        public Criteria andSellPointGreaterThanOrEqualTo(String value) {
            addCriterion("sell_point >=", value, "sellPoint");
            return (Criteria) this;
        }

        public Criteria andSellPointLessThan(String value) {
            addCriterion("sell_point <", value, "sellPoint");
            return (Criteria) this;
        }

        public Criteria andSellPointLessThanOrEqualTo(String value) {
            addCriterion("sell_point <=", value, "sellPoint");
            return (Criteria) this;
        }

        public Criteria andSellPointLike(String value) {
            addCriterion("sell_point like", value, "sellPoint");
            return (Criteria) this;
        }

        public Criteria andSellPointNotLike(String value) {
            addCriterion("sell_point not like", value, "sellPoint");
            return (Criteria) this;
        }

        public Criteria andSellPointIn(List<String> values) {
            addCriterion("sell_point in", values, "sellPoint");
            return (Criteria) this;
        }

        public Criteria andSellPointNotIn(List<String> values) {
            addCriterion("sell_point not in", values, "sellPoint");
            return (Criteria) this;
        }

        public Criteria andSellPointBetween(String value1, String value2) {
            addCriterion("sell_point between", value1, value2, "sellPoint");
            return (Criteria) this;
        }

        public Criteria andSellPointNotBetween(String value1, String value2) {
            addCriterion("sell_point not between", value1, value2, "sellPoint");
            return (Criteria) this;
        }

        public Criteria andAgeIsNull() {
            addCriterion("age is null");
            return (Criteria) this;
        }

        public Criteria andAgeIsNotNull() {
            addCriterion("age is not null");
            return (Criteria) this;
        }

        public Criteria andAgeEqualTo(String value) {
            addCriterion("age =", value, "age");
            return (Criteria) this;
        }

        public Criteria andAgeNotEqualTo(String value) {
            addCriterion("age <>", value, "age");
            return (Criteria) this;
        }

        public Criteria andAgeGreaterThan(String value) {
            addCriterion("age >", value, "age");
            return (Criteria) this;
        }

        public Criteria andAgeGreaterThanOrEqualTo(String value) {
            addCriterion("age >=", value, "age");
            return (Criteria) this;
        }

        public Criteria andAgeLessThan(String value) {
            addCriterion("age <", value, "age");
            return (Criteria) this;
        }

        public Criteria andAgeLessThanOrEqualTo(String value) {
            addCriterion("age <=", value, "age");
            return (Criteria) this;
        }

        public Criteria andAgeLike(String value) {
            addCriterion("age like", value, "age");
            return (Criteria) this;
        }

        public Criteria andAgeNotLike(String value) {
            addCriterion("age not like", value, "age");
            return (Criteria) this;
        }

        public Criteria andAgeIn(List<String> values) {
            addCriterion("age in", values, "age");
            return (Criteria) this;
        }

        public Criteria andAgeNotIn(List<String> values) {
            addCriterion("age not in", values, "age");
            return (Criteria) this;
        }

        public Criteria andAgeBetween(String value1, String value2) {
            addCriterion("age between", value1, value2, "age");
            return (Criteria) this;
        }

        public Criteria andAgeNotBetween(String value1, String value2) {
            addCriterion("age not between", value1, value2, "age");
            return (Criteria) this;
        }

        public Criteria andTeachChannelIsNull() {
            addCriterion("teach_channel is null");
            return (Criteria) this;
        }

        public Criteria andTeachChannelIsNotNull() {
            addCriterion("teach_channel is not null");
            return (Criteria) this;
        }

        public Criteria andTeachChannelEqualTo(String value) {
            addCriterion("teach_channel =", value, "teachChannel");
            return (Criteria) this;
        }

        public Criteria andTeachChannelNotEqualTo(String value) {
            addCriterion("teach_channel <>", value, "teachChannel");
            return (Criteria) this;
        }

        public Criteria andTeachChannelGreaterThan(String value) {
            addCriterion("teach_channel >", value, "teachChannel");
            return (Criteria) this;
        }

        public Criteria andTeachChannelGreaterThanOrEqualTo(String value) {
            addCriterion("teach_channel >=", value, "teachChannel");
            return (Criteria) this;
        }

        public Criteria andTeachChannelLessThan(String value) {
            addCriterion("teach_channel <", value, "teachChannel");
            return (Criteria) this;
        }

        public Criteria andTeachChannelLessThanOrEqualTo(String value) {
            addCriterion("teach_channel <=", value, "teachChannel");
            return (Criteria) this;
        }

        public Criteria andTeachChannelLike(String value) {
            addCriterion("teach_channel like", value, "teachChannel");
            return (Criteria) this;
        }

        public Criteria andTeachChannelNotLike(String value) {
            addCriterion("teach_channel not like", value, "teachChannel");
            return (Criteria) this;
        }

        public Criteria andTeachChannelIn(List<String> values) {
            addCriterion("teach_channel in", values, "teachChannel");
            return (Criteria) this;
        }

        public Criteria andTeachChannelNotIn(List<String> values) {
            addCriterion("teach_channel not in", values, "teachChannel");
            return (Criteria) this;
        }

        public Criteria andTeachChannelBetween(String value1, String value2) {
            addCriterion("teach_channel between", value1, value2, "teachChannel");
            return (Criteria) this;
        }

        public Criteria andTeachChannelNotBetween(String value1, String value2) {
            addCriterion("teach_channel not between", value1, value2, "teachChannel");
            return (Criteria) this;
        }

        public Criteria andTeachTypeIsNull() {
            addCriterion("teach_type is null");
            return (Criteria) this;
        }

        public Criteria andTeachTypeIsNotNull() {
            addCriterion("teach_type is not null");
            return (Criteria) this;
        }

        public Criteria andTeachTypeEqualTo(String value) {
            addCriterion("teach_type =", value, "teachType");
            return (Criteria) this;
        }

        public Criteria andTeachTypeNotEqualTo(String value) {
            addCriterion("teach_type <>", value, "teachType");
            return (Criteria) this;
        }

        public Criteria andTeachTypeGreaterThan(String value) {
            addCriterion("teach_type >", value, "teachType");
            return (Criteria) this;
        }

        public Criteria andTeachTypeGreaterThanOrEqualTo(String value) {
            addCriterion("teach_type >=", value, "teachType");
            return (Criteria) this;
        }

        public Criteria andTeachTypeLessThan(String value) {
            addCriterion("teach_type <", value, "teachType");
            return (Criteria) this;
        }

        public Criteria andTeachTypeLessThanOrEqualTo(String value) {
            addCriterion("teach_type <=", value, "teachType");
            return (Criteria) this;
        }

        public Criteria andTeachTypeLike(String value) {
            addCriterion("teach_type like", value, "teachType");
            return (Criteria) this;
        }

        public Criteria andTeachTypeNotLike(String value) {
            addCriterion("teach_type not like", value, "teachType");
            return (Criteria) this;
        }

        public Criteria andTeachTypeIn(List<String> values) {
            addCriterion("teach_type in", values, "teachType");
            return (Criteria) this;
        }

        public Criteria andTeachTypeNotIn(List<String> values) {
            addCriterion("teach_type not in", values, "teachType");
            return (Criteria) this;
        }

        public Criteria andTeachTypeBetween(String value1, String value2) {
            addCriterion("teach_type between", value1, value2, "teachType");
            return (Criteria) this;
        }

        public Criteria andTeachTypeNotBetween(String value1, String value2) {
            addCriterion("teach_type not between", value1, value2, "teachType");
            return (Criteria) this;
        }

        public Criteria andTrialClassIsNull() {
            addCriterion("trial_class is null");
            return (Criteria) this;
        }

        public Criteria andTrialClassIsNotNull() {
            addCriterion("trial_class is not null");
            return (Criteria) this;
        }

        public Criteria andTrialClassEqualTo(String value) {
            addCriterion("trial_class =", value, "trialClass");
            return (Criteria) this;
        }

        public Criteria andTrialClassNotEqualTo(String value) {
            addCriterion("trial_class <>", value, "trialClass");
            return (Criteria) this;
        }

        public Criteria andTrialClassGreaterThan(String value) {
            addCriterion("trial_class >", value, "trialClass");
            return (Criteria) this;
        }

        public Criteria andTrialClassGreaterThanOrEqualTo(String value) {
            addCriterion("trial_class >=", value, "trialClass");
            return (Criteria) this;
        }

        public Criteria andTrialClassLessThan(String value) {
            addCriterion("trial_class <", value, "trialClass");
            return (Criteria) this;
        }

        public Criteria andTrialClassLessThanOrEqualTo(String value) {
            addCriterion("trial_class <=", value, "trialClass");
            return (Criteria) this;
        }

        public Criteria andTrialClassLike(String value) {
            addCriterion("trial_class like", value, "trialClass");
            return (Criteria) this;
        }

        public Criteria andTrialClassNotLike(String value) {
            addCriterion("trial_class not like", value, "trialClass");
            return (Criteria) this;
        }

        public Criteria andTrialClassIn(List<String> values) {
            addCriterion("trial_class in", values, "trialClass");
            return (Criteria) this;
        }

        public Criteria andTrialClassNotIn(List<String> values) {
            addCriterion("trial_class not in", values, "trialClass");
            return (Criteria) this;
        }

        public Criteria andTrialClassBetween(String value1, String value2) {
            addCriterion("trial_class between", value1, value2, "trialClass");
            return (Criteria) this;
        }

        public Criteria andTrialClassNotBetween(String value1, String value2) {
            addCriterion("trial_class not between", value1, value2, "trialClass");
            return (Criteria) this;
        }

        public Criteria andMainImgUrlIsNull() {
            addCriterion("main_img_url is null");
            return (Criteria) this;
        }

        public Criteria andMainImgUrlIsNotNull() {
            addCriterion("main_img_url is not null");
            return (Criteria) this;
        }

        public Criteria andMainImgUrlEqualTo(String value) {
            addCriterion("main_img_url =", value, "mainImgUrl");
            return (Criteria) this;
        }

        public Criteria andMainImgUrlNotEqualTo(String value) {
            addCriterion("main_img_url <>", value, "mainImgUrl");
            return (Criteria) this;
        }

        public Criteria andMainImgUrlGreaterThan(String value) {
            addCriterion("main_img_url >", value, "mainImgUrl");
            return (Criteria) this;
        }

        public Criteria andMainImgUrlGreaterThanOrEqualTo(String value) {
            addCriterion("main_img_url >=", value, "mainImgUrl");
            return (Criteria) this;
        }

        public Criteria andMainImgUrlLessThan(String value) {
            addCriterion("main_img_url <", value, "mainImgUrl");
            return (Criteria) this;
        }

        public Criteria andMainImgUrlLessThanOrEqualTo(String value) {
            addCriterion("main_img_url <=", value, "mainImgUrl");
            return (Criteria) this;
        }

        public Criteria andMainImgUrlLike(String value) {
            addCriterion("main_img_url like", value, "mainImgUrl");
            return (Criteria) this;
        }

        public Criteria andMainImgUrlNotLike(String value) {
            addCriterion("main_img_url not like", value, "mainImgUrl");
            return (Criteria) this;
        }

        public Criteria andMainImgUrlIn(List<String> values) {
            addCriterion("main_img_url in", values, "mainImgUrl");
            return (Criteria) this;
        }

        public Criteria andMainImgUrlNotIn(List<String> values) {
            addCriterion("main_img_url not in", values, "mainImgUrl");
            return (Criteria) this;
        }

        public Criteria andMainImgUrlBetween(String value1, String value2) {
            addCriterion("main_img_url between", value1, value2, "mainImgUrl");
            return (Criteria) this;
        }

        public Criteria andMainImgUrlNotBetween(String value1, String value2) {
            addCriterion("main_img_url not between", value1, value2, "mainImgUrl");
            return (Criteria) this;
        }

        public Criteria andSubImgUrlIsNull() {
            addCriterion("sub_img_url is null");
            return (Criteria) this;
        }

        public Criteria andSubImgUrlIsNotNull() {
            addCriterion("sub_img_url is not null");
            return (Criteria) this;
        }

        public Criteria andSubImgUrlEqualTo(String value) {
            addCriterion("sub_img_url =", value, "subImgUrl");
            return (Criteria) this;
        }

        public Criteria andSubImgUrlNotEqualTo(String value) {
            addCriterion("sub_img_url <>", value, "subImgUrl");
            return (Criteria) this;
        }

        public Criteria andSubImgUrlGreaterThan(String value) {
            addCriterion("sub_img_url >", value, "subImgUrl");
            return (Criteria) this;
        }

        public Criteria andSubImgUrlGreaterThanOrEqualTo(String value) {
            addCriterion("sub_img_url >=", value, "subImgUrl");
            return (Criteria) this;
        }

        public Criteria andSubImgUrlLessThan(String value) {
            addCriterion("sub_img_url <", value, "subImgUrl");
            return (Criteria) this;
        }

        public Criteria andSubImgUrlLessThanOrEqualTo(String value) {
            addCriterion("sub_img_url <=", value, "subImgUrl");
            return (Criteria) this;
        }

        public Criteria andSubImgUrlLike(String value) {
            addCriterion("sub_img_url like", value, "subImgUrl");
            return (Criteria) this;
        }

        public Criteria andSubImgUrlNotLike(String value) {
            addCriterion("sub_img_url not like", value, "subImgUrl");
            return (Criteria) this;
        }

        public Criteria andSubImgUrlIn(List<String> values) {
            addCriterion("sub_img_url in", values, "subImgUrl");
            return (Criteria) this;
        }

        public Criteria andSubImgUrlNotIn(List<String> values) {
            addCriterion("sub_img_url not in", values, "subImgUrl");
            return (Criteria) this;
        }

        public Criteria andSubImgUrlBetween(String value1, String value2) {
            addCriterion("sub_img_url between", value1, value2, "subImgUrl");
            return (Criteria) this;
        }

        public Criteria andSubImgUrlNotBetween(String value1, String value2) {
            addCriterion("sub_img_url not between", value1, value2, "subImgUrl");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNull() {
            addCriterion("remark is null");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNotNull() {
            addCriterion("remark is not null");
            return (Criteria) this;
        }

        public Criteria andRemarkEqualTo(String value) {
            addCriterion("remark =", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotEqualTo(String value) {
            addCriterion("remark <>", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThan(String value) {
            addCriterion("remark >", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("remark >=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThan(String value) {
            addCriterion("remark <", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanOrEqualTo(String value) {
            addCriterion("remark <=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLike(String value) {
            addCriterion("remark like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotLike(String value) {
            addCriterion("remark not like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkIn(List<String> values) {
            addCriterion("remark in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotIn(List<String> values) {
            addCriterion("remark not in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkBetween(String value1, String value2) {
            addCriterion("remark between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotBetween(String value1, String value2) {
            addCriterion("remark not between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andCourseVideoUrlIsNull() {
            addCriterion("course_video_url is null");
            return (Criteria) this;
        }

        public Criteria andCourseVideoUrlIsNotNull() {
            addCriterion("course_video_url is not null");
            return (Criteria) this;
        }

        public Criteria andCourseVideoUrlEqualTo(String value) {
            addCriterion("course_video_url =", value, "courseVideoUrl");
            return (Criteria) this;
        }

        public Criteria andCourseVideoUrlNotEqualTo(String value) {
            addCriterion("course_video_url <>", value, "courseVideoUrl");
            return (Criteria) this;
        }

        public Criteria andCourseVideoUrlGreaterThan(String value) {
            addCriterion("course_video_url >", value, "courseVideoUrl");
            return (Criteria) this;
        }

        public Criteria andCourseVideoUrlGreaterThanOrEqualTo(String value) {
            addCriterion("course_video_url >=", value, "courseVideoUrl");
            return (Criteria) this;
        }

        public Criteria andCourseVideoUrlLessThan(String value) {
            addCriterion("course_video_url <", value, "courseVideoUrl");
            return (Criteria) this;
        }

        public Criteria andCourseVideoUrlLessThanOrEqualTo(String value) {
            addCriterion("course_video_url <=", value, "courseVideoUrl");
            return (Criteria) this;
        }

        public Criteria andCourseVideoUrlLike(String value) {
            addCriterion("course_video_url like", value, "courseVideoUrl");
            return (Criteria) this;
        }

        public Criteria andCourseVideoUrlNotLike(String value) {
            addCriterion("course_video_url not like", value, "courseVideoUrl");
            return (Criteria) this;
        }

        public Criteria andCourseVideoUrlIn(List<String> values) {
            addCriterion("course_video_url in", values, "courseVideoUrl");
            return (Criteria) this;
        }

        public Criteria andCourseVideoUrlNotIn(List<String> values) {
            addCriterion("course_video_url not in", values, "courseVideoUrl");
            return (Criteria) this;
        }

        public Criteria andCourseVideoUrlBetween(String value1, String value2) {
            addCriterion("course_video_url between", value1, value2, "courseVideoUrl");
            return (Criteria) this;
        }

        public Criteria andCourseVideoUrlNotBetween(String value1, String value2) {
            addCriterion("course_video_url not between", value1, value2, "courseVideoUrl");
            return (Criteria) this;
        }

        public Criteria andBackupCourseVideoUrlIsNull() {
            addCriterion("backup_course_video_url is null");
            return (Criteria) this;
        }

        public Criteria andBackupCourseVideoUrlIsNotNull() {
            addCriterion("backup_course_video_url is not null");
            return (Criteria) this;
        }

        public Criteria andBackupCourseVideoUrlEqualTo(String value) {
            addCriterion("backup_course_video_url =", value, "backupCourseVideoUrl");
            return (Criteria) this;
        }

        public Criteria andBackupCourseVideoUrlNotEqualTo(String value) {
            addCriterion("backup_course_video_url <>", value, "backupCourseVideoUrl");
            return (Criteria) this;
        }

        public Criteria andBackupCourseVideoUrlGreaterThan(String value) {
            addCriterion("backup_course_video_url >", value, "backupCourseVideoUrl");
            return (Criteria) this;
        }

        public Criteria andBackupCourseVideoUrlGreaterThanOrEqualTo(String value) {
            addCriterion("backup_course_video_url >=", value, "backupCourseVideoUrl");
            return (Criteria) this;
        }

        public Criteria andBackupCourseVideoUrlLessThan(String value) {
            addCriterion("backup_course_video_url <", value, "backupCourseVideoUrl");
            return (Criteria) this;
        }

        public Criteria andBackupCourseVideoUrlLessThanOrEqualTo(String value) {
            addCriterion("backup_course_video_url <=", value, "backupCourseVideoUrl");
            return (Criteria) this;
        }

        public Criteria andBackupCourseVideoUrlLike(String value) {
            addCriterion("backup_course_video_url like", value, "backupCourseVideoUrl");
            return (Criteria) this;
        }

        public Criteria andBackupCourseVideoUrlNotLike(String value) {
            addCriterion("backup_course_video_url not like", value, "backupCourseVideoUrl");
            return (Criteria) this;
        }

        public Criteria andBackupCourseVideoUrlIn(List<String> values) {
            addCriterion("backup_course_video_url in", values, "backupCourseVideoUrl");
            return (Criteria) this;
        }

        public Criteria andBackupCourseVideoUrlNotIn(List<String> values) {
            addCriterion("backup_course_video_url not in", values, "backupCourseVideoUrl");
            return (Criteria) this;
        }

        public Criteria andBackupCourseVideoUrlBetween(String value1, String value2) {
            addCriterion("backup_course_video_url between", value1, value2, "backupCourseVideoUrl");
            return (Criteria) this;
        }

        public Criteria andBackupCourseVideoUrlNotBetween(String value1, String value2) {
            addCriterion("backup_course_video_url not between", value1, value2, "backupCourseVideoUrl");
            return (Criteria) this;
        }

        public Criteria andH5LandingPageUrlIsNull() {
            addCriterion("h5_landing_page_url is null");
            return (Criteria) this;
        }

        public Criteria andH5LandingPageUrlIsNotNull() {
            addCriterion("h5_landing_page_url is not null");
            return (Criteria) this;
        }

        public Criteria andH5LandingPageUrlEqualTo(String value) {
            addCriterion("h5_landing_page_url =", value, "h5LandingPageUrl");
            return (Criteria) this;
        }

        public Criteria andH5LandingPageUrlNotEqualTo(String value) {
            addCriterion("h5_landing_page_url <>", value, "h5LandingPageUrl");
            return (Criteria) this;
        }

        public Criteria andH5LandingPageUrlGreaterThan(String value) {
            addCriterion("h5_landing_page_url >", value, "h5LandingPageUrl");
            return (Criteria) this;
        }

        public Criteria andH5LandingPageUrlGreaterThanOrEqualTo(String value) {
            addCriterion("h5_landing_page_url >=", value, "h5LandingPageUrl");
            return (Criteria) this;
        }

        public Criteria andH5LandingPageUrlLessThan(String value) {
            addCriterion("h5_landing_page_url <", value, "h5LandingPageUrl");
            return (Criteria) this;
        }

        public Criteria andH5LandingPageUrlLessThanOrEqualTo(String value) {
            addCriterion("h5_landing_page_url <=", value, "h5LandingPageUrl");
            return (Criteria) this;
        }

        public Criteria andH5LandingPageUrlLike(String value) {
            addCriterion("h5_landing_page_url like", value, "h5LandingPageUrl");
            return (Criteria) this;
        }

        public Criteria andH5LandingPageUrlNotLike(String value) {
            addCriterion("h5_landing_page_url not like", value, "h5LandingPageUrl");
            return (Criteria) this;
        }

        public Criteria andH5LandingPageUrlIn(List<String> values) {
            addCriterion("h5_landing_page_url in", values, "h5LandingPageUrl");
            return (Criteria) this;
        }

        public Criteria andH5LandingPageUrlNotIn(List<String> values) {
            addCriterion("h5_landing_page_url not in", values, "h5LandingPageUrl");
            return (Criteria) this;
        }

        public Criteria andH5LandingPageUrlBetween(String value1, String value2) {
            addCriterion("h5_landing_page_url between", value1, value2, "h5LandingPageUrl");
            return (Criteria) this;
        }

        public Criteria andH5LandingPageUrlNotBetween(String value1, String value2) {
            addCriterion("h5_landing_page_url not between", value1, value2, "h5LandingPageUrl");
            return (Criteria) this;
        }

        public Criteria andPcLandingPageUrlIsNull() {
            addCriterion("pc_landing_page_url is null");
            return (Criteria) this;
        }

        public Criteria andPcLandingPageUrlIsNotNull() {
            addCriterion("pc_landing_page_url is not null");
            return (Criteria) this;
        }

        public Criteria andPcLandingPageUrlEqualTo(String value) {
            addCriterion("pc_landing_page_url =", value, "pcLandingPageUrl");
            return (Criteria) this;
        }

        public Criteria andPcLandingPageUrlNotEqualTo(String value) {
            addCriterion("pc_landing_page_url <>", value, "pcLandingPageUrl");
            return (Criteria) this;
        }

        public Criteria andPcLandingPageUrlGreaterThan(String value) {
            addCriterion("pc_landing_page_url >", value, "pcLandingPageUrl");
            return (Criteria) this;
        }

        public Criteria andPcLandingPageUrlGreaterThanOrEqualTo(String value) {
            addCriterion("pc_landing_page_url >=", value, "pcLandingPageUrl");
            return (Criteria) this;
        }

        public Criteria andPcLandingPageUrlLessThan(String value) {
            addCriterion("pc_landing_page_url <", value, "pcLandingPageUrl");
            return (Criteria) this;
        }

        public Criteria andPcLandingPageUrlLessThanOrEqualTo(String value) {
            addCriterion("pc_landing_page_url <=", value, "pcLandingPageUrl");
            return (Criteria) this;
        }

        public Criteria andPcLandingPageUrlLike(String value) {
            addCriterion("pc_landing_page_url like", value, "pcLandingPageUrl");
            return (Criteria) this;
        }

        public Criteria andPcLandingPageUrlNotLike(String value) {
            addCriterion("pc_landing_page_url not like", value, "pcLandingPageUrl");
            return (Criteria) this;
        }

        public Criteria andPcLandingPageUrlIn(List<String> values) {
            addCriterion("pc_landing_page_url in", values, "pcLandingPageUrl");
            return (Criteria) this;
        }

        public Criteria andPcLandingPageUrlNotIn(List<String> values) {
            addCriterion("pc_landing_page_url not in", values, "pcLandingPageUrl");
            return (Criteria) this;
        }

        public Criteria andPcLandingPageUrlBetween(String value1, String value2) {
            addCriterion("pc_landing_page_url between", value1, value2, "pcLandingPageUrl");
            return (Criteria) this;
        }

        public Criteria andPcLandingPageUrlNotBetween(String value1, String value2) {
            addCriterion("pc_landing_page_url not between", value1, value2, "pcLandingPageUrl");
            return (Criteria) this;
        }

        public Criteria andIosLandingPageUrlIsNull() {
            addCriterion("ios_landing_page_url is null");
            return (Criteria) this;
        }

        public Criteria andIosLandingPageUrlIsNotNull() {
            addCriterion("ios_landing_page_url is not null");
            return (Criteria) this;
        }

        public Criteria andIosLandingPageUrlEqualTo(String value) {
            addCriterion("ios_landing_page_url =", value, "iosLandingPageUrl");
            return (Criteria) this;
        }

        public Criteria andIosLandingPageUrlNotEqualTo(String value) {
            addCriterion("ios_landing_page_url <>", value, "iosLandingPageUrl");
            return (Criteria) this;
        }

        public Criteria andIosLandingPageUrlGreaterThan(String value) {
            addCriterion("ios_landing_page_url >", value, "iosLandingPageUrl");
            return (Criteria) this;
        }

        public Criteria andIosLandingPageUrlGreaterThanOrEqualTo(String value) {
            addCriterion("ios_landing_page_url >=", value, "iosLandingPageUrl");
            return (Criteria) this;
        }

        public Criteria andIosLandingPageUrlLessThan(String value) {
            addCriterion("ios_landing_page_url <", value, "iosLandingPageUrl");
            return (Criteria) this;
        }

        public Criteria andIosLandingPageUrlLessThanOrEqualTo(String value) {
            addCriterion("ios_landing_page_url <=", value, "iosLandingPageUrl");
            return (Criteria) this;
        }

        public Criteria andIosLandingPageUrlLike(String value) {
            addCriterion("ios_landing_page_url like", value, "iosLandingPageUrl");
            return (Criteria) this;
        }

        public Criteria andIosLandingPageUrlNotLike(String value) {
            addCriterion("ios_landing_page_url not like", value, "iosLandingPageUrl");
            return (Criteria) this;
        }

        public Criteria andIosLandingPageUrlIn(List<String> values) {
            addCriterion("ios_landing_page_url in", values, "iosLandingPageUrl");
            return (Criteria) this;
        }

        public Criteria andIosLandingPageUrlNotIn(List<String> values) {
            addCriterion("ios_landing_page_url not in", values, "iosLandingPageUrl");
            return (Criteria) this;
        }

        public Criteria andIosLandingPageUrlBetween(String value1, String value2) {
            addCriterion("ios_landing_page_url between", value1, value2, "iosLandingPageUrl");
            return (Criteria) this;
        }

        public Criteria andIosLandingPageUrlNotBetween(String value1, String value2) {
            addCriterion("ios_landing_page_url not between", value1, value2, "iosLandingPageUrl");
            return (Criteria) this;
        }

        public Criteria andAndroidLandingPageUrlIsNull() {
            addCriterion("android_landing_page_url is null");
            return (Criteria) this;
        }

        public Criteria andAndroidLandingPageUrlIsNotNull() {
            addCriterion("android_landing_page_url is not null");
            return (Criteria) this;
        }

        public Criteria andAndroidLandingPageUrlEqualTo(String value) {
            addCriterion("android_landing_page_url =", value, "androidLandingPageUrl");
            return (Criteria) this;
        }

        public Criteria andAndroidLandingPageUrlNotEqualTo(String value) {
            addCriterion("android_landing_page_url <>", value, "androidLandingPageUrl");
            return (Criteria) this;
        }

        public Criteria andAndroidLandingPageUrlGreaterThan(String value) {
            addCriterion("android_landing_page_url >", value, "androidLandingPageUrl");
            return (Criteria) this;
        }

        public Criteria andAndroidLandingPageUrlGreaterThanOrEqualTo(String value) {
            addCriterion("android_landing_page_url >=", value, "androidLandingPageUrl");
            return (Criteria) this;
        }

        public Criteria andAndroidLandingPageUrlLessThan(String value) {
            addCriterion("android_landing_page_url <", value, "androidLandingPageUrl");
            return (Criteria) this;
        }

        public Criteria andAndroidLandingPageUrlLessThanOrEqualTo(String value) {
            addCriterion("android_landing_page_url <=", value, "androidLandingPageUrl");
            return (Criteria) this;
        }

        public Criteria andAndroidLandingPageUrlLike(String value) {
            addCriterion("android_landing_page_url like", value, "androidLandingPageUrl");
            return (Criteria) this;
        }

        public Criteria andAndroidLandingPageUrlNotLike(String value) {
            addCriterion("android_landing_page_url not like", value, "androidLandingPageUrl");
            return (Criteria) this;
        }

        public Criteria andAndroidLandingPageUrlIn(List<String> values) {
            addCriterion("android_landing_page_url in", values, "androidLandingPageUrl");
            return (Criteria) this;
        }

        public Criteria andAndroidLandingPageUrlNotIn(List<String> values) {
            addCriterion("android_landing_page_url not in", values, "androidLandingPageUrl");
            return (Criteria) this;
        }

        public Criteria andAndroidLandingPageUrlBetween(String value1, String value2) {
            addCriterion("android_landing_page_url between", value1, value2, "androidLandingPageUrl");
            return (Criteria) this;
        }

        public Criteria andAndroidLandingPageUrlNotBetween(String value1, String value2) {
            addCriterion("android_landing_page_url not between", value1, value2, "androidLandingPageUrl");
            return (Criteria) this;
        }

        public Criteria andIosUpUlinkUrlIsNull() {
            addCriterion("ios_up_ulink_url is null");
            return (Criteria) this;
        }

        public Criteria andIosUpUlinkUrlIsNotNull() {
            addCriterion("ios_up_ulink_url is not null");
            return (Criteria) this;
        }

        public Criteria andIosUpUlinkUrlEqualTo(String value) {
            addCriterion("ios_up_ulink_url =", value, "iosUpUlinkUrl");
            return (Criteria) this;
        }

        public Criteria andIosUpUlinkUrlNotEqualTo(String value) {
            addCriterion("ios_up_ulink_url <>", value, "iosUpUlinkUrl");
            return (Criteria) this;
        }

        public Criteria andIosUpUlinkUrlGreaterThan(String value) {
            addCriterion("ios_up_ulink_url >", value, "iosUpUlinkUrl");
            return (Criteria) this;
        }

        public Criteria andIosUpUlinkUrlGreaterThanOrEqualTo(String value) {
            addCriterion("ios_up_ulink_url >=", value, "iosUpUlinkUrl");
            return (Criteria) this;
        }

        public Criteria andIosUpUlinkUrlLessThan(String value) {
            addCriterion("ios_up_ulink_url <", value, "iosUpUlinkUrl");
            return (Criteria) this;
        }

        public Criteria andIosUpUlinkUrlLessThanOrEqualTo(String value) {
            addCriterion("ios_up_ulink_url <=", value, "iosUpUlinkUrl");
            return (Criteria) this;
        }

        public Criteria andIosUpUlinkUrlLike(String value) {
            addCriterion("ios_up_ulink_url like", value, "iosUpUlinkUrl");
            return (Criteria) this;
        }

        public Criteria andIosUpUlinkUrlNotLike(String value) {
            addCriterion("ios_up_ulink_url not like", value, "iosUpUlinkUrl");
            return (Criteria) this;
        }

        public Criteria andIosUpUlinkUrlIn(List<String> values) {
            addCriterion("ios_up_ulink_url in", values, "iosUpUlinkUrl");
            return (Criteria) this;
        }

        public Criteria andIosUpUlinkUrlNotIn(List<String> values) {
            addCriterion("ios_up_ulink_url not in", values, "iosUpUlinkUrl");
            return (Criteria) this;
        }

        public Criteria andIosUpUlinkUrlBetween(String value1, String value2) {
            addCriterion("ios_up_ulink_url between", value1, value2, "iosUpUlinkUrl");
            return (Criteria) this;
        }

        public Criteria andIosUpUlinkUrlNotBetween(String value1, String value2) {
            addCriterion("ios_up_ulink_url not between", value1, value2, "iosUpUlinkUrl");
            return (Criteria) this;
        }

        public Criteria andFirstCategoryCodeIsNull() {
            addCriterion("first_category_code is null");
            return (Criteria) this;
        }

        public Criteria andFirstCategoryCodeIsNotNull() {
            addCriterion("first_category_code is not null");
            return (Criteria) this;
        }

        public Criteria andFirstCategoryCodeEqualTo(Long value) {
            addCriterion("first_category_code =", value, "firstCategoryCode");
            return (Criteria) this;
        }

        public Criteria andFirstCategoryCodeNotEqualTo(Long value) {
            addCriterion("first_category_code <>", value, "firstCategoryCode");
            return (Criteria) this;
        }

        public Criteria andFirstCategoryCodeGreaterThan(Long value) {
            addCriterion("first_category_code >", value, "firstCategoryCode");
            return (Criteria) this;
        }

        public Criteria andFirstCategoryCodeGreaterThanOrEqualTo(Long value) {
            addCriterion("first_category_code >=", value, "firstCategoryCode");
            return (Criteria) this;
        }

        public Criteria andFirstCategoryCodeLessThan(Long value) {
            addCriterion("first_category_code <", value, "firstCategoryCode");
            return (Criteria) this;
        }

        public Criteria andFirstCategoryCodeLessThanOrEqualTo(Long value) {
            addCriterion("first_category_code <=", value, "firstCategoryCode");
            return (Criteria) this;
        }

        public Criteria andFirstCategoryCodeIn(List<Long> values) {
            addCriterion("first_category_code in", values, "firstCategoryCode");
            return (Criteria) this;
        }

        public Criteria andFirstCategoryCodeNotIn(List<Long> values) {
            addCriterion("first_category_code not in", values, "firstCategoryCode");
            return (Criteria) this;
        }

        public Criteria andFirstCategoryCodeBetween(Long value1, Long value2) {
            addCriterion("first_category_code between", value1, value2, "firstCategoryCode");
            return (Criteria) this;
        }

        public Criteria andFirstCategoryCodeNotBetween(Long value1, Long value2) {
            addCriterion("first_category_code not between", value1, value2, "firstCategoryCode");
            return (Criteria) this;
        }

        public Criteria andSecondCategoryCodeIsNull() {
            addCriterion("second_category_code is null");
            return (Criteria) this;
        }

        public Criteria andSecondCategoryCodeIsNotNull() {
            addCriterion("second_category_code is not null");
            return (Criteria) this;
        }

        public Criteria andSecondCategoryCodeEqualTo(Long value) {
            addCriterion("second_category_code =", value, "secondCategoryCode");
            return (Criteria) this;
        }

        public Criteria andSecondCategoryCodeNotEqualTo(Long value) {
            addCriterion("second_category_code <>", value, "secondCategoryCode");
            return (Criteria) this;
        }

        public Criteria andSecondCategoryCodeGreaterThan(Long value) {
            addCriterion("second_category_code >", value, "secondCategoryCode");
            return (Criteria) this;
        }

        public Criteria andSecondCategoryCodeGreaterThanOrEqualTo(Long value) {
            addCriterion("second_category_code >=", value, "secondCategoryCode");
            return (Criteria) this;
        }

        public Criteria andSecondCategoryCodeLessThan(Long value) {
            addCriterion("second_category_code <", value, "secondCategoryCode");
            return (Criteria) this;
        }

        public Criteria andSecondCategoryCodeLessThanOrEqualTo(Long value) {
            addCriterion("second_category_code <=", value, "secondCategoryCode");
            return (Criteria) this;
        }

        public Criteria andSecondCategoryCodeIn(List<Long> values) {
            addCriterion("second_category_code in", values, "secondCategoryCode");
            return (Criteria) this;
        }

        public Criteria andSecondCategoryCodeNotIn(List<Long> values) {
            addCriterion("second_category_code not in", values, "secondCategoryCode");
            return (Criteria) this;
        }

        public Criteria andSecondCategoryCodeBetween(Long value1, Long value2) {
            addCriterion("second_category_code between", value1, value2, "secondCategoryCode");
            return (Criteria) this;
        }

        public Criteria andSecondCategoryCodeNotBetween(Long value1, Long value2) {
            addCriterion("second_category_code not between", value1, value2, "secondCategoryCode");
            return (Criteria) this;
        }

        public Criteria andThirdCategoryCodeIsNull() {
            addCriterion("third_category_code is null");
            return (Criteria) this;
        }

        public Criteria andThirdCategoryCodeIsNotNull() {
            addCriterion("third_category_code is not null");
            return (Criteria) this;
        }

        public Criteria andThirdCategoryCodeEqualTo(Long value) {
            addCriterion("third_category_code =", value, "thirdCategoryCode");
            return (Criteria) this;
        }

        public Criteria andThirdCategoryCodeNotEqualTo(Long value) {
            addCriterion("third_category_code <>", value, "thirdCategoryCode");
            return (Criteria) this;
        }

        public Criteria andThirdCategoryCodeGreaterThan(Long value) {
            addCriterion("third_category_code >", value, "thirdCategoryCode");
            return (Criteria) this;
        }

        public Criteria andThirdCategoryCodeGreaterThanOrEqualTo(Long value) {
            addCriterion("third_category_code >=", value, "thirdCategoryCode");
            return (Criteria) this;
        }

        public Criteria andThirdCategoryCodeLessThan(Long value) {
            addCriterion("third_category_code <", value, "thirdCategoryCode");
            return (Criteria) this;
        }

        public Criteria andThirdCategoryCodeLessThanOrEqualTo(Long value) {
            addCriterion("third_category_code <=", value, "thirdCategoryCode");
            return (Criteria) this;
        }

        public Criteria andThirdCategoryCodeIn(List<Long> values) {
            addCriterion("third_category_code in", values, "thirdCategoryCode");
            return (Criteria) this;
        }

        public Criteria andThirdCategoryCodeNotIn(List<Long> values) {
            addCriterion("third_category_code not in", values, "thirdCategoryCode");
            return (Criteria) this;
        }

        public Criteria andThirdCategoryCodeBetween(Long value1, Long value2) {
            addCriterion("third_category_code between", value1, value2, "thirdCategoryCode");
            return (Criteria) this;
        }

        public Criteria andThirdCategoryCodeNotBetween(Long value1, Long value2) {
            addCriterion("third_category_code not between", value1, value2, "thirdCategoryCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeIsNull() {
            addCriterion("spu_code is null");
            return (Criteria) this;
        }

        public Criteria andSpuCodeIsNotNull() {
            addCriterion("spu_code is not null");
            return (Criteria) this;
        }

        public Criteria andSpuCodeEqualTo(Long value) {
            addCriterion("spu_code =", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeNotEqualTo(Long value) {
            addCriterion("spu_code <>", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeGreaterThan(Long value) {
            addCriterion("spu_code >", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeGreaterThanOrEqualTo(Long value) {
            addCriterion("spu_code >=", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeLessThan(Long value) {
            addCriterion("spu_code <", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeLessThanOrEqualTo(Long value) {
            addCriterion("spu_code <=", value, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeIn(List<Long> values) {
            addCriterion("spu_code in", values, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeNotIn(List<Long> values) {
            addCriterion("spu_code not in", values, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeBetween(Long value1, Long value2) {
            addCriterion("spu_code between", value1, value2, "spuCode");
            return (Criteria) this;
        }

        public Criteria andSpuCodeNotBetween(Long value1, Long value2) {
            addCriterion("spu_code not between", value1, value2, "spuCode");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Integer value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Integer value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Integer value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Integer value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Integer value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Integer> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Integer> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNull() {
            addCriterion("ctime is null");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNotNull() {
            addCriterion("ctime is not null");
            return (Criteria) this;
        }

        public Criteria andCtimeEqualTo(Timestamp value) {
            addCriterion("ctime =", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotEqualTo(Timestamp value) {
            addCriterion("ctime <>", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThan(Timestamp value) {
            addCriterion("ctime >", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("ctime >=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThan(Timestamp value) {
            addCriterion("ctime <", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("ctime <=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeIn(List<Timestamp> values) {
            addCriterion("ctime in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotIn(List<Timestamp> values) {
            addCriterion("ctime not in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime not between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNull() {
            addCriterion("mtime is null");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNotNull() {
            addCriterion("mtime is not null");
            return (Criteria) this;
        }

        public Criteria andMtimeEqualTo(Timestamp value) {
            addCriterion("mtime =", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotEqualTo(Timestamp value) {
            addCriterion("mtime <>", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThan(Timestamp value) {
            addCriterion("mtime >", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("mtime >=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThan(Timestamp value) {
            addCriterion("mtime <", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("mtime <=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeIn(List<Timestamp> values) {
            addCriterion("mtime in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotIn(List<Timestamp> values) {
            addCriterion("mtime not in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime not between", value1, value2, "mtime");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}