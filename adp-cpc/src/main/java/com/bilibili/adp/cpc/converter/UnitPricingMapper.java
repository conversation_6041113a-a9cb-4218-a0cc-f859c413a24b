package com.bilibili.adp.cpc.converter;

import com.bilibili.adp.cpc.core.bos.UnitPricingBo;
import com.bilibili.adp.cpc.wrapper.bos.UnitPricingWrapperBo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface UnitPricingMapper {
    UnitPricingMapper INSTANCE = Mappers.getMapper(UnitPricingMapper.class);

    UnitPricingBo fromWrapper(UnitPricingWrapperBo wrapper);
}
