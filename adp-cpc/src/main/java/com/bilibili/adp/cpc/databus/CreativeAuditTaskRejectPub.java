package com.bilibili.adp.cpc.databus;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.common.exception.ServiceRuntimeException;
import com.bilibili.adp.cpc.biz.constants.Constants;
import com.bilibili.adp.cpc.databus.bos.BusinessContentProducerMessageBo;
import com.bilibili.adp.cpc.databus.bos.CreativeAuditTaskRejectMsg;
import com.bilibili.adp.cpc.utils.metrics.MetricsKeyConstant;
import com.bilibili.adp.util.common.AdpCatUtils;
import com.bilibili.warp.databus.DatabusProperty;
import com.bilibili.warp.databus.DatabusTemplate;
import com.bilibili.warp.databus.Message;
import com.bilibili.warp.databus.PubResult;
import com.bilibili.warp.spring.boot.autoconfigure.databus.DatabusProperties;
import io.opentelemetry.api.common.AttributeKey;
import io.opentelemetry.api.common.Attributes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import static com.bilibili.adp.cpc.utils.metrics.MetricsKeyConstant.*;
import static com.bilibili.adp.cpc.utils.metrics.MetricsKeyConstant.response_code_fail;

/**
 * 创意驳回后，发消息，将任务驳回
 **/
@Slf4j
@Service
public class CreativeAuditTaskRejectPub {

    @Resource
    private DatabusTemplate databusTemplate;

    public static final String BUSINESS_CONTENT = "creative-audit-task-reject";
    private final String topic;
    private final String group;

    public CreativeAuditTaskRejectPub(DatabusProperties databusProperties) {
        DatabusProperty databusProperty = databusProperties.getProperties().get(BUSINESS_CONTENT);
        this.topic = databusProperty.getTopic();
        this.group = databusProperty.getPub().getGroup();
    }

    public void pub(CreativeAuditTaskRejectMsg bo) {
        log.info("CreativeAuditTaskRejectPub pub, msg={}", bo);

        AdpCatUtils.newTransaction(Constants.CAT_TYPE_DATABUS, BUSINESS_CONTENT + ":pub", transaction -> {
            // messageKey和value自定义，value会被配置的serializer序列化
            Message message = Message.Builder
                    .of(bo.getCreativeIds().toString(), bo)
                    .build();
            // topic 和 group 按照申请的填写即可. 可以自行注入相关配置，用配置的方式来发送消息
            PubResult result = databusTemplate.pub(topic, group, message);
            if (result.isSuccess()) {
                log.info("creative audit task reject, pub msg success, msg={}", bo);
            } else {
                Throwable throwable = result.getThrowable();
                log.error("creative audit task reject, pub msg error ", throwable);
                throw new RuntimeException(throwable.getMessage());
            }
        });
    }

}
