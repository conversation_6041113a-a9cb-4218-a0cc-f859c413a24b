package com.bilibili.adp.cpc.databus;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.cpc.dao.querydsl.pos.LauMaterialBilibiliVideoPo;
import com.bilibili.adp.cpc.databus.bos.LauMaterialBilibiliVideoChange;
import com.bilibili.warp.databus.DatabusProperty;
import com.bilibili.warp.databus.DatabusTemplate;
import com.bilibili.warp.databus.Message;
import com.bilibili.warp.databus.PubResult;
import com.bilibili.warp.spring.boot.autoconfigure.databus.DatabusProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description
 * @date 5/15/24
 **/
@Slf4j
@Service
public class MaterialBilibiliVideoNotifyPub {

    @Resource
    private DatabusTemplate databusTemplate;

    public static final String Material_Bilibili_Video_Notify = "material-bilibili-video-notify";
    private final String topic;
    private final String group;


    public static final String eventName = "saveToArchiveLibrary";


    public MaterialBilibiliVideoNotifyPub(DatabusProperties databusProperties) {
        DatabusProperty databusProperty = databusProperties.getProperties().get(Material_Bilibili_Video_Notify);
        this.topic = databusProperty.getTopic();
        this.group = databusProperty.getPub().getGroup();
    }

    public void pub(LauMaterialBilibiliVideoPo lauMaterialBilibiliVideoPo) {
        if (Objects.isNull(lauMaterialBilibiliVideoPo)) {
            return;
        }

        LauMaterialBilibiliVideoChange msg = new LauMaterialBilibiliVideoChange();

        msg.setEventName(eventName);
        msg.setLauMaterialBilibiliVideo(Collections.singletonList(lauMaterialBilibiliVideoPo));
        String msgStr = JSON.toJSONString(msg);

        log.info("msg={}", msgStr);

        // messageKey和value自定义，value会被配置的serializer序列化
        Message message = Message.Builder.of(lauMaterialBilibiliVideoPo.getAvid() + "", msg)
                .build();

        // topic 和 group 按照申请的填写即可. 可以自行注入相关配置，用配置的方式来发送消息
        PubResult result = databusTemplate.pub(topic, group, message);
        if (result.isSuccess()) {
            log.info("pub msg success, msg={}", msg);
        } else {
            Throwable throwable = result.getThrowable();
            log.error("pub msg error ", throwable);
            throw new RuntimeException(throwable.getMessage());
        }
    }

}
