package com.bilibili.adp.cpc.config.databus.creative;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bilibili.adp.cpc.biz.bos.WarpDatabusProperty;
import com.bilibili.adp.cpc.biz.constants.Constants;
import com.bilibili.adp.cpc.biz.services.creative.bos.CreativeEsBo;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauCreativeArchivePo;
import com.bilibili.adp.util.common.AdpCatUtils;
import com.bilibili.warp.databus.AckableMessage;
import com.bilibili.warp.databus.DatabusProperty;
import com.bilibili.warp.databus.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @Date 2023/6/16 11:38 上午
 * @Version 1.0
 **/
@Component
@Slf4j
public class CreativeArchiveBinlogMessageListener implements MessageListener {

    @Autowired
    private Creative2EsService creative2EsService;


    public static final String CREATIVE_BINLOG = "creative-archive-binlog";
    private final String topic;
    private final String group;

    @Value("${databus.creative.binlog.enabled:true}")
    private Boolean enabled;

    private static final String TEST_MOCK_TOPIC = "testTopic";
    private static final String TEST_MOCK_GROUP = "testGroup";
    private static final String ACTION = "action";
    private static final String INSERT = "insert";
    private static final String UPDATE = "update";
    private static final String DELETE = "delete";
    private static final String NEW = "new";
    private static final String OLD = "old";

    public CreativeArchiveBinlogMessageListener(WarpDatabusProperty warpDatabusProperty) {
        // 注入DatabusProperties，此处的ALIAS常量即配置文件中填写的别名，是topic的上一级配置键
        // pub场景同理，在使用DatabusTemplate的地方注入DatabusProperties来获取
        Map<String, DatabusProperty> properties = warpDatabusProperty.getProperties();
        DatabusProperty property = properties.get(CREATIVE_BINLOG);
        log.info("CreativeBinlogMessageListener, property={}", JSONObject.toJSONString(property));
        this.topic = Objects.isNull(property) ? TEST_MOCK_TOPIC : property.getTopic();
        this.group = Objects.isNull(property) ? TEST_MOCK_GROUP : property.getSub().getGroup();
    }

    public void handleMsg (String jsonString) {
        log.info("handleMsg, jsonString={}", jsonString);

        JSONObject jsonObject = JSONObject.parseObject(jsonString);
        String action = jsonObject.getString(ACTION);
        if (StringUtils.isEmpty(action)) {
            return;
        }
        LauCreativeArchivePo newCreativeArchivePo = deserializeCreativeArchivePo(jsonObject.getJSONObject(NEW));
        List<CreativeEsBo> creativeEsBos = creative2EsService.generateEsPoFromArchive(Collections.singletonList(newCreativeArchivePo));
        if (!CollectionUtils.isEmpty(creativeEsBos)) {
            creative2EsService.batchPub(creativeEsBos);
        }

    }


    private LauCreativeArchivePo deserializeCreativeArchivePo(JSONObject jsonObject) {
        if (Objects.isNull(jsonObject)) {
            return null;
        }
        LauCreativeArchivePo creativePo = null;
        try {
            creativePo = jsonObject.toJavaObject(LauCreativeArchivePo.class);
        } catch (Exception e) {
            log.error("deserializeCreativeBinlogDto error, jsonObject:{}", jsonObject, e);
        }
        return creativePo;
    }

    @Override
    public String topic() {
        return this.topic;
    }

    @Override
    public String group() {
        return this.group;
    }

    @Override
    public void onMessage(AckableMessage ackableMessage) {
        log.info("wrap onMessage, creativeBinlog ackableMessage={}", JSON.toJSONString(ackableMessage));
        if (!Boolean.TRUE.equals(enabled)) {
            return;
        }

        try {
            AdpCatUtils.newTransaction(Constants.CAT_TYPE_DATABUS, CREATIVE_BINLOG + ":sub", transaction -> {
                String value = new String(ackableMessage.payload());
                handleMsg(value);
            });
        } catch (Exception e) {
            log.error("CreativeBinlogSubTask error", e);
        }

    }

    @Override
    public boolean autoCommit() {
        return true;
    }
}
