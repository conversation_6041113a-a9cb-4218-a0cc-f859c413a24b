package com.bilibili.adp.cpc.splash_screen.creative.bos;

import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.web.bind.annotation.RequestParam;

import java.sql.Timestamp;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QuerySplashScreenCreative {

    private Integer accountId;
    private List<Integer> creativeIds;
    private Integer auditStatus;
    private Integer creativeStatus;
    private Timestamp timeFrom;
    private Timestamp timeTo;
}
