package com.bilibili.adp.cpc.dao.ad_core.querydsl;

import static com.querydsl.core.types.PathMetadataFactory.*;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauSplashScreenCreativeButtonPo;


import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.Generated;
import com.querydsl.core.types.Path;

import com.querydsl.sql.ColumnMetadata;
import java.sql.Types;




/**
 * QLauSplashScreenCreativeButton is a Querydsl query type for LauSplashScreenCreativeButtonPo
 */
@Generated("com.querydsl.sql.codegen.MetaDataSerializer")
public class QLauSplashScreenCreativeButton extends com.querydsl.sql.RelationalPathBase<LauSplashScreenCreativeButtonPo> {

    private static final long serialVersionUID = -676199730;

    public static final QLauSplashScreenCreativeButton lauSplashScreenCreativeButton = new QLauSplashScreenCreativeButton("lau_splash_screen_creative_button");

    public final StringPath bgColorDay = createString("bgColorDay");

    public final StringPath bgColorNight = createString("bgColorNight");

    public final NumberPath<Long> buttonId = createNumber("buttonId", Long.class);

    public final NumberPath<Integer> buttonType = createNumber("buttonType", Integer.class);

    public final NumberPath<Integer> clickExpandRatio = createNumber("clickExpandRatio", Integer.class);

    public final NumberPath<Integer> creativeId = createNumber("creativeId", Integer.class);

    public final DateTimePath<java.sql.Timestamp> ctime = createDateTime("ctime", java.sql.Timestamp.class);

    public final NumberPath<Integer> degradeType = createNumber("degradeType", Integer.class);

    public final NumberPath<Integer> fontRatio = createNumber("fontRatio", Integer.class);

    public final NumberPath<Integer> height = createNumber("height", Integer.class);

    public final NumberPath<Integer> interactStyle = createNumber("interactStyle", Integer.class);

    public final NumberPath<Integer> isDeleted = createNumber("isDeleted", Integer.class);

    public final StringPath jumpGuideContent = createString("jumpGuideContent");

    public final StringPath jumpImageMd5 = createString("jumpImageMd5");

    public final StringPath jumpImageUrl = createString("jumpImageUrl");

    public final DateTimePath<java.sql.Timestamp> mtime = createDateTime("mtime", java.sql.Timestamp.class);

    public final StringPath relatedIds = createString("relatedIds");

    public final StringPath schemaGuideContent = createString("schemaGuideContent");

    public final StringPath schemaImageMd5 = createString("schemaImageMd5");

    public final StringPath schemaImageUrl = createString("schemaImageUrl");

    public final NumberPath<Integer> seq = createNumber("seq", Integer.class);

    public final StringPath textColorDay = createString("textColorDay");

    public final StringPath textColorNight = createString("textColorNight");

    public final NumberPath<Integer> width = createNumber("width", Integer.class);

    public final NumberPath<Integer> x = createNumber("x", Integer.class);

    public final NumberPath<Integer> y = createNumber("y", Integer.class);

    public final com.querydsl.sql.PrimaryKey<LauSplashScreenCreativeButtonPo> primary = createPrimaryKey(buttonId);

    public QLauSplashScreenCreativeButton(String variable) {
        super(LauSplashScreenCreativeButtonPo.class, forVariable(variable), "null", "lau_splash_screen_creative_button");
        addMetadata();
    }

    public QLauSplashScreenCreativeButton(String variable, String schema, String table) {
        super(LauSplashScreenCreativeButtonPo.class, forVariable(variable), schema, table);
        addMetadata();
    }

    public QLauSplashScreenCreativeButton(String variable, String schema) {
        super(LauSplashScreenCreativeButtonPo.class, forVariable(variable), schema, "lau_splash_screen_creative_button");
        addMetadata();
    }

    public QLauSplashScreenCreativeButton(Path<? extends LauSplashScreenCreativeButtonPo> path) {
        super(path.getType(), path.getMetadata(), "null", "lau_splash_screen_creative_button");
        addMetadata();
    }

    public QLauSplashScreenCreativeButton(PathMetadata metadata) {
        super(LauSplashScreenCreativeButtonPo.class, metadata, "null", "lau_splash_screen_creative_button");
        addMetadata();
    }

    public void addMetadata() {
        addMetadata(bgColorDay, ColumnMetadata.named("bg_color_day").withIndex(5).ofType(Types.VARCHAR).withSize(128).notNull());
        addMetadata(bgColorNight, ColumnMetadata.named("bg_color_night").withIndex(6).ofType(Types.VARCHAR).withSize(128).notNull());
        addMetadata(buttonId, ColumnMetadata.named("button_id").withIndex(1).ofType(Types.BIGINT).withSize(19).notNull());
        addMetadata(buttonType, ColumnMetadata.named("button_type").withIndex(11).ofType(Types.TINYINT).withSize(3).notNull());
        addMetadata(clickExpandRatio, ColumnMetadata.named("click_expand_ratio").withIndex(19).ofType(Types.TINYINT).withSize(3).notNull());
        addMetadata(creativeId, ColumnMetadata.named("creative_id").withIndex(2).ofType(Types.INTEGER).withSize(10).notNull());
        addMetadata(ctime, ColumnMetadata.named("ctime").withIndex(25).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(degradeType, ColumnMetadata.named("degrade_type").withIndex(21).ofType(Types.TINYINT).withSize(3).notNull());
        addMetadata(fontRatio, ColumnMetadata.named("font_ratio").withIndex(20).ofType(Types.TINYINT).withSize(3).notNull());
        addMetadata(height, ColumnMetadata.named("height").withIndex(10).ofType(Types.INTEGER).withSize(10).notNull());
        addMetadata(interactStyle, ColumnMetadata.named("interact_style").withIndex(12).ofType(Types.TINYINT).withSize(3).notNull());
        addMetadata(isDeleted, ColumnMetadata.named("is_deleted").withIndex(24).ofType(Types.TINYINT).withSize(3).notNull());
        addMetadata(jumpGuideContent, ColumnMetadata.named("jump_guide_content").withIndex(13).ofType(Types.VARCHAR).withSize(32).notNull());
        addMetadata(jumpImageMd5, ColumnMetadata.named("jump_image_md5").withIndex(15).ofType(Types.VARCHAR).withSize(1024).notNull());
        addMetadata(jumpImageUrl, ColumnMetadata.named("jump_image_url").withIndex(14).ofType(Types.VARCHAR).withSize(2048).notNull());
        addMetadata(mtime, ColumnMetadata.named("mtime").withIndex(26).ofType(Types.TIMESTAMP).withSize(19).notNull());
        addMetadata(relatedIds, ColumnMetadata.named("related_ids").withIndex(23).ofType(Types.VARCHAR).withSize(64).notNull());
        addMetadata(schemaGuideContent, ColumnMetadata.named("schema_guide_content").withIndex(16).ofType(Types.VARCHAR).withSize(32).notNull());
        addMetadata(schemaImageMd5, ColumnMetadata.named("schema_image_md5").withIndex(18).ofType(Types.VARCHAR).withSize(1024).notNull());
        addMetadata(schemaImageUrl, ColumnMetadata.named("schema_image_url").withIndex(17).ofType(Types.VARCHAR).withSize(2048).notNull());
        addMetadata(seq, ColumnMetadata.named("seq").withIndex(22).ofType(Types.TINYINT).withSize(3).notNull());
        addMetadata(textColorDay, ColumnMetadata.named("text_color_day").withIndex(7).ofType(Types.VARCHAR).withSize(128).notNull());
        addMetadata(textColorNight, ColumnMetadata.named("text_color_night").withIndex(8).ofType(Types.VARCHAR).withSize(128).notNull());
        addMetadata(width, ColumnMetadata.named("width").withIndex(9).ofType(Types.INTEGER).withSize(10).notNull());
        addMetadata(x, ColumnMetadata.named("x").withIndex(3).ofType(Types.INTEGER).withSize(10).notNull());
        addMetadata(y, ColumnMetadata.named("y").withIndex(4).ofType(Types.INTEGER).withSize(10).notNull());
    }

}

