package com.bilibili.adp.cpc.splash_screen.creative.strategy.context.on_shelf_game;

import com.bilibili.adp.cpc.core.LaunchUnitGameV1Service;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauUnitGamePo;
import com.bilibili.adp.cpc.enums.ad.PromotionPurposeType;
import com.bilibili.adp.cpc.splash_screen.creative.strategy.bos.CreativeContext;
import com.bilibili.adp.cpc.splash_screen.creative.strategy.context.ContextStrategy;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class OnShelfGameContextStrategy extends ContextStrategy {
    private final LaunchUnitGameV1Service launchUnitGameV1Service;
    @Override
    public Integer getPromotionPurposeType() {
        return PromotionPurposeType.ON_SHELF_GAME.getCode();
    }

    @Override
    public void buildContext(CreativeContext context) {
        final Integer unitId = context.getLauUnitPo().getUnitId();
        final LauUnitGamePo lauUnitGamePo = launchUnitGameV1Service.get(unitId);
        context.setLauUnitGamePo(lauUnitGamePo);
    }
}
