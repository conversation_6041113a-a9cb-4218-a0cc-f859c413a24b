package com.bilibili.adp.cpc.account_diagnosis;

import com.bapis.bcg.ones.minosv2.*;
import com.bilibili.adp.cpc.account_diagnosis.bo.DiagnosisReportColumnBo;
import com.bilibili.adp.cpc.biz.constants.Constants;
import com.bilibili.adp.cpc.biz.services.AdpCpcLauUserBehaviorService;
import com.bilibili.adp.cpc.dao.querydsl.pos.LauDiagnosisReportColumnPo;
import com.bilibili.adp.cpc.proxy.TestBusinessOnesMinosProxy;
import com.bilibili.bjcom.querydsl.BaseQueryFactory;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import pleiades.venus.starter.rpc.client.RPCClient;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.bilibili.adp.cpc.dao.config.PlatformMySqlConfig.AD_BQF;
import static com.bilibili.adp.cpc.dao.querydsl.QLauDiagnosisReportColumn.lauDiagnosisReportColumn;

@Slf4j
@Service
public class AccountDiagnosisV2Service {

    @Resource
    private TestBusinessOnesMinosProxy testBusinessOnesMinosProxy;

    @Resource(name = AD_BQF)
    private BaseQueryFactory adBqf;

    @Autowired
    private AdpCpcLauUserBehaviorService adpCpcLauUserBehaviorService;

    private static final Map<String, List<String>> type2SupportMenuScopeMap = new HashMap<>();
    private static final Map<String, Integer> type2BehaviorIdMap = new HashMap<>();

    private static final String MINOS_TIME_OUT_ERROR_MESSAGE = "查询诊断信息异常 : ";

    static {
        type2SupportMenuScopeMap.put("diagnosis_account_chart", Lists.newArrayList("ALL", "ACCOUNT"));
        type2SupportMenuScopeMap.put("diagnosis_unit_chart", Lists.newArrayList("ALL", "UNIT"));

        type2BehaviorIdMap.put("diagnosis_account_chart", Constants.SUBSCRIBED_DIAGNOSIS_ACCOUNT_REPORT_COLUMNS);
        type2BehaviorIdMap.put("diagnosis_unit_chart", Constants.SUBSCRIBED_DIAGNOSIS_UNIT_REPORT_COLUMNS);
    }

    public AccountConclusionVo conclusion(ConclusionReq req) {
        return testBusinessOnesMinosProxy.conclusion(req);
    }

    public AccountIndustryInsightVo industryInsight(IndustryInsightReq req) {
       return testBusinessOnesMinosProxy.industryInsight(req);
    }

    public AbnormalUnitListNewResp abnormalUnitListNew(AbnormalUnitListNewReq req) {
            return testBusinessOnesMinosProxy.abnormalUnitListNew(req);
    }

    public AccountSummaryVoP3 summary(DiagnosisV3QueryDto query) {
        return testBusinessOnesMinosProxy.summary(query);

    }

    public AbnormalUnitNewResp abnormalUnitNew(AbnormalUnitNewReq req) {
        return testBusinessOnesMinosProxy.abnormalUnitNew(req);
    }

    public LaunchHistoryResp launchHistory(LaunchHistoryReq req) {
        return testBusinessOnesMinosProxy.launchHistory(req);
    }

    // 异常单元一键起量
    public SaveBoostUnitsResp saveBoostUnits(SaveBoostUnitsReq req) {
        return testBusinessOnesMinosProxy.saveBoostUnits(req);

    }

    // 首页-成本比值
    public CostRatioResp costRatio(CostRatioReq req) {
        return testBusinessOnesMinosProxy.costRatio(req);
    }

    // 首页-异常单元数据
    public AbnormalUnitHomepageResp abnormalUnitHomepage(AbnormalUnitHomepageReq req) {
        return testBusinessOnesMinosProxy.abnormalUnitHomepage(req);

    }

    public CubesResp cubes(CubesReq req) {
        return testBusinessOnesMinosProxy.cubes(req);

    }

    public List<DiagnosisReportColumnBo> availableColumnBos(String type, Integer accountId) {
        List<DiagnosisReportColumnBo> diagnosisReportColumnBos = allColumns();
        List<String> supportMenuScope = type2SupportMenuScopeMap.get(type);
        if (CollectionUtils.isEmpty(supportMenuScope)) {
            return new ArrayList<>();
        }

        diagnosisReportColumnBos = diagnosisReportColumnBos.stream()
                .filter(bo -> supportMenuScope.contains(bo.getMenuScope()))
                .collect(Collectors.toList());

        Map<Integer, List<DiagnosisReportColumnBo>> parentCode2ChildrenMap = diagnosisReportColumnBos.stream().collect(Collectors.groupingBy(DiagnosisReportColumnBo::getParentCode));
        diagnosisReportColumnBos = parentCode2ChildrenMap.get(0);
        diagnosisReportColumnBos.forEach(columnsBo -> {
            columnsBo.setChildren(parentCode2ChildrenMap.get(columnsBo.getCode()));
        });

        return diagnosisReportColumnBos;
    }

    public List<DiagnosisReportColumnBo> subscribedColumns(String type, Integer accountId) {
        Integer behaviorId = type2BehaviorIdMap.get(type);
        Assert.notNull(behaviorId, type + "无对应的behaviorId");

        List<DiagnosisReportColumnBo> diagnosisReportColumnBos = allColumns();
        List<Integer> defaultSubscribedColumns = diagnosisReportColumnBos.stream()
                .filter(po -> Objects.equals(po.getFixed(), 1))
                .filter(po -> StringUtils.hasText(po.getKey()))
                .map(DiagnosisReportColumnBo::getCode)
                .collect(Collectors.toList());
        List<Integer> userSubscribedColumns = adpCpcLauUserBehaviorService.getSubscribedColumns(accountId, behaviorId, 1);
        List<Integer> subscribedColumns = org.apache.commons.collections4.CollectionUtils.isEmpty(userSubscribedColumns) ? defaultSubscribedColumns : userSubscribedColumns;

        Map<Integer, DiagnosisReportColumnBo> code2BoMap = diagnosisReportColumnBos.stream().collect(Collectors.toMap(DiagnosisReportColumnBo::getCode, Function.identity()));

        return subscribedColumns.stream().map(code2BoMap::get).filter(Objects::nonNull).collect(Collectors.toList());
    }

    public List<String> getAbnormalUnitTabTypes() {
        return Constants.DIAGNOSIS_ABNORMAL_UNIT_DATA_TYPES;
    }

    public void saveSubscribedColumns(String type, Integer accountId, List<Integer> codes) {
        Integer behaviorId = type2BehaviorIdMap.get(type);
        Assert.notNull(behaviorId, type + "无对应的behaviorId");

//        List<DiagnosisReportColumnBo> diagnosisReportColumnBos = allColumns();
//        List<Integer> fixedCodes = diagnosisReportColumnBos
//                .stream()
//                .filter(po -> Objects.equals(1, po.getFixed()))
//                .map(DiagnosisReportColumnBo::getCode)
//                .collect(Collectors.toList());
//
//        List<Integer> subscribedCodes = codes
//                .stream()
//                .filter(code -> !fixedCodes.contains(code))
//                .collect(Collectors.toList());

        adpCpcLauUserBehaviorService.saveUserReportBehavior(accountId, behaviorId, codes, 1);
    }

    private List<DiagnosisReportColumnBo> allColumns() {
        List<LauDiagnosisReportColumnPo> fetch = adBqf.select(lauDiagnosisReportColumn)
                .from(lauDiagnosisReportColumn)
                .where(lauDiagnosisReportColumn.isDeleted.eq(0))
                .fetch();

        return fetch.stream()
//                .filter(po -> StringUtils.hasText(po.getKey()))
                .filter(po -> Objects.equals(1, po.getIsSelectable()))
                .map(po -> {
                    DiagnosisReportColumnBo bo = new DiagnosisReportColumnBo();
                    bo.setFixed(po.getFixed());
                    bo.setType(getTypeInt(po.getType()));
                    bo.setCode(po.getCode());
                    bo.setParentCode(po.getParentCode());
                    bo.setName(po.getName());
                    bo.setKey(po.getKey().trim());
                    bo.setDescription(po.getDescription());
//                    bo.setIsPositionSensitive(po.getIsPositionSensitive());
                    bo.setIsSelectable(po.getIsSelectable());
                    bo.setMenuScope(po.getMenuScope());
                    return bo;
                })
                .collect(Collectors.toList());
    }

    private Integer getTypeInt(String type) {
        if ("String".equals(type)) {
            return 4;
        }

        return null;
    }
}
