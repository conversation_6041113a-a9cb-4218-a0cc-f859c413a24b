package com.bilibili.adp.cpc.wrapper.bos;

import com.bilibili.adp.cpc.vo.fly.MiddleFlyInvitationInfoVo;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class SaveProgrammaticCreativeRequestBo {
    @ApiModelProperty("单元id")
    private Integer unitId;
    @ApiModelProperty("流量类型")
    private Integer channelId;
    @ApiModelProperty("是否优选场景")
    private Boolean isPreferScene;
    @ApiModelProperty("自定义场景列表")
    private List<Integer> sceneIds;
    @ApiModelProperty("单元上绑定的稿件id")
    private Long videoId;
    @ApiModelProperty("旧版商业标")
    private Integer cmMark;
    @ApiModelProperty("品牌空间")
    private Long spaceMid;
    @ApiModelProperty("品牌标")
    private Integer brandInfoId;
    @ApiModelProperty("监控链接")
    private UnitMonitorWrapperBo monitoring;
    @ApiModelProperty("创意分类")
    private UnitBusinessCategoryWrapperBo businessCategory;
    @ApiModelProperty("创意标签")
    private List<String> tags;
    @ApiModelProperty("创意信息")
    private CreativeWrapperBo creative;
    @ApiModelProperty("邀约组件信息")
    private List<MiddleFlyInvitationInfoVo> fly_invitation_infos;
    private Integer isSmartMaterial;
}
