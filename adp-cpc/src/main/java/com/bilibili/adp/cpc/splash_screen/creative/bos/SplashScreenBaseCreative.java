package com.bilibili.adp.cpc.splash_screen.creative.bos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 */

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SplashScreenBaseCreative {

    //账户ID
    private Integer accountId;

    //账户名称
    private String accountName;

    //创意ID
    private Integer creativeId;

    //缩略图(横图)
    private String baseImageUrl;

    //闪屏名称
    private String creativeName;

    //审核状态 1-待审核，2-审核通过，3-审核不通过
    private Integer auditStatus;

    //审核状态描述
    private String auditStatusDesc;

    //创意状态:1-有效,2-已暂停,3-已结束,4-已删除,5-审核中,6-审核拒绝, 7-修改待下线, 8-已下线,9-落地页待审核创意待送审
    private Integer creativeStatus;

    //创意状态描述
    private String creativeStatusDesc;

    //驳回原因
    private String reason;

    //跳转类型
    private Integer jumpType;

    //跳转类型名称
    private String jumpTypeDesc;

    //跳转链接
    private String promotionPurposeContent;

    //投放开始时间
    private String beginTime;

    //投放结束时间
    private String endTime;

    //版本
    private Integer version;

}