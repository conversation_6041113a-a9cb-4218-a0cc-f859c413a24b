package com.bilibili.adp.cpc.wrapper.bos;

import com.bilibili.adp.cpc.core.bos.ArchiveGroup;
import com.bilibili.adp.cpc.wrapper.converters.ArchiveWithCoverConverter;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ArchiveGroupWrapperBo {
    private Integer templateGroupId;
    private List<ArchiveWithCoverWrapperBo> archives;

    public ArchiveGroup toCoreBo() {
        return ArchiveGroup.builder()
                .templateGroupId(templateGroupId)
                .archives(ArchiveWithCoverConverter.MAPPER.toCoreList(archives))
                .build();
    }

    public static ArchiveGroupWrapperBo fromCoreBo(ArchiveGroup bo) {
        return ArchiveGroupWrapperBo.builder()
                .templateGroupId(bo.getTemplateGroupId())
                .archives(ArchiveWithCoverConverter.MAPPER.toWrapperList(bo.getArchives()))
                .build();
    }
}
