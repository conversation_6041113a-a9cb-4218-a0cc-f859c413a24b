package com.bilibili.adp.cpc.databus;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bilibili.adp.common.exception.ServiceRuntimeException;
import com.bilibili.adp.cpc.biz.bos.WarpDatabusProperty;
import com.bilibili.adp.cpc.biz.constants.Constants;
import com.bilibili.adp.cpc.utils.metrics.CustomMetrics;
import com.bilibili.adp.cpc.utils.metrics.MetricsKeyConstant;
import com.bilibili.adp.launch.biz.bean.audit.AuditCreativeMessage;
import com.bilibili.adp.util.common.AdpCatUtils;
import com.bilibili.warp.databus.DatabusProperty;
import com.bilibili.warp.databus.DatabusTemplate;
import com.bilibili.warp.databus.Message;
import com.bilibili.warp.databus.PubResult;
import com.dianping.cat.Cat;
import io.opentelemetry.api.common.AttributeKey;
import io.opentelemetry.api.common.Attributes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.bilibili.adp.cpc.utils.metrics.MetricsKeyConstant.*;

/**
 * 创意审核 databus pub
 *
 * <AUTHOR>
 * @date 2023/11/21 14:17
 */
@Slf4j
@Component
public class CreativeAuditDatabusPub {

    public static final String CREATIVE_AUDIT = "creative-audit";
    private final String topic;
    private final String group;


    @Value("${audit_task.redis.machine.handle:audit_task_machine_handle}")
    private String auditTaskMachineHandle;

    @Value("${audit_task.redis.machine.handle.switch:false}")
    private Boolean auditTaskMachineHandleSwitch;

    @Autowired
    private DatabusTemplate databusTemplate;

    @Resource(name = "stringValueRedisTemplate")
    private RedisTemplate<String, String> redisTemplate;
    @Resource
    private CustomMetrics customMetrics;

    public CreativeAuditDatabusPub(WarpDatabusProperty warpDatabusProperty) {
        // 注入DatabusProperties，此处的ALIAS常量即配置文件中填写的别名，是topic的上一级配置键
        // pub场景同理，在使用DatabusTemplate的地方注入DatabusProperties来获取
        Map<String, DatabusProperty> properties = warpDatabusProperty.getProperties();
        DatabusProperty property = properties.get(CREATIVE_AUDIT);
        log.info("CreativeAuditDatabusPub, property={}", JSONObject.toJSONString(property));
        this.topic = property.getTopic();
        this.group = property.getPub().getGroup();
    }

    /**
     * 发布推审消息
     *
     * @param auditCreativeMessage
     */
    public void pubMsg(AuditCreativeMessage auditCreativeMessage) {
        String msgStr = JSON.toJSONString(auditCreativeMessage);
        log.info("wrap creative audit pub msg, msg={}", msgStr);
        Assert.notNull(auditCreativeMessage, "auditCreativeMessage参数不能为空");

        AdpCatUtils.newTransaction(Constants.CAT_TYPE_DATABUS, CREATIVE_AUDIT + ":pub", transaction -> {
            // messageKey和value自定义，value会被配置的serializer序列化
            Message message = Message.Builder.of(msgStr, auditCreativeMessage)
                    .build();
            // topic 和 group 按照申请的填写即可. 可以自行注入相关配置，用配置的方式来发送消息
            PubResult result = databusTemplate.pub(topic, group, message);
            if (result.isSuccess()) {
                log.info("creative audit pub msg success, msg={}", JSON.toJSONString(auditCreativeMessage));
                if (BooleanUtils.isTrue(auditTaskMachineHandleSwitch)) {
                    dealMachineHandleRedisRecheck(auditCreativeMessage.getCreativeId());
                }

                // 打点
                Attributes attributes = Attributes.of(AttributeKey.stringKey(count_type), MetricsKeyConstant.count_type_push_to_audit,
                        AttributeKey.stringKey(response_code), response_code_success
                );
                customMetrics.count(1, attributes);
            } else {
                Throwable throwable = result.getThrowable();
                log.info("creative audit pub msg error, e={}", throwable);

                Attributes attributes = Attributes.of(AttributeKey.stringKey(count_type), MetricsKeyConstant.count_type_push_to_audit,
                        AttributeKey.stringKey(response_code), response_code_fail
                );
                customMetrics.count(1, attributes);
                throw new ServiceRuntimeException(throwable.getMessage());
            }
        });
    }

    private void dealMachineHandleRedisRecheck(List<Integer> creativeIdList) {
        try {
            long nowTime = System.currentTimeMillis();
            Map<String, String> creativeIdMap = creativeIdList.stream().collect(Collectors.toMap(String::valueOf, dummy -> String.valueOf(nowTime)));
            redisTemplate.opsForHash().putAll(auditTaskMachineHandle, creativeIdMap);
            Cat.logEvent("dealMachineHandleRedisRecheck", "add_redis_success", "success", JSON.toJSONString(creativeIdList));
        } catch (Exception e) {
            log.error("CreativeAuditDatabusPub.dealMachineHandleRedisRecheck exception, redisKey={}", JSON.toJSONString(creativeIdList), e);
            Cat.logEvent("dealMachineHandleRedisRecheck", "add_redis_exception", "success", JSON.toJSONString(creativeIdList));
        }
    }
}
