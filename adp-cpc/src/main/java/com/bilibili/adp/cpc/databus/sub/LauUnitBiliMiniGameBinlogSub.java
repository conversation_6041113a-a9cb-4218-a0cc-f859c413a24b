package com.bilibili.adp.cpc.databus.sub;

import com.alibaba.fastjson.JSONObject;
import com.bilibili.adp.cpc.biz.bos.WarpDatabusProperty;
import com.bilibili.adp.cpc.biz.services.ad_product.IAdProductService;
import com.bilibili.adp.cpc.dao.querydsl.pos.LauUnitBiliMiniGamePo;
import com.bilibili.adp.cpc.dao.querydsl_cpc.pos.AdProductMappingCPCPo;
import com.bilibili.adp.cpc.dao.querydsl_cpc.pos.AdProductTotalInfoCPCPo;
import com.bilibili.adp.cpc.databus.Constant;
import com.bilibili.adp.cpc.enums.ad_product.AdProductBelongTypeEnum;
import com.bilibili.adp.cpc.enums.ad_product.AdProductBindTypeEnum;
import com.bilibili.adp.cpc.enums.ad_product.SdpaGameProductTypeEnum;
import com.bilibili.adp.cpc.repo.AdProductRepo;
import com.bilibili.warp.databus.AckableMessage;
import com.bilibili.warp.databus.DatabusProperty;
import com.bilibili.warp.databus.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.bilibili.adp.cpc.biz.services.ad_product.AdProductServiceImpl.GAME_UNIQUE_PREFIX;

@Component
@Slf4j
public class LauUnitBiliMiniGameBinlogSub implements MessageListener {

    @Autowired
    private IAdProductService adProductService;

    private final String topic;

    private final String group;

    public LauUnitBiliMiniGameBinlogSub(WarpDatabusProperty warpDatabusProperty) {
        // 注入DatabusProperties，此处的ALIAS常量即配置文件中填写的别名，是topic的上一级配置键
        // pub场景同理，在使用DatabusTemplate的地方注入DatabusProperties来获取
        Map<String, DatabusProperty> properties = warpDatabusProperty.getProperties();
        DatabusProperty property = properties.get("lau_unit_bili_mini_game");
        log.info("lau_unit_bili_mini_game, property={}", JSONObject.toJSONString(property));
        this.topic = property.getTopic();
        this.group = property.getSub().getGroup();
    }


    @Override
    public String topic() {
        return this.topic;
    }

    @Override
    public String group() {
        return this.group;
    }

    @Override
    public void onMessage(AckableMessage message) {
        String value = new String(message.payload());
        JSONObject msg = JSONObject.parseObject(value);

        String action = msg.getString(Constant.ACTION);

        JSONObject newObject = msg.getJSONObject(Constant.NEW);

        LauUnitBiliMiniGamePo lauUnitBiliMiniGamePo = deserializeBinlogDto(newObject);

        if (Objects.equals(action, Constant.INSERT)) {
            adProductService.createBiliGameMapping(lauUnitBiliMiniGamePo);
        }
        message.ack();
    }

    private LauUnitBiliMiniGamePo deserializeBinlogDto(JSONObject jsonObject) {
        if (Objects.isNull(jsonObject)) {
            return null;
        }
        return jsonObject.toJavaObject(LauUnitBiliMiniGamePo.class);
    }

}
