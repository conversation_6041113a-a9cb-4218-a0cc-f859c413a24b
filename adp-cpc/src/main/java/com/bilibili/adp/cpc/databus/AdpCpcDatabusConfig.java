package com.bilibili.adp.cpc.databus;

import com.bilibili.mgk.platform.common.utils.NumberUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import pleiades.component.databus.pub.DatabusPub;
import pleiades.component.databus.sub.DatabusSub;
import pleiades.venus.starter.config.MixPropertySourceFactory;

import java.util.concurrent.Executors;

@Slf4j
@Configuration
@PropertySource(value = "classpath:databus_v2.yaml", factory = MixPropertySourceFactory.class)
public class AdpCpcDatabusConfig {

    public static final String PANDORA_EVENT_SUB_PROPERTIES = "PANDORA_EVENT_SUB_PROPERTIES";
    public static final String PANDORA_EVENT_SUB = "PANDORA_EVENT_SUB";

    @Bean(PANDORA_EVENT_SUB_PROPERTIES)
    @ConfigurationProperties("databus.pandora-event")
    public DatabusPropertyWrapperBo pandoraEventProperty() {
        return new DatabusPropertyWrapperBo();
    }

    @Bean(PANDORA_EVENT_SUB)
    public DatabusClient pandoraEventClient(@Qualifier(PANDORA_EVENT_SUB_PROPERTIES) DatabusPropertyWrapperBo property) {
        log.info("DatabusClient初始化参数: property={}", property);
        return getDatabusClient(property);
    }

    public static final String FORM_SUBMIT_SUB_PROPERTIES = "FORM_SUBMIT_SUB_PROPERTIES";
    public static final String FORM_SUBMIT_SUB = "FORM_SUBMIT_SUB";

    @Bean(FORM_SUBMIT_SUB_PROPERTIES)
    @ConfigurationProperties("databus.form-submit")
    public DatabusPropertyWrapperBo formSubmitProperty() {
        return new DatabusPropertyWrapperBo();
    }

    @Bean(FORM_SUBMIT_SUB)
    public DatabusClient formSubmitClient(@Qualifier(FORM_SUBMIT_SUB_PROPERTIES) DatabusPropertyWrapperBo property) {
        log.info("DatabusClient初始化参数: property={}", property);
        return getDatabusClient(property);
    }

    private DatabusClient getDatabusClient(DatabusPropertyWrapperBo property) {
        final DatabusClient client = new DatabusClient();
        if (NumberUtils.isPositive(property.getThreads())) {
            client.setEnabled(true);
            client.setExecutorService(Executors.newFixedThreadPool(property.getThreads()));
            client.setSub(new DatabusSub(property.getProperties()));
        }
        return client;
    }
}
