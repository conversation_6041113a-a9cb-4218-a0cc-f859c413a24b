package com.bilibili.adp.cpc.automatic_rule.service;

import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.automatic_rule.bos.ActionBo;
import com.bilibili.adp.cpc.automatic_rule.bos.ConditionBo;
import com.bilibili.adp.cpc.automatic_rule.bos.RuleBo;
import com.bilibili.adp.cpc.automatic_rule.bos.RuleDetailBo;
import com.bilibili.adp.cpc.automatic_rule.constants.AutomaticRuleConstant;
import com.bilibili.adp.cpc.automatic_rule.converter.ActionConverter;
import com.bilibili.adp.cpc.automatic_rule.converter.ConditionConverter;
import com.bilibili.adp.cpc.automatic_rule.converter.RuleConverter;
import com.bilibili.adp.cpc.config.AppConfig;
import com.bilibili.adp.cpc.dao.querydsl.pos.LauAutomaticRuleActionPo;
import com.bilibili.adp.cpc.dao.querydsl.pos.LauAutomaticRuleConditionPo;
import com.bilibili.adp.cpc.dao.querydsl.pos.LauAutomaticRuleObjectPo;
import com.bilibili.adp.cpc.dao.querydsl.pos.LauAutomaticRulePo;
import com.bilibili.bjcom.querydsl.BaseQueryFactory;
import com.bilibili.bjcom.querydsl.paging.Page;
import com.bilibili.bjcom.querydsl.paging.Pager;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.bilibili.adp.cpc.dao.config.PlatformMySqlConfig.AD_BQF;
import static com.bilibili.adp.cpc.dao.config.PlatformMySqlConfig.AD_TM;
import static com.bilibili.adp.cpc.dao.querydsl.QLauAutomaticRule.lauAutomaticRule;

@Slf4j
@Service
@RequiredArgsConstructor
public class RuleService {
    private final ConditionService conditionService;
    private final ActionService actionService;
    private final ObjectService objectService;
    @Resource(name = AD_BQF)
    private BaseQueryFactory adBqf;
    @Resource(name = AppConfig.OBJECT_MAPPER)
    private ObjectMapper objectMapper;

    @SneakyThrows
    private List<Integer> parseDaysOfWeek(String daysOfWeekString) {
        if (!StringUtils.hasText(daysOfWeekString)) {
            return Collections.emptyList();
        }
        return objectMapper.readValue(daysOfWeekString, new TypeReference<List<Integer>>() {
        });
    }

    @SneakyThrows
    private List<String> parseTime(String timeString) {
        if (!StringUtils.hasText(timeString)) {
            return Collections.emptyList();
        }
        return objectMapper.readValue(timeString, new TypeReference<List<String>>() {
        });
    }

    public List<LauAutomaticRulePo> listRulesByAccountId(Integer accountId) {
        return adBqf
                .selectFrom(lauAutomaticRule)
                .where(lauAutomaticRule.accountId.eq(accountId))
                .where(lauAutomaticRule.isDeleted.eq(0))
                .orderBy(lauAutomaticRule.ruleId.desc())
                .fetch();
    }

    public Page<LauAutomaticRulePo> listRulesByAccountId(Integer accountId, Integer pn, Integer ps) {
        return adBqf
                .selectFrom(lauAutomaticRule)
                .where(lauAutomaticRule.accountId.eq(accountId))
                .where(lauAutomaticRule.isDeleted.eq(0))
                .orderBy(lauAutomaticRule.ruleId.desc())
                .fetchPage(Pager.of(pn, ps));
    }

    public LauAutomaticRulePo getRuleByRuleId(Long ruleId) {
        return adBqf
                .selectFrom(lauAutomaticRule)
                .where(lauAutomaticRule.ruleId.eq(ruleId))
                .fetchFirst();
    }

    public RuleDetailBo ruleDetail(Long ruleId) {
        final LauAutomaticRulePo rule = getRuleByRuleId(ruleId);
        final List<LauAutomaticRuleConditionPo> conditionPos = conditionService.listConditionsByRuleId(ruleId);
        final List<LauAutomaticRuleActionPo> actionPos = actionService.listActionsByRuleId(ruleId);
        final List<LauAutomaticRuleObjectPo> objects = objectService.listObjectsByRuleId(ruleId);
        return ruleDetail(rule, conditionPos, actionPos, objects);
    }

    public RuleDetailBo ruleDetail(LauAutomaticRulePo rule, List<LauAutomaticRuleConditionPo> conditionPos, List<LauAutomaticRuleActionPo> actionPos, List<LauAutomaticRuleObjectPo> objectPos) {
        final List<ConditionBo> conditions = conditionPos
                .stream()
                .map(ConditionConverter.MAPPER::po2Bo)
                .collect(Collectors.toList());
        final List<ActionBo> actions = actionPos
                .stream()
                .map(ActionConverter.MAPPER::po2Bo)
                .collect(Collectors.toList());
        final boolean unlimitedObjects = objectPos
                .stream()
                .allMatch(object -> object.getObjectId().equals(AutomaticRuleConstant.UNLIMITED_OBJECTS));
        final Integer objectType = objectPos
                .stream()
                .map(LauAutomaticRuleObjectPo::getObjectType)
                .distinct()
                .findFirst()
                .orElseThrow(IllegalArgumentException::new);
        final List<Integer> objectIds = unlimitedObjects ? Collections.emptyList() : objectPos
                .stream()
                .map(LauAutomaticRuleObjectPo::getObjectId)
                .collect(Collectors.toList());
        final List<Integer> dayOfWeek = parseDaysOfWeek(rule.getDayOfWeek());
        final List<String> fixedTime = parseTime(rule.getFixedTime());
        final List<String> periodTime = parseTime(rule.getPeriodTime());
        return RuleConverter.MAPPER.po2DetailBo(rule, conditions, actions, objectType, objectIds, dayOfWeek, fixedTime, periodTime);
    }

    public RuleBo rule(LauAutomaticRulePo rule, List<LauAutomaticRuleConditionPo> conditionPos, List<LauAutomaticRuleActionPo> actionPos, List<LauAutomaticRuleObjectPo> objectPos) {
        List<String> conditions = ConditionService.conditions(conditionPos);
        List<String> actions = ActionService.actions(actionPos);
        final boolean unlimitedObjects = objectPos
                .stream()
                .allMatch(object -> object.getObjectId().equals(AutomaticRuleConstant.UNLIMITED_OBJECTS));
        final Integer objectType = objectPos
                .stream()
                .map(LauAutomaticRuleObjectPo::getObjectType)
                .distinct()
                .findFirst()
                .orElseThrow(IllegalArgumentException::new);
        final List<Integer> objectIds = unlimitedObjects ? Collections.emptyList() : objectPos
                .stream()
                .map(LauAutomaticRuleObjectPo::getObjectId)
                .collect(Collectors.toList());
        return RuleConverter.MAPPER.po2Bo(rule, conditions, actions, objectType, objectIds);
    }

    public List<RuleDetailBo> systemRules() {
        List<LauAutomaticRulePo> rules = listRulesByAccountId(0);
        final Set<Long> ruleIds = rules
                .stream()
                .map(LauAutomaticRulePo::getRuleId)
                .collect(Collectors.toSet());
        final Map<Long, List<LauAutomaticRuleConditionPo>> conditionMap = conditionService.listConditionsByRuleIds(ruleIds)
                .stream()
                .collect(Collectors.groupingBy(LauAutomaticRuleConditionPo::getRuleId));
        final Map<Long, List<LauAutomaticRuleActionPo>> actionMap = actionService.listActionsByRuleIds(ruleIds)
                .stream()
                .collect(Collectors.groupingBy(LauAutomaticRuleActionPo::getRuleId));
        final Map<Long, List<LauAutomaticRuleObjectPo>> objectMap = objectService.listObjectsByRuleIds(ruleIds)
                .stream()
                .collect(Collectors.groupingBy(LauAutomaticRuleObjectPo::getRuleId));
        return rules
                .stream()
                .map(rule -> {
                    long ruleId = rule.getRuleId();
                    List<LauAutomaticRuleConditionPo> conditionPos = conditionMap.getOrDefault(ruleId, Collections.emptyList());
                    List<LauAutomaticRuleActionPo> actionPos = actionMap.getOrDefault(ruleId, Collections.emptyList());
                    List<LauAutomaticRuleObjectPo> objectPos = objectMap.getOrDefault(ruleId, Collections.emptyList());
                    return ruleDetail(rule, conditionPos, actionPos, objectPos);
                })
                .collect(Collectors.toList());
    }

    public PageResult<RuleBo> customizedRules(Integer accountId, Integer pn, Integer ps) {
        final Page<LauAutomaticRulePo> rulesPage = listRulesByAccountId(accountId, pn, ps);
        final Set<Long> ruleIds = rulesPage
                .getRows()
                .stream()
                .map(LauAutomaticRulePo::getRuleId)
                .collect(Collectors.toSet());
        final Map<Long, List<LauAutomaticRuleConditionPo>> conditionMap = conditionService.listConditionsByRuleIds(ruleIds)
                .stream()
                .collect(Collectors.groupingBy(LauAutomaticRuleConditionPo::getRuleId));
        final Map<Long, List<LauAutomaticRuleActionPo>> actionMap = actionService.listActionsByRuleIds(ruleIds)
                .stream()
                .collect(Collectors.groupingBy(LauAutomaticRuleActionPo::getRuleId));
        final Map<Long, List<LauAutomaticRuleObjectPo>> objectMap = objectService.listObjectsByRuleIds(ruleIds)
                .stream()
                .collect(Collectors.groupingBy(LauAutomaticRuleObjectPo::getRuleId));
        final List<RuleBo> rules = rulesPage
                .getRows()
                .stream()
                .map(rule -> {
                    long ruleId = rule.getRuleId();
                    List<LauAutomaticRuleConditionPo> conditionPos = conditionMap.getOrDefault(ruleId, Collections.emptyList());
                    List<LauAutomaticRuleActionPo> actionPos = actionMap.getOrDefault(ruleId, Collections.emptyList());
                    List<LauAutomaticRuleObjectPo> objectPos = objectMap.getOrDefault(ruleId, Collections.emptyList());
                    return rule(rule, conditionPos, actionPos, objectPos);
                })
                .collect(Collectors.toList());
        return PageResult
                .<RuleBo>builder()
                .total((int) rulesPage.getTotal())
                .records(rules)
                .build();
    }

    //这个方法会查所有的 包含已删除的 给执行记录用的
    public List<LauAutomaticRulePo> listRulesByRuleIds(Collection<Long> ruleIds) {
        return adBqf
                .selectFrom(lauAutomaticRule)
                .where(lauAutomaticRule.ruleId.in(ruleIds))
                .fetch();
    }

    @Transactional(transactionManager = AD_TM, rollbackFor = Exception.class)
    public void deleteRule(Integer accountId, Long ruleId) {
        adBqf.update(lauAutomaticRule)
                .set(lauAutomaticRule.isDeleted, AutomaticRuleConstant.RULE_IS_DELETED)
                .where(lauAutomaticRule.accountId.eq(accountId))
                .where(lauAutomaticRule.ruleId.eq(ruleId))
                .execute();
        conditionService.deleteConditions(accountId, ruleId);
        actionService.deleteActions(accountId, ruleId);
        objectService.deleteObjects(accountId, ruleId);
    }
    @Transactional(transactionManager = AD_TM, rollbackFor = Exception.class)
    public void enableRule(Integer accountId, Long ruleId) {
        adBqf.update(lauAutomaticRule)
                .set(lauAutomaticRule.status, AutomaticRuleConstant.RULE_STATUS_VALID)
                .where(lauAutomaticRule.accountId.eq(accountId))
                .where(lauAutomaticRule.ruleId.eq(ruleId))
                .execute();
    }
    @Transactional(transactionManager = AD_TM, rollbackFor = Exception.class)
    public void disableRule(Integer accountId, Long ruleId) {
        adBqf.update(lauAutomaticRule)
                .set(lauAutomaticRule.status, AutomaticRuleConstant.RULE_STATUS_INVALID)
                .where(lauAutomaticRule.accountId.eq(accountId))
                .where(lauAutomaticRule.ruleId.eq(ruleId))
                .execute();
    }

    @Transactional(transactionManager = AD_TM, rollbackFor = Exception.class)
    public Long createRule(Integer accountId, RuleDetailBo detail) {
        final Long ruleId = saveAutomaticRule(accountId, detail);
        conditionService.saveConditions(accountId, ruleId, detail.getConditions());
        actionService.saveActions(accountId, ruleId, detail.getActions());
        objectService.saveObjects(accountId, ruleId, detail.getObjectType(), detail.getObjectIds());
        return ruleId;
    }

    @Transactional(transactionManager = AD_TM, rollbackFor = Exception.class)
    public Long updateRule(Integer accountId, RuleDetailBo detail) {
        final Long ruleId = saveAutomaticRule(accountId, detail);
        conditionService.saveConditions(accountId, ruleId, detail.getConditions());
        actionService.saveActions(accountId, ruleId, detail.getActions());
        objectService.saveObjects(accountId, ruleId, detail.getObjectType(), detail.getObjectIds());
        return ruleId;
    }

    @SneakyThrows
    private Long saveAutomaticRule(Integer accountId, RuleDetailBo detail) {
        final String dayOfWeek = objectMapper.writeValueAsString(detail.getDayOfWeek());
        final String fixedTime = objectMapper.writeValueAsString(detail.getFixedTime());
        final String periodTime = objectMapper.writeValueAsString(detail.getPeriodTime());
        final LauAutomaticRulePo rulePo = RuleConverter.MAPPER.bo2Po(accountId, dayOfWeek, fixedTime, periodTime, detail);
        final long ruleId;
        if (Utils.isPositive(detail.getRuleId())) {
            adBqf.update(lauAutomaticRule).updateBean(rulePo);
            ruleId = detail.getRuleId();
        } else {
            ruleId = adBqf.insert(lauAutomaticRule).insertGetKey(rulePo);
        }
        return ruleId;
    }



}
