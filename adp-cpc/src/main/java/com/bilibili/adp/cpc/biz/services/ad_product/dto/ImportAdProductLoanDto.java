package com.bilibili.adp.cpc.biz.services.ad_product.dto;

import com.bilibili.adp.common.annotation.DatabaseColumnEnum;
import com.bilibili.adp.common.annotation.DatabaseColumnName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class ImportAdProductLoanDto extends ImportAdProductBaseDto {

    private static final long serialVersionUID = 1L;


    /**
     * 最高年化利率 格式:[1]
     */
    @DatabaseColumnEnum("{'38':'9%','39':'18%','40':'24%','41':'36%','42':'其他'}")
    @DatabaseColumnName("最高年化利率")
    private String maxYearRate;
    // 格式: 9%
    private String maxYearRateName;

    @DatabaseColumnEnum("{'43':'5万以下','44':'10万','45':'15万','46':'20万','47':'30万','48':'50万','49':'100万','50':'300万','51':'300万以上'}")
    @DatabaseColumnName("最高授信额度")
    private String maxCreditLimit;
    private String maxCreditLimitName;
    @DatabaseColumnEnum("{'52':'1千~5千','53':'5千~1万','54':'1万~5万','55':'5万~10万','56':'10万~15万','57':'15万~20万','58':'20万~30万','59':'30万~50万','60':'50万~100万','61':'100万~300万','62':'300万以上'}")
    @DatabaseColumnName("平均授信额度")
    private String averageCreditLimit;
    private String averageCreditLimitName;
    @DatabaseColumnEnum("{'63':'3期','64':'6期','65':'10期','66':'12期','67':'24期','68':'36期','69':'48期及以上'}")
    @DatabaseColumnName("贷款期限")
    private String loanTerm;
    private String loanTermName;
    @DatabaseColumnEnum("{'80':'资金周转','81':'日常小额消费','82':'日常大额消费','83':'其他', '84':'允许随时提前还款','85':'允许部分提前还款','86':'允许提前结清'}")
    @DatabaseColumnName("贷款用途")
    private String loanUsage;
    private String loanUsageName;
    @DatabaseColumnName("万元日息")
    private String dailyInterest;
    private String dailyInterestName;
    /**
     * 计息方式
     */
    @DatabaseColumnEnum("{'70':'按日计息','71':'按期计息'}")
    @DatabaseColumnName("计息方式")
    private String interestType;
    private String interestTypeName;
    /**
     * 还款方式
     */
    @DatabaseColumnEnum("{'72':'等本等息','73':'等额本息','74':'等额本金','75':'一次还本付息','76':'先息后本(按月付息,到期一次性还本)','77':'一次性付息','78':'等额还本','79':'其他'}")
    @DatabaseColumnName("还款方式")
    private String rapaymentType;
    private String rapaymentTypeName;
    @DatabaseColumnEnum("{'84':'允许随时提前还款','85':'允许部分提前还款','86':'允许提前结清'}")
    @DatabaseColumnName("提前还款")
    private String earlyRepayment;
    private String earlyRepaymentName;
    @DatabaseColumnEnum("{'87':'提前还款违约金','88':'无提前还款违约金'}")
    @DatabaseColumnName("提前还款违约金")
    private String earlyRepaymentPenalty;
    private String earlyRepaymentPenaltyName;
}