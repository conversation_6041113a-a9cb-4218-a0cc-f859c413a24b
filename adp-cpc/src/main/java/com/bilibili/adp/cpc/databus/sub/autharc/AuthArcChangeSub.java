package com.bilibili.adp.cpc.databus.sub.autharc;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.biz.bos.WarpDatabusProperty;
import com.bilibili.adp.cpc.biz.bos.archive.ArchiveBo;
import com.bilibili.adp.cpc.biz.services.archive.ArchiveService;
import com.bilibili.adp.cpc.databus.bos.AuthArcChangeMsg;
import com.bilibili.adp.cpc.enums.archive.ArchiveAuthStatusEnum;
import com.bilibili.adp.cpc.enums.archive.ArchiveAuthTypeEnum;
import com.bilibili.adp.v6.resource.archive.GeneralArchiveService;
import com.bilibili.warp.databus.AckableMessage;
import com.bilibili.warp.databus.DatabusProperty;
import com.bilibili.warp.databus.MessageListener;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/12/31
 * @description 授权稿件变更订阅
 */
@Slf4j
@Component
public class AuthArcChangeSub implements MessageListener {

    @Resource
    private ObjectMapper objectMapper;
    @Resource
    private ArchiveService archiveService;
    @Resource
    private GeneralArchiveService generalArchiveService;

    private final String topic;
    private final String group;
    @Override
    public String topic() {
        return this.topic;
    }

    @Override
    public String group() {
        return this.group;
    }
    public AuthArcChangeSub(WarpDatabusProperty databusProperties){
        DatabusProperty databusProperty = databusProperties.getProperties().get("auth-arc-change");
        this.topic = databusProperty.getTopic();
        this.group = databusProperty.getSub().getGroup();
    }

    @Override
    public void onMessage(AckableMessage message) {
        try {
            AuthArcChangeMsg authArcChangeMsg = objectMapper.readValue(message.payload(), AuthArcChangeMsg.class);
            log.info("AuthArcChangeSub receive message: {}", objectMapper.writeValueAsString(authArcChangeMsg));
            Integer source = authArcChangeMsg.getSource();
            Integer type = authArcChangeMsg.getType();
            Long avid = authArcChangeMsg.getAvid();
            String title = authArcChangeMsg.getTitle();
            if (!Utils.isPositive(source) || !Utils.isPositive(type) || !Utils.isPositive(avid)){
                log.warn("AuthArcChangeSub message err message: {}", objectMapper.writeValueAsString(authArcChangeMsg));
                return;
            }

            //根据source抽出来
            ArchiveBo archiveByAid = archiveService.getArchiveByAid(avid);
            if (Objects.isNull(archiveByAid)){
                log.warn("AuthArcChangeSub message err arc is null message: {}", objectMapper.writeValueAsString(authArcChangeMsg));
                return;
            }
            if(ArchiveAuthTypeEnum.PUGV.getCode()==source && !archiveByAid.getIsPugv()){
                log.warn("AuthArcChangeSub source  message err message: {}", objectMapper.writeValueAsString(authArcChangeMsg));
            }
            switch (type) {
                case 1:
                    //标题变更
                    generalArchiveService.updateAuthArchiveTitle(avid, title);
                    break;
                case 2:
                    //授权关系失效
                    generalArchiveService.updateAuthArchiveStatus(avid, ArchiveAuthStatusEnum.AUTHORIZED.getCode(),ArchiveAuthStatusEnum.LOSE_EFFECT.getCode());
                    break;
                case 3:
                    //拒审广告
                    generalArchiveService.updateLoseEffect(avid);
                    break;
                case 4:
                    //授权由失效变为有效
                    generalArchiveService.updateAuthArchiveStatus(avid, ArchiveAuthStatusEnum.LOSE_EFFECT.getCode(),ArchiveAuthStatusEnum.AUTHORIZED.getCode());
                    break;
                default:
            }
            message.ack();
        } catch (Exception e) {
            log.error("AuthArcChangeSub err", e);
            message.nack();
        }
    }

    @Override
    public boolean autoCommit() {
        return false;
    }

}
