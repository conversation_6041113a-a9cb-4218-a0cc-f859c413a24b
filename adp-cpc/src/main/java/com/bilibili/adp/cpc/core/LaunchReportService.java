package com.bilibili.adp.cpc.core;

import com.alibaba.fastjson.JSONArray;
import com.bapis.ad.jupiter.rta.RtaReportServiceGrpc;
import com.bapis.ad.jupiter.rta.RtaStatReply;
import com.bapis.ad.jupiter.rta.RtaStatReq;
import com.bapis.ad.report.*;
import com.bapis.archive.service.Arc;
import com.bapis.datacenter.service.oneservice.*;
import com.bilibili.adp.account.dto.AccountDto;
import com.bilibili.adp.account.service.IQueryAccountService;
import com.bilibili.adp.common.Constants;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.enums.*;
import com.bilibili.adp.common.util.StringDateParser;
import com.bilibili.adp.common.util.TimeUtils;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.common.util.func.Functions;
import com.bilibili.adp.cpc.biz.bos.archive.BusinessFlyArchiveBo;
import com.bilibili.adp.cpc.biz.bos.archive.BusinessFlyArchiveLaunchBo;
import com.bilibili.adp.cpc.biz.bos.archive.BusinessFlyArchivePartStayBo;
import com.bilibili.adp.cpc.biz.bos.creative.QueryCreativeBo;
import com.bilibili.adp.cpc.biz.bos.query.QueryRtaReportBo;
import com.bilibili.adp.cpc.biz.bos.report.*;
import com.bilibili.adp.cpc.biz.converter.report.StatConverter;
import com.bilibili.adp.cpc.biz.services.account.AccountWalletService;
import com.bilibili.adp.cpc.biz.services.account.config.AccountConfig;
import com.bilibili.adp.cpc.biz.services.archive.ArchiveService;
import com.bilibili.adp.cpc.biz.services.campaign.CpcCampaignService;
import com.bilibili.adp.cpc.biz.services.campaign.dto.CpcCampaignDto;
import com.bilibili.adp.cpc.biz.services.creative.api.ICpcCreativeService;
import com.bilibili.adp.cpc.biz.services.creative.bos.programmatic.ProgrammaticInfoBo;
import com.bilibili.adp.cpc.biz.services.creative.dto.CpcCreativeDto;
import com.bilibili.adp.cpc.biz.services.dynamic.DynamicLinkProc;
import com.bilibili.adp.cpc.biz.services.game.AdpCpcGameService;
import com.bilibili.adp.cpc.biz.services.misc.AdpCpcOcpxService;
import com.bilibili.adp.cpc.biz.services.misc.bos.LauOcpxConfigBo;
import com.bilibili.adp.cpc.biz.services.ocpx.compensate.common.IOcpxAutoPayService;
import com.bilibili.adp.cpc.biz.services.ocpx.compensate.common.IOcpxAutoPayStatusService;
import com.bilibili.adp.cpc.biz.services.ocpx.compensate.common.IOcpxAutoPayTimeService;
import com.bilibili.adp.cpc.biz.services.resource.AdpCpcResourceService;
import com.bilibili.adp.cpc.biz.services.target_package.api.IResTargetItemService;
import com.bilibili.adp.cpc.biz.services.unit.*;
import com.bilibili.adp.cpc.biz.services.unit.api.ICpcUnitService;
import com.bilibili.adp.cpc.biz.services.unit.bos.LauUnitAccelerateBo;
import com.bilibili.adp.cpc.biz.services.unit.bos.LauUnitGameBo;
import com.bilibili.adp.cpc.biz.services.unit.dto.CpcUnitDto;
import com.bilibili.adp.cpc.core.bos.AllStatTargetParamBo;
import com.bilibili.adp.cpc.core.constants.DualBidTwoStageOptimization;
import com.bilibili.adp.cpc.core.constants.TemplateGroupV2;
import com.bilibili.adp.cpc.core.converter.RtaReportConverter;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauCampaignPo;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauSplashScreenCreativeImagePo;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauUnitCreativePo;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauUnitPo;
import com.bilibili.adp.cpc.dao.querydsl_clickhouse.pos.AdsCtntArchFlyArchiveVvRetention1dDPo;
import com.bilibili.adp.cpc.dao.querydsl_clickhouse.pos.DwsCtntArchFlyArchivePlayInteractADPo;
import com.bilibili.adp.cpc.dto.NewLauCampaignNextdayBudgetDto;
import com.bilibili.adp.cpc.dto.NewLauUnitNextdayBudgetDto;
import com.bilibili.adp.cpc.enums.AdpVersion;
import com.bilibili.adp.cpc.enums.*;
import com.bilibili.adp.cpc.enums.ad.CampaignAdType;
import com.bilibili.adp.cpc.enums.ad.OcpcTargetEnum;
import com.bilibili.adp.cpc.enums.ad.PromotionContentTypeEnum;
import com.bilibili.adp.cpc.enums.ad.PromotionPurposeType;
import com.bilibili.adp.cpc.enums.effectad.report.OrderEnum;
import com.bilibili.adp.cpc.enums.ocpx.UnitPeriodTypeEnum;
import com.bilibili.adp.cpc.pluto.service.LaunchReportBaseService;
import com.bilibili.adp.cpc.proxy.CpmJupiterProxy;
import com.bilibili.adp.cpc.proxy.CpmReportSvrProxy;
import com.bilibili.adp.cpc.splash_screen.creative.service.SplashScreenCreativeImageService;
import com.bilibili.adp.cpc.utils.OcpxTargetBidUtil;
import com.bilibili.adp.launch.api.common.*;
import com.bilibili.adp.launch.api.creative.dto.ImageDto;
import com.bilibili.adp.launch.api.launch.dto.OcpxAutoPayStatusDto;
import com.bilibili.adp.launch.api.launch.dto.OcpxAutoPayTimeDto;
import com.bilibili.adp.launch.api.unit.dto.UnitOcpxFloatStatusDto;
import com.bilibili.adp.launch.biz.pojo.LauUnitArchiveVideoPo;
import com.bilibili.adp.resource.api.slot_group.IResSlotGroupService;
import com.bilibili.adp.resource.api.slot_group.SlotGroupSimpleDto;
import com.bilibili.adp.resource.api.system.ISystemConfigService;
import com.bilibili.adp.resource.api.target_lau.dto.ResTargetItemDto;
import com.bilibili.adp.v6.enums.SupportAuto;
import com.bilibili.bjcom.querydsl.BaseQueryFactory;
import com.bilibili.bvid.BVIDUtils;
import com.bilibili.crm.platform.common.IsValid;
import com.bilibili.location.api.service.ITemplateGroupService;
import com.bilibili.location.api.template.dto.QueryTemplateGroupDto;
import com.bilibili.location.api.template.dto.TemplateGroupBo;
import com.bilibili.mgk.platform.api.landing_page.soa.ISoaLandingPageService;
import com.dianping.cat.Cat;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import io.vavr.control.Try;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.elasticsearch.common.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import pleiades.venus.starter.rpc.client.RPCClient;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.bilibili.adp.cpc.dao.querydsl_clickhouse.QAdsCtntArchFlyArchiveVvRetention1dD.adsCtntArchFlyArchiveVvRetention1dD;
import static com.bilibili.adp.cpc.dao.querydsl_clickhouse.QDwsCtntArchFlyArchivePlayInteractAD.dwsCtntArchFlyArchivePlayInteractAD;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LaunchReportService extends LaunchReportBaseService {
    private static final List<Integer> OCPX_SALES_TYPES = Arrays.asList(
            SalesType.CPC.getCode(),
            SalesType.CPM.getCode(),
            SalesType.FLY_PRE_PAY_GD_PLUS.getCode(),
            SalesType.NATURE_COMMENT.getCode(),
            SalesType.COMMERCE_COMMENT.getCode(),
            SalesType.NATURAL_TAKE_GOODS.getCode(),
            SalesType.NATURAL_LIVE.getCode(),
            SalesType.OUTER_GAME_EXPANSION.getCode()
    );
    private static final List<Integer> PLATFORM_SALES_TYPES = Arrays.asList(
            SalesType.CPC.getCode(),
            SalesType.CPM.getCode(),
            SalesType.FLY_PRE_PAY_GD_PLUS.getCode(),
            SalesType.NATURAL_TAKE_GOODS.getCode(),
            SalesType.NATURAL_LIVE.getCode(),
            SalesType.OUTER_GAME_EXPANSION.getCode()
    );
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm:ss");
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final String CREATIVE_TYPE_PROGRAMMATIC = "程序化创意";
    private static final String CREATIVE_TYPE_CUSTOM = "自定义创意";

    @RPCClient("datacenter.oneservice.akuya-dispatch-service")
    private OneServiceOpenApiManagerGrpc.OneServiceOpenApiManagerBlockingStub serviceBlockingStub;


    @Autowired
    private CpmReportSvrProxy cpmReportSvrProxy;
    @Resource
    private CpmJupiterProxy cpmJupiterProxy;
    private final LauBudgetService lauBudgetService;
    private final ICpcUnitService cpcUnitService;
    private final AdpCpcOcpxService ocpxService;
    private final AdpCpcResourceService slotGroupService;
    private final IResTargetItemService resTargetItemService;
    private final AdpCpcGameService adpCpcGameService;
    private final AdpCpcLauUnitGameService adpCpcLauUnitGameService;
    private final IOcpxAutoPayTimeService ocpxAutoPayTimeService;
    private final IOcpxAutoPayStatusService ocpxAutoPayStatusService;
    private final IOcpxAutoPayService ocpxAutoPayService;
    private final ITemplateGroupService templateGroupService;
    private final ICpcCreativeService cpcCreativeService;
    private final ISoaLandingPageService soaLandingPageService;
    private final ObjectMapper om;
    private final IResSlotGroupService resSlotGroupService;
    private final ISystemConfigService systemConfigService;
    private final IQueryAccountService queryAccountService;
    private final AccountConfig accountConfig;
    private final LaunchCampaignService launchCampaignService;
    private final LaunchUnitV1Service launchUnitV1Service;
    private final LaunchUnitTargetRuleService launchUnitTargetRuleService;
    private final LaunchUnitArchiveService launchUnitArchiveService;
    private final LaunchCreativeService launchCreativeService;
    private final LaunchCreativePreviewService launchCreativePreviewService;
    private final LaunchProgrammaticCreativeDetailService launchProgrammaticCreativeDetailService;
    private final SplashScreenCreativeImageService splashScreenCreativeImageService;
    private final AdpCpcUnitService unitService;
    private final CpcCampaignService LaunchCpcCampaignService;

    @Autowired
    private UnitAccelerateService unitAccelerateService;

    @Value("#{'${cpc.ocpx.auto.pay.black.unit.ids:}'.empty ? null : '${cpc.ocpx.auto.pay.black.unit.ids:}'.split(',')}")
    private List<Integer> ocpxAutoPayBlackUnitIds;
    @Autowired
    private ArchiveService archiveService;
    @Autowired
    private CpcUnitServiceDelegate cpcUnitServiceDelegate;
    @Autowired
    private BaseQueryFactory cpcClickhouseBqf;
    @Autowired
    private LaunchUnitAssistSearchService launchUnitAssistSearchService;
    @Autowired
    private AccountWalletService accountWalletService;

    private final LaunchCommonV1Service launchCommonV1Service;

    private static String getTimestampString(Timestamp timestamp) {
        return LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp.getTime()), ZoneId.systemDefault()).format(DATE_TIME_FORMATTER);
    }

    public Long getStartTime(Long fromTime) {
        return Objects.isNull(fromTime) ? LocalDateTime.of(LocalDate.now(), LocalTime.MIN).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli() : fromTime;
    }

    public Long genEndTime(Long endTime) {
        return Objects.isNull(endTime) ? LocalDateTime.of(LocalDate.now(), LocalTime.MAX).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli() : endTime;
    }

    private static LocalDateTime toLocalDateTime(long timestamp) {
        Instant instant = Instant.ofEpochMilli(timestamp);
        return LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
    }

    private static String getDate(StatReq.TimeType timeType, Long startTime, Long endTime, Long dateTime) {
        if (timeType == StatReq.TimeType.ALL) {
            LocalDateTime start = toLocalDateTime(startTime);
            LocalDateTime end = toLocalDateTime(endTime);
            return start.toLocalDate().format(DATE_FORMATTER) + "~" + end.toLocalDate().format(DATE_FORMATTER);
        } else {
            return toLocalDateTime(dateTime).toLocalDate().format(DATE_FORMATTER);
        }
    }

    private static String getDate(StatReq.TimeType timeType, Long startTime, Long endTime, LocalDateTime ldt) {
        if (timeType == StatReq.TimeType.ALL) {
            LocalDateTime start = toLocalDateTime(startTime);
            LocalDateTime end = toLocalDateTime(endTime);
            return start.toLocalDate().format(DATE_FORMATTER) + "~" + end.toLocalDate().format(DATE_FORMATTER);
        } else {
            return ldt.toLocalDate().format(DATE_FORMATTER);
        }
    }

    private static String getTime(StatReq.TimeType timeType, Long dateTime) {
        if (timeType == StatReq.TimeType.ALL) {
            return StringUtils.EMPTY;
        } else {
            return toLocalDateTime(dateTime).toLocalTime().format(TIME_FORMATTER);
        }
    }

    private static String getTime(StatReq.TimeType timeType, LocalDateTime ldt) {
        if (timeType == StatReq.TimeType.ALL) {
            return StringUtils.EMPTY;
        } else {
            return ldt.toLocalTime().format(TIME_FORMATTER);
        }
    }

    public StatsReply listStats(Long startTime, Long endTime, StatReq.Type type, StatReq.TimeType timeType, List<Integer> accountIds, List<Integer> campaignIds, List<Integer> unitIds, List<Integer> creativeIds, StatReq.Order order, StatReq.Sort sort, Integer pn, Integer ps) {
        final StatReq req = StatReq.newBuilder()
                .setStartTime(startTime)
                .setEndTime(endTime)
                .addAllOcpxSalesTypes(OCPX_SALES_TYPES)
                .addAllSalesTypes(PLATFORM_SALES_TYPES)
                .setType(type)
                .setTimeType(timeType)
                .addAllAccountIds(accountIds)
                .addAllCampaignIds(campaignIds)
                .addAllUnitIds(unitIds)
                .addAllCreativeIds(creativeIds)
                .setOrder(order)
                .setSort(sort)
                .setPn(pn)
                .setPs(ps)
                .build();
        switch (type) {
            case ACCOUNT:
                return cpmReportSvrProxy.listAccountStats(req);
            case CAMPAIGN:
                return cpmReportSvrProxy.listCampaignStats(req);
            case UNIT:
                return cpmReportSvrProxy.listUnitStats(req);
            case CREATIVE:
                return cpmReportSvrProxy.listCreativeStats(req);
            default:
                return StatsReply.getDefaultInstance();
        }
    }

    public StatChartBo getStatChart(Long startTime, Long endTime, Integer accountId, List<Integer> campaignIdList,
                                    List<Integer> unitIdList, List<Integer> creativeIdList) {
        // 账号id每次都传 下面统一按账号聚合 聚合需要使用
        List<Integer> accountIds = Collections.singletonList(accountId);
        List<Integer> campaignIds = Collections.emptyList();
        List<Integer> unitIds = Collections.emptyList();
        List<Integer> creativeIds = Collections.emptyList();
        if (CollectionUtils.isEmpty(campaignIdList)) {
            // 走账号id查询
        } else if (CollectionUtils.isEmpty(unitIdList)) {
            campaignIds = campaignIdList;
        } else if (CollectionUtils.isEmpty(creativeIdList)) {
            unitIds = unitIdList;
        } else {
            creativeIds = creativeIdList;
        }
        final long days = Duration.between(toLocalDateTime(startTime), toLocalDateTime(endTime)).toDays();
        final StatReq.TimeType timeType;
        if (days >= 1) {
            timeType = StatReq.TimeType.DAY;
        } else {
            timeType = StatReq.TimeType.HOUR;
        }
        final StatReq req = StatReq.newBuilder()
                .setStartTime(startTime)
                .setEndTime(endTime)
                .addAllOcpxSalesTypes(OCPX_SALES_TYPES)
                .addAllSalesTypes(PLATFORM_SALES_TYPES)
                .setType(StatReq.Type.ACCOUNT)
                .setTimeType(timeType)
                .addAllAccountIds(accountIds)
                .addAllCampaignIds(campaignIds)
                .addAllUnitIds(unitIds)
                .addAllCreativeIds(creativeIds)
                .build();
        StatChartReply statChartReply = cpmReportSvrProxy.getStatChart(req);
        return StatConverter.MAPPER.grpcBo2Bo(statChartReply.getStatChart());
    }

    public PageResult<StatBo> listDetailStats(Long startTime, Long endTime, Integer typeValue, Integer timeTypeValue, Integer accountId, List<Integer> campaignIdList, List<Integer> unitIdList, List<Integer> creativeIdList, String orderValue, Integer sortValue, Integer pn, Integer ps) {
        List<Integer> accountIds = Stream.of(accountId).filter(Objects::nonNull).collect(Collectors.toList());
        List<Integer> campaignIds = CollectionUtils.isEmpty(campaignIdList) ? Collections.emptyList() : campaignIdList;
        List<Integer> unitIds = CollectionUtils.isEmpty(unitIdList) ? Collections.emptyList() : unitIdList;
        List<Integer> creativeIds = CollectionUtils.isEmpty(creativeIdList) ? Collections.emptyList() : creativeIdList;
        final StatReq.Type type = StatReq.Type.forNumber(typeValue);
        final StatReq.TimeType timeType = StatReq.TimeType.forNumber(timeTypeValue);
        final StatReq.Order order = StringUtils.isEmpty(orderValue) ? StatReq.Order.DATE_TIME : OrderEnum.getOrder(orderValue);
        final StatReq.Sort sort = StatReq.Sort.forNumber(sortValue);
        final StatsReply statsReply = listStats(startTime, endTime, type, timeType, accountIds, campaignIds, unitIds, creativeIds, order, sort, pn, ps);
        final List<Stat> stats = statsReply.getStatsList();
        final List<StatBo> statBos = stats
                .stream()
                .map(StatConverter.MAPPER::grpcBo2Bo)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(stats)) {
            return new PageResult<>(0, Collections.emptyList());
        } else {
            return new PageResult<>(statsReply.getTotal(), statBos);
        }
    }

    public PageResult<ReportDetailBo> listDetailStats(Long startTime, Long endTime, Integer typeValue, Integer timeTypeValue, Integer accountId, long total, List<StatBo> stats) {
        final AccountDto account = queryAccountService.getAccount(accountId);
        final boolean isGoodsAccount = Objects.equals(account.getDepartmentId(), accountConfig.getGoodsDepartmentId());
        List<Integer> campaignIds;
        List<Integer> unitIds;
        List<Integer> creativeIds;
        final StatReq.Type type = StatReq.Type.forNumber(typeValue);
        final StatReq.TimeType timeType = StatReq.TimeType.forNumber(timeTypeValue);
        if (CollectionUtils.isEmpty(stats)) {
            return new PageResult<>((int) total, Collections.emptyList());
        }
        List<LauCampaignPo> campaigns;
        Map<Integer, LauCampaignPo> campaignMap;
        List<LauUnitPo> units;
        Map<Integer, LauUnitPo> unitMap;
        List<Integer> slotGroupIds;
        Map<Integer, String> slotGroupIdNameMap;
        List<LauUnitCreativePo> creatives;
        switch (Objects.requireNonNull(type)) {
            case CAMPAIGN:
                campaignIds = stats
                        .stream()
                        .map(StatBo::getCampaignId)
                        .filter(Utils::isPositive)
                        .collect(Collectors.toList());
                campaigns = launchCampaignService.listCampaigns(campaignIds);
                campaignMap = campaigns
                        .stream()
                        .collect(Collectors.toMap(LauCampaignPo::getCampaignId, Function.identity(), (c1, c2) -> c1));
                final List<ReportDetailBo> campaignReportDetails = stats
                        .stream()
                        .map(stat -> {
                            LauCampaignPo campaign = campaignMap.get(stat.getCampaignId());
                            ReportDetailBo reportDetail = ReportDetailBo.builder()
                                    .date(getDate(timeType, startTime, endTime, stat.getDateTime()))
                                    .time(getTime(timeType, stat.getDateTime()))
                                    .stat(stat)
                                    .build();
                            fillCampaignInfo(reportDetail, campaign, isGoodsAccount);
                            return reportDetail;
                        }).collect(Collectors.toList());
                return new PageResult<>((int) total, campaignReportDetails);
            case UNIT:
                unitIds = stats
                        .stream()
                        .map(StatBo::getUnitId)
                        .filter(Utils::isPositive)
                        .collect(Collectors.toList());
                units = launchUnitV1Service.listUnits(unitIds);
                slotGroupIds = units.stream().map(LauUnitPo::getSlotGroup).collect(Collectors.toList());
                slotGroupIdNameMap = resSlotGroupService.getSlotGroupByIds(slotGroupIds)
                        .stream()
                        .collect(Collectors.toMap(SlotGroupSimpleDto::getId, SlotGroupSimpleDto::getSlotGroupName));
                final List<Integer> campaignIdsFromUnits = units
                        .stream()
                        .map(LauUnitPo::getCampaignId)
                        .collect(Collectors.toList());
                campaigns = launchCampaignService.listCampaigns(campaignIdsFromUnits);
                campaignMap = campaigns
                        .stream()
                        .collect(Collectors.toMap(LauCampaignPo::getCampaignId, Function.identity(), (c1, c2) -> c1));
                unitMap = units
                        .stream()
                        .collect(Collectors.toMap(LauUnitPo::getUnitId, Function.identity(), (u1, u2) -> u1));
                final List<ReportDetailBo> unitReportDetails = stats
                        .stream()
                        .map(stat -> {
                            LauUnitPo unit = unitMap.get(stat.getUnitId());
                            LauCampaignPo campaign = campaignMap.get(unit.getCampaignId());
                            ReportDetailBo reportDetail = ReportDetailBo.builder()
                                    .date(getDate(timeType, startTime, endTime, stat.getDateTime()))
                                    .time(getTime(timeType, stat.getDateTime()))
                                    .stat(stat)
                                    .build();
                            fillUnitInfo(reportDetail, unit, slotGroupIdNameMap);
                            fillCampaignInfo(reportDetail, campaign, isGoodsAccount);
                            return reportDetail;
                        })
                        .collect(Collectors.toList());
                return new PageResult<>((int) total, unitReportDetails);
            case CREATIVE:
                creativeIds = stats
                        .stream()
                        .map(StatBo::getCreativeId)
                        .filter(Utils::isPositive)
                        .collect(Collectors.toList());
                creatives = launchCreativeService.listCreatives(creativeIds);
                final List<Integer> unitIdsFromCreatives = creatives
                        .stream()
                        .map(LauUnitCreativePo::getUnitId)
                        .distinct()
                        .collect(Collectors.toList());
                units = launchUnitV1Service.listUnits(unitIdsFromCreatives);
                slotGroupIds = units.stream().map(LauUnitPo::getSlotGroup).collect(Collectors.toList());
                slotGroupIdNameMap = resSlotGroupService.getSlotGroupByIds(slotGroupIds)
                        .stream()
                        .collect(Collectors.toMap(SlotGroupSimpleDto::getId, SlotGroupSimpleDto::getSlotGroupName));
                final List<Integer> campaignIdsFromCreatives = creatives
                        .stream()
                        .map(LauUnitCreativePo::getCampaignId)
                        .distinct()
                        .collect(Collectors.toList());
                campaigns = launchCampaignService.listCampaigns(campaignIdsFromCreatives);
                campaignMap = campaigns
                        .stream()
                        .collect(Collectors.toMap(LauCampaignPo::getCampaignId, Function.identity(), (c1, c2) -> c1));
                unitMap = units
                        .stream()
                        .collect(Collectors.toMap(LauUnitPo::getUnitId, Function.identity(), (u1, u2) -> u1));
                final Map<Integer, LauUnitCreativePo> creativeMap = creatives
                        .stream()
                        .collect(Collectors.toMap(LauUnitCreativePo::getCreativeId, Function.identity(), (c1, c2) -> c1));
                final List<ReportDetailBo> creativeReportDetails = stats
                        .stream()
                        .map(stat -> {
                            LauUnitCreativePo creative = creativeMap.get(stat.getCreativeId());
                            LauUnitPo unit = unitMap.get(creative.getUnitId());
                            LauCampaignPo campaign = campaignMap.get(creative.getCampaignId());
                            ReportDetailBo reportDetail = ReportDetailBo.builder()
                                    .date(getDate(timeType, startTime, endTime, stat.getDateTime()))
                                    .time(getTime(timeType, stat.getDateTime()))
                                    .stat(stat)
                                    .build();
                            fillCreativeInfo(reportDetail, creative);
                            fillUnitInfo(reportDetail, unit, slotGroupIdNameMap);
                            fillCampaignInfo(reportDetail, campaign, isGoodsAccount);
                            return reportDetail;
                        })
                        .collect(Collectors.toList());
                return new PageResult<>((int) total, creativeReportDetails);
            default:
                final List<ReportDetailBo> reportDetails = stats
                        .stream()
                        .map(stat -> ReportDetailBo.builder()
                                .date(getDate(timeType, startTime, endTime, stat.getDateTime()))
                                .time(getTime(timeType, stat.getDateTime()))
                                .stat(stat)
                                .build())
                        .collect(Collectors.toList());
                return new PageResult<>((int) total, reportDetails);
        }
    }

    private void fillCampaignInfo(ReportDetailBo reportDetail, LauCampaignPo campaign, boolean isGoodsAccount) {
        reportDetail.setCampaignId(Optional.ofNullable(campaign).map(LauCampaignPo::getCampaignId).orElse(0));
        reportDetail.setCampaignName(Optional.ofNullable(campaign).map(LauCampaignPo::getCampaignName).orElse(StringUtils.EMPTY));
        reportDetail.setPromotionPurposeType(Optional.ofNullable(campaign).map(LauCampaignPo::getPromotionPurposeType).map(PromotionPurposeType::getByCode).map(PromotionPurposeType::getDesc).orElse(StringUtils.EMPTY));
        // 带货的需求 替换文案 其他都不变
        // https://www.tapd.bilibili.co/********/prong/stories/view/11********002705475
        if (Objects.nonNull(campaign)
                && Objects.equals(campaign.getPromotionPurposeType(), PromotionPurposeType.BRAND_SPREAD.getCode())
                && isGoodsAccount) {
            reportDetail.setPromotionPurposeType("我要带货");
        }
        reportDetail.setCampaignAdType(Optional.ofNullable(campaign).map(LauCampaignPo::getAdType).map(CampaignAdType::getByCode).map(CampaignAdType::getDesc).orElse(StringUtils.EMPTY));
    }

    private void fillUnitInfo(ReportDetailBo reportDetail, LauUnitPo unit, Map<Integer, String> slotGroupIdNameMap) {
        reportDetail.setUnitId(Optional.ofNullable(unit).map(LauUnitPo::getUnitId).orElse(0));
        reportDetail.setUnitName(Optional.ofNullable(unit).map(LauUnitPo::getUnitName).orElse(StringUtils.EMPTY));
        reportDetail.setSlotGroupName(Optional.ofNullable(unit).map(LauUnitPo::getSlotGroup).map(slotGroupIdNameMap::get).orElse(StringUtils.EMPTY));
        reportDetail.setOcpxStateDesc(Optional.ofNullable(unit).map(LauUnitPo::getOcpxTargetTwo).map(OcpcTargetEnum::getByCode).map(OcpcTargetEnum::getDesc).orElse(StringUtils.EMPTY));
        reportDetail.setOcpcTargetDesc(Optional.ofNullable(unit).map(LauUnitPo::getOcpcTarget).map(OcpcTargetEnum::getByCode).map(OcpcTargetEnum::getDesc).orElse(StringUtils.EMPTY));
        reportDetail.setOfferTypeDesc(Optional.ofNullable(unit).map(LauUnitPo::getSalesType).map(SalesType::getByCode).map(SalesType::getDesc).orElse(StringUtils.EMPTY));
    }

    private void fillCreativeInfo(ReportDetailBo reportDetail, LauUnitCreativePo creative) {
        reportDetail.setCreativeId(Optional.ofNullable(creative).map(LauUnitCreativePo::getCreativeId).orElse(0));
        reportDetail.setCreativeName(Optional.ofNullable(creative).map(LauUnitCreativePo::getCreativeName).orElse(StringUtils.EMPTY));
        reportDetail.setAdvertisingModeDesc(Optional.ofNullable(creative).map(LauUnitCreativePo::getAdvertisingMode).map(AdvertisingMode::getByKey).map(AdvertisingMode::getValue).orElse(StringUtils.EMPTY));
    }

    //补充稿件和up主相关信息
    private void fillVideoAndMidInfo(ReportDetailBo reportDetail, LauUnitArchiveVideoPo lauUnitArchiveVideoPo, Map<Long, Arc> unitMapArc) {
        reportDetail.setBvId(Optional.ofNullable(lauUnitArchiveVideoPo).map(LauUnitArchiveVideoPo::getVideoId).map(unitMapArc::get).map(Arc::getAid).map(BVIDUtils::avToBv).orElse(StringUtils.EMPTY));
        reportDetail.setAvName(Optional.ofNullable(lauUnitArchiveVideoPo).map(LauUnitArchiveVideoPo::getVideoId).map(unitMapArc::get).map(Arc::getTitle).orElse(StringUtils.EMPTY));
        reportDetail.setUpName(Optional.ofNullable(lauUnitArchiveVideoPo).map(LauUnitArchiveVideoPo::getVideoId).map(unitMapArc::get).map(Arc::getAuthor).map(com.bapis.archive.service.Author::getName).orElse(StringUtils.EMPTY));
    }

    public List<StatBo> listCampaignStats(Integer accountId, List<Integer> queryCampaignIds, Long startTime, Long endTime, String orderValue, Integer sortValue, Integer pn, Integer ps) {
        final List<Integer> accountIds = Collections.singletonList(accountId);
        final Long queryStartTime = getStartTime(startTime);
        final Long queryEndTime = genEndTime(endTime);
        final StatReq.Order order = StringUtils.isEmpty(orderValue) ? StatReq.Order.CAMPAIGN_ID : OrderEnum.getOrder(orderValue);
        final StatReq.Sort sort = StatReq.Sort.forNumber(sortValue);
        final StatsReply campaignStatReply = listStats(queryStartTime, queryEndTime, StatReq.Type.CAMPAIGN, StatReq.TimeType.ALL, accountIds, queryCampaignIds, Collections.emptyList(), Collections.emptyList(), order, sort, pn, ps);
        final List<Stat> campaignStats = campaignStatReply.getStatsList();
        if (CollectionUtils.isEmpty(campaignStats)) {
            // 兼容queryCampaignIds不为空但分页后结果为空
            return Collections.emptyList();
        } else {
            return campaignStats
                    .stream()
                    .map(StatConverter.MAPPER::grpcBo2Bo)
                    .collect(Collectors.toList());
        }
    }


    public List<CampaignReportBo> listCampaignStats(Integer accountId, List<CpcCampaignDto> campaigns, Long startTime, Long endTime, List<StatBo> campaignStats) {
        final Map<Integer, CpcCampaignDto> campaignMap = campaigns
                .stream()
                .collect(Collectors.toMap(CpcCampaignDto::getCampaignId, Function.identity()));
        if (CollectionUtils.isEmpty(campaignStats)) {
            // 兼容queryCampaignIds不为空但分页后结果为空
            return Collections.emptyList();
        }

        //这里的campaign id list是分页且排序过的，后续操作都是依据此campaign id list
        final List<Integer> campaignIdsAfterSorted = campaignStats
                .stream()
                .map(StatBo::getCampaignId)
                .collect(Collectors.toList());
        final Map<Integer, StatBo> campaignStatMap = campaignStats
                .stream()
                .collect(Collectors.toMap(StatBo::getCampaignId, Function.identity(), (c1, c2) -> c1));
        //这里开始的逻辑都是 com.bilibili.adp.advertiser.portal.service.stat.hystrixCampaignService.getCpcCampaignReportVosFromES 搬运而来,如有问题可以对比调整
        final boolean notEnoughMoney = accountWalletService.isNotEnoughMoney(accountId);
        final Map<Integer, List<NewLauCampaignNextdayBudgetDto>> campaignNextdayBudgetMap = lauBudgetService.getCampaignNextdayBudgetMapInIds(campaignIdsAfterSorted);
        return campaignIdsAfterSorted
                .stream()
                .map(campaignId -> {
                    final CpcCampaignDto campaign = campaignMap.get(campaignId);
                    final StatBo stat = campaignStatMap.get(campaignId);
                    final CampaignExtraStatus extraStatus = LaunchCampaignService.getExtraStatus(campaign.getCampaignStatus(), 1L, 1L, notEnoughMoney);
                    final List<NewLauCampaignNextdayBudgetDto> campaignNextdayBudgetDtoList = campaignNextdayBudgetMap.getOrDefault(campaign.getCampaignId(), Collections.emptyList());
                    final NewLauCampaignNextdayBudgetDto campaignNextdayBudget = campaignNextdayBudgetDtoList
                            .stream()
                            .findFirst()
                            .orElse(NewLauCampaignNextdayBudgetDto
                                    .builder()
                                    .nextdayBudget(0L)
                                    .nextdayBudgetLimitType(BudgetLimitType.DESIGNATE.getCode())
                                    .isRepeat(0)
                                    .build()
                            );
                    final BigDecimal budget = IsValid.TRUE.getCode().equals(campaign.getIsGdPlus()) ? null : Utils.fromFenToYuan(campaign.getBudget());
                    return CampaignReportBo.builder()
                            .campaignId(campaign.getCampaignId())
                            .budget(budget)
                            .budgetLimitType(campaign.getBudgetLimitType())
                            .hasNextdayBudget(CollectionUtils.isEmpty(campaignNextdayBudgetDtoList) ? 0 : 1)
                            .nextdayBudget(Utils.fromFenToYuan(campaignNextdayBudget.getNextdayBudget()))
                            .nextdayBudgetLimitType(campaignNextdayBudget.getNextdayBudgetLimitType())
                            .isRepeat(campaignNextdayBudget.getIsRepeat())
                            .campaignName(campaign.getCampaignName())
                            .promotionPurposeType(campaign.getPromotionPurposeType())
                            .promotionPurposeTypeDesc(PromotionPurposeType.getByCode(campaign.getPromotionPurposeType()).getDesc())
                            .isManaged(Objects.equals(1, campaign.getIsManaged()))
                            .speedMode(campaign.getSpeedMode())
                            .budgetRemainingModifyTimes(LaunchConstant.CAMPAIGN_BUDGET_UPDATE_LIMIT)
                            .beforeBudget(budget)
                            .status(campaign.getCampaignStatus())
                            .statusDesc(CampaignStatus.getByCode(campaign.getCampaignStatus()).getDesc())
                            .campaignStatusMtime(getTimestampString(campaign.getCampaignStatusMtime()))
                            .extraStatus(Collections.singletonList(extraStatus.getCode()))
                            .extraStatusDesc(Collections.singletonList(extraStatus.getDesc()))
                            .adpVersion(campaign.getAdpVersion())
                            .adpVersionDesc(AdpVersion.getSanlianAdpVersionDesc(campaign.getAdpVersion()))
                            .createTime(getTimestampString(campaign.getCtime()))
                            .campaignAdType(CampaignAdType.getByCode(campaign.getAdType()).getDesc())
                            .supportAuto(campaign.getSupportAuto())
                            .supportAutoDesc(SupportAuto.of(campaign.getSupportAuto()).getDesc())
                            .stat(stat)
                            .build();
                }).collect(Collectors.toList());
    }

    public List<StatBo> listUnitStats(Integer accountId, List<Integer> queryUnitIds, Long startTime, Long endTime, String orderValue, Integer sortValue, Integer pn, Integer ps) {
        final List<Integer> accountIds = Collections.singletonList(accountId);
        final Long queryStartTime = getStartTime(startTime);
        final Long queryEndTime = genEndTime(endTime);
        final StatReq.Order order = StringUtils.isEmpty(orderValue) ? StatReq.Order.UNIT_ID : OrderEnum.getOrder(orderValue);
        final StatReq.Sort sort = StatReq.Sort.forNumber(sortValue);
        final StatsReply unitStatReply = listStats(queryStartTime, queryEndTime, StatReq.Type.UNIT, StatReq.TimeType.ALL, accountIds, Collections.emptyList(), queryUnitIds, Collections.emptyList(), order, sort, pn, ps);
        final List<Stat> unitStats = unitStatReply.getStatsList();
        log.info("unit stats, {}", unitStats);
        if (CollectionUtils.isEmpty(unitStats)) {
            // 兼容queryUnitIds不为空但分页后结果为空
            return Collections.emptyList();
        } else {
            return unitStats
                    .stream()
                    .map(StatConverter.MAPPER::grpcBo2Bo)
                    .collect(Collectors.toList());
        }
    }

    public List<UnitReportBo> listUnitStats(Integer accountId, List<CpcUnitDto> units, List<StatBo> unitStats) {
        final List<Integer> accountIds = Collections.singletonList(accountId);
        final Map<Integer, CpcUnitDto> unitMap = units
                .stream()
                .collect(Collectors.toMap(CpcUnitDto::getUnitId, Function.identity()));
        log.info("unit stats, {}", unitStats);
        if (CollectionUtils.isEmpty(unitStats)) {
            // 兼容queryUnitIds不为空但分页后结果为空
            return Collections.emptyList();
        }

        //这里的unit id list是分页且排序过的，后续操作都是依据此unit id list
        final List<Integer> unitIdsAfterSorted = unitStats
                .stream()
                .map(StatBo::getUnitId)
                .collect(Collectors.toList());
        log.info("unit ids after sorted :{}", unitIdsAfterSorted);
        final List<CpcUnitDto> unitsAfterSliced = units
                .stream()
                .filter(unit -> unitIdsAfterSorted.contains(unit.getUnitId()))
                .collect(Collectors.toList());

        final Map<Integer, StatBo> unitStatMap = unitStats
                .stream()
                .collect(Collectors.toMap(StatBo::getUnitId, Function.identity(), (c1, c2) -> c1));
        //这里开始的逻辑都是
        // com.bilibili.adp.advertiser.portal.service.stat.hystrixUnitService.getCpcUnitReportVosFromESV2
        // com.bilibili.adp.advertiser.helper.cpc.CpcHelper.convertUnitReportDto2VoV2
        // 搬运而来,如有问题可以对比调整
        final List<Integer> campaignIds = unitsAfterSliced.stream().map(CpcUnitDto::getCampaignId).distinct().collect(Collectors.toList());
        final Map<Integer, LauCampaignPo> campaignMap = launchCampaignService.listCampaigns(campaignIds).stream().collect(Collectors.toMap(LauCampaignPo::getCampaignId, Function.identity()));
        //final Map<Integer, Long> unitIdVideoIdMap = launchUnitArchiveService.getUnitIdVideoIdMap(unitIdsAfterSorted);
        final Map<Integer, Integer> unitIdTargetPackageIdMap = unitsAfterSliced
                .stream()
                .collect(Collectors.toMap(CpcUnitDto::getUnitId, CpcUnitDto::getTargetPackageId));
        final Map<Integer, List<Integer>> unitIdOsMap = launchUnitTargetRuleService.getUnitIdParsedOsMap(unitIdTargetPackageIdMap);
        final boolean notEnoughMoney = accountWalletService.isNotEnoughMoney(accountId);
        final Map<Integer, Integer> ocpmStage2UnitMap = launchCommonV1Service.ocpxStage2MapByType(unitIdsAfterSorted, Constants.OCPX_CONFIG_TYPE_UNIT, Constants.OCPM_CONFIG_EXP_KEY);
        final Map<Integer, Integer> ocpmStage2AccountMap = launchCommonV1Service.ocpxStage2MapByType(accountIds, Constants.OCPX_CONFIG_TYPE_ACCOUNT, Constants.OCPM_CONFIG_EXP_KEY);

        final Map<Integer, Integer> ocpcStage2UnitMap = launchCommonV1Service.ocpxStage2MapByType(unitIdsAfterSorted, Constants.OCPX_CONFIG_TYPE_UNIT, Constants.OCPX_CONFIG_EXP_KEY);
        final Map<Integer, Integer> ocpcStage2AccountMap = launchCommonV1Service.ocpxStage2MapByType(accountIds, Constants.OCPX_CONFIG_TYPE_ACCOUNT, Constants.OCPX_CONFIG_EXP_KEY);

        final Map<Integer, Set<Integer>> ocpcConfigAccountMap = ocpxService.getOcpxConfigAccountMap(accountIds).values().stream().findFirst().orElse(Collections.emptyMap());
        final Map<Integer, Map<Integer, Set<Integer>>> ocpcConfigUnitMap = ocpxService.getOcpxConfigUnitMap(unitIdsAfterSorted);
        final Map<Integer, List<Integer>> unitScenesMap = slotGroupService.getUnitScenesMap(unitIdsAfterSorted);

        final Map<Integer, String> osMap = resTargetItemService.getItemByTargetType(TargetType.OS.getCode())
                .stream()
                .collect(Collectors.toMap(ResTargetItemDto::getId, ResTargetItemDto::getName));

        final Map<Integer, List<NewLauUnitNextdayBudgetDto>> unitNextdayBudgetMap = lauBudgetService.getUnitNextdayBudgetMapInIds(unitIdsAfterSorted);
        final Map<Integer, LauOcpxConfigBo> ocpxConfigBoMap = ocpxService.getOcpxTargetMap(null, null, null);
        final Map<Integer, LauUnitGameBo> unit2Game = adpCpcLauUnitGameService.getSubPkgMap(unitIdsAfterSorted);
        final List<Integer> gameBaseIds = unit2Game.values()
                .stream()
                .map(LauUnitGameBo::getGameBaseId)
                .distinct()
                .collect(Collectors.toList());
        final Map<Integer, Integer> game2SubPkgStatus = adpCpcGameService.fetchSubPkgAvailableGameStatusMap(gameBaseIds);
        // todo
        final List<OcpxAutoPayTimeDto> autoPayTimeDtos = ocpxAutoPayTimeService.findAutoPayTimeDtosByUnitIds(unitIdsAfterSorted);
        final Map<Integer, OcpxAutoPayTimeDto> ocpxAutoPayTimeDtoMap = autoPayTimeDtos.stream()
                .collect(Collectors.toMap(OcpxAutoPayTimeDto::getUnitId, Function.identity()));
        final Map<Integer, Date> ocpxAutoPayTimeMap = autoPayTimeDtos.stream()
                .collect(Collectors.toMap(OcpxAutoPayTimeDto::getUnitId, OcpxAutoPayTimeDto::getTwoStageTime));
        final Map<Integer, Map<Integer, OcpxAutoPayStatusDto>> ocpxAutoPayStatusDtoMap = ocpxAutoPayStatusService.findAutoPayStatusByUnitIdList(unitIdsAfterSorted)
                .stream()
                .collect(Collectors.groupingBy(OcpxAutoPayStatusDto::getUnitId, Collectors.toMap(OcpxAutoPayStatusDto::getPeriod, Function.identity())));
        final Map<Integer, Integer> unitPeriodTypeMap = autoPayTimeDtos.stream()
                .collect(Collectors.toMap(OcpxAutoPayTimeDto::getUnitId, OcpxAutoPayTimeDto::getUnitPeriodType));
        final Map<Integer, Pair<Integer, String>> unitIdAssistSearchMap = launchUnitAssistSearchService.getUnitAssistSearchStatusAndTime(unitIdsAfterSorted);
        final Map<Integer, Boolean> unitIdCanCopyMap = launchUnitV1Service.unitsCanCopy(unitIdsAfterSorted, accountId);
        return unitIdsAfterSorted
                .stream()
                .map(unitId -> {
                    final CpcUnitDto unit = unitMap.get(unitId);
                    final LauCampaignPo campaign = campaignMap.get(unit.getCampaignId());
                    final StatBo stat = unitStatMap.get(unitId);
                    final int adpVersion = unit.getAdpVersion();
                    final UnitExtraStatus extraStatus = LaunchUnitV1Service.getExtraStatus(campaign, unit, notEnoughMoney, 1L);
                    final List<NewLauUnitNextdayBudgetDto> unitNextdayBudgetDtoList = unitNextdayBudgetMap.getOrDefault(unit.getUnitId(), Collections.emptyList());
                    final Integer hasNextDayBudget = CollectionUtils.isEmpty(unitNextdayBudgetDtoList) ? 0 : 1;
                    final NewLauUnitNextdayBudgetDto unitNextDayBudgetDto = unitNextdayBudgetDtoList
                            .stream()
                            .findFirst()
                            .orElse(NewLauUnitNextdayBudgetDto
                                    .builder()
                                    .nextdayBudget(0L)
                                    .isRepeat(0)
                                    .build()
                            );
                    //final Long avId = unitIdVideoIdMap.getOrDefault(unit.getUnitId(), 0L);
                    final List<Integer> os = unitIdOsMap.getOrDefault(unit.getUnitId(), Collections.emptyList());
                    final String osDesc = os
                            .stream()
                            .map(id -> osMap.getOrDefault(id, StringUtils.EMPTY))
                            .collect(Collectors.joining(","));
                    final Pair<Integer, String> unitIdAssistSearchPair = unitIdAssistSearchMap.get(unitId);
                    Integer salesType = AdpVersion.isCpcFlyMerge(unit.getAdpVersion()) && Utils.isPositive(unit.getOcpcTarget()) ?
                            SalesType.CPM.getCode() : unit.getSalesType();
                    final SalesMode salesMode = getSalesMode(salesType, unit.getOcpcTarget(), unit.getIsNoBid());
                    final BigDecimal budget = Utils.fromFenToYuan(unit.getBudget());

                    UnitReportBo unitReport = UnitReportBo.builder()
                            .accountId(unit.getAccountId())
                            .promotionPurposeType(unit.getPromotionPurposeType())
                            .ctime(unit.getCtime())
                            .ocpcTarget(unit.getOcpcTarget())
                            //.videoId(avId)
                            .slotGroupId(unit.getSlotGroup())
                            .beforeBudget(budget)
                            .campaignId(campaign.getCampaignId())
                            .campaignName(campaign.getCampaignName())
                            .unitId(unit.getUnitId())
                            .unitName(unit.getUnitName())
                            .budget(budget)
                            .hasNextdayBudget(hasNextDayBudget)
                            .nextdayBudget(Utils.fromFenToYuan(unitNextDayBudgetDto.getNextdayBudget()))
                            .isRepeat(unitNextDayBudgetDto.getIsRepeat())
                            .costPrice(Utils.fromFenToYuan(unit.getCostPrice()))
                            .beginDate(unit.getLaunchBeginDate())
                            .endDate(unit.getLaunchEndDate())
                            .statusDesc(UnitStatus.getByCode(unit.getUnitStatus()).getDesc())
                            .status(unit.getUnitStatus())
                            .unitStatusMtime(StringDateParser.getDateTimeString(unit.getUnitStatusMtime()))
                            .budgetRemainingModifyTimes(LaunchConstant.UNIT_BUDGET_UPDATE_LIMIT)
                            .isHistory(unit.getIsHistory())
                            .subPkgStatus(launchCommonV1Service.fetchSubPkgStatus(unit2Game, game2SubPkgStatus, unit.getUnitId()))
                            .salesType(salesType)
                            .salesTypeDesc(SalesType.getByCode(salesType).getName())
                            .salesMode(salesMode.getCode())
                            .salesModeDesc(salesMode.getSalesModeDesc())
                            .extraStatus(Collections.singletonList(extraStatus.getCode()))
                            .extraStatusDesc(Collections.singletonList(extraStatus.getDesc()))
                            .twoStageBid(Utils.fromFenToYuan(unit.getTwoStageBid()))
                            .ocpxTargetDesc("")
                            .ocpxTargetOneBid(Utils.fromFenToYuan(unit.getTwoStageBid()))
                            .ocpxTargetTwo(unit.getOcpxTargetTwo())
                            .ocpxTargetTwoBid(OcpxTargetBidUtil.dto2Vo(unit.getOcpxTargetTwoBid(), unit.getOcpxTargetTwo()))
                            .osDesc(osDesc)
                            .adpVersion(adpVersion)
                            .adpVersionDesc(AdpVersion.getSanlianAdpVersionDesc(adpVersion))
                            .promotionContentTypeDesc(PromotionContentTypeEnum.getByCode(unit.getPromotionPurposeType()).getName())
                            .speedMode(unit.getSpeedMode())
                            .isManaged(Objects.equals(1, unit.getIsManaged()))
                            .createTime(TimeUtils.formatDateTime(unit.getCtime()))
                            .isProgrammatic(unit.getIsProgrammatic())
                            .unitPeriodType(UnitPeriodTypeEnum.UNKNOWN.getCode())
                            //.avId(Utils.isPositive(avId) ? String.valueOf(avId) : StringUtils.EMPTY)
                            //.bvId(Utils.isPositive(avId) ? BVIDUtils.avToBv(avId) : StringUtils.EMPTY)
                            .stat(stat)
                            .assistTarget(unit.getAssistTarget())
                            .assistTargetDesc("--")
                            // todo 希望默认显示 "--"
                            .searchFirstPriceCoefficient((unit.getSearchFirstPriceCoefficient() != null) ? Utils.fromFenToYuan(new BigDecimal(unit.getSearchFirstPriceCoefficient())).toString() : "--")
                            .searchPriceCoefficient(unit.getSearchPriceCoefficient() != null ? Utils.fromFenToYuan(new BigDecimal(unit.getSearchPriceCoefficient())).toString() : "--")
                            .assistInSearch(unitIdAssistSearchPair == null ? null : unitIdAssistSearchPair.getLeft())
                            .assistSearchEndDate(unitIdAssistSearchPair == null ? null : unitIdAssistSearchPair.getRight())
                            .canCopy(unitIdCanCopyMap.getOrDefault(unitId, true))
                            .dualBidTwoStageOptimization(unit.getDualBidTwoStageOptimization())
                            .dualBidTwoStageOptimizationDesc(DualBidTwoStageOptimization.getByCode(unit.getDualBidTwoStageOptimization()).getDesc())
                            .build();
                    final OcpxStageEnum ocpxStage;
                    final OcpxStageEnum ocpxTargetTwoStage;
                    // adp6 无脑新逻辑
                    if (AdpVersion.isCpcFlyMerge(unit.getAdpVersion())) {
                        ocpxStage = ocpxService.getOcpxStage(ocpcConfigUnitMap, ocpcConfigAccountMap, unit.getUnitId(), unit.getOcpcTarget(), unitScenesMap.get(unit.getUnitId()));
                        ocpxTargetTwoStage = ocpxService.getOcpxStage(ocpcConfigUnitMap, ocpcConfigAccountMap, unit.getUnitId(), unit.getOcpxTargetTwo(), unitScenesMap.get(unit.getUnitId()));
                    } else if (SalesType.CPM.getCode() == unit.getSalesType()) {
                        // ocpm走老逻辑
                        ocpxStage = launchCommonV1Service.getOcpxStage(ocpmStage2UnitMap, ocpmStage2AccountMap, unit);
                        ocpxTargetTwoStage = OcpxStageEnum.NON;
                    } else if (SalesType.CPC.getCode() == unit.getSalesType() && AdpVersion.isMergedInGeneral(unit.getAdpVersion())) {
                        // 新版ocpc走新逻辑
                        ocpxStage = ocpxService.getOcpxStage(ocpcConfigUnitMap, ocpcConfigAccountMap, unit.getUnitId(), unit.getOcpcTarget(), unitScenesMap.get(unit.getUnitId()));
                        ocpxTargetTwoStage = ocpxService.getOcpxStage(ocpcConfigUnitMap, ocpcConfigAccountMap, unit.getUnitId(), unit.getOcpxTargetTwo(), unitScenesMap.get(unit.getUnitId()));
                    } else if (Objects.equals(SalesType.CPC.getCode(), unit.getSalesType())) {
                        // 旧版ocpc走老逻辑
                        ocpxStage = launchCommonV1Service.getOcpxStage(ocpcStage2UnitMap, ocpcStage2AccountMap, unit);
                        ocpxTargetTwoStage = OcpxStageEnum.NON;
                    } else {
                        ocpxStage = OcpxStageEnum.NON;
                        ocpxTargetTwoStage = OcpxStageEnum.NON;
                    }

                    unitReport.setOcpxTargetTwoDesc(LaunchCommonV1Service.getOcpxTargetName(ocpxConfigBoMap, unit.getOcpxTargetTwo()));

                    // oCPM "完件"，比较特殊，有双目标的话，需要显示OcpxTargetTwoStageDesc
                    if (SalesType.CPM.getCode() == unit.getSalesType() && Objects.equals(unit.getOcpcTarget(), OcpcTargetEnum.APPLY.getCode())) {
                        if (Utils.isPositive(unit.getOcpxTargetTwo())) {
                            unitReport.setOcpxTargetTwoStageDesc(OcpcTargetEnum.getByCode(unit.getOcpxTargetTwo()).getDesc());
                        } else {
                            unitReport.setOcpxTargetTwoStageDesc("未开启");
                        }
                    } else {
                        if (Objects.equals(ocpxTargetTwoStage, OcpxStageEnum.NON)) {
                            unitReport.setOcpxTargetTwoStageDesc("未开启");
                        } else {
                            unitReport.setOcpxTargetTwoStageDesc(LaunchCommonV1Service.getOcpxTargetName(ocpxConfigBoMap, unit.getOcpxTargetTwo()));
                        }
                    }
                    if (Utils.isPositive(unit.getOcpcTarget())) {
                        unitReport.setOcpxTargetId(LaunchCommonV1Service.getOcpxTargetId(ocpxConfigBoMap, unit.getOcpcTarget()));
                        unitReport.setOcpxTargetDesc(LaunchCommonV1Service.getOcpxTargetName(ocpxConfigBoMap, unit.getOcpcTarget()));
                    }
                    if (Utils.isPositive(unit.getAssistTarget())) {
                        unitReport.setAssistTargetDesc(OcpcTargetEnum.getByCode(unit.getAssistTarget()).getDesc());
                        unitReport.setAssistPrice(Utils.fromFenToYuan(unit.getAssistPrice()));
                    }
                    unitReport.setOcpxStage(ocpxStage.getCode());
                    unitReport.setOcpxStageDesc(ocpxStage.getDesc());
                    // 增加ocpx自动赔付的状态
                    OcpxAutoPayStatus finalStatus;
                    // 单元在黑名单内
                    if (!CollectionUtils.isEmpty(ocpxAutoPayBlackUnitIds)
                            && ocpxAutoPayBlackUnitIds.contains(unit.getUnitId())) {
                        finalStatus = OcpxAutoPayStatus.UNKNOWN;
                    } else {
                        finalStatus = ocpxAutoPayService.getFinalStatus(unit.getUnitId(), unit.getOcpcTarget(), unit.getCtime(), unitPeriodTypeMap.get(unit.getUnitId()), ocpxAutoPayStatusDtoMap.get(unitId));
                        Date date = ocpxAutoPayTimeMap.get(unit.getUnitId());
                        List<Integer> flyOcpxTargetAutos = Arrays.asList(OcpcTargetEnum.VIDEO_PLAY.getCode(),
                                OcpcTargetEnum.USER_FOLLOW.getCode(),
                                OcpcTargetEnum.DYNAMIC_DETAIL_BROWSE.getCode());
                        //是否真的是"成本保障生效中"
                        if (flyOcpxTargetAutos.contains(unit.getOcpcTarget())) {
                            if (OcpxAutoPayStatus.IS_PROTECTING.equals(finalStatus)) {
                                if (date != null) {
                                    long time20220501 = Utils.getTimestamp("2022-05-01").getTime();
                                    long timeUnit = date.getTime();
                                    if (timeUnit <= time20220501) {
                                        finalStatus = OcpxAutoPayStatus.UNKNOWN;
                                    }
                                }
                            }
                        }
                    }
                    unitReport.setOcpxAutoPayFinalStatus(finalStatus.getDesc());
                    Pair<String, List<UnitOcpxFloatStatusDto>> pair = ocpxAutoPayService.getFloatStatusList(unit.getUnitId(), finalStatus, ocpxAutoPayTimeDtoMap.get(unitId), Optional.ofNullable(ocpxAutoPayStatusDtoMap.get(unitId)).map(Map::values).orElse(Collections.emptyList()));
                    unitReport.setTwoStageTime(pair.getLeft());
                    unitReport.setOcpxAutoPayFloatStatusList(pair.getValue());
                    unitReport.setUnitPeriodType(unitPeriodTypeMap.getOrDefault(unit.getUnitId(), UnitPeriodTypeEnum.UNKNOWN.getCode()));
                    unitReport.setUnderbid(false);
                    return unitReport;
                })
                .collect(Collectors.toList());
    }

    private SalesMode getSalesMode(Integer salesType, Integer ocpcTarget, Integer isNoBid) {
        SalesType type = SalesType.getByCode(salesType);
        if (Utils.isPositive(isNoBid)) {
            return SalesMode.NOBID;
        }

        if (type == SalesType.CPM && ocpcTarget == 0) {
            return SalesMode.CPM;
        }
        if (type == SalesType.CPM && ocpcTarget > 0) {
            return SalesMode.OCPM;
        }
        if (type == SalesType.CPC && ocpcTarget == 0) {
            return SalesMode.CPC;
        }
        return SalesMode.OCPC;
    }

    public List<StatBo> listCreativeStats(Integer accountId, List<Integer> creativeIdList, Long startTime, Long endTime, String orderValue, Integer sortValue, Integer pn, Integer ps) {
        final List<Integer> accountIds = Collections.singletonList(accountId);
        final StatReq.Order order = StringUtils.isEmpty(orderValue) ? StatReq.Order.CREATIVE_ID : OrderEnum.getOrder(orderValue);
        final StatReq.Sort sort = StatReq.Sort.forNumber(sortValue);
        final StatsReply creativeStatReply = listStats(getStartTime(startTime), genEndTime(endTime), StatReq.Type.CREATIVE, StatReq.TimeType.ALL, accountIds, Collections.emptyList(), Collections.emptyList(), creativeIdList, order, sort, pn, ps);
        final List<Stat> creativeStats = creativeStatReply.getStatsList();
        log.info("creative stats, {}", creativeStats);
        if (CollectionUtils.isEmpty(creativeStats)) {
            return Collections.emptyList();
        } else {
            return creativeStats
                    .stream()
                    .map(StatConverter.MAPPER::grpcBo2Bo)
                    .collect(Collectors.toList());
        }
    }

    public List<CreativeReportBo> listCreativeStats(String appkey, Integer accountId, List<CpcCreativeDto> creatives, List<StatBo> creativeStats) {
        log.info("creative stats, {}", creativeStats);
        if (CollectionUtils.isEmpty(creativeStats)) {
            // 兼容queryCreativeIds不为空但分页后结果为空
            return Collections.emptyList();
        }

        //这里的creative id list是分页且排序过的，后续操作都是依据此creative id list
        final List<Integer> creativeIdsAfterSorted = creativeStats
                .stream()
                .map(StatBo::getCreativeId)
                .collect(Collectors.toList());
        final List<Integer> unitIds = creatives
                .stream()
                .filter(creative -> creativeIdsAfterSorted.contains(creative.getCreativeId()))
                .map(CpcCreativeDto::getUnitId)
                .distinct()
                .collect(Collectors.toList());
        final Map<Integer, StatBo> creativeStatMap = creativeStats
                .stream()
                .collect(Collectors.toMap(StatBo::getCreativeId, Function.identity(), (c1, c2) -> c1));
        //这里开始的逻辑都是 com.bilibili.adp.advertiser.portal.service.stat.HystrixCreativeService.getCpcCreativeReportVosFromESV2 搬运而来,如有问题可以对比调整
        final List<Integer> templateGroupIds = creatives
                .stream()
                .filter(creative -> creativeIdsAfterSorted.contains(creative.getCreativeId()))
                .map(CpcCreativeDto::getTemplateGroupId)
                .distinct()
                .collect(Collectors.toList());
        final List<TemplateGroupBo> templateGroups = templateGroupIds.isEmpty() ? Collections.emptyList() :
                templateGroupService.queryTemplateGroupDetail(QueryTemplateGroupDto.builder()
                        .templateGroupIds(templateGroupIds)
                        .withUnavailable(true)
                        .build());
        final Map<Integer, TemplateGroupBo> templateGroupBoMap = templateGroups
                .stream()
                .collect(Collectors.toMap(TemplateGroupBo::getId, Function.identity()));
        final Map<Integer, TemplateGroupBo> creativeIdTemplateGroupMap = creatives
                .stream()
                .collect(Collectors.toMap(CpcCreativeDto::getCreativeId, x -> templateGroupBoMap.getOrDefault(x.getTemplateGroupId(), new TemplateGroupBo())));
        final Map<Integer, Timestamp> creative2PreviewTimeMap = launchCreativePreviewService.creativeIdPreviewTimeMap(creativeIdsAfterSorted);

        final boolean notEnoughMoney = accountWalletService.isNotEnoughMoney(accountId);
        boolean isMapi = StringUtils.isNotBlank(appkey);
        QueryCreativeBo queryCreativeBo = QueryCreativeBo.builder()
                .creativeIds(creativeIdsAfterSorted)
                .withCampaign(true)
                .withUnit(true)
                .withImages(true)
                .withTitle(true)
                //.withVideo(true)
                .withScenes(true)
                //.withFlyScenes(true)
                .withPageGroup(true)
                .withArcCover(true)
                .withFlyDynamic(true)
                .withAdp6Video(true)
                .fromReport(true)
                .build();
        handle(!isMapi, queryCreativeBo::setWithVideo, true);
        handle(!isMapi, queryCreativeBo::setWithFlyScenes, true);
        final List<CpcCreativeDto> showCreativeList = cpcCreativeService.queryCpcCreativeDto(queryCreativeBo);
        final Map<Integer, CpcCreativeDto> creativeMap = showCreativeList
                .stream()
                .collect(Collectors.toMap(CpcCreativeDto::getCreativeId, Function.identity()));
        Map<Integer, Long> creativeMgkPageMap = new HashMap<>(showCreativeList.size());
        Map<Integer, Long> totalCreativeMgkPageMap = new HashMap<>(showCreativeList.size());
        for (CpcCreativeDto creativeDto : showCreativeList) {
            if (JumpTypeEnum.MGK_PAGE_ID.getCode().equals(creativeDto.getJumpType()) && NumberUtils.isCreatable(creativeDto.getPromotionPurposeContent())) {
                creativeMgkPageMap.put(creativeDto.getCreativeId(), Long.valueOf(creativeDto.getPromotionPurposeContent()));
            }
            totalCreativeMgkPageMap.put(creativeDto.getCreativeId(), creativeDto.getMgkPageId());
        }
        Map<Integer, Long> creativePageGroupIdMap = new HashMap<>();
        for (CpcCreativeDto creativeDto : showCreativeList) {
            creativePageGroupIdMap.put(creativeDto.getCreativeId(), creativeDto.getPageGroupId());
        }
        Map<Integer, String> mgkPageJumpUrlMap;
        if (!CollectionUtils.isEmpty(creativeMgkPageMap)) {
            Map<Long, String> jumpUrlMap = soaLandingPageService.getJumpUrlByPageIds(creativeMgkPageMap.values()
                    .stream()
                    .distinct()
                    .collect(Collectors.toList()));
            if (CollectionUtils.isEmpty(jumpUrlMap)) {
                jumpUrlMap = Collections.emptyMap();
            }
            mgkPageJumpUrlMap = new HashMap<>(creativeMgkPageMap.size());
            for (Map.Entry<Integer, Long> entry : creativeMgkPageMap.entrySet()) {
                mgkPageJumpUrlMap.put(entry.getKey(), jumpUrlMap.getOrDefault(entry.getValue(), ""));
            }
        } else {
            mgkPageJumpUrlMap = Collections.emptyMap();
        }
        final Map<Integer, LauUnitGameBo> unit2Game = adpCpcLauUnitGameService.getSubPkgMap(unitIds);
        final List<Integer> gameBaseIds = unit2Game.values()
                .stream()
                .map(LauUnitGameBo::getGameBaseId)
                .distinct()
                .filter(Utils::isPositive)
                .collect(Collectors.toList());
        final Map<Integer, Integer> game2SubPkgStatus = CollectionUtils.isEmpty(gameBaseIds) ? Collections.emptyMap() : adpCpcGameService.fetchSubPkgAvailableGameStatusMap(gameBaseIds);
        final List<Integer> noModifyCreativeAccountIds = JSONArray.parseArray(systemConfigService.getValueByItem(SystemConfig.NO_MODIFY_CREATIVE_ACCOUNT_IDS), Integer.class);
        final boolean isNoModify = noModifyCreativeAccountIds.contains(accountId);

        final List<Integer> programmaticCreativeIds = creativeIdsAfterSorted
                .stream()
                .map(creativeMap::get)
                .filter(c -> Objects.equals(c.getIsProgrammatic(), 1))
                .filter(c -> !com.bilibili.adp.cpc.enums.AdpVersion.isCpcFlyMerge(c.getAdpVersion()))
                .map(CpcCreativeDto::getCreativeId)
                .collect(Collectors.toList());
        final Map<Integer, ProgrammaticInfoBo> programmaticInfoBoMap = launchProgrammaticCreativeDetailService.getProgrammaticInfoMap(programmaticCreativeIds);

        final List<Integer> mergedProgrammaticCreativeIds = creativeIdsAfterSorted
                .stream()
                .map(creativeMap::get)
                .filter(c -> Objects.equals(c.getIsProgrammatic(), 1))
                .filter(c -> com.bilibili.adp.cpc.enums.AdpVersion.isCpcFlyMerge(c.getAdpVersion()))
                .map(CpcCreativeDto::getCreativeId)
                .collect(Collectors.toList());
        final Map<Integer, ProgrammaticInfoBo> mergedProgrammaticInfoBoMap =
                launchProgrammaticCreativeDetailService.getMergedProgrammaticInfoMap(mergedProgrammaticCreativeIds);

        if (!CollectionUtils.isEmpty(mergedProgrammaticInfoBoMap)) {
            programmaticInfoBoMap.putAll(mergedProgrammaticInfoBoMap);
        }

        //final Map<Integer, Long> unitIdVideoIdMap = launchUnitArchiveService.getUnitIdVideoIdMap(unitIds);
        List<Integer> liveRoomUnitIds = creativeMap.values().stream()
                .filter(creative ->
                        com.bilibili.adp.cpc.enums.AdpVersion.isCpcFlyMerge(creative.getAdpVersion()))
                .filter(creative -> creative.getTemplateGroupId().equals(TemplateGroupV2.LIVE))
                .map(CpcCreativeDto::getUnitId)
                .distinct()
                .collect(Collectors.toList());
        Map<Integer, String> liveRoomCoverMap =
                cpcUnitServiceDelegate.queryUnitLiveRoomCoverMap(liveRoomUnitIds);

        final List<LauSplashScreenCreativeImagePo> creativeImages = splashScreenCreativeImageService.list(creativeIdsAfterSorted);
        final Map<Integer, List<LauSplashScreenCreativeImagePo>> splashScreenCreativeIdImagesMap = creativeImages.stream()
                .collect(Collectors.groupingBy(LauSplashScreenCreativeImagePo::getCreativeId));

        return creativeIdsAfterSorted
                .stream()
                .map(creativeId -> {
                    final CpcCreativeDto creative = creativeMap.get(creativeId);
                    final StatBo stat = creativeStatMap.get(creativeId);
                    final CreativeExtraStatus extraStatus = LaunchCreativeService.getExtraStatus(creative, notEnoughMoney);
                    final Timestamp previewTime = creative2PreviewTimeMap.getOrDefault(creative.getCreativeId(), null);
                    final String jumpUrl = mgkPageJumpUrlMap.get(creative.getCreativeId());
                    final Long mgkPageId = totalCreativeMgkPageMap.get(creative.getCreativeId());
                    final String mgkPageIdStr = Utils.isPositive(mgkPageId) ? mgkPageId.toString() : "";
                    final Long pageGroupId = creativePageGroupIdMap.get(creative.getCreativeId());
                    final String pageGroupIdStr = Utils.isPositive(pageGroupId) ? pageGroupId.toString() : "";
                    final String creativeType = Try.of(() -> {
                        if (com.bilibili.adp.cpc.enums.AdpVersion.isCpcFlyMerge(creative.getAdpVersion())) {
                            return TemplateGroupV2.getDesc(creative.getTemplateGroupId());
                        }
                        return AdpVersion.isMergedInGeneral(creative.getAdpVersion()) ? TemplateGroupType.commonEnumKVs.get(creativeIdTemplateGroupMap.get(creative.getCreativeId()).getTgType()) : CreativeType.getByCode(creative.getCreativeType()).getDesc();
                    }).getOrElse("");
                    List<String> imageUrls = CollectionUtils.isEmpty(creative.getImageDtos()) ? Collections.emptyList() : creative.getImageDtos().stream().map(ImageDto::getUrl).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(imageUrls) && liveRoomCoverMap.containsKey(creative.getUnitId())) {
                        imageUrls = Lists.newArrayList(liveRoomCoverMap.get(creative.getUnitId()));
                    }
                    final Long videoId = creative.getVideoId();
                    final Long originVideoId = creative.getMaterialCenterVideoId();
                    CpcUnitDto cpcUnitDto = creative.getUnit();
                    final Integer opcxTarget = cpcUnitDto.getOcpcTarget();
                    final String opcxTargetDesc = Utils.isPositive(opcxTarget) ? OcpcTargetEnum.getByCode(opcxTarget).getDesc() : StringUtils.EMPTY;
                    //final Long avId = unitIdVideoIdMap.getOrDefault(creative.getUnitId(), 0L);
                    final List<String> splashScreenCreativeImageUrls = splashScreenCreativeIdImagesMap.getOrDefault(creativeId, Collections.emptyList()).stream().map(LauSplashScreenCreativeImagePo::getUrl).collect(Collectors.toList());
                    String promotionPurposeContent = jumpUrl != null ? jumpUrl : LaunchCommonV1Service.getAccessUrl(creative.getJumpType(), creative.getPromotionPurposeContent());
                    boolean isDynamicUrl = !StringUtils.isEmpty(promotionPurposeContent) &&
                            (promotionPurposeContent.contains(DynamicLinkProc.DYNAMIC_SKIP_LINK)
                                    || promotionPurposeContent.contains(DynamicLinkProc.DYNAMIC_MALL_LINK)
                                    || promotionPurposeContent.contains(DynamicLinkProc.DYNAMIC_INNER_LINK_BASE));
                    String contentUrl = "";
                    if (Utils.isPositive(creative.getFlyDynamicId()) && isDynamicUrl) {
                        contentUrl = DynamicLinkProc.DYNAMIC_INNER_LINK + creative.getFlyDynamicId();
                    } else if (Utils.isPositive(originVideoId)) {
                        contentUrl = com.bilibili.adp.cpc.biz.constants.Constants.BILIBILI_ARC_PC_PREFIX + originVideoId;
                    }
                    return CreativeReportBo.builder()
                            .adpVersion(creative.getAdpVersion())
                            .adpVersionDesc(AdpVersion.getSanlianAdpVersionDesc(creative.getAdpVersion()))
                            .subPkgStatus(launchCommonV1Service.fetchSubPkgStatus(unit2Game, game2SubPkgStatus, creative.getUnitId()))
                            .campaignId(creative.getCampaignId())
                            .campaignName(creative.getCampaign() == null ? "--" : creative.getCampaign().getCampaignName())
                            .unitId(creative.getUnitId())
                            .budget(new BigDecimal(cpcUnitDto.getBudget()))
                            .unitName(cpcUnitDto.getUnitName())
                            .creativeId(creative.getCreativeId())
                            .title(creative.getTitle())
                            .imageUrls(CollectionUtils.isEmpty(imageUrls) ? splashScreenCreativeImageUrls : imageUrls)
                            .arcCoverUrl(creative.getArcCoverUrl())
                            .videoId(videoId)
                            //.avId(Utils.isPositive(avId) ? String.valueOf(avId) : StringUtils.EMPTY)
                            //.bvId(Utils.isPositive(avId) ? BVIDUtils.avToBv(avId) : StringUtils.EMPTY)
                            .creativeAvId(Utils.isPositive(originVideoId) ? String.valueOf(originVideoId) : StringUtils.EMPTY)
                            .creativeBvId(Utils.isPositive(originVideoId) ? BVIDUtils.avToBv(originVideoId) : StringUtils.EMPTY)
                            .contentUrl(contentUrl)
                            .mgkVideoId(creative.getMgkVideoId())
                            .mgkVideoUrl(creative.getMgkVideoUrl())
                            .mgkPageId(mgkPageIdStr)
                            .pageGroupId(pageGroupIdStr)
                            .promotionPurposeContent(promotionPurposeContent)
                            .status(creative.getCreativeStatus())
                            .statusDesc(CreativeStatus.getByCode(creative.getCreativeStatus()).getDesc())
                            .reason(creative.getReason())
                            .previewTime(previewTime == null ? 0L : previewTime.getTime())
                            .previewStatus(LaunchCommonV1Service.getPreviewStatus(creative.getStatus(), creative.getAuditStatus(), previewTime, creative.getCampaign() == null ? null : creative.getCampaign().getPromotionPurposeType()))
                            // 历史原因, 不要纠结
                            .templateName(creativeType)
                            .isHistory(creative.getIsHistory())
                            .isModify(isNoModify ? 0 : 1)
                            .extraStatus(Collections.singletonList(extraStatus.getCode()))
                            .extraStatusDesc(Collections.singletonList(extraStatus.getDesc()))
                            .flowWeightState(creative.getFlowWeightState())
                            .cardTypes(Collections.emptyList())
                            .isProgrammatic(BooleanEnum.VALID.getCode() == Optional.ofNullable(creative.getIsProgrammatic()).orElse(0))
                            .programmaticInfo(programmaticInfoBoMap.get(creativeId))
                            .creativeType(BooleanEnum.VALID.getCode() == Optional.ofNullable(creative.getIsProgrammatic()).orElse(0) ? CREATIVE_TYPE_PROGRAMMATIC : CREATIVE_TYPE_CUSTOM)
                            .bilibiliVideo(creative.getBilibiliVideoBo())
                            //.flyCreativeStyle(LaunchCommonV1Service.creativeStyle2coverName(creative.getCreativeStyle()))
                            .creativeName(creative.getCreativeName())
                            .advertisingMode(creative.getAdvertisingMode())
                            .advertisingModeDesc(AdvertisingMode.getByKey(creative.getAdvertisingMode()).getValue())
                            ///.creativeStyle(LaunchCommonV1Service.buildCreativeStyle(creative.getCreativeStyle(), creativeIdTemplateGroupMap.get(creative.getCreativeId())))
                            .materialDesc(creative.getDescription())
                            .isManaged(Objects.equals(1, creative.getIsManaged()))
                            .createTime(TimeUtils.formatDateTime(creative.getCtime()))
                            .ocpxTargetDesc(opcxTargetDesc)
                            .stat(stat)
                            .auditStatus(creative.getAuditStatus())
                            .imageMd5(creative.getImageMd5())
                            .materialCenterVideoId(creative.getMaterialCenterVideoId())
                            .subjectId(cpcUnitDto.getSubjectId())
                            .dynamicId(creative.getDynamicId())
                            .hasAigcReplaceHistory(Functions.boolean2Integer(creative.isHasAigcReplaceHistory()))
                            .unitPromotionPurposeType(cpcUnitDto.getPromotionPurposeType())
                            .templateGroupId(creative.getTemplateGroupId())
                            .businessDomain(cpcUnitDto.getBusinessDomain())
                            .build();
                }).collect(Collectors.toList());
    }

    public StatBo getCampaignSummaryStat(Integer accountId, List<Integer> campaignIds, Long startTime, Long endTime) {
        final Long queryStartTime = getStartTime(startTime);
        final Long queryEndTime = genEndTime(endTime);
        final StatReq req = StatReq.newBuilder()
                .setStartTime(queryStartTime)
                .setEndTime(queryEndTime)
                .addAllOcpxSalesTypes(OCPX_SALES_TYPES)
                .addAllSalesTypes(PLATFORM_SALES_TYPES)
                .setTypeValue(StatQueryType.CAMPAIGN.getCode())
                .setTimeTypeValue(StatQueryTimeType.DAY.getCode())
                .addAccountIds(accountId)
                .addAllCampaignIds(campaignIds)
                .setPs(campaignIds.size())
                .build();
        StatReply campaignSummaryStat = cpmReportSvrProxy.getCampaignSummaryStat(req);
        return StatConverter.MAPPER.grpcBo2Bo(campaignSummaryStat.getStat());
    }

    public StatBo getUnitSummaryStat(Integer accountId, List<Integer> unitIds, Long startTime, Long endTime) {
        final Long queryStartTime = getStartTime(startTime);
        final Long queryEndTime = genEndTime(endTime);
        final StatReq req = StatReq.newBuilder()
                .setStartTime(queryStartTime)
                .setEndTime(queryEndTime)
                .addAllOcpxSalesTypes(OCPX_SALES_TYPES)
                .addAllSalesTypes(PLATFORM_SALES_TYPES)
                .setTypeValue(StatQueryType.UNIT.getCode())
                .setTimeTypeValue(StatQueryTimeType.DAY.getCode())
                .addAccountIds(accountId)
                .addAllUnitIds(unitIds)
                .setPs(unitIds.size())
                .build();
        StatReply unitSummaryStat = cpmReportSvrProxy.getUnitSummaryStat(req);
        return StatConverter.MAPPER.grpcBo2Bo(unitSummaryStat.getStat());
    }

    public StatBo getCreativeSummaryStat(Integer accountId, List<Integer> creativeIds, Long startTime, Long endTime) {
        final Long queryStartTime = getStartTime(startTime);
        final Long queryEndTime = genEndTime(endTime);
        final StatReq req = StatReq.newBuilder()
                .setStartTime(queryStartTime)
                .setEndTime(queryEndTime)
                .addAllOcpxSalesTypes(OCPX_SALES_TYPES)
                .addAllSalesTypes(PLATFORM_SALES_TYPES)
                .setTypeValue(StatQueryType.CREATIVE.getCode())
                .setTimeTypeValue(StatQueryTimeType.DAY.getCode())
                .addAccountIds(accountId)
                .addAllCreativeIds(creativeIds)
                .setPs(creativeIds.size())
                .build();
        StatReply creativeSummaryStat = cpmReportSvrProxy.getCreativeSummaryStat(req);
        return StatConverter.MAPPER.grpcBo2Bo(creativeSummaryStat.getStat());
    }

    public StatChartBo getBusinessFlyStatChart(Long startTime, Long endTime, Integer accountId, List<Integer> creativeIds) {
        if (CollectionUtils.isEmpty(creativeIds)) {
            return StatChartBo.builder().build();
        }
        final StatReq.Type type = StatReq.Type.ACCOUNT;//这儿原来用的StatReq.Type.CREATIVE，30天，2条创意数据，horizontalAxis会返回60条数据
        final long days = Duration.between(toLocalDateTime(startTime), toLocalDateTime(endTime)).toDays();
        final StatReq.TimeType timeType;
        if (days >= 1) {
            timeType = StatReq.TimeType.DAY;
        } else {
            timeType = StatReq.TimeType.HOUR;
        }
        final StatReq req = StatReq.newBuilder()
                .setStartTime(startTime)
                .setEndTime(endTime)
                .setType(type)
                .setTimeType(timeType)
                .addAccountIds(accountId)//accountId不赋值，会出现返回空的情况；accountId赋值，会出现返回都为0的情况，且横坐标有值
                .addAllCreativeIds(creativeIds)
                .build();
        StatChartReply statChartReply = cpmReportSvrProxy.getBusinessFlyStatChart(req);
        return StatConverter.MAPPER.grpcBo2Bo(statChartReply.getStatChart());
    }

    public PageResult<ReportDetailBo> businessFlyListDetailStats(Long startTime, Long endTime, Integer launchType, Integer timeTypeValue, Integer accountId, List<Integer> campaignIds, List<Integer> unitIds, List<Integer> creativeIds, String orderValue, Integer sortValue, Integer pn, Integer ps) {
        log.info("businessFlyListDetailStats,unitIds is: {}", unitIds);
        List<Integer> accountIds = Stream.of(accountId).filter(Objects::nonNull).collect(Collectors.toList());
        final AccountDto account = queryAccountService.getAccount(accountId);
        final boolean isGoodsAccount = Objects.equals(account.getDepartmentId(), accountConfig.getGoodsDepartmentId());
        final StatReq.Type type = StatReq.Type.forNumber(launchType);
        final StatReq.TimeType timeType = StatReq.TimeType.forNumber(timeTypeValue);
        final StatReq.Order order = StringUtils.isEmpty(orderValue) ? StatReq.Order.DATE_TIME : OrderEnum.getOrder(orderValue);
        final StatReq.Sort sort = StatReq.Sort.forNumber(sortValue);
        final StatsReply statsReply = businessFlyListStats(startTime, endTime, type, timeType, accountIds, campaignIds, unitIds, creativeIds, order, sort, pn, ps);
        final List<Stat> stats = statsReply.getStatsList();
        switch (Objects.requireNonNull(type)) {
            case CAMPAIGN:
                campaignIds = stats
                        .stream()
                        .map(Stat::getCampaignId)
                        .filter(Utils::isPositive)
                        .collect(Collectors.toList());
                List<LauCampaignPo> campaigns = launchCampaignService.listCampaigns(campaignIds);
                Map<Integer, LauCampaignPo> campaignMap = campaigns
                        .stream()
                        .collect(Collectors.toMap(LauCampaignPo::getCampaignId, Function.identity(), (c1, c2) -> c1));
                final List<ReportDetailBo> campaignReportDetails = stats
                        .stream()
                        .map(stat -> {
                            LauCampaignPo campaign = campaignMap.get(stat.getCampaignId());
                            ReportDetailBo reportDetail = ReportDetailBo.builder()
                                    .date(getDate(timeType, startTime, endTime, stat.getDateTime()))
                                    .time(getTime(timeType, stat.getDateTime()))
                                    .stat(StatConverter.MAPPER.grpcBo2Bo(stat))
                                    .build();
                            fillCampaignInfo(reportDetail, campaign, isGoodsAccount);
                            return reportDetail;
                        }).collect(Collectors.toList());
                return new PageResult<>(statsReply.getTotal(), campaignReportDetails);
            case UNIT:
                unitIds = stats
                        .stream()
                        .map(Stat::getUnitId)
                        .filter(Utils::isPositive)
                        .collect(Collectors.toList());
                List<LauUnitPo> units = launchUnitV1Service.listUnits(unitIds);
                List<Integer> slotGroupIds = units.stream().map(LauUnitPo::getSlotGroup).collect(Collectors.toList());
                Map<Integer, String> slotGroupIdNameMap = resSlotGroupService.getSlotGroupByIds(slotGroupIds)
                        .stream()
                        .collect(Collectors.toMap(SlotGroupSimpleDto::getId, SlotGroupSimpleDto::getSlotGroupName));
                final List<Integer> campaignIdsFromUnits = units
                        .stream()
                        .map(LauUnitPo::getCampaignId)
                        .collect(Collectors.toList());
                campaigns = launchCampaignService.listCampaigns(campaignIdsFromUnits);
                campaignMap = campaigns
                        .stream()
                        .collect(Collectors.toMap(LauCampaignPo::getCampaignId, Function.identity(), (c1, c2) -> c1));
                Map<Integer, LauUnitPo> unitMap = units
                        .stream()
                        .collect(Collectors.toMap(LauUnitPo::getUnitId, Function.identity(), (u1, u2) -> u1));

                //用单元id，查稿件id
                log.info("detail by unit,unitIds is: {}", unitIds);
                List<LauUnitArchiveVideoPo> lauUnitArchiveVideoPos = cpcUnitServiceDelegate.getLauUnitArchiveVideoByUnitIds(unitIds);
                Map<Integer, LauUnitArchiveVideoPo> lauUnitArchiveVideoPoMap = lauUnitArchiveVideoPos.stream().collect(Collectors.toMap(LauUnitArchiveVideoPo::getUnitId, x -> x));
                List<Long> aids = lauUnitArchiveVideoPos.stream().map(LauUnitArchiveVideoPo::getVideoId).collect(Collectors.toList());
                log.info("detail by unit,aids is: {}", aids);
                Map<Long, Arc> arcMap = archiveService.arcs(aids);

                final List<ReportDetailBo> unitReportDetails = stats
                        .stream()
                        .map(stat -> {
                            LauUnitPo unit = unitMap.get(stat.getUnitId());
                            LauCampaignPo campaign = campaignMap.get(unit.getCampaignId());
                            LauUnitArchiveVideoPo lauUnitArchiveVideoPo = lauUnitArchiveVideoPoMap.get(unit.getUnitId());
                            ReportDetailBo reportDetail = ReportDetailBo.builder()
                                    .date(getDate(timeType, startTime, endTime, stat.getDateTime()))
                                    .time(getTime(timeType, stat.getDateTime()))
                                    .stat(StatConverter.MAPPER.grpcBo2Bo(stat))
                                    .build();
                            fillUnitInfo(reportDetail, unit, slotGroupIdNameMap);
                            fillCampaignInfo(reportDetail, campaign, isGoodsAccount);
                            fillVideoAndMidInfo(reportDetail, lauUnitArchiveVideoPo, arcMap);
                            return reportDetail;
                        })
                        .collect(Collectors.toList());
                return new PageResult<>(statsReply.getTotal(), unitReportDetails);
            case CREATIVE:
                creativeIds = stats
                        .stream()
                        .map(Stat::getCreativeId)
                        .filter(Utils::isPositive)
                        .collect(Collectors.toList());
                final List<LauUnitCreativePo> creatives = launchCreativeService.listCreatives(creativeIds);
                final List<Integer> unitIdsFromCreatives = creatives
                        .stream()
                        .map(LauUnitCreativePo::getUnitId)
                        .distinct()
                        .collect(Collectors.toList());
                units = launchUnitV1Service.listUnits(unitIdsFromCreatives);
                slotGroupIds = units.stream().map(LauUnitPo::getSlotGroup).collect(Collectors.toList());
                slotGroupIdNameMap = resSlotGroupService.getSlotGroupByIds(slotGroupIds)
                        .stream()
                        .collect(Collectors.toMap(SlotGroupSimpleDto::getId, SlotGroupSimpleDto::getSlotGroupName));
                final List<Integer> campaignIdsFromCreatives = creatives
                        .stream()
                        .map(LauUnitCreativePo::getCampaignId)
                        .distinct()
                        .collect(Collectors.toList());
                campaigns = launchCampaignService.listCampaigns(campaignIdsFromCreatives);
                campaignMap = campaigns
                        .stream()
                        .collect(Collectors.toMap(LauCampaignPo::getCampaignId, Function.identity(), (c1, c2) -> c1));
                unitMap = units
                        .stream()
                        .collect(Collectors.toMap(LauUnitPo::getUnitId, Function.identity(), (u1, u2) -> u1));
                final Map<Integer, LauUnitCreativePo> creativeMap = creatives
                        .stream()
                        .collect(Collectors.toMap(LauUnitCreativePo::getCreativeId, Function.identity(), (c1, c2) -> c1));

                //用单元id，查稿件id
                log.info("detail by creative,unitIds is: {}", unitIdsFromCreatives);
                List<LauUnitArchiveVideoPo> lauUnitArchiveVideoPos1 = cpcUnitServiceDelegate.getLauUnitArchiveVideoByUnitIds(unitIdsFromCreatives);
                Map<Integer, LauUnitArchiveVideoPo> lauUnitArchiveVideoPoMap1 = lauUnitArchiveVideoPos1.stream().collect(Collectors.toMap(LauUnitArchiveVideoPo::getUnitId, x -> x));
                List<Long> aids1 = lauUnitArchiveVideoPos1.stream().map(LauUnitArchiveVideoPo::getVideoId).collect(Collectors.toList());
                log.info("detail by creative,aids is: {}", aids1);
                Map<Long, Arc> arcMap1 = archiveService.arcs(aids1);
                final List<ReportDetailBo> creativeReportDetails = stats
                        .stream()
                        .map(stat -> {
                            LauUnitCreativePo creative = creativeMap.get(stat.getCreativeId());
                            LauUnitPo unit = unitMap.get(creative.getUnitId());
                            LauCampaignPo campaign = campaignMap.get(creative.getCampaignId());
                            LauUnitArchiveVideoPo lauUnitArchiveVideoPo1 = lauUnitArchiveVideoPoMap1.get(unit.getUnitId());
                            ReportDetailBo reportDetail = ReportDetailBo.builder()
                                    .date(getDate(timeType, startTime, endTime, stat.getDateTime()))
                                    .time(getTime(timeType, stat.getDateTime()))
                                    .stat(StatConverter.MAPPER.grpcBo2Bo(stat))
                                    .build();
                            fillCreativeInfo(reportDetail, creative);
                            fillUnitInfo(reportDetail, unit, slotGroupIdNameMap);
                            fillCampaignInfo(reportDetail, campaign, isGoodsAccount);
                            fillVideoAndMidInfo(reportDetail, lauUnitArchiveVideoPo1, arcMap1);
                            return reportDetail;
                        })
                        .collect(Collectors.toList());
                return new PageResult<>(statsReply.getTotal(), creativeReportDetails);
            default:
                final List<ReportDetailBo> reportDetails = stats
                        .stream()
                        .map(stat -> ReportDetailBo.builder()
                                .date(getDate(timeType, startTime, endTime, stat.getDateTime()))
                                .time(getTime(timeType, stat.getDateTime()))
                                .stat(StatConverter.MAPPER.grpcBo2Bo(stat))
                                .build())
                        .collect(Collectors.toList());
                return new PageResult<>(statsReply.getTotal(), reportDetails);
        }
    }

    public StatsReply businessFlyListStats(Long startTime, Long endTime, StatReq.Type type, StatReq.TimeType timeType, List<Integer> accountIds, List<Integer> campaignIds, List<Integer> unitIds, List<Integer> creativeIds, StatReq.Order order, StatReq.Sort sort, Integer pn, Integer ps) {
        final StatReq req = StatReq.newBuilder()
                .setStartTime(startTime)
                .setEndTime(endTime)
                .setType(type)
                .setTimeType(timeType)
                .addAllAccountIds(accountIds)
                .addAllCampaignIds(campaignIds)
                .addAllUnitIds(unitIds)
                .addAllCreativeIds(creativeIds)
                .setOrder(order)
                .setSort(sort)
                .setPn(pn)
                .setPs(ps)
                .build();
        switch (type) {
            case ACCOUNT:
                return cpmReportSvrProxy.listBusinessFlyAccountStats(req);
            case CAMPAIGN:
                return cpmReportSvrProxy.listBusinessFlyCampaignStats(req);
            case UNIT:
                return cpmReportSvrProxy.listBusinessFlyUnitStats(req);
            case CREATIVE:
                return cpmReportSvrProxy.listBusinessFlyCreativeStats(req);
            default:
                return StatsReply.getDefaultInstance();
        }
    }

    //根据开始时间+结束时间+账号id，查询有消耗的起飞稿件
    public PageResult<BusinessFlyArchiveBo> getBusinessFlyArchiveList(Integer accountId,
                                                                      Integer page,
                                                                      Integer size,
                                                                      Long startTime,
                                                                      Long endTime,
                                                                      String avIds) {
        List<Long> videoIds = Collections.emptyList();
        if (StringUtils.isNotBlank(avIds)) {
            videoIds = Arrays.stream(avIds.split(",")).map(s -> Long.valueOf(s.trim())).collect(Collectors.toList());
        }
        BusinessFlyVideoInfoReq req = BusinessFlyVideoInfoReq.newBuilder()
                .setStartTime(startTime)
                .setEndTime(endTime)
                .setAccountId(accountId)
                .addAllVideoIds(videoIds)
                .build();
        BusinessFlyVideoInfoReply businessFlyVideoInfoReply = cpmReportSvrProxy.getBusinessFlyVideoInfo(req);
        if (Objects.isNull(businessFlyVideoInfoReply) || CollectionUtils.isEmpty(businessFlyVideoInfoReply.getBusinessFlyVideoList())) {
            return PageResult.emptyPageResult();
        }

        //排序，按花费降序
        List<BusinessFlyVideo> sortBusinessFlyVideos = businessFlyVideoInfoReply.getBusinessFlyVideoList()
                .stream().sorted(Comparator.comparing(BusinessFlyVideo::getCost).reversed())
                .collect(Collectors.toList());

        //内存分页
        List<BusinessFlyVideo> businessFlyVideos = sortBusinessFlyVideos.stream()
                .skip((long) size * (page - 1))
                .limit(size)
                .collect(Collectors.toList());

        List<Long> aids = businessFlyVideos.stream().map(BusinessFlyVideo::getVideoId).collect(Collectors.toList());

        //补充稿件信息
        Map<Long, Arc> arcMap = archiveService.arcs(aids);
        List<BusinessFlyArchiveBo> bos = new ArrayList<>();
        businessFlyVideos.forEach(businessFlyVideo -> {
            Arc arc = arcMap.get(businessFlyVideo.getVideoId());
            if (!Objects.isNull(arc)) {
                bos.add(BusinessFlyArchiveBo.builder()
                        .aid(businessFlyVideo.getVideoId())
                        .bid(BVIDUtils.avToBv(businessFlyVideo.getVideoId()))
                        .firstCid(arc.getFirstCid())
                        .pic(arc.getPic())
                        .title(arc.getTitle())
                        .duration(arc.getDuration())
                        .width(arc.getDimension().getWidth())
                        .height(arc.getDimension().getHeight())
                        .creativeIds(businessFlyVideo.getCreativeIdsList())
                        .cost(businessFlyVideo.getCost())
                        .mid(arc.getAuthor().getMid())
                        .upName(arc.getAuthor().getName())
                        .upFacePic(arc.getAuthor().getFace())
                        .pubDate(Utils.isPositive(arc.getPubDate()) ? Utils.getTimestamp2String(new Timestamp(arc.getPubDate() * 1000)) : "")
                        .build());
            }
        });
        return new PageResult<>(businessFlyVideoInfoReply.getBusinessFlyVideoList().size(), bos);
    }

    public BusinessFlyArchiveLaunchBo getBusinessFlyArchiveLaunchInfo(Long startTime, Long endTime, Integer accountId, String aids) {
        List<Long> videoIds = Collections.emptyList();
        if (StringUtils.isNotBlank(aids)) {
            videoIds = Arrays.stream(aids.split(",")).map(s -> Long.valueOf(s.trim())).collect(Collectors.toList());
        }
        BusinessFlyVideoInfoReq req = BusinessFlyVideoInfoReq.newBuilder()
                .setStartTime(startTime)
                .setEndTime(endTime)
                .setAccountId(accountId)
                .addAllVideoIds(videoIds)
                .build();

        BusinessFlyVideoInfoReply businessFlyVideoInfoReply = cpmReportSvrProxy.getBusinessFlyVideoInfo(req);
        if (Objects.isNull(businessFlyVideoInfoReply) || CollectionUtils.isEmpty(businessFlyVideoInfoReply.getBusinessFlyVideoList())) {
            return null;
        }

        Set<Integer> campaignIds = new HashSet<>();
        Set<Integer> unitIds = new HashSet<>();
        Set<Integer> creativeIds = new HashSet<>();

        businessFlyVideoInfoReply.getBusinessFlyVideoList().forEach(businessFlyVideo -> {
            campaignIds.addAll(businessFlyVideo.getCampaignIdsList());
            unitIds.addAll(businessFlyVideo.getUnitIdsList());
            creativeIds.addAll(businessFlyVideo.getCreativeIdsList());
        });
        return BusinessFlyArchiveLaunchBo.builder()
                .campaignIds(Lists.newArrayList(campaignIds))
                .unitIds(Lists.newArrayList(unitIds))
                .creativeIds(Lists.newArrayList(creativeIds))
                .build();
    }

    public List<DwsCtntArchFlyArchivePlayInteractADPo> getArchiveInteractDetail(String aids, Long startDay, Long endDay) {
        Assert.isTrue(!Strings.isNullOrEmpty(aids), "请选择稿件信息");
        List<Long> videoIds = Arrays.stream(aids.split(",")).map(s -> Long.valueOf(s.trim())).collect(Collectors.toList());
        Assert.isTrue(!CollectionUtils.isEmpty(videoIds), "稿件信息不能为空");

        List<DwsCtntArchFlyArchivePlayInteractADPo> retentionList = cpcClickhouseBqf.selectFrom(dwsCtntArchFlyArchivePlayInteractAD)
                .where(dwsCtntArchFlyArchivePlayInteractAD.avid.in(videoIds))
                .where(dwsCtntArchFlyArchivePlayInteractAD.date.goe(new java.sql.Date(startDay)))
                .where(dwsCtntArchFlyArchivePlayInteractAD.date.loe(new java.sql.Date(endDay)))
                .fetch();

        return retentionList;
    }

    public List<BusinessFlyArchivePartStayBo> getArchivePartStayRate(Long aid, Long startDay, Long endDay) {
        List<BusinessFlyArchivePartStayBo> retentionBos = new ArrayList<>();
        if (!Utils.isPositive(aid) || !Utils.isPositive(startDay) || !Utils.isPositive(endDay)) {
            return retentionBos;
        }

        String startDayStr = Utils.getTimestamp2String(new Timestamp(startDay), "yyyyMMdd");
        String endDayStr = Utils.getTimestamp2String(new Timestamp(endDay), "yyyyMMdd");

        List<AdsCtntArchFlyArchiveVvRetention1dDPo> retentionList = cpcClickhouseBqf.selectFrom(adsCtntArchFlyArchiveVvRetention1dD)
                .where(adsCtntArchFlyArchiveVvRetention1dD.avid.eq(aid))
                .where(adsCtntArchFlyArchiveVvRetention1dD.logDate.goe(startDayStr))
                .where(adsCtntArchFlyArchiveVvRetention1dD.logDate.loe(endDayStr))
                .fetch();
        if (CollectionUtils.isEmpty(retentionList)) {
            return retentionBos;
        }

        Integer intervalSize = getArchiveIntervalSize(retentionList.get(0).getVideoDuration().intValue());
        Integer interval = getArchiveInterval(retentionList.get(0).getVideoDuration().intValue());
        if (!Utils.isPositive(intervalSize) || !Utils.isPositive(interval)) {
            return retentionBos;
        }

        for (int i = 0; i < intervalSize; i++) {
            Integer durationKey = i * interval;
            BigDecimal retentionRate = BigDecimal.ZERO;
            for (AdsCtntArchFlyArchiveVvRetention1dDPo adsCtntArchFlyArchiveVvRetention1dDPo : retentionList) {
                String retentionRateStr = adsCtntArchFlyArchiveVvRetention1dDPo.getRetentionRate();
                List<BusinessFlyArchivePartStayBo> partStayBos = convert2PartStayBoList(retentionRateStr);
                retentionRate = retentionRate.add(partStayBos.get(i).getNum());
            }
            retentionBos.add(BusinessFlyArchivePartStayBo.builder()
                    .duration_key(durationKey)
                    .num(retentionRate.divide(new BigDecimal(retentionList.size()), 2, RoundingMode.HALF_UP))
                    .build());
        }

        return retentionBos;
    }

    //29:0,34:1,28:0,2:0,30:0,20:0,23:0,10:0,0:0,11:0,22:0,3:0,8:0,21:0,33:0,9:0,32:0,27:0,35:0,4:0,14:0,25:0,31:0,18:0,17:0,5:0,24:0,16:0,12:0,26:0,7:0,13:0,1:0,15:0,6:0,19:0
    //11:1.77,0:100.00,2:12.80,8:2.59,12:1.56,17:0.00,6:3.38,10:1.98,3:8.80,1:23.91,4:5.13,13:1.40,16:0.39,9:2.28,15:1.02,7:2.89,5:4.00,14:1.16
    private List<BusinessFlyArchivePartStayBo> convert2PartStayBoList(String retentionVv) {
        List<BusinessFlyArchivePartStayBo> retentionBos = new ArrayList<>();
        List<String> retentionVvList = Arrays.stream(retentionVv.split(",")).collect(Collectors.toList());
        retentionVvList.forEach(retention -> {
            List<String> retentionRateValue = Arrays.stream(retention.split(":")).collect(Collectors.toList());
            retentionBos.add(BusinessFlyArchivePartStayBo.builder().duration_key(Integer.valueOf(retentionRateValue.get(0))).num(new BigDecimal(retentionRateValue.get(1))).build());
        });

        return retentionBos.stream()
                .sorted(Comparator.comparing(BusinessFlyArchivePartStayBo::getDuration_key))
                .collect(Collectors.toList());
    }

    private Integer getArchiveIntervalSize(Integer duration) {
        if (duration <= 30) {
            return duration;
        }
        if (duration <= 60) {
            return duration % 2 == 0 ? (duration / 2 + 1) : (duration / 2 + 2);
        }
        if (duration <= 120) {
            return duration % 3 == 0 ? (duration / 3 + 1) : (duration / 3 + 2);
        }
        return duration % 5 == 0 ? (duration / 5 + 1) : (duration / 5 + 2);
    }

    private Integer getArchiveInterval(Integer duration) {
        if (duration <= 30) {
            return 1;
        }
        if (duration <= 60) {
            return 2;
        }
        if (duration <= 120) {
            return 3;
        }
        return 5;
    }

    public List<UnitReportOtherAttrBo> queryUnitReportOtherAttrs(List<Integer> unitIds, List<Integer> labelIds) {

        if (CollectionUtils.isEmpty(unitIds)) {
            return Collections.emptyList();
        }

        // 单元信息
        List<LauUnitPo> lauUnitPos = unitService.queryUnitList(unitIds);
        // video
        //final Map<Integer, Long> unitIdVideoIdMap = launchUnitArchiveService.getUnitIdVideoIdMap(unitIds);

        List<UnitReportOtherAttrBo> reportOtherAttBos = new ArrayList<>();
        for (LauUnitPo lauUnitPo : lauUnitPos) {
            //final Long avId = unitIdVideoIdMap.getOrDefault(lauUnitPo.getUnitId(), 0L);

            UnitReportOtherAttrBo otherAttrBo = UnitReportOtherAttrBo.builder()
                    .unitId(lauUnitPo.getUnitId())
                    .campaignId(lauUnitPo.getCampaignId())
                    //.videoId(avId)
                    .ocpcTarget(lauUnitPo.getOcpcTarget())
                    .ocpxTargetTwo(lauUnitPo.getOcpxTargetTwo())
                    .promotionPurposeType(lauUnitPo.getPromotionPurposeType())
                    .build();
            reportOtherAttBos.add(otherAttrBo);
        }

        // 处理单元层级带货标识情况(稿件，动态)
        //goodsArchiveHintProc.procUnitGoodsSituationNew(reportOtherAttBos, labelIds);

        // 起量
        fillAccelerateAttr(reportOtherAttBos, lauUnitPos, labelIds);

        return reportOtherAttBos;
    }

    private void fillAccelerateAttr(List<UnitReportOtherAttrBo> reportOtherAttBos, List<LauUnitPo> lauUnitPos, List<Integer> labelIds) {
        if (CollectionUtils.isEmpty(reportOtherAttBos)) {
            return;
        }

        boolean accountSupportAccelerate = unitAccelerateService.accountSupportAccelerate(labelIds);
        if (accountSupportAccelerate) {
            doFillAccelerateAttr(reportOtherAttBos, lauUnitPos);
        } else {
            reportOtherAttBos.forEach(bo -> {
                bo.setAccelerateStatus(UnitAccelerateStatus.NOT_SUPPORT.getCode());
                bo.setNotSupportAccelerateReason("该单元不支持开启一键起量");
            });
        }
    }

    private void doFillAccelerateAttr(List<UnitReportOtherAttrBo> reportOtherAttBos, List<LauUnitPo> lauUnitPos) {
        List<Integer> unitIds = lauUnitPos.stream().map(LauUnitPo::getUnitId).collect(Collectors.toList());

        Map<Integer, Boolean> unitId2HasValidCreative = new HashMap<>();
        List<Integer> hasCreativeUnitIds = cpcUnitService.hasCreativeUnitIds(unitIds, Collections.singletonList(CreativeStatus.VALID.getCode()));
        hasCreativeUnitIds.forEach(unitId -> unitId2HasValidCreative.put(unitId, Boolean.TRUE));

        Map<Integer, LauUnitAccelerateBo> unitId2LauAccelerateMap = unitAccelerateService.getUnitId2LauAccelerateMap(unitIds);

        Map<Integer, Integer> unitId2InFlySearchMap = unitAccelerateService.getUnitId2InFlySearchMap(unitIds);

        Map<Integer, CpcCampaignDto> campaignId2CampaignDtoMap = LaunchCpcCampaignService.getCampaignMapInCampaignIds(lauUnitPos.stream().map(LauUnitPo::getCampaignId).distinct().collect(Collectors.toList()));

        Map<Integer, LauUnitPo> unitId2PoMap = lauUnitPos.stream().collect(Collectors.toMap(LauUnitPo::getUnitId, Function.identity()));
        for (UnitReportOtherAttrBo reportOtherAttBo : reportOtherAttBos) {
            Integer unitId = reportOtherAttBo.getUnitId();
            LauUnitPo lauUnitPo = unitId2PoMap.get(unitId);
            if (lauUnitPo != null) {
                boolean ocpcOrOcpmUnit = unitAccelerateService.isOcpcOrOcpmUnit(lauUnitPo.getSalesType(), lauUnitPo.getOcpcTarget(), lauUnitPo.getIsNoBid());
                if (!ocpcOrOcpmUnit) {
                    reportOtherAttBo.setAccelerateStatus(UnitAccelerateStatus.NOT_SUPPORT.getCode());
                    reportOtherAttBo.setNotSupportAccelerateReason("该单元不支持开启一键起量");
                    continue;
                }
            } else {
                reportOtherAttBo.setAccelerateStatus(UnitAccelerateStatus.NOT_SUPPORT.getCode());
                reportOtherAttBo.setNotSupportAccelerateReason("该单元不支持开启一键起量");
                continue;
            }

            CpcCampaignDto cpcCampaignDto = campaignId2CampaignDtoMap.get(lauUnitPo.getCampaignId());
            Integer inFlySearch = unitId2InFlySearchMap.get(unitId);
            if (cpcCampaignDto != null && Objects.equals(cpcCampaignDto.getPromotionPurposeType(), PromotionPurposeType.BRAND_SPREAD.getCode()) && OcpcTargetEnum.GOODS_TRANSACTION.getCode().equals(lauUnitPo.getOcpcTarget()) && Objects.equals(1, inFlySearch)) {
                reportOtherAttBo.setAccelerateStatus(UnitAccelerateStatus.NOT_SUPPORT.getCode());
                reportOtherAttBo.setNotSupportAccelerateReason("该单元处于探索期，不支持开启一键起量");
                continue;
            }

            if (!Objects.equals(UnitStatus.VALID.getCode(), lauUnitPo.getStatus()) || cpcCampaignDto == null || CampaignStatus.VALID.getCode() != cpcCampaignDto.getStatus()) {
                reportOtherAttBo.setAccelerateStatus(UnitAccelerateStatus.NOT_SUPPORT.getCode());
                reportOtherAttBo.setNotSupportAccelerateReason("单元当前处于未投放状态，不支持开启一键起量");
                continue;
            }

            Boolean hasValidCreative = unitId2HasValidCreative.get(unitId);
            if (hasValidCreative == null || Boolean.FALSE.equals(hasValidCreative)) {
                reportOtherAttBo.setAccelerateStatus(UnitAccelerateStatus.NOT_SUPPORT.getCode());
                reportOtherAttBo.setNotSupportAccelerateReason("单元当前处于未投放状态，不支持开启一键起量");
                continue;
            }

            LauUnitAccelerateBo lauUnitAccelerateBo = unitId2LauAccelerateMap.get(unitId);
            reportOtherAttBo.setAccelerateStatus(lauUnitAccelerateBo == null ? UnitAccelerateStatus.INACTIVE.getCode() : lauUnitAccelerateBo.getAccelerateStatus());
        }
    }

    public List<CreativeReportOtherAttrBo> queryCreativeReportOtherAttrs(List<Integer> creativeIds,
                                                                         List<Integer> labelIds) {

        if (CollectionUtils.isEmpty(creativeIds)) {
            return Collections.emptyList();
        }
        List<LauUnitCreativePo> creatives = launchCreativeService.listCreatives(creativeIds);
        List<Integer> unitIds = creatives.stream().map(LauUnitCreativePo::getUnitId).distinct().collect(Collectors.toList());
        // video
        //final Map<Integer, Long> unitIdVideoIdMap = launchUnitArchiveService.getUnitIdVideoIdMap(unitIds);

        List<CreativeReportOtherAttrBo> otherAttrBos = new ArrayList<>();
        for (LauUnitCreativePo creativePo : creatives) {
            //final Long avId = unitIdVideoIdMap.getOrDefault(creativePo.getUnitId(), 0L);

            CreativeReportOtherAttrBo otherAttrBo = CreativeReportOtherAttrBo.builder()
                    .creativeId(creativePo.getCreativeId())
                    .unitId(creativePo.getUnitId())
                    .campaignId(creativePo.getCampaignId())
                    //.videoId(avId)
                    .build();
            otherAttrBos.add(otherAttrBo);
        }
        //goodsArchiveHintProc.procCreativeGoodsSituation(otherAttrBos, labelIds);
        return otherAttrBos;
    }

    public PageResult<RtaReportBo> queryRtaReport(QueryRtaReportBo queryBo) {
        RtaStatReq request = RtaReportConverter.MAPPER.convertQueryBo2Request(queryBo);
        RtaStatReply reply = cpmJupiterProxy.rtaStatAnalysis(request);

        int total = reply.getTotal();
        List<RtaReportBo> boList = reply.getDataList().stream()
                .map(RtaReportConverter.MAPPER::convertRtaStaBo2Stat)
                .collect(Collectors.toList());

        return PageResult.<RtaReportBo>builder()
                .records(boList)
                .total(total)
                .build();
    }

    public PageResult<StarEventBo> queryStarReport(String fromTime, String toTime, List<String> accountIds, Integer page, Integer pageSize) {
        OsHeader osHeader = OsHeader.newBuilder()
                .setAppKey("254184f13a6b17d99163b58829af4af7")
                .setSecret("xcshZ4A0Wjrtu/kcd7bWCj8u9qLKyNvndqVp8HAKEu8=")
                .setApiId("api_2006")
                .build();

        //获取前一天日期字符串，例如********
        String logData = TimeUtils.format(TimeUtils.addDays(TimeUtils.nowTimestamp(), -1), "yyyyMMdd");

        QueryReq openApiReq = QueryReq.newBuilder()
                .setOsHeader(osHeader)
                .addReqs(OperatorVo.newBuilder().setField("record_date").setOperator("between").addAllValues(Arrays.asList(fromTime, toTime)))
                .addReqs(OperatorVo.newBuilder().setField("account_id").setOperator("in").addAllValues(accountIds))
                .addReqs(OperatorVo.newBuilder().setField("log_date").setOperator("=").addAllValues(Lists.newArrayList(logData)))
                .addAllOrders(Lists.newArrayList("account_id desc"))
                .setPageReq(PageReq.newBuilder().setPage(page).setPageSize(pageSize).build())
                .build();
        QueryResp starEventResp = serviceBlockingStub.query(openApiReq);
        List<MapValue> starGrpcResult = starEventResp.getRowsList();
        PageVo pageVo = starEventResp.getPageVo();
        List<StarEventBo> starEventBos = starGrpcResult.stream()
                .map(star -> om.convertValue(star.getValueMap(), StarEventBo.class))
                .collect(Collectors.toList());

        return PageResult.<StarEventBo>builder()
                .records(starEventBos)
                .total((int) pageVo.getTotalSize())
                .build();
    }

    public DailyStatBo getDailyCostChat(Long startTime, Long endTime, Integer accountId) {
        final StatReq req = StatReq.newBuilder()
                .setStartTime(startTime)
                .setEndTime(endTime)
                .addAllOcpxSalesTypes(OCPX_SALES_TYPES)
                .addAllSalesTypes(PLATFORM_SALES_TYPES)
                .setType(StatReq.Type.ACCOUNT)
                .setTimeType(StatReq.TimeType.HOUR)
                .addAccountIds(accountId)
                .build();
        StatChartReply statChartReply = cpmReportSvrProxy.getStatChart(req);
        StatChartBo statChart = StatConverter.MAPPER.grpcBo2Bo(statChartReply.getStatChart());
        List<BigDecimal> cost = statChart.getCost();
        List<Long> chargedCostMilli = cost
                .stream()
                .map(c -> c.multiply(BigDecimal.valueOf(100_000)).longValue())
                .collect(Collectors.toList());
        statChart.getChargedCostMilli().addAll(chargedCostMilli);
        return getDailyCostChat(startTime, endTime, statChart);
    }

    public List<StatBo> listPersonAnalysisStats(AllStatTargetParamBo param) {
        log.info("getAllPercentStatTargetsFromES param: {}", param);
        // 查询报表服务
        StatReq req = StatReq.newBuilder()
                .setStartTime(param.getFromDate().getTime())
                .setEndTime(param.getToDate().getTime())
                .addAllAccountIds(CollectionUtils.isEmpty(param.getAccountIds()) ? Collections.emptyList() : param.getAccountIds())
                .addAllCampaignIds(CollectionUtils.isEmpty(param.getCampaignIds()) ? Collections.emptyList() : param.getCampaignIds())
                .addAllUnitIds(CollectionUtils.isEmpty(param.getUnitIds()) ? Collections.emptyList() : param.getUnitIds())
                .addAllCreativeIds(CollectionUtils.isEmpty(param.getCreativeIds()) ? Collections.emptyList() : param.getCreativeIds())
                .addAllSalesTypes(CollectionUtils.isEmpty(param.getSalesTypes()) ? Collections.emptyList() : param.getSalesTypes())
                .setTimeType(StatReq.TimeType.ALL)
                .setType(StatReq.Type.forNumber(param.getType()))
                .setPn(0)
                .setPs(Integer.MAX_VALUE)
                .build();
        StatsReply reply;
        Cat.logEvent("getAllPercentStatTargetsFromES", "queryReportStats start");
        // Check if the query type is for creative stats
        boolean queryCreative = StatQueryType.CREATIVE.getCode().equals(param.getType());
        if (queryCreative) {
            // If yes, then get creative stats
            reply = cpmReportSvrProxy.listCreativeStats(req);
        } else {
            // If not, then get account stats
            reply = cpmReportSvrProxy.listAccountStats(req);
        }
        List<Stat> statList = reply.getStatsList();
        log.info("getAllPercentStatTargetsFromES after queryReportStats size {}", statList.size());
        if (CollectionUtils.isEmpty(statList)) {
            Cat.logEvent("getAllPercentStatTargetsFromES", "after queryReportStats is empty");
            return Collections.emptyList();
        }
        return statList
                .stream()
                .map(StatConverter.MAPPER::grpcBo2Bo)
                .collect(Collectors.toList());
    }

    public static <T> void handle(boolean condition, Consumer<T> consumer, T param) {
        if (condition) {
            consumer.accept(param);
        }
    }

    public static <T, U> void handle(boolean condition, BiConsumer<T, U> consumer, T param1, U param2) {
        if (condition) {
            consumer.accept(param1, param2);
        }
    }
}
