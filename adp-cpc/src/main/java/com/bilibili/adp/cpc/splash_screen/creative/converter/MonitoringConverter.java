package com.bilibili.adp.cpc.splash_screen.creative.converter;

import com.bilibili.adp.cpc.dao.querydsl.pos.LauCreativeMonitoringPo;
import com.bilibili.adp.cpc.splash_screen.creative.bos.SplashScreenMonitoring;
import com.bilibili.adp.launch.api.creative.dto.CpcCreativeMonitoringDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper
public interface MonitoringConverter {
    MonitoringConverter MAPPER = Mappers.getMapper(MonitoringConverter.class);

    SplashScreenMonitoring dto2Bo(CpcCreativeMonitoringDto dto);
    @Mapping(target = "mtime", ignore = true)
    @Mapping(target = "isDeleted", ignore = true)
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "ctime", ignore = true)
    LauCreativeMonitoringPo po(Integer unitId, Integer creativeId, Integer type, String url);
}
