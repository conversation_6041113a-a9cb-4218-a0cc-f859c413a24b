package com.bilibili.adp.cpc.splash_screen.creative.converter;

import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauCreativeQualificationPo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper
public interface QualificationConverter {
    QualificationConverter MAPPER = Mappers.getMapper(QualificationConverter.class);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "ctime", ignore = true)
    @Mapping(target = "mtime", ignore = true)
    LauCreativeQualificationPo po(Integer creativeId, Integer qualificationId);
}
