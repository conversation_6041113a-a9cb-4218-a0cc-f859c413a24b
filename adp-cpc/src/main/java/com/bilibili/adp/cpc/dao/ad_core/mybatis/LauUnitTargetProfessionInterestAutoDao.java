package com.bilibili.adp.cpc.dao.ad_core.mybatis;

import com.bilibili.adp.cpc.po.ad.LauUnitTargetProfessionInterestAutoPo;
import com.bilibili.adp.cpc.po.ad.LauUnitTargetProfessionInterestAutoPoExample;
import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface LauUnitTargetProfessionInterestAutoDao {
    long countByExample(LauUnitTargetProfessionInterestAutoPoExample example);

    int deleteByExample(LauUnitTargetProfessionInterestAutoPoExample example);

    int deleteByPrimaryKey(Integer id);

    int insertUpdate(LauUnitTargetProfessionInterestAutoPo record);

    int insertBatch(List<LauUnitTargetProfessionInterestAutoPo> records);

    int insertUpdateBatch(List<LauUnitTargetProfessionInterestAutoPo> records);

    int insert(LauUnitTargetProfessionInterestAutoPo record);

    int insertUpdateSelective(LauUnitTargetProfessionInterestAutoPo record);

    int insertSelective(LauUnitTargetProfessionInterestAutoPo record);

    List<LauUnitTargetProfessionInterestAutoPo> selectByExample(LauUnitTargetProfessionInterestAutoPoExample example);

    LauUnitTargetProfessionInterestAutoPo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") LauUnitTargetProfessionInterestAutoPo record, @Param("example") LauUnitTargetProfessionInterestAutoPoExample example);

    int updateByExample(@Param("record") LauUnitTargetProfessionInterestAutoPo record, @Param("example") LauUnitTargetProfessionInterestAutoPoExample example);

    int updateByPrimaryKeySelective(LauUnitTargetProfessionInterestAutoPo record);

    int updateByPrimaryKey(LauUnitTargetProfessionInterestAutoPo record);
}