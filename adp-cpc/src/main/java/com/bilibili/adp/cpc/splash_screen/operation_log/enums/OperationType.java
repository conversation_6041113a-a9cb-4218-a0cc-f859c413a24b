package com.bilibili.adp.cpc.splash_screen.operation_log.enums;

import org.springframework.util.Assert;

import java.util.Objects;

public class OperationType {
    public static final int INSERT = 0;
    public static final int DELETE = 1;
    public static final int UPDATE = 2;

    public static boolean isDelete(Integer x) {
        Assert.notNull(x, "操作类型不能为空");
        return Objects.equals(x, DELETE);
    }
}
