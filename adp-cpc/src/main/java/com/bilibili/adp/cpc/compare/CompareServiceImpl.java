package com.bilibili.adp.cpc.compare;

import lombok.RequiredArgsConstructor;
import org.javers.core.Javers;
import org.javers.core.diff.Diff;
import org.javers.core.diff.changetype.PropertyChange;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
@RequiredArgsConstructor
public class CompareServiceImpl implements CompareService {
    private final Javers javers;
    @Override
    public <T> List<PropertyChange> propertyChanges(T oldVersion, T newVersion) {
        Diff diff = javers.compare(oldVersion, newVersion);
        if (!diff.hasChanges()) {
            return Collections.emptyList();
        }
        return diff.getChangesByType(PropertyChange.class);
    }
}
