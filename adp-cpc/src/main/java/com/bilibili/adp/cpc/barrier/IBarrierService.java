package com.bilibili.adp.cpc.barrier;

import java.time.Duration;

public interface IBarrierService {
    String CAMPAIGN_CNT_INCR = "campaignCntIncr";
    String CAMPAIGN_CNT_DECR = "campaignCntDecr";
    String UNIT_CNT_INCR = "unitCntIncr";
    String UNIT_CNT_DECR = "unitCntDecr";
    String CREATIVE_CNT_INCR = "creativeCntIncr";
    String CREATIVE_CNT_DECR = "creativeCntDecr";
    Duration LAUNCH_COUNTER_DURATION = Duration.ofHours(1);

    // 如果障碍不存在, 新建一个, 返回true, 可以执行后续操作
    // 如果障碍存在, 返回false, 不可以执行后续操作
    boolean setBarrierIfAbsent(String key, Duration duration);
}
