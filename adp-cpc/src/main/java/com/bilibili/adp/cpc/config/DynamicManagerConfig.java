package com.bilibili.adp.cpc.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/4/10 13:59
 */
@Data
@Component
public class DynamicManagerConfig {
    @Value("${bvid.switch.env:prod}")
    private String env;

    /**
     * 动态列表并发调用开关
     */
    @Value("${dynamic.list.parallel.switch:0}")
    private Integer dynamicListParallelSwitch;

    /**
     * uat: uat-manager.bilibili.co
     */
    @Value("${dynamic.manager.host:https://manager.bilibili.co}")
    private String dynamicManagerHost;

    /**
     * 获取 up 的动态列表
     */
    @Value("${dynamic.byMid.url:/x/admin/dynamic/feed/commerce/fetch_dyn_list}")
    private String midDynamicsUrl;

    /**
     * 根据动态 id 或稿件 获取动态内容
     */
    @Value("${dynamic.fetchById.url:/x/admin/dynamic/feed/commerce/fetch_dyn}")
    private String fetchDynamicByIdUrl;

    /**
     * 获取动态内容(含封面)
     */
    @Value("${dynamic.dynContentsUrl.url:/x/admin/dynamic/feed/dyn_contents}")
    private String fetchDynContents;
}
