package com.bilibili.adp.cpc.splash_screen.creative.strategy.jump_url_macro;

import com.bilibili.adp.cpc.splash_screen.creative.strategy.bos.CreativeContext;
import com.bilibili.adp.common.Constants;
import com.bilibili.adp.common.util.biz.MgkLandingPageParser;
import com.bilibili.adp.cpc.utils.MgkLandingPageParserUtils;
import org.springframework.web.util.UriComponentsBuilder;

public abstract class JumpUrlMacroStrategy implements IJumpUrlMacroStrategy {

    @Override
    public String addMacro(CreativeContext context, String jumpUrl) {
        final UriComponentsBuilder uriComponentsBuilder = UriComponentsBuilder.fromUriString(jumpUrl);
        final boolean needAddMgkPageMacro = MgkLandingPageParserUtils.isH5(jumpUrl) || MgkLandingPageParserUtils.isMiniApp(jumpUrl);
        if (needAddMgkPageMacro) {
            uriComponentsBuilder.query(Constants.MGK_PAGE_MACRO_PARAM);
        }
        return uriComponentsBuilder.build().toUriString();
    }

    @Override
    public String removeMacro(String jumpUrl) {
        final UriComponentsBuilder uriComponentsBuilder = UriComponentsBuilder.fromUriString(jumpUrl);
        final boolean needRemoveMgkPageMacro = MgkLandingPageParserUtils.isH5(jumpUrl) || MgkLandingPageParserUtils.isMiniApp(jumpUrl);
        if (needRemoveMgkPageMacro) {
            uriComponentsBuilder.replaceQueryParam(Constants.MGK_PAGE_MACRO_PARAM);
        }
        return uriComponentsBuilder.build().toUriString();
    }
}
