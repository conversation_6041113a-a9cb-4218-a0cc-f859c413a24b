package com.bilibili.adp.cpc.wrapper.bos;

import com.bilibili.adp.cpc.core.bos.UnitBusinessCategoryBo;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class UnitBusinessCategoryWrapperBo {
    @ApiModelProperty("创意分类一级类目")
    private Integer firstId;
    @ApiModelProperty("创意分类二级类目")
    private Integer secondId;
    @ApiModelProperty("创意分类三级类目")
    private Integer thirdId;

    public UnitBusinessCategoryBo toCoreBo() {
        return UnitBusinessCategoryBo.builder()
                .firstId(firstId)
                .secondId(secondId)
                .thirdId(thirdId)
                .build();
    }

    public static UnitBusinessCategoryWrapperBo fromCoreBo(UnitBusinessCategoryBo bo) {
        if (Objects.isNull(bo)) return null;

        return UnitBusinessCategoryWrapperBo.builder()
                .firstId(bo.getFirstId())
                .secondId(bo.getSecondId())
                .thirdId(bo.getThirdId())
                .build();
    }
}
