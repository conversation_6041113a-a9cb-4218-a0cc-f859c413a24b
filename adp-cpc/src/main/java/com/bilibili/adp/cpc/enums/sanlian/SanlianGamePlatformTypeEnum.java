package com.bilibili.adp.cpc.enums.sanlian;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * @ClassName SanlianGamePlatformTypeEnum
 * <AUTHOR>
 * @Date 2024/4/4 4:54 下午
 * @Version 1.0
 * 版权来自 @see com.bilibili.adp.common.enums.GamePlatformTypeEnum
 **/
@AllArgsConstructor
@Getter
public enum SanlianGamePlatformTypeEnum {

    ANDROID(1, "安卓", 399),
    IOS(2, "IOS", 398);

    private Integer code;
    private String desc;
    private Integer targetId;

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public Integer getTargetId() { return targetId; }

    public static SanlianGamePlatformTypeEnum getByCode(int code) {
        for (SanlianGamePlatformTypeEnum bean : values()) {
            if (bean.getCode() == code) {
                return bean;
            }
        }

        // 兜底
        if (Objects.equals(code, 0)) {
            return SanlianGamePlatformTypeEnum.ANDROID;
        }

        throw new IllegalArgumentException("unknown GamePlatformTypeEnum code.");
    }

}
