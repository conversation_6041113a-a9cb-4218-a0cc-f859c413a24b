package com.bilibili.adp.cpc.automatic_rule.bos;

import com.bilibili.adp.cpc.automatic_rule.enums.rule.ExecuteLogic;
import com.bilibili.adp.cpc.automatic_rule.enums.rule.ExecuteTimeType;
import com.bilibili.adp.cpc.automatic_rule.enums.object.ObjectType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RuleDetailBo {
    private Long ruleId;
    private Integer ruleType;
    private String ruleName;
    /**
     * @see ExecuteLogic
     */
    private Integer executeLogic;
    private List<ConditionBo> conditions;
    private List<ActionBo> actions;
    /**
     * @see ObjectType
     */
    private Integer objectType;

    private List<Integer> objectIds;
    /**
     * @see ExecuteTimeType
     */
    private Integer executeTimeType;
    private List<Integer> dayOfWeek;
    private List<String> fixedTime;
    private List<String> periodTime;
    private Integer status;
}
