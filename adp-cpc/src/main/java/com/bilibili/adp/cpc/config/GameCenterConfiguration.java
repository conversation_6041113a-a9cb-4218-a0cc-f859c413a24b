package com.bilibili.adp.cpc.config;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
@Data
@NoArgsConstructor
public class GameCenterConfiguration {
    private static final String ALL_ON_SHELF_GAME_LISTE = "/list";
    private static final String CP_GAME_LIST = "/cp/game_list";
    private static final String ONLINE_GAME_INFOS = "/online_infos";
    private static final String GAME_INFO = "/info";
    private static final String CP_LIST = "/cp_list";
    private static final String OFFLINE_UO_GAME_LIST = "/offline_uo/game_list";
    private static final String MULTI_GET_GAME_BASE_INFO = "/multi_get_game_base_info";
    private static final String MULTI_GET_GAME_INFO = "/multi_get_game_info";
    private static final String GAME_MONITOR_PARAM = "game_base_id=&platform_type=&track_id=__TRACKID__&mid=__UID__&gamesdkuid=__GAMESDKUID__&device_type=__OS__&time=__TIME__&account_id=__ACCOUNTID__";
    @Value("${game.center.appKey:LFNqlJK7dQjen6uU}")
    private String appKey = "LFNqlJK7dQjen6uU";
    @Value("${game.center.secret:mSEivcNRsIntMhD0WRTTt4QOY8rootQi}")
    private String secret = "mSEivcNRsIntMhD0WRTTt4QOY8rootQi";
    @Value("${game.center.open.api.url:http://line1-game-open-api.biligame.net/game}")
    private String openApiUrl = "http://line1-game-open-api.biligame.net/game";
    @Value("${game.monitor.domain:https://game-attribute.biligame.com/ad/api/click}")
    private String gameMonitorUrl = "https://game-attribute.biligame.com/ad/api/click";

    public String getGameMonitorUrl() {
        return this.gameMonitorUrl + '?' + "game_base_id=&platform_type=&track_id=__TRACKID__&mid=__UID__&gamesdkuid=__GAMESDKUID__&device_type=__OS__&time=__TIME__&account_id=__ACCOUNTID__";
    }

    public String getAllOnShelfGameList() {
        return this.getOpenApiUrl().concat("/list");
    }

    public String getCpList() {
        return this.getOpenApiUrl().concat("/cp_list");
    }

    public String getOfflineUoGameList() {
        return this.getOpenApiUrl().concat("/offline_uo/game_list");
    }

    public String getQueryCpGameListUrl() {
        return this.getOpenApiUrl().concat("/cp/game_list");
    }

    public String getQueryOnlineGameInfosUrl() {
        return this.getOpenApiUrl().concat("/online_infos");
    }

    public String getQueryGameInfoUrl() {
        return this.getOpenApiUrl().concat("/info");
    }

    public String getMuiltGetGameBaseInfoUrl() {
        return this.getOpenApiUrl().concat("/multi_get_game_base_info");
    }

    public String getMuiltGetGameInfoUrl() {
        return this.getOpenApiUrl().concat("/multi_get_game_info");
    }

}
