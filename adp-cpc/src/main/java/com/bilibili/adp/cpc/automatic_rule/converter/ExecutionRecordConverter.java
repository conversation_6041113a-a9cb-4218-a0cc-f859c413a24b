package com.bilibili.adp.cpc.automatic_rule.converter;

import com.bilibili.adp.cpc.automatic_rule.bos.ExecutionRecordBo;
import com.bilibili.adp.cpc.dao.querydsl.pos.LauAutomaticRuleExecutionRecordPo;
import com.bilibili.adp.cpc.dao.querydsl.pos.LauAutomaticRulePo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface ExecutionRecordConverter {
    ExecutionRecordConverter MAPPER = Mappers.getMapper(ExecutionRecordConverter.class);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "isDeleted", ignore = true)
    @Mapping(target = "ctime", ignore = true)
    @Mapping(target = "mtime", ignore = true)
    @Mapping(target = "triggerConditions", source = "triggerConditions")
    @Mapping(target = "conditions", source = "conditions")
    @Mapping(target = "executeTime", expression = "java(java.sql.Timestamp.valueOf(bo.getExecuteTime()))")
    LauAutomaticRuleExecutionRecordPo bo2Po(ExecutionRecordBo bo, String triggerConditions, String conditions);

    @Mapping(target = "accountId", source = "rulePo.accountId")
    @Mapping(target = "ruleId", source = "rulePo.ruleId")
    @Mapping(target = "triggerConditions", ignore = true)
    @Mapping(target = "conditions", source = "conditions")
    @Mapping(target = "executeTime", expression = "java(recordPo.getExecuteTime().toLocalDateTime())")
    ExecutionRecordBo compose(LauAutomaticRuleExecutionRecordPo recordPo, LauAutomaticRulePo rulePo, List<String> conditions);
}
