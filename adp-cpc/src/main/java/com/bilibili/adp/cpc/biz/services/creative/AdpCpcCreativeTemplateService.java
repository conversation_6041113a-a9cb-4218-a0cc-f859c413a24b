package com.bilibili.adp.cpc.biz.services.creative;

import com.bapis.ad.location.FilterGivenTemplateIdsResponse;
import com.bapis.ad.location.ResourceEntity;
import com.bapis.ad.location.ResourceFilterRequest;
import com.bapis.ad.location.ResourceServiceGrpc;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.biz.services.creative.bos.AddCreativeTemplateContextBo;
import com.bilibili.adp.cpc.biz.services.creative.bos.CreativeTemplateMetaBo;
import com.bilibili.adp.cpc.biz.services.unit.api.ICpcUnitService;
import com.bilibili.adp.cpc.core.constants.BusinessDomain;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauCreativeTemplatePo;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauUnitCreativePo;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauUnitPo;
import com.bilibili.adp.cpc.enums.AdpVersion;
import com.bilibili.adp.cpc.enums.LaunchStatus;
import com.bilibili.adp.cpc.utils.CommonFuncs;
import com.bilibili.adp.cpc.utils.RecDiffResult;
import com.bilibili.adp.launch.api.launch.dto.GetBidCostParam;
import com.bilibili.adp.launch.api.launch.dto.LauCreativeTemplateBo;
import com.bilibili.adp.launch.biz.service.resource.LaunchResourceService;
import com.bilibili.adp.resource.api.bus_mark_rule.IBusMarkRuleService;
import com.bilibili.adp.resource.api.common.BusMarkRuleAdSysEnum;
import com.bilibili.bjcom.querydsl.BaseQueryFactory;
import com.bilibili.location.api.bus_mark.dto.BusMarkDto;
import com.bilibili.location.api.service.ICardTypeService;
import com.bilibili.location.api.service.ITemplateService;
import com.bilibili.location.api.template.dto.TemplateDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.bilibili.adp.common.Constants.BIZ_STATUS_OK;
import static com.bilibili.adp.common.Constants.BIZ_STATUS_UNDER_RESERVED_PRICE;
import static com.bilibili.adp.cpc.dao.ad_core.querydsl.QLauCreativeTemplate.lauCreativeTemplate;
import static com.bilibili.adp.cpc.dao.ad_core.querydsl.QLauProgrammaticCreativeDetail.lauProgrammaticCreativeDetail;
import static com.bilibili.adp.cpc.dao.config.PlatformMySqlConfig.AD_CORE_BQF;
import static com.bilibili.adp.cpc.dao.ad_core.querydsl.QLauUnit.lauUnit;
import static com.bilibili.adp.cpc.dao.ad_core.querydsl.QLauUnitCreative.lauUnitCreative;

@Slf4j
@Service
@RequiredArgsConstructor
public class AdpCpcCreativeTemplateService {
    public static final String ID = "AdpCpcCreativeTemplateService";

    @Resource(name = AD_CORE_BQF)
    private BaseQueryFactory adCoreBqf;
    private final LaunchResourceService launchResourceService;
    private final ICpcUnitService cpcUnitService;
    private final IBusMarkRuleService busMarkRuleService;
    private final ICardTypeService cardTypeService;
    private final ITemplateService templateService;
    private final ResourceServiceGrpc.ResourceServiceBlockingStub stub;

    public List<LauCreativeTemplatePo> list(Collection<Integer> creativeIds) {
        final List<Long> longCreativeIds = creativeIds.stream()
                .map(Integer::longValue)
                .collect(Collectors.toList());
        return adCoreBqf.selectFrom(lauCreativeTemplate)
                .where(lauCreativeTemplate.creativeId.in(longCreativeIds))
                .fetch();
    }

    public List<LauUnitCreativePo> listCreatives(Integer accountId, Collection<Integer> unitIds, Collection<Integer> creativeIds) {
        return adCoreBqf.from(lauUnitCreative)
                .whereIfNotNull(accountId, lauUnitCreative.accountId::eq)
                .whereIfNotEmpty(unitIds, lauUnitCreative.unitId::in)
                .whereIfNotEmpty(creativeIds, lauUnitCreative.creativeId::in)
                .where(lauUnitCreative.status.in(LaunchStatus.START.getCode(), LaunchStatus.STOP.getCode()))
                .select(lauUnitCreative.isProgrammatic,
                        lauUnitCreative.creativeId,
                        lauUnitCreative.unitId,
                        lauUnitCreative.accountId,
                        lauUnitCreative.adpVersion,
                        lauUnitCreative.preferScene,
                        lauUnitCreative.templateGroupId)
                .fetch(LauUnitCreativePo.class);
    }

    private Map<Integer, Integer> templateId2CreativeStyle() {
        final List<Integer> allTemplateIds = templateService.getAllValid()
                .stream()
                .map(TemplateDto::getTemplateId)
                .collect(Collectors.toList());
        return templateService.getValidTemplatesInIds(allTemplateIds)
                .stream()
                .collect(Collectors.toMap(TemplateDto::getTemplateId, TemplateDto::getFallBackCreativeStyle));
    }

    public void saveCreativeTemplateByAccountIds(List<Integer> accountIds) {
        final Map<Integer, Integer> templateId2CreativeStyleMap = templateId2CreativeStyle();
        for (Integer accountId : accountIds) {
            try {
                final List<Integer> supportingStoryBusMarkIds = supportingStoryBusMarkIds(accountId);
                final List<LauUnitCreativePo> creativePos = listCreatives(accountId, null, null);
                saveCreativeTemplate(creativePos, supportingStoryBusMarkIds, templateId2CreativeStyleMap);
            } catch (Throwable t) {
                log.error(ID, t);
            }
        }
    }

    private List<Integer> supportingStoryBusMarkIds(Integer accountId) {
        final List<BusMarkDto> busMarks = busMarkRuleService.getValidBusMarkInAccount(accountId, BusMarkRuleAdSysEnum.CPC);
        return busMarks.stream()
                .filter(x -> Objects.equals(x.getId(), 72) || Objects.equals(x.getId(), 74))
                .map(BusMarkDto::getId)
                .collect(Collectors.toList());
    }

    private void saveCreativeTemplate(List<LauUnitCreativePo> creativePos, List<Integer> supportingStoryBusMarkIds, Map<Integer, Integer> templateId2CreativeStyleMap) {
        if (CollectionUtils.isEmpty(creativePos)) return;

        final List<Integer> unitIds = creativePos.stream()
                .map(LauUnitCreativePo::getUnitId)
                .distinct()
                .collect(Collectors.toList());
        final Map<Integer, LauUnitPo> unitMap = adCoreBqf.from(lauUnit)
                .where(lauUnit.unitId.in(unitIds))
                .select(lauUnit.accountId,
                        lauUnit.unitId,
                        lauUnit.promotionPurposeType,
                        lauUnit.businessDomain,
                        lauUnit.salesType,
                        lauUnit.ocpcTarget,
                        lauUnit.costPrice)
                .fetch(LauUnitPo.class)
                .stream()
                .collect(Collectors.toMap(LauUnitPo::getUnitId, Function.identity()));
        final Map<Long, List<LauCreativeTemplatePo>> creativeTemplateMap = adCoreBqf.from(lauCreativeTemplate)
                .where(lauCreativeTemplate.unitId.in(unitIds))
                .select(lauCreativeTemplate.id,
                        lauCreativeTemplate.accountId,
                        lauCreativeTemplate.creativeId,
                        lauCreativeTemplate.unitId,
                        lauCreativeTemplate.templateId,
                        lauCreativeTemplate.slotGroupId,
                        lauCreativeTemplate.creativeStyle,
                        lauCreativeTemplate.busMarkId,
                        lauCreativeTemplate.bizStatus,
                        lauCreativeTemplate.reservedPrice
                ).fetch(LauCreativeTemplatePo.class)
                .stream()
                .collect(Collectors.groupingBy(LauCreativeTemplatePo::getCreativeId));
        final List<Integer> progCreativeIds = creativePos.stream()
                .filter(x -> Objects.equals(x.getIsProgrammatic(), 1))
                .map(LauUnitCreativePo::getCreativeId)
                .collect(Collectors.toList());
        final Map<Integer, List<Integer>> progCreativeId2TemplateGroupIds;
        if (CollectionUtils.isEmpty(progCreativeIds)) {
            progCreativeId2TemplateGroupIds = Collections.emptyMap();
        } else {
            progCreativeId2TemplateGroupIds = adCoreBqf.from(lauProgrammaticCreativeDetail)
                    .where(lauProgrammaticCreativeDetail.creativeId.in(progCreativeIds))
                    .select(lauProgrammaticCreativeDetail.creativeId, lauProgrammaticCreativeDetail.templateGroupId)
                    .distinct()
                    .fetch()
                    .stream()
                    .collect(Collectors.groupingBy(x -> x.get(lauProgrammaticCreativeDetail.creativeId), Collectors.mapping(x -> x.get(lauProgrammaticCreativeDetail.templateGroupId), Collectors.toList())));
        }
        cardTypeService.getAllCardTypeSupportStyles();

        for (LauUnitCreativePo creativePo : creativePos) {
            try {
                final LauUnitPo unitPo = unitMap.get(creativePo.getUnitId());
                if (Objects.isNull(unitPo)) continue;

                final List<LauCreativeTemplatePo> creativeTemplatePos = creativeTemplateMap.get(creativePo.getCreativeId().longValue());
                saveCreativeTemplate(unitPo, creativePo, progCreativeId2TemplateGroupIds.get(creativePo.getCreativeId()), creativeTemplatePos, supportingStoryBusMarkIds, templateId2CreativeStyleMap);
            } catch (Throwable t) {
                log.error(ID, t);
            }
        }
    }

    private void saveCreativeTemplate(LauUnitPo unitPo, LauUnitCreativePo creativePo, List<Integer> progTemplateGroupIds, List<LauCreativeTemplatePo> creativeTemplatePos, List<Integer> availableStoryBusMarkIds, Map<Integer, Integer> templateId2CreativeStyleMap) {
        if (AdpVersion.isLegacy(creativePo.getAdpVersion())) return;

        if (!Objects.equals(unitPo.getBusinessDomain(), BusinessDomain.CPC)) return;

        final boolean isProgrammatic = !CollectionUtils.isEmpty(progTemplateGroupIds);
        final ResourceFilterRequest.Builder requestBuilder = ResourceFilterRequest.newBuilder()
                .setAccountId(unitPo.getAccountId())
                .setSalesType(unitPo.getSalesType())
                .setIsCpa(Utils.isPositive(unitPo.getOcpcTarget()))
                .setPromotionPurposeType(unitPo.getPromotionPurposeType())
                .setIsProgrammatic(isProgrammatic)
                .setIsPreferScene(Utils.isPositive(creativePo.getPreferScene()))
                .addAllScenes(Collections.emptyList());
        if (isProgrammatic) {
            requestBuilder.addAllTemplateGroupIds(progTemplateGroupIds);
        } else if (Utils.isPositive(creativePo.getTemplateGroupId())) {
            requestBuilder.addTemplateGroupIds(creativePo.getTemplateGroupId());
        } else {
            return;
        }
        final ResourceFilterRequest request = requestBuilder.build();
        final FilterGivenTemplateIdsResponse response = stub.filterGivenTemplateGroupIds(request);
        final List<ResourceEntity> entities = response.getEntitiesList();
        final List<LauCreativeTemplatePo> pos = entities.stream()
                .map(x -> {
                    final LauCreativeTemplatePo po = new LauCreativeTemplatePo();
                    po.setSlotGroupId(x.getSlotGroupId());
                    po.setTemplateId(x.getTemplateId());
                    po.setAccountId(unitPo.getAccountId());
                    po.setUnitId(unitPo.getUnitId());
                    po.setCreativeId(creativePo.getCreativeId().longValue());
                    if (launchResourceService.isSpecialDynamicExclusive(x.getTemplateId())) {
                        po.setBusMarkId(launchResourceService.getSpecialDynamicBusMark());
                    } else if (launchResourceService.isStory(x.getSlotGroupId())) {
                        if (CollectionUtils.isEmpty(availableStoryBusMarkIds)) {
                            po.setBusMarkId(launchResourceService.getFallBackStoryBusMarkId());
                        } else {
                            po.setBusMarkId(availableStoryBusMarkIds.get(0));
                        }
                    }
                    po.setCreativeStyle(Optional.ofNullable(templateId2CreativeStyleMap.get(x.getTemplateId())).orElse(0));
                    final Integer reservedPrice = Optional.ofNullable(cpcUnitService.getLowestCost(GetBidCostParam.builder()
                            .accountId(unitPo.getAccountId())
                            .slotGroupId(x.getSlotGroupId())
                            .launchType(unitPo.getPromotionPurposeType())
                            .salesType(unitPo.getSalesType())
                            .build()))
                            .orElse(0);
                    po.setReservedPrice(reservedPrice);

                    if (unitPo.getIsNoBid() != null && Utils.isPositive(unitPo.getIsNoBid())) {
                        po.setBizStatus(BIZ_STATUS_OK);
                    } else {
                        po.setBizStatus(unitPo.getCostPrice() < reservedPrice ? BIZ_STATUS_UNDER_RESERVED_PRICE : BIZ_STATUS_OK);
                    }
                    return po;
                }).collect(Collectors.toList());

        final RecDiffResult<LauCreativeTemplatePo, Long> result = CommonFuncs.recDiff(creativeTemplatePos, pos, this::genKey, LauCreativeTemplatePo::getId, LauCreativeTemplatePo::setId);
        if (!CollectionUtils.isEmpty(result.getNewRecords())) {
            adCoreBqf.insert(lauCreativeTemplate).insertBeans(result.getNewRecords());
        }
        if (!CollectionUtils.isEmpty(result.getChangedRecords())) {
            adCoreBqf.update(lauCreativeTemplate).updateBeans(result.getChangedRecords());
        }
        if (!CollectionUtils.isEmpty(result.getOfflineRecordKeys())) {
            adCoreBqf.delete(lauCreativeTemplate)
                    .where(lauCreativeTemplate.id.in(result.getOfflineRecordKeys()))
                    .execute();
        }
    }

    private String genKey(LauCreativeTemplatePo po) {
        return po.getCreativeId() + "-" + po.getSlotGroupId();
    }

    public LauCreativeTemplateBo from(int slotGroupId, int templateId) {
        return LauCreativeTemplateBo.builder()
                .slotGroupId(slotGroupId)
                .templateId(templateId)
                .build();
    }

    public LauCreativeTemplateBo from(int slotGroupId, int templateId, Integer busMarkId, Integer creativeStyle) {
        return LauCreativeTemplateBo.builder()
                .slotGroupId(slotGroupId)
                .templateId(templateId)
                .busMarkId(busMarkId)
                .creativeStyle(creativeStyle)
                .build();
    }

    public void addCreativeTemplates(AddCreativeTemplateContextBo ctx, Collection<Integer> creativeIds, Collection<LauCreativeTemplatePo> existingPos, CreativeTemplateMetaBo meta) {
        Assert.notNull(ctx, "上下文不能为空");
        Assert.notNull(meta, "元数据不能为空");
        Assert.isTrue(Utils.isPositive(meta.getTemplateId()), "模板数据不能为空");
        Assert.isTrue(Utils.isPositive(meta.getSlotGroupId()), "广告位组不能为空");
        Assert.isTrue(Utils.isPositive(meta.getBusMarkId()), "商业标不能为空");
        Assert.isTrue(Utils.isPositive(meta.getCreativeStyle()), "创意形态不能为空");
        final Map<Integer, LauCreativeTemplatePo> map0 = existingPos.stream()
                .collect(Collectors.toMap(x -> x.getCreativeId().intValue(), Function.identity(), (x, y) -> y));
        final Map<String, LauCreativeTemplatePo> map1 = existingPos.stream()
                .collect(Collectors.toMap(this::uk, Function.identity(), (x, y) -> y));
        final List<LauCreativeTemplatePo> pos = new ArrayList<>();
        for (Integer creativeId : creativeIds) {
            final LauCreativeTemplatePo po0 = map0.get(creativeId);
            if (Objects.isNull(po0)) {
                ctx.getNoOtherTemplateFilter().add(creativeId);
                continue;
            }
            final LauCreativeTemplatePo po1 = map1.get(uk(creativeId, meta.getSlotGroupId()));
            if (Objects.nonNull(po1)) {
                ctx.getDataExistingFilter().add(creativeId);
                continue;
            }
            ctx.getPassedCreativeIds().add(creativeId);
            final LauCreativeTemplatePo po = new LauCreativeTemplatePo();
            po.setAccountId(po0.getAccountId());
            po.setUnitId(po0.getUnitId());
            po.setCreativeId(po0.getCreativeId());
            po.setSlotGroupId(meta.getSlotGroupId());
            po.setTemplateId(meta.getTemplateId());
            po.setCreativeStyle(meta.getCreativeStyle());
            po.setBusMarkId(meta.getBusMarkId());
            pos.add(po);
        }
        adCoreBqf.insert(lauCreativeTemplate).insertBeans(pos);
    }

    private String uk(Integer creativeId, Integer slotGroupId) {
        return creativeId + "-" + slotGroupId;
    }

    private String uk(LauCreativeTemplatePo po) {
        return uk(po.getCreativeId().intValue(), po.getSlotGroupId());
    }

    public Collection<LauCreativeTemplateBo> mockCreativeTemplates(Collection<Integer> creativeIds, int templateId, int slotGroupId) {
        return mockCreativeTemplates(null, creativeIds, null, from(slotGroupId, templateId));
    }

    public Collection<LauCreativeTemplateBo> mockCreativeTemplates(Collection<Integer> unitIds, Collection<Integer> creativeIds, Map<Integer, Integer> unitPriceMap, LauCreativeTemplateBo target) {
        if (CollectionUtils.isEmpty(unitIds) && CollectionUtils.isEmpty(creativeIds)) return Collections.emptyList();
        
        final int slotGroupId = target.getSlotGroupId();
        final int templateId = target.getTemplateId();
        final List<Long> longCreativeIds;
        if (CollectionUtils.isEmpty(creativeIds)) {
            longCreativeIds = Collections.emptyList();
        } else {
            longCreativeIds = creativeIds.stream()
                    .map(Integer::longValue)
                    .collect(Collectors.toList());
        }

        final List<LauCreativeTemplateBo> creativeTemplates = adCoreBqf.selectFrom(lauCreativeTemplate)
                .whereIfNotEmpty(longCreativeIds, lauCreativeTemplate.creativeId::in)
                .whereIfNotNull(unitIds, lauCreativeTemplate.unitId::in)
                .fetch(LauCreativeTemplateBo.class);

        final Set<Long> mockedCreativeIds = new HashSet<>();
        final Map<Integer, LauCreativeTemplateBo> map = new HashMap<>();
        creativeTemplates.forEach(x -> {
            if (Objects.equals(x.getSlotGroupId(), slotGroupId)) {
                mockedCreativeIds.add(x.getCreativeId());
            }
            map.put(x.getCreativeId().intValue(), x);
        });

        final List<LauCreativeTemplateBo> list = new LinkedList<>();
        map.values().forEach(x -> {
            final Long cid = x.getCreativeId();
            if (mockedCreativeIds.contains(cid)) return;

            x.setId(null);
            x.setTemplateId(templateId);
            x.setSlotGroupId(slotGroupId);
            if (Objects.nonNull(target.getCreativeStyle())) {
                x.setCreativeStyle(target.getCreativeStyle());
            }
            if (Objects.nonNull(target.getBusMarkId())) {
                x.setBusMarkId(target.getBusMarkId());
            }
            if (!CollectionUtils.isEmpty(unitPriceMap) && Objects.nonNull(target.getReservedPrice())) {
                final Integer price = unitPriceMap.get(x.getUnitId());
                if (Objects.nonNull(price)) {
                    x.setReservedPrice(target.getReservedPrice());
                    x.setReserveRuleId(0);
                    if (target.getReservedPrice() <= price) {
                        x.setBizStatus(0);
                    } else {
                        x.setBizStatus(1);
                    }
                }
            }
            list.add(x);
        });
        return list;
    }
}
