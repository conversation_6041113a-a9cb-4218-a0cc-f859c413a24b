package com.bilibili.adp.cpc.splash_screen.unit.converter;

import com.bilibili.adp.cpc.splash_screen.unit.bos.Target;
import com.bilibili.adp.resource.api.crowd_pack.CrowdPackDto;
import com.bilibili.adp.resource.api.target_lau.dto.TargetTreeDto;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
@Mapper
public interface TargetConverter {
    TargetConverter MAPPER = Mappers.getMapper(TargetConverter.class);
    Target targetTree2Target(TargetTreeDto targetTreeDto);

    Target crowdPack2Target(CrowdPackDto crowdPackDto);
}
