package com.bilibili.adp.cpc.biz.services.common;

import com.bilibili.adp.cpc.biz.services.common.bos.BFSInfoBo;
import com.google.common.io.ByteStreams;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.codec.digest.HmacAlgorithms;
import org.apache.commons.codec.digest.HmacUtils;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.Base64Utils;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Objects;

import static java.nio.charset.StandardCharsets.UTF_8;

@Service
@Slf4j
public class SimpleBFSService {
    @Value("${bfs.host}")
    private String domain;
    @Value("${bfs.accessKey}")
    private String accessKey;
    @Value("${bfs.accessSecret}")
    private String accessSecret;
    @Value("${bfs.bucketname}")
    private String bucketName;
    @Value("${bfs.default.ext:unknown}")
    private String defaultExt;

    /**
     * 防御性，对图片做大小限制
     **/
    private static final long MAX_SOURCE_IMAGE_SIZE = 20 * 1024L * 1024L;


    @Resource(name = "okHttpClient")
    private OkHttpClient client;


    public BFSInfoBo upload(String dir, String mimeType, byte[] bytes, String ext) {
        final String md5 = DigestUtils.md5Hex(bytes);
        final String name = dir + "/" + md5 + "." + ext;
        final String requestUrl = buildRequestUrl(name);
        final Request request = new Request.Builder()
                .header(HttpHeaders.CONNECTION, "close")
                .header(HttpHeaders.AUTHORIZATION, buildAuth(name))
                .header(HttpHeaders.CONTENT_TYPE, mimeType)
                .header(HttpHeaders.HOST, domain)
                .header(HttpHeaders.DATE, ZonedDateTime.now(ZoneId.of("GMT")).format(DateTimeFormatter.RFC_1123_DATE_TIME))
                .url(requestUrl)
                .put(RequestBody.create(null, bytes))
                .build();
        try (final Response response = client.newCall(request).execute()) {
            Assert.isTrue(response.code() == 200, "上传图片至BFS返回码错误:" + response.code() + "-" + response.message());
            final String url = response.header(HttpHeaders.LOCATION).replace("http", "https");
            final String sha1 = response.header(HttpHeaders.ETAG);
            return BFSInfoBo.builder()
                    .url(url)
                    .sha1(sha1)
                    .md5(md5)
                    .build();
        } catch (IOException e) {
            throw new IllegalArgumentException("上传图片至BFS失败: " + e.getLocalizedMessage());
        }
    }

    public String getExt(String fileName) {
        if (Objects.isNull(fileName)) return defaultExt;

        final String[] splits = fileName.split("\\.");
        if (splits.length == 0) return defaultExt;

        return splits[splits.length - 1];
    }

    private String buildAuth(String name) {
        final long ts = System.currentTimeMillis()/1000;
        final String text = "PUT" + "\n" + bucketName + "\n" + name + "\n" + ts + "\n";
        return accessKey +
                ":" +
                getHmacSha1(accessSecret, text) +
                ":" +
                ts;
    }

    private String getHmacSha1(String secret, String text) {
        final byte[] bytes = new HmacUtils(HmacAlgorithms.HMAC_SHA_1, secret).hmac(text.getBytes(UTF_8));
        return Base64Utils.encodeToString(bytes);
    }

    private String buildRequestUrl(String name) {
        return "http://" + domain + "/bfs/" + bucketName + "/" + name;
    }

    @SneakyThrows
    public static String downloadPicAndGenerateMd5(String url) {
        HttpURLConnection httpUrlConnection = getHttpUrlConnection(url);
        Assert.isTrue(httpUrlConnection.getContentLengthLong() <= MAX_SOURCE_IMAGE_SIZE, "图片尺寸过大，无法处理！");
        String md5 = "";
        try (InputStream in = ByteStreams.limit(httpUrlConnection.getInputStream(), MAX_SOURCE_IMAGE_SIZE)) {
            byte[] originalBytes = IOUtils.toByteArray(in);
            md5 = DigestUtils.md5Hex(originalBytes);
        }
        return md5;
    }

    private static HttpURLConnection getHttpUrlConnection(String url) throws IOException {
        Assert.hasText(url, "下载图片url不可为空");
        URL resourceURL = new URL(url);
        HttpURLConnection httpConnection = (HttpURLConnection) resourceURL.openConnection();
        httpConnection.setRequestProperty("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
        httpConnection.setConnectTimeout(5 * 1000);
        httpConnection.connect();
        return httpConnection;
    }
}
