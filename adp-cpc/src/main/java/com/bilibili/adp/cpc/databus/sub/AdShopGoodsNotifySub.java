package com.bilibili.adp.cpc.databus.sub;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bilibili.adp.cpc.biz.bos.WarpDatabusProperty;
import com.bilibili.adp.cpc.biz.constants.Constants;
import com.bilibili.adp.cpc.biz.services.unit.api.ICpcUnitService;
import com.bilibili.adp.cpc.databus.bos.AdGoodsNotifyMQDto;
import com.bilibili.adp.util.common.AdpCatUtils;
import com.bilibili.warp.databus.AckableMessage;
import com.bilibili.warp.databus.DatabusProperty;
import com.bilibili.warp.databus.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.tika.utils.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * @ClassName AdShopGoodsNotifySub
 * <AUTHOR>
 * @Date 2024/12/11 7:47 下午
 * @Version 1.0
 **/
@Component
@Slf4j
public class AdShopGoodsNotifySub implements MessageListener {

    private final String topic;
    private final String group;

    private final String DATABUS_CONFIG_KEY = "ad-shop-goods-notify";

    @Autowired
    private ICpcUnitService cpcUnitService;

    @Override
    public String topic() {
        return this.topic;
    }

    @Override
    public String group() {
        return this.group;
    }

    public AdShopGoodsNotifySub(WarpDatabusProperty warpDatabusProperty) {
        // 注入DatabusProperties，此处的ALIAS常量即配置文件中填写的别名，是topic的上一级配置键
        // pub场景同理，在使用DatabusTemplate的地方注入DatabusProperties来获取
        Map<String, DatabusProperty> properties = warpDatabusProperty.getProperties();
        DatabusProperty property = properties.get(DATABUS_CONFIG_KEY);
        log.info(DATABUS_CONFIG_KEY + " property={}", JSONObject.toJSONString(property));
        this.topic = property.getTopic();
        this.group = property.getSub().getGroup();
    }

    @Override
    public void onMessage(AckableMessage message) {
        String val = new String(message.payload());
        log.info("AdShopGoodsNotifySub ackableMessage={}", val);
        try {
            handleMsg(val);
            message.ack();
        } catch(Exception e) {
            log.error("AdShopGoodsNotifySub consumer error, msg:{}, exception:{}", val, ExceptionUtils.getStackTrace(e));
        }
    }

    private void handleMsg(String msg) {
        AdGoodsNotifyMQDto notifyMQDto = JSON.parseObject(msg, AdGoodsNotifyMQDto.class);
        Integer goodsId = notifyMQDto.getOuterId();
        AdpCatUtils.newTransaction(Constants.CAT_TYPE_DATABUS, DATABUS_CONFIG_KEY, transaction -> {
            cpcUnitService.pauseShopGoodsUnitListByGoodsId(goodsId);
        });
    }
}
