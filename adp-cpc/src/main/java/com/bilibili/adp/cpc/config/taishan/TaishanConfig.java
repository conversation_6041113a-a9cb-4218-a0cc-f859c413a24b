/*
 * Copyright (c) 2015-2023 BiliBili Inc.
 */

package com.bilibili.adp.cpc.config.taishan;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import pleiades.venus.starter.paladin.PaladinPropertySourceFactory;

import java.util.HashMap;
import java.util.Map;

@Configuration
@PropertySource(value = "classpath:taishan.yaml", factory = PaladinPropertySourceFactory.class)
public class TaishanConfig {
    public static final String TAISHAN_MAP = "taishan_map";
    @Bean(TAISHAN_MAP)
    @ConfigurationProperties("taishan")
    public Map<String, String> getMap() {
        return new HashMap<>();
    }
}
