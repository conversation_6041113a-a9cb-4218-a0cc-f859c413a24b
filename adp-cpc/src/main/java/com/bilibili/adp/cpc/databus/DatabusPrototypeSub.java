package com.bilibili.adp.cpc.databus;

import com.bilibili.warp.databus.AckableMessage;
import com.bilibili.warp.databus.DatabusProperty;
import com.bilibili.warp.databus.MessageListener;
import com.bilibili.warp.spring.boot.autoconfigure.databus.DatabusProperties;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

@Slf4j
public abstract class DatabusPrototypeSub<T> implements MessageListener {
    private final String topic;
    private final String group;
    private final Class<T> clazz;
    private final ObjectMapper objectMapper;

    public DatabusPrototypeSub(String key, Class<T> clazz, DatabusProperties databusProperties, ObjectMapper objectMapper) {
        this.clazz = clazz;
        this.objectMapper = objectMapper;
        final DatabusProperty databusProperty = DatabusUtils.getDatabusProperty(databusProperties, key);
        Assert.notNull(databusProperty.getSub(), "未找到消费者配置");
        topic = databusProperty.getTopic();
        group = databusProperty.getSub().getGroup();
        Assert.isTrue(StringUtils.hasText(group), "消费者分组不能为空");
    }

    @Override
    public String topic() {
        return topic;
    }

    @Override
    public String group() {
        return group;
    }

    @Override
    public void onMessage(final AckableMessage message) {
        try {
            final T msgBo = objectMapper.readValue(message.payload(), clazz);
            handleMessage(msgBo);
        } catch (Throwable t) {
            log.error("消费失败", t);
        } finally {
            message.ack();
        }
    }

    public abstract void handleMessage(T msgBo);
}
