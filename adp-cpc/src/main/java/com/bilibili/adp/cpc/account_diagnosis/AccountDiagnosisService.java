package com.bilibili.adp.cpc.account_diagnosis;

import com.bapis.bcg.ones.minos.*;
import com.bilibili.adp.cpc.proxy.TestBusinessOnesMinosProxy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Service;
import pleiades.venus.starter.rpc.client.RPCClient;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class AccountDiagnosisService {
    @Resource
    private TestBusinessOnesMinosProxy testBusinessOnesMinosProxy;

    public SummaryResp summary(SummaryReq req) {
        return testBusinessOnesMinosProxy.summary(req);
    }

    public AbnormalUnitLineChartResp abnormalUnitLineChart(AbnormalUnitLineChartReq req) {
        return testBusinessOnesMinosProxy.abnormalUnitLineChart(req);
    }

    public AbnormalUnitListResp abnormalUnitList(AbnormalUnitListReq req) {
        return testBusinessOnesMinosProxy.abnormalUnitList(req);

    }

    public AbnormalUnitTypeResp abnormalUnitType(AbnormalUnitTypeReq req) {
        return testBusinessOnesMinosProxy.abnormalUnitType(req);

    }

    public OcpcTargetTypeResp ocpcTargetType(OcpcTargetTypeReq req) {
        return testBusinessOnesMinosProxy.ocpcTargetType(req);

    }

    public LaunchHistoryResp launchHistory(LaunchHistoryReq req) {
        return testBusinessOnesMinosProxy.launchHistory(req);

    }

    public AbnormalUnitIdResp abnormalUnitId(AbnormalUnitIdReq req) {
        return testBusinessOnesMinosProxy.abnormalUnitId(req);
    }
}
