package com.bilibili.adp.cpc.splash_screen.config;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
public class SplashScreenCreativeConfig {
    private List<ModifyTwistAngleOperator> modifyTwistAngleOperators;
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ModifyTwistAngleOperator {
        private String operatorName;
    }
}
