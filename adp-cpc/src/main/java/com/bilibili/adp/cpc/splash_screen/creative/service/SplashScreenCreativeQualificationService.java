package com.bilibili.adp.cpc.splash_screen.creative.service;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauCreativeExtraPo;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauCreativeQualificationPo;
import com.bilibili.adp.cpc.splash_screen.creative.converter.ExtraConverter;
import com.bilibili.adp.cpc.splash_screen.creative.converter.QualificationConverter;
import com.bilibili.adp.cpc.utils.CommonFuncs;
import com.bilibili.adp.cpc.utils.RecDiffResult;
import com.bilibili.bjcom.querydsl.BaseQueryFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

import static com.bilibili.adp.cpc.dao.ad_core.querydsl.QLauCreativeQualification.lauCreativeQualification;
import static com.bilibili.adp.cpc.dao.config.PlatformMySqlConfig.AD_CORE_BQF;
import static com.bilibili.adp.cpc.dao.config.PlatformMySqlConfig.AD_CORE_TM;

@Slf4j
@Service
@RequiredArgsConstructor
public class SplashScreenCreativeQualificationService {

    @Resource(name = AD_CORE_BQF)
    private BaseQueryFactory adCoreBqf;

    private final SplashScreenCreativeExtraService splashScreenCreativeExtraService;
    public static List<LauCreativeQualificationPo> generatePos(Integer creativeId, List<Integer> qualificationIds) {
        return qualificationIds
                .stream()
                .map(qualificationId ->QualificationConverter.MAPPER.po(creativeId, qualificationId))
                .collect(Collectors.toList());
    }
    public List<Integer> qualificationIds(Integer creativeId) {
        return adCoreBqf.select(lauCreativeQualification.qualificationId)
                .from(lauCreativeQualification)
                .where(lauCreativeQualification.creativeId.eq(creativeId))
                .fetch();
    }

    public List<LauCreativeQualificationPo> list(Collection<Integer> creativeIds) {
        return adCoreBqf.select(lauCreativeQualification)
                .from(lauCreativeQualification)
                .where(lauCreativeQualification.creativeId.in(creativeIds))
                .fetch();
    }
    @Transactional(transactionManager = AD_CORE_TM, rollbackFor = Exception.class)
    public void save(Integer accountId, Integer campaignId, Integer unitId, Integer creativeId, Integer qualificationPackageId, List<Integer> qualificationIds) {
        List<LauCreativeQualificationPo> creativeQualificationPos = generatePos(creativeId, qualificationIds);
        save(creativeId, creativeQualificationPos);
        LauCreativeExtraPo creativeExtraPo = Utils.isPositive(qualificationPackageId) ? ExtraConverter.MAPPER.po(accountId, campaignId, unitId, creativeId, qualificationPackageId) : null;
        splashScreenCreativeExtraService.save(creativeId, creativeExtraPo);
    }

    @Transactional(transactionManager = AD_CORE_TM, rollbackFor = Exception.class)
    public void save(Integer creativeId, List<LauCreativeQualificationPo> pos) {
        final List<LauCreativeQualificationPo> existingPos = list(creativeId);
        final RecDiffResult<LauCreativeQualificationPo, Long> result = CommonFuncs.recDiff(existingPos, pos, this::uk, LauCreativeQualificationPo::getId, CommonFuncs.getDefaultBiFunction(LauCreativeQualificationPo::getId, LauCreativeQualificationPo::setId));
        CommonFuncs.handleRecDiff(result, adCoreBqf, lauCreativeQualification, lauCreativeQualification.id::in);
    }
    public List<LauCreativeQualificationPo> list(Integer creativeId) {
        return adCoreBqf.selectFrom(lauCreativeQualification)
                .where(lauCreativeQualification.creativeId.eq(creativeId))
                .fetch();
    }

    private String uk(LauCreativeQualificationPo po) {
        return po.getCreativeId() + "-" + po.getQualificationId();
    }
}
