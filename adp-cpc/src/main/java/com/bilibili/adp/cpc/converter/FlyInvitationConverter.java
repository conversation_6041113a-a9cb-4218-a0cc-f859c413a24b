package com.bilibili.adp.cpc.converter;

import com.bilibili.adp.launch.api.creative.dto.FlyInvitationDto;
import com.bilibili.adp.cpc.vo.fly.MiddleFlyInvitationInfoVo;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class FlyInvitationConverter {

    static public List<MiddleFlyInvitationInfoVo> flyInvitationDtos2Vos(List<FlyInvitationDto> dtos) {
        if (dtos == null || CollectionUtils.isEmpty(dtos)) {
            return Collections.emptyList();
        }
        List<MiddleFlyInvitationInfoVo> vos = new ArrayList<>();
        dtos.forEach(dto -> {
            MiddleFlyInvitationInfoVo vo = FlyInvitationConverter.flyInvitationDto2Vo(dto);
            vos.add(vo);
        });
        return vos;
    }

    static public List<FlyInvitationDto> flyInvitationVos2Dtos(List<MiddleFlyInvitationInfoVo> vos) {
        if (vos == null || CollectionUtils.isEmpty(vos)) {
            return Collections.emptyList();
        }
        List<FlyInvitationDto> dtos = new ArrayList<>();
        vos.forEach(vo -> {
            FlyInvitationDto dto = FlyInvitationConverter.flyInvitationVo2Dto(vo);
            dtos.add(dto);
        });
        return dtos;
    }

    static public MiddleFlyInvitationInfoVo flyInvitationDto2Vo(FlyInvitationDto dto) {
        if (dto == null) {
            return new MiddleFlyInvitationInfoVo();
        }
        return MiddleFlyInvitationInfoVo.builder()
                .invitation_launch_type(dto.getInvitationLaunchType())
                .invitation_place_type(dto.getInvitationPlaceType())
                .name(dto.getName())
                .face(dto.getFace())
                .level(dto.getLevel())
                .comment_blue_text(dto.getCommentBlueText())
                .comment_black_text(dto.getCommentBlackText())
                .text_position(dto.getTextPosition())
                .template_id(dto.getTemplateId())
                .image(dto.getImage())
                .title(dto.getTitle())
                .cm_mark(dto.getCmMark())
                .desc(dto.getDesc())
                .button_desc(dto.getButtonDesc())
                .click_url(dto.getClickUrl())
                .build();
    }

    static public FlyInvitationDto flyInvitationVo2Dto(MiddleFlyInvitationInfoVo vo) {
        if (vo == null) {
            return new FlyInvitationDto();
        }
        return FlyInvitationDto.builder()
                .invitationLaunchType(vo.getInvitation_launch_type())
                .invitationPlaceType(vo.getInvitation_place_type())
                .name(vo.getName())
                .face(vo.getFace())
                .level(vo.getLevel())
                .commentBlackText(vo.getComment_black_text())
                .commentBlueText(vo.getComment_blue_text())
                .textPosition(vo.getText_position())
                .templateId(vo.getTemplate_id())
                .image(vo.getImage())
                .title(vo.getTitle())
                .cmMark(vo.getCm_mark())
                .desc(vo.getDesc())
                .buttonDesc(vo.getButton_desc())
                .clickUrl(vo.getClick_url())
                .build();
    }
}
