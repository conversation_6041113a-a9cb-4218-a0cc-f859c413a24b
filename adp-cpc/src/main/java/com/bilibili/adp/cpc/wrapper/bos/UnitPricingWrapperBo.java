package com.bilibili.adp.cpc.wrapper.bos;

import com.bilibili.adp.cpc.core.constants.OcpxTarget;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauUnitPo;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class UnitPricingWrapperBo {
    private Integer ocpxTargetTwoBid;
    private int ocpxTargetTwoBidScale;
    private boolean ocpxTargetTwoAutoBid;

    public static UnitPricingWrapperBo from(Integer value, int scale) {
        final UnitPricingWrapperBoBuilder builder = UnitPricingWrapperBo.builder()
                .ocpxTargetTwoBid(value)
                .ocpxTargetTwoBidScale(scale);
        if (Objects.equals(0, value)) {
            builder.ocpxTargetTwoAutoBid(true);
        }
        return builder.build();
    }

    public static UnitPricingWrapperBo fromPo(LauUnitPo po, Integer updateType) {
        final boolean isRoi = OcpxTarget.isRoi(updateType, po);
        return UnitPricingWrapperBo.builder()
                .ocpxTargetTwoBid(po.getOcpxTargetTwoBid())
                .ocpxTargetTwoBidScale(OcpxTarget.scale(isRoi))
                .ocpxTargetTwoAutoBid(Objects.equals(po.getOcpxTargetTwoBid(), 0))
                .build();
    }

    public String ocpxTargetTwoBidFormat() {
        if (Objects.equals(ocpxTargetTwoBid, 0)) return "自动优化";
        return BigDecimal.valueOf(ocpxTargetTwoBid).divide(BigDecimal.valueOf(10).pow(ocpxTargetTwoBidScale), ocpxTargetTwoBidScale, RoundingMode.HALF_EVEN).toString();
    }
}
