package com.bilibili.adp.cpc.splash_screen.operation_log.converter;

import com.bilibili.adp.cpc.splash_screen.operation_log.OperationContextBo;
import com.bilibili.adp.log.service.v6.bo.LogCpcOperationBo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper
public interface OperationLogConverter {

    OperationLogConverter MAPPER = Mappers.getMapper(OperationLogConverter.class);

    @Mapping(target = "operatorType", source = "ctx.operator.operatorType")
    @Mapping(target = "type", source = "ctx.operationType")
    @Mapping(target = "operatorUsername", source = "ctx.operator.operatorName")
    @Mapping(target = "bilibiliUsername", source = "ctx.operator.bilibiliUserName")
    @Mapping(target = "accountId", source = "ctx.operator.operatorId")
    @Mapping(target = "value", source = "changesStr")
    LogCpcOperationBo toBo(OperationContextBo ctx, String changesStr);
}
