package com.bilibili.adp.cpc.automatic_rule.converter;

import com.bilibili.adp.cpc.automatic_rule.bos.ConditionBo;
import com.bilibili.adp.cpc.dao.querydsl.pos.LauAutomaticRuleConditionPo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper
public interface ConditionConverter {
    ConditionConverter MAPPER = Mappers.getMapper(ConditionConverter.class);

    @Mapping(target = "isDeleted", ignore = true)
    @Mapping(target = "ctime", ignore = true)
    @Mapping(target = "mtime", ignore = true)
    LauAutomaticRuleConditionPo bo2Po(Integer accountId, Long ruleId, ConditionBo bo);

    ConditionBo po2Bo(LauAutomaticRuleConditionPo po);

    @Mapping(target = "value", source = "value")
    LauAutomaticRuleConditionPo newPo(LauAutomaticRuleConditionPo po, String value);
}
