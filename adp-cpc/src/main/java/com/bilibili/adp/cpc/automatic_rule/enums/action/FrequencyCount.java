package com.bilibili.adp.cpc.automatic_rule.enums.action;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum FrequencyCount {
    UNLIMITED(0, "不限", Integer.MAX_VALUE),
    ONE(1, "1次", 1),
    TWO(2, "2次", 2),
    THREE(3, "3次", 3),
    FOUR(4, "4次",4),
    FIVE(5, "5次", 5);
    private final int code;
    private final String desc;
    private final int count;

    public static FrequencyCount getByCode(int code) {
        return Arrays.stream(values())
                .filter(frequencyCount -> frequencyCount.getCode() == code)
                .findFirst()
                .orElse(FrequencyCount.UNLIMITED);
    }

}
