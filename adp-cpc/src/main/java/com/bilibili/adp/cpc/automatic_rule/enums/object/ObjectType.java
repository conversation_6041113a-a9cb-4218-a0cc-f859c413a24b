package com.bilibili.adp.cpc.automatic_rule.enums.object;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum ObjectType {
    ACCOUNT(1, "账户"),
    CAMPAIGN(2, "计划"),
    UNIT(3, "单元"),
    CREATIVE(4, "创意"),
    ;
    private final int code;
    private final String desc;
    public static ObjectType getByCode(int code) {
        return Arrays.stream(values())
                .filter(objectType -> objectType.getCode() == code)
                .findFirst()
                .orElseThrow(IllegalArgumentException::new);

    }
}
