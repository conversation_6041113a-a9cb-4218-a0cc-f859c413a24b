package com.bilibili.adp.cpc.biz.services.account;


import com.bapis.archive.service.*;
import com.bilibili.adp.account.exception.AccountExceptionCode;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.biz.constants.Constants;
import com.bilibili.adp.cpc.biz.services.account.bos.LauCustomerAgentCreativeMappingBo;
import com.bilibili.adp.cpc.biz.services.account.bos.VideoBindBo;
import com.bilibili.adp.cpc.biz.services.account.dto.LauGeneralAvidMappingDto;
import com.bilibili.adp.cpc.biz.services.common.BilibiliAccountLabelHelper;
import com.bilibili.adp.cpc.biz.services.common.GeneralVideoLabelHelper;
import com.bilibili.adp.cpc.biz.services.creative.bos.SaveCreativeBo;
import com.bilibili.adp.cpc.biz.services.creative.bos.programmatic.BiliVideoMediaBo;
import com.bilibili.adp.cpc.biz.services.creative.bos.programmatic.MediaGroupBo;
import com.bilibili.adp.cpc.biz.services.creative.bos.programmatic.SaveProgrammaticCreativeBo;
import com.bilibili.adp.cpc.dao.querydsl.pos.LauCustomerAgentCreativeMappingPo;
import com.bilibili.adp.cpc.dto.CpcCreativeDetailDto;
import com.bilibili.adp.cpc.enums.*;
import com.bilibili.adp.cpc.proxy.ArchiveServiceProxy;
import com.bilibili.adp.launch.api.common.CreativeStatus;
import com.bilibili.adp.launch.api.creative.dto.BilibiliVideoBo;
import com.bilibili.bjcom.querydsl.BaseQueryFactory;
import com.bilibili.bjcom.querydsl.clause.BaseQuery;
import com.bilibili.commercialorder.soa.adauth.pojo.SoaAdAuthInfoDto;
import com.bilibili.commercialorder.soa.adauth.pojo.SoaGetAdAuthReqDto;
import com.bilibili.commercialorder.soa.adauth.service.ISoaAdAuth4AdpService;
import com.bilibili.crm.platform.api.account.dto.AccountBaseDto;
import com.bilibili.crm.platform.soa.ISoaQueryAccountService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

import static com.bilibili.adp.cpc.dao.config.PlatformMySqlConfig.*;
import static com.bilibili.adp.cpc.dao.querydsl.QLauAccountCorpMidMapping.lauAccountCorpMidMapping;
import static com.bilibili.adp.cpc.dao.querydsl.QLauCustomerAgentCreativeMapping.lauCustomerAgentCreativeMapping;
import static com.bilibili.adp.cpc.dao.ad_core.querydsl.QLauUnitCreative.lauUnitCreative;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class VideoBindService {

    @Resource(name = AD_BQF)
    private BaseQueryFactory adBqf;

    @Resource(name = AD_CORE_BQF)
    private BaseQueryFactory adCoreBqf;

    @Resource(name = "creativeExtDescriptionPool")
    private ExecutorService executorService;

    @Autowired
    BilibiliAccountLabelHelper bilibiliAccountLabelHelper;

    @Autowired
    ISoaQueryAccountService soaQueryAccountService;

    @Autowired
    private ISoaAdAuth4AdpService soaAdAuth4AdpService;

    @Autowired
    private ArchiveServiceProxy archiveServiceProxy;
    @Autowired
    GeneralVideoLabelHelper generalVideoLabelHelper;

    @Autowired
    GeneralVideoAuthDaoService generalVideoAuthDaoService;


    /**
     * 非三联自定义-创意关联及创意视频是否授权
     *
     * @param accountId
     * @param creativeIds
     * @param dtos
     */
    public void refreshCustomizeCreative(Integer accountId, List<Integer> creativeIds, List<CpcCreativeDetailDto> dtos) {

        try {
            executorService.execute(() -> {

                if (dtos.size() == creativeIds.size() && Utils.isPositive(accountId)) {


                    for (int i = 0; i < dtos.size(); i++) {
                        BilibiliVideoBo bilibiliVideo = dtos.get(i).getBilibiliVideo();
                        Long avid = null;
                        Long mid = null;
                        if(null != bilibiliVideo){
                            avid = bilibiliVideo.getAvId();
                            mid = getMidByAvid(avid);
                        }

                        oldCustomizeCreativeRefresh(accountId, creativeIds, i, avid, mid);


                    }
                }


            });


        } catch (Exception e) {
            log.info("refreshCustomizeCreative Exception={}", e);
        }
    }

    /**
     *
     * @param accountId
     * @param creativeIds
     * @param i
     * @param avid
     * @param mid
     */
    private void oldCustomizeCreativeRefresh(Integer accountId, List<Integer> creativeIds, int i, Long avid, Long mid) {


        AccountBaseDto accountBaseDto = soaQueryAccountService.getAccountBaseDtoById(accountId);
        if (accountBaseDto == null) {
            throw new IllegalArgumentException(AccountExceptionCode.NOT_EXIST_ACCOUNT.getMessage());
        }
        boolean supportBindBilibiliAccount = bilibiliAccountLabelHelper.isSupportBindBilibiliAccount(accountId);


        //非三联-创意视频是否关联账户（bilibli账户）
        try {
            customizeBilibiliLastMapping(creativeIds.get(i),supportBindBilibiliAccount,accountBaseDto,mid);
        } catch (Exception e) {
           log.error("customizeBilibiliLastMapping error",e);
        }

        //非三联-创意视频是否授权(商单及bilibli账户)，用于运营后台查询
        customerCreativeVideoStatus(accountId, creativeIds.get(i), avid, mid, accountBaseDto, supportBindBilibiliAccount);

    }

    /**
     * 非三联自定义-创意视频是否授权(商单及bilibli账户)，用于运营后台查询
     * @param accountId
     * @param currentCreativeId
     * @param avid
     * @param mid
     * @param accountBaseDto
     * @param supportBindBilibiliAccount
     */
    private void customerCreativeVideoStatus(Integer accountId, Integer currentCreativeId, Long avid, Long mid, AccountBaseDto accountBaseDto, boolean supportBindBilibiliAccount) {
        try {
            if(Utils.isPositive(currentCreativeId)){

                if(Utils.isPositive(mid)){
                    //若视频来源于商单，返回
                    if (isBusinessVideoStatus(accountId, avid)) {

                        //刷新-视频已授权0307
                        refreshCreativeExtDescription(currentCreativeId, Constants.IS_BIND);
                        return;
                    }
                    boolean deletedCheckOutVideo = true;
                    //账户绑定白名单内
                    if (supportBindBilibiliAccount) {
                        //若视频存在来源于bilibili账户绑定-0307
                        if (isBilibiliVideoStatus(accountBaseDto, mid)) {
                            //刷新-视频已绑定
                            refreshCreativeExtDescription(currentCreativeId, Constants.IS_BIND);
                            deletedCheckOutVideo = false;
                        }

                    }
                    //校验是否需解绑，新建/编辑-0307
                    if (deletedCheckOutVideo) {
                        checkOutCreativeVideoBind(currentCreativeId);
                    }
                }else{
                    //不含有视频，校验是否需解绑，新建/编辑-0307
                    checkOutCreativeVideoBind(currentCreativeId);

                }

            }

        } catch (Exception e) {
            log.error("customerCreativeVideoStatus error",e);
        }
    }

    /**
     * 程序化创意刷新-兼容
     *
     * @param accountId
     * @param saveCreativeBo
     * @param bo
     */
    public void refreshProgrammaticCreative(Integer accountId, SaveCreativeBo saveCreativeBo, SaveProgrammaticCreativeBo bo) {

        try {
            executorService.execute(() -> {

                AccountBaseDto accountBaseDto = soaQueryAccountService.getAccountBaseDtoById(accountId);
                if (accountBaseDto == null) {
                    throw new IllegalArgumentException(AccountExceptionCode.NOT_EXIST_ACCOUNT.getMessage());
                }

                //新三联-非商单校验
                middleRefreshProgrammaticCreative(accountBaseDto, accountId, saveCreativeBo, bo);

                //非三连-创意关联及创意视频是否授权
//                oldRefreshProgrammaticCreative(accountId, saveCreativeBo, bo, accountBaseDto);


            });
        } catch (Exception e) {
            log.info("refreshProgrammaticCreative Exception={}", e);
        }

    }


    /**
     * 非三连-创意关联及创意视频是否授权
     * @param accountId
     * @param saveCreativeBo
     * @param bo
     * @param accountBaseDto
     */
    private void oldRefreshProgrammaticCreative(Integer accountId, SaveCreativeBo saveCreativeBo, SaveProgrammaticCreativeBo bo, AccountBaseDto accountBaseDto) {
        if (saveCreativeBo.getCreativeIds().size() > 0 && Utils.isPositive(accountId)) {
            BilibiliVideoBo bilibiliVideoBo = bo.getBilibiliVideoBo();

            List<VideoBindBo> videoBindList = new ArrayList<>();

            if (Objects.nonNull(bilibiliVideoBo) && Utils.isPositive(bilibiliVideoBo.getAvId())) {
                Long avId = bilibiliVideoBo.getAvId();
                videoBindList.add(VideoBindBo.builder().avid(avId).build());

            } else {
                List<MediaGroupBo> mediaGroups = bo.getMediaGroups();
                if (CollectionUtils.isNotEmpty(mediaGroups) && mediaGroups.size() > 0) {
                    List<List<BiliVideoMediaBo>> collect = mediaGroups.stream().filter(m -> CollectionUtils.isNotEmpty(m.getBiliVideoMediaList()) && m.getBiliVideoMediaList().size() > 0)
                            .map(m -> m.getBiliVideoMediaList())
                            .collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(collect) && collect.size() > 0) {

                        for (int i = 0; i < collect.size(); i++) {
                            List<BiliVideoMediaBo> biliVideoMediaList = collect.get(i);

                            for (BiliVideoMediaBo biliVideoMediaBo : biliVideoMediaList) {
                                Long avid = biliVideoMediaBo.getAvId();
                                videoBindList.add(VideoBindBo.builder().avid(avid).build());
                            }

                        }
                    }

                }

            }

            if (CollectionUtils.isNotEmpty(videoBindList) && videoBindList.size() > 0) {


                try {
                    getMidsByAvids(videoBindList);
                } catch (Exception e) {
                    log.error("getMidsByAvids Exception", e);
                }

            }

            boolean supportBindBilibiliAccount = bilibiliAccountLabelHelper.isSupportBindBilibiliAccount(accountId);

            //非三联-创意视频是否关联账户（bilibli账户）
            oldRefreshProgrammaticCreativeRelation(accountId, saveCreativeBo, accountBaseDto, videoBindList,supportBindBilibiliAccount);

            //非三联-创意视频是否授权(商单及bilibli账户)，用于运营后台查询
            oldRefreshProgrammaticCreativeVideoStatus(accountId, saveCreativeBo, accountBaseDto, videoBindList,supportBindBilibiliAccount);


        }
    }

    /**
     * 非三联-创意视频是否关联账户（bilibli账户）
     * @param accountId
     * @param saveCreativeBo
     * @param accountBaseDto
     * @param videoBindList
     */
    private void oldRefreshProgrammaticCreativeRelation(Integer accountId, SaveCreativeBo saveCreativeBo, AccountBaseDto accountBaseDto, List<VideoBindBo> videoBindList,boolean supportBindBilibiliAccount) {

        try {
            programmaticBilibiliLastMapping(saveCreativeBo.getCreativeIds().get(0), accountBaseDto,  videoBindList, supportBindBilibiliAccount);
        } catch (Exception e) {
            log.error("oldRefreshProgrammaticCreativeRelation exception",e);
        }

    }

    /**
     * 非三联-创意视频是否授权(商单及bilibli账户)，用于运营后台查询
     * @param accountId
     * @param saveCreativeBo
     * @param accountBaseDto
     * @param videoBindList
     */
    private void oldRefreshProgrammaticCreativeVideoStatus(Integer accountId, SaveCreativeBo saveCreativeBo, AccountBaseDto accountBaseDto, List<VideoBindBo> videoBindList,boolean supportBindBilibiliAccount) {
        try {
            if (CollectionUtils.isNotEmpty(videoBindList) && videoBindList.size() > 0) {
                //validSize > 0
                int validSize = videoBindList.size();
                int validCount = 0;
                for (VideoBindBo bindBo : videoBindList) {
                    Long mid = bindBo.getMid();
                    Long avid = bindBo.getAvid();
                    if (Utils.isPositive(mid)) {

                        //如果视频来源于商单，继续
                        if (isBusinessVideoStatus(accountId, avid)) {
                            //刷新点-视频已授权0307
                            validCount++;

                            //账户绑定白名单内
                        }else if (supportBindBilibiliAccount) {

                            //若视频存在来源于bilibili账户绑定-0307
                            if (isBilibiliVideoStatus(accountBaseDto, mid)) {
                                //刷新点
                                validCount++;
                            }

                        }

                    }
                }

                //校验是否需解绑，新建/编辑-0307
                if (validSize != validCount) {
                    checkOutCreativeVideoBind(saveCreativeBo.getCreativeIds().get(0));
                } else {
                    //刷新
                    refreshCreativeExtDescription(saveCreativeBo.getCreativeIds().get(0), Constants.IS_BIND);
                }
            }else{
                //校验是否需解绑
                checkOutCreativeVideoBind(saveCreativeBo.getCreativeIds().get(0));
            }

        } catch (Exception e) {
            log.error("oldRefreshProgrammaticCreativeVideoStatus exception",e);
        }
    }

    /**
     * 新三联-自定义视频校验
     *
     * @param accountId
     * @param creativeIds
     * @param dtos
     */
    public void middleRefreshCustomizeCreative(Integer accountId, List<Integer> creativeIds, List<CpcCreativeDetailDto> dtos) {

        try {
            executorService.execute(() -> {

                if (!generalVideoLabelHelper.isSupportGeneralVideo(accountId)) {
                    return;
                }

                if (dtos.size() == creativeIds.size() && Utils.isPositive(accountId)) {

                    AccountBaseDto accountBaseDto = soaQueryAccountService.getAccountBaseDtoById(accountId);
                    if (accountBaseDto == null) {
                        throw new IllegalArgumentException(AccountExceptionCode.NOT_EXIST_ACCOUNT.getMessage());
                    }
                    Map<Long, Integer> videoMap = getGeneralVideoList(accountBaseDto);

                    for (int i = 0; i < dtos.size(); i++) {

                        middleCustomizeGeneralLastMapping(i,videoMap,accountBaseDto,creativeIds,dtos);


                    }
                }


            });


        } catch (Exception e) {
            log.info("middleRefreshCustomizeCreative Exception={}", e);
        }
    }

    /**
     * 新三联-程序化非商单校验
     *
     * @param accountId
     * @param saveCreativeBo
     * @param bo
     */
    private void middleRefreshProgrammaticCreative(AccountBaseDto accountBaseDto, Integer accountId, SaveCreativeBo saveCreativeBo, SaveProgrammaticCreativeBo bo) {

        try {
            if (!generalVideoLabelHelper.isSupportGeneralVideo(accountId)) {
                return;
            }
            if (saveCreativeBo.getCreativeIds().size() > 0 && Utils.isPositive(accountId)) {

                Map<Long, Integer> videoMap = getGeneralVideoList(accountBaseDto);
                BilibiliVideoBo bilibiliVideoBo = bo.getBilibiliVideoBo();

                List<VideoBindBo> generalVideoList = new ArrayList<>();
                Set<Long> allVideoSet = new HashSet<>();
                if(MapUtils.isNotEmpty(videoMap)){
                    if (Objects.nonNull(bilibiliVideoBo) && Utils.isPositive(bilibiliVideoBo.getAvId())) {

                        Long avId = bilibiliVideoBo.getAvId();
                        if (!allVideoSet.contains(avId) && videoMap.containsKey(avId)) {
                            generalVideoList.add(VideoBindBo.builder().avid(avId).build());
                        }
                        allVideoSet.add(avId);


                    } else {
                        List<MediaGroupBo> mediaGroups = bo.getMediaGroups();
                        if (CollectionUtils.isNotEmpty(mediaGroups) && mediaGroups.size() > 0) {
                            List<List<BiliVideoMediaBo>> collect = mediaGroups.stream().filter(m -> CollectionUtils.isNotEmpty(m.getBiliVideoMediaList()) && m.getBiliVideoMediaList().size() > 0)
                                    .map(m -> m.getBiliVideoMediaList())
                                    .collect(Collectors.toList());
                            if(CollectionUtils.isNotEmpty(collect) && collect.size()>0 ){

                                for (int i = 0; i < collect.size(); i++) {
                                    List<BiliVideoMediaBo> biliVideoMediaList = collect.get(i);

                                    for (BiliVideoMediaBo biliVideoMediaBo : biliVideoMediaList) {
                                        Long avId = biliVideoMediaBo.getAvId();
                                        if (!allVideoSet.contains(avId) && videoMap.containsKey(avId)) {
                                            generalVideoList.add(VideoBindBo.builder().avid(avId).build());
                                        }
                                        allVideoSet.add(avId);
                                    }

                                }

                            }

                        }

                    }
                }
                middleProgrammaticGeneralLastMapping(saveCreativeBo.getCreativeIds().get(0),generalVideoList,accountBaseDto,allVideoSet);


            }
        } catch (IllegalArgumentException e) {
            log.info("middleRefreshProgrammaticCreative exception={}", e);
        }
    }


    private Map<Long, Integer> getGeneralVideoList(AccountBaseDto accountBaseDto){
        List<Integer> authStatus = Arrays.asList(GeneralVideoAuthStatusEnum.CONFIRM.getCode());
        List<LauGeneralAvidMappingDto> avidMappingDtoList = generalVideoAuthDaoService.getAvidMappingByAuthModel(accountBaseDto,authStatus,null);
        return avidMappingDtoList.stream().collect(Collectors.toMap(k -> k.getAvid(), k -> k.getId(), (k1, k2) -> k1));
    }

    private Map<Long, Integer> getGeneralVideoRejectList(AccountBaseDto accountBaseDto){
        List<Integer> authStatus = Arrays.asList(GeneralVideoAuthStatusEnum.REJECT.getCode());
        List<LauGeneralAvidMappingDto> avidMappingDtoList = generalVideoAuthDaoService.getAvidMappingByAuthModel(accountBaseDto,authStatus,null);
        return avidMappingDtoList.stream().collect(Collectors.toMap(k -> k.getAvid(), k -> k.getId(), (k1, k2) -> k1));
    }

    @Transactional(value = AD_TM, rollbackFor = Exception.class)
    public void customizeBilibiliLastMapping(Integer currentCreativeId,boolean supportBindBilibiliAccount,AccountBaseDto accountBaseDto, Long mid){
        if(Utils.isPositive(currentCreativeId)){

            //账户绑定白名单内
            if (supportBindBilibiliAccount) {
                //检查删除之前的关联关系
                List<Long> midLists = checkOutBilibiliCreative(currentCreativeId);

                //涉及非商单的编辑操作
                if(null != midLists && midLists.size()>0) {
                    if(Utils.isPositive(mid)) {
                        List<Long> rejectMids = getBilibiliRejectMid(accountBaseDto, Constants.IS_NO_BIND);
                        //视频的mid是否命中已失效mid
                        if(null == rejectMids || !rejectMids.contains(mid)){
                            generalVideoAuthDaoService.updateCreativeStatus(currentCreativeId,null, "");
                        }
                    }else{
                        generalVideoAuthDaoService.updateCreativeStatus(currentCreativeId,null, "");
                    }

                }

                if (Utils.isPositive(mid)) {

                    //若视频来源于bilibili账户绑定，且不属于企业号绑定
                    if (isBilibiliVideoStatusNoEnterpriseBind(accountBaseDto, mid)) {
                        insertBilibiliCreative(currentCreativeId, accountBaseDto, mid);
                    }

                }

            }


        }


    }


    @Transactional(value = AD_TM, rollbackFor = Exception.class)
    public void middleCustomizeGeneralLastMapping(int i, Map<Long, Integer> videoMap,AccountBaseDto accountBaseDto, List<Integer> creativeIds, List<CpcCreativeDetailDto> dtos){
        Integer currentCreativeId = creativeIds.get(i);
        if(Utils.isPositive(currentCreativeId)){
            //检查删除之前的关联关系
            List<Long> avidLists = checkOutGeneralCreative(currentCreativeId);

            BilibiliVideoBo bilibiliVideo = dtos.get(i).getBilibiliVideo();
            if(null != bilibiliVideo && Utils.isPositive(bilibiliVideo.getAvId())) {
                Long avId = bilibiliVideo.getAvId();

                //涉及非商单的编辑操作
                if(null != avidLists && avidLists.size()>0) {
                    if(!avidLists.contains(avId) || videoMap.containsKey(avId)) {
                        generalVideoAuthDaoService.updateCreativeStatus(currentCreativeId,null, "");
                    }
                }
                //建立新的非商单视频和创意的关系
                if(MapUtils.isNotEmpty(videoMap)){

                    if (videoMap.containsKey(avId)) {
                        //插入最新的关联关系
                        insertGeneralCreative(currentCreativeId, accountBaseDto, avId);
                    }

                }
            }


        }


    }

    @Transactional(value = AD_TM, rollbackFor = Exception.class)
    public void programmaticBilibiliLastMapping(Integer currentCreativeId,AccountBaseDto accountBaseDto, List<VideoBindBo> videoBindList,boolean supportBindBilibiliAccount){


        if(Utils.isPositive(currentCreativeId)){

            //bilibili账户绑定白名单内
            if (supportBindBilibiliAccount) {
                //检查删除之前的关联关系
                List<Long> midLists = checkOutBilibiliCreative(currentCreativeId);

                //涉及bilibili账户绑定的编辑操作
                if(null != midLists && midLists.size()>0) {
                    if (CollectionUtils.isNotEmpty(videoBindList) && videoBindList.size() > 0) {
                        List<Long> videoMids= videoBindList.stream().map(v -> v.getMid()).collect(Collectors.toList());
                        List<Long> rejectMids = getBilibiliRejectMid(accountBaseDto, Constants.IS_NO_BIND);

                        //所有视频的mid是否命中已失效mid
                        boolean b = videoMids.stream().anyMatch(a -> rejectMids.contains(a));
                        if(!b){
                            generalVideoAuthDaoService.updateCreativeStatus(currentCreativeId, CreativeStatus.AUDITING.getCode(), "");
                        }

                    }else{
                        generalVideoAuthDaoService.updateCreativeStatus(currentCreativeId,CreativeStatus.AUDITING.getCode(), "");

                    }
                }

                if (CollectionUtils.isNotEmpty(videoBindList) && videoBindList.size() > 0) {
                    //插入最新的关联关系
                    for (VideoBindBo bindBo : videoBindList) {

                        Long mid = bindBo.getMid();
                        if (Utils.isPositive(mid)) {

                            //若视频来源于bilibili账户绑定，且不属于企业号绑定
                            if (isBilibiliVideoStatusNoEnterpriseBind(accountBaseDto, mid)) {
                                insertBilibiliCreative(currentCreativeId, accountBaseDto, mid);
                            }

                        }

                    }
                }

            }


        }

    }


    @Transactional(value = AD_TM, rollbackFor = Exception.class)
    public void middleProgrammaticGeneralLastMapping(Integer currentCreativeId,List<VideoBindBo> generalVideoList,AccountBaseDto accountBaseDto,Set<Long> allVideoSet){


        if(Utils.isPositive(currentCreativeId)){
            //检查删除之前的关联关系
            List<Long> avidLists = checkOutGeneralCreative(currentCreativeId);

            //涉及非商单的编辑操作
            if(null != avidLists && avidLists.size()>0) {

                int allVideoSize = allVideoSet.size();
                if(CollectionUtils.isNotEmpty(generalVideoList) && generalVideoList.size() > 0){
                    if(allVideoSize > 0){
                        //非商单授权个数等于总视频个数
                        if(allVideoSize == generalVideoList.size()){
                            generalVideoAuthDaoService.updateCreativeStatus(currentCreativeId, CreativeStatus.AUDITING.getCode(), "");
                        }
                        //非商单授权个数小于总视频个数
                        if(allVideoSize > generalVideoList.size()){
                            Map<Long, Integer> videoRejectMap = getGeneralVideoRejectList(accountBaseDto);
                            boolean b = allVideoSet.stream().anyMatch(a -> videoRejectMap.containsKey(a));
                            if(!b){
                                generalVideoAuthDaoService.updateCreativeStatus(currentCreativeId,CreativeStatus.AUDITING.getCode(), "");
                            }
                        }

                    }
                }else{
                    if(allVideoSize > 0){
                        Map<Long, Integer> videoRejectMap = getGeneralVideoRejectList(accountBaseDto);
                        boolean b = allVideoSet.stream().anyMatch(a -> videoRejectMap.containsKey(a));
                        if(!b){
                            generalVideoAuthDaoService.updateCreativeStatus(currentCreativeId,CreativeStatus.AUDITING.getCode(), "");
                        }
                    }else{
                        generalVideoAuthDaoService.updateCreativeStatus(currentCreativeId,CreativeStatus.AUDITING.getCode(), "");
                    }


                }

            }
            //建立新的非商单视频和创意的关系
            if (CollectionUtils.isNotEmpty(generalVideoList) && generalVideoList.size() > 0) {
                //插入最新的关联关系
                for (VideoBindBo bindBo : generalVideoList) {
                    insertGeneralCreative(currentCreativeId, accountBaseDto, bindBo.getAvid());
                }
            }
        }


    }


    private List<Long> checkOutBilibiliCreative(Integer creativeId) {

        List<LauCustomerAgentCreativeMappingPo> fetch = getBilibiliCreativeMid(creativeId, null).fetch(LauCustomerAgentCreativeMappingPo.class);

        if (CollectionUtils.isNotEmpty(fetch) && fetch.size() > 0) {
            List<Integer> mappingIds = fetch.stream().map(f -> f.getId()).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(mappingIds) && mappingIds.size() > 0) {
                adBqf.update(lauCustomerAgentCreativeMapping)
                        .where(lauCustomerAgentCreativeMapping.id.in(mappingIds))
                        .set(lauCustomerAgentCreativeMapping.isDeleted, Constants.INT_TRUE)
                        .execute();
                return fetch.stream().filter(f->Utils.isPositive(f.getMid())).map(f ->f.getMid()).collect(Collectors.toList());
            }
        }
        return null;


    }

    private List<Long> checkOutGeneralCreative(Integer creativeId) {

        List<LauCustomerAgentCreativeMappingPo> fetch = getGeneralCreativeAvid(creativeId, null).fetch(LauCustomerAgentCreativeMappingPo.class);
        if (CollectionUtils.isNotEmpty(fetch) && fetch.size() > 0){
            List<Integer> mappingIds = fetch.stream().map(f ->f.getId()).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(mappingIds) && mappingIds.size() > 0) {
                adBqf.update(lauCustomerAgentCreativeMapping)
                        .where(lauCustomerAgentCreativeMapping.id.in(mappingIds))
                        .set(lauCustomerAgentCreativeMapping.isDeleted, Constants.INT_TRUE)
                        .execute();
                return fetch.stream().filter(f->Utils.isPositive(f.getAvid())).map(f ->f.getAvid()).collect(Collectors.toList());
            }
        }
        return null;

    }


    private Integer insertBilibiliCreative(Integer creativeId, AccountBaseDto accountBaseDto, Long mid) {
        return adBqf.insert(lauCustomerAgentCreativeMapping)
                .insertGetKey(
                        LauCustomerAgentCreativeMappingBo.builder()
                                .customerId(accountBaseDto.getCustomerId())
                                .agentId(accountBaseDto.getDependencyAgentId())
                                .creativeId(creativeId)
                                .mid(mid)
                                .creativeAvidSource(Constants.BILIBILI_MID_BIND)
                                .build());
    }

    private Integer insertGeneralCreative(Integer creativeId, AccountBaseDto accountBaseDto, Long avid) {
        return adBqf.insert(lauCustomerAgentCreativeMapping)
                .insertGetKey(
                        LauCustomerAgentCreativeMappingBo.builder()
                                .customerId(accountBaseDto.getCustomerId())
                                .agentId(accountBaseDto.getDependencyAgentId())
                                .creativeId(creativeId)
                                .avid(avid)
                                .creativeAvidSource(Constants.GENERAL_VIDEO_BIND)
                                .build());
    }

    private BaseQuery<LauCustomerAgentCreativeMappingPo> getGeneralCreativeAvid(Integer creativeId, Long avid) {
        return adBqf.selectFrom(lauCustomerAgentCreativeMapping)
                .where(lauCustomerAgentCreativeMapping.creativeId.eq(creativeId))
                .whereIfNotNull(avid, lauCustomerAgentCreativeMapping.avid::eq)
                .where(lauCustomerAgentCreativeMapping.creativeAvidSource.eq(Constants.GENERAL_VIDEO_BIND))
                .where(lauCustomerAgentCreativeMapping.isDeleted.eq(Constants.INT_FALSE));

    }

    private BaseQuery<LauCustomerAgentCreativeMappingPo> getBilibiliCreativeMid(Integer creativeId, Long mid) {
        return adBqf.selectFrom(lauCustomerAgentCreativeMapping)
                .where(lauCustomerAgentCreativeMapping.creativeId.eq(creativeId))
                .whereIfNotNull(mid, lauCustomerAgentCreativeMapping.mid::eq)
                .where(lauCustomerAgentCreativeMapping.creativeAvidSource.eq(Constants.BILIBILI_MID_BIND))
                .where(lauCustomerAgentCreativeMapping.isDeleted.eq(Constants.INT_FALSE));

    }

    private Long getMidByAvid(Long avid) {
        try {
            if (Utils.isPositive(avid)) {
                //ArcReply arcReply = grpcManager.getArcReply(ArcRequest.newBuilder().setAid(avid).build());
                ArcReply arcReply = archiveServiceProxy.arc(avid);
                Arc arc = arcReply.getArc();
                return arc.getAuthor().getMid();
            }
        } catch (Exception e) {
           log.error("getMidByAvid error",e);
        }
        return null;
    }

    private void getMidsByAvids(List<VideoBindBo> videoBindList) throws Exception {

        List<Long> avidList = videoBindList.stream().map(a ->a.getAvid()).collect(Collectors.toList());

        Map<Long, Arc> arcReplyControlledMap = new HashMap<>();
        try {
            //ArcsReply arcsReply = grpcManager.getArcsReply(ArcsRequest.newBuilder().addAllAids(avidList).build());
            ArcsReply arcsReply = archiveServiceProxy.arcs(ArcsRequest.newBuilder().addAllAids(avidList).build());
            arcReplyControlledMap = ((arcsReply == null) || (arcsReply.getArcsCount() == 0)) ? Collections.emptyMap() : arcsReply.getArcsMap();
        } catch (Exception e) {
            throw new Exception("获取稿件信息失败");
        }
        for (VideoBindBo videoBindBo : videoBindList) {
            Long avid = videoBindBo.getAvid();
            videoBindBo.setMid(Optional.ofNullable(arcReplyControlledMap.get(avid)).map(p->p.getAuthor()).map(p->p.getMid()).orElse(null));
        }
    }

    private void checkOutCreativeVideoBind(Integer creativeId) {

        Integer id = adCoreBqf.selectFrom(lauUnitCreative)
                .where(lauUnitCreative.creativeId.eq(creativeId))
                .where(lauUnitCreative.isDeleted.eq(Constants.INT_FALSE))
                .where(lauUnitCreative.isVideoBind.eq(Constants.IS_BIND))
                .select(lauUnitCreative.creativeId)
                .fetchFirst();
        if (Objects.nonNull(id)) {
            //刷新-视频未绑定
            refreshCreativeExtDescription(creativeId, Constants.IS_NO_BIND);
        }
    }


    private Boolean isBusinessVideoStatus(Integer accountId, Long avid) {

        try {
            SoaGetAdAuthReqDto soaGetAdAuthReqDto = new SoaGetAdAuthReqDto();
            soaGetAdAuthReqDto.setAdvertisersAccountId(accountId);
            soaGetAdAuthReqDto.setAvid(avid);
            List<SoaAdAuthInfoDto> authInfoDtoList = soaAdAuth4AdpService.getAdAuthLimit1000(soaGetAdAuthReqDto);
            if (CollectionUtils.isNotEmpty(authInfoDtoList)
                    &&
                    authInfoDtoList.stream().anyMatch(authInfo ->
                            Objects.equals(authInfo.getState(), AuthOrderVideoAuthStateEnum.CONFIRMED.getCode())
                                    && Objects.equals(authInfo.getStateExt(), AuthOrderVideoAuthStateExtEnum.CONFIRM_AND_ACTIVE.getCode()))) {
                return true;
            }
        } catch (Exception e) {
            log.info("isBusinessVideoStatus Exception={}", e.getMessage());
        }
        return false;

    }


    /**
     * 此mid仅bilibili绑定
     */
    private Boolean isBilibiliVideoStatusNoEnterpriseBind(AccountBaseDto accountBaseDto, Long mid) {

        Long account = getBindMidAccount(accountBaseDto, mid, Constants.IS_NO_BIND);
        if (Utils.isPositive(account)) {
            return true;
        }
        return false;
    }

    /**
     * 此mid存在bilibili绑定
     */
    private Boolean isBilibiliVideoStatus(AccountBaseDto accountBaseDto, Long mid) {
        Long account = getBindMidAccount(accountBaseDto, mid, null);
        if (Utils.isPositive(account)) {
            return true;
        }
        return false;
    }


    private Long getBindMidAccount(AccountBaseDto accountBaseDto, Long mid, Integer isEnterpriseBind) {
        return adBqf.selectFrom(lauAccountCorpMidMapping)
                .where(lauAccountCorpMidMapping.customerId.eq(accountBaseDto.getCustomerId())
                        .and(lauAccountCorpMidMapping.agentId.eq(accountBaseDto.getDependencyAgentId()))
                        .and(lauAccountCorpMidMapping.mid.eq(mid))
                        .and(lauAccountCorpMidMapping.authStatus.eq(AccountMidMappingAuthStatusEnum.AUTHORIZED.getCode()))
                        .and(lauAccountCorpMidMapping.isDeleted.eq(Constants.INT_FALSE))
                        .and(lauAccountCorpMidMapping.isBilibiliBind.eq(Constants.IS_BIND)).or(lauAccountCorpMidMapping.type.in(BiliAccountTypeEnum.BUSINESS_NO_TYPES)))
                .whereIfNotNull(isEnterpriseBind, t -> lauAccountCorpMidMapping.isEnterpriseBind.eq(isEnterpriseBind))
                .fetchCount();
    }


    private List<Long> getBilibiliRejectMid(AccountBaseDto accountBaseDto, Integer isEnterpriseBind) {
        return adBqf.selectFrom(lauAccountCorpMidMapping)
                .where(lauAccountCorpMidMapping.customerId.eq(accountBaseDto.getCustomerId())
                        .and(lauAccountCorpMidMapping.agentId.eq(accountBaseDto.getDependencyAgentId()))
                        .and(lauAccountCorpMidMapping.authStatus.eq(AccountMidMappingAuthStatusEnum.INVALID.getCode()))
                        .and(lauAccountCorpMidMapping.isDeleted.eq(Constants.INT_FALSE))
                        .and(lauAccountCorpMidMapping.isBilibiliBind.eq(Constants.IS_BIND)).or(lauAccountCorpMidMapping.type.in(BiliAccountTypeEnum.BUSINESS_NO_TYPES)))
                .whereIfNotNull(isEnterpriseBind, t -> lauAccountCorpMidMapping.isEnterpriseBind.eq(isEnterpriseBind))
                .select(lauAccountCorpMidMapping.mid).fetch();
    }


    private void insertCustomerAgentCreative(Integer creativeId, AccountBaseDto accountBaseDto, Long mid) {

        if (Objects.nonNull(creativeId)) {
            //当前创意与mid是否存在bilibili绑定关系
            Integer mappingId = getCreativeMidMapping(creativeId, mid);
            if (Objects.isNull(mappingId)) {
                Integer id = adBqf.insert(lauCustomerAgentCreativeMapping)
                        .insertGetKey(
                                LauCustomerAgentCreativeMappingBo.builder()
                                        .customerId(accountBaseDto.getCustomerId())
                                        .agentId(accountBaseDto.getDependencyAgentId())
                                        .creativeId(creativeId)
                                        .mid(mid)
                                        .creativeAvidSource(Constants.BILIBILI_MID_BIND)
                                        .build());
            } else {
                adBqf.update(lauCustomerAgentCreativeMapping)
                        .where(lauCustomerAgentCreativeMapping.id.eq(mappingId))
                        .set(lauCustomerAgentCreativeMapping.customerId, accountBaseDto.getCustomerId())
                        .set(lauCustomerAgentCreativeMapping.agentId, accountBaseDto.getDependencyAgentId())
                        .set(lauCustomerAgentCreativeMapping.mid, mid)
                        .execute();
            }

        }


    }


    private void checkOutCustomerAgentCreative(Integer creativeId, Long mid) {
        Integer mappingId = getCreativeMidMapping(creativeId, mid);
        if (Objects.nonNull(mappingId)) {
            adBqf.update(lauCustomerAgentCreativeMapping)
                    .where(lauCustomerAgentCreativeMapping.id.eq(mappingId))
                    .set(lauCustomerAgentCreativeMapping.isDeleted, Constants.INT_TRUE)
                    .execute();
        }
    }

    private Integer getCreativeMidMapping(Integer creativeId, Long mid) {
        return adBqf.selectFrom(lauCustomerAgentCreativeMapping)
                .where(lauCustomerAgentCreativeMapping.creativeId.eq(creativeId))
                .whereIfNotNull(mid,lauCustomerAgentCreativeMapping.mid::eq)
                .where(lauCustomerAgentCreativeMapping.creativeAvidSource.eq(Constants.BILIBILI_MID_BIND))
                .where(lauCustomerAgentCreativeMapping.isDeleted.eq(Constants.INT_FALSE))
                .select(lauCustomerAgentCreativeMapping.id)
                .fetchFirst();
    }


    private void refreshCreativeExtDescription(Integer creativeId, Integer isVideoBind) {

        if (Objects.nonNull(creativeId)) {
            adCoreBqf.update(lauUnitCreative)
                    .where(lauUnitCreative.creativeId.eq(creativeId)
                            .and(lauUnitCreative.isDeleted.eq(Constants.INT_FALSE)))
                    .set(lauUnitCreative.isVideoBind, isVideoBind)
                    .execute();
        }
    }


}
