package com.bilibili.adp.cpc.databus.bos;

/**
 * <AUTHOR>
 * @date 2024/5/26 09:40
 */

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 原生稿件机审驳回特征
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class NativeArchiveMachineFeature {

    /**
     * 是否蓝v
     */
    private Integer isBlueV;

    /**
     * 是否挂链
     */
    private Integer hasLink;

    /**
     * 是否黑产
     */
    private Integer isBlackAsserts;

    /**
     * 是否私域
     */
    private Integer isPrivateDomain;

}
