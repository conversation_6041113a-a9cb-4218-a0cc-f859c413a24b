package com.bilibili.adp.cpc.biz.services.unit.strategy.game_activity_card;

import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.cpc.biz.services.tag.TagService;
import com.bilibili.adp.cpc.biz.services.unit.dto.NewCpcUnitDto;
import com.bilibili.adp.cpc.biz.services.unit.dto.UpdateCpcUnitDto;
import com.bilibili.adp.cpc.biz.services.unit.strategy.UnitExtraProcessStrategy;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauUnitTargetArchivePo;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauUnitTargetTagPo;
import com.bilibili.adp.cpc.dto.GameCardUnitTargetDto;
import com.bilibili.adp.cpc.dto.UnitTargetRuleArchiveDto;
import com.bilibili.adp.cpc.dto.UnitTargetRuleTagDto;
import com.bilibili.adp.cpc.enums.ad.PromotionPurposeType;
import com.bilibili.bjcom.querydsl.BaseQueryFactory;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.bilibili.adp.cpc.dao.config.PlatformMySqlConfig.*;
import static com.bilibili.adp.cpc.dao.ad_core.querydsl.QLauUnitTargetArchive.lauUnitTargetArchive;
import static com.bilibili.adp.cpc.dao.ad_core.querydsl.QLauUnitTargetTag.lauUnitTargetTag;

/**
 * 游戏活动卡单元额外处理策略
 *
 * <AUTHOR>
 * @date 2022-02-08 8:50 下午
 */
@Slf4j
@Service
public class GameActivityCardIUnitExtraProcessStrategy extends UnitExtraProcessStrategy {
    @Resource(name = AD_CORE_BQF)
    private BaseQueryFactory adCoreBqf;

    private final TagService tagService;

    public GameActivityCardIUnitExtraProcessStrategy(
        TagService tagService) {
        this.tagService = tagService;
    }


    @Override
    public Integer getPromotionPurposeType() {
        return PromotionPurposeType.GAME_ACTIVITY_CARD.getCode();
    }

    @Override
    public void processNewUnit(Integer unitId, NewCpcUnitDto unit) {
        // 由于游戏活动卡 可能不选择 「标签」 或 「视频」定向
        // 如果没传 直接返回
        // 保存定向信息
        GameCardUnitTargetDto targets = unit.getGameCardUnitTargets();
        if (Objects.isNull(targets)) {
            return;
        }
        // 处理稿件定向
        List<Long> avids = targets.getArchive().stream().map(UnitTargetRuleArchiveDto::getAvid)
            .collect(
                Collectors.toList());
        this.dealGameActivityCardUnitArchiveTargets(unitId, avids);
        //处理标签定向
        List<Long> tagIds = targets.getTag().stream().map(UnitTargetRuleTagDto::getTagId).collect(
            Collectors.toList());
        this.dealGameActivityCardUnitTagTargets(unitId, tagIds);
        // 保存tag信息
        List<String> tagNames = targets.getTag().stream().map(UnitTargetRuleTagDto::getTagName)
            .collect(Collectors.toList());
        tagService.saveTags(tagNames);
    }

    @Override
    public void processUpdateUnit(UpdateCpcUnitDto unit) {
        // 由于游戏活动卡 可能不选择 「标签」 或 「视频」定向
        // 如果没传 直接返回
        // 保存定向信息
        GameCardUnitTargetDto targets = unit.getGameCardUnitTargets();
        if (Objects.isNull(targets)) {
            return;
        }

        // 处理稿件定向
        List<Long> avids = targets.getArchive().stream().map(UnitTargetRuleArchiveDto::getAvid)
            .collect(
                Collectors.toList());
        this.dealGameActivityCardUnitArchiveTargets(unit.getUnitId(), avids);
        //处理标签定向
        List<Long> tagIds = targets.getTag().stream().map(UnitTargetRuleTagDto::getTagId).collect(
            Collectors.toList());
        this.dealGameActivityCardUnitTagTargets(unit.getUnitId(), tagIds);
        // 保存tag信息
        List<String> tagNames = targets.getTag().stream().map(UnitTargetRuleTagDto::getTagName)
            .collect(Collectors.toList());
        tagService.saveTags(tagNames);
    }

    @Override
    public void processDeleteUnit(List<Integer> unitIds) {
        super.processDeleteUnit(unitIds);
        adCoreBqf.update(lauUnitTargetArchive)
            .set(lauUnitTargetArchive.isDeleted, IsDeleted.DELETED.getCode())
            .where(lauUnitTargetArchive.unitId.in(unitIds)).execute();
        adCoreBqf.update(lauUnitTargetTag)
            .set(lauUnitTargetTag.isDeleted, IsDeleted.DELETED.getCode())
            .where(lauUnitTargetTag.unitId.in(unitIds)).execute();
    }

    /**
     * 处理 游戏卡单元 稿件定向
     *
     * @param unitId 单元id
     * @param avids  avid 列表
     * <AUTHOR>
     * @date 2022-01-30 4:26 下午
     */
    private void dealGameActivityCardUnitArchiveTargets(@NonNull Integer unitId,
        List<Long> avids) {
        if (CollectionUtils.isEmpty(avids)) {
            adCoreBqf.update(lauUnitTargetArchive)
                .set(lauUnitTargetArchive.isDeleted, IsDeleted.DELETED.getCode())
                .where(lauUnitTargetArchive.unitId.eq(unitId)).execute();
        }
        Set<Long> newAvids = new HashSet<>(avids);
        // 当前 单元 平台 对应的 有效 稿件定向
        Map<Long, Integer> avidMap = adCoreBqf
            .select(lauUnitTargetArchive.avid, lauUnitTargetArchive.id)
            .from(lauUnitTargetArchive)
            .where(lauUnitTargetArchive.unitId.eq(unitId))
            .where(lauUnitTargetArchive.isDeleted.eq(IsDeleted.VALID.getCode()))
            .fetch(LauUnitTargetArchivePo.class)
            .stream()
            .collect(
                Collectors.toMap(LauUnitTargetArchivePo::getAvid, LauUnitTargetArchivePo::getId));
        Set<Long> existAvids = new HashSet<>(avidMap.keySet());
        Set<Long> needInsertAvids = Sets.difference(newAvids, existAvids);
        Set<Long> needDeleteAvids = Sets.difference(existAvids, newAvids);

        adCoreBqf.insert(lauUnitTargetArchive).insertBeans(needInsertAvids.stream().map(
            avid -> {
                LauUnitTargetArchivePo po = new LauUnitTargetArchivePo();
                po.setUnitId(unitId);
                po.setAvid(avid);
                return po;
            }).collect(Collectors.toList()));

        adCoreBqf.update(lauUnitTargetArchive).updateBeans(needDeleteAvids.stream().map(
            avid -> {
                Integer id = avidMap.get(avid);
                LauUnitTargetArchivePo po = new LauUnitTargetArchivePo();
                po.setId(id);
                po.setIsDeleted(IsDeleted.DELETED.getCode());
                return po;
            }
        ).collect(Collectors.toList()));
    }

    /**
     * 处理 游戏活动卡单元 标签定向
     *
     * @param unitId 单元id
     * @param tagIds 标签id 列表
     * <AUTHOR>
     * @date 2022-01-30 4:26 下午
     */
    private void dealGameActivityCardUnitTagTargets(@NonNull Integer unitId,
        List<Long> tagIds) {
        if (CollectionUtils.isEmpty(tagIds)) {
            adCoreBqf.update(lauUnitTargetTag)
                .set(lauUnitTargetTag.isDeleted, IsDeleted.DELETED.getCode())
                .where(lauUnitTargetTag.unitId.eq(unitId)).execute();
        }
        Set<Long> newTagIds = new HashSet<>(tagIds);
        Map<Long, Integer> tagIdMap = adCoreBqf
            .select(lauUnitTargetTag.tagId, lauUnitTargetTag.id)
            .from(lauUnitTargetTag)
            .where(lauUnitTargetTag.unitId.eq(unitId))
            .where(lauUnitTargetTag.isDeleted.eq(IsDeleted.VALID.getCode()))
            .fetch(LauUnitTargetTagPo.class)
            .stream()
            .collect(Collectors.toMap(LauUnitTargetTagPo::getTagId, LauUnitTargetTagPo::getId));

        Set<Long> existTagIds = new HashSet<>(tagIdMap.keySet());
        Set<Long> needInsertTagIds = Sets.difference(newTagIds, existTagIds);
        Set<Long> needDeleteTagIds = Sets.difference(existTagIds, newTagIds);

        adCoreBqf.insert(lauUnitTargetTag).insertBeans(needInsertTagIds.stream().map(
            tagId -> {
                LauUnitTargetTagPo po = new LauUnitTargetTagPo();
                po.setUnitId(unitId);
                po.setTagId(tagId);
                return po;
            }).collect(Collectors.toList()));

        adCoreBqf.update(lauUnitTargetTag).updateBeans(needDeleteTagIds.stream().map(
            tagId -> {
                Integer id = tagIdMap.get(tagId);
                LauUnitTargetTagPo po = new LauUnitTargetTagPo();
                po.setId(id);
                po.setIsDeleted(IsDeleted.DELETED.getCode());
                return po;
            }
        ).collect(Collectors.toList()));
    }
}
