package com.bilibili.adp.cpc.databus.sub;

import com.alibaba.fastjson.JSONObject;
import com.bapis.ad.component.*;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.enums.OperatorType;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.biz.bos.WarpDatabusProperty;
import com.bilibili.adp.cpc.biz.bos.component.LauStoryComponentBo;
import com.bilibili.adp.cpc.biz.services.creative.CpcHelper4Story;
import com.bilibili.adp.cpc.biz.services.creative.StoryComponentService;
import com.bilibili.adp.cpc.databus.Constant;
import com.bilibili.adp.cpc.databus.bos.StoryComponentGoodsMessageBo;
import com.bilibili.adp.cpc.proxy.CpmScvProxy;
import com.bilibili.warp.databus.AckableMessage;
import com.bilibili.warp.databus.DatabusProperty;
import com.bilibili.warp.databus.MessageListener;
import io.grpc.Status;
import io.grpc.StatusRuntimeException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import pleiades.venus.starter.rpc.client.RPCClient;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Story组件商品信息 binlog 消费
 **/
@Component
@Slf4j
public class StoryComponentGoodsBinlogSub implements MessageListener {

    @Value("${databus.consume.switch.storyComponentGoods.enable:1}")
    private Integer switchEnable;
    @Autowired
    private StoryComponentService storyComponentService;
    @Autowired
    private CpcHelper4Story cpcHelper4Story;
    @Resource
    private CpmScvProxy cpmScvProxy;

    private static final int BIZ_FROM_SANLIAN = 1;
    private static final int GOODS_SHELVES_STATUS_ON = 1;
    private static final int GOODS_SHELVES_STATUS_OFF = 2;

    private final String topic;
    private final String group;

    @Override
    public String topic() {
        return this.topic;
    }

    @Override
    public String group() {
        return this.group;
    }

    public StoryComponentGoodsBinlogSub(WarpDatabusProperty warpDatabusProperty) {
        // 注入DatabusProperties，此处的ALIAS常量即配置文件中填写的别名，是topic的上一级配置键
        // pub场景同理，在使用DatabusTemplate的地方注入DatabusProperties来获取
        Map<String, DatabusProperty> properties = warpDatabusProperty.getProperties();
        DatabusProperty property = properties.get("story-component-goods");
        log.info("story-component-goods, property={}", JSONObject.toJSONString(property));
        this.topic = property.getTopic();
        this.group = property.getSub().getGroup();
    }

    @PostConstruct
    public void init() {
        log.info("StoryComponentGoodsBinlogSub started successfully!");
    }

    @Override
    public void onMessage(AckableMessage ackableMessage) {
        try {
            if (!Utils.isPositive(switchEnable)) {
                return;
            }
            String value = new String(ackableMessage.payload());
            JSONObject msg = JSONObject.parseObject(value);
            String action = msg.getString(Constant.ACTION);
            if (!Constant.UPDATE.equals(action)) {
                return;
            }

            log.info("StoryComponentGoodsBinlogSub onMessage: {}", msg);
            JSONObject oldObject = msg.getJSONObject(Constant.OLD);
            StoryComponentGoodsMessageBo oldBo = deserializeBinlogDto(oldObject);
            JSONObject newObject = msg.getJSONObject(Constant.NEW);
            StoryComponentGoodsMessageBo newBo = deserializeBinlogDto(newObject);
            // 非三连的消息不处理
            if (oldBo.getBizFrom() != BIZ_FROM_SANLIAN) {
                ackableMessage.ack();
                return;
            }

            boolean goodsInfoIsChanged = goodsInfoIsChanged(oldBo, newBo);
            boolean goodsInfoIsChangedForCreativeReAudit = goodsInfoIsChangedForCreativeReAudit(oldBo, newBo);
            log.info("StoryComponentGoodsBinlogSub itemId: {}, goodsInfoIsChanged: {}, goodsInfoIsChangedForCreativeReAudit: {}", newBo.getItemId(), goodsInfoIsChanged, goodsInfoIsChangedForCreativeReAudit);
            if (!goodsInfoIsChanged && !goodsInfoIsChangedForCreativeReAudit) {
                ackableMessage.ack();
                return;
            }

            // 查询使用了商品的组件
            StoryComponents imageStoryComponents = cpmScvProxy.getImageStoryComponents(oldBo.getItemId());
            StoryComponents priceDifferenceStoryComponents = cpmScvProxy.getPriceDifferenceStoryComponents(oldBo.getItemId());

            log.info("StoryComponentService image storyComponent: {}", imageStoryComponents);
            log.info("StoryComponentService priceDifference storyComponent: {}", priceDifferenceStoryComponents);
            Operator operator = new Operator();
            operator.setOperatorName("商品审核消息");
            operator.setOperatorType(OperatorType.SYSTEM);

            if (goodsInfoIsChanged) {
                storyComponentService.updateComponentGoodsInfo(imageStoryComponents, priceDifferenceStoryComponents, newBo, oldBo, operator);
            }
            if (goodsInfoIsChangedForCreativeReAudit) {
                creativeReAudit(oldBo.getItemId(), imageStoryComponents, priceDifferenceStoryComponents, operator);
            }

            ackableMessage.ack();
        } catch (Exception e) {
            log.error("StoryComponentGoodsBinlogSub ackableMessage:{}, error:", ackableMessage, e);
        }
    }

    // 创意重新审核
    private void creativeReAudit(long itemId, StoryComponents imageStoryComponents, StoryComponents priceDifferenceStoryComponents, Operator operator) {
        log.info("StoryComponentGoodsBinlogSub creativeReAudit, itemId:{}", itemId);
        long componentId;
        int componentType;
        try {
            for (StoryComponent storyComponent : imageStoryComponents.getComponentsList()) {
                componentId = storyComponent.getBaseComponent().getId();
                componentType = storyComponent.getBaseComponent().getType();
                doCreativeReAudit(itemId, componentId, componentType, operator);
            }
        } catch (StatusRuntimeException e) {
            Status status = e.getStatus();
            String errorMessage = e.getMessage();
            log.info("StoryComponentGoodsBinlogSub error code:{}, description:{}, errorMessage:{}", status.getCode(), status.getDescription(), errorMessage);
            if (!e.getStatus().getCode().equals(Status.NOT_FOUND.getCode())) {
                throw e; // 抛出不是 NOT_FOUND 的异常
            }
        } catch (Exception e) {
            log.info("StoryComponentGoodsBinlogSub exception type:{} ", e.getClass().getName());
            throw e; // 抛出其他类型的异常
        }

        try {
            for (StoryComponent storyComponent : priceDifferenceStoryComponents.getComponentsList()) {
                componentId = storyComponent.getBaseComponent().getId();
                componentType = storyComponent.getBaseComponent().getType();
                doCreativeReAudit(itemId, componentId, componentType, operator);
            }
        } catch (StatusRuntimeException e) {
            Status status = e.getStatus();
            String errorMessage = e.getMessage();
            log.info("StoryComponentGoodsBinlogSub error code:{}, description:{}, errorMessage:{}", status.getCode(), status.getDescription(), errorMessage);
            if (!e.getStatus().getCode().equals(Status.NOT_FOUND.getCode())) {
                throw e; // 抛出不是 NOT_FOUND 的异常
            }
        } catch (Exception e) {
            log.info("StoryComponentGoodsBinlogSub exception type:{} ", e.getClass().getName());
            throw e; // 抛出其他类型的异常
        }
    }

    private void doCreativeReAudit(long itemId, long componentId, int componentType, Operator operator) {
        log.info("StoryComponentGoodsBinlogSub doCreativeReAudit itemId:{} componentId:{}, componentType:{}", itemId, componentId, componentType);
        List<Integer> needPushCommonCreatives = new ArrayList<>();
        List<Integer> needPushProgrammaticCreatives = new ArrayList<>();

        LauStoryComponentBo bo = new LauStoryComponentBo();
        bo.setComponentId(componentId);
        bo.setComponentType(componentType);
        storyComponentService.getAffectedCreatives(bo, operator, needPushCommonCreatives, needPushProgrammaticCreatives);
        log.info("StoryComponentGoodsBinlogSub doCreativeReAudit, itemId:{}, componentId:{}, componentType:{}, needPushCommonCreatives:{}, needPushProgrammaticCreatives:{}", itemId, componentId, componentType, needPushCommonCreatives, needPushProgrammaticCreatives);
        // 推审
        if (!CollectionUtils.isEmpty(needPushCommonCreatives)) {
            cpcHelper4Story.creativeAuditPubOrProgrammatic(needPushCommonCreatives, 0);
        }
        if (!CollectionUtils.isEmpty(needPushProgrammaticCreatives)) {
            cpcHelper4Story.creativeAuditPubOrProgrammatic(needPushProgrammaticCreatives, 1);
        }
    }

    private StoryComponentGoodsMessageBo deserializeBinlogDto(JSONObject jsonObject) {
        if (Objects.isNull(jsonObject)) {
            return null;
        }
        return jsonObject.toJavaObject(StoryComponentGoodsMessageBo.class);
    }

    // 商品信息变化：针对创意重审，只关注 商品链接、状态
    private boolean goodsInfoIsChangedForCreativeReAudit(StoryComponentGoodsMessageBo oldBo, StoryComponentGoodsMessageBo newBo) {
        return !Objects.equals(oldBo.getJumpUrl(), newBo.getJumpUrl())
                || (oldBo.getShelvesStatus() == GOODS_SHELVES_STATUS_ON && newBo.getShelvesStatus() == GOODS_SHELVES_STATUS_OFF);
    }

    // 商品信息变化: 更新组件中商品相关信息
    private boolean goodsInfoIsChanged(StoryComponentGoodsMessageBo oldBo, StoryComponentGoodsMessageBo newBo) {
        return !Objects.equals(oldBo.getJumpUrl(), newBo.getJumpUrl()) ||
                !Objects.equals(oldBo.getUrlType(), newBo.getUrlType()) ||
                !Objects.equals(oldBo.getSourceType(), newBo.getSourceType()) ||
                !Objects.equals(oldBo.getSchemaUrl(), newBo.getSchemaUrl()) ||
                !Objects.equals(oldBo.getMiniAppOriginId(), newBo.getMiniAppOriginId()) ||
                !Objects.equals(oldBo.getMiniAppName(), newBo.getMiniAppName()) ||
                !Objects.equals(oldBo.getMiniAppPath(), newBo.getMiniAppPath());
    }


}
