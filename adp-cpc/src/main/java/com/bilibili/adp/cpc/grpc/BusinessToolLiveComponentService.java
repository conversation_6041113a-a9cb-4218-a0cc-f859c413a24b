package com.bilibili.adp.cpc.grpc;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bapis.ad.adp.live_component.*;
import com.bapis.ad.business.acc.BusinessAccountServiceGrpc;
import com.bapis.ad.business.acc.LauAppConversionComponentBo;
import com.bapis.ad.business.acc.QueryAppPackageToolReply;
import com.bapis.ad.business.acc.QueryAppPackageToolReq;
import com.bapis.ad.component.UrlType;
import com.bapis.ad.mgk.business_tool.*;
import com.bilibili.adp.common.Constants;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.biz.services.archive.BusinessToolArchiveConversionComponentService;
import com.bilibili.adp.cpc.biz.services.enterprise_wechat.EnterpriseWechatQuerier;
import com.bilibili.adp.cpc.biz.services.enterprise_wechat.dto.EnterpriseWechatDto;
import com.bilibili.adp.cpc.dao.ad.LauLiveConversionComponentDao;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauCampaignPo;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauUnitCreativePo;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauUnitPo;
import com.bilibili.adp.cpc.enums.component.BusinessToolComponentStatus;
import com.bilibili.adp.cpc.enums.component.BusinessToolComponentType;
import com.bilibili.adp.cpc.po.ad.LauLiveConversionComponentPo;
import com.bilibili.adp.cpc.po.ad.LauLiveConversionComponentPoExample;
import com.bilibili.adp.cpc.proxy.MallCBusinessAccountProxy;
import com.bilibili.adp.http.utils.OkHttpUtils;
import com.bilibili.bjcom.querydsl.BaseQueryFactory;
import com.bilibili.crm.platform.api.account.dto.AccountBaseDto;
import com.bilibili.crm.platform.soa.ISoaQueryAccountService;
import com.bilibili.mgk.platform.api.landing_page.soa.ISoaLandingPageService;
import com.bilibili.mgk.platform.common.page_bean.MgkLandingPageBean;
import com.google.common.base.Throwables;
import com.google.common.collect.Lists;
import io.grpc.Status;
import io.grpc.stub.StreamObserver;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import okhttp3.Response;
import okhttp3.ResponseBody;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.util.UriComponentsBuilder;
import pleiades.venus.starter.rpc.client.RPCClient;
import pleiades.venus.starter.rpc.server.RPCService;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

import static com.bilibili.adp.cpc.dao.ad_core.querydsl.QLauCampaign.lauCampaign;
import static com.bilibili.adp.cpc.dao.config.PlatformMySqlConfig.AD_CORE_BQF;
import static com.bilibili.adp.cpc.dao.ad_core.querydsl.QLauUnit.lauUnit;
import static com.bilibili.adp.cpc.dao.ad_core.querydsl.QLauUnitCreative.lauUnitCreative;


@Slf4j
@Service
@RPCService
@RequiredArgsConstructor
public class BusinessToolLiveComponentService extends LiveComponentServiceGrpc.LiveComponentServiceImplBase {

    private final LauLiveConversionComponentDao liveConversionComponentDao;
    private final MgkBusinessToolServiceGrpc.MgkBusinessToolServiceBlockingStub toolServiceBlockingStub;
    private final ISoaQueryAccountService soaQueryAccountService;
    private final ISoaLandingPageService soaLandingPageService;
    @Resource(name = AD_CORE_BQF)
    private BaseQueryFactory adCoreBqf;
    private final EnterpriseWechatQuerier enterpriseWechatQuerier;

    @Value("${business.tool.live.template.id:643}")
    private Integer toolLiveTemplateId;
    private final MallCBusinessAccountProxy mallCBusinessAccountProxy;


    //半屏全屏逻辑控制:   __GAONENGPLAYSTYLE__
    // 0: 直接跳转， 1:半屏拉起
    private static final String GAONENG_PLAY_STYLE_PARAM = "gaoneng_play_style";
    private static final String GAONENG_PLAY_STYLE = "__GAONENGPLAYSTYLE__";

    //半屏拉起后是否支持上滑逻辑控制:
    private static final String GAONENG_HALF_STYLE_PARAM = "gaoneng_half_style";
    private static final String GAONENG_HALF_STYLE = "__GAONENGHALFSTYLE__";

    //组件trackid，下发的永远是组件的创意信息
    private static final String ASSEMBLY_TRACKID_PARAM = "assembly_track_id";
    private static final String ASSEMBLY_TRACKID = "__ASSEMBLYTRACKID__";

    //live_assembly_card=__LIVEASSEMBLYCARD__    0: 不是直播组件卡广告， 1:是直播组件卡广告
    private static final String LIVE_ASSEMBLY_CARD_PARAM = "live_assembly_card";
    private static final String LIVE_ASSEMBLY_CARD = "__LIVEASSEMBLYCARD__";

    private static final String DEFAULT_BUTTON_TEXT = "立即预约";
    private static final String BOOKING_SUB_TITLE = "人预约中";
    private static final String CONSULT_SUB_TITLE = "人咨询中";

    private static final Integer SUB_TITLE_TYPE_JOINT = 1;
    private static final Integer SUB_TITLE_TYPE_FULL_TEXT = 2;

    @Value("${query.shop.id.url:http://uat-mall.bilibili.co/business-account/intranet/account/queryUserShopInfo}")
    private String queryShopIdUrl;

    @Getter
    @Value("${component.miniGame.h5BottomLink:http://www.bilibili.com}")
    private String miniGameH5BottomLink;

    private final List<Integer> ignoreFromUriBusinessToolTypeList = Lists.newArrayList(
            BusinessToolTypeEnum.WORK_WECHAT_VALUE,
            BusinessToolTypeEnum.THIRD_PARTY_LANDING_PAGE_URL_VALUE,
            BusinessToolTypeEnum.APP_PACKAGE_VALUE
    );

    /**
     * <pre>
     * 新增直播转化组件
     * </pre>
     */
    public void addLiveComponents(AddLiveComponentsReq request,
                                  StreamObserver<AddLiveComponentReply> responseObserver) {
        try {

            log.info("addLiveComponents request : {}", request.toString());
            var accountId = request.getAccountId();
            validParam(request);

            int businessToolType = request.getBusinessToolType();
            String originUrl = "";
            String androidConversionUrl = "";
            String iosConversionUrl = "";

            Integer miniGameId = 0;
            if (Objects.equals(businessToolType, BusinessToolTypeEnum.THIRD_PARTY_LANDING_PAGE_URL_VALUE)) {
                QueryThirdPartyLandingPageByPageIdsReq queryThirdPartyLandingPageByPageIdsReq = QueryThirdPartyLandingPageByPageIdsReq.newBuilder()
                        .addAllPageIds(Collections.singletonList(Long.parseLong(request.getToolId())))
                        .build();
                QueryThirdPartyLandingPageByPageIdsReply thirdPartyLandingPageByPageId = toolServiceBlockingStub.getThirdPartyLandingPageByPageId(queryThirdPartyLandingPageByPageIdsReq);
                List<QueryThirdPartyLandingPageByPageIdInfo> thirdPartyLandingPageInfosList = thirdPartyLandingPageByPageId.getThirdPartyLandingPageInfosList();
                if (!CollectionUtils.isEmpty(thirdPartyLandingPageInfosList)) {
                    QueryThirdPartyLandingPageByPageIdInfo thirdPartyLandingPageInfo = thirdPartyLandingPageInfosList.get(0);
                    originUrl = thirdPartyLandingPageInfo.getPageUrl();
                }
            } else if (Objects.equals(businessToolType, BusinessToolTypeEnum.BOOKING_VALUE)) {
                MgkLandingPageBean mgkLandingPageBean = soaLandingPageService
                        .validatePageIdAndGetLandingPage(request.getToolId());
                originUrl = mgkLandingPageBean.getLaunchUrlSecondary();
            } else if (Objects.equals(businessToolType, BusinessToolTypeEnum.ONLINE_CONSULT_VALUE)) {
                Integer shopId = queryUserShopId(String.valueOf(request.getUid()));
                originUrl = "bilibili://link/customer/chat?shop_father_id=3" + "&shop_id=" + shopId;

            } else if (Objects.equals(businessToolType, BusinessToolTypeEnum.APPLETS_VALUE)) {
                originUrl = miniGameH5BottomLink;
                miniGameId = Integer.parseInt(request.getToolId());

            } else if (Objects.equals(businessToolType, BusinessToolTypeEnum.WORK_WECHAT_VALUE)) {
                EnterpriseWechatDto enterpriseWechatDto = enterpriseWechatQuerier.queryEnterpriseWechatInfo(accountId, Long.parseLong(request.getToolId()));
                originUrl = enterpriseWechatDto.getUrl();
            } else if (Objects.equals(businessToolType, BusinessToolTypeEnum.APP_PACKAGE_VALUE)) {
                QueryAppPackageToolReply appPackageToolReply = mallCBusinessAccountProxy.queryAppPackageTool(request.getToolId());
                log.info("appPackageToolReply={}", appPackageToolReply);
                Assert.isTrue(!appPackageToolReply.getBosList().isEmpty(), "应用组件不存在");
                LauAppConversionComponentBo appConversionComponentBo = appPackageToolReply.getBos(0);
                originUrl = appConversionComponentBo.getPcUrl();
                androidConversionUrl = appConversionComponentBo.getAndroidConversionUrl();
                iosConversionUrl = appConversionComponentBo.getIosConversionUrl();
                if (Utils.isPositive(appConversionComponentBo.getAndroidUrlPageId()) && UrlType.GAONENG_VALUE == appConversionComponentBo.getAndroidUrlType()) {
                    androidConversionUrl = genUrl(appConversionComponentBo.getAndroidUrlPageId());
                }
                if (Utils.isPositive(appConversionComponentBo.getIosUrlPageId()) && UrlType.GAONENG_VALUE == appConversionComponentBo.getIosUrlType()) {
                    iosConversionUrl = genUrl(appConversionComponentBo.getIosUrlPageId());
                }
            }

            String url = originUrl;
            if (!ignoreFromUriBusinessToolTypeList.contains(businessToolType)) {
                url = UriComponentsBuilder.fromUriString(originUrl)
                        .query(Constants.MGK_PAGE_MACRO_PARAM)
                        .queryParam(GAONENG_PLAY_STYLE_PARAM, GAONENG_PLAY_STYLE)
                        .queryParam(GAONENG_HALF_STYLE_PARAM, GAONENG_HALF_STYLE)
                        .queryParam(ASSEMBLY_TRACKID_PARAM, ASSEMBLY_TRACKID)
                        .queryParam(LIVE_ASSEMBLY_CARD_PARAM, 1)
                        .build(false)
                        .toUriString();
            }
            //应用推广 conversion 参见 lau_app_conversion_component


            LauLiveConversionComponentPoExample poExample = new LauLiveConversionComponentPoExample();
            poExample.or().andRoomIdEqualTo(request.getRoomId()).andToolIdEqualTo(request.getToolId())
                    .andComponentTypeEqualTo(BusinessToolComponentType.LANDING_PAGE.getCode())
                    .andStatusEqualTo(BusinessToolComponentStatus.LAUNCH.getCode());

            var components = liveConversionComponentDao.selectByExample(poExample);
            if (CollectionUtils.isEmpty(components)) {
                //构造空计划
                LauCampaignPo campaign = new LauCampaignPo();
                campaign.setAccountId(accountId);
                Integer campaignId = adCoreBqf.insert(lauCampaign).insertGetKey(campaign);
                //构造空单元
                LauUnitPo unit = new LauUnitPo();
                unit.setAccountId(accountId);
                unit.setCampaignId(campaignId);
                Integer unitId = adCoreBqf.insert(lauUnit).insertGetKey(unit);
                //构造空创意
                LauUnitCreativePo creative = new LauUnitCreativePo();
                creative.setAccountId(accountId);
                creative.setCampaignId(campaignId);
                creative.setUnitId(unitId);
                creative.setCreativeType(0);
                creative.setTemplateId(toolLiveTemplateId);
                creative.setAuditStatus(0);
                creative.setStatus(0);
                Integer creativeId = adCoreBqf.insert(lauUnitCreative).insertGetKey(creative);

                //保存评论转化组件
                AccountBaseDto accountBaseDto = soaQueryAccountService.getAccountBaseDtoById(accountId);
                Assert.notNull(accountBaseDto, "账户信息不能为空");

                int formSubmitCount = 0;

                if (Objects.equals(businessToolType, BusinessToolTypeEnum.BOOKING_VALUE)) {
                    PageFormSumDataReply dataReply = toolServiceBlockingStub.getPageSumData(PageWithTimeReq.newBuilder()
                            .setAccountId(accountId)
                            .setMgkPageId(Long.parseLong(request.getToolId())).build());
                    formSubmitCount = dataReply.getFormSubmitCount();
                }


                String subTitle = BOOKING_SUB_TITLE;
                String requestSubTitle = request.getSubTitle();

                if (org.apache.commons.lang3.StringUtils.isNotBlank(requestSubTitle)) {
                    subTitle = requestSubTitle;
                } else {
                    if (Objects.equals(request.getBusinessToolType(), BusinessToolTypeEnum.BOOKING_VALUE) || Objects.equals(request.getBusinessToolType(), BusinessToolTypeEnum.THIRD_PARTY_LANDING_PAGE_URL_VALUE)) {
                        subTitle = BOOKING_SUB_TITLE;
                    } else if (Objects.equals(request.getBusinessToolType(), BusinessToolTypeEnum.ONLINE_CONSULT_VALUE)) {
                        subTitle = CONSULT_SUB_TITLE;
                    }
                }
                Integer subTitleType = SUB_TITLE_TYPE_JOINT;
                if (Objects.equals(request.getBusinessToolType(), BusinessToolTypeEnum.APPLETS_VALUE) || Objects.equals(request.getBusinessToolType(), BusinessToolTypeEnum.APP_PACKAGE_VALUE)) {
                    subTitleType = SUB_TITLE_TYPE_FULL_TEXT;
                }

                liveConversionComponentDao.insertUpdateSelective(LauLiveConversionComponentPo.builder()
                        .accountId(request.getAccountId()).componentType(BusinessToolComponentType.LANDING_PAGE.getCode())
                        .campaignId(campaignId).unitId(unitId).creativeId(creativeId).scene(request.getScene())
                        .mgkPageId(Long.parseLong(request.getToolId())).roomId(request.getRoomId())
                        .status(BusinessToolComponentStatus.LAUNCH.getCode()).toolId(request.getToolId())
                        .uid(request.getUid())
                        .buttonText(org.apache.commons.lang3.StringUtils.isNotBlank(request.getButtonText()) ? request.getButtonText() : DEFAULT_BUTTON_TEXT)
                        .bookingOuterType(request.getBookingOuterType())
                        .subTitle(subTitle)
                        .agentId(accountBaseDto.getDependencyAgentId())
                        .customerId(accountBaseDto.getCustomerId())
                        .cardSamllImageUrl(request.getImageUrl()).cardTitle(request.getTitle())
                        .conversionUrl(url)
                        .androidConversionUrl(androidConversionUrl)
                        .iosConversionUrl(iosConversionUrl)
                        .formSubmit(formSubmitCount)
                        .miniGameId(miniGameId)
                        .businessToolType(businessToolType)
                        .subTitleType(subTitleType)
                        .build());
                responseObserver.onNext(AddLiveComponentReply.newBuilder().setCreativeId(creativeId)
                        .setSuccess(true).build());
            } else {
                var component = components.get(0);
                responseObserver.onNext(AddLiveComponentReply.newBuilder().setCreativeId(component.getCreativeId())
                        .setSuccess(true).build());
            }
            responseObserver.onCompleted();
        } catch (IllegalArgumentException t) {
            responseObserver.onError(Status.INVALID_ARGUMENT
                    .withDescription(t.getMessage())
                    .asRuntimeException());
            log.error("addLiveComponents 失败,{}", Throwables.getStackTraceAsString(t));
        } catch (Throwable t) {
            responseObserver.onError(Status.INTERNAL
                    .withDescription(t.getMessage())
                    .asRuntimeException());
            log.error("addLiveComponents 失败,{}", Throwables.getStackTraceAsString(t));
        }
    }

    private void validParam(AddLiveComponentsReq request) {
        Assert.isTrue(Utils.isPositive(request.getAccountId()), "账号id不能为空");
        Assert.isTrue(Utils.isPositive(request.getRoomId()), "直播房间id不能为空");
        Assert.isTrue(Utils.isPositive(request.getUid()), "直播uid不能为空");
        Assert.isTrue(StringUtils.hasText(request.getToolId()), "组件id不能为空");
    }

    /**
     * <pre>
     * 解绑转化组件
     * </pre>
     */
    public void disableComponent(DeleteLiveComponentReq request,
                                 StreamObserver<DeleteLiveComponentReply> responseObserver) {


        try {
            LauLiveConversionComponentPoExample poExample = new LauLiveConversionComponentPoExample();
            poExample.or().andCreativeIdEqualTo((int) request.getCreativeId())
                    .andComponentTypeEqualTo(BusinessToolComponentType.LANDING_PAGE.getCode())
                    .andStatusEqualTo(BusinessToolComponentStatus.LAUNCH.getCode());
            liveConversionComponentDao.updateByExampleSelective(LauLiveConversionComponentPo.builder()
                    .status(BusinessToolComponentStatus.DOWN.getCode()).build(), poExample);
            responseObserver.onNext(DeleteLiveComponentReply.newBuilder().setCreativeId(request.getCreativeId())
                    .setSuccess(true).build());
            responseObserver.onCompleted();
        } catch (IllegalArgumentException t) {
            responseObserver.onError(Status.INVALID_ARGUMENT
                    .withDescription(t.getMessage())
                    .asRuntimeException());
            log.error("addLiveComponents:disableComponent 失败,{}", Throwables.getStackTraceAsString(t));
        } catch (Throwable t) {
            responseObserver.onError(Status.INTERNAL
                    .withDescription(t.getMessage())
                    .asRuntimeException());
            log.error("addLiveComponents:disableComponent 失败,{}", Throwables.getStackTraceAsString(t));
        }
    }

    public Integer queryUserShopId(String uid) throws IOException {
        Response response = OkHttpUtils.get(queryShopIdUrl)
                .param("uid", uid)
                .header("Content-Type", "application/json")
                .callForResponse();
        ResponseBody body = response.body();

        JSONObject jsonObject = JSON.parseObject(body.string());
        JSONObject data = jsonObject.getJSONObject("data");

        if (Objects.isNull(data)) {
            throw new IllegalArgumentException("查询用户店铺信息失败");
        }
        Integer shopId = data.getInteger("shopId");
        return shopId;
    }

    private String genUrl(Long pageId) {
        if (Utils.isPositive(pageId)) {
            MgkLandingPageBean mgkLandingPageBean = soaLandingPageService.validatePageIdAndGetLandingPage(String.valueOf(pageId));
            return UriComponentsBuilder.fromUriString(mgkLandingPageBean.getLaunchUrlSecondary())
                    // 宏替换
                    .query(BusinessToolArchiveConversionComponentService.BUSINESS_TOOL_MGK_PAGE_MACRO_PARAM)
                    .build(false)
                    .toUriString();
        }
        return null;
    }

    public Integer updateUrl(Long id, String url) {

        LauLiveConversionComponentPoExample example = new LauLiveConversionComponentPoExample();
        example.or().andIdEqualTo(id);
        int executeCount = liveConversionComponentDao.updateByExampleSelective(LauLiveConversionComponentPo.builder()
                .conversionUrl(url).build(), example);
        return executeCount;
    }
}
