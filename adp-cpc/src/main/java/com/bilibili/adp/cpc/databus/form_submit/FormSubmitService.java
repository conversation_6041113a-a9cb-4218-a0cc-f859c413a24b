package com.bilibili.adp.cpc.databus.form_submit;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.cpc.dao.ad.LauLiveConversionComponentDao;
import com.bilibili.adp.cpc.databus.AdpCpcDatabusConfig;
import com.bilibili.adp.cpc.databus.DatabusClient;
import com.bilibili.adp.cpc.databus.form_submit.dto.FormSubmitMsgDto;
import com.bilibili.adp.cpc.po.ad.LauLiveConversionComponentPoExample;
import com.bilibili.adp.util.common.DistributedLock;
import com.google.common.base.Throwables;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.redisson.api.RLock;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

@Slf4j
@Service
@RequiredArgsConstructor
public class FormSubmitService {
    private final LauLiveConversionComponentDao liveConversionComponentDao;
    private final DistributedLock distributedLock;

    @Resource(name = AdpCpcDatabusConfig.FORM_SUBMIT_SUB)
    private DatabusClient client;

    private static final String LIVE_FORM_SUBMIT = "LIVE_FORM_SUBMIT";
    public static final String SUB_LOCK_SUFFIX = ":form-sub-lock:adp";

    @PostConstruct
    public void sub() {
        if (client.isEnabled()) {
            log.info("FormSubmit开始消费");
            client.getExecutorService().execute(() -> client.getSub().sub(this::exec));
        }
    }

    @SneakyThrows
    private void exec(JSON json) {
        try {
            final FormSubmitMsgDto msg = json.toJavaObject(FormSubmitMsgDto.class);
            if(LIVE_FORM_SUBMIT.equals(msg.getConvType())){
                LauLiveConversionComponentPoExample poExample = new LauLiveConversionComponentPoExample();
                poExample.or().andMgkPageIdEqualTo(msg.getPageId());
                RLock lock = distributedLock.getLock(msg.getPageId(), SUB_LOCK_SUFFIX);
                try {
                    var pos = liveConversionComponentDao.selectByExample(poExample);
                    if(!CollectionUtils.isEmpty(pos)){
                        pos.forEach(po->{
                            log.info("update liveComponent [{}]", po.getId());
                            int count = po.getFormSubmit();
                            po.setFormSubmit(count+1);
                            liveConversionComponentDao.updateByPrimaryKeySelective(po);
                        });
                    }
                } finally {
                    log.info("---FormSubmitSub unLock :{}{}----", msg.getPageId(), SUB_LOCK_SUFFIX);
                    lock.unlock();
                }
            }
        } catch (Exception e) {
            log.error("FormSubmitService msg[{}] err [{}]", json, Throwables.getStackTraceAsString(e));
        }

    }
}
