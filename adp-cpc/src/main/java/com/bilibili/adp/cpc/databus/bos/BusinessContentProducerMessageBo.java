package com.bilibili.adp.cpc.databus.bos;

import lombok.*;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @date 2024/5/15
 **/

@EqualsAndHashCode(of = {"contentId", "contentType", "firstBusinessType"})
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BusinessContentProducerMessageBo implements Serializable {

    private static final long serialVersionUID = 2279593035925360436L;

    /**
     * 内容ID。avid/ssid/room_id/dynamic_id/专栏id
     */
//    @NotNull
    private Long contentId;

    /**
     * ContentTypeEnum
     */
//    @NotNull
    private Integer contentType;

    /**
     * 内容来源。ContentFromEnum
     */
//    @NotNull
    private Integer contentFrom;

    /**
     * 是否商业治理稿件。GovernTypeEnum
     */
//    @NotNull
    private Integer governType;
    /**
     * ConsumerTypeEnum，如无特殊诉求，全都传。
     */
//    @NotNull
    private List<Integer> consumerTypes;

    /**
     * 一级商业类型，FirstBusinessTypeEnum
     */
//    @NotNull
    private Integer firstBusinessType;

    /**
     * 二级商业类型，SecondBusinessTypeEnum
     */
//    @NotNull
    private Integer secondBusinessType;
    /**
     * 小时的时间戳，单位ms，用来确定顺序
     */
//    @NotNull
    private Long messageTime;

    /**
     * 是否敏感账号。1：否，2：是
     */
    private Integer isSensitive;

    /**
     *
     */
    private Integer accountId;

    /**
     * 是否内外广。1：否，2：是
     */
    private Integer isInner;

    /**
     * 品牌ID
     */
    private Long productId;

    /**
     * 品牌名称
     */
    private String productName;

    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 集团ID
     */
    private Long groupId;

    /**
     * 集团名称
     */
    private String groupName;

    /**
     * 是否头部客户。1：否，2：是
     */
    private Integer isTopCustomer;

    /**
     * 一级行业
     */
    private String categoryFirstName;

    /**
     * 二级行业
     */
    private String categorySecondName;

    /**
     * 管控优先级 1：普通，2：高优，3：低优
     */
    private Integer controlPriority;

    /**
     * 管控截止期。格式yyyy-MM-dd HH:mm:ss
     */
    private String controlEndTime;

    /**
     * 是否原生。1：否，2：是
     */
    private Integer isOriginalAd;

    /**
     * mid
     */
    private Long mid;

    /**
     * 是否豁免 1：取消，2：生效，不填默认取消
     */
    private Integer exemptionType;

    /**
     * 豁免环节 1：自动打击，2：原生规则回查
     *
     */
    private Integer exemptionLink;

    /**
     * 1：否，2：是，不传默认为1否
     * for豁免回查消费
     */
    private Integer isOriginalAdWide;

    /**
     * 新行业
     */
    private String unitedFirstIndustry;
    private String unitedSecondIndustry;
    private String unitedThirdIndustry;
}
