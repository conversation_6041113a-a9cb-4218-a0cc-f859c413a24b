package com.bilibili.adp.cpc.biz.bos.game;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Description
 * @date 3/15/24
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class GameActivityVideoBo {

    private Long avid;

    private String bvid;

    @ApiModelProperty("视频封面")
    private String cover;

    @ApiModelProperty("视频标题")
    private String title;

    @ApiModelProperty(value = "稿件第一P的cid")
    private String firstCid;

    @ApiModelProperty(value = "是否转载 1-原创 2-转载 0-历史遗留")
    private Integer copyright;

    @ApiModelProperty(value = "稿件简介")
    private String desc;

    @ApiModelProperty(value = "稿件状态")
    private Integer state;

    @ApiModelProperty(value = "稿件状态详情")
    private String stateDesc;

    @ApiModelProperty(value = "稿件总时长")
    private Integer duration;

    @ApiModelProperty(value = "Up主mid")
    private Long mid;

    @ApiModelProperty(value = "Up主名称")
    private String name;

    @ApiModelProperty(value = "Up主头像地址")
    private String face;

    @ApiModelProperty(value = "播放数")
    private Integer view;

    @ApiModelProperty(value = "弹幕数")
    private Integer danmaku;

    @ApiModelProperty(value = "评论数")
    private Integer reply;

    @ApiModelProperty(value = "收藏数")
    private Integer fav;

    /**
     * 宽度
     */
    @ApiModelProperty(value = "宽度")
    private Integer width;
    /**
     * 高度
     */
    @ApiModelProperty(value = "高度")
    private Integer height;

    private Long game_base_id;

    /**
     * 活动状态 0已结束 1 进行中
     */
    private Integer external_act_status;



    private Long id;

    private Long up_id;

    private String game_name;
    /**
     * 稿件来源类型 0 游戏官号稿件 1 合伙人 2 内广 3 外广 4 活动
     */
    private Integer arch_source_type;

    private Long pubtime;
    /**
     * 已达合伙人分成上限
     */
    private Boolean reach_archive_income_limit;

    /**
     * 活动开始时间
     */
    private String external_act_start_time;

    /**
     * 活动结束时间
     */
    private String external_act_end_time;

    /**
     * 素材播放量
     */
    private Long video_views;

}
