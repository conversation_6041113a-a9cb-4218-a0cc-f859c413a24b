package com.bilibili.adp.cpc.biz.services.taobao.bos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName CreativeAlimamaDealBo
 * <AUTHOR>
 * @Date 2023/9/16 5:25 下午
 * @Version 1.0
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TbkCreativeDealBo {

    /**
     * 账户id 必填
     */
    private Integer accountId;

    /**
     * 创意id 选填 不填时默认为刷账户下所有创意
     */
    private Integer creativeId;

    private Integer startCreativeId;

    private Integer endCreativeId;

    /**
     * 星任务id 必填
     */
    private Long eventId;
}
