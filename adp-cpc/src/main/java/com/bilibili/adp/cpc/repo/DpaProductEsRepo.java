package com.bilibili.adp.cpc.repo;

import com.bilibili.adp.common.util.Md5Util;
import com.bilibili.adp.cpc.dao.querydsl_es.pos.DpaProductPO;
import com.bilibili.adp.cpc.dao.querydsl_es.ElasticsearchNativeQueryUtil;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * DPA商品 ES 仓库类
 * 
 * <AUTHOR>
 * @date 2025/5/9
 */
@Slf4j
@Repository
public class DpaProductEsRepo {

    @Value("${dpa.es.cluster.nodes:************:9300}")
    private String clusterNodes;

    @Value("${dpa.es.http.port:9200}")
    private String esHttpPort;

    public static final String CPC_PRODUCT_ES_INDEX = "cpc_product";
    //private static final String CPC_PRODUCT_ES_TYPE = "product";

    /**
     * 获取ES主机地址
     * 
     * @return ES HTTP API地址
     */
    private String getEsHost() {
        // 从集群节点配置中提取主机地址和端口，并转换为HTTP API地址
        // 格式从 ************:9300 转换为 http://************:[esHttpPort]
        String[] nodeInfo = clusterNodes.split(";")[0].split(":");
        String host = nodeInfo[0];
        
        // 使用配置的ES HTTP端口
        return "http://" + host + ":" + esHttpPort;
    }
    
    /**
     * 根据companyGroupId和outerId查询pid
     *
     * @param companyGroupId 公司组ID
     * @param outerId 外部商品ID
     * @return 返回pid，如果未找到返回null
     */
    public Long getPidByCompanyGroupIdAndOuterId(Integer companyGroupId, String outerId) {
        if (companyGroupId == null || outerId == null) {
            log.info("getPidByCompanyGroupIdAndOuterId param invalid, companyGroupId={}, outerId={}", companyGroupId, outerId);
            return null;
        }
        
        // 生成ES Document ID
        String uniqueId = companyGroupId + "_" + outerId;
        String docId = Md5Util.md5Hash(uniqueId);
        log.info("getPidByCompanyGroupIdAndOuterId docId={}, companyGroupId={}, outerId={}", docId, companyGroupId, outerId);
        
        try {
            // 构建查询条件
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery()
                    .must(QueryBuilders.termQuery("_id", docId));
            
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            searchSourceBuilder.query(boolQueryBuilder);
            searchSourceBuilder.fetchSource(new String[]{"pid"}, null); // 只获取pid字段
            searchSourceBuilder.size(1);
            
            // 查询ES
            List<DpaProductPO> products = ElasticsearchNativeQueryUtil.getResultFromEs(
                    searchSourceBuilder, getEsHost(), CPC_PRODUCT_ES_INDEX,
                    Collections.singletonList("pid"), DpaProductPO.class);
            
            if (!CollectionUtils.isEmpty(products)) {
                DpaProductPO product = products.get(0);
                log.info("getPidByCompanyGroupIdAndOuterId found pid={} for docId={}", product.getPid(), docId);
                return product.getPid();
            }
            
            log.info("getPidByCompanyGroupIdAndOuterId not found for docId={}", docId);
            return null;
        } catch (Exception e) {
            log.error("getPidByCompanyGroupIdAndOuterId error, docId={}", docId, e);
            return null;
        }
    }

}
