package com.bilibili.adp.cpc.dao.querydsl.pos;

import javax.annotation.Generated;

/**
 * LauAccountGameAvidMappingPo is a Querydsl bean type
 */
@Generated("com.querydsl.codegen.BeanSerializer")
public class LauAccountGameAvidMappingPo {

    private Integer accountId;

    private Integer agentId;

    private java.sql.Timestamp authEffectiveTime;

    private java.sql.Timestamp authExpireTime;

    private Long authMid;

    private Integer authMode;

    private Integer authStatus;

    private Integer authTimeType;

    private Long avid;

    private java.sql.Timestamp ctime;

    private Integer customerId;

    private Long gameBaseId;

    private Long id;

    private Integer isDeleted;

    private java.sql.Timestamp mtime;

    private Integer renewalStatus;

    public Integer getAccountId() {
        return accountId;
    }

    public void setAccountId(Integer accountId) {
        this.accountId = accountId;
    }

    public Integer getAgentId() {
        return agentId;
    }

    public void setAgentId(Integer agentId) {
        this.agentId = agentId;
    }

    public java.sql.Timestamp getAuthEffectiveTime() {
        return authEffectiveTime;
    }

    public void setAuthEffectiveTime(java.sql.Timestamp authEffectiveTime) {
        this.authEffectiveTime = authEffectiveTime;
    }

    public java.sql.Timestamp getAuthExpireTime() {
        return authExpireTime;
    }

    public void setAuthExpireTime(java.sql.Timestamp authExpireTime) {
        this.authExpireTime = authExpireTime;
    }

    public Long getAuthMid() {
        return authMid;
    }

    public void setAuthMid(Long authMid) {
        this.authMid = authMid;
    }

    public Integer getAuthMode() {
        return authMode;
    }

    public void setAuthMode(Integer authMode) {
        this.authMode = authMode;
    }

    public Integer getAuthStatus() {
        return authStatus;
    }

    public void setAuthStatus(Integer authStatus) {
        this.authStatus = authStatus;
    }

    public Integer getAuthTimeType() {
        return authTimeType;
    }

    public void setAuthTimeType(Integer authTimeType) {
        this.authTimeType = authTimeType;
    }

    public Long getAvid() {
        return avid;
    }

    public void setAvid(Long avid) {
        this.avid = avid;
    }

    public java.sql.Timestamp getCtime() {
        return ctime;
    }

    public void setCtime(java.sql.Timestamp ctime) {
        this.ctime = ctime;
    }

    public Integer getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Integer customerId) {
        this.customerId = customerId;
    }

    public Long getGameBaseId() {
        return gameBaseId;
    }

    public void setGameBaseId(Long gameBaseId) {
        this.gameBaseId = gameBaseId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    public java.sql.Timestamp getMtime() {
        return mtime;
    }

    public void setMtime(java.sql.Timestamp mtime) {
        this.mtime = mtime;
    }

    public Integer getRenewalStatus() {
        return renewalStatus;
    }

    public void setRenewalStatus(Integer renewalStatus) {
        this.renewalStatus = renewalStatus;
    }

    @Override
    public String toString() {
         return "accountId = " + accountId + ", agentId = " + agentId + ", authEffectiveTime = " + authEffectiveTime + ", authExpireTime = " + authExpireTime + ", authMid = " + authMid + ", authMode = " + authMode + ", authStatus = " + authStatus + ", authTimeType = " + authTimeType + ", avid = " + avid + ", ctime = " + ctime + ", customerId = " + customerId + ", gameBaseId = " + gameBaseId + ", id = " + id + ", isDeleted = " + isDeleted + ", mtime = " + mtime + ", renewalStatus = " + renewalStatus;
    }

}

