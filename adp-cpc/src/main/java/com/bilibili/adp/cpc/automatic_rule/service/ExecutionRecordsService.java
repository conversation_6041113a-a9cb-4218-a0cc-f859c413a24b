package com.bilibili.adp.cpc.automatic_rule.service;

import com.bapis.ad.automatic.rule.service.Condition;
import com.bapis.ad.automatic.rule.service.TriggerRequest;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.cpc.automatic_rule.bos.ExecutionRecordBo;
import com.bilibili.adp.cpc.automatic_rule.converter.ConditionConverter;
import com.bilibili.adp.cpc.automatic_rule.converter.ExecutionRecordConverter;
import com.bilibili.adp.cpc.config.AppConfig;
import com.bilibili.adp.cpc.dao.querydsl.pos.LauAutomaticRuleActionPo;
import com.bilibili.adp.cpc.dao.querydsl.pos.LauAutomaticRuleConditionPo;
import com.bilibili.adp.cpc.dao.querydsl.pos.LauAutomaticRuleExecutionRecordPo;
import com.bilibili.adp.cpc.dao.querydsl.pos.LauAutomaticRulePo;
import com.bilibili.bjcom.querydsl.BaseQueryFactory;
import com.bilibili.bjcom.querydsl.paging.Page;
import com.bilibili.bjcom.querydsl.paging.Pager;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.bilibili.adp.cpc.dao.config.PlatformMySqlConfig.AD_BQF;
import static com.bilibili.adp.cpc.dao.config.PlatformMySqlConfig.AD_TM;
import static com.bilibili.adp.cpc.dao.querydsl.QLauAutomaticRuleExecutionRecord.lauAutomaticRuleExecutionRecord;

@Slf4j
@Service
@RequiredArgsConstructor
public class ExecutionRecordsService {
    @Resource(name = AD_BQF)
    private BaseQueryFactory adBqf;
    @Resource(name = AppConfig.OBJECT_MAPPER)
    private ObjectMapper objectMapper;
    private final RuleService ruleService;

    @SneakyThrows
    private List<String> parseListString(String listString) {
        if (!StringUtils.hasText(listString)) {
            return Collections.emptyList();
        }
        return objectMapper.readValue(listString, new TypeReference<List<String>>() {
        });
    }
    @SneakyThrows
    public ExecutionRecordBo buildRecord(TriggerRequest request, LauAutomaticRulePo rule, List<LauAutomaticRuleConditionPo> conditions, LauAutomaticRuleActionPo action) {
        final Map<Long, String> idValueMap = request
                .getConditionsList()
                .stream()
                .filter(Condition::getConditionValid)
                .collect(Collectors.toMap(Condition::getConditionId, Condition::getConditionValue));
        final List<LauAutomaticRuleConditionPo> validConditions = conditions
                .stream()
                .filter(condition -> idValueMap.containsKey(condition.getConditionId()))
                .collect(Collectors.toList());
        final List<LauAutomaticRuleConditionPo> triggerConditions = conditions
                .stream()
                .filter(condition -> idValueMap.containsKey(condition.getConditionId()))
                .map(condition -> ConditionConverter.MAPPER.newPo(condition, idValueMap.get(condition.getConditionId())))
                .collect(Collectors.toList());
        final long actionId = request.getAction().getActionId();
        return ExecutionRecordBo
                .builder()
                .accountId(rule.getAccountId())
                .ruleId(rule.getRuleId())
                .triggerConditions(ConditionService.triggerConditions(triggerConditions))
                .conditions(ConditionService.conditions(validConditions))
                .actionId(actionId)
                .action(ActionService.action(action))
                .subject(action.getSubject())
                .objectType(request.getObject().getObjectTypeValue())
                .objectId(request.getObject().getObjectId())
                .executeTime(LocalDateTime.now())
                .build();
    }

    public PageResult<ExecutionRecordBo> executionRecords(Integer accountId, Long ruleId, Integer subject, LocalDateTime executeTimeGoe, LocalDateTime executeTimeLoe, Integer pn, Integer ps) {
        final Page<LauAutomaticRuleExecutionRecordPo> recordPage = listRecords(accountId, ruleId, subject, executeTimeGoe, executeTimeLoe, pn, ps);
        int total = (int) recordPage.getTotal();
        final List<Long> ruleIds = recordPage
                .getRows()
                .stream()
                .map(LauAutomaticRuleExecutionRecordPo::getRuleId)
                .distinct()
                .collect(Collectors.toList());
        final List<LauAutomaticRulePo> rules = ruleService.listRulesByRuleIds(ruleIds);
        final Map<Long, LauAutomaticRulePo> ruleIdRuleMap = rules
                .stream()
                .collect(Collectors.toMap(LauAutomaticRulePo::getRuleId, Function.identity()));
        final List<ExecutionRecordBo> records = recordPage
                .getRows()
                .stream()
                .map(record -> {
                    LauAutomaticRulePo rulePo = ruleIdRuleMap.get(record.getRuleId());
                    List<String> conditions = parseListString(record.getConditions());
                    return ExecutionRecordConverter.MAPPER.compose(record, rulePo, conditions);
                })
                .collect(Collectors.toList());
        return PageResult
                .<ExecutionRecordBo>builder()
                .total(total)
                .records(records)
                .build();
    }

    public List<LauAutomaticRuleExecutionRecordPo> listRecords(Long actionId, Integer objectType, Integer objectId, List<Integer> executeResults, LocalDateTime executeTimeGoe, LocalDateTime executeTimeLoe) {
        return adBqf
                .selectFrom(lauAutomaticRuleExecutionRecord)
                .where(lauAutomaticRuleExecutionRecord.actionId.eq(actionId))
                .where(lauAutomaticRuleExecutionRecord.objectType.eq(objectType))
                .where(lauAutomaticRuleExecutionRecord.objectId.eq(objectId))
                .where(lauAutomaticRuleExecutionRecord.executeResult.in(executeResults))
                .where(lauAutomaticRuleExecutionRecord.executeTime.goe(Timestamp.valueOf(executeTimeGoe)))
                .where(lauAutomaticRuleExecutionRecord.executeTime.loe(Timestamp.valueOf(executeTimeLoe)))
                .orderBy(lauAutomaticRuleExecutionRecord.executeTime.desc())
                .fetch();
    }

    public Page<LauAutomaticRuleExecutionRecordPo> listRecords(Integer accountId, Long ruleId, Integer subject, LocalDateTime executeTimeGoe, LocalDateTime executeTimeLoe, Integer pn, Integer ps) {
        return adBqf
                .selectFrom(lauAutomaticRuleExecutionRecord)
                .where(lauAutomaticRuleExecutionRecord.accountId.eq(accountId))
                .where(lauAutomaticRuleExecutionRecord.isDeleted.eq(0))
                .whereIfNotNull(ruleId, lauAutomaticRuleExecutionRecord.ruleId::eq)
                .whereIfNotNull(subject, lauAutomaticRuleExecutionRecord.subject::eq)
                .where(lauAutomaticRuleExecutionRecord.executeTime.goe(Timestamp.valueOf(executeTimeGoe)))
                .where(lauAutomaticRuleExecutionRecord.executeTime.loe(Timestamp.valueOf(executeTimeLoe)))
                .orderBy(lauAutomaticRuleExecutionRecord.executeTime.desc())
                .fetchPage(Pager.of(pn, ps));
    }

    @SneakyThrows
    @Transactional(value = AD_TM, rollbackFor = Exception.class)
    public void save(ExecutionRecordBo record) {
        final String triggerConditions = objectMapper.writeValueAsString(record.getTriggerConditions());
        final String conditions = objectMapper.writeValueAsString(record.getConditions());
        final LauAutomaticRuleExecutionRecordPo recordPo = ExecutionRecordConverter.MAPPER.bo2Po(record, triggerConditions, conditions);
        adBqf
                .insert(lauAutomaticRuleExecutionRecord)
                .insertBean(recordPo);
    }
}
