package com.bilibili.adp.cpc.databus.sub;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bilibili.adp.cpc.biz.bos.WarpDatabusProperty;
import com.bilibili.adp.cpc.biz.services.ad_product.IAdProductService;
import com.bilibili.adp.cpc.dao.querydsl_cpc.pos.AdProductMappingCPCPo;
import com.bilibili.adp.cpc.dao.querydsl_cpc.pos.AdProductTotalInfoCPCPo;
import com.bilibili.adp.cpc.databus.Constant;
import com.bilibili.adp.cpc.repo.AdProductRepo;
import com.bilibili.warp.databus.AckableMessage;
import com.bilibili.warp.databus.DatabusProperty;
import com.bilibili.warp.databus.MessageListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.util.Collections;
import java.util.Map;
import java.util.Objects;

@Component
@Slf4j
public class AdProductMappingBinlogSub implements MessageListener {

    @Autowired
    private IAdProductService adProductService;

    private final String topic;

    private final String group;


    public AdProductMappingBinlogSub(WarpDatabusProperty warpDatabusProperty) {
        // 注入DatabusProperties，此处的ALIAS常量即配置文件中填写的别名，是topic的上一级配置键
        // pub场景同理，在使用DatabusTemplate的地方注入DatabusProperties来获取
        Map<String, DatabusProperty> properties = warpDatabusProperty.getProperties();
        DatabusProperty property = properties.get("ad_product_mapping");
        log.info("ad_product_mapping, property={}", JSONObject.toJSONString(property));
        this.topic = property.getTopic();
        this.group = property.getSub().getGroup();
    }


    @Override
    public String topic() {
        return this.topic;
    }

    @Override
    public String group() {
        return this.group;
    }

    @Override
    public void onMessage(AckableMessage message) {
        try {
            String value = new String(message.payload());
            JSONObject msg = JSONObject.parseObject(value);

            String action = msg.getString(Constant.ACTION);

            JSONObject oldObject = msg.getJSONObject(Constant.OLD);
            AdProductMappingCPCPo oldAdProductTotalInfoCPCPo = deserializeBinlogDto(oldObject);
            JSONObject newObject = msg.getJSONObject(Constant.NEW);
            AdProductMappingCPCPo newAdProductTotalInfoCPCPo = deserializeBinlogDto(newObject);

            if (Objects.equals(action, Constant.UPDATE)) {
                adProductService.updateProductMappingByBinlog(newAdProductTotalInfoCPCPo);
            } else if (Objects.equals(action, Constant.INSERT)) {
                adProductService.insertProductMappingByBinlog(newAdProductTotalInfoCPCPo);
            }else if(Objects.equals(action, Constant.DELETE)){
                adProductService.deleteProductMappingByBinlog(newAdProductTotalInfoCPCPo);
            }

        } catch (Exception e) {
            String msg = new String(message.payload());
            log.info("AdProductMappingBinlogSub onMessage error message : {}", msg, e);
        }
        message.ack();
    }

    private AdProductMappingCPCPo deserializeBinlogDto(JSONObject jsonObject) {
        if (Objects.isNull(jsonObject)) {
            return null;
        }
        return jsonObject.toJavaObject(AdProductMappingCPCPo.class);
    }

}
