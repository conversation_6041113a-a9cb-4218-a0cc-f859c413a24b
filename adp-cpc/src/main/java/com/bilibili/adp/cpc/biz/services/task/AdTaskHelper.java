package com.bilibili.adp.cpc.biz.services.task;

import com.alibaba.fastjson.JSON;
import com.bapis.ad.adp.task.TaskStartEventEnum;
import com.bapis.ad.adp.task.*;
import com.bapis.ad.crm.category.CategoryBaseResp;
import com.bapis.ad.crm.category.CategoryOneResult;
import com.bapis.ad.crm.industry.IndustryInfo;
import com.bapis.ad.crm.industry.IndustryResp;
import com.bapis.ad.crm.platform.project.*;
import com.bapis.ad.crm.product.ProductInfo;
import com.bilibili.adp.common.OcpcTargetEnum;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.enums.SystemType;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.biz.services.task.dtos.AdTaskRuleSaveDto;
import com.bilibili.adp.cpc.biz.services.task.dtos.AdTaskSaveDto;
import com.bilibili.adp.cpc.biz.services.task.dtos.CouponTemplateInfoDto;
import com.bilibili.adp.cpc.biz.services.task.dtos.ProjectInfoDto;
import com.bilibili.adp.cpc.biz.services.task.enums.TaskEffectObjectTypeEnum;
import com.bilibili.adp.cpc.biz.services.task_project.AdTaskProjectService;
import com.bilibili.adp.cpc.dao.querydsl.pos.*;
import com.bilibili.adp.cpc.enums.task.TaskRuleConditionEnum;
import com.bilibili.adp.cpc.enums.task.TaskRuleConditionExpressionEnum;
import com.bilibili.adp.cpc.enums.task.*;
import com.bilibili.adp.cpc.proxy.CrmPlatformProxy;
import com.bilibili.adp.cpc.proxy.CrmPortalProxy;
import com.bilibili.adp.cpc.repo.AdTaskRepo;
import com.bilibili.adp.cpc.repo.NewAccAccountRepo;
import com.bilibili.bsi.api.dto.SendMessageDto;
import com.bilibili.bsi.common.enums.BsiMessageType;
import com.bilibili.crm.platform.api.customer.dto.CustomerBaseDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.bilibili.adp.cpc.enums.task.TaskStatusEnum.*;

@Component
public class AdTaskHelper {

    @Autowired
    private NewAccAccountRepo newAccAccountRepo;


    @Autowired
    private AdTaskRepo adTaskRepo;

    @Resource
    private CrmPortalProxy crmPortalProxy;

    @Resource
    private CrmPlatformProxy crmPlatformProxy;

    @Autowired
    private AdTaskProjectService adTaskProjectService;

    /**
     * 单任务下的规则限制.
     */
    private static final Integer RULE_LIMIT = 10;

    /**
     * 单任务下的账户数量限制.
     */
    private static final Integer ACCOUNT_LIMIT = 200;

    public AdTaskConfigPo generateAdTaskConfig(AdTaskSaveDto adTaskSaveDto) {
        AdTaskConfigPo adTaskConfigPo = new AdTaskConfigPo();
        adTaskConfigPo.setId(adTaskSaveDto.getId());
        adTaskConfigPo.setBeginTime(adTaskSaveDto.getStartDate());

        List<Integer> firstIndustryIds = adTaskSaveDto.getFirstIndustryIds();
        String categoryFirstIdsStr = "";
        if (!CollectionUtils.isEmpty(firstIndustryIds)) {
            categoryFirstIdsStr = firstIndustryIds.stream().map(t -> String.valueOf(t)).collect(Collectors.joining(","));
        }

        adTaskConfigPo.setCommerceCategoryFirstIds(categoryFirstIdsStr);
        adTaskConfigPo.setCompleteDimension(adTaskSaveDto.getCompleteDimension());
        adTaskConfigPo.setCouponNum(adTaskSaveDto.getCouponNum());
        adTaskConfigPo.setCouponTemplateId(adTaskSaveDto.getCouponTemplateId());
        adTaskConfigPo.setCreator(adTaskSaveDto.getCreatorName());
        adTaskConfigPo.setDescription(adTaskSaveDto.getDescription());
        if (Objects.nonNull(adTaskSaveDto.getStartDate())) {
            adTaskConfigPo.setBeginTime(adTaskSaveDto.getStartDate());
        }
        if (Objects.nonNull(adTaskSaveDto.getEndDate())) {
            adTaskConfigPo.setEndTime(adTaskSaveDto.getEndDate());
        }
        adTaskConfigPo.setName(adTaskSaveDto.getName());
        adTaskConfigPo.setPeriod(adTaskSaveDto.getPeriod());
        adTaskConfigPo.setProjectId(adTaskSaveDto.getProjectId());
        adTaskConfigPo.setStartEvent(adTaskSaveDto.getTaskStartEvent());
        adTaskConfigPo.setStartType(adTaskSaveDto.getTaskStartType());
        adTaskConfigPo.setTips(adTaskSaveDto.getTip());
        adTaskConfigPo.setType(adTaskSaveDto.getTaskType());
        adTaskConfigPo.setCtime(Utils.getNow());
        adTaskConfigPo.setMtime(Utils.getNow());
        adTaskConfigPo.setIsAccountRelsRunCompleted(0);
        adTaskConfigPo.setIsDeleted(0);
        adTaskConfigPo.setStatus(1);
        if (!CollectionUtils.isEmpty(adTaskSaveDto.getParentTaskIds())) {
            List<AdTaskConfigPo> parentAdTaskConfig = adTaskRepo.queryTask(adTaskSaveDto.getParentTaskIds());
            if (!CollectionUtils.isEmpty(parentAdTaskConfig)) {
                Integer parentTaskLayer = parentAdTaskConfig.get(0).getTaskLayer();

                String parentTaskId = adTaskSaveDto.getParentTaskIds().stream().map(String::valueOf).collect(Collectors.joining(","));
                adTaskConfigPo.setParentTaskId(parentTaskId);
                adTaskConfigPo.setTaskLayer(parentTaskLayer + 1);
            }


        }
        return adTaskConfigPo;

    }

    public List<AdTaskRuleConfigPo> generateAdTaskRuleConfigList(AdTaskSaveDto adTaskSaveDto) {
        if (CollectionUtils.isEmpty(adTaskSaveDto.getTaskRules())) {
            return Collections.EMPTY_LIST;
        }


        List<AdTaskRuleConfigPo> adTaskRuleConfigList = new ArrayList<>();
        for (AdTaskRuleSaveDto taskRule : adTaskSaveDto.getTaskRules()) {
            AdTaskRuleConfigPo adTaskRuleConfig = new AdTaskRuleConfigPo();
            adTaskRuleConfig.setTaskId(adTaskSaveDto.getId());
            adTaskRuleConfig.setConditionCode(taskRule.getConditionCode());
            adTaskRuleConfig.setConditionName(TaskRuleConditionEnum.getByCode(taskRule.getConditionCode()).getName());
            adTaskRuleConfig.setConditionExpression(taskRule.getConditionExpression());
            adTaskRuleConfig.setValue(taskRule.getValue());
            Timestamp now = Utils.getNow();
            adTaskRuleConfig.setCtime(now);
            adTaskRuleConfig.setMtime(now);
            adTaskRuleConfig.setIsDeleted(0);

            adTaskRuleConfig.setConvTarget("");
            if (!StringUtils.isEmpty(taskRule.getConvTarget()) && Integer.parseInt(taskRule.getConvTarget()) > 0) {
                adTaskRuleConfig.setConvTarget(taskRule.getConvTarget());
            }
            adTaskRuleConfigList.add(adTaskRuleConfig);
        }
        return adTaskRuleConfigList;
    }

    public List<AdAccountTaskRelPo> generateAdAccountTaskRelList(AdTaskSaveDto adTaskSaveDto, List<AccAccountPo> accAccountPos) {
        List<Integer> accountIds = accAccountPos.stream().map(AccAccountPo::getAccountId).collect(Collectors.toList());
        List<AdAccountTaskRelPo> resultList = new ArrayList<>();
        Map<Integer, AccAccountPo> accAccountPoMap = accAccountPos.stream().collect(Collectors.toMap(AccAccountPo::getAccountId, Function.identity()));
        for (Integer accountId : accountIds) {
            AccAccountPo accAccountPo = accAccountPoMap.get(accountId);
            AdAccountTaskRelPo accountTaskRelPo = new AdAccountTaskRelPo();
            accountTaskRelPo.setTaskId(adTaskSaveDto.getId());
            accountTaskRelPo.setAccountId(accountId);
            accountTaskRelPo.setCustomerId(accAccountPo.getCustomerId());
            accountTaskRelPo.setAgentId(accAccountPo.getDependencyAgentId());
            accountTaskRelPo.setProductId(accAccountPo.getProductId());
            if (adTaskSaveDto.getTaskStartType().equals(TaskStartTypeEnum.BY_TIME_VALUE)) {
                accountTaskRelPo.setBeginTime(adTaskSaveDto.getStartDate());
                accountTaskRelPo.setEndTime(adTaskSaveDto.getEndDate());
            }
            accountTaskRelPo.setCompleteDimension(adTaskSaveDto.getCompleteDimension());
            resultList.add(accountTaskRelPo);
        }
        return resultList;
    }

    public List<AdAccountTaskRuleRelPo> generateAdAccountTaskRuleRelList(AdTaskSaveDto adTaskSaveDto, List<AdTaskRuleConfigPo> taskRuleConfigPoList) {
        List<Integer> objectIdList = new ArrayList<>();
        if (Objects.equals(adTaskSaveDto.getCompleteDimension(), TaskCompleteDimensionTypeEnum.ACCOUNT_DIMENSION.getCode())) {
            objectIdList.addAll(adTaskSaveDto.getAccountIds());
        } else if (Objects.equals(adTaskSaveDto.getCompleteDimension(), TaskCompleteDimensionTypeEnum.CUSTOMER_DIMENSION.getCode())) {
            objectIdList.addAll(adTaskSaveDto.getCustomerIds());
        } else if (Objects.equals(adTaskSaveDto.getCompleteDimension(), TaskCompleteDimensionTypeEnum.AGENT_DIMENSION.getCode())) {
            objectIdList.addAll(adTaskSaveDto.getAgentIds());
        } else if (Objects.equals(adTaskSaveDto.getCompleteDimension(), TaskCompleteDimensionTypeEnum.PRODUCT_DIMENSION.getCode())) {
            objectIdList.addAll(adTaskSaveDto.getProductIds());
        }

        List<AdAccountTaskRuleRelPo> resultList = new ArrayList<>();
        List<Integer> accountIds = adTaskSaveDto.getAccountIds();
        for (Integer objectId : objectIdList) {
            for (AdTaskRuleConfigPo taskRule : taskRuleConfigPoList) {
                AdAccountTaskRuleRelPo adAccountTaskRuleRelPo = new AdAccountTaskRuleRelPo();
                adAccountTaskRuleRelPo.setAccountId(objectId);
                adAccountTaskRuleRelPo.setRuleId(taskRule.getId());
                adAccountTaskRuleRelPo.setTaskId(adTaskSaveDto.getId());
                adAccountTaskRuleRelPo.setValue(taskRule.getValue());
                resultList.add(adAccountTaskRuleRelPo);

            }
        }
        return resultList;
    }

    public List<AdAccountTaskRuleRelPo> generateAdAccountTaskRuleRelList(Long taskId, List<Integer> accountIds, List<AdTaskRuleConfigPo> taskRuleConfigPoList) {
        List<AdAccountTaskRuleRelPo> resultList = new ArrayList<>();
        for (Integer accountId : accountIds) {
            for (AdTaskRuleConfigPo taskRule : taskRuleConfigPoList) {
                AdAccountTaskRuleRelPo adAccountTaskRuleRelPo = new AdAccountTaskRuleRelPo();
                adAccountTaskRuleRelPo.setAccountId(accountId);
                adAccountTaskRuleRelPo.setRuleId(taskRule.getId());
                adAccountTaskRuleRelPo.setTaskId(taskId);
                adAccountTaskRuleRelPo.setValue(taskRule.getValue());
                resultList.add(adAccountTaskRuleRelPo);

            }
        }
        return resultList;
    }

    public FetchTaskInfoReply generateFetchTaskInfoReply(AdTaskConfigPo adTaskConfigPo, List<AdTaskRuleConfigPo> adTaskRuleConfigPoList) {
        String commerceCategoryFirstIds = adTaskConfigPo.getCommerceCategoryFirstIds();
        List<Integer> categoryFirstIdList = new ArrayList<>();
        String categoryNames = "";
        if (!StringUtils.isEmpty(commerceCategoryFirstIds)) {
            categoryFirstIdList = Arrays.stream(commerceCategoryFirstIds.split(",")).map(Integer::parseInt).collect(Collectors.toList());
        }

        Long taskId = adTaskConfigPo.getId();

        if (!CollectionUtils.isEmpty(categoryFirstIdList)) {
            CategoryBaseResp categoryBaseResp = crmPortalProxy.queryBizIndustryCategoryListByIdList(categoryFirstIdList);
            categoryNames = categoryBaseResp.getCategoryResultListList().stream().map(CategoryOneResult::getName).distinct().collect(Collectors.joining(","));
        }

        List<AdAccountTaskRelPo> accountTaskRelPoList = adTaskRepo.queryAccountTaskRels(taskId);
        List<Integer> accountIdList = accountTaskRelPoList.stream().map(AdAccountTaskRelPo::getAccountId).collect(Collectors.toList());
        Map<Integer, AccAccountPo> accountPoMap = newAccAccountRepo.queryAccountMap(accountIdList);

        List<Integer> productIdList = accountTaskRelPoList.stream().map(AdAccountTaskRelPo::getProductId).collect(Collectors.toList());
        Map<Integer, String> productNameMap = new HashMap<>();


        if (!CollectionUtils.isEmpty(productIdList)) {
            productNameMap = crmPortalProxy.batchQueryProduct(productIdList).stream().collect(Collectors.toMap(ProductInfo::getId,
                    ProductInfo::getName));
        }

        List<Integer> customerIdList = accountPoMap.values().stream().map(AccAccountPo::getCustomerId).distinct().collect(Collectors.toList());
        List<Integer> agentIdList = accountPoMap.values().stream().map(AccAccountPo::getDependencyAgentId).distinct().collect(Collectors.toList());

        Map<Integer, String> customerNameMap = crmPlatformProxy.iSoaCustomerService().getCustomerBaseDtosByIds(customerIdList).stream().collect(Collectors.toMap(CustomerBaseDto::getId, CustomerBaseDto::getUsername));
        Map<Integer, String> agentId2NameMap = crmPlatformProxy.iSoaAgentService().getAgentId2NameMapInIds(agentIdList);
        Map<Integer, String> accountNameMap = accountPoMap.values().stream().collect(Collectors.toMap(AccAccountPo::getAccountId, AccAccountPo::getUsername));

//        Set<String> customerNameList = customerIdList.stream().map(customerNameMap::get).collect(Collectors.toList());
        Set<String> customerNameSet = new HashSet<>();
        Set<String> agentNameSet = new HashSet<>();
        Set<String> accountNameSet = new HashSet<>();
        Set<String> productNameSet = new HashSet<>();

        Map<Integer, String> finalProductNameMap = productNameMap;
        List<AccountInfo> accountInfoList = new ArrayList<>();
        for (AccAccountPo accAccountPo : accountPoMap.values()) {
            String customerName = customerNameMap.get(accAccountPo.getCustomerId()) + "(" + accAccountPo.getCustomerId() + ")";
            String agentName = agentId2NameMap.get(accAccountPo.getDependencyAgentId()) + "(" + accAccountPo.getDependencyAgentId() + ")";
            String accountName = accAccountPo.getUsername() + "(" + accAccountPo.getAccountId() + ")";
            String productName = finalProductNameMap.get(accAccountPo.getProductId()) + "(" + accAccountPo.getProductId() + ")";
            customerNameSet.add(customerName);
            agentNameSet.add(customerName + "/" + agentName);
            accountNameSet.add(customerName + "/" + agentName + "/" + accountName);
            productNameSet.add(productName + "/" + accountName);
            AccountInfo singleAccountInfo = AccountInfo.newBuilder()
                    .setCustomerId(accAccountPo.getCustomerId())
                    .setAgentId(accAccountPo.getDependencyAgentId())
                    .setAccountId(accAccountPo.getAccountId())
                    .setProductId(accAccountPo.getProductId())
                    .build();
            accountInfoList.add(singleAccountInfo);
        }

        Long projectId = adTaskConfigPo.getProjectId();
        BatchQueryByProjectIdResp batchQueryByProjectIdResp = crmPlatformProxy.batchQueryByProjectIds(Collections.singletonList(projectId));

        Assert.isTrue(!CollectionUtils.isEmpty(batchQueryByProjectIdResp.getDataList()), "项目信息未查到");
        ProjectInfo projectInfo = batchQueryByProjectIdResp.getDataList().get(0);


        Map<Long, CouponTemplate> couponIdMap = projectInfo.getCouponsList().stream().collect(Collectors.toMap(CouponTemplate::getId, Function.identity()));
        CouponTemplate couponTemplate = couponIdMap.get(adTaskConfigPo.getCouponTemplateId().longValue());
        Assert.notNull(couponTemplate, "券信息未查到");


        List<TaskObjectInfo> taskObjectInfoList = new ArrayList<>();

        if (Objects.equals(adTaskConfigPo.getCompleteDimension(), TaskCompleteDimensionTypeEnum.CUSTOMER_DIMENSION.getCode())) {
            Map<Integer, List<Integer>> customerAccountIdMap = accountTaskRelPoList.stream().collect(Collectors.groupingBy(AdAccountTaskRelPo::getCustomerId, Collectors.mapping(AdAccountTaskRelPo::getAccountId, Collectors.toList())));
            for (Map.Entry<Integer, List<Integer>> entry : customerAccountIdMap.entrySet()) {
                Integer customerId = entry.getKey();

                List<AccountInfo> accountInfos = new ArrayList<>();
                List<Integer> accountIds = entry.getValue();
                for (Integer accountId : accountIds) {

                    AccountInfo accountInfo = AccountInfo.newBuilder()
                            .setAccountId(accountId)
                            .setAccountName(accountNameMap.getOrDefault(accountId, ""))
                            .build();
                    accountInfos.add(accountInfo);
                }
                TaskObjectInfo taskObjectInfo = TaskObjectInfo.newBuilder()
                        .setObjectId(customerId)
                        .setObjectName(customerNameMap.getOrDefault(customerId, ""))
                        .addAllAccountInfos(accountInfos)
                        .build();
                taskObjectInfoList.add(taskObjectInfo);
            }
        } else if (Objects.equals(adTaskConfigPo.getCompleteDimension(), TaskCompleteDimensionTypeEnum.AGENT_DIMENSION.getCode())) {
            Map<Integer, List<Integer>> agentAccountIdMap = accountTaskRelPoList.stream().collect(Collectors.groupingBy(AdAccountTaskRelPo::getAgentId, Collectors.mapping(AdAccountTaskRelPo::getAccountId, Collectors.toList())));
            for (Map.Entry<Integer, List<Integer>> entry : agentAccountIdMap.entrySet()) {
                Integer agentId = entry.getKey();

                List<AccountInfo> accountInfos = new ArrayList<>();
                List<Integer> accountIds = entry.getValue();
                for (Integer accountId : accountIds) {

                    AccountInfo accountInfo = AccountInfo.newBuilder()
                            .setAccountId(accountId)
                            .setAccountName(accountNameMap.getOrDefault(accountId, ""))
                            .build();
                    accountInfos.add(accountInfo);
                }
                TaskObjectInfo taskObjectInfo = TaskObjectInfo.newBuilder()
                        .setObjectId(agentId)
                        .setObjectName(agentId2NameMap.getOrDefault(agentId, ""))
                        .addAllAccountInfos(accountInfos)
                        .build();
                taskObjectInfoList.add(taskObjectInfo);
            }
        } else if (Objects.equals(adTaskConfigPo.getCompleteDimension(), TaskCompleteDimensionTypeEnum.PRODUCT_DIMENSION.getCode())) {
            Map<Integer, List<Integer>> productAccountIdMap = accountTaskRelPoList.stream().collect(Collectors.groupingBy(AdAccountTaskRelPo::getProductId, Collectors.mapping(AdAccountTaskRelPo::getAccountId, Collectors.toList())));
            for (Map.Entry<Integer, List<Integer>> entry : productAccountIdMap.entrySet()) {
                Integer productId = entry.getKey();

                List<AccountInfo> accountInfos = new ArrayList<>();
                List<Integer> accountIds = entry.getValue();
                for (Integer accountId : accountIds) {

                    AccountInfo accountInfo = AccountInfo.newBuilder()
                            .setAccountId(accountId)
                            .setAccountName(accountNameMap.getOrDefault(accountId, ""))
                            .build();
                    accountInfos.add(accountInfo);
                }
                TaskObjectInfo taskObjectInfo = TaskObjectInfo.newBuilder()
                        .setObjectId(productId)
                        .setObjectName(productNameMap.getOrDefault(productId, ""))
                        .addAllAccountInfos(accountInfos)
                        .build();
                taskObjectInfoList.add(taskObjectInfo);
            }
        } else if (Objects.equals(adTaskConfigPo.getCompleteDimension(), TaskCompleteDimensionTypeEnum.ACCOUNT_DIMENSION.getCode())) {
            Map<Integer, List<Integer>> accountAccountIdMap = accountTaskRelPoList.stream().collect(Collectors.groupingBy(AdAccountTaskRelPo::getAccountId, Collectors.mapping(AdAccountTaskRelPo::getAccountId, Collectors.toList())));
            for (Map.Entry<Integer, List<Integer>> entry : accountAccountIdMap.entrySet()) {
                Integer objectAccountId = entry.getKey();

                List<AccountInfo> accountInfos = new ArrayList<>();
                List<Integer> accountIds = entry.getValue();
                for (Integer accountId : accountIds) {
                    AccountInfo accountInfo = AccountInfo.newBuilder()
                            .setAccountId(accountId)
                            .setAccountName(accountNameMap.getOrDefault(accountId, ""))
                            .build();
                    accountInfos.add(accountInfo);
                }
                TaskObjectInfo taskObjectInfo = TaskObjectInfo.newBuilder()
                        .setObjectId(objectAccountId)
                        .setObjectName(accountNameMap.getOrDefault(objectAccountId, ""))
                        .addAllAccountInfos(accountInfos)
                        .build();
                taskObjectInfoList.add(taskObjectInfo);
            }
        }


        FetchTaskInfoReply fetchTaskInfoReply = FetchTaskInfoReply.newBuilder()
                .setId(adTaskConfigPo.getId())
                .setName(adTaskConfigPo.getName())
                .addAllFirstIndustryId(categoryFirstIdList)
                .setFirstIndustryName(categoryNames)
                .setTaskCompleteDimension(TaskCompleteDimensionEnum.forNumber(adTaskConfigPo.getCompleteDimension()))
                .setTaskCompleteDimensionName(TaskCompleteDimensionTypeEnum.fromValue(adTaskConfigPo.getCompleteDimension()).getDesc())
                .setDesc(adTaskConfigPo.getDescription())
                .setTip(adTaskConfigPo.getTips())
                .setTaskType(TaskTypeEnum.forNumber(adTaskConfigPo.getType()))
                .setTaskTypeName(AdpTaskTypeEnum.fromValue(adTaskConfigPo.getType()).getDesc())
                .setTaskStartType(TaskStartTypeEnum.forNumber(adTaskConfigPo.getStartType()))
                .setTaskStartTypeName(com.bilibili.adp.cpc.enums.task.AdpTaskStartTypeEnum.getByCode(adTaskConfigPo.getStartType()).getDesc())
                .setTaskStartEvent(TaskStartEventEnum.forNumber(adTaskConfigPo.getStartEvent()))
                .setTaskStartEventName(com.bilibili.adp.cpc.enums.task.TaskStartEventEnum.getByCode(adTaskConfigPo.getStartEvent()).getName())
                .setStartDate(Objects.nonNull(adTaskConfigPo.getBeginTime()) ? adTaskConfigPo.getBeginTime().getTime() : 0L)
                .setEndDate(Objects.nonNull(adTaskConfigPo.getEndTime()) ? adTaskConfigPo.getEndTime().getTime() : 0L)
                .addAllTaskRules(generateTaskRuleList(adTaskRuleConfigPoList))
                .setProjectId(adTaskConfigPo.getProjectId())
                .setProjectName(projectInfo.getProjectName())
                .setProjectBudget(projectInfo.getProjectBudget())
                .setProjectAmountRemain(projectInfo.getProjectBalance())
                .setProjectAmountGiven(projectInfo.getProjectBudget() - projectInfo.getProjectBalance())
                .setCouponNum(adTaskConfigPo.getCouponNum())
                .setCouponValue(couponTemplate.getCouponFaceValue())
                .setCouponBeginDate(StringUtils.isEmpty(couponTemplate.getCouponBeginDate()) ? 0L : Long.parseLong(couponTemplate.getCouponBeginDate()))
                .setCouponEndDate(StringUtils.isEmpty(couponTemplate.getCouponEndDate()) ? 0L : Long.parseLong(couponTemplate.getCouponEndDate()))
                .addAllCustomerIds(customerIdList)
                .addAllAgentIds(agentIdList)
                .addAllAccountIds(accountIdList)
                .addAllProductIds(productIdList)
                .setPeriod(adTaskConfigPo.getPeriod())
                .addAllCustomerNames(customerNameSet)
                .addAllAgentNames(agentNameSet)
                .addAllAccountNames(accountNameSet)
                .addAllProductNames(productNameSet)
                .addAllAccountInfos(accountInfoList)
                .setProjectEffectCustomerType(projectInfo.getEffectCustomerType())
                .setCouponTemplateId(couponTemplate.getId())
                .setCouponDateType(couponTemplate.getDateType())
                .setCouponEffectDays(couponTemplate.getEffectDays())
                .setParentTaskId(adTaskConfigPo.getParentTaskId())
                .addAllTaskObjectInfos(taskObjectInfoList)
                .build();
        return fetchTaskInfoReply;


    }

    public List<TaskRule> generateTaskRuleList(List<AdTaskRuleConfigPo> adTaskRuleConfigPoList) {
        if (CollectionUtils.isEmpty(adTaskRuleConfigPoList)) {
            return Collections.EMPTY_LIST;
        }

        List<TaskRule> resultList = new ArrayList<>();
        for (AdTaskRuleConfigPo adTaskRuleConfigPo : adTaskRuleConfigPoList) {
            TaskRule.Builder builder = TaskRule.newBuilder()
                    .setRuleConditionValue(adTaskRuleConfigPo.getConditionCode())
                    .setRuleConditionName(TaskRuleConditionEnum.getByCode(adTaskRuleConfigPo.getConditionCode()).getName())
                    .setRuleConditionExpressionValue(adTaskRuleConfigPo.getConditionExpression())
                    .setRuleConditionExpressionName(TaskRuleConditionExpressionEnum.getByCode(adTaskRuleConfigPo.getConditionExpression()).getDesc())
                    .setValue(adTaskRuleConfigPo.getValue());

            if (!StringUtils.isEmpty(adTaskRuleConfigPo.getConvTarget())) {
                builder.setConvTarget(adTaskRuleConfigPo.getConvTarget())
                        .setConvTargetName(com.bilibili.adp.cpc.enums.ad.OcpcTargetEnum.getByCode(Integer.parseInt(adTaskRuleConfigPo.getConvTarget())).getDesc());
            }
            if (adTaskRuleConfigPo.getConditionCode().equals(TaskRuleConditionEnum.ACCOUNT_ALL_LINK_CONVERSION.getCode())) {
                builder.setValueName(Arrays.stream(adTaskRuleConfigPo.getValue()
                                .split(","))
                        .map(Integer::parseInt)
                        .map(ocpcTarget -> OcpcTargetEnum.getByCode(ocpcTarget).getDesc())
                        .collect(Collectors.joining(",")));
            }
            TaskRule taskRule = builder.build();
            resultList.add(taskRule);
        }
        return resultList;
    }


    public List<SingleQueryTaskPageListReply> generateSingleQueryTaskPageListReply(List<AdTaskConfigPo> adTaskConfigPoList) {
        if (CollectionUtils.isEmpty(adTaskConfigPoList)) {
            return Collections.EMPTY_LIST;
        }

        List<SingleQueryTaskPageListReply> singleQueryTaskPageListReplies = new ArrayList<>();
        Map<Integer, String> categoryMap = new HashMap<>();

        List<Integer> categoryIdList = adTaskConfigPoList.stream()
                .map(AdTaskConfigPo::getCommerceCategoryFirstIds)
                .map(categoryStr -> categoryStr.split(","))
                .map(Arrays::asList)
                .flatMap(Collection::stream)
                .filter(t -> !StringUtils.isEmpty(t))
                .map(Integer::parseInt)
                .distinct()
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(categoryIdList)) {
            IndustryResp industryResp = crmPortalProxy.batchQueryUnitedIndustryByIds(categoryIdList);
            categoryMap = industryResp.getData().getIndustryInfosList().stream().collect(Collectors.toMap(IndustryInfo::getId, IndustryInfo::getName));
        }
        Map<Long, String> projectNameMap = new HashMap<>();
        List<Long> projectIds = adTaskConfigPoList.stream().map(AdTaskConfigPo::getProjectId).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(projectIds)) {
            BatchQueryByProjectIdResp queryByProjectIdResp = crmPlatformProxy.batchQueryByProjectIds(projectIds);
            projectNameMap = queryByProjectIdResp.getDataList().stream().collect(Collectors.toMap(ProjectInfo::getProjectId, ProjectInfo::getProjectName));
        }

        List<Long> couponTemplatedIds = adTaskConfigPoList.stream().map(AdTaskConfigPo::getCouponTemplateId).map(Long::valueOf).distinct().collect(Collectors.toList());

        Map<Long, Long> couponValueMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(couponTemplatedIds)) {
            couponValueMap = adTaskProjectService.batchQueryCouponTemplateValueById(couponTemplatedIds);
        }

        List<Long> taskIdList = adTaskConfigPoList.stream().map(AdTaskConfigPo::getId).collect(Collectors.toList());
        List<AdAccountTaskRelPo> accountTaskRelPoList = adTaskRepo.queryAccountTaskRelsByTaskIds(taskIdList);

        Map<Long, List<AdAccountTaskRelPo>> taskIdRelsMap = accountTaskRelPoList.stream().collect(Collectors.groupingBy(AdAccountTaskRelPo::getTaskId));

        List<Integer> needQueryAccountIds = accountTaskRelPoList.stream()
                .filter(adAccountTaskRelPo -> Objects.equals(adAccountTaskRelPo.getCompleteDimension(), TaskCompleteDimensionTypeEnum.ACCOUNT_DIMENSION.getCode()))
                .map(AdAccountTaskRelPo::getAccountId)
                .distinct()
                .collect(Collectors.toList());

        List<Integer> needQueryCustomerIds = accountTaskRelPoList.stream()
                .filter(adAccountTaskRelPo -> Objects.equals(adAccountTaskRelPo.getCompleteDimension(), TaskCompleteDimensionTypeEnum.CUSTOMER_DIMENSION.getCode()))
                .map(AdAccountTaskRelPo::getCustomerId)
                .distinct()
                .collect(Collectors.toList());

        List<Integer> needQueryAgentIds = accountTaskRelPoList.stream()
                .filter(adAccountTaskRelPo -> Objects.equals(adAccountTaskRelPo.getCompleteDimension(), TaskCompleteDimensionTypeEnum.AGENT_DIMENSION.getCode()))
                .map(AdAccountTaskRelPo::getAgentId)
                .distinct()
                .collect(Collectors.toList());

        List<Integer> needQueryProductIds = accountTaskRelPoList.stream()
                .filter(adAccountTaskRelPo -> Objects.equals(adAccountTaskRelPo.getCompleteDimension(), TaskCompleteDimensionTypeEnum.PRODUCT_DIMENSION.getCode()))
                .map(AdAccountTaskRelPo::getProductId)
                .distinct()
                .collect(Collectors.toList());

        Map<Integer, String> accountNameMap = new HashMap<>();
        Map<Integer, String> agentNameMap = new HashMap<>();
        Map<Integer, String> customerNameMap = new HashMap<>();
        Map<Integer, String> productNameMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(needQueryAccountIds)) {
            accountNameMap = newAccAccountRepo.queryAccountList(needQueryAccountIds).stream().collect(Collectors.toMap(AccAccountPo::getAccountId, AccAccountPo::getUsername));
        }
        if (!CollectionUtils.isEmpty(needQueryCustomerIds)) {
            customerNameMap = crmPlatformProxy.iSoaCustomerService().getCustomerBaseDtosByIds(needQueryCustomerIds).stream().collect(Collectors.toMap(CustomerBaseDto::getId, CustomerBaseDto::getUsername));
        }
        if (!CollectionUtils.isEmpty(needQueryAgentIds)) {
            agentNameMap = crmPlatformProxy.iSoaAgentService().getAgentId2NameMapInIds(needQueryAgentIds);
        }
        if (!CollectionUtils.isEmpty(needQueryProductIds)) {
            productNameMap = crmPortalProxy.batchQueryProduct(needQueryProductIds).stream().collect(Collectors.toMap(ProductInfo::getId, ProductInfo::getName));
        }


        for (AdTaskConfigPo adTaskConfigPo : adTaskConfigPoList) {
            String taskCateGoryNames = "";

            if (!StringUtils.isEmpty(adTaskConfigPo.getCommerceCategoryFirstIds())) {
                taskCateGoryNames = Arrays.stream(adTaskConfigPo.getCommerceCategoryFirstIds().split(","))
                        .map(Integer::parseInt)
                        .map(categoryMap::get)
                        .filter(Objects::nonNull)
                        .filter(t -> !StringUtils.isEmpty(t))
                        .collect(Collectors.joining(","));

            }
            Set<String> distrubtionNameSet = new HashSet<>();

            Map<Integer, List<Integer>> objectAccountMap = generateObjectAccountMap(accountTaskRelPoList);
            if (Objects.equals(adTaskConfigPo.getCompleteDimension(), TaskCompleteDimensionEnum.ACCOUNT_DIMENSION_VALUE)) {
                List<String> accountNameList = accountTaskRelPoList.stream()
                        .filter(adAccountTaskRelPo -> Objects.equals(adAccountTaskRelPo.getTaskId(), adTaskConfigPo.getId()))
                        .map(AdAccountTaskRelPo::getAccountId)
                        .map(accountNameMap::get)
                        .collect(Collectors.toList());
                distrubtionNameSet.addAll(accountNameList);
            } else if (Objects.equals(adTaskConfigPo.getCompleteDimension(), TaskCompleteDimensionEnum.CUSTOMER_DIMENSION_VALUE)) {
                List<String> customerNameList = accountTaskRelPoList.stream()
                        .filter(adAccountTaskRelPo -> Objects.equals(adAccountTaskRelPo.getTaskId(), adTaskConfigPo.getId()))
                        .map(AdAccountTaskRelPo::getCustomerId)
                        .map(customerNameMap::get)
                        .collect(Collectors.toList());
                distrubtionNameSet.addAll(customerNameList);
            } else if (Objects.equals(adTaskConfigPo.getCompleteDimension(), TaskCompleteDimensionEnum.AGENT_DIMENSION_VALUE)) {
                List<String> agentNameList = accountTaskRelPoList.stream()
                        .filter(adAccountTaskRelPo -> Objects.equals(adAccountTaskRelPo.getTaskId(), adTaskConfigPo.getId()))
                        .map(AdAccountTaskRelPo::getAgentId)
                        .map(agentNameMap::get)
                        .collect(Collectors.toList());
                distrubtionNameSet.addAll(agentNameList);
            } else if (Objects.equals(adTaskConfigPo.getCompleteDimension(), TaskCompleteDimensionEnum.PRODUCT_DIMENSION_VALUE)) {
                List<String> productNameList = accountTaskRelPoList.stream()
                        .filter(adAccountTaskRelPo -> Objects.equals(adAccountTaskRelPo.getTaskId(), adTaskConfigPo.getId()))
                        .map(AdAccountTaskRelPo::getProductId)
                        .map(productNameMap::get)
                        .collect(Collectors.toList());
                distrubtionNameSet.addAll(productNameList);
            }

            Long couponValue = couponValueMap.getOrDefault(adTaskConfigPo.getCouponTemplateId().longValue(), 0L);


            SingleQueryTaskPageListReply.Builder builder = SingleQueryTaskPageListReply.newBuilder();
            builder.setId(adTaskConfigPo.getId())
                    .setName(adTaskConfigPo.getName())
                    .setFirstIndustryName(taskCateGoryNames)
                    .setTaskCompleteDimension(TaskCompleteDimensionEnum.forNumber(adTaskConfigPo.getCompleteDimension()))
                    .setTaskCompleteDimensionName(TaskCompleteDimensionTypeEnum.fromValue(adTaskConfigPo.getCompleteDimension()).getDesc())
                    .setDesc(adTaskConfigPo.getDescription())
                    .setTaskType(TaskTypeEnum.forNumber(adTaskConfigPo.getType()))
                    .setTaskTypeName(AdpTaskTypeEnum.fromValue(adTaskConfigPo.getType()).getDesc())
                    .setProjectId(adTaskConfigPo.getProjectId())
                    .setProjectName(projectNameMap.getOrDefault(adTaskConfigPo.getProjectId(), ""))
                    .setCouponValue(couponValue)
                    .setCreator(adTaskConfigPo.getCreator())
                    .setCtime(adTaskConfigPo.getCtime().getTime());
            if (!CollectionUtils.isEmpty(distrubtionNameSet)) {
                builder.addAllDistributions(distrubtionNameSet.stream().filter(Objects::nonNull).collect(Collectors.toList()));
            }
            SingleQueryTaskPageListReply singleQueryTaskPageListReply = builder.build();

            singleQueryTaskPageListReplies.add(singleQueryTaskPageListReply);
        }
        return singleQueryTaskPageListReplies;
    }

    public AdTaskSaveDto generateAdTaskSaveDto(CreateTaskRequest request) {
        AdTaskSaveDto adTaskSaveDto = new AdTaskSaveDto();
        adTaskSaveDto.setFirstIndustryIds(request.getFirstIndustryIdsList());
        adTaskSaveDto.setName(request.getName());
        adTaskSaveDto.setDescription(request.getDesc());
        adTaskSaveDto.setTip(request.getTip());
        adTaskSaveDto.setTaskType(request.getTaskTypeValue());
        adTaskSaveDto.setTaskStartEvent(request.getTaskStartEventValue());
        adTaskSaveDto.setTaskStartType(request.getTaskStartTypeValue());
        if (Utils.isPositive(request.getStartDate())) {
            adTaskSaveDto.setStartDate(new Timestamp(request.getStartDate()));
        }
        if (Utils.isPositive(request.getEndDate())) {
            adTaskSaveDto.setEndDate(new Timestamp(request.getEndDate()));
        }
        adTaskSaveDto.setProjectId(request.getProjectId());
        adTaskSaveDto.setCouponNum(request.getCouponNum());
        adTaskSaveDto.setCreatorName(request.getCreatorName());
        adTaskSaveDto.setPeriod(request.getPeriod());
        adTaskSaveDto.setCustomerIds(request.getCustomerIdsList());
        adTaskSaveDto.setAgentIds(request.getAgentIdsList());
        adTaskSaveDto.setProductIds(request.getProductIdsList());
        adTaskSaveDto.setAccountIds(request.getAccountIdsList().stream().limit(ACCOUNT_LIMIT).collect(Collectors.toList()));
        adTaskSaveDto.setCreatorName(request.getCreatorName());
        adTaskSaveDto.setPeriod(request.getPeriod());
        adTaskSaveDto.setCompleteDimension(request.getTaskCompleteDimensionValue());
        adTaskSaveDto.setCouponTemplateId(request.getCouponTemplateId());
        adTaskSaveDto.setParentTaskIds(request.getParentTaskIdsList());

        List<AdTaskRuleSaveDto> ruleSaveDtos = request.getTaskRulesList().stream().map(t -> AdTaskRuleSaveDto.builder()
                .value(t.getValue())
                .conditionCode(t.getRuleConditionValue())
                .conditionExpression(t.getRuleConditionExpressionValue())
                .convTarget(StringUtils.isEmpty(t.getConvTarget()) ? "" : t.getConvTarget())
                .build()).collect(Collectors.toList());
        adTaskSaveDto.setTaskRules(ruleSaveDtos);

        List<Integer> objectIdList = request.getTaskObjectInfosList().stream().map(TaskObjectInfo::getObjectId).collect(Collectors.toList());
        List<Integer> accountIdList = request.getTaskObjectInfosList().stream().map(TaskObjectInfo::getAccountInfosList).flatMap(Collection::stream).map(AccountInfo::getAccountId).collect(Collectors.toList());
        if (Objects.equals(TaskCompleteDimensionTypeEnum.CUSTOMER_DIMENSION.getCode(), adTaskSaveDto.getCompleteDimension())) {
            adTaskSaveDto.setCustomerIds(objectIdList);
        } else if (Objects.equals(TaskCompleteDimensionTypeEnum.AGENT_DIMENSION.getCode(), adTaskSaveDto.getCompleteDimension())) {
            adTaskSaveDto.setAgentIds(objectIdList);
        } else if (Objects.equals(TaskCompleteDimensionTypeEnum.PRODUCT_DIMENSION.getCode(), adTaskSaveDto.getCompleteDimension())) {
            adTaskSaveDto.setProductIds(objectIdList);
        } else if (Objects.equals(TaskCompleteDimensionTypeEnum.ACCOUNT_DIMENSION.getCode(), adTaskSaveDto.getCompleteDimension())) {
            adTaskSaveDto.setAccountIds(objectIdList);
        }
        adTaskSaveDto.setAccountIds(accountIdList);

        List<TaskObjectInfo> taskObjectInfosList = request.getTaskObjectInfosList();
        Set<Integer> objectIdSet = new HashSet<>();
        for (TaskObjectInfo taskObjectInfo : taskObjectInfosList) {
            if (objectIdSet.contains(taskObjectInfo.getObjectId())) {
                throw new IllegalArgumentException("对象id重复 id:" + taskObjectInfo.getObjectId());
            } else {
                objectIdSet.add(taskObjectInfo.getObjectId());
            }
        }

        Map<Integer, List<Integer>> objectAccountMap = request.getTaskObjectInfosList().stream().collect(Collectors.toMap(TaskObjectInfo::getObjectId, taskObjectInfo -> taskObjectInfo.getAccountInfosList().stream().map(AccountInfo::getAccountId).collect(Collectors.toList())));
        adTaskSaveDto.setObjectAccountMap(objectAccountMap);

        return adTaskSaveDto;
    }

    public AdTaskSaveDto generateAdTaskSaveDto(UpdateTaskRequest request) {
        AdTaskSaveDto adTaskSaveDto = new AdTaskSaveDto();
        adTaskSaveDto.setId(request.getId());
        adTaskSaveDto.setFirstIndustryIds(request.getFirstIndustryIdsList());
        adTaskSaveDto.setName(request.getName());
        adTaskSaveDto.setDescription(request.getDesc());
        adTaskSaveDto.setTip(request.getTip());
        adTaskSaveDto.setTaskType(request.getTaskTypeValue());
        adTaskSaveDto.setTaskStartEvent(request.getTaskStartEventValue());
        adTaskSaveDto.setTaskStartType(request.getTaskStartTypeValue());
        if (Utils.isPositive(request.getStartDate())) {
            adTaskSaveDto.setStartDate(new Timestamp(request.getStartDate()));
        }
        if (Utils.isPositive(request.getEndDate())) {
            adTaskSaveDto.setEndDate(new Timestamp(request.getEndDate()));
        }
        adTaskSaveDto.setProjectId(request.getProjectId());
        adTaskSaveDto.setCouponNum(request.getCouponNum());
        adTaskSaveDto.setPeriod(request.getPeriod());
        adTaskSaveDto.setCustomerIds(request.getCustomerIdsList());
        adTaskSaveDto.setAgentIds(request.getAgentIdsList());
        adTaskSaveDto.setProductIds(request.getProductIdsList());
        adTaskSaveDto.setAccountIds(request.getAccountIdsList());
        adTaskSaveDto.setPeriod(request.getPeriod());
        adTaskSaveDto.setCompleteDimension(request.getTaskCompleteDimensionValue());
        adTaskSaveDto.setCouponTemplateId(request.getCouponTemplateId());
        adTaskSaveDto.setParentTaskIds(request.getParentTaskIdsList());
        adTaskSaveDto.setCreatorName(request.getCreatorName());
        List<AdTaskRuleSaveDto> ruleSaveDtos = request.getTaskRulesList().stream().map(t -> AdTaskRuleSaveDto.builder()
                .value(String.valueOf(t.getValue()))
                .conditionCode(t.getRuleConditionValue())
                .conditionExpression(t.getRuleConditionExpressionValue())
                .convTarget(t.getConvTarget())
                .build()).collect(Collectors.toList());
        adTaskSaveDto.setTaskRules(ruleSaveDtos);

        List<Integer> objectIdList = request.getTaskObjectInfosList().stream().map(TaskObjectInfo::getObjectId).collect(Collectors.toList());
        List<Integer> accountIdList = request.getTaskObjectInfosList().stream().map(TaskObjectInfo::getAccountInfosList).flatMap(Collection::stream).map(AccountInfo::getAccountId).collect(Collectors.toList());
        if (Objects.equals(TaskCompleteDimensionTypeEnum.CUSTOMER_DIMENSION.getCode(), adTaskSaveDto.getCompleteDimension())) {
            adTaskSaveDto.setCustomerIds(objectIdList);
        } else if (Objects.equals(TaskCompleteDimensionTypeEnum.AGENT_DIMENSION.getCode(), adTaskSaveDto.getCompleteDimension())) {
            adTaskSaveDto.setAgentIds(objectIdList);
        } else if (Objects.equals(TaskCompleteDimensionTypeEnum.PRODUCT_DIMENSION.getCode(), adTaskSaveDto.getCompleteDimension())) {
            adTaskSaveDto.setProductIds(objectIdList);
        } else if (Objects.equals(TaskCompleteDimensionTypeEnum.ACCOUNT_DIMENSION.getCode(), adTaskSaveDto.getCompleteDimension())) {
            adTaskSaveDto.setAccountIds(objectIdList);
        }
        adTaskSaveDto.setAccountIds(accountIdList);


        Map<Integer, List<Integer>> objectAccountMap = request.getTaskObjectInfosList().stream().collect(Collectors.toMap(TaskObjectInfo::getObjectId, taskObjectInfo -> taskObjectInfo.getAccountInfosList().stream().map(AccountInfo::getAccountId).collect(Collectors.toList())));
        adTaskSaveDto.setObjectAccountMap(objectAccountMap);

        return adTaskSaveDto;
    }

    public SendMessageDto generateSendMessageDto(Integer messageType, List<Integer> accountIds) {
        TaskSiteMessageTypeEnum taskSiteMessageTypeEnum = TaskSiteMessageTypeEnum.fromValue(messageType);
        Assert.notNull(taskSiteMessageTypeEnum, "站内信类型错误");

        return SendMessageDto.builder()
                .msgText(taskSiteMessageTypeEnum.getContent())
                .msgType(BsiMessageType.ACCOUNT)
                .senderId(0)
                .senderName(Operator.SYSTEM.getOperatorName())
                .system(SystemType.CPM.getCode())
                .title(taskSiteMessageTypeEnum.getTitle())
                .receiverIds(accountIds)
                .build();
    }

    public Map<Integer, String> queryCouponNameByCouponId(List<Integer> couponIdList) {
        List<AdAccountTaskCouponRelPo> adAccountTaskCouponRelPos = adTaskRepo.queryAccountTaskCouponByCouponId(couponIdList);
        Map<Integer, Long> couponIdTaskIdMap = adAccountTaskCouponRelPos.stream().collect(Collectors.toMap(AdAccountTaskCouponRelPo::getCouponId, AdAccountTaskCouponRelPo::getTaskId, (k1, k2) -> k1) );
        List<Long> taskIdList = adAccountTaskCouponRelPos.stream().map(AdAccountTaskCouponRelPo::getTaskId).distinct().collect(Collectors.toList());
        List<AdTaskConfigPo> adTaskConfigPos = adTaskRepo.queryTask(taskIdList);
        Map<Long, Integer> taskIdCouponIdMap = adTaskConfigPos.stream().collect(Collectors.toMap(AdTaskConfigPo::getId, AdTaskConfigPo::getCouponTemplateId));
        List<Long> couponTemplateIdList = adTaskConfigPos.stream().map(AdTaskConfigPo::getCouponTemplateId).map(Integer::longValue).distinct().collect(Collectors.toList());

        Map<Long, String> couponTemplateIdNameMap = adTaskProjectService.batchQueryCouponTemplateNameById(couponTemplateIdList);

        Map<Integer, String> resultMap = new HashMap<>();

        for (Integer couponId : couponIdList) {
            Long taskId = couponIdTaskIdMap.get(couponId);
            if (Objects.isNull(taskId)) {
                continue;
            }
            Integer couponTemplateId = taskIdCouponIdMap.get(taskId);
            Assert.notNull(couponTemplateId, "任务信息没查到");
            String couponName = couponTemplateIdNameMap.get(couponTemplateId.longValue());
            resultMap.put(couponId, couponName);

        }

        return resultMap;
    }

    public Boolean checkProjectObjectBalance(ProjectInfoDto projectInfoDto, AdTaskSaveDto taskCreateDto) {


        Long projectId = projectInfoDto.getProjectId();

        List<AdTaskConfigPo> adTaskConfigPos = adTaskRepo.queryTaskByProjectIds(Collections.singletonList(projectId));
        Map<Long, AdTaskConfigPo> taskIdMap = adTaskConfigPos.stream().collect(Collectors.toMap(AdTaskConfigPo::getId, Function.identity()));

        Map<Long, CouponTemplateInfoDto> couponIdTemplateInfoMap = projectInfoDto.getCouponIdTemplateInfoMap();

        List<AdAccountTaskRelPo> adAccountTaskRelPos = adTaskRepo.queryAccountTaskRelsByTaskIds(new ArrayList<>(taskIdMap.keySet()));

        Map<Integer, Long> limitMoneyAmountMap = projectInfoDto.getLimitMoneyAmountMap();

        List<AccAccountPo> accAccountPos = newAccAccountRepo.queryAccountList(taskCreateDto.getAccountIds());
        Map<Integer, List<AccAccountPo>> customerMap = accAccountPos.stream().collect(Collectors.groupingBy(AccAccountPo::getCustomerId));
        Map<Integer, List<AccAccountPo>> agentMap = accAccountPos.stream().collect(Collectors.groupingBy(AccAccountPo::getDependencyAgentId));
        Map<Integer, List<AccAccountPo>> accountMap = accAccountPos.stream().collect(Collectors.groupingBy(AccAccountPo::getAccountId));
        Map<Integer, List<AccAccountPo>> productMap = accAccountPos.stream().collect(Collectors.groupingBy(AccAccountPo::getProductId));

        for (Map.Entry<Integer, Long> limitMoneyEntry : limitMoneyAmountMap.entrySet()) {
            Integer objectType = limitMoneyEntry.getKey();
            if (Objects.equals(objectType, TaskEffectObjectTypeEnum.CUSTOMER.getCode()) && Utils.isPositive(limitMoneyEntry.getValue())) {
                List<Integer> customerIds = taskCreateDto.getCustomerIds();
                for (Integer customerId : customerIds) {
                    List<AdTaskConfigPo> customerTask = adAccountTaskRelPos.stream()
                            .filter(adAccountTaskRelPo -> Objects.equals(customerId, adAccountTaskRelPo.getCustomerId()) && !Objects.equals(adAccountTaskRelPo.getTaskId(), taskCreateDto.getId()))
                            .map(AdAccountTaskRelPo::getTaskId)
                            .map(taskIdMap::get)
                            .collect(Collectors.toList());

                    long customerAllCouponValue = customerTask.stream().map(adTaskConfigPo -> {
                        Integer couponTemplateId = adTaskConfigPo.getCouponTemplateId();
                        Long couponFaceValue = couponIdTemplateInfoMap.get(couponTemplateId.longValue()).getCouponFaceValue();
                        return couponFaceValue * adTaskConfigPo.getCouponNum();
                    }).mapToLong(Long::longValue).sum();

                    Long couponFaceValue = couponIdTemplateInfoMap.get(taskCreateDto.getCouponTemplateId().longValue()).getCouponFaceValue();
                    List<AccAccountPo> customerAccountList = customerMap.getOrDefault(customerId, new ArrayList<>());
                    long thisTaskCustomerCouponValue = taskCreateDto.getCouponNum() * couponFaceValue * customerAccountList.size();

                    if (customerAllCouponValue + thisTaskCustomerCouponValue > limitMoneyEntry.getValue() * 100) {
                        return false;
                    }
                }
            }

            if (Objects.equals(objectType, TaskEffectObjectTypeEnum.AGENT.getCode()) && Utils.isPositive(limitMoneyEntry.getValue())) {
                List<Integer> agentIds = taskCreateDto.getAgentIds();
                for (Integer agentId : agentIds) {
                    List<AdTaskConfigPo> agentTask = adAccountTaskRelPos.stream()
                            .filter(adAccountTaskRelPo -> Objects.equals(agentId, adAccountTaskRelPo.getAgentId()) && !Objects.equals(adAccountTaskRelPo.getTaskId(), taskCreateDto.getId()))
                            .map(AdAccountTaskRelPo::getTaskId)
                            .map(taskIdMap::get)
                            .collect(Collectors.toList());

                    long customerAllCouponValue = agentTask.stream().map(adTaskConfigPo -> {
                        Integer couponTemplateId = adTaskConfigPo.getCouponTemplateId();
                        Long couponFaceValue = couponIdTemplateInfoMap.get(couponTemplateId.longValue()).getCouponFaceValue();
                        return couponFaceValue * adTaskConfigPo.getCouponNum();
                    }).mapToLong(Long::longValue).sum();

                    Long couponFaceValue = couponIdTemplateInfoMap.get(taskCreateDto.getCouponTemplateId().longValue()).getCouponFaceValue();
                    List<AccAccountPo> agentAccountList = agentMap.getOrDefault(agentId, new ArrayList<>());
                    long thisTaskCustomerCouponValue = taskCreateDto.getCouponNum() * couponFaceValue * agentAccountList.size();

                    if (customerAllCouponValue + thisTaskCustomerCouponValue > limitMoneyEntry.getValue() * 100) {
                        return false;
                    }
                }
            }

            if (Objects.equals(objectType, TaskEffectObjectTypeEnum.ACCOUNT.getCode()) && Utils.isPositive(limitMoneyEntry.getValue())) {
                List<Integer> accountIds = taskCreateDto.getAccountIds();
                for (Integer accountId : accountIds) {
                    List<AdTaskConfigPo> accountTask = adAccountTaskRelPos.stream()
                            .filter(adAccountTaskRelPo -> Objects.equals(accountId, adAccountTaskRelPo.getAccountId()) && !Objects.equals(adAccountTaskRelPo.getTaskId(), taskCreateDto.getId()))
                            .map(AdAccountTaskRelPo::getTaskId)
                            .map(taskIdMap::get)
                            .collect(Collectors.toList());

                    long customerAllCouponValue = accountTask.stream().map(adTaskConfigPo -> {
                        Integer couponTemplateId = adTaskConfigPo.getCouponTemplateId();
                        Long couponFaceValue = couponIdTemplateInfoMap.get(couponTemplateId.longValue()).getCouponFaceValue();
                        return couponFaceValue * adTaskConfigPo.getCouponNum();
                    }).mapToLong(Long::longValue).sum();

                    Long couponFaceValue = couponIdTemplateInfoMap.get(taskCreateDto.getCouponTemplateId().longValue()).getCouponFaceValue();
                    List<AccAccountPo> accountList = accountMap.getOrDefault(accountId, new ArrayList<>());
                    long thisTaskCustomerCouponValue = taskCreateDto.getCouponNum() * couponFaceValue * accountList.size();

                    if (customerAllCouponValue + thisTaskCustomerCouponValue > limitMoneyEntry.getValue() * 100) {
                        return false;
                    }
                }
            }

            if (Objects.equals(objectType, TaskEffectObjectTypeEnum.PRODUCT.getCode()) && Utils.isPositive(limitMoneyEntry.getValue())) {
                List<Integer> productIds = taskCreateDto.getProductIds();
                for (Integer productId : productIds) {
                    List<AdTaskConfigPo> productTasks = adAccountTaskRelPos.stream()
                            .filter(adAccountTaskRelPo -> Objects.equals(productId, adAccountTaskRelPo.getProductId()) && !Objects.equals(adAccountTaskRelPo.getTaskId(), taskCreateDto.getId()))
                            .map(AdAccountTaskRelPo::getTaskId)
                            .map(taskIdMap::get)
                            .collect(Collectors.toList());

                    long customerAllCouponValue = productTasks.stream().map(adTaskConfigPo -> {
                        Integer couponTemplateId = adTaskConfigPo.getCouponTemplateId();
                        Long couponFaceValue = couponIdTemplateInfoMap.get(couponTemplateId.longValue()).getCouponFaceValue();
                        return couponFaceValue * adTaskConfigPo.getCouponNum();
                    }).mapToLong(Long::longValue).sum();

                    Long couponFaceValue = couponIdTemplateInfoMap.get(taskCreateDto.getCouponTemplateId().longValue()).getCouponFaceValue();
                    List<AccAccountPo> productAccountList = productMap.getOrDefault(productId, new ArrayList<>());
                    long thisTaskCustomerCouponValue = taskCreateDto.getCouponNum() * couponFaceValue * productAccountList.size();

                    if (customerAllCouponValue + thisTaskCustomerCouponValue > limitMoneyEntry.getValue() * 100) {
                        return false;
                    }
                }
            }

        }
        return true;

    }

    public TaskStatusEnum getParentTaskStatusByAccountTask(AdAccountTaskRelPo adAccountTaskRel) {
        Long taskId = adAccountTaskRel.getTaskId();
        List<AdTaskConfigPo> adTaskConfigPos = adTaskRepo.queryTask(Collections.singletonList(taskId));
        if (CollectionUtils.isEmpty(adTaskConfigPos)) {
            return null;
        }

        AdTaskConfigPo adTaskConfigPo = adTaskConfigPos.get(0);
        String parentTaskIdStr = adTaskConfigPo.getParentTaskId();
        if (org.apache.commons.lang3.StringUtils.isNotBlank(parentTaskIdStr)) {

            List<Long> parentTaskIdList = Arrays.stream(parentTaskIdStr.split(",")).map(Long::parseLong).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(parentTaskIdList)) {
                return null;
            }
            Long parentTaskId = parentTaskIdList.get(0);
            AdAccountTaskRelPo adAccountTaskRelPo = adTaskRepo.queryAccountTaskRelByAccountAndTask(adAccountTaskRel.getAccountId(), parentTaskId);
            if (adAccountTaskRelPo == null) {
                return null;
            }

            return getTaskStatusByAccountTask(adAccountTaskRelPo);

        }
        return null;
    }

    public TaskStatusEnum getTaskStatusByAccountTask(AdAccountTaskRelPo adAccountTaskRel) {

        if (adAccountTaskRel.getStatus().equals(DISCARD.getCode())) {
            return DISCARD;
        }

        Long taskId = adAccountTaskRel.getTaskId();
        List<AdTaskConfigPo> adTaskConfigPos = adTaskRepo.queryTask(Collections.singletonList(taskId));
        if (CollectionUtils.isEmpty(adTaskConfigPos)) {
            return null;
        }

        AdTaskConfigPo adTaskConfigPo = adTaskConfigPos.get(0);
        String parentTaskIdStr = adTaskConfigPo.getParentTaskId();
        if (org.apache.commons.lang3.StringUtils.isNotBlank(parentTaskIdStr)) {

            List<Long> parentTaskIdList = Arrays.stream(parentTaskIdStr.split(",")).map(Long::parseLong).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(parentTaskIdList)) {
                return null;
            }
            Long parentTaskId = parentTaskIdList.get(0);
            AdAccountTaskRelPo adAccountTaskRelPo = adTaskRepo.queryAccountTaskRelByAccountAndTask(adAccountTaskRel.getAccountId(), parentTaskId);
            if (adAccountTaskRelPo == null) {
                return null;
            }

            TaskStatusEnum parentTaskStatusByAccountTask = getTaskStatusByAccountTask(adAccountTaskRelPo);
            if (Objects.nonNull(parentTaskStatusByAccountTask) && Objects.equals(parentTaskStatusByAccountTask.getCode(), EXPIRED.getCode())) {
                return EXPIRED;
            }
            if (Objects.nonNull(parentTaskStatusByAccountTask) && !Objects.equals(parentTaskStatusByAccountTask.getCode(), COMPLETED_RECEIVED.getCode()) && !Objects.equals(parentTaskStatusByAccountTask.getCode(), COMPLETED_NOT_RECEIVE.getCode())) {
                return NOT_START;
            }
        }

        return TaskStatusEnum.fromValue(adAccountTaskRel.getStatus());

//        if (Objects.isNull(adAccountTaskRel.getBeginTime())) {
//            if (System.currentTimeMillis() - adAccountTaskRel.getCtime().getTime() > 24 * 3400 * 90 * 1000L) {
//                return EXPIRED;
//            } else {
//                return NOT_START;
//            }
//        }
//
//        if (System.currentTimeMillis() < adAccountTaskRel.getBeginTime().getTime()) {
//            return NOT_START;
//        }
//
//        //todo 这边再检查一下
//        if (Objects.equals(adAccountTaskRel.getIsCompleted(), 0)) {
//            if (System.currentTimeMillis() < adAccountTaskRel.getEndTime().getTime()) {
//                return PROCESSING;
//            } else {
//                return EXPIRED;
//            }
//        } else {
//            if (Objects.equals(adAccountTaskRel.getIsCouponReceived(), 0)) {
//                return COMPLETED_NOT_RECEIVE;
//            } else {
//                return COMPLETED_RECEIVED;
//            }
//        }
    }


    public TaskStatusEnum getTaskStatusByAccountTask(AdAccountTaskRelPo adAccountTaskRel, Map<Long, AdTaskConfigPo> adTaskConfigPoMap, List<AdAccountTaskRelPo> accountTaskRelPoList) {
        Long taskId = adAccountTaskRel.getTaskId();

        AdTaskConfigPo adTaskConfigPo = adTaskConfigPoMap.get(taskId);
        if (Objects.isNull(adTaskConfigPo)) {
            throw new IllegalArgumentException("未找到属于当前项目的任务 任务id:" + taskId);
        }

        if(Objects.equals(adAccountTaskRel.getStatus(), DISCARD.getCode())){
            return DISCARD;
        }

        String parentTaskIdStr = adTaskConfigPo.getParentTaskId();
        if (org.apache.commons.lang3.StringUtils.isNotBlank(parentTaskIdStr)) {

            List<Long> parentTaskIdList = Arrays.stream(parentTaskIdStr.split(",")).map(Long::parseLong).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(parentTaskIdList)) {
                return null;
            }
            Long parentTaskId = parentTaskIdList.get(0);
            List<AdAccountTaskRelPo> adAccountTaskRelPoList = accountTaskRelPoList.stream()
                    .filter(adAccountTaskRelPo -> Objects.equals(adAccountTaskRelPo.getAccountId(), adAccountTaskRel.getAccountId()) && Objects.equals(adAccountTaskRelPo.getTaskId(), parentTaskId))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(adAccountTaskRelPoList)) {
                throw new IllegalArgumentException("未找到属于当前的任务账户关系 任务id:" + parentTaskId + " 账户id:" + adAccountTaskRel.getAccountId());

            }
            AdAccountTaskRelPo adAccountTaskRelPo = adAccountTaskRelPoList.get(0);

            TaskStatusEnum parentTaskStatusByAccountTask = getTaskStatusByAccountTask(adAccountTaskRelPo, adTaskConfigPoMap, accountTaskRelPoList);
            if (Objects.nonNull(parentTaskStatusByAccountTask) && Objects.equals(parentTaskStatusByAccountTask.getCode(), EXPIRED.getCode())) {
                return EXPIRED;
            }
            if (Objects.nonNull(parentTaskStatusByAccountTask) && !Objects.equals(parentTaskStatusByAccountTask.getCode(), COMPLETED_RECEIVED.getCode()) && !Objects.equals(parentTaskStatusByAccountTask.getCode(), COMPLETED_NOT_RECEIVE.getCode())) {
                return NOT_START;
            }
        }

        if (Objects.isNull(adAccountTaskRel.getBeginTime())) {
            if (System.currentTimeMillis() - adAccountTaskRel.getCtime().getTime() > 24 * 3400 * 90 * 1000L) {
                return EXPIRED;
            } else {
                return NOT_START;
            }
        }

        if (System.currentTimeMillis() < adAccountTaskRel.getBeginTime().getTime()) {
            return NOT_START;
        }

        //todo 这边再检查一下
        if (Objects.equals(adAccountTaskRel.getIsCompleted(), 0)) {
            if (System.currentTimeMillis() < adAccountTaskRel.getEndTime().getTime()) {
                return PROCESSING;
            } else {
                return EXPIRED;
            }
        } else {
            if (Objects.equals(adAccountTaskRel.getIsCouponReceived(), 0)) {
                return COMPLETED_NOT_RECEIVE;
            } else {
                return COMPLETED_RECEIVED;
            }
        }
    }

    public List<Long> updateParentCompletedTime(AdAccountTaskRelPo adAccountTaskRelPo) {
        Long taskId = adAccountTaskRelPo.getTaskId();
        List<AdTaskConfigPo> adTaskConfigPos = adTaskRepo.queryTaskByParentTaskId(String.valueOf(taskId));
        List<Long> childTaskIdList = adTaskConfigPos.stream().map(AdTaskConfigPo::getId).collect(Collectors.toList());

        if (Objects.equals(adAccountTaskRelPo.getCompleteDimension(), TaskCompleteDimensionTypeEnum.ACCOUNT_DIMENSION.getCode())) {
            adTaskRepo.updateAccountTaskRelParentTaskCompletedTime(childTaskIdList, adAccountTaskRelPo.getAccountId(), null, null, null);
        } else if (Objects.equals(adAccountTaskRelPo.getCompleteDimension(), TaskCompleteDimensionTypeEnum.CUSTOMER_DIMENSION.getCode())) {
            adTaskRepo.updateAccountTaskRelParentTaskCompletedTime(childTaskIdList, null, adAccountTaskRelPo.getCustomerId(), null, null);
        } else if (Objects.equals(adAccountTaskRelPo.getCompleteDimension(), TaskCompleteDimensionTypeEnum.AGENT_DIMENSION.getCode())) {
            adTaskRepo.updateAccountTaskRelParentTaskCompletedTime(childTaskIdList, null, null, adAccountTaskRelPo.getAgentId(), null);
        } else if (Objects.equals(adAccountTaskRelPo.getCompleteDimension(), TaskCompleteDimensionTypeEnum.PRODUCT_DIMENSION.getCode())) {
            adTaskRepo.updateAccountTaskRelParentTaskCompletedTime(childTaskIdList, null, null, null, adAccountTaskRelPo.getProductId());
        }

        return childTaskIdList;
    }

    public void validateObjectAccountRelation(AdTaskSaveDto taskCreateDto, List<AccAccountPo> accAccountPos) {


        if (Objects.equals(taskCreateDto.getCompleteDimension(), TaskCompleteDimensionTypeEnum.CUSTOMER_DIMENSION.getCode())) {
            Map<Integer, List<AccAccountPo>> customerAccountListMap = accAccountPos.stream().collect(Collectors.groupingBy(AccAccountPo::getCustomerId));
            for (Map.Entry<Integer, List<AccAccountPo>> customerAccountListEntry : customerAccountListMap.entrySet()) {
                if (customerAccountListEntry.getValue().size() > 100) {
                    throw new IllegalArgumentException("客户：" + customerAccountListEntry.getKey() + "下的账户超出范围");
                }
            }

            Set<Integer> accountCustomerIdSet = accAccountPos.stream().map(AccAccountPo::getCustomerId).collect(Collectors.toSet());
            List<Integer> createCustomerIds = taskCreateDto.getCustomerIds();

            Assert.isTrue(Objects.equals(accountCustomerIdSet.size(), createCustomerIds.size()), "选择的对象id和账户的客户id不符");

            for (Integer createCustomerId : createCustomerIds) {
                if (!accountCustomerIdSet.contains(createCustomerId)) {
                    throw new IllegalArgumentException("有不在所选客户下的账户保存");
                }
            }

            Map<Integer, Integer> accountCustomerMap = accAccountPos.stream().collect(Collectors.toMap(AccAccountPo::getAccountId, AccAccountPo::getCustomerId));
            for (Map.Entry<Integer, List<Integer>> customerAccountEntry : taskCreateDto.getObjectAccountMap().entrySet()) {
                Integer customerId = customerAccountEntry.getKey();
                List<Integer> accountIds = customerAccountEntry.getValue();
                for (Integer accountId : accountIds) {
                    if (!Objects.equals(accountCustomerMap.get(accountId), customerId)) {
                        throw new IllegalArgumentException("账户id:" + accountId + "不属于客户id:" + customerId);
                    }
                }
            }
        }
        if (Objects.equals(taskCreateDto.getCompleteDimension(), TaskCompleteDimensionTypeEnum.AGENT_DIMENSION.getCode())) {
            Map<Integer, List<AccAccountPo>> agentAccountListMap = accAccountPos.stream().collect(Collectors.groupingBy(AccAccountPo::getDependencyAgentId));
            for (Map.Entry<Integer, List<AccAccountPo>> agentAccountListEntry : agentAccountListMap.entrySet()) {
                if (agentAccountListEntry.getValue().size() > 100) {
                    throw new IllegalArgumentException("代理商：" + agentAccountListEntry.getKey() + "下的账户超出范围");
                }
            }

            Set<Integer> accountAgentIdSet = accAccountPos.stream().map(AccAccountPo::getDependencyAgentId).collect(Collectors.toSet());
            List<Integer> createAgentIds = taskCreateDto.getAgentIds();
            Assert.isTrue(Objects.equals(accountAgentIdSet.size(), createAgentIds.size()), "选择的对象id和账户的代理商id不符");

            for (Integer createAgentId : createAgentIds) {
                if (!accountAgentIdSet.contains(createAgentId)) {
                    throw new IllegalArgumentException("有不在所选代理商下的账户保存");
                }
            }

            Map<Integer, Integer> accountAgentMap = accAccountPos.stream().collect(Collectors.toMap(AccAccountPo::getAccountId, AccAccountPo::getDependencyAgentId));
            for (Map.Entry<Integer, List<Integer>> agentAccountEntry : taskCreateDto.getObjectAccountMap().entrySet()) {
                Integer agentId = agentAccountEntry.getKey();
                List<Integer> accountIds = agentAccountEntry.getValue();
                for (Integer accountId : accountIds) {
                    if (!Objects.equals(accountAgentMap.get(accountId), agentId)) {
                        throw new IllegalArgumentException("账户id:" + accountId + "不属于代理商id:" + agentId);
                    }
                }
            }

        }
        if (Objects.equals(taskCreateDto.getCompleteDimension(), TaskCompleteDimensionTypeEnum.PRODUCT_DIMENSION.getCode())) {
            Map<Integer, List<AccAccountPo>> productAccountListMap = accAccountPos.stream().collect(Collectors.groupingBy(AccAccountPo::getProductId));
            for (Map.Entry<Integer, List<AccAccountPo>> productAccountListEntry : productAccountListMap.entrySet()) {
                if (productAccountListEntry.getValue().size() > 100) {
                    throw new IllegalArgumentException("品牌：" + productAccountListEntry.getKey() + "下的账户超出范围");
                }
            }

            Set<Integer> accountProductIdSet = accAccountPos.stream().map(AccAccountPo::getProductId).collect(Collectors.toSet());
            List<Integer> createProductIds = taskCreateDto.getProductIds();

            Assert.isTrue(Objects.equals(accountProductIdSet.size(), createProductIds.size()), "选择的对象id和账户的品牌id不符");

            for (Integer createProductId : createProductIds) {
                if (!accountProductIdSet.contains(createProductId)) {
                    throw new IllegalArgumentException("有不在所选品牌下的账户保存");
                }
            }

            Map<Integer, Integer> accountProductMap = accAccountPos.stream().collect(Collectors.toMap(AccAccountPo::getAccountId, AccAccountPo::getProductId));
            for (Map.Entry<Integer, List<Integer>> productAccountEntry : taskCreateDto.getObjectAccountMap().entrySet()) {
                Integer productId = productAccountEntry.getKey();
                List<Integer> accountIds = productAccountEntry.getValue();
                for (Integer accountId : accountIds) {
                    if (!Objects.equals(accountProductMap.get(accountId), productId)) {
                        throw new IllegalArgumentException("账户id:" + accountId + "不属于品牌id:" + productId);
                    }
                }
            }
        }
    }

    public void validateAccountExist(AdTaskSaveDto taskCreateDto, List<AccAccountPo> accAccountPos) {

        Set<Integer> accountIds = new HashSet<>(taskCreateDto.getAccountIds());
        Set<Integer> allAccountIds = accAccountPos.stream().map(AccAccountPo::getAccountId).collect(Collectors.toSet());
        accountIds.removeIf(allAccountIds::contains);
        Assert.isTrue(org.apache.commons.collections4.CollectionUtils.isEmpty(accountIds), "账户不存在:" + JSON.toJSONString(accountIds));
    }

    public void validateParentTask(AdTaskSaveDto saveDto, List<Long> parentTaskIds) {
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(parentTaskIds)) {
            Long parentTaskId = parentTaskIds.get(0);

            Assert.isTrue(!Objects.equals(parentTaskId, saveDto.getId()), "父任务不能为该任务本身");


            //检查下父任务是否属于该项目
            List<AdTaskConfigPo> parentTasks = adTaskRepo.queryTask(parentTaskIds);
            Assert.isTrue(org.apache.commons.collections4.CollectionUtils.isNotEmpty(parentTasks), "不存在父任务");
            AdTaskConfigPo parentTask = parentTasks.get(0);
            Long parentTaskProjectId = parentTask.getProjectId();
            Assert.isTrue(Objects.equals(parentTaskProjectId, saveDto.getProjectId()), "父任务不属于该项目");

            Assert.isTrue(Objects.equals(parentTask.getCompleteDimension(), saveDto.getCompleteDimension()), "父子任务完成维度不一致");

            //检查下账户是否都有父任务
            List<AdAccountTaskRelPo> accountParentTaskList = adTaskRepo.queryAccountTaskRelByAccountsAndTask(saveDto.getAccountIds(), parentTaskId);
            Set<Integer> taskAccountSet = new HashSet<>(saveDto.getAccountIds());
            Set<Integer> hasParentTaskAccountSet = accountParentTaskList.stream().map(AdAccountTaskRelPo::getAccountId).collect(Collectors.toSet());
            taskAccountSet.removeIf(hasParentTaskAccountSet::contains);
            Assert.isTrue(org.apache.commons.collections4.CollectionUtils.isEmpty(taskAccountSet), "账户不存在父任务:" + JSON.toJSONString(taskAccountSet));

        }
    }

    public void validateObjectProjectRuleRangeInfo(AdTaskSaveDto taskCreateDto, ProjectInfoDto projectInfoDto, List<AccAccountPo> accAccountPos) {

        List<RangeInfo> effectRangeInfoList = projectInfoDto.getEffectRangeInfoList();
        List<RangeInfo> limitRangeInfoList = projectInfoDto.getLimitRangeInfoList();

        if (Objects.equals(taskCreateDto.getCompleteDimension(), TaskCompleteDimensionTypeEnum.CUSTOMER_DIMENSION.getCode())) {
            List<Integer> customerIds = taskCreateDto.getCustomerIds();
            for (RangeInfo rangeInfo : effectRangeInfoList) {
                if (Objects.equals(rangeInfo.getObjectType(), TaskEffectObjectTypeEnum.CUSTOMER.getCode())) {
                    Set<Integer> effectCustomerIdSet = rangeInfo.getSingleRangeInfoList().stream().map(SingleRangeInfo::getObjectId).collect(Collectors.toSet());
                    for (Integer customerId : customerIds) {
                        if (!effectCustomerIdSet.contains(customerId)) {
                            throw new IllegalArgumentException("客户id不在项目生效范围内 客户id：" + customerId);
                        }
                    }

                }
            }

            for (RangeInfo rangeInfo : limitRangeInfoList) {

                if (Objects.equals(rangeInfo.getObjectType(), TaskEffectObjectTypeEnum.CUSTOMER.getCode())) {
                    Set<Integer> limitCustomerIdSet = rangeInfo.getSingleRangeInfoList().stream().map(SingleRangeInfo::getObjectId).collect(Collectors.toSet());
                    for (Integer customerId : customerIds) {
                        if (limitCustomerIdSet.contains(customerId)) {
                            throw new IllegalArgumentException("客户id在项目不生效范围内 客户id：" + customerId);
                        }
                    }
                }

            }

        } else if (Objects.equals(taskCreateDto.getCompleteDimension(), TaskCompleteDimensionTypeEnum.AGENT_DIMENSION.getCode())) {
            List<Integer> agentIds = taskCreateDto.getAgentIds();

            for (RangeInfo rangeInfo : effectRangeInfoList) {
                if (Objects.equals(rangeInfo.getObjectType(), TaskEffectObjectTypeEnum.CUSTOMER.getCode())) {
                    Set<Integer> effectCustomerIdSet = rangeInfo.getSingleRangeInfoList().stream().map(SingleRangeInfo::getObjectId).collect(Collectors.toSet());
                    Map<Integer, List<Integer>> agentCustomerIdMap = adTaskProjectService.queryCustomerByAgentIds(agentIds);
                    for (Map.Entry<Integer, List<Integer>> agentCustomerIdEntry : agentCustomerIdMap.entrySet()) {

                        List<Integer> hasEffectCustomerIdList = new ArrayList<>();
                        for (Integer customerId : agentCustomerIdEntry.getValue()) {
                            if (effectCustomerIdSet.contains(customerId)) {
                                hasEffectCustomerIdList.add(customerId);
                            }
                        }
                        if (org.apache.commons.collections4.CollectionUtils.isEmpty(hasEffectCustomerIdList)) {
                            throw new IllegalArgumentException("代理商的客户不在项目的生效范围内 代理商id" + agentCustomerIdEntry.getKey());
                        }
                    }
                }
            }

            for (RangeInfo rangeInfo : limitRangeInfoList) {
                if (Objects.equals(rangeInfo.getObjectType(), TaskEffectObjectTypeEnum.AGENT.getCode())) {
                    Set<Integer> limitAgentIdSet = rangeInfo.getSingleRangeInfoList().stream().map(SingleRangeInfoOrBuilder::getObjectId).collect(Collectors.toSet());

                    for (Integer agentId : agentIds) {
                        if (limitAgentIdSet.contains(agentId)) {
                            throw new IllegalArgumentException("代理商id在项目的不生效范围内 代理商id" + agentId);
                        }
                    }
                }
            }

        } else if (Objects.equals(taskCreateDto.getCompleteDimension(), TaskCompleteDimensionTypeEnum.PRODUCT_DIMENSION.getCode())) {
            List<Integer> productIds = taskCreateDto.getProductIds();
            for (RangeInfo rangeInfo : effectRangeInfoList) {
                if (Objects.equals(rangeInfo.getObjectType(), TaskEffectObjectTypeEnum.PRODUCT.getCode())) {
                    Set<Integer> effectProductIdSet = rangeInfo.getSingleRangeInfoList().stream().map(SingleRangeInfo::getObjectId).collect(Collectors.toSet());
                    for (Integer productId : productIds) {
                        if (!effectProductIdSet.contains(productId)) {
                            throw new IllegalArgumentException("品牌id不在项目的生效范围内 品牌id" + productId);
                        }
                    }

                }
            }
            for (RangeInfo rangeInfo : limitRangeInfoList) {
                if (Objects.equals(rangeInfo.getObjectType(), TaskEffectObjectTypeEnum.PRODUCT.getCode())) {
                    Set<Integer> limitProductIdSet = rangeInfo.getSingleRangeInfoList().stream().map(SingleRangeInfo::getObjectId).collect(Collectors.toSet());
                    for (Integer productId : productIds) {
                        if (limitProductIdSet.contains(productId)) {
                            throw new IllegalArgumentException("品牌id在项目的不生效范围内 品牌id" + productId);
                        }
                    }
                }
            }
        } else if (Objects.equals(taskCreateDto.getCompleteDimension(), TaskCompleteDimensionTypeEnum.ACCOUNT_DIMENSION.getCode())) {
            for (RangeInfo rangeInfo : effectRangeInfoList) {
                if (Objects.equals(rangeInfo.getObjectType(), TaskEffectObjectTypeEnum.ACCOUNT.getCode())) {
                    Set<Integer> effectAccountIdSet = rangeInfo.getSingleRangeInfoList().stream().map(SingleRangeInfo::getObjectId).collect(Collectors.toSet());
                    for (AccAccountPo accAccountPo : accAccountPos) {
                        if (!effectAccountIdSet.contains(accAccountPo.getAccountId())) {
                            throw new IllegalArgumentException("账户id不在项目生效范围内 账户id：" + accAccountPo.getAccountId());
                        }
                    }
                }
            }

            for (RangeInfo rangeInfo : limitRangeInfoList) {
                if (Objects.equals(rangeInfo.getObjectType(), TaskEffectObjectTypeEnum.ACCOUNT.getCode())) {
                    Set<Integer> limitAccountIdSet = rangeInfo.getSingleRangeInfoList().stream().map(SingleRangeInfo::getObjectId).collect(Collectors.toSet());
                    for (AccAccountPo accAccountPo : accAccountPos) {
                        if (limitAccountIdSet.contains(accAccountPo.getAccountId())) {
                            throw new IllegalArgumentException("账户id在项目不生效范围内 账户id：" + accAccountPo.getAccountId());
                        }
                    }
                }
            }
        }
    }

    public void validateCompleteDimensionAndStartEvent(AdTaskSaveDto adTaskSaveDto) {
        if (Objects.equals(adTaskSaveDto.getCompleteDimension(), TaskCompleteDimensionTypeEnum.ACCOUNT_DIMENSION.getCode())) {
            Assert.isTrue(Objects.equals(adTaskSaveDto.getTaskStartEvent(), TaskStartEventEnum.ACCOUNT_FIRST_CHARGE.getNumber()) || Objects.equals(adTaskSaveDto.getTaskStartEvent(), TaskStartEventEnum.ACCOUNT_FIRST_COST_VALUE), "账户维度任务只能是账户触发");
        } else if (Objects.equals(adTaskSaveDto.getCompleteDimension(), TaskCompleteDimensionTypeEnum.CUSTOMER_DIMENSION.getCode())) {
            Assert.isTrue(Objects.equals(adTaskSaveDto.getTaskStartEvent(), TaskStartEventEnum.CUSTOMER_FIRST_CHARGE.getNumber()) || Objects.equals(adTaskSaveDto.getTaskStartEvent(), TaskStartEventEnum.CUSTOMER_FIRST_COST_VALUE), "客户维度任务只能是客户触发");
        } else if (Objects.equals(adTaskSaveDto.getCompleteDimension(), TaskCompleteDimensionTypeEnum.AGENT_DIMENSION.getCode())) {
            Assert.isTrue(Objects.equals(adTaskSaveDto.getTaskStartEvent(), TaskStartEventEnum.AGENT_FIRST_CHARGE.getNumber()) || Objects.equals(adTaskSaveDto.getTaskStartEvent(), TaskStartEventEnum.AGENT_FIRST_COST_VALUE), "代理商维度任务只能是代理商触发");
        } else if (Objects.equals(adTaskSaveDto.getCompleteDimension(), TaskCompleteDimensionTypeEnum.PRODUCT_DIMENSION.getCode())) {
            Assert.isTrue(Objects.equals(adTaskSaveDto.getTaskStartEvent(), TaskStartEventEnum.PRODUCT_FIRST_CHARGE.getNumber()) || Objects.equals(adTaskSaveDto.getTaskStartEvent(), TaskStartEventEnum.PRODUCT_FIRST_COST_VALUE), "品牌维度任务只能是品牌触发");
        }
    }

    public Map<Integer, List<Integer>> generateObjectAccountMap(List<AdAccountTaskRelPo> adAccountTaskRelPoList) {
        Map<Integer, List<Integer>> resultMap = new HashMap<>();

        AdAccountTaskRelPo sampleAdAccountTaskRelPo = adAccountTaskRelPoList.get(0);
        Integer completeDimension = sampleAdAccountTaskRelPo.getCompleteDimension();

        if (Objects.equals(completeDimension, TaskCompleteDimensionTypeEnum.ACCOUNT_DIMENSION.getCode())) {
            resultMap = adAccountTaskRelPoList.stream().collect(Collectors.groupingBy(AdAccountTaskRelPo::getAccountId, Collectors.mapping(AdAccountTaskRelPo::getAccountId, Collectors.toList())));
        } else if (Objects.equals(completeDimension, TaskCompleteDimensionTypeEnum.CUSTOMER_DIMENSION.getCode())) {
            resultMap = adAccountTaskRelPoList.stream().collect(Collectors.groupingBy(AdAccountTaskRelPo::getCustomerId, Collectors.mapping(AdAccountTaskRelPo::getAccountId, Collectors.toList())));
        } else if (Objects.equals(completeDimension, TaskCompleteDimensionTypeEnum.AGENT_DIMENSION.getCode())) {
            resultMap = adAccountTaskRelPoList.stream().collect(Collectors.groupingBy(AdAccountTaskRelPo::getAgentId, Collectors.mapping(AdAccountTaskRelPo::getAccountId, Collectors.toList())));
        } else if (Objects.equals(completeDimension, TaskCompleteDimensionTypeEnum.PRODUCT_DIMENSION.getCode())) {
            resultMap = adAccountTaskRelPoList.stream().collect(Collectors.groupingBy(AdAccountTaskRelPo::getProductId, Collectors.mapping(AdAccountTaskRelPo::getAccountId, Collectors.toList())));
        }
        return resultMap;
    }

    public void validateTaskCouldDiscard(List<AdAccountTaskRelPo>adAccountTaskRelPoList){
        if(CollectionUtils.isEmpty(adAccountTaskRelPoList)){
            return;
        }
        AdAccountTaskRelPo adAccountTaskRelPo = adAccountTaskRelPoList.get(0);
        if(!Objects.equals(adAccountTaskRelPo.getStatus(),NOT_START.getCode()) && !Objects.equals(adAccountTaskRelPo.getStatus(),PROCESSING.getCode())){
            throw new IllegalArgumentException("任务状态不支持废弃");
        }

    }

}
