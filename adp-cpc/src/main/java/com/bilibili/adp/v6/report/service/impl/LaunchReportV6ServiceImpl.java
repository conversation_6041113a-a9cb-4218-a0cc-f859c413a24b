package com.bilibili.adp.v6.report.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.bapis.ad.pandora.core.auto.CountCreativeExploreCandidateReply;
import com.bapis.ad.pluto.Dimension;
import com.bapis.ad.pluto.meta.GetCountReq;
import com.bapis.ad.pluto.meta.GetCountResp;
import com.bapis.ad.pluto.service.v1.*;
import com.bilibili.adp.common.enums.*;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.StringDateParser;
import com.bilibili.adp.common.util.TimeUtils;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.common.util.func.Functions;
import com.bilibili.adp.cpc.biz.bos.component.ComponentInfoBo;
import com.bilibili.adp.cpc.biz.bos.component.CreativeConversionComponentBo;
import com.bilibili.adp.cpc.biz.bos.component.LauStoryComponentBo;
import com.bilibili.adp.cpc.biz.bos.component.LauUnderframeComponentBo;
import com.bilibili.adp.cpc.biz.bos.creative.QueryCreativeBo;
import com.bilibili.adp.cpc.biz.bos.creative.ShadowCreativeBo;
import com.bilibili.adp.cpc.biz.bos.report.CampaignReportBo;
import com.bilibili.adp.cpc.biz.bos.report.CreativeReportBo;
import com.bilibili.adp.cpc.biz.bos.report.StatBo;
import com.bilibili.adp.cpc.biz.bos.report.UnitReportBo;
import com.bilibili.adp.cpc.biz.bos.unit.QueryUnitBo;
import com.bilibili.adp.cpc.biz.services.campaign.ManagedCampaignService;
import com.bilibili.adp.cpc.biz.services.campaign.api.ICpcCampaignService;
import com.bilibili.adp.cpc.biz.services.campaign.dto.CpcCampaignDto;
import com.bilibili.adp.cpc.biz.services.campaign.dto.QueryCpcCampaignDto;
import com.bilibili.adp.cpc.biz.services.clean_up.LaunchCleanUpService;
import com.bilibili.adp.cpc.biz.services.creative.AdpCpcCreativeComponentService;
import com.bilibili.adp.cpc.biz.services.creative.CpcCreativeService;
import com.bilibili.adp.cpc.biz.services.creative.bos.programmatic.ProgrammaticInfoBo;
import com.bilibili.adp.cpc.biz.services.creative.dto.CpcCreativeDto;
import com.bilibili.adp.cpc.biz.services.dynamic.DynamicLinkProc;
import com.bilibili.adp.cpc.biz.services.game.AdpCpcGameService;
import com.bilibili.adp.cpc.biz.services.misc.AdpCpcOcpxService;
import com.bilibili.adp.cpc.biz.services.misc.bos.LauOcpxConfigBo;
import com.bilibili.adp.cpc.biz.services.ocpx.compensate.common.IOcpxAutoPayService;
import com.bilibili.adp.cpc.biz.services.ocpx.compensate.common.IOcpxAutoPayStatusService;
import com.bilibili.adp.cpc.biz.services.ocpx.compensate.common.IOcpxAutoPayTimeService;
import com.bilibili.adp.cpc.biz.services.resource.AdpCpcResourceService;
import com.bilibili.adp.cpc.biz.services.target_package.api.IResTargetItemService;
import com.bilibili.adp.cpc.biz.services.unit.AdpCpcLauUnitGameService;
import com.bilibili.adp.cpc.biz.services.unit.CpcUnitService;
import com.bilibili.adp.cpc.biz.services.unit.LauBudgetService;
import com.bilibili.adp.cpc.biz.services.unit.LauUnitExtraService;
import com.bilibili.adp.cpc.biz.services.unit.bos.LauUnitGameBo;
import com.bilibili.adp.cpc.biz.services.unit.dto.CpcUnitDto;
import com.bilibili.adp.cpc.biz.services.unit.dto.CpcUnitExtraDto;
import com.bilibili.adp.cpc.core.*;
import com.bilibili.adp.cpc.core.constants.DualBidTwoStageOptimization;
import com.bilibili.adp.cpc.core.constants.IsManaged;
import com.bilibili.adp.cpc.core.constants.LaunchStatus;
import com.bilibili.adp.cpc.core.constants.TemplateGroupV2;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauCampaignPo;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauSplashScreenCreativeImagePo;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauUnitExtraPo;
import com.bilibili.adp.cpc.dto.NewLauCampaignNextdayBudgetDto;
import com.bilibili.adp.cpc.dto.NewLauUnitNextdayBudgetDto;
import com.bilibili.adp.cpc.enums.AdpVersion;
import com.bilibili.adp.cpc.enums.ManagedVersion;
import com.bilibili.adp.cpc.enums.ad.CampaignAdType;
import com.bilibili.adp.cpc.enums.ad.OcpcTargetEnum;
import com.bilibili.adp.cpc.enums.ad.PromotionContentTypeEnum;
import com.bilibili.adp.cpc.enums.ad.PromotionPurposeType;
import com.bilibili.adp.cpc.enums.ocpx.UnitPeriodTypeEnum;
import com.bilibili.adp.cpc.excel.FreezeHeaderSheetWriteHandler;
import com.bilibili.adp.cpc.pluto.converter.StatConverter;
import com.bilibili.adp.cpc.proxy.CpmPandoraProxy;
import com.bilibili.adp.cpc.splash_screen.creative.service.SplashScreenCreativeImageService;
import com.bilibili.adp.cpc.utils.OcpxTargetBidUtil;
import com.bilibili.adp.launch.api.common.BudgetLimitType;
import com.bilibili.adp.launch.api.common.LaunchConstant;
import com.bilibili.adp.launch.api.common.OcpxAutoPayStatus;
import com.bilibili.adp.launch.api.common.OcpxStageEnum;
import com.bilibili.adp.launch.api.creative.dto.BilibiliVideoBo;
import com.bilibili.adp.launch.api.creative.dto.ImageDto;
import com.bilibili.adp.launch.api.launch.dto.OcpxAutoPayStatusDto;
import com.bilibili.adp.launch.api.launch.dto.OcpxAutoPayTimeDto;
import com.bilibili.adp.launch.api.unit.dto.UnitOcpxFloatStatusDto;
import com.bilibili.adp.launch.biz.service.LauCampaignService;
import com.bilibili.adp.launch.biz.service.component.LaunchComponentService;
import com.bilibili.adp.legacy.bo.CampaignReportDetailBo;
import com.bilibili.adp.legacy.bo.CreativeReportDetailBo;
import com.bilibili.adp.legacy.bo.ReportSummaryBo;
import com.bilibili.adp.legacy.bo.UnitReportDetailBo;
import com.bilibili.adp.legacy.converters.ReportServiceConverter;
import com.bilibili.adp.legacy.creative.NativeCreativeStatusService;
import com.bilibili.adp.legacy.dto.CampaignReportExportDto;
import com.bilibili.adp.legacy.dto.CreativeReportExportDto;
import com.bilibili.adp.legacy.dto.UnitReportExportDto;
import com.bilibili.adp.legacy.goods.GoodsRealStatusService;
import com.bilibili.adp.legacy.stat.WebStatFillZeroHelperService;
import com.bilibili.adp.legacy.statistic.SubscribedReportColumnsService;
import com.bilibili.adp.resource.api.account.label.IAccountLabelService;
import com.bilibili.adp.resource.api.system.ISystemConfigService;
import com.bilibili.adp.resource.api.target_lau.dto.ResTargetItemDto;
import com.bilibili.adp.v6.enums.SupportAuto;
import com.bilibili.adp.v6.report.AdReportService;
import com.bilibili.adp.v6.report.PlutoProxy;
import com.bilibili.adp.v6.report.bo.QueryCampaignReportV6Bo;
import com.bilibili.adp.v6.report.bo.QueryCreativeReportV6Bo;
import com.bilibili.adp.v6.report.bo.QueryUnitReportV6Bo;
import com.bilibili.adp.v6.report.enums.*;
import com.bilibili.adp.v6.report.service.LaunchReportV6Service;
import com.bilibili.adp.v6.resource.dynamic.GeneralDynamicService;
import com.bilibili.adp.v6.resource.dynamic.bos.DynamicInfoBo;
import com.bilibili.adp.web.framework.core.Pagination;
import com.bilibili.bvid.BVIDUtils;
import com.bilibili.crm.platform.common.IsValid;
import com.bilibili.sycpb.acc.api.dict.common.YesNoEnum;
import com.dianping.cat.Cat;
import com.dianping.cat.CatConstants;
import com.dianping.cat.message.Transaction;
import com.google.common.collect.Lists;
import io.vavr.control.Try;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 *
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LaunchReportV6ServiceImpl implements LaunchReportV6Service {

    private static final int MAX_PAGE_SIZE = 100;
    private static final String MAX_PAGE_SIZE_MESSAGE = "1页最大不能超过100";

    private static final String CREATIVE_TYPE_PROGRAMMATIC = "程序化创意";
    private static final String CREATIVE_TYPE_CUSTOM = "自定义创意";

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Value("#{'${cpc.ocpx.auto.pay.black.unit.ids:}'.empty ? null : '${cpc.ocpx.auto.pay.black.unit.ids:}'.split(',')}")
    private List<Integer> ocpxAutoPayBlackUnitIds;

    @Value("${export.maxSize.limit:100000}")
    private Integer MAX_EXPORT_SIZE;
    @Value("${export.interval.millisecond:50}")
    private Integer EXPORT_INTERVAL_MILLIS;
    private final PlutoProxy plutoProxy;

    @Resource
    private CpmPandoraProxy cpmPandoraProxy;

    private final ICpcCampaignService cpcCampaignService;
    private final LauCampaignService lauCampaignService;
    private final LauBudgetService lauBudgetService;
    private final ManagedCampaignService managedCampaignService;
    private final AdReportService adReportService;
    private final WebStatFillZeroHelperService webStatFillZeroHelperService;
    private final LaunchUnitV1Service launchUnitV1Service;
    private final LaunchCampaignService launchCampaignService;
    private final LaunchUnitArchiveService launchUnitArchiveService;
    private final LaunchUnitTargetRuleService launchUnitTargetRuleService;
    private final LaunchCommonV1Service launchCommonV1Service;
    private final AdpCpcOcpxService ocpxService;
    private final AdpCpcResourceService slotGroupService;
    private final IResTargetItemService resTargetItemService;
    private final AdpCpcLauUnitGameService adpCpcLauUnitGameService;
    private final AdpCpcGameService adpCpcGameService;
    private final IOcpxAutoPayTimeService ocpxAutoPayTimeService;
    private final IOcpxAutoPayStatusService ocpxAutoPayStatusService;
    private final LaunchUnitAssistSearchService launchUnitAssistSearchService;
    private final IOcpxAutoPayService ocpxAutoPayService;
    private final GoodsRealStatusService goodsStatusQueryProc;
    private final LauUnitExtraService lauUnitExtraService;
    private final CpcCreativeService cpcCreativeService;
    private final LaunchCreativePreviewService launchCreativePreviewService;
    private final LaunchProgrammaticCreativeDetailService launchProgrammaticCreativeDetailService;
    private final ISystemConfigService systemConfigService;
    private final CpcUnitService cpcUnitServiceDelegate;
    private final IAccountLabelService accountLabelService;
    private final LaunchShadowCreativeService launchShadowCreativeService;
    private final AdpCpcCreativeComponentService adpCpcCreativeComponentService;
    private final NativeCreativeStatusService nativeCreativeStatusProc;
    private final SplashScreenCreativeImageService splashScreenCreativeImageService;
    private final SubscribedReportColumnsService subscribedReportColumnsProc;
    private final LaunchCleanUpService launchCleanUpService;
    private final GeneralDynamicService dynamicService;

    private static String getTimestampString(Timestamp timestamp) {
        return LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp.getTime()), ZoneId.systemDefault()).format(DATE_TIME_FORMATTER);
    }

    private static Timestamp getQueryTime(Timestamp startTime, Timestamp endTime) {
        if (startTime != null && endTime != null && Utils.getBeginOfDay(startTime).compareTo(Utils.getBeginOfDay(endTime)) == 0) {
            return Utils.getEndOfDay(startTime);
        } else {
            return null;
        }
    }

    public Pagination<List<CampaignReportDetailBo>> listCampaignStats(QueryCampaignReportV6Bo query) {
        Pagination<List<CampaignReportDetailBo>> pagination;
        Transaction transaction = Cat.newTransaction(CatConstants.TYPE_CALL, "/web_api/v6/sanlian/report/list/campaign");
        try {
            pagination = doListCampaignStats(query);
            transaction.setStatus(Transaction.SUCCESS);
        } catch (Exception e) {
            transaction.setStatus(e);
            Cat.logError(e);
            throw e;
        } finally {
            transaction.complete();
        }
        return pagination;
    }

    public StatBo getCampaignSummaryStat(QueryCampaignReportV6Bo query) {
        CampaignSummaryStatReq req = LaunchReportV6Helper.toCampaignSummaryStatReq(query);
        CampaignSummaryStatResp resp = plutoProxy.serviceV1StatsBlockingStub().getCampaignSummaryStat(req);
        return StatConverter.MAPPER.toBo(resp.getCampaignSummaryStat());
    }

    @Override
    public void exportCampaignStats(QueryCampaignReportV6Bo query, OutputStream outputStream) throws ServiceException {
        // 导出数据时间限制
        adReportService.validateExportTimeRange(query.getStartTime(), query.getEndTime());

        // 查询一页，pageSize 比较大(性能)
        query.setSize(MAX_PAGE_SIZE);
        Function<Integer, List<CampaignReportExportDto>> queryOnce = pn -> {
            query.setPage(pn);
            Pagination<List<CampaignReportDetailBo>> campaignStats = listCampaignStats(query);
            List<CampaignReportDetailBo> reports = campaignStats.getData();

            return reports.stream().map(CampaignReportExportDto::from).collect(Collectors.toList());
        };
        // 导出
        // 获取动态字段
        List<String> fieldsSet = subscribedReportColumnsProc.getSubscribedColumnList(query.getAccountId().intValue(),
                "campaign");
        fieldsSet = fieldsSet.stream().map(StrUtil::toCamelCase).collect(Collectors.toList());
        ExcelWriter writer = null;
        try {
            writer = EasyExcel.write(outputStream).build();
            final WriteSheet sheet1 = EasyExcel.writerSheet("sheet1").head(CampaignReportExportDto.class)
                    .includeColumnFiledNames(fieldsSet).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .registerWriteHandler(new FreezeHeaderSheetWriteHandler()).build();

            for (int pn = 1; pn <= MAX_EXPORT_SIZE / MAX_PAGE_SIZE; pn++) {
                List<CampaignReportExportDto> list = queryOnce.apply(pn);
                if (list.isEmpty()) {
                    break;
                }
                writer.write(list, sheet1);
            }
            Thread.sleep(EXPORT_INTERVAL_MILLIS);
        } catch (Exception e) {
            log.error("exportCampaignList error: ", e);
            throw new RuntimeException(e);
        } finally {
            if (writer != null) {
                writer.finish();
            }
        }
    }

    public Pagination<List<UnitReportDetailBo>> listUnitStats(QueryUnitReportV6Bo query) {
        Pagination<List<UnitReportDetailBo>> pagination;
        Transaction transaction = Cat.newTransaction(CatConstants.TYPE_CALL, "/web_api/v6/sanlian/report/list/unit");
        try {
            pagination = doListUnitStats(query);
            transaction.setStatus(Transaction.SUCCESS);
        } catch (Exception e) {
            transaction.setStatus(e);
            Cat.logError(e);
            throw e;
        } finally {
            transaction.complete();
        }
        return pagination;
    }

    public StatBo getUnitSummaryStat(QueryUnitReportV6Bo query) {
        final boolean ok = LaunchReportV6Helper.appendCleanUpUnitIds(query, launchCleanUpService);
        if (!ok) return StatConverter.MAPPER.toBo(UnitSummaryStatResp.getDefaultInstance().getUnitSummaryStat());

        UnitSummaryStatReq req = LaunchReportV6Helper.toUnitSummaryStatReq(query);
        UnitSummaryStatResp resp = plutoProxy.serviceV1StatsBlockingStub().getUnitSummaryStat(req);
        return StatConverter.MAPPER.toBo(resp.getUnitSummaryStat());
    }

    @Override
    public void exportUnitStats(QueryUnitReportV6Bo query, OutputStream outputStream) throws ServiceException {
        // 导出数据时间限制
        adReportService.validateExportTimeRange(query.getStartTime(), query.getEndTime());

        // 查询一页，pageSize 比较大(性能)
        query.setSize(MAX_PAGE_SIZE);
        Function<Integer, List<UnitReportExportDto>> queryOnce = pn -> {
            query.setPage(pn);
            Pagination<List<UnitReportDetailBo>> unitStats = listUnitStats(query);
            List<UnitReportDetailBo> data = unitStats.getData();
            return data.stream().map(UnitReportExportDto::from).collect(Collectors.toList());
        };
        // 导出
        List<String> fieldsSet = subscribedReportColumnsProc.getSubscribedColumnList(query.getAccountId().intValue(),
                "unit");
        fieldsSet = fieldsSet.stream().map(StrUtil::toCamelCase).collect(Collectors.toList());
        ExcelWriter writer = null;
        try {
            writer = EasyExcel.write(outputStream).build();
            final WriteSheet sheet1 = EasyExcel.writerSheet("sheet1").head(UnitReportExportDto.class)
                    .includeColumnFiledNames(fieldsSet).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .registerWriteHandler(new FreezeHeaderSheetWriteHandler()).build();
            for (int pn = 1; pn <= MAX_EXPORT_SIZE / MAX_PAGE_SIZE; pn++) {
                List<UnitReportExportDto> unitReportVos = queryOnce.apply(pn);
                if (CollectionUtils.isEmpty(unitReportVos)) {
                    break;
                }
                writer.write(unitReportVos, sheet1);
            }
            Thread.sleep(EXPORT_INTERVAL_MILLIS);
        } catch (Exception e) {
            log.error("exportUnitList error: ", e);
            throw new RuntimeException(e);
        } finally {
            if (writer != null) {
                writer.finish();
            }
        }
    }

    public Pagination<List<CreativeReportDetailBo>> listCreativeStats(QueryCreativeReportV6Bo query) {
        Pagination<List<CreativeReportDetailBo>> pagination;
        Transaction transaction = Cat.newTransaction(CatConstants.TYPE_CALL,
                "/web_api/v6/sanlian/report/list/creative");
        try {
            pagination = doListCreativeStats(query);
            transaction.setStatus(Transaction.SUCCESS);
        } catch (Exception e) {
            transaction.setStatus(e);
            Cat.logError(e);
            throw e;
        } finally {
            transaction.complete();
        }
        return pagination;
    }

    public StatBo getCreativeSummaryStat(QueryCreativeReportV6Bo query) {
        CreativeSummaryStatReq req = LaunchReportV6Helper.toCreativeSummaryStatReq(query);
        CreativeSummaryStatResp resp = plutoProxy.serviceV1StatsBlockingStub().getCreativeSummaryStat(req);
        return StatConverter.MAPPER.toBo(resp.getCreativeSummaryStat());
    }

    @Override
    public void exportCreativeStats(QueryCreativeReportV6Bo query, OutputStream outputStream) throws ServiceException {
        // 导出数据时间限制
        adReportService.validateExportTimeRange(query.getStartTime(), query.getEndTime());
        Function<Integer, List<CreativeReportExportDto>> queryOnce = pn -> {
            // 查询一页，pageSize 比较大(性能)
            query.setPage(pn);
            query.setSize(MAX_PAGE_SIZE);
            Pagination<List<CreativeReportDetailBo>> creativeStats = listCreativeStats(query);
            List<CreativeReportDetailBo> data = creativeStats.getData();
            log.info("getCreativeList before reports:{}", JSON.toJSONString(data));
            List<CreativeReportExportDto> creativeReportVos = data.stream().map(CreativeReportExportDto::from)
                    .collect(Collectors.toList());
            log.info("getCreativeList before reports:{}", JSON.toJSONString(creativeReportVos));
            return creativeReportVos;
        };
        // 导出
        // 获取动态字段
        List<String> fieldsSet = subscribedReportColumnsProc.getSubscribedColumnList(query.getAccountId().intValue(),
                "creative");
        //只在导出时有素材url字段
        fieldsSet.add("material_url");

        fieldsSet = fieldsSet.stream().map(StrUtil::toCamelCase).collect(Collectors.toList());
        ExcelWriter writer = null;
        try {
            writer = EasyExcel.write(outputStream).build();
            final WriteSheet sheet1 = EasyExcel.writerSheet("sheet1").head(CreativeReportExportDto.class)
                    .includeColumnFiledNames(fieldsSet).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .registerWriteHandler(new FreezeHeaderSheetWriteHandler()).build();
            for (int i = 1; i <= MAX_EXPORT_SIZE / MAX_PAGE_SIZE; i++) {
                List<CreativeReportExportDto> creativeReportVos = queryOnce.apply(i);
                if (creativeReportVos.isEmpty()) {
                    break;
                }
                writer.write(creativeReportVos, sheet1);
            }
            Thread.sleep(EXPORT_INTERVAL_MILLIS);
        } catch (Exception e) {
            log.error("exportCreativeList error: ", e);
            throw new RuntimeException(e);
        } finally {
            if (writer != null) {
                writer.finish();
            }
        }
    }

    private Pagination<List<CampaignReportDetailBo>> doListCampaignStats(QueryCampaignReportV6Bo query) {
        Assert.isTrue(query.getSize() <= MAX_PAGE_SIZE, MAX_PAGE_SIZE_MESSAGE);
        int accountId = query.getAccountId().intValue();
        Long startTime = query.getStartTime();
        Long endTime = query.getEndTime();
        Integer page = query.getPage();
        CampaignStatsReq campaignStatsReq = LaunchReportV6Helper.toCampaignStatsReq(query);
        CampaignStatsResp campaignStatsResp = plutoProxy.serviceV1StatsBlockingStub().listCampaignStats(campaignStatsReq);
        long total = campaignStatsResp.getTotal();
        if (CollectionUtils.isEmpty(campaignStatsResp.getCampaignStatsList())) {
            return new Pagination<>(page, (int) total, Collections.emptyList());
        }
        List<CampaignStat> campaignStats = campaignStatsResp.getCampaignStatsList();
        List<Integer> campaignIdsAfterSorted = campaignStats
                .stream()
                .map(CampaignStat::getCampaignId)
                .map(Long::intValue)
                .collect(Collectors.toList());
        QueryCpcCampaignDto queryCpcCampaignDto = QueryCpcCampaignDto
                .builder()
                .campaignIds(campaignIdsAfterSorted)
                .build();
        List<CpcCampaignDto> campaigns = cpcCampaignService.queryCpcCampaign(queryCpcCampaignDto);
        if (CollectionUtils.isEmpty(campaigns)) {
            return new Pagination<>(page, (int) total, Collections.emptyList());
        }
        Map<Integer, CpcCampaignDto> campaignMap = campaigns
                .stream()
                .collect(Collectors.toMap(CpcCampaignDto::getCampaignId, Function.identity()));
        final Map<Integer, BigDecimal> campaignBudgetMap = lauCampaignService.getCampaignBudgetMapInIdsAndTime(campaignIdsAfterSorted, getQueryTime(new Timestamp(startTime), new Timestamp(endTime)));
        final Map<Integer, List<NewLauCampaignNextdayBudgetDto>> campaignNextdayBudgetMap = lauBudgetService.getCampaignNextdayBudgetMapInIds(campaignIdsAfterSorted);
        final Map<Integer, Integer> availableCreativesCountMap = getAvailableCreativesCountMap(campaignIdsAfterSorted, accountId, Dimension.CAMPAIGN);
        List<CampaignReportBo> campaignReportBos = campaignStats
                .stream()
                .filter(campaignStat -> {
                    int campaignId = (int) campaignStat.getCampaignId();
                    return campaignMap.containsKey(campaignId);
                })
                .map(campaignStat -> {
                    int campaignId = (int) campaignStat.getCampaignId();
                    final CpcCampaignDto campaign = campaignMap.get(campaignId);
                    if (campaign == null) {
                        return null;
                    }
                    StatBo stat = StatConverter.MAPPER.toBo(campaignStat.getStat());
                    CampaignStatusV6 statusV6 = CampaignStatusV6.of(campaignStat.getCampaignStatusValue());
                    List<CampaignExtraStatusV6> campaignExtraStatusV6s = CampaignExtraStatusV6.of(campaignStat.getAccountStatusValue(), campaignStat.getCampaignStatusValue());
                    List<Integer> extraStatus = campaignExtraStatusV6s.stream().map(CampaignExtraStatusV6::getCampaignExtraStatus).collect(Collectors.toList());
                    List<String> extraStatusDesc = campaignExtraStatusV6s.stream().map(CampaignExtraStatusV6::getCampaignExtraStatusDesc).collect(Collectors.toList());
                    final BigDecimal beforeBudget = campaignBudgetMap.getOrDefault(campaign.getCampaignId(), BigDecimal.ZERO);
                    final List<NewLauCampaignNextdayBudgetDto> campaignNextdayBudgetDtoList = campaignNextdayBudgetMap.getOrDefault(campaign.getCampaignId(), Collections.emptyList());
                    final NewLauCampaignNextdayBudgetDto campaignNextdayBudget = campaignNextdayBudgetDtoList
                            .stream()
                            .findFirst()
                            .orElse(NewLauCampaignNextdayBudgetDto
                                    .builder()
                                    .nextdayBudget(0L)
                                    .nextdayBudgetLimitType(BudgetLimitType.DESIGNATE.getCode())
                                    .isRepeat(0)
                                    .build()
                            );
                    final Integer availableCreativesCount = availableCreativesCountMap.getOrDefault(campaign.getCampaignId(), 0);
                    return CampaignReportBo.builder()
                            .campaignId(campaign.getCampaignId())
                            .budget(IsValid.TRUE.getCode().equals(campaign.getIsGdPlus()) ? null : Utils.fromFenToYuan(campaign.getBudget()))
                            .budgetLimitType(campaign.getBudgetLimitType())
                            .hasNextdayBudget(CollectionUtils.isEmpty(campaignNextdayBudgetDtoList) ? 0 : 1)
                            .nextdayBudget(Utils.fromFenToYuan(campaignNextdayBudget.getNextdayBudget()))
                            .nextdayBudgetLimitType(campaignNextdayBudget.getNextdayBudgetLimitType())
                            .isRepeat(campaignNextdayBudget.getIsRepeat())
                            .campaignName(campaign.getCampaignName())
                            .promotionPurposeType(campaign.getPromotionPurposeType())
                            .promotionPurposeTypeDesc(PromotionPurposeType.getByCode(campaign.getPromotionPurposeType()).getDesc())
                            .isManaged(Objects.equals(1, campaign.getIsManaged()))
                            .isManagedVal(campaign.getIsManaged())
                            .speedMode(campaign.getSpeedMode())
                            .budgetRemainingModifyTimes(LaunchConstant.CAMPAIGN_BUDGET_UPDATE_LIMIT)
                            .beforeBudget(beforeBudget)
                            .status(statusV6.getCampaignStatus())
                            .statusDesc(statusV6.getCampaignStatusDesc())
                            .campaignStatusMtime(getTimestampString(campaign.getCampaignStatusMtime()))
                            .extraStatus(extraStatus)
                            .extraStatusDesc(extraStatusDesc)
                            .adpVersion(campaign.getAdpVersion())
                            .adpVersionDesc(AdpVersion.getSanlianAdpVersionDesc(campaign.getAdpVersion()))
                            .createTime(getTimestampString(campaign.getCtime()))
                            .campaignAdType(CampaignAdType.getByCode(campaign.getAdType()).getDesc())
                            .supportAuto(campaign.getSupportAuto())
                            .supportAutoDesc(SupportAuto.of(campaign.getSupportAuto()).getDesc())
                            .stat(stat)
                            .availableCreativesCount(availableCreativesCount)
                            .build();
                }).filter(Objects::nonNull)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(campaignReportBos)) {
            return new Pagination<>(page, (int) total, Collections.emptyList());
        }
        // 拼接其他字段
        List<Integer> managedCids = campaigns.stream().filter(cpcCampaignDto -> YesNoEnum.YES.getCode().equals(cpcCampaignDto.getIsManaged()))
                .map(CpcCampaignDto::getCampaignId).collect(Collectors.toList());
        Map<Integer, Integer> manageVersionMap = managedCampaignService.queryCampaignJobVersionMap(managedCids);
        campaignReportBos.forEach(campaignReportBo -> {
            if (campaignReportBo.isManaged()) {
                campaignReportBo.setManagedVersion(manageVersionMap.getOrDefault(campaignReportBo.getCampaignId(), ManagedVersion.LEGACY.getKey()));
            }
        });

        // 计算复合指标
        final List<CampaignReportDetailBo> campaignReportVos = campaignReportBos
                .stream()
                .map(ReportServiceConverter.MAPPER::bo2Vo)
                .filter(Objects::nonNull)
                .peek(campaignReportVo -> adReportService.fillReportManagedInfo(campaignReportVo, campaignMap.get(campaignReportVo.getCampaignId())))
                .collect(Collectors.toList());

        List<ReportSummaryBo> summaryVoList = campaignReportVos.stream()
                .map(CampaignReportDetailBo::getReportSummary)
                .collect(Collectors.toList());
        webStatFillZeroHelperService.fillOrderSubmitZeroByAccountId(summaryVoList, accountId);

        return new Pagination<>(page, (int) total, campaignReportVos);
    }

    private Map<Integer, Integer> getAvailableCreativesCountMap(List<Integer> objectIds, Integer accountId, Dimension dim) {
        GetCountReq.Builder builder = GetCountReq.newBuilder()
                .setKeyDimension(dim)
                .setValueDimension(Dimension.CREATIVE)
                .addAccountIds(accountId)
                .addCreativeStatus(CreativeStatus.CREATIVE_STATUS_VALID.getNumber());
        List<Long> objectIdsAsLong = objectIds.stream().map(Long::valueOf).collect(Collectors.toList());
        if (dim.equals(Dimension.CAMPAIGN)) {
            builder.addAllCampaignIds(objectIdsAsLong);
        } else if (dim.equals(Dimension.UNIT)) {
            builder.addAllUnitIds(objectIdsAsLong);
        }
        GetCountResp creativeCount;
        try {
            creativeCount = plutoProxy.metaBlockingStub().getCount(builder.build());
        } catch (Exception e) {
            log.error("getAvailableCreativesCountMap error", e);
            return Collections.emptyMap();
        }
        if (creativeCount == null) {
            return Collections.emptyMap();
        }
        return creativeCount.getCountsMap().entrySet().stream()
                .collect(Collectors.toMap(
                        entry -> entry.getKey().intValue(),
                        Map.Entry::getValue
                ));
    }

    private Pagination<List<UnitReportDetailBo>> doListUnitStats(QueryUnitReportV6Bo query) {
        Assert.isTrue(query.getSize() <= MAX_PAGE_SIZE, MAX_PAGE_SIZE_MESSAGE);
        int accountId = query.getAccountId().intValue();
        List<Integer> accountIds = Collections.singletonList(accountId);
        Integer page = query.getPage();
        final boolean ok = LaunchReportV6Helper.appendCleanUpUnitIds(query, launchCleanUpService);
        if (!ok) return Pagination.emptyPagination();

        UnitStatsReq req = LaunchReportV6Helper.toUnitStatsReq(query);
        UnitStatsResp resp = plutoProxy.serviceV1StatsBlockingStub().listUnitStats(req);
        long total = resp.getTotal();
        if (CollectionUtils.isEmpty(resp.getUnitStatsList())) {
            return new Pagination<>(page, (int) total, Collections.emptyList());
        }
        List<UnitStat> unitStats = resp.getUnitStatsList();
        List<Integer> unitIdsAfterSorted = unitStats
                .stream()
                .map(UnitStat::getUnitId)
                .map(Long::intValue)
                .collect(Collectors.toList());

        QueryUnitBo queryUnitBo = QueryUnitBo.builder().unitIds(unitIdsAfterSorted).fromReport(true).build();
        List<CpcUnitDto> units = launchUnitV1Service.listUnits(queryUnitBo);
        if (CollectionUtils.isEmpty(units)) {
            return new Pagination<>(page, (int) total, Collections.emptyList());
        }
        Map<Integer, CpcUnitDto> unitMap = units
                .stream()
                .collect(Collectors.toMap(CpcUnitDto::getUnitId, Function.identity()));
        final List<CpcUnitDto> unitsAfterSliced = units
                .stream()
                .filter(unit -> unitIdsAfterSorted.contains(unit.getUnitId()))
                .collect(Collectors.toList());
        final List<Integer> campaignIds = unitsAfterSliced.stream().map(CpcUnitDto::getCampaignId).distinct().collect(Collectors.toList());
        final Map<Integer, LauCampaignPo> campaignMap = launchCampaignService.listCampaigns(campaignIds).stream().collect(Collectors.toMap(LauCampaignPo::getCampaignId, Function.identity()));
        //final Map<Integer, Long> unitIdVideoIdMap = launchUnitArchiveService.getUnitIdVideoIdMap(unitIdsAfterSorted);
        final Map<Integer, LauUnitExtraPo> unitExtraInfosMap = lauUnitExtraService.getMapByUnitIds(unitIdsAfterSorted);

        final Map<Integer, Integer> unitIdTargetPackageIdMap = unitsAfterSliced
                .stream()
                .collect(Collectors.toMap(CpcUnitDto::getUnitId, CpcUnitDto::getTargetPackageId));
        final Map<Integer, List<Integer>> unitIdOsMap = launchUnitTargetRuleService.getUnitIdParsedOsMap(unitIdTargetPackageIdMap);
        final Map<Integer, Integer> ocpmStage2UnitMap = launchCommonV1Service.ocpxStage2MapByType(unitIdsAfterSorted, com.bilibili.adp.common.Constants.OCPX_CONFIG_TYPE_UNIT, com.bilibili.adp.common.Constants.OCPM_CONFIG_EXP_KEY);
        final Map<Integer, Integer> ocpmStage2AccountMap = launchCommonV1Service.ocpxStage2MapByType(accountIds, com.bilibili.adp.common.Constants.OCPX_CONFIG_TYPE_ACCOUNT, com.bilibili.adp.common.Constants.OCPM_CONFIG_EXP_KEY);

        final Map<Integer, Integer> ocpcStage2UnitMap = launchCommonV1Service.ocpxStage2MapByType(unitIdsAfterSorted, com.bilibili.adp.common.Constants.OCPX_CONFIG_TYPE_UNIT, com.bilibili.adp.common.Constants.OCPX_CONFIG_EXP_KEY);
        final Map<Integer, Integer> ocpcStage2AccountMap = launchCommonV1Service.ocpxStage2MapByType(accountIds, com.bilibili.adp.common.Constants.OCPX_CONFIG_TYPE_ACCOUNT, com.bilibili.adp.common.Constants.OCPX_CONFIG_EXP_KEY);

        final Map<Integer, Set<Integer>> ocpcConfigAccountMap = ocpxService.getOcpxConfigAccountMap(accountIds).values().stream().findFirst().orElse(Collections.emptyMap());
        final Map<Integer, Map<Integer, Set<Integer>>> ocpcConfigUnitMap = ocpxService.getOcpxConfigUnitMap(unitIdsAfterSorted);
        final Map<Integer, List<Integer>> unitScenesMap = slotGroupService.getUnitScenesMap(unitIdsAfterSorted);

        final Map<Integer, String> osMap = resTargetItemService.getItemByTargetType(TargetType.OS.getCode())
                .stream()
                .collect(Collectors.toMap(ResTargetItemDto::getId, ResTargetItemDto::getName));

        final Map<Integer, List<NewLauUnitNextdayBudgetDto>> unitNextdayBudgetMap = lauBudgetService.getUnitNextdayBudgetMapInIds(unitIdsAfterSorted);
        final Map<Integer, LauOcpxConfigBo> ocpxConfigBoMap = ocpxService.getOcpxTargetMap(null, null, null);
        final Map<Integer, LauUnitGameBo> unit2Game = adpCpcLauUnitGameService.getSubPkgMap(unitIdsAfterSorted);
        final List<Integer> gameBaseIds = unit2Game.values()
                .stream()
                .map(LauUnitGameBo::getGameBaseId)
                .distinct()
                .collect(Collectors.toList());
        final Map<Integer, Integer> game2SubPkgStatus = adpCpcGameService.fetchSubPkgAvailableGameStatusMap(gameBaseIds);
        final List<OcpxAutoPayTimeDto> autoPayTimeDtos = ocpxAutoPayTimeService.findAutoPayTimeDtosByUnitIds(unitIdsAfterSorted);
        final Map<Integer, OcpxAutoPayTimeDto> ocpxAutoPayTimeDtoMap = autoPayTimeDtos.stream()
                .collect(Collectors.toMap(OcpxAutoPayTimeDto::getUnitId, Function.identity()));
        final Map<Integer, Date> ocpxAutoPayTimeMap = autoPayTimeDtos.stream()
                .collect(Collectors.toMap(OcpxAutoPayTimeDto::getUnitId, OcpxAutoPayTimeDto::getTwoStageTime));
        final Map<Integer, Map<Integer, OcpxAutoPayStatusDto>> ocpxAutoPayStatusDtoMap = ocpxAutoPayStatusService.findAutoPayStatusByUnitIdList(unitIdsAfterSorted)
                .stream()
                .collect(Collectors.groupingBy(OcpxAutoPayStatusDto::getUnitId, Collectors.toMap(OcpxAutoPayStatusDto::getPeriod, Function.identity())));
        final Map<Integer, Integer> unitPeriodTypeMap = autoPayTimeDtos.stream()
                .collect(Collectors.toMap(OcpxAutoPayTimeDto::getUnitId, OcpxAutoPayTimeDto::getUnitPeriodType));
        final Map<Integer, Pair<Integer, String>> unitIdAssistSearchMap = launchUnitAssistSearchService.getUnitAssistSearchStatusAndTime(unitIdsAfterSorted);
        final Map<Integer, Boolean> unitIdCanCopyMap = launchUnitV1Service.unitsCanCopy(unitIdsAfterSorted, accountId);
        final Map<Integer, Integer> availableCreativesCountMap = getAvailableCreativesCountMap(unitIdsAfterSorted, accountId, Dimension.UNIT);
        List<UnitReportBo> unitReportBos = unitStats
                .stream()
                .filter(unitStat -> {
                    int unitId = (int) unitStat.getUnitId();
                    return unitMap.containsKey(unitId);
                })
                .map(unitStat -> {
                    int unitId = (int) unitStat.getUnitId();
                    final CpcUnitDto unit = unitMap.get(unitId);
                    if (Objects.isNull(unit)) return null;

                    final LauCampaignPo campaign = campaignMap.get(unit.getCampaignId());
                    StatBo stat = StatConverter.MAPPER.toBo(unitStat.getStat());
                    final int adpVersion = unit.getAdpVersion();
                    UnitStatusV6 statusV6 = UnitStatusV6.of(unitStat.getUnitStatusValue());
                    List<UnitExtraStatusV6> unitExtraStatusV6s = UnitExtraStatusV6.of(unitStat.getAccountStatusValue(), unitStat.getCampaignStatusValue(), unitStat.getUnitStatusValue());
                    List<Integer> extraStatus = unitExtraStatusV6s.stream().map(UnitExtraStatusV6::getUnitExtraStatus).collect(Collectors.toList());
                    List<String> extraStatusDesc = unitExtraStatusV6s.stream().map(UnitExtraStatusV6::getUnitExtraStatusDesc).collect(Collectors.toList());
                    final List<NewLauUnitNextdayBudgetDto> unitNextdayBudgetDtoList = unitNextdayBudgetMap.getOrDefault(unit.getUnitId(), Collections.emptyList());
                    final Integer hasNextDayBudget = CollectionUtils.isEmpty(unitNextdayBudgetDtoList) ? 0 : 1;
                    final NewLauUnitNextdayBudgetDto unitNextDayBudgetDto = unitNextdayBudgetDtoList
                            .stream()
                            .findFirst()
                            .orElse(NewLauUnitNextdayBudgetDto
                                    .builder()
                                    .nextdayBudget(0L)
                                    .isRepeat(0)
                                    .build()
                            );
                    //final Long avId = unitIdVideoIdMap.getOrDefault(unit.getUnitId(), 0L);
                    final List<Integer> os = unitIdOsMap.getOrDefault(unit.getUnitId(), Collections.emptyList());
                    final String osDesc = os
                            .stream()
                            .map(id -> osMap.getOrDefault(id, StringUtils.EMPTY))
                            .collect(Collectors.joining(","));
                    final Pair<Integer, String> unitIdAssistSearchPair = unitIdAssistSearchMap.get(unitId);
                    int salesType = AdpVersion.isCpcFlyMerge(unit.getAdpVersion()) && Utils.isPositive(unit.getOcpcTarget()) ?
                            SalesType.CPM.getCode() : unit.getSalesType();
                    SalesModeV6 salesModeV6 = SalesModeV6.of(unitStat.getSalesMode());
                    final Integer availableCreativesCount = availableCreativesCountMap.getOrDefault(unit.getUnitId(), 0);
                    BigDecimal budget = Utils.fromFenToYuan(unit.getBudget());
                    UnitReportBo unitReport = UnitReportBo.builder()
                            .accountId(unit.getAccountId())
                            .promotionPurposeType(unit.getPromotionPurposeType())
                            .ctime(unit.getCtime())
                            .ocpcTarget(unit.getOcpcTarget())
                            //.videoId(avId)
                            .slotGroupId(unit.getSlotGroup())
                            .beforeBudget(budget)
                            .campaignId(campaign.getCampaignId())
                            .campaignName(campaign.getCampaignName())
                            .unitId(unit.getUnitId())
                            .unitName(unit.getUnitName())
                            .budget(Utils.fromFenToYuan(unit.getBudget()))
                            .hasNextdayBudget(hasNextDayBudget)
                            .nextdayBudget(Utils.fromFenToYuan(unitNextDayBudgetDto.getNextdayBudget()))
                            .isRepeat(unitNextDayBudgetDto.getIsRepeat())
                            .costPrice(Utils.fromFenToYuan(unit.getCostPrice()))
                            .beginDate(unit.getLaunchBeginDate())
                            .endDate(unit.getLaunchEndDate())
                            .statusDesc(statusV6.getUnitStatusDesc())
                            .status(statusV6.getUnitStatus())
                            .unitStatusMtime(StringDateParser.getDateTimeString(unit.getUnitStatusMtime()))
                            .budgetRemainingModifyTimes(LaunchConstant.UNIT_BUDGET_UPDATE_LIMIT)
                            .isHistory(unit.getIsHistory())
                            .subPkgStatus(launchCommonV1Service.fetchSubPkgStatus(unit2Game, game2SubPkgStatus, unit.getUnitId()))
                            .salesType(salesType)
                            .salesTypeDesc(SalesType.getByCode(salesType).getName())
                            .salesMode(salesModeV6.getSalesMode())
                            .salesModeDesc(salesModeV6.getSalesModeDesc())
                            .extraStatus(extraStatus)
                            .extraStatusDesc(extraStatusDesc)
                            .twoStageBid(Utils.fromFenToYuan(unit.getTwoStageBid()))
                            .ocpxTargetDesc("")
                            .ocpxTargetOneBid(Utils.fromFenToYuan(unit.getTwoStageBid()))
                            .ocpxTargetTwo(unit.getOcpxTargetTwo())
                            .ocpxTargetTwoBid(OcpxTargetBidUtil.dto2Vo(unit.getOcpxTargetTwoBid(), unit.getOcpxTargetTwo()))
                            .osDesc(osDesc)
                            .adpVersion(adpVersion)
                            .adpVersionDesc(AdpVersion.getSanlianAdpVersionDesc(adpVersion))
                            .promotionContentTypeDesc(PromotionContentTypeEnum.getByCode(unit.getPromotionPurposeType()).getName())
                            .speedMode(unit.getSpeedMode())
                            .isManaged(Objects.equals(1, unit.getIsManaged()))
                            .isManagedVal(unit.getIsManaged())
                            .createTime(TimeUtils.formatDateTime(unit.getCtime()))
                            .isProgrammatic(unit.getIsProgrammatic())
                            .unitPeriodType(UnitPeriodTypeEnum.UNKNOWN.getCode())
                            //.avId(Utils.isPositive(avId) ? String.valueOf(avId) : StringUtils.EMPTY)
                            //.bvId(Utils.isPositive(avId) ? BVIDUtils.avToBv(avId) : StringUtils.EMPTY)
                            .stat(stat)
                            .assistTarget(unit.getAssistTarget())
                            .assistTargetDesc("--")
                            .searchFirstPriceCoefficient(unitExtraInfosMap != null && unitExtraInfosMap.get(unitId) != null && (unitExtraInfosMap.get(unitId).getSearchFirstPriceCoefficient() != null) ? Utils.fromFenToYuan(new BigDecimal(unitExtraInfosMap.get(unitId).getSearchFirstPriceCoefficient())).toString() : "--")
                            .searchPriceCoefficient(unit.getSearchPriceCoefficient() != null ? Utils.fromFenToYuan(new BigDecimal(unit.getSearchPriceCoefficient())).toString() : "--")
                            // todo 希望默认显示 "--"
                            .assistPrice(null)
                            .assistInSearch(unitIdAssistSearchPair == null ? null : unitIdAssistSearchPair.getLeft())
                            .assistSearchEndDate(unitIdAssistSearchPair == null ? null : unitIdAssistSearchPair.getRight())
                            .canCopy(unitIdCanCopyMap.getOrDefault(unitId, true))
                            .dualBidTwoStageOptimization(unit.getDualBidTwoStageOptimization())
                            .dualBidTwoStageOptimizationDesc(DualBidTwoStageOptimization.getByCode(unit.getDualBidTwoStageOptimization()).getDesc())
                            .supportAuto(campaign.getSupportAuto())
                            .supportAutoDesc(SupportAuto.of(campaign.getSupportAuto()).getDesc())
                            .availableCreativesCount(availableCreativesCount)
                            .isNoBid(unit.getIsNoBid())
                            .build();
                    final OcpxStageEnum ocpxStage;
                    final OcpxStageEnum ocpxTargetTwoStage;
                    // adp6 无脑新逻辑
                    if (AdpVersion.isCpcFlyMerge(unit.getAdpVersion())) {
                        ocpxStage = ocpxService.getOcpxStage(ocpcConfigUnitMap, ocpcConfigAccountMap, unit.getUnitId(), unit.getOcpcTarget(), unitScenesMap.get(unit.getUnitId()));
                        ocpxTargetTwoStage = ocpxService.getOcpxStage(ocpcConfigUnitMap, ocpcConfigAccountMap, unit.getUnitId(), unit.getOcpxTargetTwo(), unitScenesMap.get(unit.getUnitId()));
                    } else if (SalesType.CPM.getCode() == unit.getSalesType()) {
                        // ocpm走老逻辑
                        ocpxStage = launchCommonV1Service.getOcpxStage(ocpmStage2UnitMap, ocpmStage2AccountMap, unit);
                        ocpxTargetTwoStage = OcpxStageEnum.NON;
                    } else if (SalesType.CPC.getCode() == unit.getSalesType() && AdpVersion.isMergedInGeneral(unit.getAdpVersion())) {
                        // 新版ocpc走新逻辑
                        ocpxStage = ocpxService.getOcpxStage(ocpcConfigUnitMap, ocpcConfigAccountMap, unit.getUnitId(), unit.getOcpcTarget(), unitScenesMap.get(unit.getUnitId()));
                        ocpxTargetTwoStage = ocpxService.getOcpxStage(ocpcConfigUnitMap, ocpcConfigAccountMap, unit.getUnitId(), unit.getOcpxTargetTwo(), unitScenesMap.get(unit.getUnitId()));
                    } else if (Objects.equals(SalesType.CPC.getCode(), unit.getSalesType())) {
                        // 旧版ocpc走老逻辑
                        ocpxStage = launchCommonV1Service.getOcpxStage(ocpcStage2UnitMap, ocpcStage2AccountMap, unit);
                        ocpxTargetTwoStage = OcpxStageEnum.NON;
                    } else {
                        ocpxStage = OcpxStageEnum.NON;
                        ocpxTargetTwoStage = OcpxStageEnum.NON;
                    }

                    unitReport.setOcpxTargetTwoDesc(LaunchCommonV1Service.getOcpxTargetName(ocpxConfigBoMap, unit.getOcpxTargetTwo()));

                    // oCPM "完件"，比较特殊，有双目标的话，需要显示OcpxTargetTwoStageDesc
                    if (SalesType.CPM.getCode() == unit.getSalesType() && Objects.equals(unit.getOcpcTarget(), OcpcTargetEnum.APPLY.getCode())) {
                        if (Utils.isPositive(unit.getOcpxTargetTwo())) {
                            unitReport.setOcpxTargetTwoStageDesc(OcpcTargetEnum.getByCode(unit.getOcpxTargetTwo()).getDesc());
                        } else {
                            unitReport.setOcpxTargetTwoStageDesc("未开启");
                        }
                    } else {
                        if (Objects.equals(ocpxTargetTwoStage, OcpxStageEnum.NON)) {
                            unitReport.setOcpxTargetTwoStageDesc("未开启");
                        } else {
                            unitReport.setOcpxTargetTwoStageDesc(LaunchCommonV1Service.getOcpxTargetName(ocpxConfigBoMap, unit.getOcpxTargetTwo()));
                        }
                    }
                    if (Utils.isPositive(unit.getOcpcTarget())) {
                        unitReport.setOcpxTargetId(LaunchCommonV1Service.getOcpxTargetId(ocpxConfigBoMap, unit.getOcpcTarget()));
                        unitReport.setOcpxTargetDesc(LaunchCommonV1Service.getOcpxTargetName(ocpxConfigBoMap, unit.getOcpcTarget()));
                    }
                    if (Utils.isPositive(unit.getAssistTarget())) {
                        unitReport.setAssistTargetDesc(OcpcTargetEnum.getByCode(unit.getAssistTarget()).getDesc());
                        unitReport.setAssistPrice(Utils.fromFenToYuan(unit.getAssistPrice()));
                    }
                    unitReport.setOcpxStage(ocpxStage.getCode());
                    unitReport.setOcpxStageDesc(ocpxStage.getDesc());
                    // 增加ocpx自动赔付的状态
                    OcpxAutoPayStatus finalStatus;
                    // 单元在黑名单内
                    if (!CollectionUtils.isEmpty(ocpxAutoPayBlackUnitIds)
                            && ocpxAutoPayBlackUnitIds.contains(unit.getUnitId())) {
                        finalStatus = OcpxAutoPayStatus.UNKNOWN;
                    } else {
                        finalStatus = ocpxAutoPayService.getFinalStatus(unit.getUnitId(), unit.getOcpcTarget(), unit.getCtime(), unitPeriodTypeMap.get(unit.getUnitId()), ocpxAutoPayStatusDtoMap.get(unitId));
                        Date date = ocpxAutoPayTimeMap.get(unit.getUnitId());
                        List<Integer> flyOcpxTargetAutos = Arrays.asList(OcpcTargetEnum.VIDEO_PLAY.getCode(),
                                OcpcTargetEnum.USER_FOLLOW.getCode(),
                                OcpcTargetEnum.DYNAMIC_DETAIL_BROWSE.getCode());
                        //是否真的是"成本保障生效中"
                        if (flyOcpxTargetAutos.contains(unit.getOcpcTarget())) {
                            if (OcpxAutoPayStatus.IS_PROTECTING.equals(finalStatus)) {
                                if (date != null) {
                                    long time20220501 = Utils.getTimestamp("2022-05-01").getTime();
                                    long timeUnit = date.getTime();
                                    if (timeUnit <= time20220501) {
                                        finalStatus = OcpxAutoPayStatus.UNKNOWN;
                                    }
                                }
                            }
                        }
                    }
                    unitReport.setOcpxAutoPayFinalStatus(finalStatus.getDesc());
                    Pair<String, List<UnitOcpxFloatStatusDto>> pair = ocpxAutoPayService.getFloatStatusList(unit.getUnitId(), finalStatus, ocpxAutoPayTimeDtoMap.get(unitId), Optional.ofNullable(ocpxAutoPayStatusDtoMap.get(unitId)).map(Map::values).orElse(Collections.emptyList()));
                    unitReport.setTwoStageTime(pair.getLeft());
                    unitReport.setOcpxAutoPayFloatStatusList(pair.getValue());
                    unitReport.setUnitPeriodType(unitPeriodTypeMap.getOrDefault(unit.getUnitId(), UnitPeriodTypeEnum.UNKNOWN.getCode()));
                    unitReport.setUnderbid(false);
                    return unitReport;
                }).filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(unitReportBos)) {
            return new Pagination<>(page, (int) total, Collections.emptyList());
        }

        final List<UnitReportDetailBo> unitReportVos = unitReportBos
                .stream()
                .map(ReportServiceConverter.MAPPER::bo2Vo)
                .collect(Collectors.toList());

        // 单元的稿件是否有带货稿件
        LaunchReportService.handle(!query.isMapi(), goodsStatusQueryProc::procUnitGoodsStatus, unitReportVos, accountId);
        LaunchReportService.handle(!query.isMapi(), this::unitPostProcess, unitReportVos, accountId);

        return new Pagination<>(page, (int) total, unitReportVos);
    }

    private void unitPostProcess(List<UnitReportDetailBo> unitReportVos, Integer accountId) {
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(unitReportVos)) {
            List<Integer> reportCampaignIds = unitReportVos.stream().map(UnitReportDetailBo::getCampaignId)
                    .distinct()
                    .collect(Collectors.toList());
            // 获取计划
            List<CpcCampaignDto> cpcCampaignDtos = cpcCampaignService.queryCpcCampaign(QueryCpcCampaignDto.builder()
                    .campaignIds(reportCampaignIds)
                    .build());
            Map<Integer, Integer> campaign2PromotionPurposeType = cpcCampaignDtos.stream().collect(Collectors.toMap(CpcCampaignDto::getCampaignId, CpcCampaignDto::getPromotionPurposeType));
            unitReportVos.forEach(o -> {
                o.setPromotionPurposeType(campaign2PromotionPurposeType.getOrDefault(o.getCampaignId(), 0));
            });
        }

        List<ReportSummaryBo> summaryVoList = unitReportVos.stream()
                .map(UnitReportDetailBo::getReportSummary)
                .collect(Collectors.toList());
        webStatFillZeroHelperService.fillOrderSubmitZeroByAccountId(summaryVoList, accountId);
    }

    private Pagination<List<CreativeReportDetailBo>> doListCreativeStats(QueryCreativeReportV6Bo query) {
        Assert.isTrue(query.getSize() <= MAX_PAGE_SIZE, MAX_PAGE_SIZE_MESSAGE);
        int accountId = query.getAccountId().intValue();
        Integer page = query.getPage();
        CreativeStatsReq req = LaunchReportV6Helper.toCreativeStatsReq(query);
        CreativeStatsResp resp = plutoProxy.serviceV1StatsBlockingStub().listCreativeStats(req);
        if (CollectionUtils.isEmpty(resp.getCreativeStatsList())) {
            return new Pagination<>(page, 0, Collections.emptyList());
        }
        long total = resp.getTotal();
        List<CreativeStat> creativeStats = resp.getCreativeStatsList();
        List<Integer> creativeIdsAfterSorted = creativeStats
                .stream()
                .map(CreativeStat::getCreativeId)
                .map(Long::intValue)
                .collect(Collectors.toList());
        final List<CpcCreativeDto> creatives = cpcCreativeService.queryCpcCreativeDto(QueryCreativeBo.builder()
                .creativeIds(creativeIdsAfterSorted)
                .withCampaign(true)
                .withUnit(true)
                .withUnitExtra(true)
                .withImages(true)
                .withTitle(true)
                .withVideo(true)
                .withScenes(true)
                .withFlyScenes(true)
                .withPageGroup(true)
                .withArcCover(true)
                .withFlyDynamic(true)
                .withAdp6Video(true)
                .fromReport(true)
                .build());
        if (CollectionUtils.isEmpty(creatives)) {
            return new Pagination<>(page, (int) total, Collections.emptyList());
        }
        final Map<Integer, CpcCreativeDto> creativeMap = creatives
                .stream()
                .collect(Collectors.toMap(CpcCreativeDto::getCreativeId, Function.identity()));
        List<Integer> unitIds = creatives
                .stream()
                .filter(creative -> creativeIdsAfterSorted.contains(creative.getCreativeId()))
                .map(CpcCreativeDto::getUnitId)
                .distinct()
                .collect(Collectors.toList());
        //这里开始的逻辑都是 com.bilibili.adp.advertiser.portal.service.stat.HystrixCreativeService.getCpcCreativeReportVosFromESV2 搬运而来,如有问题可以对比调整
        final Map<Integer, Timestamp> creative2PreviewTimeMap = launchCreativePreviewService.creativeIdPreviewTimeMap(creativeIdsAfterSorted);
        Map<Integer, Long> totalCreativeMgkPageMap = new HashMap<>(creatives.size());
        for (CpcCreativeDto creativeDto : creatives) {
            totalCreativeMgkPageMap.put(creativeDto.getCreativeId(), creativeDto.getMgkPageId());
        }
        Map<Integer, Long> creativePageGroupIdMap = new HashMap<>();
        for (CpcCreativeDto creativeDto : creatives) {
            creativePageGroupIdMap.put(creativeDto.getCreativeId(), creativeDto.getPageGroupId());
        }
        final Map<Integer, LauUnitGameBo> unit2Game = adpCpcLauUnitGameService.getSubPkgMap(unitIds);
        final List<Integer> gameBaseIds = unit2Game.values()
                .stream()
                .map(LauUnitGameBo::getGameBaseId)
                .distinct()
                .collect(Collectors.toList());
        final Map<Integer, Integer> game2SubPkgStatus = adpCpcGameService.fetchSubPkgAvailableGameStatusMap(gameBaseIds);
        final List<Integer> noModifyCreativeAccountIds = JSONArray.parseArray(systemConfigService.getValueByItem(SystemConfig.NO_MODIFY_CREATIVE_ACCOUNT_IDS), Integer.class);
        final boolean isNoModify = noModifyCreativeAccountIds.contains(accountId);

        final List<Integer> programmaticCreativeIds = creativeIdsAfterSorted
                .stream()
                .map(creativeMap::get)
                .filter(Objects::nonNull)
                .filter(c -> Objects.equals(c.getIsProgrammatic(), 1))
                .filter(c -> !com.bilibili.adp.cpc.enums.AdpVersion.isCpcFlyMerge(c.getAdpVersion()))
                .map(CpcCreativeDto::getCreativeId)
                .collect(Collectors.toList());
        final Map<Integer, ProgrammaticInfoBo> programmaticInfoBoMap = launchProgrammaticCreativeDetailService.getProgrammaticInfoMap(programmaticCreativeIds);

        final List<Integer> mergedProgrammaticCreativeIds = creativeIdsAfterSorted
                .stream()
                .map(creativeMap::get)
                .filter(Objects::nonNull)
                .filter(c -> Objects.equals(c.getIsProgrammatic(), 1))
                .filter(c -> com.bilibili.adp.cpc.enums.AdpVersion.isCpcFlyMerge(c.getAdpVersion()))
                .map(CpcCreativeDto::getCreativeId)
                .collect(Collectors.toList());
        final Map<Integer, ProgrammaticInfoBo> mergedProgrammaticInfoBoMap =
                launchProgrammaticCreativeDetailService.getMergedProgrammaticInfoMap(mergedProgrammaticCreativeIds);

        if (!CollectionUtils.isEmpty(mergedProgrammaticInfoBoMap)) {
            programmaticInfoBoMap.putAll(mergedProgrammaticInfoBoMap);
        }

        //final Map<Integer, Long> unitIdVideoIdMap = launchUnitArchiveService.getUnitIdVideoIdMap(unitIds);
        List<Integer> liveRoomUnitIds = creativeMap.values().stream()
                .filter(creative ->
                        com.bilibili.adp.cpc.enums.AdpVersion.isCpcFlyMerge(creative.getAdpVersion()))
                .filter(creative -> creative.getTemplateGroupId().equals(TemplateGroupV2.LIVE))
                .map(CpcCreativeDto::getUnitId)
                .distinct()
                .collect(Collectors.toList());
        Map<Integer, String> liveRoomCoverMap =
                cpcUnitServiceDelegate.queryUnitLiveRoomCoverMap(liveRoomUnitIds);

        final List<LauSplashScreenCreativeImagePo> creativeImages = splashScreenCreativeImageService.list(creativeIdsAfterSorted);
        final Map<Integer, List<LauSplashScreenCreativeImagePo>> splashScreenCreativeIdImagesMap = creativeImages.stream()
                .collect(Collectors.groupingBy(LauSplashScreenCreativeImagePo::getCreativeId));

        Map<Integer, Integer> creativeExploreCandidateCountMap = new HashMap<>();
        List<Integer> creativeExploreFakeCreativeIds = creatives.stream()
                .filter(creative -> !Objects.equals(LaunchStatus.DELETED, creative.getStatus()))
                .filter(creative -> Objects.equals(AuditStatus.ACCEPT.getCode(), creative.getAuditStatus()))
                .filter(creative -> Objects.equals(IsManaged.EXPLORE, creative.getIsManaged()))
                .map(CpcCreativeDto::getCreativeId)
                .collect(Collectors.toList());
        if (!query.isMapi() && !CollectionUtils.isEmpty(creativeExploreFakeCreativeIds)) {
            CountCreativeExploreCandidateReply exploreCandidateReply = cpmPandoraProxy.countCreativeExploreCandidate(creativeExploreFakeCreativeIds);
            creativeExploreCandidateCountMap = exploreCandidateReply.getCreativeCandidateCountMap();
        }

        Map<Integer, Integer> finalCreativeExploreCandidateCountMap = creativeExploreCandidateCountMap;

        final List<Long> dynamicIds = query.isMapi() ? Collections.emptyList() : creatives.stream().map(CpcCreativeDto::getFlyDynamicId).filter(Utils::isPositive).distinct().collect(Collectors.toList());
        final Map<Long, DynamicInfoBo> dynamicInfoBoMap = dynamicService.getDynamicsInfo(dynamicIds).stream().collect(Collectors.toMap(DynamicInfoBo::getDynamicId, Function.identity()));
        List<CreativeReportBo> creativeReportBos = creativeStats
                .stream()
                .filter(creativeStat -> {
                    int creativeId = (int) creativeStat.getCreativeId();
                    return creativeMap.containsKey(creativeId);
                })
                .map(creativeStat -> {
                    int creativeId = (int) creativeStat.getCreativeId();
                    final CpcCreativeDto creative = creativeMap.get(creativeId);
                    StatBo stat = StatConverter.MAPPER.toBo(creativeStat.getStat());
                    CreativeStatusV6 statusV6 = CreativeStatusV6.of(creativeStat.getCreativeStatusValue());
                    List<CreativeExtraStatusV6> creativeExtraStatusV6s = CreativeExtraStatusV6.of(creativeStat.getAccountStatusValue(), creativeStat.getCampaignStatusValue(), creativeStat.getUnitStatusValue(), creativeStat.getCreativeStatusValue());
                    List<Integer> extraStatus = creativeExtraStatusV6s.stream().map(CreativeExtraStatusV6::getCreativeExtraStatus).collect(Collectors.toList());
                    List<String> extraStatusDesc = creativeExtraStatusV6s.stream().map(CreativeExtraStatusV6::getCreativeExtraStatusDesc).collect(Collectors.toList());
                    final Timestamp previewTime = creative2PreviewTimeMap.getOrDefault(creative.getCreativeId(), null);
                    final Long mgkPageId = totalCreativeMgkPageMap.get(creative.getCreativeId());
                    final String mgkPageIdStr = Utils.isPositive(mgkPageId) ? mgkPageId.toString() : "";
                    final Long pageGroupId = creativePageGroupIdMap.get(creative.getCreativeId());
                    final String pageGroupIdStr = Utils.isPositive(pageGroupId) ? pageGroupId.toString() : "";
                    final String creativeType = Try.of(() -> {
                        if (com.bilibili.adp.cpc.enums.AdpVersion.isCpcFlyMerge(creative.getAdpVersion())) {
                            return TemplateGroupV2.getDesc(creative.getTemplateGroupId());
                        }
                        return StringUtils.EMPTY;
                    }).getOrElse("");
                    List<String> imageUrls = CollectionUtils.isEmpty(creative.getImageDtos()) ? Collections.emptyList() : creative.getImageDtos().stream().map(ImageDto::getUrl).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(imageUrls) && liveRoomCoverMap.containsKey(creative.getUnitId())) {
                        imageUrls = Lists.newArrayList(liveRoomCoverMap.get(creative.getUnitId()));
                    }
                    final Long videoId = creative.getVideoId();
                    final Long originVideoId = creative.getMaterialCenterVideoId();
                    CpcUnitDto cpcUnitDto = creative.getUnit();
                    final Integer opcxTarget = cpcUnitDto.getOcpcTarget();
                    final String opcxTargetDesc = Utils.isPositive(opcxTarget) ? OcpcTargetEnum.getByCode(opcxTarget).getDesc() : StringUtils.EMPTY;
                    //final Long avId = unitIdVideoIdMap.getOrDefault(creative.getUnitId(), 0L);
                    final List<String> splashScreenCreativeImageUrls = splashScreenCreativeIdImagesMap.getOrDefault(creativeId, Collections.emptyList()).stream().map(LauSplashScreenCreativeImagePo::getUrl).collect(Collectors.toList());
                    String promotionPurposeContent = Objects.nonNull(creative.getMgkLandingPage()) ? creative.getMgkLandingPage().getPageUrl() : LaunchCommonV1Service.getAccessUrl(creative.getJumpType(), creative.getPromotionPurposeContent());
                    boolean isDynamicUrl = !StringUtils.isEmpty(promotionPurposeContent) &&
                            (promotionPurposeContent.contains(DynamicLinkProc.DYNAMIC_SKIP_LINK)
                                    || promotionPurposeContent.contains(DynamicLinkProc.DYNAMIC_MALL_LINK)
                                    || promotionPurposeContent.contains(DynamicLinkProc.DYNAMIC_INNER_LINK_BASE));
                    String contentUrl = "";
                    if (Utils.isPositive(creative.getFlyDynamicId()) && isDynamicUrl) {
                        contentUrl = DynamicLinkProc.DYNAMIC_INNER_LINK + creative.getFlyDynamicId();
                    } else if (Utils.isPositive(originVideoId)) {
                        contentUrl = com.bilibili.adp.cpc.biz.constants.Constants.BILIBILI_ARC_PC_PREFIX + originVideoId;
                    }
                    CpcUnitExtraDto unitExtraDto = creative.getUnitExtra();
                    Integer generalVersion = 1;
                    if (unitExtraDto != null) {
                        generalVersion = unitExtraDto.getGeneralVersion();
                    }
                    String arcCoverUrl = Optional.ofNullable(creative.getBilibiliVideoBo())
                            .map(BilibiliVideoBo::getCoverUrl)
                            .filter(StringUtils::isNotEmpty)
                            .orElse(creative.getArcCoverUrl());

                    return CreativeReportBo.builder()
                            .adpVersion(creative.getAdpVersion())
                            .adpVersionDesc(AdpVersion.getSanlianAdpVersionDesc(creative.getAdpVersion()))
                            .subPkgStatus(launchCommonV1Service.fetchSubPkgStatus(unit2Game, game2SubPkgStatus, creative.getUnitId()))
                            .campaignId(creative.getCampaignId())
                            .campaignName(creative.getCampaign() == null ? "--" : creative.getCampaign().getCampaignName())
                            .unitId(creative.getUnitId())
                            .budget(new BigDecimal(cpcUnitDto.getBudget()))
                            .unitName(cpcUnitDto.getUnitName())
                            .creativeId(creative.getCreativeId())
                            .title(creative.getTitle())
                            .imageUrls(CollectionUtils.isEmpty(imageUrls) ? splashScreenCreativeImageUrls : imageUrls)
                            .arcCoverUrl(arcCoverUrl)
                            .videoId(videoId)
                            //.avId(Utils.isPositive(avId) ? String.valueOf(avId) : StringUtils.EMPTY)
                            //.bvId(Utils.isPositive(avId) ? BVIDUtils.avToBv(avId) : StringUtils.EMPTY)
                            .creativeAvId(Utils.isPositive(originVideoId) ? String.valueOf(originVideoId) : StringUtils.EMPTY)
                            .creativeBvId(Utils.isPositive(originVideoId) ? BVIDUtils.avToBv(originVideoId) : StringUtils.EMPTY)
                            .contentUrl(contentUrl)
                            .mgkVideoId(creative.getMgkVideoId())
                            .mgkVideoUrl(creative.getMgkVideoUrl())
                            .supportAuto(creative.getCampaign().getSupportAuto())
                            .supportAutoDesc(SupportAuto.of(creative.getCampaign().getSupportAuto()).getDesc())
                            .isParentCreative(creative.getCampaign().getSupportAuto().equals(1))
                            .mgkPageId(mgkPageIdStr)
                            .pageGroupId(pageGroupIdStr)
                            .promotionPurposeContent(promotionPurposeContent)
                            .status(statusV6.getCreativeStatus())
                            .statusDesc(statusV6.getCreativeStatusDesc())
                            .reason(creative.getReason())
                            .previewTime(previewTime == null ? 0L : previewTime.getTime())
                            .previewStatus(LaunchCommonV1Service.getPreviewStatus(creative.getStatus(), creative.getAuditStatus(), previewTime, creative.getCampaign() == null ? null : creative.getCampaign().getPromotionPurposeType()))
                            // 历史原因, 不要纠结
                            .templateName(creativeType)
                            .isHistory(creative.getIsHistory())
                            .isModify(isNoModify ? 0 : 1)
                            .extraStatus(extraStatus)
                            .generalVersion(generalVersion)
                            .generalVersionDesc(generalVersion.equals(2) ? "新版" : "旧版")
                            .extraStatusDesc(extraStatusDesc)
                            .flowWeightState(creative.getFlowWeightState())
                            .cardTypes(Collections.emptyList())
                            .isProgrammatic(BooleanEnum.VALID.getCode() == Optional.ofNullable(creative.getIsProgrammatic()).orElse(0))
                            .programmaticInfo(programmaticInfoBoMap.get(creativeId))
                            .creativeType(BooleanEnum.VALID.getCode() == Optional.ofNullable(creative.getIsProgrammatic()).orElse(0) ? CREATIVE_TYPE_PROGRAMMATIC : CREATIVE_TYPE_CUSTOM)
                            .bilibiliVideo(creative.getBilibiliVideoBo())
                            .creativeName(creative.getCreativeName())
                            .advertisingMode(creative.getAdvertisingMode())
                            .advertisingModeDesc(AdvertisingMode.getByKey(creative.getAdvertisingMode()).getValue())
                            .materialDesc(creative.getDescription())
                            .isManaged(Objects.equals(1, creative.getIsManaged()))
                            .isManagedVal(creative.getIsManaged())
                            .creativeExploreCandidateCount(finalCreativeExploreCandidateCountMap.getOrDefault(creative.getCreativeId(), 0))
                            .createTime(TimeUtils.formatDateTime(creative.getCtime()))
                            .ocpxTargetDesc(opcxTargetDesc)
                            .stat(stat)
                            .auditStatus(creative.getAuditStatus())
                            .imageMd5(creative.getImageMd5())
                            .materialCenterVideoId(creative.getMaterialCenterVideoId())
                            .subjectId(cpcUnitDto.getSubjectId())
                            .dynamicId(creative.getDynamicId())
                            .hasAigcReplaceHistory(Functions.boolean2Integer(creative.isHasAigcReplaceHistory()))
                            .unitPromotionPurposeType(cpcUnitDto.getPromotionPurposeType())
                            .templateGroupId(creative.getTemplateGroupId())
                            .businessDomain(cpcUnitDto.getBusinessDomain())
                            .liveCoverUrl(liveRoomCoverMap.getOrDefault(creative.getUnitId(), StringUtils.EMPTY))
                            .dynamicInfo(Utils.isPositive(creative.getFlyDynamicId()) ? dynamicInfoBoMap.get(creative.getFlyDynamicId()) : null)
                            .build();
                })
                .collect(Collectors.toList());
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(creativeReportBos)) {
            return new Pagination<>(page, (int) total, Collections.emptyList());
        }
        // 计算复核指标
        List<Integer> creativeIds = creativeReportBos
                .stream()
                .map(CreativeReportBo::getCreativeId)
                .collect(Collectors.toList());
        final List<Integer> accountLabelIdList = accountLabelService.getLabelIdsByAccountId(accountId);
        // 原生创意状态
        LaunchReportService.handle(!query.isMapi(), nativeCreativeStatusProc::processNativeCreativeStatus, creativeReportBos, accountLabelIdList);

        final List<Integer> shadowCreativeAuditStatus = Collections.singletonList(com.bilibili.adp.cpc.core.constants.AuditStatus.AUDITING);
        final List<Integer> shadowCreativeCreativeStatus = Arrays.asList(com.bilibili.adp.cpc.core.constants.CreativeStatus.AUDITING, com.bilibili.adp.cpc.core.constants.CreativeStatus.LANDING_PAGE_AUDITING);
        Map<Integer, ShadowCreativeBo> shadowCreativeBoMap = launchShadowCreativeService.shadowCreatives(creativeIds, shadowCreativeAuditStatus, shadowCreativeCreativeStatus);

        final List<CreativeReportDetailBo> vos = creativeReportBos
                .stream()
                .map(ReportServiceConverter.MAPPER::bo2Vo)
                .filter(Objects::nonNull)
                .peek(creativeReport -> {
                    ShadowCreativeBo shadowCreativeBo = shadowCreativeBoMap.get(creativeReport.getCreativeId());
                    creativeReport.setHasAuditingShadowCreative(shadowCreativeBo != null ? 1 : 0);
                    if (shadowCreativeBo != null) {
                        creativeReport.setMgkPageId(shadowCreativeBo.getMgkPageId() + "");
                    }
                })
                .collect(Collectors.toList());

        Cat.logEvent("getCreativeList", "getSortedLaunchList");
        // 创意的稿件是否有带货稿件
        LaunchReportService.handle(!query.isMapi(), goodsStatusQueryProc::procCreativeGoodsStatus, vos, accountId);
        // 起飞指定场景了 story & 暗投了搜索广告位组，如果有图片，则移除(暗投的不应该显示)
        LaunchReportService.handle(!query.isMapi(), adReportService::operateStoryDarkSearchImageUrls, vos);
        //md5转素材中心id，素材url处理
        LaunchReportService.handle(!query.isMapi(), adReportService::md5ConvertMaterialCenterId, vos);
        //businessDomain后处理
        LaunchReportService.handle(!query.isMapi(), this::creativePostProcess, vos, accountId);
        // 替链信息处理
        adReportService.fillCreativeLinkReplaceInfo(vos);
        return new Pagination<>(page, (int) total, vos);
    }

    private void creativePostProcess(List<CreativeReportDetailBo> vos, Integer accountId) {
        if (!org.apache.commons.collections4.CollectionUtils.isEmpty(vos)) {
            List<Integer> creativeIdList = vos.stream().map(CreativeReportDetailBo::getCreativeId)
                    .collect(Collectors.toList());
            final Map<Integer, CreativeConversionComponentBo> componentMap = adpCpcCreativeComponentService.fetchMap(accountId, creativeIdList);
            vos.forEach(o -> {
                final CreativeConversionComponentBo creativeComponent = componentMap.get(o.getCreativeId());
                if (Objects.isNull(creativeComponent)) return;

                final List<ComponentInfoBo> componentInfoBos = new LinkedList<>();
                final LauStoryComponentBo storyComponent = creativeComponent.getStoryComponent();
                // story的三种类型
                if (Objects.nonNull(storyComponent)) {
                    componentInfoBos.add(ComponentInfoBo.builder()
                            .id(storyComponent.getComponentId())
                            .name(storyComponent.getComponentName())
                            .type(storyComponent.getComponentType())
                            .build());
                }
                final LauUnderframeComponentBo underframeComponent = creativeComponent.getUnderframeComponent();
                // 框下一种类型
                if (Objects.nonNull(underframeComponent)) {
                    componentInfoBos.add(ComponentInfoBo.builder()
                            .id(underframeComponent.getId())
                            .name(underframeComponent.getComponentName())
                            .type(LaunchComponentService.UNDERFRAME)
                            .build());
                }
                o.setComponents(componentInfoBos);
                StringBuilder componentsShow = new StringBuilder();
                if (!org.apache.commons.collections4.CollectionUtils.isEmpty(componentInfoBos)) {
                    for (ComponentInfoBo bo : componentInfoBos) {
                        if (bo.getType() == LaunchComponentService.UNDERFRAME) {
                            componentsShow.append("框下组件ID：" + bo.getId() + "<br>");
                            componentsShow.append("框下组件名称：" + bo.getName());
                        } else {
                            componentsShow.append("story组件ID：" + bo.getId() + "<br>");
                            componentsShow.append("story组件名称：" + bo.getName());
                        }
                    }
                }
                o.setComponentsShow(componentsShow.toString());
            });
        }

        List<ReportSummaryBo> summaryVoList = vos.stream()
                .map(CreativeReportDetailBo::getReportSummary)
                .collect(Collectors.toList());
        webStatFillZeroHelperService.fillOrderSubmitZeroByAccountId(summaryVoList, accountId);
    }
}
