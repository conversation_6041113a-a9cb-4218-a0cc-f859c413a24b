package com.bilibili.adp.v6.resource.archive.converters;

import com.bapis.ad.commercialorder.adauth.GetPageAdAuthRs;
import com.bapis.archive.service.Arc;
import com.bilibili.adp.v6.resource.archive.bos.CommercialOrderArchiveAuthBo;
import com.bilibili.adp.v6.resource.archive.bos.CommercialOrderArchiveStatusBo;
import com.bilibili.adp.v6.resource.archive.bos.CommercialOrderArchiveBo;
import com.bilibili.adp.v6.resource.common.CommercialOrderAuthCommonServiceConverter;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper(uses = CommercialOrderAuthCommonServiceConverter.class)
public interface CommercialOrderArchiveServiceConverter {
    CommercialOrderArchiveServiceConverter MAPPER = Mappers.getMapper(CommercialOrderArchiveServiceConverter.class);


    @Mapping(target = "hasAnchor", ignore = true)
    @Mapping(target = "name", source = "arc.author.name")
    @Mapping(target = "bvid", expression = "java(com.bilibili.bvid.BVIDUtils.avToBv(arc.getAid()))")
    @Mapping(target = "width", source = "arc.dimension.width")
    @Mapping(target = "height", source = "arc.dimension.height")
    @Mapping(target = "rotate", source = "arc.dimension.rotate")
    @Mapping(target = "mid", source = "arc.author.mid")
    @Mapping(target = "face", source = "arc.author.face")
    CommercialOrderArchiveBo toBo(Arc arc);

    default CommercialOrderArchiveBo toBo(Arc arc, Integer hasAnchor) {
        CommercialOrderArchiveBo bo = toBo(arc);
        bo.setHasAnchor(hasAnchor);
        return bo;
    }

    @Mapping(target = "archiveInfo", source = "arc")
    CommercialOrderArchiveStatusBo toBo(int authTimes, int renewalTimes, Arc arc);

    @Mapping(target = "authId", source = "item.applyId")
    @Mapping(target = "timeInfo", source = "item")
    @Mapping(target = "authStatus", source = "item.stateExt")
    @Mapping(target = "authSource", source = "item.type")
    @Mapping(target = "authMode", source = "item.mode")
    @Mapping(target = "sourceAccountId", source = "item.accountId")
    @Mapping(target = "archiveInfo", source = "arc")
    CommercialOrderArchiveAuthBo toBo(GetPageAdAuthRs.GetPageAdAuthRsItem item, Arc arc, String renewalStateExtDesc);
}
