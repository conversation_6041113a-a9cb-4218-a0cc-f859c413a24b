package com.bilibili.adp.v6.unit.bos.info;

import com.bilibili.adp.cpc.biz.services.live.bos.LiveReservationInfoBo;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @see LiveReservationInfoBo
 * @ClassName SanlianLiveReserveInfoBo
 * <AUTHOR>
 * @Date 2024/3/6 9:48 下午
 * @Version 1.0
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class SanlianLiveReserveInfoBo {

    private Integer id;

    /**
     * 直播预约 id
     */
    private String sid;

    /**
     * 标题
     */
    private String title;

    /**
     * 开始结束时间
     */
    private String startTime;
    private String endTime;

    /**
     * 直播预计开始时间
     */
    private String livePlanStartTime;

    /**
     * 直播up主mid
     */
    private String upMid;

    /**
     * 直播间id
     */
    private String roomId;

    /**
     * 预约状态
     */
    private Integer state;

    /**
     * 是否可预约
     */
    private Integer isAvailable;

    /**
     * 预约关系类型
     * @see com.bilibili.adp.cpc.enums.ReserveRelationTypeEnum
     */
    private Integer type;

}
