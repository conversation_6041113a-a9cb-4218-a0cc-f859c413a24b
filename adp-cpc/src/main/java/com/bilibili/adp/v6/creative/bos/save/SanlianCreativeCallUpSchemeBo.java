package com.bilibili.adp.v6.creative.bos.save;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName SanlianSaveCreativeCallUpSchemeBo
 * <AUTHOR>
 * @Date 2024/3/13 11:49 上午
 * @Version 1.0
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class SanlianCreativeCallUpSchemeBo {
    private Integer isSystemGenerated;
    private String schemeUrl;
}
