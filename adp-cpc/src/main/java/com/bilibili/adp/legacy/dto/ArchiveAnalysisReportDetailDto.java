package com.bilibili.adp.legacy.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ArchiveAnalysisReportDetailDto {

    /**
     * aid
     */
    @ApiModelProperty(value = "aid")
    @ExcelProperty(value = "aid")
    private String aid;
    /**
     * bvid
     */
    @ApiModelProperty(value = "bvid")
    @ExcelProperty(value = "bvid")
    private String bvid;
    /**
     * 封面
     */
    @ExcelIgnore
    @ExcelProperty(value = "封面")
    private String pic;
    /**
     * 标题
     */
    @ExcelProperty(value = "视频标题")
    private String title;
    /**
     * 稿件总时长
     */
    @ExcelIgnore
    @ExcelProperty(value = "稿件总时长")
    private Integer duration;
    //region 稿件作者信息
    /**
     * Up主mid
     */
    @ApiModelProperty(value = "Up主mid")
    @ExcelProperty(value = "Up主mid")
    private String mid;
    /**
     * Up主名称
     */
    @ExcelIgnore
    @ExcelProperty(value = "Up主名称")
    private String name;
    /**
     * 宽度
     */
    @ExcelIgnore
    @ExcelProperty(value = "宽度")
    private Integer width;
    /**
     * 高度
     */
    @ExcelIgnore
    @ExcelProperty(value = "高度")
    private Integer height;
    /**
     * 日期
     */

    @ApiModelProperty(value = "日期")
    @ExcelProperty(value = "日期")
    private String logDate;
    /**
     * 花费
     */
    @ApiModelProperty(value = "花费")
    @ExcelProperty(value = "花费")
    private BigDecimal cost;
    /**
     * 展示量
     */
    @ApiModelProperty(value = "展示量")
    @ExcelProperty(value = "展示量")
    private Long pv;
    /**
     * 点击量
     */
    @ApiModelProperty(value = "点击量")
    @ExcelProperty(value = "点击量")
    private Long click;
    /**
     * 点击率
     */
    @ApiModelProperty(value = "点击率")
    @ExcelProperty(value = "点击率")
    private BigDecimal clickThroughRate;
    /**
     * 单次点击价格
     */
    @ApiModelProperty(value = "单次点击价格")
    @ExcelProperty(value = "单次点击价格")
    private BigDecimal costPerClick;
    /**
     * 千次展示价格
     */
    @ApiModelProperty(value = "千次展示价格")
    @ExcelProperty(value = "千次展示价格")
    private BigDecimal costPerMille;
    /**
     * 3s播放率
     */
    @ApiModelProperty(value = "3s播放率")
    @ExcelProperty(value = "3s播放率")
    @JsonProperty(value = "play_3s_rate")
    private BigDecimal play3SRate;
    /**
     * 5s播放率
     */
    @ApiModelProperty(value = "5s播放率")
    @ExcelProperty(value = "5s播放率")
    @JsonProperty(value = "play_5s_rate")
    private BigDecimal play5SRate;
    /**
     * 10s播放率
     */
    @ApiModelProperty(value = "10s播放率")
    @ExcelProperty(value = "10s播放率")
    @JsonProperty(value = "play_10s_rate")
    private BigDecimal play10SRate;
    /**
     * 当日播放
     */
    @ApiModelProperty(value = "当日播放")
    @ExcelProperty(value = "当日播放")
    private Long playIncrement;
    /**
     * 新增评论数
     */
    @ApiModelProperty(value = "新增评论数")
    @ExcelProperty(value = "新增评论数")
    private Long replyIncrement;
    /**
     * 新增收藏数
     */
    @ApiModelProperty(value = "新增收藏数")
    @ExcelProperty(value = "新增收藏数")
    private Long favIncrement;
    /**
     * 新增投币数
     */
    @ApiModelProperty(value = "新增投币数")
    @ExcelProperty(value = "新增投币数")
    private Long coinIncrement;
    /**
     * 新增弹幕数
     */
    @ApiModelProperty(value = "新增弹幕数")
    @ExcelProperty(value = "新增弹幕数")
    private Long danmakuIncrement;
    /**
     * 新增分享数
     */
    @ApiModelProperty(value = "新增分享数")
    @ExcelProperty(value = "新增分享数")
    private Long shareIncrement;
    /**
     * 新增点赞数
     */
    @ApiModelProperty(value = "新增点赞数")
    @ExcelProperty(value = "新增点赞数")
    private Long likesIncrement;

    @ApiModelProperty(value = "播放量")
    @ExcelProperty(value = "播放量")
    private Long videoPlayCount;

    @ApiModelProperty(value = "播放成本")
    @ExcelProperty(value = "播放成本")
    private BigDecimal costPerVideoPlay;

    @ApiModelProperty(value = "播放率")
    @ExcelProperty(value = "播放率")
    private BigDecimal videoPlayRate;

    @ApiModelProperty(value = "互动量")
    @ExcelProperty(value = "互动量")
    private Long videoLikeCount;

    @ApiModelProperty(value = "互动成本")
    @ExcelProperty(value = "互动成本")
    private BigDecimal costPerVideoLike;

    @ApiModelProperty(value = "互动率")
    @ExcelProperty(value = "互动率")
    private BigDecimal videoLikeRate;

    @ApiModelProperty(value = "涨粉数")
    @ExcelProperty(value = "涨粉数")
    private Long fanIncreaseCount;

    @ApiModelProperty(value = "涨粉成本")
    @ExcelProperty(value = "涨粉成本")
    private BigDecimal costPerFanIncrease;

    @ApiModelProperty(value = "涨粉率")
    @ExcelProperty(value = "涨粉率")
    private BigDecimal fanIncreaseRate;

    @ApiModelProperty(value = "首条评论复制数")
    @ExcelProperty(value = "首条评论复制数")
    private Long firstCommentCopyCount;

    @ApiModelProperty(value = "首条评论复制成本")
    @ExcelProperty(value = "首条评论复制成本")
    private BigDecimal costPerFirstCommentCopy;

    @ApiModelProperty(value = "首条评论复制率")
    @ExcelProperty(value = "首条评论复制率")
    private BigDecimal firstCommentCopyRate;

    @ApiModelProperty(value = "框下链接点击数")
    @ExcelProperty(value = "框下链接点击数")
    private Long underBoxLinkCount;

    @ApiModelProperty(value = "框下链接点击成本")
    @ExcelProperty(value = "框下链接点击成本")
    private BigDecimal costPerUnderBoxLink;

    @ApiModelProperty(value = "框下链接点击率")
    @ExcelProperty(value = "框下链接点击率")
    private BigDecimal underBoxLinkRate;

    @ApiModelProperty(value = "评论链接曝光数")
    @ExcelProperty(value = "评论链接曝光数")
    private Long commentUrlShowCount;

    @ApiModelProperty(value = "评论链接曝光率")
    @ExcelProperty(value = "评论链接曝光率")
    private BigDecimal commentUrlShowRate;

    @ApiModelProperty(value = "评论链接曝光点击率")
    @ExcelProperty(value = "评论链接曝光点击率")
    private BigDecimal commentUrlShowClickRate;

    @ApiModelProperty(value = "评论链接唤起数量")
    @ExcelProperty(value = "评论链接唤起数量")
    private Long commentCallUpSuccessCount;

    @ApiModelProperty(value = "评论链接点击数")
    @ExcelProperty(value = "评论链接点击数")
    private Long commentUrlClickCount;
    @ApiModelProperty(value = "评论链接点击率")
    @ExcelProperty(value = "评论链接点击率")
    private BigDecimal commentUrlClickRate;
    @ApiModelProperty(value = "评论链接点击成本")
    @ExcelProperty(value = "评论链接点击成本")
    private BigDecimal costPerCommentUrlClick;
    //region 销售线索
    //region 表单提交
    /**
     * 表单提交数
     */
    @ApiModelProperty(value = "表单提交数")
    @ExcelProperty(value = "表单提交数")
    private Long formSubmitCount;
    /**
     * 表单提交成本
     */
    @ApiModelProperty(value = "表单提交成本")
    @ExcelProperty(value = "表单提交成本")
    //private BigDecimal formSubmitCost;
    private BigDecimal costPerFormSubmit;
    /**
     * 表单提交率
     */
    @ApiModelProperty(value = "表单提交率")
    @ExcelProperty(value = "表单提交率")
    private BigDecimal formSubmitRate;
    //endregion
    //region 表单付费
    /**
     * 表单付费数
     */
    @ApiModelProperty(value = "表单付费数")
    @ExcelProperty(value = "表单付费数")
    private Long formUserCostCount;
    /**
     * 表单付费成本
     */
    @ApiModelProperty(value = "表单付费成本")
    @ExcelProperty(value = "表单付费成本")
    //private BigDecimal formUserCostCost;
    private BigDecimal costPerFormUserCost;
    /**
     * 表单付费率
     */
    @ApiModelProperty(value = "表单付费率")
    @ExcelProperty(value = "表单付费率")
    private BigDecimal formUserCostRate;
    //endregion
    //region 有效线索
    /**
     * 有效线索数
     */
    @ApiModelProperty(value = "有效线索数")
    @ExcelProperty(value = "有效线索数")
    //private Long clueCount;
    private Long clueValidCount;
    /**
     * 有效线索成本
     */
    @ApiModelProperty(value = "有效线索成本")
    @ExcelProperty(value = "有效线索成本")
    //private BigDecimal clueCost;
    private BigDecimal costPerClueValid;
    /**
     * 有效线索率
     */
    @ApiModelProperty(value = "有效线索率")
    @ExcelProperty(value = "有效线索率")
    //private BigDecimal clueRate;
    private BigDecimal clueValidCountRate;

    @ApiModelProperty(value = "完件数")
    @ExcelProperty(value = "完件数")
    private Long applyCount;

    @ApiModelProperty(value = "完件成本")
    @ExcelProperty(value = "完件成本")
    private BigDecimal costPerApply;

    @ApiModelProperty(value = "完件率")
    @ExcelProperty(value = "完件率")
    private BigDecimal applyRate;

    @ApiModelProperty(value = "授信数")
    @ExcelProperty(value = "授信数")
    private Long creditCount;

    @ApiModelProperty(value = "授信成本")
    @ExcelProperty(value = "授信成本")
    private BigDecimal costPerCredit;

    @ApiModelProperty(value = "授信率")
    @ExcelProperty(value = "授信率")
    private BigDecimal creditRate;

    @ApiModelProperty(value = "放款数")
    @ExcelProperty(value = "放款数")
    private Long withdrawDepositsCount;

    @ApiModelProperty(value = "放款成本")
    @ExcelProperty(value = "放款成本")
    private BigDecimal costPerWithdrawDeposits;

    @ApiModelProperty(value = "放款率")
    @ExcelProperty(value = "放款率")
    private BigDecimal withdrawDepositsRate;
    /**
     * 游戏预约数
     */
    @ApiModelProperty(value = "游戏预约数")
    @ExcelProperty(value = "游戏预约数")
    private Long gameSubscribeApiCount;
    /**
     * 游戏预约成本
     */
    @ApiModelProperty(value = "游戏预约成本")
    @ExcelProperty(value = "游戏预约成本")
    //private BigDecimal gameSubscribeApiCost;
    private BigDecimal costPerGameSubscribeApi;
    /**
     * 游戏预约率
     */
    @ApiModelProperty(value = "游戏预约率")
    @ExcelProperty(value = "游戏预约率")
    private BigDecimal gameSubscribeApiRate;
    //endregion
    //region 安卓下载
    /**
     * 安卓下载数
     */
    @ApiModelProperty(value = "安卓下载数")
    @ExcelProperty(value = "安卓下载数")
    private Long downloadSuccessCount;
    /**
     * 安卓下载成本
     */
    @ApiModelProperty(value = "安卓下载成本")
    @ExcelProperty(value = "安卓下载成本")
    private BigDecimal costPerDownloadSuccess;
    /**
     * 安卓下载率
     */
    @ApiModelProperty(value = "安卓下载率")
    @ExcelProperty(value = "安卓下载率")
    private BigDecimal downloadSuccessRate;

    //region 安卓安装
    /**
     * 安卓安装数
     */
    @ApiModelProperty(value = "安卓安装数")
    @ExcelProperty(value = "安卓安装数")
    private Long installSuccessCount;
    /**
     * 安卓安装成本
     */
    @ApiModelProperty(value = "安卓安装成本")
    @ExcelProperty(value = "安卓安装成本")
    private BigDecimal costPerInstallSuccess;
    /**
     * 安卓安装率
     */
    @ApiModelProperty(value = "安卓安装率")
    @ExcelProperty(value = "安卓安装率")
    private BigDecimal installSuccessRate;

    //region 应用接活
    /**
     * ios激活数
     */
    @ApiModelProperty(value = "ios激活数")
    @ExcelProperty(value = "ios激活数")
    //private Long iosAppFirstActiveCount;
    private Long iosAppActiveCount;
    /**
     * 安卓激活数
     */
    @ApiModelProperty(value = "安卓激活数")
    @ExcelProperty(value = "安卓激活数")
    //private Long androidAppFirstActiveCount;
    private Long androidAppActiveCount;
    /**
     * 应用激活数
     */
    @ApiModelProperty(value = "应用激活数")
    @ExcelProperty(value = "应用激活数")
    //private Long appFirstActiveCount;
    private Long appActiveCount;
    /**
     * 应用激活成本
     */
    @ApiModelProperty(value = "应用激活成本")
    @ExcelProperty(value = "应用激活成本")
    //private BigDecimal appFirstActiveCost;
    private BigDecimal costPerAppActive;
    /**
     * 应用激活率
     */
    @ApiModelProperty(value = "应用激活率")
    @ExcelProperty(value = "应用激活率")
    //private BigDecimal appFirstActiveRate;
    private BigDecimal appActiveRate;

    //region 安卓游戏中心激活
    /**
     * 安卓游戏中心激活数
     */
    @ApiModelProperty(value = "安卓游戏中心激活数")
    @ExcelProperty(value = "安卓游戏中心激活数")
    private Long gameActiveApiCount;
    /**
     * 安卓游戏中心激活成本
     */
    @ApiModelProperty(value = "安卓游戏中心激活成本")
    @ExcelProperty(value = "安卓游戏中心激活成本")
    //private BigDecimal gameActiveApiCost;
    private BigDecimal costPerGameActiveApi;
    /**
     * 安卓游戏中心激活率
     */
    @ApiModelProperty(value = "安卓游戏中心激活率")
    @ExcelProperty(value = "安卓游戏中心激活率")
    private BigDecimal gameActiveApiRate;
    /**
     * 用户注册数
     */
    @ApiModelProperty(value = "用户注册数")
    @ExcelProperty(value = "用户注册数")
    private Long userRegisterCount;
    /**
     * 用户注册成本
     */
    @ApiModelProperty(value = "用户注册成本")
    @ExcelProperty(value = "用户注册成本")
    //private BigDecimal userRegisterCost;
    private BigDecimal costPerUserRegister;
    /**
     * 用户注册率
     */
    @ApiModelProperty(value = "用户注册率")
    @ExcelProperty(value = "用户注册率")
    private BigDecimal userRegisterRate;

    /**
     * 应用内付费
     */
    @ApiModelProperty(value = "应用内付费")
    @ExcelProperty(value = "应用内付费")
    private Long userCostCount;
    /**
     * 应用内付费金额
     */
    @ApiModelProperty(value = "应用内付费金额")
    @ExcelProperty(value = "应用内付费金额")
    //private BigDecimal userCostValue;
    private BigDecimal userCostAmount;
    /**
     * 应用内首次付费数
     */
    @ApiModelProperty(value = "应用内首次付费数")
    @ExcelProperty(value = "应用内首次付费数")
    private Long userFirstCostCount;
    /**
     * 应用内首次付费金额
     */
    @ApiModelProperty(value = "应用内首次付费金额")
    @ExcelProperty(value = "应用内首次付费金额")
    //private BigDecimal userFirstCostValue;
    private BigDecimal userFirstCostAmount;
    /**
     * 应用内首次付费成本
     */
    @ApiModelProperty(value = "应用内首次付费成本")
    @ExcelProperty(value = "应用内首次付费成本")
    //private BigDecimal userFirstCostCost;
    private BigDecimal costPerUserFirstCost;
    /**
     * 应用内首次付费率
     */
    @ApiModelProperty(value = "应用内首次付费率")
    @ExcelProperty(value = "应用内首次付费率")
    private BigDecimal userFirstCostRate;

    /**
     *  安卓游戏中心应用内付费次数
     */
    @ApiModelProperty(value = "安卓游戏中心应用内付费次数")
    @ExcelProperty(value = "安卓游戏中心应用内付费次数")
    private Long gameUserCostCount;
    /**
     *  安卓游戏中心应用内首次付费次数
     */
    @ApiModelProperty(value = "安卓游戏中心应用内首次付费次数")
    @ExcelProperty(value = "安卓游戏中心应用内首次付费次数")
    private Long gameUserFirstCostCount;
    /**
     *  安卓游戏中心应用内首次付费成本
     */
    @ApiModelProperty(value = "安卓游戏中心应用内首次付费成本")
    @ExcelProperty(value = "安卓游戏中心应用内首次付费成本")
    //private BigDecimal gameUserFirstCostCost;
    private BigDecimal costPerGameUserFirstCost;
    /**
     *  安卓游戏中心应用内首次付费率
     */
    @ApiModelProperty(value = "安卓游戏中心应用内首次付费率")
    @ExcelProperty(value = "安卓游戏中心应用内首次付费率")
    private BigDecimal gameUserFirstCostRate;

    /**
     * 次日留存数
     */
    @ApiModelProperty(value = "次日留存数")
    @ExcelProperty(value = "次日留存数")
    private Long retentionCount;
    /**
     * 次日留存成本
     */
    @ApiModelProperty(value = "次日留存成本")
    @ExcelProperty(value = "次日留存成本")
    //private BigDecimal retentionCost;
    private BigDecimal costPerRetention;
    /**
     * 次日留存率
     */
    @ApiModelProperty(value = "次日留存率")
    @ExcelProperty(value = "次日留存率")
    private BigDecimal retentionRate;

    /**
     * 应用唤起数
     */
    @ApiModelProperty(value = "应用内访问数")
    @ExcelProperty(value = "应用内访问数")
    //private Long appCallupCount;
    private Long appCallUpCount;
    /**
     * 应用唤起成本
     */
    @ApiModelProperty(value = "应用内访问数成本")
    @ExcelProperty(value = "应用内访问数成本")
    //private BigDecimal appCallupCost;
    private BigDecimal costPerAppCallUp;
    /**
     * 应用唤起率
     */
    @ApiModelProperty(value = "应用内访问率")
    @ExcelProperty(value = "应用内访问率")
    //private BigDecimal appCallupRate;
    private BigDecimal appCallUpRate;


//    /**
//     * 落地页调起店铺数
//     */
//    @ApiModelProperty(value = "落地页调起店铺数")
//    @ExcelProperty(value = "落地页调起店铺数")
//    private Long lpCallupCount;
    /**
     * 落地页调起店铺成功数
     */
    @ApiModelProperty(value = "应用唤起数")
    @ExcelProperty(value = "应用唤起数")
    //private Long callupSucCount;
    private Long lpCallUpSuccessCount;
    /**
     * 落地页调起店铺成功成本
     */
    @ApiModelProperty(value = "应用唤起成本")
    @ExcelProperty(value = "应用唤起成本")
    //private BigDecimal callupSucCost;
    private BigDecimal costPerLpCallUpSuccess;
    /**
     * 落地页调起店铺成功率
     */
    @ApiModelProperty(value = "应用唤起率")
    @ExcelProperty(value = "应用唤起率")
    //private BigDecimal callupSucRate;
    private BigDecimal lpCallUpSuccessRate;

    /**
     * 店铺停留数
     */
    @ApiModelProperty(value = "店铺停留数")
    @ExcelProperty(value = "店铺停留数")
    //private Long lpCallupSuccStayCount;
    private Long lpCallUpSuccessStayCount;
    /**
     * 店铺停留成本
     */
    @ApiModelProperty(value = "店铺停留成本")
    @ExcelProperty(value = "店铺停留成本")
    //private BigDecimal lpCallupSuccStayCost;
    private BigDecimal costPerLpCallUpSuccessStay;
    /**
     * 店铺停留率
     */
    @ApiModelProperty(value = "店铺停留率")
    @ExcelProperty(value = "店铺停留率")
    //private BigDecimal lpCallupSuccStayRate;
    private BigDecimal lpCallUpSuccessStayRate;
    /**
     * 订单提交数
     */
    @ApiModelProperty(value = "订单提交数")
    @ExcelProperty(value = "订单提交数")
    private Long orderPlaceCount;
    /**
     * 订单提交金额
     */
    @ApiModelProperty(value = "订单提交金额")
    @ExcelProperty(value = "订单提交金额")
    //private BigDecimal orderPlaceValue;
    private BigDecimal orderPlaceAmount;
    /**
     * 订单提交成本
     */
    @ApiModelProperty(value = "订单提交成本")
    @ExcelProperty(value = "订单提交成本")
    //private BigDecimal orderPlaceCost;
    private BigDecimal costPerOrderPlace;
    /**
     * 订单提交率
     */
    @ApiModelProperty(value = "订单提交率")
    @ExcelProperty(value = "订单提交率")
    private BigDecimal orderPlaceRate;
    /**
     * 订单ROI
     */
    @ApiModelProperty(value = "订单ROI")
    @ExcelProperty(value = "订单ROI")
    private BigDecimal orderRoi;
    /**
     * 订单转化率
     */
    @ApiModelProperty(value = "订单转化率")
    @ExcelProperty(value = "订单转化率")
    private BigDecimal orderConversionRate;

    /**
     * 用户首次付费数
     */
    @ApiModelProperty(value = "用户首次付费数")
    @ExcelProperty(value = "用户首次付费数")
    private Long firstOrderPlaceCount;

    /**
     * 用户首次付费金额
     */
    @ApiModelProperty(value = "用户首次付费金额")
    @ExcelProperty(value = "用户首次付费金额")
    //private BigDecimal firstOrderPlaceValue;
    private BigDecimal firstOrderPlaceAmount;
    /**
     * 用户首次付费成本
     */
    @ApiModelProperty(value = "用户首次付费成本")
    @ExcelProperty(value = "用户首次付费成本")
    //private BigDecimal firstOrderPlaceCost;
    private BigDecimal costPerFirstOrderPlace;
    /**
     * 用户首次付费率
     */
    @ApiModelProperty(value = "用户首次付费率")
    @ExcelProperty(value = "用户首次付费率")
    private BigDecimal firstOrderPlaceRate;
    /**
     * 关键行为数
     */
    @ApiModelProperty(value = "关键行为数")
    @ExcelProperty(value = "关键行为数")
    private Long actionValidCount;

    /**
     * 关键行为成本
     */
    @ApiModelProperty(value = "关键行为成本")
    @ExcelProperty(value = "关键行为成本")
    //private BigDecimal actionValidCost;
    private BigDecimal costPerActionValid;
    /**
     * 关键行为率
     */
    @ApiModelProperty(value = "关键行为率")
    @ExcelProperty(value = "关键行为率")
    private BigDecimal actionValidRate;
    /**
     *
     */
    @ApiModelProperty(value = "素材中心id")
    @ExcelProperty(value = "素材中心id")
    private String materialCenterId;

    @ApiModelProperty(value = "素材url")
    @ExcelProperty(value = "素材url")
    private String materialUrl;

    @ApiModelProperty(value = "素材质量分")
    @ExcelProperty(value = "素材质量分")
    private String materialScore;

    @ApiModelProperty(value = "激活后24小时变现数")
    @ExcelProperty(value = "激活后24小时变现数")
    @JsonProperty("game_charge_in_24h_count")
    private Long gameChargeIn24HCount;

    @ApiModelProperty(value = "激活后24小时变现成本")
    @ExcelProperty(value = "激活后24小时变现成本")
    @JsonProperty("cost_per_game_charge_in_24h")
    private BigDecimal costPerGameChargeIn24H;

    @ApiModelProperty(value = "激活后24小时变现金额")
    @ExcelProperty(value = "激活后24小时变现金额")
    @JsonProperty("game_charge_in_24h_amount")
    private BigDecimal gameChargeIn24HAmount;

    @ApiModelProperty(value = "激活后24小时变现ROI")
    @ExcelProperty(value = "激活后24小时变现ROI")
    @JsonProperty("game_charge_in_24h_roi")
    private BigDecimal gameChargeIn24HRoi;

    @ApiModelProperty(value = "激活后首自然日变现数")
    @ExcelProperty(value = "激活后首自然日变现数")
    @JsonProperty("game_charge_in_1d_count")
    private Long gameChargeIn1DCount;

    @ApiModelProperty(value = "激活后首自然日变现成本")
    @ExcelProperty(value = "激活后首自然日变现成本")
    @JsonProperty("cost_per_game_charge_in_1d")
    private BigDecimal costPerGameChargeIn1D;

    @ApiModelProperty(value = "激活后首自然日变现金额")
    @ExcelProperty(value = "激活后首自然日变现金额")
    @JsonProperty("game_charge_in_1d_amount")
    private BigDecimal gameChargeIn1DAmount;

    @ApiModelProperty(value = "激活后首自然日变现ROI")
    @ExcelProperty(value = "激活后首自然日变现ROI")
    @JsonProperty("game_charge_in_1d_roi")
    private BigDecimal gameChargeIn1DRoi;

    @ApiModelProperty(value = "激活后7日变现数")
    @ExcelProperty(value = "激活后7日变现数")
    @JsonProperty("game_charge_in_7d_count")
    private Long gameChargeIn7DCount;

    @ApiModelProperty(value = "激活后7日变现成本")
    @ExcelProperty(value = "激活后7日变现成本")
    @JsonProperty("cost_per_game_charge_in_7d")
    private BigDecimal costPerGameChargeIn7D;

    @ApiModelProperty(value = "激活后7日变现金额")
    @ExcelProperty(value = "激活后7日变现金额")
    @JsonProperty("game_charge_in_7d_amount")
    private BigDecimal gameChargeIn7DAmount;

    @ApiModelProperty(value = "激活后7日变现ROI")
    @ExcelProperty(value = "激活后7日变现ROI")
    @JsonProperty("game_charge_in_7d_roi")
    private BigDecimal gameChargeIn7DRoi;

    @ApiModelProperty(value = "激活后14日变现数")
    @ExcelProperty(value = "激活后14日变现数")
    @JsonProperty("game_charge_in_14d_count")
    private Long gameChargeIn14DCount;

    @ApiModelProperty(value = "激活后14日变现成本")
    @ExcelProperty(value = "激活后14日变现成本")
    @JsonProperty("cost_per_game_charge_in_14d")
    private BigDecimal costPerGameChargeIn14D;

    @ApiModelProperty(value = "激活后14日变现金额")
    @ExcelProperty(value = "激活后14日变现金额")
    @JsonProperty("game_charge_in_14d_amount")
    private BigDecimal gameChargeIn14DAmount;

    @ApiModelProperty(value = "激活后14日变现ROI")
    @ExcelProperty(value = "激活后14日变现ROI")
    @JsonProperty("game_charge_in_14d_roi")
    private BigDecimal gameChargeIn14DRoi;

    @ApiModelProperty(value = "激活后24小时混合变现金额")
    @ExcelProperty(value = "激活后24小时混合变现金额")
    @JsonProperty("game_charge_in_24h_mix_amount")
    private BigDecimal gameChargeIn24HMixAmount;

    @ApiModelProperty(value = "激活后24小时混合变现ROI")
    @ExcelProperty(value = "激活后24小时混合变现ROI")
    @JsonProperty("game_charge_in_24h_mix_roi")
    private BigDecimal gameChargeIn24HMixRoi;

    @ApiModelProperty(value = "激活后首自然日混合变现金额")
    @ExcelProperty(value = "激活后首自然日混合变现金额")
    @JsonProperty("game_charge_in_1d_mix_amount")
    private BigDecimal gameChargeIn1DMixAmount;

    @ApiModelProperty(value = "激活后首自然日混合变现ROI")
    @ExcelProperty(value = "激活后首自然日混合变现ROI")
    @JsonProperty("game_charge_in_1d_mix_roi")
    private BigDecimal gameChargeIn1DMixRoi;

    @ApiModelProperty(value = "激活后7日混合变现金额")
    @ExcelProperty(value = "激活后7日混合变现金额")
    @JsonProperty("game_charge_in_7d_mix_amount")
    private BigDecimal gameChargeIn7DMixAmount;

    @ApiModelProperty(value = "激活后7日混合变现ROI")
    @ExcelProperty(value = "激活后7日混合变现ROI")
    @JsonProperty("game_charge_in_7d_mix_roi")
    private BigDecimal gameChargeIn7DMixRoi;

    @ApiModelProperty(value = "激活后14天混合变现金额")
    @ExcelProperty(value = "激活后14天混合变现金额")
    @JsonProperty("game_charge_in_14d_mix_amount")
    private BigDecimal gameChargeIn14DMixAmount;

    @ApiModelProperty(value = "激活后14天混合变现ROI")
    @ExcelProperty(value = "激活后14天混合变现ROI")
    @JsonProperty("game_charge_in_14d_mix_roi")
    private BigDecimal gameChargeIn14DMixRoi;
}
