package com.bilibili.adp.legacy.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Description
 * @date 2024/6/14
 **/

/**
 * 复制的代码，从web模块下沉到service模块
 *
 * @see com.bilibili.adp.advertiser.portal.webapi.effect_ad.vo.SearchOvertExportVo
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SearchOvertExportDto {

    @ExcelProperty("推荐词")
    private String keyword;

    @ExcelProperty("推荐标签")
    private String recommendTags;

    @ExcelProperty("月均搜搜量指标")
    private Long monthlySearchCount;

    @ExcelProperty("竞争激烈程度")
    private String competition;


}
