package com.bilibili.adp.legacy.dto;

import com.bilibili.adp.web.framework.annotations.ExcelSheet;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;


/**
 * 复制的代码，从web模块下沉到service模块
 *
 * @see com.bilibili.adp.advertiser.portal.webapi.statistic.vo.ExportAllCrowdAnalysisVo
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExportAllCrowdAnalysisDto {

    @ExcelSheet("年龄")
    private List<ExportAllTargetStatDto> age;

    @ExcelSheet("性别")
    private List<ExportAllTargetStatDto> gender;

    @ExcelSheet("设备")
    private List<ExportAllTargetStatDto> device;

    @ExcelSheet("省级地域")
    private List<ExportAllTargetStatDto> province;

    @ExcelSheet("市级地域")
    private List<ExportAllTargetStatDto> city;

    public static ExportAllCrowdAnalysisDto empty() {
        return ExportAllCrowdAnalysisDto.builder()
                .age(Collections.emptyList())
                .gender(Collections.emptyList())
                .device(Collections.emptyList())
                .province(Collections.emptyList())
                .city(Collections.emptyList())
                .build();
    }

}
