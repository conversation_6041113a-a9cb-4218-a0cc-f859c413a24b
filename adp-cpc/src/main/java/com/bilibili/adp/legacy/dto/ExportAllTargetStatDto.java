package com.bilibili.adp.legacy.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.bilibili.adp.web.framework.annotations.ExcelColumn;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ExportAllTargetStatDto extends AllTargetStatDto {

    @ExcelProperty("日期范围")
    @ExcelColumn("日期范围")
    private String dateRang;

    @ExcelProperty("计划ID")
    @ExcelColumn(value = "计划ID", columnWidth = 12)
    private Integer campaignId;

    @ExcelProperty("计划名称")
    @ExcelColumn("计划名称")
    private String campaignName;

    @ExcelProperty("单元ID")
    @ExcelColumn(value = "单元ID", columnWidth = 12)
    private Integer unitId;

    @ExcelProperty("单元名称")
    @ExcelColumn("单元名称")
    private String unitName;

    @ExcelProperty("创意ID")
    @ExcelColumn(value = "创意ID", columnWidth = 12)
    private Integer creativeId;
}
