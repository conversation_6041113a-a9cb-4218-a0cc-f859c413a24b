package com.bilibili.adp.legacy.creative;

import com.bapis.ad.audit.NativeBizType;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.biz.bos.report.CreativeReportBo;
import com.bilibili.adp.cpc.biz.services.creative.config.AccLabelConfig;
import com.bilibili.adp.cpc.biz.services.unit.LauUnitExtraService;
import com.bilibili.adp.cpc.core.constants.AuditStatus;
import com.bilibili.adp.cpc.core.constants.TemplateGroupV2;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauCreativeNativeArchiveRelativityPo;
import com.bilibili.adp.cpc.dao.querydsl.pos.LauNativeArchivePo;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauUnitExtraPo;
import com.bilibili.adp.cpc.enums.AdpVersion;
import com.bilibili.adp.cpc.enums.ad.PromotionPurposeType;
import com.bilibili.adp.cpc.enums.audit.SecondAuditStatus;
import com.bilibili.adp.cpc.repo.LauCreativeNativeArchiveRelativityRepo;
import com.bilibili.adp.cpc.repo.LauNativeArchiveRepo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/3/21 20:49
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class NativeCreativeStatusService {

    private final LauCreativeNativeArchiveRelativityRepo lauCreativeNativeArchiveRelativityRepo;
    private final LauNativeArchiveRepo lauNativeArchiveRepo;
    private final LauUnitExtraService lauUnitExtraService;
    private final AccLabelConfig accLabelConfig;

    public void processNativeCreativeStatus(List<CreativeReportBo> creativeDtos, List<Integer> accountLabelIdList) {

        if (CollectionUtils.isEmpty(creativeDtos)) {
            return;
        }
        List<Integer> creativeIds = creativeDtos.stream().map(t -> t.getCreativeId()).collect(Collectors.toList());
        List<Integer> unitIds = creativeDtos.stream().map(t -> t.getUnitId()).distinct().collect(Collectors.toList());
        Map<Integer, LauCreativeNativeArchiveRelativityPo> creativeNativeArchiveRelativityPoMap = lauCreativeNativeArchiveRelativityRepo.queryNativeArchiveRelativityPoMap(creativeIds);
        // 稿件，动态，直播的 objIds 可能重复，先不限制类型，然后内存过滤出
        List<Long> objIds = creativeNativeArchiveRelativityPoMap.values().stream().map(LauCreativeNativeArchiveRelativityPo::getAvid)
                .filter(Utils::isPositive)
                .distinct()
                .collect(Collectors.toList());
        List<LauNativeArchivePo> nativeArchivePos = lauNativeArchiveRepo.queryNativeArchive(objIds, NativeBizType.SANLIAN_VALUE, null);
        // group by avid+type
        Map<String, LauNativeArchivePo> nativeArchivePoMapByObjIdAndType = nativeArchivePos.stream()
                .collect(Collectors.toMap(t -> t.getAvid() + "_" + t.getType(), t -> t, (t1, t2) -> t1));

        Map<Integer, LauUnitExtraPo> unitExtraPoMap = lauUnitExtraService.getMapByUnitIds(unitIds);


        LauNativeArchivePo defaultNativeArchivePo = new LauNativeArchivePo();
        defaultNativeArchivePo.setAuditStatus(AuditStatus.REJECTED);
        for (CreativeReportBo creativeDto : creativeDtos) {
            creativeDto.setIsShowAdditionalRevenue(false);
            LauUnitExtraPo unitExtraPo = unitExtraPoMap.get(creativeDto.getUnitId());
            if (unitExtraPo != null) {
                creativeDto.setIsBiliNative(unitExtraPo.getIsBiliNative());
            }

            LauCreativeNativeArchiveRelativityPo relativityPo = creativeNativeArchiveRelativityPoMap.get(creativeDto.getCreativeId());
            if (relativityPo == null) {
                creativeDto.setNativeAuditStatusDesc("非原生");
                continue;
            }

            LauNativeArchivePo nativeArchivePo = nativeArchivePoMapByObjIdAndType.getOrDefault(relativityPo.getAvid() + "_" + relativityPo.getType(), defaultNativeArchivePo);

            if (Objects.equals(AuditStatus.PASSED, creativeDto.getAuditStatus())) {
                // 两个都通过就是通过，一个驳回就是驳回；否则就是审核中

                if (Objects.equals(AuditStatus.PASSED, relativityPo.getAuditStatus()) && Objects.equals(AuditStatus.PASSED, nativeArchivePo.getAuditStatus())) {
                    creativeDto.setNativeAuditStatusDesc("有效");
                } else if (Objects.equals(AuditStatus.REJECTED, relativityPo.getAuditStatus()) || Objects.equals(AuditStatus.REJECTED, nativeArchivePo.getAuditStatus())) {
                    creativeDto.setNativeAuditStatusDesc("审核拒绝");
                } else {
                    creativeDto.setNativeAuditStatusDesc("审核中");
                }

                //审核二级状态 在广审通过时根据原生审核状态变化
                if (Objects.equals(relativityPo.getAuditStatus(), AuditStatus.PASSED)) {
                    creativeDto.setSecondAuditStatus(SecondAuditStatus.NATIVE_PASS.getCode());
                    creativeDto.setSecondAuditStatusDesc("原生审核通过，有原生增益流量");

                    boolean newVersion = AdpVersion.isCpcFlyMerge(creativeDto.getAdpVersion());
                    boolean isLiveRoom = newVersion ?
                            Objects.equals(creativeDto.getUnitPromotionPurposeType(), PromotionPurposeType.LIVE_ROOM.getCode()) :
                            (Objects.equals(creativeDto.getUnitPromotionPurposeType(), PromotionPurposeType.LIVE_ROOM.getCode()) ||
                                    Objects.equals(creativeDto.getUnitPromotionPurposeType(), PromotionPurposeType.GOODS_LIVE.getCode()));
                    boolean isGraphic = newVersion ?
                            Objects.equals(creativeDto.getTemplateGroupId(), TemplateGroupV2.DYNAMIC) :
                            Objects.equals(creativeDto.getUnitPromotionPurposeType(), PromotionPurposeType.DYNAMIC.getCode());
                    if (isLiveRoom || isGraphic) {
                        // 图文or直播创意，且当前账号在884和803标签内
                        if (accountLabelIdList.contains(accLabelConfig.getDynamicLiveRoomNativeStatusLabelId()) &&
                                accountLabelIdList.contains(accLabelConfig.getNativeCreativeLabelId())) {
                            creativeDto.setIsShowAdditionalRevenue(true);
                        }
                    } else if (newVersion) {
                        // 新版
                        if (unitExtraPo != null && Objects.equals(unitExtraPo.getIsBiliNative(), 1)) {
                            // 原生
                            creativeDto.setIsShowAdditionalRevenue(true);
                        } else if (accountLabelIdList.contains(accLabelConfig.getSupportCpcNativeStatusLabelId()) &&
                                accountLabelIdList.contains(accLabelConfig.getNativeCreativeLabelId())) {
                            // 投放模式为【非原生投放】的稿件创意 ， 且当前账号同时在925和803标签内
                            creativeDto.setIsShowAdditionalRevenue(true);
                        }
                    } else if (accountLabelIdList.contains(accLabelConfig.getNativeCreativeLabelId())) {
                        creativeDto.setIsShowAdditionalRevenue(true);
                    }
                } else if (Objects.equals(relativityPo.getAuditStatus(), AuditStatus.AUDITING)) {
                    creativeDto.setSecondAuditStatus(SecondAuditStatus.NATIVE_AUDITING.getCode());
                    creativeDto.setSecondAuditStatusDesc("原生审核中，没有原生增益流量");
                } else if (Objects.equals(relativityPo.getAuditStatus(), AuditStatus.REJECTED)) {
                    creativeDto.setSecondAuditStatus(SecondAuditStatus.NATIVE_REJECT.getCode());
                    creativeDto.setSecondAuditStatusDesc("原生审核拒绝，没有原生增益流量");
                    //extraStatus 强依赖枚举, 不允许设置自定义值
//                    if (Objects.equals(creativeDto.getExtraStatus(), CreativeExtraStatus.RUNNING.getCode())) {
//                        creativeDto.setExtraStatusDesc("投放中(可优化)");
//                    }
                }


            } else if (Objects.equals(AuditStatus.REJECTED, creativeDto.getAuditStatus())) {
                creativeDto.setNativeAuditStatusDesc("不可投放");
            } else if (Objects.equals(AuditStatus.AUDITING, creativeDto.getAuditStatus())) {
                creativeDto.setNativeAuditStatusDesc("审核中");
            }
        }
    }
}
