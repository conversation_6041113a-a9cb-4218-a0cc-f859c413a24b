package com.bilibili.adp.legacy.converters;

import com.bilibili.adp.legacy.bo.*;
import com.bilibili.adp.legacy.dto.CampaignReportExportDto;
import com.bilibili.adp.legacy.dto.CreativeReportExportDto;
import com.bilibili.adp.legacy.dto.LaunchDataDetailExportDto;
import com.bilibili.adp.legacy.dto.UnitReportExportDto;
import com.bilibili.adp.legacy.utils.ConvertUtil;
import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;

@Mapper(builder = @Builder(disableBuilder = true))
public interface ReportExportDtoConverter {
    ReportExportDtoConverter INSTANCE = Mappers.getMapper(ReportExportDtoConverter.class);

    @Mapping(target = "dynamicDetailPageBrowseCount", source = "vo.dynamicDetailPageBrowseCount")
    @Mapping(target = "dynamicDetailPageBrowseCost", source = "vo.dynamicDetailPageBrowseCost")
    @Mapping(target = "dynamicDetailPageBrowseRate", source = "vo.dynamicDetailPageBrowseRate")
    @Mapping(target = "activityPagePullUpCount", source = "vo.activityPagePullUpCount")
    @Mapping(target = "activityPagePullUpCost", source = "vo.activityPagePullUpCost")
    @Mapping(target = "activityPagePullUpRate", source = "vo.activityPagePullUpRate")
    @Mapping(target = "paidIn24hRoi", source = "reportSummary.paidIn24hROI")
    @Mapping(target = "paidIn7dRoi", source = "reportSummary.paidIn7dROI")
    @Mapping(target = "lpCallUpSuccessStayRate", source = "reportSummary.lpCallUpSuccessRate")
    @Mapping(target = "gameChargeIn7dRoi", source = "reportSummary.gameChargeIn7DRoi")
    @Mapping(target = "gameChargeIn24hCount", source = "reportSummary.gameChargeIn24HCount")
    @Mapping(target = "gameChargeIn24hAmount", source = "reportSummary.gameChargeIn24HAmount")
    @Mapping(target = "costPerGameChargeIn24h", source = "reportSummary.costPerGameChargeIn24H")
    @Mapping(target = "gameChargeIn24hRoi", source = "reportSummary.gameChargeIn24HRoi")
    @Mapping(target = "gameChargeIn1dCount", source = "reportSummary.gameChargeIn1DCount")
    @Mapping(target = "gameChargeIn1dAmount", source = "reportSummary.gameChargeIn1DAmount")
    @Mapping(target = "costPerGameChargeIn1d", source = "reportSummary.costPerGameChargeIn1D")
    @Mapping(target = "gameChargeIn1dRoi", source = "reportSummary.gameChargeIn1DRoi")
    @Mapping(target = "gameChargeIn7dCount", source = "reportSummary.gameChargeIn7DCount")
    @Mapping(target = "gameChargeIn7dAmount", source = "reportSummary.gameChargeIn7DAmount")
    @Mapping(target = "costPerGameChargeIn7d", source = "reportSummary.costPerGameChargeIn7D")
    @Mapping(target = "callUpOrderSuccessRate", ignore = true)
    CampaignReportExportDto toCampaignExportDto(CampaignReportDetailBo vo, ReportSummaryBo reportSummary);

    default String bigDecimal2Str(BigDecimal bigDecimal) {
        return ConvertUtil.getStringOrDefault(bigDecimal);
    }

    @Mapping(target = "status", source = "vo.statusDesc")
    @Mapping(target = "slotGroup", source = "vo.slotGroupName")
    @Mapping(target = "dynamicDetailPageBrowseCount", source = "vo.dynamicDetailPageBrowseCount")
    @Mapping(target = "dynamicDetailPageBrowseCost", source = "vo.dynamicDetailPageBrowseCost")
    @Mapping(target = "dynamicDetailPageBrowseRate", source = "vo.dynamicDetailPageBrowseRate")
    @Mapping(target = "activityPagePullUpCount", source = "vo.activityPagePullUpCount")
    @Mapping(target = "activityPagePullUpCost", source = "vo.activityPagePullUpCost")
    @Mapping(target = "activityPagePullUpRate", source = "vo.activityPagePullUpRate")
    @Mapping(target = "paidIn24hRoi", source = "reportSummary.paidIn24hROI")
    @Mapping(target = "paidIn7dRoi", source = "reportSummary.paidIn7dROI")
    @Mapping(target = "gameChargeIn7dRoi", source = "reportSummary.gameChargeIn7DRoi")
    @Mapping(target = "gameChargeIn24hCount", source = "reportSummary.gameChargeIn24HCount")
    @Mapping(target = "gameChargeIn24hAmount", source = "reportSummary.gameChargeIn24HAmount")
    @Mapping(target = "costPerGameChargeIn24h", source = "reportSummary.costPerGameChargeIn24H")
    @Mapping(target = "gameChargeIn24hRoi", source = "reportSummary.gameChargeIn24HRoi")
    @Mapping(target = "gameChargeIn1dCount", source = "reportSummary.gameChargeIn1DCount")
    @Mapping(target = "gameChargeIn1dAmount", source = "reportSummary.gameChargeIn1DAmount")
    @Mapping(target = "costPerGameChargeIn1d", source = "reportSummary.costPerGameChargeIn1D")
    @Mapping(target = "gameChargeIn1dRoi", source = "reportSummary.gameChargeIn1DRoi")
    @Mapping(target = "gameChargeIn7dCount", source = "reportSummary.gameChargeIn7DCount")
    @Mapping(target = "gameChargeIn7dAmount", source = "reportSummary.gameChargeIn7DAmount")
    @Mapping(target = "costPerGameChargeIn7d", source = "reportSummary.costPerGameChargeIn7D")
    UnitReportExportDto toUnitExportDto(UnitReportDetailBo vo, ReportSummaryBo reportSummary);

    @Mapping(target = "status", source = "vo.statusDesc")
    @Mapping(target = "dynamicDetailPageBrowseCount", source = "vo.dynamicDetailPageBrowseCount")
    @Mapping(target = "dynamicDetailPageBrowseCost", source = "vo.dynamicDetailPageBrowseCost")
    @Mapping(target = "dynamicDetailPageBrowseRate", source = "vo.dynamicDetailPageBrowseRate")
    @Mapping(target = "activityPagePullUpCount", source = "vo.activityPagePullUpCount")
    @Mapping(target = "activityPagePullUpCost", source = "vo.activityPagePullUpCost")
    @Mapping(target = "activityPagePullUpRate", source = "vo.activityPagePullUpRate")
    @Mapping(target = "paidIn24hRoi", source = "reportSummary.paidIn24hROI")
    @Mapping(target = "paidIn7dRoi", source = "reportSummary.paidIn7dROI")
    @Mapping(target = "gameChargeIn7dRoi", source = "reportSummary.gameChargeIn7DRoi")
    @Mapping(target = "gameChargeIn24hCount", source = "reportSummary.gameChargeIn24HCount")
    @Mapping(target = "gameChargeIn24hAmount", source = "reportSummary.gameChargeIn24HAmount")
    @Mapping(target = "costPerGameChargeIn24h", source = "reportSummary.costPerGameChargeIn24H")
    @Mapping(target = "gameChargeIn24hRoi", source = "reportSummary.gameChargeIn24HRoi")
    @Mapping(target = "gameChargeIn1dCount", source = "reportSummary.gameChargeIn1DCount")
    @Mapping(target = "gameChargeIn1dAmount", source = "reportSummary.gameChargeIn1DAmount")
    @Mapping(target = "costPerGameChargeIn1d", source = "reportSummary.costPerGameChargeIn1D")
    @Mapping(target = "gameChargeIn1dRoi", source = "reportSummary.gameChargeIn1DRoi")
    @Mapping(target = "gameChargeIn7dCount", source = "reportSummary.gameChargeIn7DCount")
    @Mapping(target = "gameChargeIn7dAmount", source = "reportSummary.gameChargeIn7DAmount")
    @Mapping(target = "costPerGameChargeIn7d", source = "reportSummary.costPerGameChargeIn7D")
    CreativeReportExportDto toCreativeExportDto(CreativeReportDetailBo vo, ReportSummaryBo reportSummary);

    @Mapping(target = "promotionType", source = "vo.promotionPurposeType")
    @Mapping(target = "dynamicDetailPageBrowseCount", source = "vo.dynamicDetailPageBrowseCount")
    @Mapping(target = "dynamicDetailPageBrowseCost", source = "vo.dynamicDetailPageBrowseCost")
    @Mapping(target = "dynamicDetailPageBrowseRate", source = "vo.dynamicDetailPageBrowseRate")
    @Mapping(target = "activityPagePullUpCount", source = "vo.activityPagePullUpCount")
    @Mapping(target = "activityPagePullUpCost", source = "vo.activityPagePullUpCost")
    @Mapping(target = "activityPagePullUpRate", source = "vo.activityPagePullUpRate")
    @Mapping(target = "paidIn24hRoi", source = "reportSummary.paidIn24hROI")
    @Mapping(target = "paidIn7dRoi", source = "reportSummary.paidIn7dROI")
    @Mapping(target = "gameChargeIn7dRoi", source = "reportSummary.gameChargeIn7DRoi")
    @Mapping(target = "gameChargeIn24hCount", source = "reportSummary.gameChargeIn24HCount")
    @Mapping(target = "gameChargeIn24hAmount", source = "reportSummary.gameChargeIn24HAmount")
    @Mapping(target = "costPerGameChargeIn24h", source = "reportSummary.costPerGameChargeIn24H")
    @Mapping(target = "gameChargeIn24hRoi", source = "reportSummary.gameChargeIn24HRoi")
    @Mapping(target = "gameChargeIn1dCount", source = "reportSummary.gameChargeIn1DCount")
    @Mapping(target = "gameChargeIn1dAmount", source = "reportSummary.gameChargeIn1DAmount")
    @Mapping(target = "costPerGameChargeIn1d", source = "reportSummary.costPerGameChargeIn1D")
    @Mapping(target = "gameChargeIn1dRoi", source = "reportSummary.gameChargeIn1DRoi")
    @Mapping(target = "gameChargeIn7dCount", source = "reportSummary.gameChargeIn7DCount")
    @Mapping(target = "gameChargeIn7dAmount", source = "reportSummary.gameChargeIn7DAmount")
    @Mapping(target = "costPerGameChargeIn7d", source = "reportSummary.costPerGameChargeIn7D")
    LaunchDataDetailExportDto toLaunchDataDetailExportDto(ReportByTypeBo vo, ReportSummaryBo reportSummary);
}
