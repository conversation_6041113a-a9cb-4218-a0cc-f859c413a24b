package com.bilibili.adp.legacy.utils;

import org.springframework.util.StringUtils;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/9/13
 */
public class ConvertUtil {

    private ConvertUtil() {}

    public static String getStringOrDefault(BigDecimal data) {
        return data != null ? data.toString() : "--";
    }

    public static Integer getValueOrDefault(Integer data) {
        return data != null ? data : 0;
    }

    public static BigDecimal getValueOrDefault(BigDecimal data) {
        return data != null ? data : BigDecimal.ZERO;
    }

    public static String getStringOrDefault(String data, String defaultValue) {
        return !StringUtils.isEmpty(data) ? data : defaultValue;
    }
}
