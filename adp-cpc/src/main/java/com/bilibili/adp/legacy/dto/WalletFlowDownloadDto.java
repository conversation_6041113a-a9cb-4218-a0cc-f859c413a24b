package com.bilibili.adp.legacy.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * Created by fan<PERSON><PERSON> on 16/10/2.
 */

/**
 * 复制的代码，从web模块下沉到service模块
 *
 * @see com.bilibili.adp.advertiser.portal.webapi.finance.vo.WalletFlowDownloadVo
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class WalletFlowDownloadDto {
	@ExcelProperty("日期")
    private String date;
	@ExcelProperty("资金收支")
    private BigDecimal money;
	@ExcelProperty("交易类型")
    private String operate_type;
}
