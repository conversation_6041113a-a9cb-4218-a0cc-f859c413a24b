package com.bilibili.adp.legacy.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;


/**
 * 复制的代码，从web模块下沉到service模块
 *
 * @see com.bilibili.adp.advertiser.portal.webapi.statistic.vo.AllTargetStatVo
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class AllTargetStatDto {
    @ExcelIgnore
    @ApiModelProperty("定向类型")
    private String targetType;

    @ExcelProperty("维度")
    @ApiModelProperty("定向名称")
    private String targetName;

    @ExcelIgnore
    @ApiModelProperty("点击率")
    private BigDecimal clickRate;

    @ExcelIgnore
    @ApiModelProperty("曝光占比")
    private BigDecimal showPercent;

    @ExcelIgnore
    @ApiModelProperty("点击占比")
    private BigDecimal clickPercent;

    // 统计字段
    @ExcelProperty("展示量")
    private Long showCount;

    @ExcelProperty("点击量")
    private Integer clickCount;

    @ExcelProperty("总花费")
    private BigDecimal cost;

    @ExcelProperty("订单提交数")
    private Integer orderPlaceCount;

    @ExcelProperty("订单提交金额")
    private BigDecimal orderPlaceAmount;

    @ExcelProperty("应用内付费次数")
    private Integer userCostCount;

    @ExcelProperty("应用内累计付费金额")
    private BigDecimal userCostAmount;

    @ExcelProperty("游戏预约数")
    private Integer gameSubscribeApiCount;

    @ExcelProperty("涨粉数")
    private Integer fanIncreaseCount;

    @ExcelProperty("IOS激活数")
    private Integer iosAppActiveCount;

    @ExcelProperty("Android激活数")
    private Integer androidAppActiveCount;

    @ExcelProperty("应用激活数")
    private Integer appActiveCount;

    @ExcelProperty("用户注册数")
    private Integer userRegisterCount;

    @ExcelProperty("表单提交数")
    private Integer formSubmitCount;

    @ExcelProperty("安卓下载数")
    private Integer androidDownloadSuccessCount;

    @ExcelProperty("安卓安装数")
    private Integer androidInstallSuccessCount;

    @ExcelProperty("有效线索数")
    private Integer clueValidCount;

    @ExcelProperty("次日留存数")
    private Integer retentionCount;

    @ExcelProperty("应用内访问数")
    private Integer appCallUpCount;

    @ExcelIgnore
    private Integer lpCallUpCount;

    @ExcelProperty("应用唤起数")
    private Integer lpCallUpSuccessCount;

    @ExcelProperty("店铺停留数")
    private Integer lpCallUpSuccessStayCount;

    @ExcelProperty("表单付费数")
    private Integer formPaidCount;

    @ExcelProperty("安卓游戏中心激活数")
    private Integer androidGameCenterActivationCount;

    @ExcelProperty("账号关注数")
    private Integer accountSubscribeCount;

    @ExcelProperty("播放量")
    private Integer playCount;

    @ExcelProperty("框下链接点击数")
    private Integer underBoxLinkCount;

    @ExcelIgnore
    private Integer dynamicDetailPageBrowseCount;

    @ExcelProperty("评论点击数")
    private Integer commentUrlClickCount;

    @ExcelProperty("评论唤起数")
    private Integer commentCallUpSuccessCount;

    // 占比
    @ExcelIgnore
    private BigDecimal showCountPercent;
    @ExcelIgnore
    private BigDecimal clickCountPercent;
    @ExcelIgnore
    private BigDecimal costPercent;
    @ExcelIgnore
    private BigDecimal orderPlaceCountPercent;
    @ExcelIgnore
    private BigDecimal orderPlaceAmountPercent;
    @ExcelIgnore
    private BigDecimal userCostCountPercent;
    @ExcelIgnore
    private BigDecimal userCostAmountPercent;
    @ExcelIgnore
    private BigDecimal gameSubscribeApiCountPercent;
    @ExcelIgnore
    private BigDecimal fanIncreaseCountPercent;
    @ExcelIgnore
    private BigDecimal iosAppActiveCountPercent;
    @ExcelIgnore
    private BigDecimal androidAppActiveCountPercent;
    @ExcelIgnore
    private BigDecimal userRegisterCountPercent;
    @ExcelIgnore
    private BigDecimal appActiveCountPercent;
    @ExcelIgnore
    private BigDecimal formSubmitCountPercent;
    @ExcelIgnore
    private BigDecimal androidDownloadSuccessCountPercent;
    @ExcelIgnore
    private BigDecimal androidInstallSuccessCountPercent;
    @ExcelIgnore
    private BigDecimal clueValidCountPercent;
    @ExcelIgnore
    private BigDecimal retentionCountPercent;
    @ExcelIgnore
    private BigDecimal appCallUpCountPercent;
    @ExcelIgnore
    private BigDecimal lpCallUpCountPercent;
    @ExcelIgnore
    private BigDecimal lpCallUpSuccessCountPercent;
    @ExcelIgnore
    private BigDecimal lpCallUpSuccessStayCountPercent;
    @ExcelIgnore
    private BigDecimal formPaidCountPercent;
    @ExcelIgnore
    private BigDecimal androidGameCenterActivationCountPercent;
    @ExcelIgnore
    private BigDecimal accountSubscribeCountPercent;
    @ExcelIgnore
    private BigDecimal playCountPercent;
    @ExcelIgnore
    private BigDecimal underBoxLinkCountPercent;
    @ExcelIgnore
    private BigDecimal dynamicDetailPageBrowseCountPercent;
    @ExcelIgnore
    private BigDecimal commentUrlClickCountPercent;
    @ExcelIgnore
    private BigDecimal commentCallUpSuccessCountPercent;

    public static AllTargetStatDto empty(String target_type, String target_name) {
        return builder()
                .targetType(target_type)
                .targetName(target_name)
                .showCount(0L)
                .clickCount(0)
                .cost(BigDecimal.ZERO)
                .clickRate(BigDecimal.ZERO)
                .showPercent(BigDecimal.ZERO)
                .clickPercent(BigDecimal.ZERO)
                .orderPlaceCount(0)
                .orderPlaceAmount(BigDecimal.ZERO)
                .userCostCount(0)
                .userCostAmount(BigDecimal.ZERO)
                .gameSubscribeApiCount(0)
                .fanIncreaseCount(0)
                .iosAppActiveCount(0)
                .androidAppActiveCount(0)
                .appActiveCount(0)
                .userRegisterCount(0)
                .formSubmitCount(0)
                .androidDownloadSuccessCount(0)
                .androidInstallSuccessCount(0)
                .clueValidCount(0)
                .retentionCount(0)
                .appCallUpCount(0)
                .lpCallUpCount(0)
                .lpCallUpSuccessCount(0)
                .lpCallUpSuccessStayCount(0)
                .formPaidCount(0)
                .androidGameCenterActivationCount(0)
                .accountSubscribeCount(0)
                .playCount(0)
                .underBoxLinkCount(0)
                .dynamicDetailPageBrowseCount(0)
                .commentUrlClickCount(0)
                .commentCallUpSuccessCount(0)
                .showCountPercent(BigDecimal.ZERO)
                .clickCountPercent(BigDecimal.ZERO)
                .costPercent(BigDecimal.ZERO)
                .orderPlaceCountPercent(BigDecimal.ZERO)
                .orderPlaceAmountPercent(BigDecimal.ZERO)
                .userCostCountPercent(BigDecimal.ZERO)
                .userCostAmountPercent(BigDecimal.ZERO)
                .gameSubscribeApiCountPercent(BigDecimal.ZERO)
                .fanIncreaseCountPercent(BigDecimal.ZERO)
                .iosAppActiveCountPercent(BigDecimal.ZERO)
                .androidAppActiveCountPercent(BigDecimal.ZERO)
                .appActiveCountPercent(BigDecimal.ZERO)
                .userRegisterCountPercent(BigDecimal.ZERO)
                .formSubmitCountPercent(BigDecimal.ZERO)
                .androidDownloadSuccessCountPercent(BigDecimal.ZERO)
                .androidInstallSuccessCountPercent(BigDecimal.ZERO)
                .clueValidCountPercent(BigDecimal.ZERO)
                .retentionCountPercent(BigDecimal.ZERO)
                .appCallUpCountPercent(BigDecimal.ZERO)
                .lpCallUpCountPercent(BigDecimal.ZERO)
                .lpCallUpSuccessCountPercent(BigDecimal.ZERO)
                .lpCallUpSuccessStayCountPercent(BigDecimal.ZERO)
                .formPaidCountPercent(BigDecimal.ZERO)
                .androidGameCenterActivationCountPercent(BigDecimal.ZERO)
                .accountSubscribeCountPercent(BigDecimal.ZERO)
                .playCountPercent(BigDecimal.ZERO)
                .underBoxLinkCountPercent(BigDecimal.ZERO)
                .dynamicDetailPageBrowseCountPercent(BigDecimal.ZERO)
                .commentUrlClickCountPercent(BigDecimal.ZERO)
                .commentCallUpSuccessCountPercent(BigDecimal.ZERO)
                .build();
    }

}
