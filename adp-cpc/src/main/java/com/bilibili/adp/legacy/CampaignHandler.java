/** 
* <AUTHOR> 
* @date  2017年6月9日
*/

package com.bilibili.adp.legacy;

import com.bilibili.adp.launch.biz.handler.log_post_handlers.BasePostHandler;
import com.bilibili.adp.log.bean.DiffItem;
import com.bilibili.adp.log.dto.LogOperateDto;
import com.bilibili.adp.log.handler.LogOperatePostHandler;
import org.springframework.stereotype.Component;

import java.util.Iterator;
import java.util.List;
import java.util.Objects;

@Component("LegacyCampaignHandler")
public class CampaignHandler extends BasePostHandler implements LogOperatePostHandler {
	@Override
	public void handle(List<LogOperateDto> dtos) {
		
		for (LogOperateDto dto : dtos) {
			List<DiffItem> diffs = dto.getValue();
			Iterator<DiffItem> it = diffs.iterator();
			
			while (it.hasNext()) {
				DiffItem d = it.next();
				
				switch (d.getKey()) {
				case "budget":
				case "nextdayBudget":
					d.setOldValue(longStringToBigDecimalString(d.getOldValue()));
					d.setNewValue(longStringToBigDecimalString(d.getNewValue()));
					break;
				}
				if (Objects.equals(d.getOldValue(), d.getNewValue())) {
					it.remove();
				}
			}
		}
	}
}
