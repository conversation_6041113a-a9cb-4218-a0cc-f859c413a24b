package com.bilibili.adp.legacy.stat;

import com.bilibili.adp.cpc.biz.services.pic.bos.PicAnalysisReportExportDto;
import com.bilibili.adp.legacy.bo.ReportSummaryBo;
import com.bilibili.adp.legacy.dto.ArchiveAnalysisReportDetailDto;
import com.bilibili.adp.resource.api.account.label.IAccountLabelService;
import com.bilibili.report.platform.api.dto.StatLaunchDetailDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;


/**
 * @ClassName WebStatFillZeroService
 * <AUTHOR>
 * @Date 2023/8/26 12:30 上午
 * @Version 1.0
 **/
@Service
@Slf4j
public class WebStatFillZeroHelperService {

    @Value("${report.order.fill.zero.label.id:732}")
    private Integer reportOrderFillZeroLabelId;

    @Autowired
    private IAccountLabelService accountLabelService;

    public void fillOrderSubmitZeroByAccountId(List<ReportSummaryBo> summaryVoList, int accountId) {
        if (CollectionUtils.isEmpty(summaryVoList)) {
            return;
        }
        Boolean needFillZero = accountLabelService.isAccountIdInLabel(accountId, reportOrderFillZeroLabelId);
        if (!Boolean.TRUE.equals(needFillZero)) {
            return;
        }

        summaryVoList.forEach(reportSummaryVo -> {
            reportSummaryVo.setOrderSubmitAmount(BigDecimal.ZERO);
            reportSummaryVo.setOrderSubmitCount(0);
            reportSummaryVo.setOrderSubmitCost(BigDecimal.ZERO);
            reportSummaryVo.setOrderSubmitRate(BigDecimal.ZERO);
            reportSummaryVo.setGoodsRoi(BigDecimal.ZERO);
        });
    }

    public void fillPicReportOrderSubmitZeroByAccountId(List<PicAnalysisReportExportDto> voList, int accountId) {
        if (CollectionUtils.isEmpty(voList)) {
            return;
        }
        Boolean needFillZero = accountLabelService.isAccountIdInLabel(accountId, reportOrderFillZeroLabelId);
        if (!Boolean.TRUE.equals(needFillZero)) {
            return;
        }

        voList.forEach(vo -> {
            vo.setOrderSubmitAmount(BigDecimal.ZERO);
            vo.setOrderSubmitCount(0L);
            vo.setOrderSubmitCost(BigDecimal.ZERO);
            vo.setOrderSubmitRate(BigDecimal.ZERO);
            vo.setGoodsRoi(BigDecimal.ZERO);
        });
    }

    //public void fillPicReportChartOrderSubmitZeroByAccountId(List<PicAnalysisReportChartVo> voList, int accountId) {
    //    if (CollectionUtils.isEmpty(voList)) {
    //        return;
    //    }
    //    Boolean needFillZero = accountLabelService.isAccountIdInLabel(accountId, reportOrderFillZeroLabelId);
    //    if (!Boolean.TRUE.equals(needFillZero)) {
    //        return;
    //    }
    //
    //    voList.forEach(vo -> {
    //        vo.setOrderSubmitAmount(BigDecimal.ZERO);
    //        vo.setOrderSubmitCount(0L);
    //        vo.setOrderSubmitCost(BigDecimal.ZERO);
    //        vo.setOrderSubmitRate(BigDecimal.ZERO);
    //        vo.setGoodsRoi(BigDecimal.ZERO);
    //    });
    //}

    //public void fillArcReportOrderSubmitZeroByAccountId(List<ArchiveAnalysisReportDetailVo> voList, int accountId) {
    //    if (CollectionUtils.isEmpty(voList)) {
    //        return;
    //    }
    //    Boolean needFillZero = accountLabelService.isAccountIdInLabel(accountId, reportOrderFillZeroLabelId);
    //    if (!Boolean.TRUE.equals(needFillZero)) {
    //        return;
    //    }
    //
    //    voList.forEach(vo -> {
    //        //vo.setOrderPlaceValue(BigDecimal.ZERO);
    //        vo.setOrderPlaceAmount(BigDecimal.ZERO);
    //        vo.setOrderPlaceCount(0L);
    //        //vo.setOrderPlaceCost(BigDecimal.ZERO);
    //        vo.setCostPerOrderPlace(BigDecimal.ZERO);
    //        vo.setOrderPlaceRate(BigDecimal.ZERO);
    //        vo.setOrderRoi(BigDecimal.ZERO);
    //    });
    //}

    public void fillArcReportOrderExportSubmitZeroByAccountId(List<ArchiveAnalysisReportDetailDto> voList, int accountId) {
        if (CollectionUtils.isEmpty(voList)) {
            return;
        }
        Boolean needFillZero = accountLabelService.isAccountIdInLabel(accountId, reportOrderFillZeroLabelId);
        if (!Boolean.TRUE.equals(needFillZero)) {
            return;
        }

        voList.forEach(vo -> {
            //vo.setOrderPlaceValue(BigDecimal.ZERO);
            vo.setOrderPlaceAmount(BigDecimal.ZERO);
            vo.setOrderPlaceCount(0L);
            //vo.setOrderPlaceCost(BigDecimal.ZERO);
            vo.setCostPerOrderPlace(BigDecimal.ZERO);
            vo.setOrderPlaceRate(BigDecimal.ZERO);
            vo.setOrderRoi(BigDecimal.ZERO);
        });
    }

    public void fillSingleOrderSubmitZeroByAccountId(ReportSummaryBo vo, Integer accountId) {
        if (Objects.isNull(vo)) {
            return;
        }

        Boolean needFillZero = accountLabelService.isAccountIdInLabel(accountId, reportOrderFillZeroLabelId);
        if (!Boolean.TRUE.equals(needFillZero)) {
            return;
        }

        vo.setOrderSubmitAmount(BigDecimal.ZERO);
        vo.setOrderSubmitCount(0);
        vo.setOrderSubmitCost(BigDecimal.ZERO);
        vo.setOrderSubmitRate(BigDecimal.ZERO);
        vo.setGoodsRoi(BigDecimal.ZERO);
    }

    public void fillStatisticOrderSubmitZeroByAccountId(List<StatLaunchDetailDto> dtoList, Integer accountId) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return;
        }

        Boolean needFillZero = accountLabelService.isAccountIdInLabel(accountId, reportOrderFillZeroLabelId);
        if (!Boolean.TRUE.equals(needFillZero)) {
            return;
        }

        dtoList.forEach(dto -> {
            dto.setOrderAddCount(0);
            dto.setOrderAddPrice(BigDecimal.ZERO);
        });
    }
}
