package com.bilibili.adp.legacy.creative;

import com.bapis.ad.archive.ArchiveInfo;
import com.bapis.ad.archive.CmArchiveServiceGrpc;
import com.bapis.ad.archive.Conditions;
import com.bapis.ad.mgk.material.MaterialIdRegistry;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.cpc.biz.bos.creative.QueryCreativeBo;
import com.bilibili.adp.cpc.biz.bos.material.LauMaterialBo;
import com.bilibili.adp.cpc.biz.constants.Constants;
import com.bilibili.adp.cpc.biz.services.material.AdpCpcLauMaterialService;
import com.bilibili.adp.cpc.biz.services.material.MaterialCenterGrpcService;
import com.bilibili.adp.cpc.enums.MaterialType;
import com.bilibili.adp.cpc.proxy.CpmScvProxy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import pleiades.venus.starter.rpc.client.RPCClient;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/5/27
 * @description 素材注册
 */

@Service
@Slf4j
public class RefactorMaterialIdCenterRegisterService {

    @Resource
    private AdpCpcLauMaterialService cpcLauMaterialService;
    @Resource
    private MaterialCenterGrpcService materialCenterGrpcService;

    @Resource
    private CpmScvProxy cpmScvProxy;

    public void register(String md5, String name, String url, String accountId) throws ServiceException {
        try {
            if (StringUtils.isEmpty(md5)){
                log.warn("MaterialIdCenterRegisterService register err md5=null");
                return;
            }
            //拼接lau_material数据
            LauMaterialBo lauMaterialBo = LauMaterialBo.from(md5, url, MaterialType.IMAGE);
            Map<String, Long> lauMaterialMap =
                    cpcLauMaterialService.insertAndGetMapping(Collections.singletonList(lauMaterialBo));

            //插入lau_material数据，若该数据存在，则key为 type + "-" + md5
            Long materialId = lauMaterialMap.getOrDefault(md5,0L);
            if (!Utils.isPositive(materialId)) {
                materialId = lauMaterialMap.getOrDefault(MaterialType.IMAGE.getCode() + Constants.DASH + md5, 0L);
            }
            if (!Utils.isPositive(materialId)){
                throw new Exception("上传素材中心失败,数据未进入lau_material表中");
            }
            log.info("MaterialIdCenterRegisterService register md5={},name={},material_id={},url={},account={}"
            ,md5,name,materialId,url,accountId);
            materialCenterGrpcService.produceImageSnowId(md5, name, materialId.toString(), url, accountId);
        } catch (Exception e) {
            log.error("上传素材失败,md5={},name={},url={},accountId={}",md5,name,url,accountId);
            throw new ServiceException("上传素材中心失败");
        }
    }

    public List<ArchiveInfo> getCmArchiveInfos(List<String> md5s, List<Long> avids){
        return cpmScvProxy.getCmArchiveInfos(md5s,avids);

    }

    public List<String> materialCenterIdBuildQuery(String materialCenterId, QueryCreativeBo.QueryCreativeBoBuilder builder) {
        //分割，
        List<String> materialCenterIds = Arrays.stream(materialCenterId.split(",")).distinct().collect(Collectors.toList());

        //查md5
        Map<String, MaterialIdRegistry> materialRegisterMap = materialCenterGrpcService.findByMaterialIds(materialCenterIds);
        List<String> materialMd5s = materialRegisterMap.values().stream().map(MaterialIdRegistry::getMaterialUk).distinct().collect(Collectors.toList());

        List<ArchiveInfo> cmArchiveInfosList =
                cpmScvProxy.queryCmArchiveByConditions(materialMd5s);
        List<String> videoMd5s = cmArchiveInfosList.stream().map(ArchiveInfo::getMd5).distinct().collect(Collectors.toList());

        List<Long> avids = cmArchiveInfosList.stream().map(ArchiveInfo::getAvid).distinct().collect(Collectors.toList());

        //以图片投放的list
        List<String> imageMd5s = materialMd5s.stream().filter(md5 -> !videoMd5s.contains(md5)).collect(Collectors.toList());
        builder.materialMd5s(materialMd5s);
        builder.materialToImgMd5s(imageMd5s.stream().filter(StringUtils::hasText).distinct().collect(Collectors.toList()));
        builder.materialToAids(avids.stream().filter(Utils::isPositive).distinct().collect(Collectors.toList()));

        return materialMd5s;
    }
}
