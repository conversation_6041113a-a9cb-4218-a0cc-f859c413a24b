package com.bilibili.adp.legacy.bo;

import com.bilibili.adp.cpc.biz.bos.component.ComponentInfoBo;
import com.bilibili.adp.cpc.biz.services.creative.bos.programmatic.ProgrammaticInfoBo;
import com.bilibili.adp.launch.api.creative.dto.BilibiliVideoBo;
import com.bilibili.adp.v6.resource.dynamic.bos.DynamicInfoBo;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonUnwrapped;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * 复制的代码，从web模块下沉到service模块
 *
 * @see com.bilibili.adp.advertiser.portal.webapi.v2.vo.responses.CreativeReportVo
 */


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CreativeReportDetailBo {
    
    private Integer adpVersion;
    private String adpVersionDesc;

    private int campaignId;
    private String campaignName;
    private int unitId;
    private String unitName;
    private int creativeId;
    private int advertisingMode;
    private String advertisingModeDesc;
    private String pageGroupId;
    private String title;
    private int status;
    private String statusDesc;
    private List<String> imageUrls;
    private String arcCoverUrl;

    @ApiModelProperty(value = "单元预算")
    private BigDecimal budget;

    private long videoId;
    
    private int mgkVideoId;
    
    private String mgkVideoUrl;
    /**
     * 仅三连推广有值
     */
    private String mgkPageId;
    private String promotionPurposeContent;
    private String contentUrl;
    
    private String accessUrl;
    private String reason;

    private List<Integer> extraStatus;

    private List<String> extraStatusDesc;

    
    private Integer flowWeightState;

    
    private BigDecimal beforeBudget;

    
    private long budgetRemainingModifyTimes;

    private long previewTime;
    private int previewStatus;
    /**
     * 素材格式
     */

    
    private String templateName;

    private Integer templateGroupId;

    private int isHistory;

    private int isModify;
    private boolean isProgrammatic;
    private ProgrammaticInfo programmaticInfo;

    
    private List<Integer> cardTypes;

    /**
     * 创意类型 自定义创意 程序化创意
     */
    private String creativeType;
    private BilibiliVideoBo bilibiliVideo;

    //起飞创意形式
    
    private String flyCreativeStyle;
    //创意名称
    private String creativeName;
    private Integer dynamicDetailPageBrowseCount;
    private BigDecimal dynamicDetailPageBrowseCost;
    private BigDecimal dynamicDetailPageBrowseRate;
    /**
     * 创意形式
     */
    private String creativeStyle;
    /**
     * 素材描述
     */
    private String materialDesc;

    /**
     * 是否托管
     */
    
    private boolean isManaged;
    private Integer isManagedVal;

    /**
     * 是否GD+
     */

    
    private Boolean is_gd_plus;

    private String createTime;

    /**
     * 活动页浮层拉起数
     */
    private Integer activityPagePullUpCount;

    /**
     * 活动页浮层拉起成本(元)
     */
    private BigDecimal activityPagePullUpCost;
    private BigDecimal activityPagePullUpRate;

    /**
     * (仅三连系统使用)业务范畴: 0-未知; 1-必选; 2-商业起飞
     */
    
    private Integer businessDomain;

    
    private Integer subPkgStatus;

    @ApiModelProperty(value = "预算过低提醒")
    private Boolean unitBudgetLowHint;

    @ApiModelProperty(value = "素材id")
    private String materialCenterId;

    @ApiModelProperty(value = "图片md5")
    private String imageMd5;

    @ApiModelProperty(value = "创意投的avid")
    private Long materialCenterVideoId;

    @ApiModelProperty(value = "素材url")
    private String materialUrl;

    private Integer isBiliNative;

    @ApiModelProperty(value = "当前是否有aigc物料替换历史")
    private Integer hasAigcReplaceHistory;

    private String liveCoverUrl;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class ProgrammaticInfo {
        private List<String> sampleImageUrls;
        private String sampleTitle;
        private int mediaCount;
        private int titleCount;
        private int auditRejectedCount;
        private int auditWaitingCount;
        private List<String> allTitles;

        public static ProgrammaticInfo fromBo(ProgrammaticInfoBo bo) {
            final ProgrammaticInfo vo = new ProgrammaticInfo();
            BeanUtils.copyProperties(bo, vo);
            return vo;
        }
    }

    @JsonUnwrapped
    private ReportSummaryBo reportSummary;

    private List<ComponentInfoBo> components;

    private String componentsShow;

    private Integer hasAuditingShadowCreative;

    public static final String CREATIVE_TYPE_PROGRAMMIC = "程序化创意";
    public static final String CREATIVE_TYPE_CUSTOM = "自定义创意";
    private String ocpxTargetDesc;
    private String avId;
    private String bvId;
    private String creativeAvId;
    private String creativeBvId;

    private Integer unitPromotionPurposeType;

    @ApiModelProperty(value = "商品实际状态=审核通过 & 启用")
    private Integer goodsIsValid;

    @ApiModelProperty(value = "原生状态")
    private String nativeAuditStatusDesc;

    @ApiModelProperty(value = "二级审核状态")
    private String secondAuditStatus;

    @ApiModelProperty(value = "二级审核状态描述")
    private String secondAuditStatusDesc;

    @ApiModelProperty(value = "是否展示原生增益")
    private Boolean isShowAdditionalRevenue;

    @ApiModelProperty(value = "创编版本")
    private Integer generalVersion;
    @ApiModelProperty(value = "创编版本描述")
    private String generalVersionDesc;
    @ApiModelProperty(value = "是否为自动投放创意")
    private Integer supportAuto;
    @ApiModelProperty(value = "是否为自动投放创意描述")
    private String supportAutoDesc;
    @ApiModelProperty(value = "是否母创意")
    private Boolean isParentCreative;
    @ApiModelProperty(value = "有几个探索候选创意")
    private Integer creativeExploreCandidateCount;

    @ApiModelProperty("替链信息")
    private CreativeReplaceUrlBo creativeReplaceUrlInfo;

    public Optional<Long> fetchAvidOfVideoTypeCreative(){

        return Optional.ofNullable(materialCenterVideoId)
                .filter(avid -> avid > 0);

    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class CreativeReplaceUrlBo {
        // 是否替换链接
        private Integer isReplaceUrl;

        // 替链落地页类型:1落地页2微信小程序
        private Integer replaceUrlType;

        // 转化链链接(兜底链接)类型，1-三方落地页 5-高能建站落地页
        private Integer conversionUrlType;

        // 转化链链接(兜底链接)高能建站落地页page_id, 针对type = 5 回显用
        private String conversionUrlPageId;

        // 转化链连接(兜底链接)用户填写的原始落地页/落地页地址
        private String conversionUrl;

        // 微信小程序id(是否开启一跳)
        private Integer miniGameId;

        // app换端跳转链接(双端公用)
        private String appJumpUrl;
    }

    @ApiModelProperty("动态信息")
    private DynamicInfoBo dynamicInfo;

}
