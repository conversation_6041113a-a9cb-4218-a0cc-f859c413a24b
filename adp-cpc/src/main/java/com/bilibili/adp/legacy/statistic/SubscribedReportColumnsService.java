package com.bilibili.adp.legacy.statistic;

import com.bilibili.adp.account.dto.AccountBaseDto;
import com.bilibili.adp.account.service.QueryAccountService;
import com.bilibili.adp.cpc.biz.constants.*;
import com.bilibili.adp.cpc.biz.services.AdpCpcLauUserBehaviorService;
import com.bilibili.adp.cpc.biz.services.account.config.AccountConfig;
import com.bilibili.adp.cpc.biz.services.creative.config.AccLabelConfig;
import com.bilibili.adp.cpc.biz.services.misc.AdpCpcOcpxService;
import com.bilibili.adp.cpc.utils.CommonFuncs;
import com.bilibili.adp.legacy.bo.ReportColumnBo;
import com.bilibili.adp.resource.api.account.label.IAccountLabelService;
import io.swagger.annotations.ApiParam;
import io.vavr.Tuple;
import io.vavr.Tuple4;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.bilibili.adp.cpc.biz.constants.ReportColumn.*;

/**
 * <AUTHOR>
 * @date 2022/12/21 上午10:57
 */

/**
 * 复制的代码，从web模块下沉到service模块
 *
 * @see com.bilibili.adp.advertiser.portal.webapi.statistic.SubscribedReportColumnsProc
 */
@Slf4j
@Component
public class SubscribedReportColumnsService {

    public static final String PROGRAMMED_ANALYSIS = "programmed_analysis";

    @Autowired
    private QueryAccountService queryAccountService;
    @Autowired
    private AdpCpcLauUserBehaviorService adpCpcLauUserBehaviorService;
    @Autowired
    private AdpCpcOcpxService ocpxService;
    @Autowired
    private IAccountLabelService accountLabelService;
    @Autowired
    private AccountConfig accountConfig;
    @Autowired
    private AccLabelConfig accLabelConfig;

    public List<String> getSubscribedColumnList(Integer accountId, String campaign) {
        List<ReportColumnBo> reportColumnVos = this.getSubscribedReportColumnBos(campaign, accountId);

        return reportColumnVos.stream().map(t -> t.getKey()).collect(Collectors.toList());
    }

    public List<ReportColumnBo> getSubscribedReportColumnBos(@PathVariable("dim") @ApiParam(allowableValues = "campaign," +
            "unit,creative,summary,material,home,pic") String dim, Integer accountId) {
        // 获取需要排除的 columns
        final Set<Integer> excludedColumnIds = getExcludedColumnIds(accountId);
        // 获取通用的维度的自定义列 Tuple4: 字段1:自定义列的类型: 计划/单元/创意，字段2:固定列，字段3:默认列，字段4:变动列
        final Tuple4<Integer, List<ReportColumn>, List<ReportColumn>, List<ReportColumn>> tuple4 = getTuple4ByDim(dim, excludedColumnIds);
        // 获取账户的计划的自定义的列，tuple4._1: 自定列的类型: 如计划/单元
        final List<Integer> subscribedIdList = adpCpcLauUserBehaviorService.getSubscribedColumns(accountId, tuple4._1,1);

        // 该账户的自定义列
        final List<ReportColumn> subscribedList;

        // 账户如果没有自定义列，则用默认列，tuple4._3: 默认列
        if (Objects.isNull(subscribedIdList)) {
            subscribedList = tuple4._3;
        }
        // 账户存在自定义列
        else {
            subscribedList = subscribedIdList.stream().flatMap(x -> {
                final ReportColumn rc = ReportColumn.codeDict.get(x);
                if (rc == null) {
                    return Stream.empty();
                }
                return Stream.of(rc);
            }).collect(Collectors.toList());
        }

        // 自定义列合并固定列(tuple4._2)
        List<ReportColumnBo> mergedList = CommonFuncs.mergeIntoList(tuple4._2, subscribedList)
                .stream()
                .map(x -> ReportColumnBo.fromReportColumn(x, null, true, true))
                .collect(Collectors.toList());

        // 去重
        Set<Integer> reportColumnCode = new HashSet<>();
        Iterator<ReportColumnBo> iterator = mergedList.iterator();
        while (iterator.hasNext()) {
            ReportColumnBo next = iterator.next();
            if (reportColumnCode.contains(next.getCode())) {
                iterator.remove();
            } else {
                reportColumnCode.add(next.getCode());
            }
        }
        return mergedList;
    }

    public Set<Integer> getExcludedColumnIds(Integer aid) {
        final AccountBaseDto accountBaseDto = queryAccountService.getAccountBaseDtoByIdFromCache(aid);
        final Set<Integer> excludedColumnIds = new HashSet<>();
        // 仅外广非dpa开放oCPX赔付状态
        if (Objects.equals(accountBaseDto.getIsInner(), Constants.INT_TRUE) || Objects.equals(accountBaseDto.getIsSupportDpa(), Constants.INT_TRUE)) {
            excludedColumnIds.add(OCPX_AUTO_PAY_FINAL_STATUS.getCode());
        }
//        // 非深度转化自动出价或双出价白名单客户过滤深度转化字段
//        if (!ocpxService.supportDualBidAuto(aid) && !ocpxService.supportDualBidCustom(aid)) {
//            excludedColumnIds.add(OCPX_TARGET_TWO_BID.getCode());
//            excludedColumnIds.add(OCPX_TARGET_TWO_STATUS.getCode());
//        }
        // 不在账号标签内，排除安卓游戏中心应用内首次付费金额，安卓游戏中心应用内累计付费金额字段
        List<Integer> labelIds = accountLabelService.getLabelIdsByAccountId(aid);
        if(labelIds.contains(accLabelConfig.getAndroidGameCenterDataBlackListLabelId())){
            excludedColumnIds.add(ANDROID_GAME_CENTER_FIRST_PAYMENT_IN_APP_AMOUNT.getCode());
            excludedColumnIds.add(ANDROID_GAME_CENTER_PAYMENT_IN_APP_AMOUNT.getCode());
            excludedColumnIds.add(ANDROID_GAME_ACTIVE_PAID_IN_24H_COUNT.getCode());
            excludedColumnIds.add(ANDROID_GAME_ACTIVE_PAID_IN_24H_COST.getCode());
            excludedColumnIds.add(ANDROID_GAME_ACTIVE_PAID_IN_24H_AMOUNT.getCode());
            excludedColumnIds.add(ANDROID_GAME_ACTIVE_PAID_IN_24H_ROI.getCode());
            excludedColumnIds.add(ANDROID_GAME_ACTIVE_PAID_IN_7D_COUNT.getCode());
            excludedColumnIds.add(ANDROID_GAME_ACTIVE_PAID_IN_7D_COST.getCode());
            excludedColumnIds.add(ANDROID_GAME_ACTIVE_PAID_IN_7D_AMOUNT.getCode());
            excludedColumnIds.add(ANDROID_GAME_ACTIVE_PAID_IN_7D_ROI.getCode());
            excludedColumnIds.add(ANDROID_GAME_ACTIVE_PAID_IN_1D_COUNT.getCode());
            excludedColumnIds.add(ANDROID_GAME_ACTIVE_PAID_IN_1D_COST.getCode());
            excludedColumnIds.add(ANDROID_GAME_ACTIVE_PAID_IN_1D_AMOUNT.getCode());
            excludedColumnIds.add(ANDROID_GAME_ACTIVE_PAID_IN_1D_ROI.getCode());
            excludedColumnIds.add(ANDROID_GAME_ACTIVE_PAID_IN_1D_RATE.getCode());
        }

        // 直播唤起指标
        if(!labelIds.contains(accLabelConfig.getFlyLiveCallUpReportColumns())){
            excludedColumnIds.add(LIVE_CALL_UP.getCode());
            excludedColumnIds.add(LIVE_CALL_UP_COUNT.getCode());
            excludedColumnIds.add(LIVE_CALL_UP_COST.getCode());
            excludedColumnIds.add(LIVE_CALL_UP_RATE.getCode());
        }
        if(!labelIds.contains(accLabelConfig.getFlyFirstWithdrawReportColumns())){
        // 去除首日放款数指标
            excludedColumnIds.add(FIRST_WITHDRAW_COUNT.getCode());
            excludedColumnIds.add(FIRST_WITHDRAW_RATE.getCode());
            excludedColumnIds.add(FIRST_WITHDRAW_COST.getCode());
        }
//        if(!labelIds.contains(accLabelConfig.getLiveGameCardColums())){
//            excludedColumnIds.add(LIVE_GAME_CARD.getCode());
//            excludedColumnIds.add(LIVE_GAME_CARD_SHOW_COUNT.getCode());
//            excludedColumnIds.add(LIVE_GAME_CARD_CLICK_COUNT.getCode());
//            excludedColumnIds.add(LIVE_GAME_CARD_CLICK_RATE.getCode());
//            excludedColumnIds.add(LIVE_GAME_CARD_CLICK_COST.getCode());
//        }
//        if(!labelIds.contains(accLabelConfig.getNativeCreativeLabelId())){
//            excludedColumnIds.add(NATIVE_AUDIT_STATUS_DESC.getCode());
//        }

        if (!accountConfig.supportSanlianMergeAdp6(labelIds)) {
            excludedColumnIds.add(CREATIVE_AV_ID.getCode());
            excludedColumnIds.add(CREATIVE_BV_ID.getCode());
            excludedColumnIds.add(PROMOTION_CONTENT_TYPE_DESC.getCode());
        }
        log.info("excludedColumnIds = {}", excludedColumnIds);
        return excludedColumnIds;
    }

    public Tuple4<Integer, List<ReportColumn>, List<ReportColumn>, List<ReportColumn>> getTuple4ByDim(@NonNull String dim, Set<Integer> excludedReportColumnCodes) {
        final int bid;
        List<ReportColumn> requiredColumns;
        List<ReportColumn> defaultColumns;
        List<ReportColumn> variableColumns;
        switch (dim) {
            case "campaign":
                bid = Constants.SUBSCRIBED_CAMPAIGN_REPORT_COLUMNS;
                requiredColumns = CampaignReportColumns.REQUIRED_LIST;
                defaultColumns = CampaignReportColumns.DEFAULT_LIST;
                variableColumns = CampaignReportColumns.MIDDLE_VARIABLE_LIST;
                break;
            case "unit":
                bid = Constants.SUBSCRIBED_UNIT_REPORT_COLUMNS;
                requiredColumns = UnitReportColumns.REQUIRED_LIST;
                defaultColumns = UnitReportColumns.DEFAULT_LIST;
                variableColumns = UnitReportColumns.MIDDLE_VARIABLE_LIST;
                break;
            case "creative":
                bid = Constants.SUBSCRIBED_CREATIVE_REPORT_COLUMNS;
                requiredColumns = CreativeReportColumns.REQUIRED_LIST;
                defaultColumns = CreativeReportColumns.DEFAULT_LIST;
                variableColumns = CreativeReportColumns.MIDDLE_VARIABLE_LIST;
                break;
            case "summary":
                bid = Constants.SUBSCRIBED_SUMMARY_REPORT_COLUMNS;
                requiredColumns = SummaryReportColumns.REQUIRED_LIST;
                defaultColumns = Collections.emptyList();
                variableColumns = SummaryReportColumns.MIDDLE_VARIABLE_LIST;
                break;
            case "material":
                bid = Constants.SUBSCRIBED_MATERIAL_REPORT_COLUMNS;
                requiredColumns = MaterialReportColumns.REQUIRED_LIST;
                defaultColumns = Collections.emptyList();
                variableColumns = MaterialReportColumns.MIDDLE_VARIABLE_LIST;
                break;
            case "home" :
                bid = Constants.SUBSCRIBED_HOME_REPORT_COLUMNS;
                requiredColumns = HomeReportColumns.REQUIRED_LIST;
                defaultColumns = HomeReportColumns.DEFAULT_LIST;
                variableColumns = HomeReportColumns.MIDDLE_VARIABLE_LIST;
                break;
            case "order":
                bid = Constants.SUBSCRIBED_ORDER_REPORT_COLUMNS;
                requiredColumns = OrderReportColumns.REQUIRED_LIST;
                defaultColumns = OrderReportColumns.DEFAULT_LIST;
                variableColumns = OrderReportColumns.MIDDLE_VARIABLE_LIST;
                break;
            case "page_group":
                bid = Constants.SUBSCRIBED_PIC_REPORT_COLUMNS;
                requiredColumns = PageGroupReportColumns.REQUIRED_LIST;
                defaultColumns = PageGroupReportColumns.DEFAULT_LIST;
                variableColumns = PageGroupReportColumns.VARIABLE_LIST;
                break;
            case "pic":
                bid = Constants.SUBSCRIBED_PIC_REPORT_COLUMNS;
                requiredColumns = PicReportColumns.REQUIRED_LIST;
                defaultColumns = PicReportColumns.DEFAULT_LIST;
                variableColumns = PicReportColumns.VARIABLE_LIST;
                variableColumns.remove(FIRST_WITHDRAW_COUNT);
                variableColumns.remove(FIRST_WITHDRAW_COST);
                variableColumns.remove(FIRST_WITHDRAW_RATE);
                break;
            case PROGRAMMED_ANALYSIS:
                bid = Constants.SUBSCRIBED_PROGRAMED_CREATIVE_ANALYSIS_REPORT_COLUMNS;
                requiredColumns = ProgramedCreativeAnalysisReportColumns.REQUIRED_LIST;
                defaultColumns = ProgramedCreativeAnalysisReportColumns.DEFAULT_LIST;
                variableColumns = ProgramedCreativeAnalysisReportColumns.VARIABLE_LIST;
                variableColumns.remove(FIRST_WITHDRAW_COUNT);
                variableColumns.remove(FIRST_WITHDRAW_COST);
                variableColumns.remove(FIRST_WITHDRAW_RATE);
                break;
            default:
                throw new RuntimeException(String.format("期望dim=[campaign|unit|creative|summary|pic], 实际dim=[%s]", dim));
        }
        // 过滤不显示的列
        if (!CollectionUtils.isEmpty(excludedReportColumnCodes)) {
            requiredColumns = requiredColumns.stream()
                    .filter(x -> !excludedReportColumnCodes.contains(x.getCode()))
                    .collect(Collectors.toList());
            defaultColumns = defaultColumns.stream()
                    .filter(x -> !excludedReportColumnCodes.contains(x.getCode()))
                    .collect(Collectors.toList());
            variableColumns = variableColumns.stream()
                    .filter(x -> !excludedReportColumnCodes.contains(x.getCode()))
                    .collect(Collectors.toList());
        }
        return Tuple.of(bid, requiredColumns, defaultColumns, variableColumns);
    }


}
