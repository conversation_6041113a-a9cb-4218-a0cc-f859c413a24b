package com.bilibili.adp.legacy.goods;

import com.bapis.ad.cmc.cidgoods.GoodsDetail;
import com.bilibili.adp.cpc.biz.services.goods.GoodsManagerService;
import com.bilibili.adp.cpc.biz.services.goods.GoodsRealStatusJudger;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauUnitGoodsPo;
import com.bilibili.adp.cpc.enums.ad.PromotionPurposeType;
import com.bilibili.adp.cpc.repo.OuterLauUnitRepo;
import com.bilibili.adp.cpc.repo.UnitGoodsRepo;
import com.bilibili.adp.legacy.bo.CreativeReportDetailBo;
import com.bilibili.adp.legacy.bo.UnitReportDetailBo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 商品真实状态查询处理器
 *
 * <AUTHOR>
 * @date 2023/9/20 16:53
 */

/**
 * 复制的代码，从web模块下沉到service模块
 *
 * @see com.bilibili.adp.advertiser.portal.service.goods.GoodsRealStatusProc
 */
@RequiredArgsConstructor
@Component
public class GoodsRealStatusService {

    
    private final GoodsManagerService goodsManagerService;
    private final UnitGoodsRepo unitGoodsRepo;
    private final OuterLauUnitRepo outerLauUnitRepo;

    public void procUnitGoodsStatus(List<UnitReportDetailBo> unitReportVos, Integer accountId) {

        // 商品投放商品状态
        List<Integer> goodsUnitIds = unitReportVos.stream().filter(t -> Objects.equals(PromotionPurposeType.GOODS.getCode(),
                t.getPromotionPurposeType())).map(t -> t.getUnitId()).collect(Collectors.toList());
        List<LauUnitGoodsPo> unitGoodsPos = unitGoodsRepo.queryListByUnitIds(goodsUnitIds);

        List<Long> itemIds = unitGoodsPos.stream().map(t -> t.getItemId()).distinct().collect(Collectors.toList());
        Map<Integer, Long> unitItemIdMap = unitGoodsPos.stream().collect(Collectors.toMap(t -> t.getUnitId(), t -> t.getItemId(), (t1, t2) -> t2));
        Map<Long, GoodsDetail> goodsDetailMap = goodsManagerService.queryMapByItemIds(itemIds, accountId);

        unitReportVos.stream().forEach(unitReportVo -> {
            if (!Objects.equals(PromotionPurposeType.GOODS.getCode(), unitReportVo.getPromotionPurposeType())) {
                return;
            }
            Long itemId = unitItemIdMap.getOrDefault(unitReportVo.getUnitId(), 0L);
            GoodsDetail goodsDetail = goodsDetailMap.get(itemId);
            if (goodsDetail != null) {
                // 商品实际状态
                Integer goodsIsValid = GoodsRealStatusJudger.judgeGoodsRealStatus(goodsDetail.getAuditStatus(),
                        goodsDetail.getOnShelves());
                unitReportVo.setGoodsIsValid(goodsIsValid);
            } else {
                unitReportVo.setGoodsIsValid(0);
            }
        });
    }

    public void procCreativeGoodsStatus(List<CreativeReportDetailBo> vos, Integer accountId) {
        List<Integer> goodsUnitIds = vos.stream().filter(t -> Objects.equals(PromotionPurposeType.GOODS.getCode(), t.getUnitPromotionPurposeType()))
                .map(CreativeReportDetailBo::getUnitId)
                .collect(Collectors.toList());
        List<LauUnitGoodsPo> unitGoodsPos = unitGoodsRepo.queryListByUnitIds(goodsUnitIds);

        List<Long> itemIds = unitGoodsPos.stream().map(t -> t.getItemId()).distinct().collect(Collectors.toList());
        Map<Integer, Long> unitItemIdMap = unitGoodsPos.stream().collect(Collectors.toMap(t -> t.getUnitId(), t -> t.getItemId(), (t1, t2) -> t2));
        Map<Long, GoodsDetail> goodsDetailMap = goodsManagerService.queryMapByItemIds(itemIds, accountId);

        vos.stream().forEach(vo -> {
            if (!Objects.equals(PromotionPurposeType.GOODS.getCode(), vo.getUnitPromotionPurposeType())) {
                return;
            }

            Long itemId = unitItemIdMap.getOrDefault(vo.getUnitId(), 0L);
            GoodsDetail goodsDetail = goodsDetailMap.get(itemId);
            if (goodsDetail != null) {
                // 商品实际状态
                Integer goodsIsValid = GoodsRealStatusJudger.judgeGoodsRealStatus(goodsDetail.getAuditStatus(),
                        goodsDetail.getOnShelves());
                vo.setGoodsIsValid(goodsIsValid);
            } else {
                vo.setGoodsIsValid(0);
            }
        });
    }

}
