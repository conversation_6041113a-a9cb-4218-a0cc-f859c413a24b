package com.bilibili.adp.legacy.bo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ReportSummaryBo {
    /**
     * 次日预算是否开启重复
     * 此处骚操作，放在上层前端拿不到。。。
     */
    private Integer isRepeat;
    private Long showCount;
    private Integer clickCount;
    private BigDecimal clickRate;

    private Long chargedCostMilli;

    private BigDecimal averageCostPerThousand;
    private BigDecimal costPerClick;
    private BigDecimal cost;

    private Integer orderSubmitCount;

    /**
     * 订单提交金额
     */
    private BigDecimal orderSubmitAmount;
    private BigDecimal orderSubmitCost;
    private BigDecimal orderSubmitRate;

    private Integer firstOrderPlaceCount;
    private BigDecimal firstOrderPlaceAmount;
    private BigDecimal firstOrderPlaceRate;
    private BigDecimal firstOrderPlaceCost;

    /**
     * 首日付费相关 改成当日付费相关
     * https://www.tapd.bilibili.co/67874887/prong/stories/view/1167874887002966600
     */
    private Integer firstDayPayCount;
    private BigDecimal firstDayPayAmount;
    private BigDecimal firstDayPayRate;
    private BigDecimal firstDayPayCost;
    private BigDecimal firstDayPayROI;

    /**
     * 首日付费相关 改成当日付费相关
     * https://www.tapd.bilibili.co/67874887/prong/stories/view/1167874887002966600
     */
    private Integer newFirstDayPayCount;
    private BigDecimal newFirstDayPayAmount;
    private BigDecimal newFirstDayPayRate;
    private BigDecimal newFirstDayPayCost;
    private BigDecimal newFirstDayPayRoi;

    /**
     * 应用内付费次数
     */
    private Integer orderPayCount;
    /**
     * 应用内累计付费金额
     */
    private BigDecimal orderPayAmount;
    /**
     * 应用内首次付费次数
     */
    private Integer orderFirstPayCount;
    /**
     * 应用内首次付费金额
     */
    private BigDecimal orderFirstPayAmount;
    /**
     * 应用内首次付费消耗 = cost(总花费) / orderFirstPayCount(应用内首次付费次数)
     */
    private BigDecimal orderFirstPayCost;
    /**
     * 应用内首次付费比例 = orderFirstPayCount(应用内首次付费数) / clickCount(点击数)
     */
    private BigDecimal orderFirstPayRate;
    /**
     * 安卓游戏中心 应用内付费数
     */
    private Integer androidGameCenterPaymentInAppCount;
    /**
     * 安卓游戏中心 应用内付费金额
     */
    private BigDecimal androidGameCenterPaymentInAppAmount;
    /**
     * 安卓游戏中心 应用内首次付费数
     */
    private Integer androidGameCenterFirstPaymentInAppCount;
    /**
     * 安卓游戏中心 应用内首次付费金额
     */
    private BigDecimal androidGameCenterFirstPaymentInAppAmount;
    /**
     * 安卓游戏中心 应用内首次付费成本 【安卓游戏中心应用内首次付费成本】=总花费/【安卓游戏中心应用内首次付费数】
     */
    private BigDecimal androidGameCenterFirstPaymentInAppCost;
    /**
     * 安卓游戏中心 应用内首次付费率 【安卓游戏中心应用内首次付费率】=【安卓游戏中心应用内首次付费数】/【安卓游戏中心应用激活数】
     */
    private BigDecimal androidGameCenterFirstPaymentInAppRate;

    private Integer androidGameActivePaidIn24hCount;
    private BigDecimal androidGameActivePaidIn24hAmount;
    private BigDecimal androidGameActivePaidIn24hCost;
    private BigDecimal androidGameActivePaidIn24hRoi;

    private Integer androidGameActivePaidIn7dCount;
    private BigDecimal androidGameActivePaidIn7dAmount;
    private BigDecimal androidGameActivePaidIn7dCost;
    private BigDecimal androidGameActivePaidIn7dRoi;

    private Integer androidGameActivePaidIn1dCount;
    private BigDecimal androidGameActivePaidIn1dAmount;
    private BigDecimal androidGameActivePaidIn1dCost;
    private BigDecimal androidGameActivePaidIn1dRoi;
    private BigDecimal androidGameActivePaidIn1dRate;
    /**
     * 关键行为数
     */
    private Integer keyBehaviorCount;
    /**
     * 关键行为成本 【关键行为成本】=总花费/【关键行为数】
     */
    private BigDecimal keyBehaviorCost;
    /**
     * 关键行为率 【关键行为率】=【关键行为数】/【应用激活】
     */
    private BigDecimal keyBehaviorRate;

    private Integer registerCount;
    private BigDecimal registerAverageCost;
    private BigDecimal registerRate;

    /**
     * 订单转化率
     */
    private BigDecimal goodsConversionRate;

    /**
     * 【唤起成单率】=【订单提交数】/【应用唤起数】
     */
    private BigDecimal callUpOrderSuccessRate;

    /**
     * 订单ROI =  订单提交金额 / 总花费
     */
    private BigDecimal goodsRoi;

    private Integer gameActivateCount;
    private BigDecimal gameActivateAverageCost;
    private BigDecimal gameActivateRate;

    private Integer gameReserveCount;
    private BigDecimal gameReserveAverageCost;
    private BigDecimal gameReserveRate;

    private Integer fansIncreaseCount;
    private BigDecimal fansIncreaseAverageCost;
    private BigDecimal fansIncreaseRate;
    private BigDecimal play2FansRate;

    private Integer iosActivateCount;
    private Integer androidActivateCount;
    private Integer activateCount;
    private BigDecimal appActivateAverageCost;
    private BigDecimal appActivateRate;

    private Integer formSubmitCount;
    private BigDecimal formSubmitAverageCost;
    private BigDecimal formSubmitRate;

    private Integer androidDownloadCount;
    private BigDecimal androidDownloadAverageCost;
    private BigDecimal androidDownloadRate;

    private Integer androidInstallCount;
    private BigDecimal androidInstallAverageCost;
    private BigDecimal androidInstallRate;

    private Integer validClueCount;
    private BigDecimal validClueAverageCost;
    private BigDecimal validClueRate;

    private Integer retentionCount;
    private BigDecimal retentionCost;
    private BigDecimal retentionRate;

    private Integer appCallupCount;
    private BigDecimal appCallupCost;
    private BigDecimal appCallupRate;

    private Integer lpCallupCount;
    private BigDecimal lpCallupCost;
    private BigDecimal lpCallupRate;

    private Integer androidGameCenterActivationCount;
    private BigDecimal androidGameCenterActivationCost;
    private BigDecimal androidGameCenterActivationRate;

    private Integer formPaidCount;
    private BigDecimal formPaidCost;
    private BigDecimal formPaidRate;

    // 下载没有
    private Integer lpCallUpSuccessCount;
    private BigDecimal lpCallUpSuccessCost;
    private BigDecimal lpCallUpSuccessRate;

    private Integer lpCallUpSuccessStayCount;
    private BigDecimal lpCallUpSuccessStayCost;
    private BigDecimal lpCallUpSuccessStayRate;

    private Integer accountSubscribeCount;
    private BigDecimal accountSubscribeCost;
    private BigDecimal accountSubscribeRate;

    private Integer dynamicDetailPageBrowseCount;
    private BigDecimal dynamicDetailPageBrowseCost;

    private BigDecimal playCost;
    private Integer playCount;
    private BigDecimal costPerPlayCount;
    private Integer playShowCount;
    private BigDecimal playRate;

    private Integer underBoxLinkClickCount;
    private BigDecimal costPerUnderBoxLinkClickCount;
    private BigDecimal underBoxLinkClickRate;

    private Integer firstCommentCopyCount;
    private BigDecimal costPerFirstCommentCopyCount;
    private BigDecimal firstCommentCopyRate;

    /**
     * 评论链接点击数
     */
    private Integer commentClick;
    /**
     * 评论链接点击成本（消耗/评论链接点击数)
     */
    private BigDecimal commentClickCost;

    /**
     * 评论链接点击率（评论链接点击数/播放数）
     */
    private BigDecimal commentClickRate;

    /**
     * 微信复制数
     */
    private Integer wxCopyCount;
    /**
     * 【微信复制成本】=总花费/【微信复制数】
     */
    private BigDecimal wxCopyCost;
    /**
     * 【微信复制率】=【微信复制数】/点击量
     */
    private BigDecimal wxCopyRate;
    /**
     * 完件数
     */
    private Integer applyCount;
    /**
     * 【完件成本】= 总花费 /【完件数】
     */
    private BigDecimal applyCost;
    /**
     * 【完件率】=【完件数】/ 点击量
     */
    private BigDecimal applyRate;

    /**
     * 授信数
     */
    private Integer creditCount;
    /**
     * 【授信成本】= 总花费 /【授信数】
     */
    private BigDecimal creditCost;
    /**
     * 【授信率】=【授信数】/ 点击量
     */
    private BigDecimal creditRate;

    /**
     * 放款数
     */
    private Integer withdrawDepositsCount;
    /**
     * 【放款成本】= 总花费 /【放款数】
     */
    private BigDecimal withdrawDepositsCost;
    /**
     * 【放款率】=【放款数】/ 点击量
     */
    private BigDecimal withdrawDepositsRate;

    private Integer videoLikeCount;
    private BigDecimal videoLikeRate;
    private BigDecimal costPerVideoLike;
    private Integer videoFavCount;
    private BigDecimal videoFavRate;
    private BigDecimal costPerVideoFav;
    private Integer videoShareCount;
    private BigDecimal videoShareRate;
    private BigDecimal costPerVideoShare;
    private Integer videoBulletCount;
    private BigDecimal videoBulletRate;
    private BigDecimal costPerVideoBullet;
    private Integer videoReplyCount;
    private BigDecimal videoReplyRate;
    private BigDecimal costPerVideoReply;
    private Integer videoCoinCount;
    private BigDecimal videoCoinRate;
    private BigDecimal costPerVideoCoin;
    private Integer videoInteractCount;
    private BigDecimal videoInteractRate;
    private BigDecimal costPerVideoInteract;

    /**
     * 24h付费数
     */
    private Integer paidIn24hCount;

    /**
     * 24h付费金额
     */
    private BigDecimal paidIn24hPrice;

    /**
     * 24h付费成本
     */
    private BigDecimal paidIn24hCost;

    /**
     * 24h付ROI
     */
    private BigDecimal paidIn24hROI;

    /**
     * 7d付费数
     */
    private Integer paidIn7dCount;

    /**
     * 7d付费金额
     */
    private BigDecimal paidIn7dPrice;

    /**
     * 7d付费成本
     */
    private BigDecimal paidIn7dCost;

    /**
     * 7d付ROI
     */
    private BigDecimal paidIn7dROI;


    @ApiModelProperty(notes = "ocpx转化-销售线索搜集-微信加粉数量")
    private Integer wxAddFansCount;

    @ApiModelProperty(notes = "ocpx转化-销售线索搜集-微信加粉率")
    private BigDecimal wxAddFansRate;

    @ApiModelProperty(notes = "ocpx转化-销售线索搜集-微信加粉成本")
    private BigDecimal wxAddFansCost;

    @ApiModelProperty(notes = "评论链接曝光数")
    private Integer commentShowCount;

    @ApiModelProperty(notes = "评论链接曝光率")
    private BigDecimal commentShowRate;

    @ApiModelProperty(notes = "评论链接曝光点击率")
    private BigDecimal commentShowClickRate;

    @ApiModelProperty(notes = "评论链接唤起数量")
    private Integer commentCallUpCount;

    @ApiModelProperty(notes = "评论唤起率")
    private BigDecimal commentCallUpRate;

    @ApiModelProperty(notes = "播放唤起率")
    private BigDecimal playCallUpRate;

    /**
     * 后来回填的，排序用
     */
    private BigDecimal dynamicDetailPageBrowseRate;
    // 活动拉起次数
    private Integer activityPagePullUpCount;
    // 活动拉起成本
    private BigDecimal activityPagePullUpCost;
    private BigDecimal activityPagePullUpRate;

    /**
     * 直播间商品点击次数
     */
    private Integer liveEntryCount;
    private BigDecimal liveEntryCost;
    private BigDecimal liveEntryRate;

    /**
     * 直播间进场人数
     */
    private Integer liveCallUpCount;
    private BigDecimal liveCallUpCost;
    private BigDecimal liveCallUpRate;

    /**
     * 直播间预约数
     */
    private Integer liveReserveCount;
    private BigDecimal liveReserveCost;
    private BigDecimal liveReserveRate;

    private Integer firstWithdrawCount;
    private BigDecimal firstWithdrawCost;
    private BigDecimal firstWithdrawRate;
    /**
     * 进店UV
     */
    private Long uvNum;
    /**
     * 新访客UV
     */
    private Long newVisitorUvNum;
    /**
     * 成交UV
     */
    private Long payBuyerUv;
    /**
     * 收藏UV
     */
    private Long cltUvNum;

    /**
     * 手淘搜索进店UV
     */
    private Long seLeadConv;
    /**
     * 加购UV
     */
    private Long addUvNum;

    private Integer componentClickCount;
    private BigDecimal componentClickRate;
    private BigDecimal componentClickCost;


    private Integer liveGameCardShowCount;
    private Integer liveGameCardClickCount;
    private BigDecimal liveGameCardClickRate;
    private BigDecimal liveGameCardClickCost;

    private Integer liveBottomIconShow;
    private Integer liveBottomIconClick;
    private Integer liveNativeCardShow;
    private Integer liveNativeCardClick;


    private Integer dynamicDetailBrowseCount;
    private Integer dynamicGoodsUrlShowCount;
    private Integer dynamicGoodsUrlClickCount;

    /**
     * 智能创意花费（元）
     */
    private BigDecimal intelligentCost;
    /**
     * 智能创意花费占比
     */
    private BigDecimal intelligentCostRate;

    private Long cardOpenCount;
    private BigDecimal cardOpenRate;
    private BigDecimal costPerCardOpen;

    private BigDecimal notAutoPlayClickThroughRate;

    private Long gameChargeIn24HCount;
    private BigDecimal gameChargeIn24HAmount;
    private BigDecimal costPerGameChargeIn24H;
    private BigDecimal gameChargeIn24HRoi;

    private Long gameChargeIn1DCount;
    private BigDecimal gameChargeIn1DAmount;
    private BigDecimal costPerGameChargeIn1D;
    private BigDecimal gameChargeIn1DRoi;

    private Long gameChargeIn7DCount;
    private BigDecimal gameChargeIn7DAmount;
    private BigDecimal costPerGameChargeIn7D;
    private BigDecimal gameChargeIn7DRoi;

    private Long browseUv;
    private Long addCartUv;
    private Long followUv;
    private Long orderUv;
    private BigDecimal orderGmv;
    private Long dealUv;
    private BigDecimal dealGmv;
    private Long newUserNum;
    private Long newBrowseUserNum;
    private Long searchUv;

    private Long videoLikeOldCount;
    private Long videoInteractOldCount;

    private Long messageChatCount;
    private BigDecimal messageChatRate;
    private BigDecimal costPerMessageChat;
    private Long messageChatUvCount;

    private Long shallowConvCount;

    private Long gameFirstChargeCount;

    private Long messageLeadCount;
    private BigDecimal messageLeadRate;
    private BigDecimal costPerMessageLead;

    private Long gameChargeIn14dCount;
    private BigDecimal gameChargeIn14dAmount;
    private BigDecimal costPerGameChargeIn14d;
    private BigDecimal gameChargeIn14dRoi;

    private Long gameChargeIn30dCount;
    private BigDecimal gameChargeIn30dAmount;
    private BigDecimal costPerGameChargeIn30d;
    private BigDecimal gameChargeIn30dRoi;

    private Long paidIn14dCount;
    private BigDecimal paidIn14dAmount;
    private BigDecimal costPerPaidIn14d;
    private BigDecimal paidIn14dRoi;

    private Long paidIn30dCount;
    private BigDecimal paidIn30dAmount;
    private BigDecimal costPerPaidIn30d;
    private BigDecimal paidIn30dRoi;

    private BigDecimal gameChargeIn24hMixAmount;
    private BigDecimal gameChargeIn24hMixRoi;
    private BigDecimal gameChargeIn1dMixAmount;
    private BigDecimal gameChargeIn1dMixRoi;
    private BigDecimal gameChargeIn7dMixAmount;
    private BigDecimal gameChargeIn7dMixRoi;

    private BigDecimal gameChargeIn14dMixAmount;
    private BigDecimal gameChargeIn14dMixRoi;
    private BigDecimal gameChargeIn30dMixAmount;
    private BigDecimal gameChargeIn30dMixRoi;

    private Long retention3dCount;
    private BigDecimal retention3dRate;
    private BigDecimal costPerRetention3d;

    private Long retention5dCount;
    private BigDecimal retention5dRate;
    private BigDecimal costPerRetention5d;

    private Long retention7dCount;
    private BigDecimal retention7dRate;
    private BigDecimal costPerRetention7d;

    private Long retentionDaysCount;
    private BigDecimal retentionDaysRate;
    private BigDecimal costPerRetentionDays;

    private Long openActionValidCount;
    private BigDecimal costPerOpenActionValid;
}
