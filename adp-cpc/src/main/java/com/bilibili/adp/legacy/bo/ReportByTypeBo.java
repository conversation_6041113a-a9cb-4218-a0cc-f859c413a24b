package com.bilibili.adp.legacy.bo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonUnwrapped;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ReportByTypeBo {
    private String date;
    private String time;
    private String campaignName;
    private Integer campaignId;
    private String campaignAdType;
    private String promotionPurposeType;
    private String unitName;
    private Integer unitId;
    private String slotGroupName;
    private Long creativeId;
    private String creativeName;
    private String advertisingModeDesc;
    private String offerTypeDesc;
    private String ocpxStateDesc;


    private Integer dynamicDetailPageBrowseCount;
    private BigDecimal dynamicDetailPageBrowseCost;
    private BigDecimal dynamicDetailPageBrowseRate;

    //起飞活动相关
    private Integer activityPagePullUpCount;
    private BigDecimal activityPagePullUpCost;
    private BigDecimal activityPagePullUpRate;

    private String bvId;
    private String avName;
    private String upName;
    /**
     * 优化目标
     */
    private String ocpcTargetDesc;

    @JsonUnwrapped
    private ReportSummaryBo reportSummary;

    //public static ReportByTypeVo from(LaunchDataDetailVo vo) {
    //    ReportSummaryVo summaryVo = new ReportSummaryVo();
    //    summaryVo.setShowCount(vo.getShowCount());
    //    summaryVo.setClickCount(vo.getClickCount());
    //    summaryVo.setCost(vo.getCost());
    //    summaryVo.setOrderSubmitCount(vo.getOrderAddCount());
    //    summaryVo.setOrderSubmitAmount(vo.getOrderAddPrice());
    //    summaryVo.setFirstOrderPlaceCount(vo.getFirstOrderPlaceCount());
    //    summaryVo.setFirstOrderPlaceAmount(vo.getFirstOrderPlaceAmount());
    //    summaryVo.setOrderPayCount(vo.getOrderCount());
    //    summaryVo.setOrderPayAmount(vo.getOrderPayment());
    //    summaryVo.setOrderFirstPayCount(vo.getOrderFirstCount());
    //    summaryVo.setOrderFirstPayAmount(vo.getOrderFirstPayment());
    //    summaryVo.setRegisterCount(vo.getRegisterCount());
    //    summaryVo.setGameActivateCount(vo.getActivateCount());
    //    summaryVo.setGameReserveCount(vo.getReserveCount());
    //    summaryVo.setFansIncreaseCount(vo.getFanFollowCount());
    //    summaryVo.setIosActivateCount(vo.getIosActivateCount());
    //    summaryVo.setAndroidActivateCount(vo.getAndroidActivateCount());
    //    summaryVo.setFormSubmitCount(vo.getFormSubmitCount());
    //    summaryVo.setAndroidDownloadCount(vo.getAndroidDownloadCount());
    //    summaryVo.setAndroidInstallCount(vo.getAndroidInstallCount());
    //    summaryVo.setValidClueCount(vo.getValidClueCount());
    //    summaryVo.setRetentionCount(vo.getRetentionCount());
    //    summaryVo.setAppCallupCount(vo.getAppCallupCount());
    //    // 这个字段vo没有，后面也不再用了
    //    summaryVo.setLpCallupCount(0);
    //    summaryVo.setAndroidGameCenterActivationCount(vo.getAndroidGameCenterActivationCount());
    //    summaryVo.setFormPaidCount(vo.getFormPaidCount());
    //    summaryVo.setLpCallUpSuccessCount(vo.getLpCallUpSuccessCount());
    //    summaryVo.setLpCallUpSuccessStayCount(vo.getLpCallUpSuccessStayCount());
    //    summaryVo.setAccountSubscribeCount(vo.getAccountSubscribeCount());
    //    summaryVo.setPlayCount(vo.getPlayCount());
    //    summaryVo.setPlayCost(vo.getPlayCost());
    //    summaryVo.setPlayShowCount(vo.getPlayShowCount());
    //    summaryVo.setFirstCommentCopyCount(vo.getFirstCommentCopyCount());
    //    summaryVo.setCommentClick(vo.getCommentClick());
    //    summaryVo.setUnderBoxLinkClickCount(vo.getUnderBoxLinkClickCount());
    //    summaryVo.setAndroidGameCenterPaymentInAppCount(vo.getAndroidGameCenterPaymentInAppCount());
    //    summaryVo.setAndroidGameCenterPaymentInAppAmount(vo.getAndroidGameCenterPaymentInAppAmount());
    //    summaryVo.setAndroidGameCenterFirstPaymentInAppCount(vo.getAndroidGameCenterFirstPaymentInAppCount());
    //    summaryVo.setAndroidGameCenterFirstPaymentInAppAmount(vo.getAndroidGameCenterFirstPaymentInAppAmount());
    //
    //    summaryVo.setAndroidGameActivePaidIn24hCount(vo.getAndroidGameActivePaidIn24hCount());
    //    summaryVo.setAndroidGameActivePaidIn24hAmount(vo.getAndroidGameActivePaidIn24hAmount());
    //    summaryVo.setAndroidGameActivePaidIn7dCount(vo.getAndroidGameActivePaidIn7dCount());
    //    summaryVo.setAndroidGameActivePaidIn7dAmount(vo.getAndroidGameActivePaidIn7dAmount());
    //    summaryVo.setAndroidGameActivePaidIn1dCount(vo.getAndroidGameActivePaidIn1dCount());
    //    summaryVo.setAndroidGameActivePaidIn1dAmount(vo.getAndroidGameActivePaidIn1dAmount());
    //
    //    summaryVo.setKeyBehaviorCount(vo.getKeyBehaviorCount());
    //    summaryVo.setWxCopyCount(vo.getWxCopyCount());
    //    summaryVo.setApplyCount(vo.getApplyCount());
    //    summaryVo.setCreditCount(vo.getCreditCount());
    //    summaryVo.setWithdrawDepositsCount(vo.getWithdrawDepositsCount());
    //    summaryVo.setVideoLikeCount(vo.getVideoLikeCount());
    //    summaryVo.setPaidIn24hCount(vo.getPaidIn24hCount());
    //    summaryVo.setPaidIn24hPrice(vo.getPaidIn24hPrice());
    //    summaryVo.setPaidIn7dCount(vo.getPaidIn7dCount());
    //    summaryVo.setPaidIn7dPrice(vo.getPaidIn7dPrice());
    //    summaryVo.setWxAddFansCount(vo.getWxAddFansCount());
    //    summaryVo.setCommentCallUpCount(vo.getCommentCallUpCount());
    //    summaryVo.setCommentShowCount(vo.getCommentShowCount());
    //    summaryVo.setFirstDayPayCount(vo.getFirstDayPayCount());
    //    summaryVo.setFirstDayPayAmount(vo.getFirstDayPayAmount());
    //    summaryVo.setNewFirstDayPayAmount(vo.getNewFirstDayPayAmount());
    //    summaryVo.setNewFirstDayPayCount(vo.getNewFirstDayPayCount());
    //    summaryVo.setLiveEntryCount(vo.getLiveEntryCount());
    //    summaryVo.setLiveReserveCount(vo.getLiveReserveCount());
    //    summaryVo.setFirstWithdrawCount(vo.getFirstWithdrawCount());
    //    summaryVo.setLiveCallUpCount(vo.getLiveCallUpCount());
    //    summaryVo.setComponentClickCount(vo.getComponentClickCount());
    //    summaryVo.setLiveGameCardShowCount(vo.getLiveGameCardShowCount());
    //    summaryVo.setLiveGameCardClickCount(vo.getLiveGameCardClickCount());
    //    summaryVo.setLiveBottomIconShow(vo.getLiveBottomIconShow());
    //    summaryVo.setLiveBottomIconClick(vo.getLiveBottomIconClick());
    //    summaryVo.setLiveNativeCardShow(vo.getLiveNativeCardShow());
    //    summaryVo.setLiveNativeCardClick(vo.getLiveNativeCardClick());
    //
    //    summaryVo.setUvNum(vo.getUvNum());
    //    summaryVo.setNewVisitorUvNum(vo.getNewVisitorUvNum());
    //    summaryVo.setPayBuyerUv(vo.getPayBuyerUv());
    //    summaryVo.setCltUvNum(vo.getCltUvNum());
    //    summaryVo.setSeLeadConv(vo.getSeLeadConv());
    //    summaryVo.setAddUvNum(vo.getAddUvNum());
    //    summaryVo.update();
    //
    //    final ReportByTypeVo reportByTypeVo = ReportByTypeVo.builder()
    //            .date(vo.getDate())
    //            .time(vo.getTime())
    //            .campaignId(vo.getCampaignId())
    //            .campaignName(vo.getCampaignName())
    //            .campaignAdType(vo.getCampaignAdType())
    //            .unitId(vo.getUnitId())
    //            .unitName(vo.getUnitName())
    //            .advertisingModeDesc(vo.getAdvertisingModeDesc())
    //            .promotionPurposeType(vo.getPromotionType())
    //            .slotGroupName(vo.getSlotGroupName())
    //            .reportSummaryVo(summaryVo)
    //            .dynamicDetailPageBrowseCount(vo.getDynamicDetailPageBrowseCount())
    //            .dynamicDetailPageBrowseCost(vo.getDynamicDetailPageBrowseCost())
    //            .dynamicDetailPageBrowseRate(vo.getDynamicDetailPageBrowseRate())
    //            .activityPagePullUpCount(vo.getActivityPagePullUpCount())
    //            .activityPagePullUpRate(vo.getActivityPagePullUpRate())
    //            .offerTypeDesc(vo.getOfferTypeDesc())
    //            .ocpxStateDesc(vo.getOcpxStateDesc())
    //            .build();
    //
    //    if (!Strings.isNullOrEmpty(vo.getCreativeName())) {
    //        reportByTypeVo.setCreativeId(Long.parseLong(vo.getCreativeName()));
    //    }
    //    return reportByTypeVo;
    //}
}
