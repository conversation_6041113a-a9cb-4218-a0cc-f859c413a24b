package com.bilibili.adp.legacy.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.bilibili.adp.common.util.ObjectUtils;
import com.bilibili.adp.cpc.enums.ad.PromotionPurposeType;
import com.bilibili.adp.legacy.bo.UnitReportDetailBo;
import com.bilibili.adp.legacy.converters.ReportExportDtoConverter;
import com.bilibili.adp.legacy.utils.ConvertUtil;
import com.bilibili.adp.web.framework.annotations.ExcelResources;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.Objects;


/**
 * 复制的代码，从web模块下沉到service模块
 *
 * @see com.bilibili.adp.advertiser.portal.webapi.v2.vo.responses.UnitReportExportVo
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class UnitReportExportDto {
    @ExcelIgnore
    private int adpVersion;

    @ExcelIgnore
    private Integer speedMode;


    @ExcelProperty(value = "单元ID")
    private Integer unitId;

    @ExcelProperty(value = "单元名称")
    private String unitName;

    @ExcelProperty(value = "广告版本")
    private String adpVersionDesc;

    @ExcelProperty(value = "推广内容")
    private String promotionContentTypeDesc;

    @ExcelProperty(value = "单元状态")
    private String status;

    @ExcelProperty(value = "单元预算")
    private BigDecimal budget;

    @ExcelIgnore
    private Integer slotGroupId;


    @ExcelProperty(value = "广告位")
    private String slotGroup;


    @ExcelProperty(value = "设备")
    private String osDesc;

    @ExcelProperty(value = "投放日期")
    private String launchRange;

    @ExcelProperty(value = "计划名称")
    private String campaignName;

    @ExcelProperty(value = "计划ID")
    private Integer campaignId;
    //由于 cpc ocpc 的 sales type相同，所以用sales mode来区分

    private Integer salesMode;

    @ExcelProperty(value = "出价方式")

    private String salesModeDesc;

    @ExcelProperty(value = "出价(元)")
    private BigDecimal costPrice;
    private Integer ocpxStage;

    private String ocpxStageDesc;
    private String ocpxTargetDesc;


    @ExcelProperty(value = "oCPX深度转化目标")
    private String ocpxTargetTwoStageDesc;
    @ApiModelProperty("目标转化出价")
    @ExcelProperty(value = "目标转化出价")
    private BigDecimal ocpxTargetOneBid;
    @ApiModelProperty("深度转化出价")
    @ExcelProperty(value = "深度转化出价")
    private BigDecimal ocpxTargetTwoBid;
    @ApiModelProperty("oCPX转化目标")
    @ExcelProperty(value = "oCPX转化目标")
    private String ocpxStateDesc;
    @ApiModelProperty("oCPX赔付状态")
    @ExcelProperty(value = "oCPX赔付状态")
    private String ocpxAutoPayFinalStatus;
    @ApiModelProperty("双出价两阶段优化")
    @ExcelProperty(value = "双出价两阶段优化")
    private String dualBidTwoStageOptimizationDesc;


    @ExcelProperty(value = "总花费")
    private BigDecimal cost;

    @ExcelProperty(value = "展示量")
    private Long showCount;

    @ExcelProperty(value = "点击量")
    private Integer clickCount;

    @ExcelProperty(value = "点击率")
    private String clickRate;

    @ExcelProperty(value = "千次展示价格")
    private String averageCostPerThousand;

    @ExcelProperty(value = "单次点击价格")
    private String costPerClick;


    @ExcelProperty(value = "表单提交数")
    private Integer formSubmitCount;

    @ExcelProperty(value = "表单提交率")
    private String formSubmitRate;

    @ExcelProperty(value = "表单提交成本")
    private String formSubmitAverageCost;

    @ExcelProperty(value = "表单付费数")
    private Integer formPaidCount;

    @ExcelProperty(value = "表单付费率")
    private String formPaidRate;

    @ExcelProperty(value = "表单付费成本")
    private String formPaidCost;

    @ExcelProperty(value = "有效线索数")
    private Integer validClueCount;

    @ExcelProperty(value = "有效线索率")
    private String validClueRate;

    @ExcelProperty(value = "有效线索成本")
    private String validClueAverageCost;

    @ExcelProperty(value = "微信复制数")
    private Integer wxCopyCount;
    /**
     * 【微信复制成本】=总花费/【微信复制数】
     */

    @ExcelProperty(value = "微信复制成本")
    private String wxCopyCost;
    /**
     * 【微信复制率】=【微信复制数】/点击量
     */

    @ExcelProperty(value = "微信复制率")
    private String wxCopyRate;


    @ExcelProperty(value = "微信加粉数")
    private Integer wxAddFansCount;

    @ExcelProperty(value = "微信加粉率")
    private String wxAddFansRate;

    @ExcelProperty(value = "微信加粉成本")
    private String wxAddFansCost;


    @ExcelProperty(value = "完件数")
    private Integer applyCount;
    /**
     * 【完件成本】= 总花费 /【完件数】
     */

    @ExcelProperty(value = "完件成本")
    private String applyCost;
    /**
     * 【完件率】=【完件数】/ 点击量
     */

    @ExcelProperty(value = "完件率")
    private String applyRate;
    /**
     * 授信数
     */

    @ExcelProperty(value = "授信数")
    private Integer creditCount;
    /**
     * 【授信成本】= 总花费 /【授信数】
     */

    @ExcelProperty(value = "授信成本")
    private String creditCost;
    /**
     * 【授信率】=【授信数】/ 点击量
     */

    @ExcelProperty(value = "授信率")
    private String creditRate;
    /**
     * 放款数
     */

    @ExcelProperty(value = "放款数")
    private Integer withdrawDepositsCount;
    /**
     * 【放款成本】= 总花费 /【放款数】
     */

    @ExcelProperty(value = "放款成本")
    private String withdrawDepositsCost;
    /**
     * 【放款率】=【放款数】/ 点击量
     */

    @ExcelProperty(value = "放款率")
    private String withdrawDepositsRate;


    @ExcelProperty(value = "游戏预约数")
    private Integer gameReserveCount;

    @ExcelProperty(value = "游戏预约成本")
    private String gameReserveAverageCost;

    @ExcelProperty(value = "游戏预约率")
    private String gameReserveRate;


    @ExcelProperty(value = "IOS激活数")
    private Integer iosActivateCount;

    @ExcelProperty(value = "Android激活数")
    private Integer androidActivateCount;

    @ExcelProperty(value = "应用激活数")
    private Integer activateCount;

    @ExcelProperty(value = "应用激活成本")
    private String appActivateAverageCost;

    @ExcelProperty(value = "应用激活率")
    private String appActivateRate;

    @ExcelProperty(value = "安卓游戏中心激活数")
    private Integer androidGameCenterActivationCount;

    @ExcelProperty(value = "安卓游戏中心成本")
    private String androidGameCenterActivationCost;

    @ExcelProperty(value = "安卓游戏中心激活率")
    private String androidGameCenterActivationRate;

    @ExcelProperty(value = "用户注册数")
    private Integer registerCount;

    @ExcelProperty(value = "用户注册成本")
    private String registerAverageCost;

    @ExcelProperty(value = "用户注册率")
    private String registerRate;

    @ExcelProperty(value = "应用内付费次数")
    private Integer orderPayCount;

    @ExcelProperty(value = "应用内累计付费金额")
    private String orderPayAmount;

    @ExcelProperty(value = "应用内首次付费次数")
    private Integer orderFirstPayCount;

    @ExcelProperty(value = "应用内首次付费金额")
    private String orderFirstPayAmount;
    /**
     * 应用内首次付费消耗 = cost(总花费) / orderFirstPayCount(应用内首次付费次数)
     */

    @ExcelProperty(value = "应用内首次付费成本")
    private String orderFirstPayCost;
    /**
     * 应用内首次付费率 = orderFirstPayCount(应用内首次付费数) / clickCount(点击数)
     */

    @ExcelProperty(value = "应用内首次付费率")
    private String orderFirstPayRate;

    @ExcelProperty(value = "激活后24小时付费数")
    private Integer paidIn24hCount;

    @ExcelProperty(value = "激活后24小时付费成本")
    private String paidIn24hCost;

    @ExcelProperty(value = "激活后24小时付费金额")
    private String paidIn24hPrice;

    @ExcelProperty(value = "激活后24小时付费ROI")
    private String paidIn24hRoi;


    @ExcelProperty(value = "激活后7日内付费数")
    private Integer paidIn7dCount;

    @ExcelProperty(value = "激活后7日内付费成本")
    private String paidIn7dCost;

    @ExcelProperty(value = "激活后7日内付费金额")
    private String paidIn7dPrice;

    @ExcelProperty(value = "激活后7日内付费ROI")
    private String paidIn7dRoi;


    @ExcelProperty(value = "安卓游戏中心应用内付费次数")
    private Integer androidGameCenterPaymentInAppCount;
    /**
     * 安卓游戏中心 应用内付费金额
     */

    @ExcelProperty(value = "安卓游戏中心应用内累计付费金额")
    private String androidGameCenterPaymentInAppAmount;
    /**
     * 安卓游戏中心 应用内首次付费数
     */

    @ExcelProperty(value = "安卓游戏中心应用内首次付费数")
    private Integer androidGameCenterFirstPaymentInAppCount;
    /**
     * 安卓游戏中心 应用内首次付费金额
     */

    @ExcelProperty(value = "安卓游戏中心应用内首次付费金额")
    private String androidGameCenterFirstPaymentInAppAmount;
    /**
     * 安卓游戏中心 应用内首次付费成本 【安卓游戏中心应用内首次付费成本】=总花费/【安卓游戏中心应用内首次付费数】
     */

    @ExcelProperty(value = "安卓游戏中心应用内首次付费成本")
    private String androidGameCenterFirstPaymentInAppCost;
    /**
     * 安卓游戏中心 应用内首次付费率 【安卓游戏中心应用内首次付费率】=【安卓游戏中心应用内首次付费数】/【安卓游戏中心应用激活数】
     */

    @ExcelProperty(value = "安卓游戏中心应用内首次付费率")
    private String androidGameCenterFirstPaymentInAppRate;

    @ExcelProperty(value = "次日留存数")
    private Integer retentionCount;

    @ExcelProperty(value = "次日留存成本")
    private String retentionCost;

    @ExcelProperty(value = "次日留存率")
    private String retentionRate;


    @ExcelProperty(value = "应用内访问数")
    private Integer appCallupCount;

    @ExcelProperty(value = "应用内访问成本")
    private String appCallupCost;

    @ExcelProperty(value = "应用内访问率")
    private String appCallupRate;


    @ExcelProperty(value = "应用唤起数")
    private Integer lpCallUpSuccessCount;

    @ExcelProperty(value = "应用唤起成本")
    private String lpCallUpSuccessCost;

    @ExcelProperty(value = "应用唤起率")
    private String lpCallUpSuccessRate;
    /**
     * 关键行为数
     */

    @ExcelProperty(value = "关键行为数")
    private Integer keyBehaviorCount;
    /**
     * 关键行为成本 【关键行为成本】=总花费/【关键行为数】
     */

    @ExcelProperty(value = "关键行为成本")
    private String keyBehaviorCost;
    /**
     * 关键行为率 【关键行为率】=【关键行为数】/【应用激活】
     */

    @ExcelProperty(value = "关键行为率")
    private String keyBehaviorRate;

    @ExcelProperty(value = "店铺停留数")
    private Integer lpCallUpSuccessStayCount;

    @ExcelProperty(value = "店铺停留成本")
    private String lpCallUpSuccessStayCost;

    @ExcelProperty(value = "店铺停留率")
    private String lpCallUpSuccessStayRate;

    @ExcelProperty(value = "订单提交数")
    private Integer orderSubmitCount;

    @ExcelProperty(value = "订单提交金额")
    private BigDecimal orderSubmitAmount;

    @ExcelProperty(value = "订单提交成本")
    private String orderSubmitCost;

    @ExcelProperty(value = "订单提交率")
    private String orderSubmitRate;

    @ExcelProperty(value = "订单转化率")
    private String goodsConversionRate;

    @ExcelProperty(value = "订单ROI")
    private String goodsRoi;

    @ExcelProperty(value = "唤起成单率")
    private String callUpOrderSuccessRate;


    @ExcelProperty(value = "首次购买数")
    private Integer firstOrderPlaceCount;

    @ExcelProperty(value = "首次购买金额")
    private String firstOrderPlaceAmount;

    @ExcelProperty(value = "首次购买率")
    private String firstOrderPlaceRate;

    @ExcelProperty(value = "首次购买成本")
    private String firstOrderPlaceCost;

    @ExcelProperty(value = "播放量")
    private int playCount;

    @ExcelProperty(value = "播放率")
    private String playRate;

    @ExcelProperty(value = "播放成本")
    private String costPerPlayCount;


    @ExcelProperty(value = "涨粉数")
    private Integer fansIncreaseCount;

    @ExcelProperty(value = "涨粉成本")
    private String fansIncreaseAverageCost;

    @ExcelProperty(value = "涨粉率")
    private String fansIncreaseRate;

    @ExcelProperty(value = "播转粉率")
    private String play2FansRate;

    @ExcelProperty(value = "账号关注数")
    private Integer accountSubscribeCount;

    @ExcelProperty(value = "账号关注成本")
    private String accountSubscribeCost;

    @ExcelProperty(value = "账号关注率")
    private String accountSubscribeRate;

    @ExcelProperty(value = "动态详情页浏览数")
    private Integer dynamicDetailPageBrowseCount;

    @ExcelProperty(value = "动态详情页浏览成本")
    private String dynamicDetailPageBrowseCost;

    @ExcelProperty(value = "动态详情页浏览率")
    private String dynamicDetailPageBrowseRate;

    @ExcelProperty(value = "首条评论复制数")
    private int firstCommentCopyCount;

    @ExcelProperty(value = "首条评论复制成本")
    private String costPerFirstCommentCopyCount;

    @ExcelProperty(value = "首条评论复制率")
    private String firstCommentCopyRate;

    @ExcelProperty(value = "评论点击数")
    private Integer commentClick;
    /**
     * 评论链接点击成本（消耗/评论链接点击数)
     */

    @ExcelProperty(value = "评论点击成本")
    private String commentClickCost;
    /**
     * 评论链接点击率（评论链接点击数/播放数）
     */

    @ExcelProperty(value = "评论点击率")
    private String commentClickRate;

    @ExcelProperty(value = "框下链接点击数")
    private int underBoxLinkClickCount;

    @ExcelProperty(value = "框下链接点击成本")
    private String costPerUnderBoxLinkClickCount;

    @ExcelProperty(value = "框下链接点击率")
    private String underBoxLinkClickRate;

    @ExcelProperty(value = "活动页浮层拉起数")
    private Integer activityPagePullUpCount;
    /**
     * 活动页浮层拉起成本(元)
     */

    @ExcelProperty(value = "活动页浮层拉起成本")
    private String activityPagePullUpCost;
    /**
     * 活动页浮层拉起率(元)
     */

    @ExcelProperty(value = "活动页浮层拉起率")
    private String activityPagePullUpRate;


    @ExcelProperty(value = "评论链接曝光数")
    private Integer commentShowCount;


    @ExcelProperty(value = "评论链接曝光率")
    private String commentShowRate;


    @ExcelProperty(value = "评论链接曝光点击率")
    private String commentShowClickRate;


    @ExcelProperty(value = "评论链接唤起数量")
    private Integer commentCallUpCount;


    @ExcelProperty(value = "评论唤起率")
    private String commentCallUpRate;


    @ExcelProperty(value = "播放唤起率")
    private String playCallUpRate;


    @ExcelProperty(value = "当日付费数")
    private Integer firstDayPayCount;

    @ExcelProperty(value = "当日付费金额")
    private String firstDayPayAmount;

    @ExcelProperty(value = "当日付费率")
    private String firstDayPayRate;

    @ExcelProperty(value = "当日付费成本")
    private String firstDayPayCost;

    @ExcelProperty(value = "当日付费ROI")
    private String firstDayPayROI;


    private Integer promotionPurposeType;


    private String unitStatusMtime;

    private String beginDate;
    private String endDate;

    private Integer salesType;

    @ExcelProperty(value = "安卓游戏激活后24小时付费数")
    private Integer androidGameActivePaidIn24hCount;
    @ExcelProperty(value = "安卓游戏激活后24小时付费金额")
    private String androidGameActivePaidIn24hAmount;
    @ExcelProperty(value = "安卓游戏激活后24小时付费成本")
    private String androidGameActivePaidIn24hCost;
    @ExcelProperty(value = "安卓游戏激活后24小时付费ROI")
    private String androidGameActivePaidIn24hRoi;
    @ExcelProperty(value = "安卓游戏激活后7日内付费数")
    private Integer androidGameActivePaidIn7dCount;
    @ExcelProperty(value = "安卓游戏激活后7日内付费金额")
    private String androidGameActivePaidIn7dAmount;
    @ExcelProperty(value = "安卓游戏激活后7日内付费成本")
    private String androidGameActivePaidIn7dCost;
    @ExcelProperty(value = "安卓游戏激活后7日内付费ROI")
    private String androidGameActivePaidIn7dRoi;
    @ExcelProperty(value = "安卓游戏首日付费数")
    private Integer androidGameActivePaidIn1dCount;
    @ExcelProperty(value = "安卓游戏首日付费金额")
    private String androidGameActivePaidIn1dAmount;
    @ExcelProperty(value = "安卓游戏首日付费成本")
    private String androidGameActivePaidIn1dCost;
    @ExcelProperty(value = "安卓游戏首日付费ROI")
    private String androidGameActivePaidIn1dRoi;
    @ExcelProperty(value = "安卓游戏首日付费ROI")
    private String androidGameActivePaidIn1dRate;


    @ExcelProperty(value = "首日付费数")
    private Integer newFirstDayPayCount;

    @ExcelProperty(value = "首日付费金额")
    private String newFirstDayPayAmount;

    @ExcelProperty(value = "首日付费率")
    private String newFirstDayPayRate;

    @ExcelProperty(value = "首日付费成本")
    private String newFirstDayPayCost;

    @ExcelProperty(value = "首日付费ROI")
    private String newFirstDayPayRoi;


    @ExcelProperty(value = "辅助目标")
    private String assistTargetDesc;


    @ExcelProperty(value = "辅助出价")
    private BigDecimal assistPrice;


    @ExcelProperty(value = "首日放款数")
    private Integer firstWithdrawCount;

    @ExcelProperty(value = "首日放款率")
    private String firstWithdrawRate;

    @ExcelProperty(value = "首日放款成本")
    private String firstWithdrawCost;


    @ExcelProperty(value = "直播间商品点击次数")
    private Integer liveCallUpCount;

    @ExcelProperty(value = "直播间商品点击成本")
    private String liveCallUpCost;

    @ExcelProperty(value = "直播进房商品点击率")
    private String liveCallUpRate;


    @ExcelProperty(value = "组件点击数")
    private Integer componentClickCount;

    @ExcelProperty(value = "组件点击率")
    private String componentClickRate;

    @ExcelProperty(value = "组件点击成本")
    private String componentClickCost;


    @ExcelProperty(value = "直播间游戏卡曝光")
    private Integer liveGameCardShowCount;

    @ExcelProperty(value = "直播间游戏卡点击")
    private Integer liveGameCardClickCount;

    @ExcelProperty(value = "直播间游戏卡点击率")
    private String liveGameCardClickRate;

    @ExcelProperty(value = "直播间游戏卡点击成本")
    private String liveGameCardClickCost;

    @ExcelProperty(value = "直播间底部icon展示")
    private Integer liveBottomIconShow;

    @ExcelProperty(value = "直播间底部icon点击")
    private Integer liveBottomIconClick;

    @ExcelProperty(value = "直播间讲解卡展示")
    private Integer liveNativeCardShow;

    @ExcelProperty(value = "直播间讲解卡点击")
    private Integer liveNativeCardClick;

    /**
     * 直播间进场人数
     */
    @ExcelProperty(value = "直播间进人次数")
    private Integer liveEntryCount;
    @ExcelProperty(value = "直播间进人成本")
    private String liveEntryCost;
    @ExcelProperty(value = "直播间进人率")
    private String liveEntryRate;

    /**
     * 直播间预约数
     */
    @ExcelProperty(value = "直播间预约数")
    private Integer liveReserveCount;
    @ExcelProperty(value = "直播间预约成本")
    private String liveReserveCost;
    @ExcelProperty(value = "直播间点击预约率")
    private String liveReserveRate;

    @ExcelProperty(value = "安卓下载数")
    private Integer androidDownloadCount;
    @ExcelProperty(value = "安卓下载率")
    private String androidDownloadAverageCost;
    @ExcelProperty(value = "安卓下载成本")
    private String androidDownloadRate;

    @ExcelProperty(value = "安卓安装数")
    private Integer androidInstallCount;
    @ExcelProperty(value = "安卓安装率")
    private String androidInstallAverageCost;
    @ExcelProperty(value = "安卓安装成本")
    private String androidInstallRate;


    /**
     * 进店UV
     */
    @ExcelProperty(value = "进店UV")
    private Long uvNum;
    /**
     * 新访客UV
     */
    @ExcelProperty(value = "新访客UV")
    private Long newVisitorUvNum;
    /**
     * 成交UV
     */
    @ExcelProperty(value = "成交UV")
    private Long payBuyerUv;
    /**
     * 收藏UV
     */
    @ExcelProperty(value = "收藏UV")
    private Long cltUvNum;

    /**
     * 手淘搜索进店UV
     */
    @ExcelProperty(value = "手淘搜索进店UV")
    private Long seLeadConv;
    /**
     * 加购UV
     */
    @ExcelProperty(value = "加购UV")
    private Long addUvNum;
    @ExcelProperty(value = "图文/动态详情浏览数")
    private Integer dynamicDetailBrowseCount;
    @ExcelProperty(value = "图文/动态蓝链曝光数")
    private Integer dynamicGoodsUrlShowCount;
    @ExcelProperty(value = "图文/动态蓝链点击数")
    private Integer dynamicGoodsUrlClickCount;

    @ExcelProperty(value = "内容详情")
    private String contentDetail;


    @ExcelProperty(value = "智能创意花费")
    private BigDecimal intelligentCost;
    @ExcelProperty(value = "智能创意花费占比")
    private BigDecimal intelligentCostRate;


    @ExcelProperty(value = "播放点赞量")
    private Integer videoLikeCount;

    @ExcelProperty(value = "播放点赞率")
    private String videoLikeRate;

    @ExcelProperty(value = "播放点赞成本")
    private String costPerVideoLike;

    @ExcelProperty(value = "收藏量")
    private Integer videoFavCount;

    @ExcelProperty(value = "收藏率")
    private String videoFavRate;

    @ExcelProperty(value = "收藏成本")
    private String costPerVideoFav;

    @ExcelProperty(value = "分享量")
    private Integer videoShareCount;

    @ExcelProperty(value = "分享率")
    private String videoShareRate;

    @ExcelProperty(value = "分享成本")
    private String costPerVideoShare;

    @ExcelProperty(value = "弹幕量")
    private Integer videoBulletCount;

    @ExcelProperty(value = "弹幕率")
    private String videoBulletRate;

    @ExcelProperty(value = "弹幕成本")
    private String costPerVideoBullet;

    @ExcelProperty(value = "评论量")
    private Integer videoReplyCount;

    @ExcelProperty(value = "评论率")
    private String videoReplyRate;

    @ExcelProperty(value = "评论成本")
    private String costPerVideoReply;

    @ExcelProperty(value = "投币量")
    private Integer videoCoinCount;

    @ExcelProperty(value = "投币率")
    private String videoCoinRate;

    @ExcelProperty(value = "投币成本")
    private String costPerVideoCoin;

    @ExcelProperty(value = "播放互动量")
    private Integer videoInteractCount;

    @ExcelProperty(value = "播放互动率")
    private String videoInteractRate;

    @ExcelProperty(value = "播放互动成本")
    private String costPerVideoInteract;

    @ExcelProperty(value = "封面点击率")
    private String notAutoPlayClickThroughRate;

    @ExcelProperty(value = "激活后24小时变现数")
    private Long gameChargeIn24hCount;

    @ExcelProperty(value = "激活后24小时变现金额")
    private String gameChargeIn24hAmount;

    @ExcelProperty(value = "激活后24小时变现成本")
    private String costPerGameChargeIn24h;

    @ExcelProperty(value = "激活后24小时变现ROI")
    private String gameChargeIn24hRoi;

    @ExcelProperty(value = "激活后首自然日变现数")
    private Long gameChargeIn1dCount;

    @ExcelProperty(value = "激活后首自然日变现金额")
    private String gameChargeIn1dAmount;

    @ExcelProperty(value = "激活后首自然日变现成本")
    private String costPerGameChargeIn1d;

    @ExcelProperty(value = "激活后首自然日变现ROI")
    private String gameChargeIn1dRoi;

    @ExcelProperty(value = "激活后7日变现数")
    private Long gameChargeIn7dCount;

    @ExcelProperty(value = "激活后7日变现金额")
    private String gameChargeIn7dAmount;

    @ExcelProperty(value = "激活后7日变现成本")
    private String costPerGameChargeIn7d;

    @ExcelProperty(value = "激活后7日变现ROI")
    private String gameChargeIn7dRoi;

    @ExcelProperty(value = "商详浏览UV")
    private Long browseUv;

    @ExcelProperty(value = "加购UV")
    private Long addCartUv;

    @ExcelProperty(value = "收藏UV")
    private Long followUv;

    @ExcelProperty(value = "下单UV")
    private Long orderUv;

    @ExcelProperty(value = "下单GMV")
    private String orderGmv;

    @ExcelProperty(value = "成交UV")
    private Long dealUv;

    @ExcelProperty(value = "成交GMV")
    private String dealGmv;

    @ExcelProperty(value = "新客数")
    private Long newUserNum;

    @ExcelProperty(value = "新访客数")
    private Long newBrowseUserNum;

    @ExcelProperty(value = "搜索UV")
    private Long searchUv;

    @ExcelProperty(value = "点赞量")
    private Long videoLikeOldCount;

    @ExcelProperty(value = "互动量")
    private Long videoInteractOldCount;

    @ExcelProperty(value = "私信开口数")
    @ExcelResources(title = "私信开口数")
    private Long messageChatCount;

    @ExcelProperty(value = "私信开口率")
    @ExcelResources(title = "私信开口率")
    private String messageChatRate;

    @ExcelProperty(value = "私信开口成本")
    @ExcelResources(title = "私信开口成本")
    private String costPerMessageChat;

    @ExcelProperty(value = "私信开口UV")
    @ExcelResources(title = "私信开口UV")
    private Long messageChatUvCount;

    @ExcelProperty("搜索词明投出价系数")
    private String searchPriceCoefficient;
    @ExcelProperty("搜索词明投首位出价系数")
    private String searchFirstPriceCoefficient;

    @ExcelProperty(value = "变现人数")
    private Long gameFirstChargeCount;

    @ExcelProperty(value = "私信留资数")
    private Long messageLeadCount;

    @ExcelProperty(value = "私信留资率")
    private String messageLeadRate;

    @ExcelProperty(value = "私信留资成本")
    private String costPerMessageLead;

    @ExcelProperty(value = "激活14天内充值次数")
    private Long gameChargeIn14dCount;

    /**
     * 激活14天内变现金额
     */
    @ExcelProperty(value = "激活14天内变现金额")
    private BigDecimal gameChargeIn14dAmount;

    /**
     * 激活14天内变现成本
     */
    @ExcelProperty(value = "激活14天内变现成本")
    private BigDecimal costPerGameChargeIn14d;

    /**
     * 激活14天内变现ROI
     */
    @ExcelProperty(value = "激活14天内变现ROI")
    private BigDecimal gameChargeIn14dRoi;

    /**
     * 激活30天内变现次数
     */
    @ExcelProperty(value = "激活30天内变现次数")
    private Long gameChargeIn30dCount;

    /**
     * 激活30天内变现金额
     */
    @ExcelProperty(value = "激活30天内变现金额")
    private BigDecimal gameChargeIn30dAmount;

    /**
     * 激活30天内变现成本
     */
    @ExcelProperty(value = "激活30天内变现成本")
    private BigDecimal costPerGameChargeIn30d;

    /**
     * 激活30天内变现ROI
     */
    @ExcelProperty(value = "激活30天内变现ROI")
    private BigDecimal gameChargeIn30dRoi;

    /**
     * 14天内付费次数
     */
    @ExcelProperty(value = "14天内付费次数")
    private Long paidIn14dCount;

    /**
     * 14天内付费金额
     */
    @ExcelProperty(value = "14天内付费金额")
    private BigDecimal paidIn14dAmount;

    /**
     * 14天内付费成本
     */
    @ExcelProperty(value = "14天内付费成本")
    private BigDecimal costPerPaidIn14d;

    /**
     * 14天内付费ROI
     */
    @ExcelProperty(value = "14天内付费ROI")
    private BigDecimal paidIn14dRoi;

    /**
     * 30天内付费次数
     */
    @ExcelProperty(value = "30天内付费次数")
    private Long paidIn30dCount;

    /**
     * 30天内付费金额
     */
    @ExcelProperty(value = "30天内付费金额")
    private BigDecimal paidIn30dAmount;

    /**
     * 30天内付费成本
     */
    @ExcelProperty(value = "30天内付费成本")
    private BigDecimal costPerPaidIn30d;

    /**
     * 30天内付费ROI
     */
    @ExcelProperty(value = "30天内付费ROI")
    private BigDecimal paidIn30dRoi;

    /**
     * 激活24小时内混合变现金额
     */
    @ExcelProperty(value = "激活24小时内混合变现金额")
    private BigDecimal gameChargeIn24hMixAmount;

    /**
     * 激活24小时内混合变现ROI
     */
    @ExcelProperty(value = "激活24小时内混合变现ROI")
    private BigDecimal gameChargeIn24hMixRoi;

    /**
     * 激活1天内混合变现金额
     */
    @ExcelProperty(value = "激活1天内混合变现金额")
    private BigDecimal gameChargeIn1dMixAmount;

    /**
     * 激活1天内混合变现ROI
     */
    @ExcelProperty(value = "激活1天内混合变现ROI")
    private BigDecimal gameChargeIn1dMixRoi;

    /**
     * 激活7天内混合变现金额
     */
    @ExcelProperty(value = "激活7天内混合变现金额")
    private BigDecimal gameChargeIn7dMixAmount;

    /**
     * 激活7天内混合变现ROI
     */
    @ExcelProperty(value = "激活7天内混合变现ROI")
    private BigDecimal gameChargeIn7dMixRoi;

    /**
     * 激活14天内混合变现金额
     */
    @ExcelProperty(value = "激活14天内混合变现金额")
    private BigDecimal gameChargeIn14dMixAmount;

    /**
     * 激活14天内混合变现ROI
     */
    @ExcelProperty(value = "激活14天内混合变现ROI")
    private BigDecimal gameChargeIn14dMixRoi;

    /**
     * 激活30天内混合变现金额
     */
    @ExcelProperty(value = "激活30天内混合变现金额")
    private BigDecimal gameChargeIn30dMixAmount;

    /**
     * 激活30天内混合变现ROI
     */
    @ExcelProperty(value = "激活30天内混合变现ROI")
    private BigDecimal gameChargeIn30dMixRoi;

    /**
     * 3天留存次数
     */
    @ExcelProperty(value = "3天留存次数")
    private Long retention3dCount;

    /**
     * 3天留存率
     */
    @ExcelProperty(value = "3天留存率")
    private BigDecimal retention3dRate;

    /**
     * 3天留存成本
     */
    @ExcelProperty(value = "3天留存成本")
    private BigDecimal costPerRetention3d;

    /**
     * 5天留存次数
     */
    @ExcelProperty(value = "5天留存次数")
    private Long retention5dCount;

    /**
     * 5天留存率
     */
    @ExcelProperty(value = "5天留存率")
    private BigDecimal retention5dRate;

    /**
     * 5天留存成本
     */
    @ExcelProperty(value = "5天留存成本")
    private BigDecimal costPerRetention5d;

    /**
     * 7天留存次数
     */
    @ExcelProperty(value = "7天留存次数")
    private Long retention7dCount;

    /**
     * 7天留存率
     */
    @ExcelProperty(value = "7天留存率")
    private BigDecimal retention7dRate;

    /**
     * 7天留存成本
     */
    @ExcelProperty(value = "7天留存成本")
    private BigDecimal costPerRetention7d;

    /**
     * 每日留存次数
     */
    @ExcelProperty(value = "每日留存次数")
    private Long retentionDaysCount;

    /**
     * 每日留存率
     */
    @ExcelProperty(value = "每日留存率")
    private BigDecimal retentionDaysRate;

    /**
     * 每日留存成本
     */
    @ExcelProperty(value = "每日留存成本")
    private BigDecimal costPerRetentionDays;

    /**
     * 客户自定义关键行为
     */
    @ExcelProperty(value = "客户自定义关键行为")
    private Long openActionValidCount;

    /**
     * 客户关键行为成本
     */
    @ExcelProperty(value = "客户关键行为成本")
    private BigDecimal costPerOpenActionValid;


    public static UnitReportExportDto from(UnitReportDetailBo vo) {
        if (Objects.isNull(vo)) {
            return null;
        }

        UnitReportExportDto reportExportVo = ReportExportDtoConverter.INSTANCE.toUnitExportDto(vo,
                vo.getReportSummary());
        BeanUtils.copyProperties(vo, reportExportVo);
        reportExportVo.setOsDesc(ConvertUtil.getStringOrDefault(vo.getOsDesc(), "不限"));
        reportExportVo.setLaunchRange(
                ConvertUtil.getStringOrDefault(vo.getBeginDate(), "-") + "~" + ConvertUtil.getStringOrDefault(
                        vo.getEndDate(), "-"));

        // 品牌传播，不展示一阶段二阶段
        if (ObjectUtils.nullSafeEquals(PromotionPurposeType.BRAND_SPREAD.getCode(), vo.getPromotionPurposeType())) {
            reportExportVo.setOcpxStateDesc(vo.getOcpxTargetDesc());
        } else {
            reportExportVo.setOcpxStateDesc(vo.getOcpxStageDesc() + "|" + vo.getOcpxTargetDesc());
        }

        return reportExportVo;
    }

}
