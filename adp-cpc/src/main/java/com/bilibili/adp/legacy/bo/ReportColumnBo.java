package com.bilibili.adp.legacy.bo;

import com.bilibili.adp.cpc.biz.constants.Constants;
import com.bilibili.adp.cpc.biz.constants.ReportColumn;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.*;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * 复制的代码，从web模块下沉到service模块
 *
 * @see com.bilibili.adp.advertiser.portal.webapi.v2.vo.responses.ReportColumnVo
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ReportColumnBo {
    // 真正的key, 数据库里按code存, 方便查问题
    private Integer code;
    // 报表数据对应的key
    private String key;
    // 报表的表头
    private String title;
    // 报表值的类型
    private String valueStyle;
    // 可否订阅, 每个报表都有几列是固定的, 其他可以订阅
    private Boolean subscribable;
    // 可否排序
    private Boolean sortable;

    private Boolean required;

    private String desc;
    private List<ReportColumnBo> children;

    private static String fetchValueStyle(Integer code) {
        if (ReportColumn.integerSet.contains(code)) {
            return Constants.REPORT_COLUMN_STYLE_INTEGER;
        } else if (ReportColumn.moneySet.contains(code)) {
            return Constants.REPORT_COLUMN_STYLE_MONEY;
        } else if (ReportColumn.percentSet.contains(code)) {
            return Constants.REPORT_COLUMN_STYLE_PERCENT;
        } else if (ReportColumn.numericSet.contains(code)) {
            return Constants.REPORT_COLUMN_STYLE_NUMERIC;
        }
        return Constants.REPORT_COLUMN_STYLE_LITERAL;
    }

    public static ReportColumnBo fromReportColumn(@NonNull ReportColumn rc, Set<Integer> fixedSet, boolean required, boolean isMiddle) {
        final Boolean sub;
        if (fixedSet == null) {
            sub = null;
        } else {
            sub = !fixedSet.contains(rc.getCode());
        }

        // 新版排序字段集合和老版排序字段集合
        boolean contains = false;
        if (isMiddle) {
            contains = ReportColumn.sortableSetNew.contains(rc.getCode());
        } else {
            contains = ReportColumn.sortableSetOld.contains(rc.getCode());
        }

        return ReportColumnBo.builder()
                .code(rc.getCode())
                .key(rc.getKey())
                .title(rc.getName())
                .valueStyle(fetchValueStyle(rc.getCode()))
                .sortable(contains)
                .required(required)
                .subscribable(sub)
                .desc(rc.getDesc())
                .build();
    }

    public static ReportColumnBo buildVo(@NonNull ReportColumn rc, Set<Integer> requiredColumnCode,
                                         List<ReportColumnBo> children, boolean isMiddle) {
        // 新版排序字段集合和老版排序字段集合
        boolean contains = false;
        if (isMiddle) {
            contains = ReportColumn.sortableSetNew.contains(rc.getCode());
        } else {
            contains = ReportColumn.sortableSetOld.contains(rc.getCode());
        }

        return ReportColumnBo.builder()
                .code(rc.getCode())
                .key(rc.getKey())
                .title(rc.getName())
                .valueStyle(fetchValueStyle(rc.getCode()))
                .sortable(contains)
                .required(!CollectionUtils.isEmpty(requiredColumnCode)
                        && requiredColumnCode.contains(rc.getCode()))
                .desc(rc.getDesc())
                .children(children)
                .build();
    }

    public static ReportColumnBo buildTree(ReportColumn rc, Set<Integer> requiredColumn, boolean isMiddle,Set<Integer> excludedReportColumnCodes) {
        return ReportColumnBo.builder()
                .code(rc.getCode())
                .key(rc.getKey())
                .title(rc.getName())
                .desc(rc.getDesc())
                .children(rc.getChildren()
                        .stream()
                        // 第二层需要过滤的进行过滤处理
                        .filter(x -> !excludedReportColumnCodes.contains(x.getCode()))
                        .map(x -> buildVo(x, requiredColumn,
                                x.getChildren()
                                        .stream()
                                        .filter(xx->!excludedReportColumnCodes.contains(xx.getCode()))
                                        .map(xx -> buildVo(xx, requiredColumn, Collections.emptyList(), isMiddle))
                                        .collect(Collectors.toList()), isMiddle
                                )).collect(Collectors.toList())).build();
    }


    public static void getChildren(ReportColumn rc, List<ReportColumn> children) {
        List<ReportColumn> reports = rc.getChildren();
        if (CollectionUtils.isEmpty(reports)) {
            children.add(rc);
            return;
        }
        reports.forEach(x -> getChildren(x, children));
    }
}
