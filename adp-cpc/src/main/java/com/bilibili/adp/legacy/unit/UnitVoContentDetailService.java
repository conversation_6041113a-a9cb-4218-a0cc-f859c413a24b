package com.bilibili.adp.legacy.unit;

import com.bilibili.adp.cpc.core.bos.ContentDetailBo;
import com.bilibili.adp.cpc.core.promotion.PromotionContentDetailFacade;
import com.bilibili.adp.cpc.dao.ad_core.querydsl.pos.LauCampaignPo;
import com.bilibili.adp.cpc.enums.ad.PromotionPurposeType;
import com.bilibili.adp.cpc.enums.ad.UnitDetailPromotionContentEnum;
import com.bilibili.adp.cpc.repo.OuterLauCampaignRepo;
import com.bilibili.adp.launch.api.creative.dto.LiveReservationInfoBo;
import com.bilibili.adp.launch.biz.service.live.LaunchLiveReserveService;
import com.bilibili.adp.legacy.bo.UnitReportDetailBo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.bilibili.adp.cpc.enums.ad.PromotionPurposeType.*;

/**
 * 单元内容详情处理
 *
 * <AUTHOR>
 * @date 2023/12/19 18:02
 */

/**
 * 复制的代码，从web模块下沉到service模块
 *
 * @see com.bilibili.adp.advertiser.portal.service.unit.UnitVoContentDetailProc
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class UnitVoContentDetailService {

    private final PromotionContentDetailFacade promotionContentLinkFacade;
    private final LaunchLiveReserveService launchLiveReserveService;
    private final OuterLauCampaignRepo outerLauCampaignRepo;

    public static final List<Integer> APP_PROMOTIONS = Arrays.asList(APP_DOWNLOAD.getCode(), LANDING_PAGE.getCode(),
            SALE_GOODS.getCode());

    public void procUnitContentDetail(List<UnitReportDetailBo> unitReportVos) {

        setUnitDetailPromotionContentEnum(unitReportVos);

        // 按 UnitDetailPromotionContentEnum 分组遍历
        Map<UnitDetailPromotionContentEnum, List<UnitReportDetailBo>> reportVoMapByUnitPpt = unitReportVos.stream().collect(Collectors.groupingBy(unitReportVo -> unitReportVo.getUnitDetailPromotionContentEnum()));
        Set<Map.Entry<UnitDetailPromotionContentEnum, List<UnitReportDetailBo>>> entries = reportVoMapByUnitPpt.entrySet();
        Iterator<Map.Entry<UnitDetailPromotionContentEnum, List<UnitReportDetailBo>>> iterator = entries.iterator();
        while (iterator.hasNext()) {
            Map.Entry<UnitDetailPromotionContentEnum, List<UnitReportDetailBo>> next = iterator.next();
            UnitDetailPromotionContentEnum unitDetailPromotionContentEnum = next.getKey();
            List<UnitReportDetailBo> unitReportVosOfPpt = next.getValue();

            // 处理一组 UnitDetailPromotionContentEnum 的单元数据
            List<Integer> unitIds = unitReportVosOfPpt.stream().map(unitReportVo -> unitReportVo.getUnitId()).collect(Collectors.toList());
            Integer accountId = unitReportVosOfPpt.get(0).getAccountId();

            // 获取同组 UnitDetailPromotionContentEnum 的单元的内容详情
            Map<Integer, ContentDetailBo> contentDetailBoMap =
                    promotionContentLinkFacade.queryUnitContentDetailMap(unitDetailPromotionContentEnum, unitIds, accountId);
            for (UnitReportDetailBo unitReportVo : unitReportVosOfPpt) {
                ContentDetailBo contentDetailBo = contentDetailBoMap.get(unitReportVo.getUnitId());
                if (contentDetailBo == null) {
                    continue;
                }
                // 有些推广目的的内容详情用链接填充的，因为获取耗时
                unitReportVo.setContentDetail(contentDetailBo.getTitle());
            }
        }

    }

    /**
     * 设置单元的 UnitDetailPromotionContentEnum
     *
     * @param unitReportVos
     */
    private void setUnitDetailPromotionContentEnum(List<UnitReportDetailBo> unitReportVos) {

        List<Integer> unitIds = unitReportVos.stream().map(unitReportVo -> unitReportVo.getUnitId()).collect(Collectors.toList());
        List<Integer> campaignIds = unitReportVos.stream().map(unitReportVo -> unitReportVo.getCampaignId()).collect(Collectors.toList());
        Map<Integer, LiveReservationInfoBo> liveReservationInfoBoMap = launchLiveReserveService.fetchUnitLiveReserveMap(unitIds);

        Map<Integer, LauCampaignPo> campaignPoMap = outerLauCampaignRepo.queryCampaignMap(campaignIds);

        for (UnitReportDetailBo unitReportVo : unitReportVos) {
            Integer unitPpt = unitReportVo.getPromotionPurposeType();
            PromotionPurposeType promotionPurposeType = PromotionPurposeType.getByCode(unitPpt);
            UnitDetailPromotionContentEnum unitPromotionContentEnum = UnitDetailPromotionContentEnum.NONE;


            switch (promotionPurposeType) {
                case ARCHIVE_CONTENT: // 投稿内容(品牌传播，账号推广)
                    unitPromotionContentEnum = UnitDetailPromotionContentEnum.ARCHIVE;
                    if (StringUtils.isEmpty(unitReportVo.getAvId())) {
                        LauCampaignPo lauCampaignPo = campaignPoMap.get(unitReportVo.getCampaignId());
                        if (APP_PROMOTIONS.contains(lauCampaignPo.getPromotionPurposeType())) {
                            unitPromotionContentEnum = UnitDetailPromotionContentEnum.APP_PACKAGE;
                        }
                    }
                    break;
                case ON_SHELF_GAME: // 安卓游戏
                    unitPromotionContentEnum = UnitDetailPromotionContentEnum.ON_SHELF_GAME;
                    break;
                case GAME_CARD: // 游戏卡
                    unitPromotionContentEnum = UnitDetailPromotionContentEnum.ANDROID_GAME;
                    break;
                case APP_DOWNLOAD: // 应用推广
                case LANDING_PAGE: // 销售线索收集
                case SALE_GOODS: // 店铺推广
                    unitPromotionContentEnum = UnitDetailPromotionContentEnum.APP_PACKAGE;
                    break;
                case SHOP_GOODS: // 会员购
                    unitPromotionContentEnum = UnitDetailPromotionContentEnum.MEMBER_SHOP_GOODS;
                    break;
                case DYNAMIC: // 动态
                    unitPromotionContentEnum = UnitDetailPromotionContentEnum.DYNAMIC;
                    break;
                case LIVE_ROOM:
                case GOODS_LIVE: // 品牌传播-带货直播22，直播推广下直播间
                    unitPromotionContentEnum = UnitDetailPromotionContentEnum.LIVE_ROOM;

                    // 直播预约
                    LiveReservationInfoBo liveReservationInfoBo = liveReservationInfoBoMap.get(unitReportVo.getUnitId());
                    if (liveReservationInfoBo != null) {
                        unitPromotionContentEnum = UnitDetailPromotionContentEnum.LIVE_RESERVE;
                    }
                    break;
                case LIVE_RESERVE:
                    unitPromotionContentEnum = UnitDetailPromotionContentEnum.LIVE_ROOM;

                    // 直播预约
                    liveReservationInfoBo = liveReservationInfoBoMap.get(unitReportVo.getUnitId());
                    if (liveReservationInfoBo != null) {
                        unitPromotionContentEnum = UnitDetailPromotionContentEnum.LIVE_RESERVE;
                    }
                    break;
                case GOODS: // 商品
                    unitPromotionContentEnum = UnitDetailPromotionContentEnum.LIVE_GOODS;
                    break;
                case OGV:
                    unitPromotionContentEnum = UnitDetailPromotionContentEnum.OGV;
                    break;
                case ENTERPRISE_PROMOTION:
                    unitPromotionContentEnum = UnitDetailPromotionContentEnum.LANDING_PAGE;
                    break;
                default:
                    // 落地页, 游戏活动卡，商品目录等其他类别: 什么都不做

            }
            unitReportVo.setUnitDetailPromotionContentEnum(unitPromotionContentEnum);
        }
    }
}
