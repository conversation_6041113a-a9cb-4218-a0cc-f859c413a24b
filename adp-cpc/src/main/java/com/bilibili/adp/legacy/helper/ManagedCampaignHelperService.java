package com.bilibili.adp.legacy.helper;

import com.bilibili.adp.cpc.biz.services.ocpx.compensate.managed.IOcpxManagedAutoPayService;
import com.bilibili.adp.launch.api.common.OcpxAutoPayStatus;
import com.bilibili.adp.launch.api.unit.dto.UnitOcpxFloatStatusDto;
import com.bilibili.adp.legacy.bo.UnitOcpxFloatStatusBo;
import com.bilibili.adp.legacy.bo.CampaignReportDetailBo;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 复制方法，方法从web模块下沉到service模块
 *
 * @see com.bilibili.adp.advertiser.portal.webapi.flyPro.v2.helper.ManagedCampaignHelper
 */
@Component
public class ManagedCampaignHelperService {

    @Value("#{'${cpc.ocpx.auto.pay.black.campaign.ids:}'.empty ? null : '${cpc.ocpx.auto.pay.black.campaign.ids:}'.split(',')}")
    private List<Integer> blackCampaignIds;

    @Autowired
    private IOcpxManagedAutoPayService ocpxManagedAutoPayService;


    public void setOcpxPayInfo(CampaignReportDetailBo vo) {
        // 增加ocpx自动赔付的状态
        OcpxAutoPayStatus finalStatus;
        // 计划处于二阶段且在黑名单内
        if (!CollectionUtils.isEmpty(blackCampaignIds) && blackCampaignIds.contains(vo.getCampaignId())) {
            finalStatus = OcpxAutoPayStatus.UNKNOWN;
        } else {
            finalStatus = ocpxManagedAutoPayService.getFinalStatus(vo.getCampaignId());
        }
        vo.setOcpxAutoPayFinalStatus(finalStatus.getDesc());
        vo.setCampaignOcpxAutoPayFinalStatus(finalStatus.getDesc());
        Pair<String, List<UnitOcpxFloatStatusDto>> pair = ocpxManagedAutoPayService.getFloatStatusList(vo.getCampaignId(), finalStatus);
        List<UnitOcpxFloatStatusDto> floatStatusList = pair.getValue();
        List<UnitOcpxFloatStatusBo> floatStatusVoList = new ArrayList<>();
        floatStatusList.forEach(floatStatusDto -> {
            floatStatusVoList.add(UnitOcpxFloatStatusBo.builder()
                    .autoPayPeriod(floatStatusDto.getAutoPayPeriod())
                    .autoPayStatus(floatStatusDto.getAutoPayStatus())
                    .autoPayPeriodDate(floatStatusDto.getAutoPayPeriodDate())
                    .build());
        });
        vo.setOcpxAutoPayFloatStatusList(floatStatusVoList);
        vo.setTwoStageTime(pair.getLeft());
    }
}
