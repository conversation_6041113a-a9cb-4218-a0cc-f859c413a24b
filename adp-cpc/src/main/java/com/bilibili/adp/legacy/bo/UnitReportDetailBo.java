package com.bilibili.adp.legacy.bo;

import com.bilibili.adp.cpc.enums.ad.UnitDetailPromotionContentEnum;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonUnwrapped;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;


/**
 * 复制的代码，从web模块下沉到service模块
 *
 * @see com.bilibili.adp.advertiser.portal.webapi.v2.vo.responses.UnitReportVo
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class UnitReportDetailBo {

    private int adpVersion;
    private String adpVersionDesc;

    private Integer speedMode;

    private Integer accountId;
    private Integer campaignId;
    private String campaignName;
    private Integer unitId;
    private String unitName;

    private Long videoId;


    private Integer promotionPurposeType;
    private String promotionContentTypeDesc;
    private BigDecimal costPrice;

    private Timestamp ctime;
    private Integer isNoBid;
    // nobid 上限
    private BigDecimal noBidMax;

    private BigDecimal budget;

    @ApiModelProperty(value = "单元预算过低提醒")
    private Boolean unitBudgetLowHint;
    @ApiModelProperty(value = "单元是否有可用带货链接")

    private Integer ocpcTarget;

    private Integer hasNextdayBudget;

    private BigDecimal nextdayBudget;
    /**
     * 次日预算是否开启重复
     */
    private Integer isRepeat;
    private Integer status;
    private String statusDesc;

    private String unitStatusMtime;
    private String beginDate;
    private String endDate;
    private Integer salesType;
    private String salesTypeDesc;

    //由于 cpc ocpc 的 sales type相同，所以用sales mode来区分

    private Integer salesMode;

    private String salesModeDesc;

    private List<Integer> extraStatus;
    private List<String> extraStatusDesc;

    private Integer slotGroupId;

    private String slotGroupName;

    private BigDecimal beforeBudget;

    private Long budgetRemainingModifyTimes;

    private Integer isHistory;

    private String createTime;

    private Integer isProgrammatic;

    private Integer ocpxStage;
    private String ocpxStageDesc;
    private BigDecimal twoStageBid;
    private Integer ocpxTargetId;
    private String ocpxTargetDesc;
    private String osDesc;

    private Boolean underbid;
    @ApiModelProperty("oCPX第一目标出价")
    private BigDecimal ocpxTargetOneBid;
    @ApiModelProperty("oCPX第二目标")
    private Integer ocpxTargetTwo;
    @ApiModelProperty("oCPX第二目标描述")
    private String ocpxTargetTwoDesc;
    @ApiModelProperty("oCPX第二目标出价")
    private BigDecimal ocpxTargetTwoBid;
    @ApiModelProperty("oCPX第二目标阶段")
    private String ocpxTargetTwoStageDesc;

    private String ocpxAutoPayFinalStatus;
    private List<UnitOcpxFloatStatusBo> ocpxAutoPayFloatStatusList;
    private Integer unitPeriodType;

    private String twoStageTime;

    private Integer subPkgStatus;
    private String avId;
    private String bvId;

    /**
     * 动态详情页浏览数
     */
    private Integer dynamicDetailPageBrowseCount;
    /**
     * 动态详情页浏览成本
     */
    private BigDecimal dynamicDetailPageBrowseCost;
    /**
     * 动态详情页浏览率
     */
    private BigDecimal dynamicDetailPageBrowseRate;
    /**
     * 活动页浮层拉起数
     */
    private Integer activityPagePullUpCount;
    /**
     * 活动页浮层拉起成本(元)
     */
    private BigDecimal activityPagePullUpCost;
    /**
     * 活动页浮层拉起率(元)
     */
    private BigDecimal activityPagePullUpRate;


    private Boolean canCopy;
    /**
     * 是否托管
     */

    private boolean isManaged;
    private Integer isManagedVal;
    @JsonUnwrapped
    private ReportSummaryBo reportSummary;


    private Integer assistTarget;
    private String assistTargetDesc;
    private BigDecimal assistPrice;
    private Integer assistInSearch;
    private String assistSearchEndDate;

    @ApiModelProperty(value = "商品实际状态=审核通过 & 启用")
    private Integer goodsIsValid;

    private UnitDetailPromotionContentEnum unitDetailPromotionContentEnum;
    @ApiModelProperty(value = "内容详情")
    private String contentDetail;
    private Integer dualBidTwoStageOptimization;
    private String dualBidTwoStageOptimizationDesc;

    private String searchPriceCoefficient;
    private String searchFirstPriceCoefficient;
    /**
     * 单元起量状态
     */
    private Integer unitAccelerateStatus;

    //public static UnitReportVo from(CpcUnitReportVo vo) {
    //    final CpcReportCommonColumnVo commonColumnVo = vo.getCpcReportCommonColumnVo();
    //    if (Objects.isNull(commonColumnVo)) {
    //        return null;
    //    }
    //    final ReportSummaryVo summaryVo = ReportSummaryVo.fromCpcReportCommonColumnVo(commonColumnVo).update();
    //
    //    UnitReportVo unitReportVo = UnitReportVo.builder()
    //            .adpVersion(vo.getAdpVersion())
    //            .videoId(vo.getVideoId())
    //            .accountId(vo.getAccountId())
    //            .campaignId(vo.getCampaignId())
    //            .campaignName(vo.getCampaignName())
    //            .unitId(vo.getUnitId())
    //            .unitName(vo.getUnitName())
    //            .ctime(vo.getCtime())
    //            .promotionPurposeType(vo.getPromotionPurposeType())
    //            .costPrice(vo.getCostPrice())
    //            .isNoBid(vo.getIsNoBid())
    //            .noBidMax(vo.getNoBidMax())
    //            .budget(vo.getBudget())
    //            .hasNextdayBudget(vo.getHasNextdayBudget())
    //            .nextdayBudget(vo.getNextdayBudget())
    //            .isRepeat(vo.getIsRepeat())
    //            .status(vo.getStatus())
    //            .statusDesc(vo.getStatusDesc())
    //            .unitStatusMtime(vo.getUnitStatusMtime())
    //            .beginDate(vo.getBeginDate())
    //            .endDate(vo.getEndDate())
    //            .salesType(vo.getSalesType())
    //            .salesTypeDesc(vo.getSalesTypeDesc())
    //            .salesMode(vo.getSalesMode())
    //            .salesModeDesc(vo.getSalesModeDesc())
    //            .extraStatus(vo.getExtraStatus())
    //            .extraStatusDesc(vo.getExtraStatusDesc())
    //            .ocpcTarget(vo.getOcpcTarget())
    //            .slotGroupId(vo.getSlotGroupId())
    //            .slotGroupName(vo.getSlotGroupName())
    //            .isHistory(vo.getIsHistory())
    //            .beforeBudget(vo.getBeforeBudget())
    //            .budgetRemainingModifyTimes(vo.getBudgetRemainingModifyTimes())
    //            .ocpxStage(vo.getOcpxStage())
    //            .ocpxStageDesc(vo.getOcpxStageDesc())
    //            .ocpxTargetId(vo.getOcpxTargetId())
    //            .ocpxTargetDesc(vo.getOcpxTargetDesc())
    //            .twoStageBid(vo.getTwoStageBid())
    //            .ocpxTargetOneBid(vo.getOcpxTargetOneBid())
    //            .ocpxTargetTwo(vo.getOcpxTargetTwo())
    //            .ocpxTargetTwoDesc(vo.getOcpxTargetTwoDesc())
    //            .ocpxTargetTwoBid(vo.getOcpxTargetTwoBid())
    //            .ocpxTargetTwoStageDesc(vo.getOcpxTargetTwoStageDesc())
    //            .osDesc(vo.getOsDesc())
    //            .underbid(vo.getUnderbid())
    //            .subPkgStatus(vo.getSubPkgStatus())
    //            .reportSummaryVo(summaryVo)
    //            .ocpxAutoPayFinalStatus(vo.getOcpxAutoPayFinalStatus())
    //            .ocpxAutoPayFloatStatusList(vo.getOcpxAutoPayFloatStatusList())
    //            .unitPeriodType(vo.getUnitPeriodType())
    //            .twoStageTime(vo.getTwoStageTime())
    //            .dynamicDetailPageBrowseCount(vo.getDynamicDetailPageBrowseCount())
    //            .dynamicDetailPageBrowseCost(vo.getDynamicDetailPageBrowseCost())
    //            .dynamicDetailPageBrowseRate(vo.getDynamicDetailPageBrowseRate())
    //            .activityPagePullUpCount(vo.getActivityPagePullUpCount())
    //            .activityPagePullUpCost(vo.getActivityPagePullUpCost())
    //            .activityPagePullUpRate(vo.getActivityPagePullUpRate())
    //            .isManaged(vo.isManaged())
    //            .createTime(vo.getCreateTime())
    //            .isProgrammatic(vo.getIsProgrammatic())
    //            .unitBudgetLowHint(false)
    //            .build();
    //    BeanUtils.copyProperties(summaryVo, unitReportVo);
    //    return unitReportVo;
    //}

    private Integer unitPromotionPurposeType;

    /**
     * 是否支持自动化，与单元对应的campaign的support_auto值一致
     */
    private Integer supportAuto;
    private String supportAutoDesc;

    private Integer availableCreativesCount;
}
