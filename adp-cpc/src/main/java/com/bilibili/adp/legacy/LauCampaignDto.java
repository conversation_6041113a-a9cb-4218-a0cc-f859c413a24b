package com.bilibili.adp.legacy;

import com.bilibili.adp.common.annotation.DatabaseColumnEnum;
import com.bilibili.adp.common.annotation.DatabaseColumnName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LauCampaignDto implements Serializable {

    private static final long serialVersionUID = -8033952291186719153L;
    private Integer campaignId;

    private Integer accountId;

    @DatabaseColumnName("推广计划名称")
    private String campaignName;

    private Integer costType;

    private Integer budgetType;

    @DatabaseColumnName("推广预算")
    private Long budget;

    @DatabaseColumnName("投放模式")
    @DatabaseColumnEnum("{'1':'匀速投放','2':'加速投放'}")
    private Integer speedMode;

    @DatabaseColumnName("状态")
    @DatabaseColumnEnum("{'1':'有效','2':'暂停','3':'删除'}")
    private Integer status;

    private Integer isDeleted;

    private Timestamp ctime;

    private Timestamp mtime;

    private Integer salesType;

    private Integer orderId;

    private Integer campaignStatus;

    @DatabaseColumnName("推广目的类型")
    @DatabaseColumnEnum("{'2':'销售线索收集','4':'应用推广','5':'会员购','6':'游戏','7':'投稿内容','8':'直播间','9':'电商','10':'bilibili视频','11':'商品目录','12':'企业号推广'}")
    private Integer promotionPurposeType;

    private Integer lauAccountId;

    private Integer unitCount;

    @DatabaseColumnName("预算限制类型")
    @DatabaseColumnEnum("{'1':'指定预算', '2':'不限预算', '3':'总预算'}")
    private Integer budgetLimitType;

    @DatabaseColumnName("次日预算")
    private Integer nextdayBudget;

    @DatabaseColumnName("次日预算限制类型")
    @DatabaseColumnEnum("{'1':'指定预算', '2':'不限预算', '3':'总预算'}")
    private Integer nextdayBudgetLimitType;

    private Integer flag;

    @DatabaseColumnName("是否定价保量")
    @DatabaseColumnEnum("{'0':'否', '1':'是'}")
    private Integer isGdPlus;
}
