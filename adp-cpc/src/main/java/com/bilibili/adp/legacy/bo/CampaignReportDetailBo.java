package com.bilibili.adp.legacy.bo;

import com.fasterxml.jackson.annotation.JsonUnwrapped;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;


/**
 * 复制的代码，从web模块下沉到service模块
 *
 * @see com.bilibili.adp.advertiser.portal.webapi.v2.vo.responses.CampaignReportVo
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CampaignReportDetailBo {

    private int adpVersion;
    private String adpVersionDesc;
    private int campaignId;
    private String campaignName;
    private int promotionPurposeType;
    private String promotionPurposeTypeDesc;
    private BigDecimal budget;
    private Integer budgetLimitType;

    private Integer hasNextdayBudget;

    private BigDecimal nextdayBudget;

    private Integer nextdayBudgetLimitType;
    /**
     * 次日预算是否开启重复
     */
    private Integer isRepeat;

    private int speedMode;
    private int status;
    private String statusDesc;

    private String campaignStatusMtime;
    private List<Integer> extraStatus;
    private List<String> extraStatusDesc;

    private BigDecimal beforeBudget;

    private long budgetRemainingModifyTimes;
    /**
     * 创建时间，格式为：yyyy-MM-dd HH:mm:ss
     */
    private String createTime;

    /**
     * 是否托管
     */

    private boolean isManaged;
    private Integer isManagedVal;
    /**
     * 托管计划创建状态
     */

    private int managedCreatingStatus;

    /**
     * 对应前端传参 预算类型:0-不限 1-日预算 2-总预算
     */
    private int budgetType;

    /**
     * 是否GD+
     */

    private Boolean is_gd_plus;

    /**
     * 活动拉起次数
     */
    private Integer activityPagePullUpCount;

    /**
     * 活动拉起成本
     */
    private BigDecimal activityPagePullUpCost;
    private BigDecimal activityPagePullUpRate;

    private Integer dynamicDetailPageBrowseCount;
    private BigDecimal dynamicDetailPageBrowseCost;
    private BigDecimal dynamicDetailPageBrowseRate;


    private String campaignOcpxAutoPayFinalStatus = "--";

    private String ocpxAutoPayFinalStatus = "--";

    private List<UnitOcpxFloatStatusBo> ocpxAutoPayFloatStatusList;

    private String twoStageTime;

    private String campaignAdType;

    /**
     * 托管的版本
     *
     * @see com.bilibili.adp.cpc.enums.ManagedVersion
     */
    private Integer managedVersion;

    private Integer supportAuto;
    private String supportAutoDesc;
    private Integer availableCreativesCount;
    /**
     * 统计指标，此处用了将对象扁平的注解: @JsonUnwrapped
     */
    @JsonUnwrapped
    private ReportSummaryBo reportSummary;

    //public static CampaignReportDetailBo from(CpcCampaignReportVo vo) {
    //    final CpcReportCommonColumnVo commonColumnVo = vo.getCpcReportCommonColumnVo();
    //    if (Objects.isNull(commonColumnVo)) {
    //        return null;
    //    }
    //    final ReportSummaryVo summaryVo = ReportSummaryVo.fromCpcReportCommonColumnVo(commonColumnVo);
    //    summaryVo.update();
    //
    //    summaryVo.setActivityPagePullUpCount(vo.getActivityPagePullUpCount());
    //    summaryVo.setActivityPagePullUpCost(vo.getActivityPagePullUpCost());
    //    summaryVo.setActivityPagePullUpRate(vo.getActivityPagePullUpRate());
    //    //summaryVo.setDynamicDetailPageBrowseRate(vo.getDynamicDetailPageBrowseRate());
    //
    //    CampaignReportDetailBo campaignReportVo = new CampaignReportDetailBo();
    //            campaignReportVo.setAdpVersion(vo.getAdp_version());
    //            campaignReportVo.setCampaignId(vo.getCampaign_id());
    //            campaignReportVo.setCampaignName(vo.getCampaign_name());
    //            campaignReportVo.setPromotionPurposeType(vo.getPromotion_purpose_type());
    //            campaignReportVo.setPromotionPurposeTypeDesc(vo.getPromotion_purpose_type_desc());
    //            campaignReportVo.setBudget(vo.getBudget());
    //            campaignReportVo.setBudgetLimitType(vo.getBudget_limit_type());
    //            campaignReportVo.setHasNextdayBudget(vo.getHas_nextday_budget());
    //            campaignReportVo.setNextdayBudget(vo.getNextday_budget());
    //            campaignReportVo.setNextdayBudgetLimitType(vo.getNextday_budget_limit_type());
    //            campaignReportVo.setIsRepeat(vo.getIs_repeat());
    //            campaignReportVo.setSpeedMode(vo.getSpeed_mode());
    //            campaignReportVo.setStatus(vo.getStatus());
    //            campaignReportVo.setStatusDesc(vo.getStatus_desc());
    //            campaignReportVo.setCampaignStatusMtime(vo.getCampaign_status_mtime());
    //            campaignReportVo.setExtraStatus(vo.getExtra_status());
    //            campaignReportVo.setExtraStatusDesc(vo.getExtra_status_desc());
    //            campaignReportVo.setBeforeBudget(vo.getBefore_budget());
    //            campaignReportVo.setBudgetRemainingModifyTimes(vo.getBudget_remaining_modify_times());
    //            campaignReportVo.setReportSummaryVo(summaryVo);
    //            campaignReportVo.setActivityPagePullUpCount(vo.getActivityPagePullUpCount());
    //            campaignReportVo.setActivityPagePullUpCost(vo.getActivityPagePullUpCost());
    //            campaignReportVo.setActivityPagePullUpRate(vo.getActivityPagePullUpRate());
    //            //campaignReportVo.setDynamicDetailPageBrowseCount(vo.getDynamicDetailPageBrowseCount());
    //            //campaignReportVo.setDynamicDetailPageBrowseCost(vo.getDynamicDetailPageBrowseCost());
    //            //campaignReportVo.setDynamicDetailPageBrowseRate(vo.getDynamicDetailPageBrowseRate());
    //            campaignReportVo.setOcpxAutoPayFinalStatus(vo.getOcpxAutoPayFinalStatus());
    //            campaignReportVo.setCampaignOcpxAutoPayFinalStatus(vo.getCampaignOcpxAutoPayFinalStatus());
    //            campaignReportVo.setOcpxAutoPayFloatStatusList(vo.getOcpxAutoPayFloatStatusList());
    //            campaignReportVo.setTwoStageTime(vo.getTwoStageTime());
    //            campaignReportVo.setCreateTime(vo.getCreate_time());
    //            campaignReportVo.setCampaignAdType(vo.getCampaignAdType());
    //
    //    BeanUtils.copyProperties(summaryVo, campaignReportVo);
    //    return campaignReportVo;
    //}

}
