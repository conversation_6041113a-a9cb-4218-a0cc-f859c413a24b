package com.bilibili.adp.legacy.converters;

import com.bilibili.adp.cpc.biz.bos.report.CampaignReportBo;
import com.bilibili.adp.cpc.biz.bos.report.CreativeReportBo;
import com.bilibili.adp.cpc.biz.bos.report.ReportDetailBo;
import com.bilibili.adp.cpc.biz.bos.report.UnitReportBo;
import com.bilibili.adp.cpc.enums.ad.OcpcTargetEnum;
import com.bilibili.adp.legacy.bo.*;
import com.bilibili.adp.v6.report.bo.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.Objects;

@Mapper(uses = {StatServiceConverter.class})
public interface ReportServiceConverter {
    ReportServiceConverter MAPPER = Mappers.getMapper(ReportServiceConverter.class);


    @Mapping(target = "isMapi", source = "mapi")
    @Mapping(target = "appkey", ignore = true)
    @Mapping(target = "pageSize", ignore = true)
    @Mapping(target = "page", expression = "java(1)")
    QueryCampaignReportBo toBo(ExportCampaignReportBo query);

    @Mapping(target = "appkey", ignore = true)
    @Mapping(target = "page", expression = "java(1)")
    @Mapping(target = "pageSize", ignore = true)
    @Mapping(target = "isMapi", source = "mapi")
    QueryUnitReportBo toBo(ExportUnitReportBo query);

    @Mapping(target = "querySameUnitCreatives", expression = "java(0)")
    @Mapping(target = "appkey", ignore = true)
    @Mapping(target = "page", expression = "java(1)")
    @Mapping(target = "pageSize", ignore = true)
    @Mapping(target = "isMapi", source = "mapi")
    QueryCreativeReportBo toBo(ExportCreativeReportBo query);

    /**
     * convert CampaignReportBo to CampaignReportVo
     * @param campaignReportBo bo
     * @return CampaignReportVo vo
     */
    @Mapping(target = "twoStageTime", ignore = true)
    @Mapping(target = "ocpxAutoPayFloatStatusList", ignore = true)
    @Mapping(target = "ocpxAutoPayFinalStatus", ignore = true)
    @Mapping(target = "managedCreatingStatus", ignore = true)
    @Mapping(target = "is_gd_plus", ignore = true)
    @Mapping(target = "isManaged", source = "managed")
    @Mapping(target = "dynamicDetailPageBrowseRate", ignore = true)
    @Mapping(target = "dynamicDetailPageBrowseCount", ignore = true)
    @Mapping(target = "dynamicDetailPageBrowseCost", ignore = true)
    @Mapping(target = "budgetType", ignore = true)
    @Mapping(target = "activityPagePullUpRate", ignore = true)
    @Mapping(target = "activityPagePullUpCount", ignore = true)
    @Mapping(target = "activityPagePullUpCost", ignore = true)
    @Mapping(target = "reportSummary", source = "stat")
    CampaignReportDetailBo bo2Vo(CampaignReportBo campaignReportBo);

    /**
     * convert UnitReportBo to UnitReportVo
     * @param unitReportBo bo
     * @return UnitReportVo vo
     */
    @Mapping(target = "unitBudgetLowHint", ignore = true)
    @Mapping(target = "isManaged", source = "managed")
    @Mapping(target = "dynamicDetailPageBrowseRate", ignore = true)
    @Mapping(target = "dynamicDetailPageBrowseCount", ignore = true)
    @Mapping(target = "dynamicDetailPageBrowseCost", ignore = true)
    @Mapping(target = "activityPagePullUpRate", ignore = true)
    @Mapping(target = "activityPagePullUpCount", ignore = true)
    @Mapping(target = "activityPagePullUpCost", ignore = true)
    @Mapping(target = "reportSummary",
            expression = "java(com.bilibili.adp.legacy.converters.ReportServiceConverter.generateSummaryBo(unitReportBo))")
    @Mapping(target = "unitPromotionPurposeType", source = "promotionPurposeType")
    @Mapping(target = "ocpxTargetOneBid",
            expression = "java(com.bilibili.adp.cpc.utils.OcpxTargetBidUtil.generateOcpxTargetBidValueWith24RoiHandle(unitReportBo.getOcpxTargetOneBid(), unitReportBo.getOcpcTarget()))")
    @Mapping(target = "twoStageBid",
            expression = "java(com.bilibili.adp.cpc.utils.OcpxTargetBidUtil.generateOcpxTargetBidValueWith24RoiHandle(unitReportBo.getTwoStageBid(), unitReportBo.getOcpcTarget()))")
    @Mapping(target = "ocpxTargetDesc",
            expression = "java(com.bilibili.adp.legacy.converters.ReportServiceConverter.getOcpxTargetDesc(unitReportBo.getOcpcTarget(), unitReportBo.getOcpxTargetDesc()))")
    UnitReportDetailBo bo2Vo(UnitReportBo unitReportBo);

    static ReportSummaryBo generateSummaryBo(UnitReportBo bo) {
        ReportSummaryBo reportSummaryBo = StatServiceConverter.MAPPER.statBo2SummaryVo(bo.getStat());
        if (Objects.isNull(reportSummaryBo)) {
            return null;
        }
        reportSummaryBo.setIsRepeat(bo.getIsRepeat());
        return reportSummaryBo;
    }

    static String getOcpxTargetDesc(Integer ocpxTarget, String originDesc) {
        if (Objects.equals(ocpxTarget, OcpcTargetEnum.PAID_IN_7D_COST.getCode())) {
            return "每次付费";
        }
        return originDesc;
    }

    /**
     * convert CreativeReportBo to CreativeReportVo
     * @param creativeReportBo bo
     * @return CreativeReportVo vo
     */
    @Mapping(target = "accessUrl", ignore = true)
    @Mapping(target = "unitBudgetLowHint", ignore = true)
    @Mapping(target = "is_gd_plus", ignore = true)
    @Mapping(target = "hasAuditingShadowCreative", ignore = true)
    @Mapping(target = "dynamicDetailPageBrowseRate", ignore = true)
    @Mapping(target = "dynamicDetailPageBrowseCount", ignore = true)
    @Mapping(target = "dynamicDetailPageBrowseCost", ignore = true)
    @Mapping(target = "components", ignore = true)
    @Mapping(target = "budgetRemainingModifyTimes", ignore = true)
    @Mapping(target = "beforeBudget", ignore = true)
    @Mapping(target = "activityPagePullUpRate", ignore = true)
    @Mapping(target = "activityPagePullUpCount", ignore = true)
    @Mapping(target = "activityPagePullUpCost", ignore = true)
    @Mapping(target = "isProgrammatic", source = "programmatic")
    @Mapping(target = "reportSummary", source = "stat")
    @Mapping(target = "secondAuditStatus",source = "secondAuditStatus")
    @Mapping(target = "secondAuditStatusDesc",source = "secondAuditStatusDesc")
    CreativeReportDetailBo bo2Vo(CreativeReportBo creativeReportBo);

    /**
     * convert report detail bo to report by type vo
     * @param reportDetailBo report detail bo
     * @return ReportByTypeVo report by type vo
     */
    @Mapping(target = "dynamicDetailPageBrowseRate", ignore = true)
    @Mapping(target = "dynamicDetailPageBrowseCount", ignore = true)
    @Mapping(target = "dynamicDetailPageBrowseCost", ignore = true)
    @Mapping(target = "activityPagePullUpRate", ignore = true)
    @Mapping(target = "activityPagePullUpCount", ignore = true)
    @Mapping(target = "activityPagePullUpCost", ignore = true)
    @Mapping(target = "reportSummary", source = "stat")
    ReportByTypeBo bo2Vo(ReportDetailBo reportDetailBo);
}
