package com.bilibili.adp.cpc.biz.services.unit.dto;

import com.bilibili.bsi.common.utils.TimeUtils;
import junit.framework.TestCase;
import org.junit.Assert;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class UnitStatusESDtoConverterTest extends TestCase {

    public void testTransBeginTime_1() {
        String beginTime = "2025-01-17";
        String launchTime = "111111111111111111111111111111111111111111111111111111111111111111111111000000000000000000000000000000001111001111100000111111111111111111111111111111111111111111111111";

        Date date1 = UnitStatusESDtoConverter.INSTANCE.transBeginTime(beginTime, launchTime);
        Assert.assertEquals(TimeUtils.parseDate("2025-01-17 08:00:00", "yyyy-MM-dd hh:mm:ss"), date1);
    }

    public void testTransBeginTime_2() {
        String beginTime = "2025-01-16";
        String launchTime = "111111111111111111111111111111111111111111111111111111111111111111111111000000000000000000000000000000001111001111100000111111111111111111111111111111111111111111111111";

        Date date1 = UnitStatusESDtoConverter.INSTANCE.transBeginTime(beginTime, launchTime);
        Assert.assertEquals(TimeUtils.parseDate("2025-01-17 00:00:00.00", "yyyy-MM-dd hh:mm:ss"), date1);
    }

    public void testTransBeginTime_3() {
        String beginTime = "2025-01-15";
        String launchTime = "111111111111111111111111111111111111111111111111111111111111111111111111000000000000000000000000000000001111001111100000111111111111111111111111111111111111111111111111";

        Date date1 = UnitStatusESDtoConverter.INSTANCE.transBeginTime(beginTime, launchTime);
        Assert.assertEquals(TimeUtils.parseDate("2025-01-15 00:00:00.00", "yyyy-MM-dd hh:mm:ss"), date1);
    }

    public void testTransBeginTime_4() {
        String beginTime = "2025-01-19";
        String launchTime = "111111111111111111111111111111111111111111111111111111111111111111111111000000000000000000000000000000001111001111100000111111111111111111111111111111111111111111111111";

        Date date1 = UnitStatusESDtoConverter.INSTANCE.transBeginTime(beginTime, launchTime);
        Assert.assertEquals(TimeUtils.parseDate("2025-01-19 00:00:00.00", "yyyy-MM-dd hh:mm:ss"), date1);
    }

    public void testTransBeginTime_5() {
        String beginTime = "2025-01-17";
        String launchTime = "";

        Date date1 = UnitStatusESDtoConverter.INSTANCE.transBeginTime(beginTime, launchTime);
        Assert.assertEquals(TimeUtils.parseDate("2025-01-17 00:00:00.00", "yyyy-MM-dd hh:mm:ss"), date1);
    }

    public void testTransBeginTime_6() {
        String beginTime = "2025-01-12";
        String launchTime = "111111111111111111111111111111111111111111111111111111111111111111111111000000000000000000000000000000001111001111100000111111111111111111111111111111111111111111111111";

        Date date1 = UnitStatusESDtoConverter.INSTANCE.transBeginTime(beginTime, launchTime);
        Assert.assertEquals(TimeUtils.parseDate("2025-01-12 00:00:00.00", "yyyy-MM-dd hh:mm:ss"), date1);
    }

    public void testTransEndTime_1() {
        String endTime = "2025-01-17";
        String launchTime = "111111111111111111111111111111111111111111111111111111111111111111111111000000000000000000000000000000001111001111100000111111111111111111111111111111111111111111111111";

        Date date1 = UnitStatusESDtoConverter.INSTANCE.transEndTime(endTime, launchTime);
        Assert.assertEquals(TimeUtils.parseDate("2025-01-17 18:59:59.00", "yyyy-MM-dd hh:mm:ss"), date1);
    }

    public void testTransEndTime_2() {
        String endTime = "2025-01-18";
        String launchTime = "111111111111111111111111111111111111111111111111111111111111111111111111000000000000000000000000000000001111001111100000111111111111111111111111111111111111111111111111";

        Date date1 = UnitStatusESDtoConverter.INSTANCE.transEndTime(endTime, launchTime);
        Assert.assertEquals(TimeUtils.parseDate("2025-01-18 23:59:59.00", "yyyy-MM-dd hh:mm:ss"), date1);
    }

    public void testTransEndTime_3() {
        String endTime = "2025-01-19";
        String launchTime = "111111111111111111111111111111111111111111111111111111111111111111111111000000000000000000000000000000001111001111100000111111111111111111111111111111111111111111111111";

        Date date1 = UnitStatusESDtoConverter.INSTANCE.transEndTime(endTime, launchTime);
        Assert.assertEquals(TimeUtils.parseDate("2025-01-18 23:59:59.00", "yyyy-MM-dd hh:mm:ss"), date1);
    }

    public void testTransEndTime_4() {
        String endTime = "2025-01-12";
        String launchTime = "111111111111111111111111111111111111111111111111111111111111111111111111000000000000000000000000000000001111001111100000111111111111111111111111111111111111111111111111";

        Date date1 = UnitStatusESDtoConverter.INSTANCE.transEndTime(endTime, launchTime);
        Assert.assertEquals(TimeUtils.parseDate("2025-01-12 23:59:59.00", "yyyy-MM-dd hh:mm:ss"), date1);
    }

    public void testTransEndTime_5() {
        String endTime = "2025-01-16";
        String launchTime = "111111111111111111111111111111111111111111111111111111111111111111111111000000000000000000000000000000001111001111100000111111111111111111111111111111111111111111111111";

        Date date1 = UnitStatusESDtoConverter.INSTANCE.transEndTime(endTime, launchTime);
        Assert.assertEquals(TimeUtils.parseDate("2025-01-15 23:59:59.00", "yyyy-MM-dd hh:mm:ss"), date1);
    }

    public void testTransTime_1() {
        String launchTime = "";
        String s = UnitStatusESDtoConverter.INSTANCE.transTime(launchTime);
        Assert.assertEquals("", s);
    }

    public void testTransTime_2() {
        String launchTime = "111111111111111111111111111111111111111111111111111111111111111111111111000000000000000000000000000000001111001111100000111111111111111111111111111111111111111111111111";
        String s = UnitStatusESDtoConverter.INSTANCE.transTime(launchTime);
        Assert.assertEquals(
                "1-00, 1-01, 1-02, 1-03, 1-04, 1-05, 1-06, 1-07, 1-08, 1-09, 1-10, 1-11, 1-12, 1-13, 1-14, 1-15, 1-16, 1-17, 1-18, 1-19, 1-20, 1-21, 1-22, 1-23, 2-00, 2-01, 2-02, 2-03, 2-04, 2-05, 2-06, 2-07, 2-08, 2-09, 2-10, 2-11, 2-12, 2-13, 2-14, 2-15, 2-16, 2-17, 2-18, 2-19, 2-20, 2-21, 2-22, 2-23, 3-00, 3-01, 3-02, 3-03, 3-04, 3-05, 3-06, 3-07, 3-08, 3-09, 3-10, 3-11, 3-12, 3-13, 3-14, 3-15, 3-16, 3-17, 3-18, 3-19, 3-20, 3-21, 3-22, 3-23, 5-08, 5-09, 5-10, 5-11, 5-14, 5-15, 5-16, 5-17, 5-18, 6-00, 6-01, 6-02, 6-03, 6-04, 6-05, 6-06, 6-07, 6-08, 6-09, 6-10, 6-11, 6-12, 6-13, 6-14, 6-15, 6-16, 6-17, 6-18, 6-19, 6-20, 6-21, 6-22, 6-23, 7-00, 7-01, 7-02, 7-03, 7-04, 7-05, 7-06, 7-07, 7-08, 7-09, 7-10, 7-11, 7-12, 7-13, 7-14, 7-15, 7-16, 7-17, 7-18, 7-19, 7-20, 7-21, 7-22, 7-23",
                s);
    }

    public void test() {
        List<Integer> dayBudgetUnitIds = new ArrayList<>();
        dayBudgetUnitIds.add(1);
        boolean empty = CollectionUtils.isEmpty(dayBudgetUnitIds);
        System.out.println(empty);
    }
}