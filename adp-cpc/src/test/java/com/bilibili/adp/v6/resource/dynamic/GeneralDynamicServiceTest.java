package com.bilibili.adp.v6.resource.dynamic;

import com.bilibili.adp.cpc.biz.services.creative.CpcSaveCreativeService;
import com.bilibili.adp.cpc.enums.archive.ArchiveShareScopeEnum;
import com.bilibili.adp.launch.api.creative.dto.CpcCreativeMonitoringDto;
import com.bilibili.adp.web.framework.core.Context;
import com.querydsl.core.types.dsl.BooleanExpression;
import org.junit.Test;

import java.util.Calendar;
import java.util.Random;

import static com.bilibili.adp.cpc.dao.querydsl.QLauArchiveAuthorizedRelation.lauArchiveAuthorizedRelation;
//import org.mockito.Mock;


public class GeneralDynamicServiceTest {
    private CommercialOrderDynamicService commercialOrderDynamicService;

    //    @InjectMocks
    private GeneralDynamicService generalDynamicService;

    private BooleanExpression genShareExpr(Integer accountId, int customerId, int dependencyAgentId, int productId) {
        // 仅本账号
        BooleanExpression expr = lauArchiveAuthorizedRelation.accountId.eq(Long.valueOf(accountId));
        // 同客户&同代理
        expr = expr.or(lauArchiveAuthorizedRelation.customerId.eq(customerId))
                .and(lauArchiveAuthorizedRelation.agentId.eq(dependencyAgentId))
                .and(lauArchiveAuthorizedRelation.sharingScope.eq(
                        ArchiveShareScopeEnum.SAME_CUSTOMER_SAME_AGENT.getCode()));
        // 同客户&同品牌
        expr = expr.or(lauArchiveAuthorizedRelation.customerId.eq(customerId))
                .and(lauArchiveAuthorizedRelation.productId.eq(productId))
                .and(lauArchiveAuthorizedRelation.sharingScope.eq(
                        ArchiveShareScopeEnum.SAME_CUSTOMER_SAME_BRAND.getCode()));
        // 同客户&同品牌&同代理
        expr = expr.or(lauArchiveAuthorizedRelation.customerId.eq(customerId))
                .and(lauArchiveAuthorizedRelation.productId.eq(productId))
                .and(lauArchiveAuthorizedRelation.agentId.eq(dependencyAgentId))
                .and(lauArchiveAuthorizedRelation.sharingScope.eq(
                        ArchiveShareScopeEnum.SAME_CUSTOMER_SAME_BRAND_SAME_AGENT.getCode()));
        // 同代理
        expr = expr.or(lauArchiveAuthorizedRelation.agentId.eq(dependencyAgentId))
                .and(lauArchiveAuthorizedRelation.sharingScope.eq(ArchiveShareScopeEnum.SAME_AGENT.getCode()));
        return expr;
    }

    @Test
    public void list() {

        Context context = new Context();

        Integer authSource = null;
        Integer status = null;
        Long upMid = null;
        Integer page = 1;
        Integer pageSize = 10;

//        Mockito.when(bqf).thenReturn()selectFrom(Mockito.any()).fetchPage(Mockito.any(), Mockito.any());
        Object result = generalDynamicService.list(context.getAccountId(), authSource, status, upMid, page,
                pageSize, null, null);
        System.out.println(result);
    }

    @Test
    public void info() {
        BooleanExpression booleanExpression = genShareExpr(********, 49, 69, 157);
        System.out.println(booleanExpression);
    }

    @Test
    public void accept() {
    }

    @Test
    public void reject() {
        CpcSaveCreativeService cpcSaveCreativeService = new CpcSaveCreativeService(null, null);
        CpcCreativeMonitoringDto cpcCreativeMonitoringDto = cpcSaveCreativeService.genGameHiddenMonitoring(1, 2, 3,
                true);
        System.out.println(cpcCreativeMonitoringDto);
    }

    @Test
    public void state() {
        Calendar instance = Calendar.getInstance();
        instance.setFirstDayOfWeek(Calendar.MONDAY);
        instance.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
        instance.set(Calendar.HOUR_OF_DAY, 0);
        instance.set(Calendar.MINUTE, 0);
        instance.set(Calendar.SECOND, 0);
        instance.set(Calendar.MILLISECOND, 0);
        instance.add(Calendar.DAY_OF_WEEK, 7);
        Random random = new Random(System.currentTimeMillis());
        long l = instance.getTime().getTime() - System.currentTimeMillis() - (random.nextInt(300) * 1000L);
        System.out.println(instance);
        System.out.println(l);

    }
}