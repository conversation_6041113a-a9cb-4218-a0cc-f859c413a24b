package com.bilibili.adp.v6.report;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.cpc.biz.services.account.dto.CouponCostChartQueryDto;
import com.bilibili.adp.cpc.biz.services.account.dto.CrmCouponProjectAccountAmountDto;
import junit.framework.TestCase;

import java.util.ArrayList;
import java.util.List;

public class FinanceServiceTest extends TestCase {

    public void testSupplyMissingDates1() {

        List<CrmCouponProjectAccountAmountDto> accountAmountDtos = new ArrayList<>();
        List<CrmCouponProjectAccountAmountDto> accountAmountDtos1 = FinanceService.supplyMissingDates(CouponCostChartQueryDto.builder().build(), accountAmountDtos);
        System.out.println(accountAmountDtos1);
    }

    public void testSupplyMissingDates2() {

        List<CrmCouponProjectAccountAmountDto> accountAmountDtos = new ArrayList<>();
        accountAmountDtos.add(CrmCouponProjectAccountAmountDto.builder().logDate("********").sumCouponValue(100L).build());
        accountAmountDtos.add(CrmCouponProjectAccountAmountDto.builder().logDate("********").sumCouponValue(100L).build());
        CouponCostChartQueryDto queryDto = CouponCostChartQueryDto.builder()
                .fromDate(1732982400000L)
                .toDate(1733155200000L)
                .build();
        List<CrmCouponProjectAccountAmountDto> accountAmountDtos1 = FinanceService.supplyMissingDates(queryDto, accountAmountDtos);
        System.out.println(accountAmountDtos1);
    }

    public void testCase1() {
        int[][] actual = calculateTaskRanges(1, 101, 2, null);
        printArr(actual);

        int[][] actual2 = calculateTaskRanges(1, 100 - 1 + 1, 5, new int[]{11, 42, 45, 71, 99});
        printArr(actual2);
    }

    public void testCase2() {
        int[][] actual = calculateTaskRanges(1, 5 - 1 + 1, 10);
        printArr(actual);
    }

    public void testCase3() {
        int[][] actual = calculateTaskRanges(*********, *********-*********+1, 12);
        printArr(actual);
    }

    public void testCase4() {
        List<Integer> creativeIds = new ArrayList<>();
        for (int i = 1; i <= 21; i++) {
            creativeIds.add(i);
        }

        // 计算需要的批次数
        int batchSize = 10;
        int totalSize = creativeIds.size();
        int count = (totalSize + batchSize - 1) / batchSize; // 上取整

        // 分批处理creativeIds
        for (int i = 0; i < count; i++) {
            int fromIndex = i * batchSize;
            int toIndex = Math.min(fromIndex + batchSize, totalSize);

            // 提取当前批次的子列表
            List<Integer> batchList = creativeIds.subList(fromIndex, toIndex);
            System.out.println("i=" + i + ", batchList=" + JSON.toJSONString(batchList));
        }
    }

    private void printArr(int[][] actual) {
        for (int i = 0; i < actual.length; i++) {
            System.out.println(JSON.toJSONString(actual[i]));
        }
    }

    private static int[][] calculateTaskRanges(int fromId, int totalDataCount, int numberOfThreads) {
        int[][] ranges = new int[numberOfThreads][2];
        int chunkSize = totalDataCount / numberOfThreads;
        int remainder = totalDataCount % numberOfThreads;

        int currentStartId = fromId;
        for (int i = 0; i < numberOfThreads; i++) {
            int currentEndId = currentStartId + chunkSize - 1;
            if (i < remainder) { // 分配剩余任务
                currentEndId++;
            }
            ranges[i][0] = currentStartId;
            ranges[i][1] = currentEndId;
            currentStartId = currentEndId + 1;
        }
        return ranges;
    }

    private static int[][] calculateTaskRanges(int fromId, int maxCreativeId, int numberOfThreads, int[] startCreativeIds) {
        int totalDataCount = (maxCreativeId >= fromId) ? (maxCreativeId - fromId + 1) : 0;
        if (totalDataCount <= 0) {
            return new int[0][0]; // No data to process if the range is invalid
        }

        int[][] ranges = new int[numberOfThreads][2];
        int currentStartId = fromId;
        int chunkSize = totalDataCount / numberOfThreads;
        int remainder = totalDataCount % numberOfThreads;

        for (int i = 0; i < numberOfThreads; i++) {
            // 如果 startCreativeIds 有效则使用它, 否则使用默认计算的
            if (startCreativeIds != null && startCreativeIds.length > i && startCreativeIds[i] >= currentStartId) {
                ranges[i][0] = startCreativeIds[i];
            } else {
                ranges[i][0] = currentStartId;
            }

            if (ranges[i][0] > maxCreativeId) {
                ranges[i][0] = ranges[i][1] = -1; // Indicator for no task
            } else {
                int currentEndId = currentStartId + chunkSize - 1;
                if (i < remainder) {
                    currentEndId++; // Ensure a balanced distribution of remainder
                }
                ranges[i][1] = Math.min(currentEndId, maxCreativeId);
                currentStartId = ranges[i][1] + 1; // Update start for next chunk
            }
        }
        return ranges;
    }
}