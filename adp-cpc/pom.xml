<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>advertiser-platform</artifactId>
        <groupId>com.bilibili.adp</groupId>
        <version>2.1.13-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>adp-cpc</artifactId>

    <properties>
        <bapi.version>1.29.0.1.mr23100.**************.b2d35ccb3704</bapi.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>sycpb.platform</groupId>
            <artifactId>boggart-datasource-proxy</artifactId>
            <version>${boggart.version}</version>
        </dependency>
        <dependency>
            <groupId>net.dongliu</groupId>
            <artifactId>apk-parser</artifactId>
            <version>2.6.10</version>
        </dependency>
        <dependency>
            <groupId>pleiades.component.datasource</groupId>
            <artifactId>mysql</artifactId>
        </dependency>
        <dependency>
            <groupId>pleiades.component.datasource</groupId>
            <artifactId>datasource-common</artifactId>
        </dependency>
        <dependency>
            <groupId>org.hibernate.validator</groupId>
            <artifactId>hibernate-validator</artifactId>
        </dependency>

        <dependency>
            <groupId>javax.validation</groupId>
            <artifactId>validation-api</artifactId>
        </dependency>
        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>s3</artifactId>
        </dependency>
        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>s3-transfer-manager</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis</artifactId>
        </dependency>
        <dependency>
            <groupId>pleiades.component.rpc</groupId>
            <artifactId>rpc-server</artifactId>
            <version>${pleiades.version}</version>
        </dependency>
        <!-- 加了http-server metrics 才能看到rpc调用log -->
        <dependency>
            <groupId>pleiades.component.http</groupId>
            <artifactId>http-server</artifactId>
            <version>${pleiades.version}</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
            <version>${spring-framework.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>javax</groupId>
                    <artifactId>javaee-web-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
            <version>${spring-framework.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-tx</artifactId>
            <version>${spring-framework.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-jdbc</artifactId>
            <version>${spring-framework.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bilibili.bjcom</groupId>
            <artifactId>bjcom-querydsl</artifactId>
            <version>1.1.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>plexus-container-default</artifactId>
                    <groupId>org.codehaus.plexus</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.bilibili.bjcom</groupId>
            <artifactId>bjcom-test</artifactId>
            <version>1.1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.taobao.cps</groupId>
            <artifactId>taobao-cps-client</artifactId>
            <version>8.7.15-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>${jackson-databind.version}</version>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>${mysql.version}</version>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>${junit.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.jooq</groupId>
            <artifactId>joor-java-8</artifactId>
            <version>0.9.13</version>
        </dependency>
        <dependency>
            <groupId>io.vavr</groupId>
            <artifactId>vavr</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.9</version>
        </dependency>
        <dependency>
            <groupId>com.bilibili.crm</groupId>
            <artifactId>crm-api</artifactId>
            <version>${crm.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.bilibili.adp</groupId>
                    <artifactId>common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.bilibili.adp</groupId>
                    <artifactId>passport-biz</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.bilibili.adp</groupId>
            <artifactId>common</artifactId>
            <version>${adp.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.bilibili.mgk</groupId>
                    <artifactId>mgk-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.bilibili.bjcom</groupId>
            <artifactId>bjcom-cat-mybatis</artifactId>
            <version>1.0.1-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.google.code.gson</groupId>
                    <artifactId>gson</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.mchange</groupId>
            <artifactId>c3p0</artifactId>
            <version>${c3p0.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bilibili.bjcom</groupId>
            <artifactId>bjcom-cat-spring</artifactId>
            <version>1.0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.bilibili.location</groupId>
            <artifactId>location-api</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>http-util</artifactId>
                    <groupId>com.bilibili.adp</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.bilibili.adp</groupId>
            <artifactId>launch-biz</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>commercial-order-api</artifactId>
                    <groupId>com.bilibili.ad</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.bilibili.adp</groupId>
            <artifactId>launch-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bilibili.adp</groupId>
            <artifactId>log-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.javers</groupId>
            <artifactId>javers-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bilibili.mgk</groupId>
            <artifactId>collage-api</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
        </dependency>
        <dependency>
            <groupId>ru.yandex.clickhouse</groupId>
            <artifactId>clickhouse-jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zaxxer</groupId>
            <artifactId>HikariCP</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-net</groupId>
            <artifactId>commons-net</artifactId>
            <version>3.8.0</version>
        </dependency>
        <dependency>
            <groupId>com.bilibili.ad</groupId>
            <artifactId>commercial-order-api</artifactId>
            <version>${commercial.order.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bilibili.mas</groupId>
            <artifactId>mas-common</artifactId>
            <version>4.7.1-SNAPSHOT</version>
        </dependency>

        <!-- pleiades databus -->
        <dependency>
            <groupId>pleiades.component</groupId>
            <artifactId>databus</artifactId>
            <version>${pleiades.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bilibili</groupId>
            <artifactId>warp-spring-boot-starter-databus</artifactId>
            <version>${wrap-databus.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.bilibili.microservices</groupId>
                    <artifactId>databus-java</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.bilibili.microservices</groupId>
            <artifactId>databus-java</artifactId>
            <version>${databus-java.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bilibili</groupId>
            <artifactId>warp-spring-boot-autoconfigure</artifactId>
            <version>1.0.12</version>
        </dependency>
        <dependency>
            <groupId>com.bilibili.config</groupId>
            <artifactId>config-refresh</artifactId>
            <version>${config-refresh.version}</version>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>

        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
            <optional>true</optional>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>io.github.resilience4j</groupId>
            <artifactId>resilience4j-timelimiter</artifactId>
            <version>${resilience4j.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bilibili.dmp</groupId>
            <artifactId>dmp-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bilibili.mgk</groupId>
            <artifactId>mgk-common</artifactId>
        </dependency>
        <dependency>
            <groupId>io.grpc</groupId>
            <artifactId>grpc-services</artifactId>
            <version>1.29.0</version>
        </dependency>
        <dependency>
            <groupId>com.bilibili.business.content</groupId>
            <artifactId>business-content-api</artifactId>
            <version>1.0.1</version>
        </dependency>
        <dependency>
            <groupId>pleiades.venus</groupId>
            <artifactId>infoc</artifactId>
            <version>${pleiades.version}</version>
        </dependency>
        <dependency>
            <groupId>pleiades.venus</groupId>
            <artifactId>starter</artifactId>
            <version>${pleiades.version}</version>
        </dependency>
        <!--bapi start-->
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_pandora.core.batch_grpc_java</artifactId>
            <version>1.29.0.1.mr23100.**************.b2d35ccb3704</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_pandora.core.auto_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_adp.component_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_adp.anchor_grpc_java</artifactId>
            <version>1.29.0.1.mr23100.**************.b2d35ccb3704</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_scv.anchor_grpc_java</artifactId>
            <version>1.29.0.1.mr23100.**************.b2d35ccb3704</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_scv.component_group_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_component_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_adp.game_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_mgk.audit_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>

        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_adp.bluelink_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>infra_service.sequence_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_adp.jinghuo_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_adp.creative_common_task_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_location.adp.v6_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_mgk.component_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>

        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_adp.app_package_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_commercialorder.adauth_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_commercialorder.archive_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_commercialorder.declare_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_pandora.service_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_pandora.core.auto_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_pandora.core_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_pandora.core.v6_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_pandora.resource_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_campaign_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_unit_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_creative_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_creative.core_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_creative.splash_screen_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>

        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_external_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_location_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>crm_service.uprating.ratingv3_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>community_interface.dm_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>community_interface.reply_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ott_service_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>pgc_service.season.episode_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>content.flow.control_service_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>archive_service_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_brand_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_live_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_goods_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_report_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>live_e.commerce.goods_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_resource_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_ott_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_mgk.page-group_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_mgk.media_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_mgk.tag_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_personal.up.core_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>cpm_dmp_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_automatic.rule.service_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_cmc.goods_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_cmc.cidgoods_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_cmc.up_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_cmc.launchresource_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_adp.archive_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_resource.business_atmosphere_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_account.product_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_account.group_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>community_service.govern_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>account_service.relation.v1_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_takumi.auth_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_archive_grpc_java</artifactId>
            <version>${bapi.version}</version>

        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>archive.extra_extra.v1_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>dynamic_service.feed_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>dynamic_admin.feed_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_mas.goods_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_mas.inviteorder_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>

        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_mas.cluepass_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>community_service.dm.open_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>community_interface.govern.filter_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>bcg_ones.minos_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>dynamic_service.index_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_jupiter.programed.creative_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_account.label_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>

        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_adp.qualification_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_adp.mini_game_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_adp.log_grpc_java</artifactId>
            <version>1.29.0.1.mr23100.**************.b2d35ccb3704</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_mgk.business_tool_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_audit.danmaku_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_jupiter.rta_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_adp.game_activity_video_auth_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_account.crm.acc_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_account.category_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_adp.live_component_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_adp.task_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_crm.account_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_crm.agent.account_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_crm.category_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_crm.customer_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_crm.platform.project_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_crm.industry_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_crm.product_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_crm.wallet_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_crm.wallet.original_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_crm.wallet.coupon.query_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_crm.wallet.soa.wallet_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_crm.platform.coupon_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_jupiter.archive_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_jupiter.search.words_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_adp.sdpa_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_jupiter.img_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_pluto.service_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_pluto.service.v1_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_pluto.meta_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_pluto.stat_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>cheese_service.season.business.v1_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_adp.budget_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>datacenter_service.oneservice_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>bcg_onesv2.minos_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_mgk.material_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_adp.overrule_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_audit_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>cpm_bdata.service_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>cpm_partner.task_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>cpm_partner.arch_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_mgk_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_business.acc_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_adp.video_auth_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_adp.ad_count_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_pandora.core.list_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_pandora.core.list_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_adp.mid_auth_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_adp.trade_blacklist_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>bcg_stat_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>infra_service.taishan_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>

        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_crm.account_label_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>
        <!--bapi end-->
        <dependency>
            <groupId>com.jd.sdk</groupId>
            <artifactId>open-api-sdk</artifactId>
            <version>4.7</version>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.maven.surefire</groupId>
            <artifactId>maven-surefire-common</artifactId>
            <version>3.2.5</version>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <artifactId>maven-artifact</artifactId>
                    <groupId>org.apache.maven</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.bilibili.adp</groupId>
            <artifactId>adp-web-framework</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>pleiades.component.datasource</groupId>
            <artifactId>mysql</artifactId>
            <version>1.2.9</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>
    </dependencies>
    <pluginRepositories>
        <pluginRepository>
            <id>bilibili-plugin-snapshots</id>
            <name>Bilibili Nexus Plugin Snapshot</name>
            <url>https://nexus.bilibili.co/content/repositories/snapshots</url>
            <releases>
                <enabled>false</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>daily</updatePolicy>
                <checksumPolicy>warn</checksumPolicy>
            </snapshots>
        </pluginRepository>
        <pluginRepository>
            <id>bilibili-plugin-releases</id>
            <name>Bilibili Nexus Plugin Release</name>
            <url>https://nexus.bilibili.co/content/repositories/releases</url>
            <releases>
                <enabled>true</enabled>
                <checksumPolicy>warn</checksumPolicy>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>

    <build>
        <plugins>
            <plugin>
                <groupId>com.querydsl</groupId>
                <artifactId>querydsl-maven-plugin</artifactId>
                <version>4.2.1</version>
                <configuration>
                    <jdbcDriver>com.mysql.jdbc.Driver</jdbcDriver>
                    <jdbcUrl>
                        ***********************************************************************************************
                    </jdbcUrl>
                    <jdbcUser>bilibili_business_ad</jdbcUser>
                    <jdbcPassword>32CrCuwxOpuXTJirRt9VnE6kkFnlNz6U</jdbcPassword>
                    <exportForeignKeys>false</exportForeignKeys>
                    <sourceFolder>${project.basedir}/src/main/resources</sourceFolder>
                    <targetFolder>${project.basedir}/src/main/java</targetFolder>
                    <exportBeans>true</exportBeans>
                    <packageName>com.bilibili.adp.cpc.dao.querydsl</packageName>
                    <beanPackageName>com.bilibili.adp.cpc.dao.querydsl.pos</beanPackageName>
                    <beanSuffix>Po</beanSuffix>
                    <beanAddToString>true</beanAddToString>
                    <tableNamePattern>lau_account_auto_launch_draft</tableNamePattern>
                    <numericMappings>
                        <numericMapping>
                            <total>1</total>
                            <decimal>0</decimal>
                            <javaType>java.lang.Integer</javaType>
                        </numericMapping>
                        <numericMapping>
                            <total>2</total>
                            <decimal>0</decimal>
                            <javaType>java.lang.Integer</javaType>
                        </numericMapping>
                        <numericMapping>
                            <total>3</total>
                            <decimal>0</decimal>
                            <javaType>java.lang.Integer</javaType>
                        </numericMapping>
                        <numericMapping>
                            <total>4</total>
                            <decimal>0</decimal>
                            <javaType>java.lang.Integer</javaType>
                        </numericMapping>
                        <numericMapping>
                            <total>5</total>
                            <decimal>0</decimal>
                            <javaType>java.lang.Integer</javaType>
                        </numericMapping>
                        <numericMapping>
                            <total>6</total>
                            <decimal>0</decimal>
                            <javaType>java.lang.Integer</javaType>
                        </numericMapping>
                    </numericMappings>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>mysql</groupId>
                        <artifactId>mysql-connector-java</artifactId>
                        <version>${mysql.version}</version>
                    </dependency>
                </dependencies>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.5.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                    <compilerArgs>
                        <arg>-parameters</arg>
                    </compilerArgs>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>no.entur.mapstruct.spi</groupId>
                            <artifactId>protobuf-spi-impl</artifactId>
                            <version>1.43</version>
                        </path>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${org.mapstruct.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok-mapstruct-binding</artifactId>
                            <version>0.2.0</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>