stages:
  - build

variables:
  # 服务树名
  TREE_NAME: sycpb.cpm.cpm-adp
  # 需要发送的安全依赖文件
  DEPEND_FILE: pom.xml
  # 语言类型
  LANGUAGE: java

build:
  stage: build
  tags:
    - rider-shared-shell
  script:
    - file=`cat $DEPEND_FILE | base64 | tr -d '\n'`
    - echo {\"owner\":\"$GITLAB_USER_EMAIL\", \"app_name\":\"$TREE_NAME\", \"branch\":\"$CI_COMMIT_BRANCH\",\"commit_id\":\"$CI_COMMIT_SHA\",\"lang\":\"$LANGUAGE\",\"app_from\":\"git\",\"data\":\"$file\",\"build_task_id\":\"$CI_JOB_ID\"} | curl -m 5 -s -X POST -H "Content-Type:application/json" -d @- "https://riki.bilibili.co/api/bvd/package/"
  only:
    - branches
    - tags